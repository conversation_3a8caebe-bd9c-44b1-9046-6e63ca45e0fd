﻿Imports System.Runtime.CompilerServices
Imports System.Text
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.XtraRichEdit.API.Native

Module DevexpressExtensions

    <Extension>
    Public Sub TryShowWaitForm(ssm As DevExpress.XtraSplashScreen.SplashScreenManager, show As Boolean)
        Try
            If show Then
                ssm.ShowWaitForm()
                Application.DoEvents()
            Else
                If ssm.IsSplashFormVisible Then
                    ssm.CloseWaitForm()
                End If
                Application.DoEvents()
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error Showing splash screen")
        End Try
    End Sub


    <Extension>
    Public Function ToBarItemVisibility(b As Boolean) As DevExpress.XtraBars.BarItemVisibility
        If b Then
            Return DevExpress.XtraBars.BarItemVisibility.Always
        Else
            Return DevExpress.XtraBars.BarItemVisibility.Never
        End If
    End Function

    <Extension>
    Public Function ToBarItemVisibility(b? As Boolean) As DevExpress.XtraBars.BarItemVisibility
        If b.HasValue AndAlso b Then
            Return DevExpress.XtraBars.BarItemVisibility.Always
        Else
            Return DevExpress.XtraBars.BarItemVisibility.Never
        End If
    End Function

    <Extension>
    Public Sub ToTitleCase(rec As DevExpress.XtraRichEdit.RichEditControl)
        AddHandler rec.ContentChanged, AddressOf Rec_ContentChanged
    End Sub

    Private Sub Rec_ContentChanged(sender As Object, e As EventArgs)
        Dim rec As DevExpress.XtraRichEdit.RichEditControl = sender
        Try
            rec.BeginUpdate()

            Dim start As DocumentPosition = rec.Document.CaretPosition
            If start.ToInt() = 0 OrElse start.ToInt() = 2 Then Exit Sub

            If start.ToInt() = 1 Then
                Dim range1 As DocumentRange = rec.Document.CreateRange(start.ToInt() - 1, 1)
                Dim text1 = rec.Document.GetText(range1)
                If text1.IsNotNullOrWhiteSpace() AndAlso text1 <> text1.ToUpper() Then rec.Document.Replace(range1, text1.ToUpper())
                Exit Sub
            End If

            Dim range As DocumentRange = rec.Document.CreateRange(start.ToInt() - 3, 3)

            Dim text = rec.Document.GetText(range)

            If (text.StartsWith(". ") OrElse text.Contains(Environment.NewLine)) AndAlso text <> text.ToUpper() Then
                If text.Contains(Environment.NewLine) Then
                    Dim idx = text.IndexOf(Environment.NewLine)
                    Dim sb = New StringBuilder(text)
                    If (sb(idx + 2) = Char.ToUpper(sb(idx + 2))) Then
                        Exit Sub
                    End If

                    sb(idx + 2) = Char.ToUpper(sb(idx + 2))
                    rec.Document.Replace(range, sb.ToString())
                Else
                    rec.Document.Replace(range, text.ToUpper())
                End If
            ElseIf text = " i " Then
                rec.Document.Replace(range, " I ")
            End If

        Catch ex As Exception

        Finally
            rec.EndUpdate()
        End Try
    End Sub

    <Extension>
    Public Sub ToTitleCase(memoEdit As DevExpress.XtraEditors.MemoEdit)
        AddHandler memoEdit.TextChanged, AddressOf MemoEdit_TextChanged
    End Sub

    Private Sub MemoEdit_TextChanged(sender As Object, e As EventArgs)
        Dim memoEdit As DevExpress.XtraEditors.MemoEdit = sender
        Try
            Dim text As String = memoEdit.Text
            If text Is Nothing Then Exit Sub
            Dim possition = memoEdit.SelectionStart
            If text.Count = 1 AndAlso text = text.ToLower Then
                GetInnerTextBox(memoEdit).Text = text.ToUpper
            ElseIf text.Count > 2 Then
                Dim sb As StringBuilder = New StringBuilder(text)
                Dim i = text.Length - 3
                text = text.Substring(i, 3)
                If text.StartsWith(". ") Then
                    sb.Remove(i, 3)
                    sb.Insert(i, ". " & Char.ToUpper(text(2)))
                ElseIf text.Contains(vbCrLf) Then
                    If sb(i + 2) <> Char.ToUpper(sb(i + 2)) Then
                        sb(i + 2) = Char.ToUpper(sb(i + 2))
                    End If
                ElseIf text = " i " Then
                    sb.Remove(i, 3)
                    sb.Insert(i, " I ")
                End If
                GetInnerTextBox(memoEdit).Text = sb.ToString()
            End If
            memoEdit.SelectionStart = possition
        Finally
        End Try
    End Sub

    Private Function GetInnerTextBox(ByVal editor As DevExpress.XtraEditors.TextEdit) As System.Windows.Forms.TextBox
        If Not (editor Is Nothing) Then
            Dim control As Control
            For Each control In editor.Controls
                If TypeOf control Is System.Windows.Forms.TextBox Then
                    Return CType(control, System.Windows.Forms.TextBox)
                End If
            Next control
        End If
        Return Nothing
    End Function 'GetInnerTextBox


    <Extension>
    Public Sub AddDeleteButton(slue As DevExpress.XtraEditors.SearchLookUpEdit)
        slue.Properties.Buttons.Add(New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Delete))
        AddHandler slue.ButtonClick, New DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(AddressOf DeleteButtonClick)
    End Sub

    Private Sub DeleteButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
        TryCast(sender, DevExpress.XtraEditors.BaseEdit).EditValue = Nothing
    End Sub

    <Extension>
    Public Sub AddPhoneButton(be As ButtonEdit)
        be.Properties.Buttons.Clear()
        be.Properties.Buttons.Add(New Controls.EditorButton(Controls.ButtonPredefines.Glyph) With {.Image = My.Resources.Phone_Small, .IsLeft = True})
        AddHandler be.ButtonClick, New Controls.ButtonPressedEventHandler(AddressOf PhoneButtonClick)
    End Sub

    <Extension>
    Public Sub AddFaxButton(be As ButtonEdit)
        be.Properties.Buttons.Clear()
        be.Properties.Buttons.Add(New Controls.EditorButton(Controls.ButtonPredefines.Glyph) With {.Image = My.Resources.Phone_Small})
        AddHandler be.ButtonClick, New Controls.ButtonPressedEventHandler(AddressOf FaxButtonClick)
    End Sub

    <Extension>
    Public Sub AddEmailButton(be As ButtonEdit)
        be.Properties.Buttons.Clear()
        be.Properties.Buttons.Add(New Controls.EditorButton(Controls.ButtonPredefines.Glyph) With {.Image = My.Resources.mail_16x16})
        AddHandler be.ButtonClick, New Controls.ButtonPressedEventHandler(AddressOf EmailButtonClick)
    End Sub

    <Extension>
    Public Sub AddClearButton(be As ButtonEdit)
        be.Properties.Buttons.Add(New Controls.EditorButton(Controls.ButtonPredefines.Delete))
        AddHandler be.ButtonClick, New Controls.ButtonPressedEventHandler(AddressOf ClearButtonClick)
    End Sub

    Private Sub ClearButtonClick(sender As Object, e As ButtonPressedEventArgs)
        If e.Button.Kind <> ButtonPredefines.Delete Then Exit Sub
        Dim tb = CType(sender, ButtonEdit)
        tb.EditValue = Nothing
    End Sub

    Private Sub EmailButtonClick(sender As Object, e As ButtonPressedEventArgs)
        If e.Button.Kind <> ButtonPredefines.Glyph Then Exit Sub
        Dim tb = CType(sender, ButtonEdit)
        EmailHelpers.ComposeEmail(tb.Text, "", "").ShowDialog()
    End Sub

    Private Sub FaxButtonClick(sender As Object, e As ButtonPressedEventArgs)
        If e.Button.Kind <> ButtonPredefines.Glyph Then Exit Sub
        Dim tb = CType(sender, ButtonEdit)
        If tb.Text.IsNotNullOrWhiteSpace Then
            Dim frm = New frmPhoneFax(tb.Text, "")
            frm.ShowDialog()
        End If
    End Sub

    Private Sub PhoneButtonClick(sender As Object, e As ButtonPressedEventArgs)
        If e.Button.Kind <> ButtonPredefines.Glyph Then Exit Sub
        Dim tb = CType(sender, ButtonEdit)
        If tb.Text.IsNotNullOrWhiteSpace Then
            Dim frm = New frmPhone(tb.Text)
        End If
    End Sub

    <Extension>
    Public Function GetCheckedItems(ccb As CheckedComboBoxEdit) As List(Of String)
        Return ccb.Properties.GetCheckedItems().ToString().Split(",").Select(Function(v) v.Trim()).Where(Function(s) s.IsNotNullOrWhiteSpace()).ToList
        'Return ccb.Properties.GetCheckedItems().ToString().Split(",").Split(",").Where(Function(s) s.IsNotNullOrWhiteSpace()).ToList()
    End Function
End Module

