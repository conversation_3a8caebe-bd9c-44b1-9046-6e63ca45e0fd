﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmManualBillingDraftAdd
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.btnSubmit = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEdit6 = New DevExpress.XtraEditors.TextEdit()
        Me.manual_Trans_Account_listsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.TextEdit4 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.teRoutingNumber = New DevExpress.XtraEditors.TextEdit()
        Me.teAccountNumber = New DevExpress.XtraEditors.TextEdit()
        Me.pceConum = New DevExpress.XtraEditors.PopupContainerEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colDivision = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCOUNT_SOURCE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colROUTING_NUMBER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCOUNT_NUMBER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDDIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDDIVNAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPriority_Lvl = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCOUNT_ORDER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACH_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTrans_Note = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRequested_Effective_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBilling_Dept_Notes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAMOUNT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTransaction_Status = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDate_Requested = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRequested_By = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTransaction_Effective_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_Routing = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_Account = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_ACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_CONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_EMPNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.TextEdit5 = New DevExpress.XtraEditors.DateEdit()
        Me.teAmount = New DevExpress.XtraEditors.CalcEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.SimpleSeparator1 = New DevExpress.XtraLayout.SimpleSeparator()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.TextEdit6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.manual_Trans_Account_listsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teRoutingNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teAccountNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pceConum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit5.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleSeparator1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.btnSubmit)
        Me.LayoutControl1.Controls.Add(Me.TextEdit6)
        Me.LayoutControl1.Controls.Add(Me.TextEdit4)
        Me.LayoutControl1.Controls.Add(Me.TextEdit3)
        Me.LayoutControl1.Controls.Add(Me.teRoutingNumber)
        Me.LayoutControl1.Controls.Add(Me.teAccountNumber)
        Me.LayoutControl1.Controls.Add(Me.pceConum)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Controls.Add(Me.TextEdit5)
        Me.LayoutControl1.Controls.Add(Me.teAmount)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(1015, 229, 650, 400)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(873, 372)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'btnSubmit
        '
        Me.btnSubmit.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.apply_16x161
        Me.btnSubmit.Location = New System.Drawing.Point(744, 326)
        Me.btnSubmit.Name = "btnSubmit"
        Me.btnSubmit.Size = New System.Drawing.Size(105, 22)
        Me.btnSubmit.TabIndex = 13
        Me.btnSubmit.Text = "Submit"
        '
        'TextEdit6
        '
        Me.TextEdit6.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "Billing_Dept_Notes", True))
        Me.TextEdit6.Location = New System.Drawing.Point(695, 200)
        Me.TextEdit6.Name = "TextEdit6"
        Me.TextEdit6.Size = New System.Drawing.Size(154, 20)
        Me.TextEdit6.TabIndex = 11
        '
        'manual_Trans_Account_listsBindingSource
        '
        Me.manual_Trans_Account_listsBindingSource.DataSource = GetType(Brands_FrontDesk.Manual_Trans_Account_list)
        '
        'TextEdit4
        '
        Me.TextEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "Trans_Note", True))
        Me.TextEdit4.Location = New System.Drawing.Point(695, 152)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Size = New System.Drawing.Size(154, 20)
        Me.TextEdit4.TabIndex = 9
        '
        'TextEdit3
        '
        Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "ACH_NAME", True))
        Me.TextEdit3.Location = New System.Drawing.Point(695, 128)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.MaxLength = 16
        Me.TextEdit3.Size = New System.Drawing.Size(154, 20)
        Me.TextEdit3.TabIndex = 8
        '
        'teRoutingNumber
        '
        Me.teRoutingNumber.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "ROUTING_NUMBER", True))
        Me.teRoutingNumber.Location = New System.Drawing.Point(695, 80)
        Me.teRoutingNumber.Name = "teRoutingNumber"
        Me.teRoutingNumber.Size = New System.Drawing.Size(154, 20)
        Me.teRoutingNumber.TabIndex = 7
        '
        'teAccountNumber
        '
        Me.teAccountNumber.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "ACCOUNT_NUMBER", True))
        Me.teAccountNumber.Location = New System.Drawing.Point(695, 104)
        Me.teAccountNumber.Name = "teAccountNumber"
        Me.teAccountNumber.Size = New System.Drawing.Size(154, 20)
        Me.teAccountNumber.TabIndex = 6
        '
        'pceConum
        '
        Me.pceConum.Location = New System.Drawing.Point(44, 12)
        Me.pceConum.Name = "pceConum"
        Me.pceConum.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.pceConum.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.pceConum.Size = New System.Drawing.Size(263, 20)
        Me.pceConum.StyleController = Me.LayoutControl1
        Me.pceConum.TabIndex = 5
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.manual_Trans_Account_listsBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(12, 48)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(519, 312)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colDivision, Me.colACCOUNT_SOURCE, Me.colROUTING_NUMBER, Me.colACCOUNT_NUMBER, Me.colACCT_TYPE, Me.colCONUM, Me.colCO_NAME, Me.colDDIVNUM, Me.colDDIVNAME, Me.colPriority_Lvl, Me.colACCOUNT_ORDER, Me.colACH_NAME, Me.colTrans_Note, Me.colRequested_Effective_Date, Me.colBilling_Dept_Notes, Me.colAMOUNT, Me.colTransaction_Status, Me.colDate_Requested, Me.colRequested_By, Me.colTransaction_Effective_Date, Me.colCredit_Routing, Me.colCredit_Account, Me.colCredit_ACCT_TYPE, Me.colCredit_CONUM, Me.colCredit_EMPNUM})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colPriority_Lvl, DevExpress.Data.ColumnSortOrder.Ascending)})
        '
        'colDivision
        '
        Me.colDivision.Caption = "Division"
        Me.colDivision.FieldName = "GridColumn1"
        Me.colDivision.Name = "colDivision"
        Me.colDivision.UnboundExpression = "[DDIVNUM] + ' - ' + [DDIVNAME]"
        Me.colDivision.UnboundType = DevExpress.Data.UnboundColumnType.[String]
        Me.colDivision.Visible = True
        Me.colDivision.VisibleIndex = 0
        '
        'colACCOUNT_SOURCE
        '
        Me.colACCOUNT_SOURCE.Caption = "Source"
        Me.colACCOUNT_SOURCE.FieldName = "ACCOUNT_SOURCE"
        Me.colACCOUNT_SOURCE.Name = "colACCOUNT_SOURCE"
        Me.colACCOUNT_SOURCE.Visible = True
        Me.colACCOUNT_SOURCE.VisibleIndex = 1
        '
        'colROUTING_NUMBER
        '
        Me.colROUTING_NUMBER.Caption = "Routing #"
        Me.colROUTING_NUMBER.FieldName = "ROUTING_NUMBER"
        Me.colROUTING_NUMBER.Name = "colROUTING_NUMBER"
        Me.colROUTING_NUMBER.Visible = True
        Me.colROUTING_NUMBER.VisibleIndex = 2
        '
        'colACCOUNT_NUMBER
        '
        Me.colACCOUNT_NUMBER.Caption = "Account #"
        Me.colACCOUNT_NUMBER.FieldName = "ACCOUNT_NUMBER"
        Me.colACCOUNT_NUMBER.Name = "colACCOUNT_NUMBER"
        Me.colACCOUNT_NUMBER.Visible = True
        Me.colACCOUNT_NUMBER.VisibleIndex = 3
        '
        'colACCT_TYPE
        '
        Me.colACCT_TYPE.Caption = "Type"
        Me.colACCT_TYPE.FieldName = "ACCT_TYPE"
        Me.colACCT_TYPE.Name = "colACCT_TYPE"
        Me.colACCT_TYPE.Visible = True
        Me.colACCT_TYPE.VisibleIndex = 4
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        '
        'colCO_NAME
        '
        Me.colCO_NAME.FieldName = "CO_NAME"
        Me.colCO_NAME.Name = "colCO_NAME"
        '
        'colDDIVNUM
        '
        Me.colDDIVNUM.FieldName = "DDIVNUM"
        Me.colDDIVNUM.Name = "colDDIVNUM"
        '
        'colDDIVNAME
        '
        Me.colDDIVNAME.FieldName = "DDIVNAME"
        Me.colDDIVNAME.Name = "colDDIVNAME"
        '
        'colPriority_Lvl
        '
        Me.colPriority_Lvl.FieldName = "Priority_Lvl"
        Me.colPriority_Lvl.Name = "colPriority_Lvl"
        '
        'colACCOUNT_ORDER
        '
        Me.colACCOUNT_ORDER.FieldName = "ACCOUNT_ORDER"
        Me.colACCOUNT_ORDER.Name = "colACCOUNT_ORDER"
        '
        'colACH_NAME
        '
        Me.colACH_NAME.FieldName = "ACH_NAME"
        Me.colACH_NAME.Name = "colACH_NAME"
        '
        'colTrans_Note
        '
        Me.colTrans_Note.FieldName = "Trans_Note"
        Me.colTrans_Note.Name = "colTrans_Note"
        '
        'colRequested_Effective_Date
        '
        Me.colRequested_Effective_Date.FieldName = "Requested_Effective_Date"
        Me.colRequested_Effective_Date.Name = "colRequested_Effective_Date"
        '
        'colBilling_Dept_Notes
        '
        Me.colBilling_Dept_Notes.FieldName = "Billing_Dept_Notes"
        Me.colBilling_Dept_Notes.Name = "colBilling_Dept_Notes"
        '
        'colAMOUNT
        '
        Me.colAMOUNT.FieldName = "AMOUNT"
        Me.colAMOUNT.Name = "colAMOUNT"
        '
        'colTransaction_Status
        '
        Me.colTransaction_Status.FieldName = "Transaction_Status"
        Me.colTransaction_Status.Name = "colTransaction_Status"
        '
        'colDate_Requested
        '
        Me.colDate_Requested.FieldName = "Date_Requested"
        Me.colDate_Requested.Name = "colDate_Requested"
        '
        'colRequested_By
        '
        Me.colRequested_By.FieldName = "Requested_By"
        Me.colRequested_By.Name = "colRequested_By"
        '
        'colTransaction_Effective_Date
        '
        Me.colTransaction_Effective_Date.FieldName = "Transaction_Effective_Date"
        Me.colTransaction_Effective_Date.Name = "colTransaction_Effective_Date"
        '
        'colCredit_Routing
        '
        Me.colCredit_Routing.FieldName = "Credit_Routing"
        Me.colCredit_Routing.Name = "colCredit_Routing"
        '
        'colCredit_Account
        '
        Me.colCredit_Account.FieldName = "Credit_Account"
        Me.colCredit_Account.Name = "colCredit_Account"
        '
        'colCredit_ACCT_TYPE
        '
        Me.colCredit_ACCT_TYPE.FieldName = "Credit_ACCT_TYPE"
        Me.colCredit_ACCT_TYPE.Name = "colCredit_ACCT_TYPE"
        '
        'colCredit_CONUM
        '
        Me.colCredit_CONUM.FieldName = "Credit_CONUM"
        Me.colCredit_CONUM.Name = "colCredit_CONUM"
        '
        'colCredit_EMPNUM
        '
        Me.colCredit_EMPNUM.FieldName = "Credit_EMPNUM"
        Me.colCredit_EMPNUM.Name = "colCredit_EMPNUM"
        '
        'TextEdit5
        '
        Me.TextEdit5.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "Requested_Effective_Date", True))
        Me.TextEdit5.EditValue = Nothing
        Me.TextEdit5.Location = New System.Drawing.Point(695, 176)
        Me.TextEdit5.Name = "TextEdit5"
        Me.TextEdit5.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit5.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit5.Properties.DisplayFormat.FormatString = ""
        Me.TextEdit5.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.TextEdit5.Properties.EditFormat.FormatString = ""
        Me.TextEdit5.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.TextEdit5.Properties.Mask.EditMask = ""
        Me.TextEdit5.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit5.Size = New System.Drawing.Size(154, 20)
        Me.TextEdit5.TabIndex = 10
        '
        'teAmount
        '
        Me.teAmount.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.manual_Trans_Account_listsBindingSource, "AMOUNT", True))
        Me.teAmount.Location = New System.Drawing.Point(695, 224)
        Me.teAmount.Name = "teAmount"
        Me.teAmount.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.teAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.teAmount.Size = New System.Drawing.Size(154, 20)
        Me.teAmount.TabIndex = 12
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AppearanceItemCaption.Font = New System.Drawing.Font("Segoe UI Semibold", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LayoutControlGroup1.AppearanceItemCaption.Options.UseFont = True
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.SplitterItem1, Me.LayoutControlItem2, Me.LayoutControlGroup2, Me.SimpleSeparator1, Me.EmptySpaceItem3})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(873, 372)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(523, 316)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.Location = New System.Drawing.Point(523, 36)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(8, 316)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.pceConum
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(299, 24)
        Me.LayoutControlItem2.Text = "Co#: "
        Me.LayoutControlItem2.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(27, 15)
        Me.LayoutControlItem2.TextToControlDistance = 5
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.LayoutControlItem3, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem9, Me.LayoutControlItem10, Me.EmptySpaceItem2, Me.EmptySpaceItem1})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(531, 36)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(322, 316)
        Me.LayoutControlGroup2.Text = "Add Manual Billing Draft"
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.teRoutingNumber
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem4.Text = "Routing #: "
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.teAccountNumber
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem3.Text = "Account #: "
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.TextEdit3
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem5.Text = "ACH Name: "
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.TextEdit4
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem6.Text = "Trans Note: "
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.TextEdit5
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem7.Text = "Requested Effective Date: "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.TextEdit6
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 120)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem8.Text = "Billing Dep Notes: "
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.teAmount
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 144)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(298, 24)
        Me.LayoutControlItem9.Text = "Amount: "
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(137, 15)
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.btnSubmit
        Me.LayoutControlItem10.Location = New System.Drawing.Point(189, 246)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(109, 26)
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem10.TextVisible = False
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 246)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(189, 26)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 168)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(298, 78)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'SimpleSeparator1
        '
        Me.SimpleSeparator1.AllowHotTrack = False
        Me.SimpleSeparator1.Location = New System.Drawing.Point(0, 24)
        Me.SimpleSeparator1.Name = "SimpleSeparator1"
        Me.SimpleSeparator1.Size = New System.Drawing.Size(853, 12)
        Me.SimpleSeparator1.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 10)
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(299, 0)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(554, 24)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'frmManualBillingDraftAdd
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(873, 372)
        Me.Controls.Add(Me.LayoutControl1)
        Me.IconOptions.ShowIcon = False
        Me.Name = "frmManualBillingDraftAdd"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Add Manual Billing Draft"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.TextEdit6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.manual_Trans_Account_listsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teRoutingNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teAccountNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pceConum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit5.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleSeparator1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents TextEdit6 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teRoutingNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teAccountNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents pceConum As DevExpress.XtraEditors.PopupContainerEdit
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents manual_Trans_Account_listsBindingSource As BindingSource
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDDIVNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDDIVNAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCOUNT_SOURCE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colROUTING_NUMBER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCOUNT_NUMBER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPriority_Lvl As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCOUNT_ORDER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACH_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTrans_Note As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRequested_Effective_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBilling_Dept_Notes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colAMOUNT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTransaction_Status As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDate_Requested As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRequested_By As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTransaction_Effective_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_Routing As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_Account As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_ACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_CONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_EMPNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnSubmit As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEdit5 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents teAmount As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents SimpleSeparator1 As DevExpress.XtraLayout.SimpleSeparator
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents colDivision As DevExpress.XtraGrid.Columns.GridColumn
End Class
