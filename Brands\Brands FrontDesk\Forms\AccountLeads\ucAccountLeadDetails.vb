﻿Imports System.ComponentModel
Imports System.IO
Imports Brands_FrontDesk
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports Humanizer

Public Class ucAccountLeadDetails

    Dim frmLogger As Serilog.ILogger = Logger.ForContext(Of frmAccountLeads)()
    Dim FilesPath = "I:\Account Leads"
    Dim _AccountLeads As view_AccountLead
    Dim _Enrollment As enrollment
    Private ReadOnly _AccountLeadsDataService As AccountLeadsDataService

    Sub New()
        InitializeComponent()
        frmLogger.Verbose("Openned ucAccountLeadDetails")
        _AccountLeadsDataService = New AccountLeadsDataService
    End Sub

    Private Sub frmAccountLeads_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If LicenseManager.UsageMode = LicenseUsageMode.Designtime OrElse IsInDesignMode Then Exit Sub
        ButtonEdit1.AddPhoneButton
        ButtonEdit2.AddPhoneButton
        ButtonEdit3.AddEmailButton
        ButtonEdit4.AddFaxButton
        Using db = New dbEPDataDataContext(GetConnectionString)
            bsCompanies.DataSource = db.view_CompanySumarries.ToList()
        End Using
    End Sub

    Friend Sub SetAvailableStatus(availableStatuses() As String)
        cbeStatus.Properties.Items.Clear()
        cbeStatus.Properties.Items.AddRange(availableStatuses)
    End Sub

    Public Sub SetDate(accountLeads As view_AccountLead)
        If LicenseManager.UsageMode = LicenseUsageMode.Designtime OrElse IsInDesignMode Then Exit Sub
        Try
            If accountLeads Is Nothing Then
                bsAccountLead.Clear()
                _AccountLeads = Nothing
                _Enrollment = Nothing
                bsRecentFiles.Clear()
                Enabled = False
            Else
                Enabled = True
                _AccountLeads = accountLeads
                bsAccountLead.DataSource = accountLeads
                LoadFiles()
                If accountLeads.EnrollmentID.HasValue Then
                    Using db = New dbEPDataDataContext(GetConnectionString)
                        _Enrollment = db.enrollments.Single(Function(e) e.EnrollmentID = accountLeads.EnrollmentID)
                    End Using
                Else
                    _Enrollment = Nothing
                End If
                SetEnrollmentInfo()
            End If
        Catch ex As Exception
            frmLogger.Error(ex, "Error loading Data")
            DisplayErrorMessage("Error Loading Account Leads", ex)
        End Try
    End Sub

    Private Sub LoadFiles()
        Try
            If _AccountLeads Is Nothing Then
                bsRecentFiles.Clear()
                Exit Sub
            End If
            Dim destPath = System.IO.Path.Combine(FilesPath, _AccountLeads.ID)
            If Not Directory.Exists(destPath) Then
                bsRecentFiles.Clear()
            Else
                Dim files = New DirectoryInfo(destPath).GetFiles()
                bsRecentFiles.DataSource = files.Select(Function(f) New ucRecentReports.RecentFiles With {.FileName = f.Name, .FilePath = f.FullName, .Extension = f.Extension, .CreateDate = f.LastWriteTime, .Checked = False}).OrderByDescending(Function(o) o.CreateDate).ToList()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading files", ex)
        End Try
    End Sub
    Private Sub hllcEditNotes_Click(sender As Object, e As EventArgs) Handles hllcEditNotes.Click
        Try
            Dim frm = New frmEditNotes(_AccountLeads.Notes)
            If frm.ShowDialog = DialogResult.OK Then
                _AccountLeads.Notes = frm.GetNote
                SaveRowNotes(_AccountLeads.ID, frm.GetNote)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error while editing notes", ex)
        End Try
    End Sub

    Private Sub SaveRowNotes(id As Integer, note As String)
        Try
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim row = db.AccountLeads.Single(Function(a) a.ID = id)
            row.Notes = note
            db.SaveChanges
        Catch ex As Exception
            frmLogger.Error(ex, "Error saving notes {ID}", id)
            DisplayErrorMessage("Error saving notes to Database", ex)
        End Try
    End Sub

    Private Sub btnAddFile_Click(sender As Object, e As EventArgs) Handles btnAddFile.Click
        Try
            Using fd = New OpenFileDialog()
                If fd.ShowDialog = DialogResult.OK Then
                    Dim destPath = System.IO.Path.Combine(FilesPath, _AccountLeads.ID)
                    If Not System.IO.Directory.Exists(destPath) Then
                        System.IO.Directory.CreateDirectory(destPath)
                    End If
                    System.IO.File.Copy(fd.FileName, System.IO.Path.Combine(destPath, System.IO.Path.GetFileName(fd.FileName)))
                    LoadFiles()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error adding file.", ex)
        End Try
    End Sub

    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles gvFiles.DoubleClick
        Dim file = CType(bsRecentFiles.Current, ucRecentReports.RecentFiles)
        If file IsNot Nothing Then
            Try
                'System.Diagnostics.Process.Start(file.FilePath)
                Dim psi As New System.Diagnostics.ProcessStartInfo()
                psi.FileName = file.FilePath
                psi.UseShellExecute = True
                System.Diagnostics.Process.Start(psi)
            Catch ex As Exception
                DisplayErrorMessage("Error opening file: {0}, {1}Error is: {2}".FormatWith(file.FileName, vbCrLf, ex.Message), ex)
            End Try
        End If
    End Sub

    Private Sub gvFiles_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvFiles.PopupMenuShowing
        Dim file = CType(bsRecentFiles.Current, ucRecentReports.RecentFiles)
        Dim row As view_AccountLead = bsAccountLead.Current
        If file IsNot Nothing AndAlso e.MenuType = GridMenuType.Row AndAlso e.Allow AndAlso e.HitInfo.InRow Then
            e.Menu.Items.Add(New DXMenuItem("Delete", Sub() DeleteFile(file, row), My.Resources.delete_16x16))
            e.Menu.Items.Add(New DXMenuItem("Save", Sub() SaveFile(file), My.Resources.saveas_16x16))
        End If
    End Sub

    Private Sub SaveFile(file As ucRecentReports.RecentFiles)
        Try
            Dim sfd = New SaveFileDialog()
            sfd.FileName = file.FileName
            If sfd.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                Try
                    System.IO.File.Copy(file.FilePath, sfd.FileName)
                Catch ex As Exception
                    DisplayErrorMessage("Error saving file", ex)
                End Try
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving file.", ex)
        End Try
    End Sub

    Private Sub DeleteFile(file As ucRecentReports.RecentFiles, row As view_AccountLead)
        Try
            If XtraMessageBox.Show("Are you sure you would like to delete this file?", "Delete?", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                System.IO.File.Delete(file.FilePath)
                LoadFiles()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting file.", ex)
        End Try
    End Sub

    Private Sub cbeStatus_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles cbeStatus.EditValueChanging
        If _AccountLeads IsNot Nothing AndAlso e.NewValue <> _AccountLeads.Status Then
            e.Cancel = Not _AccountLeadsDataService.UpdateStatus(_AccountLeads, e.NewValue)
        End If
    End Sub

    Private Sub MemoEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles MemoEdit1.EditValueChanged
        SetEnrollmentInfo()
    End Sub

    Private Sub SetEnrollmentInfo()
        Try
            If _Enrollment IsNot Nothing Then
                LabelControl1.Text = MemoEdit1.Text.FormatWith(_Enrollment.EnrollmentID,
                                                               _Enrollment.create_date,
                                                               _Enrollment.last_modified,
                                                               _Enrollment.email,
                                                               _Enrollment.conum,
                                                               _Enrollment.empnum,
                                                               _Enrollment.workflow,
                                                               _Enrollment.company_name,
                                                               _Enrollment.company_street,
                                                               _Enrollment.company_city,
                                                               _Enrollment.company_state,
                                                               _Enrollment.company_zip,
                                                               _Enrollment.company_phone,
                                                               _Enrollment.company_fax,
                                                               _Enrollment.employee_first,
                                                               _Enrollment.employee_last,
                                                               _Enrollment.employee_phone,
                                                               _Enrollment.company_fedid,
                                                               _Enrollment.routing,
                                                               _Enrollment.account,
                                                               _Enrollment.create_date.Humanize(False),
                                                               _Enrollment.last_modified.Humanize(False))
            Else
                LabelControl1.Text = "No Enrollment"
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub LabelControl1_HyperlinkClick(sender As Object, e As DevExpress.Utils.HyperlinkClickEventArgs) Handles LabelControl1.HyperlinkClick

    End Sub
End Class