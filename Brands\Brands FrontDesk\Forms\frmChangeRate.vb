﻿Imports System.ComponentModel

Public Class frmChangeRate

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmpRow As EMPLOYEE
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsSalary As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property OldRate As Decimal?
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PayCode As Decimal

    Private Sub frmChangeRate_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If IsSalary Then
            Me.Text = "Update Employees Salary"
            Me.lblTitle.Text = "New Salary:"
            Me.lblTitel2.Text = "Old Salary:"
            Me.tbOldRate.EditValue = If(EmpRow.SALARY_AMT Is Nothing, "None", EmpRow.SALARY_AMT.Value.ToString)
        Else
            Me.Text = "Update Employees Rate"
            Me.lblTitle.Text = "New Rate:"
            Me.lblTitel2.Text = "Old Rate:"
            Me.tbOldRate.EditValue = If(OldRate.HasValue, OldRate.Value.ToString("N2"), "None")
        End If
    End Sub

    Private Sub OK_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OK_Button.Click
        Try
            Dim DB As New dbEPDataDataContext(GetConnectionString)
            Dim Emp = (From A In DB.EMPLOYEEs Where A.CONUM = EmpRow.CONUM AndAlso A.EMPNUM = EmpRow.EMPNUM).Single
            If Not IsSalary Then
                If PayCode = 0 Then
                    Dim RatesList As New List(Of Decimal?)({EmpRow.RATE_1, EmpRow.RATE_2, EmpRow.RATE_3})
                    Dim RateNum = RatesList.IndexOf(OldRate) + 1
                    If RateNum = 0 Then RateNum = 1
                    Select Case RateNum
                        Case 1
                            Emp.RATE_1 = Decimal.Parse(Me.NewRate.EditValue)
                        Case 2
                            Emp.RATE_2 = Decimal.Parse(Me.NewRate.EditValue)
                        Case 3
                            Emp.RATE_3 = Decimal.Parse(Me.NewRate.EditValue)
                    End Select
                Else
                    Dim OPRow = (From A In DB.EMP_OPS Where A.CONUM = EmpRow.CONUM AndAlso A.EMPNUM = EmpRow.EMPNUM AndAlso A.OPS_NUM = PayCode).FirstOrDefault
                    If OPRow IsNot Nothing Then
                        OPRow.op_rate = Decimal.Parse(Me.NewRate.EditValue)
                    End If
                End If
            Else
                Emp.SALARY_AMT = Decimal.Parse(Me.NewRate.EditValue)
            End If
            DB.SubmitChanges()
            DB.Dispose()
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error changing rate", ex)
        End Try
    End Sub

    Private Sub Cancel_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Button.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
    End Sub

End Class
