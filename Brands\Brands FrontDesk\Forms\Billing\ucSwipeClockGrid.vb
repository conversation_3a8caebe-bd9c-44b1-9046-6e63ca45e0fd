﻿Imports System.ComponentModel
Imports System.Text.RegularExpressions
Imports DevExpress.Data
Imports DevExpress.Spreadsheet
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Grid

Namespace Billing

    Public Class ucSwipeClockGrid
        Implements IBillingImportService

        Private IsAodMode As Boolean
        Private Property db As dbEPDataDataContext
        Private Property billingUtilities As BillingUtilities
        Private hasCapturedLayout As Boolean = False

        Private regGridKey As String = "BrandsFrontDesktop\ucSwipeClock\XtraGrid\Layouts\MainLayout"

        Public SwipeClockData As List(Of SwipeClock)

        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property PriceCodesList As List(Of Integer) Implements IBillingImportService.PriceCodesList
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property globalBilling As List(Of ACT_ITEM) Implements IBillingImportService.globalBilling
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property InvoiceDate As DateTime Implements IBillingImportService.InvoiceDate

        Public ReadOnly Property Records As List(Of IRecord) Implements IBillingImportService.Records
            Get
                Return SwipeClockData.OfType(Of IRecord).ToList
            End Get
        End Property

        Public Event SelectedRowChanged(row As IRecord) Implements IBillingImportService.SelectedRowChanged
        Public Event SelectionChanged() Implements IBillingImportService.SelectionChanged


        Public Sub New(_IsAodMode As Boolean)
            IsAodMode = _IsAodMode
            InitializeComponent()
            Initialize()
            SetGridMode()
        End Sub

        Public Sub Initialize() Implements IBillingImportService.Initialize
            db = New dbEPDataDataContext(GetConnectionString)
            PriceCodesList = New List(Of Integer)([Enum].GetValues(GetType(PriceCodes)))
            globalBilling = db.ACT_ITEMS.Where(Function(b) PriceCodesList.Contains(b.ITEM_NUM)).ToList
            billingUtilities = New BillingUtilities(Me)
        End Sub

        Public Sub ImportFile() Implements IBillingImportService.ImportFile
            If IsAodMode Then
                ImportAodFile()
            Else
                ImportSwipeClockFile()
            End If
            GC.Collect()
        End Sub

        Private Sub ImportSwipeClockFile()
            Dim fd = New OpenFileDialog()
            If fd.ShowDialog() = DialogResult.OK Then
                Dim book = New Workbook()
                book.Options.Import.ThrowExceptionOnInvalidDocument = True
                book.LoadDocument(fd.FileName)
                Dim sheet = book.Worksheets.ActiveWorksheet
                Dim list = New List(Of SwipeClock)
                For index = 1 To sheet.Rows.LastUsedIndex
                    Dim row = sheet.Rows.Item(index)
                    Dim Description As String, ExtDescription As String, RowType As String, Client As String, CoNum As Decimal

                    Description = row(2).DisplayText
                    RowType = If(Description = "Services", "SC", If(Description.EndsWith("Saas Services-Hub"), "HUB", If(Description.EndsWith("TWT Clock Activation Fee") OrElse Description.EndsWith("Timeclock Activation"), "SCA", If(Description.Contains("Monthly Hosted VoiceClock fee"), "VC", If(Description.StartsWith("Custom Scripting"), "CS", "")))))
                    ExtDescription = row(7).DisplayText.Replace(vbCrLf, " ")
                    Client = row(8).DisplayText
                    CoNum = row(9).Value.NumericValue
                    If CoNum = 0 AndAlso ExtDescription Like "*-*;*" Then
                        Dim grps = New System.Text.RegularExpressions.Regex("-(.+?);").Match(ExtDescription).Groups

                        If grps.Count > 1 Then
                            Client = grps(1).Value.Trim()
                            Dim clientInfo = db.COMPANies.Where(Function(c) c.CO_NAME = Client).FirstOrDefault()
                            If clientInfo IsNot Nothing Then
                                CoNum = clientInfo.CONUM
                            End If
                        Else
                            Client = ExtDescription
                        End If
                    ElseIf CoNum = 0 AndAlso (ExtDescription Like "Client* s/n:*" OrElse RowType = "CS") Then
                        Dim grps As GroupCollection

                        If RowType = "CS" Then
                            grps = New Regex("(.+)\sSite:\s\d+").Match(ExtDescription).Groups
                        Else
                            grps = New Regex("Client:\s(.+?)\ss/n:").Match(ExtDescription).Groups
                        End If

                        If grps.Count > 1 Then
                            Client = grps(1).Value.Trim()
                            Dim clientInfo = db.COMPANies.Where(Function(c) c.CO_NAME = Client).FirstOrDefault()
                            If clientInfo IsNot Nothing Then
                                CoNum = clientInfo.CONUM
                            End If
                        Else
                            Client = ExtDescription
                        End If
                    ElseIf CoNum = 0 AndAlso RowType = "CS" AndAlso ExtDescription Like "*: [0-9]*" Then

                    End If

                    Dim sc As SwipeClock = Nothing

                    If RowType = "HUB" Then
                        Dim haveRec = list.Where(Function(f) f.Conum = CoNum AndAlso f.RowType = RowType).FirstOrDefault()
                        If haveRec IsNot Nothing Then
                            sc = haveRec
                        Else
                            sc = New SwipeClock()
                        End If

                        If Not ExtDescription.Contains("Employee Count") Then
                            sc.TotalAmount += row(6).Value.NumericValue
                            If haveRec Is Nothing Then
                                sc.RowType = RowType
                                sc.Client = Client
                                sc.Conum = CoNum
                                sc.ExtDescription = ""
                                list.Add(sc)
                            End If
                            Continue For
                        End If
                    Else
                        sc = New SwipeClock()

                        If RowType = "SCA" Then
                            sc.Notes = "not billed"
                        End If
                    End If

                    sc.RowType = RowType
                    sc.Client = Client

                    sc.Conum = CoNum

                    If sc.Notes Is Nothing AndAlso sc.Conum < 1000 Then
                        sc.Notes = "Company less than 1000"
                    End If

                    sc.LineItem = row(0).DisplayText
                    sc.ItemDate = row(1).Value.DateTimeValue
                    sc.Description = row(2).DisplayText
                    sc.Rate = row(4).Value.NumericValue
                    sc.TaxRate = row(5).Value.NumericValue

                    If sc.RowType = "CS" Then
                        sc.TotalAmount = sc.Rate
                    Else
                        sc.TotalAmount += row(6).Value.NumericValue 'if HUB we add to prev row
                    End If

                    sc.ExtDescription = ExtDescription
                    sc.Employees = If(sc.RowType = "HUB" OrElse sc.RowType = "VC", row(3).Value.NumericValue, row(10).Value.NumericValue)
                    sc.Clocks = Math.Ceiling(row(11).Value.NumericValue)

                    If sc.RowType = "" Then
                        sc.Client += " - " + sc.Description + " - " + sc.ExtDescription
                    ElseIf sc.RowType = "SCA" Then
                        Dim serialStart = sc.ExtDescription.LastIndexOf(":")
                        Dim serialClock As String = If(serialStart >= 0 AndAlso ExtDescription.Length > serialStart, sc.ExtDescription.Substring(serialStart + 1, sc.ExtDescription.Length - serialStart - 1), "")

                        If serialClock <> "" Then
                            sc.Client += " - " + serialClock

                            Dim clockInfo = db.SuppliesInventories.Where(Function(f) f.SerialNumber = serialClock.Trim()).FirstOrDefault()

                            If clockInfo IsNot Nothing Then
                                sc.Client += " - " + clockInfo.Log
                            End If
                        End If
                    End If

                    sc.tmp_LineItem = row(0).DisplayText
                    sc.tmp_ItemDate = row(1).DisplayText
                    sc.tmp_Description = row(2).DisplayText
                    sc.tmp_Quantity = row(3).DisplayText
                    sc.tmp_Rate = row(4).DisplayText
                    sc.tmp_TaxRate = row(5).DisplayText
                    sc.tmp_TotalAmount = row(6).DisplayText
                    sc.tmp_ExtDescription = row(7).DisplayText.Replace(vbCrLf, " ")
                    sc.tmp_Client = row(8).DisplayText
                    sc.tmp_ClientTag = row(9).DisplayText
                    sc.tmp_Employees = row(10).DisplayText
                    sc.tmp_Clocks = row(11).DisplayText

                    list.Add(sc)
                Next

                'if has HUB with SC then add hub scBilled to SC and set emp count to bigger one, don't bill for hub
                Dim Hub_with_SC_clients = (From s In list Join h In list On h.Conum Equals s.Conum Where s.RowType = "SC" AndAlso h.RowType = "HUB" Select s.Conum Distinct).ToList()
                Dim co As Decimal, scSC As SwipeClock, scHubs As List(Of SwipeClock), scHub As SwipeClock
                For Each co In Hub_with_SC_clients
                    scSC = list.Where(Function(f) f.Conum = co AndAlso f.RowType = "SC").FirstOrDefault()
                    scHubs = list.Where(Function(f) f.Conum = co AndAlso f.RowType = "HUB").ToList()
                    For Each scHub In scHubs
                        scSC.TotalAmount += scHub.TotalAmount
                        scHub.BilledInAnotherRow = True
                        scHub.Notes = "Billed in SC row"
                        If scHub.Employees > scSC.Employees Then
                            scSC.Employees = scHub.Employees
                        End If
                    Next
                Next

                Dim FindPrevItem = list.FirstOrDefault(Function(l) l.RowType <> "")
                If FindPrevItem IsNot Nothing Then
                    InvoiceDate = FindPrevItem.ItemDate
                    InvoiceDate = InvoiceDate.AddDays(DateTime.DaysInMonth(InvoiceDate.Year, InvoiceDate.Month) - InvoiceDate.Day) 'get last day of month
                End If

                Dim LastCapturedInvoice = Query(Of Date)($"select top 1 * from custom.SwipeClockData where Dtm < '{InvoiceDate}' order by dtm desc").FirstOrDefault()

                Dim LastInvoiceRevisedExtDescriptions As New List(Of String)

                If Not LastCapturedInvoice = Nothing Then
                    MessageBox.Show($"Compared with previous invoice on {LastCapturedInvoice.ToShortDateString()}")
                    LastInvoiceRevisedExtDescriptions = Query(Of String)($"select ExtDescriptionRevised from custom.SwipeClockData where Dtm = '{LastCapturedInvoice}'").ToList()
                End If

                For Each sc In list.Where(Function(c) c.Conum <> 0)
                    If db.COMPANies.Where(Function(f) f.CONUM = sc.Conum).Count = 0 Then
                        Throw New Exception($"CoNum {sc.Conum} not found.  Please correct and import again.")
                    End If
                    sc.CO_BILL_FREQ = If(db.COOPTIONs.Where(Function(f) f.CONUM = sc.Conum).FirstOrDefault().CO_BILL_FREQ, "")

                    Dim lastInvNum = db.invoice_masters.Where(Function(f) f.conum = sc.Conum).Max(Function(m) m.invoice_number)
                    sc.AftClcMonInvNum = If(sc.CO_BILL_FREQ = "Per Payroll" OrElse lastInvNum Is Nothing, 0, lastInvNum)

                    If sc.ExtDescription.Contains("$") AndAlso LastInvoiceRevisedExtDescriptions.Count <> 0 Then
                        If LastInvoiceRevisedExtDescriptions.IndexOf(sc.BillingItems) = -1 Then
                            sc.Modified = True
                        End If
                    End If
                Next

                SwipeClockData = list.OrderBy(Function(f) f.Client).ThenBy(Function(f2) f2.RowType).ToList()
            End If
        End Sub

        Private Sub ImportAodFile()
            Dim fd = New OpenFileDialog()
            If fd.ShowDialog <> DialogResult.OK Then Exit Sub
            Dim book = New Workbook()
            book.Options.Import.ThrowExceptionOnInvalidDocument = True
            If Not book.LoadDocument(fd.FileName) Then Exit Sub
            Dim sheet = book.Worksheets.ActiveWorksheet
            Dim list = New List(Of SwipeClock)

            For index = 1 To sheet.Rows.LastUsedIndex
                Dim row = sheet.Rows.Item(index)
                Dim exisitngRow = list.SingleOrDefault(Function(aod) aod.Client = row(1).DisplayText.Trim())
                If exisitngRow IsNot Nothing Then
                    If row(7).DisplayText = "Active Employees" Then
                        exisitngRow.Employees = row(9).Value.NumericValue
                    ElseIf row(7).DisplayText = "Standard User Accounts" Then
                        exisitngRow.AdditionalUsers = row(9).Value.NumericValue
                    End If
                ElseIf row(1).DisplayText.IsNotNullOrWhiteSpace Then
                    Dim sc = New SwipeClock()
                    sc.Description = row(0).DisplayText
                    sc.Client = row(1).DisplayText.Trim
                    sc.ItemDate = row(4).Value.DateTimeValue
                    sc.Conum = If((From c In db.CoOptions_Payrolls Where c.AodCode = sc.Client).SingleOrDefault()?.CoNum, 0)

                    If row(7).DisplayText = "Active Employees" Then
                        sc.Employees = row(9).Value.NumericValue
                    ElseIf row(7).DisplayText = "Standard User Accounts" Then
                        sc.AdditionalUsers = row(9).Value.NumericValue
                    End If
                    sc.RowType = "AoD"
                    sc.TotalAmount = row(10).Value.NumericValue
                    list.Add(sc)
                End If
            Next
            'SwipeClockData = list
            InvoiceDate = list.FirstOrDefault()?.ItemDate.AddDays(-1)

            For Each sc In list.Where(Function(c) c.Conum <> 0)
                sc.CO_BILL_FREQ = If(db.COOPTIONs.Where(Function(f) f.CONUM = sc.Conum).FirstOrDefault().CO_BILL_FREQ, "")

                Dim lastInvNum = db.invoice_masters.Where(Function(f) f.conum = sc.Conum).Max(Function(m) m.invoice_number)
                sc.AftClcMonInvNum = If(sc.CO_BILL_FREQ = "Per Payroll" OrElse lastInvNum Is Nothing, 0, lastInvNum)
            Next

            SwipeClockData = list.OrderBy(Function(f) f.Client).ThenBy(Function(f2) f2.RowType).ToList()
        End Sub

        Public Sub CalculatePrices() Implements IBillingImportService.CalculatePrices
            If SwipeClockData Is Nothing Then Exit Sub
            db = New dbEPDataDataContext(GetConnectionString)

            'Dim MinimumSwipeClockPriceForEmployee As Decimal = 12
            'If Not Decimal.TryParse(Nz(GetUdfValue("MinimumSwipeClockPriceForEmployee"), ""), MinimumSwipeClockPriceForEmployee) Then
            '    MinimumSwipeClockPriceForEmployee = 12
            'End If

            Dim invoiceLogs = (From i As invoice_item_detail In db.invoice_item_details Join m As invoice_master In db.invoice_masters On m.conum Equals i.conum And m.invoice_key Equals i.invoice_key Where PriceCodesList.Contains(i.item_num) AndAlso i.item_date.Value.Year = InvoiceDate.Year AndAlso (i.item_deleted = 0 OrElse i.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing) Select i).ToList
            Dim BaseFeeItemNum As Int16 = 0, EmployeesFee As Int16 = 0, MinHubEmployeeFee As Decimal = 0, MinHubEmployeeFeeDefault = 0
            Dim MinClockFeeDefault As Decimal = 0, MinSwipeClockFeeDefault As Decimal = 0, CustomScriptTimeclock As Decimal = 0, MinCustomScriptTimeclockDefault As Decimal = 0

            MinHubEmployeeFeeDefault = db.ACT_ITEMS.Where(Function(f) f.ITEM_NUM = PriceCodes.HubMinimum).FirstOrDefault().STD_PRICE
            MinClockFeeDefault = db.ACT_ITEMS.Where(Function(f) f.ITEM_NUM = PriceCodes.MinClockFee).FirstOrDefault().STD_PRICE
            MinSwipeClockFeeDefault = db.ACT_ITEMS.Where(Function(f) f.ITEM_NUM = PriceCodes.MinSwipeClockFee).FirstOrDefault().STD_PRICE
            MinCustomScriptTimeclockDefault = db.ACT_ITEMS.Where(Function(f) f.ITEM_NUM = PriceCodes.CustomScriptTimeclock).FirstOrDefault().STD_PRICE

            For Each item In SwipeClockData
                If item.Conum > 0 Then
                    If item.RowType = "SC" OrElse item.RowType = "AoD" Then
                        BaseFeeItemNum = PriceCodes.BaseFee
                        EmployeesFee = PriceCodes.EmployeesFee
                    ElseIf item.RowType = "HUB" Then
                        BaseFeeItemNum = PriceCodes.HubBaseFee
                        EmployeesFee = PriceCodes.HubEmployeeFee
                        MinHubEmployeeFee = If(db.ACT_AUTO_PriceOverrides.Where(Function(f) f.ITEM_NUM = PriceCodes.HubMinimum AndAlso f.CONUM = item.Conum).SingleOrDefault()?.ACTUAL_PRICE, MinHubEmployeeFeeDefault)
                    ElseIf item.RowType = "SCA" Then
                        BaseFeeItemNum = PriceCodes.ScaBaseFee
                    ElseIf item.RowType = "VC" Then
                        EmployeesFee = PriceCodes.VCEmployeeFee
                    ElseIf item.RowType = "AoD" Then
                        EmployeesFee = PriceCodes.EmployeesFee
                    ElseIf item.RowType = "CS" Then
                        CustomScriptTimeclock = PriceCodes.CustomScriptTimeclock
                    Else
                        Continue For
                    End If

                    'The base fee is only charged if it has been overriten
                    Dim z = db.ACT_AUTO_PriceOverrides.Where(Function(a) a.CONUM = item.Conum AndAlso a.ITEM_NUM = BaseFeeItemNum).SingleOrDefault

                    If item.RowType = "SCA" Then
                        Dim baseFee As Decimal = If(z IsNot Nothing, z.ACTUAL_PRICE, db.ACT_ITEMS.Where(Function(f) f.ITEM_NUM = BaseFeeItemNum).FirstOrDefault().STD_PRICE)
                        item.PriceForBaseFee = baseFee
                    ElseIf item.RowType = "CS" Then
                        item.PriceForBaseFee = item.Rate * 3

                        If item.PriceForBaseFee < MinCustomScriptTimeclockDefault Then
                            item.PriceForBaseFee = MinCustomScriptTimeclockDefault
                        End If
                    ElseIf item.RowType <> "HUB" AndAlso item.RowType <> "VC" Then
                        item.PriceForBaseFee = If(z?.ACTUAL_PRICE, 0)
                        Dim MinClockFee = If(db.ACT_AUTO_PriceOverrides.Where(Function(f) f.ITEM_NUM = PriceCodes.MinClockFee AndAlso f.CONUM = item.Conum).SingleOrDefault()?.ACTUAL_PRICE, MinClockFeeDefault)

                        'when there's a base fee we don't charge per clock, unless it has been overriten
                        If item.PriceForBaseFee > 0 Then
                            'item.PriceForClocks = If((From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = item.Conum AndAlso b.ITEM_NUM = PriceCodes.ClocksFee).SingleOrDefault?.ACTUAL_PRICE * item.Clocks, 0)
                            item.PriceForBaseFee += If((From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = item.Conum AndAlso b.ITEM_NUM = PriceCodes.ClocksFee).SingleOrDefault?.ACTUAL_PRICE * item.Clocks, 0)
                        Else
                            'item.PriceForClocks = billingUtilities.GetPrice(db, item.Conum, PriceCodes.ClocksFee, item.Clocks)
                            item.PriceForBaseFee += billingUtilities.GetPrice(db, item.Conum, PriceCodes.ClocksFee, item.Clocks)
                        End If

                        If item.PriceForBaseFee < MinClockFee Then
                            item.PriceForBaseFee = MinClockFee
                        End If
                    End If

                    If EmployeesFee <> 0 Then
                        item.PriceForEmployees = billingUtilities.GetPrice(db, item.Conum, EmployeesFee, item.Employees)
                    End If

                    Dim MinimumSwipeClockPriceForEmployee As Decimal = If(db.ACT_AUTO_PriceOverrides.Where(Function(f) f.ITEM_NUM = PriceCodes.MinSwipeClockFee AndAlso f.CONUM = item.Conum).SingleOrDefault()?.ACTUAL_PRICE, MinSwipeClockFeeDefault)

                    'If item.RowType = "SC" AndAlso item.PriceForEmployees < MinimumSwipeClockPriceForEmployee Then 'minimum price for employees fee
                    '    item.PriceForEmployees = MinimumSwipeClockPriceForEmployee
                    'End If

                    If item.RowType = "SC" AndAlso item.PriceForEmployees + item.PriceForClocks + item.PriceForBaseFee < MinimumSwipeClockPriceForEmployee Then 'minimum price for employees fee
                        item.PriceForEmployees = MinimumSwipeClockPriceForEmployee - item.PriceForClocks - item.PriceForBaseFee
                    End If

                    If IsAodMode Then
                        item.PriceForAdditionalUsers = billingUtilities.GetPrice(db, item.Conum, PriceCodes.AdditionalUsers, item.AdditionalUsers)
                    End If

                    If item.BilledInAnotherRow OrElse item.RowType = "SCA" Then
                        item.TotalPrice = 0
                    Else
                        item.TotalPrice = item.PriceForBaseFee + item.PriceForEmployees + item.PriceForClocks + item.PriceForAdditionalUsers
                    End If

                    If item.RowType = "HUB" Then
                        Dim HubBaseFee As Decimal = db.ACT_ITEMS.Where(Function(f) f.ITEM_NUM = PriceCodes.HubBaseFee).FirstOrDefault().STD_PRICE
                        If z?.ACTUAL_PRICE <> 0 Then
                            HubBaseFee = z.ACTUAL_PRICE
                        End If

                        item.PriceForEmployees += HubBaseFee

                        If item.PriceForEmployees < MinHubEmployeeFee Then 'minimum price for employees fee
                            item.PriceForEmployees = MinHubEmployeeFee
                        End If

                        If item.BilledInAnotherRow Then
                            item.TotalPrice = 0
                        Else
                            item.TotalPrice = item.PriceForEmployees
                        End If
                    End If

                    item.EmployeesInvoiced = billingUtilities.GetIsInvoiced(item.Conum, item.PriceForEmployees, EmployeesFee, invoiceLogs)
                    item.ClocksInvoiced = billingUtilities.GetIsInvoiced(item.Conum, item.PriceForClocks + item.PriceForBaseFee, If(item.RowType = "SCA", PriceCodes.ScaBaseFee, If(item.RowType = "CS", PriceCodes.CustomScriptTimeclock, PriceCodes.ClocksFee)), invoiceLogs)
                    item.AdditionalUsersInvoiced = billingUtilities.GetIsInvoiced(item.Conum, item.PriceForAdditionalUsers, PriceCodes.AdditionalUsers, invoiceLogs)
                    item.AllInvoiced = (Not item.EmployeesInvoiced.HasValue OrElse item.EmployeesInvoiced) AndAlso
                 (Not item.ClocksInvoiced.HasValue OrElse item.ClocksInvoiced) AndAlso
                 (Not IsAodMode OrElse (Not item.AdditionalUsersInvoiced.HasValue OrElse item.AdditionalUsersInvoiced))

                End If
            Next
        End Sub

        Public Function GetSelectedRow() As IRecord Implements IBillingImportService.GetSelectedRow
            Return GridView1.GetFocusedRow()
        End Function

        Public Function GetSelectedRows() As List(Of IRecord) Implements IBillingImportService.GetSelectedRows
            Dim selectedlist As List(Of IRecord) = GridView1.GetSelectedRows(Of IRecord)()
            Return selectedlist
        End Function

        Sub Gridview_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles GridView1.SelectionChanged
            RemoveHandler GridView1.SelectionChanged, AddressOf Gridview_SelectionChanged
            Dim row = CType(GridView1.GetFocusedRow(), SwipeClock)
            If row.TotalPrice = 0 AndAlso Array.IndexOf(GridView1.GetSelectedRows(), GridView1.FocusedRowHandle) > -1 Then
                GridView1.UnselectRow(GridView1.FocusedRowHandle)
            End If
            AddHandler GridView1.SelectionChanged, AddressOf Gridview_SelectionChanged
            RaiseEvent SelectionChanged()
        End Sub

        Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GridView1.FocusedRowObjectChanged
            Try
                RaiseEvent SelectedRowChanged(e.Row)
            Catch ex As Exception
                DisplayErrorMessage("Error loading company details", ex)
            End Try
        End Sub

        Sub RefreshData(ShowBilled As Boolean, ShowNonMatched As Boolean) Implements IBillingImportService.RefreshData
            If SwipeClockData Is Nothing Then
                Return
            End If

            Dim col As GridColumn

            If ShowNonMatched Then
                GridView1.OptionsSelection.MultiSelect = False
                GridView1.OptionsSelection.MultiSelectMode = GridMultiSelectMode.RowSelect
                GridView1.ActiveFilterString = "[RowType] = ''"

                For Each col In GridView1.Columns
                    col.Visible = False
                Next

                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_LineItem", .Caption = "LineItem", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_ItemDate", .Caption = "ItemDate", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_Description", .Caption = "Description", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_Quantity", .Caption = "Quantity", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_Rate", .Caption = "Rate", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_TaxRate", .Caption = "TaxRate", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_TotalAmount", .Caption = "TotalAmount", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_ExtDescription", .Caption = "ExtDescription", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_Client", .Caption = "Client", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_ClientTag", .Caption = "ClientTag", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_Employees", .Caption = "Employees", .Visible = True})
                GridView1.Columns.Add(New GridColumn With {.FieldName = "tmp_Clocks", .Caption = "Clocks", .Visible = True})
            Else
                GridView1.OptionsSelection.MultiSelect = True
                GridView1.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect
                GridView1.ActiveFilterString = "NOT [RowType] = ''"

                If hasCapturedLayout Then
                    While (True)
                        col = GridView1.Columns.Where(Function(f) f.FieldName.StartsWith("tmp_")).FirstOrDefault()
                        If col Is Nothing Then
                            Exit While
                        Else
                            GridView1.Columns.Remove(col)
                        End If
                    End While

                    GridControl1.MainView.RestoreLayoutFromRegistry(regGridKey)
                End If
            End If

            GridControl1.DataSource = (From s In SwipeClockData Where ShowBilled OrElse Not s.AllInvoiced).ToList()
            colIsPriceForEmployeesInvoiced.Visible = ShowBilled
            'colIsPriceForClocksInvoiced.Visible = ShowBilled AndAlso Not IsAodMode
            colIsAllInvoiced.Visible = ShowBilled
            AddSummaryToColumns()
            GridView1.BestFitColumns()

            If Not hasCapturedLayout Then
                GridControl1.MainView.SaveLayoutToRegistry(regGridKey)
                hasCapturedLayout = True
            End If
        End Sub

        Private Sub SetGridMode()
            colConum.Visible = True
            colClient.Visible = True
            colAdditionalUsers.Visible = IsAodMode
            colPriceForAdditionalUsers.Visible = IsAodMode
            colIsPriceForAdditionalUsersInvoiced.Visible = IsAodMode
            'colPriceForClocks.Visible = Not IsAodMode
            colClocks.Visible = Not IsAodMode
            'colPriceForClocks.Visible = Not IsAodMode
            colIsPriceForClocksInvoiced.Visible = Not IsAodMode
            If Not IsAodMode Then
                GridView1.ActiveFilterString = "NOT [RowType] = ''"
            End If
        End Sub

        Sub AddSummaryToColumns()
            Dim col As GridColumn
            For Each col In GridView1.Columns
                If col Is colPriceForEmployees OrElse col Is colPriceForClocks OrElse col Is colPriceForBaseFee OrElse col Is colTotalPrice OrElse col Is colTotalAmount OrElse col Is colPriceForAdditionalUsers Then
                    If col.Summary.Count = 0 Then
                        col.Summary.AddRange(New GridSummaryItem() {New GridColumnSummaryItem(SummaryItemType.Sum, SummaryMode.AllRows, col.FieldName, "{0}")})
                    End If
                End If
            Next
        End Sub

        Public Enum PriceCodes
            BaseFee = 450
            EmployeesFee = 358
            ClocksFee = 371
            AdditionalUsers = 419
            HubBaseFee = 479
            HubEmployeeFee = 467
            HubMinimum = 478
            ScaBaseFee = 426
            VCEmployeeFee = 422
            MinClockFee = 35
            MinSwipeClockFee = 36
            CustomScriptTimeclock = 447
        End Enum

        Public Class SwipeClock
            Implements IRecord

            Private regRep As New System.Text.RegularExpressions.Regex("\d|\n|\r|\f|[1-9]", System.Text.RegularExpressions.RegexOptions.Compiled)
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property RowType As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Conum As Decimal Implements IRecord.Conum
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property LineItem As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property ItemDate As DateTime
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Description As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Rate As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property TaxRate As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property TotalAmount As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property ExtDescription As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Client As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Employees As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Clocks As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property AdditionalUsers As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property PriceForEmployees As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property PriceForClocks As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property PriceForBaseFee As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property PriceForAdditionalUsers As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property TotalPrice As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property EmployeesInvoiced As Boolean?
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property ClocksInvoiced As Boolean?
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property AdditionalUsersInvoiced As Boolean?
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property AllInvoiced As Boolean Implements IRecord.IsAllInvoiced
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property CO_BILL_FREQ As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property AftClcMonInvNum As Decimal?
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Notes As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property BilledInAnotherRow As Boolean
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_LineItem As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_ItemDate As DateTime
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_Description As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_Quantity As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_Rate As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_TaxRate As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_TotalAmount As Decimal
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_ExtDescription As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_Client As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_ClientTag As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_Employees As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property tmp_Clocks As String
            <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
            Property Modified As Boolean

            ReadOnly Property TagInvNum As Boolean
                Get
                    Return AftClcMonInvNum = 0 AndAlso CO_BILL_FREQ = "Per Month"
                End Get
            End Property

            ReadOnly Property Markup As Decimal
                Get
                    Return If(TotalAmount = 0, 0, TotalPrice / TotalAmount)
                End Get
            End Property

            Public Function BillingItems() As String
                Dim ret = Replace(ExtDescription, "'", "''")

                ret = regRep.Replace(ret, "")
                ret = Replace(ret, "Clocks", "Clock")
                ret = Replace(ret, "Employees", "Employee")
                ret = Replace(ret, ".Employee: $.Clock:", ".Clock: $.Employee:")
                Return ret
            End Function
        End Class

        Public Sub InvoiceRow(record As IRecord) Implements IBillingImportService.InvoiceRow
            Dim row = DirectCast(record, SwipeClock)
            Dim EmployeesFee As Decimal = 0
            Dim BasicFee As Decimal = 0
            If row.RowType = "SC" Then
                EmployeesFee = PriceCodes.EmployeesFee
                BasicFee = PriceCodes.ClocksFee
            ElseIf row.RowType = "HUB" Then
                EmployeesFee = PriceCodes.HubEmployeeFee
                BasicFee = PriceCodes.HubBaseFee
            ElseIf row.RowType = "SCA" Then
                BasicFee = PriceCodes.ScaBaseFee
            ElseIf row.RowType = "VC" Then
                BasicFee = PriceCodes.VCEmployeeFee
            ElseIf row.RowType = "AoD" Then
                EmployeesFee = PriceCodes.EmployeesFee
                BasicFee = PriceCodes.ClocksFee
            ElseIf row.RowType = "CS" Then
                BasicFee = PriceCodes.CustomScriptTimeclock
            End If

            If Not row.EmployeesInvoiced AndAlso row.PriceForEmployees > 0 And row.RowType <> "VC" Then
                billingUtilities.InsertInvoiceLog(row.Conum, EmployeesFee, row.PriceForEmployees, row.Employees)
            ElseIf Not row.EmployeesInvoiced AndAlso row.PriceForEmployees > 0 And row.RowType = "VC" Then
                billingUtilities.InsertInvoiceLog(row.Conum, BasicFee, row.PriceForEmployees, row.Employees)
            End If

            If Not row.ClocksInvoiced AndAlso (row.PriceForClocks > 0 OrElse row.PriceForBaseFee > 0) Then
                billingUtilities.InsertInvoiceLog(row.Conum, BasicFee, row.PriceForClocks + row.PriceForBaseFee, row.Clocks)
            End If
            If IsAodMode Then
                If Not row.AdditionalUsersInvoiced AndAlso row.PriceForAdditionalUsers > 0 Then
                    billingUtilities.InsertInvoiceLog(row.Conum, PriceCodes.AdditionalUsers, row.PriceForAdditionalUsers, row.AdditionalUsers)
                End If
            End If
        End Sub
    End Class
End Namespace