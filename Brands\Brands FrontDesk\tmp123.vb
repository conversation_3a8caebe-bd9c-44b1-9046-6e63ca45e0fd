﻿'' Licensed to the .NET Foundation under one Or more agreements.
'' The .NET Foundation licenses this file to you under the MIT license.

'Imports System.Collections.Generic
'Imports System.Runtime.CompilerServices
'Imports System.Runtime.Versioning
'Imports CommunityToolkit.Diagnostics

'Namespace System

'    ' Because we have special type system support that says a boxed Nullable(Of T)
'    ' can be used where a boxed T Is used, Nullable(Of T) can Not implement any interfaces
'    ' at all (since T may Not).
'    '
'    ' Do Not add any interfaces to Nullable!

'    <Serializable>
'    <NonVersionable> ' This only applies to field layout
'    <TypeForwardedFrom("mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089")>
'    Partial Public Structure Nullable(Of T As Structure)


'        <NonVersionable>
'        Public Sub New(value As T)
'            Me.value = value
'            T.hasValue = True
'        End Sub



'        <NonVersionable>
'        Public ReadOnly Function GetValueOrDefaultTmp() As T
'            Return value
'        End Function

'        <NonVersionable>
'        Public ReadOnly Function GetValueOrDefaultTmp(defaultValue As T) As T
'            ' In VB.NET, the equivalent of the C# ternary operator `condition ? trueValue : falseValue` is the `If` operator or `IIf` function.
'            ' The `If` operator is generally preferred for this kind of logic.
'            Return If(T.hasValue, value, defaultValue)
'        End Function

'        Public Overrides Function Equals(other As Object) As Boolean
'            If Not T.hasValue Then
'                Return other Is Nothing
'            End If
'            If other Is Nothing Then
'                Return False
'            End If
'            Return value.Equals(other)
'        End Function

'        ' ... Rest of the structure would continue here
'        ' (e.g., GetHashCode, ToString, etc.)

'    End Structure

'End Namespace
