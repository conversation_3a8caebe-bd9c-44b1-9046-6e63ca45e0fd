﻿Imports DevExpress.XtraEditors
Imports Microsoft.EntityFrameworkCore

Public Class frmCheckFigures

    Private ReadOnly _coNum As Decimal
    Private db As dbEPDataDataContext
    Private Emp As EMPLOYEE
    Private EmpsList As List(Of EMPLOYEE)
    Private manChkMast As MAN_CHK_MAST
    Private localCodes As List(Of LOCAL_EE_INFO)
    Private IsEditMode As Boolean

    Public Sub New(_coNum As Decimal, Optional EmpNum As Decimal? = Nothing, Optional ChkCntr As Decimal? = Nothing)
        InitializeComponent()
        Me._coNum = _coNum
        Text = "Manual Check - Co#: {0}".FormatWith(_coNum)

        db = New dbEPDataDataContext(GetConnectionString)
        If EmpNum.HasValue AndAlso ChkCntr.HasValue Then
            manChkMast = (From A In db.MAN_CHK_MASTs Where A.CONUM = _coNum AndAlso A.EMPNUM = EmpNum AndAlso A.CHK_COUNTER = ChkCntr).FirstOrDefault
            If manChkMast Is Nothing Then
                Throw New Exception("Check not found")
            End If
            IsEditMode = True
        Else
            manChkMast = New MAN_CHK_MAST
            manChkMast.CONUM = _coNum
            manChkMast.PAYROLL_NUM = -1
            manChkMast.check_date = Today
            manChkMast.rowguid = Guid.NewGuid()
            manChkMast.HIRE_Act_Eligible = "NO"
            manChkMast.acc_sick = "YES"
            manChkMast.acc_vac = "YES"
            manChkMast.acc_per = "YES"
            manChkMast.CHK_TYPE = "MANUAL"
            manChkMast.CHRG4CHK = "NO"
            manChkMast.exclude_from_special = "NO"
            manChkMast.mpwStatus = "New"
            manChkMast.CHK_NUM = -1 'populated with trigger

            db.MAN_CHK_MASTs.InsertOnSubmit(manChkMast)
        End If
    End Sub


    Private Sub frmCheckFigures_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.TabbedControlGroup1.SelectedTabPageIndex = 0

        'bsDivisions.DataSource = db.DIVISIONs.Where(Function(d) d.CONUM = _coNum)
        Dim myDiv = db.DIVISIONs.Where(Function(c) c.CONUM = _coNum)

        myDiv.Load()
        bsDivisions.DataSource = db.DIVISIONs.Local.ToBindingList()
        'db.DIVISIONs.Where(Function(d) d.CONUM = _coNum).Load()
        'bsDivisions.DataSource = db.DIVISIONs.Local.ToBindingList().Where(Function(d) d.CONUM = _coNum)

        Dim employees = From A In db.EMPLOYEEs Where A.CONUM = _coNum
        If IsEditMode Then
            employees = From a In employees Where a.EMPNUM = manChkMast.EMPNUM
        End If
        EmpsList = employees.ToList
        If IsEditMode Then
            Emp = EmpsList.SingleOrDefault(Function(em) em.EMPNUM = manChkMast.EMPNUM)
        End If
        bsEmployeeList.DataSource = EmpsList

        bsManChkMast.DataSource = manChkMast
        bsManChkDetPay.DataSource = manChkMast.MAN_CHK_DET_PAYs
        bsManChkDetDed.DataSource = manChkMast.MAN_CHK_DET_DEDs
        bsManChkDetMemo.DataSource = manChkMast.MAN_CHK_DET_MEMOs

        Dim otherPays = db.OTHER_PAYs.Where(Function(o) o.CONUM = _coNum AndAlso o.OACTIVE = "YES").OrderBy(Function(o) o.OTH_PAY_NUM).ToList
        riOtherPays.DataSource = otherPays

        Dim Deds = db.DEDUCTIONs.Where(Function(p) p.CONUM = _coNum AndAlso p.DACTIVE = "YES").OrderBy(Function(p) p.DED_NUM).ToList
        riLookUpEditDeds.DataSource = Deds

        Dim memos = db.MEMOs.Where(Function(p) p.CONUM = _coNum AndAlso p.MACTIVE = "YES").OrderBy(Function(p) p.MEMO_NUM).ToList
        riLookUpEditMemos.DataSource = memos

        Dim jobs = (From A In db.CO_JOBs Where A.conum = _coNum AndAlso A.active = "YES" Order By A.job_descr).ToList
        riLookUpEditJob.DataSource = jobs

        GridViewPays.Columns("CL_DEPT").ColumnEdit = riLueDepartments
        GridViewPays.Columns("job_id").ColumnEdit = riLookUpEditJob
        GridViewPays.Columns("CL_CODE").ColumnEdit = riOtherPays

        GridViewLocals.Columns("CL_CODE").ColumnEdit = riOtherPays
        GridViewLocals.Columns("CL_DEPT").ColumnEdit = riLueDepartments
        GridViewLocals.Columns("CL_LOC_CD1").ColumnEdit = riLocalCode
        GridViewLocals.Columns("CL_LOC_CD2").ColumnEdit = riLocalCode
        GridViewLocals.Columns("CL_LOC_CD3").ColumnEdit = riLocalCode
        GridViewLocals.Columns("CL_LOC_CD4").ColumnEdit = riLocalCode
        GridViewLocals.Columns("CL_LOC_CD5").ColumnEdit = riLocalCode

        GridViewDeds.Columns("CL_DEPT").ColumnEdit = riLueDepartments
        GridViewDeds.Columns("job_id").ColumnEdit = riLookUpEditJob
        GridViewDeds.Columns("CL_OR_FLAT").ColumnEdit = riTextEditAmount
        GridViewDeds.Columns("CL_OR_PERCENT").ColumnEdit = riTextEditPercent

        GridViewMemos.Columns("CL_DEPT").ColumnEdit = riLueDepartments
        GridViewMemos.Columns("job_id").ColumnEdit = riLookUpEditJob
        GridViewMemos.Columns("CL_OR_FLAT").ColumnEdit = riTextEditAmount
        GridViewMemos.Columns("CL_OR_PERCENT").ColumnEdit = riTextEditPercent

        If IsEditMode Then
            slueEmps.EditValue = manChkMast.EMPNUM
        End If
        BindData()
    End Sub

    Private Sub BindData()
        Me.slueEmps.ReadOnly = IsEditMode
        Me.bbiDelete.Enabled = IsEditMode

        Dim localWH As Decimal? = (From A As MAN_CHK_DET_PAY In bsManChkDetPay.List Select A.CL_LOC_AMT1.GetValueOrDefault + A.OR_CL_LOC_AMT2.GetValueOrDefault + A.OR_CL_LOC_AMT3.GetValueOrDefault + A.OR_CL_LOC_AMT4.GetValueOrDefault + A.OR_CL_LOC_AMT4.GetValueOrDefault).Sum
        Me.txtLocalWH.EditValue = localWH
    End Sub

    Private Sub slueEmps_EditValueChanged(sender As Object, e As EventArgs) Handles slueEmps.EditValueChanged
        Dim slue As SearchLookUpEdit = sender
        If slue.EditValue Is Nothing OrElse slue.EditValue.ToString().IsNullOrWhiteSpace Then Exit Sub
        Emp = EmpsList.SingleOrDefault(Function(em) em.EMPNUM = slue.EditValue)
        If Emp Is Nothing Then Exit Sub
        If Emp.TERM_DATE.HasValue Then
            XtraMessageBox.Show("Terminated Employee", "Terminated Employee", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If

        Dim eeInfo = (From s In db.STATE_EE_INFOs Where s.CONUM = Emp.CONUM AndAlso s.EMPNUM = Emp.EMPNUM AndAlso (s.DEFAULT_WORK = "YES" OrElse s.DEFAULT_RES = "YES")).ToList
        localCodes = (From a In db.LOCAL_EE_INFOs Where a.conum = Emp.CONUM AndAlso a.empnum = Emp.EMPNUM AndAlso a.local_active = "YES" Order By If(a.default_position > 0, 1, 2)).ToList
        Dim LocalIds = (From A In localCodes Select A.local_id).Distinct.ToList
        Dim LocalDesc = (From A In db.LOCALs Where LocalIds.Contains(A.CODE) Select A.CODE, Display = A.CODE & "-" & A.DESCRIPTION).Distinct.ToList
        LocalDesc.Add(New With {Key .CODE = 0D, Key .Display = "0-None"})

        riLocalCode.DataSource = LocalDesc

        Dim States = eeInfo.Select(Function(p) p.STATE).Distinct.ToList
        txtSTATE_UCI.Properties.Items.Clear()
        txtSTATE_UCI.Properties.Items.AddRange(States)
        txtSTATE_WH.Properties.Items.Clear()
        txtSTATE_WH.Properties.Items.AddRange(States)
        txtSTATE_WH_RES.Properties.Items.Clear()
        txtSTATE_WH_RES.Properties.Items.AddRange(States)

        Dim locOverrides As New List(Of LocalOverrides)
        locOverrides.AddRange(From A In LocalIds Select New LocalOverrides With {.Code = A})
        Dim FirstRec = (From A As MAN_CHK_DET_PAY In Me.bsManChkDetPay Order By A.CL_NUM).FirstOrDefault
        If FirstRec IsNot Nothing Then
            For x = 1 To 5
                Dim Code As Decimal?, Amount As Decimal?
                Code = FirstRec.GetType.GetProperty("CL_LOC_CD" & x).GetValue(FirstRec)
                Amount = FirstRec.GetType.GetProperty("OR_CL_LOC_AMT" & x).GetValue(FirstRec)
                If Amount.HasValue AndAlso Code.HasValue Then
                    Dim rec = (From A In locOverrides Where A.Code = Code).FirstOrDefault
                    If rec IsNot Nothing Then
                        rec.Amount = Amount
                    End If
                End If
            Next
        End If
        Me.GridControlLocOvr.DataSource = locOverrides

        If Not IsEditMode Then
            manChkMast.DIVNUM = Emp.DIVNUM
            manChkMast.EMPNUM = Emp.EMPNUM
            manChkMast.STATE_UCI = Emp.UCI_STATE

            'Dim chkCntr = (From m In db.MAN_CHK_MASTs Where m.CONUM = Emp.CONUM AndAlso m.EMPNUM = Emp.EMPNUM Select New Decimal?(m.CHK_COUNTER)).Max
            Dim chkCntr = (From m In db.MAN_CHK_MASTs Where m.CONUM = Emp.CONUM AndAlso m.EMPNUM = Emp.EMPNUM Select New Decimal?(m.CHK_COUNTER)).ToList().Max
            manChkMast.CHK_COUNTER = If(chkCntr.HasValue, chkCntr.Value, 0) + 1
            manChkMast.import_id = manChkMast.CHK_COUNTER
            manChkMast.PAY_FREQ = Emp.PAY_FREQ

            manChkMast.STATE_WH = eeInfo.Where(Function(w) w.DEFAULT_WORK = "YES").FirstOrDefault?.STATE
            manChkMast.STATE_WH_RES = eeInfo.Where(Function(w) w.DEFAULT_RES = "YES").FirstOrDefault?.STATE

            If Emp.default_hours.GetValueOrDefault > 0 Then
                Dim pay As MAN_CHK_DET_PAY = Me.bsManChkDetPay.AddNew
                pay.CL_CODE = 0
                pay.CL_REG_HRS = Emp.default_hours
                pay.CL_DEPT = Emp.DEPTNUM
            End If

            If Emp.SALARY_AMT.GetValueOrDefault > 0 Then
                Dim pay As MAN_CHK_DET_PAY = Me.bsManChkDetPay.AddNew
                pay.CL_CODE = 0
                pay.CL_REG_PAY = Emp.SALARY_AMT
                pay.CL_DEPT = Emp.DEPTNUM
            End If

            'Auto pays/deds
            Dim PaysAndDed = db.prc_GetAutoPaysForPayroll(_coNum, Nothing, Emp.EMPNUM, False, False).ToList
            For Each itm In PaysAndDed
                If Not itm.all_prds = "YES" Then Continue For
                If itm.Type = "P" Then
                    Dim pay As MAN_CHK_DET_PAY = Me.bsManChkDetPay.AddNew
                    pay.CL_CODE = itm.Code
                    pay.CL_DEPT = itm.CL_DEPT
                    pay.CL_RATE = itm.Rate
                    pay.CL_REG_PAY = itm.Amount
                ElseIf itm.Type = "D" Then
                    Dim ded As MAN_CHK_DET_DED = Me.bsManChkDetDed.AddNew
                    ded.CL_CODE = itm.Code
                    ded.CL_DEPT = itm.CL_DEPT
                    ded.CL_OR_FLAT = itm.Amount
                    ded.CL_AMOUNT = itm.Amount
                    ded.CL_TYPE = "AUTOMATIC"
                End If
            Next
        End If
        Me.slueEmps.ReadOnly = True
    End Sub

    Private Sub lueDivisions_EditValueChanged(sender As Object, e As EventArgs) Handles lueDivisions.EditValueChanged
        Dim lue As LookUpEdit = sender
        If lue.EditValue Is Nothing OrElse lue.EditValue.ToString().IsNullOrWhiteSpace Then Exit Sub
        Dim DivNum As Decimal = lue.EditValue
        Dim Depts = (From A In db.DEPARTMENTs Where A.CONUM = _coNum AndAlso A.DIVNUMD = DivNum AndAlso (A.DPACTIVE = "YES" OrElse A.DIVNUMD = Emp.DIVNUM)).ToList
        riLueDepartments.DataSource = Depts

        Me.GridViewPays.RefreshData()
        Me.GridViewDeds.RefreshData()
        Me.GridViewMemos.RefreshData()
    End Sub

    Private Sub bbiSave_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSave.ItemClick
        DxErrorProvider1.ClearErrors()
        bsManChkMast.EndEdit()
        bsManChkDetDed.EndEdit()
        bsManChkDetPay.EndEdit()

        If seCheckNum.EditValue Is Nothing OrElse seCheckNum.EditValue = 0 Then
            DxErrorProvider1.SetError(seCheckNum, "Check # is Required.")
        End If

        If deCheckDate.DateTime = DateTime.MinValue Then
            DxErrorProvider1.SetError(deCheckDate, "Check Date is Required.")
        End If

        If DxErrorProvider1.HasErrors Then
            DisplayMessageBox("Please fix all errors")
            Exit Sub
        End If

        Dim Depts As List(Of DEPARTMENT) = riLueDepartments.DataSource
        Dim DepartmentsIds = (From A In Depts Select A.DEPTNUM).ToList
        For Each view In {GridViewPays, GridViewMemos, GridViewDeds}
            For x = 0 To view.DataRowCount - 1
                If String.IsNullOrEmpty(view.GetRowCellDisplayText(x, "CL_DEPT")) Then
                    DisplayMessageBox("Please set the department for all details")
                    view.Focus()
                    view.FocusedRowHandle = x
                    view.FocusedColumn = view.Columns("CL_DEPT")
                    Exit Sub
                End If
            Next
        Next

        If (From A As MAN_CHK_DET_PAY In bsManChkDetPay.List Where A.CL_DEPT Is Nothing).Any OrElse
            (From A As MAN_CHK_DET_DED In bsManChkDetDed.List Where A.CL_DEPT Is Nothing).Any OrElse
            (From A As MAN_CHK_DET_MEMO In bsManChkDetMemo.List Where A.CL_DEPT Is Nothing).Any Then
            DisplayMessageBox("Please set the department for all details")
            Exit Sub
        End If

        DxErrorProvider1.ClearErrors()

        DirectCast(bsManChkDetPay.List, IEnumerable(Of MAN_CHK_DET_PAY)).ToList.ForEach(Sub(p) If p.rowguid = Guid.Empty Then p.rowguid = Guid.NewGuid)
        DirectCast(bsManChkDetDed.List, IEnumerable(Of MAN_CHK_DET_DED)).ToList.ForEach(Sub(p) If p.rowguid = Guid.Empty Then p.rowguid = Guid.NewGuid)
        DirectCast(bsManChkDetMemo.List, IEnumerable(Of MAN_CHK_DET_MEMO)).ToList.ForEach(Sub(p) If p.rowguid = Guid.Empty Then p.rowguid = Guid.NewGuid)

        Try
            Me.ShowDefaultWaitForm(description:="Saving manual checks.")

            Dim FirstRec = (From A As MAN_CHK_DET_PAY In Me.bsManChkDetPay Order By A.CL_NUM).FirstOrDefault
            Dim locOverrides As List(Of LocalOverrides) = GridControlLocOvr.DataSource
            If FirstRec IsNot Nothing AndAlso locOverrides?.Count > 0 Then
                For x = 1 To 5
                    Dim Code As Decimal?, Amount As Decimal?
                    Code = FirstRec.GetType.GetProperty("CL_LOC_CD" & x).GetValue(FirstRec)
                    If Code.GetValueOrDefault <> 0 Then
                        Amount = (From A In locOverrides Where A.Code = Code Select val = A.Amount).FirstOrDefault
                        FirstRec.GetType.GetProperty("OR_CL_LOC_AMT" & x).SetValue(FirstRec, Amount)
                    End If
                Next
            End If

            db.SubmitChanges()

            Dim API = EP_API()
            API.CompanyID = _coNum

            'If Not UsePPxLibrary Then
            '    CType(API, PPxPayroll).CheckPayrollStatus()
            'Else
            '    API.CheckPayrollStatus(_coNum)
            'End If

            API.CheckPayrollStatus(_coNum)

            'API.PayrollAPI.CreateNewManualCheck(API.ProviderID, 812, -1, 10, 0, "YES", "YES", "YES", "YES", "", 9998, #01/12/2020#, -1, UserName)
            'API.PayrollAPI.AddManualCheck(API.ProviderID, API.CompanyID, -1, 10, 1, -1)

            Dim retKey As Integer
            Dim errors As String = Nothing

            Dim results As Boolean
            'If Not UsePPxLibrary Then
            '    Dim ppx = CType(API, PPxPayroll)
            '    results = ppx.PayrollAPI.CalculateManualCheck(API.ProviderID, API.CompanyID, manChkMast.EMPNUM, manChkMast.CHK_COUNTER, manChkMast.PAY_FREQ, manChkMast.check_date.Value, retKey, UserName, errors)
            'Else
            '    results = API.CalculateManualCheck(manChkMast.CONUM, manChkMast.EMPNUM, manChkMast.CHK_COUNTER, manChkMast.PAY_FREQ, manChkMast.check_date.Value, errors)
            'End If

            results = API.CalculateManualCheck(manChkMast.CONUM, manChkMast.EMPNUM, manChkMast.CHK_COUNTER, manChkMast.PAY_FREQ, manChkMast.check_date.Value, errors)

            If Not results Then
                If String.IsNullOrEmpty(errors) Then errors = "Unkonown results"
                Throw New Exception("Calculate manual check failed: " & errors)
            End If
            'Execupay.Foundation.Payroll.Engine.API.Int.fnGetTaxablesTaxesManual()

            db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, manChkMast)
            db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, manChkMast.MAN_CHK_DET_PAYs)
            Me.bsManChkMast.ResetBindings(False)
            Me.bsManChkDetPay.ResetBindings(False)
            IsEditMode = True
            BindData()
        Catch ex As Exception
            DisplayErrorMessage(ex.Message, ex)
            Return
        Finally
            Me.CloseWaitForm()
        End Try

    End Sub

    Private Sub bsManChkDetPay_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles bsManChkDetPay.AddingNew
        Dim chkDet = New MAN_CHK_DET_PAY
        chkDet.CONUM = manChkMast.CONUM
        chkDet.EMPNUM = manChkMast.EMPNUM
        chkDet.PAYROLL_NUM = -1
        chkDet.CHK_COUNTER = manChkMast.CHK_COUNTER
        chkDet.CL_LOC_CD1 = 0
        chkDet.CL_LOC_CD2 = 0
        chkDet.CL_LOC_CD3 = 0
        chkDet.CL_LOC_CD4 = 0
        chkDet.CL_LOC_CD5 = 0
        chkDet.CL_LOC_AMT1 = 0
        chkDet.CL_LOC_AMT2 = 0
        chkDet.CL_LOC_AMT3 = 0
        chkDet.CL_LOC_AMT4 = 0
        chkDet.CL_LOC_AMT5 = 0
        chkDet.OR_CALC = "NO"
        chkDet.LI_HRS = 0

        'Dim nums = (From l In db.CHK_DET_PAYs Where l.CONUM = manChkMast.CONUM AndAlso l.EMPNUM = manChkMast.EMPNUM AndAlso l.CHK_COUNTER = manChkMast.CHK_COUNTER Select l.CHK_COUNTER)
        'chkDet.CL_NUM = (From A As MAN_CHK_DET_PAY In Me.bsManChkDetPay.List Select New Decimal?(A.CL_NUM)).Max.GetValueOrDefault() + 1
        chkDet.CL_NUM = (From A As MAN_CHK_DET_PAY In Me.bsManChkDetPay.List Select New Decimal?(A.CL_NUM)).ToList().Max.GetValueOrDefault() + 1
        If lueDivisions.EditValue IsNot Nothing AndAlso lueDivisions.EditValue IsNot DBNull.Value AndAlso lueDivisions.EditValue = Emp.DIVNUM Then
            chkDet.CL_DEPT = Emp.DEPTNUM
        End If

        If lueDivisions.EditValue IsNot Nothing AndAlso lueDivisions.EditValue IsNot DBNull.Value AndAlso lueDivisions.EditValue = Emp.DIVNUM Then
            chkDet.CL_DEPT = Emp.DEPTNUM
        End If

        'plug in locals
        Dim usedPositions = (From a In localCodes Select a.default_position.GetValueOrDefault).ToList
        For Each local In localCodes
            Dim position = local.default_position.GetValueOrDefault
            If position = 0 Then
                position = Enumerable.Range(1, 5).Where(Function(p) Not usedPositions.Contains(p)).FirstOrDefault
                usedPositions.Add(position)
            End If
            chkDet.GetType.GetProperty("CL_LOC_CD" & position).SetValue(chkDet, local.local_id)
        Next

        e.NewObject = chkDet
    End Sub

    Private Sub GridViewPays_ShownEditor(sender As Object, e As EventArgs) Handles GridViewPays.ShownEditor
        Dim FldName As String = Me.GridViewPays.FocusedColumn.FieldName
        Dim HasCode = Me.GridViewPays.GetFocusedRowCellValue("CL_CODE") IsNot Nothing
        If Not HasCode Then Exit Sub
        If FldName = "CL_DEPT" Then
            If lueDivisions.EditValue IsNot Nothing AndAlso lueDivisions.EditValue IsNot DBNull.Value AndAlso lueDivisions.EditValue = Emp.DIVNUM Then
                GridViewPays.ActiveEditor.EditValue = Emp.DEPTNUM
            End If


        ElseIf FldName = "CL_RATE" Then
            If Emp.RATE_1.GetValueOrDefault > 0 Then
                GridViewPays.ActiveEditor.EditValue = Emp.RATE_1
            End If
        ElseIf FldName = "CL_REG_HRS" Then

        End If
    End Sub

    Private Sub GridViewPays_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridViewPays.CellValueChanged
        Dim FldName As String = e.Column.FieldName
        Dim row As MAN_CHK_DET_PAY = Me.GridViewPays.GetRow(e.RowHandle)
        If FldName = "CL_RATE" Then
            GridViewPays.SetRowCellValue(e.RowHandle, "CL_REG_PAY", row.CL_RATE.GetValueOrDefault * row.CL_REG_HRS.GetValueOrDefault)
            If row.CL_OT_HRS.HasValue Then
                GridViewPays.SetRowCellValue(e.RowHandle, "CL_OT_PAY", (row.CL_RATE.GetValueOrDefault * row.CL_REG_HRS.GetValueOrDefault) * 1.5)
            End If
        ElseIf FldName = "CL_REG_HRS" Then
            GridViewPays.SetRowCellValue(e.RowHandle, "CL_REG_PAY", row.CL_RATE.GetValueOrDefault * row.CL_REG_HRS.GetValueOrDefault)
        ElseIf FldName = "CL_OT_HRS" Then
            GridViewPays.SetRowCellValue(e.RowHandle, "CL_OT_PAY", (row.CL_RATE.GetValueOrDefault * row.CL_REG_HRS.GetValueOrDefault) * 1.5)
        End If
    End Sub

    Private Sub bsManChkDetDed_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles bsManChkDetDed.AddingNew
        Dim chkDet = New MAN_CHK_DET_DED
        chkDet.CONUM = manChkMast.CONUM
        chkDet.EMPNUM = manChkMast.EMPNUM
        chkDet.PAYROLL_NUM = -1
        chkDet.CHK_COUNTER = manChkMast.CHK_COUNTER
        'chkDet.CL_NUM = (From A As MAN_CHK_DET_DED In Me.bsManChkDetDed.List Select New Decimal?(A.CL_NUM)).Max.GetValueOrDefault() + 1
        chkDet.CL_NUM = (From A As MAN_CHK_DET_DED In Me.bsManChkDetDed.List Select New Decimal?(A.CL_NUM)).ToList().Max.GetValueOrDefault() + 1
        chkDet.CL_TYPE = "MANUAL"
        chkDet.rowguid = Guid.NewGuid
        If lueDivisions.EditValue IsNot Nothing AndAlso lueDivisions.EditValue IsNot DBNull.Value AndAlso lueDivisions.EditValue = Emp.DIVNUM Then
            chkDet.CL_DEPT = Emp.DEPTNUM
        End If
        e.NewObject = chkDet
    End Sub

    Private Sub bsManChkDetMemo_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles bsManChkDetMemo.AddingNew
        Dim chkDet = New MAN_CHK_DET_MEMO
        chkDet.CONUM = manChkMast.CONUM
        chkDet.EMPNUM = manChkMast.EMPNUM
        chkDet.PAYROLL_NUM = -1
        chkDet.CHK_COUNTER = manChkMast.CHK_COUNTER
        chkDet.CL_NUM = (From A As MAN_CHK_DET_DED In Me.bsManChkDetMemo.List Select New Decimal?(A.CL_NUM)).Max.GetValueOrDefault() + 1
        chkDet.CL_TYPE = "MANUAL"
        chkDet.rowguid = Guid.NewGuid
        If lueDivisions.EditValue IsNot Nothing AndAlso lueDivisions.EditValue IsNot DBNull.Value AndAlso lueDivisions.EditValue = Emp.DIVNUM Then
            chkDet.CL_DEPT = Emp.DEPTNUM
        End If
        e.NewObject = chkDet
    End Sub

    Private Sub GridViewDeds_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridViewDeds.CellValueChanged, GridViewMemos.CellValueChanged
        Dim FldName As String = e.Column.FieldName
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        If FldName = "CL_OR_FLAT" Then
            view.SetFocusedRowCellValue("CL_AMOUNT", e.Value)
        End If
    End Sub

    Private Sub bbiCancel_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCancel.ItemClick
        Me.Close()
        Me.Dispose()
    End Sub

    Private Sub GridViewPays_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewPays.PopupMenuShowing, GridViewDeds.PopupMenuShowing, GridViewMemos.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete Row", click:=Sub() DeleteDetailRow(sender, e)))
        End If
    End Sub

    Private Sub DeleteDetailRow(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs)
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        'Dim grid = view.GridControl
        'Dim bs As BindingSource = grid.DataSource
        Dim row = view.GetRow(e.HitInfo.RowHandle)
        Dim rowId As Guid = row.GetType.GetProperty("rowguid").GetValue(row)
        If rowId <> Guid.Empty Then
            Select Case view.Name
                Case GridViewPays.Name
                    db.MAN_CHK_DET_PAYs.DeleteOnSubmit(row)
                Case GridViewDeds.Name
                    db.MAN_CHK_DET_DEDs.DeleteOnSubmit(row)
                Case GridViewMemos.Name
                    db.MAN_CHK_DET_MEMOs.DeleteOnSubmit(row)
            End Select
        End If
        view.DeleteRow(e.HitInfo.RowHandle)
    End Sub

    Private Sub GridViewPays_ShowingEditor(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles GridViewPays.ShowingEditor, GridViewDeds.ShowingEditor, GridViewMemos.ShowingEditor
        If Not slueEmps.HasValue Then
            e.Cancel = True
        End If
    End Sub

    Private Sub bbiDelete_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiDelete.ItemClick
        Try
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = DialogResult.Yes Then
                Dim API = EP_API()
                API.CompanyID = _coNum

                'If Not UsePPxLibrary Then
                '    Dim ppx = CType(API, PPxPayroll)
                '    ppx.PayrollAPI.DeleteManualCheck(API.ProviderID, manChkMast.CONUM, manChkMast.PAYROLL_NUM, manChkMast.EMPNUM, manChkMast.CHK_COUNTER, -1)
                'Else
                '    API.DeleteManualCheck(manChkMast.CONUM, manChkMast.PAYROLL_NUM, manChkMast.EMPNUM, manChkMast.CHK_COUNTER)
                'End If

                API.DeleteManualCheck(manChkMast.CONUM, manChkMast.PAYROLL_NUM, manChkMast.EMPNUM, manChkMast.CHK_COUNTER)

                Using db2 = New dbEPDataDataContext(GetConnectionString)
                    Dim rec = (From A In db2.MAN_CHK_MASTs Where A.CONUM = manChkMast.CONUM AndAlso A.EMPNUM = manChkMast.EMPNUM AndAlso A.PAYROLL_NUM = manChkMast.PAYROLL_NUM AndAlso A.CHK_COUNTER = manChkMast.CHK_COUNTER).FirstOrDefault
                    If rec Is Nothing Then
                        Me.bbiCancel.PerformClick()
                    End If
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in Delete Check", ex)
        End Try
    End Sub

    Private Sub frmCheckFigures_FormClosed(sender As Object, e As FormClosedEventArgs) Handles Me.FormClosed
        If db IsNot Nothing Then db.Dispose()
    End Sub

    Private Class LocalOverrides
        Property Code As Decimal
        Property Amount As Decimal?
    End Class

    Private Sub bbiManulChecks_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiManualChecks.ItemClick
        Dim frm As New frmManualChecks With {.CoNum = _coNum, .CheckDate = Today, .UpdateZendeskTicketId = bbiManualChecks.Tag}
        Dim results = frm.ShowDialog
        frm.Dispose()
    End Sub
End Class