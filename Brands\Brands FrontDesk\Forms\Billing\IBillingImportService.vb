﻿Namespace Billing

    Public Interface IBillingImportService

        Property InvoiceDate As DateTime
        Property globalBilling As List(Of ACT_ITEM)
        Property PriceCodesList As List(Of Integer)
        ReadOnly Property Records As List(Of IRecord)

        Event SelectedRowChanged(row As IRecord)
        Event SelectionChanged()

        Sub Initialize()
        Sub ImportFile()
        Sub CalculatePrices()
        'Sub PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs)
        Sub InvoiceRow(record As IRecord)
        Function GetSelectedRow() As IRecord
        Function GetSelectedRows() As List(Of IRecord)
        Sub RefreshData(ShowBilled As Boolean, ShowNonMatched As Boolean)


        'Public Function GetItemName(ItemNum As Decimal) As String
        '    Return globalBilling.Single(Function(b) b.ITEM_NUM = ItemNum).ITEM_NAME & "~" & InvoiceDate.ToString("MMMM") & " " & InvoiceDate.ToString("yyyy")
        'End Function

        'Public Sub InsertInvoiceLog(conum As Decimal, ItemNum As Decimal, price As Decimal, count As Decimal)
        '    Try
        '        Dim db = New dbEPDataDataContext(GetConnectionString)
        '        Dim invNum = db.fn_GetInvoiceNumForCo(conum, InvoiceDate)
        '        Dim CO = db.COOPTIONs.Single(Function(c) c.CONUM = conum)

        '        Dim invoice = New INVOICE_xLOG With {
        '    .CONUM = conum,
        '    .ITEM_DATE = InvoiceDate,
        '    .ITEM_CAT = "Miscellaneous",
        '    .ITEM_NUM = ItemNum,
        '    .PRICE = price,
        '    .CHK_ENTRIES = count,
        '    .TAXABLE = "YES",
        '    .INVOICE_NUM = invNum,
        '    .PRNUM = 0,
        '    .INV_SECTION = "Miscellaneous",
        '    .ITEM_TYPE = "Manual",
        '    .ITEM_NAME = GetItemName(ItemNum),
        '    .DIVNUM = 0,
        '    .ITEM_FREQ_TYPE = CO.CO_BILL_FREQ,
        '     .rowguid = Guid.NewGuid()}
        '        db.INVOICE_xLOGs.InsertOnSubmit(invoice)
        '        db.SaveChanges
        '    Catch ex As Exception
        '        DisplayErrorMessage($"Error inserting invoice log for Item#: {ItemNum} Co#: {conum} Price: {price} Count: {count}", ex)
        '    End Try
        'End Sub
    End Interface
End Namespace