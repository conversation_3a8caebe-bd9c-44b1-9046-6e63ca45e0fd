﻿Imports System.ComponentModel

Public Class frmActivatePayDeds

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    Dim DB As dbEPDataDataContext

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ActivatedPays As List(Of Decimal)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ActivatedDeds As List(Of Decimal)

    Private Sub frmActivatePayDeds_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        ActivatedPays = New List(Of Decimal)
        ActivatedDeds = New List(Of Decimal)
        DB = New dbEPDataDataContext(GetConnectionString)
        Dim Pays = (From A In DB.OTHER_PAYS
                    Where A.CONUM = CoNum AndAlso (A.OACTIVE = "NO" OrElse A.ops_webshow = "NO")
                    Order By A.OTH_PAY_NUM).ToList
        Me.BindingSourcePays.DataSource = Pays

        Dim Deds = (From A In DB.DEDUCTIONs
                    Where A.CONUM = CoNum AndAlso (A.DACTIVE = "NO" OrElse A.deds_webshow = "NO")
                    Order By A.DED_NUM).ToList
        Me.BindingSourceDeds.DataSource = Deds
    End Sub

    Private Sub GridViewPays_CellValueChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridViewPays.CellValueChanged
        If e.Column.FieldName = "Activate" Then
            Dim Row As OTHER_PAY = Me.GridViewPays.GetRow(Me.GridViewPays.FocusedRowHandle)
            Dim IsActivated As Boolean = e.Value
            Row.OACTIVE = IsActivated.ToYesNoString
            Row.ops_webshow = IsActivated.ToYesNoString
            If IsActivated Then
                If Not ActivatedPays.Contains(Row.OTH_PAY_NUM) Then Me.ActivatedPays.Add(Row.OTH_PAY_NUM)
            Else
                If ActivatedPays.Contains(Row.OTH_PAY_NUM) Then Me.ActivatedPays.Remove(Row.OTH_PAY_NUM)
            End If
        End If
    End Sub

    Private Sub GridViewDeds_CellValueChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridViewDeds.CellValueChanged
        If e.Column.FieldName = "Activate" Then
            Dim Row As DEDUCTION = Me.GridViewDeds.GetRow(Me.GridViewDeds.FocusedRowHandle)
            Dim IsActivated As Boolean = e.Value
            Row.DACTIVE = IsActivated.ToYesNoString
            Row.deds_webshow = IsActivated.ToYesNoString
            If IsActivated Then
                If Not ActivatedDeds.Contains(Row.DED_NUM) Then Me.ActivatedDeds.Add(Row.DED_NUM)
            Else
                If ActivatedDeds.Contains(Row.DED_NUM) Then Me.ActivatedDeds.Remove(Row.DED_NUM)
            End If
        End If
    End Sub

    Private Sub OK_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OK_Button.Click
        Dim Changes = DB.GetChangeSet.Updates.Count
        If Changes > 0 Then
            If DB.SaveChanges() Then
                Me.DialogResult = System.Windows.Forms.DialogResult.OK
            Else
                Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
            End If
        Else
            Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        End If
        Me.Close()
    End Sub

    Private Sub Cancel_Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Button.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

End Class
