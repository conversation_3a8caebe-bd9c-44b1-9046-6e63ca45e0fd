﻿''------------------------------------------------------------------------------
'' <auto-generated>
''     This code was generated by a tool.
''     Runtime Version:4.0.30319.42000
''
''     Changes to this file may cause incorrect behavior and will be lost if
''     the code is regenerated.
'' </auto-generated>
''------------------------------------------------------------------------------

'Option Strict On
'Option Explicit On

'Imports System
'Imports System.Collections.Generic
'Imports System.ComponentModel
'Imports System.Data
'Imports System.Data.Linq
'Imports System.Data.Linq.Mapping
'Imports System.Linq
'Imports System.Linq.Expressions
'Imports System.Reflection


'<Global.System.Data.Linq.Mapping.DatabaseAttribute(Name:="EPDATA")>  _
'Partial Public Class testDataContext
'	Inherits System.Data.Linq.DataContext

'	Private Shared mappingSource As System.Data.Linq.Mapping.MappingSource = New AttributeMappingSource()

'  #Region "Extensibility Method Definitions"
'  Partial Private Sub OnCreated()
'  End Sub
'  #End Region

'	Public Sub New()
'		MyBase.New(Global.Brands_FrontDesk.My.MySettings.Default.EPDATAConnectionString1, mappingSource)
'		OnCreated
'	End Sub

'	Public Sub New(ByVal connection As String)
'		MyBase.New(connection, mappingSource)
'		OnCreated
'	End Sub

'	Public Sub New(ByVal connection As System.Data.IDbConnection)
'		MyBase.New(connection, mappingSource)
'		OnCreated
'	End Sub

'	Public Sub New(ByVal connection As String, ByVal mappingSource As System.Data.Linq.Mapping.MappingSource)
'		MyBase.New(connection, mappingSource)
'		OnCreated
'	End Sub

'	Public Sub New(ByVal connection As System.Data.IDbConnection, ByVal mappingSource As System.Data.Linq.Mapping.MappingSource)
'		MyBase.New(connection, mappingSource)
'		OnCreated
'	End Sub
'End Class
