﻿Imports System.Runtime.CompilerServices
Imports System.Runtime.InteropServices

Module DateTimeExtensions

    <Extension()>
    Public Function Quarter(input As Date) As Integer
        Return (input.Month - 1) \ 3 + 1
    End Function

    <Extension()>
    Public Function ToEST(ByVal value As DateTime) As DateTime
        Dim tz As TimeZoneInfo = If(RuntimeInformation.IsOSPlatform(OSPlatform.Windows), TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"), TimeZoneInfo.FindSystemTimeZoneById("America/New_York"))
        Return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, tz)
    End Function
End Module
