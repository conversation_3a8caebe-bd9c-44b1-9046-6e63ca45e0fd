﻿Imports System.ComponentModel
Public Class frmUpdatePayrollPeriod

    Private _db As dbEPDataDataContext

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal

    Dim PrRec As PAYROLL
    Dim CurrrentSelectedCals As List(Of CALENDAR)
    Dim _isLoading As Boolean = True

    Private Sub frmUpdatePayrollPeriod_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        _db = New dbEPDataDataContext(GetConnectionString)

        slueCompany.Properties.DataSource = (
            From A In _db.COMPANies
            Order By A.CONUM
            Select New CompanySummary With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                                                .CO_DBA = A.CO_DBA, .CO_PHONE = A.CO_PHONE, .CO_FAX = A.CO_FAX,
                                                .CO_EMAIL = A.CO_EMAIL, .CO_STATUS = A.CO_STATUS}
                                            ).ToList
        slueCompany.Properties.DisplayMember = "CONUM"
        slueCompany.Properties.ValueMember = "CONUM"

        _isLoading = False

        MemoEdit1.Text = GetUdfValue("Payroll-Period Update Instructions")
    End Sub

    Private Sub slueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles slueCompany.EditValueChanged
        Me.txtCoName.EditValue = Nothing
        Me.CoNum = Nothing
        Me.BindingSourcePayrollList.Clear()
        Me.PayrollPeriodsBindingSource.Clear()
        Me.CALENDARBindingSource.Clear()
        Me.btnApply.Enabled = False

        If Me.slueCompany.HasValue Then
            Me.CoNum = Me.slueCompany.EditValue
            Me.txtCoName.EditValue = (From A In CType(Me.slueCompany.Properties.DataSource, IList) Where A.CONUM = CoNum Select A.CO_NAME).First

            Dim PayrollNumbers = (From A In _db.PAYROLLs Where A.CONUM = Me.CoNum Order By A.PRNUM Descending Select A.PRNUM, CheckDate = A.CHECK_DATE, A.PAYROLL_STATUS).Take(60).ToList
            Me.BindingSourcePayrollList.DataSource = PayrollNumbers
        End If
    End Sub

    Private Sub ddlPayrollNum_EditValueChanged(sender As Object, e As EventArgs) Handles ddlPayrollNum.EditValueChanged
        Dim PrNum As Decimal = ddlPayrollNum.EditValue

        PrRec = (From A In _db.PAYROLLs Where A.CONUM = Me.CoNum AndAlso A.PRNUM = PrNum).FirstOrDefault
        Me.txtCheckDate2.EditValue = PrRec.CHECK_DATE
        Me.txtPayrollStatus.EditValue = PrRec.PAYROLL_STATUS

        If PrRec.PAYROLL_STATUS = "Entering Checks" Then
            DisplayMessageBox("Payroll status is Entering Checks. Cannot make changes now.")
        End If
        Me.btnApply.Enabled = PrRec.PAYROLL_STATUS <> "Entering Checks"

        Dim PrDetails = (From A In Enumerable.Range(1, 3) Select New PayrollPeriodDates With {
                                                                  .[Set] = A,
                                                                  .Used = PrRec.FED_UCI.ToString.Contains(A),
                                                                  .PayFreq = PrRec.GetType.GetProperty($"PAY_FREQ{If(A > 1, A, "")}").GetValue(PrRec),
                                                                  .PeriodStartDate = PrRec.GetType.GetProperty($"PER{A}_ST_DATE").GetValue(PrRec),
                                                                  .PeridoEndDate = PrRec.GetType.GetProperty($"PER{A}_END_DATE").GetValue(PrRec)
                                                              }
                                                              ).ToList

        Me.txtFromDate.Properties.MinValue = PrDetails.Min(Function(p) p.PeriodStartDate)
        Me.txtToDate.Properties.MinValue = PrDetails.Max(Function(p) p.PeridoEndDate)

        _isLoading = True
        Me.txtFromDate.EditValue = PrRec.CHECK_DATE.Value.AddDays(-(4 * 14))
        Me.txtToDate.EditValue = PrRec.CHECK_DATE.Value.AddDays(6 * 14)

        Me.PayrollPeriodsBindingSource.DataSource = PrDetails

        _isLoading = False

        LoadCalendar()
        LoadNextCalendar()
    End Sub

    Sub LoadCalendar()
        Dim FromDate As Date = Me.txtFromDate.EditValue
        Dim ToDate As Date = Me.txtToDate.EditValue

        Dim PrDetails As List(Of PayrollPeriodDates) = Me.PayrollPeriodsBindingSource.List

        Dim cals = (From A In _db.CALENDARs
                    Where A.conum = Me.CoNum AndAlso ((A.check_date >= FromDate AndAlso A.check_date <= ToDate) OrElse A.payroll_num = PrRec.PRNUM)
                    Order By A.check_date, A.period_id, A.cal_id
                        ).ToList
        'Check only by date not by payroll #
        ''p.payroll_num.HasValue AndAlso p.payroll_num.Value = PrRec.PRNUM AndAlso
        cals.ForEach(Sub(p) p.IsSelectedForPowergrid = PrDetails.Exists(Function(f) f.Set = p.period_id AndAlso f.PeriodStartDate.GetValueOrDefault = p.start_date))
        CurrrentSelectedCals = (From A In cals Where A.IsSelectedForPowergrid = True).ToList

        Me.CALENDARBindingSource.DataSource = cals

        Dim FirstSeleted = (From A In cals Where A.IsSelectedForPowergrid = True).FirstOrDefault
        If FirstSeleted IsNot Nothing Then
            Dim Ix = cals.IndexOf(FirstSeleted)
            Me.GridViewPCalendar.MakeRowVisible(Ix)
        End If
    End Sub

    'Private Sub GridViewCalendar_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles GridViewCalendar.CustomUnboundColumnData
    '    If e.Column.FieldName = "colUsedCal" AndAlso e.IsGetData Then
    '        Dim PrNum As Decimal = ddlPayrollNum.EditValue
    '        Dim RowPrNum As Decimal? = e.Row.GetType.GetProperty("payroll_num").GetValue(e.Row)
    '        e.Value = RowPrNum = PrNum
    '    End If
    'End Sub

    Private Sub btnCancelCal_Click(sender As Object, e As EventArgs) Handles btnCancelCal.Click
        If Not Me.ddlPayrollNum.HasValue Then
            Exit Sub
        End If
        ddlPayrollNum_EditValueChanged(ddlPayrollNum, e)
    End Sub

    Private Sub riSelectCal_EditValueChanged(sender As Object, e As EventArgs) Handles riSelectCal.EditValueChanged
        Me.GridViewPCalendar.PostEditor()
        Me.GridViewPCalendar.UpdateCurrentRow()

        'Uncheck Other
        Dim Cal As CALENDAR = Me.GridViewPCalendar.GetRow(Me.GridViewPCalendar.FocusedRowHandle)
        Dim Others = (From A As CALENDAR In Me.CALENDARBindingSource.List Where A.period_id = Cal.period_id AndAlso A.cal_id <> Cal.cal_id AndAlso A.IsSelectedForPowergrid).ToList
        Others.ForEach(Sub(p) p.IsSelectedForPowergrid = False)
        Me.GridViewPCalendar.RefreshData()

        'check each period, if no check is selected, uncheck top check box 
        Dim calList = (From A As CALENDAR In Me.CALENDARBindingSource.List Where A.IsSelectedForPowergrid = True).ToList
        For Each rec As PayrollPeriodDates In Me.PayrollPeriodsBindingSource.List
            Dim selectedCal = calList.Where(Function(p) p.period_id = rec.Set).FirstOrDefault
            rec.Used = selectedCal IsNot Nothing
            rec.PeriodStartDate = selectedCal?.start_date
            rec.PeridoEndDate = selectedCal?.end_date
            rec.PayFreq = selectedCal?.frequency
        Next
        Me.GridViewPayrollPeriods.RefreshData()
    End Sub

    Private Sub btnApply_Click(sender As Object, e As EventArgs) Handles btnApply.Click
        Dim PayFreq = String.Join("", (From A As PayrollPeriodDates In Me.PayrollPeriodsBindingSource.List Where A.Used Order By A.Set Select v = A.Set.ToString).ToList)
        PrRec.FED_UCI = PayFreq
        For Each rec As PayrollPeriodDates In Me.PayrollPeriodsBindingSource.List
            PrRec.GetType.GetProperty($"PAY_FREQ{If(rec.Set > 1, rec.Set, "")}").SetValue(PrRec, If(rec.Used, rec.PayFreq, ""))
            PrRec.GetType.GetProperty($"PER{rec.Set}_ST_DATE").SetValue(PrRec, If(rec.Used, rec.PeriodStartDate, Nothing))
            PrRec.GetType.GetProperty($"PER{rec.Set}_END_DATE").SetValue(PrRec, If(rec.Used, rec.PeridoEndDate, Nothing))
        Next

        Dim selectedCals = (From A As CALENDAR In Me.CALENDARBindingSource.List Where A.IsSelectedForPowergrid = True).ToList
        For Each cal In selectedCals
            If cal.payroll_num.HasValue Then
                'Leave it as is
            Else
                cal.completed = "YES"
                cal.payroll_num = PrRec.PRNUM
                cal.completed_date = PrRec.CHECK_DATE
            End If
        Next

        For Each rec In selectedCals
            Dim previousSelected = (From A In CurrrentSelectedCals Where A.period_id = rec.period_id).FirstOrDefault
            If previousSelected IsNot Nothing Then
                If previousSelected.start_date > rec.start_date Then
                    previousSelected.completed = "NO"
                    previousSelected.payroll_num = Nothing
                    previousSelected.completed_date = Nothing
                End If
            End If
        Next

        _db.SubmitChanges()

        ddlPayrollNum_EditValueChanged(ddlPayrollNum, e)
        'Me.DialogResult = DialogResult.OK
        'Me.Close()
    End Sub

    Private Sub txtFromDate_EditValueChanged(sender As Object, e As EventArgs) Handles txtFromDate.EditValueChanged, txtToDate.EditValueChanged
        If Not _isLoading Then
            LoadCalendar()
        End If
    End Sub

    Sub LoadNextCalendar()
        'Load Calendar
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim CalendarData = (From A In db.CALENDARs Order By A.check_date, A.period_id, A.cal_id
                                Where A.conum = Me.CoNum _
                            AndAlso A.check_date >= Today.AddMonths(-2) _
                            AndAlso A.check_date <= Today.AddMonths(2)).ToList
            Dim pSets = (From A In CalendarData Group By A.period_id, A.frequency Into Group
                         Select period_id, frequency, details = Group.ToList).ToList
            For Each pSet In pSets
                Dim Cal = (From A In pSet.details Where A.check_date >= Today AndAlso A.completed <> "YES").FirstOrDefault
                If Cal IsNot Nothing Then
                    Cal.IsSelectedForPowergrid = True
                End If
            Next
            Me.gridCalendarUpcoming.DataSource = pSets
        End Using
        For x = 0 To GridView1.DataRowCount - 1
            GridView1.ExpandMasterRow(x)
        Next
    End Sub

    Private Sub GridViewCalendar_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewCalendar.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()

            Dim row As CALENDAR = e.HitInfo.View.GetRow(e.HitInfo.RowHandle)
            If Not row.payroll_num.HasValue Then
                If row.completed = "NO" Then
                    e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Set Skipped", AddressOf OnSkipRowClick) With {.Tag = row})
                ElseIf row.completed = "Skipped" Then
                    e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Undo Skip", AddressOf OnSkipRowClick) With {.Tag = row})
                End If
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Attach to payroll", AddressOf OnAttachToPayrollClick) With {.Tag = row})
            End If
        End If
    End Sub

    Private Sub OnAttachToPayrollClick(sender As Object, e As EventArgs)
        Dim mItem As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim Row As CALENDAR = mItem.Tag
        If Row Is Nothing Then Exit Sub

        Dim viewCal = (From A In _db.view_Calendars Where A.CalID = Row.cal_id AndAlso A.CoNum = Row.conum AndAlso A.PeriodID = Row.period_id).FirstOrDefault
        If viewCal Is Nothing Then
            DisplayMessageBox("This calender record is not in the view_Calender view. Cannot be attached from here. See Admin")
            Exit Sub
        End If
        Dim frm As New frmCalendarLinkToPayroll With {.CalRecord = viewCal}
        Dim results = frm.ShowDialog()
        If results = System.Windows.Forms.DialogResult.OK Then
            LoadNextCalendar()
        End If
    End Sub

    Private Sub OnSkipRowClick(sender As Object, e As EventArgs)
        Dim mItem As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim Row As CALENDAR = mItem.Tag
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim CalRec = (From A In db.CALENDARs Where A.cal_id = Row.cal_id AndAlso A.conum = Row.conum AndAlso A.period_id = Row.period_id).Single
            If mItem.Caption = "Set Skipped" Then
                CalRec.completed = "Skipped"
            Else
                CalRec.completed = "NO"
            End If
            If Not db.SaveChanges() Then
                DisplayErrorMessage("Unable to save calendar status due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
            LoadNextCalendar()
        End Using
    End Sub
End Class