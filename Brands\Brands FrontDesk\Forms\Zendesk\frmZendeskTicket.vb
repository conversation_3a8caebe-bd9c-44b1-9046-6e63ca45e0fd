Imports System.ComponentModel
Imports System.Net
Imports Brands.Core.ZendeskServices
Imports Brands.DAL.DomainModels.App.Zendesk
Imports Brands_FrontDesk
Imports DevExpress.Utils
Imports DevExpress.XtraBars
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports Serilog
Imports ZendeskApi_v2.Models.Shared

Public Class frmZendeskTicket
    Private ReadOnly logger As ILogger
    Private Property _SetTicketConumInfo As SetTicketConumInfo
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _Attachments As List(Of Attachment)
    Private Property _UpdateTicketService As UpdateTicketService
    'Private Property _AttachmentCache As Dictionary(Of ZendeskApi_v2.Models.Shared.Attachment, String) = New Dictionary(Of ZendeskApi_v2.Models.Shared.Attachment, String)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _filePreviewUserControl As FilePreviewUserControl

    Public Sub New()
        logger = modGlobals.Logger.ForContext(Of frmZendeskTicket)
        InitializeComponent()
        TabbedControlGroup1.ShowTabHeader = DefaultBoolean.False
        ucSearchCompany.BindPopupContainerEdit(Me.PopupContainerEdit1, False)
        _UpdateTicketService = New Brands.Core.ZendeskServices.UpdateTicketService(New Brands.DAL.EPDATAContext(GetConnectionString()), logger, GetZendeskApi)
    End Sub

    Public Sub SetTicketInfo(ticketInfo As SetTicketConumInfo)
        Try
            logger.Debug("Entering SetTicketId. {@SetTicketConumInfo}", ticketInfo)
            _SetTicketConumInfo = ticketInfo
            _Attachments = modZendeskIntegrationClient.ticketAttachments(ticketInfo.TicketId)
            gcAttachments.DataSource = _Attachments
            If _UpdateTicketService.IsEmailSenderFaxProvider(ticketInfo.RequesterEmail) Then
                ucSearchCompany.SetSearchText(Me.PopupContainerEdit1, _UpdateTicketService.GetFaxNumberFromEmailBody(ticketInfo.Comment))
            Else
                ucSearchCompany.SetSearchText(Me.PopupContainerEdit1, ticketInfo.RequesterEmail)
            End If
            If ticketInfo.Conum.HasValue Then
                PopupContainerEdit1.CancelPopup()
                ucSearchCompany.SetCompany(Me.PopupContainerEdit1, ticketInfo.Conum)
            End If
            Text = $"Ticket Attachments [{ticketInfo.TicketId}]"
        Catch ex As Exception
            DisplayErrorMessage("Error loading attachments", ex)
        End Try
    End Sub

    Private Async Sub frmSetTicketOrg_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        Try
            Await Task.Delay(250)
            PopupContainerEdit1.Properties.PopupFormSize = New Size(1050, 450)
            If _SetTicketConumInfo?.Conum Is Nothing Then PopupContainerEdit1.ShowPopup()
            Dim macroHelper = New ZendeskMacrosHelper
            Await macroHelper.PopulatMacrosAsync(PopupMenu1, AddressOf MacroItemClickedAsync)
            PopupContainerEdit1.Focus()
        Catch ex As Exception
            DisplayErrorMessage("Error initizliaing form", ex)
        End Try
    End Sub

    Private Async Sub MacroItemClickedAsync(sender As Object, e As ItemClickEventArgs)
        Try
            LayoutControl1.ShowProgessPanel
            Dim m As ZendeskApi_v2.Models.Macros.Macro = DirectCast(e.Item, BarButtonItem).Tag
            Await _UpdateTicketService.ApplyMacro(_SetTicketConumInfo.TicketId, m.Id.Value)
        Catch ex As Exception
            DisplayErrorMessage("Error executing macro", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Sub gvAttachments_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvAttachments.FocusedRowObjectChanged
        Try
            Dim attachment As ZendeskApi_v2.Models.Shared.Attachment = e.Row
            Dim fileName As String = GetAttachmentPath(attachment)
            If System.IO.Path.GetExtension(fileName).ToLower() = ".pdf" Then
                TabbedControlGroup1.SelectedTabPageIndex = 0
                PdfViewer1.DocumentFilePath = fileName
            Else
                TabbedControlGroup1.SelectedTabPageIndex = 1
                If _filePreviewUserControl Is Nothing Then
                    _filePreviewUserControl = New FilePreviewUserControl()
                    Dim lci = New DevExpress.XtraLayout.LayoutControlItem
                    lci.Control = _filePreviewUserControl
                    lci.TextVisible = False
                    Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {lci})
                End If
                _filePreviewUserControl.SetFile(fileName, attachment.FileName)
            End If
        Catch ex As Exception
            logger.Error(ex, "Error previwing attachment.")
        End Try
    End Sub

    Private Function GetAttachmentPath(attachment As Attachment) As String
        Dim path As String = Nothing

        'If _AttachmentCache.TryGetValue(attachment, path) Then
        '    Return path
        'End If

        Dim dir = System.IO.Path.Combine(System.IO.Path.GetTempPath(), "ZendeskTicketAttachments")
        If Not System.IO.Directory.Exists(dir) Then
            System.IO.Directory.CreateDirectory(dir)
        End If

        'Solomon added on Jan 5, '24.  Caching causes issues with files with same name
        Dim fileName = System.IO.Path.Combine(dir, _SetTicketConumInfo.TicketId.ToString() + " - " + attachment.FileName)
        If System.IO.File.Exists(fileName) Then
            Try
                IO.File.Delete(fileName)
            Catch ex As Exception

            End Try
        End If

        Using client As New WebClient()
            Try
                'client.DownloadFile(attachment.ContentUrl, fileName)
                Dim request As WebRequest = WebRequest.Create(attachment.ContentUrl)
                request.Method = "GET"

                Try
                    Using response As WebResponse = request.GetResponse()
                        Using stream As IO.Stream = response.GetResponseStream()
                            Using fileStream As New IO.FileStream(fileName, IO.FileMode.Create)
                                stream.CopyTo(fileStream)
                            End Using
                        End Using
                    End Using
                Catch ex As Exception
                    Console.WriteLine(ex.Message)
                End Try

            Catch ex As Exception
                logger.Error(ex, "Error in GetAttachmentPath")
            End Try
        End Using
        '_AttachmentCache.Add(attachment, fileName)
        Return fileName
    End Function

    Private Async Sub btnSave_ClickAsync(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Await _UpdateTicketService.SetTicketCoNumber(_SetTicketConumInfo.TicketId, PopupContainerEdit1.EditValue)
            Close()
        Catch ex As Exception
            logger.Error(ex, "Error setting ticket co#. TicketId: {TicketId} Co#: {Conum}", _SetTicketConumInfo.TicketId, PopupContainerEdit1.EditValue)
            LayoutControl1.HideProgressPanel
            DisplayErrorMessage("Error setting ticket co#", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Async Sub btnSaveAndStartPayroll_Click(sender As Object, e As EventArgs) Handles btnSaveAndStartPayroll.Click
        Try
            Dim conum As Long? = If(PopupContainerEdit1.EditValue Is Nothing OrElse (PopupContainerEdit1.EditValue?.ToString.IsNullOrWhiteSpace).GetValueOrDefault(), _SetTicketConumInfo.Conum, Convert.ToInt32(PopupContainerEdit1.EditValue))
            Await _UpdateTicketService.SetTicketCoNumber(_SetTicketConumInfo.TicketId, conum)
            Dim payrollMacroId = GetUdfValue("Zendesk_NewPayroll_MacroId")
            If payrollMacroId.IsNotNullOrWhiteSpace Then
                Await _UpdateTicketService.ApplyMacro(_SetTicketConumInfo.TicketId, payrollMacroId)
            End If
            LayoutControl1.HideProgressPanel()
            Dim options As OpenPowerGridPayrollOptions = New OpenPowerGridPayrollOptions(conum) With {
                                                                                 ._ZendeskTicketId = _SetTicketConumInfo.TicketId,
                                                                                 .CenterToScreen = Screen.FromControl(MainForm)}
            MainForm.OpenPowerGridPayroll(options)
        Catch ex As Exception
            logger.Error(ex, "Error setting ticket co#. TicketId: {TicketId} Co#: {Conum}", _SetTicketConumInfo.TicketId, PopupContainerEdit1.EditValue)
            LayoutControl1.HideProgressPanel
            DisplayErrorMessage("Error setting ticket co#", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Sub gvAttachments_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvAttachments.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As ZendeskApi_v2.Models.Shared.Attachment = gvAttachments.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Save As", Sub() SaveAsAttachment(row), My.Resources.saveas_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Save All", Sub() SaveAllAttachments(), My.Resources.saveall_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Save to EP CRM Co# {_SetTicketConumInfo.Conum}", Sub() SaveLetterAttachment(row), My.Resources.saveas_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Save to EP CRM (Different Co#)", Sub() SaveLetterAttachment(row, True), My.Resources.saveas_16x16))

            Dim isMemberInTax As Boolean = Query(Of Boolean)("SELECT custom.IsDbRoleMember('EPAY - Tax Dept', DEFAULT)").FirstOrDefault()
            If isMemberInTax Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add Tax Notice", Sub() AddTaxNotice(row), My.Resources.saveas_16x16))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add all as binder to Tax Notice", Sub() AddAllAttachmentsToTaxNotice(), My.Resources.saveas_16x16))
            End If
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Remove from Temporary list", Sub() RemoveFromTempList(), My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub RemoveFromTempList()
        gvAttachments.DeleteRow(gvAttachments.FocusedRowHandle)
    End Sub

    Private Sub AddTaxNotice(row As Attachment)
        Dim path = GetAttachmentPath(row)
        Dim TaxNotice As New frmTaxNotices(_SetTicketConumInfo.TicketId, _SetTicketConumInfo.Conum, path)
        MainForm.ShowForm(TaxNotice)
    End Sub

    Private Sub AddAllAttachmentsToTaxNotice()
        Try
            Dim lstFile As New List(Of String)
            For i = 0 To gvAttachments.RowCount - 1
                Dim row As ZendeskApi_v2.Models.Shared.Attachment = gvAttachments.GetRow(i)
                lstFile.Add(GetAttachmentPath(row))
            Next
            Dim pdfFile = frmPdfCombine.CombinePdf(lstFile, IO.Path.GetTempFileName().Replace(".tmp", ".pdf"))
            Dim TaxNotice As New frmTaxNotices(_SetTicketConumInfo.TicketId, _SetTicketConumInfo.Conum, pdfFile)
            MainForm.ShowForm(TaxNotice)
        Catch ex As Exception
            DisplayErrorMessage("Error in AddAllAttachmentsToTaxNotice", ex)
        End Try
    End Sub

    Private Sub SaveAsAttachment(row As Attachment)
        Dim path = GetAttachmentPath(row)
        Dim sfd = New SaveFileDialog()
        sfd.FileName = System.IO.Path.GetFileName(path)
        If sfd.ShowDialog = System.Windows.Forms.DialogResult.OK Then
            Try
                System.IO.File.Copy(path, sfd.FileName)
            Catch ex As Exception
                DisplayErrorMessage("Error saving attachment", ex)
            End Try
        End If
    End Sub

    Private Sub SaveAllAttachments()
        Dim d = New System.Windows.Forms.FolderBrowserDialog
        If d.ShowDialog() = DialogResult.OK Then
            Try
                For Each attach In _Attachments
                    Dim path = GetAttachmentPath(attach)
                    IO.File.Copy(path, IO.Path.Combine(d.SelectedPath, IO.Path.GetFileName(path)))
                Next
            Catch ex As Exception
                DisplayErrorMessage("Error Saving All Email Attachments", ex)
            End Try
        End If
    End Sub


    Dim downHitInfo As GridHitInfo = Nothing
    Private Sub gvRecentReports_MouseDown(sender As Object, e As MouseEventArgs) Handles gvAttachments.MouseDown
        Dim view As GridView = sender
        downHitInfo = Nothing
        Dim hitInfo As GridHitInfo = view.CalcHitInfo(New Point(e.X, e.Y))
        If Control.ModifierKeys <> Keys.None Then Return
        If e.Button = MouseButtons.Left AndAlso hitInfo.RowHandle >= 0 Then downHitInfo = hitInfo
    End Sub

    Private Sub gvRecentReports_MouseMove(sender As Object, e As MouseEventArgs) Handles gvAttachments.MouseMove
        Dim view As GridView = sender
        If e.Button = MouseButtons.Left AndAlso downHitInfo IsNot Nothing Then
            Dim dragSize As Size = SystemInformation.DragSize
            Dim dragRect As Rectangle = New Rectangle(New Point(downHitInfo.HitPoint.X - dragSize.Width / 2, downHitInfo.HitPoint.Y - dragSize.Height / 2), dragSize)
            If Not dragRect.Contains(New Point(e.X, e.Y)) Then
                Dim row As ZendeskApi_v2.Models.Shared.Attachment = view.GetRow(downHitInfo.RowHandle)
                Dim paths = New String() {GetAttachmentPath(row)}
                Dim dataObject = New DataObject(DataFormats.FileDrop, paths)
                dataObject.SetData(DataFormats.StringFormat, paths(0))
                DoDragDrop(dataObject, DragDropEffects.Copy)
            Else

            End If
            downHitInfo = Nothing
            DevExpress.Utils.DXMouseEventArgs.GetMouseArgs(e).Handled = True
        End If
    End Sub

    Private Sub gvAttachments_DoubleClick(sender As Object, e As EventArgs) Handles gvAttachments.DoubleClick
        Try
            Dim ea As DXMouseEventArgs = TryCast(e, DXMouseEventArgs)
            Dim view As GridView = TryCast(sender, GridView)
            Dim info As GridHitInfo = view.CalcHitInfo(ea.Location)
            If info.InRow OrElse info.InRowCell Then
                Dim row As ZendeskApi_v2.Models.Shared.Attachment = view.GetRow(downHitInfo.RowHandle)
                'System.Diagnostics.Process.Start(GetAttachmentPath(row))
                Dim psi As New System.Diagnostics.ProcessStartInfo()
                psi.FileName = GetAttachmentPath(row)
                psi.UseShellExecute = True
                System.Diagnostics.Process.Start(psi)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error openning file", ex)
        End Try
    End Sub

    Private Sub bbiOpenCompany_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiOpenCompany.ItemClick
        Dim conum As Long? = If(PopupContainerEdit1.EditValue, _SetTicketConumInfo.Conum)
        If conum IsNot Nothing Then
            MainForm.OpenCompForm(conum)
        End If
    End Sub

    Private Sub SaveLetterAttachment(row As Attachment, Optional diffCo As Boolean = False)
        Dim path = GetAttachmentPath(row)
        Dim coNum As Decimal = _SetTicketConumInfo.Conum

        If diffCo Then
            Using frm = New frmSelectCompany()
                If frm.ShowDialog = DialogResult.OK Then
                    coNum = frm.GetSelectedConum
                Else
                    Exit Sub
                End If
            End Using
        End If
        SaveLetterAndSendEmail(coNum, path)
    End Sub

    Public Sub SaveLetterAndSendEmail(CoNum As Decimal, file As String)
        Using frm = New frmCompanyDocumentsAddOrEdit(CoNum)
            frm._doc.title = New IO.FileInfo(file).Name
            frm._doc.category = "Misc"
            If frm.AddDocument(file) Then
                If frm.ShowDialog() = DialogResult.OK Then
                    'Dim properties = New EmailProperties() With
                    '    {
                    '        .Subject = "Co#: {0} {1}".FormatWith(CoNum, file),
                    '        .Body = "Attached is the Letter for Co#: {0}".FormatWith(CoNum),
                    '        .AutoReleaseTicketOnClosing = False
                    '    }
                    'Dim frmEml = New frmComposeEmail(properties)
                    'properties.Attachments.Add(file)
                    'properties.ToEmail.AddRange(GetUdfValue("InactiveLetterEmailNotification").Split(";"))
                    'frmEml.ShowDialog()
                End If
            End If
        End Using
    End Sub
End Class