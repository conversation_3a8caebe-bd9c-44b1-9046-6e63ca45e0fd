﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors

Public Class ucShippingAddress
    Sub New()
        InitializeComponent()

        beShippingPhone.AddPhoneButton
        beShppingModem.AddPhoneButton
        bePerChangePhone.AddPhoneButton
        bePerChangeModem.AddPhoneButton
        beTempChangePhone.AddPhoneButton
        beTempChangeModem.AddPhoneButton

        bePhonePermCoChange.AddPhoneButton
        beModemPermCoChange.AddPhoneButton
    End Sub

    Private Property DB As dbEPDataDataContext
    Private Property SelectedDivision As prc_GetShippingAddressResult
    Private Property Division As DIVISION
    Private Property Override As ShipAddressOverride
    'Solomon added.  Must use LabelControl so we can use formatting not available with SimpleLabelControl, but we can not hide label.  Instead we will set/remove text
    Private Property LastPrStatus As String

    Private Sub ucShippingAddress_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If DesignMode Then Exit Sub
        btnPermanentlyChange.Enabled = False
        btnTemporarilyChange.Enabled = False
        btnTemporaryNote.Enabled = False
        btnCopyTemporary.Enabled = False

        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim States = ctxDB.ZIPS.Select(Function(z) z.STATE).Distinct().ToArray
            cbeStates.Properties.Items.AddRange(States)
            cbeStatesOverrideShipAddress.Properties.Items.AddRange(States)
            cbeStatePermCoChange.Properties.Items.AddRange(States)
            Dim deliveryOptions As String() = ctxDB.dbo_DELIVERies.Select(Function(d) d.DELDESC).ToArray
            cbeDivisionDeliveryMethod.Properties.Items.AddRange(deliveryOptions)
            cbeDeliveryMethodOverride.Properties.Items.AddRange(deliveryOptions)
            cbeCurrentShipDelvMethod.Properties.Items.AddRange(deliveryOptions)
            cbeDelvMethodPermCoChange.Properties.Items.AddRange(deliveryOptions)
        End Using
        LoadData()


    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal

    Public Sub LoadData()
        Try
            If DesignMode Then Exit Sub
            DB = New dbEPDataDataContext(GetConnectionString)
            Dim result = DB.prc_GetShippingAddress(CoNum).ToList
            If Not ceShowInactiveDivisions.Checked Then result = result.Where(Function(d) d.EEcnt > 0).ToList
            gcDivisions.DataSource = result
            If result.Count = 0 Then
                btnPermanentlyChange.Enabled = False
                btnTemporarilyChange.Enabled = False
                btnTemporaryNote.Enabled = False
                btnCopyTemporary.Enabled = False
                BindingSourceCurrentShippingInfo.DataSource = New prc_GetShippingAddressResult
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading shipping addresses", ex)
        End Try
    End Sub

    Private Sub ceShowInactiveDivisions_CheckedChanged(sender As Object, e As EventArgs) Handles ceShowInactiveDivisions.CheckedChanged
        LoadData()
    End Sub

    Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvDivisions.FocusedRowObjectChanged
        SelectedDivision = e.Row
        BindingSourceCurrentShippingInfo.DataSource = e.Row

        Dim AllowTempChanges = SelectedDivision IsNot Nothing _
            AndAlso (SelectedDivision.DDIVNUM = 0 OrElse SelectedDivision.DivShip = "DivShip" OrElse SelectedDivision.CoShip = "CoShip")

        btnPermanentlyChange.Enabled = SelectedDivision IsNot Nothing
        btnTemporarilyChange.Enabled = AllowTempChanges
        btnTemporaryNote.Enabled = AllowTempChanges
        btnCopyTemporary.Enabled = AllowTempChanges

        gcOverrides.DataSource = DB.ShipAddressOverrides.Where(Function(o) o.CONUM = CoNum AndAlso o.DIVNUM = SelectedDivision.DDIVNUM).OrderByDescending(Function(d) d.AddDateTime).ToList

        SetGroupBoxText(lcgCurrentShippingInfo, e.Row)
        SetGroupBoxText(lcgPermanentlyChange, e.Row)
        SetGroupBoxText(lcgTemporarilyChange, e.Row)
        SetGroupBoxText(lcgPermanentlyCoChange, e.Row)

        cbeCurrentShipDelvMethod.Properties.ReadOnly = SelectedDivision?.DDIVNUM <> 0

        lcCurrentShipSource.Text = $"Using {IIf(SelectedDivision.DivShip = "DivShip", "Division", IIf(SelectedDivision.CoShip = "CoShip", "Company Shipping", "Company Main"))} Address"
        If (SelectedDivision.DDIVNUM <> 0 AndAlso SelectedDivision.DivShip <> "DivShip") OrElse (SelectedDivision.DDIVNUM = 0 AndAlso SelectedDivision.CoShip = "") Then
            lcCurrentShipSource.Appearance.ForeColor = Color.Red
        Else
            lcCurrentShipSource.Appearance.ForeColor = Color.Black
        End If
    End Sub

    Private Sub SetGroupBoxText(box As DevExpress.XtraLayout.LayoutControlGroup, row As prc_GetShippingAddressResult)
        If row Is Nothing Then
            box.Text = box.Tag
        Else
            box.Text = $"{box.Tag} - Div: {row.DDIVNUM} - {row.DDIVNAME}"
        End If
    End Sub

    Private Sub cbeCurrentShipDelvMethod_Closed(sender As Object, e As Controls.ClosedEventArgs) Handles cbeCurrentShipDelvMethod.Closed
        Try
            Dim company = DB.COMPANies.Single(Function(c) c.CONUM = CoNum)
            If XtraMessageBox.Show("Are you sure you would like to update the company shipping method?", "Update Shipping Method?", MessageBoxButtons.YesNo) = DialogResult.No Then
                cbeCurrentShipDelvMethod.EditValue = company.DELVDESC
                Exit Sub
            End If
            If e.CloseMode = PopupCloseMode.Normal Then
                COMPANY.DELVDESC = cbeCurrentShipDelvMethod.EditValue
                DB.SaveChanges
            End If
        Catch ex As Exception
            DisplayErrorMessage($"Error updating company shipping info. Co#: {CoNum} Div#: {SelectedDivision?.DDIVNUM}", ex)
        End Try
    End Sub

    Private Sub btnPermanentlyOverride_Click(sender As Object, e As EventArgs) Handles btnPermanentlyChange.Click
        DB = New dbEPDataDataContext(GetConnectionString)
        If SelectedDivision.DDIVNUM = 0 Then
            Dim company = DB.COMPANies.Single(Function(c) c.CONUM = CoNum)
            If SelectedDivision.CoShip <> "CoShip" Then
                If XtraMessageBox.Show("Currently this division is using main company Address, would you like to setup a separate Shipping address for this division?", "Setup New Shipping Address", MessageBoxButtons.YesNo) = DialogResult.No Then
                    Exit Sub
                End If
            End If
            BindingSourcePermCoChange.DataSource = company
            lcgPermanentlyCoChange.Visibility = True.ToBarItemVisibility
            lcgPermanentlyCoChange.Expanded = True
        Else
            If SelectedDivision.DivShip <> "DivShip" Then
                If XtraMessageBox.Show("Currently this division is using Division 0 Address, would you like to setup a separate Shipping address for this division?", "Setup New Shipping Address", MessageBoxButtons.YesNo) = DialogResult.No Then
                    Exit Sub
                End If
            End If
            Dim div = DB.DIVISIONs.Single(Function(d) d.CONUM = CoNum AndAlso d.DDIVNUM = SelectedDivision.DDIVNUM)
            Division = div
            BindingSourceDivision.DataSource = Division
            lcgPermanentlyChange.Visibility = True.ToBarItemVisibility
            lcgPermanentlyChange.Expanded = True
        End If
        SetNavigationEnables(False)
    End Sub

    Private Sub btnCanelPermanentlyOverride_Click(sender As Object, e As EventArgs) Handles btnCanelPermanentlyOverride.Click
        lcgPermanentlyChange.Visibility = False.ToBarItemVisibility
        SetNavigationEnables(True)
    End Sub


    Private Sub SetNavigationEnables(value As Boolean)
        gcDivisions.Enabled = value
        btnPermanentlyChange.Enabled = value
        btnTemporarilyChange.Enabled = value
        btnTemporaryNote.Enabled = value
        btnCopyTemporary.Enabled = value
    End Sub

    Private Sub btnSavePermanentlyOverride_Click(sender As Object, e As EventArgs) Handles btnSavePermChange.Click
        Try
            Dim curDiv = SelectedDivision.DDIVNUM

            If Not Me.DxValidationProviderPermChange.Validate Then Exit Sub
            Division.DSHIPSAME = "NO"
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save shipping address changes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
            lcgPermanentlyChange.Visibility = False.ToBarItemVisibility
            SetNavigationEnables(True)

            LoadData()
            Dim rh = gvDivisions.LocateByValue("DDIVNUM", curDiv)
            If rh <> DevExpress.XtraGrid.GridControl.InvalidRowHandle Then
                gvDivisions.FocusedRowHandle = rh
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving division address change", ex)
        End Try
    End Sub

    Private Sub teDivisionZip_Validated(sender As Object, e As EventArgs) Handles teDivisionZip.Validated
        If Not Me.teDivisionZip.HasValue Then Return
        If Me.teDivisionZip.EditValue = Me.teDivisionZip.OldEditValue & "" Then Return
        Dim Zip = (From A In DB.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.teDivisionZip.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            Division.DSH_STATE = Zip.STATE
            Division.DSH_CITY = Zip.CITY
        End If
    End Sub

    Private Sub teDivisionStreet_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teDivisionStreet.PreviewKeyDown
        If e.KeyCode = Keys.Enter Then
            teDivisionZip.Focus()
        End If
    End Sub

    Private Sub btnTemporarilyChange_Click(sender As Object, e As EventArgs) Handles btnTemporarilyChange.Click, btnTemporaryNote.Click
        Dim isNoteOnly = sender Is btnTemporaryNote
        DB = New dbEPDataDataContext(GetConnectionString)
        Override = New ShipAddressOverride With {.CONUM = CoNum, .DIVNUM = SelectedDivision.DDIVNUM, .AddUser = UserName, .AddDateTime = Now, .IsNoteOnly = isNoteOnly, .Permanent = False}
        DB.ShipAddressOverrides.InsertOnSubmit(Override)
        BindingSourceShipAddressOverride.DataSource = Override
        lcgTemporarilyChange.Visibility = True.ToBarItemVisibility
        lcgTemporarilyChange.Expanded = True
        lcgTemporarilyChangeAddressFields.Enabled = Not isNoteOnly
        SetNavigationEnables(False)
        deOverrideExpirationDate.Properties.MinValue = Today
        Dim lastPyaroll = DB.PAYROLLs.Where(Function(p) p.CONUM = CoNum).OrderByDescending(Function(p) p.PRNUM).FirstOrDefault
        seOverridePayrollNum.Properties.MinValue = lastPyaroll.PRNUM
        seOverridePayrollNum.EditValue = lastPyaroll.PRNUM
        seOverridePayrollNum.Value = lastPyaroll.PRNUM
        'lcTempChangePrStatus.Text = $"<b>Last Pr#:</b> {lastPyaroll.PRNUM} <b>Status:</b> {lastPyaroll.PAYROLL_STATUS}"
        LastPrStatus = $"<b>Last Pr#:</b> {lastPyaroll.PRNUM} <b>Status:</b> {lastPyaroll.PAYROLL_STATUS}"
        lcTempChangePrStatus.Text = LastPrStatus
        rgExpirationMode.SelectedIndex = 0
    End Sub

    Private Sub btnSaveTemporarilyOverride_Click(sender As Object, e As EventArgs) Handles btnSaveTempChange.Click
        Try
            If rgExpirationMode.SelectedIndex = 0 AndAlso Not seOverridePayrollNum.HasValue Then
                DisplayMessageBox("Please select a Payroll #.")
                Exit Sub
            ElseIf rgExpirationMode.SelectedIndex = 2 AndAlso deOverrideExpirationDate.DateTime = DateTime.MinValue Then
                DisplayMessageBox("Please select an expiration date.")
                Exit Sub
            End If
            If lcgTemporarilyChangeAddressFields.Enabled Then
                If Not DxValidationProviderTempChange.Validate Then Exit Sub
            Else
                If meNotes.Text.IsNullOrWhiteSpace Then
                    DisplayMessageBox("Please enter a note.")
                    Exit Sub
                End If
            End If
            DB.SubmitChanges()
            lcgTemporarilyChange.Visibility = False.ToBarItemVisibility
            SetNavigationEnables(True)
        Catch ex As Exception
            DisplayErrorMessage("Error saving temporary division address change", ex)
        End Try
    End Sub

    Private Sub btnCancelTemporarilyOverride_Click(sender As Object, e As EventArgs) Handles btnCancelTemporarilyOverride.Click
        lcgTemporarilyChange.Visibility = False.ToBarItemVisibility
        SetNavigationEnables(True)
    End Sub

    Private Sub teZipOverride_Validated(sender As Object, e As EventArgs) Handles teZipOverride.Validated
        If Not Me.teZipOverride.HasValue Then Return
        If Me.teZipOverride.EditValue = Me.teZipOverride.OldEditValue & "" Then Return
        Dim Zip = (From A In DB.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.teZipOverride.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            Override.SHIP_STATE = Zip.STATE
            Override.SHIP_CITY = Zip.CITY
        End If
    End Sub

    Private Sub teOverrideStreet_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teOverrideStreet.PreviewKeyDown
        If e.KeyCode = Keys.Enter Then teZipOverride.Focus()
    End Sub

    Private Sub rgExpirationMode_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgExpirationMode.SelectedIndexChanged
        LayoutControl1.BeginUpdate()
        sliQeYeNote.Visibility = (rgExpirationMode.SelectedIndex = 1).ToBarItemVisibility
        lciOverridePayrollNum.Visibility = (rgExpirationMode.SelectedIndex = 0).ToBarItemVisibility
        lciQeYeDate.Visibility = (rgExpirationMode.SelectedIndex = 1).ToBarItemVisibility
        lciOverrideExpireDate.Visibility = (rgExpirationMode.SelectedIndex = 2).ToBarItemVisibility

        If rgExpirationMode.SelectedIndex = 0 Then
            'PrNum
            lcTempChangePrStatus.Text = LastPrStatus                'show pr status
            LayoutControl1.EndUpdate()
            deOverrideExpirationDate.EditValue = Nothing            'remove exp date
            deOverrideExpirationDate.Properties.MinValue = Nothing  'remove exp date
            Override.ExpirationDate = Nothing
            deQeYeDate.EditValue = Nothing                          'remove QE/YE date
            Override.QtrEndDate = Nothing
        ElseIf rgExpirationMode.SelectedIndex = 1 Then
            'QE/YE
            lcTempChangePrStatus.Text = ""                          'remove pr status
            LayoutControl1.EndUpdate()
            Dim DateToday As Date = Date.Today
            Dim DateYearBegin As Date = DateToday.AddDays(-(DateToday.Day - 1)).AddMonths(-(DateToday.Month - 1))
            Dim DateQtrEnd As Date = DateYearBegin.AddMonths(DateToday.Quarter * 3).AddMilliseconds(-1)
            deQeYeDate.EditValue = DateQtrEnd.ToShortDateString
            Override.QtrEndDate = DateQtrEnd.ToShortDateString
            deOverrideExpirationDate.EditValue = Nothing            'remove exp date
            deOverrideExpirationDate.Properties.MinValue = Nothing  'remove exp date
            Override.ExpirationDate = Nothing
            seOverridePayrollNum.EditValue = Nothing                'remove pr number
            seOverridePayrollNum.Properties.MinValue = Nothing      'remove pr number
            Override.PRNUM = Nothing
        ElseIf rgExpirationMode.SelectedIndex = 2 Then
            'Exp Date
            lcTempChangePrStatus.Text = LastPrStatus                'Show pr status
            LayoutControl1.EndUpdate()
            seOverridePayrollNum.EditValue = Nothing                'remove pr number
            seOverridePayrollNum.Properties.MinValue = Nothing      'remove pr number
            Override.PRNUM = Nothing
            deQeYeDate.EditValue = Nothing                          'remove QE/YE date
            Override.QtrEndDate = Nothing
        End If
    End Sub

    Private Sub teDivisionZip_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teDivisionZip.PreviewKeyDown, teZipOverride.PreviewKeyDown, teZipPermCoChange.PreviewKeyDown
        If e.KeyCode = Keys.Enter Then DirectCast(sender, TextEdit).DoValidate()
    End Sub

    Private Sub teDivisionZip_Enter(sender As Object, e As EventArgs) Handles teDivisionZip.Enter, teZipOverride.Enter, teZipPermCoChange.Enter
        DirectCast(sender, TextEdit).SelectAll()
    End Sub

    Private Sub teZipPermCoChange_Validated(sender As Object, e As EventArgs) Handles teZipPermCoChange.Validated
        If Not Me.teZipPermCoChange.HasValue Then Return
        If Me.teZipPermCoChange.EditValue = Me.teZipPermCoChange.OldEditValue & "" Then Return
        Dim Zip = (From A In DB.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.teZipPermCoChange.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            Dim co As COMPANY = BindingSourcePermCoChange.DataSource
            co.SH_STATE = Zip.STATE
            co.SH_CITY = Zip.CITY
        End If
    End Sub

    Private Sub btnSavePermCoChange_Click(sender As Object, e As EventArgs) Handles btnSavePermCoChange.Click
        Try
            Dim co As COMPANY = BindingSourcePermCoChange.DataSource
            co.SHIPSAME = "NO"
            DB.SubmitChanges()
            lcgPermanentlyCoChange.Visibility = False.ToBarItemVisibility
            SetNavigationEnables(True)
        Catch ex As Exception
            DisplayErrorMessage("Error saving shipping info in company table", ex)
        End Try
    End Sub

    Private Sub teStreetPermCoChange_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teStreetPermCoChange.PreviewKeyDown
        If e.KeyCode = Keys.Enter Then teZipPermCoChange.Focus()
    End Sub

    Private Sub btnCancelPermCoChange_Click(sender As Object, e As EventArgs) Handles btnCancelPermCoChange.Click
        lcgPermanentlyCoChange.Visibility = False.ToBarItemVisibility
        SetNavigationEnables(True)
    End Sub

    Private Sub btnCopyTemporary_Click(sender As Object, e As EventArgs) Handles btnCopyTemporary.Click
        If LayoutView1.SelectedRowsCount = 0 Then
            Return
        End If

        Dim selectedOverride = DirectCast(LayoutView1.GetRow(LayoutView1.GetSelectedRows(0)), ShipAddressOverride)

        Dim isNoteOnly = sender Is btnTemporaryNote
        DB = New dbEPDataDataContext(GetConnectionString)
        Override = New ShipAddressOverride With {.CONUM = CoNum, .DIVNUM = SelectedDivision.DDIVNUM, .AddUser = UserName, .AddDateTime = Now, .IsNoteOnly = isNoteOnly, .Permanent = False,
            .SHIP_NAME = selectedOverride.SHIP_NAME, .SHIP_STREET = selectedOverride.SHIP_STREET, .SHIP_CITY = selectedOverride.SHIP_CITY,
            .SHIP_STATE = selectedOverride.SHIP_STATE, .SHIP_ZIP = selectedOverride.SHIP_ZIP, .SHIP_PHONE = selectedOverride.SHIP_PHONE,
            .SHIP_EXTENSION = selectedOverride.SHIP_EXTENSION, .SHIP_MODEM = selectedOverride.SHIP_MODEM, .SHIP_FAX = selectedOverride.SHIP_FAX,
            .PR_CONTACT = selectedOverride.PR_CONTACT, .DELVDESC = selectedOverride.DELVDESC, .Notes = selectedOverride.Notes}
        DB.ShipAddressOverrides.InsertOnSubmit(override)
        BindingSourceShipAddressOverride.DataSource = override
        lcgTemporarilyChange.Visibility = True.ToBarItemVisibility
        lcgTemporarilyChange.Expanded = True
        lcgTemporarilyChangeAddressFields.Enabled = Not isNoteOnly
        SetNavigationEnables(False)
        deOverrideExpirationDate.Properties.MinValue = Today
        Dim lastPyaroll = DB.PAYROLLs.Where(Function(p) p.CONUM = CoNum).OrderByDescending(Function(p) p.PRNUM).FirstOrDefault
        'lcTempChangePrStatus.Text = $"<b>Last Pr#:</b> {lastPyaroll.PRNUM} <b>Status:</b> {lastPyaroll.PAYROLL_STATUS}"
        LastPrStatus = $"<b>Last Pr#:</b> {lastPyaroll.PRNUM} <b>Status:</b> {lastPyaroll.PAYROLL_STATUS}"
        lcTempChangePrStatus.Text = LastPrStatus
        rgExpirationMode.SelectedIndex = 0
    End Sub

    Private Sub deQeYeDate_EditValueChanging(sender As Object, e As Controls.ChangingEventArgs) Handles deQeYeDate.EditValueChanging
        If e.NewValue <> Nothing AndAlso Date.Parse(e.NewValue).Quarter = Date.Parse(e.NewValue).AddDays(1).Quarter Then
            XtraMessageBox.Show("Can only choose a QE / YE date", "Invalid QE / YE date")
            e.Cancel = True
        End If
    End Sub
End Class
