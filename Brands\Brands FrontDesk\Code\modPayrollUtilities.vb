﻿'Imports CrystalDecisions.CrystalReports.Engine
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Imports Dapper
Imports QueueBuilder

Namespace QueuePayrollProcessor
    Public Class ActionLogTypeEventArgs
        Inherits EventArgs

        Property LogLevel As WebLogger.LogLevel
        Property Message As String
        Property Ex As Exception
        Property PayrollProcessStep As Decimal
    End Class

    Module modPayrollUtilities

        Public Sub QueuePayrollReports(queueBuilder As QueueBuilder.QueueBuilder, conum As Decimal, prnum As Decimal, lastCheckDate As DateTime)
            Logger.Information("Entering QueuePayrollReports Co#: {CoNum} Pr#: {PrNum}", conum, prnum)
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim CoPayrollOptions = (From p In db.CoOptions_Payrolls Where p.CoNum = conum).SingleOrDefault
                If CoPayrollOptions Is Nothing Then Throw New Exception("This company is not setup to get the register.")
                Dim co = (From c In db.COMPANies Where c.CONUM = conum).Single
                Dim pr_batch_in_proc = db.pr_batch_in_processes.Single(Function(c) c.CoNum = conum AndAlso c.PrNum = prnum AndAlso (Not c.IsDeleted.HasValue OrElse c.IsDeleted.Value = False))

                If CoPayrollOptions.ReportsToSendWhenSubmiting.IsNotNullOrWhiteSpace() Then
                    queueBuilder.EnqueueConcurrent(Sub(concurrentQueueBuilder)
                                                       For Each f As String In CoPayrollOptions.ReportsToSendWhenSubmiting.Split(";")
                                                           QueueReports(concurrentQueueBuilder, prnum, lastCheckDate, f)
                                                       Next
                                                   End Sub)

                    Dim payrollApprovalEmail = If(CoPayrollOptions.EmailAddressForSubmitting.IsNullOrWhiteSpace(), co.CO_EMAIL, CoPayrollOptions.EmailAddressForSubmitting)
                    Dim body = "{0} {1} {2}{2}please review attached payroll and reply to approve or if any comments{2}{2}Thanks{2}{2}Brands Paycheck & HR Services"
                    body = body.FormatWith(Greeting(), co.PR_CONTACT, vbCrLf)
                    Dim macroId = GetUdfValue_AsLong("Zendesk_Payroll_Approval_MacroId")
                    Dim subject = $"Payroll Approval - {db.COMPANies.Single(Function(c) c.CONUM = conum).CO_NAME}"
                    queueBuilder.EnqueueStaticEmail(payrollApprovalEmail, subject, body, "TEXT", macroId)
                    queueBuilder.EnqueuePayrollApprovalNote(prnum)
                End If
            End Using
        End Sub

        Public Sub QueuePayrollReports(queueBuilder As QueueBuilder.QueueBuilder, conum As Decimal, prnum As Decimal, lastCheckDate As DateTime, overrideRequestedBy As String)
            Logger.Information("Entering QueuePayrollReports Co#: {CoNum} Pr#: {PrNum}", conum, prnum)
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim CoPayrollOptions = (From p In db.CoOptions_Payrolls Where p.CoNum = conum).SingleOrDefault
                If CoPayrollOptions Is Nothing Then Throw New Exception("This company is not setup to get the register.")
                Dim co = (From c In db.COMPANies Where c.CONUM = conum).Single
                Dim pr_batch_in_proc = db.pr_batch_in_processes.Single(Function(c) c.CoNum = conum AndAlso c.PrNum = prnum AndAlso (Not c.IsDeleted.HasValue OrElse c.IsDeleted.Value = False))
                Dim payroll = db.PAYROLLs.Single(Function(p) p.CONUM = conum AndAlso p.PRNUM = prnum)

                If CoPayrollOptions.ReportsToSendWhenSubmiting.IsNotNullOrWhiteSpace() Then
                    queueBuilder.EnqueueConcurrent(Sub(concurrentQueueBuilder)
                                                       For Each f As String In CoPayrollOptions.ReportsToSendWhenSubmiting.Split(";")
                                                           QueueReports(concurrentQueueBuilder, prnum, lastCheckDate, f)
                                                       Next
                                                   End Sub)

                    Dim payrollApprovalEmail = If(CoPayrollOptions.EmailAddressForSubmitting.IsNullOrWhiteSpace(), co.CO_EMAIL, CoPayrollOptions.EmailAddressForSubmitting)
                    Dim subject = $"Payroll Approval - {co.CO_NAME}"
                    Dim macroId = GetUdfValue_AsLong("Zendesk_Payroll_Approval_MacroId")

                    If GetUdfValue("PayrollApproval_UseNewEmailTemplateWithLink") = "Yes" Then
                        Using epDataContext = New Brands.DAL.EPDATAContext(GetConnectionString)
                            Dim jsonBody = New With {
                                    .c = co.rowguid,
                                    .co = CoPayrollOptions.rowguid,
                                    .p = payroll.rowguid,
                                    .b = pr_batch_in_proc.rowguid}
                            Dim jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(jsonBody, Newtonsoft.Json.Formatting.None)
                            Dim token = System.Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes(jsonString))

                            Dim prDates = GetPayPeriods(conum, prnum)
                            If prDates Is Nothing Then Throw New Exception($"No record found in custom.ViewR_CHK_MASTs for Co#: {conum} Pr#: {prnum}")

                            Dim data = New With {
                                    .Link = $"https://paydeck.brandspaycheck.com/authorize-payroll?token={token}",
                                    .CoName = co.CO_NAME,
                                    .Conum = co.CONUM,
                                    .Prnum = payroll.PRNUM,
                                    .Greeting = Greeting(),
                                    .CheckDate = payroll.CHECK_DATE?.ToString("d"),
                                    .Periods = $"{prDates.PERIOD_START_DATE:d} to {prDates.PERIOD_END_DATE:d}"}

                            Dim template = epDataContext.ReportEmailTemplates.Single(Function(t) t.Name = "Payroll Approval")
                            Dim options = New QueueEmailExtensions.EnqueueEmailTemplateOptions With {
                                    .EmailTo = payrollApprovalEmail,
                                    .EmailBodyDataJson = data,
                                    .ApplyMacroToTicket = macroId}
                            If overrideRequestedBy.IsNotNullOrWhiteSpace Then
                                options.OverrideRequestedBy = overrideRequestedBy
                            End If
                            queueBuilder.EnqueueEmailTemplate(template, options, Brands.DAL.DeliverVia.Zendesk)
                        End Using
                    Else
                        Dim body = "{0} {1} {2}{2}please review attached payroll and reply to approve or if any comments{2}{2}Thanks{2}{2}Brands Paycheck & HR Services"
                        body = body.FormatWith(Greeting(), co.PR_CONTACT, vbCrLf)
                        queueBuilder.EnqueueStaticEmail(payrollApprovalEmail, subject, body, "TEXT", macroId)
                    End If
                    queueBuilder.EnqueuePayrollApprovalNote(prnum)
                End If
            End Using
        End Sub

        Private Sub QueueReports(queueBuilder As QueueBuilderDetails, prnum As Decimal, lastCheckDate As Date, f As String)
            Using epDataContext = New Brands.DAL.EPDATAContext(GetConnectionString)
                Dim REPORT = epDataContext.ReportEmailTemplates.SingleOrDefault(Function(rp) rp.Name = f.Trim)
                If REPORT IsNot Nothing Then
                    queueBuilder.EnqueueReport(REPORT, lastCheckDate, prnum)
                Else
                    If String.Equals(f.Trim(), "Payroll Register (Combined)") Then
                        REPORT = epDataContext.ReportEmailTemplates.SingleOrDefault(Function(rp) rp.Name = "ChkRegister_RgChecks")
                        queueBuilder.EnqueueReport(REPORT, lastCheckDate, prnum)

                        REPORT = epDataContext.ReportEmailTemplates.SingleOrDefault(Function(rp) rp.Name = "ChkRegister_SpChecks")
                        queueBuilder.EnqueueReport(REPORT, lastCheckDate, prnum)
                    Else
                        Throw New Exception("Error processing payroll report {0} - Report not found.".FormatWith(f.Trim()))
                    End If
                End If
            End Using
        End Sub

        Public Function SendPayrollReports(coNum As Decimal, prNum As Decimal, LastCheckDate As DateTime) As String
            Dim result As String = String.Empty
            Try
                Logger.Information("Entering SendPayrollReports Co#: {CoNum} Pr#: {PrNum}", coNum, prNum)
                Dim db = New dbEPDataDataContext(GetConnectionString)

                Dim optPay = (From p In db.CoOptions_Payrolls Where p.CoNum = coNum).SingleOrDefault
                If optPay Is Nothing Then Return "This company is not setup to get the register."
                Dim co = (From c In db.COMPANies Where c.CONUM = coNum).Single
                Dim pr_batch_in_proc = db.pr_batch_in_processes.Single(Function(c) c.CoNum = coNum AndAlso c.PrNum = prNum AndAlso (Not c.IsDeleted.HasValue OrElse c.IsDeleted.Value = False))

                If optPay.ReportsToSendWhenSubmiting.IsNotNullOrWhiteSpace() Then
                    Dim files = ProcessPayrollReport(optPay.ReportsToSendWhenSubmiting, db, co, prNum, LastCheckDate)
                    If optPay.SendRegisterBeforSubmitting = "Email" Then
                        Dim pdfPassword As String = GetComPassword(coNum)
                        files = files.Select(Function(f) ReportsExtensions.SetPdfPassword(f, pdfPassword)).ToList()
                        Dim payrollApprovalEmail = If(optPay.EmailAddressForSubmitting.IsNullOrWhiteSpace(), co.CO_EMAIL, optPay.EmailAddressForSubmitting)

                        Dim eml = New EmailService("<EMAIL>")
                        eml.Subject = "Payroll Approval"
                        eml.Category = "Payroll Approval"
                        eml.CreateNewTicket = True
                        eml.CoNUm = coNum
                        eml.ToEmail.AddRange(payrollApprovalEmail.Split(";"))
                        eml.Body = "{0} {1} {2}{2}please review attached payroll and reply to approve or if any comments{2}{2}Thanks{2}{2}Brands Paycheck & HR Services" _
                    & "{2}{2}This message and any attachments may contain confidential or privileged information and are intended only for the use of the intended recipients of this message. If you are not the intended recipient of this message, please notify the sender by return email, and delete this and all copies of this message and any attachments from your system. Any unauthorized disclosure, use, distribution, or reproduction of this message or any attachments is prohibited and may be unlawful,"
                        eml.Body = eml.Body.FormatWith(Greeting(), co.PR_CONTACT, vbCrLf)
                        eml.Attachments.AddRange(files)

                        eml.SendEmail()
                        Try
                            Dim ticket = (From e In db.Emails Where e.EmailNum = eml._NewEmail.EmailNum).Single
                            ticket.UdfInt1 = prNum
                            ticket.UdfString1 = pr_batch_in_proc.ProcessedBy
                            db.SubmitChanges()
                        Catch ex As Exception
                            Logger.Error(ex, "Error in SendPayrollReports-Setting UDF Pr# & ProcessedBy")
                            result &= "Error in SendPayrollReports-Setting UDF: " & ex.Message
                        End Try

                        EmailPassword(coNum, False, payrollApprovalEmail)
                    ElseIf optPay.ReportsToSendWhenSubmiting = "Fax" Then
                        Dim faxNum = If(optPay.FaxNumberForSubmiting.IsNullOrWhiteSpace(), co.CO_FAX, optPay.FaxNumberForSubmiting)
                        Dim fileName = Path.Combine(modReports.GetSecureCrystalReportsFolder(), modReports.GetFileName(co, "Faxed Payroll Approval", "pdf"))
                        Dim destPath = PdfUtilities.CombinePdfs(fileName, "", files.ToArray())
                        Dim coverSheet = New FaxCoverSheet With {.FaxPath = destPath, .FromName = "Payroll Department", .ToName = co.PR_CONTACT, .ToPhone = co.CO_PHONE, .ToFax = faxNum, .TodaysDate = DateTime.Now, .Re = "Payroll Approval", .FType = FaxCoverSheet.FaxType.PleaseReply}
                        coverSheet.Comments = String.Format("{0} {1} {2}{2}please review attached payroll and reply to approve or if any comments{2}{2}Thanks{2}Brands Paycheck & HR Services{2}" _
                    & "This message and any attachments may contain confidential or privileged information and are intended only for the use of the intended recipients of this message. If you are not the intended recipient of this message, please notify the sender by return email, and delete this and all copies of this message and any attachments from your system. Any unauthorized disclosure, use, distribution, or reproduction of this message or any attachments is prohibited and may be unlawful," _
                    & "", Greeting(), co.PR_CONTACT, vbCrLf)
                        Dim frm = New frmPhoneFax()
                        'set to phone #
                        Dim p = frm.GetFaxCoverSheet(coverSheet)
                        frm.SendFax(p, faxNum)
                    End If
                End If
                result &= "Payroll Register Sent To Client for Approval on " & DateTime.Now
            Catch ex As Exception
                Logger.Error(ex, "Error in SendPayrollReports {CoNum} {PrNum}", coNum, prNum)
                result &= "Error in SendPayrollReports: " & ex.Message
            End Try
            Return result
        End Function


        Private Function ProcessPayrollReport(reportsList As String, db As dbEPDataDataContext, co As COMPANY, prNum As Decimal, LastCheckDate As DateTime) As List(Of String)
            Dim files = New List(Of String)
            For Each f As String In reportsList.Split(";")
                Dim report = db.ReportEmailTeplates.SingleOrDefault(Function(rp) rp.Name = f.Trim)
                If report IsNot Nothing Then
                    Dim processor = New ReportProcessor(co, report, FileType.Pdf) _
                With {.LastCheckDate = LastCheckDate, .LastPrNum = prNum, .showInRecentReports = True, .showParametersForm = False, .useEPTestPath = False}
                    Dim result = processor.ProcessReport()
                    Logger.Information("Processed send payroll Report: {Report} Results: {@Results}", report.Name, result)
                    If Not result.Cancalled AndAlso result.AllFileExist Then files.AddRange(result.Paths)
                Else
                    If String.Equals(f.Trim(), "Payroll Register (Combined)") Then
                        Dim processor = New ReportProcessor(co.CONUM, "ChkRegister_RgChecks", FileType.Pdf) With {.LastCheckDate = LastCheckDate, .LastPrNum = prNum, .showInRecentReports = False, .showParametersForm = False, .useEPTestPath = False}
                        Dim result = processor.ProcessReport()
                        Logger.Information("Processed send payroll Report: {Report} Results: {@Results}", "ChkRegister_RgChecks", result)

                        processor = New ReportProcessor(co.CONUM, "ChkRegister_SpChecks", FileType.Pdf) With {.LastCheckDate = LastCheckDate, .LastPrNum = prNum, .showInRecentReports = False, .showParametersForm = False, .useEPTestPath = False}
                        Dim result2 = processor.ProcessReport()
                        Logger.Information("Processed send payroll Report: {Report} Results: {@Results}", "ChkRegister_SpChecks", result)

                        If (Not result.Cancalled AndAlso Not result2.Cancalled) AndAlso (result.AllFileExist AndAlso result2.AllFileExist) Then
                            Dim destPath = Path.Combine(modReports.GetCrystalReportsFolder(), modReports.GetFileName(co, "Payroll Approval", "pdf"))
                            files.Add(PdfUtilities.CombinePdfs(destPath, "", result.Paths(0), result2.Paths(0)))
                        End If
                    Else
                        Throw New Exception("Error processing payroll report {0} - Report not found.".FormatWith(f.Trim()))
                    End If
                End If
            Next
            Return files
        End Function

        Public Class DeletePayroll

            Public Event StatusMessage(StatusMessage As String)

            Public Function CanDeletePayroll(CoNum As Decimal, PrNum As Decimal, ByRef CheckDate As Date) As Boolean
                Using db As New dbEPDataDataContext(GetConnectionString)
                    Dim PrInfo = (From A In db.PAYROLLs Where A.CONUM = CoNum AndAlso A.PRNUM = PrNum Select A.PAYROLL_STATUS, A.CHECK_DATE, A.BILL_ACH_STAT, A.TOTALTAX_STATUS).Single
                    Dim allowDelete = db.ExecuteQuery(Of Boolean)(String.Format("EXEC custom.prc_DeletePayroll {0}, {1}, {2}", CoNum, PrNum, True)).FirstOrDefault
                    If Not allowDelete Then
                        DisplayMessageBox("Payroll cannot be deleted, either because it's not the last payroll for this company, or because status is not Entering Checks...")
                        Return False
                    ElseIf DevExpress.XtraEditors.XtraMessageBox.Show($"Are you absolutely sure you wish to proceed with deleting Payroll # {PrNum} for Company # {CoNum}?{vbCrLf}(This is a non-reversible action)", "Confirm Delete Payroll", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = DialogResult.No Then
                        Return False
                    End If
                    CheckDate = PrInfo.CHECK_DATE
                End Using
                Return True
            End Function

            Public Async Function DeletePayroll(CoNum As Decimal, PrNum As Decimal, SaveRegister As Boolean, CheckDate As Date) As Threading.Tasks.Task(Of Boolean)
                'Save Register
                If SaveRegister Then
                    RaiseEvent StatusMessage("Saving Register")
                    Dim processor = New ReportProcessor(CoNum, "Check Register", FileType.Pdf) _
                        With {.LastCheckDate = CheckDate, .LastPrNum = PrNum, .showInRecentReports = True, .showParametersForm = False, .useEPTestPath = False}
                    Dim result = processor.ProcessReport()
                    For x = 0 To result.Paths.Count - 1
                        Dim Ext = IO.Path.GetExtension(result.Paths(x))
                        Dim DestFile = $"\\brands.local\DFS\Execupay\Deleted Payrolls\{CoNum} PR {PrNum} DT {CheckDate:MM-dd-yyyy} DL On {Now:MM-dd-yyyy hmmtt} {x + 1}{Ext}"
                        IO.File.Copy(result.Paths(x), DestFile)
                    Next
                End If

                Dim IsError As Boolean
                Dim DeleteError As String = Nothing
                Try
                    Using conn As New SqlConnection(GetConnectionString)
                        conn.FireInfoMessageEventOnUserErrors = True
                        conn.Open()

                        AddHandler conn.InfoMessage, Sub(sender As Object, e As SqlInfoMessageEventArgs)
                                                         If e.Errors?.Count > 0 Then
                                                             Dim ex = e.Errors(0)
                                                             If ex.State <> 1 Then
                                                                 DeleteError = ex.Message
                                                                 IsError = True
                                                             End If
                                                         End If
                                                         RaiseEvent StatusMessage(e.Message)
                                                     End Sub
                        Dim cmd = conn.CreateCommand()
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.CommandText = "custom.prc_DeletePayroll"
                        cmd.Parameters.AddWithValue("@CoCode", CoNum)
                        cmd.Parameters.AddWithValue("@PrNum", PrNum)
                        cmd.Parameters.AddWithValue("@CheckOnly", False)
                        Dim results = Await cmd.ExecuteNonQueryAsync

                        conn.Close()
                    End Using
                    RaiseEvent StatusMessage("")
                    If Not IsError Then
                        DisplayMessageBox("Payroll Deleted Successfull")
                    Else
                        DisplayErrorMessage(DeleteError, New Exception(DeleteError))
                    End If
                    Return Not IsError
                Catch ex As Exception
                    DisplayErrorMessage(ex.Message, ex)
                End Try
                Return False
            End Function


        End Class

        Public Property Logger As Serilog.ILogger

        Public Property IsInitialized As Boolean = False

        Public _connectionString As String

        Public _rabitMQConnectionString As String

        Public Property UserName As String
        Public Sub Initialize(connectionString As String, _logger As Serilog.ILogger, rabitMQConnectionString As String, Optional _username As String = "Queue")
            _connectionString = connectionString
            _rabitMQConnectionString = rabitMQConnectionString
            Logger = _logger
            UserName = _username
            IsInitialized = True
        End Sub

        Private Class RabbitMqConnection
            Implements QueueBuilder.IRabbitMQConnectionString

            Public ReadOnly Property RabbitMQConnectionString As String Implements QueueBuilder.IRabbitMQConnectionString.RabbitMQConnectionString
                Get
                    Return _rabitMQConnectionString
                End Get
            End Property
        End Class

        Function GetQueueBuilder(Optional requestedBy As String = Nothing) As QueueBuilder.QueueBuilder
            Dim q = New QueueBuilder.QueueBuilder(New Brands.DAL.EPDATAContext(GetConnectionString), Logger, New RabbitMqConnection())
            q.WithRequesterInfo(If(requestedBy.IsNullOrWhiteSpace(), UserName, requestedBy), "FD")
            Return q
        End Function
    End Module


End Namespace

Public Module modPayrollUtilitiesNew

    Public Sub QueuePayrollReports(queueBuilder As QueueBuilder.QueueBuilder, conum As Decimal, prnum As Decimal, lastCheckDate As DateTime, overrideRequestedBy As String)
        Logger.Information("Entering QueuePayrollReports Co#: {CoNum} Pr#: {PrNum}", conum, prnum)
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim CoPayrollOptions = (From p In db.CoOptions_Payrolls Where p.CoNum = conum).SingleOrDefault
            If CoPayrollOptions Is Nothing Then Throw New Exception("This company is not setup to get the register.")
            Dim co = (From c In db.COMPANies Where c.CONUM = conum).Single
            Dim pr_batch_in_proc = db.pr_batch_in_processes.Single(Function(c) c.CoNum = conum AndAlso c.PrNum = prnum AndAlso (Not c.IsDeleted.HasValue OrElse c.IsDeleted.Value = False))
            Dim payroll = db.PAYROLLs.Single(Function(p) p.CONUM = conum AndAlso p.PRNUM = prnum)

            If CoPayrollOptions.ReportsToSendWhenSubmiting.IsNotNullOrWhiteSpace() Then
                queueBuilder.EnqueueConcurrent(Sub(concurrentQueueBuilder)
                                                   For Each f As String In CoPayrollOptions.ReportsToSendWhenSubmiting.Split(";")
                                                       QueueReports(concurrentQueueBuilder, prnum, lastCheckDate, f)
                                                   Next
                                               End Sub)

                Dim payrollApprovalEmail = If(CoPayrollOptions.EmailAddressForSubmitting.IsNullOrWhiteSpace(), co.CO_EMAIL, CoPayrollOptions.EmailAddressForSubmitting)
                Dim subject = $"Payroll Approval - {co.CO_NAME}"
                Dim macroId = GetUdfValue_AsLong("Zendesk_Payroll_Approval_MacroId")

                If GetUdfValue("PayrollApproval_UseNewEmailTemplateWithLink") = "Yes" Then
                    Using epDataContext = New Brands.DAL.EPDATAContext(GetConnectionString)
                        Dim jsonBody = New With {
                            .c = co.rowguid,
                            .co = CoPayrollOptions.rowguid,
                            .p = payroll.rowguid,
                            .b = pr_batch_in_proc.rowguid}
                        Dim jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(jsonBody, Newtonsoft.Json.Formatting.None)
                        Dim token = System.Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes(jsonString))

                        Dim prDates = GetPayPeriods(conum, prnum)
                        If prDates Is Nothing Then Throw New Exception($"No record found in custom.ViewR_CHK_MASTs for Co#: {conum} Pr#: {prnum}")

                        Dim data = New With {
                            .Link = $"https://paydeck.brandspaycheck.com/authorize-payroll?token={token}",
                            .CoName = co.CO_NAME,
                            .Conum = co.CONUM,
                            .Prnum = payroll.PRNUM,
                            .Greeting = Greeting(),
                            .CheckDate = payroll.CHECK_DATE?.ToString("d"),
                            .Periods = $"{prDates.PERIOD_START_DATE:d} to {prDates.PERIOD_END_DATE:d}"}

                        Dim template = epDataContext.ReportEmailTemplates.Single(Function(t) t.Name = "Payroll Approval")
                        Dim options = New QueueEmailExtensions.EnqueueEmailTemplateOptions With {
                            .EmailTo = payrollApprovalEmail,
                            .EmailBodyDataJson = data,
                            .ApplyMacroToTicket = macroId}
                        If overrideRequestedBy.IsNotNullOrWhiteSpace Then
                            options.OverrideRequestedBy = overrideRequestedBy
                        End If
                        queueBuilder.EnqueueEmailTemplate(template, options, Brands.DAL.DeliverVia.Zendesk)
                    End Using
                Else
                    Dim body = "{0} {1} {2}{2}please review attached payroll and reply to approve or if any comments{2}{2}Thanks{2}{2}Brands Paycheck & HR Services"
                    body = body.FormatWith(Greeting(), co.PR_CONTACT, vbCrLf)
                    queueBuilder.EnqueueStaticEmail(payrollApprovalEmail, subject, body, "TEXT", macroId)
                End If
                queueBuilder.EnqueuePayrollApprovalNote(prnum)
            End If
        End Using
    End Sub

    Private Sub QueueReports(queueBuilder As QueueBuilderDetails, prnum As Decimal, lastCheckDate As Date, f As String)
        Using epDataContext = New Brands.DAL.EPDATAContext(GetConnectionString)
            Dim REPORT = epDataContext.ReportEmailTemplates.SingleOrDefault(Function(rp) rp.Name = f.Trim)
            If REPORT IsNot Nothing Then
                queueBuilder.EnqueueReport(REPORT, lastCheckDate, prnum)
            Else
                If String.Equals(f.Trim(), "Payroll Register (Combined)") Then
                    REPORT = epDataContext.ReportEmailTemplates.SingleOrDefault(Function(rp) rp.Name = "ChkRegister_RgChecks")
                    queueBuilder.EnqueueReport(REPORT, lastCheckDate, prnum)

                    REPORT = epDataContext.ReportEmailTemplates.SingleOrDefault(Function(rp) rp.Name = "ChkRegister_SpChecks")
                    queueBuilder.EnqueueReport(REPORT, lastCheckDate, prnum)
                Else
                    Throw New Exception("Error processing payroll report {0} - Report not found.".FormatWith(f.Trim()))
                End If
            End If
        End Using
    End Sub

    Public Class PayPeriods
        Public PERIOD_START_DATE As DateTime
        Public PERIOD_END_DATE As DateTime
    End Class

    Public Function GetPayPeriods(conum As Decimal, prnum As Decimal) As PayPeriods
        Using con = New SqlConnection(GetConnectionString())
            con.Open()
            Dim results = con.QuerySingle(Of PayPeriods)("SELECT PERIOD_START_DATE = CASE
						    WHEN FED_UCI in (1,12,13,123) then PER1_ST_DATE
						    WHEN FED_UCI in (2,12,23,123) then PER2_ST_DATE
						    WHEN FED_UCI in (3,13,23,123) then PER3_ST_DATE
						    ELSE PER1_ST_DATE 
						    END,
            PERIOD_END_DATE = CASE
						    WHEN FED_UCI in (1,12,13,123) then PER1_END_DATE
						    WHEN FED_UCI in (2,12,23,123) then PER2_END_DATE
						    WHEN FED_UCI in (3,13,23,123) then PER3_END_DATE
						    ELSE PER1_END_DATE 
						    END
            FROM PAYROLL p 
            WHERE conum = @conum AND P.PRNUM = @prnum", New With {.conum = conum, .prnum = prnum})
            Return results
        End Using
    End Function
End Module
