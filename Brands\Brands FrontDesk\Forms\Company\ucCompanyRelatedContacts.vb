﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors

Public Class ucCompanyRelatedContacts

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Conum As Decimal

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property RelatedConums As List(Of Decimal)

    Private Sub ucCompanyRelatedContacts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            If LicenseManager.UsageMode = LicenseUsageMode.Designtime OrElse IsInDesignMode Then
                Exit Sub
            End If
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error loading related contacts", ex)
        End Try
    End Sub

    Sub LoadData()
        If Conum = 0 Then Exit Sub
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim data = db.prc_GetAllRelatedContacts(Conum, Me.chkSearchByPhone.Checked, Me.chkSearchByCPA.Checked, cbSearchByUdf22.Checked, Me.cboActiveStatus.SelectedIndex).ToList()
            For index = data.Count - 1 To 0 Step -1
                Dim row = data(index)
                If (row.user_email?.Contains(";")).GetValueOrDefault() OrElse (row.user_email?.Contains(",")).GetValueOrDefault() Then
                    For Each email In row.user_email.Replace(",", ";").Split(";")
                        Dim newRow = row.CloneEntity
                        newRow.user_email = email
                        data.Add(newRow)
                    Next
                    data.Remove(row)
                End If
                row.CO_NAME = row.CoNum.ToString() + " - " + row.CO_NAME
            Next
            data = (From A In data Order By A.Sort, A.CoNum, A.EmpNum, A.user_email).ToList
            GridControl1.DataSource = data
            colMatchedBy.VisibleIndex = If((From d In data Where d.MatchedBy > "").Count > 0, colRoles.VisibleIndex - 1, -1)

        End Using
        colCO_STATUS.VisibleIndex = If(cboActiveStatus.Text = "All", 2, -1)
        colEmpConum.VisibleIndex = If(chkSearchByCPA.Checked OrElse chkSearchByPhone.Checked OrElse cbSearchByUdf22.Checked, 1, -1)
        colEmpNum.VisibleIndex = If(chkSearchByCPA.Checked OrElse chkSearchByPhone.Checked OrElse cbSearchByUdf22.Checked, 2, -1)
        colCoNum.VisibleIndex = -1
        GridView1.BestFitColumns()
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub


    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            Dim row As prc_GetAllRelatedContactsResult = Me.GridView1.GetFocusedRow
            Dim conums = GetRelatedCompanies()
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit Contact (Option To Add/Remove Co#)", Sub() AddOrEditContact(row, False, False), My.Resources.editcontact_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Add This Contact To all Related Co# - {String.Join(",", conums)}", Sub() AddOrEditContact(row, True, False), My.Resources.hyperlink_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Add New Contact (Current Co# - {row.CoNum})", Sub() AddOrEditContact(Nothing, False, False), My.Resources.add_16x16) With {.BeginGroup = True})
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Add New Contact (To all Related Co# - {String.Join(",", conums)})", Sub() AddOrEditContact(Nothing, True, False), My.Resources.add_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Remove This Contact (Current Co# - {row.CoNum})", Sub() AddOrEditContact(row, False, True), My.Resources.delete_16x16) With {.BeginGroup = True})
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Remove This Contact (From all Related Co# - {String.Join(",", conums)})", Sub() AddOrEditContact(row, True, True), My.Resources.delete_16x16))
        End If
    End Sub

    Sub AddOrEditContact(row As prc_GetAllRelatedContactsResult, allLinkedCompanies As Boolean, remove As Boolean)
        Try
            Using frm As New frmEditCoContact With {.Contact = row}
                Dim coNums As List(Of Decimal)
                If allLinkedCompanies Then
                    coNums = GetRelatedCompanies()
                Else
                    coNums = New List(Of Decimal)({If(row IsNot Nothing, row.CoNum, Me.Conum)})
                End If
                frm.InitializeForm()

                If remove Then
                    Dim message = $"Are you sure you would like to remove Contact [{row.Name}]" & If(allLinkedCompanies, $" (From all Related Co# - {String.Join(",", GetRelatedCompanies())}", $" (Current Co# {row.CoNum})")
                    If XtraMessageBox.Show(message, "Remove Contact", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                        frm.RemoveContact(coNums)
                    End If
                Else
                    If row Is Nothing OrElse allLinkedCompanies Then frm.LinkContactToAllCompanies(coNums)
                    If frm.ShowDialog <> DialogResult.OK Then Exit Sub
                End If
                Me.LoadData()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error editing contact", ex)
        End Try
    End Sub

    Private Sub btnLinkedCompanies_Click(sender As Object, e As EventArgs) Handles btnLinkedCompanies.Click
        Dim frm = New frmLinkedCompanies With {.CoNum = Me.Conum}
        Dim results = frm.ShowDialog
        frm.Dispose()
        If results = DialogResult.OK Then
            Me.LoadData()
        End If
    End Sub

    Function GetRelatedCompanies() As List(Of Decimal)
        If RelatedConums IsNot Nothing Then Return RelatedConums
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim codes = GetRelatedCompanies(Conum, db)
            codes.Add(Conum)
            For Each item In codes.ToArray
                codes.AddRange(GetRelatedCompanies(item, db))
            Next
            RelatedConums = codes.Distinct().ToList
        End Using
        Return RelatedConums
    End Function

    Function GetRelatedCompanies(CoCode As Decimal, db As dbEPDataDataContext) As List(Of Decimal)
        Dim Udf22 = (From A In db.COUSERDEFs Where A.conum = CoCode Select A.udf22_data).FirstOrDefault
        If Udf22.IsNotNullOrWhiteSpace Then
            Dim d As Decimal
            Dim CoCodes = (From A In Udf22.Split(",") Where A IsNot Nothing AndAlso Decimal.TryParse(nz(A, ""), d) Select Num = d).ToList
            Return CoCodes
        Else
            Return New List(Of Decimal)
        End If
    End Function

    Private Sub chk_CheckedChanged(sender As Object, e As EventArgs) Handles chkSearchByPhone.CheckedChanged, chkSearchByCPA.CheckedChanged, cbSearchByUdf22.CheckedChanged
        LoadData()
    End Sub

    Private Sub cboActiveStatus_EditValueChanged(sender As Object, e As EventArgs) Handles cboActiveStatus.EditValueChanged
        LoadData()
    End Sub

    Private Sub btnLinkExistingEmployee_Click(sender As Object, e As EventArgs) Handles btnLinkExistingEmployee.Click
        Try
            Using frm = New frmLinkExistingEmployee(Conum)
                If frm.ShowDialog = DialogResult.OK Then
                    LoadData()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error linking existing employee", ex)
        End Try
    End Sub

    Private Sub ribeRCPhoneDial_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles ribeRCPhoneDial.ButtonClick
        Try
            Dim btn As ButtonEdit = sender
            Dim strToDial = btn.EditValue

            If strToDial.ToString().Trim() > "" Then
                Dim frm = New frmPhone(strToDial)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in ribeRCPhoneDial_ButtonClick", ex)
        End Try
    End Sub

    Private Sub ribeRCPhoneDial_MouseUp(sender As Object, e As MouseEventArgs) Handles ribeRCPhoneDial.MouseUp

    End Sub
End Class
