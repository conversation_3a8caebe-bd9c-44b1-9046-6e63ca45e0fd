﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Imports System.Text
Imports Brands_FrontDesk
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraReports.UI
Imports DocumentFormat.OpenXml
Imports DocumentFormat.OpenXml.Packaging
Imports DocumentFormat.OpenXml.Spreadsheet
'Imports Missing = System.Reflection.Missing
'Imports Workbook = Microsoft.Office.Interop.Excel.Workbook

Public Class ucCompanyServices

    Private CoNum As Decimal

    Private services As List(Of prc_GetCompanyServicesResult)

    Private Divisions As DataTable
    Private RelatedCompanies As DataTable
    Private db As dbEPDataDataContext

    Public Sub New(CoNum As Decimal, services As List(Of prc_GetCompanyServicesResult))
        InitializeComponent()
        Me.services = services
        Me.CoNum = CoNum
        ShowServices(False)

        Try
            db = New dbEPDataDataContext(GetConnectionString)
            Dim isMember = db.fn_FrontDeskUserRoles(UserName).Where(Function(r) r.RoleID = 16).FirstOrDefault?.IsMember
            If isMember Is Nothing OrElse Not isMember Then
                xtpPriceIncrease.PageVisible = False
                Return
            End If
        Catch ex As Exception
            DisplayErrorMessage("error in ucCompanyService new", ex)
        End Try
    End Sub

    Public Sub ShowServices(All As Boolean)
        If Not All Then
            prc_GetCompanyServicesResultBindingSource.DataSource = services.Where(Function(p) p.Enrolled.IsNotNullOrWhiteSpace)
        Else
            prc_GetCompanyServicesResultBindingSource.DataSource = services
            db = New dbEPDataDataContext(GetConnectionString)
            Me.ServiceBindingSource.DataSource = (From a In db.services Where a.SortOrder.HasValue Order By a.SortOrder Select a.ServiceID, a.Description).ToList
            Me.ServicesOfferedBindingSource.DataSource = (From a In db.ServicesOffereds Where a.CoCode = CoNum Order By a.DateOffered Descending).ToList
        End If
        Me.colToOffer.Visible = All
        Me.colComments.Visible = All
        Me.GridView1.OptionsView.ShowHorizontalLines = All
        If All Then
            Me.colToOffer.VisibleIndex = 2
            Me.colComments.VisibleIndex = 3
        End If
        Me.GridView1.BestFitColumns()

        Me.pnlTop.Visible = All
        Me.pnlBottom.Visible = All

        If All Then
            Me.pnlTop.Dock = DockStyle.Top
            Me.XtraTabControl1.Top = Me.pnlTop.Top + Me.pnlTop.Height
        Else
            Me.pnlTop.Dock = DockStyle.None
            Me.XtraTabControl1.Top = Me.pnlTop.Top
        End If

        XtraTabControl1.Visible = True

        If All AndAlso Divisions Is Nothing Then
            Divisions = New DataTable
            RelatedCompanies = New DataTable

            Using conn As New SqlConnection(GetConnectionString)
                Dim cmd = conn.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "[custom].[prc_GetCompnayDivisions]"
                cmd.Parameters.AddWithValue("@CoCode", Me.CoNum)

                conn.Open()
                Dim reader = cmd.ExecuteReader()
                Divisions.Load(reader)
                reader.Close()

                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT CONUM, CO_NAME FROM COMPANY 
WHERE CONUM IN (SELECT col1 FROM dbo.fn_TableFromString((SELECT ISNULL(udf22_data, '-1') FROM COUSERDEFS WHERE conum = @CoCode)) t WHERE TRY_CONVERT(decimal(6,0), t.col1) IS NOT NULL) 
AND CO_STATUS = 'Active Status'
AND CONUM <> @CoCode
ORDER BY 1"
                reader = cmd.ExecuteReader
                RelatedCompanies.Load(reader)
                reader.Close()

                conn.Close()
            End Using

            Me.GridViewDivisions.Columns.Clear()
            Me.GridViewDivisions.PopulateColumns(Divisions)

            Dim Col = Me.GridViewDivisions.Columns("Emp Count")
            If Col IsNot Nothing Then
                Col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                Col.DisplayFormat.FormatString = "N0"
                Col.SummaryItem.DisplayFormat = "{0:N0}"
                Col.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If
            'For Each dCol As DataColumn In Divisions.Columns
            '    Select Case Type.GetTypeCode(dCol.DataType)
            '        Case TypeCode.Decimal, TypeCode.Int32, TypeCode.Int16, TypeCode.Double, TypeCode.Single, TypeCode.Int64
            '            Dim gCol = Me.GridViewDivisions.Columns(dCol.ColumnName)
            '            gCol.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
            '            gCol.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
            '    End Select
            '    'If col.ColumnType = GetType(Decimal) Then
            'Next

            Me.GridControlDivision.DataSource = Divisions
            Me.GridViewDivisions.BestFitColumns()

            Me.GridControlRelatedCompanies.DataSource = RelatedCompanies
            Me.GridViewRelatedCompanies.BestFitColumns()
        End If
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        Using rpt As New rptCompanyServices
            rpt.SqlDataSource1.Connection.ConnectionString = GetConnectionString()
            rpt.SqlDataSource1.Queries(0).Parameters(0).Value = Me.CoNum
            rpt.BindingSourceServices.DataSource = Me.prc_GetCompanyServicesResultBindingSource
            rpt.BindingSourceDivisions.DataSource = Me.GridControlDivision.DataSource
            rpt.BindingSourceRelatedCompanies.DataSource = Me.GridControlRelatedCompanies.DataSource
            rpt.BindingSourceCalendar.DataSource = (From A In db.fn_NextScheduledPayroll(Me.CoNum) Where A.completed = "NO" OrElse A.completed Is Nothing).ToList
            rpt.BindingSourceAutos.DataSource = (From A In db.ACT_AUTOs Where A.CONUM = Me.CoNum
                                                 Order By A.ITEM_FREQ, A.ITEM_NUM
                                                 Select A.DIVNUM, A.ITEM_NAME, A.ITEM_FREQ, A.ACTUAL_PRICE).ToList
            rpt.BindingSourceNotes.DataSource = (From A In db.NOTEs Where A.conum = Me.CoNum AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date >= Today) AndAlso A.category <> "Employee"
                                                 Order By A.category).ToList
            Dim contacts = db.prc_GetAllRelatedContacts(CoNum, False, False, False, 0).ToList()
            contacts = (From A In contacts Where A.ContactType <> "Client").ToList
            rpt.BindingSourceContacts.DataSource = contacts

            rpt.Parameters("CoRank").Value = db.fn_GetCoRank(Me.CoNum).GetValueOrDefault()

            Dim Industry = (From A In db.CoOptions_Payrolls Where A.CoNum = Me.CoNum Select v = A.Industry).FirstOrDefault
            If String.IsNullOrEmpty(Industry) Then Industry = Nothing
            rpt.Parameters("Industry").Value = Industry

            rpt.ShowRibbonPreviewDialog
        End Using
    End Sub

    Private Sub btnAddOffer_Click(sender As Object, e As EventArgs) Handles btnAddOffer.Click
        Me.GridViewServicesOffered.AddNewRow()
        Me.GridViewServicesOffered.ShowEditForm()
    End Sub

    Private Sub GridViewServicesOffered_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridViewServicesOffered.ValidateRow
        Dim row As ServicesOffered = e.Row
        If row.ServiceID Is Nothing Then
            GridViewServicesOffered.SetColumnError(colServiceID1, "Required")
            e.Valid = False
        End If
        If row.DateOffered Is Nothing Then
            GridViewServicesOffered.SetColumnError(colDateOffered, "Required")
            e.Valid = False
        End If
        If row.Response Is Nothing Then
            GridViewServicesOffered.SetColumnError(colResponse, "Required")
            e.Valid = False
        End If
    End Sub

    Private Sub GridViewServicesOffered_EditFormPrepared(sender As Object, e As DevExpress.XtraGrid.Views.Grid.EditFormPreparedEventArgs) Handles GridViewServicesOffered.EditFormPrepared
        For Each cntrl In e.Panel.Controls.OfType(Of DevExpress.XtraEditors.PanelControl).FirstOrDefault?.Controls
            'If cntrl.Text = GridLocalizer.Active.GetLocalizedString(GridStringId.EditFormCancelButton) Then
            '    AddHandler cntrl.Click, Sub(bsender As Object, be As EventArgs)
            '                                If e.RowHandle < 0 Then
            '                                    Dim view = GridViewServicesOffered
            '                                    view.ClearColumnErrors()
            '                                    view.CancelUpdateCurrentRow()
            '                                    view.HideEditForm()
            '                                End If
            '                            End Sub
            'End If

            If cntrl.ToString() = "DevExpress.XtraGrid.EditForm.Helpers.Controls.EditFormCancelButton" Then
                Dim sb = CType(cntrl, DevExpress.XtraEditors.SimpleButton)
                AddHandler sb.Click, Sub(bsender As Object, be As EventArgs)
                                         If e.RowHandle < 0 Then
                                             Dim view = GridViewServicesOffered
                                             view.ClearColumnErrors()
                                             view.CancelUpdateCurrentRow()
                                             view.HideEditForm()
                                         End If
                                     End Sub
            End If

        Next
    End Sub

    Private Sub GridViewServicesOffered_EditFormShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.EditFormShowingEventArgs) Handles GridViewServicesOffered.EditFormShowing
        Dim row = e.RowHandle

        Me.GridViewServicesOffered.OptionsEditForm.FormCaptionFormat = If(row < 0, "Add new record", "Edit Record")
    End Sub

    Private Sub GridViewServicesOffered_InvalidRowException(sender As Object, e As InvalidRowExceptionEventArgs) Handles GridViewServicesOffered.InvalidRowException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction
        'Me.GridViewServicesOffered.ShowEditForm()
    End Sub

    Private Sub GridViewServicesOffered_InitNewRow(sender As Object, e As InitNewRowEventArgs) Handles GridViewServicesOffered.InitNewRow
        GridViewServicesOffered.SetRowCellValue(e.RowHandle, colDateOffered, Now)
        GridViewServicesOffered.SetRowCellValue(e.RowHandle, colByUser, UserName)
    End Sub

    Private Sub GridViewServicesOffered_ShowingPopupEditForm(sender As Object, e As ShowingPopupEditFormEventArgs) Handles GridViewServicesOffered.ShowingPopupEditForm
        e.EditForm.CloseBox = False
        e.EditForm.MinimizeBox = False
    End Sub

    Private Sub GridViewServicesOffered_RowUpdated(sender As Object, e As RowObjectEventArgs) Handles GridViewServicesOffered.RowUpdated
        'Save to database
        BeginInvoke(Sub()
                        Dim row As ServicesOffered = e.Row
                        If row.ID <= 0 Then
                            row.CoCode = Me.CoNum
                            db.ServicesOffereds.InsertOnSubmit(row)
                        End If
                        db.SaveChanges
                        Me.ServicesOfferedBindingSource.DataSource = (From a In db.ServicesOffereds Where a.CoCode = CoNum Order By a.DateOffered Descending).ToList
                    End Sub)
    End Sub

    Private Sub GridViewServicesOffered_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridViewServicesOffered.PopupMenuShowing
        If e.MenuType = GridMenuType.Row Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete Record", click:=Sub()
                                                                                              Dim Ix = e.HitInfo.RowHandle
                                                                                              Dim row As ServicesOffered = GridViewServicesOffered.GetRow(Ix)
                                                                                              GridViewServicesOffered.DeleteRow(Ix)
                                                                                              If row.ID > 0 Then
                                                                                                  db.ServicesOffereds.DeleteOnSubmit(row)
                                                                                                  db.SaveChanges
                                                                                              End If
                                                                                          End Sub))
        End If
    End Sub

    Private Sub riButtonEditCoNum_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riButtonEditCoNum.ButtonClick
        Dim row = Me.GridViewRelatedCompanies.GetFocusedDataRow
        MainForm.OpenCompForm(row("CONUM"))
    End Sub

    Public Sub FillActAutoIncrease()
        CType(CType(Me.TopLevelControl, Form).ActiveControl, frmCompanySumarry).ShowWaitForm()

        SpreadsheetControl1.Visible = False

        'Dim Excel As Microsoft.Office.Interop.Excel.Application = Nothing
        'Dim book As Workbook = Nothing

        Try
            Dim data = Query(String.Format("EXEC custom.prc_RptLastActAutoIncrXml {0}", CoNum))
            Dim sb = New StringBuilder(10000)

            If data.Rows.Count < 2 Then
                lblNoData.Visible = True
                CType(CType(Me.TopLevelControl, Form).ActiveControl, frmCompanySumarry).CloseWaitForm()
                Return
            Else
                lblNoData.Visible = False
            End If

            Dim dr As DataRow
            For Each dr In data.Rows
                sb.Append(dr(0) + vbCrLf)
            Next

            Dim filename As String = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\\ActAutoIncrementLog.xml"

            System.IO.File.WriteAllText(filename, sb.ToString())

            'Excel = New Microsoft.Office.Interop.Excel.Application()
            'book = Excel.Application.Workbooks.OpenXML(filename)
            'Excel.Visible = False
            'Excel.DisplayAlerts = False

            'Dim xlsxFile As String = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\ActAutoIncrementLog.xlsx"
            'book.SaveAs(xlsxFile, 51, Missing.Value, Missing.Value, False, False, 1, 1, True, Missing.Value, Missing.Value, Missing.Value)

            Dim xlsxFile As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "ActAutoIncrementLog.xlsx")
            ConvertXmlToXlsx(filename, xlsxFile)

            SpreadsheetControl1.LoadDocument(xlsxFile)
            SpreadsheetControl1.Visible = True
        Catch ex As Exception
            DisplayErrorMessage("error in FillActAutoIncrease", ex)
            'Finally
            '    If Not book Is Nothing Then
            '        book.Close(True)
            '        InteropServices.Marshal.ReleaseComObject(book)
            '    End If

            '    If Not Excel Is Nothing Then
            '        Excel.Quit()
            '        InteropServices.Marshal.ReleaseComObject(Excel)
            '    End If
        End Try

        CType(CType(Me.TopLevelControl, Form).ActiveControl, frmCompanySumarry).CloseWaitForm()
    End Sub

    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs) Handles XtraTabControl1.SelectedPageChanged
        If e.Page.Equals(xtpPriceIncrease) AndAlso SpreadsheetControl1.ActiveCell.Value.IsEmpty Then
            FillActAutoIncrease()
        End If
    End Sub

    ' Function to convert an XML file to an XLSX file using Open XML SDK
    Public Shared Sub ConvertXmlToXlsx(filename As String, xlsxFile As String)
        Try
            ' Ensure the target directory exists
            Dim targetDirectory As String = Path.GetDirectoryName(xlsxFile)
            If Not Directory.Exists(targetDirectory) Then
                Directory.CreateDirectory(targetDirectory)
            End If

            ' Create a new SpreadsheetDocument for the XLSX file
            Using spreadsheetDocument As SpreadsheetDocument = SpreadsheetDocument.Create(xlsxFile, SpreadsheetDocumentType.Workbook)
                ' Add a WorkbookPart to the document
                Dim workbookPart As WorkbookPart = spreadsheetDocument.AddWorkbookPart()
                workbookPart.Workbook = New Workbook()

                ' Add a WorksheetPart to the WorkbookPart
                Dim worksheetPart As WorksheetPart = workbookPart.AddNewPart(Of WorksheetPart)()
                worksheetPart.Worksheet = New Worksheet(New SheetData())

                ' Load the XML data and process it
                ' This part will depend on the structure of your XML file
                ' You'll need to parse the XML and populate the SheetData with rows and cells

                ' Example (assuming a simple XML structure):
                Using reader As New StreamReader(filename)
                    ' Example: Read and process the XML
                    ' Replace this with your actual XML parsing logic
                    Dim xmlContent As String = reader.ReadToEnd()

                    ' You'll need to write code to parse the xmlContent
                    ' and add the data to the worksheetPart.Worksheet's SheetData
                End Using

                ' Save the changes to the SpreadsheetDocument
                workbookPart.Workbook.Save()

                ' The using statement automatically closes and disposes the document
            End Using

        Catch ex As Exception
            ' Handle any exceptions (e.g., file not found, XML parsing errors)
            Console.WriteLine($"Error converting XML to XLSX: {ex.Message}")
        End Try
    End Sub

End Class
