﻿Imports System.ComponentModel
Imports System.Data
Imports DevExpress.XtraEditors

Public Class ucCompInfo

    Private _co As COMPANY
    Private db As dbEPDataDataContext
    Private autoRefreshCoAfterSave As Boolean = True

    Public Sub New()
        InitializeComponent()
    End Sub

    Public Sub New(comp As COMPANY, empsCount As Integer, _CoOptions As COOPTION)
        InitializeComponent()

        Try
            _co = comp
            bsCompInfo.DataSource = comp
            teEmpsCount.Text = empsCount
            teTaxService.Text = If(_CoOptions IsNot Nothing, _CoOptions.TaxService, "")
            db = New dbEPDataDataContext(GetConnectionString())
            Dim cop = db.CoOptions_Payrolls.Where(Function(f) f.CoNum = comp.CONUM).FirstOrDefault()
            lblAchRisk.Visibility = IIf(cop.AchRisk, DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
            teGroup.EditValue = cop.ClientGroup
            AccountManagerTextEdit.EditValue = cop.AccountManager
            label1099Company.Visibility = If(cop IsNot Nothing AndAlso cop.Is1099OnlyCompany, DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
            Dim SqlNsfClearDate = GetUdfValue("Client NSF Scheduled Clear Date")
            Dim NsfClearDate = String.Format("{0:d}", Query(SqlNsfClearDate, New SqlClient.SqlParameter("CoNum", comp.CONUM)).Rows(0)(0))
            teNsfClearedDate.EditValue = NsfClearDate
            GetClosedStatus(comp.CONUM)
            If comp.ShipSameBool Then
                TabbedControlGroup1.SelectedTabPageIndex = 2
            Else
                TabbedControlGroup1.SelectedTabPageIndex = 1
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in instantiating company info", ex)
        End Try
    End Sub

    Public Sub LoadDate(comp As COMPANY, empsCount As Integer?, _CoOptions As COOPTION)
        _co = comp
        bsCompInfo.DataSource = comp
        teEmpsCount.EditValue = empsCount
        teTaxService.Text = If(_CoOptions IsNot Nothing, _CoOptions.TaxService, "")
        autoRefreshCoAfterSave = False

        Try
            db = New dbEPDataDataContext(GetConnectionString())
            Dim cop = db.CoOptions_Payrolls.Where(Function(f) f.CoNum = comp.CONUM).FirstOrDefault()
            lblAchRisk.Visibility = IIf(cop.AchRisk, DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
            teGroup.EditValue = cop.ClientGroup
            AccountManagerTextEdit.EditValue = cop.AccountManager
            label1099Company.Visibility = If(cop IsNot Nothing AndAlso cop.Is1099OnlyCompany, DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
            Dim SqlNsfClearDate = GetUdfValue("Client NSF Scheduled Clear Date")
            Dim NsfClearDate = String.Format("{0:d}", Query(SqlNsfClearDate, New SqlClient.SqlParameter("CoNum", comp.CONUM)).Rows(0)(0))
            teNsfClearedDate.EditValue = NsfClearDate
            GetClosedStatus(comp.CONUM)
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadDate form company info", ex)
        End Try
    End Sub

    Public Sub Clean()
        bsCompInfo.Clear()
        teEmpsCount.Text = ""
        teTaxService.Text = ""
        teClosedStatus.EditValue = ""
        teGroup.EditValue = ""
        AccountManagerTextEdit.EditValue = ""
        lblAchRisk.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
    End Sub

    Private Sub CO_PHONETextEdit_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles CO_PHONETextEdit.ButtonClick, CO_MODEMTextEdit.ButtonClick, teShPhone.ButtonClick, teShModem.ButtonClick
        Dim tb = CType(sender, ButtonEdit)
        If tb.Text.IsNotNullOrWhiteSpace Then
            Dim frm = New frmPhone(tb.Text)
        End If
    End Sub

    Private Sub CO_FAXTextEdit_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles CO_FAXTextEdit.ButtonClick, teShFax.ButtonClick
        Dim tb = CType(sender, ButtonEdit)
        If tb.Text.IsNotNullOrWhiteSpace Then
            Dim frm = New frmPhoneFax(tb.Text, "", _co)
            frm.ShowDialog()
        End If
    End Sub

    Private Sub hllcEdit_Click(sender As Object, e As EventArgs) Handles hllcEdit.Click
        Try
            lcRoot.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.False
            db = New dbEPDataDataContext(GetConnectionString)
            _co = db.COMPANies.Single(Function(c) c.CONUM = _co.CONUM)
            bsCompInfo.DataSource = _co
            Dim citys = db.ZIPS.Select(Function(z) z.STATE).Distinct.ToList
            cbeStateGeneral.Properties.Items.Clear()
            cbeStateGeneral.Properties.Items.AddRange(citys)
            cbeShStats.Properties.Items.Clear()
            cbeShStats.Properties.Items.AddRange(citys)
            GetClosedStatus(_co.CONUM)
            '_co.PR_CONTACT = "test"
            '_co.ShipAddress1

            SetEnabled(True)
        Catch ex As Exception
            DisplayErrorMessage("Error editing company info", ex)
        End Try
    End Sub

    Private Sub hllcUpdate_Click(sender As Object, e As EventArgs) Handles hllcUpdate.Click
        bsCompInfo.EndEdit()
        Dim i = db.GetChangeSet.Updates.Count
        If db.SaveChanges Then
            lcRoot.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.True
            SetEnabled(False)
            If autoRefreshCoAfterSave Then MainForm.OpenCompForm(_co.CONUM)
        End If
    End Sub

    Private Sub SetEnabled(Enabled As Boolean)
        If Enabled Then
            'lcgGeneralShipping.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            'lcgShipping.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            'lcgShippingGeneral.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            lciHyperLinkEdit.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            lciHyperLinkUpdate.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            lciHyperLinkCancel.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        Else
            'lcgGeneralShipping.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            'lcgShipping.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            'lcgShippingGeneral.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            lciHyperLinkEdit.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            lciHyperLinkUpdate.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            lciHyperLinkCancel.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        End If

        teShName.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShStreet.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShCity.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        cbeShStats.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShZip.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShPhone.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShExt.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShFax.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teShModem.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled
        teDelvDesc2.Enabled = Not ceShSameAsCompany.Checked OrElse Not Enabled

        lcgShippingGeneral.Enabled = Not Enabled
        'TextEdit1.Enabled = Enabled
        'TextEdit2.Enabled = Enabled
        'teCoCity.Enabled = Enabled
        'cbeStateGeneral.Enabled = Enabled
        'teCoZip.Enabled = Enabled
        'teCoCounty.Enabled = Enabled
        'teDelvDesc1.Enabled = Enabled
        'DELVDESCTextEdit.Enabled = Enabled

        CONUMSpinEdit.Enabled = Not Enabled
        teEmpsCount.Enabled = Not Enabled
        CO_STATUSTextEdit.Enabled = Not Enabled
        CO_NAMETextEdit.Enabled = Not Enabled
        CO_DBATextEdit.Enabled = Not Enabled
        CO_DBATextEdit.Enabled = Not Enabled
        OP_OWNERTextEdit.Enabled = Not Enabled
        teTaxService.Enabled = Not Enabled
        teGroup.Enabled = Not Enabled
        AccountManagerTextEdit.Enabled = Not Enabled
    End Sub

    Private Sub hllcCancel_Click(sender As Object, e As EventArgs) Handles hllcCancel.Click
        bsCompInfo.CancelEdit()
        lcRoot.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.True
        SetEnabled(False)
    End Sub

    Private Sub ceShSameAsCompany_CheckedChanged(sender As Object, e As EventArgs) Handles ceShSameAsCompany.CheckedChanged
        teShName.Enabled = Not ceShSameAsCompany.Checked
        teShStreet.Enabled = Not ceShSameAsCompany.Checked
        teShCity.Enabled = Not ceShSameAsCompany.Checked
        cbeShStats.Enabled = Not ceShSameAsCompany.Checked
        teShZip.Enabled = Not ceShSameAsCompany.Checked
        teShPhone.Enabled = Not ceShSameAsCompany.Checked
        teShExt.Enabled = Not ceShSameAsCompany.Checked
        teShFax.Enabled = Not ceShSameAsCompany.Checked
        teShModem.Enabled = Not ceShSameAsCompany.Checked
        teDelvDesc2.Enabled = Not ceShSameAsCompany.Checked
    End Sub

    Private Sub PR_PASSWORDTextEdit_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles PR_PASSWORDTextEdit.Validating
        If PR_PASSWORDTextEdit.EditValue.ToString().IsNotNullOrWhiteSpace AndAlso PR_PASSWORDTextEdit.OldEditValue <> PR_PASSWORDTextEdit.EditValue Then
            If InputBox("Re-Enter Value: ", "Verification on entry") <> PR_PASSWORDTextEdit.EditValue Then
                e.Cancel = True
            End If
        End If
    End Sub

    <ToolboxItem(True)>
    <DefaultValue(True)>
    Public Property CompanyInfoGroupBordersVisible As Boolean
        Get
            Return lcgCompanyInfo.GroupBordersVisible
        End Get
        Set(value As Boolean)
            lcgCompanyInfo.GroupBordersVisible = value
        End Set
    End Property

    <ToolboxItem(True)>
    <DefaultValue(True)>
    Public Property CompanyInfoTabbedControlVisible As Boolean
        Get
            Return TabbedControlGroup1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        End Get
        Set(value As Boolean)
            TabbedControlGroup1.Visibility = If(value, DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
        End Set
    End Property

    Private Sub teCoZip_Validated(sender As Object, e As EventArgs) Handles teCoZip.Validated
        If Not teCoZip.HasValue Then Return
        If teCoZip.EditValue = teCoZip.OldEditValue & "" Then Return
        Try
            Dim zipInfo = (From z In db.ZIPS Where z.CITYTYPE = "D" AndAlso z.ZIP = teCoZip.EditValue.ToString() Select z).FirstOrDefault()

            If zipInfo IsNot Nothing Then
                Dim coInfo = CType(bsCompInfo.DataSource, COMPANY)
                coInfo.CO_CITY = zipInfo.CITY
                coInfo.CO_STATE = zipInfo.STATE
                coInfo.CO_COUNTY = zipInfo.COUNTY

                teCoCity.EditValue = zipInfo.CITY
                cbeStateGeneral.EditValue = zipInfo.STATE
                teCoCounty.EditValue = zipInfo.COUNTY

                Dim phone = System.Text.RegularExpressions.Regex.Replace(CO_PHONETextEdit.EditValue.ToString(), "[^0-9]", "")
                If phone.Length < 4 AndAlso phone <> zipInfo.AREACODE Then
                    CO_PHONETextEdit.EditValue = zipInfo.AREACODE
                    coInfo.CO_PHONE = zipInfo.AREACODE
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("error in teCoZip_EditValueChanged", ex)
        End Try
    End Sub

    Private Sub teShZip_Validated(sender As Object, e As EventArgs) Handles teShZip.Validated
        Try
            If Not teShZip.HasValue Then Return
            If teShZip.EditValue = teShZip.OldEditValue & "" Then Return

            Dim zipInfo = (From z In db.ZIPS Where z.CITYTYPE = "D" AndAlso z.ZIP = teShZip.EditValue.ToString() Select z).FirstOrDefault()

            If zipInfo IsNot Nothing Then
                Dim coInfo = CType(bsCompInfo.DataSource, COMPANY)
                coInfo.SH_CITY = zipInfo.CITY
                coInfo.SH_STATE = zipInfo.STATE
                coInfo.SH_ZIP = zipInfo.ZIP

                teShCity.EditValue = zipInfo.CITY
                cbeShStats.EditValue = zipInfo.STATE

                Dim phone = System.Text.RegularExpressions.Regex.Replace(teShPhone.EditValue.ToString(), "[^0-9]", "")
                If phone.Length < 4 AndAlso phone <> zipInfo.AREACODE Then
                    teShPhone.EditValue = zipInfo.AREACODE
                    coInfo.SH_PHONE = zipInfo.AREACODE
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("error in teShZip_EditValueChanged", ex)
        End Try
    End Sub

    Sub GetClosedStatus(CoNum As Decimal)
        Try
            Dim udfDesc = db.CO_UDFs.Where(Function(c) c.CONUM = CoNum AndAlso c.UDF_DESCR = "INACTIVE STATUS").SingleOrDefault?.UDF_STRING
            teClosedStatus.EditValue = udfDesc
        Catch ex As Exception
            DisplayErrorMessage("error in getting closed status", ex)
        End Try
    End Sub
End Class
