﻿Imports DevExpress.Utils.Menu

Public Class frmDelayDD
    Dim popupMenu As DXPopupMenu
    Dim menuPrintPreview As New DXMenuItem() With {.Caption = "Print Preview"}

    Private Sub frmDelayDD_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            popupMenu = New DXPopupMenu()
            menuPrintPreview = New DXMenuItem() With {.Caption = "Print Preview"}
            menuPrintPreview.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.preview_16x16
            popupMenu.Items.Add(menuPrintPreview)
            AddHandler menuPrintPreview.Click, AddressOf btnPrint_Click
            DropDownButtonPrint.DropDownControl = popupMenu

            'Dim DB = New dbEPDataDataContext(GetConnectionString)
            GridControl1.DataSource = Query("SELECT	c.CONUM, c.CO_NAME, p.PRNUM, p.CHECK_DATE, rma.Status, DelayDdUploadDate 
FROM custom.RiskManagementAlerts rma
INNER JOIN PAYROLL p ON p.CONUM = rma.CoNum AND p.PRNUM = rma.PrNum
INNER JOIN COMPANY c ON c.CONUM = rma.CoNum
AND rma.Status IN('Delayed DD', 'Wire Requested') AND p.PAYROLL_STATUS NOT IN ('Done')")
        Catch ex As Exception
            DisplayErrorMessage("error in loading form", ex)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Close()
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs)
        Try
            Dim Report = Query(Of ReportEmailTeplate)("SELECT * FROM custom.ReportEmailTeplate ret WHERE ret.ID = 462").First()
            Dim Co As COMPANY = Query(Of COMPANY)("SELECT top 1 * FROM Company").First()
            Dim processor = New ReportProcessor(Co, Report, FileType.Pdf) With {.showParametersForm = False}
            Dim result = processor.ProcessReport()

            If result.AllFileExist AndAlso result.Paths.Count <> 0 Then
                If sender Is menuPrintPreview Then
                    'System.Diagnostics.Process.Start(result.Paths(0))
                    Dim psi As New System.Diagnostics.ProcessStartInfo()
                    psi.FileName = result.Paths(0)
                    psi.UseShellExecute = True
                    System.Diagnostics.Process.Start(psi)
                    Return
                End If

                Dim startInfo = New ProcessStartInfo With {
                    .Verb = "PrintTo",
                    .FileName = result.Paths(0),
                    .CreateNoWindow = True,
                    .WindowStyle = ProcessWindowStyle.Minimized,
                    .UseShellExecute = True,
                    .Arguments = Nothing 'New Printing.PrinterSettings().PrinterName
                }
                Process.Start(startInfo)

            End If
        Catch ex As Exception
            DisplayErrorMessage("error printing report", ex)
        End Try
    End Sub
End Class