﻿Imports System.ComponentModel

Public Class ucDeliveryHistory
    Sub New()
        InitializeComponent()
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal

    Private Sub frmDeliveryHistory_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Public Sub LoadData()
        Try
            Dim DB As New dbEPDataDataContext(GetConnectionString)
            Dim Q = (From A In DB.view_Deliveries Where A.CoNum = CoNum Order By A.ScannedDate Descending).Take(200).ToList
            Me.GridControl1.DataSource = Q.ToList
            GridView1.BestFitColumns()
            slueCompany.Properties.DataSource = DB.view_CompanySumarries.ToList()
        Catch ex As Exception
            DisplayErrorMessage("Error loading deliveries", ex)
        End Try
    End Sub

    Private Sub slueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles slueCompany.EditValueChanged
        Dim co As Decimal
        If Decimal.TryParse(nz(slueCompany.EditValue, ""), co) Then
            Dim DB As New dbEPDataDataContext(GetConnectionString)
            Dim Q = (From A In DB.view_Deliveries Where A.CoNum = co Order By A.ScannedDate Descending).Take(200).ToList
            Me.GridControl2.DataSource = Q.ToList
            GridView2.BestFitColumns()
        End If
    End Sub
End Class

Partial Class view_Delivery
    Public ReadOnly Property Address As String
        Get
            Return $"{ShipCo}{vbCrLf}{ShipAddress}{vbCrLf}{ShipCity}, {ShipState}, {ShipZip} {vbTab} {ShipPhone}"
        End Get
    End Property
End Class