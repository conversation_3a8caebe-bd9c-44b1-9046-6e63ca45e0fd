﻿Module Files

    Public Sub OpenFile(FileName As String)
        Dim p As New Process()
        Dim ps As New ProcessStartInfo(FileName) With {.UseShellExecute = True, .WindowStyle = ProcessWindowStyle.Maximized}
        p.StartInfo = ps
        Try
            p.Start()
        Catch ex As Exception
            If ex.Message = "The system cannot find the file specified" Then
                DisplayErrorMessage($"File not found{vbCrLf}File Name: {FileName}", ex)
            Else
                DisplayErrorMessage("Error opening file [{0}]".FormatWith(FileName), ex)
            End If
        End Try
    End Sub

    Public Function GetIconForFile(FileName As String) As Icon
        Dim Ext = IO.Path.GetExtension(FileName)
        If String.IsNullOrEmpty(Ext) Then Return Nothing

        'Use the function to get the path to the executable for the file
        Dim exePath = GetAssociatedProgram(Ext)

        'Use ExtractAssociatedIcon to get an icon from the path
        If exePath.IsNullOrWhiteSpace Then Return Nothing
        Try
            Dim exeIcon = Icon.ExtractAssociatedIcon(exePath)
            Return exeIcon
        Catch ex As Exception
            Logger.Error(ex, "Error Getting GetIconForFile for File: {File}", FileName)
            Return Nothing
        End Try
    End Function

    Public Function GetAssociatedProgram(ByVal FileExtension As String) As String
        ' Returns the application associated with the specified FileExtension
        ' ie, path\denenv.exe for "VB" files
        Dim objExtReg As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.ClassesRoot
        Dim objAppReg As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.ClassesRoot
        Dim strExtValue As String
        Try
            ' Add trailing period if doesn't exist
            If FileExtension.Substring(0, 1) <> "." Then FileExtension = "." & FileExtension
            ' Open registry areas containing launching app details
            objExtReg = objExtReg.OpenSubKey(FileExtension.Trim)
            strExtValue = objExtReg?.GetValue("").ToString
            objAppReg = objAppReg.OpenSubKey(strExtValue & "\shell\open\command")

            If objAppReg Is Nothing Then Return ""

            ' Parse out, tidy up and return result
            Dim SplitArray() As String
            SplitArray = Split(objAppReg.GetValue(Nothing).ToString, """")
            If SplitArray(0).Trim.Length > 0 Then
                Return SplitArray(0).Replace("%1", "")
            Else
                Return SplitArray(1).Replace("%1", "")
            End If
        Catch
            Return ""
        End Try
    End Function

End Module
