﻿Public Class frmCommPlans

    Dim _DB As dbEPDataDataContext
    Dim _Ent As CommPlan
    Dim _ItemLoaded As Boolean

    Private Sub frmCommPlans_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Loadlist()
    End Sub

    Sub Loadlist()
        Dim DB = New dbEPDataDataContext(GetConnectionString)
        Me.CommPlanBindingSourceList.DataSource = DB.CommPlans.ToList
        Me.lstCommPlans.SelectedIndex = 0
        If Me.CommPlanBindingSourceList.Count = 0 Then
            lstCommPlans_SelectedIndexChanged(Me, New EventArgs)
        End If
        Me.lstCommPlans.Enabled = True
    End Sub

    Private Sub lstCommPlans_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lstCommPlans.SelectedIndexChanged
        Dim SelectedPlan As CommPlan = Me.lstCommPlans.SelectedItem
        If SelectedPlan IsNot Nothing Then
            LoadPlan(SelectedPlan.PlanID)
        End If
        Me.SplitContainerControl1.Panel2.Enabled = SelectedPlan IsNot Nothing
        Me.btnAddNew.Enabled = True
        Me.btnDelete.Enabled = SelectedPlan IsNot Nothing
        Me.btnSave.Enabled = False
        Me.btnCancel.Enabled = False
    End Sub

    Sub LoadPlan(ByVal PlanID As Guid)
        _ItemLoaded = False
        _DB = New dbEPDataDataContext(GetConnectionString)
        If Not PlanID.Equals(Guid.Empty) Then
            _Ent = (From A In _DB.CommPlans Where A.PlanID = PlanID).Single
        Else
            _Ent = New CommPlan
        End If
        Me.CommPlanBindingSource.DataSource = _Ent
        Me.CommPlanDetailBindingSource.DataSource = (From A In _Ent.CommPlanDetails Order By A.StartDay).ToList
        Me.CommPlanDetailsGridControl.DataSource = Me.CommPlanDetailBindingSource
        _ItemLoaded = True
    End Sub

    Private Sub btnAddNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddNew.Click
        Me.lstCommPlans.Enabled = False
        LoadPlan(Guid.Empty)
        Me.SplitContainerControl1.Panel2.Enabled = True
        Me.btnAddNew.Enabled = False
        Me.btnDelete.Enabled = False
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If Me.CommPlanDetailBindingSource.Count = 0 Then
            If DevExpress.XtraEditors.XtraMessageBox.Show("You didn't enter any details for this plan. Save anyway?", "No detail", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                Exit Sub
            End If
        End If
        If Not Me.PlanNameTextEdit.HasValue Then
            DisplayMessageBox("Plan Name is required")
            Exit Sub
        Else
            Dim Q = (From A In _DB.CommPlans Where A.PlanName = _Ent.PlanName AndAlso A.PlanID <> _Ent.PlanID).Count
            If Q > 0 Then
                DisplayMessageBox("The name '" & _Ent.PlanName & "' is already in use")
                Exit Sub
            End If
        End If
        If _Ent.PlanID.Equals(Guid.Empty) Then
            _Ent.PlanID = Guid.NewGuid
            _DB.CommPlans.InsertOnSubmit(_Ent)
        End If
        _DB.SubmitChanges()
        Loadlist()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Loadlist()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If _Ent IsNot Nothing Then
            Dim UsedCount = _Ent.CommCompanySetups.Count
            If UsedCount > 0 Then
                DisplayMessageBox("Cannot delete plan, because it is used on companies")
                Exit Sub
            End If
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to delete this plan?", "Confirm delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes Then
                _DB.CommPlans.DeleteOnSubmit(_Ent)
                _DB.SubmitChanges()
            End If
        End If
        Loadlist()
    End Sub

    Private Sub GridViewCommPlanDetails_ValidateRow(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridViewCommPlanDetails.ValidateRow
        Dim Row As CommPlanDetail = e.Row
        If Not (Row.StartDay.HasValue AndAlso Row.EndDay.HasValue AndAlso Row.CommPct.HasValue) Then
            e.Valid = False
            e.ErrorText = "Please fill all columns"
        Else
            If Row.ID.Equals(Guid.Empty) Then
                Row.ID = Guid.NewGuid
                _Ent.CommPlanDetails.Add(Row)
            End If
        End If
    End Sub

    Private Sub GridViewCommPlanDetails_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewCommPlanDetails.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", AddressOf DeleteRow, My.Resources.Action_Delete))
        End If
    End Sub

    Private Sub DeleteRow()
        Dim Row = Me.GridViewCommPlanDetails.GetRow(Me.GridViewCommPlanDetails.FocusedRowHandle)
        Me.CommPlanDetailBindingSource.Remove(Row)
        _DB.CommPlanDetails.DeleteOnSubmit(Row)
    End Sub

    Private Sub CommPlanDetailBindingSource_CurrentItemChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CommPlanDetailBindingSource.CurrentItemChanged, CommPlanBindingSource.CurrentItemChanged
        If Not _ItemLoaded Then Exit Sub
        Me.lstCommPlans.Enabled = False
        Me.btnAddNew.Enabled = False
        Me.btnSave.Enabled = True
        Me.btnCancel.Enabled = True
    End Sub
End Class