﻿Imports System.ComponentModel
Imports DevExpress.XtraSplashScreen

Public Class frmCalendar

    Dim DB As dbEPDataDataContext
    Dim _IsLoaded As Boolean
    Dim _SelectedAutos As List(Of Integer)

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoRunAutos As Boolean

    Private _RunAutos As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property RunAutos() As Boolean
        Get
            Return _RunAutos
        End Get
        Set(ByVal value As Boolean)
            If value Then
                _SelectedAutos = New List(Of Integer)
            End If
            _RunAutos = value
            colSelectAuto.Visible = value
            colSelectAuto.VisibleIndex = 0
            pnlAutoPays.Visible = value
            Me.ddlEntryType.EditValue = "Auto PR"
            If value Then
                LoadData()
            End If
        End Set
    End Property

    Private Sub frmCalendar_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        If Me.AutoRunAutos Then
            Me.RunAutos = True
        Else
            LoadData()
        End If

        Me.gridCalendar.ForceInitialize()

        'Make all read only
        For Each col As DevExpress.XtraGrid.Columns.GridColumn In Me.gridViewCalendar.Columns
            If col IsNot colSelectAuto Then _
                col.OptionsColumn.ReadOnly = True
        Next

        _IsLoaded = True
        Me.gridViewCalendar.FocusedRowHandle = -1
        Me.gridViewCalendar.Focus()
        Me.gridCalendar.Select()
        If Me.gridViewCalendar.RowCount > 0 Then
            Me.gridViewCalendar_FocusedRowChanged(sender, New DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs(-1, 0))
        End If
    End Sub

    'Dim CurrentRecNotes As List(Of CalendarNote)

    Private Sub frmCalendar_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        DB = New dbEPDataDataContext(GetConnectionString)
        DB.CommandTimeout = 1200  '4 Minutes 240
        Me.FromDateDE.EditValue = Today.AddDays(-6)
        Me.ToDateDE.EditValue = Today

        Dim AllUsers = (From A In DB.DBUSERs Order By A.name Select A.name).ToList
        Me.ByUserCB.Properties.Items.AddRange(AllUsers)
        If Not Me.AutoRunAutos Then Me.ByUserCB.EditValue = UserName

        Dim EntryTypes = (From A In DB.CALENDAR_RULES Where A.entry_type IsNot Nothing Select A.entry_type).Distinct.OrderBy(Function(p) p).ToArray
        Me.ddlEntryType.Properties.Items.AddRange(EntryTypes)
        If Permissions.CalenderDefaultEntryTypeFilter IsNot Nothing Then
            Dim Include = Permissions.CalenderDefaultEntryTypeFilterType = "IN"
            Dim Values = (From A In Permissions.CalenderDefaultEntryTypeFilter.Split(",") Select A.Trim).ToList
            For X = 0 To EntryTypes.Length - 1
                If Values.Contains(EntryTypes(X)) Then
                    ddlEntryType.Properties.Items(X).CheckState = If(Include, CheckState.Checked, CheckState.Unchecked)
                End If
            Next
        End If
        SplitContainerControl1.SplitterPosition = Me.Height - (Me.Height / 5)
        'ViewCalendarBindingSource_CurrentChanged(sender, e)
    End Sub

    Public Sub LoadData(Optional ByVal KeepPosition As Boolean = False)

        SplashScreenManager.ShowForm(Me, GetType(WaitForm1), True, True, False)

        Dim CurrenRowList As List(Of view_Calendar) = New List(Of view_Calendar)
        If KeepPosition AndAlso Me.gridViewCalendar.FocusedRowHandle > 0 Then

            Dim i As Int32 = gridViewCalendar.FocusedRowHandle + 1
            For index = 0 To 10

                While True
                    Dim curRow As view_Calendar = gridViewCalendar.GetRow(i)

                    If IsNothing(curRow) Then
                        Exit For
                    End If

                    If curRow.PayNum Is Nothing AndAlso gridViewCalendar.IsRowVisible(i) Then
                        CurrenRowList.Add(curRow)
                        i += 1
                        Exit While
                    Else
                        i += 1
                    End If
                End While
            Next

        End If


        Me.ToolStripStatusLabel1.Text = "Loading Data"
        Me.gridCalendar.Enabled = False
        Application.DoEvents()
        DB = New dbEPDataDataContext(GetConnectionString)

        Dim FromDate, ToDate As Date?, EntryTypes As String = Nothing, OpOwner As String = Nothing
        If Me.FromDateDE.HasValue Then
            FromDate = Me.FromDateDE.EditValue
        End If
        If Me.ToDateDE.HasValue Then
            ToDate = Me.ToDateDE.EditValue
            ToDate = ToDate.Value.AddHours(23.99)
        End If
        If Me.ddlEntryType.HasValue Then
            EntryTypes = Me.ddlEntryType.EditValue
        End If
        If Me.ByUserCB.HasValue Then
            OpOwner = Me.ByUserCB.EditValue.ToString
        End If
        Dim Results = DB.prc_Calendar(FromDate, ToDate, EntryTypes, OpOwner, chkExcludeJ.Checked, chkIncludeJ.Checked).ToList

        If Not chkShowDoNotCallAndSpecialistOnly.Checked Then
            Results = (From A In Results Where (Not A.DoNotCall.HasValue OrElse Not A.DoNotCall.Value) AndAlso (Not A.SpecialistCall.HasValue OrElse Not A.SpecialistCall.Value)).ToList()
        End If

        If Not RunAutos Then
            If Results.Count = 0 AndAlso Me.ByUserCB.HasValue Then
                Me.ByUserCB.EditValue = Nothing
                LoadData()
                Exit Sub
            End If
        End If
        If RunAutos Then
            _SelectedAutos.Clear()
            For Each row In Results
                If Not row.PayNum.HasValue AndAlso (row.UDF4Data & "").ToUpper = "YES" AndAlso (Not row.DoNotProcessInAutoSystem.HasValue OrElse Not row.DoNotProcessInAutoSystem.Value) AndAlso String.IsNullOrEmpty(row.PRPassword) Then
                    Dim d As Date
                    If row.ProcessTime.IsNullOrWhiteSpace() OrElse (Not Date.TryParse(row.ProcessTime, d)) OrElse d.TimeOfDay <= DateTime.Now.TimeOfDay Then
                        _SelectedAutos.Add(row.CalID)
                    End If
                End If
            Next
            RefreshSelectedCount()
        End If
        Me.ViewCalendarBindingSource.DataSource = Results
        Me.gridViewCalendar.BestFitColumns()
        Me.ToolStripStatusLabel1.Text = "Ready - " & Results.Count & " record" & If(Results.Count = 1, "", "s") & " loaded"

        'If Results.Count > 0 AndAlso KeepPosition AndAlso CurrenRow IsNot Nothing Then
        'Try
        '_IsLoaded = False
        'Me.gridViewCalendar.BeginUpdate()
        'Dim FindRowIX = Me.ViewCalendarBindingSource.Add(CurrenRow)
        'Me.gridViewCalendar.RefreshData()
        'Dim SelectedRowIX = Me.gridViewCalendar.GetRowHandle(FindRowIX)
        'Me.ViewCalendarBindingSource.Remove(CurrenRow)
        'Me.gridViewCalendar.RefreshData()
        'Me.gridViewCalendar.FocusedRowHandle = SelectedRowIX
        'Me.gridViewCalendar_FocusedRowChanged(Me.gridViewCalendar, New DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs(-1, SelectedRowIX))
        'Me.gridViewCalendar.EndUpdate()
        ''End If
        ' Catch ex As Exception
        'DisplayErrorMessage(ex.Message)
        ' End Try
        '_IsLoaded = True
        'Me.gridViewCalendar.EndDataUpdate()
        'End If


        CancelledSelection = True
        For index = 1 To CurrenRowList.Count
            Dim inde = index - 1
            Dim i = Results.FindIndex(Function(v) v.CalID = CurrenRowList(inde).CalID)
            i = gridViewCalendar.LocateByDisplayText(0, colCoNum, CurrenRowList(inde).CoNum)

            If i > 0 Then
                If Results(i).PayNum Is Nothing Then
                    gridViewCalendar.MakeRowVisible(i)
                    gridViewCalendar.FocusedRowHandle = i
                    Exit For
                End If
            End If
        Next



        'Me.gridViewCalendar.MakeRowVisible(Me.gridViewCalendar.FocusedRowHandle)
        Me.gridCalendar.Enabled = True
        SplashScreenManager.CloseForm(False)
    End Sub

    Private Sub FromDateDE_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles _
                    ToDateDE.EditValueChanged,
                    FromDateDE.EditValueChanged,
                    ByUserCB.Validated,
                    ddlEntryType.Validated, chkExcludeJ.CheckedChanged, chkShowDoNotCallAndSpecialistOnly.CheckedChanged, chkIncludeJ.CheckedChanged
        If Not _IsLoaded Then Exit Sub

        Static IgnoreEvent As Boolean = False
        If IgnoreEvent Then
            Return
        End If

        Dim ctrl As DevExpress.XtraEditors.BaseEdit = sender

        If ctrl.Equals(chkIncludeJ) AndAlso chkIncludeJ.Checked AndAlso chkExcludeJ.Checked Then
            IgnoreEvent = True
            chkExcludeJ.Checked = False
            IgnoreEvent = False
        ElseIf ctrl.Equals(chkExcludeJ) AndAlso chkExcludeJ.Checked AndAlso chkIncludeJ.Checked Then
            IgnoreEvent = True
            chkIncludeJ.Checked = False
            IgnoreEvent = False
        End If

        If ctrl.IsModified Then
            ctrl.IsModified = False
            Try
                LoadData()
            Catch ex As Exception
                DisplayErrorMessage("Error loading data.", ex)
            End Try
        End If
    End Sub

    Dim CallingFromRowHandle As Integer
    Dim CancelledSelection As Boolean
    Private Sub gridViewCalendar_FocusedRowChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles gridViewCalendar.FocusedRowChanged
        If Not _IsLoaded Then Exit Sub
        If e.FocusedRowHandle >= 0 Then
            If e.PrevFocusedRowHandle >= 0 AndAlso Not CancelledSelection AndAlso Not RunAutos Then
                Try
                    Dim TodaysNotes = (From A As CalendarNote In Me.NotesBindingSource.List Where A.DateEntered > Now.AddMinutes(-5)).FirstOrDefault
                    If TodaysNotes Is Nothing AndAlso CallingFromRowHandle <> e.FocusedRowHandle Then
                        Dim Results = MessageBox.Show("You did not enter any payroll/notes for this record." & vbCrLf & "Do you want to enter a note now?", "Confirm Leave", MessageBoxButtons.YesNoCancel)
                        If Results = System.Windows.Forms.DialogResult.Cancel Then
                            CallingFromRowHandle = e.PrevFocusedRowHandle
                            Me.gridViewCalendar.FocusedRowHandle = e.PrevFocusedRowHandle
                            Exit Sub
                        ElseIf Results = System.Windows.Forms.DialogResult.Yes Then
                            CallingFromRowHandle = e.PrevFocusedRowHandle
                            Me.gridViewCalendar.FocusedRowHandle = e.PrevFocusedRowHandle
                            Me.ActiveControl = Me.gridCalendarNotes
                            Me.gridCalendarNotes.Focus()
                            Me.GridViewCalendarNotes.Focus()
                            Me.GridViewCalendarNotes.FocusedColumn = Me.GridViewCalendarNotes.Columns("NoteType")
                            Me.GridViewCalendarNotes.ShowEditor()
                            Exit Sub
                        End If
                    End If
                Catch ex As Exception
                    Dim P = "Me"
                End Try
            End If
            'TODO if not working FIX
            'Dim Row = Me.gridViewCalendar.GetRow(e.FocusedRowHandle)
            'GetNotes(Row)
            'If Row.ProcessTime <> "" Then
            '    Me.txtSetProcessTime.Time = Row.ProcessTime
            'Else
            '    Me.txtSetProcessTime.Time = #12:00:00 AM# 'DateTime.MaxValue
            'End If

        End If
        CancelledSelection = False
    End Sub

    Sub GetNotes(ByVal Row As view_Calendar)
        Dim Notes = (From A In DB.CalendarNotes
                     Order By A.CalID
                     Where A.CalID = Row.CalID AndAlso A.CoNum = Row.CoNum AndAlso A.PeriodID = Row.PeriodID AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).ToList
        Me.NotesBindingSource.DataSource = Notes
        Me.Notes_tsBindingSource.DataSource = (From A In DB.NOTEs
                                               Where A.conum = Row.CoNum _
                                               AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) _
                                               AndAlso A.category <> "Employee"
                                               Order By If(A.category = "Delivery", 1, 2)).ToList

        'Me.lblCompanyHeader.Text = "Company: " & Row.CoName & vbTab & vbTab & "Contact: " & Row.PrContact & vbTab & vbTab & _
        '                    "Employee Count: " & Row.EmpCount & vbCrLf & _
        '                    "Phone: " & Row.CoPhone & " " & Row.Ext & vbTab & vbTab & _
        '                    "Fax: " & Row.CoFax & vbTab & vbTab & "Modem: " & Row.CoModem

        bsiCompany.Caption = Row.CoName
        bsiContact.Caption = Row.PrContact
        bsiEmpsCount.Caption = If(Row.EmpCount.HasValue, Row.EmpCount.Value, "")
        bbiCallOffice.Caption = Row.CoPhone & If(Row.Ext.IsNotNullOrWhiteSpace(), " X " & Row.Ext, "")
        bbiCellPhone.Caption = Row.CoModem
        bsiFax.Caption = "Fax: " & Row.CoFax

        Dim Udf4Value = Row.UDF4Data
        bbiOpenTimeSheet.ItemAppearance.Normal.Font = New Font(bbiOpenTimeSheet.ItemAppearance.Normal.Font, FontStyle.Regular)
        If Udf4Value = "Yes" Then
            bbiOpenTimeSheet.ItemAppearance.Normal.ForeColor = Color.DarkGreen
            bbiOpenTimeSheet.ItemAppearance.Normal.Font = New Font(bbiOpenTimeSheet.ItemAppearance.Normal.Font, FontStyle.Bold)
        ElseIf Udf4Value = "No" Then
            bbiOpenTimeSheet.ItemAppearance.Normal.ForeColor = Color.Red
        Else
            bbiOpenTimeSheet.ItemAppearance.Normal.ForeColor = Color.FromName("ControlText")
        End If
    End Sub

    Private Sub GridViewCalendarNotes_RowUpdated(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridViewCalendarNotes.RowUpdated
        Try
            Dim CurrentRec As view_Calendar = Me.ViewCalendarBindingSource.Current
            If CurrentRec Is Nothing Then Exit Sub
            Dim Row As CalendarNote = e.Row
            If Row.NoteID = 0 Then
                Row.CalID = CurrentRec.CalID
                Row.CoNum = CurrentRec.CoNum
                Row.PeriodID = CurrentRec.PeriodID
                Row.EnteredBy = UserName
                Row.DateEntered = Now
                DB.CalendarNotes.InsertOnSubmit(Row)
            Else
                'Solomon modified, need to check if working
                'Dim Modifications = DB.CalendarNotes.GetModifiedMembers(Row)
                'If Modifications.Length > 0 Then
                '    Dim OrigNote As String = Row.Note
                '    Dim OrigFollowUp As Date? = Row.FollowUpOn
                '    For Each itm In Modifications
                '        If itm.Member.Name = "Note" Then
                '            OrigNote = itm.OriginalValue
                '        ElseIf itm.Member.Name = "FollowUpOn" Then
                '            OrigFollowUp = itm.OriginalValue
                '        End If
                '    Next
                '    Dim Log As New CalendarNote With {.CalID = Row.CalID, .CoNum = Row.CoNum, .PeriodID = Row.PeriodID,
                '                                                      .Note = OrigNote, .FollowUpOn = OrigFollowUp,
                '                                                      .EnteredBy = Row.EnteredBy, .DateEntered = Row.DateEntered,
                '                                                      .IsDeleted = True, .DeletedBy = UserName, .DateDeleted = Now}
                '    DB.CalendarNotes.InsertOnSubmit(Log)
                '    'Change user and date
                '    Row.EnteredBy = UserName
                '    Row.DateEntered = Now
                'End If

                Dim entry = DB.Entry(Of CalendarNote)(Row)
                If entry.State = Microsoft.EntityFrameworkCore.EntityState.Modified Then
                    Dim OrigNote As String = Row.Note
                    Dim OrigFollowUp As Date? = Row.FollowUpOn

                    For Each prop In entry.Properties
                        Dim propertyName = prop.Metadata.Name
                        Dim originalValue = prop.OriginalValue
                        Dim currentValue = prop.CurrentValue

                        If propertyName = "Note" Then
                            OrigNote = originalValue
                        ElseIf propertyName = "FollowUpOn" Then
                            OrigFollowUp = originalValue
                        End If
                    Next

                    Dim Log As New CalendarNote With {.CalID = Row.CalID, .CoNum = Row.CoNum, .PeriodID = Row.PeriodID,
                                                                      .Note = OrigNote, .FollowUpOn = OrigFollowUp,
                                                                      .EnteredBy = Row.EnteredBy, .DateEntered = Row.DateEntered,
                                                                      .IsDeleted = True, .DeletedBy = UserName, .DateDeleted = Now}
                    DB.CalendarNotes.InsertOnSubmit(Log)
                    'Change user and date
                    Row.EnteredBy = UserName
                    Row.DateEntered = Now
                End If
            End If
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save calendar notes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
            'Me.gridViewCalendar.CollapseAllDetails()
            'Me.gridViewCalendar.MoveNext()
            LoadData(True)
        Catch ex As Exception
            DisplayErrorMessage("Error saving calendar notes", ex)
        End Try
    End Sub

    Private Sub GridViewCalendarNotes_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewCalendarNotes.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete Note", AddressOf OnDeleteRowClick)
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Private Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Try
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to delete this note?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.Yes Then
                Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
                Dim rowHandle As Integer = item.Tag
                Dim View = Me.GridViewCalendarNotes
                Dim Row As CalendarNote = View.GetRow(View.FocusedRowHandle)
                Row.IsDeleted = True
                Row.DeletedBy = UserName
                Row.DateDeleted = Now
                DB.SubmitChanges()
                View.DeleteRow(rowHandle)
                LoadData(True)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting note.", ex)
        End Try
    End Sub

    Private Sub gridViewCalendar_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gridViewCalendar.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Open Timesheet", AddressOf OnStartPayrollClicked) With {.Tag = e.HitInfo.RowHandle}
            e.Menu.Items.Add(mItem)

            mItem = New DevExpress.Utils.Menu.DXMenuItem("Set Skipped", AddressOf OnSkipRowClick)
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)

            mItem = New DevExpress.Utils.Menu.DXMenuItem("Attach to payroll", AddressOf OnAttachToPayrollClick)
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)

            mItem = New DevExpress.Utils.Menu.DXMenuItem("Cancel", AddressOf OnCancelButtonClicked) With {.Tag = e.HitInfo.RowHandle, .BeginGroup = True}
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Private Sub OnSkipRowClick(ByVal sender As Object, ByVal e As EventArgs)
        DevExpress.XtraEditors.XtraMessageBox.Show("Enter skip reason in the notes", "", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Dim view = Me.GridViewCalendarNotes
        view.Focus()
        view.AddNewRow()
        view.SetFocusedRowCellValue("NoteType", "Skip This Calendar")
        view.FocusedColumn = view.Columns("Note")
        view.ShowEditor()
        'SkipRow(rowHandle)
    End Sub

    Private Sub OnAttachToPayrollClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        AttachToPayroll(rowHandle)
    End Sub

    Private Sub OnStartPayrollClicked(ByVal sender As System.Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As view_Calendar = Me.gridViewCalendar.GetRow(rowHandle)
        MainForm.OpenPowerGridPayroll(New OpenPowerGridPayrollOptions(Row.CoNum) With {.CallBackForm = Me})
    End Sub

    Private Sub OnCancelButtonClicked(ByVal sender As System.Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        CancelledSelection = True
    End Sub

    Private Function SkipRow() As Boolean
        Dim Row As view_Calendar = Me.gridViewCalendar.GetRow(Me.gridViewCalendar.FocusedRowHandle)
        If Row Is Nothing Then Return False
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure client wants to skip this calendar?", "Confirm Skip", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
            Return False
        End If

        Dim CalRec = (From A In DB.CALENDARs Where A.cal_id = Row.CalID AndAlso A.conum = Row.CoNum AndAlso A.period_id = Row.PeriodID).Single
        CalRec.completed = "Skipped"
        If Not DB.SaveChanges() Then
            DisplayErrorMessage("Unable to save calendar skip status due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
            Return False
        End If
        Return True
    End Function

    Private Sub AttachToPayroll(ByVal RowHandle As Integer)
        Dim Row As view_Calendar = Me.gridViewCalendar.GetRow(Me.gridViewCalendar.FocusedRowHandle)
        If Row Is Nothing Then Exit Sub
        Dim frm As New frmCalendarLinkToPayroll With {.CalRecord = Row}
        Dim results = frm.ShowDialog()
        If results = System.Windows.Forms.DialogResult.OK Then
            LoadData(True)
        End If
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub

    Private Sub btnResetSort_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnResetSort.Click
        Me.gridViewCalendar.ClearSorting()
        Me.gridViewCalendar.SortInfo.Add(colRowNumber, DevExpress.Data.ColumnSortOrder.Ascending)
    End Sub

    Private Sub riAddNote_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riAddNote.EditValueChanged
        Dim editor As DevExpress.XtraEditors.ComboBoxEdit = Me.GridViewCalendarNotes.ActiveEditor
        If editor.HasValue Then
            Dim FollowUpTime As Date = Now.AddHours(3)
            If FollowUpTime > Today.AddHours(16.5) Then
                FollowUpTime = Today.AddDays(1).AddHours(9)
            End If
            While Not (FollowUpTime.DayOfWeek > 0 AndAlso FollowUpTime.DayOfWeek < 6)
                FollowUpTime = FollowUpTime.AddDays(1).Date.AddHours(9)
            End While
            Dim View = Me.GridViewCalendarNotes
            View.SetFocusedRowCellValue(colNoteType, editor.EditValue)
            If editor.EditValue = "Left Message" OrElse editor.EditValue = "Call Back" OrElse editor.EditValue = "No Answer" Then
                View.SetRowCellValue(View.FocusedRowHandle, "FollowUpOn", FollowUpTime)
            ElseIf editor.EditValue = "Skip This Calendar" Then
                View.FocusedColumn = colNote
            ElseIf editor.EditValue = "No More Payroll" Then
                bbiNoMorePayroll.PerformClick()
            End If
        End If
    End Sub

    Private Sub riAddNote_EditValueChanging(ByVal sender As Object, ByVal e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles riAddNote.EditValueChanging
        Dim view = Me.GridViewCalendarNotes
        If Not view.IsNewItemRow(view.FocusedRowHandle) Then
            e.Cancel = True
        End If
    End Sub

    Private Sub GridViewCalendarNotes_ValidateRow(ByVal sender As Object, ByVal e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridViewCalendarNotes.ValidateRow
        Dim View = Me.GridViewCalendarNotes
        Dim Row As CalendarNote = View.GetRow(View.FocusedRowHandle)
        If Row.NoteID = 0 Then
            If Row.NoteType Is Nothing Then
                Row.NoteType = "Other"
            End If
            If Row.NoteType = "Skip This Calendar" Then
                If String.IsNullOrEmpty(Row.Note) Then
                    DevExpress.XtraEditors.XtraMessageBox.Show("Enter skip reason in the notes", "", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    View.FocusedColumn = colNote
                    e.Valid = False
                    e.ErrorText = "{Note Is Required}"
                ElseIf Not SkipRow() Then
                    e.Valid = False
                    e.ErrorText = "{Skip Cancelled}"
                End If
            End If
        End If
    End Sub

    Private Sub GridViewCalendarNotes_InvalidRowException(ByVal sender As Object, ByVal e As DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs) Handles GridViewCalendarNotes.InvalidRowException
        Dim View = Me.GridViewCalendarNotes
        If e.ErrorText = "{Note Is Required}" Then
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction
            View.FocusedRowHandle = e.RowHandle
            View.FocusedColumn = colNote
            View.ShowEditor()
        ElseIf e.ErrorText = "{Skip Cancelled}" Then
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore
        End If
    End Sub

    Private Sub gridViewCalendar_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles gridViewCalendar.KeyDown
        If Not e.KeyCode = Keys.Escape Then Return
        CancelledSelection = True
    End Sub

    Private Sub gridViewCalendar_CustomColumnSort(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CustomColumnSortEventArgs) Handles gridViewCalendar.CustomColumnSort
        If Not e.Column.FieldName = "DelDesc" Then Return

        Dim Row1 As view_Calendar = e.RowObject1
        Dim Row2 As view_Calendar = e.RowObject2
        Dim Drivers = {"B", "R", "S", "O", "L"}
        If Drivers.Contains(Row1.DelDesc) AndAlso Drivers.Contains(Row2.DelDesc) Then
            e.Result = String.Compare(Row1.DelDesc, Row2.DelDesc)
        ElseIf Drivers.Contains(Row1.DelDesc) Or Drivers.Contains(Row2.DelDesc) Then
            If Drivers.Contains(Row1.DelDesc) AndAlso Not Drivers.Contains(Row2.DelDesc) Then
                e.Result = -1
            ElseIf Not Drivers.Contains(Row1.DelDesc) AndAlso Drivers.Contains(Row2.DelDesc) Then
                e.Result = 1
            End If
        ElseIf Row1.DelDesc = "Lazer Ship" Or Row2.DelDesc = "Lazer Ship" Then
            If Row1.DelDesc = "Lazer Ship" AndAlso Row2.DelDesc <> "Lazer Ship" Then
                e.Result = 1
            ElseIf Row1.DelDesc <> "Lazer Ship" AndAlso Row2.DelDesc = "Lazer Ship" Then
                e.Result = -1
            Else
                e.Result = 0
            End If
        Else
            e.Result = String.Compare(Row1.DelDesc, Row2.DelDesc)
        End If
        e.Handled = True
    End Sub

    Private Sub GridViewCalendarNotes_CustomRowCellEdit(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridViewCalendarNotes.CustomRowCellEdit
        If e.Column.FieldName = "NoteType" Then
            e.RepositoryItem = Me.riAddNote
        ElseIf e.Column.FieldName = "FollowUpOn" Then
            e.RepositoryItem = Me.riFollowupDate
        End If
    End Sub

    Private Sub btnRunAutos_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRunAutos.CheckedChanged
        RunAutos = Me.btnRunAutos.Checked
    End Sub

    Private Sub riSelectRunAuto_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riSelectRunAuto.EditValueChanged
        Dim value = CType(Me.gridViewCalendar.ActiveEditor, DevExpress.XtraEditors.CheckEdit).Checked
        Dim row As view_Calendar = Me.gridViewCalendar.GetRow(Me.gridViewCalendar.FocusedRowHandle)
        If row Is Nothing Then Exit Sub
        If value Then
            If (row.UDF4Data & "").ToUpper <> "YES" Then
                DisplayMessageBox("SilverPay is not allowed for this company (UDF4)")
                value = False
            ElseIf (row.UDF21Data & "").Contains("B53") Then
                DisplayMessageBox("SilverPay auto process is not allowed for this company (UDF21 B53)")
                value = False
            ElseIf (Not String.IsNullOrEmpty(row.PRPassword)) Then
                If Not ConfirmPassword(row.PRPassword) Then
                    value = False
                End If
            End If
            If Not value Then
                Me.gridViewCalendar.SetRowCellValue(Me.gridViewCalendar.FocusedRowHandle, colSelectAuto, False)
            End If
        End If
        If value Then

            If Not _SelectedAutos.Contains(row.CalID) Then
                _SelectedAutos.Add(row.CalID)
            End If
        Else
            If _SelectedAutos.Contains(row.CalID) Then
                _SelectedAutos.Remove(row.CalID)
            End If
        End If
        Me.gridViewCalendar.PostEditor()
        Me.gridViewCalendar.UpdateCurrentRow()
    End Sub

    Private Sub gridViewCalendar_CustomUnboundColumnData(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles gridViewCalendar.CustomUnboundColumnData
        If RunAutos AndAlso e.Column Is colSelectAuto AndAlso e.IsGetData Then
            Dim Row As view_Calendar = e.Row
            e.Value = _SelectedAutos.Contains(Row.CalID)
            RefreshSelectedCount()
        End If
    End Sub

    Sub RefreshSelectedCount()
        Me.lblSelectedCount.Text = _SelectedAutos.Count & " calendar" & If(_SelectedAutos.Count = 1, "", "s") & " selected to process."
        Me.btnProcessAutos.Enabled = _SelectedAutos.Count > 0
    End Sub

    Private Sub btnProcessAutos_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnProcessAutos.Click
        ProcessAutos(True)
    End Sub

    Public Function ProcessAutos(RunOnBackgroundThread As Boolean) As frmRunAutos
        Logger.Information("Calendar: Entering ProcessAutos")
        Dim SelectedCals = (From A As view_Calendar In Me.ViewCalendarBindingSource.List Where _SelectedAutos.Contains(A.CalID)
                            Order By A.CoNum, A.CheckDate
                            Select New RunAutoStatus With {.CalID = A.CalID,
                                                           .CoNum = A.CoNum,
                                                           .CoName = A.CoName,
                                                           .Status = "Waiting",
                                                           .EndDate = A.PREnd,
                                                           .CheckDate = A.CheckDate}
                                                       ).ToList
        If Me.AutoRunAutos Then
            SetUdfValue("AutoPayrollSelectedCalsCount", SelectedCals.Count)
        End If
        Dim frm = New frmRunAutos With {.RunAutoList = SelectedCals, .CloseWhenDone = Me.AutoRunAutos, .RunOnBackgroundThread = RunOnBackgroundThread}
        Dim results = frm.ShowDialog()
        Me.LoadData()
        Return frm
    End Function

    Private Sub btnSelectNone_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectNone.Click
        _SelectedAutos.Clear()
        Me.gridViewCalendar.RefreshData()
    End Sub

    Private Sub ViewCalendarBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles ViewCalendarBindingSource.CurrentChanged
        Dim Row As view_Calendar = ViewCalendarBindingSource.Current
        GetNotes(Row)
        If Row.ProcessTime <> "" Then
            SetProcessTime(Row.ProcessTime)
            bbiChangeProcessTime.Caption = "Change Process Time" & vbCrLf & Row.ProcessTime
            'Me.txtSetProcessTime.Time = Row.ProcessTime
        Else
            'Me.txtSetProcessTime.Time = #12:00:00 AM# 'DateTime.MaxValue
            SetProcessTime("07:00AM")
        End If

        'HyperlinkLabelControl1.Text = "Co Phone: " & Row.CoPhone
        'HyperlinkLabelControl2.Text = "Mobile: " & Row.CoModem
    End Sub

    Private Sub btnAutosLog_CheckedChanged(sender As Object, e As EventArgs) Handles btnAutosLog.CheckedChanged
        Dim frm As New frmRunAutosLog
        frm.ShowDialog()
    End Sub

    Private Sub gridViewCalendar_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs) Handles gridViewCalendar.RowCellStyle
        If e.Column Is colDelDesc1 Then
            Dim row = gridViewCalendar.GetRowCellDisplayText(e.RowHandle, colDelDesc1)
            If (row = "L" OrElse row = "S") AndAlso (DateTime.Now.TimeOfDay.Hours = 12 AndAlso DateTime.Now.TimeOfDay.Minutes < 30) Then
                e.Appearance.BackColor = Color.LightCoral
            ElseIf row <> "No Delivery" Then
                e.Appearance.BackColor = Color.FromArgb(&HFF, &HFF, &H99)
                e.Appearance.ForeColor = Color.Black
            End If
        ElseIf e.Column Is colProcessTime Then
            Dim row As view_Calendar = gridViewCalendar.GetRow(e.RowHandle)
            If row Is Nothing Then Exit Sub
            If row.ProcessDate.Date < Today OrElse (row.ProcessDate = Today AndAlso row.ProcessTime.IsNotNullOrWhiteSpace() AndAlso Convert.ToDateTime(row.ProcessTime).Hour > DateTime.Now.Hour) Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.ForeColor = Color.Black
            End If
        ElseIf e.Column Is colPRPassword Then
            Dim pwd As String = e.CellValue
            If pwd IsNot Nothing AndAlso
                    (pwd.IndexOf("owes money", StringComparison.InvariantCultureIgnoreCase) >= 0 OrElse
                     pwd.IndexOf("c david b @ nsf", StringComparison.InvariantCultureIgnoreCase) >= 0 OrElse
                     pwd.IndexOf("c tax dept @ nsf", StringComparison.InvariantCultureIgnoreCase) >= 0) Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.ForeColor = Color.Black
            End If
        End If
    End Sub

    Private Sub SetProcessTime(time As String)
        Dim dt As DateTime
        If DateTime.TryParse(time, dt) Then
            Dim hr = If(dt.Hour > 12, dt.Hour - 12, dt.Hour)
            cbeProcessTime.SelectedItem = If(hr.ToString().Length = 1, "0" & hr, hr) & ":" & If(dt.Minute = 0, "00", dt.Minute) & If(dt.Hour < 12, "AM", "PM")
        Else
            SetProcessTime("07:00AM")
        End If
    End Sub

    Private Function GetProcessTime() As DateTime
        Return DateTime.Parse(cbeProcessTime.EditValue)
    End Function

    Private Sub bbiOpenTimeSheet_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiOpenTimeSheet.ItemClick
        Dim Row As view_Calendar = Me.gridViewCalendar.GetRow(Me.gridViewCalendar.FocusedRowHandle)
        If Row IsNot Nothing Then
            MainForm.OpenPowerGridPayroll(New OpenPowerGridPayrollOptions(Row.CoNum) With {.CallBackForm = Me})
        End If
    End Sub

    Private Sub bbiAttachToPayroll_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiAttachToPayroll.ItemClick
        Dim RowIX = Me.gridViewCalendar.FocusedRowHandle
        AttachToPayroll(RowIX)
    End Sub

    Private Sub bbiSkipCalendar_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSkipCalendar.ItemClick
        OnSkipRowClick(sender, e)
    End Sub

    Private Sub bbiChangeProcessTime_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiChangeProcessTime.ItemClick
        panelChangeProcessTime.Visible = True
    End Sub

    Private Sub bbiCallOffice_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCallOffice.ItemClick
        Dim Row As view_Calendar = ViewCalendarBindingSource.Current
        If Row IsNot Nothing AndAlso Row.CoPhone.IsNotNullOrWhiteSpace Then
            Dim frm = New frmPhone(Row.CoPhone)
        End If
    End Sub

    Private Sub bbiCellPhone_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCellPhone.ItemClick
        Dim Row As view_Calendar = ViewCalendarBindingSource.Current
        If Row IsNot Nothing AndAlso Row.CoModem.IsNotNullOrWhiteSpace Then
            Dim frm = New frmPhone(Row.CoModem)
        End If
    End Sub

    Private Sub btnCloseProcessTimePopUp_Click(sender As Object, e As EventArgs) Handles btnCloseProcessTimePopUp.Click
        panelChangeProcessTime.Visible = False
    End Sub

    Private Sub panelChangeProcessTime_VisibleChanged(sender As Object, e As EventArgs) Handles panelChangeProcessTime.VisibleChanged
        If panelChangeProcessTime.Visible Then
            panelChangeProcessTime.BringToFront()
            panelChangeProcessTime.Location = New Point((gridCalendar.Width - panelChangeProcessTime.Width) / 2, (Me.gridCalendar.Height - panelChangeProcessTime.Height) / 2)
        End If
    End Sub

    Private Sub btnSetProcessTime_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSetProcessTime.Click
        Try
            Dim Row As view_Calendar = Me.gridViewCalendar.GetRow(gridViewCalendar.FocusedRowHandle)
            Dim calenderRule = (From A In DB.CALENDAR_RULES Where A.conum = Row.CoNum AndAlso A.period_id = Row.PeriodID).Single
            If calenderRule.ProcessTime = GetProcessTime().ToLongTimeString() Then Exit Sub
            If String.Compare(calenderRule.ProcessTime, "") Then
                If GetProcessTime() = DateTime.MinValue Then ' "#12:00:00 AM#" 
                    'Me.txtSetProcessTime.Focus()
                    DisplayMessageBox("Please provide a default process time")
                    Exit Sub
                Else
                    calenderRule.ProcessTime = GetProcessTime().ToShortTimeString
                    If Not DB.SaveChanges() Then
                        DisplayErrorMessage("Unable to save process time due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                        Exit Sub
                    End If
                    LoadData()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error setting calendar process time", ex)
        Finally
            panelChangeProcessTime.Visible = False
        End Try
    End Sub

    Private Sub bbiEmail_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEmail.ItemClick
        Dim row As view_Calendar = ViewCalendarBindingSource.Current
        If row IsNot Nothing Then
            Dim frm = New frmEmailComp(row.CoNum, True, False)
            frm.ShowDialog()
        End If
    End Sub

    Private Sub bbiCoOptions_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCoOptions.ItemClick
        Dim row As view_Calendar = ViewCalendarBindingSource.Current
        If row IsNot Nothing Then
            Dim CoFrm As New frmCoOptionsPayroll With {.CoNum = row.CoNum}
            CoFrm.ShowDialog()
            CoFrm.Dispose()
        End If
    End Sub


    Private Async Sub bbiNoMorePayroll_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiNoMorePayroll.ItemClick
        Dim row As view_Calendar = ViewCalendarBindingSource.Current
        If row IsNot Nothing Then
            Await modCompanyUtils.NoMorePayroll(row.CoNum)
        End If
    End Sub
End Class