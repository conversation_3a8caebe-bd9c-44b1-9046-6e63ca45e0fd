﻿Imports Brands_FrontDesk
Imports DevExpress.Spreadsheet
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraLayout

Namespace Billing

    Public Class frmBillingImports
        Private Property BillingImportService As IBillingImportService
        Private Property db As dbEPDataDataContext

        Private SC_Items As New List(Of Int32)
        Private HUB_Items As New List(Of Int32)
        Private SCA_Items As New List(Of Int32)
        Private VC_Items As New List(Of Int32)
        Private AoD_Items As New List(Of Int32)
        Private CS_Items As New List(Of Int32)


        Public Sub New()
            InitializeComponent()
            db = New dbEPDataDataContext(GetConnectionString)

            SC_Items.Add(ucSwipeClockGrid.PriceCodes.AdditionalUsers)
            SC_Items.Add(ucSwipeClockGrid.PriceCodes.BaseFee)
            SC_Items.Add(ucSwipeClockGrid.PriceCodes.ClocksFee)
            SC_Items.Add(ucSwipeClockGrid.PriceCodes.EmployeesFee)
            SC_Items.Add(ucSwipeClockGrid.PriceCodes.MinClockFee)
            SC_Items.Add(ucSwipeClockGrid.PriceCodes.MinSwipeClockFee)
            HUB_Items.Add(ucSwipeClockGrid.PriceCodes.HubBaseFee)
            HUB_Items.Add(ucSwipeClockGrid.PriceCodes.HubEmployeeFee)
            HUB_Items.Add(ucSwipeClockGrid.PriceCodes.HubMinimum)
            SCA_Items.Add(ucSwipeClockGrid.PriceCodes.ScaBaseFee)
            VC_Items.Add(ucSwipeClockGrid.PriceCodes.VCEmployeeFee)
            AoD_Items.Add(ucSwipeClockGrid.PriceCodes.AdditionalUsers)
            AoD_Items.Add(ucSwipeClockGrid.PriceCodes.BaseFee)
            AoD_Items.Add(ucSwipeClockGrid.PriceCodes.ClocksFee)
            AoD_Items.Add(ucSwipeClockGrid.PriceCodes.EmployeesFee)
            AoD_Items.Add(ucSwipeClockGrid.PriceCodes.MinClockFee)
            AoD_Items.Add(ucSwipeClockGrid.PriceCodes.MinSwipeClockFee)
            CS_Items.Add(ucSwipeClockGrid.PriceCodes.CustomScriptTimeclock)
        End Sub

        Private Sub frmSwipeClock_Load(sender As Object, e As EventArgs) Handles MyBase.Load
            Try
                'gvSwipeClockImport.SetGridLayoutAndAddMenues("SwipeClock")
                deInvoiceDate.DateTime = DateTime.Today.AddDays(-Date.Today.Day)
                bsViewCompanySummary.DataSource = db.view_CompanySumarries.ToList()
            Catch ex As Exception
                DisplayErrorMessage("Error Openning form SwipClock", ex)
            End Try
        End Sub

        Private Sub bbiSwipeClockFile_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSwipeClockFile.ItemClick
            Try
                CheckShowNonProcessed.Checked = False
                lcRoot.ShowProgessPanel
                Dim grid = New Billing.ucSwipeClockGrid(False)
                SetService(grid)
                Text = "Billing Import - Swipe Clock"
                BillingImportService.ImportFile()
                If (deInvoiceDate.DateTime = BillingImportService.InvoiceDate) Then
                    deInvoiceDate_EditValueChanged(Nothing, Nothing)
                Else
                    deInvoiceDate.DateTime = BillingImportService.InvoiceDate
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error importing SwipClock File.", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub bbiAodFile_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiAodFile.ItemClick
            Try
                lcRoot.ShowProgessPanel
                Dim grid = New Billing.ucSwipeClockGrid(True)
                SetService(grid)
                Text = "Billing Import - AOD"
                BillingImportService.ImportFile()
                If (deInvoiceDate.DateTime = BillingImportService.InvoiceDate) Then
                    deInvoiceDate_EditValueChanged(Nothing, Nothing)
                Else
                    deInvoiceDate.DateTime = BillingImportService.InvoiceDate
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error importing File.", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub bbiShugoFile_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiShugoFile.ItemClick
            Try
                lcRoot.ShowProgessPanel
                Dim grid = New ucShugoFileGrid()
                SetService(grid)
                Text = "Billing Import - Shugo"
                BillingImportService.ImportFile()
                If (deInvoiceDate.DateTime = BillingImportService.InvoiceDate) Then
                    deInvoiceDate_EditValueChanged(Nothing, Nothing)
                Else
                    deInvoiceDate.DateTime = BillingImportService.InvoiceDate
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error importing SwipClock File.", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub LoadData()
            If CheckShowNonProcessed.Checked Then
                Me.bbiRecalculatePrice.Enabled = False
                Me.bbiInvoiceMany.Enabled = False
                Me.btsiShowBilled.Enabled = False
            Else
                Me.bbiRecalculatePrice.Enabled = True
                Me.bbiInvoiceMany.Enabled = True
                Me.btsiShowBilled.Enabled = True
            End If

            BillingImportService.RefreshData(btsiShowBilled.Checked, CheckShowNonProcessed.Checked)
            If BillingImportService.GetSelectedRow Is Nothing Then
                SelectedRowChanged(Nothing)
            End If
        End Sub

        Private Sub SetService(service As IBillingImportService)
            If BillingImportService IsNot Nothing Then
                RemoveHandler BillingImportService.SelectedRowChanged, AddressOf SelectedRowChanged
                RemoveHandler BillingImportService.SelectionChanged, AddressOf SelectionChanged
            End If


            BillingImportService = service
            AddHandler BillingImportService.SelectedRowChanged, AddressOf SelectedRowChanged
            AddHandler BillingImportService.SelectionChanged, AddressOf SelectionChanged
            lcgGrid.Clear()
            Dim li As LayoutControlItem = lcgGrid.AddItem
            li.Padding = New DevExpress.XtraLayout.Utils.Padding(0)
            li.Control = service
            tcgMainGrid.SelectedTabPage = lcgGrid
            'gcGlobalBilling.DataSource = db.ACT_ITEMs.Where(Function(i) service.PriceCodesList.Contains(i.ITEM_NUM)).ToList()
        End Sub

        Private Sub SelectedRowChanged(row As IRecord)
            Try
                If row Is Nothing Then
                    UcCompInfo1.Clean()
                    BindingSource1.Clear()
                    gcRecentInvoiceLog.DataSource = Nothing
                Else
                    Dim co As Decimal = row.Conum
                    Dim _db = New dbEPDataDataContext(GetConnectionString)
                    Dim comp = _db.COMPANies.SingleOrDefault(Function(c) c.CONUM = row.Conum)
                    If comp Is Nothing Then
                        UcCompInfo1.Clean()
                        BindingSource1.Clear()
                        gcRecentInvoiceLog.DataSource = Nothing
                        Exit Sub
                    End If
                    Dim _empsCount = _db.EMPLOYEEs.Where(Function(em) em.CONUM = row.Conum AndAlso Not em.TERM_DATE.HasValue).Count
                    Dim _coOptions = _db.COOPTIONs.FirstOrDefault(Function(c) c.CONUM = row.Conum)
                    Me.UcCompInfo1.LoadDate(comp, _empsCount, _coOptions)

                    If row.GetType() = GetType(ucSwipeClockGrid.SwipeClock) Then
                        Dim r As ucSwipeClockGrid.SwipeClock = row
                        If r.RowType = "SC" OrElse r.RowType = "AoD" Then
                            BindingSource1.DataSource = (From b In _db.ACT_AUTO_PriceOverrides Where b.CONUM = co AndAlso SC_Items.Contains(b.ITEM_NUM)).ToList()
                            gcRecentInvoiceLog.DataSource = (From d In _db.invoice_item_details Join m In _db.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                             Where d.conum = row.Conum AndAlso SC_Items.Contains(d.item_num) AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                             Select ITEM_NAME = d.item_name, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, m.invoice_date).ToList()
                            gcGlobalBilling.DataSource = db.ACT_ITEMS.Where(Function(i) SC_Items.Contains(i.ITEM_NUM)).ToList()
                        ElseIf r.RowType = "HUB" Then
                            BindingSource1.DataSource = (From b In _db.ACT_AUTO_PriceOverrides Where b.CONUM = co AndAlso HUB_Items.Contains(b.ITEM_NUM)).ToList()
                            gcRecentInvoiceLog.DataSource = (From d In _db.invoice_item_details Join m In _db.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                             Where d.conum = row.Conum AndAlso HUB_Items.Contains(d.item_num) AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                             Select ITEM_NAME = d.item_name, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, m.invoice_date).ToList()
                            gcGlobalBilling.DataSource = db.ACT_ITEMS.Where(Function(i) HUB_Items.Contains(i.ITEM_NUM)).ToList()
                        ElseIf r.RowType = "SCA" Then
                            BindingSource1.DataSource = (From b In _db.ACT_AUTO_PriceOverrides Where b.CONUM = co AndAlso SCA_Items.Contains(b.ITEM_NUM)).ToList()
                            gcRecentInvoiceLog.DataSource = (From d In _db.invoice_item_details Join m In _db.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                             Where d.conum = row.Conum AndAlso SCA_Items.Contains(d.item_num) AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                             Select ITEM_NAME = d.item_name, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, m.invoice_date).ToList()
                            gcGlobalBilling.DataSource = db.ACT_ITEMS.Where(Function(i) SCA_Items.Contains(i.ITEM_NUM)).ToList()
                        ElseIf r.RowType = "VC" Then
                            BindingSource1.DataSource = (From b In _db.ACT_AUTO_PriceOverrides Where b.CONUM = co AndAlso VC_Items.Contains(b.ITEM_NUM)).ToList()
                            gcRecentInvoiceLog.DataSource = (From d In _db.invoice_item_details Join m In _db.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                             Where d.conum = row.Conum AndAlso VC_Items.Contains(d.item_num) AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                             Select ITEM_NAME = d.item_name, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, m.invoice_date).ToList()
                            gcGlobalBilling.DataSource = db.ACT_ITEMS.Where(Function(i) VC_Items.Contains(i.ITEM_NUM)).ToList()
                        ElseIf r.RowType = "CS" Then
                            BindingSource1.DataSource = (From b In _db.ACT_AUTO_PriceOverrides Where b.CONUM = co AndAlso CS_Items.Contains(b.ITEM_NUM)).ToList()
                            gcRecentInvoiceLog.DataSource = (From d In _db.invoice_item_details Join m In _db.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                             Where d.conum = row.Conum AndAlso CS_Items.Contains(d.item_num) AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                             Select ITEM_NAME = d.item_name, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, m.invoice_date).ToList()
                            gcGlobalBilling.DataSource = db.ACT_ITEMS.Where(Function(i) CS_Items.Contains(i.ITEM_NUM)).ToList()
                        End If
                    Else
                        BindingSource1.DataSource = (From b In _db.ACT_AUTO_PriceOverrides Where b.CONUM = co AndAlso BillingImportService.PriceCodesList.Contains(b.ITEM_NUM)).ToList()
                        gcRecentInvoiceLog.DataSource = (From d In _db.invoice_item_details Join m In _db.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                         Where d.conum = row.Conum AndAlso BillingImportService.PriceCodesList.Contains(d.item_num) AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                         Select ITEM_NAME = d.item_name, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, m.invoice_date).ToList()
                        gcGlobalBilling.DataSource = db.ACT_ITEMS.Where(Function(i) BillingImportService.PriceCodesList.Contains(i.ITEM_NUM)).ToList()
                    End If

                    gvRecentInvoiceLog.ExpandAllGroups()
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error loading company details", ex)
            End Try
        End Sub

        Private Sub bbiOpenCompany_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiOpenCompany.ItemClick
            If BillingImportService IsNot Nothing AndAlso BillingImportService.GetSelectedRow.Conum > 0 Then
                MainForm.OpenCompForm(BillingImportService.GetSelectedRow.Conum)
            End If
        End Sub

        Private Sub bbiRecalculatePrice_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRecalculatePrice.ItemClick
            Try
                lcRoot.ShowProgessPanel
                If BillingImportService Is Nothing Then
                    DisplayMessageBox("Please load a file first.")
                    Return
                End If
                db = New dbEPDataDataContext(GetConnectionString)
                BillingImportService.CalculatePrices()
            Catch ex As Exception
                DisplayErrorMessage("Error calculating Price", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub gvSwipeClockImport_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs)
            ' BillingImportService.PopupMenuShowing(sender, e)
            '            e.Menu.Items.Add(New DXMenuItem("Map Code To Co#", Sub()
            '                                                                   pccMapCoToAodCode.Tag = row
            '                                                                   teAodCode.Text = row.Client
            '                                                                   slueCoNumAodMap.EditValue = row.ClientTag
            '                                                                   pccMapCoToAodCode.Show()
            '                                                               End Sub))
            '            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Invoice Additional Users", Sub()
            '                                                                                                  pccAdditionalUsersInvoice.Tag = row
            '                                                                                                  pccAdditionalUsersInvoice.BringToFront()
            '                                                                                                  pccAdditionalUsersInvoice.Show()
            '                                                                                              End Sub) With {.BeginGroup = True})
            '        End If
        End Sub


        Private Sub SelectionChanged()
            If CheckShowNonProcessed.Checked Then
                Return
            End If

            Dim selectedRowCount = BillingImportService.GetSelectedRows.Count
            bbiInvoiceMany.Enabled = selectedRowCount > 0
            bbiInvoiceMany.Caption = $"Invoice ({selectedRowCount}) Clients"
        End Sub

        Private Sub bbiInvoiceMany_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiInvoiceMany.ItemClick
            Try
                lcRoot.ShowProgessPanel
                Dim selectedlist = BillingImportService.GetSelectedRows
                For Each row In selectedlist
                    BillingImportService.InvoiceRow(row)
                Next
                db = New dbEPDataDataContext(GetConnectionString)
                BillingImportService.CalculatePrices()
                LoadData()
                SelectedRowChanged(BillingImportService.GetSelectedRow())
                SelectionChanged()
            Catch ex As Exception
                lcRoot.HideProgressPanel
                DisplayErrorMessage("Error in Invoice All", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub brnAdditUsersInvoice_Click(sender As Object, e As EventArgs) Handles brnAdditUsersInvoice.Click
            'If seAdditionalUsersCount.Value < 1 Then
            '    DisplayErrorMessage("Please enter the amount of additional users to invoice")
            '    Exit Sub
            'End If
            'Dim row As SwipeClock = pccAdditionalUsersInvoice.Tag
            'Dim price = GetPriceForAdditionalUsers(row.ClientTag, seAdditionalUsersCount.Value)
            'InsertInvoiceLog(pccAdditionalUsersInvoice.Tag, PriceCodes.AdditionalUsers, price, seAdditionalUsersCount.Value)
            'pccAdditionalUsersInvoice.Tag = Nothing
            'pccAdditionalUsersInvoice.Hide()
            'gcSwipeClockImport.Enabled = True
            'gvSwipeClockImport.Focus()
        End Sub

        Private Sub btnAdditUsersCancel_Click(sender As Object, e As EventArgs) Handles btnAdditUsersCancel.Click
            pccAdditionalUsersInvoice.Tag = Nothing
            pccAdditionalUsersInvoice.Hide()
        End Sub

        Private Sub btsiShowBilled_CheckedChanged(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles btsiShowBilled.CheckedChanged
            LoadData()
            btsiShowBilled.Caption = If(btsiShowBilled.Checked, "All", "Unbilled Only")
        End Sub

        Private Sub deInvoiceDate_EditValueChanged(sender As Object, e As EventArgs) Handles deInvoiceDate.EditValueChanged
            Try
                lcRoot.ShowProgessPanel
                If BillingImportService IsNot Nothing Then
                    BillingImportService.InvoiceDate = deInvoiceDate.DateTime
                    BillingImportService.CalculatePrices()
                    LoadData()
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error calculating Price", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub PopupControlContainer1_VisibleChanged(sender As Object, e As EventArgs) Handles pccMapCoToAodCode.VisibleChanged, pccAdditionalUsersInvoice.VisibleChanged
            Dim pcc As PopupControlContainer = sender
            pcc.Location = New Point((Width - pcc.Width) / 2, (Height - pcc.Height) / 2)
            lcgGrid.Enabled = Not pcc.Visible
        End Sub

        Private Sub btnSaveCoMap_Click(sender As Object, e As EventArgs) Handles btnSaveCoMap.Click
            'Try
            '    If slueCoNumAodMap.EditValue Is Nothing OrElse slueCoNumAodMap.EditValue = 0 Then
            '        XtraMessageBox.Show("Please select a Co#.")
            '        Exit Sub
            '    End If
            '    Dim co As Decimal = slueCoNumAodMap.EditValue
            '    Dim row As SwipeClock = pccMapCoToAodCode.Tag
            '    Using _db As New dbEPDataDataContext(GetConnectionString)
            '        Dim coOpt = _db.CoOptions_Payrolls.SingleOrDefault(Function(c) c.CoNum = co)
            '        If coOpt Is Nothing Then
            '            coOpt = New CoOptions_Payroll With {.CoNum = co}
            '            _db.CoOptions_Payrolls.InsertOnSubmit(coOpt)
            '        End If
            '        coOpt.AodCode = row.Client
            '        _db.SaveChanges
            '        row.ClientTag = co
            '        db = New dbEPDataDataContext(GetConnectionString)
            '        BillingImportService.CalculatePrices()
            '    End Using
            '    pccMapCoToAodCode.Hide()
            'Catch ex As Exception
            '    DisplayErrorMessage("Error Mapping Aod code to Co#", ex)
            'End Try
        End Sub

        Private Sub btnCancelCoMap_Click(sender As Object, e As EventArgs) Handles btnCancelCoMap.Click
            pccMapCoToAodCode.Hide()
            pccMapCoToAodCode.Tag = Nothing
        End Sub

        Private Sub CheckShowNonProcessed_CheckedChanged(sender As Object, e As EventArgs) Handles CheckShowNonProcessed.CheckedChanged
            LoadData()
        End Sub

        Private Sub bbiInactiveClientWOpenInvoices_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiInactiveClientWOpenInvoices.ItemClick
            Dim frm = New frmYearEndDecPayroll
            Dim runType As frmYearEndDecPayroll.RunTypeEnum = frmYearEndDecPayroll.RunTypeEnum.OpenInvoice
            CType(frm, frmYearEndDecPayroll).RunType = runType
            MainForm.ShowForm(frm)
        End Sub

        Private Sub bbiCaptureBill_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiCaptureBill.ItemClick
            'Dim t As ucSwipeClockGrid = (From r In Controls(0).Controls Where r.name = "ucSwipeClockGrid" Select r).FirstOrDefault()
            'Dim sdf = t.GridControl1

            Try
                Dim SwipeClockGrid As ucSwipeClockGrid = BillingImportService

                If BillingImportService IsNot Nothing AndAlso SwipeClockGrid.SwipeClockData IsNot Nothing AndAlso Text = "Billing Import - Swipe Clock" Then
                    Dim list = BillingImportService.Records
                    Dim v As ucSwipeClockGrid.SwipeClock

                    If list.Count = 0 Then Return
                    Dim hasDtm = Query(Of Date)($"select top 1 dtm from custom.SwipeClockData where dtm = '{deInvoiceDate.EditValue}'").FirstOrDefault()

                    If Not hasDtm = Nothing Then
                        If DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to override old capture?", "Already Captured", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                            Return
                        End If

                        db.ExecuteCommand($"delete custom.SwipeClockData where dtm = '{deInvoiceDate.EditValue}'")
                    End If

                    Dim regRep As New System.Text.RegularExpressions.Regex("\d|\n|\r|\f|[1-9]", System.Text.RegularExpressions.RegexOptions.Compiled)
                    Dim sql As String = ""
                    For Each v In list
                        If Not v.ExtDescription.Contains("$") Then Continue For
                        Dim ExtDescription = Replace(v.ExtDescription, "'", "''")

                        'Dim revExtDescription As String = regRep.Replace(ExtDescription, "")
                        'revExtDescription = Replace(revExtDescription, "Clocks", "Clock")
                        'revExtDescription = Replace(revExtDescription, "Employees", "Employee")
                        'revExtDescription = Replace(revExtDescription, ".Employee: $.Clock:", ".Clock: $.Employee:")
                        Dim revExtDescription As String = v.BillingItems()
                        sql += $"insert custom.SwipeClockData(dtm, ExtDescription, ExtDescriptionRevised) select '{deInvoiceDate.EditValue}', '{ExtDescription}', '{revExtDescription}';"
                    Next
                    db.ExecuteCommand(sql)
                    XtraMessageBox.Show("Saved Bill", "Captured", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error Saving bill", ex)
            End Try
        End Sub
    End Class
End Namespace