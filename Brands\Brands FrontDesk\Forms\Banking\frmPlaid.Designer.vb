﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPlaid
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.lueCompany = New DevExpress.XtraEditors.LookUpEdit()
        Me.GCBalance = New DevExpress.XtraGrid.GridControl()
        Me.GVBalance = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GCAuth = New DevExpress.XtraGrid.GridControl()
        Me.GVAauth = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.lueAccount = New DevExpress.XtraEditors.LookUpEdit()
        Me.btnClose = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAuth = New DevExpress.XtraEditors.SimpleButton()
        Me.btnBalance = New DevExpress.XtraEditors.SimpleButton()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.SimpleLabelItem1 = New DevExpress.XtraLayout.SimpleLabelItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem4 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem5 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem6 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem8 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lblCostNote = New DevExpress.XtraLayout.SimpleLabelItem()
        Me.lblCostNote2 = New DevExpress.XtraLayout.SimpleLabelItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem7 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem9 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem10 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.GCClientAccounts = New DevExpress.XtraGrid.GridControl()
        Me.GVClientAccounts = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.lueCompany.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GCBalance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GVBalance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GCAuth, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GVAauth, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueAccount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleLabelItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblCostNote, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblCostNote2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GCClientAccounts, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GVClientAccounts, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.GCClientAccounts)
        Me.LayoutControl1.Controls.Add(Me.lueCompany)
        Me.LayoutControl1.Controls.Add(Me.GCBalance)
        Me.LayoutControl1.Controls.Add(Me.GCAuth)
        Me.LayoutControl1.Controls.Add(Me.lueAccount)
        Me.LayoutControl1.Controls.Add(Me.btnClose)
        Me.LayoutControl1.Controls.Add(Me.btnAuth)
        Me.LayoutControl1.Controls.Add(Me.btnBalance)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(343, 4, 650, 400)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(862, 662)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'lueCompany
        '
        Me.lueCompany.Location = New System.Drawing.Point(185, 24)
        Me.lueCompany.Name = "lueCompany"
        Me.lueCompany.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueCompany.Properties.NullText = "Choose Company"
        Me.lueCompany.Size = New System.Drawing.Size(562, 20)
        Me.lueCompany.StyleController = Me.LayoutControl1
        Me.lueCompany.TabIndex = 10
        '
        'GCBalance
        '
        Me.GCBalance.Location = New System.Drawing.Point(12, 346)
        Me.GCBalance.MainView = Me.GVBalance
        Me.GCBalance.Name = "GCBalance"
        Me.GCBalance.Size = New System.Drawing.Size(838, 131)
        Me.GCBalance.TabIndex = 6
        Me.GCBalance.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GVBalance})
        '
        'GVBalance
        '
        Me.GVBalance.GridControl = Me.GCBalance
        Me.GVBalance.Name = "GVBalance"
        Me.GVBalance.OptionsBehavior.Editable = False
        Me.GVBalance.OptionsCustomization.AllowGroup = False
        Me.GVBalance.OptionsView.ShowGroupPanel = False
        Me.GVBalance.OptionsView.ShowViewCaption = True
        Me.GVBalance.ViewCaption = "Balance"
        '
        'GCAuth
        '
        Me.GCAuth.Location = New System.Drawing.Point(12, 179)
        Me.GCAuth.MainView = Me.GVAauth
        Me.GCAuth.Name = "GCAuth"
        Me.GCAuth.Size = New System.Drawing.Size(838, 124)
        Me.GCAuth.TabIndex = 5
        Me.GCAuth.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GVAauth})
        '
        'GVAauth
        '
        Me.GVAauth.GridControl = Me.GCAuth
        Me.GVAauth.Name = "GVAauth"
        Me.GVAauth.OptionsBehavior.Editable = False
        Me.GVAauth.OptionsView.ShowGroupPanel = False
        Me.GVAauth.OptionsView.ShowViewCaption = True
        Me.GVAauth.ViewCaption = "Auth"
        '
        'lueAccount
        '
        Me.lueAccount.Location = New System.Drawing.Point(42, 72)
        Me.lueAccount.Name = "lueAccount"
        Me.lueAccount.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueAccount.Size = New System.Drawing.Size(808, 20)
        Me.lueAccount.StyleController = Me.LayoutControl1
        Me.lueAccount.TabIndex = 4
        '
        'btnClose
        '
        Me.btnClose.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnClose.Location = New System.Drawing.Point(773, 628)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Size = New System.Drawing.Size(77, 22)
        Me.btnClose.StyleController = Me.LayoutControl1
        Me.btnClose.TabIndex = 7
        Me.btnClose.Text = "Close"
        '
        'btnAuth
        '
        Me.btnAuth.Location = New System.Drawing.Point(749, 153)
        Me.btnAuth.Name = "btnAuth"
        Me.btnAuth.Size = New System.Drawing.Size(101, 22)
        Me.btnAuth.StyleController = Me.LayoutControl1
        Me.btnAuth.TabIndex = 8
        Me.btnAuth.Text = "Auth"
        '
        'btnBalance
        '
        Me.btnBalance.Location = New System.Drawing.Point(749, 320)
        Me.btnBalance.Name = "btnBalance"
        Me.btnBalance.Size = New System.Drawing.Size(101, 22)
        Me.btnBalance.StyleController = Me.LayoutControl1
        Me.btnBalance.TabIndex = 9
        Me.btnBalance.Text = "Balance"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.EmptySpaceItem2, Me.SimpleLabelItem1, Me.LayoutControlItem1, Me.EmptySpaceItem3, Me.LayoutControlItem2, Me.EmptySpaceItem4, Me.LayoutControlItem3, Me.EmptySpaceItem5, Me.EmptySpaceItem6, Me.LayoutControlItem4, Me.EmptySpaceItem1, Me.EmptySpaceItem8, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.lblCostNote, Me.lblCostNote2, Me.LayoutControlItem7, Me.EmptySpaceItem7, Me.EmptySpaceItem9, Me.EmptySpaceItem10, Me.LayoutControlItem8})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(862, 662)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 0)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(842, 12)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'SimpleLabelItem1
        '
        Me.SimpleLabelItem1.AllowHotTrack = False
        Me.SimpleLabelItem1.AppearanceItemCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.SimpleLabelItem1.AppearanceItemCaption.Options.UseFont = True
        Me.SimpleLabelItem1.AppearanceItemCaption.Options.UseTextOptions = True
        Me.SimpleLabelItem1.AppearanceItemCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleLabelItem1.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleLabelItem1.ContentVertAlignment = DevExpress.Utils.VertAlignment.Center
        Me.SimpleLabelItem1.Location = New System.Drawing.Point(0, 36)
        Me.SimpleLabelItem1.Name = "SimpleLabelItem1"
        Me.SimpleLabelItem1.OptionsPrint.AppearanceItem.Options.UseTextOptions = True
        Me.SimpleLabelItem1.OptionsPrint.AppearanceItem.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleLabelItem1.OptionsPrint.AppearanceItemText.Options.UseTextOptions = True
        Me.SimpleLabelItem1.OptionsPrint.AppearanceItemText.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleLabelItem1.OptionsPrint.AppearanceItemText.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.SimpleLabelItem1.Size = New System.Drawing.Size(842, 24)
        Me.SimpleLabelItem1.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.SimpleLabelItem1.TextSize = New System.Drawing.Size(50, 20)
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.lueAccount
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 60)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(842, 24)
        Me.LayoutControlItem1.Text = "Acct:"
        Me.LayoutControlItem1.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(25, 13)
        Me.LayoutControlItem1.TextToControlDistance = 5
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(0, 84)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(842, 43)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.GCAuth
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 167)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(842, 128)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'EmptySpaceItem4
        '
        Me.EmptySpaceItem4.AllowHotTrack = False
        Me.EmptySpaceItem4.Location = New System.Drawing.Point(0, 295)
        Me.EmptySpaceItem4.Name = "EmptySpaceItem4"
        Me.EmptySpaceItem4.Size = New System.Drawing.Size(842, 13)
        Me.EmptySpaceItem4.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.GCBalance
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 334)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(842, 135)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'EmptySpaceItem5
        '
        Me.EmptySpaceItem5.AllowHotTrack = False
        Me.EmptySpaceItem5.Location = New System.Drawing.Point(0, 469)
        Me.EmptySpaceItem5.Name = "EmptySpaceItem5"
        Me.EmptySpaceItem5.Size = New System.Drawing.Size(842, 15)
        Me.EmptySpaceItem5.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem6
        '
        Me.EmptySpaceItem6.AllowHotTrack = False
        Me.EmptySpaceItem6.Location = New System.Drawing.Point(0, 616)
        Me.EmptySpaceItem6.Name = "EmptySpaceItem6"
        Me.EmptySpaceItem6.Size = New System.Drawing.Size(420, 26)
        Me.EmptySpaceItem6.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.btnClose
        Me.LayoutControlItem4.Location = New System.Drawing.Point(761, 616)
        Me.LayoutControlItem4.MaxSize = New System.Drawing.Size(81, 26)
        Me.LayoutControlItem4.MinSize = New System.Drawing.Size(81, 26)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(81, 26)
        Me.LayoutControlItem4.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(420, 616)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(341, 26)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem8
        '
        Me.EmptySpaceItem8.AllowHotTrack = False
        Me.EmptySpaceItem8.Location = New System.Drawing.Point(0, 127)
        Me.EmptySpaceItem8.Name = "EmptySpaceItem8"
        Me.EmptySpaceItem8.Size = New System.Drawing.Size(842, 14)
        Me.EmptySpaceItem8.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.btnAuth
        Me.LayoutControlItem5.Location = New System.Drawing.Point(737, 141)
        Me.LayoutControlItem5.MaxSize = New System.Drawing.Size(105, 26)
        Me.LayoutControlItem5.MinSize = New System.Drawing.Size(105, 26)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(105, 26)
        Me.LayoutControlItem5.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.btnBalance
        Me.LayoutControlItem6.Location = New System.Drawing.Point(737, 308)
        Me.LayoutControlItem6.MaxSize = New System.Drawing.Size(105, 26)
        Me.LayoutControlItem6.MinSize = New System.Drawing.Size(105, 26)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(105, 26)
        Me.LayoutControlItem6.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'lblCostNote
        '
        Me.lblCostNote.AllowHotTrack = False
        Me.lblCostNote.AppearanceItemCaption.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblCostNote.AppearanceItemCaption.ForeColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.lblCostNote.AppearanceItemCaption.Options.UseFont = True
        Me.lblCostNote.AppearanceItemCaption.Options.UseForeColor = True
        Me.lblCostNote.AppearanceItemCaption.Options.UseTextOptions = True
        Me.lblCostNote.AppearanceItemCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lblCostNote.Location = New System.Drawing.Point(0, 141)
        Me.lblCostNote.Name = "lblCostNote"
        Me.lblCostNote.OptionsPrint.AppearanceItem.ForeColor = System.Drawing.Color.Red
        Me.lblCostNote.OptionsPrint.AppearanceItem.Options.UseFont = True
        Me.lblCostNote.OptionsPrint.AppearanceItem.Options.UseForeColor = True
        Me.lblCostNote.Size = New System.Drawing.Size(737, 26)
        Me.lblCostNote.Text = "There is a cost assoicated with each call"
        Me.lblCostNote.TextSize = New System.Drawing.Size(261, 16)
        '
        'lblCostNote2
        '
        Me.lblCostNote2.AllowHotTrack = False
        Me.lblCostNote2.AppearanceItemCaption.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblCostNote2.AppearanceItemCaption.ForeColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.lblCostNote2.AppearanceItemCaption.Options.UseFont = True
        Me.lblCostNote2.AppearanceItemCaption.Options.UseForeColor = True
        Me.lblCostNote2.AppearanceItemCaption.Options.UseTextOptions = True
        Me.lblCostNote2.AppearanceItemCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lblCostNote2.CustomizationFormText = "There is a cost assoicated with each call"
        Me.lblCostNote2.Location = New System.Drawing.Point(0, 308)
        Me.lblCostNote2.Name = "lblCostNote2"
        Me.lblCostNote2.OptionsPrint.AppearanceItem.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblCostNote2.OptionsPrint.AppearanceItem.ForeColor = System.Drawing.Color.Red
        Me.lblCostNote2.OptionsPrint.AppearanceItem.Options.UseFont = True
        Me.lblCostNote2.OptionsPrint.AppearanceItem.Options.UseForeColor = True
        Me.lblCostNote2.OptionsPrint.AppearanceItemControl.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.lblCostNote2.OptionsPrint.AppearanceItemControl.Options.UseFont = True
        Me.lblCostNote2.OptionsPrint.AppearanceItemText.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.lblCostNote2.OptionsPrint.AppearanceItemText.Options.UseFont = True
        Me.lblCostNote2.Size = New System.Drawing.Size(737, 26)
        Me.lblCostNote2.Text = "There is a cost assoicated with each call"
        Me.lblCostNote2.TextSize = New System.Drawing.Size(261, 16)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.lueCompany
        Me.LayoutControlItem7.Location = New System.Drawing.Point(123, 12)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(616, 24)
        Me.LayoutControlItem7.Text = "Company"
        Me.LayoutControlItem7.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(45, 13)
        Me.LayoutControlItem7.TextToControlDistance = 5
        '
        'EmptySpaceItem7
        '
        Me.EmptySpaceItem7.AllowHotTrack = False
        Me.EmptySpaceItem7.Location = New System.Drawing.Point(0, 12)
        Me.EmptySpaceItem7.Name = "EmptySpaceItem7"
        Me.EmptySpaceItem7.Size = New System.Drawing.Size(123, 24)
        Me.EmptySpaceItem7.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem9
        '
        Me.EmptySpaceItem9.AllowHotTrack = False
        Me.EmptySpaceItem9.Location = New System.Drawing.Point(739, 12)
        Me.EmptySpaceItem9.Name = "EmptySpaceItem9"
        Me.EmptySpaceItem9.Size = New System.Drawing.Size(103, 24)
        Me.EmptySpaceItem9.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem10
        '
        Me.EmptySpaceItem10.AllowHotTrack = False
        Me.EmptySpaceItem10.Location = New System.Drawing.Point(0, 606)
        Me.EmptySpaceItem10.Name = "EmptySpaceItem10"
        Me.EmptySpaceItem10.Size = New System.Drawing.Size(842, 10)
        Me.EmptySpaceItem10.TextSize = New System.Drawing.Size(0, 0)
        '
        'GCClientAccounts
        '
        Me.GCClientAccounts.Location = New System.Drawing.Point(12, 496)
        Me.GCClientAccounts.MainView = Me.GVClientAccounts
        Me.GCClientAccounts.Name = "GCClientAccounts"
        Me.GCClientAccounts.Size = New System.Drawing.Size(838, 118)
        Me.GCClientAccounts.TabIndex = 11
        Me.GCClientAccounts.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GVClientAccounts})
        '
        'GVClientAccounts
        '
        Me.GVClientAccounts.GridControl = Me.GCClientAccounts
        Me.GVClientAccounts.Name = "GVClientAccounts"
        Me.GVClientAccounts.OptionsBehavior.Editable = False
        Me.GVClientAccounts.OptionsCustomization.AllowGroup = False
        Me.GVClientAccounts.OptionsView.ShowGroupPanel = False
        Me.GVClientAccounts.OptionsView.ShowViewCaption = True
        Me.GVClientAccounts.ViewCaption = "Client Accounts"
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.GCClientAccounts
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 484)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(842, 122)
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'frmPlaid
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.btnClose
        Me.ClientSize = New System.Drawing.Size(862, 662)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "frmPlaid"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Plaid"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.lueCompany.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GCBalance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GVBalance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GCAuth, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GVAauth, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueAccount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleLabelItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblCostNote, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblCostNote2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GCClientAccounts, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GVClientAccounts, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents lueAccount As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents SimpleLabelItem1 As DevExpress.XtraLayout.SimpleLabelItem
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GCAuth As DevExpress.XtraGrid.GridControl
    Friend WithEvents GVAauth As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GCBalance As DevExpress.XtraGrid.GridControl
    Friend WithEvents GVBalance As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents EmptySpaceItem4 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnClose As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAuth As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnBalance As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents EmptySpaceItem5 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem6 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem8 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lblCostNote As DevExpress.XtraLayout.SimpleLabelItem
    Friend WithEvents lblCostNote2 As DevExpress.XtraLayout.SimpleLabelItem
    Friend WithEvents lueCompany As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem7 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem9 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents GCClientAccounts As DevExpress.XtraGrid.GridControl
    Friend WithEvents GVClientAccounts As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents EmptySpaceItem10 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
End Class
