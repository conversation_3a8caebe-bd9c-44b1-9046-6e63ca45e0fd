﻿Imports DevExpress.XtraGrid

<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucEmployeeInfo
    Inherits DevExpress.XtraEditors.XtraUserControl

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim AUTO_SAL_HRSLabel As System.Windows.Forms.Label
        Dim Default_hoursLabel As System.Windows.Forms.Label
        Dim PAY_FREQLabel As System.Windows.Forms.Label
        Dim SALARY_AMTLabel As System.Windows.Forms.Label
        Dim RATE_3Label As System.Windows.Forms.Label
        Dim RATE_2Label As System.Windows.Forms.Label
        Dim RATE_1Label As System.Windows.Forms.Label
        Dim M_NAMELabel As System.Windows.Forms.Label
        Dim CITYLabel As System.Windows.Forms.Label
        Dim Contact_pagerLabel As System.Windows.Forms.Label
        Dim STREETLabel As System.Windows.Forms.Label
        Dim GENDERLabel As System.Windows.Forms.Label
        Dim Contact_homephoneLabel As System.Windows.Forms.Label
        Dim F_NAMELabel As System.Windows.Forms.Label
        Dim Contact_homeemailLabel As System.Windows.Forms.Label
        Dim B_DAYLabel As System.Windows.Forms.Label
        Dim L_NAMELabel As System.Windows.Forms.Label
        Dim User_emailLabel As System.Windows.Forms.Label
        Dim TERM_DATELabel As System.Windows.Forms.Label
        Dim START_DATELabel As System.Windows.Forms.Label
        Dim DEPTNUMLabel As System.Windows.Forms.Label
        Dim DIVNUMLabel As System.Windows.Forms.Label
        Dim EMP_TYPELabel As System.Windows.Forms.Label
        Dim Default_positionLabel As System.Windows.Forms.Label
        Dim Local_activeLabel As System.Windows.Forms.Label
        Dim Local_exemptLabel As System.Windows.Forms.Label
        Dim Fixed_whLabel As System.Windows.Forms.Label
        Dim Extra_whLabel As System.Windows.Forms.Label
        Dim SUTA_EXE_FGLabel As System.Windows.Forms.Label
        Dim UCI_STATELabel As System.Windows.Forms.Label
        Dim FUTA_EXE_FGELabel As System.Windows.Forms.Label
        Dim FED_WH_FIXEDLabel As System.Windows.Forms.Label
        Dim OASDI_EXE_FGELabel As System.Windows.Forms.Label
        Dim FED_WH_EXE_FGELabel As System.Windows.Forms.Label
        Dim FED_WH_EXTRALabel As System.Windows.Forms.Label
        Dim FED_DEPSLabel As System.Windows.Forms.Label
        Dim FED_STATUSLabel As System.Windows.Forms.Label
        Dim ExcludeFromUtilityImportLabel As System.Windows.Forms.Label
        Dim Label14 As System.Windows.Forms.Label
        Dim OTSeperateCheckHoursMoreThanLabel As System.Windows.Forms.Label
        Dim OTSeperateCheckLabel As System.Windows.Forms.Label
        Dim Label21 As System.Windows.Forms.Label
        Dim Label20 As System.Windows.Forms.Label
        Dim Label19 As System.Windows.Forms.Label
        Dim Label18 As System.Windows.Forms.Label
        Dim Label1 As System.Windows.Forms.Label
        Dim CheckTypeLabel As System.Windows.Forms.Label
        Dim MedicareOverrideAmountLabel As System.Windows.Forms.Label
        Dim OASDIOverrideAmountLabel As System.Windows.Forms.Label
        Dim FedOverrideAmountLabel As System.Windows.Forms.Label
        Dim TaxFrequencyLabel As System.Windows.Forms.Label
        Dim STOverrideAmountLabel As System.Windows.Forms.Label
        Dim DBOverrideAmountLabel As System.Windows.Forms.Label
        Dim LOCOverrideAmountLabel As System.Windows.Forms.Label
        Dim Label17 As System.Windows.Forms.Label
        Dim NetOverrideAdjustTypeLabel As System.Windows.Forms.Label
        Dim NetOverrideAmountLabel As System.Windows.Forms.Label
        Dim OTHoursLabel As System.Windows.Forms.Label
        Dim Label15 As System.Windows.Forms.Label
        Dim Label16 As System.Windows.Forms.Label
        Dim CheckCounterLabel As System.Windows.Forms.Label
        Dim PayDedTypeLabel As System.Windows.Forms.Label
        Dim PayDedCodeLabel As System.Windows.Forms.Label
        Dim PayDedAmountLabel As System.Windows.Forms.Label
        Dim RecordTypeLabel As System.Windows.Forms.Label
        Dim Label13 As System.Windows.Forms.Label
        Dim Label7 As System.Windows.Forms.Label
        Dim Label12 As System.Windows.Forms.Label
        Dim Label8 As System.Windows.Forms.Label
        Dim Label9 As System.Windows.Forms.Label
        Dim Label10 As System.Windows.Forms.Label
        Dim Label6 As System.Windows.Forms.Label
        Dim Label11 As System.Windows.Forms.Label
        Dim Label3 As System.Windows.Forms.Label
        Dim Label4 As System.Windows.Forms.Label
        Dim Label2 As System.Windows.Forms.Label
        Dim Label5 As System.Windows.Forms.Label
        Dim EMPNUMLabel As System.Windows.Forms.Label
        Dim Label22 As System.Windows.Forms.Label
        Dim Label23 As System.Windows.Forms.Label
        Dim Label24 As System.Windows.Forms.Label
        Dim Label25 As System.Windows.Forms.Label
        Dim Label26 As System.Windows.Forms.Label
        Dim lblPriorDDAcctType3 As System.Windows.Forms.Label
        Dim lblPriorDDAcctType1 As System.Windows.Forms.Label
        Dim lblPriorDDAcctType2 As System.Windows.Forms.Label
        Dim lblPriorDDAcctType4 As System.Windows.Forms.Label
        Dim Label27 As System.Windows.Forms.Label
        Dim Label28 As System.Windows.Forms.Label
        Dim StyleFormatCondition3 As DevExpress.XtraGrid.StyleFormatCondition = New DevExpress.XtraGrid.StyleFormatCondition()
        Dim StyleFormatCondition4 As DevExpress.XtraGrid.StyleFormatCondition = New DevExpress.XtraGrid.StyleFormatCondition()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ucEmployeeInfo))
        Dim GridFormatRule2 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleExpression2 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
        Me.colLimit = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colLimit1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.pcPriorPaysDeds = New DevExpress.XtraEditors.PanelControl()
        Me.sbDismissAutoPaysDedsPanel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnUtilities = New DevExpress.XtraEditors.DropDownButton()
        Me.UcEmployeePayHistory2 = New Brands_FrontDesk.ucEmployeePayHistory()
        Me.DataNavigator1 = New DevExpress.XtraEditors.DataNavigator()
        Me.btnSaveAndCopyPays = New DevExpress.XtraEditors.SimpleButton()
        Me.GridDeds = New DevExpress.XtraGrid.GridControl()
        Me.EmpAutoDedsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.EmpAutoPaysBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridViewDeds = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCodeDescription1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDedCalc = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colScheduling1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFirstCheckOnly1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSuppPrd1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colStartDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBalance = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.btnEditAutoDeduction = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl18 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddAutoDeduction = New DevExpress.XtraEditors.SimpleButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.btnEditAutoPay = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAddAutoPay = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl17 = New DevExpress.XtraEditors.LabelControl()
        Me.GridPays = New DevExpress.XtraGrid.GridControl()
        Me.GridViewPays = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCodeDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riLookupEarning = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.OTHERPAYBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colScheduling = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFirstCheckOnly = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSuppPrd = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBalance1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnCopy = New DevExpress.XtraEditors.SimpleButton()
        Me.btnNewEmployee = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSave = New DevExpress.XtraEditors.SimpleButton()
        Me.PanelControl2 = New DevExpress.XtraEditors.PanelControl()
        Me.btnParsonage = New DevExpress.XtraEditors.CheckButton()
        Me.pnlTerminate = New DevExpress.XtraEditors.PanelControl()
        Me.lblEmployeeStatus = New DevExpress.XtraEditors.LabelControl()
        Me.btnTerminate = New DevExpress.XtraEditors.SimpleButton()
        Me.PanelControl8 = New DevExpress.XtraEditors.PanelControl()
        Me.EMPNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.EMPLOYEEBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.Default_hoursTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.SALARY_AMTTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.RATE_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.RATE_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.RATE_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PAY_FREQTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.AUTO_SAL_HRSTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEdit4 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit2 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.DateEdit()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.GroupControl11 = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl16 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl15 = New DevExpress.XtraEditors.LabelControl()
        Me.ComboBoxEdit2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.SSNLabelControl = New DevExpress.XtraEditors.LabelControl()
        Me.TINTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.SSNTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.M_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CITYTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ZIPTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Contact_pagerTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.GENDERLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.Contact_homephoneTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.B_DAYDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.F_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Contact_homeemailTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.User_emailTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.STREETTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.L_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TINLabel = New DevExpress.XtraEditors.LabelControl()
        Me.ADDR_STATETextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.START_DATEDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.DEPTNUMLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.DEPARTMENTBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.DIVNUMLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.DIVISIONBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.EMP_TYPETextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TERM_DATEDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.PanelControlPayrollTax = New DevExpress.XtraEditors.PanelControl()
        Me.grpStateNotes = New DevExpress.XtraEditors.GroupControl()
        Me.txtStateNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.GroupControl9 = New DevExpress.XtraEditors.GroupControl()
        Me.RadioGroup1 = New DevExpress.XtraEditors.RadioGroup()
        Me.LOCAL_EE_INFOBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.LabelControl12 = New DevExpress.XtraEditors.LabelControl()
        Me.LocalActiveTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Local_statusLabel = New System.Windows.Forms.Label()
        Me.Fixed_whTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Extra_whTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DependentsLabel = New System.Windows.Forms.Label()
        Me.DependentsTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Alternate_w4Label = New System.Windows.Forms.Label()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddLocal = New DevExpress.XtraEditors.SimpleButton()
        Me.btnRemoveLocal = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.lstSelectedLocals = New DevExpress.XtraEditors.ListBoxControl()
        Me.lstAvailLocals = New DevExpress.XtraEditors.ListBoxControl()
        Me.Alternate_w4TextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Local_exemptTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Local_statusTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.GroupControl5 = New DevExpress.XtraEditors.GroupControl()
        Me.FLIEXELabel = New System.Windows.Forms.Label()
        Me.FLIEXECheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.STATE_EE_INFOBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddState = New DevExpress.XtraEditors.SimpleButton()
        Me.btnRemoveState = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.lstSelectedStates = New DevExpress.XtraEditors.ListBoxControl()
        Me.lstAvailableStates = New DevExpress.XtraEditors.ListBoxControl()
        Me.ST_WH_FIXEDLabel = New System.Windows.Forms.Label()
        Me.ST_WH_FIXEDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ST_WH_EXTRALabel = New System.Windows.Forms.Label()
        Me.ST_WH_EXTRATextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.SDIEXELabel = New System.Windows.Forms.Label()
        Me.ST_DEPSLabel = New System.Windows.Forms.Label()
        Me.ST_DEPSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ST_STATUSLabel = New System.Windows.Forms.Label()
        Me.ST_WH_EXE_FGELabel = New System.Windows.Forms.Label()
        Me.ALTW4Label = New System.Windows.Forms.Label()
        Me.DEFAULT_WORKLabel = New System.Windows.Forms.Label()
        Me.DEFAULT_RESLabel = New System.Windows.Forms.Label()
        Me.UCI_STATETextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DEFAULT_RESTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.DEFAULT_WORKTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ALTW4TextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ST_WH_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ST_STATUSTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.SDIEXETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.SUTA_EXE_FGTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControl4 = New DevExpress.XtraEditors.GroupControl()
        Me.grpFedOptions = New DevExpress.XtraEditors.GroupControl()
        Me.FED_WH_FIXEDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FED_STATUSTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.FED_DEPSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FED_WH_EXTRATextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.grpTY2020Options = New DevExpress.XtraEditors.GroupControl()
        Me.txtW4_deductions = New DevExpress.XtraEditors.TextEdit()
        Me.chkW4_multijobs = New DevExpress.XtraEditors.CheckEdit()
        Me.txtW4_dependents = New DevExpress.XtraEditors.TextEdit()
        Me.txtW4_otherinc = New DevExpress.XtraEditors.TextEdit()
        Me.rgrp_w4Style = New DevExpress.XtraEditors.RadioGroup()
        Me.FED_WH_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.OASDI_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.FUTA_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.PanelControl4 = New DevExpress.XtraEditors.PanelControl()
        Me.GroupControl7 = New DevExpress.XtraEditors.GroupControl()
        Me.GridControlCoOptionsSecondCheckPay = New DevExpress.XtraGrid.GridControl()
        Me.CoOptionsSecondCheckPayCodeBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridViewSecondCheckPay = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colPayCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riPayCodes = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.colSeparateCheck = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnDelete = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.pnlOverrideGroups = New System.Windows.Forms.Panel()
        Me.pnlGeneralOptions = New System.Windows.Forms.Panel()
        Me.ExcludeFromUtilityImportCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Pr_batch_overrideBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GroupControl8 = New DevExpress.XtraEditors.GroupControl()
        Me.OTSeperateCheckHoursMoreThanTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.OTSeperateCheckCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ddlPayUnderEmpNum = New DevExpress.XtraEditors.LookUpEdit()
        Me.EMPLOYEEBindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
        Me.grpSecondCheckTaxes = New DevExpress.XtraEditors.GroupControl()
        Me.UIStateComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ResStateComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.WrkStateComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.FLIOverrdieAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.MedicareOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.OASDIOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FedOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CheckTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TaxFrequencyTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DBOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.STOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.LOCOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.tbManualCheckNumber = New DevExpress.XtraEditors.TextEdit()
        Me.grpScheduling = New DevExpress.XtraEditors.GroupControl()
        Me.FilterToPayFreqComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.FirstOfQtrCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.LastOfQtrCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.LastOfMonthCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd5CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd4CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd3CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd2CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd1CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.AllPrdsCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.pnlNetOverride = New DevExpress.XtraEditors.GroupControl()
        Me.NetOverrideDedNumTextEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.DEDUCTIONBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.NetOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.NetOverrideAdjustTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.pnlOTHours = New System.Windows.Forms.Panel()
        Me.OTHoursTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.pnlSecondCheck = New System.Windows.Forms.Panel()
        Me.DeptNum2ndCheck = New DevExpress.XtraEditors.LookUpEdit()
        Me.DEPARTMENTBindingSource2 = New System.Windows.Forms.BindingSource(Me.components)
        Me.DivNum2ndCheck = New DevExpress.XtraEditors.LookUpEdit()
        Me.PayDedCodeLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.CheckCounterTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PayDedAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PayDedTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.pnlRecordType = New System.Windows.Forms.Panel()
        Me.RecordTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.gridViewCheckOverrides = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colRecordType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCheckCounter = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.PanelControl5 = New DevExpress.XtraEditors.PanelControl()
        Me.gcEmpPriorDD = New DevExpress.XtraEditors.GroupControl()
        Me.sbDismiss = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl19 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl20 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl21 = New DevExpress.XtraEditors.LabelControl()
        Me.gcDdSetup = New DevExpress.XtraEditors.GroupControl()
        Me.sbRequestDDSetup = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.DD_STATUS_4ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_AMT_4TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_STATUS_3ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_MET_4ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_AMT_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_NO_4TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_SPLIT_MET_3ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_BANK_RT_4TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_NO_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_TYPE_4ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_BANK_RT_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_TYPE_3ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.DD_STATUS_2ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_AMT_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_SPLIT_MET_2ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_ACC_NO_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_BANK_RT_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_TYPE_2ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_STATUS_1ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_MET_1ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_ACC_TYPE_1ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_ACC_NO_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_BANK_RT_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_SPLIT_AMT_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PanelControl6 = New DevExpress.XtraEditors.PanelControl()
        Me.UcNotes1 = New Brands_FrontDesk.ucNotes()
        Me.lcEmpName = New DevExpress.XtraEditors.LabelControl()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroupTabs = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
        Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroupPayrollTax = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.layou = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgDirectDeposit = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup8 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.tabAutoPaysAndDeds = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem22 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lciPriorPaysDeds = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgPayHistory = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.btnSaveAndCopyPaysLI = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciDataNavigator = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.taxesBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.popupCopyFrom = New DevExpress.XtraEditors.PopupContainerControl()
        Me.GroupControl10 = New DevExpress.XtraEditors.GroupControl()
        Me.txtEmpNum = New DevExpress.XtraEditors.TextEdit()
        Me.txtCoName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.slueEmployeeCopyFrom = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView3 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colEmpNum2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEmpName2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl13 = New DevExpress.XtraEditors.LabelControl()
        Me.btnOk = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancelCopyToNewCompany = New DevExpress.XtraEditors.SimpleButton()
        Me.slueCompanyCopyFrom = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCONUM1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.chkUseSameEmpID = New DevExpress.XtraEditors.CheckEdit()
        AUTO_SAL_HRSLabel = New System.Windows.Forms.Label()
        Default_hoursLabel = New System.Windows.Forms.Label()
        PAY_FREQLabel = New System.Windows.Forms.Label()
        SALARY_AMTLabel = New System.Windows.Forms.Label()
        RATE_3Label = New System.Windows.Forms.Label()
        RATE_2Label = New System.Windows.Forms.Label()
        RATE_1Label = New System.Windows.Forms.Label()
        M_NAMELabel = New System.Windows.Forms.Label()
        CITYLabel = New System.Windows.Forms.Label()
        Contact_pagerLabel = New System.Windows.Forms.Label()
        STREETLabel = New System.Windows.Forms.Label()
        GENDERLabel = New System.Windows.Forms.Label()
        Contact_homephoneLabel = New System.Windows.Forms.Label()
        F_NAMELabel = New System.Windows.Forms.Label()
        Contact_homeemailLabel = New System.Windows.Forms.Label()
        B_DAYLabel = New System.Windows.Forms.Label()
        L_NAMELabel = New System.Windows.Forms.Label()
        User_emailLabel = New System.Windows.Forms.Label()
        TERM_DATELabel = New System.Windows.Forms.Label()
        START_DATELabel = New System.Windows.Forms.Label()
        DEPTNUMLabel = New System.Windows.Forms.Label()
        DIVNUMLabel = New System.Windows.Forms.Label()
        EMP_TYPELabel = New System.Windows.Forms.Label()
        Default_positionLabel = New System.Windows.Forms.Label()
        Local_activeLabel = New System.Windows.Forms.Label()
        Local_exemptLabel = New System.Windows.Forms.Label()
        Fixed_whLabel = New System.Windows.Forms.Label()
        Extra_whLabel = New System.Windows.Forms.Label()
        SUTA_EXE_FGLabel = New System.Windows.Forms.Label()
        UCI_STATELabel = New System.Windows.Forms.Label()
        FUTA_EXE_FGELabel = New System.Windows.Forms.Label()
        FED_WH_FIXEDLabel = New System.Windows.Forms.Label()
        OASDI_EXE_FGELabel = New System.Windows.Forms.Label()
        FED_WH_EXE_FGELabel = New System.Windows.Forms.Label()
        FED_WH_EXTRALabel = New System.Windows.Forms.Label()
        FED_DEPSLabel = New System.Windows.Forms.Label()
        FED_STATUSLabel = New System.Windows.Forms.Label()
        ExcludeFromUtilityImportLabel = New System.Windows.Forms.Label()
        Label14 = New System.Windows.Forms.Label()
        OTSeperateCheckHoursMoreThanLabel = New System.Windows.Forms.Label()
        OTSeperateCheckLabel = New System.Windows.Forms.Label()
        Label21 = New System.Windows.Forms.Label()
        Label20 = New System.Windows.Forms.Label()
        Label19 = New System.Windows.Forms.Label()
        Label18 = New System.Windows.Forms.Label()
        Label1 = New System.Windows.Forms.Label()
        CheckTypeLabel = New System.Windows.Forms.Label()
        MedicareOverrideAmountLabel = New System.Windows.Forms.Label()
        OASDIOverrideAmountLabel = New System.Windows.Forms.Label()
        FedOverrideAmountLabel = New System.Windows.Forms.Label()
        TaxFrequencyLabel = New System.Windows.Forms.Label()
        STOverrideAmountLabel = New System.Windows.Forms.Label()
        DBOverrideAmountLabel = New System.Windows.Forms.Label()
        LOCOverrideAmountLabel = New System.Windows.Forms.Label()
        Label17 = New System.Windows.Forms.Label()
        NetOverrideAdjustTypeLabel = New System.Windows.Forms.Label()
        NetOverrideAmountLabel = New System.Windows.Forms.Label()
        OTHoursLabel = New System.Windows.Forms.Label()
        Label15 = New System.Windows.Forms.Label()
        Label16 = New System.Windows.Forms.Label()
        CheckCounterLabel = New System.Windows.Forms.Label()
        PayDedTypeLabel = New System.Windows.Forms.Label()
        PayDedCodeLabel = New System.Windows.Forms.Label()
        PayDedAmountLabel = New System.Windows.Forms.Label()
        RecordTypeLabel = New System.Windows.Forms.Label()
        Label13 = New System.Windows.Forms.Label()
        Label7 = New System.Windows.Forms.Label()
        Label12 = New System.Windows.Forms.Label()
        Label8 = New System.Windows.Forms.Label()
        Label9 = New System.Windows.Forms.Label()
        Label10 = New System.Windows.Forms.Label()
        Label6 = New System.Windows.Forms.Label()
        Label11 = New System.Windows.Forms.Label()
        Label3 = New System.Windows.Forms.Label()
        Label4 = New System.Windows.Forms.Label()
        Label2 = New System.Windows.Forms.Label()
        Label5 = New System.Windows.Forms.Label()
        EMPNUMLabel = New System.Windows.Forms.Label()
        Label22 = New System.Windows.Forms.Label()
        Label23 = New System.Windows.Forms.Label()
        Label24 = New System.Windows.Forms.Label()
        Label25 = New System.Windows.Forms.Label()
        Label26 = New System.Windows.Forms.Label()
        lblPriorDDAcctType3 = New System.Windows.Forms.Label()
        lblPriorDDAcctType1 = New System.Windows.Forms.Label()
        lblPriorDDAcctType2 = New System.Windows.Forms.Label()
        lblPriorDDAcctType4 = New System.Windows.Forms.Label()
        Label27 = New System.Windows.Forms.Label()
        Label28 = New System.Windows.Forms.Label()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.pcPriorPaysDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pcPriorPaysDeds.SuspendLayout()
        CType(Me.GridDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmpAutoDedsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmpAutoPaysBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.GridPays, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewPays, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riLookupEarning, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OTHERPAYBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl2.SuspendLayout()
        CType(Me.pnlTerminate, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTerminate.SuspendLayout()
        CType(Me.PanelControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl8.SuspendLayout()
        CType(Me.EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMPLOYEEBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.Default_hoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SALARY_AMTTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RATE_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RATE_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RATE_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PAY_FREQTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AUTO_SAL_HRSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.GroupControl11, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl11.SuspendLayout()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TINTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SSNTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.M_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CITYTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ZIPTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Contact_pagerTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GENDERLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Contact_homephoneTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.B_DAYDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.B_DAYDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.F_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Contact_homeemailTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.User_emailTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.STREETTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.L_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ADDR_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.START_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.START_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEPTNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEPARTMENTBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DIVNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DIVISIONBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMP_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TERM_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TERM_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControlPayrollTax, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControlPayrollTax.SuspendLayout()
        CType(Me.grpStateNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpStateNotes.SuspendLayout()
        CType(Me.txtStateNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl9.SuspendLayout()
        CType(Me.RadioGroup1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LOCAL_EE_INFOBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LocalActiveTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Fixed_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Extra_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DependentsTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstSelectedLocals, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstAvailLocals, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Alternate_w4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Local_exemptTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Local_statusTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl5.SuspendLayout()
        CType(Me.FLIEXECheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.STATE_EE_INFOBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstSelectedStates, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstAvailableStates, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UCI_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEFAULT_RESTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEFAULT_WORKTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ALTW4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SDIEXETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SUTA_EXE_FGTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl4.SuspendLayout()
        CType(Me.grpFedOptions, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpFedOptions.SuspendLayout()
        CType(Me.FED_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.grpTY2020Options, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpTY2020Options.SuspendLayout()
        CType(Me.txtW4_deductions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkW4_multijobs.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtW4_dependents.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtW4_otherinc.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.rgrp_w4Style.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OASDI_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FUTA_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl4.SuspendLayout()
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl7.SuspendLayout()
        CType(Me.GridControlCoOptionsSecondCheckPay, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CoOptionsSecondCheckPayCodeBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewSecondCheckPay, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riPayCodes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlOverrideGroups.SuspendLayout()
        Me.pnlGeneralOptions.SuspendLayout()
        CType(Me.ExcludeFromUtilityImportCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Pr_batch_overrideBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl8.SuspendLayout()
        CType(Me.OTSeperateCheckHoursMoreThanTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OTSeperateCheckCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ddlPayUnderEmpNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMPLOYEEBindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.grpSecondCheckTaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpSecondCheckTaxes.SuspendLayout()
        CType(Me.UIStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ResStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.WrkStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FLIOverrdieAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.MedicareOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OASDIOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FedOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TaxFrequencyTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DBOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.STOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LOCOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tbManualCheckNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.grpScheduling, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpScheduling.SuspendLayout()
        CType(Me.FilterToPayFreqComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FirstOfQtrCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LastOfQtrCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LastOfMonthCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd5CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd4CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd3CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd2CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd1CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AllPrdsCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pnlNetOverride, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlNetOverride.SuspendLayout()
        CType(Me.NetOverrideDedNumTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEDUCTIONBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NetOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NetOverrideAdjustTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlOTHours.SuspendLayout()
        CType(Me.OTHoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSecondCheck.SuspendLayout()
        CType(Me.DeptNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEPARTMENTBindingSource2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DivNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PayDedCodeLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckCounterTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PayDedAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PayDedTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlRecordType.SuspendLayout()
        CType(Me.RecordTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gridViewCheckOverrides, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl5.SuspendLayout()
        CType(Me.gcEmpPriorDD, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gcEmpPriorDD.SuspendLayout()
        CType(Me.gcDdSetup, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gcDdSetup.SuspendLayout()
        CType(Me.DD_STATUS_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_STATUS_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_STATUS_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_STATUS_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl6.SuspendLayout()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroupTabs, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroupPayrollTax, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layou, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgDirectDeposit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tabAutoPaysAndDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciPriorPaysDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgPayHistory, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.btnSaveAndCopyPaysLI, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciDataNavigator, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.taxesBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.popupCopyFrom, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.popupCopyFrom.SuspendLayout()
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl10.SuspendLayout()
        CType(Me.txtEmpNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtCoName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueEmployeeCopyFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueCompanyCopyFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkUseSameEmpID.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'AUTO_SAL_HRSLabel
        '
        AUTO_SAL_HRSLabel.AutoSize = True
        AUTO_SAL_HRSLabel.Location = New System.Drawing.Point(4, 167)
        AUTO_SAL_HRSLabel.Name = "AUTO_SAL_HRSLabel"
        AUTO_SAL_HRSLabel.Size = New System.Drawing.Size(121, 13)
        AUTO_SAL_HRSLabel.TabIndex = 12
        AUTO_SAL_HRSLabel.Text = "Add Auto Default Hours"
        '
        'Default_hoursLabel
        '
        Default_hoursLabel.AutoSize = True
        Default_hoursLabel.Location = New System.Drawing.Point(207, 33)
        Default_hoursLabel.Name = "Default_hoursLabel"
        Default_hoursLabel.Size = New System.Drawing.Size(47, 13)
        Default_hoursLabel.TabIndex = 10
        Default_hoursLabel.Text = "Def Hrs:"
        '
        'PAY_FREQLabel
        '
        PAY_FREQLabel.AutoSize = True
        PAY_FREQLabel.Location = New System.Drawing.Point(4, 33)
        PAY_FREQLabel.Name = "PAY_FREQLabel"
        PAY_FREQLabel.Size = New System.Drawing.Size(83, 13)
        PAY_FREQLabel.TabIndex = 8
        PAY_FREQLabel.Text = "Pay Frequency:"
        '
        'SALARY_AMTLabel
        '
        SALARY_AMTLabel.AutoSize = True
        SALARY_AMTLabel.Location = New System.Drawing.Point(46, 137)
        SALARY_AMTLabel.Name = "SALARY_AMTLabel"
        SALARY_AMTLabel.Size = New System.Drawing.Size(41, 13)
        SALARY_AMTLabel.TabIndex = 6
        SALARY_AMTLabel.Text = "Salary:"
        '
        'RATE_3Label
        '
        RATE_3Label.AutoSize = True
        RATE_3Label.Location = New System.Drawing.Point(44, 111)
        RATE_3Label.Name = "RATE_3Label"
        RATE_3Label.Size = New System.Drawing.Size(43, 13)
        RATE_3Label.TabIndex = 4
        RATE_3Label.Text = "Rate 3:"
        '
        'RATE_2Label
        '
        RATE_2Label.AutoSize = True
        RATE_2Label.Location = New System.Drawing.Point(44, 85)
        RATE_2Label.Name = "RATE_2Label"
        RATE_2Label.Size = New System.Drawing.Size(43, 13)
        RATE_2Label.TabIndex = 2
        RATE_2Label.Text = "Rate 2:"
        '
        'RATE_1Label
        '
        RATE_1Label.AutoSize = True
        RATE_1Label.Location = New System.Drawing.Point(44, 59)
        RATE_1Label.Name = "RATE_1Label"
        RATE_1Label.Size = New System.Drawing.Size(43, 13)
        RATE_1Label.TabIndex = 0
        RATE_1Label.Text = "Rate 1:"
        '
        'M_NAMELabel
        '
        M_NAMELabel.AutoSize = True
        M_NAMELabel.Location = New System.Drawing.Point(256, 31)
        M_NAMELabel.Name = "M_NAMELabel"
        M_NAMELabel.Size = New System.Drawing.Size(19, 13)
        M_NAMELabel.TabIndex = 24
        M_NAMELabel.Text = "MI"
        '
        'CITYLabel
        '
        CITYLabel.AutoSize = True
        CITYLabel.Location = New System.Drawing.Point(56, 162)
        CITYLabel.Name = "CITYLabel"
        CITYLabel.Size = New System.Drawing.Size(30, 13)
        CITYLabel.TabIndex = 8
        CITYLabel.Text = "City:"
        '
        'Contact_pagerLabel
        '
        Contact_pagerLabel.AutoSize = True
        Contact_pagerLabel.Location = New System.Drawing.Point(25, 338)
        Contact_pagerLabel.Name = "Contact_pagerLabel"
        Contact_pagerLabel.Size = New System.Drawing.Size(61, 13)
        Contact_pagerLabel.TabIndex = 22
        Contact_pagerLabel.Text = "Cell Phone:"
        '
        'STREETLabel
        '
        STREETLabel.AutoSize = True
        STREETLabel.Location = New System.Drawing.Point(36, 136)
        STREETLabel.Name = "STREETLabel"
        STREETLabel.Size = New System.Drawing.Size(50, 13)
        STREETLabel.TabIndex = 6
        STREETLabel.Text = "Address:"
        '
        'GENDERLabel
        '
        GENDERLabel.AutoSize = True
        GENDERLabel.Location = New System.Drawing.Point(40, 93)
        GENDERLabel.Name = "GENDERLabel"
        GENDERLabel.Size = New System.Drawing.Size(46, 13)
        GENDERLabel.TabIndex = 4
        GENDERLabel.Text = "Gender:"
        '
        'Contact_homephoneLabel
        '
        Contact_homephoneLabel.AutoSize = True
        Contact_homephoneLabel.Location = New System.Drawing.Point(12, 312)
        Contact_homephoneLabel.Name = "Contact_homephoneLabel"
        Contact_homephoneLabel.Size = New System.Drawing.Size(71, 13)
        Contact_homephoneLabel.TabIndex = 20
        Contact_homephoneLabel.Text = "Home Phone:"
        '
        'F_NAMELabel
        '
        F_NAMELabel.AutoSize = True
        F_NAMELabel.Location = New System.Drawing.Point(24, 31)
        F_NAMELabel.Name = "F_NAMELabel"
        F_NAMELabel.Size = New System.Drawing.Size(62, 13)
        F_NAMELabel.TabIndex = 2
        F_NAMELabel.Text = "First Name:"
        '
        'Contact_homeemailLabel
        '
        Contact_homeemailLabel.AutoSize = True
        Contact_homeemailLabel.Location = New System.Drawing.Point(21, 286)
        Contact_homeemailLabel.Name = "Contact_homeemailLabel"
        Contact_homeemailLabel.Size = New System.Drawing.Size(65, 13)
        Contact_homeemailLabel.TabIndex = 18
        Contact_homeemailLabel.Text = "Home Email:"
        '
        'B_DAYLabel
        '
        B_DAYLabel.AutoSize = True
        B_DAYLabel.Location = New System.Drawing.Point(35, 226)
        B_DAYLabel.Name = "B_DAYLabel"
        B_DAYLabel.Size = New System.Drawing.Size(51, 13)
        B_DAYLabel.TabIndex = 14
        B_DAYLabel.Text = "Birthday:"
        '
        'L_NAMELabel
        '
        L_NAMELabel.AutoSize = True
        L_NAMELabel.Location = New System.Drawing.Point(25, 57)
        L_NAMELabel.Name = "L_NAMELabel"
        L_NAMELabel.Size = New System.Drawing.Size(61, 13)
        L_NAMELabel.TabIndex = 0
        L_NAMELabel.Text = "Last Name:"
        '
        'User_emailLabel
        '
        User_emailLabel.AutoSize = True
        User_emailLabel.Location = New System.Drawing.Point(20, 260)
        User_emailLabel.Name = "User_emailLabel"
        User_emailLabel.Size = New System.Drawing.Size(66, 13)
        User_emailLabel.TabIndex = 16
        User_emailLabel.Text = "Work Email :"
        '
        'TERM_DATELabel
        '
        TERM_DATELabel.AutoSize = True
        TERM_DATELabel.Location = New System.Drawing.Point(26, 158)
        TERM_DATELabel.Name = "TERM_DATELabel"
        TERM_DATELabel.Size = New System.Drawing.Size(61, 13)
        TERM_DATELabel.TabIndex = 8
        TERM_DATELabel.Text = "Term Date:"
        '
        'START_DATELabel
        '
        START_DATELabel.AutoSize = True
        START_DATELabel.Location = New System.Drawing.Point(27, 132)
        START_DATELabel.Name = "START_DATELabel"
        START_DATELabel.Size = New System.Drawing.Size(61, 13)
        START_DATELabel.TabIndex = 6
        START_DATELabel.Text = "Start Date:"
        '
        'DEPTNUMLabel
        '
        DEPTNUMLabel.AutoSize = True
        DEPTNUMLabel.Location = New System.Drawing.Point(20, 83)
        DEPTNUMLabel.Name = "DEPTNUMLabel"
        DEPTNUMLabel.Size = New System.Drawing.Size(68, 13)
        DEPTNUMLabel.TabIndex = 4
        DEPTNUMLabel.Text = "Department:"
        '
        'DIVNUMLabel
        '
        DIVNUMLabel.AutoSize = True
        DIVNUMLabel.Location = New System.Drawing.Point(40, 57)
        DIVNUMLabel.Name = "DIVNUMLabel"
        DIVNUMLabel.Size = New System.Drawing.Size(47, 13)
        DIVNUMLabel.TabIndex = 2
        DIVNUMLabel.Text = "Division:"
        '
        'EMP_TYPELabel
        '
        EMP_TYPELabel.AutoSize = True
        EMP_TYPELabel.Location = New System.Drawing.Point(52, 30)
        EMP_TYPELabel.Name = "EMP_TYPELabel"
        EMP_TYPELabel.Size = New System.Drawing.Size(35, 13)
        EMP_TYPELabel.TabIndex = 0
        EMP_TYPELabel.Text = "Type:"
        '
        'Default_positionLabel
        '
        Default_positionLabel.AutoSize = True
        Default_positionLabel.Location = New System.Drawing.Point(264, 26)
        Default_positionLabel.Name = "Default_positionLabel"
        Default_positionLabel.Size = New System.Drawing.Size(48, 13)
        Default_positionLabel.TabIndex = 40
        Default_positionLabel.Text = "Position:"
        '
        'Local_activeLabel
        '
        Local_activeLabel.AutoSize = True
        Local_activeLabel.Location = New System.Drawing.Point(225, 26)
        Local_activeLabel.Name = "Local_activeLabel"
        Local_activeLabel.Size = New System.Drawing.Size(37, 13)
        Local_activeLabel.TabIndex = 39
        Local_activeLabel.Text = "Active"
        '
        'Local_exemptLabel
        '
        Local_exemptLabel.AutoSize = True
        Local_exemptLabel.Location = New System.Drawing.Point(243, 63)
        Local_exemptLabel.Name = "Local_exemptLabel"
        Local_exemptLabel.Size = New System.Drawing.Size(98, 13)
        Local_exemptLabel.TabIndex = 37
        Local_exemptLabel.Text = "Local W/H Exempt:"
        '
        'Fixed_whLabel
        '
        Fixed_whLabel.AutoSize = True
        Fixed_whLabel.Location = New System.Drawing.Point(378, 119)
        Fixed_whLabel.Name = "Fixed_whLabel"
        Fixed_whLabel.Size = New System.Drawing.Size(80, 13)
        Fixed_whLabel.TabIndex = 36
        Fixed_whLabel.Text = "Fixed Loc W/H:"
        '
        'Extra_whLabel
        '
        Extra_whLabel.AutoSize = True
        Extra_whLabel.Location = New System.Drawing.Point(378, 93)
        Extra_whLabel.Name = "Extra_whLabel"
        Extra_whLabel.Size = New System.Drawing.Size(80, 13)
        Extra_whLabel.TabIndex = 35
        Extra_whLabel.Text = "Extra Loc W/H:"
        '
        'SUTA_EXE_FGLabel
        '
        SUTA_EXE_FGLabel.AutoSize = True
        SUTA_EXE_FGLabel.Location = New System.Drawing.Point(172, 149)
        SUTA_EXE_FGLabel.Name = "SUTA_EXE_FGLabel"
        SUTA_EXE_FGLabel.Size = New System.Drawing.Size(76, 13)
        SUTA_EXE_FGLabel.TabIndex = 20
        SUTA_EXE_FGLabel.Text = "SUTA Exempt:"
        '
        'UCI_STATELabel
        '
        UCI_STATELabel.AutoSize = True
        UCI_STATELabel.Location = New System.Drawing.Point(7, 149)
        UCI_STATELabel.Name = "UCI_STATELabel"
        UCI_STATELabel.Size = New System.Drawing.Size(58, 13)
        UCI_STATELabel.TabIndex = 0
        UCI_STATELabel.Text = "UCI State:"
        '
        'FUTA_EXE_FGELabel
        '
        FUTA_EXE_FGELabel.AutoSize = True
        FUTA_EXE_FGELabel.Location = New System.Drawing.Point(521, 113)
        FUTA_EXE_FGELabel.Name = "FUTA_EXE_FGELabel"
        FUTA_EXE_FGELabel.Size = New System.Drawing.Size(72, 13)
        FUTA_EXE_FGELabel.TabIndex = 4
        FUTA_EXE_FGELabel.Text = "FUTA Exempt"
        '
        'FED_WH_FIXEDLabel
        '
        FED_WH_FIXEDLabel.AutoSize = True
        FED_WH_FIXEDLabel.Location = New System.Drawing.Point(6, 73)
        FED_WH_FIXEDLabel.Name = "FED_WH_FIXEDLabel"
        FED_WH_FIXEDLabel.Size = New System.Drawing.Size(96, 13)
        FED_WH_FIXEDLabel.TabIndex = 6
        FED_WH_FIXEDLabel.Text = "Fixed Withholding:"
        '
        'OASDI_EXE_FGELabel
        '
        OASDI_EXE_FGELabel.AutoSize = True
        OASDI_EXE_FGELabel.Location = New System.Drawing.Point(521, 88)
        OASDI_EXE_FGELabel.Name = "OASDI_EXE_FGELabel"
        OASDI_EXE_FGELabel.Size = New System.Drawing.Size(70, 13)
        OASDI_EXE_FGELabel.TabIndex = 2
        OASDI_EXE_FGELabel.Text = "FICA Exempt"
        '
        'FED_WH_EXE_FGELabel
        '
        FED_WH_EXE_FGELabel.AutoSize = True
        FED_WH_EXE_FGELabel.Location = New System.Drawing.Point(14, 59)
        FED_WH_EXE_FGELabel.Name = "FED_WH_EXE_FGELabel"
        FED_WH_EXE_FGELabel.Size = New System.Drawing.Size(92, 13)
        FED_WH_EXE_FGELabel.TabIndex = 0
        FED_WH_EXE_FGELabel.Text = "Fed W/H Exempt:"
        '
        'FED_WH_EXTRALabel
        '
        FED_WH_EXTRALabel.AutoSize = True
        FED_WH_EXTRALabel.Location = New System.Drawing.Point(6, 50)
        FED_WH_EXTRALabel.Name = "FED_WH_EXTRALabel"
        FED_WH_EXTRALabel.Size = New System.Drawing.Size(96, 13)
        FED_WH_EXTRALabel.TabIndex = 4
        FED_WH_EXTRALabel.Text = "Extra Withholding:"
        '
        'FED_DEPSLabel
        '
        FED_DEPSLabel.AutoSize = True
        FED_DEPSLabel.Location = New System.Drawing.Point(36, 27)
        FED_DEPSLabel.Name = "FED_DEPSLabel"
        FED_DEPSLabel.Size = New System.Drawing.Size(64, 13)
        FED_DEPSLabel.TabIndex = 2
        FED_DEPSLabel.Text = "Allowances:"
        '
        'FED_STATUSLabel
        '
        FED_STATUSLabel.AutoSize = True
        FED_STATUSLabel.Location = New System.Drawing.Point(58, 4)
        FED_STATUSLabel.Name = "FED_STATUSLabel"
        FED_STATUSLabel.Size = New System.Drawing.Size(42, 13)
        FED_STATUSLabel.TabIndex = 0
        FED_STATUSLabel.Text = "Status:"
        '
        'ExcludeFromUtilityImportLabel
        '
        ExcludeFromUtilityImportLabel.AutoSize = True
        ExcludeFromUtilityImportLabel.Location = New System.Drawing.Point(10, 89)
        ExcludeFromUtilityImportLabel.Name = "ExcludeFromUtilityImportLabel"
        ExcludeFromUtilityImportLabel.Size = New System.Drawing.Size(140, 13)
        ExcludeFromUtilityImportLabel.TabIndex = 23
        ExcludeFromUtilityImportLabel.Text = "Exclude From Utility Import:"
        '
        'Label14
        '
        Label14.AutoSize = True
        Label14.Location = New System.Drawing.Point(10, 62)
        Label14.Name = "Label14"
        Label14.Size = New System.Drawing.Size(120, 13)
        Label14.TabIndex = 23
        Label14.Text = "Pay under Employee #:"
        '
        'OTSeperateCheckHoursMoreThanLabel
        '
        OTSeperateCheckHoursMoreThanLabel.AutoSize = True
        OTSeperateCheckHoursMoreThanLabel.Location = New System.Drawing.Point(167, 28)
        OTSeperateCheckHoursMoreThanLabel.Name = "OTSeperateCheckHoursMoreThanLabel"
        OTSeperateCheckHoursMoreThanLabel.Size = New System.Drawing.Size(134, 13)
        OTSeperateCheckHoursMoreThanLabel.TabIndex = 17
        OTSeperateCheckHoursMoreThanLabel.Text = "Only if hours is more than:"
        '
        'OTSeperateCheckLabel
        '
        OTSeperateCheckLabel.AutoSize = True
        OTSeperateCheckLabel.Location = New System.Drawing.Point(10, 28)
        OTSeperateCheckLabel.Name = "OTSeperateCheckLabel"
        OTSeperateCheckLabel.Size = New System.Drawing.Size(121, 13)
        OTSeperateCheckLabel.TabIndex = 16
        OTSeperateCheckLabel.Text = "OT On Seperate Check:"
        '
        'Label21
        '
        Label21.AutoSize = True
        Label21.Location = New System.Drawing.Point(372, 121)
        Label21.Name = "Label21"
        Label21.Size = New System.Drawing.Size(58, 13)
        Label21.TabIndex = 41
        Label21.Text = "UCI State:"
        '
        'Label20
        '
        Label20.AutoSize = True
        Label20.Location = New System.Drawing.Point(372, 99)
        Label20.Name = "Label20"
        Label20.Size = New System.Drawing.Size(58, 13)
        Label20.TabIndex = 39
        Label20.Text = "Res State:"
        '
        'Label19
        '
        Label19.AutoSize = True
        Label19.Location = New System.Drawing.Point(371, 77)
        Label19.Name = "Label19"
        Label19.Size = New System.Drawing.Size(59, 13)
        Label19.TabIndex = 37
        Label19.Text = "Wrk State:"
        '
        'Label18
        '
        Label18.AutoSize = True
        Label18.Location = New System.Drawing.Point(212, 121)
        Label18.Name = "Label18"
        Label18.Size = New System.Drawing.Size(26, 13)
        Label18.TabIndex = 36
        Label18.Text = "FLI:"
        '
        'Label1
        '
        Label1.AutoSize = True
        Label1.Location = New System.Drawing.Point(379, 24)
        Label1.Name = "Label1"
        Label1.Size = New System.Drawing.Size(51, 13)
        Label1.TabIndex = 34
        Label1.Text = "Check #:"
        '
        'CheckTypeLabel
        '
        CheckTypeLabel.AutoSize = True
        CheckTypeLabel.Location = New System.Drawing.Point(217, 24)
        CheckTypeLabel.Name = "CheckTypeLabel"
        CheckTypeLabel.Size = New System.Drawing.Size(67, 13)
        CheckTypeLabel.TabIndex = 32
        CheckTypeLabel.Text = "Check Type:"
        '
        'MedicareOverrideAmountLabel
        '
        MedicareOverrideAmountLabel.AutoSize = True
        MedicareOverrideAmountLabel.Location = New System.Drawing.Point(25, 99)
        MedicareOverrideAmountLabel.Name = "MedicareOverrideAmountLabel"
        MedicareOverrideAmountLabel.Size = New System.Drawing.Size(54, 13)
        MedicareOverrideAmountLabel.TabIndex = 31
        MedicareOverrideAmountLabel.Text = "Medicare:"
        '
        'OASDIOverrideAmountLabel
        '
        OASDIOverrideAmountLabel.AutoSize = True
        OASDIOverrideAmountLabel.Location = New System.Drawing.Point(36, 77)
        OASDIOverrideAmountLabel.Name = "OASDIOverrideAmountLabel"
        OASDIOverrideAmountLabel.Size = New System.Drawing.Size(43, 13)
        OASDIOverrideAmountLabel.TabIndex = 30
        OASDIOverrideAmountLabel.Text = "OASDI:"
        '
        'FedOverrideAmountLabel
        '
        FedOverrideAmountLabel.AutoSize = True
        FedOverrideAmountLabel.Location = New System.Drawing.Point(32, 55)
        FedOverrideAmountLabel.Name = "FedOverrideAmountLabel"
        FedOverrideAmountLabel.Size = New System.Drawing.Size(47, 13)
        FedOverrideAmountLabel.TabIndex = 25
        FedOverrideAmountLabel.Text = "Federal:"
        '
        'TaxFrequencyLabel
        '
        TaxFrequencyLabel.AutoSize = True
        TaxFrequencyLabel.Location = New System.Drawing.Point(17, 23)
        TaxFrequencyLabel.Name = "TaxFrequencyLabel"
        TaxFrequencyLabel.Size = New System.Drawing.Size(62, 13)
        TaxFrequencyLabel.TabIndex = 29
        TaxFrequencyLabel.Text = "Frequency:"
        '
        'STOverrideAmountLabel
        '
        STOverrideAmountLabel.AutoSize = True
        STOverrideAmountLabel.Location = New System.Drawing.Point(201, 55)
        STOverrideAmountLabel.Name = "STOverrideAmountLabel"
        STOverrideAmountLabel.Size = New System.Drawing.Size(37, 13)
        STOverrideAmountLabel.TabIndex = 26
        STOverrideAmountLabel.Text = "State:"
        '
        'DBOverrideAmountLabel
        '
        DBOverrideAmountLabel.AutoSize = True
        DBOverrideAmountLabel.Location = New System.Drawing.Point(179, 99)
        DBOverrideAmountLabel.Name = "DBOverrideAmountLabel"
        DBOverrideAmountLabel.Size = New System.Drawing.Size(59, 13)
        DBOverrideAmountLabel.TabIndex = 28
        DBOverrideAmountLabel.Text = "Disabillaty:"
        '
        'LOCOverrideAmountLabel
        '
        LOCOverrideAmountLabel.AutoSize = True
        LOCOverrideAmountLabel.Location = New System.Drawing.Point(187, 77)
        LOCOverrideAmountLabel.Name = "LOCOverrideAmountLabel"
        LOCOverrideAmountLabel.Size = New System.Drawing.Size(51, 13)
        LOCOverrideAmountLabel.TabIndex = 27
        LOCOverrideAmountLabel.Text = "NY Local:"
        '
        'Label17
        '
        Label17.AutoSize = True
        Label17.Location = New System.Drawing.Point(369, 24)
        Label17.Name = "Label17"
        Label17.Size = New System.Drawing.Size(41, 13)
        Label17.TabIndex = 7
        Label17.Text = "Ded #:"
        '
        'NetOverrideAdjustTypeLabel
        '
        NetOverrideAdjustTypeLabel.AutoSize = True
        NetOverrideAdjustTypeLabel.Location = New System.Drawing.Point(195, 23)
        NetOverrideAdjustTypeLabel.Name = "NetOverrideAdjustTypeLabel"
        NetOverrideAdjustTypeLabel.Size = New System.Drawing.Size(57, 13)
        NetOverrideAdjustTypeLabel.TabIndex = 2
        NetOverrideAdjustTypeLabel.Text = "Adjust By:"
        '
        'NetOverrideAmountLabel
        '
        NetOverrideAmountLabel.AutoSize = True
        NetOverrideAmountLabel.Location = New System.Drawing.Point(7, 23)
        NetOverrideAmountLabel.Name = "NetOverrideAmountLabel"
        NetOverrideAmountLabel.Size = New System.Drawing.Size(93, 13)
        NetOverrideAmountLabel.TabIndex = 0
        NetOverrideAmountLabel.Text = "Override Amount:"
        '
        'OTHoursLabel
        '
        OTHoursLabel.AutoSize = True
        OTHoursLabel.Location = New System.Drawing.Point(42, 6)
        OTHoursLabel.Name = "OTHoursLabel"
        OTHoursLabel.Size = New System.Drawing.Size(56, 13)
        OTHoursLabel.TabIndex = 0
        OTHoursLabel.Text = "OT Hours:"
        '
        'Label15
        '
        Label15.AutoSize = True
        Label15.Location = New System.Drawing.Point(32, 139)
        Label15.Name = "Label15"
        Label15.Size = New System.Drawing.Size(68, 13)
        Label15.TabIndex = 14
        Label15.Text = "Department:"
        '
        'Label16
        '
        Label16.AutoSize = True
        Label16.Location = New System.Drawing.Point(52, 113)
        Label16.Name = "Label16"
        Label16.Size = New System.Drawing.Size(47, 13)
        Label16.TabIndex = 12
        Label16.Text = "Division:"
        '
        'CheckCounterLabel
        '
        CheckCounterLabel.AutoSize = True
        CheckCounterLabel.Location = New System.Drawing.Point(17, 86)
        CheckCounterLabel.Name = "CheckCounterLabel"
        CheckCounterLabel.Size = New System.Drawing.Size(82, 13)
        CheckCounterLabel.TabIndex = 10
        CheckCounterLabel.Text = "Check Counter:"
        '
        'PayDedTypeLabel
        '
        PayDedTypeLabel.AutoSize = True
        PayDedTypeLabel.Location = New System.Drawing.Point(47, 8)
        PayDedTypeLabel.Name = "PayDedTypeLabel"
        PayDedTypeLabel.Size = New System.Drawing.Size(52, 13)
        PayDedTypeLabel.TabIndex = 3
        PayDedTypeLabel.Text = "Pay/Ded:"
        '
        'PayDedCodeLabel
        '
        PayDedCodeLabel.AutoSize = True
        PayDedCodeLabel.Location = New System.Drawing.Point(19, 34)
        PayDedCodeLabel.Name = "PayDedCodeLabel"
        PayDedCodeLabel.Size = New System.Drawing.Size(80, 13)
        PayDedCodeLabel.TabIndex = 5
        PayDedCodeLabel.Text = "Pay/Ded Code:"
        '
        'PayDedAmountLabel
        '
        PayDedAmountLabel.AutoSize = True
        PayDedAmountLabel.Location = New System.Drawing.Point(51, 60)
        PayDedAmountLabel.Name = "PayDedAmountLabel"
        PayDedAmountLabel.Size = New System.Drawing.Size(48, 13)
        PayDedAmountLabel.TabIndex = 7
        PayDedAmountLabel.Text = "Amount:"
        '
        'RecordTypeLabel
        '
        RecordTypeLabel.AutoSize = True
        RecordTypeLabel.Location = New System.Drawing.Point(27, 6)
        RecordTypeLabel.Name = "RecordTypeLabel"
        RecordTypeLabel.Size = New System.Drawing.Size(72, 13)
        RecordTypeLabel.TabIndex = 1
        RecordTypeLabel.Text = "Record Type:"
        '
        'Label13
        '
        Label13.AutoSize = True
        Label13.Location = New System.Drawing.Point(40, 352)
        Label13.Name = "Label13"
        Label13.Size = New System.Drawing.Size(70, 13)
        Label13.TabIndex = 38
        Label13.Text = "Split Method:"
        '
        'Label7
        '
        Label7.AutoSize = True
        Label7.Location = New System.Drawing.Point(40, 157)
        Label7.Name = "Label7"
        Label7.Size = New System.Drawing.Size(70, 13)
        Label7.TabIndex = 38
        Label7.Text = "Split Method:"
        '
        'Label12
        '
        Label12.AutoSize = True
        Label12.Location = New System.Drawing.Point(11, 326)
        Label12.Name = "Label12"
        Label12.Size = New System.Drawing.Size(99, 13)
        Label12.TabIndex = 37
        Label12.Text = "Employee Acct. # :"
        '
        'Label8
        '
        Label8.AutoSize = True
        Label8.Location = New System.Drawing.Point(11, 131)
        Label8.Name = "Label8"
        Label8.Size = New System.Drawing.Size(99, 13)
        Label8.TabIndex = 37
        Label8.Text = "Employee Acct. # :"
        '
        'Label9
        '
        Label9.AutoSize = True
        Label9.Location = New System.Drawing.Point(25, 300)
        Label9.Name = "Label9"
        Label9.Size = New System.Drawing.Size(85, 13)
        Label9.TabIndex = 36
        Label9.Text = "Bank Routing #:"
        '
        'Label10
        '
        Label10.AutoSize = True
        Label10.Location = New System.Drawing.Point(25, 105)
        Label10.Name = "Label10"
        Label10.Size = New System.Drawing.Size(85, 13)
        Label10.TabIndex = 36
        Label10.Text = "Bank Routing #:"
        '
        'Label6
        '
        Label6.AutoSize = True
        Label6.Location = New System.Drawing.Point(30, 275)
        Label6.Name = "Label6"
        Label6.Size = New System.Drawing.Size(80, 13)
        Label6.TabIndex = 35
        Label6.Text = "Account Type :"
        '
        'Label11
        '
        Label11.AutoSize = True
        Label11.Location = New System.Drawing.Point(30, 79)
        Label11.Name = "Label11"
        Label11.Size = New System.Drawing.Size(80, 13)
        Label11.TabIndex = 35
        Label11.Text = "Account Type :"
        '
        'Label3
        '
        Label3.AutoSize = True
        Label3.Location = New System.Drawing.Point(51, 404)
        Label3.Name = "Label3"
        Label3.Size = New System.Drawing.Size(59, 13)
        Label3.TabIndex = 28
        Label3.Text = "DD Status:"
        '
        'Label4
        '
        Label4.AutoSize = True
        Label4.Location = New System.Drawing.Point(51, 209)
        Label4.Name = "Label4"
        Label4.Size = New System.Drawing.Size(59, 13)
        Label4.TabIndex = 28
        Label4.Text = "DD Status:"
        '
        'Label2
        '
        Label2.AutoSize = True
        Label2.Location = New System.Drawing.Point(59, 378)
        Label2.Name = "Label2"
        Label2.Size = New System.Drawing.Size(51, 13)
        Label2.TabIndex = 27
        Label2.Text = "Amount :"
        '
        'Label5
        '
        Label5.AutoSize = True
        Label5.Location = New System.Drawing.Point(59, 183)
        Label5.Name = "Label5"
        Label5.Size = New System.Drawing.Size(51, 13)
        Label5.TabIndex = 27
        Label5.Text = "Amount :"
        '
        'EMPNUMLabel
        '
        EMPNUMLabel.AutoSize = True
        EMPNUMLabel.Location = New System.Drawing.Point(9, 7)
        EMPNUMLabel.Name = "EMPNUMLabel"
        EMPNUMLabel.Size = New System.Drawing.Size(71, 13)
        EMPNUMLabel.TabIndex = 1
        EMPNUMLabel.Text = "Employee ID:"
        '
        'Label22
        '
        Label22.AutoSize = True
        Label22.Location = New System.Drawing.Point(328, 27)
        Label22.Name = "Label22"
        Label22.Size = New System.Drawing.Size(123, 13)
        Label22.TabIndex = 18
        Label22.Text = "Only on Pay Frequency:"
        '
        'Label23
        '
        Label23.AutoSize = True
        Label23.Location = New System.Drawing.Point(4, 34)
        Label23.Name = "Label23"
        Label23.Size = New System.Drawing.Size(104, 13)
        Label23.TabIndex = 9
        Label23.Text = "Federal W4 Version:"
        '
        'Label24
        '
        Label24.AutoSize = True
        Label24.Location = New System.Drawing.Point(4, 74)
        Label24.Name = "Label24"
        Label24.Size = New System.Drawing.Size(116, 13)
        Label24.TabIndex = 16
        Label24.Text = "Deduction Adjust (4b):"
        '
        'Label25
        '
        Label25.AutoSize = True
        Label25.Location = New System.Drawing.Point(4, 51)
        Label25.Name = "Label25"
        Label25.Size = New System.Drawing.Size(128, 13)
        Label25.TabIndex = 14
        Label25.Text = "Oth. Income Adjust (4a):"
        '
        'Label26
        '
        Label26.AutoSize = True
        Label26.Location = New System.Drawing.Point(4, 28)
        Label26.Name = "Label26"
        Label26.Size = New System.Drawing.Size(114, 13)
        Label26.TabIndex = 12
        Label26.Text = "Claim Dependents (3):"
        '
        'lblPriorDDAcctType3
        '
        lblPriorDDAcctType3.AutoSize = True
        lblPriorDDAcctType3.Location = New System.Drawing.Point(80, 182)
        lblPriorDDAcctType3.Name = "lblPriorDDAcctType3"
        lblPriorDDAcctType3.Size = New System.Drawing.Size(80, 13)
        lblPriorDDAcctType3.TabIndex = 35
        lblPriorDDAcctType3.Text = "Account Type :"
        '
        'lblPriorDDAcctType1
        '
        lblPriorDDAcctType1.AutoSize = True
        lblPriorDDAcctType1.Location = New System.Drawing.Point(80, 130)
        lblPriorDDAcctType1.Name = "lblPriorDDAcctType1"
        lblPriorDDAcctType1.Size = New System.Drawing.Size(80, 13)
        lblPriorDDAcctType1.TabIndex = 35
        lblPriorDDAcctType1.Text = "Account Type :"
        '
        'lblPriorDDAcctType2
        '
        lblPriorDDAcctType2.AutoSize = True
        lblPriorDDAcctType2.Location = New System.Drawing.Point(254, 130)
        lblPriorDDAcctType2.Name = "lblPriorDDAcctType2"
        lblPriorDDAcctType2.Size = New System.Drawing.Size(80, 13)
        lblPriorDDAcctType2.TabIndex = 60
        lblPriorDDAcctType2.Text = "Account Type :"
        '
        'lblPriorDDAcctType4
        '
        lblPriorDDAcctType4.AutoSize = True
        lblPriorDDAcctType4.Location = New System.Drawing.Point(254, 182)
        lblPriorDDAcctType4.Name = "lblPriorDDAcctType4"
        lblPriorDDAcctType4.Size = New System.Drawing.Size(80, 13)
        lblPriorDDAcctType4.TabIndex = 61
        lblPriorDDAcctType4.Text = "Account Type :"
        '
        'Label27
        '
        Label27.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Label27.ForeColor = System.Drawing.Color.Red
        Label27.Location = New System.Drawing.Point(22, 23)
        Label27.Name = "Label27"
        Label27.Size = New System.Drawing.Size(396, 56)
        Label27.TabIndex = 62
        Label27.Text = "This employee had direct deposit active prior to terminating, confirm with client" &
    " if to activate again and review all dd setup"
        '
        'Label28
        '
        Label28.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Label28.ForeColor = System.Drawing.Color.Red
        Label28.Location = New System.Drawing.Point(22, 43)
        Label28.Name = "Label28"
        Label28.Size = New System.Drawing.Size(780, 39)
        Label28.TabIndex = 63
        Label28.Text = "This employee has Auto Pays\Deds, make sure with client that this is correct or m" &
    "ake the appropriate changes"
        '
        'colLimit
        '
        Me.colLimit.Caption = "Limit"
        Me.colLimit.DisplayFormat.FormatString = "c"
        Me.colLimit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colLimit.FieldName = "Limit"
        Me.colLimit.Name = "colLimit"
        Me.colLimit.OptionsColumn.ReadOnly = True
        Me.colLimit.Visible = True
        Me.colLimit.VisibleIndex = 6
        Me.colLimit.Width = 88
        '
        'colLimit1
        '
        Me.colLimit1.Caption = "Limit"
        Me.colLimit1.DisplayFormat.FormatString = "c"
        Me.colLimit1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colLimit1.FieldName = "Limit"
        Me.colLimit1.Name = "colLimit1"
        Me.colLimit1.OptionsColumn.ReadOnly = True
        Me.colLimit1.Visible = True
        Me.colLimit1.VisibleIndex = 6
        Me.colLimit1.Width = 89
        '
        'LayoutControl1
        '
        Me.LayoutControl1.AllowCustomization = False
        Me.LayoutControl1.Controls.Add(Me.PanelControl2)
        Me.LayoutControl1.Controls.Add(Me.pcPriorPaysDeds)
        Me.LayoutControl1.Controls.Add(Me.btnUtilities)
        Me.LayoutControl1.Controls.Add(Me.UcEmployeePayHistory2)
        Me.LayoutControl1.Controls.Add(Me.DataNavigator1)
        Me.LayoutControl1.Controls.Add(Me.btnSaveAndCopyPays)
        Me.LayoutControl1.Controls.Add(Me.GridDeds)
        Me.LayoutControl1.Controls.Add(Me.Panel2)
        Me.LayoutControl1.Controls.Add(Me.Panel1)
        Me.LayoutControl1.Controls.Add(Me.GridPays)
        Me.LayoutControl1.Controls.Add(Me.btnCopy)
        Me.LayoutControl1.Controls.Add(Me.btnNewEmployee)
        Me.LayoutControl1.Controls.Add(Me.btnCancel)
        Me.LayoutControl1.Controls.Add(Me.btnSave)
        Me.LayoutControl1.Controls.Add(Me.PanelControlPayrollTax)
        Me.LayoutControl1.Controls.Add(Me.PanelControl4)
        Me.LayoutControl1.Controls.Add(Me.PanelControl5)
        Me.LayoutControl1.Controls.Add(Me.PanelControl6)
        Me.LayoutControl1.Controls.Add(Me.lcEmpName)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(62, 211, 1334, 650)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1090, 620)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'pcPriorPaysDeds
        '
        Me.pcPriorPaysDeds.Controls.Add(Me.sbDismissAutoPaysDedsPanel)
        Me.pcPriorPaysDeds.Controls.Add(Label28)
        Me.pcPriorPaysDeds.Location = New System.Drawing.Point(12, 500)
        Me.pcPriorPaysDeds.Name = "pcPriorPaysDeds"
        Me.pcPriorPaysDeds.Size = New System.Drawing.Size(993, 108)
        Me.pcPriorPaysDeds.TabIndex = 34
        '
        'sbDismissAutoPaysDedsPanel
        '
        Me.sbDismissAutoPaysDedsPanel.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.sbDismissAutoPaysDedsPanel.Appearance.ForeColor = System.Drawing.Color.Red
        Me.sbDismissAutoPaysDedsPanel.Appearance.Options.UseFont = True
        Me.sbDismissAutoPaysDedsPanel.Appearance.Options.UseForeColor = True
        Me.sbDismissAutoPaysDedsPanel.Location = New System.Drawing.Point(850, 43)
        Me.sbDismissAutoPaysDedsPanel.Name = "sbDismissAutoPaysDedsPanel"
        Me.sbDismissAutoPaysDedsPanel.Size = New System.Drawing.Size(88, 22)
        Me.sbDismissAutoPaysDedsPanel.StyleController = Me.LayoutControl1
        Me.sbDismissAutoPaysDedsPanel.TabIndex = 64
        Me.sbDismissAutoPaysDedsPanel.Text = "Dismiss"
        '
        'btnUtilities
        '
        Me.btnUtilities.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show
        Me.btnUtilities.Location = New System.Drawing.Point(506, 2)
        Me.btnUtilities.Name = "btnUtilities"
        Me.btnUtilities.Size = New System.Drawing.Size(57, 22)
        Me.btnUtilities.StyleController = Me.LayoutControl1
        Me.btnUtilities.TabIndex = 33
        Me.btnUtilities.Text = "Utilities"
        '
        'UcEmployeePayHistory2
        '
        Me.UcEmployeePayHistory2.CoNum = Nothing
        Me.UcEmployeePayHistory2.Empnum = Nothing
        Me.UcEmployeePayHistory2.Location = New System.Drawing.Point(12, 70)
        Me.UcEmployeePayHistory2.Margin = New System.Windows.Forms.Padding(6)
        Me.UcEmployeePayHistory2.Name = "UcEmployeePayHistory2"
        Me.UcEmployeePayHistory2.Size = New System.Drawing.Size(1066, 538)
        Me.UcEmployeePayHistory2.TabIndex = 32
        '
        'DataNavigator1
        '
        Me.DataNavigator1.Buttons.Append.Visible = False
        Me.DataNavigator1.Buttons.CancelEdit.Visible = False
        Me.DataNavigator1.Buttons.EndEdit.Visible = False
        Me.DataNavigator1.Buttons.NextPage.Visible = False
        Me.DataNavigator1.Buttons.PrevPage.Visible = False
        Me.DataNavigator1.Buttons.Remove.Visible = False
        Me.DataNavigator1.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.DataNavigator1.Location = New System.Drawing.Point(326, 2)
        Me.DataNavigator1.Name = "DataNavigator1"
        Me.DataNavigator1.Size = New System.Drawing.Size(165, 33)
        Me.DataNavigator1.StyleController = Me.LayoutControl1
        Me.DataNavigator1.TabIndex = 28
        Me.DataNavigator1.Text = "DataNavigator1"
        Me.DataNavigator1.TextLocation = DevExpress.XtraEditors.NavigatorButtonsTextLocation.Center
        Me.DataNavigator1.TextStringFormat = "{0} | {1}"
        '
        'btnSaveAndCopyPays
        '
        Me.btnSaveAndCopyPays.Location = New System.Drawing.Point(567, 2)
        Me.btnSaveAndCopyPays.Name = "btnSaveAndCopyPays"
        Me.btnSaveAndCopyPays.Size = New System.Drawing.Size(170, 22)
        Me.btnSaveAndCopyPays.StyleController = Me.LayoutControl1
        Me.btnSaveAndCopyPays.TabIndex = 19
        Me.btnSaveAndCopyPays.Text = "Save && Copy Pays/Deductions"
        '
        'GridDeds
        '
        Me.GridDeds.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridDeds.DataSource = Me.EmpAutoDedsBindingSource
        Me.GridDeds.Location = New System.Drawing.Point(12, 309)
        Me.GridDeds.MainView = Me.GridViewDeds
        Me.GridDeds.Name = "GridDeds"
        Me.GridDeds.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemTextEdit1})
        Me.GridDeds.Size = New System.Drawing.Size(993, 187)
        Me.GridDeds.TabIndex = 17
        Me.GridDeds.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewDeds})
        '
        'EmpAutoDedsBindingSource
        '
        Me.EmpAutoDedsBindingSource.DataSource = Me.EmpAutoPaysBindingSource
        '
        'GridViewDeds
        '
        Me.GridViewDeds.Appearance.HeaderPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GridViewDeds.Appearance.HeaderPanel.Options.UseFont = True
        Me.GridViewDeds.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCodeDescription1, Me.colDedCalc, Me.colScheduling1, Me.colFirstCheckOnly1, Me.colSuppPrd1, Me.colStartDate, Me.colLimit, Me.colBalance, Me.GridColumn4})
        StyleFormatCondition3.Appearance.ForeColor = System.Drawing.Color.Silver
        StyleFormatCondition3.Appearance.Options.HighPriority = True
        StyleFormatCondition3.Appearance.Options.UseForeColor = True
        StyleFormatCondition3.ApplyToRow = True
        StyleFormatCondition3.Column = Me.colLimit
        StyleFormatCondition3.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression
        StyleFormatCondition3.Expression = "[IsOpen]  !=  True"
        StyleFormatCondition4.Appearance.BackColor = System.Drawing.Color.Yellow
        StyleFormatCondition4.Appearance.Options.UseBackColor = True
        StyleFormatCondition4.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression
        StyleFormatCondition4.Expression = "Not IsNull([TTOOverride])"
        Me.GridViewDeds.FormatConditions.AddRange(New DevExpress.XtraGrid.StyleFormatCondition() {StyleFormatCondition3, StyleFormatCondition4})
        Me.GridViewDeds.GridControl = Me.GridDeds
        Me.GridViewDeds.LevelIndent = 0
        Me.GridViewDeds.Name = "GridViewDeds"
        Me.GridViewDeds.OptionsBehavior.ReadOnly = True
        Me.GridViewDeds.OptionsView.ShowDetailButtons = False
        Me.GridViewDeds.OptionsView.ShowGroupPanel = False
        Me.GridViewDeds.PreviewIndent = 0
        Me.GridViewDeds.ViewCaption = "Auto Deductions"
        '
        'colCodeDescription1
        '
        Me.colCodeDescription1.Caption = "Deduction"
        Me.colCodeDescription1.FieldName = "CodeDescription"
        Me.colCodeDescription1.Name = "colCodeDescription1"
        Me.colCodeDescription1.OptionsColumn.ReadOnly = True
        Me.colCodeDescription1.Visible = True
        Me.colCodeDescription1.VisibleIndex = 0
        Me.colCodeDescription1.Width = 144
        '
        'colDedCalc
        '
        Me.colDedCalc.FieldName = "DedCalc"
        Me.colDedCalc.Name = "colDedCalc"
        Me.colDedCalc.OptionsColumn.ReadOnly = True
        Me.colDedCalc.Visible = True
        Me.colDedCalc.VisibleIndex = 1
        Me.colDedCalc.Width = 86
        '
        'colScheduling1
        '
        Me.colScheduling1.Caption = "Scheduling"
        Me.colScheduling1.FieldName = "Scheduling"
        Me.colScheduling1.Name = "colScheduling1"
        Me.colScheduling1.Visible = True
        Me.colScheduling1.VisibleIndex = 2
        Me.colScheduling1.Width = 73
        '
        'colFirstCheckOnly1
        '
        Me.colFirstCheckOnly1.Caption = "1st Only"
        Me.colFirstCheckOnly1.FieldName = "FirstCheckOnly"
        Me.colFirstCheckOnly1.Name = "colFirstCheckOnly1"
        Me.colFirstCheckOnly1.OptionsColumn.ReadOnly = True
        Me.colFirstCheckOnly1.Visible = True
        Me.colFirstCheckOnly1.VisibleIndex = 3
        Me.colFirstCheckOnly1.Width = 40
        '
        'colSuppPrd1
        '
        Me.colSuppPrd1.Caption = "Aup Pr"
        Me.colSuppPrd1.FieldName = "SuppPrd"
        Me.colSuppPrd1.Name = "colSuppPrd1"
        Me.colSuppPrd1.Visible = True
        Me.colSuppPrd1.VisibleIndex = 4
        Me.colSuppPrd1.Width = 52
        '
        'colStartDate
        '
        Me.colStartDate.DisplayFormat.FormatString = "d"
        Me.colStartDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colStartDate.FieldName = "LimitStartDate"
        Me.colStartDate.Name = "colStartDate"
        Me.colStartDate.OptionsColumn.ReadOnly = True
        Me.colStartDate.Visible = True
        Me.colStartDate.VisibleIndex = 5
        Me.colStartDate.Width = 88
        '
        'colBalance
        '
        Me.colBalance.Caption = "Balance"
        Me.colBalance.DisplayFormat.FormatString = "c"
        Me.colBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colBalance.FieldName = "Balance"
        Me.colBalance.Name = "colBalance"
        Me.colBalance.OptionsColumn.ReadOnly = True
        Me.colBalance.Visible = True
        Me.colBalance.VisibleIndex = 7
        Me.colBalance.Width = 88
        '
        'GridColumn4
        '
        Me.GridColumn4.Caption = "Sch Amount"
        Me.GridColumn4.FieldName = "FormattedAmount"
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 8
        Me.GridColumn4.Width = 98
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Mask.EditMask = "f2"
        Me.RepositoryItemTextEdit1.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.btnEditAutoDeduction)
        Me.Panel2.Controls.Add(Me.LabelControl18)
        Me.Panel2.Controls.Add(Me.btnAddAutoDeduction)
        Me.Panel2.Location = New System.Drawing.Point(12, 275)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(993, 30)
        Me.Panel2.TabIndex = 27
        '
        'btnEditAutoDeduction
        '
        Me.btnEditAutoDeduction.ImageOptions.Image = CType(resources.GetObject("btnEditAutoDeduction.ImageOptions.Image"), System.Drawing.Image)
        Me.btnEditAutoDeduction.Location = New System.Drawing.Point(279, 4)
        Me.btnEditAutoDeduction.Name = "btnEditAutoDeduction"
        Me.btnEditAutoDeduction.Size = New System.Drawing.Size(125, 23)
        Me.btnEditAutoDeduction.TabIndex = 21
        Me.btnEditAutoDeduction.Text = "Edit Auto Deduction"
        '
        'LabelControl18
        '
        Me.LabelControl18.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!)
        Me.LabelControl18.Appearance.Options.UseFont = True
        Me.LabelControl18.Location = New System.Drawing.Point(22, 5)
        Me.LabelControl18.Name = "LabelControl18"
        Me.LabelControl18.Size = New System.Drawing.Size(117, 19)
        Me.LabelControl18.TabIndex = 18
        Me.LabelControl18.Text = "Auto Deductions"
        '
        'btnAddAutoDeduction
        '
        Me.btnAddAutoDeduction.ImageOptions.Image = CType(resources.GetObject("btnAddAutoDeduction.ImageOptions.Image"), System.Drawing.Image)
        Me.btnAddAutoDeduction.Location = New System.Drawing.Point(146, 4)
        Me.btnAddAutoDeduction.Name = "btnAddAutoDeduction"
        Me.btnAddAutoDeduction.Size = New System.Drawing.Size(127, 23)
        Me.btnAddAutoDeduction.TabIndex = 19
        Me.btnAddAutoDeduction.Text = "Add Auto Deduction"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.btnEditAutoPay)
        Me.Panel1.Controls.Add(Me.btnAddAutoPay)
        Me.Panel1.Controls.Add(Me.LabelControl17)
        Me.Panel1.Location = New System.Drawing.Point(12, 70)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(993, 33)
        Me.Panel1.TabIndex = 26
        '
        'btnEditAutoPay
        '
        Me.btnEditAutoPay.ImageOptions.Image = CType(resources.GetObject("btnEditAutoPay.ImageOptions.Image"), System.Drawing.Image)
        Me.btnEditAutoPay.Location = New System.Drawing.Point(253, 7)
        Me.btnEditAutoPay.Name = "btnEditAutoPay"
        Me.btnEditAutoPay.Size = New System.Drawing.Size(101, 23)
        Me.btnEditAutoPay.TabIndex = 20
        Me.btnEditAutoPay.Text = "Edit Auto Pay"
        '
        'btnAddAutoPay
        '
        Me.btnAddAutoPay.ImageOptions.Image = CType(resources.GetObject("btnAddAutoPay.ImageOptions.Image"), System.Drawing.Image)
        Me.btnAddAutoPay.Location = New System.Drawing.Point(146, 7)
        Me.btnAddAutoPay.Name = "btnAddAutoPay"
        Me.btnAddAutoPay.Size = New System.Drawing.Size(101, 23)
        Me.btnAddAutoPay.TabIndex = 16
        Me.btnAddAutoPay.Text = "Add Auto Pay"
        '
        'LabelControl17
        '
        Me.LabelControl17.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!)
        Me.LabelControl17.Appearance.Options.UseFont = True
        Me.LabelControl17.Location = New System.Drawing.Point(22, 7)
        Me.LabelControl17.Name = "LabelControl17"
        Me.LabelControl17.Size = New System.Drawing.Size(71, 19)
        Me.LabelControl17.TabIndex = 15
        Me.LabelControl17.Text = "Auto Pays"
        '
        'GridPays
        '
        Me.GridPays.DataSource = Me.EmpAutoPaysBindingSource
        Me.GridPays.Location = New System.Drawing.Point(12, 107)
        Me.GridPays.MainView = Me.GridViewPays
        Me.GridPays.Name = "GridPays"
        Me.GridPays.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riLookupEarning})
        Me.GridPays.Size = New System.Drawing.Size(993, 164)
        Me.GridPays.TabIndex = 14
        Me.GridPays.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewPays})
        '
        'GridViewPays
        '
        Me.GridViewPays.Appearance.HeaderPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GridViewPays.Appearance.HeaderPanel.Options.UseFont = True
        Me.GridViewPays.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCodeDescription, Me.GridColumn1, Me.colScheduling, Me.colFirstCheckOnly, Me.colSuppPrd, Me.GridColumn2, Me.colLimit1, Me.colBalance1, Me.GridColumn3})
        GridFormatRule2.ApplyToRow = True
        GridFormatRule2.Column = Me.colLimit1
        GridFormatRule2.Name = "Format0"
        FormatConditionRuleExpression2.Appearance.ForeColor = System.Drawing.Color.Silver
        FormatConditionRuleExpression2.Appearance.Options.HighPriority = True
        FormatConditionRuleExpression2.Appearance.Options.UseForeColor = True
        FormatConditionRuleExpression2.Expression = "[IsOpen]  !=  True"
        GridFormatRule2.Rule = FormatConditionRuleExpression2
        Me.GridViewPays.FormatRules.Add(GridFormatRule2)
        Me.GridViewPays.GridControl = Me.GridPays
        Me.GridViewPays.LevelIndent = 0
        Me.GridViewPays.Name = "GridViewPays"
        Me.GridViewPays.OptionsBehavior.ReadOnly = True
        Me.GridViewPays.OptionsView.ShowGroupPanel = False
        Me.GridViewPays.PreviewIndent = 0
        Me.GridViewPays.ViewCaption = "Auto Pays"
        '
        'colCodeDescription
        '
        Me.colCodeDescription.Caption = "Earning"
        Me.colCodeDescription.ColumnEdit = Me.riLookupEarning
        Me.colCodeDescription.FieldName = "CL_Code"
        Me.colCodeDescription.Name = "colCodeDescription"
        Me.colCodeDescription.OptionsColumn.ReadOnly = True
        Me.colCodeDescription.Visible = True
        Me.colCodeDescription.VisibleIndex = 0
        Me.colCodeDescription.Width = 144
        '
        'riLookupEarning
        '
        Me.riLookupEarning.AutoHeight = False
        Me.riLookupEarning.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riLookupEarning.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_DESC", "Earning", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CATEGORY", "Category", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.riLookupEarning.DataSource = Me.OTHERPAYBindingSource
        Me.riLookupEarning.DisplayMember = "OTH_PAY_DESC"
        Me.riLookupEarning.Name = "riLookupEarning"
        Me.riLookupEarning.NullText = ""
        Me.riLookupEarning.ValueMember = "OTH_PAY_NUM"
        '
        'OTHERPAYBindingSource
        '
        Me.OTHERPAYBindingSource.DataSource = GetType(Brands_FrontDesk.OTHER_PAY)
        '
        'GridColumn1
        '
        Me.GridColumn1.FieldName = "Category"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 1
        Me.GridColumn1.Width = 86
        '
        'colScheduling
        '
        Me.colScheduling.Caption = "Scheduling"
        Me.colScheduling.FieldName = "Scheduling"
        Me.colScheduling.Name = "colScheduling"
        Me.colScheduling.OptionsColumn.ReadOnly = True
        Me.colScheduling.Visible = True
        Me.colScheduling.VisibleIndex = 2
        Me.colScheduling.Width = 70
        '
        'colFirstCheckOnly
        '
        Me.colFirstCheckOnly.Caption = "1st Only"
        Me.colFirstCheckOnly.FieldName = "FirstCheckOnly"
        Me.colFirstCheckOnly.Name = "colFirstCheckOnly"
        Me.colFirstCheckOnly.OptionsColumn.ReadOnly = True
        Me.colFirstCheckOnly.Visible = True
        Me.colFirstCheckOnly.VisibleIndex = 3
        Me.colFirstCheckOnly.Width = 40
        '
        'colSuppPrd
        '
        Me.colSuppPrd.Caption = "Sup Pr"
        Me.colSuppPrd.FieldName = "SuppPrd"
        Me.colSuppPrd.Name = "colSuppPrd"
        Me.colSuppPrd.OptionsColumn.ReadOnly = True
        Me.colSuppPrd.Visible = True
        Me.colSuppPrd.VisibleIndex = 4
        Me.colSuppPrd.Width = 52
        '
        'GridColumn2
        '
        Me.GridColumn2.FieldName = "Rate"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 5
        Me.GridColumn2.Width = 89
        '
        'colBalance1
        '
        Me.colBalance1.Caption = "Balance"
        Me.colBalance1.DisplayFormat.FormatString = "c"
        Me.colBalance1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colBalance1.FieldName = "Balance"
        Me.colBalance1.Name = "colBalance1"
        Me.colBalance1.OptionsColumn.ReadOnly = True
        Me.colBalance1.Visible = True
        Me.colBalance1.VisibleIndex = 7
        Me.colBalance1.Width = 89
        '
        'GridColumn3
        '
        Me.GridColumn3.Caption = "Sch Amount"
        Me.GridColumn3.DisplayFormat.FormatString = "c"
        Me.GridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn3.FieldName = "Amount"
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 8
        Me.GridColumn3.Width = 98
        '
        'btnCopy
        '
        Me.btnCopy.ImageOptions.Image = CType(resources.GetObject("btnCopy.ImageOptions.Image"), System.Drawing.Image)
        Me.btnCopy.Location = New System.Drawing.Point(968, 2)
        Me.btnCopy.Name = "btnCopy"
        Me.btnCopy.Size = New System.Drawing.Size(120, 22)
        Me.btnCopy.StyleController = Me.LayoutControl1
        Me.btnCopy.TabIndex = 11
        Me.btnCopy.Text = "Copy Emp From..."
        '
        'btnNewEmployee
        '
        Me.btnNewEmployee.ImageOptions.Image = CType(resources.GetObject("btnNewEmployee.ImageOptions.Image"), System.Drawing.Image)
        Me.btnNewEmployee.Location = New System.Drawing.Point(862, 2)
        Me.btnNewEmployee.Name = "btnNewEmployee"
        Me.btnNewEmployee.Size = New System.Drawing.Size(102, 22)
        Me.btnNewEmployee.StyleController = Me.LayoutControl1
        Me.btnNewEmployee.TabIndex = 18
        Me.btnNewEmployee.Text = "New Employee"
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.ImageOptions.Image = CType(resources.GetObject("btnCancel.ImageOptions.Image"), System.Drawing.Image)
        Me.btnCancel.Location = New System.Drawing.Point(797, 2)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(61, 22)
        Me.btnCancel.StyleController = Me.LayoutControl1
        Me.btnCancel.TabIndex = 16
        Me.btnCancel.Text = "Cancel"
        '
        'btnSave
        '
        Me.btnSave.ImageOptions.Image = CType(resources.GetObject("btnSave.ImageOptions.Image"), System.Drawing.Image)
        Me.btnSave.Location = New System.Drawing.Point(741, 2)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Size = New System.Drawing.Size(52, 22)
        Me.btnSave.StyleController = Me.LayoutControl1
        Me.btnSave.TabIndex = 17
        Me.btnSave.Text = "Save"
        '
        'PanelControl2
        '
        Me.PanelControl2.Controls.Add(Me.btnParsonage)
        Me.PanelControl2.Controls.Add(Me.pnlTerminate)
        Me.PanelControl2.Controls.Add(Me.PanelControl8)
        Me.PanelControl2.Controls.Add(Me.GroupControl3)
        Me.PanelControl2.Controls.Add(Me.GroupControl1)
        Me.PanelControl2.Controls.Add(Me.GroupControl2)
        Me.PanelControl2.Location = New System.Drawing.Point(12, 70)
        Me.PanelControl2.Name = "PanelControl2"
        Me.PanelControl2.Size = New System.Drawing.Size(1066, 538)
        Me.PanelControl2.TabIndex = 20
        '
        'btnParsonage
        '
        Me.btnParsonage.Location = New System.Drawing.Point(711, 98)
        Me.btnParsonage.Name = "btnParsonage"
        Me.btnParsonage.Size = New System.Drawing.Size(150, 23)
        Me.btnParsonage.TabIndex = 10
        Me.btnParsonage.Text = "Mark Emp as Parsonage"
        '
        'pnlTerminate
        '
        Me.pnlTerminate.Controls.Add(Me.lblEmployeeStatus)
        Me.pnlTerminate.Controls.Add(Me.btnTerminate)
        Me.pnlTerminate.Location = New System.Drawing.Point(711, 57)
        Me.pnlTerminate.Name = "pnlTerminate"
        Me.pnlTerminate.Size = New System.Drawing.Size(175, 28)
        Me.pnlTerminate.TabIndex = 7
        '
        'lblEmployeeStatus
        '
        Me.lblEmployeeStatus.Location = New System.Drawing.Point(76, 7)
        Me.lblEmployeeStatus.Name = "lblEmployeeStatus"
        Me.lblEmployeeStatus.Size = New System.Drawing.Size(30, 13)
        Me.lblEmployeeStatus.TabIndex = 1
        Me.lblEmployeeStatus.Text = "Active"
        '
        'btnTerminate
        '
        Me.btnTerminate.Location = New System.Drawing.Point(5, 5)
        Me.btnTerminate.Name = "btnTerminate"
        Me.btnTerminate.Size = New System.Drawing.Size(65, 19)
        Me.btnTerminate.TabIndex = 0
        Me.btnTerminate.Text = "Terminate"
        '
        'PanelControl8
        '
        Me.PanelControl8.Controls.Add(EMPNUMLabel)
        Me.PanelControl8.Controls.Add(Me.EMPNUMTextEdit)
        Me.PanelControl8.Location = New System.Drawing.Point(711, 15)
        Me.PanelControl8.Name = "PanelControl8"
        Me.PanelControl8.Size = New System.Drawing.Size(175, 28)
        Me.PanelControl8.TabIndex = 6
        '
        'EMPNUMTextEdit
        '
        Me.EMPNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "EMPNUM", True))
        Me.EMPNUMTextEdit.Location = New System.Drawing.Point(90, 4)
        Me.EMPNUMTextEdit.Name = "EMPNUMTextEdit"
        Me.EMPNUMTextEdit.Size = New System.Drawing.Size(75, 20)
        Me.EMPNUMTextEdit.TabIndex = 2
        '
        'EMPLOYEEBindingSource
        '
        Me.EMPLOYEEBindingSource.DataSource = GetType(Brands_FrontDesk.EMPLOYEE)
        '
        'GroupControl3
        '
        Me.GroupControl3.Controls.Add(AUTO_SAL_HRSLabel)
        Me.GroupControl3.Controls.Add(Default_hoursLabel)
        Me.GroupControl3.Controls.Add(Me.Default_hoursTextEdit)
        Me.GroupControl3.Controls.Add(PAY_FREQLabel)
        Me.GroupControl3.Controls.Add(SALARY_AMTLabel)
        Me.GroupControl3.Controls.Add(Me.SALARY_AMTTextEdit)
        Me.GroupControl3.Controls.Add(RATE_3Label)
        Me.GroupControl3.Controls.Add(Me.RATE_3TextEdit)
        Me.GroupControl3.Controls.Add(RATE_2Label)
        Me.GroupControl3.Controls.Add(Me.RATE_2TextEdit)
        Me.GroupControl3.Controls.Add(RATE_1Label)
        Me.GroupControl3.Controls.Add(Me.RATE_1TextEdit)
        Me.GroupControl3.Controls.Add(Me.PAY_FREQTextEdit)
        Me.GroupControl3.Controls.Add(Me.AUTO_SAL_HRSTextEdit)
        Me.GroupControl3.Controls.Add(Me.TextEdit4)
        Me.GroupControl3.Controls.Add(Me.TextEdit3)
        Me.GroupControl3.Controls.Add(Me.TextEdit2)
        Me.GroupControl3.Controls.Add(Me.TextEdit1)
        Me.GroupControl3.Location = New System.Drawing.Point(359, 215)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.Size = New System.Drawing.Size(335, 301)
        Me.GroupControl3.TabIndex = 4
        Me.GroupControl3.Text = "Pay"
        '
        'Default_hoursTextEdit
        '
        Me.Default_hoursTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "default_hours", True))
        Me.Default_hoursTextEdit.Location = New System.Drawing.Point(256, 31)
        Me.Default_hoursTextEdit.Name = "Default_hoursTextEdit"
        Me.Default_hoursTextEdit.Properties.Mask.EditMask = "f2"
        Me.Default_hoursTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Default_hoursTextEdit.Size = New System.Drawing.Size(58, 20)
        Me.Default_hoursTextEdit.TabIndex = 2
        '
        'SALARY_AMTTextEdit
        '
        Me.SALARY_AMTTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "SALARY_AMT", True))
        Me.SALARY_AMTTextEdit.Location = New System.Drawing.Point(93, 134)
        Me.SALARY_AMTTextEdit.Name = "SALARY_AMTTextEdit"
        Me.SALARY_AMTTextEdit.Properties.Mask.EditMask = "c"
        Me.SALARY_AMTTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.SALARY_AMTTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.SALARY_AMTTextEdit.TabIndex = 9
        '
        'RATE_3TextEdit
        '
        Me.RATE_3TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "RATE_3", True))
        Me.RATE_3TextEdit.Location = New System.Drawing.Point(93, 108)
        Me.RATE_3TextEdit.Name = "RATE_3TextEdit"
        Me.RATE_3TextEdit.Size = New System.Drawing.Size(107, 20)
        Me.RATE_3TextEdit.TabIndex = 7
        '
        'RATE_2TextEdit
        '
        Me.RATE_2TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "RATE_2", True))
        Me.RATE_2TextEdit.Location = New System.Drawing.Point(93, 82)
        Me.RATE_2TextEdit.Name = "RATE_2TextEdit"
        Me.RATE_2TextEdit.Size = New System.Drawing.Size(107, 20)
        Me.RATE_2TextEdit.TabIndex = 5
        '
        'RATE_1TextEdit
        '
        Me.RATE_1TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "RATE_1", True))
        Me.RATE_1TextEdit.Location = New System.Drawing.Point(93, 56)
        Me.RATE_1TextEdit.Name = "RATE_1TextEdit"
        Me.RATE_1TextEdit.Properties.Mask.EditMask = "f4"
        Me.RATE_1TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.RATE_1TextEdit.Size = New System.Drawing.Size(107, 20)
        Me.RATE_1TextEdit.TabIndex = 3
        '
        'PAY_FREQTextEdit
        '
        Me.PAY_FREQTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "PAY_FREQ", True))
        Me.PAY_FREQTextEdit.Location = New System.Drawing.Point(93, 30)
        Me.PAY_FREQTextEdit.Name = "PAY_FREQTextEdit"
        Me.PAY_FREQTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.PAY_FREQTextEdit.Properties.Items.AddRange(New Object() {"Weekly", "Bi-Weekly", "Semi-Monthly", "Monthly", "Quarterly", "Annually"})
        Me.PAY_FREQTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.PAY_FREQTextEdit.TabIndex = 1
        '
        'AUTO_SAL_HRSTextEdit
        '
        Me.AUTO_SAL_HRSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "AUTO_SAL_HRS", True))
        Me.AUTO_SAL_HRSTextEdit.EditValue = Nothing
        Me.AUTO_SAL_HRSTextEdit.Location = New System.Drawing.Point(131, 164)
        Me.AUTO_SAL_HRSTextEdit.Name = "AUTO_SAL_HRSTextEdit"
        Me.AUTO_SAL_HRSTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.AUTO_SAL_HRSTextEdit.Properties.Caption = ""
        Me.AUTO_SAL_HRSTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.AUTO_SAL_HRSTextEdit.Properties.ValueChecked = "YES"
        Me.AUTO_SAL_HRSTextEdit.Properties.ValueUnchecked = "NO"
        Me.AUTO_SAL_HRSTextEdit.Size = New System.Drawing.Size(22, 19)
        Me.AUTO_SAL_HRSTextEdit.TabIndex = 11
        '
        'TextEdit4
        '
        Me.TextEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "LAST_RAISE_1", True))
        Me.TextEdit4.EditValue = Nothing
        Me.TextEdit4.Location = New System.Drawing.Point(210, 57)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit4.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit4.Properties.Mask.EditMask = "f4"
        Me.TextEdit4.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit4.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit4.TabIndex = 4
        '
        'TextEdit3
        '
        Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "LAST_RAISE_2", True))
        Me.TextEdit3.EditValue = Nothing
        Me.TextEdit3.Location = New System.Drawing.Point(210, 82)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit3.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit3.Properties.Mask.EditMask = ""
        Me.TextEdit3.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit3.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit3.TabIndex = 6
        '
        'TextEdit2
        '
        Me.TextEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "LAST_RAISE_3", True))
        Me.TextEdit2.EditValue = Nothing
        Me.TextEdit2.Location = New System.Drawing.Point(210, 108)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit2.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit2.Properties.Mask.EditMask = ""
        Me.TextEdit2.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit2.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit2.TabIndex = 8
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "LAST_SALARY_RAISE", True))
        Me.TextEdit1.EditValue = Nothing
        Me.TextEdit1.Location = New System.Drawing.Point(210, 134)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit1.Properties.Mask.EditMask = ""
        Me.TextEdit1.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit1.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit1.TabIndex = 10
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.GroupControl11)
        Me.GroupControl1.Controls.Add(Me.SSNLabelControl)
        Me.GroupControl1.Controls.Add(Me.TINTextEdit)
        Me.GroupControl1.Controls.Add(M_NAMELabel)
        Me.GroupControl1.Controls.Add(Me.SSNTextEdit)
        Me.GroupControl1.Controls.Add(CITYLabel)
        Me.GroupControl1.Controls.Add(Me.M_NAMETextEdit)
        Me.GroupControl1.Controls.Add(Me.CITYTextEdit)
        Me.GroupControl1.Controls.Add(Contact_pagerLabel)
        Me.GroupControl1.Controls.Add(STREETLabel)
        Me.GroupControl1.Controls.Add(Me.ZIPTextEdit)
        Me.GroupControl1.Controls.Add(Me.Contact_pagerTextEdit)
        Me.GroupControl1.Controls.Add(GENDERLabel)
        Me.GroupControl1.Controls.Add(Me.GENDERLookUpEdit)
        Me.GroupControl1.Controls.Add(Contact_homephoneLabel)
        Me.GroupControl1.Controls.Add(F_NAMELabel)
        Me.GroupControl1.Controls.Add(Me.Contact_homephoneTextEdit)
        Me.GroupControl1.Controls.Add(Me.B_DAYDateEdit)
        Me.GroupControl1.Controls.Add(Me.F_NAMETextEdit)
        Me.GroupControl1.Controls.Add(Contact_homeemailLabel)
        Me.GroupControl1.Controls.Add(B_DAYLabel)
        Me.GroupControl1.Controls.Add(L_NAMELabel)
        Me.GroupControl1.Controls.Add(Me.Contact_homeemailTextEdit)
        Me.GroupControl1.Controls.Add(Me.User_emailTextEdit)
        Me.GroupControl1.Controls.Add(Me.STREETTextEdit)
        Me.GroupControl1.Controls.Add(Me.L_NAMETextEdit)
        Me.GroupControl1.Controls.Add(User_emailLabel)
        Me.GroupControl1.Controls.Add(Me.TINLabel)
        Me.GroupControl1.Controls.Add(Me.ADDR_STATETextEdit)
        Me.GroupControl1.Location = New System.Drawing.Point(14, 15)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(329, 501)
        Me.GroupControl1.TabIndex = 0
        Me.GroupControl1.Text = "Personal"
        '
        'GroupControl11
        '
        Me.GroupControl11.Controls.Add(Me.LabelControl16)
        Me.GroupControl11.Controls.Add(Me.LabelControl15)
        Me.GroupControl11.Controls.Add(Me.ComboBoxEdit2)
        Me.GroupControl11.Controls.Add(Me.ComboBoxEdit1)
        Me.GroupControl11.Location = New System.Drawing.Point(25, 367)
        Me.GroupControl11.Name = "GroupControl11"
        Me.GroupControl11.Size = New System.Drawing.Size(285, 104)
        Me.GroupControl11.TabIndex = 14
        Me.GroupControl11.Text = "ACA Status"
        '
        'LabelControl16
        '
        Me.LabelControl16.Location = New System.Drawing.Point(13, 53)
        Me.LabelControl16.Name = "LabelControl16"
        Me.LabelControl16.Size = New System.Drawing.Size(62, 13)
        Me.LabelControl16.TabIndex = 3
        Me.LabelControl16.Text = "ACA Status: "
        '
        'LabelControl15
        '
        Me.LabelControl15.Location = New System.Drawing.Point(13, 26)
        Me.LabelControl15.Name = "LabelControl15"
        Me.LabelControl15.Size = New System.Drawing.Size(59, 13)
        Me.LabelControl15.TabIndex = 2
        Me.LabelControl15.Text = "Pay Status: "
        '
        'ComboBoxEdit2
        '
        Me.ComboBoxEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "aca_status", True))
        Me.ComboBoxEdit2.Location = New System.Drawing.Point(78, 50)
        Me.ComboBoxEdit2.Name = "ComboBoxEdit2"
        Me.ComboBoxEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit2.Properties.Items.AddRange(New Object() {"Full-Time", "Part-Time", "Seasonal", "Variable"})
        Me.ComboBoxEdit2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit2.Size = New System.Drawing.Size(194, 20)
        Me.ComboBoxEdit2.TabIndex = 16
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "Pay_Status", True))
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(78, 23)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.Items.AddRange(New Object() {"Hourly", "Salary", "Salary Non-Exempt"})
        Me.ComboBoxEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(194, 20)
        Me.ComboBoxEdit1.TabIndex = 15
        '
        'SSNLabelControl
        '
        Me.SSNLabelControl.Location = New System.Drawing.Point(60, 200)
        Me.SSNLabelControl.Name = "SSNLabelControl"
        Me.SSNLabelControl.Size = New System.Drawing.Size(26, 13)
        Me.SSNLabelControl.TabIndex = 27
        Me.SSNLabelControl.Text = "SSN: "
        '
        'TINTextEdit
        '
        Me.TINTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "TIN", True))
        Me.TINTextEdit.Location = New System.Drawing.Point(92, 192)
        Me.TINTextEdit.Name = "TINTextEdit"
        Me.TINTextEdit.Properties.Mask.EditMask = "00-0000000"
        Me.TINTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.TINTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TINTextEdit.Properties.ValidateOnEnterKey = True
        Me.TINTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.TINTextEdit.TabIndex = 8
        '
        'SSNTextEdit
        '
        Me.SSNTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "SSN", True))
        Me.SSNTextEdit.Location = New System.Drawing.Point(91, 197)
        Me.SSNTextEdit.Name = "SSNTextEdit"
        Me.SSNTextEdit.Properties.Mask.EditMask = "***********"
        Me.SSNTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.SSNTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.SSNTextEdit.Properties.ValidateOnEnterKey = True
        Me.SSNTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.SSNTextEdit.TabIndex = 8
        '
        'M_NAMETextEdit
        '
        Me.M_NAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "M_NAME", True))
        Me.M_NAMETextEdit.Location = New System.Drawing.Point(281, 28)
        Me.M_NAMETextEdit.Name = "M_NAMETextEdit"
        Me.M_NAMETextEdit.Properties.Mask.EditMask = "[A-Z]{1}\."
        Me.M_NAMETextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.M_NAMETextEdit.Properties.ValidateOnEnterKey = True
        Me.M_NAMETextEdit.Size = New System.Drawing.Size(29, 20)
        Me.M_NAMETextEdit.TabIndex = 1
        '
        'CITYTextEdit
        '
        Me.CITYTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "CITY", True))
        Me.CITYTextEdit.Location = New System.Drawing.Point(91, 159)
        Me.CITYTextEdit.Name = "CITYTextEdit"
        Me.CITYTextEdit.Properties.ValidateOnEnterKey = True
        Me.CITYTextEdit.Size = New System.Drawing.Size(90, 20)
        Me.CITYTextEdit.TabIndex = 6
        '
        'ZIPTextEdit
        '
        Me.ZIPTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "ZIP", True))
        Me.ZIPTextEdit.Location = New System.Drawing.Point(230, 159)
        Me.ZIPTextEdit.Name = "ZIPTextEdit"
        Me.ZIPTextEdit.Properties.Mask.EditMask = "\d{5}|(\d{5}-\d{4})"
        Me.ZIPTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ZIPTextEdit.Properties.Mask.ShowPlaceHolders = False
        Me.ZIPTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.ZIPTextEdit.Properties.ValidateOnEnterKey = True
        Me.ZIPTextEdit.Size = New System.Drawing.Size(80, 20)
        Me.ZIPTextEdit.TabIndex = 5
        '
        'Contact_pagerTextEdit
        '
        Me.Contact_pagerTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "contact_cellphone", True))
        Me.Contact_pagerTextEdit.Location = New System.Drawing.Point(91, 337)
        Me.Contact_pagerTextEdit.Name = "Contact_pagerTextEdit"
        Me.Contact_pagerTextEdit.Properties.Mask.BeepOnError = True
        Me.Contact_pagerTextEdit.Properties.Mask.EditMask = "(*************"
        Me.Contact_pagerTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.Contact_pagerTextEdit.Properties.ValidateOnEnterKey = True
        Me.Contact_pagerTextEdit.Size = New System.Drawing.Size(217, 20)
        Me.Contact_pagerTextEdit.TabIndex = 13
        '
        'GENDERLookUpEdit
        '
        Me.GENDERLookUpEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "GENDER", True))
        Me.GENDERLookUpEdit.Location = New System.Drawing.Point(91, 90)
        Me.GENDERLookUpEdit.Name = "GENDERLookUpEdit"
        Me.GENDERLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.GENDERLookUpEdit.Properties.DisplayMember = "Value"
        Me.GENDERLookUpEdit.Properties.NullText = ""
        Me.GENDERLookUpEdit.Properties.SearchMode = DevExpress.XtraEditors.Controls.SearchMode.AutoComplete
        Me.GENDERLookUpEdit.Properties.ShowHeader = False
        Me.GENDERLookUpEdit.Properties.ValidateOnEnterKey = True
        Me.GENDERLookUpEdit.Properties.ValueMember = "Key"
        Me.GENDERLookUpEdit.Size = New System.Drawing.Size(100, 20)
        Me.GENDERLookUpEdit.TabIndex = 3
        '
        'Contact_homephoneTextEdit
        '
        Me.Contact_homephoneTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "PH_NO", True))
        Me.Contact_homephoneTextEdit.Location = New System.Drawing.Point(91, 309)
        Me.Contact_homephoneTextEdit.Name = "Contact_homephoneTextEdit"
        Me.Contact_homephoneTextEdit.Properties.Mask.BeepOnError = True
        Me.Contact_homephoneTextEdit.Properties.Mask.EditMask = "(*************"
        Me.Contact_homephoneTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.Contact_homephoneTextEdit.Properties.ValidateOnEnterKey = True
        Me.Contact_homephoneTextEdit.Size = New System.Drawing.Size(217, 20)
        Me.Contact_homephoneTextEdit.TabIndex = 12
        '
        'B_DAYDateEdit
        '
        Me.B_DAYDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "B_DAY", True))
        Me.B_DAYDateEdit.EditValue = Nothing
        Me.B_DAYDateEdit.Location = New System.Drawing.Point(91, 223)
        Me.B_DAYDateEdit.Name = "B_DAYDateEdit"
        Me.B_DAYDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.B_DAYDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.B_DAYDateEdit.Properties.ValidateOnEnterKey = True
        Me.B_DAYDateEdit.Size = New System.Drawing.Size(100, 20)
        Me.B_DAYDateEdit.TabIndex = 9
        '
        'F_NAMETextEdit
        '
        Me.F_NAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "F_NAME", True))
        Me.F_NAMETextEdit.Location = New System.Drawing.Point(91, 28)
        Me.F_NAMETextEdit.Name = "F_NAMETextEdit"
        Me.F_NAMETextEdit.Properties.ValidateOnEnterKey = True
        Me.F_NAMETextEdit.Size = New System.Drawing.Size(159, 20)
        Me.F_NAMETextEdit.TabIndex = 0
        '
        'Contact_homeemailTextEdit
        '
        Me.Contact_homeemailTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "contact_homeemail", True))
        Me.Contact_homeemailTextEdit.Location = New System.Drawing.Point(91, 283)
        Me.Contact_homeemailTextEdit.Name = "Contact_homeemailTextEdit"
        Me.Contact_homeemailTextEdit.Properties.Mask.BeepOnError = True
        Me.Contact_homeemailTextEdit.Properties.Mask.EditMask = "(*************"
        Me.Contact_homeemailTextEdit.Properties.ValidateOnEnterKey = True
        Me.Contact_homeemailTextEdit.Size = New System.Drawing.Size(217, 20)
        Me.Contact_homeemailTextEdit.TabIndex = 11
        '
        'User_emailTextEdit
        '
        Me.User_emailTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "user_email", True))
        Me.User_emailTextEdit.Location = New System.Drawing.Point(91, 257)
        Me.User_emailTextEdit.Name = "User_emailTextEdit"
        Me.User_emailTextEdit.Properties.Mask.BeepOnError = True
        Me.User_emailTextEdit.Properties.Mask.EditMask = "(*************"
        Me.User_emailTextEdit.Properties.ValidateOnEnterKey = True
        Me.User_emailTextEdit.Size = New System.Drawing.Size(218, 20)
        Me.User_emailTextEdit.TabIndex = 10
        '
        'STREETTextEdit
        '
        Me.STREETTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "STREET", True))
        Me.STREETTextEdit.Location = New System.Drawing.Point(91, 133)
        Me.STREETTextEdit.Name = "STREETTextEdit"
        Me.STREETTextEdit.Properties.ValidateOnEnterKey = True
        Me.STREETTextEdit.Size = New System.Drawing.Size(219, 20)
        Me.STREETTextEdit.TabIndex = 4
        '
        'L_NAMETextEdit
        '
        Me.L_NAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "L_NAME", True))
        Me.L_NAMETextEdit.Location = New System.Drawing.Point(91, 54)
        Me.L_NAMETextEdit.Name = "L_NAMETextEdit"
        Me.L_NAMETextEdit.Properties.ValidateOnEnterKey = True
        Me.L_NAMETextEdit.Size = New System.Drawing.Size(219, 20)
        Me.L_NAMETextEdit.TabIndex = 2
        '
        'TINLabel
        '
        Me.TINLabel.Location = New System.Drawing.Point(49, 195)
        Me.TINLabel.Name = "TINLabel"
        Me.TINLabel.Size = New System.Drawing.Size(36, 13)
        Me.TINLabel.TabIndex = 30
        Me.TINLabel.Text = "Tax ID:"
        Me.TINLabel.Visible = False
        '
        'ADDR_STATETextEdit
        '
        Me.ADDR_STATETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "ADDR_STATE", True))
        Me.ADDR_STATETextEdit.Location = New System.Drawing.Point(187, 159)
        Me.ADDR_STATETextEdit.Name = "ADDR_STATETextEdit"
        Me.ADDR_STATETextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ADDR_STATETextEdit.Properties.Items.AddRange(New Object() {"AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DC", "DE", "FL", "GA", "HI", "IA", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MS", "MT", "NC", "ND", "NE", "NH", "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "WV", "WY"})
        Me.ADDR_STATETextEdit.Properties.MaxLength = 2
        Me.ADDR_STATETextEdit.Properties.Sorted = True
        Me.ADDR_STATETextEdit.Properties.ValidateOnEnterKey = True
        Me.ADDR_STATETextEdit.Size = New System.Drawing.Size(37, 20)
        Me.ADDR_STATETextEdit.TabIndex = 7
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(TERM_DATELabel)
        Me.GroupControl2.Controls.Add(START_DATELabel)
        Me.GroupControl2.Controls.Add(Me.START_DATEDateEdit)
        Me.GroupControl2.Controls.Add(DEPTNUMLabel)
        Me.GroupControl2.Controls.Add(Me.DEPTNUMLookUpEdit)
        Me.GroupControl2.Controls.Add(DIVNUMLabel)
        Me.GroupControl2.Controls.Add(Me.DIVNUMLookUpEdit)
        Me.GroupControl2.Controls.Add(EMP_TYPELabel)
        Me.GroupControl2.Controls.Add(Me.EMP_TYPETextEdit)
        Me.GroupControl2.Controls.Add(Me.TERM_DATEDateEdit)
        Me.GroupControl2.Location = New System.Drawing.Point(359, 15)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(335, 190)
        Me.GroupControl2.TabIndex = 3
        Me.GroupControl2.Text = "Status"
        '
        'START_DATEDateEdit
        '
        Me.START_DATEDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "START_DATE", True))
        Me.START_DATEDateEdit.EditValue = Nothing
        Me.START_DATEDateEdit.Location = New System.Drawing.Point(93, 129)
        Me.START_DATEDateEdit.Name = "START_DATEDateEdit"
        Me.START_DATEDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.START_DATEDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.START_DATEDateEdit.Properties.ValidateOnEnterKey = True
        Me.START_DATEDateEdit.Size = New System.Drawing.Size(100, 20)
        Me.START_DATEDateEdit.TabIndex = 7
        '
        'DEPTNUMLookUpEdit
        '
        Me.DEPTNUMLookUpEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DEPTNUM", True))
        Me.DEPTNUMLookUpEdit.Location = New System.Drawing.Point(92, 80)
        Me.DEPTNUMLookUpEdit.Name = "DEPTNUMLookUpEdit"
        Me.DEPTNUMLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DEPTNUMLookUpEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "Descr", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DEPTNUMLookUpEdit.Properties.DataSource = Me.DEPARTMENTBindingSource
        Me.DEPTNUMLookUpEdit.Properties.DisplayMember = "Descr"
        Me.DEPTNUMLookUpEdit.Properties.NullText = ""
        Me.DEPTNUMLookUpEdit.Properties.ShowHeader = False
        Me.DEPTNUMLookUpEdit.Properties.ValidateOnEnterKey = True
        Me.DEPTNUMLookUpEdit.Properties.ValueMember = "DEPTNUM"
        Me.DEPTNUMLookUpEdit.Size = New System.Drawing.Size(100, 20)
        Me.DEPTNUMLookUpEdit.TabIndex = 5
        '
        'DEPARTMENTBindingSource
        '
        Me.DEPARTMENTBindingSource.DataSource = GetType(Brands_FrontDesk.DEPARTMENT)
        '
        'DIVNUMLookUpEdit
        '
        Me.DIVNUMLookUpEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DIVNUM", True))
        Me.DIVNUMLookUpEdit.Location = New System.Drawing.Point(92, 54)
        Me.DIVNUMLookUpEdit.Name = "DIVNUMLookUpEdit"
        Me.DIVNUMLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DIVNUMLookUpEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DIVNUMLookUpEdit.Properties.DataSource = Me.DIVISIONBindingSource
        Me.DIVNUMLookUpEdit.Properties.DisplayMember = "Descr"
        Me.DIVNUMLookUpEdit.Properties.NullText = ""
        Me.DIVNUMLookUpEdit.Properties.ShowHeader = False
        Me.DIVNUMLookUpEdit.Properties.ValidateOnEnterKey = True
        Me.DIVNUMLookUpEdit.Properties.ValueMember = "DDIVNUM"
        Me.DIVNUMLookUpEdit.Size = New System.Drawing.Size(100, 20)
        Me.DIVNUMLookUpEdit.TabIndex = 3
        '
        'DIVISIONBindingSource
        '
        Me.DIVISIONBindingSource.DataSource = GetType(Brands_FrontDesk.DIVISION)
        '
        'EMP_TYPETextEdit
        '
        Me.EMP_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "EMP_TYPE", True))
        Me.EMP_TYPETextEdit.Location = New System.Drawing.Point(92, 27)
        Me.EMP_TYPETextEdit.Name = "EMP_TYPETextEdit"
        Me.EMP_TYPETextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EMP_TYPETextEdit.Properties.Items.AddRange(New Object() {"REGULAR", "CONTRACT"})
        Me.EMP_TYPETextEdit.Properties.ValidateOnEnterKey = True
        Me.EMP_TYPETextEdit.Size = New System.Drawing.Size(100, 20)
        Me.EMP_TYPETextEdit.TabIndex = 1
        '
        'TERM_DATEDateEdit
        '
        Me.TERM_DATEDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "TERM_DATE", True))
        Me.TERM_DATEDateEdit.EditValue = Nothing
        Me.TERM_DATEDateEdit.Location = New System.Drawing.Point(93, 155)
        Me.TERM_DATEDateEdit.Name = "TERM_DATEDateEdit"
        Me.TERM_DATEDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TERM_DATEDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.TERM_DATEDateEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.TERM_DATEDateEdit.Size = New System.Drawing.Size(100, 20)
        Me.TERM_DATEDateEdit.TabIndex = 9
        '
        'PanelControlPayrollTax
        '
        Me.PanelControlPayrollTax.Controls.Add(Me.grpStateNotes)
        Me.PanelControlPayrollTax.Controls.Add(Me.GroupControl9)
        Me.PanelControlPayrollTax.Controls.Add(Me.GroupControl5)
        Me.PanelControlPayrollTax.Controls.Add(Me.GroupControl4)
        Me.PanelControlPayrollTax.Location = New System.Drawing.Point(12, 70)
        Me.PanelControlPayrollTax.Name = "PanelControlPayrollTax"
        Me.PanelControlPayrollTax.Size = New System.Drawing.Size(1066, 538)
        Me.PanelControlPayrollTax.TabIndex = 21
        '
        'grpStateNotes
        '
        Me.grpStateNotes.Controls.Add(Me.txtStateNotes)
        Me.grpStateNotes.Location = New System.Drawing.Point(581, 189)
        Me.grpStateNotes.Name = "grpStateNotes"
        Me.grpStateNotes.Size = New System.Drawing.Size(482, 178)
        Me.grpStateNotes.TabIndex = 6
        Me.grpStateNotes.Text = "Instuctions"
        '
        'txtStateNotes
        '
        Me.txtStateNotes.Dock = System.Windows.Forms.DockStyle.Fill
        Me.txtStateNotes.Location = New System.Drawing.Point(2, 22)
        Me.txtStateNotes.Name = "txtStateNotes"
        Me.txtStateNotes.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.txtStateNotes.Properties.Appearance.Options.UseBackColor = True
        Me.txtStateNotes.Properties.AppearanceFocused.BackColor = System.Drawing.Color.Transparent
        Me.txtStateNotes.Properties.AppearanceFocused.Options.UseBackColor = True
        Me.txtStateNotes.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Transparent
        Me.txtStateNotes.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.txtStateNotes.Properties.ReadOnly = True
        Me.txtStateNotes.Properties.ValidateOnEnterKey = True
        Me.txtStateNotes.Size = New System.Drawing.Size(478, 154)
        Me.txtStateNotes.TabIndex = 6
        '
        'GroupControl9
        '
        Me.GroupControl9.Controls.Add(Me.RadioGroup1)
        Me.GroupControl9.Controls.Add(Me.LabelControl12)
        Me.GroupControl9.Controls.Add(Me.LocalActiveTextEdit)
        Me.GroupControl9.Controls.Add(Default_positionLabel)
        Me.GroupControl9.Controls.Add(Local_activeLabel)
        Me.GroupControl9.Controls.Add(Me.Local_statusLabel)
        Me.GroupControl9.Controls.Add(Local_exemptLabel)
        Me.GroupControl9.Controls.Add(Fixed_whLabel)
        Me.GroupControl9.Controls.Add(Me.Fixed_whTextEdit)
        Me.GroupControl9.Controls.Add(Extra_whLabel)
        Me.GroupControl9.Controls.Add(Me.Extra_whTextEdit)
        Me.GroupControl9.Controls.Add(Me.DependentsLabel)
        Me.GroupControl9.Controls.Add(Me.DependentsTextEdit)
        Me.GroupControl9.Controls.Add(Me.Alternate_w4Label)
        Me.GroupControl9.Controls.Add(Me.LabelControl9)
        Me.GroupControl9.Controls.Add(Me.btnAddLocal)
        Me.GroupControl9.Controls.Add(Me.btnRemoveLocal)
        Me.GroupControl9.Controls.Add(Me.LabelControl10)
        Me.GroupControl9.Controls.Add(Me.lstSelectedLocals)
        Me.GroupControl9.Controls.Add(Me.lstAvailLocals)
        Me.GroupControl9.Controls.Add(Me.Alternate_w4TextEdit)
        Me.GroupControl9.Controls.Add(Me.Local_exemptTextEdit)
        Me.GroupControl9.Controls.Add(Me.Local_statusTextEdit)
        Me.GroupControl9.Location = New System.Drawing.Point(15, 375)
        Me.GroupControl9.Name = "GroupControl9"
        Me.GroupControl9.Size = New System.Drawing.Size(560, 150)
        Me.GroupControl9.TabIndex = 5
        Me.GroupControl9.Text = "Local Taxes"
        '
        'RadioGroup1
        '
        Me.RadioGroup1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "default_position", True))
        Me.RadioGroup1.Location = New System.Drawing.Point(306, 23)
        Me.RadioGroup1.Name = "RadioGroup1"
        Me.RadioGroup1.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.RadioGroup1.Properties.Appearance.Options.UseBackColor = True
        Me.RadioGroup1.Properties.Columns = 6
        Me.RadioGroup1.Properties.Items.AddRange(New DevExpress.XtraEditors.Controls.RadioGroupItem() {New DevExpress.XtraEditors.Controls.RadioGroupItem(0, "None"), New DevExpress.XtraEditors.Controls.RadioGroupItem(1, "1"), New DevExpress.XtraEditors.Controls.RadioGroupItem(2, "2"), New DevExpress.XtraEditors.Controls.RadioGroupItem(3, "3"), New DevExpress.XtraEditors.Controls.RadioGroupItem(4, "4"), New DevExpress.XtraEditors.Controls.RadioGroupItem(5, "5")})
        Me.RadioGroup1.Size = New System.Drawing.Size(264, 20)
        Me.RadioGroup1.TabIndex = 43
        '
        'LOCAL_EE_INFOBindingSource
        '
        Me.LOCAL_EE_INFOBindingSource.DataSource = GetType(Brands_FrontDesk.LOCAL_EE_INFO)
        '
        'LabelControl12
        '
        Me.LabelControl12.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl12.Appearance.ForeColor = System.Drawing.Color.Black
        Me.LabelControl12.Appearance.Options.UseFont = True
        Me.LabelControl12.Appearance.Options.UseForeColor = True
        Me.LabelControl12.LineColor = System.Drawing.Color.Black
        Me.LabelControl12.LineLocation = DevExpress.XtraEditors.LineLocation.Center
        Me.LabelControl12.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal
        Me.LabelControl12.LineStyle = System.Drawing.Drawing2D.DashStyle.Dash
        Me.LabelControl12.LineVisible = True
        Me.LabelControl12.Location = New System.Drawing.Point(201, 42)
        Me.LabelControl12.Name = "LabelControl12"
        Me.LabelControl12.Size = New System.Drawing.Size(348, 13)
        Me.LabelControl12.TabIndex = 29
        Me.LabelControl12.Text = "---------------------------------------------------------------------------------" &
    "------"
        '
        'LocalActiveTextEdit
        '
        Me.LocalActiveTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "local_active", True))
        Me.LocalActiveTextEdit.Location = New System.Drawing.Point(201, 23)
        Me.LocalActiveTextEdit.Name = "LocalActiveTextEdit"
        Me.LocalActiveTextEdit.Properties.Caption = ""
        Me.LocalActiveTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.LocalActiveTextEdit.Properties.ValueChecked = "YES"
        Me.LocalActiveTextEdit.Properties.ValueUnchecked = "NO"
        Me.LocalActiveTextEdit.Size = New System.Drawing.Size(25, 19)
        Me.LocalActiveTextEdit.TabIndex = 42
        '
        'Local_statusLabel
        '
        Me.Local_statusLabel.AutoSize = True
        Me.Local_statusLabel.Location = New System.Drawing.Point(199, 93)
        Me.Local_statusLabel.Name = "Local_statusLabel"
        Me.Local_statusLabel.Size = New System.Drawing.Size(69, 13)
        Me.Local_statusLabel.TabIndex = 38
        Me.Local_statusLabel.Text = "Local Status:"
        '
        'Fixed_whTextEdit
        '
        Me.Fixed_whTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "fixed_wh", True))
        Me.Fixed_whTextEdit.Location = New System.Drawing.Point(464, 116)
        Me.Fixed_whTextEdit.Name = "Fixed_whTextEdit"
        Me.Fixed_whTextEdit.Size = New System.Drawing.Size(83, 20)
        Me.Fixed_whTextEdit.TabIndex = 37
        '
        'Extra_whTextEdit
        '
        Me.Extra_whTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "extra_wh", True))
        Me.Extra_whTextEdit.Location = New System.Drawing.Point(464, 90)
        Me.Extra_whTextEdit.Name = "Extra_whTextEdit"
        Me.Extra_whTextEdit.Size = New System.Drawing.Size(83, 20)
        Me.Extra_whTextEdit.TabIndex = 36
        '
        'DependentsLabel
        '
        Me.DependentsLabel.AutoSize = True
        Me.DependentsLabel.Location = New System.Drawing.Point(204, 119)
        Me.DependentsLabel.Name = "DependentsLabel"
        Me.DependentsLabel.Size = New System.Drawing.Size(64, 13)
        Me.DependentsLabel.TabIndex = 34
        Me.DependentsLabel.Text = "Allowances:"
        '
        'DependentsTextEdit
        '
        Me.DependentsTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "dependents", True))
        Me.DependentsTextEdit.Location = New System.Drawing.Point(274, 116)
        Me.DependentsTextEdit.Name = "DependentsTextEdit"
        Me.DependentsTextEdit.Properties.ReadOnly = True
        Me.DependentsTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.DependentsTextEdit.TabIndex = 35
        '
        'Alternate_w4Label
        '
        Me.Alternate_w4Label.AutoSize = True
        Me.Alternate_w4Label.Location = New System.Drawing.Point(384, 63)
        Me.Alternate_w4Label.Name = "Alternate_w4Label"
        Me.Alternate_w4Label.Size = New System.Drawing.Size(163, 13)
        Me.Alternate_w4Label.TabIndex = 33
        Me.Alternate_w4Label.Text = "Filed Separate W4 for Local WH:"
        '
        'LabelControl9
        '
        Me.LabelControl9.Location = New System.Drawing.Point(113, 25)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(41, 13)
        Me.LabelControl9.TabIndex = 33
        Me.LabelControl9.Text = "Selected"
        '
        'btnAddLocal
        '
        Me.btnAddLocal.ImageOptions.Image = CType(resources.GetObject("btnAddLocal.ImageOptions.Image"), System.Drawing.Image)
        Me.btnAddLocal.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnAddLocal.Location = New System.Drawing.Point(92, 62)
        Me.btnAddLocal.Name = "btnAddLocal"
        Me.btnAddLocal.Size = New System.Drawing.Size(16, 22)
        Me.btnAddLocal.TabIndex = 32
        Me.btnAddLocal.ToolTip = "Add Selected State"
        '
        'btnRemoveLocal
        '
        Me.btnRemoveLocal.ImageOptions.Image = CType(resources.GetObject("btnRemoveLocal.ImageOptions.Image"), System.Drawing.Image)
        Me.btnRemoveLocal.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnRemoveLocal.Location = New System.Drawing.Point(92, 90)
        Me.btnRemoveLocal.Name = "btnRemoveLocal"
        Me.btnRemoveLocal.Size = New System.Drawing.Size(16, 22)
        Me.btnRemoveLocal.TabIndex = 31
        Me.btnRemoveLocal.ToolTip = "Remove Selected State"
        '
        'LabelControl10
        '
        Me.LabelControl10.Location = New System.Drawing.Point(10, 26)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(43, 13)
        Me.LabelControl10.TabIndex = 30
        Me.LabelControl10.Text = "Availalbe"
        '
        'lstSelectedLocals
        '
        Me.lstSelectedLocals.Cursor = System.Windows.Forms.Cursors.Default
        Me.lstSelectedLocals.DataSource = Me.LOCAL_EE_INFOBindingSource
        Me.lstSelectedLocals.DisplayMember = "LocalDescription"
        Me.lstSelectedLocals.HorizontalScrollbar = True
        Me.lstSelectedLocals.Location = New System.Drawing.Point(113, 42)
        Me.lstSelectedLocals.Name = "lstSelectedLocals"
        Me.lstSelectedLocals.Size = New System.Drawing.Size(78, 95)
        Me.lstSelectedLocals.TabIndex = 29
        Me.lstSelectedLocals.ValueMember = "local_id"
        '
        'lstAvailLocals
        '
        Me.lstAvailLocals.DisplayMember = "Value"
        Me.lstAvailLocals.HorizontalScrollbar = True
        Me.lstAvailLocals.Location = New System.Drawing.Point(10, 42)
        Me.lstAvailLocals.Name = "lstAvailLocals"
        Me.lstAvailLocals.Size = New System.Drawing.Size(76, 95)
        Me.lstAvailLocals.TabIndex = 28
        Me.lstAvailLocals.ValueMember = "Key"
        '
        'Alternate_w4TextEdit
        '
        Me.Alternate_w4TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "alternate_w4", True))
        Me.Alternate_w4TextEdit.EditValue = Nothing
        Me.Alternate_w4TextEdit.Location = New System.Drawing.Point(360, 60)
        Me.Alternate_w4TextEdit.Name = "Alternate_w4TextEdit"
        Me.Alternate_w4TextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.Alternate_w4TextEdit.Properties.Caption = ""
        Me.Alternate_w4TextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.Alternate_w4TextEdit.Properties.ValueChecked = "YES"
        Me.Alternate_w4TextEdit.Properties.ValueUnchecked = "NO"
        Me.Alternate_w4TextEdit.Size = New System.Drawing.Size(24, 19)
        Me.Alternate_w4TextEdit.TabIndex = 34
        '
        'Local_exemptTextEdit
        '
        Me.Local_exemptTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "local_exempt", True))
        Me.Local_exemptTextEdit.EditValue = Nothing
        Me.Local_exemptTextEdit.Location = New System.Drawing.Point(223, 60)
        Me.Local_exemptTextEdit.Name = "Local_exemptTextEdit"
        Me.Local_exemptTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.Local_exemptTextEdit.Properties.Caption = ""
        Me.Local_exemptTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.Local_exemptTextEdit.Properties.ValueChecked = 1
        Me.Local_exemptTextEdit.Properties.ValueUnchecked = 0
        Me.Local_exemptTextEdit.Size = New System.Drawing.Size(24, 19)
        Me.Local_exemptTextEdit.TabIndex = 38
        '
        'Local_statusTextEdit
        '
        Me.Local_statusTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.LOCAL_EE_INFOBindingSource, "local_status", True))
        Me.Local_statusTextEdit.Location = New System.Drawing.Point(274, 90)
        Me.Local_statusTextEdit.Name = "Local_statusTextEdit"
        Me.Local_statusTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Local_statusTextEdit.Properties.ReadOnly = True
        Me.Local_statusTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.Local_statusTextEdit.TabIndex = 39
        '
        'GroupControl5
        '
        Me.GroupControl5.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat
        Me.GroupControl5.Controls.Add(Me.FLIEXELabel)
        Me.GroupControl5.Controls.Add(Me.FLIEXECheckEdit)
        Me.GroupControl5.Controls.Add(Me.LabelControl11)
        Me.GroupControl5.Controls.Add(Me.LabelControl8)
        Me.GroupControl5.Controls.Add(Me.btnAddState)
        Me.GroupControl5.Controls.Add(Me.btnRemoveState)
        Me.GroupControl5.Controls.Add(Me.LabelControl7)
        Me.GroupControl5.Controls.Add(Me.lstSelectedStates)
        Me.GroupControl5.Controls.Add(Me.lstAvailableStates)
        Me.GroupControl5.Controls.Add(SUTA_EXE_FGLabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_FIXEDLabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_FIXEDTextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXTRALabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXTRATextEdit)
        Me.GroupControl5.Controls.Add(Me.SDIEXELabel)
        Me.GroupControl5.Controls.Add(Me.ST_DEPSLabel)
        Me.GroupControl5.Controls.Add(Me.ST_DEPSTextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_STATUSLabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXE_FGELabel)
        Me.GroupControl5.Controls.Add(Me.ALTW4Label)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_WORKLabel)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_RESLabel)
        Me.GroupControl5.Controls.Add(UCI_STATELabel)
        Me.GroupControl5.Controls.Add(Me.UCI_STATETextEdit)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_RESTextEdit)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_WORKTextEdit)
        Me.GroupControl5.Controls.Add(Me.ALTW4TextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXE_FGETextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_STATUSTextEdit)
        Me.GroupControl5.Controls.Add(Me.SDIEXETextEdit)
        Me.GroupControl5.Controls.Add(Me.SUTA_EXE_FGTextEdit)
        Me.GroupControl5.Location = New System.Drawing.Point(15, 189)
        Me.GroupControl5.Name = "GroupControl5"
        Me.GroupControl5.Size = New System.Drawing.Size(560, 178)
        Me.GroupControl5.TabIndex = 4
        Me.GroupControl5.Text = "State Taxes"
        '
        'FLIEXELabel
        '
        Me.FLIEXELabel.AutoSize = True
        Me.FLIEXELabel.Location = New System.Drawing.Point(334, 149)
        Me.FLIEXELabel.Name = "FLIEXELabel"
        Me.FLIEXELabel.Size = New System.Drawing.Size(65, 13)
        Me.FLIEXELabel.TabIndex = 29
        Me.FLIEXELabel.Text = "FLI Exempt:"
        '
        'FLIEXECheckEdit
        '
        Me.FLIEXECheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "fliexe", True))
        Me.FLIEXECheckEdit.EditValue = Nothing
        Me.FLIEXECheckEdit.Location = New System.Drawing.Point(314, 146)
        Me.FLIEXECheckEdit.Name = "FLIEXECheckEdit"
        Me.FLIEXECheckEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.FLIEXECheckEdit.Properties.Caption = ""
        Me.FLIEXECheckEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.FLIEXECheckEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.FLIEXECheckEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.FLIEXECheckEdit.Size = New System.Drawing.Size(26, 19)
        Me.FLIEXECheckEdit.TabIndex = 30
        '
        'STATE_EE_INFOBindingSource
        '
        Me.STATE_EE_INFOBindingSource.DataSource = GetType(Brands_FrontDesk.STATE_EE_INFO)
        '
        'LabelControl11
        '
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.ForeColor = System.Drawing.Color.Black
        Me.LabelControl11.Appearance.Options.UseFont = True
        Me.LabelControl11.Appearance.Options.UseForeColor = True
        Me.LabelControl11.LineColor = System.Drawing.Color.Black
        Me.LabelControl11.LineLocation = DevExpress.XtraEditors.LineLocation.Center
        Me.LabelControl11.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal
        Me.LabelControl11.LineStyle = System.Drawing.Drawing2D.DashStyle.Dash
        Me.LabelControl11.LineVisible = True
        Me.LabelControl11.Location = New System.Drawing.Point(200, 41)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(348, 13)
        Me.LabelControl11.TabIndex = 28
        Me.LabelControl11.Text = "---------------------------------------------------------------------------------" &
    "------"
        '
        'LabelControl8
        '
        Me.LabelControl8.Location = New System.Drawing.Point(113, 24)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(41, 13)
        Me.LabelControl8.TabIndex = 27
        Me.LabelControl8.Text = "Selected"
        '
        'btnAddState
        '
        Me.btnAddState.ImageOptions.Image = CType(resources.GetObject("btnAddState.ImageOptions.Image"), System.Drawing.Image)
        Me.btnAddState.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnAddState.Location = New System.Drawing.Point(92, 66)
        Me.btnAddState.Name = "btnAddState"
        Me.btnAddState.Size = New System.Drawing.Size(16, 22)
        Me.btnAddState.TabIndex = 26
        Me.btnAddState.ToolTip = "Add Selected State"
        '
        'btnRemoveState
        '
        Me.btnRemoveState.ImageOptions.Image = CType(resources.GetObject("btnRemoveState.ImageOptions.Image"), System.Drawing.Image)
        Me.btnRemoveState.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnRemoveState.Location = New System.Drawing.Point(92, 94)
        Me.btnRemoveState.Name = "btnRemoveState"
        Me.btnRemoveState.Size = New System.Drawing.Size(16, 22)
        Me.btnRemoveState.TabIndex = 25
        Me.btnRemoveState.ToolTip = "Remove Selected State"
        '
        'LabelControl7
        '
        Me.LabelControl7.Location = New System.Drawing.Point(10, 24)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(43, 13)
        Me.LabelControl7.TabIndex = 24
        Me.LabelControl7.Text = "Availalbe"
        '
        'lstSelectedStates
        '
        Me.lstSelectedStates.Cursor = System.Windows.Forms.Cursors.Default
        Me.lstSelectedStates.DataSource = Me.STATE_EE_INFOBindingSource
        Me.lstSelectedStates.DisplayMember = "StateName"
        Me.lstSelectedStates.Location = New System.Drawing.Point(113, 41)
        Me.lstSelectedStates.Name = "lstSelectedStates"
        Me.lstSelectedStates.Size = New System.Drawing.Size(78, 95)
        Me.lstSelectedStates.TabIndex = 23
        Me.lstSelectedStates.ValueMember = "STATE"
        '
        'lstAvailableStates
        '
        Me.lstAvailableStates.DisplayMember = "Value"
        Me.lstAvailableStates.Location = New System.Drawing.Point(10, 41)
        Me.lstAvailableStates.Name = "lstAvailableStates"
        Me.lstAvailableStates.Size = New System.Drawing.Size(76, 95)
        Me.lstAvailableStates.TabIndex = 22
        Me.lstAvailableStates.ValueMember = "Key"
        '
        'ST_WH_FIXEDLabel
        '
        Me.ST_WH_FIXEDLabel.AutoSize = True
        Me.ST_WH_FIXEDLabel.Location = New System.Drawing.Point(380, 110)
        Me.ST_WH_FIXEDLabel.Name = "ST_WH_FIXEDLabel"
        Me.ST_WH_FIXEDLabel.Size = New System.Drawing.Size(78, 13)
        Me.ST_WH_FIXEDLabel.TabIndex = 18
        Me.ST_WH_FIXEDLabel.Text = "Fixed St. W/H:"
        '
        'ST_WH_FIXEDTextEdit
        '
        Me.ST_WH_FIXEDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "ST_WH_FIXED", True))
        Me.ST_WH_FIXEDTextEdit.Location = New System.Drawing.Point(464, 107)
        Me.ST_WH_FIXEDTextEdit.Name = "ST_WH_FIXEDTextEdit"
        Me.ST_WH_FIXEDTextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_WH_FIXEDTextEdit.Size = New System.Drawing.Size(83, 20)
        Me.ST_WH_FIXEDTextEdit.TabIndex = 19
        '
        'ST_WH_EXTRALabel
        '
        Me.ST_WH_EXTRALabel.AutoSize = True
        Me.ST_WH_EXTRALabel.Location = New System.Drawing.Point(380, 87)
        Me.ST_WH_EXTRALabel.Name = "ST_WH_EXTRALabel"
        Me.ST_WH_EXTRALabel.Size = New System.Drawing.Size(78, 13)
        Me.ST_WH_EXTRALabel.TabIndex = 16
        Me.ST_WH_EXTRALabel.Text = "Extra St. W/H:"
        '
        'ST_WH_EXTRATextEdit
        '
        Me.ST_WH_EXTRATextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "ST_WH_EXTRA", True))
        Me.ST_WH_EXTRATextEdit.Location = New System.Drawing.Point(464, 84)
        Me.ST_WH_EXTRATextEdit.Name = "ST_WH_EXTRATextEdit"
        Me.ST_WH_EXTRATextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_WH_EXTRATextEdit.Size = New System.Drawing.Size(83, 20)
        Me.ST_WH_EXTRATextEdit.TabIndex = 17
        '
        'SDIEXELabel
        '
        Me.SDIEXELabel.AutoSize = True
        Me.SDIEXELabel.Location = New System.Drawing.Point(452, 149)
        Me.SDIEXELabel.Name = "SDIEXELabel"
        Me.SDIEXELabel.Size = New System.Drawing.Size(92, 13)
        Me.SDIEXELabel.TabIndex = 14
        Me.SDIEXELabel.Text = "Disability Exempt:"
        '
        'ST_DEPSLabel
        '
        Me.ST_DEPSLabel.Location = New System.Drawing.Point(192, 110)
        Me.ST_DEPSLabel.Name = "ST_DEPSLabel"
        Me.ST_DEPSLabel.Size = New System.Drawing.Size(94, 13)
        Me.ST_DEPSLabel.TabIndex = 12
        Me.ST_DEPSLabel.Text = "Total Allowances:"
        Me.ST_DEPSLabel.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'ST_DEPSTextEdit
        '
        Me.ST_DEPSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "ST_DEPS", True))
        Me.ST_DEPSTextEdit.Location = New System.Drawing.Point(289, 107)
        Me.ST_DEPSTextEdit.Name = "ST_DEPSTextEdit"
        Me.ST_DEPSTextEdit.Properties.ReadOnly = True
        Me.ST_DEPSTextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_DEPSTextEdit.Size = New System.Drawing.Size(82, 20)
        Me.ST_DEPSTextEdit.TabIndex = 13
        '
        'ST_STATUSLabel
        '
        Me.ST_STATUSLabel.Location = New System.Drawing.Point(192, 87)
        Me.ST_STATUSLabel.Name = "ST_STATUSLabel"
        Me.ST_STATUSLabel.Size = New System.Drawing.Size(94, 13)
        Me.ST_STATUSLabel.TabIndex = 10
        Me.ST_STATUSLabel.Text = "State Status:"
        Me.ST_STATUSLabel.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'ST_WH_EXE_FGELabel
        '
        Me.ST_WH_EXE_FGELabel.AutoSize = True
        Me.ST_WH_EXE_FGELabel.Location = New System.Drawing.Point(243, 56)
        Me.ST_WH_EXE_FGELabel.Name = "ST_WH_EXE_FGELabel"
        Me.ST_WH_EXE_FGELabel.Size = New System.Drawing.Size(100, 13)
        Me.ST_WH_EXE_FGELabel.TabIndex = 8
        Me.ST_WH_EXE_FGELabel.Text = "State W/H Exempt:"
        '
        'ALTW4Label
        '
        Me.ALTW4Label.AutoSize = True
        Me.ALTW4Label.Location = New System.Drawing.Point(384, 56)
        Me.ALTW4Label.Name = "ALTW4Label"
        Me.ALTW4Label.Size = New System.Drawing.Size(165, 13)
        Me.ALTW4Label.TabIndex = 6
        Me.ALTW4Label.Text = "Filed Separate W4 for State WH:"
        '
        'DEFAULT_WORKLabel
        '
        Me.DEFAULT_WORKLabel.AutoSize = True
        Me.DEFAULT_WORKLabel.Location = New System.Drawing.Point(225, 28)
        Me.DEFAULT_WORKLabel.Name = "DEFAULT_WORKLabel"
        Me.DEFAULT_WORKLabel.Size = New System.Drawing.Size(123, 13)
        Me.DEFAULT_WORKLabel.TabIndex = 4
        Me.DEFAULT_WORKLabel.Text = "Default Work WH State:"
        '
        'DEFAULT_RESLabel
        '
        Me.DEFAULT_RESLabel.AutoSize = True
        Me.DEFAULT_RESLabel.Location = New System.Drawing.Point(384, 28)
        Me.DEFAULT_RESLabel.Name = "DEFAULT_RESLabel"
        Me.DEFAULT_RESLabel.Size = New System.Drawing.Size(140, 13)
        Me.DEFAULT_RESLabel.TabIndex = 2
        Me.DEFAULT_RESLabel.Text = "Default Resident WH State:"
        '
        'UCI_STATETextEdit
        '
        Me.UCI_STATETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "UCI_STATE", True))
        Me.UCI_STATETextEdit.Location = New System.Drawing.Point(71, 146)
        Me.UCI_STATETextEdit.Name = "UCI_STATETextEdit"
        Me.UCI_STATETextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.UCI_STATETextEdit.Properties.PopupSizeable = True
        Me.UCI_STATETextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.UCI_STATETextEdit.Properties.ValidateOnEnterKey = True
        Me.UCI_STATETextEdit.Size = New System.Drawing.Size(68, 20)
        Me.UCI_STATETextEdit.TabIndex = 1
        '
        'DEFAULT_RESTextEdit
        '
        Me.DEFAULT_RESTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "DEFAULT_RES", True))
        Me.DEFAULT_RESTextEdit.EditValue = Nothing
        Me.DEFAULT_RESTextEdit.Location = New System.Drawing.Point(361, 25)
        Me.DEFAULT_RESTextEdit.Name = "DEFAULT_RESTextEdit"
        Me.DEFAULT_RESTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.DEFAULT_RESTextEdit.Properties.Caption = ""
        Me.DEFAULT_RESTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.DEFAULT_RESTextEdit.Properties.ValueChecked = "YES"
        Me.DEFAULT_RESTextEdit.Properties.ValueUnchecked = "NO"
        Me.DEFAULT_RESTextEdit.Size = New System.Drawing.Size(26, 19)
        Me.DEFAULT_RESTextEdit.TabIndex = 3
        '
        'DEFAULT_WORKTextEdit
        '
        Me.DEFAULT_WORKTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "DEFAULT_WORK", True))
        Me.DEFAULT_WORKTextEdit.EditValue = Nothing
        Me.DEFAULT_WORKTextEdit.Location = New System.Drawing.Point(201, 25)
        Me.DEFAULT_WORKTextEdit.Name = "DEFAULT_WORKTextEdit"
        Me.DEFAULT_WORKTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.DEFAULT_WORKTextEdit.Properties.Caption = ""
        Me.DEFAULT_WORKTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.DEFAULT_WORKTextEdit.Properties.ValueChecked = "YES"
        Me.DEFAULT_WORKTextEdit.Properties.ValueUnchecked = "NO"
        Me.DEFAULT_WORKTextEdit.Size = New System.Drawing.Size(24, 19)
        Me.DEFAULT_WORKTextEdit.TabIndex = 5
        '
        'ALTW4TextEdit
        '
        Me.ALTW4TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "ALTW4", True))
        Me.ALTW4TextEdit.EditValue = Nothing
        Me.ALTW4TextEdit.Location = New System.Drawing.Point(361, 54)
        Me.ALTW4TextEdit.Name = "ALTW4TextEdit"
        Me.ALTW4TextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.ALTW4TextEdit.Properties.Caption = ""
        Me.ALTW4TextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.ALTW4TextEdit.Properties.ValueChecked = "YES"
        Me.ALTW4TextEdit.Properties.ValueUnchecked = "NO"
        Me.ALTW4TextEdit.Size = New System.Drawing.Size(23, 19)
        Me.ALTW4TextEdit.TabIndex = 7
        '
        'ST_WH_EXE_FGETextEdit
        '
        Me.ST_WH_EXE_FGETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "ST_WH_EXE_FGE", True))
        Me.ST_WH_EXE_FGETextEdit.EditValue = Nothing
        Me.ST_WH_EXE_FGETextEdit.Location = New System.Drawing.Point(223, 53)
        Me.ST_WH_EXE_FGETextEdit.Name = "ST_WH_EXE_FGETextEdit"
        Me.ST_WH_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.ST_WH_EXE_FGETextEdit.Properties.Caption = ""
        Me.ST_WH_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.ST_WH_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.ST_WH_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.ST_WH_EXE_FGETextEdit.Size = New System.Drawing.Size(23, 19)
        Me.ST_WH_EXE_FGETextEdit.TabIndex = 9
        '
        'ST_STATUSTextEdit
        '
        Me.ST_STATUSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "ST_STATUS", True))
        Me.ST_STATUSTextEdit.Location = New System.Drawing.Point(289, 84)
        Me.ST_STATUSTextEdit.Name = "ST_STATUSTextEdit"
        Me.ST_STATUSTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ST_STATUSTextEdit.Properties.Items.AddRange(New Object() {"Single", "Married"})
        Me.ST_STATUSTextEdit.Properties.ReadOnly = True
        Me.ST_STATUSTextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_STATUSTextEdit.Size = New System.Drawing.Size(82, 20)
        Me.ST_STATUSTextEdit.TabIndex = 11
        '
        'SDIEXETextEdit
        '
        Me.SDIEXETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.STATE_EE_INFOBindingSource, "SDIEXE", True))
        Me.SDIEXETextEdit.EditValue = Nothing
        Me.SDIEXETextEdit.Location = New System.Drawing.Point(432, 146)
        Me.SDIEXETextEdit.Name = "SDIEXETextEdit"
        Me.SDIEXETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.SDIEXETextEdit.Properties.Caption = ""
        Me.SDIEXETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.SDIEXETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.SDIEXETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.SDIEXETextEdit.Size = New System.Drawing.Size(26, 19)
        Me.SDIEXETextEdit.TabIndex = 15
        '
        'SUTA_EXE_FGTextEdit
        '
        Me.SUTA_EXE_FGTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "SUTA_EXE_FG", True))
        Me.SUTA_EXE_FGTextEdit.EditValue = Nothing
        Me.SUTA_EXE_FGTextEdit.Location = New System.Drawing.Point(152, 146)
        Me.SUTA_EXE_FGTextEdit.Name = "SUTA_EXE_FGTextEdit"
        Me.SUTA_EXE_FGTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.SUTA_EXE_FGTextEdit.Properties.Caption = ""
        Me.SUTA_EXE_FGTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.SUTA_EXE_FGTextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.SUTA_EXE_FGTextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.SUTA_EXE_FGTextEdit.Size = New System.Drawing.Size(25, 19)
        Me.SUTA_EXE_FGTextEdit.TabIndex = 21
        '
        'GroupControl4
        '
        Me.GroupControl4.Controls.Add(Me.grpFedOptions)
        Me.GroupControl4.Controls.Add(Me.grpTY2020Options)
        Me.GroupControl4.Controls.Add(Label23)
        Me.GroupControl4.Controls.Add(Me.rgrp_w4Style)
        Me.GroupControl4.Controls.Add(FUTA_EXE_FGELabel)
        Me.GroupControl4.Controls.Add(OASDI_EXE_FGELabel)
        Me.GroupControl4.Controls.Add(FED_WH_EXE_FGELabel)
        Me.GroupControl4.Controls.Add(Me.FED_WH_EXE_FGETextEdit)
        Me.GroupControl4.Controls.Add(Me.OASDI_EXE_FGETextEdit)
        Me.GroupControl4.Controls.Add(Me.FUTA_EXE_FGETextEdit)
        Me.GroupControl4.Location = New System.Drawing.Point(15, 5)
        Me.GroupControl4.Name = "GroupControl4"
        Me.GroupControl4.Size = New System.Drawing.Size(1048, 176)
        Me.GroupControl4.TabIndex = 3
        Me.GroupControl4.Text = "Federal Taxes"
        '
        'grpFedOptions
        '
        Me.grpFedOptions.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.grpFedOptions.Controls.Add(Me.FED_WH_FIXEDTextEdit)
        Me.grpFedOptions.Controls.Add(Me.FED_STATUSTextEdit)
        Me.grpFedOptions.Controls.Add(FED_STATUSLabel)
        Me.grpFedOptions.Controls.Add(Me.FED_DEPSTextEdit)
        Me.grpFedOptions.Controls.Add(FED_DEPSLabel)
        Me.grpFedOptions.Controls.Add(FED_WH_FIXEDLabel)
        Me.grpFedOptions.Controls.Add(Me.FED_WH_EXTRATextEdit)
        Me.grpFedOptions.Controls.Add(FED_WH_EXTRALabel)
        Me.grpFedOptions.Location = New System.Drawing.Point(5, 79)
        Me.grpFedOptions.Name = "grpFedOptions"
        Me.grpFedOptions.ShowCaption = False
        Me.grpFedOptions.Size = New System.Drawing.Size(215, 93)
        Me.grpFedOptions.TabIndex = 19
        Me.grpFedOptions.Text = "GroupControl12"
        '
        'FED_WH_FIXEDTextEdit
        '
        Me.FED_WH_FIXEDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FED_WH_FIXED", True))
        Me.FED_WH_FIXEDTextEdit.Location = New System.Drawing.Point(103, 70)
        Me.FED_WH_FIXEDTextEdit.Name = "FED_WH_FIXEDTextEdit"
        Me.FED_WH_FIXEDTextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_WH_FIXEDTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.FED_WH_FIXEDTextEdit.TabIndex = 7
        '
        'FED_STATUSTextEdit
        '
        Me.FED_STATUSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FED_STATUS", True))
        Me.FED_STATUSTextEdit.Location = New System.Drawing.Point(103, 1)
        Me.FED_STATUSTextEdit.Name = "FED_STATUSTextEdit"
        Me.FED_STATUSTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.FED_STATUSTextEdit.Properties.Items.AddRange(New Object() {"Single", "Married", "Head of Household"})
        Me.FED_STATUSTextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_STATUSTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.FED_STATUSTextEdit.TabIndex = 1
        '
        'FED_DEPSTextEdit
        '
        Me.FED_DEPSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FED_DEPS", True))
        Me.FED_DEPSTextEdit.Location = New System.Drawing.Point(103, 24)
        Me.FED_DEPSTextEdit.Name = "FED_DEPSTextEdit"
        Me.FED_DEPSTextEdit.Properties.Mask.EditMask = "\d{0,2}"
        Me.FED_DEPSTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.FED_DEPSTextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_DEPSTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.FED_DEPSTextEdit.TabIndex = 3
        '
        'FED_WH_EXTRATextEdit
        '
        Me.FED_WH_EXTRATextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FED_WH_EXTRA", True))
        Me.FED_WH_EXTRATextEdit.Location = New System.Drawing.Point(103, 47)
        Me.FED_WH_EXTRATextEdit.Name = "FED_WH_EXTRATextEdit"
        Me.FED_WH_EXTRATextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_WH_EXTRATextEdit.Size = New System.Drawing.Size(107, 20)
        Me.FED_WH_EXTRATextEdit.TabIndex = 5
        '
        'grpTY2020Options
        '
        Me.grpTY2020Options.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.grpTY2020Options.Controls.Add(Me.txtW4_deductions)
        Me.grpTY2020Options.Controls.Add(Label24)
        Me.grpTY2020Options.Controls.Add(Me.chkW4_multijobs)
        Me.grpTY2020Options.Controls.Add(Me.txtW4_dependents)
        Me.grpTY2020Options.Controls.Add(Label25)
        Me.grpTY2020Options.Controls.Add(Label26)
        Me.grpTY2020Options.Controls.Add(Me.txtW4_otherinc)
        Me.grpTY2020Options.Location = New System.Drawing.Point(231, 79)
        Me.grpTY2020Options.Name = "grpTY2020Options"
        Me.grpTY2020Options.ShowCaption = False
        Me.grpTY2020Options.Size = New System.Drawing.Size(249, 93)
        Me.grpTY2020Options.TabIndex = 18
        Me.grpTY2020Options.Text = "TY2020 Options"
        '
        'txtW4_deductions
        '
        Me.txtW4_deductions.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "w4_deductions", True))
        Me.txtW4_deductions.Location = New System.Drawing.Point(134, 71)
        Me.txtW4_deductions.Name = "txtW4_deductions"
        Me.txtW4_deductions.Properties.Mask.EditMask = "c"
        Me.txtW4_deductions.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.txtW4_deductions.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.txtW4_deductions.Properties.ValidateOnEnterKey = True
        Me.txtW4_deductions.Size = New System.Drawing.Size(107, 20)
        Me.txtW4_deductions.TabIndex = 17
        '
        'chkW4_multijobs
        '
        Me.chkW4_multijobs.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "w4_multijobs", True))
        Me.chkW4_multijobs.EditValue = Nothing
        Me.chkW4_multijobs.Location = New System.Drawing.Point(134, 2)
        Me.chkW4_multijobs.Name = "chkW4_multijobs"
        Me.chkW4_multijobs.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.chkW4_multijobs.Properties.Caption = "Step 2 Option (c)"
        Me.chkW4_multijobs.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.chkW4_multijobs.Properties.ValueChecked = "YES"
        Me.chkW4_multijobs.Properties.ValueUnchecked = "NO"
        Me.chkW4_multijobs.Size = New System.Drawing.Size(107, 19)
        Me.chkW4_multijobs.TabIndex = 11
        '
        'txtW4_dependents
        '
        Me.txtW4_dependents.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "w4_dependents", True))
        Me.txtW4_dependents.Location = New System.Drawing.Point(134, 25)
        Me.txtW4_dependents.Name = "txtW4_dependents"
        Me.txtW4_dependents.Properties.Mask.EditMask = "c"
        Me.txtW4_dependents.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.txtW4_dependents.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.txtW4_dependents.Properties.ValidateOnEnterKey = True
        Me.txtW4_dependents.Size = New System.Drawing.Size(107, 20)
        Me.txtW4_dependents.TabIndex = 13
        '
        'txtW4_otherinc
        '
        Me.txtW4_otherinc.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "w4_otherinc", True))
        Me.txtW4_otherinc.Location = New System.Drawing.Point(134, 48)
        Me.txtW4_otherinc.Name = "txtW4_otherinc"
        Me.txtW4_otherinc.Properties.Mask.EditMask = "c"
        Me.txtW4_otherinc.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.txtW4_otherinc.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.txtW4_otherinc.Properties.ValidateOnEnterKey = True
        Me.txtW4_otherinc.Size = New System.Drawing.Size(107, 20)
        Me.txtW4_otherinc.TabIndex = 15
        '
        'rgrp_w4Style
        '
        Me.rgrp_w4Style.Location = New System.Drawing.Point(112, 30)
        Me.rgrp_w4Style.Name = "rgrp_w4Style"
        Me.rgrp_w4Style.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.rgrp_w4Style.Properties.Appearance.Options.UseBackColor = True
        Me.rgrp_w4Style.Properties.Columns = 2
        Me.rgrp_w4Style.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.[Default]
        Me.rgrp_w4Style.Properties.Items.AddRange(New DevExpress.XtraEditors.Controls.RadioGroupItem() {New DevExpress.XtraEditors.Controls.RadioGroupItem(0, "TY2019 && Back"), New DevExpress.XtraEditors.Controls.RadioGroupItem(1, "TY2020 && Forward")})
        Me.rgrp_w4Style.Properties.NullText = "0"
        Me.rgrp_w4Style.Size = New System.Drawing.Size(368, 24)
        Me.rgrp_w4Style.TabIndex = 8
        '
        'FED_WH_EXE_FGETextEdit
        '
        Me.FED_WH_EXE_FGETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FED_WH_EXE_FGE", True))
        Me.FED_WH_EXE_FGETextEdit.EditValue = Nothing
        Me.FED_WH_EXE_FGETextEdit.Location = New System.Drawing.Point(111, 56)
        Me.FED_WH_EXE_FGETextEdit.Name = "FED_WH_EXE_FGETextEdit"
        Me.FED_WH_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.FED_WH_EXE_FGETextEdit.Properties.Caption = ""
        Me.FED_WH_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.FED_WH_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.FED_WH_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.FED_WH_EXE_FGETextEdit.Size = New System.Drawing.Size(22, 19)
        Me.FED_WH_EXE_FGETextEdit.TabIndex = 1
        '
        'OASDI_EXE_FGETextEdit
        '
        Me.OASDI_EXE_FGETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "OASDI_EXE_FGE", True))
        Me.OASDI_EXE_FGETextEdit.EditValue = Nothing
        Me.OASDI_EXE_FGETextEdit.Location = New System.Drawing.Point(501, 85)
        Me.OASDI_EXE_FGETextEdit.Name = "OASDI_EXE_FGETextEdit"
        Me.OASDI_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.OASDI_EXE_FGETextEdit.Properties.Caption = ""
        Me.OASDI_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.OASDI_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.OASDI_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.OASDI_EXE_FGETextEdit.Size = New System.Drawing.Size(23, 19)
        Me.OASDI_EXE_FGETextEdit.TabIndex = 3
        '
        'FUTA_EXE_FGETextEdit
        '
        Me.FUTA_EXE_FGETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FUTA_EXE_FGE", True))
        Me.FUTA_EXE_FGETextEdit.EditValue = Nothing
        Me.FUTA_EXE_FGETextEdit.Location = New System.Drawing.Point(501, 110)
        Me.FUTA_EXE_FGETextEdit.Name = "FUTA_EXE_FGETextEdit"
        Me.FUTA_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.FUTA_EXE_FGETextEdit.Properties.Caption = ""
        Me.FUTA_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.FUTA_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.FUTA_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.FUTA_EXE_FGETextEdit.Size = New System.Drawing.Size(23, 19)
        Me.FUTA_EXE_FGETextEdit.TabIndex = 5
        '
        'PanelControl4
        '
        Me.PanelControl4.Controls.Add(Me.GroupControl7)
        Me.PanelControl4.Controls.Add(Me.btnDelete)
        Me.PanelControl4.Controls.Add(Me.btnAdd)
        Me.PanelControl4.Controls.Add(Me.pnlOverrideGroups)
        Me.PanelControl4.Controls.Add(Me.GridControl1)
        Me.PanelControl4.Location = New System.Drawing.Point(12, 70)
        Me.PanelControl4.Name = "PanelControl4"
        Me.PanelControl4.Size = New System.Drawing.Size(1066, 538)
        Me.PanelControl4.TabIndex = 22
        '
        'GroupControl7
        '
        Me.GroupControl7.Controls.Add(Me.GridControlCoOptionsSecondCheckPay)
        Me.GroupControl7.Location = New System.Drawing.Point(17, 316)
        Me.GroupControl7.Name = "GroupControl7"
        Me.GroupControl7.Size = New System.Drawing.Size(191, 180)
        Me.GroupControl7.TabIndex = 25
        Me.GroupControl7.Text = "Following pays on second check:"
        '
        'GridControlCoOptionsSecondCheckPay
        '
        Me.GridControlCoOptionsSecondCheckPay.DataSource = Me.CoOptionsSecondCheckPayCodeBindingSource
        Me.GridControlCoOptionsSecondCheckPay.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControlCoOptionsSecondCheckPay.Location = New System.Drawing.Point(2, 22)
        Me.GridControlCoOptionsSecondCheckPay.MainView = Me.GridViewSecondCheckPay
        Me.GridControlCoOptionsSecondCheckPay.Name = "GridControlCoOptionsSecondCheckPay"
        Me.GridControlCoOptionsSecondCheckPay.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riPayCodes})
        Me.GridControlCoOptionsSecondCheckPay.Size = New System.Drawing.Size(187, 156)
        Me.GridControlCoOptionsSecondCheckPay.TabIndex = 18
        Me.GridControlCoOptionsSecondCheckPay.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewSecondCheckPay})
        '
        'CoOptionsSecondCheckPayCodeBindingSource
        '
        Me.CoOptionsSecondCheckPayCodeBindingSource.DataSource = GetType(Brands_FrontDesk.CoOptions_SecondCheckPayCode)
        '
        'GridViewSecondCheckPay
        '
        Me.GridViewSecondCheckPay.Appearance.ViewCaption.Font = New System.Drawing.Font("Tahoma", 8.24!)
        Me.GridViewSecondCheckPay.Appearance.ViewCaption.ForeColor = System.Drawing.Color.Black
        Me.GridViewSecondCheckPay.Appearance.ViewCaption.Options.UseFont = True
        Me.GridViewSecondCheckPay.Appearance.ViewCaption.Options.UseForeColor = True
        Me.GridViewSecondCheckPay.Appearance.ViewCaption.Options.UseTextOptions = True
        Me.GridViewSecondCheckPay.Appearance.ViewCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridViewSecondCheckPay.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colPayCode, Me.colSeparateCheck})
        Me.GridViewSecondCheckPay.GridControl = Me.GridControlCoOptionsSecondCheckPay
        Me.GridViewSecondCheckPay.LevelIndent = 0
        Me.GridViewSecondCheckPay.Name = "GridViewSecondCheckPay"
        Me.GridViewSecondCheckPay.OptionsCustomization.AllowGroup = False
        Me.GridViewSecondCheckPay.OptionsMenu.EnableColumnMenu = False
        Me.GridViewSecondCheckPay.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
        Me.GridViewSecondCheckPay.OptionsView.ShowGroupPanel = False
        Me.GridViewSecondCheckPay.PreviewIndent = 0
        Me.GridViewSecondCheckPay.ViewCaption = "Following pays on second check:"
        '
        'colPayCode
        '
        Me.colPayCode.ColumnEdit = Me.riPayCodes
        Me.colPayCode.FieldName = "PayCode"
        Me.colPayCode.Name = "colPayCode"
        Me.colPayCode.Visible = True
        Me.colPayCode.VisibleIndex = 0
        Me.colPayCode.Width = 150
        '
        'riPayCodes
        '
        Me.riPayCodes.AutoHeight = False
        Me.riPayCodes.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riPayCodes.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_NUM", "Pay Code", 75, DevExpress.Utils.FormatType.Numeric, "", True, DevExpress.Utils.HorzAlignment.Center, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_DESC", "Description", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.riPayCodes.DataSource = Me.OTHERPAYBindingSource
        Me.riPayCodes.DisplayMember = "OTH_PAY_DESC"
        Me.riPayCodes.Name = "riPayCodes"
        Me.riPayCodes.NullText = ""
        Me.riPayCodes.PopupFormMinSize = New System.Drawing.Size(225, 0)
        Me.riPayCodes.ValueMember = "OTH_PAY_NUM"
        '
        'colSeparateCheck
        '
        Me.colSeparateCheck.Caption = "SepChk"
        Me.colSeparateCheck.FieldName = "SeparateCheck"
        Me.colSeparateCheck.Name = "colSeparateCheck"
        Me.colSeparateCheck.Visible = True
        Me.colSeparateCheck.VisibleIndex = 1
        '
        'btnDelete
        '
        Me.btnDelete.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnDelete.Location = New System.Drawing.Point(85, 17)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Size = New System.Drawing.Size(62, 23)
        Me.btnDelete.TabIndex = 24
        Me.btnDelete.Text = "Delete"
        '
        'btnAdd
        '
        Me.btnAdd.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnAdd.Location = New System.Drawing.Point(17, 17)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Size = New System.Drawing.Size(62, 23)
        Me.btnAdd.TabIndex = 23
        Me.btnAdd.Text = "Add"
        '
        'pnlOverrideGroups
        '
        Me.pnlOverrideGroups.AutoScroll = True
        Me.pnlOverrideGroups.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.pnlOverrideGroups.Controls.Add(Me.pnlGeneralOptions)
        Me.pnlOverrideGroups.Controls.Add(Me.grpSecondCheckTaxes)
        Me.pnlOverrideGroups.Controls.Add(Me.grpScheduling)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlNetOverride)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlOTHours)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlSecondCheck)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlRecordType)
        Me.pnlOverrideGroups.Location = New System.Drawing.Point(214, 17)
        Me.pnlOverrideGroups.Name = "pnlOverrideGroups"
        Me.pnlOverrideGroups.Size = New System.Drawing.Size(550, 479)
        Me.pnlOverrideGroups.TabIndex = 22
        '
        'pnlGeneralOptions
        '
        Me.pnlGeneralOptions.AutoScroll = True
        Me.pnlGeneralOptions.Controls.Add(ExcludeFromUtilityImportLabel)
        Me.pnlGeneralOptions.Controls.Add(Me.ExcludeFromUtilityImportCheckEdit)
        Me.pnlGeneralOptions.Controls.Add(Label14)
        Me.pnlGeneralOptions.Controls.Add(Me.GroupControl8)
        Me.pnlGeneralOptions.Controls.Add(Me.ddlPayUnderEmpNum)
        Me.pnlGeneralOptions.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlGeneralOptions.Location = New System.Drawing.Point(0, 475)
        Me.pnlGeneralOptions.Name = "pnlGeneralOptions"
        Me.pnlGeneralOptions.Size = New System.Drawing.Size(531, 112)
        Me.pnlGeneralOptions.TabIndex = 27
        '
        'ExcludeFromUtilityImportCheckEdit
        '
        Me.ExcludeFromUtilityImportCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "ExcludeFromUtilityImport", True))
        Me.ExcludeFromUtilityImportCheckEdit.Location = New System.Drawing.Point(156, 86)
        Me.ExcludeFromUtilityImportCheckEdit.Name = "ExcludeFromUtilityImportCheckEdit"
        Me.ExcludeFromUtilityImportCheckEdit.Properties.Caption = ""
        Me.ExcludeFromUtilityImportCheckEdit.Size = New System.Drawing.Size(27, 19)
        Me.ExcludeFromUtilityImportCheckEdit.TabIndex = 24
        '
        'Pr_batch_overrideBindingSource
        '
        Me.Pr_batch_overrideBindingSource.DataSource = GetType(Brands_FrontDesk.pr_batch_overrides_setup)
        '
        'GroupControl8
        '
        Me.GroupControl8.Appearance.BackColor = System.Drawing.Color.White
        Me.GroupControl8.Appearance.Options.UseBackColor = True
        Me.GroupControl8.AppearanceCaption.BackColor = System.Drawing.Color.White
        Me.GroupControl8.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GroupControl8.AppearanceCaption.Options.UseBackColor = True
        Me.GroupControl8.AppearanceCaption.Options.UseFont = True
        Me.GroupControl8.Controls.Add(Me.OTSeperateCheckHoursMoreThanTextEdit)
        Me.GroupControl8.Controls.Add(OTSeperateCheckHoursMoreThanLabel)
        Me.GroupControl8.Controls.Add(OTSeperateCheckLabel)
        Me.GroupControl8.Controls.Add(Me.OTSeperateCheckCheckEdit)
        Me.GroupControl8.Dock = System.Windows.Forms.DockStyle.Top
        Me.GroupControl8.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl8.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.GroupControl8.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl8.Name = "GroupControl8"
        Me.GroupControl8.Size = New System.Drawing.Size(531, 53)
        Me.GroupControl8.TabIndex = 21
        Me.GroupControl8.Text = "Overtime Options"
        '
        'OTSeperateCheckHoursMoreThanTextEdit
        '
        Me.OTSeperateCheckHoursMoreThanTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "OTSeperateCheckHoursMoreThan", True))
        Me.OTSeperateCheckHoursMoreThanTextEdit.Enabled = False
        Me.OTSeperateCheckHoursMoreThanTextEdit.Location = New System.Drawing.Point(307, 25)
        Me.OTSeperateCheckHoursMoreThanTextEdit.Name = "OTSeperateCheckHoursMoreThanTextEdit"
        Me.OTSeperateCheckHoursMoreThanTextEdit.Properties.Mask.EditMask = "n2"
        Me.OTSeperateCheckHoursMoreThanTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.OTSeperateCheckHoursMoreThanTextEdit.Size = New System.Drawing.Size(44, 22)
        Me.OTSeperateCheckHoursMoreThanTextEdit.TabIndex = 15
        '
        'OTSeperateCheckCheckEdit
        '
        Me.OTSeperateCheckCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "OTSeperateCheck", True))
        Me.OTSeperateCheckCheckEdit.Location = New System.Drawing.Point(137, 25)
        Me.OTSeperateCheckCheckEdit.Name = "OTSeperateCheckCheckEdit"
        Me.OTSeperateCheckCheckEdit.Properties.Caption = ""
        Me.OTSeperateCheckCheckEdit.Size = New System.Drawing.Size(24, 17)
        Me.OTSeperateCheckCheckEdit.TabIndex = 14
        '
        'ddlPayUnderEmpNum
        '
        Me.ddlPayUnderEmpNum.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "PayUnderEmpNum", True))
        Me.ddlPayUnderEmpNum.Location = New System.Drawing.Point(136, 59)
        Me.ddlPayUnderEmpNum.Name = "ddlPayUnderEmpNum"
        Me.ddlPayUnderEmpNum.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ddlPayUnderEmpNum.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("EMPNUM", "Emp #", 50, DevExpress.Utils.FormatType.Numeric, "", True, DevExpress.Utils.HorzAlignment.Far, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("LastName", "Last", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("FirstName", "First", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("MiddleName", "MI", 20, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.ddlPayUnderEmpNum.Properties.DataSource = Me.EMPLOYEEBindingSource1
        Me.ddlPayUnderEmpNum.Properties.DisplayMember = "EMPNUM"
        Me.ddlPayUnderEmpNum.Properties.NullText = ""
        Me.ddlPayUnderEmpNum.Properties.ValueMember = "EMPNUM"
        Me.ddlPayUnderEmpNum.Size = New System.Drawing.Size(215, 20)
        Me.ddlPayUnderEmpNum.TabIndex = 22
        '
        'EMPLOYEEBindingSource1
        '
        Me.EMPLOYEEBindingSource1.DataSource = GetType(Brands_FrontDesk.EMPLOYEE)
        '
        'grpSecondCheckTaxes
        '
        Me.grpSecondCheckTaxes.Appearance.BackColor = System.Drawing.Color.White
        Me.grpSecondCheckTaxes.Appearance.Options.UseBackColor = True
        Me.grpSecondCheckTaxes.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.grpSecondCheckTaxes.AppearanceCaption.Options.UseFont = True
        Me.grpSecondCheckTaxes.Controls.Add(Label21)
        Me.grpSecondCheckTaxes.Controls.Add(Me.UIStateComboBoxEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Label20)
        Me.grpSecondCheckTaxes.Controls.Add(Me.ResStateComboBoxEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Label19)
        Me.grpSecondCheckTaxes.Controls.Add(Me.WrkStateComboBoxEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Label18)
        Me.grpSecondCheckTaxes.Controls.Add(Me.FLIOverrdieAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Label1)
        Me.grpSecondCheckTaxes.Controls.Add(CheckTypeLabel)
        Me.grpSecondCheckTaxes.Controls.Add(MedicareOverrideAmountLabel)
        Me.grpSecondCheckTaxes.Controls.Add(Me.MedicareOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(OASDIOverrideAmountLabel)
        Me.grpSecondCheckTaxes.Controls.Add(Me.OASDIOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.FedOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.CheckTypeTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(FedOverrideAmountLabel)
        Me.grpSecondCheckTaxes.Controls.Add(TaxFrequencyLabel)
        Me.grpSecondCheckTaxes.Controls.Add(STOverrideAmountLabel)
        Me.grpSecondCheckTaxes.Controls.Add(Me.TaxFrequencyTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(DBOverrideAmountLabel)
        Me.grpSecondCheckTaxes.Controls.Add(Me.DBOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.STOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(LOCOverrideAmountLabel)
        Me.grpSecondCheckTaxes.Controls.Add(Me.LOCOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.tbManualCheckNumber)
        Me.grpSecondCheckTaxes.Dock = System.Windows.Forms.DockStyle.Top
        Me.grpSecondCheckTaxes.Location = New System.Drawing.Point(0, 334)
        Me.grpSecondCheckTaxes.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.grpSecondCheckTaxes.LookAndFeel.UseDefaultLookAndFeel = False
        Me.grpSecondCheckTaxes.Name = "grpSecondCheckTaxes"
        Me.grpSecondCheckTaxes.Size = New System.Drawing.Size(531, 141)
        Me.grpSecondCheckTaxes.TabIndex = 16
        Me.grpSecondCheckTaxes.Text = "Tax Overrides (Optional)"
        '
        'UIStateComboBoxEdit
        '
        Me.UIStateComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "UIState", True))
        Me.UIStateComboBoxEdit.Location = New System.Drawing.Point(433, 117)
        Me.UIStateComboBoxEdit.Name = "UIStateComboBoxEdit"
        Me.UIStateComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.UIStateComboBoxEdit.Properties.PopupSizeable = True
        Me.UIStateComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.UIStateComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.UIStateComboBoxEdit.Size = New System.Drawing.Size(68, 22)
        Me.UIStateComboBoxEdit.TabIndex = 42
        '
        'ResStateComboBoxEdit
        '
        Me.ResStateComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "ResState", True))
        Me.ResStateComboBoxEdit.Location = New System.Drawing.Point(433, 95)
        Me.ResStateComboBoxEdit.Name = "ResStateComboBoxEdit"
        Me.ResStateComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ResStateComboBoxEdit.Properties.PopupSizeable = True
        Me.ResStateComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ResStateComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.ResStateComboBoxEdit.Size = New System.Drawing.Size(68, 22)
        Me.ResStateComboBoxEdit.TabIndex = 40
        '
        'WrkStateComboBoxEdit
        '
        Me.WrkStateComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "WrkState", True))
        Me.WrkStateComboBoxEdit.Location = New System.Drawing.Point(433, 73)
        Me.WrkStateComboBoxEdit.Name = "WrkStateComboBoxEdit"
        Me.WrkStateComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.WrkStateComboBoxEdit.Properties.PopupSizeable = True
        Me.WrkStateComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.WrkStateComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.WrkStateComboBoxEdit.Size = New System.Drawing.Size(68, 22)
        Me.WrkStateComboBoxEdit.TabIndex = 38
        '
        'FLIOverrdieAmountTextEdit
        '
        Me.FLIOverrdieAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "FLIOverrdieAmount", True))
        Me.FLIOverrdieAmountTextEdit.Location = New System.Drawing.Point(241, 117)
        Me.FLIOverrdieAmountTextEdit.Name = "FLIOverrdieAmountTextEdit"
        Me.FLIOverrdieAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.FLIOverrdieAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.FLIOverrdieAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.FLIOverrdieAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.FLIOverrdieAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.FLIOverrdieAmountTextEdit.TabIndex = 35
        '
        'MedicareOverrideAmountTextEdit
        '
        Me.MedicareOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "MedicareOverrideAmount", True))
        Me.MedicareOverrideAmountTextEdit.Location = New System.Drawing.Point(82, 95)
        Me.MedicareOverrideAmountTextEdit.Name = "MedicareOverrideAmountTextEdit"
        Me.MedicareOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.MedicareOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.MedicareOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.MedicareOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.MedicareOverrideAmountTextEdit.TabIndex = 7
        '
        'OASDIOverrideAmountTextEdit
        '
        Me.OASDIOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "OASDIOverrideAmount", True))
        Me.OASDIOverrideAmountTextEdit.Location = New System.Drawing.Point(82, 73)
        Me.OASDIOverrideAmountTextEdit.Name = "OASDIOverrideAmountTextEdit"
        Me.OASDIOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.OASDIOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.OASDIOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.OASDIOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.OASDIOverrideAmountTextEdit.TabIndex = 5
        '
        'FedOverrideAmountTextEdit
        '
        Me.FedOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "FedOverrideAmount", True))
        Me.FedOverrideAmountTextEdit.Location = New System.Drawing.Point(82, 51)
        Me.FedOverrideAmountTextEdit.Name = "FedOverrideAmountTextEdit"
        Me.FedOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.FedOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.FedOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.FedOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.FedOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.FedOverrideAmountTextEdit.TabIndex = 3
        '
        'CheckTypeTextEdit
        '
        Me.CheckTypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "CheckType", True))
        Me.CheckTypeTextEdit.Location = New System.Drawing.Point(286, 21)
        Me.CheckTypeTextEdit.Name = "CheckTypeTextEdit"
        Me.CheckTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CheckTypeTextEdit.Properties.Items.AddRange(New Object() {"", "MANUAL", "NORMAL"})
        Me.CheckTypeTextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.CheckTypeTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.CheckTypeTextEdit.TabIndex = 1
        '
        'TaxFrequencyTextEdit
        '
        Me.TaxFrequencyTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "TaxFrequency", True))
        Me.TaxFrequencyTextEdit.Location = New System.Drawing.Point(82, 20)
        Me.TaxFrequencyTextEdit.Name = "TaxFrequencyTextEdit"
        Me.TaxFrequencyTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TaxFrequencyTextEdit.Properties.Items.AddRange(New Object() {"", "Weekly", "Bi-Weekly", "Semi-Monthly", "Monthly", "Quarterly", "Annually"})
        Me.TaxFrequencyTextEdit.Size = New System.Drawing.Size(125, 22)
        Me.TaxFrequencyTextEdit.TabIndex = 0
        '
        'DBOverrideAmountTextEdit
        '
        Me.DBOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "DBOverrideAmount", True))
        Me.DBOverrideAmountTextEdit.Location = New System.Drawing.Point(241, 95)
        Me.DBOverrideAmountTextEdit.Name = "DBOverrideAmountTextEdit"
        Me.DBOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.DBOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.DBOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.DBOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.DBOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.DBOverrideAmountTextEdit.TabIndex = 8
        '
        'STOverrideAmountTextEdit
        '
        Me.STOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "STOverrideAmount", True))
        Me.STOverrideAmountTextEdit.Location = New System.Drawing.Point(241, 51)
        Me.STOverrideAmountTextEdit.Name = "STOverrideAmountTextEdit"
        Me.STOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.STOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.STOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.STOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.STOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.STOverrideAmountTextEdit.TabIndex = 4
        '
        'LOCOverrideAmountTextEdit
        '
        Me.LOCOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "LOCOverrideAmount", True))
        Me.LOCOverrideAmountTextEdit.Location = New System.Drawing.Point(241, 73)
        Me.LOCOverrideAmountTextEdit.Name = "LOCOverrideAmountTextEdit"
        Me.LOCOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.LOCOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.LOCOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.LOCOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.LOCOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.LOCOverrideAmountTextEdit.TabIndex = 6
        '
        'tbManualCheckNumber
        '
        Me.tbManualCheckNumber.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "ManualCheckNumber", True))
        Me.tbManualCheckNumber.Location = New System.Drawing.Point(433, 21)
        Me.tbManualCheckNumber.Name = "tbManualCheckNumber"
        Me.tbManualCheckNumber.Size = New System.Drawing.Size(86, 22)
        Me.tbManualCheckNumber.TabIndex = 2
        '
        'grpScheduling
        '
        Me.grpScheduling.Appearance.BackColor = System.Drawing.Color.White
        Me.grpScheduling.Appearance.Options.UseBackColor = True
        Me.grpScheduling.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.grpScheduling.AppearanceCaption.Options.UseFont = True
        Me.grpScheduling.Controls.Add(Label22)
        Me.grpScheduling.Controls.Add(Me.FilterToPayFreqComboBoxEdit)
        Me.grpScheduling.Controls.Add(Me.FirstOfQtrCheckEdit)
        Me.grpScheduling.Controls.Add(Me.LastOfQtrCheckEdit)
        Me.grpScheduling.Controls.Add(Me.LabelControl2)
        Me.grpScheduling.Controls.Add(Me.LastOfMonthCheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd5CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd4CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd3CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd2CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd1CheckEdit)
        Me.grpScheduling.Controls.Add(Me.AllPrdsCheckEdit)
        Me.grpScheduling.Dock = System.Windows.Forms.DockStyle.Top
        Me.grpScheduling.Location = New System.Drawing.Point(0, 264)
        Me.grpScheduling.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.grpScheduling.LookAndFeel.UseDefaultLookAndFeel = False
        Me.grpScheduling.Name = "grpScheduling"
        Me.grpScheduling.Size = New System.Drawing.Size(531, 70)
        Me.grpScheduling.TabIndex = 25
        Me.grpScheduling.Text = "Scheduling"
        '
        'FilterToPayFreqComboBoxEdit
        '
        Me.FilterToPayFreqComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "FilterToPayFreq", True))
        Me.FilterToPayFreqComboBoxEdit.Location = New System.Drawing.Point(331, 43)
        Me.FilterToPayFreqComboBoxEdit.Margin = New System.Windows.Forms.Padding(2)
        Me.FilterToPayFreqComboBoxEdit.Name = "FilterToPayFreqComboBoxEdit"
        Me.FilterToPayFreqComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.FilterToPayFreqComboBoxEdit.Properties.Items.AddRange(New Object() {"Weekly", "Bi-Weekly", "Semi-Monthly", "Monthly", "Quarterly", "Annually"})
        Me.FilterToPayFreqComboBoxEdit.Size = New System.Drawing.Size(113, 22)
        Me.FilterToPayFreqComboBoxEdit.TabIndex = 17
        '
        'FirstOfQtrCheckEdit
        '
        Me.FirstOfQtrCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "FirstOfQtr", True))
        Me.FirstOfQtrCheckEdit.Location = New System.Drawing.Point(220, 45)
        Me.FirstOfQtrCheckEdit.Name = "FirstOfQtrCheckEdit"
        Me.FirstOfQtrCheckEdit.Properties.Caption = "First Of Qtr"
        Me.FirstOfQtrCheckEdit.Size = New System.Drawing.Size(99, 17)
        Me.FirstOfQtrCheckEdit.TabIndex = 16
        '
        'LastOfQtrCheckEdit
        '
        Me.LastOfQtrCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "LastOfQtr", True))
        Me.LastOfQtrCheckEdit.Location = New System.Drawing.Point(105, 45)
        Me.LastOfQtrCheckEdit.Name = "LastOfQtrCheckEdit"
        Me.LastOfQtrCheckEdit.Properties.Caption = "Last Of Qtr"
        Me.LastOfQtrCheckEdit.Size = New System.Drawing.Size(99, 17)
        Me.LastOfQtrCheckEdit.TabIndex = 15
        '
        'LabelControl2
        '
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.Blue
        Me.LabelControl2.Appearance.Options.UseForeColor = True
        Me.LabelControl2.Location = New System.Drawing.Point(87, 28)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(38, 13)
        Me.LabelControl2.TabIndex = 14
        Me.LabelControl2.Text = "OR Prd:"
        '
        'LastOfMonthCheckEdit
        '
        Me.LastOfMonthCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "LastOfMonth", True))
        Me.LastOfMonthCheckEdit.Location = New System.Drawing.Point(6, 45)
        Me.LastOfMonthCheckEdit.Name = "LastOfMonthCheckEdit"
        Me.LastOfMonthCheckEdit.Properties.Caption = "Last Of Month"
        Me.LastOfMonthCheckEdit.Size = New System.Drawing.Size(99, 17)
        Me.LastOfMonthCheckEdit.TabIndex = 13
        '
        'Prd5CheckEdit
        '
        Me.Prd5CheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "Prd5", True))
        Me.Prd5CheckEdit.Location = New System.Drawing.Point(260, 25)
        Me.Prd5CheckEdit.Name = "Prd5CheckEdit"
        Me.Prd5CheckEdit.Properties.Caption = "5"
        Me.Prd5CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd5CheckEdit.TabIndex = 11
        '
        'Prd4CheckEdit
        '
        Me.Prd4CheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "Prd4", True))
        Me.Prd4CheckEdit.Location = New System.Drawing.Point(226, 25)
        Me.Prd4CheckEdit.Name = "Prd4CheckEdit"
        Me.Prd4CheckEdit.Properties.Caption = "4"
        Me.Prd4CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd4CheckEdit.TabIndex = 9
        '
        'Prd3CheckEdit
        '
        Me.Prd3CheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "Prd3", True))
        Me.Prd3CheckEdit.Location = New System.Drawing.Point(192, 25)
        Me.Prd3CheckEdit.Name = "Prd3CheckEdit"
        Me.Prd3CheckEdit.Properties.Caption = "3"
        Me.Prd3CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd3CheckEdit.TabIndex = 7
        '
        'Prd2CheckEdit
        '
        Me.Prd2CheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "Prd2", True))
        Me.Prd2CheckEdit.Location = New System.Drawing.Point(158, 25)
        Me.Prd2CheckEdit.Name = "Prd2CheckEdit"
        Me.Prd2CheckEdit.Properties.Caption = "2"
        Me.Prd2CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd2CheckEdit.TabIndex = 5
        '
        'Prd1CheckEdit
        '
        Me.Prd1CheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "Prd1", True))
        Me.Prd1CheckEdit.Location = New System.Drawing.Point(124, 25)
        Me.Prd1CheckEdit.Name = "Prd1CheckEdit"
        Me.Prd1CheckEdit.Properties.Caption = "1"
        Me.Prd1CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd1CheckEdit.TabIndex = 3
        '
        'AllPrdsCheckEdit
        '
        Me.AllPrdsCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "AllPrds", True))
        Me.AllPrdsCheckEdit.Location = New System.Drawing.Point(6, 25)
        Me.AllPrdsCheckEdit.Name = "AllPrdsCheckEdit"
        Me.AllPrdsCheckEdit.Properties.Caption = "All Periods"
        Me.AllPrdsCheckEdit.Size = New System.Drawing.Size(75, 17)
        Me.AllPrdsCheckEdit.TabIndex = 1
        '
        'pnlNetOverride
        '
        Me.pnlNetOverride.Appearance.BackColor = System.Drawing.Color.White
        Me.pnlNetOverride.Appearance.Options.UseBackColor = True
        Me.pnlNetOverride.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.pnlNetOverride.AppearanceCaption.Options.UseFont = True
        Me.pnlNetOverride.Controls.Add(Me.NetOverrideDedNumTextEdit)
        Me.pnlNetOverride.Controls.Add(Label17)
        Me.pnlNetOverride.Controls.Add(NetOverrideAdjustTypeLabel)
        Me.pnlNetOverride.Controls.Add(NetOverrideAmountLabel)
        Me.pnlNetOverride.Controls.Add(Me.NetOverrideAmountTextEdit)
        Me.pnlNetOverride.Controls.Add(Me.NetOverrideAdjustTypeTextEdit)
        Me.pnlNetOverride.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlNetOverride.Location = New System.Drawing.Point(0, 217)
        Me.pnlNetOverride.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.pnlNetOverride.LookAndFeel.UseDefaultLookAndFeel = False
        Me.pnlNetOverride.Name = "pnlNetOverride"
        Me.pnlNetOverride.Size = New System.Drawing.Size(531, 47)
        Me.pnlNetOverride.TabIndex = 13
        Me.pnlNetOverride.Text = "Net Override"
        '
        'NetOverrideDedNumTextEdit
        '
        Me.NetOverrideDedNumTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "NetOverrideDedNum", True))
        Me.NetOverrideDedNumTextEdit.Location = New System.Drawing.Point(416, 19)
        Me.NetOverrideDedNumTextEdit.Name = "NetOverrideDedNumTextEdit"
        Me.NetOverrideDedNumTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.NetOverrideDedNumTextEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("DED_DESC", "DED_DESC", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.NetOverrideDedNumTextEdit.Properties.DataSource = Me.DEDUCTIONBindingSource
        Me.NetOverrideDedNumTextEdit.Properties.DisplayMember = "DED_DESC"
        Me.NetOverrideDedNumTextEdit.Properties.NullText = ""
        Me.NetOverrideDedNumTextEdit.Properties.PopupFormMinSize = New System.Drawing.Size(150, 0)
        Me.NetOverrideDedNumTextEdit.Properties.ShowHeader = False
        Me.NetOverrideDedNumTextEdit.Properties.ValueMember = "DED_NUM"
        Me.NetOverrideDedNumTextEdit.Size = New System.Drawing.Size(103, 22)
        Me.NetOverrideDedNumTextEdit.TabIndex = 8
        '
        'DEDUCTIONBindingSource
        '
        Me.DEDUCTIONBindingSource.DataSource = GetType(Brands_FrontDesk.DEDUCTION)
        '
        'NetOverrideAmountTextEdit
        '
        Me.NetOverrideAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "NetOverrideAmount", True))
        Me.NetOverrideAmountTextEdit.Location = New System.Drawing.Point(105, 20)
        Me.NetOverrideAmountTextEdit.Name = "NetOverrideAmountTextEdit"
        Me.NetOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.NetOverrideAmountTextEdit.Properties.Mask.EditMask = "c"
        Me.NetOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.NetOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.NetOverrideAmountTextEdit.Size = New System.Drawing.Size(84, 22)
        Me.NetOverrideAmountTextEdit.TabIndex = 1
        '
        'NetOverrideAdjustTypeTextEdit
        '
        Me.NetOverrideAdjustTypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "NetOverrideAdjustType", True))
        Me.NetOverrideAdjustTypeTextEdit.Location = New System.Drawing.Point(258, 20)
        Me.NetOverrideAdjustTypeTextEdit.Name = "NetOverrideAdjustTypeTextEdit"
        Me.NetOverrideAdjustTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.NetOverrideAdjustTypeTextEdit.Properties.Items.AddRange(New Object() {"Gross", "Fed", "Deduction", "Deduction After 'Credit To Net'"})
        Me.NetOverrideAdjustTypeTextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.NetOverrideAdjustTypeTextEdit.Size = New System.Drawing.Size(103, 22)
        Me.NetOverrideAdjustTypeTextEdit.TabIndex = 3
        '
        'pnlOTHours
        '
        Me.pnlOTHours.Controls.Add(OTHoursLabel)
        Me.pnlOTHours.Controls.Add(Me.OTHoursTextEdit)
        Me.pnlOTHours.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlOTHours.Location = New System.Drawing.Point(0, 189)
        Me.pnlOTHours.Name = "pnlOTHours"
        Me.pnlOTHours.Size = New System.Drawing.Size(531, 28)
        Me.pnlOTHours.TabIndex = 26
        '
        'OTHoursTextEdit
        '
        Me.OTHoursTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "OTHours", True))
        Me.OTHoursTextEdit.Location = New System.Drawing.Point(105, 3)
        Me.OTHoursTextEdit.Name = "OTHoursTextEdit"
        Me.OTHoursTextEdit.Properties.Mask.EditMask = "n3"
        Me.OTHoursTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.OTHoursTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.OTHoursTextEdit.TabIndex = 1
        '
        'pnlSecondCheck
        '
        Me.pnlSecondCheck.Controls.Add(Label15)
        Me.pnlSecondCheck.Controls.Add(Me.DeptNum2ndCheck)
        Me.pnlSecondCheck.Controls.Add(Label16)
        Me.pnlSecondCheck.Controls.Add(Me.DivNum2ndCheck)
        Me.pnlSecondCheck.Controls.Add(Me.PayDedCodeLookUpEdit)
        Me.pnlSecondCheck.Controls.Add(CheckCounterLabel)
        Me.pnlSecondCheck.Controls.Add(Me.CheckCounterTextEdit)
        Me.pnlSecondCheck.Controls.Add(PayDedTypeLabel)
        Me.pnlSecondCheck.Controls.Add(PayDedCodeLabel)
        Me.pnlSecondCheck.Controls.Add(PayDedAmountLabel)
        Me.pnlSecondCheck.Controls.Add(Me.PayDedAmountTextEdit)
        Me.pnlSecondCheck.Controls.Add(Me.PayDedTypeTextEdit)
        Me.pnlSecondCheck.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlSecondCheck.Location = New System.Drawing.Point(0, 27)
        Me.pnlSecondCheck.Name = "pnlSecondCheck"
        Me.pnlSecondCheck.Size = New System.Drawing.Size(531, 162)
        Me.pnlSecondCheck.TabIndex = 12
        '
        'DeptNum2ndCheck
        '
        Me.DeptNum2ndCheck.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "DeptNum", True))
        Me.DeptNum2ndCheck.Location = New System.Drawing.Point(105, 136)
        Me.DeptNum2ndCheck.Name = "DeptNum2ndCheck"
        Me.DeptNum2ndCheck.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DeptNum2ndCheck.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "Descr", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DeptNum2ndCheck.Properties.DataSource = Me.DEPARTMENTBindingSource2
        Me.DeptNum2ndCheck.Properties.DisplayMember = "Descr"
        Me.DeptNum2ndCheck.Properties.NullText = ""
        Me.DeptNum2ndCheck.Properties.ShowHeader = False
        Me.DeptNum2ndCheck.Properties.ValidateOnEnterKey = True
        Me.DeptNum2ndCheck.Properties.ValueMember = "DEPTNUM"
        Me.DeptNum2ndCheck.Size = New System.Drawing.Size(100, 20)
        Me.DeptNum2ndCheck.TabIndex = 15
        '
        'DEPARTMENTBindingSource2
        '
        Me.DEPARTMENTBindingSource2.DataSource = GetType(Brands_FrontDesk.DEPARTMENT)
        '
        'DivNum2ndCheck
        '
        Me.DivNum2ndCheck.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "DivNum", True))
        Me.DivNum2ndCheck.Location = New System.Drawing.Point(105, 110)
        Me.DivNum2ndCheck.Name = "DivNum2ndCheck"
        Me.DivNum2ndCheck.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DivNum2ndCheck.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DivNum2ndCheck.Properties.DataSource = Me.DIVISIONBindingSource
        Me.DivNum2ndCheck.Properties.DisplayMember = "Descr"
        Me.DivNum2ndCheck.Properties.NullText = ""
        Me.DivNum2ndCheck.Properties.ShowHeader = False
        Me.DivNum2ndCheck.Properties.ValidateOnEnterKey = True
        Me.DivNum2ndCheck.Properties.ValueMember = "DDIVNUM"
        Me.DivNum2ndCheck.Size = New System.Drawing.Size(100, 20)
        Me.DivNum2ndCheck.TabIndex = 13
        '
        'PayDedCodeLookUpEdit
        '
        Me.PayDedCodeLookUpEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "PayDedCode", True))
        Me.PayDedCodeLookUpEdit.Location = New System.Drawing.Point(105, 31)
        Me.PayDedCodeLookUpEdit.Name = "PayDedCodeLookUpEdit"
        Me.PayDedCodeLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.PayDedCodeLookUpEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_DESC", "OTH_PAY_DESC", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.PayDedCodeLookUpEdit.Properties.DataSource = Me.OTHERPAYBindingSource
        Me.PayDedCodeLookUpEdit.Properties.DisplayMember = "OTH_PAY_DESC"
        Me.PayDedCodeLookUpEdit.Properties.NullText = ""
        Me.PayDedCodeLookUpEdit.Properties.PopupFormMinSize = New System.Drawing.Size(150, 0)
        Me.PayDedCodeLookUpEdit.Properties.ShowHeader = False
        Me.PayDedCodeLookUpEdit.Properties.ValueMember = "OTH_PAY_NUM"
        Me.PayDedCodeLookUpEdit.Size = New System.Drawing.Size(200, 20)
        Me.PayDedCodeLookUpEdit.TabIndex = 6
        '
        'CheckCounterTextEdit
        '
        Me.CheckCounterTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "CheckCounter", True))
        Me.CheckCounterTextEdit.Location = New System.Drawing.Point(105, 83)
        Me.CheckCounterTextEdit.Name = "CheckCounterTextEdit"
        Me.CheckCounterTextEdit.Properties.Mask.EditMask = "f0"
        Me.CheckCounterTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CheckCounterTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.CheckCounterTextEdit.TabIndex = 11
        '
        'PayDedAmountTextEdit
        '
        Me.PayDedAmountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "PayDedAmount", True))
        Me.PayDedAmountTextEdit.Location = New System.Drawing.Point(105, 57)
        Me.PayDedAmountTextEdit.Name = "PayDedAmountTextEdit"
        Me.PayDedAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.PayDedAmountTextEdit.Properties.Mask.EditMask = "c"
        Me.PayDedAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.PayDedAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.PayDedAmountTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.PayDedAmountTextEdit.TabIndex = 8
        '
        'PayDedTypeTextEdit
        '
        Me.PayDedTypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "PayDedType", True))
        Me.PayDedTypeTextEdit.Location = New System.Drawing.Point(105, 5)
        Me.PayDedTypeTextEdit.Name = "PayDedTypeTextEdit"
        Me.PayDedTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.PayDedTypeTextEdit.Properties.Items.AddRange(New Object() {"P", "D"})
        Me.PayDedTypeTextEdit.Size = New System.Drawing.Size(48, 20)
        Me.PayDedTypeTextEdit.TabIndex = 4
        '
        'pnlRecordType
        '
        Me.pnlRecordType.Controls.Add(Me.RecordTypeTextEdit)
        Me.pnlRecordType.Controls.Add(RecordTypeLabel)
        Me.pnlRecordType.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlRecordType.Location = New System.Drawing.Point(0, 0)
        Me.pnlRecordType.Name = "pnlRecordType"
        Me.pnlRecordType.Size = New System.Drawing.Size(531, 27)
        Me.pnlRecordType.TabIndex = 1
        '
        'RecordTypeTextEdit
        '
        Me.RecordTypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.Pr_batch_overrideBindingSource, "RecordType", True))
        Me.RecordTypeTextEdit.Location = New System.Drawing.Point(105, 3)
        Me.RecordTypeTextEdit.Name = "RecordTypeTextEdit"
        Me.RecordTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RecordTypeTextEdit.Properties.Items.AddRange(New Object() {"Second Check", "Net Override", "Auto OT Hours", "Pay Only On", "General Options"})
        Me.RecordTypeTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.RecordTypeTextEdit.TabIndex = 2
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.Pr_batch_overrideBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(17, 46)
        Me.GridControl1.MainView = Me.gridViewCheckOverrides
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(191, 264)
        Me.GridControl1.TabIndex = 21
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gridViewCheckOverrides})
        '
        'gridViewCheckOverrides
        '
        Me.gridViewCheckOverrides.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colRecordType, Me.colCheckCounter})
        Me.gridViewCheckOverrides.GridControl = Me.GridControl1
        Me.gridViewCheckOverrides.LevelIndent = 0
        Me.gridViewCheckOverrides.Name = "gridViewCheckOverrides"
        Me.gridViewCheckOverrides.OptionsBehavior.Editable = False
        Me.gridViewCheckOverrides.OptionsCustomization.AllowColumnMoving = False
        Me.gridViewCheckOverrides.OptionsCustomization.AllowGroup = False
        Me.gridViewCheckOverrides.OptionsMenu.EnableColumnMenu = False
        Me.gridViewCheckOverrides.OptionsView.ShowGroupPanel = False
        Me.gridViewCheckOverrides.PreviewIndent = 0
        '
        'colRecordType
        '
        Me.colRecordType.FieldName = "RecordType"
        Me.colRecordType.Name = "colRecordType"
        Me.colRecordType.Visible = True
        Me.colRecordType.VisibleIndex = 0
        Me.colRecordType.Width = 100
        '
        'colCheckCounter
        '
        Me.colCheckCounter.Caption = "Chk Cnt"
        Me.colCheckCounter.FieldName = "CheckCounter"
        Me.colCheckCounter.Name = "colCheckCounter"
        Me.colCheckCounter.Visible = True
        Me.colCheckCounter.VisibleIndex = 1
        Me.colCheckCounter.Width = 50
        '
        'PanelControl5
        '
        Me.PanelControl5.Controls.Add(Me.gcEmpPriorDD)
        Me.PanelControl5.Controls.Add(Me.gcDdSetup)
        Me.PanelControl5.Location = New System.Drawing.Point(12, 70)
        Me.PanelControl5.Name = "PanelControl5"
        Me.PanelControl5.Size = New System.Drawing.Size(1066, 538)
        Me.PanelControl5.TabIndex = 23
        '
        'gcEmpPriorDD
        '
        Me.gcEmpPriorDD.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gcEmpPriorDD.AppearanceCaption.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.Critical
        Me.gcEmpPriorDD.AppearanceCaption.Options.UseFont = True
        Me.gcEmpPriorDD.AppearanceCaption.Options.UseForeColor = True
        Me.gcEmpPriorDD.AppearanceCaption.Options.UseTextOptions = True
        Me.gcEmpPriorDD.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.gcEmpPriorDD.Controls.Add(Me.sbDismiss)
        Me.gcEmpPriorDD.Controls.Add(Label27)
        Me.gcEmpPriorDD.Controls.Add(lblPriorDDAcctType4)
        Me.gcEmpPriorDD.Controls.Add(lblPriorDDAcctType2)
        Me.gcEmpPriorDD.Controls.Add(Me.LabelControl1)
        Me.gcEmpPriorDD.Controls.Add(Me.LabelControl19)
        Me.gcEmpPriorDD.Controls.Add(Me.LabelControl20)
        Me.gcEmpPriorDD.Controls.Add(Me.LabelControl21)
        Me.gcEmpPriorDD.Controls.Add(lblPriorDDAcctType3)
        Me.gcEmpPriorDD.Controls.Add(lblPriorDDAcctType1)
        Me.gcEmpPriorDD.Location = New System.Drawing.Point(472, 10)
        Me.gcEmpPriorDD.Name = "gcEmpPriorDD"
        Me.gcEmpPriorDD.Size = New System.Drawing.Size(435, 471)
        Me.gcEmpPriorDD.TabIndex = 3
        '
        'sbDismiss
        '
        Me.sbDismiss.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.sbDismiss.Appearance.ForeColor = System.Drawing.Color.Red
        Me.sbDismiss.Appearance.Options.UseFont = True
        Me.sbDismiss.Appearance.Options.UseForeColor = True
        Me.sbDismiss.Location = New System.Drawing.Point(165, 225)
        Me.sbDismiss.Name = "sbDismiss"
        Me.sbDismiss.Size = New System.Drawing.Size(88, 22)
        Me.sbDismiss.StyleController = Me.LayoutControl1
        Me.sbDismiss.TabIndex = 63
        Me.sbDismiss.Text = "Dismiss"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Location = New System.Drawing.Point(254, 156)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(86, 13)
        Me.LabelControl1.TabIndex = 59
        Me.LabelControl1.Text = "Fourth Account"
        '
        'LabelControl19
        '
        Me.LabelControl19.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl19.Appearance.Options.UseFont = True
        Me.LabelControl19.Location = New System.Drawing.Point(80, 156)
        Me.LabelControl19.Name = "LabelControl19"
        Me.LabelControl19.Size = New System.Drawing.Size(78, 13)
        Me.LabelControl19.TabIndex = 59
        Me.LabelControl19.Text = "Third Account"
        '
        'LabelControl20
        '
        Me.LabelControl20.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl20.Appearance.Options.UseFont = True
        Me.LabelControl20.Location = New System.Drawing.Point(80, 109)
        Me.LabelControl20.Name = "LabelControl20"
        Me.LabelControl20.Size = New System.Drawing.Size(74, 13)
        Me.LabelControl20.TabIndex = 52
        Me.LabelControl20.Text = "First Account"
        '
        'LabelControl21
        '
        Me.LabelControl21.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl21.Appearance.Options.UseFont = True
        Me.LabelControl21.Location = New System.Drawing.Point(254, 109)
        Me.LabelControl21.Name = "LabelControl21"
        Me.LabelControl21.Size = New System.Drawing.Size(90, 13)
        Me.LabelControl21.TabIndex = 51
        Me.LabelControl21.Text = "Second Account"
        '
        'gcDdSetup
        '
        Me.gcDdSetup.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gcDdSetup.AppearanceCaption.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.Critical
        Me.gcDdSetup.AppearanceCaption.Options.UseFont = True
        Me.gcDdSetup.AppearanceCaption.Options.UseForeColor = True
        Me.gcDdSetup.AppearanceCaption.Options.UseTextOptions = True
        Me.gcDdSetup.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.gcDdSetup.Controls.Add(Me.sbRequestDDSetup)
        Me.gcDdSetup.Controls.Add(Me.LabelControl6)
        Me.gcDdSetup.Controls.Add(Me.LabelControl5)
        Me.gcDdSetup.Controls.Add(Me.DD_STATUS_4ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_AMT_4TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_STATUS_3ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_MET_4ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_AMT_3TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_NO_4TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_MET_3ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_BANK_RT_4TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_NO_3TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_TYPE_4ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_BANK_RT_3TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_TYPE_3ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.LabelControl4)
        Me.gcDdSetup.Controls.Add(Me.LabelControl3)
        Me.gcDdSetup.Controls.Add(Me.DD_STATUS_2ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_AMT_2TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_MET_2ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_NO_2TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_BANK_RT_2TextEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_TYPE_2ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_STATUS_1ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_MET_1ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_TYPE_1ComboBoxEdit)
        Me.gcDdSetup.Controls.Add(Label13)
        Me.gcDdSetup.Controls.Add(Label7)
        Me.gcDdSetup.Controls.Add(Label12)
        Me.gcDdSetup.Controls.Add(Label8)
        Me.gcDdSetup.Controls.Add(Me.DD_ACC_NO_1TextEdit)
        Me.gcDdSetup.Controls.Add(Label9)
        Me.gcDdSetup.Controls.Add(Label10)
        Me.gcDdSetup.Controls.Add(Me.DD_BANK_RT_1TextEdit)
        Me.gcDdSetup.Controls.Add(Label6)
        Me.gcDdSetup.Controls.Add(Label11)
        Me.gcDdSetup.Controls.Add(Label3)
        Me.gcDdSetup.Controls.Add(Label4)
        Me.gcDdSetup.Controls.Add(Label2)
        Me.gcDdSetup.Controls.Add(Me.DD_SPLIT_AMT_1TextEdit)
        Me.gcDdSetup.Controls.Add(Label5)
        Me.gcDdSetup.Location = New System.Drawing.Point(14, 10)
        Me.gcDdSetup.Name = "gcDdSetup"
        Me.gcDdSetup.Size = New System.Drawing.Size(423, 471)
        Me.gcDdSetup.TabIndex = 2
        '
        'sbRequestDDSetup
        '
        Me.sbRequestDDSetup.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.sbRequestDDSetup.Appearance.ForeColor = System.Drawing.Color.Red
        Me.sbRequestDDSetup.Appearance.Options.UseFont = True
        Me.sbRequestDDSetup.Appearance.Options.UseForeColor = True
        Me.sbRequestDDSetup.Location = New System.Drawing.Point(266, 26)
        Me.sbRequestDDSetup.Name = "sbRequestDDSetup"
        Me.sbRequestDDSetup.Size = New System.Drawing.Size(133, 22)
        Me.sbRequestDDSetup.StyleController = Me.LayoutControl1
        Me.sbRequestDDSetup.TabIndex = 60
        Me.sbRequestDDSetup.Text = "Request DD Setup"
        '
        'LabelControl6
        '
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl6.Appearance.Options.UseFont = True
        Me.LabelControl6.Location = New System.Drawing.Point(266, 253)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(86, 13)
        Me.LabelControl6.TabIndex = 59
        Me.LabelControl6.Text = "Fourth Account"
        '
        'LabelControl5
        '
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.Options.UseFont = True
        Me.LabelControl5.Location = New System.Drawing.Point(116, 253)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(78, 13)
        Me.LabelControl5.TabIndex = 59
        Me.LabelControl5.Text = "Third Account"
        '
        'DD_STATUS_4ComboBoxEdit
        '
        Me.DD_STATUS_4ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_STATUS_3", True))
        Me.DD_STATUS_4ComboBoxEdit.Location = New System.Drawing.Point(266, 401)
        Me.DD_STATUS_4ComboBoxEdit.Name = "DD_STATUS_4ComboBoxEdit"
        Me.DD_STATUS_4ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_4ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_4ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_4ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_4ComboBoxEdit.TabIndex = 24
        '
        'DD_SPLIT_AMT_4TextEdit
        '
        Me.DD_SPLIT_AMT_4TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FormattedDDAmount4", True))
        Me.DD_SPLIT_AMT_4TextEdit.Location = New System.Drawing.Point(266, 375)
        Me.DD_SPLIT_AMT_4TextEdit.Name = "DD_SPLIT_AMT_4TextEdit"
        Me.DD_SPLIT_AMT_4TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_4TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_4TextEdit.TabIndex = 23
        '
        'DD_STATUS_3ComboBoxEdit
        '
        Me.DD_STATUS_3ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_STATUS_3", True))
        Me.DD_STATUS_3ComboBoxEdit.Location = New System.Drawing.Point(116, 401)
        Me.DD_STATUS_3ComboBoxEdit.Name = "DD_STATUS_3ComboBoxEdit"
        Me.DD_STATUS_3ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_3ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_3ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_3ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_3ComboBoxEdit.TabIndex = 18
        '
        'DD_SPLIT_MET_4ComboBoxEdit
        '
        Me.DD_SPLIT_MET_4ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_SPLIT_MET_4", True))
        Me.DD_SPLIT_MET_4ComboBoxEdit.Location = New System.Drawing.Point(266, 349)
        Me.DD_SPLIT_MET_4ComboBoxEdit.Name = "DD_SPLIT_MET_4ComboBoxEdit"
        Me.DD_SPLIT_MET_4ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_4ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_4ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_4ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_4ComboBoxEdit.TabIndex = 22
        '
        'DD_SPLIT_AMT_3TextEdit
        '
        Me.DD_SPLIT_AMT_3TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FormattedDDAmount3", True))
        Me.DD_SPLIT_AMT_3TextEdit.Location = New System.Drawing.Point(116, 375)
        Me.DD_SPLIT_AMT_3TextEdit.Name = "DD_SPLIT_AMT_3TextEdit"
        Me.DD_SPLIT_AMT_3TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_3TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_3TextEdit.TabIndex = 17
        '
        'DD_ACC_NO_4TextEdit
        '
        Me.DD_ACC_NO_4TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_NO_4", True))
        Me.DD_ACC_NO_4TextEdit.Location = New System.Drawing.Point(266, 323)
        Me.DD_ACC_NO_4TextEdit.Name = "DD_ACC_NO_4TextEdit"
        Me.DD_ACC_NO_4TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_4TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_4TextEdit.TabIndex = 21
        '
        'DD_SPLIT_MET_3ComboBoxEdit
        '
        Me.DD_SPLIT_MET_3ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_SPLIT_MET_3", True))
        Me.DD_SPLIT_MET_3ComboBoxEdit.Location = New System.Drawing.Point(116, 349)
        Me.DD_SPLIT_MET_3ComboBoxEdit.Name = "DD_SPLIT_MET_3ComboBoxEdit"
        Me.DD_SPLIT_MET_3ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_3ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_3ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_3ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_3ComboBoxEdit.TabIndex = 16
        '
        'DD_BANK_RT_4TextEdit
        '
        Me.DD_BANK_RT_4TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_BANK_RT_4", True))
        Me.DD_BANK_RT_4TextEdit.Location = New System.Drawing.Point(266, 297)
        Me.DD_BANK_RT_4TextEdit.Name = "DD_BANK_RT_4TextEdit"
        Me.DD_BANK_RT_4TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_4TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_4TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_4TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_4TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_4TextEdit.TabIndex = 20
        '
        'DD_ACC_NO_3TextEdit
        '
        Me.DD_ACC_NO_3TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_NO_3", True))
        Me.DD_ACC_NO_3TextEdit.Location = New System.Drawing.Point(116, 323)
        Me.DD_ACC_NO_3TextEdit.Name = "DD_ACC_NO_3TextEdit"
        Me.DD_ACC_NO_3TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_3TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_3TextEdit.TabIndex = 15
        '
        'DD_ACC_TYPE_4ComboBoxEdit
        '
        Me.DD_ACC_TYPE_4ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_TYPE_4", True))
        Me.DD_ACC_TYPE_4ComboBoxEdit.Location = New System.Drawing.Point(266, 271)
        Me.DD_ACC_TYPE_4ComboBoxEdit.Name = "DD_ACC_TYPE_4ComboBoxEdit"
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_4ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_4ComboBoxEdit.TabIndex = 19
        '
        'DD_BANK_RT_3TextEdit
        '
        Me.DD_BANK_RT_3TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_BANK_RT_3", True))
        Me.DD_BANK_RT_3TextEdit.Location = New System.Drawing.Point(116, 297)
        Me.DD_BANK_RT_3TextEdit.Name = "DD_BANK_RT_3TextEdit"
        Me.DD_BANK_RT_3TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_3TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_3TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_3TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_3TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_3TextEdit.TabIndex = 14
        '
        'DD_ACC_TYPE_3ComboBoxEdit
        '
        Me.DD_ACC_TYPE_3ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_TYPE_3", True))
        Me.DD_ACC_TYPE_3ComboBoxEdit.Location = New System.Drawing.Point(116, 271)
        Me.DD_ACC_TYPE_3ComboBoxEdit.Name = "DD_ACC_TYPE_3ComboBoxEdit"
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_3ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_3ComboBoxEdit.TabIndex = 13
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.Options.UseFont = True
        Me.LabelControl4.Location = New System.Drawing.Point(116, 58)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(74, 13)
        Me.LabelControl4.TabIndex = 52
        Me.LabelControl4.Text = "First Account"
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.Options.UseFont = True
        Me.LabelControl3.Location = New System.Drawing.Point(266, 58)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(90, 13)
        Me.LabelControl3.TabIndex = 51
        Me.LabelControl3.Text = "Second Account"
        '
        'DD_STATUS_2ComboBoxEdit
        '
        Me.DD_STATUS_2ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_STATUS_2", True))
        Me.DD_STATUS_2ComboBoxEdit.Location = New System.Drawing.Point(266, 206)
        Me.DD_STATUS_2ComboBoxEdit.Name = "DD_STATUS_2ComboBoxEdit"
        Me.DD_STATUS_2ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_2ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_2ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_2ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_2ComboBoxEdit.TabIndex = 12
        '
        'DD_SPLIT_AMT_2TextEdit
        '
        Me.DD_SPLIT_AMT_2TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FormattedDDAmount2", True))
        Me.DD_SPLIT_AMT_2TextEdit.Location = New System.Drawing.Point(266, 180)
        Me.DD_SPLIT_AMT_2TextEdit.Name = "DD_SPLIT_AMT_2TextEdit"
        Me.DD_SPLIT_AMT_2TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_2TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_2TextEdit.TabIndex = 11
        '
        'DD_SPLIT_MET_2ComboBoxEdit
        '
        Me.DD_SPLIT_MET_2ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_SPLIT_MET_2", True))
        Me.DD_SPLIT_MET_2ComboBoxEdit.Location = New System.Drawing.Point(266, 154)
        Me.DD_SPLIT_MET_2ComboBoxEdit.Name = "DD_SPLIT_MET_2ComboBoxEdit"
        Me.DD_SPLIT_MET_2ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_2ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_2ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_2ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_2ComboBoxEdit.TabIndex = 10
        '
        'DD_ACC_NO_2TextEdit
        '
        Me.DD_ACC_NO_2TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_NO_2", True))
        Me.DD_ACC_NO_2TextEdit.Location = New System.Drawing.Point(266, 128)
        Me.DD_ACC_NO_2TextEdit.Name = "DD_ACC_NO_2TextEdit"
        Me.DD_ACC_NO_2TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_2TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_2TextEdit.TabIndex = 9
        '
        'DD_BANK_RT_2TextEdit
        '
        Me.DD_BANK_RT_2TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_BANK_RT_2", True))
        Me.DD_BANK_RT_2TextEdit.Location = New System.Drawing.Point(266, 102)
        Me.DD_BANK_RT_2TextEdit.Name = "DD_BANK_RT_2TextEdit"
        Me.DD_BANK_RT_2TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_2TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_2TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_2TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_2TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_2TextEdit.TabIndex = 8
        '
        'DD_ACC_TYPE_2ComboBoxEdit
        '
        Me.DD_ACC_TYPE_2ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_TYPE_2", True))
        Me.DD_ACC_TYPE_2ComboBoxEdit.Location = New System.Drawing.Point(266, 76)
        Me.DD_ACC_TYPE_2ComboBoxEdit.Name = "DD_ACC_TYPE_2ComboBoxEdit"
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_2ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_2ComboBoxEdit.TabIndex = 7
        '
        'DD_STATUS_1ComboBoxEdit
        '
        Me.DD_STATUS_1ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_STATUS_1", True))
        Me.DD_STATUS_1ComboBoxEdit.Location = New System.Drawing.Point(116, 206)
        Me.DD_STATUS_1ComboBoxEdit.Name = "DD_STATUS_1ComboBoxEdit"
        Me.DD_STATUS_1ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_1ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_1ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_1ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_1ComboBoxEdit.TabIndex = 6
        '
        'DD_SPLIT_MET_1ComboBoxEdit
        '
        Me.DD_SPLIT_MET_1ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_SPLIT_MET_1", True))
        Me.DD_SPLIT_MET_1ComboBoxEdit.Location = New System.Drawing.Point(116, 154)
        Me.DD_SPLIT_MET_1ComboBoxEdit.Name = "DD_SPLIT_MET_1ComboBoxEdit"
        Me.DD_SPLIT_MET_1ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_1ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_1ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_1ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_1ComboBoxEdit.TabIndex = 4
        '
        'DD_ACC_TYPE_1ComboBoxEdit
        '
        Me.DD_ACC_TYPE_1ComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_TYPE_1", True))
        Me.DD_ACC_TYPE_1ComboBoxEdit.Location = New System.Drawing.Point(116, 76)
        Me.DD_ACC_TYPE_1ComboBoxEdit.Name = "DD_ACC_TYPE_1ComboBoxEdit"
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_1ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_1ComboBoxEdit.TabIndex = 1
        '
        'DD_ACC_NO_1TextEdit
        '
        Me.DD_ACC_NO_1TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_ACC_NO_1", True))
        Me.DD_ACC_NO_1TextEdit.Location = New System.Drawing.Point(116, 128)
        Me.DD_ACC_NO_1TextEdit.Name = "DD_ACC_NO_1TextEdit"
        Me.DD_ACC_NO_1TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_1TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_1TextEdit.TabIndex = 3
        '
        'DD_BANK_RT_1TextEdit
        '
        Me.DD_BANK_RT_1TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "DD_BANK_RT_1", True))
        Me.DD_BANK_RT_1TextEdit.Location = New System.Drawing.Point(116, 102)
        Me.DD_BANK_RT_1TextEdit.Name = "DD_BANK_RT_1TextEdit"
        Me.DD_BANK_RT_1TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_1TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_1TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_1TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_1TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_1TextEdit.TabIndex = 2
        '
        'DD_SPLIT_AMT_1TextEdit
        '
        Me.DD_SPLIT_AMT_1TextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.EMPLOYEEBindingSource, "FormattedDDAmount1", True))
        Me.DD_SPLIT_AMT_1TextEdit.Location = New System.Drawing.Point(116, 180)
        Me.DD_SPLIT_AMT_1TextEdit.Name = "DD_SPLIT_AMT_1TextEdit"
        Me.DD_SPLIT_AMT_1TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_1TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_1TextEdit.TabIndex = 5
        '
        'PanelControl6
        '
        Me.PanelControl6.Controls.Add(Me.UcNotes1)
        Me.PanelControl6.Location = New System.Drawing.Point(12, 70)
        Me.PanelControl6.Name = "PanelControl6"
        Me.PanelControl6.Size = New System.Drawing.Size(1066, 538)
        Me.PanelControl6.TabIndex = 24
        '
        'UcNotes1
        '
        Me.UcNotes1.CoNum = New Decimal(New Integer() {0, 0, 0, 0})
        Me.UcNotes1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.UcNotes1.Location = New System.Drawing.Point(2, 2)
        Me.UcNotes1.Margin = New System.Windows.Forms.Padding(6)
        Me.UcNotes1.Name = "UcNotes1"
        Me.UcNotes1.Size = New System.Drawing.Size(1062, 534)
        Me.UcNotes1.TabIndex = 1
        '
        'lcEmpName
        '
        Me.lcEmpName.Appearance.Font = New System.Drawing.Font("Tahoma", 20.0!)
        Me.lcEmpName.Appearance.ForeColor = System.Drawing.Color.Blue
        Me.lcEmpName.Appearance.Options.UseFont = True
        Me.lcEmpName.Appearance.Options.UseForeColor = True
        Me.lcEmpName.Location = New System.Drawing.Point(2, 2)
        Me.lcEmpName.Name = "lcEmpName"
        Me.lcEmpName.Size = New System.Drawing.Size(320, 33)
        Me.lcEmpName.StyleController = Me.LayoutControl1
        Me.lcEmpName.TabIndex = 8
        Me.lcEmpName.Text = "LabelControl1"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem5, Me.LayoutControlGroupTabs, Me.LayoutControlItem15, Me.LayoutControlItem16, Me.LayoutControlItem17, Me.LayoutControlItem18, Me.EmptySpaceItem2, Me.btnSaveAndCopyPaysLI, Me.lciDataNavigator, Me.LayoutControlItem1})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1090, 620)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.lcEmpName
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem5.MinSize = New System.Drawing.Size(300, 37)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(324, 37)
        Me.LayoutControlItem5.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlGroupTabs
        '
        Me.LayoutControlGroupTabs.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.TabbedControlGroup1})
        Me.LayoutControlGroupTabs.Location = New System.Drawing.Point(0, 37)
        Me.LayoutControlGroupTabs.Name = "LayoutControlGroupTabs"
        Me.LayoutControlGroupTabs.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroupTabs.Size = New System.Drawing.Size(1090, 583)
        Me.LayoutControlGroupTabs.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroupTabs.TextVisible = False
        '
        'TabbedControlGroup1
        '
        Me.TabbedControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
        Me.TabbedControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(5, 5, 5, 5)
        Me.TabbedControlGroup1.SelectedTabPage = Me.LayoutControlGroup4
        Me.TabbedControlGroup1.Size = New System.Drawing.Size(1088, 581)
        Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup4, Me.LayoutControlGroupPayrollTax, Me.layou, Me.lcgDirectDeposit, Me.LayoutControlGroup8, Me.tabAutoPaysAndDeds, Me.lcgPayHistory})
        '
        'LayoutControlGroup4
        '
        Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3})
        Me.LayoutControlGroup4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup4.Name = "LayoutControlGroup4"
        Me.LayoutControlGroup4.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlGroup4.Text = "Primary Info"
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.PanelControl2
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlGroupPayrollTax
        '
        Me.LayoutControlGroupPayrollTax.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem9})
        Me.LayoutControlGroupPayrollTax.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroupPayrollTax.Name = "LayoutControlGroupPayrollTax"
        Me.LayoutControlGroupPayrollTax.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlGroupPayrollTax.Text = "Payroll / Tax Info"
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.PanelControlPayrollTax
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem9.TextVisible = False
        '
        'layou
        '
        Me.layou.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem10})
        Me.layou.Location = New System.Drawing.Point(0, 0)
        Me.layou.Name = "layou"
        Me.layou.Size = New System.Drawing.Size(1070, 542)
        Me.layou.Text = "Check Overrides Setup"
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.PanelControl4
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem10.TextVisible = False
        '
        'lcgDirectDeposit
        '
        Me.lcgDirectDeposit.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem11})
        Me.lcgDirectDeposit.Location = New System.Drawing.Point(0, 0)
        Me.lcgDirectDeposit.Name = "lcgDirectDeposit"
        Me.lcgDirectDeposit.Size = New System.Drawing.Size(1070, 542)
        Me.lcgDirectDeposit.Text = "Direct Deposit"
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.PanelControl5
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem11.TextVisible = False
        '
        'LayoutControlGroup8
        '
        Me.LayoutControlGroup8.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem12})
        Me.LayoutControlGroup8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup8.Name = "LayoutControlGroup8"
        Me.LayoutControlGroup8.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlGroup8.Text = "Notes"
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.PanelControl6
        Me.LayoutControlItem12.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem12.TextVisible = False
        '
        'tabAutoPaysAndDeds
        '
        Me.tabAutoPaysAndDeds.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem19, Me.LayoutControlItem20, Me.LayoutControlItem21, Me.LayoutControlItem22, Me.EmptySpaceItem3, Me.lciPriorPaysDeds})
        Me.tabAutoPaysAndDeds.Location = New System.Drawing.Point(0, 0)
        Me.tabAutoPaysAndDeds.Name = "tabAutoPaysAndDeds"
        Me.tabAutoPaysAndDeds.Size = New System.Drawing.Size(1070, 542)
        Me.tabAutoPaysAndDeds.Text = "Auto Pays / Deds"
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.Panel1
        Me.LayoutControlItem19.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem19.MaxSize = New System.Drawing.Size(0, 37)
        Me.LayoutControlItem19.MinSize = New System.Drawing.Size(104, 37)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(997, 37)
        Me.LayoutControlItem19.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem19.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem19.TextVisible = False
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.GridPays
        Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 37)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(997, 168)
        Me.LayoutControlItem20.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem20.TextVisible = False
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.Panel2
        Me.LayoutControlItem21.Location = New System.Drawing.Point(0, 205)
        Me.LayoutControlItem21.MaxSize = New System.Drawing.Size(0, 34)
        Me.LayoutControlItem21.MinSize = New System.Drawing.Size(104, 34)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(997, 34)
        Me.LayoutControlItem21.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem21.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem21.TextVisible = False
        '
        'LayoutControlItem22
        '
        Me.LayoutControlItem22.Control = Me.GridDeds
        Me.LayoutControlItem22.Location = New System.Drawing.Point(0, 239)
        Me.LayoutControlItem22.Name = "LayoutControlItem22"
        Me.LayoutControlItem22.Size = New System.Drawing.Size(997, 191)
        Me.LayoutControlItem22.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem22.TextVisible = False
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(997, 0)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(73, 542)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'lciPriorPaysDeds
        '
        Me.lciPriorPaysDeds.Control = Me.pcPriorPaysDeds
        Me.lciPriorPaysDeds.Location = New System.Drawing.Point(0, 430)
        Me.lciPriorPaysDeds.Name = "lciPriorPaysDeds"
        Me.lciPriorPaysDeds.Size = New System.Drawing.Size(997, 112)
        Me.lciPriorPaysDeds.TextSize = New System.Drawing.Size(0, 0)
        Me.lciPriorPaysDeds.TextVisible = False
        '
        'lcgPayHistory
        '
        Me.lcgPayHistory.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem8})
        Me.lcgPayHistory.Location = New System.Drawing.Point(0, 0)
        Me.lcgPayHistory.Name = "lcgPayHistory"
        Me.lcgPayHistory.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.lcgPayHistory.Size = New System.Drawing.Size(1070, 542)
        Me.lcgPayHistory.Text = "Pay History"
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.UcEmployeePayHistory2
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(1070, 542)
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.btnSave
        Me.LayoutControlItem15.Location = New System.Drawing.Point(739, 0)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(56, 37)
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem15.TextVisible = False
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.btnCancel
        Me.LayoutControlItem16.Location = New System.Drawing.Point(795, 0)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(65, 37)
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem16.TextVisible = False
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.btnNewEmployee
        Me.LayoutControlItem17.Location = New System.Drawing.Point(860, 0)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(106, 37)
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem17.TextVisible = False
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.btnCopy
        Me.LayoutControlItem18.Location = New System.Drawing.Point(966, 0)
        Me.LayoutControlItem18.Name = "LayoutControlItem18"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(124, 37)
        Me.LayoutControlItem18.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem18.TextVisible = False
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(493, 0)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(11, 37)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'btnSaveAndCopyPaysLI
        '
        Me.btnSaveAndCopyPaysLI.Control = Me.btnSaveAndCopyPays
        Me.btnSaveAndCopyPaysLI.Location = New System.Drawing.Point(565, 0)
        Me.btnSaveAndCopyPaysLI.Name = "btnSaveAndCopyPaysLI"
        Me.btnSaveAndCopyPaysLI.Size = New System.Drawing.Size(174, 37)
        Me.btnSaveAndCopyPaysLI.TextSize = New System.Drawing.Size(0, 0)
        Me.btnSaveAndCopyPaysLI.TextVisible = False
        '
        'lciDataNavigator
        '
        Me.lciDataNavigator.Control = Me.DataNavigator1
        Me.lciDataNavigator.Location = New System.Drawing.Point(324, 0)
        Me.lciDataNavigator.MinSize = New System.Drawing.Size(156, 23)
        Me.lciDataNavigator.Name = "lciDataNavigator"
        Me.lciDataNavigator.Size = New System.Drawing.Size(169, 37)
        Me.lciDataNavigator.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.lciDataNavigator.TextSize = New System.Drawing.Size(0, 0)
        Me.lciDataNavigator.TextVisible = False
        Me.lciDataNavigator.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.btnUtilities
        Me.LayoutControlItem1.Location = New System.Drawing.Point(504, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(61, 37)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'taxesBindingSource
        '
        Me.taxesBindingSource.DataSource = GetType(Brands_FrontDesk.ucEmployeeInfo.Taxes)
        '
        'popupCopyFrom
        '
        Me.popupCopyFrom.Controls.Add(Me.GroupControl10)
        Me.popupCopyFrom.Location = New System.Drawing.Point(604, 268)
        Me.popupCopyFrom.Name = "popupCopyFrom"
        Me.popupCopyFrom.Size = New System.Drawing.Size(405, 160)
        Me.popupCopyFrom.TabIndex = 12
        '
        'GroupControl10
        '
        Me.GroupControl10.Controls.Add(Me.chkUseSameEmpID)
        Me.GroupControl10.Controls.Add(Me.txtEmpNum)
        Me.GroupControl10.Controls.Add(Me.txtCoName)
        Me.GroupControl10.Controls.Add(Me.LabelControl14)
        Me.GroupControl10.Controls.Add(Me.slueEmployeeCopyFrom)
        Me.GroupControl10.Controls.Add(Me.LabelControl13)
        Me.GroupControl10.Controls.Add(Me.btnOk)
        Me.GroupControl10.Controls.Add(Me.btnCancelCopyToNewCompany)
        Me.GroupControl10.Controls.Add(Me.slueCompanyCopyFrom)
        Me.GroupControl10.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl10.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl10.LookAndFeel.SkinName = "Office 2010 Blue"
        Me.GroupControl10.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl10.Name = "GroupControl10"
        Me.GroupControl10.Size = New System.Drawing.Size(405, 160)
        Me.GroupControl10.TabIndex = 11
        Me.GroupControl10.Text = "Select Source Company && Employee "
        '
        'txtEmpNum
        '
        Me.txtEmpNum.Location = New System.Drawing.Point(144, 62)
        Me.txtEmpNum.Name = "txtEmpNum"
        Me.txtEmpNum.Properties.ReadOnly = True
        Me.txtEmpNum.Size = New System.Drawing.Size(232, 20)
        Me.txtEmpNum.TabIndex = 7
        '
        'txtCoName
        '
        Me.txtCoName.Location = New System.Drawing.Point(144, 35)
        Me.txtCoName.Name = "txtCoName"
        Me.txtCoName.Properties.ReadOnly = True
        Me.txtCoName.Size = New System.Drawing.Size(232, 20)
        Me.txtCoName.TabIndex = 6
        '
        'LabelControl14
        '
        Me.LabelControl14.Location = New System.Drawing.Point(13, 65)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(35, 13)
        Me.LabelControl14.TabIndex = 5
        Me.LabelControl14.Text = "Emp #:"
        '
        'slueEmployeeCopyFrom
        '
        Me.slueEmployeeCopyFrom.EditValue = ""
        Me.slueEmployeeCopyFrom.Location = New System.Drawing.Point(51, 62)
        Me.slueEmployeeCopyFrom.Name = "slueEmployeeCopyFrom"
        Me.slueEmployeeCopyFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueEmployeeCopyFrom.Properties.DisplayMember = "EMPNUM"
        Me.slueEmployeeCopyFrom.Properties.NullText = ""
        Me.slueEmployeeCopyFrom.Properties.PopupFormSize = New System.Drawing.Size(250, 0)
        Me.slueEmployeeCopyFrom.Properties.PopupView = Me.GridView3
        Me.slueEmployeeCopyFrom.Properties.ValueMember = "EMPNUM"
        Me.slueEmployeeCopyFrom.Size = New System.Drawing.Size(87, 20)
        Me.slueEmployeeCopyFrom.TabIndex = 1
        '
        'GridView3
        '
        Me.GridView3.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colEmpNum2, Me.colEmpName2})
        Me.GridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView3.Name = "GridView3"
        Me.GridView3.OptionsFind.FindDelay = 100
        Me.GridView3.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView3.OptionsView.ColumnAutoWidth = False
        Me.GridView3.OptionsView.ShowGroupPanel = False
        '
        'colEmpNum2
        '
        Me.colEmpNum2.FieldName = "EMPNUM"
        Me.colEmpNum2.Name = "colEmpNum2"
        Me.colEmpNum2.Visible = True
        Me.colEmpNum2.VisibleIndex = 0
        '
        'colEmpName2
        '
        Me.colEmpName2.FieldName = "EmpName"
        Me.colEmpName2.Name = "colEmpName2"
        Me.colEmpName2.Visible = True
        Me.colEmpName2.VisibleIndex = 1
        Me.colEmpName2.Width = 150
        '
        'LabelControl13
        '
        Me.LabelControl13.Location = New System.Drawing.Point(13, 38)
        Me.LabelControl13.Name = "LabelControl13"
        Me.LabelControl13.Size = New System.Drawing.Size(32, 13)
        Me.LabelControl13.TabIndex = 3
        Me.LabelControl13.Text = "Co. #:"
        '
        'btnOk
        '
        Me.btnOk.ImageOptions.Image = CType(resources.GetObject("btnOk.ImageOptions.Image"), System.Drawing.Image)
        Me.btnOk.Location = New System.Drawing.Point(201, 112)
        Me.btnOk.Name = "btnOk"
        Me.btnOk.Size = New System.Drawing.Size(73, 23)
        Me.btnOk.TabIndex = 2
        Me.btnOk.Text = "Ok"
        '
        'btnCancelCopyToNewCompany
        '
        Me.btnCancelCopyToNewCompany.ImageOptions.Image = CType(resources.GetObject("btnCancelCopyToNewCompany.ImageOptions.Image"), System.Drawing.Image)
        Me.btnCancelCopyToNewCompany.Location = New System.Drawing.Point(104, 112)
        Me.btnCancelCopyToNewCompany.Name = "btnCancelCopyToNewCompany"
        Me.btnCancelCopyToNewCompany.Size = New System.Drawing.Size(70, 23)
        Me.btnCancelCopyToNewCompany.TabIndex = 3
        Me.btnCancelCopyToNewCompany.Text = "Cancel"
        '
        'slueCompanyCopyFrom
        '
        Me.slueCompanyCopyFrom.EditValue = ""
        Me.slueCompanyCopyFrom.Location = New System.Drawing.Point(51, 35)
        Me.slueCompanyCopyFrom.Name = "slueCompanyCopyFrom"
        Me.slueCompanyCopyFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueCompanyCopyFrom.Properties.DisplayMember = "CONUM"
        Me.slueCompanyCopyFrom.Properties.NullText = ""
        Me.slueCompanyCopyFrom.Properties.PopupFormSize = New System.Drawing.Size(350, 0)
        Me.slueCompanyCopyFrom.Properties.PopupView = Me.GridView1
        Me.slueCompanyCopyFrom.Properties.ValueMember = "CONUM"
        Me.slueCompanyCopyFrom.Size = New System.Drawing.Size(87, 20)
        Me.slueCompanyCopyFrom.TabIndex = 0
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM1, Me.GridColumn5, Me.GridColumn6})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsFind.FindDelay = 100
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colCONUM1
        '
        Me.colCONUM1.FieldName = "CONUM"
        Me.colCONUM1.Name = "colCONUM1"
        Me.colCONUM1.Visible = True
        Me.colCONUM1.VisibleIndex = 0
        '
        'GridColumn5
        '
        Me.GridColumn5.FieldName = "CO_NAME"
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 1
        Me.GridColumn5.Width = 150
        '
        'GridColumn6
        '
        Me.GridColumn6.FieldName = "FED_ID"
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 2
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'chkUseSameEmpID
        '
        Me.chkUseSameEmpID.Location = New System.Drawing.Point(51, 88)
        Me.chkUseSameEmpID.Name = "chkUseSameEmpID"
        Me.chkUseSameEmpID.Properties.Caption = "Use same Employee ID in new company if available"
        Me.chkUseSameEmpID.Size = New System.Drawing.Size(325, 19)
        Me.chkUseSameEmpID.TabIndex = 8
        '
        'ucEmployeeInfo
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.popupCopyFrom)
        Me.Name = "ucEmployeeInfo"
        Me.Size = New System.Drawing.Size(1090, 620)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.pcPriorPaysDeds, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pcPriorPaysDeds.ResumeLayout(False)
        CType(Me.GridDeds, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmpAutoDedsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmpAutoPaysBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewDeds, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.GridPays, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewPays, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riLookupEarning, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OTHERPAYBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl2.ResumeLayout(False)
        CType(Me.pnlTerminate, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTerminate.ResumeLayout(False)
        Me.pnlTerminate.PerformLayout()
        CType(Me.PanelControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl8.ResumeLayout(False)
        Me.PanelControl8.PerformLayout()
        CType(Me.EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMPLOYEEBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        Me.GroupControl3.PerformLayout()
        CType(Me.Default_hoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SALARY_AMTTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RATE_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RATE_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RATE_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PAY_FREQTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AUTO_SAL_HRSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.GroupControl11, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl11.ResumeLayout(False)
        Me.GroupControl11.PerformLayout()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TINTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SSNTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.M_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CITYTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ZIPTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Contact_pagerTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GENDERLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Contact_homephoneTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.B_DAYDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.B_DAYDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.F_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Contact_homeemailTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.User_emailTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.STREETTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.L_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ADDR_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        CType(Me.START_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.START_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEPTNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEPARTMENTBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DIVNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DIVISIONBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMP_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TERM_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TERM_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControlPayrollTax, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControlPayrollTax.ResumeLayout(False)
        CType(Me.grpStateNotes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpStateNotes.ResumeLayout(False)
        CType(Me.txtStateNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl9.ResumeLayout(False)
        Me.GroupControl9.PerformLayout()
        CType(Me.RadioGroup1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LOCAL_EE_INFOBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LocalActiveTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Fixed_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Extra_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DependentsTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstSelectedLocals, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstAvailLocals, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Alternate_w4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Local_exemptTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Local_statusTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl5.ResumeLayout(False)
        Me.GroupControl5.PerformLayout()
        CType(Me.FLIEXECheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.STATE_EE_INFOBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstSelectedStates, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstAvailableStates, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UCI_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEFAULT_RESTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEFAULT_WORKTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ALTW4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SDIEXETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SUTA_EXE_FGTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl4.ResumeLayout(False)
        Me.GroupControl4.PerformLayout()
        CType(Me.grpFedOptions, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpFedOptions.ResumeLayout(False)
        Me.grpFedOptions.PerformLayout()
        CType(Me.FED_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.grpTY2020Options, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpTY2020Options.ResumeLayout(False)
        Me.grpTY2020Options.PerformLayout()
        CType(Me.txtW4_deductions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkW4_multijobs.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtW4_dependents.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtW4_otherinc.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.rgrp_w4Style.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OASDI_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FUTA_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl4.ResumeLayout(False)
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl7.ResumeLayout(False)
        CType(Me.GridControlCoOptionsSecondCheckPay, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CoOptionsSecondCheckPayCodeBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewSecondCheckPay, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riPayCodes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlOverrideGroups.ResumeLayout(False)
        Me.pnlGeneralOptions.ResumeLayout(False)
        Me.pnlGeneralOptions.PerformLayout()
        CType(Me.ExcludeFromUtilityImportCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Pr_batch_overrideBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl8.ResumeLayout(False)
        Me.GroupControl8.PerformLayout()
        CType(Me.OTSeperateCheckHoursMoreThanTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OTSeperateCheckCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ddlPayUnderEmpNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMPLOYEEBindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.grpSecondCheckTaxes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpSecondCheckTaxes.ResumeLayout(False)
        Me.grpSecondCheckTaxes.PerformLayout()
        CType(Me.UIStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ResStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.WrkStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FLIOverrdieAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.MedicareOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OASDIOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FedOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TaxFrequencyTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DBOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.STOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LOCOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tbManualCheckNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.grpScheduling, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpScheduling.ResumeLayout(False)
        Me.grpScheduling.PerformLayout()
        CType(Me.FilterToPayFreqComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FirstOfQtrCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LastOfQtrCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LastOfMonthCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd5CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd4CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd3CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd2CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd1CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AllPrdsCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pnlNetOverride, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlNetOverride.ResumeLayout(False)
        Me.pnlNetOverride.PerformLayout()
        CType(Me.NetOverrideDedNumTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEDUCTIONBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NetOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NetOverrideAdjustTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlOTHours.ResumeLayout(False)
        Me.pnlOTHours.PerformLayout()
        CType(Me.OTHoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSecondCheck.ResumeLayout(False)
        Me.pnlSecondCheck.PerformLayout()
        CType(Me.DeptNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEPARTMENTBindingSource2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DivNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PayDedCodeLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckCounterTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PayDedAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PayDedTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlRecordType.ResumeLayout(False)
        Me.pnlRecordType.PerformLayout()
        CType(Me.RecordTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gridViewCheckOverrides, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl5.ResumeLayout(False)
        CType(Me.gcEmpPriorDD, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gcEmpPriorDD.ResumeLayout(False)
        Me.gcEmpPriorDD.PerformLayout()
        CType(Me.gcDdSetup, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gcDdSetup.ResumeLayout(False)
        Me.gcDdSetup.PerformLayout()
        CType(Me.DD_STATUS_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_STATUS_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_STATUS_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_STATUS_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl6.ResumeLayout(False)
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroupTabs, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroupPayrollTax, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layou, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgDirectDeposit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tabAutoPaysAndDeds, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciPriorPaysDeds, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgPayHistory, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.btnSaveAndCopyPaysLI, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciDataNavigator, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.taxesBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.popupCopyFrom, System.ComponentModel.ISupportInitialize).EndInit()
        Me.popupCopyFrom.ResumeLayout(False)
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl10.ResumeLayout(False)
        Me.GroupControl10.PerformLayout()
        CType(Me.txtEmpNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtCoName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueEmployeeCopyFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueCompanyCopyFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkUseSameEmpID.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents lcEmpName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents taxesBindingSource As BindingSource
    Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
    Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroupPayrollTax As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents layou As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents lcgDirectDeposit As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup8 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents tabAutoPaysAndDeds As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LOCAL_EE_INFOBindingSource As BindingSource
    Friend WithEvents STATE_EE_INFOBindingSource As BindingSource
    Friend WithEvents EMPLOYEEBindingSource As BindingSource
    Friend WithEvents Pr_batch_overrideBindingSource As BindingSource
    Friend WithEvents CoOptionsSecondCheckPayCodeBindingSource As BindingSource
    Friend WithEvents OTHERPAYBindingSource As BindingSource
    Friend WithEvents DEPARTMENTBindingSource As BindingSource
    Friend WithEvents DIVISIONBindingSource As BindingSource
    Friend WithEvents EMPLOYEEBindingSource1 As BindingSource
    Friend WithEvents DEDUCTIONBindingSource As BindingSource
    Friend WithEvents DEPARTMENTBindingSource2 As BindingSource
    Friend WithEvents EmpAutoPaysBindingSource As BindingSource
    Friend WithEvents EmpAutoDedsBindingSource As BindingSource
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents PanelControl6 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PanelControl5 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PanelControl4 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PanelControlPayrollTax As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PanelControl2 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Default_hoursTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SALARY_AMTTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RATE_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RATE_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RATE_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PAY_FREQTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents AUTO_SAL_HRSTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GroupControl11 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl16 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl15 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ComboBoxEdit2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents SSNLabelControl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TINTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SSNTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents M_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CITYTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ZIPTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Contact_pagerTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GENDERLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents Contact_homephoneTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents B_DAYDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents F_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Contact_homeemailTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents User_emailTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents STREETTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents L_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TINLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents START_DATEDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents DEPTNUMLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents DIVNUMLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents EMP_TYPETextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TERM_DATEDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents GroupControl9 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents RadioGroup1 As DevExpress.XtraEditors.RadioGroup
    Friend WithEvents LabelControl12 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LocalActiveTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Local_statusLabel As Label
    Friend WithEvents Fixed_whTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Extra_whTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DependentsLabel As Label
    Friend WithEvents DependentsTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Alternate_w4Label As Label
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAddLocal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnRemoveLocal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lstSelectedLocals As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents lstAvailLocals As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents Alternate_w4TextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Local_exemptTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Local_statusTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents GroupControl5 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents FLIEXELabel As Label
    Friend WithEvents FLIEXECheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAddState As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnRemoveState As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lstSelectedStates As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents lstAvailableStates As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents ST_WH_FIXEDLabel As Label
    Friend WithEvents ST_WH_FIXEDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ST_WH_EXTRALabel As Label
    Friend WithEvents ST_WH_EXTRATextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SDIEXELabel As Label
    Friend WithEvents ST_DEPSLabel As Label
    Friend WithEvents ST_DEPSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ST_STATUSLabel As Label
    Friend WithEvents ST_WH_EXE_FGELabel As Label
    Friend WithEvents ALTW4Label As Label
    Friend WithEvents DEFAULT_WORKLabel As Label
    Friend WithEvents DEFAULT_RESLabel As Label
    Friend WithEvents UCI_STATETextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DEFAULT_RESTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents DEFAULT_WORKTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ALTW4TextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ST_WH_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ST_STATUSTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents SDIEXETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents SUTA_EXE_FGTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl4 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents FED_WH_FIXEDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FED_WH_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents OASDI_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FED_WH_EXTRATextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FUTA_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FED_DEPSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FED_STATUSTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents GroupControl7 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridControlCoOptionsSecondCheckPay As GridControl
    Friend WithEvents GridViewSecondCheckPay As Views.Grid.GridView
    Friend WithEvents colPayCode As Columns.GridColumn
    Friend WithEvents riPayCodes As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents colSeparateCheck As Columns.GridColumn
    Friend WithEvents btnDelete As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents pnlOverrideGroups As Panel
    Friend WithEvents pnlGeneralOptions As Panel
    Friend WithEvents ExcludeFromUtilityImportCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl8 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents OTSeperateCheckHoursMoreThanTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents OTSeperateCheckCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ddlPayUnderEmpNum As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents grpSecondCheckTaxes As DevExpress.XtraEditors.GroupControl
    Friend WithEvents UIStateComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ResStateComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents WrkStateComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents FLIOverrdieAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents MedicareOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents OASDIOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FedOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TaxFrequencyTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DBOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents STOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LOCOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tbManualCheckNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents grpScheduling As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LastOfMonthCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd5CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd4CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd3CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd2CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd1CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents AllPrdsCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents pnlNetOverride As DevExpress.XtraEditors.GroupControl
    Friend WithEvents NetOverrideDedNumTextEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents NetOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents NetOverrideAdjustTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents pnlOTHours As Panel
    Friend WithEvents OTHoursTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents pnlSecondCheck As Panel
    Friend WithEvents DeptNum2ndCheck As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents DivNum2ndCheck As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents PayDedCodeLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents CheckCounterTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PayDedAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PayDedTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents pnlRecordType As Panel
    Friend WithEvents RecordTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents GridControl1 As GridControl
    Friend WithEvents gridViewCheckOverrides As Views.Grid.GridView
    Friend WithEvents colRecordType As Columns.GridColumn
    Friend WithEvents colCheckCounter As Columns.GridColumn
    Friend WithEvents gcDdSetup As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents DD_STATUS_4ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_AMT_4TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_STATUS_3ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_MET_4ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_AMT_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_NO_4TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_SPLIT_MET_3ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_BANK_RT_4TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_NO_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_TYPE_4ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_BANK_RT_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_TYPE_3ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents DD_STATUS_2ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_AMT_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_SPLIT_MET_2ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_ACC_NO_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_BANK_RT_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_TYPE_2ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_STATUS_1ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_MET_1ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_ACC_TYPE_1ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_ACC_NO_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_BANK_RT_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_SPLIT_AMT_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents UcNotes1 As ucNotes
    Friend WithEvents btnEditAutoDeduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnEditAutoPay As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridPays As GridControl
    Friend WithEvents GridViewPays As Views.Grid.GridView
    Friend WithEvents colCodeDescription As Columns.GridColumn
    Friend WithEvents riLookupEarning As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents GridColumn1 As Columns.GridColumn
    Friend WithEvents colScheduling As Columns.GridColumn
    Friend WithEvents colFirstCheckOnly As Columns.GridColumn
    Friend WithEvents colSuppPrd As Columns.GridColumn
    Friend WithEvents GridColumn2 As Columns.GridColumn
    Friend WithEvents colLimit1 As Columns.GridColumn
    Friend WithEvents colBalance1 As Columns.GridColumn
    Friend WithEvents GridColumn3 As Columns.GridColumn
    Friend WithEvents btnAddAutoDeduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl18 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GridDeds As GridControl
    Friend WithEvents GridViewDeds As Views.Grid.GridView
    Friend WithEvents colCodeDescription1 As Columns.GridColumn
    Friend WithEvents colDedCalc As Columns.GridColumn
    Friend WithEvents colScheduling1 As Columns.GridColumn
    Friend WithEvents colFirstCheckOnly1 As Columns.GridColumn
    Friend WithEvents colSuppPrd1 As Columns.GridColumn
    Friend WithEvents colStartDate As Columns.GridColumn
    Friend WithEvents colLimit As Columns.GridColumn
    Friend WithEvents colBalance As Columns.GridColumn
    Friend WithEvents GridColumn4 As Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents btnAddAutoPay As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl17 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControl10 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtEmpNum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtCoName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents slueEmployeeCopyFrom As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView3 As Views.Grid.GridView
    Friend WithEvents colEmpNum2 As Columns.GridColumn
    Friend WithEvents colEmpName2 As Columns.GridColumn
    Friend WithEvents LabelControl13 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnOk As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancelCopyToNewCompany As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents slueCompanyCopyFrom As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView1 As Views.Grid.GridView
    Friend WithEvents colCONUM1 As Columns.GridColumn
    Friend WithEvents GridColumn5 As Columns.GridColumn
    Friend WithEvents GridColumn6 As Columns.GridColumn
    Friend WithEvents pnlTerminate As DevExpress.XtraEditors.PanelControl
    Friend WithEvents lblEmployeeStatus As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnTerminate As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PanelControl8 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents EMPNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnCopy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnParsonage As DevExpress.XtraEditors.CheckButton
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents btnSaveAndCopyPays As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnNewEmployee As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents popupCopyFrom As DevExpress.XtraEditors.PopupContainerControl
    Friend WithEvents LayoutControlGroupTabs As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel1 As Panel
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem22 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents btnSaveAndCopyPaysLI As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents DataNavigator1 As DevExpress.XtraEditors.DataNavigator
    Friend WithEvents lciDataNavigator As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lcgPayHistory As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents UcEmployeePayHistory2 As ucEmployeePayHistory
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents grpStateNotes As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtStateNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents btnUtilities As DevExpress.XtraEditors.DropDownButton
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents FirstOfQtrCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LastOfQtrCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FilterToPayFreqComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ADDR_STATETextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents rgrp_w4Style As DevExpress.XtraEditors.RadioGroup
    Friend WithEvents grpTY2020Options As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtW4_deductions As DevExpress.XtraEditors.TextEdit
    Friend WithEvents chkW4_multijobs As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents txtW4_dependents As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtW4_otherinc As DevExpress.XtraEditors.TextEdit
    Friend WithEvents grpFedOptions As DevExpress.XtraEditors.GroupControl
    Friend WithEvents sbRequestDDSetup As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents gcEmpPriorDD As DevExpress.XtraEditors.GroupControl
    Friend WithEvents sbDismiss As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl19 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl20 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl21 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents pcPriorPaysDeds As DevExpress.XtraEditors.PanelControl
    Friend WithEvents sbDismissAutoPaysDedsPanel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lciPriorPaysDeds As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents chkUseSameEmpID As DevExpress.XtraEditors.CheckEdit
End Class
