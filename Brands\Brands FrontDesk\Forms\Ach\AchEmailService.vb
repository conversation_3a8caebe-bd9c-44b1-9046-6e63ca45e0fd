﻿Imports System.Data.SqlClient

Namespace Ach
    Public Class AchEmailService
        Property frmLogger As Serilog.ILogger
        Property _Settings As AchSettings
        Property _Db As dbEPDataDataContext
        Private ReadOnly _RunningFromAutoEmails As Boolean

        Public Sub New(setting As AchSettings, runningFromAutoEmails As Boolean)
            frmLogger = Logger.ForContext(Of AchEmailService)
            _Db = New dbEPDataDataContext(GetConnectionString)
            _Settings = setting
            _RunningFromAutoEmails = runningFromAutoEmails
        End Sub

        Public Async Function EmailClient(coNum As Decimal, templateName As String, shouldSetCoPassword As Boolean, EntryDescType As String, showEmailForm As Boolean, Optional rowId As Integer? = Nothing) As Threading.Tasks.Task
            Dim emailTemplate = GetEmailTemplate(templateName)
            Dim email = New ReportProcessor(coNum, emailTemplate, FileType.Pdf)
            email.DefaultParamValues = New List(Of KeyValuePair)
            email.DefaultParamValues.Add(New KeyValuePair("@CoNum", coNum))
            If rowId.HasValue Then
                email.DefaultParamValues.Add(New KeyValuePair("@ID", rowId))
            Else
                email.DefaultParamValues.Add(New KeyValuePair("@ID", Nothing))
            End If
            email.DefaultParamValues.Add(New KeyValuePair("@EmailTemplate", templateName))
            email.DefaultParamValues.Add(New KeyValuePair("@RunningFromAutoEmails", _RunningFromAutoEmails))
            email.showParametersForm = False
            Dim result = email.ProcessReport
            Dim emlSender = New ReportSender(result) With {.showEmailList = showEmailForm, .showWebPost = False}

            'Solomon modified on Mar 7, '24 to send via zendesk ticket when auto
            Dim sentEmail As Boolean = False
            If showEmailForm Then
                sentEmail = emlSender.EmailReport(showEmailForm)
            Else
                sentEmail = Await emlSender.SendZendeskEmail()
            End If

            If sentEmail Then
                'SetQtrEndHold(coNum)
                If shouldSetCoPassword Then
                    SetCoPassword(coNum, EntryDescType)
                End If
                If emailTemplate.UpdateSql.IsNotNullOrWhiteSpace Then
                    Dim params As List(Of SqlParameter) = New List(Of SqlParameter)
                    params.Add(New SqlParameter("@CoNum", coNum))
                    params.Add(New SqlParameter("@ID", If(rowId, DBNull.Value)))
                    params.Add(New SqlParameter("@EmailTemplate", templateName))
                    params.Add(New SqlParameter("@RunningFromAutoEmails", _RunningFromAutoEmails))
                    Await UpdateSqlAsync(emailTemplate.UpdateSql, Nothing, params.ToArray)
                End If
                'If rowId.HasValue Then
                '    Dim matchedCoRow = (From a In _Db.AchTransactionsLogs Where a.CoNum = coNum AndAlso Not a.Completed AndAlso a.EmailTemplate = template.Name).ToList
                '    For Each r In matchedCoRow
                '        Dim frm = New frmEditNotes(r.Notes, $"Email ({template.Name}) sent to client.")
                '        r.Notes = frm.GetNote
                '        r.LastEmailDate = DateTime.Now
                '    Next
                'Else
                '    Dim frm = New frmEditNotes(Row.Notes, $"Email ({template.Name}) sent to client.")
                '    Row.Notes = frm.GetNote
                '    Row.LastEmailDate = DateTime.Now
                'End If
                'DB.SaveChanges
            End If
        End Function

        Private templatesStorage As Dictionary(Of String, ReportEmailTeplate)


        Private Function GetEmailTemplate(templateName As String) As ReportEmailTeplate
            If templatesStorage Is Nothing Then
                templatesStorage = New Dictionary(Of String, ReportEmailTeplate)
            End If
            Dim emailTemplate As ReportEmailTeplate = Nothing
            If Not templatesStorage.TryGetValue(templateName, emailTemplate) Then
                emailTemplate = _Db.ReportEmailTeplates.SingleOrDefault(Function(r) r.Name = templateName)
                If emailTemplate Is Nothing Then
                    Throw New Exception($"No email template found with name: {templateName}")
                End If
                templatesStorage.Add(templateName, emailTemplate)
            End If
            Return emailTemplate
        End Function

        Private Sub SetQtrEndHold(coNum As Decimal)
            Try
                Dim _db = New dbEPDataDataContext(GetConnectionString)
                Dim coOpt = (From c In _db.COOPTIONs Where c.CONUM = coNum).Single
                Dim co = (From c In _db.view_CompanySumarries Where c.CONUM = coNum).Single
                If coOpt.QYEND_HOLD_TYPE = "None" Then
                    frmLogger.Information("Setting QYEND_HOLD_TYPE to Billing, old status {QYEND_HOLD_TYPE}", coOpt.QYEND_HOLD_TYPE)
                    coOpt.QYEND_HOLD_TYPE = "Billing"
                    _db.SaveChanges
                ElseIf coOpt.QYEND_HOLD_TYPE <> "Billing" Then
                    Dim email = New EmailService()
                    email.ToEmail.Add(GetUdfValue("AchTransactions-EmailNotification"))
                    email.Subject = "Ach - Client Qtr End Hold Status"
                    email.Body = $"Please note that Co#: {co.CONUM} Co Name: {co.CO_NAME} has an open ACH transaction, but the system did not set the client on 'Billing', {vbCrLf}{vbCrLf}the client current status is: {coOpt.QYEND_HOLD_TYPE}"
                    email.SendEmail()
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error setting Qtr End Hold from ACH form.", ex)
            End Try
        End Sub

        Public Sub SetCoPassword(coNum As Decimal, entryDescType As String, Optional NewPassword As String = Nothing)
            Dim _db = New dbEPDataDataContext(GetConnectionString)
            Dim co = (From c In _db.COMPANies Where c.CONUM = coNum).Single

            If co.PR_PASSWORD?.StartsWith("c david b @ nsf") OrElse co.PR_PASSWORD?.StartsWith("c tax dept @ nsf") Then
                Exit Sub
            End If

            If NewPassword Is Nothing Then
                NewPassword = co.PR_PASSWORD.IfNotNullThenAdd(" - ") & "c tax dept @ nsf " & entryDescType
            End If

            Logger.Debug("Changing Co# {CoNum} Password from {OldPassword} to {NewPassword}", coNum, co.PR_PASSWORD, NewPassword)
            co.PR_PASSWORD = NewPassword
            _db.SaveChanges
        End Sub

        Function GetNewPassword(conum As Decimal, entryDescType As String) As String
            Dim _db = New dbEPDataDataContext(GetConnectionString)
            Dim co = (From c In _db.COMPANies Where c.CONUM = conum).Single
            If co.PR_PASSWORD?.StartsWith("c david b @ nsf") OrElse co.PR_PASSWORD?.StartsWith("c tax dept @ nsf") Then
                Return co.PR_PASSWORD
            End If
            Return co.PR_PASSWORD.IfNotNullThenAdd(" - ") & "c tax dept @ nsf " & entryDescType
        End Function

        Function GetCurrentPassword(conum As Decimal) As String
            Dim _db = New dbEPDataDataContext(GetConnectionString)
            Dim co = (From c In _db.COMPANies Where c.CONUM = conum).Single
            Return co.PR_PASSWORD
        End Function

        Public Sub RemoveCoPassword(coNum As Decimal)
            Using db As New dbEPDataDataContext(GetConnectionString())
                Dim CO = (From c In db.COMPANies Where c.CONUM = coNum).Single
                CO.PR_PASSWORD = Nothing
                db.SaveChanges()
            End Using
        End Sub

    End Class
End Namespace