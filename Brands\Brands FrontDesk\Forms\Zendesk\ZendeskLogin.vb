﻿Module ZendeskLogin
    Public Sub LoginToZendesk()
        Try
            If PermissionsZD.ZendeskEmailAddress.IsNullOrWhiteSpace Then
                DevExpress.XtraEditors.XtraMessageBox.Show("You're not setup with zendesk")
                Return
            End If

            Dim oneTimeSignInCode = CreateString(150)
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim user = db.FrontDeskPermissions.Single(Function(u) u.UserName = PermissionsZD.UserName)
                user.ZendeskOneTimeSignInCode = oneTimeSignInCode
                db.SubmitChanges()
            End Using
            Dim oneTimeSignInCodeEncoded = Net.WebUtility.UrlEncode(oneTimeSignInCode)
            Logger.Debug("Logging into zendesk. oneTimeSignInCode: {OneTimeSignInCode} Encoded: {oneTimeSignInCodeEncoded}", oneTimeSignInCode, oneTimeSignInCodeEncoded)
            'https://localhost:44337/
            'https://zendeskapi.brandspayroll.com
            Dim url = $"https://zendeskapi.brandspayroll.com/account/login?ZendeskOneTimeSignInCode={oneTimeSignInCodeEncoded}"

            'Process.Start("chrome", url)

            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = "chrome"
            psi.Arguments = url
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)

        Catch ex As Exception
            DisplayErrorMessage("Error logging into zendesk", ex)
        End Try
    End Sub

    Friend Async Function AutoLogin() As Task
        Try
            Logger.Debug("Entering AutoLogin")
            If PermissionsZD.AutoLoginToZendesk AndAlso Not Await UserHasAnyActiveConnections() AndAlso PermissionsZD.ZendeskEmailAddress.IsNotNullOrWhiteSpace() Then
                LoginToZendesk()
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in AutoLogin to zendesk")
        End Try
    End Function

    Friend Function CreateString(ByVal stringLength As Integer) As String
        Dim rd As Random = New Random()
        Const allowedChars As String = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz0123456789!@$?_-"
        Dim chars As Char() = New Char(stringLength - 1) {}

        For i As Integer = 0 To stringLength - 1
            chars(i) = allowedChars(rd.[Next](0, allowedChars.Length))
        Next

        Return New String(chars)
    End Function

End Module
