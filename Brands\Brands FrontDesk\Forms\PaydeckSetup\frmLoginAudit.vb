﻿Public Class frmLoginAudit
    Private Sub frmLoginAudit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        deFromDate.DateTime = Today.AddDays(-3)
        deToDate.DateTime = Today.AddDays(1)
    End Sub

    Private Sub btnLoadData_Click(sender As Object, e As EventArgs) Handles btnLoadData.Click
        Dim epDB = New dbEPDataDataContext(GetConnectionString)
        gcLoginAuditMain.DataSource = epDB.BrandsAuthLoginRequests.Where(Function(lr) lr.Date > deFromDate.DateTime AndAlso lr.Date < deToDate.DateTime).OrderByDescending(Function(lr) lr.Date).ToList
    End Sub

    Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvLoginAuditMain.FocusedRowObjectChanged
        Dim row As BrandsAuthLoginRequest = e.Row
        If row IsNot Nothing Then
            Dim epDB = New dbEPDataDataContext(GetConnectionString)
            gcLoginByUser.DataSource = epDB.BrandsAuthLoginRequests.Where(Function(lr) lr.Username = row.Username).OrderByDescending(Function(lr) lr.Date).ToList
            gcLoginByIP.DataSource = epDB.BrandsAuthLoginRequests.Where(Function(lr) lr.IpAddress = row.IpAddress).OrderByDescending(Function(lr) lr.Date).ToList
        End If
    End Sub
End Class