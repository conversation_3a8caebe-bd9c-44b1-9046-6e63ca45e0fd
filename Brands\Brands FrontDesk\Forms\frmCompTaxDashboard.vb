﻿Imports System.ComponentModel
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Imports DevExpress.Utils
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid

Public Class frmCompTaxDashboard
    Private _co As COMPANY
    Private db As dbEPDataDataContext
    Private boolSearchedUnmatchedTran As Boolean = False

    Private Async Function LoadData(RefreshOnly As Boolean) As Threading.Tasks.Task
        'clear boolSearchedUnmatchedTran when expecting changes
        If boolSearchedUnmatchedTran Then
            boolSearchedUnmatchedTran = RefreshOnly
        End If

        Dim SqlScriptID As Int16 = 1253
        Try
            Dim FilePath = "I:\Downloads\QuarterTTF\General Quarterly Records\"
            If Not IsDBNull(teYear.EditValue) AndAlso teYear.EditValue.ToString() <> "" Then
                FilePath += teYear.EditValue.ToString() + "\"

                If Not IsDBNull(lueQtr.EditValue) AndAlso lueQtr.EditValue.ToString() <> "" Then
                    FilePath += $"Q-{lueQtr.EditValue} {teYear.EditValue}"
                End If
            End If
            hllcQuarterTTF.Text = FilePath
            lueNoticeType.Properties.DataSource = GetUdfValueSplitted("NoticeType")
            Dim CoNum = CDec(pceSearchCompany.EditValue)
            If pceSearchCompany.EditValue Is Nothing Then Return
            _co = db.COMPANies.Where(Function(c) c.CONUM = CoNum).FirstOrDefault()

            Dim IsLinked = db.COMPANies.Where(Function(c) c.FED_ID = _co.FED_ID).Count > 1

            lciShowDataForAllLinked.Visibility = ToBarItemVisibility(IsLinked)

            Dim cop = db.COOPTIONs.Where(Function(c) c.CONUM = CoNum).FirstOrDefault()
            Dim cud = db.CO_UDFs.Where(Function(c) c.CONUM = CoNum AndAlso c.UDF_DESCR = "INACTIVE STATUS").FirstOrDefault()
            teCoStatus.EditValue = _co.CO_STATUS
            teClosedStatus.EditValue = cud?.UDF_STRING
            teTaxService.EditValue = IIf(cop.DOSTSFED = "YES", "CTS", IIf(cop.TOTAL_TAX = "YES", "TTF", ""))
            teCO_Name.EditValue = _co.CO_NAME
            teCoStreet.EditValue = _co.CO_STREET
            teCoCity.EditValue = _co.CO_CITY
            cbeStateGeneral.EditValue = _co.CO_STATE
            teCoZip.EditValue = _co.CO_ZIP
            teFedID.EditValue = _co.FED_ID

            GVEvents.ViewCaption = $"Events Co# {CoNum}"
            GVTickets.ViewCaption = $"Tickets Co# {CoNum} (EP>Execupay Suite>CRM>Ticket Center)"
            LcRoot.ShowProgessPanel()
            Dim script = Query(Of SqlScript)($"SELECT TOP 1 * FROM custom.SqlScripts WHERE ID = {SqlScriptID}").FirstOrDefault()?.SelectSql()

            Dim params = {New SqlClient.SqlParameter("@year", teYear.EditValue), New SqlClient.SqlParameter("@qtr", lueQtr.EditValue), New SqlClient.SqlParameter("@conum", pceSearchCompany.EditValue), New SqlClient.SqlParameter("@QtrEndDate", New Date(teYear.EditValue, If(IsDBNull(lueQtr.EditValue), 1, lueQtr.EditValue * 3), 1).AddMonths(1).AddDays(-1))}
            Dim data As DataTable = Await QueryAsync(script, 1000, params)


            GCEvents.DataSource = data
            GVEvents.BestFitColumns()
            Dim column As DevExpress.XtraGrid.Columns.GridColumn
            For Each column In GVEvents.Columns
                If column.FieldName.ToLower() = "sort" Then
                    column.Visible = False
                    Exit For
                    'ElseIf "events,status".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    '    column.BestFit()
                End If
            Next

            Dim option2 = If(IsDBNull(lueQtr.EditValue), "NULL", "'" + lueQtr.EditValue.ToString() + "'")
            GCPayrolls.DataSource = Query($"EXEC custom.prc_FDCoNumMultiReport	@Action = 'PayrollListForTaxNotice', @CoNum = {CoNum}
									, @Option1 = '{teYear.EditValue}', @Option2 = {option2}, @Option3 = '{IIf(ckbtnShowDataForAllLinked.Checked, "Yes", "No")}'")

            GVBandedPayrolls.BestFitColumns()

            For Each column In GVBandedPayrolls.Columns
                If "tax941,split_debit".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 AndAlso column.Summary.Count = 0 Then
                    column.Width = 100
                    column.DisplayFormat.FormatType = FormatType.Numeric
                    column.DisplayFormat.FormatString = "c2"
                    column.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, DevExpress.Data.SummaryMode.Selection, column.FieldName, "{0:c}")})
                End If
            Next

            GCTickets.DataSource = Query($"EXEC custom.prc_FDCoNumMultiReport	@Action = 'CoIssuesForTaxNotice', @CoNum = {CoNum}
									, @Option1 = '{teYear.EditValue}', @Option2 = {option2}, @Option3 = '{IIf(ckbtnShowDataForAllLinked.Checked, "Yes", "No")}'")

            GVTickets.BestFitColumns()
            For Each column In GVTickets.Columns
                If column.FieldName.ToLower() = "conum" Then
                    column.Visible = ckbtnShowDataForAllLinked.Checked
                    'column.BestFit()
                    'Exit For
                    'ElseIf "ticket_id,type,category,yr,qtr,subcategory".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    '    column.BestFit()
                End If
            Next

            lstUciStates.DataSource = Query($"EXEC custom.prc_FDCoNumMultiReport	@Action = 'TaxStatesForTaxNotice', @CoNum = {CoNum}
									, @Option1 = '{teYear.EditValue}', @Option2 = {option2}, @Option3 = '{IIf(ckbtnShowDataForAllLinked.Checked, "Yes", "No")}'")

            Dim PeriodEndReportCoNum As Decimal = CoNum

            If ckbtnShowDataForAllLinked.Checked AndAlso _co.FED_ID <> "" Then
                Dim Linked900Co = db.COMPANies.Where(Function(c) c.FED_ID = _co.FED_ID AndAlso c.CONUM > 899 AndAlso c.CONUM <= 999).FirstOrDefault()

                If Linked900Co IsNot Nothing AndAlso Linked900Co.CONUM > 0 Then
                    PeriodEndReportCoNum = Linked900Co.CONUM
                End If
            End If

            GCPeriodEndReports.DataSource = Query($"EXEC custom.prc_FDCoNumMultiReport	@Action = 'PeriodEndReportsForTaxNotice', @CoNum = {PeriodEndReportCoNum}
									, @Option1 = '{luePeriodEndYears.EditValue}'")
            GVPeriodEndReports.ExpandAllGroups()
            GVPeriodEndReports.BestFitColumns()
            GVPeriodEndReports.ActiveFilterString = "Job_Type = 'Etax'"

            GCPayrollReports.DataSource = Query($"EXEC custom.prc_FDCoNumMultiReport	@Action = 'PayrollReportsForTaxNotice', @CoNum = {CoNum}
									, @Option1 = '{luePayrollYear.EditValue}', @Option2 = '{IIf(ckbtnShowDataForAllLinked.Checked, "YES", "NO")}'")

            'GVPayrollReports.ExpandAllGroups()
            GVPayrollReports.ClearSorting()
            GridColumnCheckDate.SortOrder = DevExpress.Data.ColumnSortOrder.Descending
            GridColumnCheckDate.SortIndex = 0
            GridColumnRptConum.SortOrder = DevExpress.Data.ColumnSortOrder.Ascending
            GridColumnRptConum.SortIndex = 1
            GVPayrollReports.ExpandGroupLevel(0)
            GVPayrollReports.BestFitColumns()

            GCDocs.DataSource = (From d In db.documents Where d.conum = CoNum AndAlso d.issue_id Is Nothing Select New docs With {.doc_id = d.doc_id, .category = d.category, .title = d.title, .create_date = d.create_date, .file = d.file, .desc = d.desc, .modify_date = d.modify_date}).OrderByDescending(Function(s) s.modify_date).ToList()

            For Each column In GVDocs.Columns
                If column.FieldName.ToLower() = "doc_id" OrElse column.FieldName.ToLower() = "desc" Then
                    column.Visible = False
                End If
            Next
            GVDocs.BestFitColumns()

            GCTicketDocs.DataSource = Query($"SELECT	IssueTitle = it.title, d.doc_id, d.category, d.title, d.create_date, d.[file], d.[desc], d.modify_date
FROM	documents d
		INNER JOIN issues it ON it.issue_id = d.issue_id
WHERE	d.conum = {CoNum} AND d.issue_id IS NOT NULL
ORDER BY it.title")

            For Each column In GVTicketDocs.Columns
                If column.FieldName.ToLower() = "doc_id" OrElse column.FieldName.ToLower() = "desc" Then
                    column.Visible = False
                End If
            Next
            GVTicketDocs.BestFitColumns()
        Catch ex As Exception
            Logger.Error(ex, "LoadData")
        Finally
            LcRoot.HideProgressPanel()
        End Try
    End Function

    Private Sub pceSearchCompany_Popup(sender As Object, e As EventArgs) Handles pceSearchCompany.Popup
        ucSearchCompany.FocusToSearch(pceSearchCompany)
    End Sub

    Private Async Sub pceSearchCompany_EditValueChanged(sender As Object, e As EventArgs) Handles pceSearchCompany.EditValueChanged
        Try
            If pceSearchCompany.EditValue Is Nothing Then Return
            Dim CoNum = CDec(pceSearchCompany.EditValue)
            _co = db.COMPANies.Where(Function(c) c.CONUM = CoNum).FirstOrDefault()
            Dim IsLinked = db.COMPANies.Where(Function(c) c.FED_ID = _co.FED_ID).Count > 1
            ckbtnShowDataForAllLinked.Checked = IsLinked
            Await LoadData(False)

            Dim IsOnNSF = db.ExecuteQuery(Of Boolean)($"SELECT custom.fn_IsOnNsf({CoNum})").FirstOrDefault()
            Dim IsOnBillingHold = nz(db.COOPTIONs.Where(Function(c) c.CONUM = CoNum).FirstOrDefault().QYEND_HOLD_TYPE?.ToString(), "").ToString().ToLower().Contains("billing")

            Dim issue As frmAcctOnHoldPopup.Issue = If(IsOnNSF AndAlso IsOnBillingHold, frmAcctOnHoldPopup.Issue.NsfAndOpenInvoice, If(IsOnNSF, frmAcctOnHoldPopup.Issue.Nsf, If(IsOnBillingHold, frmAcctOnHoldPopup.Issue.OpenInvoice, frmAcctOnHoldPopup.Issue.None)))

            If issue <> frmAcctOnHoldPopup.Issue.None Then
                lciAcctOnHold.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                Dim frm = New frmAcctOnHoldPopup(issue)
                frm.ShowDialog()
            Else
                lciAcctOnHold.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in pceSearchCompany_EditValueChanged", ex)
        End Try
    End Sub

    Private Sub frmCompTaxDashboard_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            'may crash in design in parentform
            If LicenseManager.UsageMode = LicenseUsageMode.Designtime Then Exit Sub

            db = New dbEPDataDataContext(GetConnectionString())
            Dim Y = If(Today.Month = 12, Today, New Date(Today.Year - 1, 12, 1))
            Y = New Date(Y.Year, 12, 1)
            lueQtr.Properties.ValueMember = "Qtr"
            lueQtr.Properties.DisplayMember = "Qtr"
            lueQtr.Properties.DataSource = Query($"SELECT Qtr = 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT NULL")

            Dim QEDtm As DateTime = Now
            If {3, 6, 9, 12}.ToList().IndexOf(QEDtm.Month) = -1 Then
                QEDtm = QEDtm.AddDays(-QEDtm.Day + 1).AddMonths(-QEDtm.Month Mod 3)
            End If

            teYear.EditValue = QEDtm.Year
            lueQtr.EditValue = QEDtm.Quarter

            Dim PeriodEndYearsOptions As New List(Of String) From {"1 Year", "2 Years", "3 Years", "4 Years", "All Years"}
            luePeriodEndYears.Properties.DataSource = PeriodEndYearsOptions
            luePeriodEndYears.EditValue = "1 Year"
            luePayrollYear.Properties.DataSource = PeriodEndYearsOptions
            luePayrollYear.EditValue = "1 Year"
            ucSearchCompany.BindPopupContainerEdit(pceSearchCompany, True)
        Catch ex As Exception
            DisplayErrorMessage("error in frmCompTaxDashboard", ex)
        End Try
    End Sub

    Private Async Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Await LoadData(True)
    End Sub

    Private Async Sub lueQtr_EditValueChanged(sender As Object, e As EventArgs) Handles lueQtr.EditValueChanged
        If _co IsNot Nothing AndAlso _co.CONUM <> Nothing Then
            Await LoadData(False)
        End If
    End Sub

    Private Sub lstUciStates_SelectedIndexChanged(sender As Object, e As EventArgs) Handles lstUciStates.SelectedIndexChanged
        Try
            If lstUciStates.SelectedValue Is Nothing Then Return
            Dim CoNum = CDec(pceSearchCompany.EditValue)
            Dim option2 = If(IsDBNull(lueQtr.EditValue), "NULL", "'" + lueQtr.EditValue.ToString() + "'")

            GCTaxes.DataSource = Query($"EXEC custom.prc_FDCoNumMultiReport	@Action = 'TaxListForTaxNotice', @CoNum = {CoNum}
									, @Option1 = '{teYear.EditValue}', @Option2 = {option2}, @Option3 = '{IIf(ckbtnShowDataForAllLinked.Checked, "Yes", "No")}', @Option4 = '{lstUciStates.SelectedValue}'")

            Dim avgW = (From c In BGVTaxes.Columns Select c.Width).Average(Function(c) c)

            'for some strange reason, the first time it does not work.  putting focus didn't help either
            If avgW = 75 AndAlso XtraTabControl1.SelectedTabPage.Equals(xtpPayrolls) Then
                XtraTabControl1.SelectedTabPage = xtpTaxes
                BGVTaxes.BestFitColumns()

                'Dim column As DevExpress.XtraGrid.Columns.GridColumn
                'For Each column In BGVTaxes.Columns
                '    If "amount,paid_amt".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 And column.Summary.Count = 0 Then
                '        column.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, DevExpress.Data.SummaryMode.Selection, column.FieldName, "{0:c2}")})
                '        column.Width = 5000
                '        column.DisplayFormat.FormatType = FormatType.Numeric
                '        column.DisplayFormat.FormatString = "c2"
                '    End If

                '    If "conum,prnum,div,tax_type,trans_date,sent_date,chk_num,pstat,checkdate,rundate".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                '        column.BestFit()
                '    End If
                'Next
                XtraTabControl1.SelectedTabPage = xtpPayrolls
            End If

            Dim column As DevExpress.XtraGrid.Columns.GridColumn
            For Each column In BGVTaxes.Columns
                If column.FieldName.ToLower = "start_time" Then
                    column.DisplayFormat.FormatString = "g"
                    column.DisplayFormat.FormatType = FormatType.DateTime
                End If
            Next

            BGVTaxes.BestFitColumns()

            For Each column In BGVTaxes.Columns
                If "amount,paid_amt,eramt,split_credit,split_debit,spc_amt3,spc_amt4,spc_amt5,spc_amt6,spc_amt7,spc_amt8,spc_amt9,spc_amt10,spc_amt11,spc_amt12".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 AndAlso column.Summary.Count = 0 Then
                    column.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, DevExpress.Data.SummaryMode.Selection, column.FieldName, "{0:0.##}")})
                    column.Width = 5000
                    column.DisplayFormat.FormatType = FormatType.Numeric
                    column.DisplayFormat.FormatString = "c2"
                ElseIf "conum,prnum,div,tax_type,trans_date,sent_date,chk_num,pstat,checkdate,rundate".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    column.BestFit()
                ElseIf column.FieldName = "State" AndAlso lstUciStates.SelectedValue <> "NY WH" Then
                    column.Visible = False
                End If
            Next
            BGVTaxes.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error in lstUciStates_SelectedIndexChanged change", ex)
        End Try
    End Sub

    Private Async Sub ckbtnShowDataForAllLinked_CheckedChanged(sender As Object, e As EventArgs) Handles ckbtnShowDataForAllLinked.CheckedChanged
        Try
            Await LoadData(False)
        Catch ex As Exception
            DisplayErrorMessage("Error in ckbtnShowDataForAllLinked", ex)
        End Try
    End Sub

    Private Async Function RunSelect(script As String, ParamArray params As SqlParameter()) As Task(Of DataTable)
        Dim data As DataTable = Await QueryAsync(script, 1000, params)
        Return data
    End Function

    Private Async Sub btnSearchUnmatchedTrans_Click(sender As Object, e As EventArgs) Handles btnSearchUnmatchedTrans.Click
        boolSearchedUnmatchedTran = True
        Try
            Dim SqlScriptID As Int16 = 1251
            Try
                Dim CoNum = CDec(pceSearchCompany.EditValue)
                If pceSearchCompany.EditValue Is Nothing Then Return
                _co = db.COMPANies.Where(Function(c) c.CONUM = CoNum).FirstOrDefault()

                Dim linkedCo As String = _co.CONUM
                If _co.FED_ID <> "" Then
                    linkedCo = Join(db.COMPANies.Where(Function(c) c.FED_ID = _co.FED_ID).Select(Of String)(Function(c) c.CONUM).ToArray(), ",")
                End If

                LcRoot.ShowProgessPanel()
                Dim script = Query(Of SqlScript)($"SELECT TOP 1 * FROM custom.SqlScripts WHERE ID = {SqlScriptID}").FirstOrDefault()?.SelectSql()

                Dim params = {New SqlClient.SqlParameter("@YR", teYear.EditValue), New SqlClient.SqlParameter("@Qtr", lueQtr.EditValue), New SqlClient.SqlParameter("@conum", pceSearchCompany.EditValue), New SqlClient.SqlParameter("@ConumSplitByComma", linkedCo), New SqlClient.SqlParameter("@QtrEndDate", New Date(teYear.EditValue, lueQtr.EditValue * 3, 1).AddMonths(1).AddDays(-1))}

                ''Dim data As DataTable = Await QueryAsync(script, 1000, params)
                Dim data As DataTable = Await RunSelect(script, params)

                GCUnmatchedTrans.DataSource = data
                GVUnmatchedTrans.BestFitColumns()
            Catch ex As Exception
                Logger.Error(ex, "LoadData")
            Finally
                LcRoot.HideProgressPanel()
            End Try
        Catch ex As Exception
            DisplayErrorMessage("Error in SearchUnmatchedTrans", ex)
        End Try
    End Sub

    Private Function ProcessReports(rowHandle As Integer, gv As GridView) As String
        Try
            Dim newPdf = New DevExpress.Pdf.PdfDocumentProcessor
            newPdf.CreateEmptyDocument()

            Dim rptNameCol As String

            If gv Is GVPeriodEndReports Then
                rptNameCol = "RPT_NAME"
            ElseIf gv Is GVPayrollReports Then
                rptNameCol = "REPORT_NAME"
            Else
                Return ""
            End If

            Dim fileName As String = If(gv.SelectedRowsCount = 1, CType(gv.GetRow(rowHandle), DataRowView).Row(rptNameCol), "Payroll Reports")

            fileName = fileName.Replace("/", "_")
            Dim filePath = Path.Combine(modReports.GetCrystalReportsFolder(), modReports.GetFileName(_co, fileName, "pdf"))
            Dim row As DataRowView = Nothing
            row = gv.GetRow(rowHandle)
            Try
                Dim dt = modGlobals.Query("SELECT ru.REPORT_FILE FROM dbo.REPORTS_UNION ru WHERE ru.id = @P0 AND ru.CONUM = @P1", New SqlClient.SqlParameter("@P0", row("ID")), New SqlClient.SqlParameter("@P1", row("CONUM")))
                Dim byteArray As Byte() = dt.Rows(0)(0)
                Dim pdf = New DevExpress.Pdf.PdfDocumentProcessor()
                pdf.LoadDocument(New MemoryStream(byteArray))
                If pdf.Document.Pages.Count = 0 AndAlso row("RPT_ID") = 206 Then
                    Exit Function
                End If
                PdfUtilities.CopyPages(pdf, newPdf, row(rptNameCol))
            Catch ex As Exception
                Logger.Error(ex, "Error creating PDF file for {CoNum} {ReportName} CheckDate: {CheckDate}", _co.CONUM, row(rptNameCol), row("CHECK_DATE"))
                If XtraMessageBox.Show("Error creating PDF for Report: {0} Would you like to continue without it?".FormatWith(row(rptNameCol)), "Skip Report", MessageBoxButtons.YesNo) = DialogResult.No Then
                    Return Nothing
                End If
            End Try

            If newPdf.Document.Pages.Count = 0 Then
                DisplayMessageBox("The document is empty.")
                Return Nothing
            End If
            newPdf.SaveDocument(filePath)
            Return filePath
        Catch ex As Exception
            DisplayErrorMessage("Error creating PDF for selected Payroll Reports.", ex)
        End Try
        Return Nothing
    End Function

    Private Sub gvAttachments_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GVPeriodEndReports.FocusedRowObjectChanged
        Try
            If GVPeriodEndReports.GetSelectedRows().Count = 0 OrElse e.RowHandle < 0 Then Return
            Dim fileName As String = ProcessReports(e.RowHandle, GVPeriodEndReports)
            If fileName IsNot Nothing AndAlso System.IO.Path.GetExtension(fileName).ToLower() = ".pdf" Then
                PdfViewerPeriodEndReports.DocumentFilePath = fileName
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error previwing attachment.")
        End Try
    End Sub

    Private Async Sub lue_EditValueChanged(sender As Object, e As EventArgs) Handles luePeriodEndYears.EditValueChanged, luePayrollYear.EditValueChanged
        If _co IsNot Nothing AndAlso _co.CONUM <> Nothing Then
            Await LoadData(False)
        End If
    End Sub

    Private Async Sub teYear_Leave(sender As Object, e As EventArgs) Handles teYear.Leave
        Await LoadData(False)
    End Sub

    Private Async Sub teYear_EditValueChanged(sender As Object, e As EventArgs) Handles teYear.EditValueChanged
        If teYear.SelectionStart = 4 Then
            Await LoadData(False)
        End If
    End Sub

    Private Sub bbiOpenCo_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiOpenCo.ItemClick
        If pceSearchCompany.EditValue Is Nothing Then Return
        Dim CoNum = CDec(pceSearchCompany.EditValue)

        MainForm.OpenCompForm(CoNum)
    End Sub

    Private Sub GVTickets_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GVTickets.RowCellStyle
        Try
            If e.CellValue Is Nothing Then Return
            Dim row As DataRow = CType(GVTickets.GetRow(e.RowHandle), DataRowView).Row

            If Not IsDBNull(lueQtr.EditValue) AndAlso (e.Column.FieldName.ToLower = "title" OrElse e.Column.FieldName.ToLower = "conum") AndAlso Not IsDBNull(row("Yr")) AndAlso row("Yr") = teYear.EditValue AndAlso Not IsDBNull(row("Qtr")) AndAlso row("Qtr") = lueQtr.EditValue Then
                If e.Column.FieldName.ToLower <> "conum" Then
                    e.Appearance.BackColor = Color.Yellow
                ElseIf row("conum") <> _co.CONUM Then
                    e.Appearance.BackColor = Color.Yellow
                End If
            End If
        Catch ex As Exception
            Logger.Error(ex, "GVTickets_RowCellStyle")
        End Try
    End Sub

    Private Sub GVBandedPayrolls_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GVBandedPayrolls.RowCellStyle
        Try
            If e.CellValue Is Nothing Then Return
            Dim row As DataRow = CType(GVBandedPayrolls.GetRow(e.RowHandle), DataRowView).Row

            If e.Column.FieldName.ToLower = "check_date" Then
                e.Appearance.BackColor = Choose(CDate(row("CHECK_DATE")).Quarter, Color.LightPink, Color.LightGreen, Color.LightSkyBlue, Color.Khaki)
            ElseIf e.Column.FieldName.ToLower = "starttime" AndAlso Not IsDBNull(row("starttime")) AndAlso CDate(row("Check_Date")).Date < CDate(row("starttime")).Date Then
                e.Appearance.BackColor = Color.LightYellow
            ElseIf e.Column.FieldName.ToLower = "tax941" AndAlso row("Tax941") < 0 AndAlso Not IsDBNull(row("starttime")) AndAlso CDate(row("Check_Date")).Date < CDate(row("starttime")).Date Then
                e.Appearance.BackColor = Color.LightYellow
            End If
        Catch ex As Exception
            Logger.Error(ex, "GVPayrolls_RowCellStyle")
        End Try
    End Sub

    Private Sub BGVTaxes_RowCellStyle(sender As Object, e As RowCellStyleEventArgs)
        Try
            If e.CellValue Is Nothing Then Return
            Dim row As DataRow = CType(BGVTaxes.GetRow(e.RowHandle), DataRowView).Row

            If e.Column.FieldName.ToLower = "checkdate" Then
                e.Appearance.BackColor = Choose(CDate(row("CheckDate")).Quarter, Color.LightPink, Color.LightGreen, Color.LightSkyBlue, Color.Khaki)
            ElseIf e.Column.FieldName.ToLower = "start_time" AndAlso CDate(row("CheckDate")).Date < CDate(row("start_time")).Date Then
                e.Appearance.BackColor = Color.LightYellow
            ElseIf ((e.Column.FieldName.ToLower = "amount" AndAlso row("amount") < 0) OrElse (e.Column.FieldName.ToLower = "paid_amt" AndAlso row("paid_amt") < 0)) AndAlso CDate(row("CheckDate")).Date < CDate(row("start_time")).Date Then
                e.Appearance.BackColor = Color.LightYellow
            End If
        Catch ex As Exception
            Logger.Error(ex, "BGVTaxes_RowCellStyle")
        End Try
    End Sub

    Private Sub GVDocs_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GVDocs.RowCellStyle
        Try
            If e.CellValue Is Nothing Then Return
            Dim doc As docs = CType(GVDocs.GetRow(e.RowHandle), docs)

            If Not doc.file.ToString().ToLower.EndsWith(".pdf") Then
                e.Appearance.BackColor = Color.LightGray
            End If
        Catch ex As Exception
            Logger.Error(ex, "BGVTaxes_RowCellStyle")
        End Try
    End Sub

    Private Sub xtcMain_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs) Handles xtcMain.SelectedPageChanged
        If e.Page.Equals(xtpPeriodEndReports) Then
            Me.PdfCommandBarPeriodEndReports.Control = Me.PdfViewerPeriodEndReports
            Me.PdfCommandBarPeriodEndReports.StandaloneBarDockControl = Me.StandaloneBarDockControl1
        Else
            Me.PdfCommandBarPeriodEndReports.Control = Me.PdfViewerPayrollReports
            Me.PdfCommandBarPeriodEndReports.StandaloneBarDockControl = Me.StandaloneBarDockControl2
        End If
    End Sub

    Private Sub hllcQuarterTTF_HyperlinkClick(sender As Object, e As HyperlinkClickEventArgs) Handles hllcQuarterTTF.HyperlinkClick, hllcBankStatements.HyperlinkClick, HyperlinkLabelControl2.HyperlinkClick, HyperlinkLabelControl1.HyperlinkClick, HyperlinkLabelControl3.HyperlinkClick, HyperlinkLabelControl5.HyperlinkClick, HyperlinkLabelControl4.HyperlinkClick, HyperlinkLabelControl6.HyperlinkClick
        Try
            'System.Diagnostics.Process.Start(e.Text)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = e.Text
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error in hllcQuarterTTF_HyperlinkClick", ex)
        End Try
    End Sub

    Private Sub GVDocs_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GVDocs.FocusedRowObjectChanged
        Try
            If GVDocs.GetSelectedRows().Count = 0 Then Return
            Dim doc = CType(GVDocs.GetRow(e.RowHandle), docs)
            teDocumentNotes.RtfText = doc.desc
            If Not doc.file.ToLower().EndsWith(".pdf") Then
                PdfViewerDocs.CloseDocument()
                Return
            End If
            Dim docID = doc.doc_id
            Dim byteArray As Byte() = Query("select blob from document_storage where document_id = '" + docID.ToString() + "'").Rows(0)(0)
            Dim pdf = New DevExpress.Pdf.PdfDocumentProcessor()
            PdfViewerDocs.LoadDocument(New MemoryStream(byteArray))
        Catch ex As Exception
            Logger.Error(ex, "Error previwing attachment.")
        End Try
    End Sub

    Private Async Sub bbiOrderSupplies_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiManualBilling.ItemClick
        If pceSearchCompany.EditValue Is Nothing Then Return
        Dim CoNum = CDec(pceSearchCompany.EditValue)

        MainForm.OpenCompForm(CoNum,,,, OpenOrderSupplies:=True)
    End Sub

    Private Sub GVPayrollReports_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GVPayrollReports.FocusedRowObjectChanged
        Try
            If GVPayrollReports.GetSelectedRows().Count = 0 OrElse e.RowHandle < 0 Then Return
            Dim fileName As String = ProcessReports(e.RowHandle, GVPayrollReports)
            If fileName IsNot Nothing AndAlso System.IO.Path.GetExtension(fileName).ToLower() = ".pdf" Then
                PdfViewerPayrollReports.DocumentFilePath = fileName
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error previwing attachment.")
        End Try
    End Sub

    Private Sub bbiErrorLog_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiErrorLog.ItemClick
        MainForm.AddOrActivateForm(Of frmErrorLogEntry)()
    End Sub
    Class docs
        Public Property doc_id As Guid
        Public Property category As String
        Public Property title As String
        Public Property create_date As Date?
        Public Property file As String
        Public Property desc As String
        Public Property modify_date As Date?
    End Class

    Private Sub GVUnmatchedTrans_CustomDrawEmptyForeground(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs) Handles GVUnmatchedTrans.CustomDrawEmptyForeground
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = TryCast(sender, DevExpress.XtraGrid.Views.Grid.GridView)
        If view.RowCount <> 0 OrElse Not boolSearchedUnmatchedTran Then
            Return
        End If
        Dim drawFormat As New StringFormat()
        drawFormat.LineAlignment = StringAlignment.Center
        drawFormat.Alignment = drawFormat.LineAlignment
        e.Appearance.Font = New Font(FontFamily.GenericSansSerif, 20)
        e.Graphics.DrawString("No Unmatched Data", e.Appearance.Font, SystemBrushes.ControlDark, New RectangleF(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height), drawFormat)
    End Sub

    Private Sub pceSearchCompany_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles pceSearchCompany.EditValueChanging
        Dim conum As Decimal?

        Try
            conum = e.OldValue
        Catch ex As Exception
            Return
        End Try

        If e.OldValue > 0 AndAlso e.NewValue = Nothing Then
            e.NewValue = e.OldValue
            pceSearchCompany.EditValue = e.OldValue
            e.Cancel = True
        End If
    End Sub

    Private Sub GVPayrollReports_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GVPayrollReports.RowCellStyle
        Try
            If e.CellValue Is Nothing Then Return
            Dim row As DataRow = CType(GVPayrollReports.GetRow(e.RowHandle), DataRowView).Row

            If e.Column.FieldName.ToLower = "check_date" Then
                e.Appearance.BackColor = Choose(CDate(row("CHECK_DATE")).Quarter, Color.LightPink, Color.LightGreen, Color.LightSkyBlue, Color.Khaki)
            End If
        Catch ex As Exception
            Logger.Error(ex, "GVPayrollReports_RowCellStyle")
        End Try
    End Sub

    Private Sub CheckEditShowSpecialAmounts_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditShowSpecialAmounts.CheckedChanged
        Dim TaxGridHasSpAmtCols As Boolean = colBgv_spc_amt3.Visible
        Dim spCols = {colBgv_spc_amt3, colBgv_spc_amt4, colBgv_spc_amt5, colBgv_spc_amt6, colBgv_spc_amt7, colBgv_spc_amt8, colBgv_spc_amt9, colBgv_spc_amt9, colBgv_spc_amt10, colBgv_spc_amt11, colBgv_spc_amt12}
        If CheckEditShowSpecialAmounts.Checked <> TaxGridHasSpAmtCols Then
            For Each col In spCols
                col.Visible = CheckEditShowSpecialAmounts.Checked
            Next
        End If
    End Sub

    Private Sub GVTicketDocs_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GVTicketDocs.FocusedRowObjectChanged
        Try
            If GVTicketDocs.GetSelectedRows().Count = 0 Then Return
            Dim row = CType(GVTicketDocs.GetRow(e.RowHandle), System.Data.DataRowView)

            teTicketDocumentNotes.RtfText = row("desc")
            teTicketDocumentNotes.Text += row("IssueTitle")
            If Not row("file").ToLower().EndsWith(".pdf") Then
                PdfViewerTicketDocs.CloseDocument()
                Return
            End If
            Dim docID = row("doc_id")
            Dim byteArray As Byte() = Query("select blob from document_storage where document_id = '" + row("doc_id").ToString() + "'").Rows(0)(0)
            Dim pdf = New DevExpress.Pdf.PdfDocumentProcessor()
            PdfViewerTicketDocs.LoadDocument(New MemoryStream(byteArray))
        Catch ex As Exception
            Logger.Error(ex, "Error previwing attachment.")
        End Try
    End Sub

    Private Sub PictureEditAccountOnHold_Click(sender As Object, e As EventArgs) Handles PictureEditAccountOnHold.Click
        Try
            Dim CoNum = CDec(pceSearchCompany.EditValue)
            Dim IsOnNSF = db.ExecuteQuery(Of Boolean)($"SELECT custom.fn_IsOnNsf({CoNum})").FirstOrDefault()
            Dim IsOnBillingHold = nz(db.COOPTIONs.Where(Function(c) c.CONUM = CoNum).FirstOrDefault().QYEND_HOLD_TYPE?.ToString(), "").ToString().ToLower().Contains("billing")
            Dim issue As frmAcctOnHoldPopup.Issue = If(IsOnNSF AndAlso IsOnBillingHold, frmAcctOnHoldPopup.Issue.NsfAndOpenInvoice, If(IsOnNSF, frmAcctOnHoldPopup.Issue.Nsf, If(IsOnBillingHold, frmAcctOnHoldPopup.Issue.OpenInvoice, frmAcctOnHoldPopup.Issue.None)))
            Dim frm = New frmAcctOnHoldPopup(issue)
            frm.ShowDialog()
        Catch ex As Exception
            DisplayErrorMessage("Error in PictureEditAccountOnHold_Click", ex)
        End Try
    End Sub
End Class