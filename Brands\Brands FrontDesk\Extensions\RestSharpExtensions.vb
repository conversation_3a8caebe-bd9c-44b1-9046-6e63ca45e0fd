﻿Imports System.Runtime.CompilerServices
Imports RestSharp

Module RestSharpExtensions

    <Extension>
    Public Sub CheckResponse(Of T)(response As RestResponse(Of T), Optional description As String = "")

        If description.IsNotNullOrWhiteSpace Then
            description = " " & description & vbCrLf
        End If

        If response Is Nothing Then
            Throw New Exception("{0}Rosposne is null".FormatWith(description))
        ElseIf response.ErrorMessage.IsNotNullOrWhiteSpace Then
            Throw New Exception("{0}Error while attempting to make request. {1}".FormatWith(description, response.ErrorMessage))
        ElseIf response.StatusCode <> Net.HttpStatusCode.OK Then
            Throw New Exception("{0}Error Code: {1} Content: {2}".FormatWith(description, response.StatusCode, response.Content))
        End If
    End Sub

    <Extension>
    Public Sub CheckResponse(response As RestResponse, Optional description As String = "")

        If description.IsNotNullOrWhiteSpace Then
            description = " " & description & vbCrLf
        End If

        If response Is Nothing Then
            Throw New Exception("{0}Rosponse is null".FormatWith(description))
        ElseIf response.ErrorMessage.IsNotNullOrWhiteSpace Then
            Throw New Exception("{0}Error while attempting to make request. {1}".FormatWith(description, response.ErrorMessage))
        ElseIf response.StatusCode <> Net.HttpStatusCode.OK Then
            Throw New Exception("{0}Error Code: {1} Content: {2}".FormatWith(description, response.StatusCode, response.Content))
        End If
    End Sub

    <Extension>
    Public Async Function ExecuteAsync(request As RestRequest, url As String, Optional CheckResponseMessage As String = "") As Task(Of RestResponse)
        Logger.Debug("Entering [ExecuteAsync] Url: {Url} CheckResponseMessage: {CheckResponseMessage}", url, CheckResponseMessage)
        Dim client = New RestClient(url)
        Dim results = Await client.ExecuteAsync(request)
        If CheckResponseMessage.IsNotNullOrWhiteSpace Then
            results.CheckResponse(CheckResponseMessage)
        End If
        Return results
    End Function

End Module
