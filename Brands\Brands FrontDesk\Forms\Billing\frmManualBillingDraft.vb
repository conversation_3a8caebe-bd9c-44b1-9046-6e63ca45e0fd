﻿Imports DevExpress.XtraEditors

Public Class frmManualBillingDraft

    Private Property db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
        deFromDate.DateTime = Today.AddMonths(-1)
    End Sub

    Private Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            dD_Manual_Trans_LogsBindingSource.DataSource = db.DD_Manual_Trans_Logs.Where(Function(d) d.Date_Requested > deFromDate.DateTime).ToList()
        Catch ex As Exception
            DisplayErrorMessage("Error loading Data", ex)
        End Try
    End Sub

    Private Sub frmManualBillingDraft_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub bbiCancelDraft_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCancelDraft.ItemClick
        Dim row As DD_Manual_Trans_Log = dD_Manual_Trans_LogsBindingSource.Current
        If row Is Nothing Then
            XtraMessageBox.Show("No row selected")
        ElseIf row.CanNotBeCanceled Then
            XtraMessageBox.Show("Sorry this transaction cannot be canceled. Ask banking dep. for assistance")
        Else
            If XtraMessageBox.Show($"Are you sure you want to cancel this transaction {row.ID}?", "Cancel Transaction", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                row.Transaction_Status = "Canceled"
                row.CanNotBeCanceled = True
                db.SaveChanges
            End If
        End If
    End Sub

    Private Sub bbiAddDraft_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiAddDraft.ItemClick
        Using frm = New frmManualBillingDraftAdd
            If frm.ShowDialog = DialogResult.OK Then
                LoadData()
            End If
        End Using
    End Sub

    Private Sub GridControl1_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles GridControl1.MouseDoubleClick
        Dim hi = Me.GridView1.CalcHitInfo(e.Location)
        If hi.RowHandle >= 0 Then
            Dim row As DD_Manual_Trans_Log = Me.GridView1.GetRow(hi.RowHandle)
            If Not row.TransType = "Refund" Then Exit Sub
            Dim edit = row.CloneEntity
            Using frm As New frmManualBillingDraftEdit
                frm.EditRow = edit
                Dim results = frm.ShowDialog
                If results = DialogResult.OK Then
                    Dim props = {"Transaction_Status", "ROUTING_NUMBER", "ACCOUNT_NUMBER", "ACCT_TYPE", "Credit_Routing", "Credit_Account", "Credit_ACCT_TYPE"}
                    For Each prop In props
                        row.GetType.GetProperty(prop).SetValue(row, edit.GetType.GetProperty(prop).GetValue(edit))
                    Next
                    db.SaveChanges
                End If
            End Using
        End If
    End Sub
End Class