﻿Imports Brands_FrontDesk

Public Class frmDocumentation
    Dim _FocuseDocumentation As Documentation

    Public Sub New()
        InitializeComponent()
        TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        rpgAdmin.Visible = Permissions.AllowDocumentationManagment
        TabbedControlGroup1.SelectedTabPageIndex = 0
    End Sub

    Private Sub frmDocumentation_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            documentationBindingSource.DataSource = New dbEPDataDataContext(GetConnectionString).Documentations
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub BarButtonItem1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem1.ItemClick
        LoadData()
    End Sub

    Private Sub bbiAddDocument_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiAddDocument.ItemClick
        AddDocument(Nothing)
    End Sub

    Private Sub TreeList1_FocusedNodeChanged(sender As Object, e As DevExpress.XtraTreeList.FocusedNodeChangedEventArgs) Handles TreeList1.FocusedNodeChanged
        Try
            Me.SuspendLayout()
            _FocuseDocumentation = TreeList1.GetRow(e.Node.Id)
            SetDocumentPreview(_FocuseDocumentation)
            e.Node.Expand()
            Dim parentNode = e.Node.ParentNode
            Dim parentNodeIdsList = New List(Of Integer)
            parentNodeIdsList.Add(e.Node.Id)
            While parentNode IsNot Nothing
                parentNodeIdsList.Add(parentNode.Id)
                parentNode = parentNode.ParentNode
            End While
            Dim nodesList = TreeList1.GetNodeList().ToList
            nodesList = nodesList.Where(Function(n) n.Expanded).ToList
            For Each node In nodesList
                If Not parentNodeIdsList.Contains(node.Id) Then node.Collapse()
            Next

            TreeList1.RefreshNode(e.Node)
            TreeList1.RefreshNode(e.OldNode)
        Catch ex As Exception
            Logger.Error(ex, "Error in documentation when changing focused row.")
        Finally
            ResumeLayout()
        End Try
    End Sub

    Private Sub SetDocumentPreview(doc As Documentation)
        If doc IsNot Nothing Then
            If doc.DocumentType = "pdf" Then
                TabbedControlGroup1.SelectedTabPageIndex = 1
                PdfViewer1.LoadDocument(New System.IO.MemoryStream(doc.Document.ToArray()))
            ElseIf doc.DocumentType = "word" Then
                TabbedControlGroup1.SelectedTabPageIndex = 2
                RichEditControl1.LoadDocument(New System.IO.MemoryStream(doc.Document.ToArray()), doc.DocumentFormat)
            ElseIf doc.DocumentType = "video" Then
                TabbedControlGroup1.SelectedTabPageIndex = 3

            End If
        Else
            TabbedControlGroup1.SelectedTabPageIndex = 0
        End If
    End Sub

    Private Sub TreeList1_PopupMenuShowing(sender As Object, e As DevExpress.XtraTreeList.PopupMenuShowingEventArgs) Handles TreeList1.PopupMenuShowing
        Try
            If Not Permissions.AllowDocumentationManagment Then Exit Sub
            Dim node = TreeList1.GetNodeAt(e.Point)
            If node Is Nothing Then Exit Sub
            Dim doc As Documentation = TreeList1.GetRow(node.Id)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add Document", Sub() AddDocument(doc.Id), My.Resources.add_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit", Sub() ShowEditWindow(doc), My.Resources.edit_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteDocument(doc), My.Resources.delete_16x16))
        Catch ex As Exception
            DisplayErrorMessage("Error in show popup", ex)
        End Try
    End Sub

    Private Sub AddDocument(parentCategoryId As Integer?)
        Using frm As New frmDocumentationAddOrEdit(parentCategoryId)
            If frm.ShowDialog() = DialogResult.OK Then LoadData()
        End Using
    End Sub

    Private Sub ShowEditWindow(doc As Documentation)
        Using frm As New frmDocumentationAddOrEdit(doc)
            If frm.ShowDialog() = DialogResult.OK Then LoadData()
        End Using
    End Sub

    Private Sub DeleteDocument(doc As Documentation)
        Try
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you would like to delete this document?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If
            Using db As New dbEPDataDataContext(GetConnectionString)
                db.Documentations.Attach(doc)
                db.Documentations.DeleteOnSubmit(doc)
                db.SubmitChanges()
                LoadData()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error deleting document.", ex)
        End Try
    End Sub

    Private Sub TextEdit1_Properties_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles TextEdit1.Properties.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Clear Then
            TextEdit1.EditValue = Nothing
        End If
    End Sub

    Private Sub TextEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles TextEdit1.EditValueChanged
        TreeList1.FindFilterText = TextEdit1.Text
    End Sub

    Private Sub bbiEditDocument_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiEditDocument.ItemClick
        Try
            If _FocuseDocumentation IsNot Nothing Then
                If _FocuseDocumentation.DocumentType = "pdf" Then
                    DisplayMessageBox("Sorry, Editing PDF documents is not supported.")
                    Exit Sub
                End If

                Using frm As New frmDocumentationEditWord(_FocuseDocumentation)
                    If frm.ShowDialog() = DialogResult.OK Then
                        Using db As New dbEPDataDataContext(GetConnectionString())
                            db.Documentations.Attach(_FocuseDocumentation)
                            _FocuseDocumentation.Document = frm.GetDocumentBytes().ToArray()
                            _FocuseDocumentation.DocumentText = frm.GetDocumentText()
                            _FocuseDocumentation.ChangeDateTime = Now
                            _FocuseDocumentation.ChangeUser = UserName
                            db.SaveChanges()
                            SetDocumentPreview(_FocuseDocumentation)
                        End Using
                    End If
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error editing document.", ex)
        End Try
    End Sub

    Private Sub bbiExportDocument_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiExportDocument.ItemClick
        If _FocuseDocumentation IsNot Nothing Then
            Try
                Using fd = New SaveFileDialog()
                    fd.FileName = _FocuseDocumentation.Name
                    If fd.ShowDialog() = DialogResult.OK Then
                        If _FocuseDocumentation.DocumentType = "pdf" Then
                            Dim path = $"{fd.FileName}.pdf"
                            System.IO.File.WriteAllBytes(path, _FocuseDocumentation.Document.ToArray())
                            'System.Diagnostics.Process.Start(path)
                            Dim psi As New System.Diagnostics.ProcessStartInfo()
                            psi.FileName = path
                            psi.UseShellExecute = True
                            System.Diagnostics.Process.Start(psi)
                        ElseIf _FocuseDocumentation.DocumentType = "word" Then
                            Dim path = $"{fd.FileName}{_FocuseDocumentation.FileExtension}"
                            RichEditControl1.SaveDocument(path, _FocuseDocumentation.DocumentFormat)
                            'System.Diagnostics.Process.Start(path)
                            Dim psi As New System.Diagnostics.ProcessStartInfo()
                            psi.FileName = path
                            psi.UseShellExecute = True
                            System.Diagnostics.Process.Start(psi)
                        End If
                    End If
                End Using
            Catch ex As Exception
                DisplayErrorMessage("Error exporting document.", ex)
            End Try
        End If
    End Sub

    Private Sub btnPlayVideo_Click(sender As Object, e As EventArgs) Handles btnPlayVideo.Click
        Try
            If _FocuseDocumentation IsNot Nothing Then
                Dim path = System.IO.Path.ChangeExtension(System.IO.Path.GetTempFileName(), "mp4")
                System.IO.File.WriteAllBytes(path, _FocuseDocumentation.Document.ToArray())
                'System.Diagnostics.Process.Start(path)
                Dim psi As New System.Diagnostics.ProcessStartInfo()
                psi.FileName = path
                psi.UseShellExecute = True
                System.Diagnostics.Process.Start(psi)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error playing video.", ex)
        End Try
    End Sub
End Class



Partial Class Documentation
    Public ReadOnly Property DocumentFormat As DevExpress.XtraRichEdit.DocumentFormat
        Get
            If FileExtension = ".doc" Then
                Return DevExpress.XtraRichEdit.DocumentFormat.Doc
            ElseIf FileExtension = ".docx" Then
                Return DevExpress.XtraRichEdit.DocumentFormat.OpenXml
            Else
                Throw New ArgumentOutOfRangeException("DocumentType", $"Unknown DocumentType: {FileExtension}")
            End If
        End Get
    End Property



End Class
