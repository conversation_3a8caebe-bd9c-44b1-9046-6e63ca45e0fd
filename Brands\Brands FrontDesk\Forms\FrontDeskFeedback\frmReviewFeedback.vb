﻿Imports DevExpress.XtraEditors

Public Class frmReviewFeedback

    Private Property db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
    End Sub

    Private Sub frmFeedbacks_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub bbiDelete_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiDelete.ItemClick
        Dim row As FrontDeskFeedback = frontDeskFeedbacksBindingSource.Current
        DeleteRow(row)
    End Sub

    Private Sub DeleteRow(row As FrontDeskFeedback)
        Try
            If row IsNot Nothing Then
                If XtraMessageBox.Show("Are you sure you would to permenantly delete this record?", "Confirm Delete", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                    db.FrontDeskFeedbacks.DeleteOnSubmit(row)
                    db.SubmitChanges()
                    frontDeskFeedbacksBindingSource.RemoveCurrent()

                    Dim path = $"\\Brands.local\DFS\Execupay\FrontDesk\Feedback\{row.Id}"
                    If System.IO.Directory.Exists(path) Then System.IO.Directory.Delete(path, True)
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting Feedback", ex)
        End Try
    End Sub

    Private Sub LoadData()
        Try
            cbeStatus.Properties.Items.Clear()
            cbeType.Properties.Items.Clear()
            cbeStatus.Properties.Items.AddRange(GetUdfValueSplitted("Feedback Status"))
            cbeType.Properties.Items.AddRange(GetUdfValueSplitted("Feedback Types"))

            db = New dbEPDataDataContext(GetConnectionString)
            frontDeskFeedbacksBindingSource.DataSource = db.FrontDeskFeedbacks.ToList()
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error loading feedback data", ex)
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As FrontDeskFeedback = GridView1.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(row), My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub frontDeskFeedbacksBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles frontDeskFeedbacksBindingSource.CurrentChanged
        lbcAttachents.Items.Clear()
        Dim row As FrontDeskFeedback = frontDeskFeedbacksBindingSource.Current
        Dim path = $"\\Brands.local\DFS\Execupay\FrontDesk\Feedback\{row?.Id}\Screenshots"
        If row IsNot Nothing AndAlso System.IO.Directory.Exists(path) Then
            Dim files = (New IO.DirectoryInfo(path)).GetFiles()
            For Each file In files
                lbcAttachents.Items.Add(file)
            Next
        End If
    End Sub


    Private Sub lbcAttachents_SelectedValueChanged(sender As Object, e As EventArgs) Handles lbcAttachents.SelectedValueChanged
        Dim row As System.IO.FileInfo = lbcAttachents.SelectedItem
        If row IsNot Nothing Then
            FilePreviewUserControl1.SetFile(row.FullName, row.Name)
        Else
            FilePreviewUserControl1.Clear()
        End If
    End Sub

    Private Sub hllcErrorId_HyperlinkClick(sender As Object, e As DevExpress.Utils.HyperlinkClickEventArgs) Handles hllcErrorId.HyperlinkClick
        'System.Diagnostics.Process.Start($"http://appserver:5341/#/events?signal=signal-3,signal-5&filter=ErrorId%20%3D%20'{DirectCast(frontDeskFeedbacksBindingSource.Current, FrontDeskFeedback).ErrorId}'")
        Dim filePath = $"http://appserver:5341/#/events?signal=signal-3,signal-5&filter=ErrorId%20%3D%20'{DirectCast(frontDeskFeedbacksBindingSource.Current, FrontDeskFeedback).ErrorId}'"
        Dim psi As New System.Diagnostics.ProcessStartInfo()
        psi.FileName = filePath
        psi.UseShellExecute = True
        System.Diagnostics.Process.Start(psi)
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs)
        db.SaveChanges()
    End Sub

    Private Sub bbiSave_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSave.ItemClick
        frontDeskFeedbacksBindingSource.EndEdit()
        db.SaveChanges
    End Sub

End Class