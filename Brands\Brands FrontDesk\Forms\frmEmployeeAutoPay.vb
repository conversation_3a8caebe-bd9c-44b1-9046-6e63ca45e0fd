﻿Imports System.ComponentModel
Public Class frmEmployeeAutoPay

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeForm As ucEmployeeInfo

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoPayEnt As EmpAutoPay

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property RecType As Char

    Private EmpRec As EMPLOYEE
    Private RecTypeDescription As String
    Private IsNew As Boolean

    Private DB As dbEPDataDataContext

    Private Sub frmEmployeeAutoPay_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.EmpRec = Me.EmployeForm.EmployeeEnt
        If AutoPayEnt Is Nothing Then
            AutoPayEnt = New EmpAutoPay With {.CoNum = EmpRec.CONUM, .EmpNum = EmpRec.EMPNUM,
                .IsNewRec = True,
                .IsOpen = True,
                .Type = Me.RecType,
                .Divison = EmpRec.DIVNUM,
                .CL_DEPT = EmpRec.DEPTNUM
            }
            Me.IsNew = True
        End If

        DB = New dbEPDataDataContext(GetConnectionString)

        Dim Div = (From A In DB.DIVISIONs
                   Where A.CONUM = EmpRec.CONUM
                   Select A.DDIVNUM, A.DDIVNAME, Descr = A.DDIVNUM & "-" & A.DDIVNAME).ToList
        Me.DIVISIONBindingSource.DataSource = Div

        If AutoPayEnt.Type = "P" Then
            RecTypeDescription = "Auto-Pay"
            Me.XtraTabPagePayDetail.Text = "Pay Details"
            Me.grpScheduling.Text = "Pay Scheduling"
            Me.OTHERPAYBindingSource.DataSource = (From A In DB.OTHER_PAYS
                                                   Where A.CONUM = EmpRec.CONUM AndAlso (A.OACTIVE = "YES" OrElse A.OTH_PAY_NUM = AutoPayEnt.CL_Code.GetValueOrDefault(-1))
                                                   Order By A.OTH_PAY_NUM).ToList
        Else
            RecTypeDescription = "Auto-Deduction"
            Me.XtraTabPagePayDetail.Text = "Deduction Details"
            Me.grpScheduling.Text = "Deduction Payroll Scheduling"
            Me.DEDUCTIONBindingSource.DataSource = (From A In DB.DEDUCTIONs
                                                    Where A.CONUM = EmpRec.CONUM AndAlso (A.DACTIVE = "YES" OrElse A.DED_NUM = AutoPayEnt.CL_Code.GetValueOrDefault(-1))
                                                    Order By A.DED_NUM).ToList

            Dim Calcs = (From A In DB.CO_CALCS Where A.CONUM = Me.EmpRec.CONUM AndAlso A.DISPLAY = "YES" Order By A.CALC_NAME Select A.CALC_NAME).ToArray
            Me.DedCalcTextEdit.Properties.Items.AddRange(Calcs)

            Dim DedScdls = (From A In DB.emp_deds_amount_schedules
                            Where A.conum = EmpRec.CONUM AndAlso A.empnum = EmpRec.EMPNUM AndAlso A.deduction_key = AutoPayEnt.RecordKey
                            Order By If(A.end_date.HasValue, 2, 1), A.end_date Descending).ToList

            If AutoPayEnt.DedSchedules IsNot Nothing Then
                DedScdls.AddRange(AutoPayEnt.DedSchedules)
            End If

            Me.EmpdedsamountscheduleBindingSource.DataSource = DedScdls

            If Me.IsNew Then
                Me.EmpdedsamountscheduleBindingSource.Add(New emp_deds_amount_schedule With {.start_date = #01/01/1900#})
                AutoPayEnt.SourceEntity = New EMP_DEDS_SETUP With {.EMPNUM = Me.EmpRec.EMPNUM, .CONUM = Me.EmpRec.CONUM,
                    .all_divsd = "NO",
                    .PRIORITYD = 1,
                    .AFTER_TAXD = "NO",
                    .GEN_CHECK = "NO",
                    .medical_indicator = "NO",
                    .county_fips = "", .PAYEE_CASE = "", .ach_prenote = "Pre-Note"}
            End If

            Dim PaymentMethods = New ArrayList(New Dictionary(Of String, String) From {{"NO", "None"}, {"CHK", "Via Check"}, {"ACH", "Via ACH"}})
            Me.PaymentMethodTextEdit.Properties.DisplayMember = "Value"
            Me.PaymentMethodTextEdit.Properties.ValueMember = "Key"
            Me.PaymentMethodTextEdit.Properties.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value"))
            Me.PaymentMethodTextEdit.Properties.DataSource = New BindingSource(PaymentMethods, Nothing)
            LoadPayees()
            Me.grpVendors.Enabled = CType(AutoPayEnt.SourceEntity, EMP_DEDS_SETUP).GEN_CHECK <> "NO"
            Me.grpACH.Enabled = (Me.PaymentMethodTextEdit.EditValue & "") = "ACH"
        End If
        Me.XtraTabPagePaymentInfo.PageVisible = AutoPayEnt.Type = "D"
        Me.pnlPayFields.Visible = AutoPayEnt.Type = "P"

        If pnlPayFields.Visible = False Then
            GridControlDedSchedule.Height = GridControlDedSchedule.Height + pnlPayFields.Height
            lblDedLimitTextEdit.Top = lblDedLimitTextEdit.Top + pnlPayFields.Height
            DedLimitTextEdit.Top = DedLimitTextEdit.Top + pnlPayFields.Height
            lblDedStartDateTextEdit.Top = lblDedStartDateTextEdit.Top + pnlPayFields.Height
            DedStartDateTextEdit.Top = DedStartDateTextEdit.Top + pnlPayFields.Height
            lblDedBalanceTextEdit.Top = lblDedBalanceTextEdit.Top + pnlPayFields.Height
            DedBalanceTextEdit.Top = DedBalanceTextEdit.Top + pnlPayFields.Height
        End If

        Me.pnlDeductionFields.Visible = AutoPayEnt.Type = "D"
        Me.pnlPriorityOrder.Visible = AutoPayEnt.Type = "D"
        Me.AllDivisionsCheckEdit.Visible = AutoPayEnt.Type = "D"
        Me.CL_CodeTextEdit.ReadOnly = Not Me.IsNew
        Me.DeductionNumLookup.ReadOnly = Not Me.IsNew
        Me.btnAddSchedule.Enabled = Not IsNew AndAlso Not AutoPayEnt.IsNewRec
        Me.btnDeleteSchedule.Enabled = Not IsNew AndAlso Me.EmpdedsamountscheduleBindingSource.Count > 1

        Me.EmpAutoPaysBindingSource.DataSource = AutoPayEnt
        If AutoPayEnt.Type = "D" Then
            Me.EMP_DEDS_SETUPBindingSource.DataSource = AutoPayEnt.SourceEntity
            If AutoPayEnt.CL_Code.HasValue Then
                Dim row As DEDUCTION = Me.DeductionNumLookup.Properties.GetDataSourceRowByKeyValue(AutoPayEnt.CL_Code)
                Me.DedLimitTextEdit.Enabled = row.EMP_HAVE_LIMITD = "YES"
                Me.DedStartDateTextEdit.Enabled = Me.DedLimitTextEdit.Enabled AndAlso Me.AutoPayEnt.Limit.HasValue
            End If
        End If
        LoadDepartments()
        Me.XtraTabControl1.SelectedTabPageIndex = 0

        If AutoPayEnt.IsNewRec Then
            Me.Text = $"Add New {RecTypeDescription} for employee"
        Else
            Me.Text = "Edit employee " & RecTypeDescription
        End If

        Me.btnDelete.Enabled = Not Me.IsNew

        Me.txtEmpNum.Text = EmpRec.EMPNUM
        Me.txtEmpName.Text = EmpRec.F_NAME & " " & If(Not String.IsNullOrEmpty(EmpRec.M_NAME), EmpRec.M_NAME & " ", "") & EmpRec.L_NAME
    End Sub

    Sub LoadPayees()
        Dim PayeeID = CType(AutoPayEnt.SourceEntity, EMP_DEDS_SETUP).payee_id
        Dim q = (From A In DB.PAYMENT_ADDRESSes
                 Where A.conum = EmpRec.CONUM OrElse (A.ispublic = "YES" AndAlso Me.chkDisplayPublicVendors.Checked) OrElse A.payee_id = PayeeID OrElse A.payee_descr = "None"
                 Order By A.payee_descr).ToList
        Me.PAYMENT_ADDRESSBindingSource.DataSource = q
        Me.PayeeIDLookUpEdit_Validated(Me, New EventArgs)
    End Sub

    Private Sub frmEmployeeAutoPay_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        If Me.IsNew Then
            If Not Me.AutoPayEnt.CL_DEPT.HasValue Then
                Me.DEPTNUMLookUpEdit.Focus()
            Else
                If Me.RecType = "P"c Then
                    Me.CL_CodeTextEdit.Focus()
                Else
                    Me.DeductionNumLookup.Focus()
                End If
            End If
        End If
    End Sub

    Private Sub DIVNUMLookUpEdit_EditValueChanged(sender As Object, e As EventArgs) Handles DIVNUMLookUpEdit.EditValueChanged
        If Not Equals(DIVNUMLookUpEdit.EditValue, DIVNUMLookUpEdit.OldEditValue) Then
            Me.DEPTNUMLookUpEdit.EditValue = Nothing
            LoadDepartments()
        End If
    End Sub

    Sub LoadDepartments()
        If Me.AutoPayEnt.Divison.HasValue Then
            Me.DEPARTMENTBindingSource.DataSource = (From A In DB.DEPARTMENTs
                                                     Where A.CONUM = EmpRec.CONUM AndAlso A.DIVNUMD = Me.AutoPayEnt.Divison AndAlso (A.DPACTIVE = "YES" OrElse A.DEPTNUM = AutoPayEnt.CL_DEPT.GetValueOrDefault(-1))
                                                     Select A.DEPTNUM, A.DEPT_DESC, Descr = A.DEPTNUM & "-" & A.DEPT_DESC
                                                     ).ToList
        End If
    End Sub

    Private Sub AllPeriodsCheckEdit_CheckedChanged(sender As Object, e As EventArgs) Handles AllPeriodsCheckEdit.CheckedChanged
        Me.Validate()

        If Me.AutoPayEnt.AllPeriods Then
            Me.AutoPayEnt.Prd1 = False
            Me.AutoPayEnt.Prd2 = False
            Me.AutoPayEnt.Prd3 = False
            Me.AutoPayEnt.Prd4 = False
            Me.AutoPayEnt.Prd5 = False
            Me.EmpAutoPaysBindingSource.ResetCurrentItem()
        End If
    End Sub

    Private Sub PrdCheckEdit_CheckedChanged(sender As Object, e As EventArgs) Handles Prd1CheckEdit.CheckedChanged, Prd2CheckEdit.CheckedChanged, Prd3CheckEdit.CheckedChanged, Prd4CheckEdit.CheckedChanged, Prd5CheckEdit.CheckedChanged
        If CType(sender, DevExpress.XtraEditors.CheckEdit).Checked Then
            Me.Validate()
            Me.AutoPayEnt.AllPeriods = False
            Me.EmpAutoPaysBindingSource.ResetCurrentItem()
        End If
    End Sub

    Private Sub GridViewDedSchedule_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridViewDedSchedule.ValidateRow

    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If AutoPayEnt.Type = "D" Then
            Dim schedules As List(Of emp_deds_amount_schedule) = EmpdedsamountscheduleBindingSource.DataSource

            Dim overlaps = (From s1 In schedules Join s2 In schedules On s2.deduction_key Equals s1.deduction_key
                            Where s2.amount_key <> s1.amount_key _
                            AndAlso ((s2.start_date >= s1.start_date AndAlso s2.start_date <= s1.end_date) OrElse (s1.start_date >= s2.start_date AndAlso s1.start_date <= s2.end_date))
                 )
            If overlaps.Count > 0 Then
                MessageBox.Show("Deduction schedule is overlapping", "Deduction Overlap", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If
        End If
        If Me.SaveRecord() Then Me.DialogResult = DialogResult.OK
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to delete this " & Me.RecTypeDescription & " record?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = DialogResult.Yes Then
            Me.SaveRecord(True)
            Me.DialogResult = DialogResult.OK
        End If
    End Sub

    Function SaveRecord(Optional Delete As Boolean = False) As Boolean
        Me.Validate()
        If Not Delete Then
            'Validate
            Dim Errors As New List(Of String)
            If Not Me.AutoPayEnt.Divison.HasValue Then Errors.Add(" - Division Is required")
            If Not Me.AutoPayEnt.CL_DEPT.HasValue Then Errors.Add(" - Department Is required")
            If Not Me.AutoPayEnt.CL_Code.HasValue Then Errors.Add($" - {If(RecType = "P"c, "Pay", "Deduction")} # is required")
            If RecType = "D"c Then
                If Not Me.DedCalcTextEdit.HasValue Then Errors.Add(" - Ded Calc is required")
                Dim Scdl = CType(Me.EmpdedsamountscheduleBindingSource.List, List(Of emp_deds_amount_schedule)).FirstOrDefault
                If Scdl Is Nothing Then
                    Errors.Add("Schedule not loaded")
                Else
                    If Scdl.start_date < #01/01/1900# Then Errors.Add(" - Invalid start date")
                End If
                Dim Ded As EMP_DEDS_SETUP = Me.EMP_DEDS_SETUPBindingSource.Current
                If Ded.GEN_CHECK <> "NO" Then
                    If String.IsNullOrEmpty(CType(Me.PAYMENT_ADDRESSBindingSource.Current, PAYMENT_ADDRESS).payee_name) Then
                        Errors.Add(" - Select a Vendor or change Payment Method")
                    End If
                End If
            Else
                If Not Me.AutoPayEnt.Amount.HasValue Then Errors.Add(" - Amount is required")
            End If
            If Errors.Count > 0 Then
                DisplayMessageBox("Please correct the following errors" & vbCrLf & String.Join(vbCrLf, Errors))
                Return False
            End If
        End If

        If Me.AutoPayEnt.Type = "P" Then
            Dim record As EMP_OP = Me.AutoPayEnt.SourceEntity
            If record Is Nothing Then
                If Me.AutoPayEnt.IsNewRec Then
                    record = New EMP_OP With {.EMPNUM = Me.EmpRec.EMPNUM, .CONUM = Me.EmpRec.CONUM}
                    DB.EMP_OPS.InsertOnSubmit(record)
                Else
                    record = (From A In DB.EMP_OPS Where A.CONUM = Me.EmpRec.CONUM AndAlso A.EMPNUM = Me.EmpRec.EMPNUM AndAlso A.otherpay_key = Me.AutoPayEnt.RecordKey).SingleOrDefault
                End If
            End If
            If Delete Then
                If record IsNot Nothing Then
                    DB.EMP_OPS.DeleteOnSubmit(record)
                End If
            Else
                AutoPayEnt.To_EMP_OP_Record(record)
            End If
        Else
            Dim record As EMP_DEDS_SETUP = Me.AutoPayEnt.SourceEntity
            If Me.IsNew Then
                If Me.AutoPayEnt.IsNewRec Then
                    'record = New EMP_DEDS_SETUP With {.EMPNUM = Me.EmpRec.EMPNUM, .CONUM = Me.EmpRec.CONUM}
                    DB.EMP_DEDS_SETUPs.InsertOnSubmit(record)
                Else
                    record = (From A In DB.EMP_DEDS_SETUPs Where A.CONUM = Me.EmpRec.CONUM AndAlso A.EMPNUM = Me.EmpRec.EMPNUM AndAlso A.deduction_key = Me.AutoPayEnt.RecordKey).SingleOrDefault
                End If
            Else
                If record IsNot Nothing Then
                    'reload to attache to current context
                    record = (From A In DB.EMP_DEDS_SETUPs Where A.CONUM = Me.EmpRec.CONUM AndAlso A.EMPNUM = Me.EmpRec.EMPNUM AndAlso A.deduction_key = Me.AutoPayEnt.RecordKey).SingleOrDefault
                End If
            End If
            If Delete Then
                If record IsNot Nothing Then
                    For Each itm As emp_deds_amount_schedule In Me.EmpdedsamountscheduleBindingSource.List
                        If Not itm.amount_key = Guid.Empty Then
                            DB.emp_deds_amount_schedules.DeleteOnSubmit(itm)
                        End If
                    Next
                    DB.EMP_DEDS_SETUPs.DeleteOnSubmit(record)
                End If
            Else
                AutoPayEnt.To_EMP_DED_Record(record)
                Dim DedRow As DEDUCTION = Me.DeductionNumLookup.Properties.GetDataSourceRowByKeyValue(Me.DeductionNumLookup.EditValue)
                If DedRow.payee_id = record.payee_id Then
                    record.payee_memo = DedRow.payee_memo
                    record.ach_prenote = DedRow.ach_prenote
                    record.GEN_CHECK = DedRow.GENCHK
                End If

                For Each itm As emp_deds_amount_schedule In Me.EmpdedsamountscheduleBindingSource.List
                    If itm.amount.GetValueOrDefault = 0 Then
                        DisplayMessageBox("Schedule with 0 amount will not be saved.")
                        If Not itm.amount_key = Guid.Empty Then
                            DB.emp_deds_amount_schedules.DeleteOnSubmit(itm)
                        End If
                        Continue For
                    End If
                    If itm.amount_key = Guid.Empty Then
                        itm.amount_key = Guid.NewGuid
                        itm.conum = Me.EmpRec.CONUM
                        itm.deduction_key = record.deduction_key
                        itm.empnum = Me.EmpRec.EMPNUM
                        itm.rowguid = Guid.NewGuid
                        DB.emp_deds_amount_schedules.InsertOnSubmit(itm)

                        If AutoPayEnt.DedSchedules Is Nothing Then AutoPayEnt.DedSchedules = New List(Of emp_deds_amount_schedule)
                        AutoPayEnt.DedSchedules.Add(itm)
                    End If
                Next

                Dim curScdl = (From A As emp_deds_amount_schedule In Me.EmpdedsamountscheduleBindingSource.List Where A.start_date <= Today AndAlso (A.end_date Is Nothing OrElse A.end_date > Now)).FirstOrDefault
                If curScdl IsNot Nothing Then
                    Me.AutoPayEnt.Amount = curScdl.amount
                Else
                    Me.AutoPayEnt.Amount = Nothing
                End If
            End If
        End If
        DB.SubmitChanges()
        Return True
    End Function

    Private Sub DeductionNumLookup_Validated(sender As Object, e As EventArgs) Handles DeductionNumLookup.Validated
        If Not Me.DeductionNumLookup.HasValue Then Exit Sub
        Dim row As DEDUCTION = Me.DeductionNumLookup.Properties.GetDataSourceRowByKeyValue(Me.DeductionNumLookup.EditValue)
        Me.AutoPayEnt.CodeDescription = row.DED_DESC
        Me.AutoPayEnt.DedCalc = row.DED_CALC
        Me.AutoPayEnt.Category = row.DCATEGORY
        Me.AutoPayEnt.AllPeriods = row.all_prdsd = "YES"
        Me.AutoPayEnt.FirstCheckOnly = row.first_onlyd = "YES"
        Me.AutoPayEnt.SuppPrd = row.supp_prd = "YES"
        Me.AutoPayEnt.Prd1 = row.period1d = "YES"
        Me.AutoPayEnt.Prd2 = row.period2d = "YES"
        Me.AutoPayEnt.Prd3 = row.period3d = "YES"
        Me.AutoPayEnt.Prd4 = row.period4d = "YES"
        Me.AutoPayEnt.Prd5 = row.period5d = "YES"
        Me.DedLimitTextEdit.Enabled = row.EMP_HAVE_LIMITD = "YES"
        Me.DedStartDateTextEdit.Enabled = Me.DedLimitTextEdit.Enabled AndAlso Me.AutoPayEnt.Limit.HasValue
        If Not Me.DedLimitTextEdit.Enabled Then
            Me.AutoPayEnt.Limit = Nothing
            Me.AutoPayEnt.StartDate = Nothing
        End If
        Me.EmpAutoPaysBindingSource.ResetBindings(False)

        Dim DedSetup As EMP_DEDS_SETUP = Me.EMP_DEDS_SETUPBindingSource.Current
        DedSetup.all_divsd = row.all_divs
        DedSetup.PRIORITYD = row.PRIORITYD.GetValueOrDefault(1)
        DedSetup.AFTER_TAXD = row.AFTER_TAXD
        DedSetup.payee_id = row.payee_id
        Me.EMP_DEDS_SETUPBindingSource.ResetBindings(False)
    End Sub

    Private Sub CL_CodeTextEdit_Validated(sender As Object, e As EventArgs) Handles CL_CodeTextEdit.Validated
        If Not Me.CL_CodeTextEdit.HasValue Then Exit Sub
        Dim row As OTHER_PAY = Me.CL_CodeTextEdit.Properties.GetDataSourceRowByKeyValue(Me.CL_CodeTextEdit.EditValue)
        Me.AutoPayEnt.Category = row.CATEGORY
        Me.AutoPayEnt.AllPeriods = row.all_prds = "YES"
        Me.AutoPayEnt.FirstCheckOnly = row.first_only = "YES"
        Me.AutoPayEnt.SuppPrd = row.supp_pr = "YES"
        Me.AutoPayEnt.Prd1 = row.period1 = "YES"
        Me.AutoPayEnt.Prd2 = row.period2 = "YES"
        Me.AutoPayEnt.Prd3 = row.period3 = "YES"
        Me.AutoPayEnt.Prd4 = row.period4 = "YES"
        Me.AutoPayEnt.Prd5 = row.period5 = "YES"
        Me.LimitTextEdit.Enabled = row.EMP_HAVE_LIMIT = "YES"
        Me.StartDateDateEdit.Enabled = Me.LimitTextEdit.Enabled
        If Not Me.LimitTextEdit.Enabled Then
            Me.AutoPayEnt.Limit = Nothing
            Me.AutoPayEnt.StartDate = Nothing
        End If
        Me.EmpAutoPaysBindingSource.ResetBindings(False)
    End Sub

    Private Sub btnDeleteSchedule_Click(sender As Object, e As EventArgs) Handles btnDeleteSchedule.Click
        Dim itm As emp_deds_amount_schedule = Me.GridViewDedSchedule.GetFocusedRow
        If Not itm.amount_key = Guid.Empty Then
            DB.emp_deds_amount_schedules.DeleteOnSubmit(itm)
        End If
        Me.GridViewDedSchedule.DeleteRow(Me.GridViewDedSchedule.FocusedRowHandle)
        Me.btnDeleteSchedule.Enabled = Not IsNew AndAlso Me.EmpdedsamountscheduleBindingSource.Count > 1
    End Sub

    Private Sub btnAddSchedule_Click(sender As Object, e As EventArgs) Handles btnAddSchedule.Click
        Dim StartDate As Date = Today
        Dim List As List(Of emp_deds_amount_schedule) = Me.EmpdedsamountscheduleBindingSource.List
        Dim LastRec = (From A In List Order By If(A.end_date.HasValue, 2, 1), A.end_date Descending, A.start_date Descending).FirstOrDefault
        If LastRec IsNot Nothing AndAlso Not LastRec.end_date.HasValue Then
            If LastRec.start_date >= StartDate Then
                StartDate = LastRec.start_date.AddDays(1)
            End If
            LastRec.end_date = StartDate.AddDays(-1)
        ElseIf LastRec IsNot Nothing AndAlso LastRec.end_date > Today Then
            If LastRec.start_date > Today Then
                StartDate = LastRec.start_date.AddDays(1)
            End If
            LastRec.end_date = StartDate.AddDays(-1)
        End If

        Dim deductionkey = If(List.Count > 0, List.FirstOrDefault().deduction_key, AutoPayEnt.RecordKey)
        List.Add(New emp_deds_amount_schedule With {.start_date = StartDate, .amount = 0, .deduction_key = deductionkey})
        Me.EmpdedsamountscheduleBindingSource.DataSource = (From A In List Order By If(A.end_date.HasValue, 2, 1), A.end_date Descending).ToList
        Me.btnDeleteSchedule.Enabled = Not IsNew AndAlso Me.EmpdedsamountscheduleBindingSource.Count > 1
    End Sub

    Private Sub PaymentMethodTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles PaymentMethodTextEdit.EditValueChanged
        Me.grpVendors.Enabled = (Me.PaymentMethodTextEdit.EditValue & "") <> "NO"
        Me.grpACH.Enabled = (Me.PaymentMethodTextEdit.EditValue & "") = "ACH"
    End Sub

    Private Sub chkDisplayPublicVendors_CheckedChanged(sender As Object, e As EventArgs) Handles chkDisplayPublicVendors.CheckedChanged
        LoadPayees()
    End Sub

    Private Sub PayeeIDLookUpEdit_Validated(sender As Object, e As EventArgs) Handles PayeeIDLookUpEdit.Validated
        Dim PayeeID = CType(AutoPayEnt.SourceEntity, EMP_DEDS_SETUP).payee_id
        Dim Ix = Me.PAYMENT_ADDRESSBindingSource.List.IndexOf((From A As PAYMENT_ADDRESS In Me.PAYMENT_ADDRESSBindingSource.List Where A.payee_id = PayeeID).FirstOrDefault)
        Me.PAYMENT_ADDRESSBindingSource.Position = Ix
    End Sub

    Private Sub DeductionNumLookup_KeyDown(sender As Object, e As KeyEventArgs) Handles DeductionNumLookup.KeyDown
        Dim editor As DevExpress.XtraEditors.LookUpEdit = sender
        If e.KeyCode = Keys.Enter Then
            editor.DoValidate()
        End If
    End Sub

    Private Sub GetLastPayrollDate()
        If EmpRec Is Nothing Then Exit Sub
        'Dim LastPayrollDate = (From A In From A In DB.PAYROLLs Where A.CONUM = Me.EmpRec.CONUM Select A.CHECK_DATE).Max.GetValueOrDefault(Today)
        Dim LastPayrollDate = (From A In From A In DB.PAYROLLs Where A.CONUM = Me.EmpRec.CONUM Select A.CHECK_DATE).ToList().Max.GetValueOrDefault(Today)
        Me.AutoPayEnt.LimitStartDate = LastPayrollDate.Date
        Me.DedStartDateTextEdit.EditValue = LastPayrollDate.Date
    End Sub

    Private Sub DedLimitTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles DedLimitTextEdit.EditValueChanged
        If Not Me.DedLimitTextEdit.IsModified Then Return
        If Me.DedLimitTextEdit.HasValue Then
            Me.DedStartDateTextEdit.Enabled = True
            If Not Me.AutoPayEnt.LimitStartDate.HasValue Then
                GetLastPayrollDate()
            End If
        Else
            Me.DedStartDateTextEdit.Enabled = False
            Me.AutoPayEnt.LimitStartDate = Nothing
            Me.DedStartDateTextEdit.EditValue = Nothing
        End If
    End Sub

    Private Sub DedStartDateTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles DedStartDateTextEdit.EditValueChanged
        If Me.DedStartDateTextEdit.Enabled AndAlso Not Me.DedStartDateTextEdit.HasValue Then
            GetLastPayrollDate()
            Me.DedStartDateTextEdit.DoValidate()
        End If
    End Sub
End Class