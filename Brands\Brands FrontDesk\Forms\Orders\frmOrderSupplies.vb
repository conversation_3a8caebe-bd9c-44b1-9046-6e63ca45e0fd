﻿Imports System.ComponentModel
Imports System.Data
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraSplashScreen

Public Class frmOrderSupplies
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public ReadOnly Property Conum As Decimal
    Public ReadOnly Property CO_BILL_FREQ As String

    Private _WhiteGloveMode As Boolean = False

    Private Property WhiteGloveMode
        Get
            Return _WhiteGloveMode
        End Get
        Set(value)
            _WhiteGloveMode = value
            GetItems("")
        End Set
    End Property

    Public Sub New(_conum As Decimal)
        InitializeComponent()
        Me.Conum = _conum
        CO_BILL_FREQ = DB.COOPTIONs.Where(Function(c) c.CONUM = Me.Conum).First.CO_BILL_FREQ
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property OrderID As Integer

    Dim DB As New dbEPDataDataContext(GetConnectionString)

    Private Itemslist As List(Of SuppliesOrderItemDisplay)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Company As COMPANY
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property NextPayroll As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsPaperless As String

    Private blnOpenItems As Boolean

    Private Sub frmOrderSupplies_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        'Me.lblCompany.Text = String.Format("{0} - {1}", Company.CONUM, Company.CO_NAME)
        LoadOrder()

        'AddInvoiceItems
        Dim isMemberBilling = DB.fn_FrontDeskUserRoles(UserName).Where(Function(r) r.RoleID = 4).FirstOrDefault?.IsMember
        Dim isMemberInvItemCls = DB.fn_FrontDeskUserRoles(UserName).Where(Function(r) r.RoleID = 17).FirstOrDefault?.IsMember
        Dim isMemberWhiteGlove = DB.fn_FrontDeskUserRoles(UserName).Where(Function(r) r.RoleID = 24).FirstOrDefault?.IsMember

        If isMemberBilling Is Nothing OrElse Not isMemberBilling Then
            xtpManualBilling.PageVisible = False
            lciClass.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            lciDescription.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            lciInfo.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            lciDelivery.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            gvItemGroup.OptionsBehavior.Editable = False
            gvItemClass.OptionsBehavior.Editable = False
            sbSaveGroup.Enabled = False
            btnSaveItemClass.Enabled = False

            If isMemberWhiteGlove IsNot Nothing AndAlso isMemberWhiteGlove Then
                xtpManualBilling.PageVisible = True
                xtpManualBilling.Text = "White Glove"
                WhiteGloveMode = True
                ToggleSwitchWhiteGlove.Visible = False
            End If

            'InvoiceItemClassGroupViewOnly
            If isMemberInvItemCls Is Nothing OrElse Not isMemberInvItemCls Then
                xtpItemGroup.PageVisible = False
                xtpItemClass.PageVisible = False
            End If
        End If

        'Solomon added
        FillDD()
    End Sub

    Sub LoadOrder()
        Dim OrderEnt As SuppliesOrder
        OrderEnt = New SuppliesOrder With {.CoNum = Company.CONUM, .Date = Now, .ByUser = UserName, .NextPayroll = Me.NextPayroll}
        'Dim IsPaperless = CType(My.Forms.frmMain.Panel1.Controls(0), frmTickets).IsPayperless
        OrderEnt.SendWithNextPayroll = Not {"Paperless", "WebPost"}.Contains(IsPaperless)

        Dim payrollList = New List(Of ShipWithPayroll)
        Dim lastPrNum As Integer = 0
        Dim payroll = DB.PAYROLLs.Where(Function(c) c.CONUM = Company.CONUM).OrderByDescending(Function(c) c.PRNUM).FirstOrDefault()
        If payroll IsNot Nothing Then
            lastPrNum = payroll.PRNUM
            Dim LastScan = (From A In DB.view_Deliveries Where A.CoNum = Company.CONUM AndAlso A.PayrollNum = payroll.PRNUM Order By A.ID Descending).FirstOrDefault
            If LastScan Is Nothing OrElse Not LastScan.IsShipped Then
                payrollList.Add(New ShipWithPayroll With {.PrNum = payroll.PRNUM, .PrStatus = payroll.PAYROLL_STATUS})
            End If
        End If
        payrollList.Add(New ShipWithPayroll With {.PrNum = lastPrNum + 1, .PrStatus = "Future Payroll"})
        payrollList.Add(New ShipWithPayroll With {.PrNum = lastPrNum + 2, .PrStatus = "Future Payroll"})
        lueShipWithPayrollNumber.Properties.DataSource = payrollList
        OrderEnt.SendWithPrNum = payrollList.OrderBy(Function(p) p.PrNum).First.PrNum

        Itemslist = Query(Of SuppliesOrderItemDisplay)("SELECT s.ProductID, ai.[ITEM_NUM], ISNULL(ai.[ITEM_NAME], s.ProductName) ProductName, s.QtyPerPack,
                 ISNULL(CASE WHEN [STD_PRICE] = .01 THEN 0 ELSE ISNULL(po.ACTUAL_PRICE, [STD_PRICE]) END,0) PricePerPack,
                 0 PackQty, s.IsClock, CONVERT(bit, 0) WaiveCharge, NULL WaiveChargeReason
                  FROM custom.Supplies s 
                  LEFT OUTER JOIN ACT_ITEMS ai ON ai.ITEM_NUM = s.INV_ITEM_NUM
                  LEFT OUTER JOIN custom.ACT_AUTO_PriceOverrides po ON ai.ITEM_NAME = po.ITEM_NAME and po.CONUM = @Conum
                  WHERE s.IsVisible = 1 AND (IsPromotionalItem = 0 OR custom.fn_UserInRole('PromotionalItemsSupply', @User) = 1) ORDER BY s.Sort", New With {.Conum = Conum, .User = UserName})

        Me.SuppliesOrderItemDisplayBindingSource.DataSource = Itemslist

        Me.SuppliesOrderBindingSource.DataSource = OrderEnt

        SendWithNextPayroll = OrderEnt.SendWithNextPayroll
    End Sub

    Private Sub btnSubmit_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSubmit.Click
        Dim Handle As IOverlaySplashScreenHandle = Nothing
        Try
            SuppliesOrderBindingSource.EndEdit()
            Dim Items = (From A In Itemslist Where A.PackQty > 0).ToList

            If Items.Count = 0 Then
                MessageBox.Show("No Items Selected")
                Return
            ElseIf Itemslist.Where(Function(f) f.ProductName = "Poster" AndAlso f.PackQty > 0).Count > 0 Then
                Dim hasPosterSvc As Boolean = Query(Of Boolean)($"SELECT custom.fn_HasService({Conum}, 'PosterElite', 0)").FirstOrDefault()

                If hasPosterSvc Then
                    Dim annivMonth As String = Query(Of String)($"SELECT DATENAME(MONTH, ppe.Dtm) FROM custom.PaydeckPosterElite ppe WHERE ppe.CoNum = {Conum}").FirstOrDefault()
                    Dim msg = $"The client already subscribed to EUpdate service, which will automatically send them a new All-in-one poster annualy in the month of {annivMonth} " +
                        vbCrLf + "Would you still like to proceed with your order?"

                    If XtraMessageBox.Show(msg, "Already on EUpdate service", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2) = DialogResult.No Then
                        Return
                    End If
                End If

                If Itemslist.Where(Function(f) f.ProductName <> "Poster" AndAlso f.PackQty > 0).Count > 0 Then
                    DisplayErrorMessage("Cannot combine Poster with other items.", Nothing)
                    Return
                ElseIf rgSendWithNextPayroll.EditValue <> 2 Then
                    DisplayErrorMessage("""Don't wait for payroll"" must be Set for Poster", Nothing)
                    Return
                End If
            End If

            If MessageBox.Show("Are you sure?", "Confirm Order", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.No Then
                Return
            End If

            Handle = SplashScreenManager.ShowOverlayForm(Me)

            Dim sOrder As SuppliesOrder = Me.SuppliesOrderBindingSource.Current
            sOrder.Status = "Open"

            sOrder.SendWithNextPayroll = SendWithNextPayroll
            If sOrder.SendWithNextPayroll Then
                Dim payroll = DB.PAYROLLs.FirstOrDefault(Function(p) p.CONUM = sOrder.CoNum AndAlso p.PRNUM = sOrder.SendWithPrNum)
                If payroll Is Nothing OrElse payroll.PAYROLL_STATUS = "Entering Checks" Then
                    sOrder.Status = "Pending"
                End If
            ElseIf rgSendWithNextPayroll.EditValue = 3 Then 'Solomon added on Mar 17, '21.  No delivery, will pickup 
                sOrder.SendWithPrNum = 0
            Else
                sOrder.SendWithPrNum = Nothing
            End If

            Dim shippingAdddressOverride As ShipAddressOverride = Nothing

            If sOrder.SendWithPrNum = 0 Then
                sOrder.DivNum = 0
            Else
                Using frm = New frmOrderShippingAddressSelection(Company.CONUM, sOrder.SendWithNextPayroll)
                    If frm.ShowDialog = DialogResult.Cancel Then
                        SplashScreenManager.CloseOverlayForm(Handle)
                        Exit Sub
                    End If
                    If frm.IsDivionNumberSelected Then
                        sOrder.DivNum = frm.DivionNumber
                    Else
                        shippingAdddressOverride = frm.ShippingAddressOverride
                        sOrder.DivNum = 0
                    End If
                End Using
            End If

            For Each itm In Items
                If itm.PackQty > 0 Then
                    sOrder.SuppliesOrderItems.Add(New SuppliesOrderItem With {.ProductID = itm.ProductID, .Qty = itm.PackQty, .UnitPrice = itm.PricePerPack, .UOM = "Pack", .WaiveCharge = itm.WaiveCharge, .WaiveChargeReason = itm.WaiveChargeReason})
                End If
            Next

            DB.SuppliesOrders.InsertOnSubmit(sOrder)
            DB.SubmitChanges()

            If shippingAdddressOverride IsNot Nothing Then
                shippingAdddressOverride.PRNUM = sOrder.ID
                DB.ShipAddressOverrides.InsertOnSubmit(shippingAdddressOverride)
                DB.SubmitChanges()
            End If

            modSignalRClient.SuppliesOrderUpdated(sOrder)
            Me.OrderID = sOrder.ID

            LoadOrder()
            Dim ClockItem = (From a In sOrder.SuppliesOrderItems Where a.Supply.IsClock = True).FirstOrDefault()
            If ClockItem IsNot Nothing Then
                EmailInvoice(sOrder)
            End If
            SplashScreenManager.CloseOverlayForm(Handle)
            XtraMessageBox.Show($"Your order [{sOrder.ID}] has been submitted To the shipping room")
            'DialogResult = DialogResult.OK
        Catch ex As Exception
            SplashScreenManager.CloseOverlayForm(Handle)
            DisplayErrorMessage("Error saving order", ex)
        End Try
    End Sub

    Private Sub btnOrderHistory_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnOrderHistory.Click
        Dim frm = New frmOrderHistory With {.CoCode = Me.Company.CONUM}
        Dim results = frm.ShowDialog
        frm.Dispose()
    End Sub

    Private Sub SuppliesOrderItemDisplayBindingSource_CurrentItemChanged(ByVal sender As Object, ByVal e As EventArgs) Handles SuppliesOrderItemDisplayBindingSource.CurrentItemChanged
        Me.txtOrderTotal.Text = (From A In Itemslist Select T = A.PackQty * A.PricePerPack).Sum.ToString("C2")
    End Sub

    Private Sub btnCancel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property SendWithNextPayroll As Boolean
        Get
            Return rgSendWithNextPayroll.SelectedIndex = 0
        End Get
        Set(value As Boolean)
            rgSendWithNextPayroll.SelectedIndex = IIf(value, 0, 1)
            lciPayrollNumber.Visibility = value.ToBarItemVisibility
        End Set
    End Property

    Private Sub riPackQty_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs)
        Dim row As SuppliesOrderItemDisplay = GridView1.GetFocusedRow
        If row IsNot Nothing AndAlso row.IsClock Then
            Dim qty As Integer = e.NewValue
            Dim productId As Integer = row.ProductID
            Dim availibleQty As Integer = DB.SuppliesInventories.Where(Function(o) o.ProductId = productId AndAlso Not o.CoNum.HasValue).Count
            If availibleQty < qty Then
                DisplayMessageBox($"Sorry, We only have availible {availibleQty} clock's in system")
                e.Cancel = True
            End If
        End If
    End Sub

    Private Sub rgSendWithNextPayroll_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgSendWithNextPayroll.SelectedIndexChanged
        lciPayrollNumber.Visibility = SendWithNextPayroll.ToBarItemVisibility
    End Sub

    Private Sub riWaiveCharge_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs)
        Dim row As SuppliesOrderItemDisplay = GridView1.GetFocusedRow
        If row IsNot Nothing Then
            Dim waiveCharge As Boolean = e.NewValue
            If waiveCharge Then
                Using frm = New frmWaiveChargeReason
                    If frm.ShowDialog = DialogResult.OK Then
                        row.WaiveChargeReason = frm.meWaiveChargeReason.Text
                    Else
                        e.Cancel = True
                    End If
                End Using
            Else
                row.WaiveChargeReason = Nothing
            End If
        End If
    End Sub

    Private Sub rgSendWithNextPayroll_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs)
        Dim sOrder As SuppliesOrder = Me.SuppliesOrderBindingSource.Current
        'If {"Paperless", "WebPost"}.Contains(IsPaperless) AndAlso e.NewValue = True AndAlso sOrder.ID = 0 Then
        If {"Paperless", "WebPost"}.Contains(IsPaperless) AndAlso e.NewValue = 1 AndAlso sOrder.ID = 0 Then
            DisplayMessageBox("This is a payperless client, can't send with payroll.")
            e.Cancel = True
        End If
    End Sub

    'copy from ManualBilling
    Sub LoadManualBillingTabData()
        Try
            GetItems(Nothing)
            lueServiceClass.EditValue = "<ALL>"

            gcDelivery.DataSource = (From i In DB.dbo_DELIVERies Where i.DEACTIVE = "YES" Select i Order By i.DELDESC).ToList()

            riDivisionLookup.DataSource = (From i In DB.DIVISIONs Where i.CONUM = Conum AndAlso (i.DDIVNUM = 0 OrElse i.DBANKSAME = "NO") Select i.DDIVNUM, i.DDIVNAME, Descr = i.DDIVNUM & "-" & i.DDIVNAME).ToList()
            cbeDivision.Properties.Items.AddRange((From i In DB.DIVISIONs Where i.CONUM = Conum AndAlso (i.DDIVNUM = 0 OrElse i.DBANKSAME = "NO") Select i.DDIVNUM).ToArray())

            'annnonmous type cannot add to it
            'gc_Billing_Overrides.DataSource = From i In DB.ACT_AUTO_PriceOverrides Where i.CONUM = iConum Select i.CONUM, i.DIVNUM, i.ITEM_NUM, i.ACTUAL_PRICE, i.Line_Id
            'create a list of ACT_AUTO_PriceOverrides and can add to it
            'gc_InvoiceLog.DataSource = (From i In DB.INVOICE_xLOGs Where i.CONUM = Conum And 1 = 0).ToList
            Dim listIl As New List(Of IL_with_params)
            gc_InvoiceLog.DataSource = (From i In listIl).ToList

            'Dim actItems As Decimal() = DB.ACT_ITEMs.Select(Function(d) d.ITEM_NUM).ToArray()
            'lueItem.Properties.DataSource = actItems
            lueItem.Properties.DataSource = (From i In DB.ACT_ITEMS Select i.ITEM_NUM, i.ITEM_NAME).ToList()
            lueItem.Properties.DisplayMember = "ITEM_NAME"
            lueItem.Properties.ValueMember = "ITEM_NUM"

            gc_ManualInvoices.DataSource = Query("
SELECT	im.CONUM, ITEM_DATE, ITEM_CAT, ITEM_NUM, PRICE = item_final_price, CHK_ENTRIES = id.item_count, 
		INVOICE_NUM = invoice_number, PRNUM = id.item_prnum,
		INV_SECTION, ITEM_TYPE, ITEM_NAME, DIVNUM, ITEM_FREQ_TYPE = invoice_gen_freq
FROM	invoice_master im 
		INNER JOIN invoice_item_detail id ON id.conum = im.conum AND id.invoice_key = im.invoice_key 
		OUTER APPLY (
			SELECT	ITEM_CAT = list_value 
			FROM	GLOBAL_LISTS gl 
			WHERE	gl.list_name = 'Invoice Report Categories' AND cat_id = id.item_category_id
		) cat
		OUTER APPLY (
			SELECT	INV_SECTION = list_value
			FROM	GLOBAL_LISTS gl 
			WHERE	gl.list_name = 'Invoice Group Sections' AND cat_id = id.item_category_id
		) section
WHERE	ITEM_TYPE = 'Manual' AND ISNULL(im.invoice_deleted, 0) = 0 AND ISNULL(id.item_deleted, 0) = 0 AND id.conum = " + Conum.ToString())
        Catch ex As Exception
            Logger.Error(ex, "Error in LoadManualBillingTabData")
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Sub LoadItemGroupTabData()
        Try
            'GroupNameBindingSource.DataSource = (From i In DB.view_ACT_ITEMs Where i.AACTIVE = "YES" Order By i.ITEM_CAT, i.ITEM_NAME, i.STD_PRICE).ToList()
            GroupNameBindingSource.DataSource = Query("SELECT ai.GroupName, ai.ITEM_NUM, ai.ITEM_NAME, am.Class, am.Description, am.Info
FROM custom.view_ACT_ITEMS ai
LEFT JOIN custom.AccntMapping am 
       ON am.ITEM_NUM = ai.ITEM_NUM
WHERE ai.AACTIVE = 'YES'
ORDER BY ai.ITEM_CAT, ai.ITEM_NAME, ai.STD_PRICE
")
            gcItemGroup.DataSource = GroupNameBindingSource

            ricbGroupName.Items.AddRange((From g In DB.view_ACT_ITEMs Where g.GroupName <> "" Select g.GroupName).Distinct.ToList)
        Catch ex As Exception
            Logger.Error(ex, "Error in LoadItemGroupTabData")
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Sub LoadItemClassTabData()
        Try
            gcItemClass.DataSource = (From am In DB.view_AccntMappings Order By If(am.ITEM_NUM.HasValue, 2, 1) Descending, am.ITEM_NUM, am.Item).ToList()

            '            gcItemClass.DataSource = Query("SELECT	ID, am.ITEM_NUM, am.Item, am.ItemType, am.Active, 
            '		am.Description, am.Account, am.AccountShort, am.Class, am.Info
            'FROM	custom.AccntMapping am
            'ORDER BY CASE WHEN am.ITEM_NUM IS NOT NULL THEN 1 ELSE 2 END, am.ITEM_NUM, am.Item").AsEnumerable().ToList()
        Catch ex As Exception
            Logger.Error(ex, "Error in LoadItemClassTabData")
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Sub GetItems(ServiceClass As String)
        Dim ds As IOrderedQueryable(Of view_ACT_ITEM)
        Dim WhiteGloveBillingItems = GetUdfValueSplitted("WhiteGloveBillingItems")

        If ServiceClass Is Nothing OrElse ServiceClass = "" Then
            ds = Query(Of view_ACT_ITEM)("SELECT	i.*
FROM	custom.view_ACT_ITEMS i
		INNER JOIN GLOBAL_LISTS gl ON gl.list_name = 'Invoice Report Categories' AND gl.cat_id = i.item_category_id
WHERE	(i.ITEM_TYPE = 'Flat' 
			OR gl.list_value IN ('DIRECT DEPOSIT', 'CHECK', 'NEW HIRE', 'SIGN CHECKS', 'STUFF CHECKS', 'W2')
		) 
		AND i.ITEM_NUM > 0 
		AND i.AACTIVE = 'YES'
ORDER BY 
		i.ITEM_NAME, i.STD_PRICE;").AsQueryable()

            If WhiteGloveMode Then
                ds = ds.Where(Function(f) WhiteGloveBillingItems.Contains(f.ITEM_NUM.ToString))
            End If
        Else
            ds = Query(Of view_ACT_ITEM)($"SELECT	i.*
FROM	custom.view_ACT_ITEMS i
		INNER JOIN GLOBAL_LISTS gl ON gl.list_name = 'Invoice Report Categories' AND gl.cat_id = i.item_category_id
		INNER JOIN custom.AccntMapping am ON am.ITEM_NUM = i.ITEM_NUM AND am.Class = '{ServiceClass}'
WHERE	(i.ITEM_TYPE = 'Flat' 
			OR gl.list_value IN ('DIRECT DEPOSIT', 'CHECK', 'NEW HIRE', 'SIGN CHECKS', 'STUFF CHECKS', 'W2')
		) 
		AND i.ITEM_NUM > 0 
		AND i.AACTIVE = 'YES'
ORDER BY 
		i.ITEM_NAME, i.STD_PRICE;").AsQueryable

            If WhiteGloveMode Then
                ds = ds.Where(Function(f) WhiteGloveBillingItems.Contains(f.ITEM_NUM.ToString))
            End If
        End If
        Dim overPrices = DB.ACT_AUTO_PriceOverrides.Where(Function(f) f.CONUM = Conum AndAlso (f.PriceFromQty = 0 OrElse f.PriceFromQty = 1)).ToList()

        Dim dsFinal = New List(Of view_ACT_ITEMWithOverridePrice)
        For Each i In ds
            Dim newItem = New view_ACT_ITEMWithOverridePrice With {.ITEM_CAT = i.ITEM_CAT, .ITEM_NAME = i.ITEM_NAME, .ITEM_NUM = i.ITEM_NUM, .ITEM_TAXABLE = i.ITEM_TAXABLE, .STD_PRICE = i.STD_PRICE, .GroupName = i.GroupName}

            Dim opItem = overPrices.Where(Function(f) f.ITEM_NUM = i.ITEM_NUM).FirstOrDefault

            If opItem IsNot Nothing Then
                newItem.Override = opItem.ACTUAL_PRICE
            End If
            dsFinal.Add(newItem)
        Next

        gcItems.DataSource = dsFinal
        If ds.Where(Function(f) f.GroupName > "").Count > 0 Then
            gvItems.Columns().Where(Function(f) f.FieldName = "GroupName").First().Group()
        Else
            gvItems.Columns().Where(Function(f) f.FieldName = "GroupName").First().UnGroup()
        End If
    End Sub

    'copy from ManualBilling
    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles sbSave.Click
        Dim Handle As IOverlaySplashScreenHandle = Nothing

        Try
            CurRowBingingSource.EndEdit()
            Dim Items = CType(gv_InvoiceLog.DataSource, List(Of IL_with_params)).ToList()
            If Items.Count = 0 Then
                MessageBox.Show("No Items Selected")
                Return
            ElseIf MessageBox.Show("Are you sure?", "Confirm billing item", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.No Then
                Return
            Else
                Dim row As IL_with_params = gv_InvoiceLog.GetRow(Me.gv_InvoiceLog.FocusedRowHandle)
                If Not row Is Nothing AndAlso Not cbeSection.HasValue Then
                    XtraMessageBox.Show("Missing Invoice Section")
                    cbeSection.Focus()
                    Return
                End If
            End If

            Handle = SplashScreenManager.ShowOverlayForm(Me)
            If Not DB.Connection.State = ConnectionState.Open Then
                DB.Connection.Open()
            End If

            Dim paymentTerms As Nullable(Of Integer) = Query(Of Integer)("SELECT glt.cat_id FROM global_lists glt WHERE glt.list_name = 'Invoice Payment Terms' AND glt.list_value = 'Payment Due Upon Receipt'").FirstOrDefault()

            If Not txtNotes.HasValue Then
                'Dim il As INVOICE_xLOG
                For Each item In Items
                    'il = New INVOICE_xLOG With {.CHK_ENTRIES = item.CHK_ENTRIES, .CONUM = item.CONUM, .DIVNUM = item.DIVNUM, .INVOICE_NUM = 0, .INV_SECTION = item.INV_SECTION, .ITEM_CAT = item.ITEM_CAT, .ITEM_DATE = item.ITEM_DATE, .ITEM_FREQ_TYPE = CO_BILL_FREQ, .ITEM_NAME = item.FinalItem, .ITEM_NUM = item.ITEM_NUM, .ITEM_TYPE = "Manual", .PRICE = item.PRICE, .PRNUM = 0, .TAXABLE = item.TAXABLE, .rowguid = Guid.NewGuid}
                    'DB.INVOICE_xLOGs.InsertOnSubmit(il)

                    'Dim ExInv = (From i In DB.invoice_masters Where i.conum = Conum AndAlso i.divnum = item.DIVNUM AndAlso i.invoice_number = 0 AndAlso (i.invoice_deleted = 0 OrElse i.invoice_deleted Is Nothing)).FirstOrDefault
                    Dim ExInv As invoice_master

                    If item.AddToExisingInvoiceKey = Guid.Empty Then
                        ExInv = Billing.BillingUtilities.GetInvoiceNumForCo(Conum, item.DIVNUM, item.ITEM_DATE, 0)
                    Else
                        ExInv = DB.invoice_masters.Where(Function(i) i.conum = Me.Conum AndAlso i.invoice_key = item.AddToExisingInvoiceKey).First()
                    End If

                    Dim sort As Int16 = 1

                    If ExInv Is Nothing Then
                        ExInv = New invoice_master() With {
                            .invoice_key = Guid.NewGuid, .conum = Conum, .invoice_number = 0, .invoice_date = item.ITEM_DATE, .divnum = item.DIVNUM,
                            .invoice_gen_freq = CO_BILL_FREQ, .prnum = 0, .inv_payment_terms_id = nz(paymentTerms, 0), .inv_payment_method = "Direct Deposit",
                            .inv_salestax_exempt = "NO", .inv_display_type = "Detailed", .inv_summ_desc = "Payroll Processing", .invoice_exported = 2,
                            .created_by = UserName, .created_date = DateTime.Now, .rowguid = Guid.NewGuid
                        }
                        DB.invoice_masters.InsertOnSubmit(ExInv)
                    Else
                        Dim lastItem = (From d In DB.invoice_item_details.Where(Function(f) f.conum = ExInv.conum AndAlso f.invoice_key = ExInv.invoice_key) Select d).OrderByDescending(Function(o) o.item_section_sort_order).FirstOrDefault()
                        If Not lastItem Is Nothing Then
                            sort = lastItem.item_section_sort_order + 1
                        End If
                    End If

                    Dim itm = DB.ACT_ITEMS.Where(Function(i) i.ITEM_NUM = item.ITEM_NUM).FirstOrDefault()

                    Dim NewDetail = New invoice_item_detail With {
                        .detail_key = Guid.NewGuid,
                        .conum = Conum, .invoice_key = ExInv.invoice_key, .item_date = item.ITEM_DATE, .item_category_id = itm.item_category_id, .item_num = item.ITEM_NUM,
                        .item_type = "Manual", .item_sales_taxable = "YES",
                        .item_intial_price = 0, .item_final_price = item.PRICE, .item_overide_price = item.PRICE,
                        .item_count = item.CHK_ENTRIES,
                        .item_prnum = ExInv.prnum, .item_group_section_id = itm.invoicerpt_group_section, .item_discount_amt = 0,
                        .item_name = item.FinalItem,
                        .item_section_sort_order = sort, .exclude_from_invminmax = "NO", .item_minxmax_applied = 0, .item_deleted = 0, .created_by = UserName,
                        .created_date = Now, .rowguid = Guid.NewGuid
                    }
                    DB.invoice_item_details.InsertOnSubmit(NewDetail)
                Next
                DB.SubmitChanges()
                SplashScreenManager.CloseOverlayForm(Handle)
                XtraMessageBox.Show($"Your invoice was saved")
            Else
                'copied scripts from load & save.....
                'we may need to change ...
                Dim sOrder As SuppliesOrder
                sOrder = New SuppliesOrder With {.CoNum = Company.CONUM, .Date = Now, .ByUser = UserName, .NextPayroll = Me.NextPayroll, .Notes = Me.txtNotes.EditValue, .GenByManBilling = True}
                sOrder.SendWithNextPayroll = Not {"Paperless", "WebPost"}.Contains(IsPaperless)

                Dim payrollList = New List(Of ShipWithPayroll)
                Dim lastPrNum As Integer = 0
                Dim payroll = DB.PAYROLLs.Where(Function(c) c.CONUM = Company.CONUM).OrderByDescending(Function(c) c.PRNUM).FirstOrDefault()
                If payroll IsNot Nothing Then
                    lastPrNum = payroll.PRNUM
                    Dim LastScan = (From A In DB.view_Deliveries Where A.CoNum = Company.CONUM AndAlso A.PayrollNum = payroll.PRNUM Order By A.ID Descending).FirstOrDefault
                    If LastScan Is Nothing OrElse Not LastScan.IsShipped Then
                        payrollList.Add(New ShipWithPayroll With {.PrNum = payroll.PRNUM, .PrStatus = payroll.PAYROLL_STATUS})
                    End If
                End If
                payrollList.Add(New ShipWithPayroll With {.PrNum = lastPrNum + 1, .PrStatus = "Future Payroll"})
                payrollList.Add(New ShipWithPayroll With {.PrNum = lastPrNum + 2, .PrStatus = "Future Payroll"})
                sOrder.SendWithPrNum = payrollList.OrderBy(Function(p) p.PrNum).First.PrNum

                sOrder.Status = "Open"
                If sOrder.SendWithNextPayroll Then
                    'why are we getting payroll again ??
                    payroll = DB.PAYROLLs.FirstOrDefault(Function(p) p.CONUM = sOrder.CoNum AndAlso p.PRNUM = sOrder.SendWithPrNum)
                    If payroll Is Nothing OrElse payroll.PAYROLL_STATUS = "Entering Checks" Then
                        sOrder.Status = "Pending"
                    End If
                ElseIf rgSendWithNextPayroll.EditValue = 3 Then 'Solomon added on Mar 17, '21.  No delivery, will pickup 
                    sOrder.SendWithPrNum = 0
                Else
                    'was set before ??
                    sOrder.SendWithPrNum = Nothing
                End If

                Dim shippingAdddressOverride As ShipAddressOverride = Nothing

                If sOrder.SendWithPrNum = 0 Then
                    sOrder.DivNum = 0
                Else
                    Using frm = New frmOrderShippingAddressSelection(Company.CONUM, sOrder.SendWithNextPayroll)
                        If frm.ShowDialog = DialogResult.Cancel Then
                            SplashScreenManager.CloseOverlayForm(Handle)
                            Exit Sub
                        End If
                        If frm.IsDivionNumberSelected Then
                            sOrder.DivNum = frm.DivionNumber
                        Else
                            shippingAdddressOverride = frm.ShippingAddressOverride
                            sOrder.DivNum = 0
                        End If
                    End Using
                End If

                Dim itm As IL_with_params
                For Each itm In Items
                    If itm.CHK_ENTRIES > 0 Then
                        Dim supply = DB.Supplies.Where(Function(s) s.INV_ITEM_NUM = itm.ITEM_NUM).FirstOrDefault
                        Dim ProdId = IIf(Not supply Is Nothing, supply?.ProductID, -999)
                        Dim ItemDesc = IIf(supply Is Nothing OrElse itm.FinalItem <> itm.ITEM_NAME, itm.FinalItem, Nothing)
                        Dim ItemNum = IIf(ProdId = -999, itm.ITEM_NUM, Nothing)
                        sOrder.SuppliesOrderItems.Add(New SuppliesOrderItem With {.ProductID = ProdId, .Qty = itm.CHK_ENTRIES, .UnitPrice = itm.UnitPrice, .UOM = "Pack", .WaiveCharge = 0, .WaiveChargeReason = Nothing, .ItemDescription = ItemDesc, .ITEM_NUM = ItemNum})
                    End If
                Next

                DB.SuppliesOrders.InsertOnSubmit(sOrder)
                DB.SubmitChanges()

                If shippingAdddressOverride IsNot Nothing Then
                    shippingAdddressOverride.PRNUM = sOrder.ID
                    DB.ShipAddressOverrides.InsertOnSubmit(shippingAdddressOverride)
                    DB.SubmitChanges()
                End If

                modSignalRClient.SuppliesOrderUpdated(sOrder)
                Me.OrderID = sOrder.ID
                LoadOrder()
                blnOpenItems = False
                SplashScreenManager.CloseOverlayForm(Handle)
                XtraMessageBox.Show($"Your order [{sOrder.ID}] has been saved")
            End If
            'DialogResult = DialogResult.OK
        Catch ex As Exception
            SplashScreenManager.CloseOverlayForm(Handle)
            Logger.Error(ex, "Error in OrderSupplies_btnSave_Click")
            DisplayErrorMessage("Error saving data", ex)
        End Try
        gc_InvoiceLog.DataSource = New List(Of IL_with_params)
        CurRowBingingSource.DataSource = New IL_with_params With {.ITEM_NAME = "", .Qty = 0, .UnitPrice = 0}
        txtNotes.Text = ""

    End Sub

    'copy from ManualBilling
    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles sbDelete.Click
        Try
            'remove focus from delete button
            gv_InvoiceLog.Focus()
            Dim item As IL_with_params = Me.gv_InvoiceLog.GetFocusedRow
            If item Is Nothing Then Exit Sub

            If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you want to delete item{vbCrLf}""{item.ITEM_NAME}""", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.Yes Then
                Me.gv_InvoiceLog.DeleteRow(Me.gv_InvoiceLog.FocusedRowHandle)
                blnOpenItems = True
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in OrderSupplies_btnDelete_Click")
            DisplayErrorMessage("Error deleting item", ex)
        End Try
    End Sub

    'copy from ManualBilling
    Private Sub gcItems_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles gcItems.MouseDoubleClick
        Try
            Dim row As IL_with_params = gv_InvoiceLog.GetRow(Me.gv_InvoiceLog.FocusedRowHandle)
            If Not row Is Nothing AndAlso Not cbeSection.HasValue Then
                XtraMessageBox.Show("Missing Invoice Section")
                cbeSection.Focus()
                Return
            End If

            Dim hi = Me.gvItems.CalcHitInfo(e.Location)
            If hi.RowHandle >= 0 Then
                Dim item = CType(gvItems.GetRow(hi.RowHandle), view_ACT_ITEMWithOverridePrice)

                Dim NewItem = New IL_with_params With {.ITEM_NUM = item.ITEM_NUM, .ITEM_NAME = item.ITEM_NAME, .UnitPrice = nz(item.Override, item.STD_PRICE), .CONUM = Conum, .ITEM_CAT = item.ITEM_CAT, .TAXABLE = item.ITEM_TAXABLE, .DIVNUM = 0, .Qty = 1, .ITEM_DATE = Date.Today, .INV_SECTION = "Miscellaneous"}
                CType(gc_InvoiceLog.DataSource, List(Of IL_with_params)).Add(NewItem)
                gv_InvoiceLog.RefreshData()
                gv_InvoiceLog.MoveLast()
                txtUnitPrice.EditValue = NewItem.PRICE

                Dim clsRow = DB.AccntMappings.Where(Function(r) r.ITEM_NUM = item.ITEM_NUM).FirstOrDefault()

                If clsRow IsNot Nothing Then
                    txtFormItemClass.EditValue = clsRow.Class
                    txtFormItemDescription.EditValue = clsRow.Description
                    txtFormItemInfo.EditValue = clsRow.Info
                End If

                blnOpenItems = True
                CheckIfAddToExistingInvoice(NewItem)
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in OrderSupplies_gcItems_MouseDoubleClick")
            DisplayErrorMessage("Error in mouse double click", ex)
        End Try
    End Sub

    'copy from ManualBilling
    Private Sub gcDelivery_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles gcDelivery.MouseDoubleClick
        Try
            Dim row As IL_with_params = gv_InvoiceLog.GetRow(Me.gv_InvoiceLog.FocusedRowHandle)
            If Not row Is Nothing AndAlso Not cbeSection.HasValue Then
                XtraMessageBox.Show("Missing Invoice Section")
                cbeSection.Focus()
                Return
            End If

            Dim hi = Me.gvDeliveries.CalcHitInfo(e.Location)
            If hi.RowHandle >= 0 Then
                Dim deliv = CType(gvDeliveries.GetRow(hi.RowHandle), dbo_DELIVERY)

                Dim NewItem = New IL_with_params With {.ITEM_NUM = -1, .ITEM_NAME = deliv.DELDESC, .UnitPrice = deliv.STD_PRICE, .CONUM = Conum, .ITEM_CAT = "Delivery", .INV_SECTION = "Delivery", .TAXABLE = "YES", .DIVNUM = 0, .Qty = 1, .ITEM_DATE = Date.Now}
                CType(gv_InvoiceLog.DataSource, List(Of IL_with_params)).Add(NewItem)
                gv_InvoiceLog.RefreshData()
                gv_InvoiceLog.MoveLast()
                txtUnitPrice.EditValue = NewItem.PRICE
                blnOpenItems = True
                CheckIfAddToExistingInvoice(NewItem)
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in OrderSupplies_gcDelivery_MouseDoubleClick")
            DisplayErrorMessage("Error in mouse double click", ex)
        End Try
    End Sub

    'copy from ManualBilling
    Private Sub gv_Billing_Overrides_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles gv_InvoiceLog.FocusedRowChanged
        Dim row As IL_with_params = Me.gv_InvoiceLog.GetRow(e.FocusedRowHandle)
        If Not row Is Nothing Then
            CurRowBingingSource.DataSource = row

            Dim clsRow = DB.AccntMappings.Where(Function(r) r.ITEM_NUM = row.ITEM_NUM).FirstOrDefault()

            If clsRow IsNot Nothing Then
                txtFormItemClass.EditValue = clsRow.Class
                txtFormItemDescription.EditValue = clsRow.Description
                txtFormItemInfo.EditValue = clsRow.Info
            End If
        End If
    End Sub

    Private Function FillDD()
        Dim CoPayrolls = (From A In DB.PAYROLLs
                          Where A.CONUM = Conum
                          Select New With {.PrNum = CStr(A.PRNUM), .CheckDate = A.CHECK_DATE, .PrDescr = A.PR_DESCR, .PrNumber = A.PRNUM}).OrderByDescending(Function(o) o.PrNumber).ToList
        Dim dt As Date?
        CoPayrolls.Insert(0, New With {.PrNum = "", .CheckDate = dt, .PrDescr = "", .PrNumber = CDec(0)})
        Dim list = (From A In CoPayrolls Select A.PrNum, A.CheckDate, Value = String.Format("{0}  {1} {2}", A.PrNum, If(A.CheckDate.HasValue, A.CheckDate.Value.ToShortDateString, ""), A.PrDescr)).ToList

        luePrNum.Properties.DataSource = list
        luePrNum.Properties.DisplayMember = "PrNum"
        luePrNum.Properties.ValueMember = "PrNum"
        luePrNum.Properties.Columns.Add(New LookUpColumnInfo("Value", 50))
        luePrNum.Properties.PopupWidth = 50
        luePrNum.Properties.NullText = String.Empty
        luePrNum.Properties.ShowHeader = False

        'Dim years = DB.INVOICE_xLOGs.Select(Function(d) d.ITEM_DATE.Year).Distinct().OrderByDescending(Function(s) s).ToArray()
        Dim years = DB.invoice_masters.Where(Function(d) d.invoice_date.HasValue).Select(Function(d) d.invoice_date.Value.Year).Distinct().OrderByDescending(Function(s) s).ToArray()
        cbeYear.Properties.Items.AddRange(years)
        cbeYear.Properties.Items.Insert(0, "")
    End Function

    Public Shared Sub ChargeShippingForPaperlessClients(db As dbEPDataDataContext, barCode As String)
        Dim split = barCode.Split("-")
        Dim coNum As Decimal = split(0)
        Dim prnum As Integer = split(1)
        If prnum < 10000 Then
            If {"Paperless", "WebPost"}.Contains(db.fn_GetIsPaperless(coNum)) Then
                Dim order = New SuppliesOrder With {.Date = Now, .ByUser = UserName, .CoNum = coNum, .SendWithNextPayroll = False, .Status = "Completed", .Notes = $"This order was automatically generated by the system, because the client is paperless and a payroll [{prnum}] was scanned to be delivered."}
                Dim item = db.Supplies.Single(Function(i) i.ProductID = 19)
                order.SuppliesOrderItems.Add(New SuppliesOrderItem With {.ProductID = 19, .UnitPrice = item.PricePerPack, .Qty = 1, .UOM = "Pack"})
                db.SuppliesOrders.InsertOnSubmit(order)
            End If
        End If
    End Sub

    Public Function EmailInvoice(Order As SuppliesOrder) As Boolean
        Dim report = New ReportProcessor(Order.CoNum, "Payroll Cover Sheet", FileType.Pdf) With {
            .showParametersForm = False,
            .showInRecentReports = False,
            .LastPrNum = Order.ID}
        Dim result = report.ProcessReport()
        Dim Attach = result.Paths.Single

        Dim Settings = DB.FrontDeskOptions.First
        Dim ToAdresses = New List(Of String)
        'ToAdresses.AddRange(Settings.SuppliesOrdersEmailDeliveryTicketTo.Split(";"))
        ToAdresses.AddRange(Settings.SuppliesOrdersEmailOrderTo.Split(";"))

        Using MM As New Net.Mail.MailMessage
            MM.From = ReportFunctions.GetSenderAddress(DB)
            MM.Subject = $"Co#: {Order.CoNum} - {Settings.SuppliesOrdersEmailDeliveryTicketSubject}"
            MM.IsBodyHtml = True
            MM.Body = MM.Body.Replace(vbCrLf, vbCrLf & "<br/>")
            For Each item In ToAdresses
                If item.Length > 0 AndAlso item.Contains("@") Then
                    MM.To.Add(item)
                End If
            Next
            MM.CC.Add(MM.From)
            MM.Attachments.Add(New Net.Mail.Attachment(Attach))
            Using Client As New Net.Mail.SmtpClient()
                Client.Send(MM)
            End Using
            Return True
        End Using
    End Function

    Public Class ShipWithPayroll
        Public Property PrNum As Decimal
        Public Property PrStatus As String

        Public ReadOnly Property Display
            Get
                Return $"{PrNum} - {PrStatus}"
            End Get
        End Property
    End Class

    Private Sub Item_Rel_Controls_EditValueChanged(sender As Object, e As EventArgs) Handles txtItem.EditValueChanged, txtVar2.EditValueChanged, txtVar1.EditValueChanged, luePrNum.EditValueChanged, cbeYear.EditValueChanged, cbeQtr.EditValueChanged, cbeMonth.EditValueChanged, txtUnitPrice.EditValueChanged, txtEntries.EditValueChanged, deDate.EditValueChanged, cbeSection.EditValueChanged, cbeDivision.EditValueChanged
        If Not TypeOf (CurRowBingingSource.DataSource) Is IL_with_params Then
            Return
        End If

        If Not CType(e, ChangingEventArgs).IsBoundUpdatingEditValue Then
            Dim liwp = CType(CurRowBingingSource.DataSource, IL_with_params)
            If sender Is txtVar1 Then
                liwp.Var1 = txtVar1.EditValue
            ElseIf sender Is luePrNum Then
                liwp.PrNum2 = luePrNum.EditValue
            ElseIf sender Is cbeMonth Then
                liwp.Mo = cbeMonth.EditValue
            ElseIf sender Is cbeQtr Then
                liwp.Qtr = cbeQtr.EditValue
            ElseIf sender Is cbeYear Then
                liwp.Year = cbeYear.EditValue
            ElseIf sender Is txtVar2 Then
                liwp.Var2 = txtVar2.EditValue
            ElseIf sender Is cbeDivision Then
                liwp.DIVNUM = cbeDivision.EditValue
                CheckIfAddToExistingInvoice(liwp)
            ElseIf sender Is txtEntries Then
                liwp.Qty = txtEntries.EditValue
            ElseIf sender Is txtUnitPrice Then
                liwp.UnitPrice = txtUnitPrice.EditValue
            ElseIf sender Is cbeSection Then
                liwp.INV_SECTION = cbeSection.EditValue
            ElseIf sender Is deDate Then
                CheckIfAddToExistingInvoice(liwp)
                Return
            Else
                Return
            End If
            CurRowBingingSource.ResetBindings(False)
            gv_InvoiceLog.RefreshData()
        End If
    End Sub

    Sub CheckIfAddToExistingInvoice(row As IL_with_params)
        If CO_BILL_FREQ = "Per Month" Then
            Dim ExInv = Billing.BillingUtilities.GetInvoiceNumForCo(Conum, cbeDivision.EditValue, deDate.EditValue, 1, 1)
            If ExInv Is Nothing AndAlso Not row.AddToExisingInvoiceKey = Nothing Then
                row.AddToExisingInvoiceKey = Nothing
            ElseIf ExInv IsNot Nothing AndAlso row.AddToExisingInvoiceKey = Nothing Then
                If MessageBox.Show("Would you like to add this new item to the existing invoice", "Existing Invoice Found", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2) = DialogResult.Yes Then
                    row.AddToExisingInvoiceKey = ExInv.invoice_key
                End If
            End If
        End If
    End Sub

    Private Sub sliNoteAlert_CustomDraw(sender As Object, e As DevExpress.XtraLayout.ItemCustomDrawEventArgs) Handles sliNoteAlert.CustomDraw
        e.Item.PaintAppearanceItemCaption.ForeColor = Color.Red
    End Sub

    Public Class IL_with_params
        'Inherits INVOICE_xLOG

        Public Property CONUM As Decimal
        Public Property ITEM_DATE As Date
        Public Property ITEM_CAT As String
        Public Property ITEM_NUM As Decimal
        Public Property PRICE As System.Nullable(Of Decimal)
        Public Property CHK_ENTRIES As System.Nullable(Of Decimal)
        Public Property TAXABLE As String
        Public Property INVOICE_NUM As System.Nullable(Of Decimal)
        Public Property PRNUM As Decimal
        Public Property INV_SECTION As String
        Public Property ITEM_TYPE As String
        Public Property ITEM_NAME As String
        Public Property DIVNUM As System.Nullable(Of Decimal)
        Public Property ITEM_FREQ_TYPE As String
        Public Property timestamp As Byte()
        Public Property rowguid As System.Guid


        Public Property Var1 As String
        Public Property PrNum2 As String
        Public Property Mo As String
        Public Property Qtr As String
        Public Property Year As String
        Public Property Var2 As String
        Private _UnitPrice As Decimal

        Public Property AddToExisingInvoiceKey As Guid

        Private Function addPropToItem(item As String, prop As String, cat As String) As String
            If Not item.Contains("~") Then
                item += "~" + cat + prop
            Else
                item += " " + cat + prop
            End If

            Return item
        End Function

        Public Property UnitPrice As Decimal
            Get
                Return _UnitPrice
            End Get
            Set(value As Decimal)
                _UnitPrice = value
                PRICE = _UnitPrice * CHK_ENTRIES
            End Set
        End Property

        Public Property Qty As Decimal
            Get
                Return CHK_ENTRIES
            End Get
            Set(value As Decimal)
                CHK_ENTRIES = value
                PRICE = _UnitPrice * CHK_ENTRIES
            End Set
        End Property

        Public ReadOnly Property FinalItem As String
            Get
                Dim finalValue As String = ITEM_NAME

                If Not IsNullOrWhiteSpace(Var1) Then finalValue = addPropToItem(finalValue, Var1, "")
                If Not IsNullOrWhiteSpace(PrNum2) Then finalValue = addPropToItem(finalValue, PrNum2, "Pr#")
                If Not IsNullOrWhiteSpace(Mo) Then finalValue = addPropToItem(finalValue, Mo, "")
                If Not IsNullOrWhiteSpace(Qtr) Then finalValue = addPropToItem(finalValue, Qtr, "")
                If Not IsNullOrWhiteSpace(Year) Then finalValue = addPropToItem(finalValue, Year, "")
                If Not IsNullOrWhiteSpace(Var2) Then finalValue = addPropToItem(finalValue, Var2, "")

                Return finalValue
            End Get
        End Property

        Public ReadOnly Property CharsLeft As Int16
            Get
                Return 75 - FinalItem.Length
            End Get
        End Property
    End Class

    Private Sub sbSaveGroup_Click(sender As Object, e As EventArgs) Handles sbSaveGroup.Click
        Try
            DB.SaveChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error in sbSaveGroup_Click", ex)
        End Try
    End Sub

    Private Sub gvItemGroup_RowUpdated(sender As Object, e As RowObjectEventArgs) Handles gvItemGroup.RowUpdated
        gvItemGroup.PostEditor()
        Dim ItemNum As Decimal = DirectCast(gvItemGroup.GetFocusedRow(), DataRowView).Row("Item_Num")
        Dim GroupName = DirectCast(gvItemGroup.GetFocusedRow(), DataRowView).Row("GroupName")
        Dim ig1 As Act_Items_Detail = DB.Act_Items_Details.Where(Function(f) f.ITEM_NUM = ItemNum).First
        ig1.GroupName = If(IsDBNull(GroupName), Nothing, GroupName)
    End Sub

    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs) Handles XtraTabControl1.SelectedPageChanged
        If e.Page.Equals(xtpManualBilling) Then
            LoadManualBillingTabData()
            lueServiceClass.Properties.DataSource = Query("SELECT DISTINCT am.Class FROM custom.AccntMapping am WHERE am.Class IS NOT NULL
UNION ALL SELECT '<ALL>'
ORDER BY am.Class")
            lueServiceClass.Properties.ValueMember = "Class"
            lueServiceClass.Properties.DisplayMember = "Class"
        ElseIf e.Page.Equals(xtpItemGroup) Then
            LoadItemGroupTabData()
        ElseIf e.Page.Equals(xtpItemClass) Then
            LoadItemClassTabData()
        End If
    End Sub

    Private Sub btnSaveItemClass_Click(sender As Object, e As EventArgs) Handles btnSaveItemClass.Click
        Try
            DB.SaveChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error in btnSaveItem_Click", ex)
        End Try
    End Sub

    Private Sub lueServiceClass_EditValueChanged(sender As Object, e As EventArgs) Handles lueServiceClass.EditValueChanged
        If lueServiceClass.HasValue AndAlso lueServiceClass.EditValue <> "<ALL>" Then
            GetItems(lueServiceClass.EditValue)
        Else
            GetItems(Nothing)
        End If
    End Sub

    Private Sub gvItems_FocusedRowChanged(sender As Object, e As FocusedRowChangedEventArgs) Handles gvItems.FocusedRowChanged
        Dim row As view_ACT_ITEMWithOverridePrice
        row = gvItems.GetRow(gvItems.FocusedRowHandle)
        If row Is Nothing Then
            txtItemClass.EditValue = ""
            txtItemDescripton.EditValue = ""
            txtItemInfo.EditValue = ""

            Return
        End If
        Dim clsRow = DB.AccntMappings.Where(Function(r) r.ITEM_NUM = row.ITEM_NUM).FirstOrDefault()

        If clsRow IsNot Nothing Then
            txtItemClass.EditValue = clsRow.Class
            txtItemDescripton.EditValue = clsRow.Description
            txtItemInfo.EditValue = clsRow.Info
        End If
    End Sub

    Private Sub ToggleSwitchWhiteGlove_Toggled(sender As Object, e As EventArgs) Handles ToggleSwitchWhiteGlove.Toggled
        WhiteGloveMode = ToggleSwitchWhiteGlove.EditValue
    End Sub
End Class
Class view_ACT_ITEMWithOverridePrice
    Property ITEM_NUM As Decimal
    Property STD_PRICE As Decimal?
    Property Override As Decimal?
    Property ITEM_CAT As String
    Property ITEM_NAME As String
    Property ITEM_TAXABLE As String
    Property GroupName As String
End Class