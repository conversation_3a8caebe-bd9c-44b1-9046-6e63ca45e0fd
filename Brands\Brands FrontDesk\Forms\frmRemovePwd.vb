﻿Public Class frmRemovePwd
    Private _isLoading As Boolean = True

    Private _coNum As Decimal?
    Private Property db As dbEPDataDataContext
    Private Property frmLogger As Serilog.ILogger

    Sub New()
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmRemovePwd)()
    End Sub

    Sub New(coNum As Decimal)
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmRemovePwd)()
        _coNum = coNum
    End Sub

    Private Sub frmRemovePwd_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        BindingSource1.DataSource = db.view_CompanySumarries.ToList()
        If _coNum.HasValue Then
            sluCoNum.EditValue = _coNum
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you would like to remove the password?", "Remove Password?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Return
            End If

            If _coNum IsNot Nothing Then
                frmLogger.Information("Removing password from Co#: {CoNum}", _coNum)
                db.ExecuteCommand($"UPDATE COMPANY SET PR_PASSWORD = '' WHERE CoNum = {_coNum}")
                tePR_PASSWORD.EditValue = Nothing
                If db.SaveChanges Then sluCoNum.EditValue = Nothing
                Dim ach As New Ach.frmAchTransactions()
                ach.EmailSend(_coNum)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error removing password", ex)
        End Try
    End Sub

    Private Sub sluCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles sluCoNum.EditValueChanged
        Try
            _isLoading = True
            _coNum = If(sluCoNum.EditValue = Nothing OrElse sluCoNum.EditValue.ToString() = "", Nothing, Decimal.Parse(sluCoNum.EditValue))
            If sluCoNum.EditValue IsNot Nothing AndAlso sluCoNum.EditValue.ToString().IsNotNullOrWhiteSpace AndAlso sluCoNum.Text.IsNotNullOrWhiteSpace Then
                frmLogger.Debug("Loading Co#: {CoNum}", _coNum)
                tePR_PASSWORD.EditValue = Query(Of String)($"SELECT PR_PASSWORD FROM COMPANY WHERE CONUM = {_coNum}").FirstOrDefault()
                LoadAchTran(_coNum)
            Else
                GridControlAchTran.DataSource = Nothing
                _coNum = Nothing
                tePR_PASSWORD.EditValue = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading company.", ex)
        Finally
            _isLoading = False
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Async Sub LoadAchTran(CoNum As Decimal)
        Try
            Dim Q = From A In db.AchTransactionsLogs Where Not A.IsDeleted AndAlso A.CoNum = CoNum

            Me.GridControlAchTran.DataSource = Await Threading.Tasks.Task.Run(Function() Q.ToList)

            If GridViewAchTran.SortInfo.Count = 0 Then
                If Me.GridViewAchTran.Columns("ID") IsNot Nothing AndAlso Me.GridViewAchTran.Columns("ID").Visible Then
                    Me.GridViewAchTran.Columns("ID").Visible = False
                End If

                GridViewAchTran.SortInfo.Add(New DevExpress.XtraGrid.Columns.GridColumnSortInfo(GridViewAchTran.Columns("DateReceived"), DevExpress.Data.ColumnSortOrder.Descending))

                Me.GridViewAchTran.Columns("Status").VisibleIndex = 4

                Me.GridViewAchTran.Columns("DateReceived").BestFit()
                Me.GridViewAchTran.Columns("CoNum").BestFit()
                Me.GridViewAchTran.Columns("EntryDescType").BestFit()
                Me.GridViewAchTran.Columns("RejectionCode").BestFit()
                Me.GridViewAchTran.Columns("Status").BestFit()
                Me.GridViewAchTran.Columns("DebitAmount").BestFit()
                Me.GridViewAchTran.Columns("CreditAmount").BestFit()
                GridViewAchTran.ActiveFilterString = "[EmpNum] Is Null And [DebitAmount] Is Not Null"
                GridViewAchTran.FocusedRowHandle = 0
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub
End Class