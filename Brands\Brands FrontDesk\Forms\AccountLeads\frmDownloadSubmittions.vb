﻿Imports Brands.Core.AccountLeads.Models
Imports DevExpress.XtraEditors

Public Class frmDownloadSubmittions
    Public Sub New(id As String)
        InitializeComponent()
        If id.IsNotNullOrWhiteSpace Then
            XtraTabControl1.SelectedTabPageIndex = 1
            TextEdit1.Text = id
        End If
    End Sub

    Private Sub frmDownloadSubmittions_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DateEdit1.DateTime = GetUdfValue("JotForms Api Last Download")
    End Sub

    Private Async Sub btnDownloadByDate_ClickAsync(sender As Object, e As EventArgs) Handles btnDownloadByDate.Click
        Await Download()
    End Sub

    Private Async Sub btnDownloadById_ClickAsync(sender As Object, e As EventArgs) Handles btnDownloadById.Click
        Await Download()
    End Sub

    Private Async Function Download() As Threading.Tasks.Task
        Try
            Dim apiKey = GetUdfValue("JotForms Api")
            Dim formId = GetUdfValue("JotForms FormId")
            Dim json = GetUdfValue("JotForms Field Mapping")
            Dim fieldMappings = Newtonsoft.Json.JsonConvert.DeserializeObject(Of List(Of FieldMap))(json)
            json = GetUdfValue("JotForms Parameter Mapping")
            Dim parameterMappings = Newtonsoft.Json.JsonConvert.DeserializeObject(Of List(Of FieldMap))(json)

            Dim api = New Brands.Core.AccountLeads.AccountLeadsApi(apiKey, Logger)

            Dim formSubmissions = New List(Of FormSubmissions)

            If XtraTabControl1.SelectedTabPageIndex = 0 Then
                Dim questions = Await api.GetFormsSubmissionsAsync(formId, DateEdit1.DateTime)
                formSubmissions = questions.Content
            Else
                Dim q = Await api.GetSubmissionsAsync(TextEdit1.Text)
                formSubmissions.Add(q.Content)
            End If
            If XtraTabControl1.SelectedTabPageIndex = 0 Then SetUdfValue("JotForms Api Last Download", DateTime.Now)

            For Each item In formSubmissions
                api.InsertOrUpdateLead(GetConnectionString, item, fieldMappings, parameterMappings)
            Next

            If Not formSubmissions.Any() Then
                XtraMessageBox.Show("No submittions to download")
            Else
                XtraMessageBox.Show($"Downloaded {formSubmissions.Count} submittions")
            End If

            DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error downloading data", ex)
        End Try
    End Function


End Class