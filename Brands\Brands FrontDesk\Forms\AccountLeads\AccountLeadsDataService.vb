﻿Public Class AccountLeadsDataService

    Public Function UpdateStatus(row As view_AccountLead, newStatus As String) As Boolean
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim lead = db.AccountLeads.Single(Function(al) al.ID = row.ID)
            lead.Status = newStatus
            row.Status = newStatus
            Return db.SaveChanges()
        End Using
    End Function

End Class
