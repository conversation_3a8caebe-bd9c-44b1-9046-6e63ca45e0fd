﻿Imports System.ComponentModel

Public Class frmHoursOnly

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property PrNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property EmpNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property DeptNum As Decimal

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Data As List(Of pr_batch_hours_only)

    Dim _db As dbEPDataDataContext

    Private Sub frmHoursOnly_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        _db = New dbEPDataDataContext(GetConnectionString)
        Me.OTHERPAYBindingSource.DataSource = (From A In _db.OTHER_PAYS Where A.CONUM = CoNum Order By A.OTH_PAY_NUM).ToList
        Data = (From A In _db.pr_batch_hours_only
                Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum AndAlso A.EMPNUM = Me.EmpNum AndAlso A.DEPTNUM = Me.DeptNum
                Order By A.PayCode).ToList
        Me.PrbatchhoursonlyBindingSource.DataSource = Data
    End Sub

    Private Sub GridView1_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete Note", AddressOf OnDeleteRowClick)
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As pr_batch_hours_only = Me.GridView1.GetRow(rowHandle)
        Me.GridView1.DeleteRow(rowHandle)
        If Row IsNot Nothing AndAlso Not Row.rowguid.Equals(Guid.Empty) Then
            _db.pr_batch_hours_only.DeleteOnSubmit(Row)
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        For Each itm In Me.Data
            If itm.Hours.GetValueOrDefault = 0 Then
                If Not itm.rowguid.Equals(Guid.Empty) Then
                    _db.pr_batch_hours_only.DeleteOnSubmit(itm)
                End If
            Else
                If itm.rowguid.Equals(Guid.Empty) Then
                    itm.rowguid = Guid.NewGuid
                    itm.CONUM = Me.CoNum
                    itm.PRNUM = Me.PrNum
                    itm.DEPTNUM = Me.DeptNum
                    itm.EMPNUM = Me.EmpNum
                    _db.pr_batch_hours_only.InsertOnSubmit(itm)
                End If
            End If
        Next
        If _db.SaveChanges() Then
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        End If
    End Sub



End Class