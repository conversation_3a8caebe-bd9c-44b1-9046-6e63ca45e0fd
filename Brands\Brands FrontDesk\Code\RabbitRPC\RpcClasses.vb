﻿Imports Newtonsoft.Json

Namespace RabbitRpc
    Public Class RpcClasses
        Private whoIsCalling As String = ""

        Public Enum FuncStatus
            [Error]
            Success
        End Enum
        Private Shared Function ExecuteRequest(ByVal request As FunctionStatus) As FunctionStatus
            request.UserName = UserName
            Dim response As String
            Dim rpcClient = New RpcClient()
            response = rpcClient.[Call](JsonConvert.SerializeObject(request))
            rpcClient.Close()
            Console.WriteLine(String.Format(" [.] Got '{0}'", response))
            Dim ret = JsonConvert.DeserializeObject(Of FunctionStatus)(response)
            Return JsonConvert.DeserializeObject(Of FunctionStatus)(response)
        End Function

        Function CalculateManualCheck(CoNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal, pay_freq As String, check_date As DateTime, errors As String) As Boolean
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.CalculateManualCheck, .CoNum = CoNum, .EmpNum = EmpNum, .ChkCounter = ChkCounter, .pay_freq = pay_freq, .check_date = check_date, .errors = errors}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Message <> "" Then
                Throw New Exception(serObj.Message)
            ElseIf serObj.Status <> FuncStatus.Success Then
                Throw New Exception("CalculateManualCheck did not succeed")
            End If

            Return serObj.Status
        End Function

        Public Function CalculateSingleCheck(CoNum As Double, PrNum As Double, ByVal EmpNum As Decimal, ByVal ChkCounter As Decimal) As Boolean
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.CalculateSingleCheck, .CoNum = CoNum, .PrNum = PrNum, .EmpNum = EmpNum, .ChkCounter = ChkCounter}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Message <> "" Then
                Throw New Exception(serObj.Message)
            ElseIf serObj.Status <> FuncStatus.Success Then
                Throw New Exception("CalculateSingleCheck did not succeed")
            End If

            Return serObj.Status
        End Function

        Public Function CalculateTotals(CoNum As Double, PrNum As Double) As CalculateTotalsResult
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.CalculateTotals, .CoNum = CoNum, .PrNum = PrNum}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Obj Is Nothing AndAlso serObj.Status = 0 AndAlso serObj.Message <> "" Then
                Throw New Exception(serObj.Message)
            End If
            Return JsonConvert.DeserializeObject(Of CalculateTotalsResult)(serObj.ToString())
        End Function

        Public Function CheckPayrollStatus(ByVal CoNum As Decimal) As PayrollStatusResult
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.CheckPayrollStatus, .CoNum = CoNum}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)
            Return JsonConvert.DeserializeObject(Of PayrollStatusResult)(serObj.ToString())
        End Function

        Public Function CreatePayrollWithCalendar(ByVal CoNum As Decimal, ByVal calendarID As Decimal, ByVal payrollPassword As String) As Integer
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.CreatePayrollWithCalendar, .CoNum = CoNum, .calendarID = calendarID, .payrollPassword = payrollPassword}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)
            Return JsonConvert.DeserializeObject(Of Integer)(serObj.ToString())
        End Function

        Public Sub DeleteManualCheck(CoNum As Decimal, PrNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal)
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.DeleteManualCheck, .CoNum = CoNum, .PrNum = PrNum, .EmpNum = EmpNum, .ChkCounter = ChkCounter}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Status = FunctionStatus.FuncStatus.Error Then
                Throw New Exception(serObj.Message)
            End If
        End Sub

        Public Function GetSinglePayrollInfo(ByVal CoNum As Decimal, ByVal PrNum As Decimal) As PayrollInfo
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.GetSinglePayrollInfo, .CoNum = CoNum, .PrNum = PrNum}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)
            Return JsonConvert.DeserializeObject(Of PayrollInfo)(serObj.ToString())
        End Function

        Public Function ProcessPowerGrid(ByVal CoNum As Decimal, ByVal PrNum As Decimal, PowerBatchGridID As String) As ProcessPowerGridResult
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.ProcessPowerGrid, .CoNum = CoNum, .PrNum = PrNum, .PowerBatchGridID = PowerBatchGridID}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)
            Return JsonConvert.DeserializeObject(Of ProcessPowerGridResult)(serObj.ToString())
        End Function

        Public Function SubmitPayroll(CoNum As Double, PrNum As Double, PayrollNotes As String, submitType As Byte) As Boolean
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.SubmitPayroll, .CoNum = CoNum, .PrNum = PrNum, .PayrollNotes = PayrollNotes, .submitType = submitType}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)
            Return JsonConvert.DeserializeObject(Of Boolean)(serObj.ToString())
        End Function

        Public Function UndoPayroll(CoNum As Double, PrNum As Double, deletePayrollGrid As Boolean, PowerGridBatchID As Guid) As Boolean
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "") With {.UserName = UserName, .FuncToRun = FunctionToRun.UndoPayroll, .CoNum = CoNum, .PrNum = PrNum, .deletePayrollGrid = deletePayrollGrid, .PowerBatchGridGuiID = PowerGridBatchID}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)
            Return JsonConvert.DeserializeObject(Of Boolean)(serObj.ToString())
        End Function
    End Class

    Public Class CalculateTotalsResult
        Public Property Success As Boolean
        Public Property LogInfo1 As String
        Public Property LogInfo2 As String
        Public Property LogError As String
        Public Property PPxPayrollStatus As String
        Public Property exception As Exception
    End Class

    Public Class PayrollStatusResult
        Public Property PayrollStatus As String
        Public Property PayrollID As Decimal
        Public Property PPxPayrollStep As PPxPayrollSteps
        Public Property PPxPayrollStatus As String
        Public Property PowerGridBatchID As Guid
        Public Property AnyPowerGridPendingToProcess As Boolean
        Public Property IsPowerGridProcessing As Boolean
        Public Property CanCreate As Boolean
        Public Property CanEdit As Boolean
    End Class

    Public Class FunctionStatus
        Private _status As Int16
        Private _message As String
        Private _obj As Object

        Public Property UserName As String
        Public Property FuncToRun As FunctionToRun
        Public Property CoNum As Decimal
        Public Property PrNum As Decimal
        Public Property EmpNum As Decimal
        Public Property ChkCounter As Decimal
        Public Property pay_freq As String
        Public Property check_date As DateTime
        Public Property calendarID As Decimal
        Public Property payrollPassword As String
        Public Property PowerBatchGridID As String
        Public Property PowerBatchGridGuiID As Guid
        Public Property PayrollNotes As String
        Public Property submitType As Byte
        Public Property deletePayrollGrid As Boolean
        Public Property errors As String

        Public ReadOnly Property Status As Int16
            Get
                Return _status
            End Get
        End Property

        Public ReadOnly Property Message As String
            Get
                Return _message
            End Get
        End Property

        Public ReadOnly Property Obj As Object
            Get
                Return _obj
            End Get
        End Property

        Public Enum FuncStatus
            Unknown
            [Error]
            Success
        End Enum

        Public Sub New(ByVal Status As Int16, ByVal Message As String, ByVal Obj As Object)
            _status = Status
            _message = Message
            _obj = Obj
        End Sub

        Public Shared Function Success(ByVal obj As Object) As FunctionStatus
            Return New FunctionStatus(CType(FuncStatus.Success, Int16), "", obj)
        End Function

        Public Shared Function [Error](ByVal Message As String) As FunctionStatus
            Return New FunctionStatus(CType(FuncStatus.[Error], Int16), Message, Nothing)
        End Function

        Public Overrides Function ToString() As String
            If Obj.ToString() = "False" Then
                Return "0"
            ElseIf Obj.ToString() = "True" Then
                Return "1"
            Else
                Return Obj.ToString()
            End If
        End Function
    End Class

    Public Class PayrollInfo
        Public Property TotalChecks As Decimal?
        Public Property TotalGross As Decimal?
        Public Property EndDate As DateTime?
    End Class

    Public Enum FunctionToRun
        CalculateManualCheck
        CalculateSingleCheck
        CalculateTotals
        CheckPayrollStatus
        CreatePayrollWithCalendar
        DeleteManualCheck
        GetSinglePayrollInfo
        ProcessPowerGrid
        SubmitPayroll
        UndoPayroll
    End Enum

    Public Class ProcessPowerGridResult
        'Public Property PPxPayrollStatus As String
        'Public Property PowerGridBatchID As String
        Public Property Success As Boolean
        Public Property Status As Byte
        Public Property exception As Exception
        'Public Property Calculation As Byte
        Public Property pr_batches As List(Of pr_batch_msg_classs)
    End Class

    Public Class pr_batch_msg_classs
        Public Property id As Guid
        Public Property conum As Decimal
        Public Property empnum As Decimal?
        Public Property chk_counter As Integer?
        Public Property msg_type As String
        Public Property msg_body As String
        Public Property batch_id As Guid
        Public Property NoteTitle As String
        Public Property NoteBody As String

    End Class

End Namespace