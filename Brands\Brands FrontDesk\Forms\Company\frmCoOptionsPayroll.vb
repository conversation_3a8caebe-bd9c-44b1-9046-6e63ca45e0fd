﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Public Class frmCoOptionsPayroll

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal

    Private DB As dbEPDataDataContext
    Private CoOptionsEnt As CoOptions_Payroll
    Private CoOptionsSecondCheck As List(Of CoOptions_SecondCheckPayCode)
    Private _isLoaded As Boolean

    Private Sub frmCoOptionsPayroll_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            seRaitesDailyFrom.AddClearButton()
            lciSuppressNewPayrollOnlineAlert.Visibility = (Permissions.AllowSuppressNewPayrollOnlineAlert).ToBarItemVisibility
            TabbedControlGroup1.SelectedTabPageIndex = 0
            DB = New dbEPDataDataContext(GetConnectionString)
            cbeReportToEmailWhenSubmiting.Properties.Items.AddRange(DB.ReportEmailTeplates.Where(Function(r) r.ShowInPayrollRun).Select(Function(r) r.Name).ToArray())
            'cbeReportToEmailWhenSubmiting.Properties.Items.Add("Payroll Register (Combined)")

            Me.OTHERPAYBindingSource.DataSource = (From A In DB.OTHER_PAYS Where A.CONUM = Me.CoNum Order By A.OTH_PAY_NUM).ToList
            Me.DEDUCTIONBindingSource.DataSource = (From A In DB.DEDUCTIONs Where A.CONUM = Me.CoNum Order By A.DED_NUM).ToList
            Me.DIVISIONBindingSource.DataSource = (From A In DB.DIVISIONs Where A.CONUM = Me.CoNum Order By A.DDIVNAME Select A.DDIVNUM, A.DDIVNAME).ToList

            Dim UnionNames = (From A In DB.UnionDepartments Where A.Conum = Me.CoNum Select A.UnionNamePositionFund).Distinct.ToList
            Dim aUnionNames = (From A In UnionNames Select If(A.IndexOf("_") > 0, A.Substring(0, A.IndexOf("_") + 1), A)).Distinct.ToArray
            Me.Run_prc_PrUtilityImport.Properties.Items.AddRange(aUnionNames)
            Me.lueClientGroup.Properties.DataSource = GetUdfValueSplitted("ClientGroups")
            CoOptionsEnt = (From A In DB.CoOptions_Payrolls Where A.CoNum = Me.CoNum).SingleOrDefault
            If CoOptionsEnt Is Nothing Then
                CoOptionsEnt = New CoOptions_Payroll With {.CoNum = Me.CoNum, .rowguid = Guid.NewGuid}
                DB.CoOptions_Payrolls.InsertOnSubmit(CoOptionsEnt)
            End If
            Me.CoOptions_PayrollBindingSource.DataSource = CoOptionsEnt

            CoOptionsSecondCheck = (From A In DB.CoOptions_SecondCheckPayCodes Where A.CoNum = Me.CoNum Order By A.PayCode, A.EmpNum).ToList
            Me.CoOptionsSecondCheckPayCodeBindingSource.DataSource = CoOptionsSecondCheck

            Dim weekDays = [Enum].GetNames(GetType(DayOfWeek)).ToList
            weekDays.Insert(0, "")
            Me.ddlDoCheckForEachWeekday.Properties.Items.AddRange(weekDays.ToArray)
            lueImportTemplateId.Properties.DataSource = DB.ImportEmployeesLoginTemplates.ToList()
            lueImportTemplateId.Properties.ValueMember = "ImportID"
            lueImportTemplateId.Properties.DisplayMember = "ImportID"
            ccbeAccountManager.Properties.DataSource = GetUdfValueSplitted("AccountManagers")
            _isLoaded = True
            LoadCompanyGroups()
            LoadCompanyAccounts()
            LoadReportOptions()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
            Close()
        End Try
    End Sub

    Private Sub LoadReportOptions()
        prc_ReportOptionsResultBindingSource.DataSource = DB.prc_ReportOptions(CoNum, Nothing, Nothing).ToList()
    End Sub

    Private Sub PaycodeFor1099TextEdit_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles PaycodeFor1099TextEdit.KeyDown, DedcodeFor1099TextEdit.KeyDown
        Dim tb As DevExpress.XtraEditors.BaseEdit = sender
        If e.KeyCode = Keys.Back OrElse e.KeyCode = Keys.Delete Then
            tb.EditValue = Nothing
            tb.DoValidate()
        End If
    End Sub

    Private Sub PaycodeFor1099TextEdit_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PaycodeFor1099TextEdit.Validated
        If Not _isLoaded Then Exit Sub
        If Me.PaycodeFor1099TextEdit.HasValue Then
            CoOptionsEnt.DedcodeFor1099 = Nothing
        End If
    End Sub

    Private Sub DedcodeFor1099TextEdit_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DedcodeFor1099TextEdit.Validated
        If Not _isLoaded Then Exit Sub
        If Me.DedcodeFor1099TextEdit.HasValue Then
            CoOptionsEnt.PaycodeFor1099 = Nothing
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Try
            If ComboBoxEdit1.Text.IsNotNullOrWhiteSpace() AndAlso cbeReportToEmailWhenSubmiting.Properties.GetCheckedItems().ToString().IsNullOrWhiteSpace() Then
                DisplayMessageBox("You selected to send reports during payroll, please select a report to send Or disable this feature.")
                Exit Sub
            End If

            If CoOptionsSecondCheck IsNot Nothing Then
                For Each n In CoOptionsSecondCheck
                    If n.ID = 0 Then
                        n.CoNum = Me.CoNum
                        DB.CoOptions_SecondCheckPayCodes.InsertOnSubmit(n)
                    End If
                Next
            End If
            If DB.SaveChanges() Then
                AccountContextDB.SaveChanges()
                Me.DialogResult = System.Windows.Forms.DialogResult.OK
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        End Try
    End Sub

    Private Sub GridViewCoOptionsSecondCheckPay_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewCoOptionsSecondCheckPay.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete Line", AddressOf OnDeleteRowClick, My.Resources.delete_16x16)
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As CoOptions_SecondCheckPayCode = Me.GridViewCoOptionsSecondCheckPay.GetRow(rowHandle)
        Me.GridViewCoOptionsSecondCheckPay.DeleteRow(rowHandle)
        If Row IsNot Nothing AndAlso Row.ID > 0 Then
            DB.CoOptions_SecondCheckPayCodes.DeleteOnSubmit(Row)
        End If
    End Sub

    Private Sub OTSeperateCheckCheckEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OTSeperateCheckCheckEdit.CheckedChanged
        Me.OTSeperateCheckHoursMoreThanTextEdit.Enabled = Me.OTSeperateCheckCheckEdit.Checked
    End Sub

    Private Sub ParsonageAllCheckEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ParsonageAllCheckEdit.CheckedChanged
        Me.ParsonageDivisionsCheckList.Enabled = Me.ParsonageAllCheckEdit.Checked
    End Sub

    Private Sub btnShowSort_EditValueChanged(sender As Object, e As EventArgs) Handles btnShowSort.ButtonClick
        Me.txtPowerGridDefaultSort.ShowPopup()
    End Sub


    Private Sub LoadCompanyGroups()
        Dim _db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
        Dim groups = (From l In _db.LEASE_GROUPS Where Not l.lease_chknum.HasValue Select l.lease_descr).ToArray
        cbeGroup.Properties.Items.AddRange(groups)
    End Sub

    Private Sub cbeGroup_SelectedValueChanged(sender As Object, e As EventArgs) Handles cbeGroup.SelectedValueChanged
        Try
            Using _db = New dbEPDataDataContext(GetConnectionString)
                Dim divDeps = _db.DivDepGroups.Where(Function(d) d.CoNum = CoNum AndAlso d.GroupName = cbeGroup.Text).ToList
                Dim divs = (From d In _db.DIVISIONs Where d.CONUM = CoNum Select New DivisionGroups With {.DivNum = d.DDIVNUM, .DivName = d.DDIVNAME}).ToList
                For Each d In divs
                    d.DepartmentGroups = (From dg In _db.DEPARTMENTs Where dg.CONUM = CoNum AndAlso dg.DIVNUMD = d.DivNum Select New DepartmentGroups(d) With {.DepName = dg.DEPT_DESC, .DepNum = dg.DEPTNUM}).ToList

                    If divDeps.Exists(Function(dp) dp.DivNum = d.DivNum AndAlso Not dp.DepNum.HasValue) Then
                        d.Include = True
                        d.ChangeAllInclude(True)
                    ElseIf divDeps.Any(Function(dp) dp.DivNum = d.DivNum) Then
                        For Each r In divDeps.Where(Function(a) a.DivNum = d.DivNum)
                            Dim t = d.DepartmentGroups.FirstOrDefault(Function(a) a.DepNum = r.DepNum)
                            If t IsNot Nothing Then t.Include = True
                        Next
                    End If
                Next
                GridControl1.DataSource = divs
                ExpandAllRows(GridView1, True)
            End Using
        Catch ex As Exception
            DisplayErrorMessage($"Error laoding data for Co#: {CoNum} Group: {cbeGroup.Text}", ex)
        End Try
    End Sub

    Public Sub ExpandAllRows(ByVal View As GridView, expanded As Boolean)
        View.BeginUpdate()
        Try
            Dim dataRowCount As Integer = View.DataRowCount
            Dim rHandle As Integer
            For rHandle = 0 To dataRowCount - 1
                View.SetMasterRowExpanded(rHandle, expanded)
            Next
        Finally
            View.EndUpdate()
        End Try
    End Sub


    Public Class DivisionGroups

        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Include As Boolean
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DivNum As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DivName As String

        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DepartmentGroups As List(Of DepartmentGroups) = New List(Of DepartmentGroups)
        Public ReadOnly Property IsAllDepsIncluded As Boolean
            Get
                Return DepartmentGroups.All(Function(d) d.Include)
            End Get
        End Property
        Public ReadOnly Property IsAllDepsExcluded As Boolean
            Get
                Return DepartmentGroups.All(Function(d) Not d.Include)
            End Get
        End Property

        Public Sub ChangeAllInclude(val As Boolean)
            For Each r In DepartmentGroups
                r.Include = val
            Next
        End Sub
    End Class

    Public Class DepartmentGroups
        Public Sub New(div As DivisionGroups)
            DivGroup = div
        End Sub
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Include As Boolean
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DepNum As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DepName As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DivGroup As DivisionGroups
    End Class

    Private Sub RepositoryItemCheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles RepositoryItemCheckEdit1.CheckedChanged
        Try
            Dim isChecked As Boolean = DirectCast(sender, CheckEdit).Checked
            Dim gv As GridView = GridControl1.FocusedView
            gv.PostEditor()

            If gv Is GridView1 Then
                'division table
                Dim row As DivisionGroups = gv.GetFocusedRow
                RemoveCoGroup(row.DivNum, Nothing, True)
                If isChecked Then
                    AddCoGroup(row.DivNum, Nothing)
                End If
                row.ChangeAllInclude(isChecked)
                Dim dgv As GridView = gv.GetDetailView(gv.FocusedRowHandle, 0)
                If dgv IsNot Nothing Then dgv.RefreshData()
            Else
                'department table
                Dim row As DepartmentGroups = gv.GetFocusedRow
                If isChecked Then
                    If row.DivGroup.IsAllDepsIncluded Then
                        RemoveCoGroup(row.DivGroup.DivNum, Nothing, True)
                        AddCoGroup(row.DivGroup.DivNum, Nothing)
                        row.DivGroup.Include = True
                    Else
                        AddCoGroup(row.DivGroup.DivNum, row.DepNum)
                    End If
                Else
                    If row.DivGroup.Include Then
                        RemoveCoGroup(row.DivGroup.DivNum, Nothing, True)
                        row.DivGroup.Include = False
                        For Each r In row.DivGroup.DepartmentGroups
                            AddCoGroup(row.DivGroup.DivNum, r.DepNum)
                        Next
                    End If
                    RemoveCoGroup(row.DivGroup.DivNum, row.DepNum, False)
                End If
            End If
            GridView1.RefreshData()
            GridView2.RefreshData()
        Catch ex As Exception
            DisplayErrorMessage($"Error in Adding/Removing group from Co#: {CoNum}", ex)
        End Try
    End Sub

    Private Sub AddCoGroup(div As Decimal?, dep As Decimal?)
        Using _db = New dbEPDataDataContext(GetConnectionString)
            Dim divDep = New DivDepGroup With {.CoNum = CoNum, .GroupName = cbeGroup.Text, .DivNum = div, .DepNum = dep}
            _db.DivDepGroups.InsertOnSubmit(divDep)
            _db.SubmitChanges()
        End Using
    End Sub

    Private Sub RemoveCoGroup(div As Decimal?, dep As Decimal?, allDeps As Boolean)
        Using _db = New dbEPDataDataContext(GetConnectionString)
            Dim divDep = _db.DivDepGroups.Where(Function(d) d.CoNum = CoNum AndAlso d.GroupName = cbeGroup.Text AndAlso d.DivNum = div AndAlso (allDeps OrElse d.DepNum = dep))
            _db.DivDepGroups.DeleteAllOnSubmit(divDep)
            _db.SubmitChanges()
        End Using
    End Sub

    Private Sub HyperLinkEdit1_Click(sender As Object, e As EventArgs) Handles HyperLinkEdit1.Click
        ExpandAllRows(GridView1, True)
    End Sub

    Private Sub HyperLinkEdit2_Click(sender As Object, e As EventArgs) Handles HyperLinkEdit2.Click
        ExpandAllRows(GridView1, False)
    End Sub

    Private Sub cbeGroup_EditValueChanging(sender As Object, e As Controls.ChangingEventArgs) Handles cbeGroup.EditValueChanging
        For Each val As Object In cbeGroup.Properties.Items
            If val.Equals(e.NewValue) Then
                Return
            End If
        Next
        e.Cancel = True
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AccountContextDB As dbEPDataDataContext
    Private Sub LoadCompanyAccounts()
        Try
            AccountContextDB = New dbEPDataDataContext(GetConnectionString)
            lcgAccount.Visibility = Permissions.AllowCompanyAccounts.ToBarItemVisibility()
            accountsBindingSource.DataSource = AccountContextDB.Accounts.Where(Function(a) a.Conum = CoNum).ToList()
            riAccountFiles.Items.AddRange(AccountContextDB.Accounts.Select(Function(a) a.Filename).Distinct().ToArray)
        Catch ex As Exception
            DisplayErrorMessage("Error loading company accounts", ex)
        End Try
    End Sub

    Private Sub accountsBindingSource_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles accountsBindingSource.AddingNew
        Dim newAccount = New Account With {.Conum = CoNum}
        AccountContextDB.Accounts.InsertOnSubmit(newAccount)
        e.NewObject = newAccount
    End Sub

    Private Sub gvAccounts_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvAccounts.PopupMenuShowing
        Try
            Dim row As Account = gvAccounts.GetRow(e.HitInfo.RowHandle)
            If row Is Nothing Then Exit Sub
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() Delete(row, e.HitInfo.RowHandle), My.Resources.delete_16x16))
        Catch ex As Exception
            DisplayErrorMessage("Error in show menu in Accounts Grid", ex)
        End Try
    End Sub
    Private Sub Delete(row As Account, rowHandle As Integer)
        If XtraMessageBox.Show("Are you sure you would like to delete this row?", "Delete Row?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            gvAccounts.DeleteRow(rowHandle)
            AccountContextDB.Accounts.DeleteOnSubmit(row)
        End If
    End Sub

    Private Sub btnRefreshAccounts_Click(sender As Object, e As EventArgs) Handles btnRefreshAccounts.Click
        LoadCompanyAccounts()
    End Sub
End Class