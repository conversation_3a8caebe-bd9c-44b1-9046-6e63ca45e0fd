﻿Imports System.ComponentModel
Imports System.Data
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Public Class ucPaydeckUsers

    Dim logger As Serilog.ILogger
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Company As COMPANY
    Private Property _FormMode As FormMode?

    Public Sub New()
        InitializeComponent()
        If modGlobals.IsInDesignMode Then Exit Sub
        logger = modGlobals.Logger.ForContext(Of ucPaydeckUsers)
        gvLinkedToCompanies.SetGridLayoutAndAddMenues("gvLinkedToCompanies")
        gvUserEvents.SetGridLayoutAndAddMenues("gvUserEvents")
        cbeSiteUrl.Properties.Items.Clear()
        cbeSiteUrl.Properties.Items.AddRange(modGlobals.GetUdfValueSplitted("PaydeckSiteUrls"))
    End Sub

    Private Sub ucPaydeckUsers_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If modGlobals.IsInDesignMode Then Exit Sub
        lciInviteNewUser.Visibility = (Company IsNot Nothing).ToBarItemVisibility
        lciImpersonateCompany.Visibility = (Company IsNot Nothing).ToBarItemVisibility

        If Not Company Is Nothing AndAlso lciImpersonateCompany.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always AndAlso
                Not (Permissions.LinkedPaydeckUserId.HasValue AndAlso Permissions.ImpersonateWithUserId.HasValue AndAlso Permissions.ImpersonateWithConum.HasValue) Then
            lciImpersonateCompany.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        End If

        LoadData()
    End Sub

    Public Sub SetFormMode(formMode As FormMode)
        _FormMode = formMode
        gcUsers.ForceInitialize()
        If formMode = FormMode.AllUsers OrElse formMode = FormMode.AuthEvents Then
            colEmpName.Visible = False
            colEmpnum.Visible = False
            colEmpConum.Visible = False
            colLastLogin.Visible = False
            colIsActive.Visible = False
            colHasLinkedContact.Visible = False
            colUserRole.Visible = False
            colPermissions.Visible = False
        End If

        If formMode = FormMode.AuthEvents Then
            colEventType.Visible = True
            colEventMessage.Visible = True
            colConum.Visible = True
            colIpAddress.Visible = True
            colUserAgent.Visible = True
            colIsSuccsess.Visible = True
            colCreatedByUserId.Visible = True
            colCreatedByUserType.Visible = True
            colEventDateTime.Visible = True
            colMachineName.Visible = True
            colEventType.VisibleIndex = 2
            colEventDateTime.VisibleIndex = 3
            lcgDateFilters.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            deFromDate.DateTime = Today.AddDays(-4)
            deToDate.DateTime = Today.AddDays(1)
            gvUsers.OptionsView.ColumnAutoWidth = False
        End If

        If formMode = FormMode.AllUsers Then
            gvUsers.ShowFindPanel()
        End If

        If formMode = FormMode.AuthEvents Then
            Dim rule = gvUsers.FormatRules.AddValueRule(colIsSuccsess, New AppearanceDefault(Color.Red, Color.LightPink), FormatCondition.Equal, False)
            rule.ApplyToRow = True
            gvUsers.OptionsSelection.EnableAppearanceFocusedRow = False
        End If
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            If Not _FormMode.HasValue Then
                Exit Sub
            ElseIf _FormMode = FormMode.Company Then
                gcUsers.DataSource = Query("SELECT 
	                            bau.Id, 
	                            bau.Email, 
	                            EmpName = e.F_NAME + ' ' + e.L_NAME,
	                            bau.EmailConfirmed, 
	                            PasswordSet = iif(passwordhash IS NULL, 0,1),
								bau.PhoneNumber,
								bau.PhoneNumberConfirmed,
                                FORMAT(SWITCHOFFSET(bau.LockoutEnd, '-04:00'), 'g') LockoutEnd,
	                            bau.AccessFailedCount,
	                            bau.LastLoginConum,
	                            ue.Empnum,
	                            ue.EmpConum,
	                            ue.LastLogin,
								ue.IsActive,
								HasLinkedContact = iif(cc.conum IS NULL, 'Missing', 'YES'),
                                ue.UserRole,
							    ue.ClaimsDisplay,
                                bau.DismissedBannerId,
                                bau.ShouldEnableTwoFactor
                            FROM custom.BrandsAuthUser bau
                            INNER JOIN custom.BrandsAuthUserEmployee ue ON bau.Id = ue.UserId
                            INNER JOIN EPDATA.dbo.EMPLOYEE e ON e.CONUM = ue.EmpConum AND e.EMPNUM = ue.Empnum 
							LEFT OUTER JOIN EPDATA.dbo.co_contacts cc ON cc.conum = ue.Conum AND cc.empconum = ue.EmpConum AND cc.empnum = ue.Empnum
                            WHERE ue.Conum = @Conum", New SqlClient.SqlParameter("Conum", Company.CONUM))
            ElseIf _FormMode = FormMode.AuthEvents Then
                gcUsers.DataSource = Query("SELECT 
	                            bau.Id, 
	                            bau.Email,
							    ue.EventType,
							    ue.EventMessage,
							    ue.Conum,
							    ue.IpAddress,
							    ue.UserAgent,
							    ue.IsSuccsess,
							    ue.CreatedByUserId,
							    ue.CreatedByUserType,
							    ue.EventDateTime,
							    ue.MachineName,
	                            bau.EmailConfirmed, 
	                            PasswordSet = iif(passwordhash IS NULL, 0,1),
								bau.PhoneNumber,
								bau.PhoneNumberConfirmed,
	                            bau.LockoutEnd,
	                            bau.AccessFailedCount,
	                            bau.LastLoginConum,
                                bau.DismissedBannerId,
                                bau.ShouldEnableTwoFactor
                            FROM custom.BrandsAuthUser bau
							INNER JOIN custom.BrandsAuthUserEvent ue ON bau.Id = ue.UserId
							WHERE ue.EventDateTime BETWEEN @FromDate AND @EndDate
                            ORDER BY ue.EventDateTime DESC", New SqlClient.SqlParameter("FromDate", deFromDate.DateTime), New SqlClient.SqlParameter("EndDate", deToDate.DateTime))
                gvUsers.BestFitColumns()
            ElseIf _FormMode = FormMode.AllUsers Then
                gcUsers.DataSource = Query("SELECT 
	                            bau.Id, 
	                            bau.Email,
							    bau.EmailConfirmed, 
	                            PasswordSet = iif(passwordhash IS NULL, 0,1),
								bau.PhoneNumber,
								bau.PhoneNumberConfirmed,
	                            bau.LockoutEnd,
	                            bau.AccessFailedCount,
	                            bau.LastLoginConum,
                                bau.DismissedBannerId,
                                bau.ShouldEnableTwoFactor
                            FROM custom.BrandsAuthUser bau")
            End If
            FormatDateTimeColumns(gcUsers)
            gvUsers.BestFitColumns()

            If Not Company Is Nothing Then
                lciSiteId.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                lciIpRestriction.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always

                Dim account = Query(
                "SELECT iat.account
                FROM integration_accounts iat 
                WHERE iat.conum = @conum
                AND iat.provider_id IN (18, 2) AND iat.active = 'YES' AND iat.account IS NOT NULL", New SqlClient.SqlParameter("CoNum", Company.CONUM)
                )

                If account.Rows.Count Then
                    teSiteId.Text = account.Rows(0)(0)
                End If

                Dim ipRestriction = Query(
                "SELECT 
	                CASE 
		                WHEN cop.PaydeckEnableIpRestriction = 0
		                THEN 'IP Restrictions are off'
	                ELSE
		                REPLACE('Allowed IP range [@P1].', '@P1', cop.PaydeckAllowedIps) +
		                IIF(cop.PaydeckIpRestDisableUntil IS NOT NULL AND cop.PaydeckIpRestDisableUntil > GETDATE(), REPLACE(' Temporarily disabled until: [@P1]f', '@P1', cop.PaydeckIpRestDisableUntil), '')
	                END
                FROM custom.CoOptions_Payroll cop 
                WHERE cop.CoNum = @conum", New SqlClient.SqlParameter("CoNum", Company.CONUM)
                )

                If ipRestriction.Rows.Count Then
                    teIpRestrictions.Text = ipRestriction.Rows(0)(0).ToString()
                End If
            Else
                lciSiteId.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                lciIpRestriction.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading Paydeck Users", ex)
        End Try
    End Sub

    Private Sub bbiRefresh_Click(sender As Object, e As EventArgs) Handles bbiRefresh.Click
        LoadData()
    End Sub

    Private Sub bbiInviteNewUser_Click(sender As Object, e As EventArgs) Handles bbiInviteNewUser.Click
        Using frm = New frmPaydeckInviteUser(GetBaseUrl(), Company.CONUM)
            If frm.ShowDialog() = DialogResult.OK Then
                LoadData()
            End If
        End Using
    End Sub

    Private Sub gvUsers_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvUsers.FocusedRowObjectChanged
        Try
            If e.Row IsNot Nothing AndAlso e.RowHandle >= 0 Then
                Dim row As System.Data.DataRowView = e.Row
                Dim userId As Integer = row.Row.Field(Of Integer)("Id")
                LoadUserDetails(userId)
            Else
                gcLinkedToCompanies.DataSource = Nothing
                gcUserEvents.DataSource = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in loading user details", ex)
        End Try
    End Sub

    Private Sub LoadUserDetails(userId As Integer)
        Try
            gcLinkedToCompanies.DataSource = Query("SELECT 
	                                                    ue.UserId,
														IsEss = CONVERT(BIT,0),
	                                                    ue.Conum,
	                                                    CoName = c.CO_NAME,
	                                                    ue.EmpConum,
	                                                    ue.Empnum,
	                                                    EmpName = e.F_NAME + ' ' + e.L_NAME,
	                                                    HasLinkedContact = iif(cc.conum IS NULL, 'Missing', 'YES'),
                                                        SwipeClockUser = ue.SwipeClockLoginName,
                                                        ue.LastLogin,
														ue.IsActive
                                                    FROM custom.BrandsAuthUserEmployee ue
                                                    INNER JOIN EPDATA.dbo.COMPANY c ON ue.Conum = c.CONUM
                                                    INNER JOIN EPDATA.dbo.EMPLOYEE e ON ue.EmpConum = e.CONUM AND ue.Empnum = e.EMPNUM
                                                    LEFT OUTER JOIN EPDATA.dbo.co_contacts cc ON cc.conum = ue.Conum AND cc.empconum = ue.EmpConum AND cc.empnum = ue.Empnum
                                                    WHERE ue.UserId = @userid
												UNION 
													SELECT 
														ue.UserId,
														IsEss = CONVERT(BIT,1),
	                                                    e.Conum,
	                                                    CoName = c.CO_NAME,
	                                                    EmpConum = NULL,
	                                                    e.Empnum,
	                                                    EmpName = e.F_NAME + ' ' + e.L_NAME,
	                                                    HasLinkedContact = 'ESS',
                                                        SwipeClockUser = NULL,
                                                        ue.LastLogin,
														IsActive = CONVERT(BIT,1)
													FROM custom.BrandsAuthUserEssEmployees ue
													JOIN EMPLOYEE e ON ue.EmployeeEntryId = e.entry_id
													INNER JOIN EPDATA.dbo.COMPANY c ON e.Conum = c.CONUM
													WHERE ue.UserId = @userid", New SqlClient.SqlParameter("userId", userId))
            gvLinkedToCompanies.BestFitColumns()
            FormatDateTimeColumns(gcLinkedToCompanies)

            gcUserEvents.DataSource = Query("SELECT * FROM EPDATA.custom.BrandsAuthUserEvent WHERE UserId = @UserId ORDER BY EventDateTime DESC", New SqlClient.SqlParameter("UserId", userId))
            FormatDateTimeColumns(gcUserEvents)
            gvUserEvents.BestFitColumns()
            Dim col = gvUserEvents.Columns.ColumnByFieldName("EventType")
            col.Width = col.GetBestWidth() + 10
        Catch ex As Exception
            DisplayErrorMessage($"Error loading user details. UserId: {userId}", ex)
        End Try
    End Sub

    Private Shared Sub FormatDateTimeColumns(grid As GridControl)
        For Each col As DataColumn In DirectCast(grid.DataSource, DataTable).Columns
            If col.DataType = GetType(DateTime) Then
                Dim c = (DirectCast(grid.MainView, GridView)).Columns.ColumnByFieldName(col.ColumnName)
                c.DisplayFormat.FormatType = FormatType.DateTime
                c.DisplayFormat.FormatString = "G"
            End If
        Next
    End Sub

    Private Sub gvUsers_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvUsers.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As System.Data.DataRowView = gvUsers.GetRow(e.HitInfo.RowHandle)
            Dim userId As Integer = row.Row.Field(Of Integer)("Id")
            If Company IsNot Nothing Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Resend Invite", Sub()
                                                                                           ResendInvite(userId)
                                                                                       End Sub, My.Resources.send_16x16))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit Permissions", Sub()
                                                                                              EditPermission(userId)
                                                                                          End Sub, My.Resources.locknavigation_16x16))
            End If
            If UserInRole("PaydeckDeleteUser") Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete User", Sub()
                                                                                         PaydeckApiDeleteUser(userId)
                                                                                     End Sub, My.Resources.delete_16x16))
            End If
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Mark Phone Number Confirmed", Sub()
                                                                                                     PaydeckApiMarkPhoneNumberConfirmed(userId)
                                                                                                 End Sub))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Clear Lockout", Sub()
                                                                                       ClearLockout(userId)
                                                                                   End Sub))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Update Phone Number", Sub()
                                                                                             UpdatePhoneNumber(userId)
                                                                                         End Sub))

            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Set 2FA method to email", Sub()
                                                                                                 Update2FAToEmail(userId)
                                                                                             End Sub))
            If UserInRole("AllowManagePayDeckBanner") Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Reset Dismissed Banner", Sub()
                                                                                                    ResetDismissedBanner(userId)
                                                                                                End Sub))
            End If

            Dim sql = String.Format("SELECT LinkedPaydeckUserId FROM custom.FrontDeskPermissions WHERE UserName = '{0}'", UserName)
            Dim LinkedPaydeckUserId = Query(Of Int32?)(sql).First()

            If Permissions.LinkedPaydeckUserId.HasValue Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Impersonate Permissions", Sub()
                                                                                                     ImpersonateUser(userId, True)
                                                                                                 End Sub) With {.BeginGroup = True})
            End If

            sql = String.Format("SELECT custom.fn_UserInRole('PaydeckImpersonateUser', '{0}')", UserName)
            Dim granted = Query(Of Boolean)(sql).First()

            If granted AndAlso Permissions.LinkedPaydeckUserId.HasValue Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Impersonate User", Sub()
                                                                                              ImpersonateUser(userId, False)
                                                                                          End Sub))
            End If
        End If
    End Sub

    Private Async Sub ImpersonateUser(userId? As Integer, permissionsOnly As Boolean, Optional conum As Decimal? = Nothing)
        Try
            lcRoot.ShowProgessPanel
            Using db = New Brands.DAL.EPDATAContext(GetConnectionString)

                If userId.HasValue AndAlso Not permissionsOnly And Not conum.HasValue Then
                    Dim PhoneNumberConfirmed = Query("SELECT bau.PhoneNumberConfirmed FROM custom.BrandsAuthUser bau WHERE bau.Id = @userid", New SqlClient.SqlParameter("userId", userId))(0)(0)
                    If Not PhoneNumberConfirmed Then
                        XtraMessageBox.Show($"User phone number was not confirmed yet, can't impersonate.")
                        Return
                    End If
                End If

                Dim impersonate = New Brands.DAL.PaydeckImpersonate With {
                    .Conum = IIf(Company?.CONUM Is Nothing, conum, Company?.CONUM),
                    .CreatedOn = DateTime.Now,
                    .FdUserName = modGlobals.UserName,
                    .Guid = Guid.NewGuid(),
                    .ImpersonatePaydeckUserId = userId,
                    .PaydeckUserId = Permissions.LinkedPaydeckUserId}

                If Not userId.HasValue AndAlso Permissions.ImpersonateWithUserId.HasValue AndAlso Permissions.ImpersonateWithConum.HasValue Then
                    impersonate.ImpersonatePaydeckUserId = Nothing
                    impersonate.CopyPermissionsFromPaydeckUserId = Permissions.ImpersonateWithUserId
                    impersonate.CopyPermissionsFromConum = Permissions.ImpersonateWithConum
                ElseIf (permissionsOnly) Then
                    impersonate.ImpersonatePaydeckUserId = Nothing
                    impersonate.CopyPermissionsFromPaydeckUserId = userId
                    impersonate.CopyPermissionsFromConum = Company.CONUM
                End If
                db.PaydeckImpersonate.Add(impersonate)
                Await db.SaveChangesAsync()
                'System.Diagnostics.Process.Start($"{cbeSiteUrl.EditValue}/auth/logout/{impersonate.Guid}")
                Dim filePath = $"{cbeSiteUrl.EditValue}/auth/logout/{impersonate.Guid}"
                Dim psi As New System.Diagnostics.ProcessStartInfo()
                psi.FileName = filePath
                psi.UseShellExecute = True
                System.Diagnostics.Process.Start(psi)
            End Using
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Updating User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Sub UpdatePhoneNumber(ByVal userId As Integer)
        Try
            lcRoot.ShowProgessPanel
            Dim phone = XtraInputBox.Show("Phone Number:", "Enter Updated Phone Number", "")
            If phone.IsNullOrWhiteSpace() Then Exit Sub
            Await modPaydeckAPI.InviteUser(cbeSiteUrl.EditValue, New InviteEmployee With {
                                           .CreatedByUserId = UserName,
                                           .CreatedByUserType = "FD",
                                           .UpdatePhoneNumberInternal = New InviteEmployee.UpdatePhoneNumber With {.UserId = userId, .PhoneNumber = phone}
                                           })
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Updating User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Sub Update2FAToEmail(ByVal userId As Integer)
        Try
            lcRoot.ShowProgessPanel
            modGlobals.UpdateSql("UPDATE custom.BrandsAuthUser SET TwoFactorType = 2 WHERE Id = @UserId", New SqlClient.SqlParameter("UserId", userId))
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in Update2FAToEmail", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub ResetDismissedBanner(ByVal userId As Integer)
        Try
            lcRoot.ShowProgessPanel
            modGlobals.UpdateSql("UPDATE custom.BrandsAuthUser SET DismissedBannerId = NULL WHERE Id = @UserId", New SqlClient.SqlParameter("UserId", userId))
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error ResetDismissedBanner User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub EditPermission(userId As Integer)
        Try
            Using frm As New frmPaydeckInviteUser(GetBaseUrl(), Company.CONUM, userId, False)
                If frm.ShowDialog = DialogResult.OK Then
                    LoadData()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error editing permissions", ex)
        End Try
    End Sub

    Private Async Sub ClearLockout(userId As Integer)
        Try
            lcRoot.ShowProgessPanel
            If XtraMessageBox.Show($"Are you sure you would like to clear user {userId} lockout", "Clear Lockout", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If
            Await modPaydeckAPI.InviteUser(cbeSiteUrl.EditValue, New InviteEmployee With {
                                           .CreatedByUserId = UserName,
                                           .CreatedByUserType = "FD",
                                           .ClearLockoutInternal = New InviteEmployee.ClearLockout With {.UserId = userId}
                                           })
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Updating User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub ResendInvite(userId As Integer)
        Using frm As New frmPaydeckInviteUser(GetBaseUrl(), Company.CONUM, userId, True)
            If frm.ShowDialog = DialogResult.OK Then
                LoadData()
            End If
        End Using
    End Sub

    Function GetBaseUrl() As String
        Return cbeSiteUrl.EditValue.ToString()
    End Function

    Private Sub gvLinkedToCompanies_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvLinkedToCompanies.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As System.Data.DataRowView = gvLinkedToCompanies.GetRow(e.HitInfo.RowHandle)
            Dim userId As Integer = row.Row.Field(Of Integer)("UserId")
            Dim conumId As Integer = row.Row.Field(Of Decimal)("Conum")
            Dim EmpNumId As Integer = row.Row.Field(Of Decimal)("EmpNum")
            Dim isActive As Boolean = row.Row.Field(Of Boolean)("IsActive")
            Dim isEss As Boolean = row.Row.Field(Of Boolean)("IsEss")

            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Impersonate User", Sub()
                                                                                          ImpersonateUser(userId, False, conumId)
                                                                                      End Sub))

            If Not isEss Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete Company Link", Sub()
                                                                                                 PaydeckApiActivateCoLink(userId, conumId, "DeleteIt")
                                                                                             End Sub, My.Resources.send_16x16))
                If isActive Then
                    e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Inactivate Company Link", Sub()
                                                                                                         PaydeckApiActivateCoLink(userId, conumId, "InactivateIt")
                                                                                                     End Sub))
                Else
                    e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Activate Company Link", Sub()
                                                                                                       PaydeckApiActivateCoLink(userId, conumId, "ActivateIt")
                                                                                                   End Sub))
                End If
            Else
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete ESS profile link", Sub()
                                                                                                     DeleteEssProfileLink(userId, conumId, EmpNumId)
                                                                                                 End Sub))
            End If
        End If
    End Sub

    Private Sub DeleteEssProfileLink(userId As Integer, conum As Integer, empnum As Integer)
        Try
            lcRoot.ShowProgessPanel

            If XtraMessageBox.Show($"Are you sure you would like to delete the ESS user-company link {userId}", $"Delete User-Company Link", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If

            modGlobals.UpdateSql("DELETE bauee
FROM custom.BrandsAuthUserEssEmployees bauee
INNER JOIN EMPLOYEE e ON e.entry_id = bauee.EmployeeEntryId
INNER JOIN COMPANY c ON c.CONUM = e.CONUM
WHERE bauee.UserId = @UserId AND c.CONUM = @CoNum AND e.EMPNUM = @EmpNum", New SqlClient.SqlParameter("UserId", userId), New SqlClient.SqlParameter("CoNum", conum), New SqlClient.SqlParameter("EmpNum", empnum))

            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Deleting ESS profile link", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Sub PaydeckApiActivateCoLink(userId As Integer, conum As Integer, action As String)
        Try
            lcRoot.ShowProgessPanel

            If action = "ActivateIt" Then
                Dim BlockPaydeck = Query(Of Boolean)($"SELECT BlockPayDeck FROM custom.CoOptions_Payroll WHERE CONUM = {conum}").FirstOrDefault()

                If BlockPaydeck Then
                    DisplayMessageBox("Company has a block for PayDeck.  Check with Tax Dept.")
                    Return
                End If
            End If

            If XtraMessageBox.Show($"Are you sure you would like to {action} user-company link {userId}", $"{action} User-Comapny Link", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If
            Await modPaydeckAPI.InviteUser(cbeSiteUrl.EditValue, New InviteEmployee With {
                                           .CreatedByUserId = UserName,
                                           .CreatedByUserType = "FD",
                                           .ChangeCompanyLinkInternal = New InviteEmployee.ChangeCompanyLink With {.UserId = userId, .Conum = conum, .DeleteOrInactivate = action}
                                           })
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Updating User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Sub PaydeckApiDeleteUser(userId As Integer)
        Try
            lcRoot.ShowProgessPanel
            If XtraMessageBox.Show($"Are you sure you would like to delete user {userId}", "Delete User", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If
            Await modPaydeckAPI.InviteUser(cbeSiteUrl.EditValue, New InviteEmployee With {
                                           .CreatedByUserId = UserName,
                                           .CreatedByUserType = "FD",
                                           .DeleteUserRequestInternal = New InviteEmployee.DeleteUserRequest With {.UserId = userId}
                                           })
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Updating User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Sub PaydeckApiMarkPhoneNumberConfirmed(userId As Integer)
        Try
            lcRoot.ShowProgessPanel
            If XtraMessageBox.Show($"Are you sure you would like to mark user {userId} phone number confirmed?", "Mark Phone Number Confirmed?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If
            Await modPaydeckAPI.InviteUser(cbeSiteUrl.EditValue, New InviteEmployee With {
                                           .CreatedByUserId = UserName,
                                           .CreatedByUserType = "FD",
                                           .MarkUserPhoneNumberConfirmedInternal = New InviteEmployee.MarkUserPhoneNumberConfirmed With {.UserId = userId}
                                           })
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error Updating User", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Public Enum FormMode
        Company
        AllUsers
        AuthEvents
    End Enum

    Private Sub btnImpersonateCompany_Click(sender As Object, e As EventArgs) Handles btnImpersonateCompany.Click
        If Permissions.LinkedPaydeckUserId.HasValue AndAlso Permissions.ImpersonateWithUserId.HasValue AndAlso Permissions.ImpersonateWithConum.HasValue Then
            ImpersonateUser(Nothing, False)
        End If
    End Sub

    Private Sub gvUserEvents_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvUserEvents.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row = gvUserEvents.GetDataRow(e.HitInfo.RowHandle)
            If row("IpAddress") IsNot Nothing Then
                e.Menu.Items.Add(New DXMenuItem("Open Seq Logs", Sub() OpenLogs(row("IpAddress"))))
            End If
        End If
    End Sub

    Private Sub OpenLogs(ip As String)
        Try
            'System.Diagnostics.Process.Start($"http://appserver:5341/#/events?signal=signal-3,signal-5,signal-162&filter=IPAddress%20%3D%20'{ip}'")
            Dim filePath = $"http://appserver:5341/#/events?signal=signal-3,signal-5,signal-162&filter=IPAddress%20%3D%20'{ip}'"
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = filePath
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error opening Seq logs", ex)
        End Try
    End Sub
End Class
