﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmManualBillingDraftDetails
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.dD_Manual_Trans_LogsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.IDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForID = New DevExpress.XtraLayout.LayoutControlItem()
        Me.CONUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForCONUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.DIVNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForDIVNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.DDIVNAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForDDIVNAME = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ACCOUNT_SOURCETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForACCOUNT_SOURCE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ROUTING_NUMBERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForROUTING_NUMBER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ACCOUNT_NUMBERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForACCOUNT_NUMBER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ACCT_TYPETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForACCT_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Priority_LvlTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForPriority_Lvl = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ACCOUNT_ORDERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForACCOUNT_ORDER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ACH_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForACH_NAME = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Trans_NoteTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForTrans_Note = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Requested_Effective_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.ItemForRequested_Effective_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Billing_Dept_NotesTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForBilling_Dept_Notes = New DevExpress.XtraLayout.LayoutControlItem()
        Me.AMOUNTTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForAMOUNT = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Transaction_StatusTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForTransaction_Status = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Date_RequestedDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.ItemForDate_Requested = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Requested_ByTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForRequested_By = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Transaction_Effective_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.ItemForTransaction_Effective_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Process_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.ItemForProcess_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Credit_RoutingTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForCredit_Routing = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Credit_AccountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForCredit_Account = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Credit_ACCT_TYPETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForCredit_ACCT_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Credit_CONUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForCredit_CONUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Credit_EMPNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForCredit_EMPNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Transaction_NACHA_IDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForTransaction_NACHA_ID = New DevExpress.XtraLayout.LayoutControlItem()
        Me.CanNotBeCanceledCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ItemForCanNotBeCanceled = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForChangeLog = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ChangeLogTextEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.btnUpdateStatus = New DevExpress.XtraEditors.SimpleButton()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        CType(Me.dD_Manual_Trans_LogsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForID, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CONUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCONUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DIVNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDIVNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DDIVNAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDDIVNAME, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCOUNT_SOURCETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCOUNT_SOURCE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ROUTING_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForROUTING_NUMBER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCOUNT_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCOUNT_NUMBER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCT_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Priority_LvlTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPriority_Lvl, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCOUNT_ORDERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCOUNT_ORDER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACH_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACH_NAME, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Trans_NoteTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTrans_Note, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Requested_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Requested_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForRequested_Effective_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Billing_Dept_NotesTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForBilling_Dept_Notes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AMOUNTTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForAMOUNT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Transaction_StatusTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTransaction_Status, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_RequestedDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDate_Requested, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Requested_ByTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForRequested_By, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Transaction_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Transaction_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTransaction_Effective_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Process_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Process_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForProcess_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_RoutingTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_Routing, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_AccountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_Account, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_ACCT_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_CONUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_CONUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_EMPNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Transaction_NACHA_IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTransaction_NACHA_ID, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CanNotBeCanceledCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCanNotBeCanceled, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForChangeLog, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChangeLogTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'dD_Manual_Trans_LogsBindingSource
        '
        Me.dD_Manual_Trans_LogsBindingSource.DataSource = GetType(Brands_FrontDesk.DD_Manual_Trans_Log)
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.Controls.Add(Me.btnCancel)
        Me.DataLayoutControl1.Controls.Add(Me.btnUpdateStatus)
        Me.DataLayoutControl1.Controls.Add(Me.LabelControl1)
        Me.DataLayoutControl1.Controls.Add(Me.IDTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.CONUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.DIVNUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.DDIVNAMETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCOUNT_SOURCETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ROUTING_NUMBERTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCOUNT_NUMBERTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCT_TYPETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Priority_LvlTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCOUNT_ORDERTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACH_NAMETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Trans_NoteTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Requested_Effective_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Billing_Dept_NotesTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.AMOUNTTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Transaction_StatusTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Date_RequestedDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Requested_ByTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Transaction_Effective_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Process_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_RoutingTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_AccountTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_ACCT_TYPETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_CONUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_EMPNUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Transaction_NACHA_IDTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.CanNotBeCanceledCheckEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ChangeLogTextEdit)
        Me.DataLayoutControl1.DataSource = Me.dD_Manual_Trans_LogsBindingSource
        Me.DataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.DataLayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.[True]
        Me.DataLayoutControl1.Root = Me.LayoutControlGroup1
        Me.DataLayoutControl1.Size = New System.Drawing.Size(783, 511)
        Me.DataLayoutControl1.TabIndex = 0
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(783, 511)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.AllowDrawBackground = False
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForID, Me.ItemForCONUM, Me.ItemForDIVNUM, Me.ItemForDDIVNAME, Me.ItemForACCOUNT_SOURCE, Me.ItemForROUTING_NUMBER, Me.ItemForACCOUNT_NUMBER, Me.ItemForACCT_TYPE, Me.ItemForPriority_Lvl, Me.ItemForACCOUNT_ORDER, Me.ItemForACH_NAME, Me.ItemForTrans_Note, Me.ItemForRequested_Effective_Date, Me.ItemForBilling_Dept_Notes, Me.ItemForAMOUNT, Me.ItemForTransaction_Status, Me.ItemForDate_Requested, Me.ItemForRequested_By, Me.ItemForTransaction_Effective_Date, Me.ItemForProcess_Date, Me.ItemForCredit_Routing, Me.ItemForCredit_Account, Me.ItemForCredit_ACCT_TYPE, Me.ItemForCredit_CONUM, Me.ItemForCredit_EMPNUM, Me.ItemForTransaction_NACHA_ID, Me.ItemForCanNotBeCanceled, Me.ItemForChangeLog, Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.EmptySpaceItem1})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup2.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(763, 491)
        '
        'IDTextEdit
        '
        Me.IDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ID", True))
        Me.IDTextEdit.Location = New System.Drawing.Point(158, 63)
        Me.IDTextEdit.Name = "IDTextEdit"
        Me.IDTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.IDTextEdit.Properties.Mask.EditMask = "N0"
        Me.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.IDTextEdit.Properties.ReadOnly = True
        Me.IDTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.IDTextEdit.StyleController = Me.DataLayoutControl1
        Me.IDTextEdit.TabIndex = 4
        '
        'ItemForID
        '
        Me.ItemForID.Control = Me.IDTextEdit
        Me.ItemForID.Location = New System.Drawing.Point(0, 51)
        Me.ItemForID.Name = "ItemForID"
        Me.ItemForID.Size = New System.Drawing.Size(381, 24)
        Me.ItemForID.Text = "ID"
        Me.ItemForID.TextSize = New System.Drawing.Size(134, 13)
        '
        'CONUMTextEdit
        '
        Me.CONUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "CONUM", True))
        Me.CONUMTextEdit.Location = New System.Drawing.Point(539, 63)
        Me.CONUMTextEdit.Name = "CONUMTextEdit"
        Me.CONUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.CONUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.CONUMTextEdit.Properties.Mask.EditMask = "G"
        Me.CONUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CONUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.CONUMTextEdit.Properties.ReadOnly = True
        Me.CONUMTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.CONUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.CONUMTextEdit.TabIndex = 5
        '
        'ItemForCONUM
        '
        Me.ItemForCONUM.Control = Me.CONUMTextEdit
        Me.ItemForCONUM.Location = New System.Drawing.Point(381, 51)
        Me.ItemForCONUM.Name = "ItemForCONUM"
        Me.ItemForCONUM.Size = New System.Drawing.Size(382, 24)
        Me.ItemForCONUM.Text = "CONUM"
        Me.ItemForCONUM.TextSize = New System.Drawing.Size(134, 13)
        '
        'DIVNUMTextEdit
        '
        Me.DIVNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "DIVNUM", True))
        Me.DIVNUMTextEdit.Location = New System.Drawing.Point(158, 87)
        Me.DIVNUMTextEdit.Name = "DIVNUMTextEdit"
        Me.DIVNUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.DIVNUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.DIVNUMTextEdit.Properties.Mask.EditMask = "G"
        Me.DIVNUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.DIVNUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.DIVNUMTextEdit.Properties.ReadOnly = True
        Me.DIVNUMTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.DIVNUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.DIVNUMTextEdit.TabIndex = 6
        '
        'ItemForDIVNUM
        '
        Me.ItemForDIVNUM.Control = Me.DIVNUMTextEdit
        Me.ItemForDIVNUM.Location = New System.Drawing.Point(0, 75)
        Me.ItemForDIVNUM.Name = "ItemForDIVNUM"
        Me.ItemForDIVNUM.Size = New System.Drawing.Size(381, 24)
        Me.ItemForDIVNUM.Text = "DIVNUM"
        Me.ItemForDIVNUM.TextSize = New System.Drawing.Size(134, 13)
        '
        'DDIVNAMETextEdit
        '
        Me.DDIVNAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "DDIVNAME", True))
        Me.DDIVNAMETextEdit.Location = New System.Drawing.Point(539, 87)
        Me.DDIVNAMETextEdit.Name = "DDIVNAMETextEdit"
        Me.DDIVNAMETextEdit.Properties.ReadOnly = True
        Me.DDIVNAMETextEdit.Size = New System.Drawing.Size(232, 20)
        Me.DDIVNAMETextEdit.StyleController = Me.DataLayoutControl1
        Me.DDIVNAMETextEdit.TabIndex = 7
        '
        'ItemForDDIVNAME
        '
        Me.ItemForDDIVNAME.Control = Me.DDIVNAMETextEdit
        Me.ItemForDDIVNAME.Location = New System.Drawing.Point(381, 75)
        Me.ItemForDDIVNAME.Name = "ItemForDDIVNAME"
        Me.ItemForDDIVNAME.Size = New System.Drawing.Size(382, 24)
        Me.ItemForDDIVNAME.Text = "DDIVNAME"
        Me.ItemForDDIVNAME.TextSize = New System.Drawing.Size(134, 13)
        '
        'ACCOUNT_SOURCETextEdit
        '
        Me.ACCOUNT_SOURCETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ACCOUNT_SOURCE", True))
        Me.ACCOUNT_SOURCETextEdit.Location = New System.Drawing.Point(158, 111)
        Me.ACCOUNT_SOURCETextEdit.Name = "ACCOUNT_SOURCETextEdit"
        Me.ACCOUNT_SOURCETextEdit.Properties.ReadOnly = True
        Me.ACCOUNT_SOURCETextEdit.Size = New System.Drawing.Size(231, 20)
        Me.ACCOUNT_SOURCETextEdit.StyleController = Me.DataLayoutControl1
        Me.ACCOUNT_SOURCETextEdit.TabIndex = 8
        '
        'ItemForACCOUNT_SOURCE
        '
        Me.ItemForACCOUNT_SOURCE.Control = Me.ACCOUNT_SOURCETextEdit
        Me.ItemForACCOUNT_SOURCE.Location = New System.Drawing.Point(0, 99)
        Me.ItemForACCOUNT_SOURCE.Name = "ItemForACCOUNT_SOURCE"
        Me.ItemForACCOUNT_SOURCE.Size = New System.Drawing.Size(381, 24)
        Me.ItemForACCOUNT_SOURCE.Text = "ACCOUNT_SOURCE"
        Me.ItemForACCOUNT_SOURCE.TextSize = New System.Drawing.Size(134, 13)
        '
        'ROUTING_NUMBERTextEdit
        '
        Me.ROUTING_NUMBERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ROUTING_NUMBER", True))
        Me.ROUTING_NUMBERTextEdit.Location = New System.Drawing.Point(539, 111)
        Me.ROUTING_NUMBERTextEdit.Name = "ROUTING_NUMBERTextEdit"
        Me.ROUTING_NUMBERTextEdit.Properties.ReadOnly = True
        Me.ROUTING_NUMBERTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.ROUTING_NUMBERTextEdit.StyleController = Me.DataLayoutControl1
        Me.ROUTING_NUMBERTextEdit.TabIndex = 9
        '
        'ItemForROUTING_NUMBER
        '
        Me.ItemForROUTING_NUMBER.Control = Me.ROUTING_NUMBERTextEdit
        Me.ItemForROUTING_NUMBER.Location = New System.Drawing.Point(381, 99)
        Me.ItemForROUTING_NUMBER.Name = "ItemForROUTING_NUMBER"
        Me.ItemForROUTING_NUMBER.Size = New System.Drawing.Size(382, 24)
        Me.ItemForROUTING_NUMBER.Text = "ROUTING_NUMBER"
        Me.ItemForROUTING_NUMBER.TextSize = New System.Drawing.Size(134, 13)
        '
        'ACCOUNT_NUMBERTextEdit
        '
        Me.ACCOUNT_NUMBERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ACCOUNT_NUMBER", True))
        Me.ACCOUNT_NUMBERTextEdit.Location = New System.Drawing.Point(158, 135)
        Me.ACCOUNT_NUMBERTextEdit.Name = "ACCOUNT_NUMBERTextEdit"
        Me.ACCOUNT_NUMBERTextEdit.Properties.ReadOnly = True
        Me.ACCOUNT_NUMBERTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.ACCOUNT_NUMBERTextEdit.StyleController = Me.DataLayoutControl1
        Me.ACCOUNT_NUMBERTextEdit.TabIndex = 10
        '
        'ItemForACCOUNT_NUMBER
        '
        Me.ItemForACCOUNT_NUMBER.Control = Me.ACCOUNT_NUMBERTextEdit
        Me.ItemForACCOUNT_NUMBER.Location = New System.Drawing.Point(0, 123)
        Me.ItemForACCOUNT_NUMBER.Name = "ItemForACCOUNT_NUMBER"
        Me.ItemForACCOUNT_NUMBER.Size = New System.Drawing.Size(381, 24)
        Me.ItemForACCOUNT_NUMBER.Text = "ACCOUNT_NUMBER"
        Me.ItemForACCOUNT_NUMBER.TextSize = New System.Drawing.Size(134, 13)
        '
        'ACCT_TYPETextEdit
        '
        Me.ACCT_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ACCT_TYPE", True))
        Me.ACCT_TYPETextEdit.Location = New System.Drawing.Point(539, 135)
        Me.ACCT_TYPETextEdit.Name = "ACCT_TYPETextEdit"
        Me.ACCT_TYPETextEdit.Properties.ReadOnly = True
        Me.ACCT_TYPETextEdit.Size = New System.Drawing.Size(232, 20)
        Me.ACCT_TYPETextEdit.StyleController = Me.DataLayoutControl1
        Me.ACCT_TYPETextEdit.TabIndex = 11
        '
        'ItemForACCT_TYPE
        '
        Me.ItemForACCT_TYPE.Control = Me.ACCT_TYPETextEdit
        Me.ItemForACCT_TYPE.Location = New System.Drawing.Point(381, 123)
        Me.ItemForACCT_TYPE.Name = "ItemForACCT_TYPE"
        Me.ItemForACCT_TYPE.Size = New System.Drawing.Size(382, 24)
        Me.ItemForACCT_TYPE.Text = "ACCT_TYPE"
        Me.ItemForACCT_TYPE.TextSize = New System.Drawing.Size(134, 13)
        '
        'Priority_LvlTextEdit
        '
        Me.Priority_LvlTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Priority_Lvl", True))
        Me.Priority_LvlTextEdit.Location = New System.Drawing.Point(158, 159)
        Me.Priority_LvlTextEdit.Name = "Priority_LvlTextEdit"
        Me.Priority_LvlTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.Priority_LvlTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.Priority_LvlTextEdit.Properties.Mask.EditMask = "N0"
        Me.Priority_LvlTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Priority_LvlTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Priority_LvlTextEdit.Properties.ReadOnly = True
        Me.Priority_LvlTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.Priority_LvlTextEdit.StyleController = Me.DataLayoutControl1
        Me.Priority_LvlTextEdit.TabIndex = 12
        '
        'ItemForPriority_Lvl
        '
        Me.ItemForPriority_Lvl.Control = Me.Priority_LvlTextEdit
        Me.ItemForPriority_Lvl.Location = New System.Drawing.Point(0, 147)
        Me.ItemForPriority_Lvl.Name = "ItemForPriority_Lvl"
        Me.ItemForPriority_Lvl.Size = New System.Drawing.Size(381, 24)
        Me.ItemForPriority_Lvl.Text = "Priority_Lvl"
        Me.ItemForPriority_Lvl.TextSize = New System.Drawing.Size(134, 13)
        '
        'ACCOUNT_ORDERTextEdit
        '
        Me.ACCOUNT_ORDERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ACCOUNT_ORDER", True))
        Me.ACCOUNT_ORDERTextEdit.Location = New System.Drawing.Point(539, 159)
        Me.ACCOUNT_ORDERTextEdit.Name = "ACCOUNT_ORDERTextEdit"
        Me.ACCOUNT_ORDERTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.ACCOUNT_ORDERTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.ACCOUNT_ORDERTextEdit.Properties.Mask.EditMask = "N0"
        Me.ACCOUNT_ORDERTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.ACCOUNT_ORDERTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.ACCOUNT_ORDERTextEdit.Properties.ReadOnly = True
        Me.ACCOUNT_ORDERTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.ACCOUNT_ORDERTextEdit.StyleController = Me.DataLayoutControl1
        Me.ACCOUNT_ORDERTextEdit.TabIndex = 13
        '
        'ItemForACCOUNT_ORDER
        '
        Me.ItemForACCOUNT_ORDER.Control = Me.ACCOUNT_ORDERTextEdit
        Me.ItemForACCOUNT_ORDER.Location = New System.Drawing.Point(381, 147)
        Me.ItemForACCOUNT_ORDER.Name = "ItemForACCOUNT_ORDER"
        Me.ItemForACCOUNT_ORDER.Size = New System.Drawing.Size(382, 24)
        Me.ItemForACCOUNT_ORDER.Text = "ACCOUNT_ORDER"
        Me.ItemForACCOUNT_ORDER.TextSize = New System.Drawing.Size(134, 13)
        '
        'ACH_NAMETextEdit
        '
        Me.ACH_NAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ACH_NAME", True))
        Me.ACH_NAMETextEdit.Location = New System.Drawing.Point(158, 183)
        Me.ACH_NAMETextEdit.Name = "ACH_NAMETextEdit"
        Me.ACH_NAMETextEdit.Properties.ReadOnly = True
        Me.ACH_NAMETextEdit.Size = New System.Drawing.Size(231, 20)
        Me.ACH_NAMETextEdit.StyleController = Me.DataLayoutControl1
        Me.ACH_NAMETextEdit.TabIndex = 14
        '
        'ItemForACH_NAME
        '
        Me.ItemForACH_NAME.Control = Me.ACH_NAMETextEdit
        Me.ItemForACH_NAME.Location = New System.Drawing.Point(0, 171)
        Me.ItemForACH_NAME.Name = "ItemForACH_NAME"
        Me.ItemForACH_NAME.Size = New System.Drawing.Size(381, 24)
        Me.ItemForACH_NAME.Text = "ACH_NAME"
        Me.ItemForACH_NAME.TextSize = New System.Drawing.Size(134, 13)
        '
        'Trans_NoteTextEdit
        '
        Me.Trans_NoteTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Trans_Note", True))
        Me.Trans_NoteTextEdit.Location = New System.Drawing.Point(539, 183)
        Me.Trans_NoteTextEdit.Name = "Trans_NoteTextEdit"
        Me.Trans_NoteTextEdit.Properties.ReadOnly = True
        Me.Trans_NoteTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Trans_NoteTextEdit.StyleController = Me.DataLayoutControl1
        Me.Trans_NoteTextEdit.TabIndex = 15
        '
        'ItemForTrans_Note
        '
        Me.ItemForTrans_Note.Control = Me.Trans_NoteTextEdit
        Me.ItemForTrans_Note.Location = New System.Drawing.Point(381, 171)
        Me.ItemForTrans_Note.Name = "ItemForTrans_Note"
        Me.ItemForTrans_Note.Size = New System.Drawing.Size(382, 24)
        Me.ItemForTrans_Note.Text = "Trans_Note"
        Me.ItemForTrans_Note.TextSize = New System.Drawing.Size(134, 13)
        '
        'Requested_Effective_DateDateEdit
        '
        Me.Requested_Effective_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Requested_Effective_Date", True))
        Me.Requested_Effective_DateDateEdit.EditValue = Nothing
        Me.Requested_Effective_DateDateEdit.Location = New System.Drawing.Point(158, 207)
        Me.Requested_Effective_DateDateEdit.Name = "Requested_Effective_DateDateEdit"
        Me.Requested_Effective_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Requested_Effective_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Requested_Effective_DateDateEdit.Properties.ReadOnly = True
        Me.Requested_Effective_DateDateEdit.Size = New System.Drawing.Size(231, 20)
        Me.Requested_Effective_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.Requested_Effective_DateDateEdit.TabIndex = 16
        '
        'ItemForRequested_Effective_Date
        '
        Me.ItemForRequested_Effective_Date.Control = Me.Requested_Effective_DateDateEdit
        Me.ItemForRequested_Effective_Date.Location = New System.Drawing.Point(0, 195)
        Me.ItemForRequested_Effective_Date.Name = "ItemForRequested_Effective_Date"
        Me.ItemForRequested_Effective_Date.Size = New System.Drawing.Size(381, 24)
        Me.ItemForRequested_Effective_Date.Text = "Requested_Effective_Date"
        Me.ItemForRequested_Effective_Date.TextSize = New System.Drawing.Size(134, 13)
        '
        'Billing_Dept_NotesTextEdit
        '
        Me.Billing_Dept_NotesTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Billing_Dept_Notes", True))
        Me.Billing_Dept_NotesTextEdit.Location = New System.Drawing.Point(539, 207)
        Me.Billing_Dept_NotesTextEdit.Name = "Billing_Dept_NotesTextEdit"
        Me.Billing_Dept_NotesTextEdit.Properties.ReadOnly = True
        Me.Billing_Dept_NotesTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Billing_Dept_NotesTextEdit.StyleController = Me.DataLayoutControl1
        Me.Billing_Dept_NotesTextEdit.TabIndex = 17
        '
        'ItemForBilling_Dept_Notes
        '
        Me.ItemForBilling_Dept_Notes.Control = Me.Billing_Dept_NotesTextEdit
        Me.ItemForBilling_Dept_Notes.Location = New System.Drawing.Point(381, 195)
        Me.ItemForBilling_Dept_Notes.Name = "ItemForBilling_Dept_Notes"
        Me.ItemForBilling_Dept_Notes.Size = New System.Drawing.Size(382, 24)
        Me.ItemForBilling_Dept_Notes.Text = "Billing_Dept_Notes"
        Me.ItemForBilling_Dept_Notes.TextSize = New System.Drawing.Size(134, 13)
        '
        'AMOUNTTextEdit
        '
        Me.AMOUNTTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "AMOUNT", True))
        Me.AMOUNTTextEdit.Location = New System.Drawing.Point(158, 231)
        Me.AMOUNTTextEdit.Name = "AMOUNTTextEdit"
        Me.AMOUNTTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.AMOUNTTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.AMOUNTTextEdit.Properties.Mask.EditMask = "G"
        Me.AMOUNTTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.AMOUNTTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.AMOUNTTextEdit.Properties.ReadOnly = True
        Me.AMOUNTTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.AMOUNTTextEdit.StyleController = Me.DataLayoutControl1
        Me.AMOUNTTextEdit.TabIndex = 18
        '
        'ItemForAMOUNT
        '
        Me.ItemForAMOUNT.Control = Me.AMOUNTTextEdit
        Me.ItemForAMOUNT.Location = New System.Drawing.Point(0, 219)
        Me.ItemForAMOUNT.Name = "ItemForAMOUNT"
        Me.ItemForAMOUNT.Size = New System.Drawing.Size(381, 24)
        Me.ItemForAMOUNT.Text = "AMOUNT"
        Me.ItemForAMOUNT.TextSize = New System.Drawing.Size(134, 13)
        '
        'Transaction_StatusTextEdit
        '
        Me.Transaction_StatusTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Transaction_Status", True))
        Me.Transaction_StatusTextEdit.Location = New System.Drawing.Point(539, 231)
        Me.Transaction_StatusTextEdit.Name = "Transaction_StatusTextEdit"
        Me.Transaction_StatusTextEdit.Properties.ReadOnly = True
        Me.Transaction_StatusTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Transaction_StatusTextEdit.StyleController = Me.DataLayoutControl1
        Me.Transaction_StatusTextEdit.TabIndex = 19
        '
        'ItemForTransaction_Status
        '
        Me.ItemForTransaction_Status.Control = Me.Transaction_StatusTextEdit
        Me.ItemForTransaction_Status.Location = New System.Drawing.Point(381, 219)
        Me.ItemForTransaction_Status.Name = "ItemForTransaction_Status"
        Me.ItemForTransaction_Status.Size = New System.Drawing.Size(382, 24)
        Me.ItemForTransaction_Status.Text = "Transaction_Status"
        Me.ItemForTransaction_Status.TextSize = New System.Drawing.Size(134, 13)
        '
        'Date_RequestedDateEdit
        '
        Me.Date_RequestedDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Date_Requested", True))
        Me.Date_RequestedDateEdit.EditValue = Nothing
        Me.Date_RequestedDateEdit.Location = New System.Drawing.Point(158, 255)
        Me.Date_RequestedDateEdit.Name = "Date_RequestedDateEdit"
        Me.Date_RequestedDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_RequestedDateEdit.Properties.ReadOnly = True
        Me.Date_RequestedDateEdit.Size = New System.Drawing.Size(231, 20)
        Me.Date_RequestedDateEdit.StyleController = Me.DataLayoutControl1
        Me.Date_RequestedDateEdit.TabIndex = 20
        '
        'ItemForDate_Requested
        '
        Me.ItemForDate_Requested.Control = Me.Date_RequestedDateEdit
        Me.ItemForDate_Requested.Location = New System.Drawing.Point(0, 243)
        Me.ItemForDate_Requested.Name = "ItemForDate_Requested"
        Me.ItemForDate_Requested.Size = New System.Drawing.Size(381, 24)
        Me.ItemForDate_Requested.Text = "Date_Requested"
        Me.ItemForDate_Requested.TextSize = New System.Drawing.Size(134, 13)
        '
        'Requested_ByTextEdit
        '
        Me.Requested_ByTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Requested_By", True))
        Me.Requested_ByTextEdit.Location = New System.Drawing.Point(539, 255)
        Me.Requested_ByTextEdit.Name = "Requested_ByTextEdit"
        Me.Requested_ByTextEdit.Properties.ReadOnly = True
        Me.Requested_ByTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Requested_ByTextEdit.StyleController = Me.DataLayoutControl1
        Me.Requested_ByTextEdit.TabIndex = 21
        '
        'ItemForRequested_By
        '
        Me.ItemForRequested_By.Control = Me.Requested_ByTextEdit
        Me.ItemForRequested_By.Location = New System.Drawing.Point(381, 243)
        Me.ItemForRequested_By.Name = "ItemForRequested_By"
        Me.ItemForRequested_By.Size = New System.Drawing.Size(382, 24)
        Me.ItemForRequested_By.Text = "Requested_By"
        Me.ItemForRequested_By.TextSize = New System.Drawing.Size(134, 13)
        '
        'Transaction_Effective_DateDateEdit
        '
        Me.Transaction_Effective_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Transaction_Effective_Date", True))
        Me.Transaction_Effective_DateDateEdit.EditValue = Nothing
        Me.Transaction_Effective_DateDateEdit.Location = New System.Drawing.Point(158, 279)
        Me.Transaction_Effective_DateDateEdit.Name = "Transaction_Effective_DateDateEdit"
        Me.Transaction_Effective_DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Transaction_Effective_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Transaction_Effective_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Transaction_Effective_DateDateEdit.Properties.ReadOnly = True
        Me.Transaction_Effective_DateDateEdit.Size = New System.Drawing.Size(231, 20)
        Me.Transaction_Effective_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.Transaction_Effective_DateDateEdit.TabIndex = 22
        '
        'ItemForTransaction_Effective_Date
        '
        Me.ItemForTransaction_Effective_Date.Control = Me.Transaction_Effective_DateDateEdit
        Me.ItemForTransaction_Effective_Date.Location = New System.Drawing.Point(0, 267)
        Me.ItemForTransaction_Effective_Date.Name = "ItemForTransaction_Effective_Date"
        Me.ItemForTransaction_Effective_Date.Size = New System.Drawing.Size(381, 24)
        Me.ItemForTransaction_Effective_Date.Text = "Transaction_Effective_Date"
        Me.ItemForTransaction_Effective_Date.TextSize = New System.Drawing.Size(134, 13)
        '
        'Process_DateDateEdit
        '
        Me.Process_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Process_Date", True))
        Me.Process_DateDateEdit.EditValue = Nothing
        Me.Process_DateDateEdit.Location = New System.Drawing.Point(539, 279)
        Me.Process_DateDateEdit.Name = "Process_DateDateEdit"
        Me.Process_DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Process_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Process_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Process_DateDateEdit.Properties.ReadOnly = True
        Me.Process_DateDateEdit.Size = New System.Drawing.Size(232, 20)
        Me.Process_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.Process_DateDateEdit.TabIndex = 23
        '
        'ItemForProcess_Date
        '
        Me.ItemForProcess_Date.Control = Me.Process_DateDateEdit
        Me.ItemForProcess_Date.Location = New System.Drawing.Point(381, 267)
        Me.ItemForProcess_Date.Name = "ItemForProcess_Date"
        Me.ItemForProcess_Date.Size = New System.Drawing.Size(382, 24)
        Me.ItemForProcess_Date.Text = "Process_Date"
        Me.ItemForProcess_Date.TextSize = New System.Drawing.Size(134, 13)
        '
        'Credit_RoutingTextEdit
        '
        Me.Credit_RoutingTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Credit_Routing", True))
        Me.Credit_RoutingTextEdit.Location = New System.Drawing.Point(158, 303)
        Me.Credit_RoutingTextEdit.Name = "Credit_RoutingTextEdit"
        Me.Credit_RoutingTextEdit.Properties.ReadOnly = True
        Me.Credit_RoutingTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.Credit_RoutingTextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_RoutingTextEdit.TabIndex = 24
        '
        'ItemForCredit_Routing
        '
        Me.ItemForCredit_Routing.Control = Me.Credit_RoutingTextEdit
        Me.ItemForCredit_Routing.Location = New System.Drawing.Point(0, 291)
        Me.ItemForCredit_Routing.Name = "ItemForCredit_Routing"
        Me.ItemForCredit_Routing.Size = New System.Drawing.Size(381, 24)
        Me.ItemForCredit_Routing.Text = "Credit_Routing"
        Me.ItemForCredit_Routing.TextSize = New System.Drawing.Size(134, 13)
        '
        'Credit_AccountTextEdit
        '
        Me.Credit_AccountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Credit_Account", True))
        Me.Credit_AccountTextEdit.Location = New System.Drawing.Point(539, 303)
        Me.Credit_AccountTextEdit.Name = "Credit_AccountTextEdit"
        Me.Credit_AccountTextEdit.Properties.ReadOnly = True
        Me.Credit_AccountTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Credit_AccountTextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_AccountTextEdit.TabIndex = 25
        '
        'ItemForCredit_Account
        '
        Me.ItemForCredit_Account.Control = Me.Credit_AccountTextEdit
        Me.ItemForCredit_Account.Location = New System.Drawing.Point(381, 291)
        Me.ItemForCredit_Account.Name = "ItemForCredit_Account"
        Me.ItemForCredit_Account.Size = New System.Drawing.Size(382, 24)
        Me.ItemForCredit_Account.Text = "Credit_Account"
        Me.ItemForCredit_Account.TextSize = New System.Drawing.Size(134, 13)
        '
        'Credit_ACCT_TYPETextEdit
        '
        Me.Credit_ACCT_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Credit_ACCT_TYPE", True))
        Me.Credit_ACCT_TYPETextEdit.Location = New System.Drawing.Point(158, 327)
        Me.Credit_ACCT_TYPETextEdit.Name = "Credit_ACCT_TYPETextEdit"
        Me.Credit_ACCT_TYPETextEdit.Properties.ReadOnly = True
        Me.Credit_ACCT_TYPETextEdit.Size = New System.Drawing.Size(231, 20)
        Me.Credit_ACCT_TYPETextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_ACCT_TYPETextEdit.TabIndex = 26
        '
        'ItemForCredit_ACCT_TYPE
        '
        Me.ItemForCredit_ACCT_TYPE.Control = Me.Credit_ACCT_TYPETextEdit
        Me.ItemForCredit_ACCT_TYPE.Location = New System.Drawing.Point(0, 315)
        Me.ItemForCredit_ACCT_TYPE.Name = "ItemForCredit_ACCT_TYPE"
        Me.ItemForCredit_ACCT_TYPE.Size = New System.Drawing.Size(381, 24)
        Me.ItemForCredit_ACCT_TYPE.Text = "Credit_ACCT_TYPE"
        Me.ItemForCredit_ACCT_TYPE.TextSize = New System.Drawing.Size(134, 13)
        '
        'Credit_CONUMTextEdit
        '
        Me.Credit_CONUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Credit_CONUM", True))
        Me.Credit_CONUMTextEdit.Location = New System.Drawing.Point(539, 327)
        Me.Credit_CONUMTextEdit.Name = "Credit_CONUMTextEdit"
        Me.Credit_CONUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.Credit_CONUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.Credit_CONUMTextEdit.Properties.Mask.EditMask = "G"
        Me.Credit_CONUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Credit_CONUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Credit_CONUMTextEdit.Properties.ReadOnly = True
        Me.Credit_CONUMTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Credit_CONUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_CONUMTextEdit.TabIndex = 27
        '
        'ItemForCredit_CONUM
        '
        Me.ItemForCredit_CONUM.Control = Me.Credit_CONUMTextEdit
        Me.ItemForCredit_CONUM.Location = New System.Drawing.Point(381, 315)
        Me.ItemForCredit_CONUM.Name = "ItemForCredit_CONUM"
        Me.ItemForCredit_CONUM.Size = New System.Drawing.Size(382, 24)
        Me.ItemForCredit_CONUM.Text = "Credit_CONUM"
        Me.ItemForCredit_CONUM.TextSize = New System.Drawing.Size(134, 13)
        '
        'Credit_EMPNUMTextEdit
        '
        Me.Credit_EMPNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Credit_EMPNUM", True))
        Me.Credit_EMPNUMTextEdit.Location = New System.Drawing.Point(158, 351)
        Me.Credit_EMPNUMTextEdit.Name = "Credit_EMPNUMTextEdit"
        Me.Credit_EMPNUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.Credit_EMPNUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.Credit_EMPNUMTextEdit.Properties.Mask.EditMask = "G"
        Me.Credit_EMPNUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Credit_EMPNUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Credit_EMPNUMTextEdit.Properties.ReadOnly = True
        Me.Credit_EMPNUMTextEdit.Size = New System.Drawing.Size(231, 20)
        Me.Credit_EMPNUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_EMPNUMTextEdit.TabIndex = 28
        '
        'ItemForCredit_EMPNUM
        '
        Me.ItemForCredit_EMPNUM.Control = Me.Credit_EMPNUMTextEdit
        Me.ItemForCredit_EMPNUM.Location = New System.Drawing.Point(0, 339)
        Me.ItemForCredit_EMPNUM.Name = "ItemForCredit_EMPNUM"
        Me.ItemForCredit_EMPNUM.Size = New System.Drawing.Size(381, 24)
        Me.ItemForCredit_EMPNUM.Text = "Credit_EMPNUM"
        Me.ItemForCredit_EMPNUM.TextSize = New System.Drawing.Size(134, 13)
        '
        'Transaction_NACHA_IDTextEdit
        '
        Me.Transaction_NACHA_IDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "Transaction_NACHA_ID", True))
        Me.Transaction_NACHA_IDTextEdit.Location = New System.Drawing.Point(539, 351)
        Me.Transaction_NACHA_IDTextEdit.Name = "Transaction_NACHA_IDTextEdit"
        Me.Transaction_NACHA_IDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.Transaction_NACHA_IDTextEdit.Properties.ReadOnly = True
        Me.Transaction_NACHA_IDTextEdit.Size = New System.Drawing.Size(232, 20)
        Me.Transaction_NACHA_IDTextEdit.StyleController = Me.DataLayoutControl1
        Me.Transaction_NACHA_IDTextEdit.TabIndex = 29
        '
        'ItemForTransaction_NACHA_ID
        '
        Me.ItemForTransaction_NACHA_ID.Control = Me.Transaction_NACHA_IDTextEdit
        Me.ItemForTransaction_NACHA_ID.Location = New System.Drawing.Point(381, 339)
        Me.ItemForTransaction_NACHA_ID.Name = "ItemForTransaction_NACHA_ID"
        Me.ItemForTransaction_NACHA_ID.Size = New System.Drawing.Size(382, 24)
        Me.ItemForTransaction_NACHA_ID.Text = "Transaction_NACHA_ID"
        Me.ItemForTransaction_NACHA_ID.TextSize = New System.Drawing.Size(134, 13)
        '
        'CanNotBeCanceledCheckEdit
        '
        Me.CanNotBeCanceledCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "CanNotBeCanceled", True))
        Me.CanNotBeCanceledCheckEdit.Location = New System.Drawing.Point(12, 375)
        Me.CanNotBeCanceledCheckEdit.Name = "CanNotBeCanceledCheckEdit"
        Me.CanNotBeCanceledCheckEdit.Properties.Caption = "Can Not Be Canceled"
        Me.CanNotBeCanceledCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.[Default]
        Me.CanNotBeCanceledCheckEdit.Properties.ReadOnly = True
        Me.CanNotBeCanceledCheckEdit.Size = New System.Drawing.Size(759, 20)
        Me.CanNotBeCanceledCheckEdit.StyleController = Me.DataLayoutControl1
        Me.CanNotBeCanceledCheckEdit.TabIndex = 30
        '
        'ItemForCanNotBeCanceled
        '
        Me.ItemForCanNotBeCanceled.Control = Me.CanNotBeCanceledCheckEdit
        Me.ItemForCanNotBeCanceled.Location = New System.Drawing.Point(0, 363)
        Me.ItemForCanNotBeCanceled.Name = "ItemForCanNotBeCanceled"
        Me.ItemForCanNotBeCanceled.Size = New System.Drawing.Size(763, 24)
        Me.ItemForCanNotBeCanceled.Text = "Can Not Be Canceled"
        Me.ItemForCanNotBeCanceled.TextSize = New System.Drawing.Size(0, 0)
        Me.ItemForCanNotBeCanceled.TextVisible = False
        '
        'ItemForChangeLog
        '
        Me.ItemForChangeLog.Control = Me.ChangeLogTextEdit
        Me.ItemForChangeLog.Location = New System.Drawing.Point(0, 387)
        Me.ItemForChangeLog.Name = "ItemForChangeLog"
        Me.ItemForChangeLog.Size = New System.Drawing.Size(763, 78)
        Me.ItemForChangeLog.Text = "Change Log"
        Me.ItemForChangeLog.TextSize = New System.Drawing.Size(134, 13)
        '
        'ChangeLogTextEdit
        '
        Me.ChangeLogTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Manual_Trans_LogsBindingSource, "ChangeLog", True))
        Me.ChangeLogTextEdit.Location = New System.Drawing.Point(158, 399)
        Me.ChangeLogTextEdit.Name = "ChangeLogTextEdit"
        Me.ChangeLogTextEdit.Properties.ReadOnly = True
        Me.ChangeLogTextEdit.Size = New System.Drawing.Size(613, 74)
        Me.ChangeLogTextEdit.StyleController = Me.DataLayoutControl1
        Me.ChangeLogTextEdit.TabIndex = 31
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Segoe UI Semibold", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseTextOptions = True
        Me.LabelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl1.Location = New System.Drawing.Point(12, 12)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(759, 47)
        Me.LabelControl1.StyleController = Me.DataLayoutControl1
        Me.LabelControl1.TabIndex = 40
        Me.LabelControl1.Text = "This Transaction matches the following Manual Billing Draft"
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.LabelControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.MinSize = New System.Drawing.Size(362, 21)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(763, 51)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'btnUpdateStatus
        '
        Me.btnUpdateStatus.Location = New System.Drawing.Point(567, 477)
        Me.btnUpdateStatus.Name = "btnUpdateStatus"
        Me.btnUpdateStatus.Size = New System.Drawing.Size(204, 22)
        Me.btnUpdateStatus.StyleController = Me.DataLayoutControl1
        Me.btnUpdateStatus.TabIndex = 41
        Me.btnUpdateStatus.Text = "Mark Transaction Status as ""Draft Failed"""
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.btnUpdateStatus
        Me.LayoutControlItem2.Location = New System.Drawing.Point(555, 465)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(208, 26)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'btnCancel
        '
        Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(481, 477)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(82, 22)
        Me.btnCancel.StyleController = Me.DataLayoutControl1
        Me.btnCancel.TabIndex = 42
        Me.btnCancel.Text = "Cancel"
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.btnCancel
        Me.LayoutControlItem3.Location = New System.Drawing.Point(469, 465)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(86, 26)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 465)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(469, 26)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'frmManualBillingDraftDetails
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.btnCancel
        Me.ClientSize = New System.Drawing.Size(783, 511)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.Name = "frmManualBillingDraftDetails"
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Manual Billing Draft Details"
        CType(Me.dD_Manual_Trans_LogsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForID, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CONUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCONUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DIVNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDIVNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DDIVNAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDDIVNAME, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCOUNT_SOURCETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCOUNT_SOURCE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ROUTING_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForROUTING_NUMBER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCOUNT_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCOUNT_NUMBER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCT_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Priority_LvlTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPriority_Lvl, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCOUNT_ORDERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCOUNT_ORDER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACH_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACH_NAME, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Trans_NoteTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTrans_Note, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Requested_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Requested_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForRequested_Effective_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Billing_Dept_NotesTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForBilling_Dept_Notes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AMOUNTTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForAMOUNT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Transaction_StatusTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTransaction_Status, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_RequestedDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDate_Requested, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Requested_ByTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForRequested_By, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Transaction_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Transaction_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTransaction_Effective_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Process_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Process_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForProcess_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_RoutingTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_Routing, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_AccountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_Account, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_ACCT_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_CONUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_CONUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_EMPNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Transaction_NACHA_IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTransaction_NACHA_ID, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CanNotBeCanceledCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCanNotBeCanceled, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForChangeLog, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChangeLogTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents dD_Manual_Trans_LogsBindingSource As BindingSource
    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents IDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CONUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DIVNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DDIVNAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCOUNT_SOURCETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ROUTING_NUMBERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCOUNT_NUMBERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCT_TYPETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Priority_LvlTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCOUNT_ORDERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACH_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Trans_NoteTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Requested_Effective_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Billing_Dept_NotesTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents AMOUNTTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Transaction_StatusTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Date_RequestedDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Requested_ByTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Transaction_Effective_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Process_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Credit_RoutingTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Credit_AccountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Credit_ACCT_TYPETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Credit_CONUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Credit_EMPNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Transaction_NACHA_IDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CanNotBeCanceledCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ChangeLogTextEdit As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForID As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCONUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDIVNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDDIVNAME As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCOUNT_SOURCE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForROUTING_NUMBER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCOUNT_NUMBER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCT_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForPriority_Lvl As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCOUNT_ORDER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACH_NAME As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTrans_Note As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForRequested_Effective_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForBilling_Dept_Notes As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForAMOUNT As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTransaction_Status As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDate_Requested As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForRequested_By As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTransaction_Effective_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForProcess_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_Routing As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_Account As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_ACCT_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_CONUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_EMPNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTransaction_NACHA_ID As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCanNotBeCanceled As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForChangeLog As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnUpdateStatus As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
End Class
