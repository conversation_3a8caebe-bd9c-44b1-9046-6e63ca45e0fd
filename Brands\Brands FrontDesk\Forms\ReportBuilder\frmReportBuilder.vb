﻿Imports System.Collections.ObjectModel
Imports System.ComponentModel
Imports System.Data
Imports System.IO
Imports System.Threading
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Docking2010.Views.WindowsUI
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Repository
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Menu
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports DevExpress.XtraSplashScreen
Imports Microsoft.Data.SqlClient
Imports Microsoft.EntityFrameworkCore

Public Class frmReportBuilder
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property DefaultSelectedConum As Decimal

    Public ReadOnly Property ConnectionString As String
    Private ReadOnly Property userLookAndFeel As DevExpress.LookAndFeel.UserLookAndFeel
    Private Property ReportId As Integer
    Private Property Report As CustomReport
    Private Property ReportLayoutId As Integer?
    Private Property ReportLayout As CustomReportLayout
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ReportLogger As Serilog.ILogger = Logger.ForContext(Of frmReportBuilder)
    Private Property DB As dbEPDataDataContext
    Private Property SelectedColumns As ObservableCollection(Of Column)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AvailColumns As List(Of CustomReportColumnTemplate)
    Private Property IsAdmin As Boolean = False
    Private Property SelectedCoNum As Decimal?
    Private Property expressionHelper As CustomColumnExpressionHelper
    Private Property LayoutSettings As ReportLayoutSettingsJson
    Private ReadOnly Property IsRunningFromPPImports As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsScheduledExport As Boolean
    Private cancellationToken As CancellationTokenSource

    Dim PayCodes As Dictionary(Of Decimal, String)
    Dim DedCodes As Dictionary(Of Decimal, String)
    Dim MemoCodes As Dictionary(Of Decimal, String)

    Sub New(conString As String, rptId As Integer, _userLookAndFeel As DevExpress.LookAndFeel.UserLookAndFeel, _IsRunningFromPPImports As Boolean, Optional layoutId As Integer? = Nothing)
        IsRunningFromPPImports = _IsRunningFromPPImports
        ConnectionString = conString
        InitializeComponent()

        Try
            DB = New dbEPDataDataContext(ConnectionString)
            userLookAndFeel = _userLookAndFeel
            ReportLogger = ReportLogger.ForContext("ReportId", rptId)
            ReportLogger = ReportLogger.ForContext("ReportLayoutId", layoutId)
            ReportLogger.Information("Opening Report Builder - form with ReportId: {ReportId} LayoutId: {LayoutId}", rptId, layoutId)
            ReportId = rptId
            ReportLayoutId = layoutId
            pccProgressPanel.AutoCenter
        Catch ex As Exception
            DisplayErrorMessage("Error opening form", ex)
        End Try
    End Sub

    Private Sub frmRebortBuilder_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            TabbedControlGroup1.SelectedTabPageIndex = 0
            If Not Me.IsScheduledExport Then ucSearchCompany.BindPopupContainerEdit(pceCoNum)
            InitializeForm()
        Catch ex As Exception
            DisplayErrorMessage("Error in frmRebortBuilder_Load", ex)
        End Try
    End Sub

    Public Sub InitializeForm()
        Try
            If Not AllowReporting() Then
                Return
            End If
            IsAdmin = GetIsAdmin()
            rpgAdmin.Visible = IsAdmin
            bciUseAdminMode.Checked = IsAdmin
            ReportLogger.Information("IsAdmin: {IsAdmin}", IsAdmin)
            cbeDateRange.Properties.Items.Clear()
            cbeDateRange.Properties.Items.Insert(0, "Last Check Date")
            cbeDateRange.Properties.Items.AddRange(DateFunctions.DateRangeOptions)
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)

                Report = db.CustomReports.Single(Function(r) r.ID = ReportId)
                ReportLogger = ReportLogger.ForContext("ReportName", Report.Name)
                LoadAvailableColumns()
                expressionHelper = New CustomColumnExpressionHelper(AvailColumns, userLookAndFeel, ConnectionString())

                SelectedColumns = New ObservableCollection(Of Column)
                availableColumn.SetSelectedColumns(SelectedColumns)

                If ReportLayoutId.HasValue Then
                    ReportLayout = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Single(Function(rl) rl.ID = ReportLayoutId)
                    ReportLogger = ReportLogger.ForContext("ReportLayoutName", ReportLayout.LayoutName)
                    If Not UserName.Equals(ReportLayout.UserName, StringComparison.CurrentCultureIgnoreCase) Then
                        bbiSaveChanges_UserLayout.Visibility = False.ToBarItemVisibility
                        bbiClearGridLayout.Visibility = IsAdmin
                        bbiShareReport.Visibility = False.ToBarItemVisibility
                    Else
                        bbiSaveChanges_UserLayout.Visibility = True.ToBarItemVisibility
                        bbiShareReport.Visibility = True.ToBarItemVisibility
                    End If
                    LoadLayout()
                    bbiSaveChanges_UserLayout.Enabled = True
                    bbiSaveAsNew_UserLayout.Enabled = True
                    bbiSaveChanges_AdminLayout.Visibility = (Not ReportLayout.ParentReportLayoutId.HasValue).ToBarItemVisibility
                Else
                    If Not IsAdmin Then Throw New Exception("No Report Layout Was Detected. Only admin users can save new layouts.")
                    ReportLogger.Information("No layout to load")
                    Text = $"Report Builder [{Report.Name}]"
                    bbiSaveChanges_AdminLayout.Visibility = False.ToBarItemVisibility
                    bbiSaveChanges_UserLayout.Visibility = False.ToBarItemVisibility
                    bbiSaveAsNew_UserLayout.Enabled = False
                    rgFilterBy.SelectedIndex = 0
                    bbiClearGridLayout.Visibility = False.ToBarItemVisibility
                    bbiShareReport.Visibility = False.ToBarItemVisibility
                End If
                bbiExportData.Visibility = (ReportLayoutId.HasValue).ToBarItemVisibility
                bbiEmailReport.Visibility = (ReportLayoutId.HasValue).ToBarItemVisibility
                bbiSaveAsNew_UserLayout.Visibility = (ReportLayoutId.HasValue).ToBarItemVisibility
                bsiReportName.Caption = $"Report Name: {Report.Name}"
                bsiReportLayoutName.Caption = "Report Layout: " & If(ReportLayout IsNot Nothing, ReportLayout.LayoutName, "N/A")
                ReportLogger.Information("Initialized Report Builder - {ReportName} - {LayoutName}. With ReportId: {ReportId} LayoutId: {LayoutId}", Report.Name, ReportLayout?.LayoutName, Report.ID, ReportLayout?.ID)

                ucOrderGroup.SetColumn(SelectedColumns, IsAdmin)
                SetGroupFilter()
                'MainForm.RibbonControl1.SelectedPage = MainForm.RibbonControl1.Pages(0)
                view_CompanySumarryBindingSource.DataSource = db.view_CompanySumarries.ToList()
                slueCoNum_EditValueChanged(Nothing, Nothing)

                UpdateReportFiltersVisibility()
                ApplyAllColumnSettingsToGrid()
            End Using

            bbiScheduleReport.Enabled = ReportLayoutId.HasValue AndAlso UserName.Equals(ReportLayout?.UserName, StringComparison.CurrentCultureIgnoreCase)
        Catch ex As Exception
            ReportLogger.Error(ex, "Error in InitializeForm")
            DisplayErrorMessage("Error Initializing form", ex)
        End Try
    End Sub

    Private Function GetIsAdmin() As Boolean
        Return If(Permissions Is Nothing, False, Permissions.AllowSetup)
    End Function

    Private Async Sub frmReportBuilder_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        Try
            If Not AllowReporting() Then
                DisplayErrorMessage("Reporting is currently unavailable.")
                Close()
                Return
            End If
            Dim currentSkin As DevExpress.Skins.Skin = DevExpress.Skins.EditorsSkins.GetSkin(Me.LookAndFeel)
            AddHandler Me.LookAndFeel.StyleChanged, AddressOf frmReportBuilder_Shown
            Dim element = currentSkin(DevExpress.Skins.EditorsSkins.SkinEditorButton)
            element.Image.Image = Nothing
            Me.LookAndFeel.UpdateStyleSettings()

            If IsScheduledExport Then
                Return
            End If

            If DefaultSelectedConum > 0 Then
                ucSearchCompany.SetCompany(pceCoNum, DefaultSelectedConum)
            Else
                Await Task.Delay(250)
                pceCoNum.Properties.PopupFormSize = New Size(1050, 450)
                pceCoNum.ShowPopup()
            End If
        Catch ex As Exception
            ReportLogger.Error(ex, "Error in frmReportBuilder_Shown")
            DisplayErrorMessage("Error frmReportBuilder_Shown", ex)
        End Try
    End Sub

    Private Function AllowReporting() As Boolean
        Try
            Using db = New dbEPDataDataContext(ConnectionString)
                db.ObjectTrackingEnabled = False
                If Not db.FrontDeskOptions.Single.AllowReporting Then
                    Logger.Fatal("Reporting has bean diabled in settings table")
                    Return False
                End If
            End Using
            Return True
        Catch ex As Exception
            DisplayErrorMessage("Error in AllowReporting", ex)
            Return False
        End Try
    End Function

    Private Sub LoadAvailableColumns()
        Try
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                Report = db.CustomReports.Single(Function(r) r.ID = ReportId)
                AvailColumns = New List(Of CustomReportColumnTemplate)
                For Each table In Report.CustomReportTableLinks
                    AvailColumns.AddRange(From a In table.CustomReportTable.CustomReportColumnTemplates
                                          Where (a.IsHidden Is Nothing OrElse a.IsHidden = False) _
                                    AndAlso a.ColumnDescription.IsNotNullOrWhiteSpace() _
                                    AndAlso a.FieldName.IsNotNullOrWhiteSpace)
                Next
                AvailColumns = AvailColumns.OrderBy(Function(a) a.ColumnSortOrder).ToList
                AvailColumns.Select(Function(c) c.CustomReportTable.Name).ToList
                availableColumn.SetAvailableColumns(AvailColumns.OrderBy(Function(c) c.ColumnSortOrder).ToList(), IsAdmin)

                Dim columns As New DevExpress.XtraEditors.Filtering.FilterColumnCollection()
                For Each col In AvailColumns
                    Dim newCol = GetColumnType(col)
                    columns.Add(newCol)
                Next
                FilterControlAdvancedFilter.SetFilterColumnsCollection(columns)
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadAvailableColumns", ex)
        End Try
    End Sub

    Private Sub UpdateReportFiltersVisibility()
        Try
            lciEmptStatus.Visibility = Report.EmpStatusParam.ToBarItemVisibility
            lciEmpType.Visibility = Report.EmpTypeParam.ToBarItemVisibility
            lcgEmployeeFilter.Visibility = (Report.EmpStatusParam OrElse Report.EmpTypeParam).ToBarItemVisibility

            lciDateFilters.Visibility = ((Report.FilterByDateOrPrNum AndAlso (rgFilterBy.SelectedIndex = 1 OrElse rgFilterBy.SelectedIndex = 2)) OrElse (Not Report.FilterByDateOrPrNum AndAlso Report.DateRangeParam)).ToBarItemVisibility
            lcgPayrollFilter.Visibility = ((Report.FilterByDateOrPrNum AndAlso rgFilterBy.SelectedIndex = 0) OrElse (Not Report.FilterByDateOrPrNum AndAlso Report.PrNumParam)).ToBarItemVisibility()

            lciFilterByPrOrDate.Visibility = (Report.PrNumParam AndAlso Report.DateRangeParam).ToBarItemVisibility
            lcgAllocationFilter.Visibility = Report.AllocateParam.ToBarItemVisibility

            lciIncludePayDedMemoHistory.Visibility = Report.IncludePayDedMemoHistory.ToBarItemVisibility
        Catch ex As Exception
            DisplayErrorMessage("Error in UpdateReportFiltersVisibility", ex)
        End Try
    End Sub

    Private Sub RibbonControl1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles RibbonControl1.ItemClick, bbiClearAllColumns.ItemClick
        Try
            ReportLogger.Information("User clicked on button: {ButtonName}", e.Item.Caption)
            If e.Item Is bbiRunReport Then
                If Not SelectedColumns.Any Then
                    DisplayErrorMessage("Please select at least one column.", Nothing, False)
                    Exit Sub
                End If
                LoadReportDataAsync(False).GetAwaiter()
            ElseIf e.Item Is bbiExportData Then
                ExportData()
            ElseIf e.Item Is bbiSaveAsNew_UserLayout Then
                Dim newLayout = SaveUserLayout(Me)
                If newLayout IsNot Nothing Then
                    ReportLayoutId = newLayout.ID
                    InitializeForm()
                End If
            ElseIf e.Item Is bbiSaveChanges_UserLayout Then
                SaveUserLayout(Me, ReportLayoutId)
            ElseIf e.Item Is bbiEmailReport Then
                EmailReport()
            ElseIf e.Item Is bbiViewSql Then
                Using frm = New frmErrorDetails
                    frm.meSql.Text = BuildSelectSql()
                    frm.ShowDialog()
                End Using
            ElseIf e.Item Is bbiSaveAsNew_AdminLayout Then
                SaveAdminLayout()
            ElseIf e.Item Is bbiSaveChanges_AdminLayout Then
                UpdateAdminLayout()
            ElseIf e.Item Is bbiReloadReport Then
                InitializeForm()
            ElseIf e.Item Is bbiEditReport Then
                EditReport(Nothing)
            ElseIf e.Item Is bciUseAdminMode Then
                IsAdmin = bciUseAdminMode.Checked
            ElseIf e.Item Is bbiResetAllCustomColumnCaption Then
                ResetAllColumnCaptions()
            ElseIf e.Item Is bbiClearAllColumns Then
                RemoveAllColumns()
            ElseIf e.Item Is bbiClearGridLayout Then
                ClearGridUserLayout()
            ElseIf e.Item Is bbiAddAllColumns Then
                AddAllColumns()
            ElseIf e.Item Is bbiShareReport Then
                ShowShareReportOptions()
            ElseIf e.Item Is bbiScheduleReport Then
                Try
                    Using frm = New frmExportSetup(Nothing, Me.pceCoNum.EditValue, Me.ReportLayoutId) With {.DefaultEmailSubjectName = ReportLayout?.LayoutName}
                        Dim results = frm.ShowDialog
                    End Using
                Catch ex As Exception
                    DisplayErrorMessage("Error setting report schedule", ex, True)
                End Try
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in RibbonControl1_ItemClick", ex)
        End Try
    End Sub

    Private Sub ExportData()
        Try
            Using FD As New SaveFileDialog With {.DefaultExt = ".xlsx", .AddExtension = True, .Filter = "Excel Files|*.xls*", .FileName = ReportLayout?.LayoutName}
                If FD.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                    gvReportData.OptionsPrint.AutoWidth = False
                    gvReportData.Export(DevExpress.XtraPrinting.ExportTarget.Xlsx, FD.FileName)
                    Dim P As New ProcessStartInfo(FD.FileName)
                    Process.Start(P)
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error exporting data.", ex, True)
        End Try
    End Sub

    Private Sub ShowShareReportOptions()
        Try
            Using frm = New frmShareReport(ReportLayoutId)
                frm.ShowDialog()
            End Using
        Catch ex As Exception
            DisplayErrorMessage($"", ex)
        End Try
    End Sub

    Private Sub EditReport(db As dbEPDataDataContext)
        Try
            If db Is Nothing Then
                db = New dbEPDataDataContext(ConnectionString)
                Report = db.CustomReports.Single(Function(r) r.ID = Report.ID)
            End If
            Dim frm = New frmCustomReportAddOrEdit(Report)
            If frm.ShowDialog = DialogResult.OK Then
                If db.SaveChanges Then
                    db.Dispose()
                    InitializeForm()
                Else
                    EditReport(db)
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in EditReport", ex)
        End Try
    End Sub

    Private Sub DisplayErrorMessage(message As String, Optional ex As Exception = Nothing, Optional addMessage As Boolean = True)
        Try
            If ex IsNot Nothing Then
                ReportLogger.Error(ex, message)
            Else
                ReportLogger.Debug(message)
            End If
            MainForm.CloseWaitForm
            If IsAdmin Then
                modGlobals.DisplayErrorMessage(message, ex)
            Else
                If addMessage Then
                    message &= vbCrLf & "We apologize, your request could not be completed." & vbCrLf & "The administrator had been notified and this will be fixed soon. Please try again later."
                End If
                Dim action = New FlyoutAction With {.Caption = "Error", .Description = message}
                action.Commands.Add(FlyoutCommand.OK)
                DevExpress.XtraBars.Docking2010.Customization.FlyoutDialog.Show(Me, action)
            End If
        Catch
        End Try
    End Sub

    Private Sub LoadLayout()
        Try
            Text = $"Report Builder [{ReportLayout.LayoutName}]"
            If ReportLayout.Layout.IsNotNullOrWhiteSpace Then
                Dim byteArray = System.Text.Encoding.ASCII.GetBytes(ReportLayout.Layout)
                If byteArray.Length > 0 Then
                    Dim ms = New MemoryStream(byteArray)
                    gvReportData.RestoreLayoutFromStream(ms, OptionsLayoutBase.FullLayout)
                End If
            End If


            LayoutSettings = Newtonsoft.Json.JsonConvert.DeserializeObject(Of ReportLayoutSettingsJson)(ReportLayout.SelectedColumns)

            If LayoutSettings IsNot Nothing Then
                bciAutoRefresh.Checked = LayoutSettings.AutoRefresh
                cbeEmpStatusFilter.EditValue = LayoutSettings.EmployeeStatusFilter
                cbeEmpTypeFilter.EditValue = LayoutSettings.EmployeeTypeFilter
                rgFilterBy.SelectedIndex = LayoutSettings.rgFilterBy
                cbeDateRange.EditValue = LayoutSettings.DateRange
                If LayoutSettings.DateRange = "Custom" Then
                    If LayoutSettings.CustomDateFromDate.HasValue Then DateEdit1.DateTime = LayoutSettings.CustomDateFromDate
                    If LayoutSettings.CustomDateToDate.HasValue Then DateEdit2.DateTime = LayoutSettings.CustomDateToDate
                End If
                FilterControlAdvancedFilter.FilterString = LayoutSettings.AdvancedFilter
                FilterControlGroupFilter.FilterString = LayoutSettings.GroupFilter
                LoadDateFilters(cbeDateRange.Text)
                cbeAllocation.EditValue = LayoutSettings.AllocationFilter
                ceIncludePayDedMemoHistory.Checked = LayoutSettings.IncludePayDedMemoHistory
                Dim missingColumns = New List(Of Column)
                For Each col In LayoutSettings.SelectedColumns
                    If SelectedColumns.Any(Function(cAlias) cAlias.ColumnAlias = col.ColumnAlias) Then
                        ReportLogger.Error("Duplicate ColumnAliasName {ColumnAliasName} found in layout settings json", col.ColumnAlias)
                        Continue For
                    End If

                    Dim reportColumn = AvailColumns.SingleOrDefault(Function(a) a.ID = col.CustomReportColumnId)
                    If reportColumn IsNot Nothing Then
                        col.ColumnTemplate = reportColumn
                        SelectedColumns.Add(col)
                    ElseIf col.Expression.IsNotNullOrWhiteSpace() Then
                        SelectedColumns.Add(col)
                    Else
                        missingColumns.Add(col)
                    End If
                Next

                If missingColumns.Any Then
                    ReportLogger.Debug("There's {MissingColumnsCount} missing columns", missingColumns.Count)
                    For index = missingColumns.Count - 1 To 0 Step -1
                        Dim missingCol = missingColumns(index)
                        Dim matchedColByTable = AvailColumns.Where(Function(c) c.FieldName = missingCol.Name AndAlso c.Table.ActualName = missingCol.Table.ActualName).ToList
                        If matchedColByTable.Count = 1 Then
                            ReportLogger.Debug("Replacing ColumnId {OldColId} with ColumnId {NewColId}", missingCol.CustomReportColumnId, matchedColByTable.Single.ID)
                            missingCol.ColumnTemplate = matchedColByTable.Single
                            SelectedColumns.Add(missingCol)
                            missingColumns.Remove(missingCol)
                        End If
                    Next

                    missingColumns = missingColumns.Where(Function(c) c.Caption.IsNotNullOrWhiteSpace).ToList
                    If missingColumns.Any Then
                        Dim message = String.Join(vbCrLf, missingColumns.Select(Function(c) c.Caption))
                        If XtraMessageBox.Show($"The following ({missingColumns.Count}) columns isn't available anymore for this report{vbCrLf}{message}{vbCrLf}Would you like to remove it from the grid as well?", "Remove missing columns?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                            For Each col1 In missingColumns
                                RemoveColumn(False, col1)
                            Next
                        End If
                    End If

                    Dim gridColsToRemove = New List(Of GridColumn)
                    For Each gridCol As GridColumn In gvReportData.Columns
                        If Not SelectedColumns.Any(Function(c) c.ColumnAlias = gridCol.FieldName) Then
                            gridColsToRemove.Add(gridCol)
                        End If
                    Next
                    For Each gc In gridColsToRemove
                        gvReportData.Columns.Remove(gc)
                    Next
                End If

            End If
        Catch ex As Exception
            ReportLogger.Error(ex, "Error loading report from layout")
            DisplayErrorMessage("Error loading report from layout", ex)
        End Try
    End Sub

    Private Sub frmRebortBuilder_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        Try
            Dim frm = New frmReportDesignerClosing
            If Not ReportLayoutId.HasValue AndAlso SelectedColumns.Any Then
                Dim results = frm.ShowDialog
                If results = DialogResult.Cancel Then
                    e.Cancel = True
                    Exit Sub
                ElseIf results = DialogResult.Yes Then
                    SaveUserLayout(Me)
                End If
            End If

            If ReportLayout IsNot Nothing Then
                Dim json = GetLayoutSettingsJson()
                If ReportLayout.SelectedColumns <> json Then
                    Dim results = frm.ShowDialog
                    If results = DialogResult.Cancel Then
                        e.Cancel = True
                        Exit Sub
                    ElseIf results = DialogResult.Yes Then
                        SaveUserLayout(Me, IIf(UserName.Equals(ReportLayout.UserName, StringComparison.CurrentCultureIgnoreCase), ReportLayoutId, Nothing))
                    End If
                End If
            End If

            ReportLogger.Information("Closing Form")
        Catch ex As Exception
            DisplayErrorMessage("Error in frmRebortBuilder_FormClosing", ex)
        End Try
    End Sub

    Private Sub AddAllColumns(Optional groupName As String = Nothing, Optional startingVisibleIndex As Integer? = Nothing)
        lcRoot.ShowProgessPanel
        Application.DoEvents()
        Try
            Dim oldAutoRefreshSettings = bciAutoRefresh.Checked
            bciAutoRefresh.Checked = False
            For Each col In AvailColumns.Where(Function(c) groupName.IsNullOrWhiteSpace OrElse c.GroupName = groupName).OrderBy(Function(c) c.ColumnSortOrder).ToList
                AddColumn(col, startingVisibleIndex)
                If startingVisibleIndex.HasValue Then startingVisibleIndex += 1
            Next
            bciAutoRefresh.Checked = oldAutoRefreshSettings
            LoadReportDataAsync(True).GetAwaiter()
            availableColumn.RefreshData()
        Catch ex As Exception
            DisplayErrorMessage("Error Adding all columns", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub AddColumn(SelectedCol As CustomReportColumnTemplate, Optional visibleIndex As Integer? = Nothing)
        Try
            ReportLogger.Information("Entering AddColumn Id: {Id} Description: {Description}", SelectedCol.ID, SelectedCol.ColumnDescription)

            Dim ColumnAlias As String = GetColumnAlias()

            Dim table = New Table(SelectedCol.CustomReportTable.Name, SelectedCol.CustomReportTable.Alias)
            Dim ColCaption = SelectedCol.Caption
            Dim Col As New Column(table, SelectedCol.ID, SelectedCol, ColumnAlias, ColCaption) With {.HideColumnInOutput = SelectedCol.HideColumnInOutput}
            Dim newGridColumn As GridColumn = AddColumn(Col, visibleIndex)

            If (SelectedCol.ColumnSummaryType.IsNotNullOrWhiteSpace()) Then
                If [Enum].IsDefined(GetType(DevExpress.Data.SummaryItemType), SelectedCol.ColumnSummaryType) Then
                    Dim sumType As DevExpress.Data.SummaryItemType = DirectCast([Enum].Parse(GetType(DevExpress.Data.SummaryItemType), SelectedCol.ColumnSummaryType), DevExpress.Data.SummaryItemType)
                    Dim sumItem = newGridColumn.Summary.Add(sumType)
                Else
                    ReportLogger.Error("Invalid value specified for ColumnSummaryType {ColumnSummaryType}", SelectedCol.ColumnSummaryType)
                End If
            End If

            gvReportData.MakeColumnVisible(newGridColumn)
        Catch ex As Exception
            If SelectedCol Is Nothing Then
                ReportLogger.Error(ex, "SelectedCol is nothing")
            Else
                ReportLogger.Error(ex, "Error adding ColumnId: {ColId} to report", SelectedCol.ID)
            End If
            DisplayErrorMessage("Error adding Column", ex)
        End Try
    End Sub

    Private Function GetColumnAlias() As String
        Dim ColNum = SelectedColumns.Count + 1
        Dim ColumnAlias As String = ""
        Do While True
            ColumnAlias = "Col" & ColNum
            If (From A In SelectedColumns Where A.ColumnAlias = ColumnAlias).Any Then
                ColNum += 1
            ElseIf TryCast(BindingSourceReport.DataSource, DataTable)?.Columns().Contains(ColumnAlias) Then
                ColNum += 1
            Else
                Exit Do
            End If
        Loop

        Return ColumnAlias
    End Function

    Private Function AddColumn(col As Column, Optional visibleIndex As Integer? = Nothing) As GridColumn
        Try
            Dim newGridColumn As GridColumn = Nothing
            ReportLogger.Information("New Column ColumnAliasName {Name}", col.ColumnAlias)

            If SelectedColumns.Any(Function(c) c.ColumnAlias = col.ColumnAlias) Then
                Throw New Exception($"Duplicate Column FieldNmae: {col.ColumnAlias}")
            End If
            SelectedColumns.Add(col)

            Dim colType As Type
            Select Case col.DataType
                Case "Numeric"
                    colType = Type.GetType("System.Decimal")
                Case "DateTime"
                    colType = Type.GetType("System.DateTime")
                Case Else
                    colType = Type.GetType("System.String")
            End Select

            If col.HideColumnInOutput Then
                Dim col1 = Me.gvReportData.Columns.Add()
                col1.FieldName = col.ColumnAlias
                col1.Caption = col.Caption
                newGridColumn = col1
            Else
                Dim col1 = Me.gvReportData.Columns.AddVisible(col.ColumnAlias, col.Caption)
                newGridColumn = col1
                If visibleIndex IsNot Nothing Then col1.VisibleIndex = visibleIndex
            End If
            newGridColumn.Tag = col

            availableColumn.RefreshedFocusedRow()

            ucOrderGroup.VerifyAggregateColumns()
            ucOrderGroup.RefreshDataSource()
            SetGroupFilter()
            Dim t = LoadReportDataAsync(True)
            If col.SelectCodes.IsNotNullOrWhiteSpace() Then
                ShowColumnOptions(newGridColumn)
            End If
            Return newGridColumn
        Catch ex As Exception
            DisplayErrorMessage("Error in AddColumn", ex)
            Return Nothing
        End Try
    End Function

    Private Sub RemoveAllColumns(Optional groupName As String = Nothing)
        Try
            Dim allCols = SelectedColumns.ToArray
            For Each col In allCols.Where(Function(c) groupName.IsNullOrWhiteSpace OrElse (c IsNot Nothing AndAlso c.ColumnTemplate.GroupName = groupName)).ToList
                RemoveColumn(groupName.IsNotNullOrWhiteSpace, col)
            Next
            If groupName.IsNullOrWhiteSpace Then
                FilterControlGroupFilter.FilterString = String.Empty
            End If

            If groupName.IsNullOrWhiteSpace OrElse gvReportData.Columns.Count = 0 Then
                Dim allGridColumns = gvReportData.Columns.ToArray
                For Each col In allGridColumns
                    gvReportData.Columns.Remove(col)
                Next
                Me.BindingSourceReport.DataSource = Nothing
            End If
            availableColumn.RefreshData()
        Catch ex As Exception
            DisplayErrorMessage("Error in RemoveAllColumns", ex)
        End Try
    End Sub

    Private Sub SetGroupFilter(sender As Object, e As EventArgs) Handles ucOrderGroup.GroupingOrAggregateChanged
        SetGroupFilter()
    End Sub

    Public Sub SetGroupFilter()
        Try
            Dim reportColumns = SelectedColumns.Where(Function(c) c.Grouping OrElse c.Aggregate.IsNotNullOrWhiteSpace)
            lcgGroupFilter.Enabled = reportColumns.Any
            For Each col As GridColumn In gvReportData.Columns
                If col.FieldName.IsNotNullOrWhiteSpace Then FilterControlGroupFilter.FilterColumns(col.FieldName).SetColumnCaption(col.Caption)
            Next
            FilterControlGroupFilter.Model.RebuildElements()
        Catch ex As Exception
            DisplayErrorMessage("Error in SetGroupFilter", ex)
        End Try
    End Sub

    Private Sub RemoveColumn(sender As Object, e As EventArgs) Handles ucOrderGroup.RemoveColumnEvent
        RemoveColumn(True, DirectCast(sender, Column))
    End Sub

    Private Function RemoveColumn(checkIfColumnIsInGroupFilter As Boolean, col1 As Column, Optional col As GridColumn = Nothing) As Boolean
        Try
            If checkIfColumnIsInGroupFilter Then
                Dim columns = SqlBuilder.GetOperandProperties(FilterControlGroupFilter.FilterCriteria)
                If columns.Any(Function(c) c.PropertyName = col1.ColumnAlias) Then
                    DisplayErrorMessage("This column is used in the Group Filter, Please remove it first from the Group Filter, and try again to remove the column from the grid.")
                    Return False
                End If
            End If

            Me.SelectedColumns.Remove(col1)
            If col Is Nothing Then
                col = gvReportData.Columns.ColumnByFieldName(col1.ColumnAlias)
            End If
            If col IsNot Nothing Then Me.gvReportData.Columns.Remove(col)
            Return True
        Catch ex As Exception
            DisplayErrorMessage("Error in RemoveColumn", ex)
            Return False
        End Try
    End Function

    Private Async Function LoadReportDataAsync(isAutoRefresh As Boolean) As Task
        If Not AllowReporting() Then
            DisplayErrorMessage("Sorry reporting is currently unavailable.")
            Close()
            Return
        End If
        If SelectedColumns Is Nothing Then Exit Function
        If isAutoRefresh AndAlso Not bciAutoRefresh.Checked Then Exit Function
        Dim finalSql As String = ""
        Try
            gcReportData.BeginUpdate()
            If Not SelectedColumns.Any Then Exit Function
            finalSql = BuildSelectSql()
            If finalSql.IsNullOrWhiteSpace() Then Exit Function
            Dim paramList As List(Of SqlParameter) = GetSqlParameters()
            'lcRoot.ShowProgessPanel
            pccProgressPanel.Parent = Me
            pccProgressPanel.Show()
            pccProgressPanel.BringToFront()
            lcRoot.Enabled = False
            Application.DoEvents()
            Dim con = New Brands.DAL.SqlService(ConnectionString)
            cancellationToken = New CancellationTokenSource()
            Dim tbl = Await con.QueryAsync(finalSql, cancellationToken, paramList.ToArray)
            Me.BindingSourceReport.DataSource = tbl
            gvReportData.BestFitColumns()
        Catch ex As Exception
            pccProgressPanel.Hide()
            ReportLogger.Error(ex, "Error getting report data. {Sql}", finalSql)
            If IsAdmin Then
                Dim frm = New frmErrorDetails
                frm.meSql.Text = finalSql
                frm.meError.Text = ex.ToString()
                frm.ShowDialog()
            ElseIf Not ex.Message.Contains("Operation cancelled by user") Then
                DisplayErrorMessage("Error getting report data", ex)
            End If
        Finally
            gcReportData.EndUpdate()
            lcRoot.Enabled = True
            pccProgressPanel.Hide()
        End Try
    End Function


    Private Sub GroupControl1_CustomButtonClick(sender As Object, e As Docking2010.BaseButtonEventArgs) Handles GroupControl1.CustomButtonClick
        Try
            cancellationToken.Cancel()
        Catch ex As Exception
            DisplayErrorMessage("Error in GroupControl1_CustomButtonClick", ex)
        End Try
    End Sub

    Private Function GetSqlParameters() As List(Of SqlParameter)
        Try
            Dim paramList = New List(Of SqlParameter)
            paramList.Add(New SqlParameter("CoNum", SelectedCoNum))
            If Report.DateRangeParam Then
                paramList.Add(New SqlParameter("FromDate", If(DateEdit1.DateTime < System.Data.SqlTypes.SqlDateTime.MinValue.Value OrElse (Report.FilterByDateOrPrNum AndAlso rgFilterBy.SelectedIndex = 0), DBNull.Value, DateEdit1.DateTime)) With {.SqlDbType = SqlDbType.DateTime})
                paramList.Add(New SqlParameter("ToDate", If(DateEdit2.DateTime < System.Data.SqlTypes.SqlDateTime.MinValue.Value OrElse (Report.FilterByDateOrPrNum AndAlso rgFilterBy.SelectedIndex = 0), DBNull.Value, DateEdit2.DateTime)) With {.SqlDbType = SqlDbType.DateTime})
            End If
            If Report.PrNumParam Then
                Dim p = New SqlParameter("PrNum", SqlDbType.Decimal)
                p.Value = If(luePayrollFilter.EditValue = Nothing OrElse (Report.FilterByDateOrPrNum AndAlso rgFilterBy.SelectedIndex = 1), DBNull.Value, luePayrollFilter.EditValue)
                paramList.Add(p)
            End If
            If Report.FilterByDateOrPrNum Then
                paramList.Add(New SqlParameter("FilterType", GetFilterTypeText))
            End If
            If Report.EmpStatusParam Then paramList.Add(New SqlParameter("EmpStatus", If(cbeEmpStatusFilter.EditValue, DBNull.Value)))
            If Report.EmpTypeParam Then paramList.Add(New SqlParameter("EmpType", If(cbeEmpTypeFilter.EditValue, DBNull.Value)))
            If Report.AllocateParam Then paramList.Add(New SqlParameter("Allocate", If(cbeAllocation.EditValue, DBNull.Value)))
            If Report.IncludePayDedMemoHistory Then paramList.Add(New SqlParameter("IncludePayDedMemoHistory", ceIncludePayDedMemoHistory.Checked))
            paramList.Add(New SqlParameter("P0", DBNull.Value))
            Return paramList
        Catch ex As Exception
            DisplayErrorMessage("Error in GetSqlParameters", ex)
            Return Nothing
        End Try
    End Function

    Private Function GetFilterTypeText() As String
        If rgFilterBy.SelectedIndex = 0 Then
            Return "Pr"
        ElseIf rgFilterBy.SelectedIndex = 1 Then
            Return "ChkDte"
        ElseIf rgFilterBy.SelectedIndex = 2 Then
            Return "PrEnd"
        Else
            Throw New ArgumentOutOfRangeException("rgFilterBy", rgFilterBy.SelectedIndex)
        End If
    End Function

    Private Function BuildSelectSql() As String
        Dim finalSql As String = ""
        Try
            ucOrderGroup.VerifyAggregateColumns()
            Dim _sqlBuilder = New SqlBuilder(Report, SelectedColumns.ToList, GetSqlParameters(), expressionHelper) With {.FilterCriteria = FilterControlAdvancedFilter.FilterCriteria, .GroupFilterCriteria = FilterControlGroupFilter.FilterCriteria}
            finalSql = _sqlBuilder.BuildSelectSql()
        Catch ex As Exception
            DisplayErrorMessage("Error building sql statement")
        End Try
        Return finalSql
    End Function

    Private Sub GridViewReport_ColumnPositionChanged(sender As Object, e As EventArgs) Handles gvReportData.ColumnPositionChanged
        Try
            Dim col As GridColumn = sender
            If col.VisibleIndex = -1 Then
                Dim col1 = SelectedColumns.SingleOrDefault(Function(c) c.ColumnAlias = col.FieldName)
                If Not col1?.HideColumnInOutput Then
                    If Not RemoveColumn(True, col1, col) Then
                        col.VisibleIndex = 0
                    End If
                    availableColumn.RefreshData()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error removing column", ex)
        End Try
    End Sub

    Private Sub SaveAdminLayout()
        Try
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                Dim reportLayout = New CustomReportLayout With {.CustomReportId = Report.ID, .AuthorName = UserName, .IsAdminLayout = True, .AddDate = Now, .ChgDate = Now}
                Using frm As New frmReportLayoutAddOrEdit(reportLayout)
                    If frm.ShowDialog <> DialogResult.OK Then Exit Sub
                End Using
                db.CustomReportLayouts.InsertOnSubmit(reportLayout)
                reportLayout.Layout = gvReportData.GetGridLayout
                reportLayout.SelectedColumns = GetLayoutSettingsJson()
                db.SaveChanges()
            End Using
        Catch ex As Exception
            ReportLogger.Error(ex, "Error saving report layout")
            DisplayErrorMessage("Error saving report layout", ex)
        End Try
    End Sub

    Private Sub UpdateAdminLayout()
        Try
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                Dim rl = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Single(Function(r) r.ID = ReportLayout.ID)
                rl.Layout = gvReportData.GetGridLayout
                rl.SelectedColumns = GetLayoutSettingsJson()
                rl.ChgDate = Now
                Using frm As New frmReportLayoutAddOrEdit(rl)
                    If frm.ShowDialog <> DialogResult.OK Then Exit Sub
                End Using
                db.SaveChanges()
            End Using
        Catch ex As Exception
            ReportLogger.Error(ex, "Error updating report layout")
            DisplayErrorMessage("Error updating report layout", ex)
        End Try
    End Sub

    Private Async Sub slueCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles pceCoNum.EditValueChanged
        Try
            Dim num As Decimal
            If Decimal.TryParse(nz(pceCoNum.EditValue, ""), num) Then
                SelectedCoNum = num
            ElseIf IsScheduledExport Then
                SelectedCoNum = SelectedCoNum
            Else
                SelectedCoNum = Nothing
            End If
            ReportLogger.Information("CoNum changed to: {CoNum}", SelectedCoNum)

            tnpReportData.PageVisible = SelectedCoNum.HasValue
            tnpOrderGroup.PageVisible = SelectedCoNum.HasValue
            tnpNoCoSelected.PageVisible = Not SelectedCoNum.HasValue
            rpgOptions.Enabled = SelectedCoNum.HasValue
            lcgReportFilters.Visibility = SelectedCoNum.HasValue.ToBarItemVisibility
            ddbAdvancedOptions.Enabled = SelectedCoNum.HasValue

            If SelectedCoNum Is Nothing Then
                TabPane1.SelectedPage = tnpNoCoSelected
            Else
                TabPane1.SelectedPage = tnpReportData
                Dim prList = DB.PAYROLLs.Where(Function(c) c.CONUM = SelectedCoNum) _
                .Where(Function(a) a.CHECK_DATE.HasValue) _
                .Select(Function(A) New With {.PrNum = A.PRNUM,
                                                        .CheckDate = A.CHECK_DATE,
                                                        .PrDesc = A.PR_DESCR,
                                                        .Value = [String].Format("{0}  {1} {2}", A.PRNUM, A.CHECK_DATE.Value.ToShortDateString(), A.PR_DESCR)}) _
                    .OrderByDescending(Function(a) a.PrNum).ToList()
                luePayrollFilter.Properties.DataSource = prList
                luePayrollFilter.EditValue = prList.FirstOrDefault?.PrNum
                LoadDateFilters(cbeDateRange.Text)
                If Not IsScheduledExport Then
                    Await LoadReportDataAsync(True)
                    FlyoutPanel1.ShowBeakForm()
                End If
            End If
        Catch ex As Exception
            ReportLogger.Error(ex, "Error in changing Co#")
            DisplayErrorMessage("Error loading report", ex)
        End Try
    End Sub

    Private Function GetLayoutSettingsJson() As String
        Dim settings = New ReportLayoutSettingsJson With {
            .AutoRefresh = bciAutoRefresh.Checked,
            .EmployeeTypeFilter = cbeEmpTypeFilter.EditValue,
            .EmployeeStatusFilter = cbeEmpStatusFilter.EditValue,
            .DateRange = cbeDateRange.EditValue,
            .rgFilterBy = rgFilterBy.SelectedIndex,
            .AllocationFilter = cbeAllocation.EditValue,
            .CustomDateFromDate = DateEdit1.DateTime,
            .CustomDateToDate = DateEdit2.DateTime,
            .IncludePayDedMemoHistory = ceIncludePayDedMemoHistory.Checked,
            .AdvancedFilter = FilterControlAdvancedFilter.FilterCriteria?.ToString(),
            .GroupFilter = FilterControlGroupFilter.FilterCriteria?.ToString()
        }
        settings.SelectedColumns = SelectedColumns.ToList
        Dim result = Newtonsoft.Json.JsonConvert.SerializeObject(settings)
        Return result
    End Function

    Private Sub TabPane1_SelectedPageChanged(sender As Object, e As DevExpress.XtraBars.Navigation.SelectedPageChangedEventArgs) Handles TabPane1.SelectedPageChanged
        Try
            If e.OldPage Is tnpOrderGroup AndAlso e.Page Is tnpReportData Then
                ApplyAllColumnSettingsToGrid()
                LoadReportDataAsync(True).GetAwaiter()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in TabPane1_SelectedPageChanged", ex)
        End Try
    End Sub

    Private Sub ResetAllColumnCaptions()
        Try
            For Each i In SelectedColumns
                If i.ColumnTemplate.DefaultColumnCaption.IsNotNullOrWhiteSpace Then i.Caption = i.ColumnTemplate.DefaultColumnCaption
            Next
            ApplyAllColumnSettingsToGrid()
        Catch ex As Exception
            DisplayErrorMessage("Error in ApplyCustomColumnCaption", ex)
        End Try
    End Sub

    Private Sub ApplyAllColumnSettingsToGrid()
        Try
            ucOrderGroup.PostEditor()
            For Each col In SelectedColumns
                ApplyColumnSettingsToGrid(col)
            Next
            SetGroupFilter()
        Catch ex As Exception
            ReportLogger.Error(ex, "Error ApplyColumnSettingsToGrid")
            DisplayErrorMessage("Error in Grid Layout", ex)
        End Try
    End Sub

    Private Sub ApplyColumnSettingsToGrid(col As Column)
        Try
            Dim gridCol As GridColumn = gvReportData.Columns.ColumnByFieldName(col.ColumnAlias)
            If gridCol Is Nothing Then Exit Sub

            If col.Caption.IsNullOrWhiteSpace Then
                col.Caption = AvailColumns.SingleOrDefault(Function(c) c.ID = col.CustomReportColumnId)?.DefaultColumnCaption
            End If
            gridCol.Caption = col.Caption

            If col.HideColumnInOutput = gridCol.Visible Then
                gridCol.Visible = Not col.HideColumnInOutput
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in ApplyColumnSettingsToGrid", ex)
        End Try
    End Sub

    Private Sub cbeEmpStatusFilter_EditValueChanged(sender As Object, e As EventArgs) Handles cbeEmpStatusFilter.EditValueChanged, cbeEmpTypeFilter.EditValueChanged
        LoadReportDataAsync(True).GetAwaiter()
    End Sub

    Private Sub RadioGroup1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgFilterBy.SelectedIndexChanged
        UpdateReportFiltersVisibility()
    End Sub

    Private Sub cbeDateRange_QueryCloseUp(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles cbeDateRange.QueryCloseUp
        LoadDateFilters(cbeDateRange.Text)
        LoadReportDataAsync(True).GetAwaiter()
    End Sub

    Private Sub LoadDateFilters(val As String)
        If val = "Last Check Date" Then
            Dim lastPr = DB.PAYROLLs.Where(Function(c) c.CONUM = SelectedCoNum).Where(Function(a) a.CHECK_DATE.HasValue).OrderByDescending(Function(a) a.PRNUM).FirstOrDefault
            If lastPr IsNot Nothing Then
                DateEdit1.DateTime = lastPr.CHECK_DATE
                DateEdit2.DateTime = lastPr.CHECK_DATE
            End If
        Else
            DateFunctions.GetDateRange(cbeDateRange.Text, DateEdit1.DateTime, DateEdit2.DateTime)
        End If
    End Sub

    Private Sub luePayrollFilter_QueryCloseUp(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles luePayrollFilter.QueryCloseUp
        LoadReportDataAsync(True)
    End Sub

    Private Sub AnyFilterEditor_EditValueChanged(sender As Object, e As EventArgs) Handles rgFilterBy.EditValueChanged
        LoadReportDataAsync(True)
    End Sub

    Private Sub DateFilter_QueryCloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.ClosedEventArgs) Handles DateEdit1.Closed, DateEdit2.Closed
        If e.CloseMode <> PopupCloseMode.Cancel Then
            cbeDateRange.EditValue = "Custom"
            LoadReportDataAsync(True)
        End If
    End Sub

    Private Sub ClearGridUserLayout()
        Try
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                Dim _reportLayout = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Single(Function(l) l.ID = ReportLayoutId)
                _reportLayout.Layout = Nothing
                db.SaveChanges()
                Close()
            End Using
        Catch ex As Exception
            ReportLogger.Error(ex, "Error clearing report grid layout")
            DisplayErrorMessage("Error clearing report grid layout", ex)
        End Try
    End Sub

    Public Function SaveUserLayout(frm As Form, Optional layoutId As Integer? = Nothing, Optional updateSettings As Boolean = True) As CustomReportLayout
        Try
            Dim _reportLayout As CustomReportLayout
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                If layoutId.HasValue Then
                    _reportLayout = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Single(Function(l) l.ID = layoutId)
                    If _reportLayout.IsAdminLayout Then
                        ReportLogger.Warning("User somehow entered SaveUserLayout, However this is a admin report layout. Requires invenstigation.")
                        Throw New Exception("Cannot save changes to this report.")
                    End If
                    _reportLayout.ChgDate = Now
                    ReportLogger.Debug("Updating Report Layout {LayoutId}", layoutId)
                Else
                    ReportLogger.Debug("Saving new report layout")
                    _reportLayout = New CustomReportLayout With {.CustomReportId = Report.ID,
                        .ParentReportLayoutId = ReportLayout.ID,
                        .Category = ReportLayout.Category,
                        .LayoutDescription = ReportLayout.LayoutDescription,
                        .UserName = UserName,
                        .AuthorName = UserName,
                        .AddDate = Now,
                        .ChgDate = Now}
                    db.CustomReportLayouts.InsertOnSubmit(_reportLayout)
                End If
                Dim control = New ucInputBox()
                control.LabelControl1.Text = "Please enter a report name"
                control.teReportName.Text = ReportLayout.LayoutName
                Dim result = DevExpress.XtraBars.Docking2010.Customization.FlyoutDialog.Show(frm, control)
                If result <> DialogResult.OK OrElse Name.IsNullOrWhiteSpace Then Return Nothing
                _reportLayout.LayoutName = control.teReportName.Text
                _reportLayout.LayoutDescription = control.meDescription.Text
                If updateSettings Then
                    _reportLayout.Layout = gvReportData.GetGridLayout
                    _reportLayout.SelectedColumns = GetLayoutSettingsJson()
                End If
                db.SubmitChanges()
                ReportLayout = _reportLayout
                Dim al = New DevExpress.XtraBars.Alerter.AlertControl With {.AllowHtmlText = True, .AllowHotTrack = False, .FormLocation = DevExpress.XtraBars.Alerter.AlertFormLocation.BottomRight, .FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.MoveVertical, .AutoFormDelay = TimeSpan.FromSeconds(7).TotalMilliseconds}
                al.Show(Me, "Report Saved successfully", $"New Report <b>{control.teReportName.Text}</b> Saved successfully.")
            End Using
            Return _reportLayout
        Catch ex As Exception
            ReportLogger.Error(ex, "Error saving report layout")
            DisplayErrorMessage("Error saving report layout", ex)
        End Try
        Return Nothing
    End Function

    Private Async Sub HighlighColumnAsync(row As CustomReportColumnTemplate)
        Try
            If row Is Nothing OrElse SelectedColumns Is Nothing Then Exit Sub
            Dim col = SelectedColumns.FirstOrDefault(Function(c) c.CustomReportColumnId = row.ID)
            If col IsNot Nothing Then
                Dim gridCol = gvReportData.Columns.ColumnByFieldName(col.ColumnAlias)
                If gridCol Is Nothing Then Exit Sub
                gvReportData.FocusedColumn = gridCol
                gvReportData.MakeColumnVisible(gridCol)
                Dim color = gridCol.AppearanceCell.BackColor
                gridCol.AppearanceCell.BackColor = Color.LightGreen
                Await Task.Delay(750)
                gridCol.AppearanceCell.BackColor = Color.FromArgb(0, 0, 0, 0)
            End If
        Catch ex As Exception
            ReportLogger.Error(ex, "Error in HighlightColumnAsync")
        End Try
    End Sub

    Private Sub gvReportData_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvReportData.PopupMenuShowing
        Try
            If e.MenuType = GridMenuType.Summary Then
                Dim footerMenu As DevExpress.XtraGrid.Menu.GridViewFooterMenu = TryCast(e.Menu, DevExpress.XtraGrid.Menu.GridViewFooterMenu)
                Dim check As Boolean = e.HitInfo.Column.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Custom AndAlso Equals("Count", e.HitInfo.Column.SummaryItem.Tag)
                Dim menuItem As DevExpress.Utils.Menu.DXMenuItem = New DevExpress.Utils.Menu.DXMenuCheckItem("Count Distinct", check, Nothing, New EventHandler(AddressOf MyMenuItem))
                menuItem.Tag = e.HitInfo.Column
                For Each item As DevExpress.Utils.Menu.DXMenuItem In footerMenu.Items
                    item.Enabled = True
                Next item
                footerMenu.Items.Add(menuItem)
            ElseIf e.MenuType = GridMenuType.Column Then
                Dim menu As GridViewColumnMenu = TryCast(e.Menu, GridViewColumnMenu)
                menu.Items.Add(New DXMenuItem("Add Unbound Column", Sub() AddUnboundColumn(), My.Resources.add_16x16) With {.BeginGroup = True})
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub AddUnboundColumn()
        Dim column = gvReportData.Columns.AddVisible($"Col{gvReportData.Columns.Count + 1}")
        column.UnboundType = DevExpress.Data.UnboundColumnType.Object
        column.ShowUnboundExpressionMenu = True
        gvReportData.ShowUnboundExpressionEditor(column)
    End Sub

    Private Sub MyMenuItem(ByVal sender As Object, ByVal e As EventArgs)
        Dim Item As DevExpress.Utils.Menu.DXMenuItem = TryCast(sender, DevExpress.Utils.Menu.DXMenuItem)
        Dim col As GridColumn = TryCast(Item.Tag, GridColumn)
        col.SummaryItem.Tag = "Count"
        col.SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, String.Empty)
    End Sub

    Dim distinctList As List(Of Object) = Nothing

    Private Sub gvOrderGroup_CustomSummaryCalculate(sender As Object, e As DevExpress.Data.CustomSummaryEventArgs) Handles gvReportData.CustomSummaryCalculate
        Dim View As GridView = CType(sender, GridView)
        If e.SummaryProcess = DevExpress.Data.CustomSummaryProcess.Start Then
            distinctList = New List(Of Object)
        ElseIf e.SummaryProcess = DevExpress.Data.CustomSummaryProcess.Calculate Then
            If e.FieldValue IsNot Nothing Then distinctList.Add(e.FieldValue)
        ElseIf e.SummaryProcess = DevExpress.Data.CustomSummaryProcess.Finalize Then
            e.TotalValue = distinctList.Distinct().Count()
        End If
    End Sub

    Private Sub FlyoutPanel1_ButtonClick(sender As Object, e As FlyoutPanelButtonClickEventArgs) Handles FlyoutPanel1.ButtonClick
        FlyoutPanel1.HideBeakForm()
    End Sub

    Private Sub gvReportData_MouseUp(sender As Object, e As MouseEventArgs) Handles gvReportData.MouseUp
        gvReportData.OptionsCustomization.AllowSort = True
        If e.Button = MouseButtons.Left Then
            Dim hitInfo = gvReportData.CalcHitInfo(e.Location)

            If hitInfo.InColumn Then
                If fpColumnHeaderOptions.IsPopupOpen Then
                    fpColumnHeaderOptions.HideBeakForm()
                    Application.DoEvents()
                End If

                fpColumnHeaderOptions.OptionsBeakPanel.CloseOnOuterClick = False
                If hitInfo.HitTest = GridHitTest.Column Then
                    ShowColumnOptions(hitInfo.Column)
                End If
            End If
        End If
    End Sub

    Private Sub gvReportData_MouseDown(sender As Object, e As MouseEventArgs) Handles gvReportData.MouseDown
        If e.Button = MouseButtons.Left Then
            Dim hitInfo = gvReportData.CalcHitInfo(e.Location)
            If hitInfo.HitTest = GridHitTest.Column Then
                gvReportData.OptionsCustomization.AllowSort = False
            End If
        End If
    End Sub

    Private Sub ShowColumnOptions(column As GridColumn)
        Try
            Dim col = SelectedColumns.SingleOrDefault(Function(c) c.ColumnAlias = column.FieldName)
            If col IsNot Nothing Then
                fpColumnHeaderOptions.Height = 138
                beCustomColumnCaption.Text = col.Caption
                CheckEdit1.Checked = col.HideColumnInOutput
                If col.Type = Brands_FrontDesk.Column.ColumnType.Regular Then teColumnDescription.Text = col.ColumnTemplate.ColumnDescription
                meSqlExpression.Text = col.Expression
                lciColumnDescription.Visibility = (col.Type = Brands_FrontDesk.Column.ColumnType.Regular).ToBarItemVisibility
                lciSqlExpression.Visibility = (col.Type = Brands_FrontDesk.Column.ColumnType.Expression).ToBarItemVisibility
                LayoutControlItem3.Visibility = True.ToBarItemVisibility

                If col.Type = Brands_FrontDesk.Column.ColumnType.Regular Then
                    lcgPayCodes.Visibility = col.SelectCodes.IsNotNullOrWhiteSpace.ToBarItemVisibility
                    If lcgPayCodes.Visible Then
                        fpColumnHeaderOptions.Height += 325
                        LoadColumnPayCodes(col)
                    End If
                Else
                    lcgPayCodes.Visibility = False.ToBarItemVisibility
                End If
                fpColumnHeaderOptions.Tag = col
            Else
                fpColumnHeaderOptions.Height = 115
                beCustomColumnCaption.Text = column.Caption
                lciColumnDescription.Visibility = False.ToBarItemVisibility
                lciSqlExpression.Visibility = True.ToBarItemVisibility
                lcgPayCodes.Visibility = False.ToBarItemVisibility
                LayoutControlItem3.Visibility = False.ToBarItemVisibility
                meSqlExpression.Text = column.UnboundExpression
                fpColumnHeaderOptions.Tag = column
            End If

            Dim vi As GridViewInfo = gvReportData.GetViewInfo()
            Dim colInfo = vi.ColumnsInfo(column)
            If colInfo IsNot Nothing Then
                fpColumnHeaderOptions.OptionsBeakPanel.CloseOnOuterClick = True
                Dim bounds As Rectangle = colInfo.Bounds
                Dim p As Point = gvReportData.GridControl.PointToScreen(bounds.Location)
                p.Y += 11
                p.X += colInfo.Bounds.Width / 2
                fpColumnHeaderOptions.ShowBeakForm(p) ', False, gcReportData, New Point(0, 10))
                beCustomColumnCaption.Focus()
            End If
        Catch ex As Exception
            ReportLogger.Error(ex, "Error showing column option popup")
            DisplayErrorMessage("Error showing column option popup", ex)
        End Try
    End Sub

    Private Sub LoadColumnPayCodes(SelectedCol As Column)
        If Me.PayCodes Is Nothing Then
            Dim CoCode As Decimal = Me.pceCoNum.EditValue
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                PayCodes = (From A In db.OTHER_PAYS Where A.CONUM = CoCode AndAlso A.OACTIVE = "YES" Select A.OTH_PAY_NUM, A.OTH_PAY_DESC).ToDictionary(Function(p) p.OTH_PAY_NUM, Function(p) p.OTH_PAY_DESC)
                DedCodes = (From A In db.DEDUCTIONs Where A.CONUM = CoCode AndAlso A.DACTIVE = "YES" Select A.DED_NUM, A.DED_DESC).ToDictionary(Function(p) p.DED_NUM, Function(p) p.DED_DESC)
                MemoCodes = (From A In db.MEMOS Where A.CONUM = CoCode AndAlso A.MACTIVE = "Yes" Select A.MEMO_NUM, A.MEMO_DESC).ToDictionary(Function(p) p.MEMO_NUM, Function(p) p.MEMO_DESC)
            End Using

            UcColumnCustomize1.AvailablePayCodes = PayCodes
            UcColumnCustomize1.AvailableDeductionCodes = DedCodes
            UcColumnCustomize1.AvailableMemoCodes = MemoCodes
        End If

        UcColumnCustomize1.SetColumn(SelectedCol)
        If SelectedCol.SelectCodes = "All" Then


            AddHandler UcColumnCustomize1.VisibleChanged, Sub()
                                                              UcColumnCustomize1.SetSelectedCodes(SelectedCol.PaysSelectedCodes, PayCodes, UcColumnCustomize1.gcPaysCode)
                                                              UcColumnCustomize1.SetSelectedCodes(SelectedCol.DedsSelectedCodes, DedCodes, UcColumnCustomize1.gcDedsCode)
                                                              UcColumnCustomize1.SetSelectedCodes(SelectedCol.MemosSelectedCodes, MemoCodes, UcColumnCustomize1.gcMemosCode)
                                                          End Sub
        Else

        End If
    End Sub

    Private Sub btnApplyColumnSettings_Click(sender As Object, e As EventArgs) Handles btnApplyColumnSettings.Click
        If TypeOf fpColumnHeaderOptions.Tag Is Column Then
            Dim col As Column = fpColumnHeaderOptions.Tag
            col.Caption = beCustomColumnCaption.Text
            col.HideColumnInOutput = CheckEdit1.Checked
            col.Expression = meSqlExpression.Text
            If lcgPayCodes.Visible Then UcColumnCustomize1.ApplyChanges()
            ApplyColumnSettingsToGrid(col)
            SetGroupFilter()
        ElseIf TypeOf fpColumnHeaderOptions.Tag Is GridColumn Then
            Dim col As GridColumn = fpColumnHeaderOptions.Tag
            col.Caption = beCustomColumnCaption.Text
        End If
        fpColumnHeaderOptions.HideBeakForm()
    End Sub

    Private Sub meSqlExpression_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles meSqlExpression.ButtonClick
        fpColumnHeaderOptions.HideBeakForm()
        If TypeOf fpColumnHeaderOptions.Tag Is Column Then
            fpColumnHeaderOptions.Tag = Tuple.Create(Of Boolean, Column)(True, fpColumnHeaderOptions.Tag)
        ElseIf TypeOf fpColumnHeaderOptions.Tag Is GridColumn Then
            fpColumnHeaderOptions.Tag = Tuple.Create(Of Boolean, GridColumn)(True, fpColumnHeaderOptions.Tag)
        End If
    End Sub

    Private Sub fpColumnHeaderOptions_Hidden(sender As Object, e As FlyoutPanelEventArgs) Handles fpColumnHeaderOptions.Hidden
        If fpColumnHeaderOptions.Tag IsNot Nothing Then
            If TypeOf fpColumnHeaderOptions.Tag Is Tuple(Of Boolean, Column) Then
                Dim b As Tuple(Of Boolean, Column) = fpColumnHeaderOptions.Tag
                If b.Item1 Then
                    Dim result = expressionHelper.ShowExpressionEditor(b.Item2.Expression)
                    If result.IsNotNullOrWhiteSpace Then
                        b.Item2.Expression = result
                    End If
                End If
            ElseIf TypeOf fpColumnHeaderOptions.Tag Is Tuple(Of Boolean, GridColumn) Then
                Dim t As Tuple(Of Boolean, GridColumn) = fpColumnHeaderOptions.Tag
                gvReportData.ShowUnboundExpressionEditor(t.Item2)
            End If
        End If
    End Sub

    Private Sub btnCancelColumnSettings_Click(sender As Object, e As EventArgs) Handles btnCancelColumnSettings.Click
        fpColumnHeaderOptions.HideBeakForm()
    End Sub

    Private Sub btnRemoveColumn_Click(sender As Object, e As EventArgs) Handles DropDownButton1.Click
        If TypeOf fpColumnHeaderOptions.Tag Is Column Then
            Dim col As Column = fpColumnHeaderOptions.Tag
            RemoveColumn(True, col)
        ElseIf TypeOf fpColumnHeaderOptions.Tag Is GridColumn Then
            gvReportData.Columns.Remove(fpColumnHeaderOptions.Tag)
        End If
        fpColumnHeaderOptions.HideBeakForm()
    End Sub

    Private Sub bbiRemoveAllColumnsInGroup_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiRemoveAllColumnsInGroup.ItemClick
        If TypeOf fpColumnHeaderOptions.Tag Is Column Then
            Dim col As Column = fpColumnHeaderOptions.Tag
            If col.Type = Column.ColumnType.Expression Then
                RemoveColumn(True, col)
            Else
                RemoveAllColumns(col.ColumnTemplate.GroupName)
            End If
        ElseIf TypeOf fpColumnHeaderOptions.Tag Is GridColumn Then
            gvReportData.Columns.Remove(fpColumnHeaderOptions.Tag)
        End If
        fpColumnHeaderOptions.HideBeakForm()
    End Sub

    Private Sub EmailReport()
        Dim handle As IOverlaySplashScreenHandle = Nothing
        Try
            handle = SplashScreenManager.ShowOverlayForm(Me)
            Using db As dbEPDataDataContext = New dbEPDataDataContext(ConnectionString)
                Dim emailTemplate = db.ReportEmailTeplates.Single(Function(t) t.Name = "Custom Reports")
                Dim comp = db.COMPANies.Single(Function(c) c.CONUM = SelectedCoNum)
                Dim processor = New ReportProcessor(comp, emailTemplate, FileType.Pdf) With {.showParametersForm = False}
                processor.DefaultParamValues = New List(Of KeyValuePair)
                processor.DefaultParamValues.Add(New KeyValuePair("@LayoutId", ReportLayout.ID))
                Dim result = processor.ProcessReport
                Dim p = Path.Combine(modReports.GetCrystalReportsFolder(), modReports.GetFileName(comp, ReportLayout.LayoutName, ".xlsx"))
                gvReportData.OptionsPrint.AutoWidth = False
                gvReportData.Export(DevExpress.XtraPrinting.ExportTarget.Xlsx, p)
                'gvReportData.ExportToXlsx(p) ', New DevExpress.XtraPrinting.XlsxExportOptions(DevExpress.XtraPrinting.TextExportMode.Value, True, True, False))
                result.Paths.Add(p)
                Dim emailSender = New ReportSender(result) With {.showWebPost = False}
                If IsRunningFromPPImports Then
                    Dim toEmail = ""
                    Using frm = New frmEmailReport()
                        If frm.ShowDialog = DialogResult.OK Then
                            toEmail = String.Join(";", frm.GetEmailAddress())
                        Else
                            Exit Sub
                        End If
                    End Using
                    Dim email = EmailHelpers.ComposeEmail(toEmail, emailSender)
                    email.FromEmail = "<EMAIL>"
                    email.FromDisplayEmail = "CS"
                    email.CreateNewTicket = False
                    email.SendEmail()
                Else
                    emailSender.EmailReport()
                End If
            End Using
            If handle IsNot Nothing Then SplashScreenManager.CloseOverlayForm(handle)
        Catch ex As Exception
            If handle IsNot Nothing Then SplashScreenManager.CloseOverlayForm(handle)
            ReportLogger.Error(ex, "Error In EmailReport")
            DisplayErrorMessage("Error Emailing Report", ex)
        Finally
            If handle IsNot Nothing Then SplashScreenManager.CloseOverlayForm(handle)
        End Try
    End Sub

    Private Sub beCustomColumnCaption_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles beCustomColumnCaption.ButtonClick
        Dim be As ButtonEdit = sender
        be.Text = String.Empty
    End Sub

    Private Sub beCustomColumnCaption_KeyDown(sender As Object, e As KeyEventArgs) Handles beCustomColumnCaption.KeyDown
        If e.KeyCode = Keys.Enter Then
            btnApplyColumnSettings.PerformClick()
        ElseIf e.KeyCode = Keys.Escape Then
            btnCancelColumnSettings.PerformClick()
        End If
    End Sub

    Private Sub grid_DragOver(ByVal sender As Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles gcReportData.DragOver
        If e.Data.GetDataPresent(GetType(CustomReportColumnTemplate)) Then
            Dim row As CustomReportColumnTemplate = e.Data.GetData(GetType(CustomReportColumnTemplate))
            e.Effect = DragDropEffects.Move
            Cursor.Current = GetCursor(row.ColumnDescription)
        ElseIf e.Data.GetDataPresent(GetType(String)) Then
            Dim groupName As String = e.Data.GetData(GetType(String))
            e.Effect = DragDropEffects.Move
            Cursor.Current = GetCursor($"Group - {groupName.RemoveFromStart("- ")}", "Add Group Of Columns")
        Else
            e.Effect = DragDropEffects.None
            Cursor.Current = GetCursor("Not Allowed.")
        End If
    End Sub

    Dim _cursorsList As Dictionary(Of String, Cursor) = New Dictionary(Of String, Cursor)
    Private Function GetCursor(msg As String, Optional groupText As String = "Add Column") As Cursor
        Dim _cursor As Cursor
        Try
            If Not _cursorsList.TryGetValue(msg, _cursor) Then
                Using uc = New ucColumnMouseMove()
                    uc.PictureEdit1.Text = msg
                    uc.GroupControl1.Text = groupText
                    Using bmp = New Bitmap(uc.Width * 2, uc.Height * 2)
                        uc.DrawToBitmap(bmp, New Rectangle(uc.Width, uc.Height, uc.Width, uc.Height))
                        _cursor = New Cursor(bmp.GetHicon)
                    End Using
                    _cursorsList.Add(msg, _cursor)
                End Using
            End If
        Catch ex As Exception
            Logger.Error(ex, "error in GetCursor")
        End Try
        Return _cursor
    End Function

    Private Sub grid_DragDrop(ByVal sender As Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles gcReportData.DragDrop
        Dim grid As GridControl = CType(sender, GridControl)
        Dim row As CustomReportColumnTemplate = CType(e.Data.GetData(GetType(CustomReportColumnTemplate)), CustomReportColumnTemplate)
        Dim groupName As String = CType(e.Data.GetData(GetType(String)), String)
        If row IsNot Nothing OrElse groupName.IsNotNullOrWhiteSpace Then
            Dim visibleIndex As Integer? = Nothing
            Dim gc As GridControl = sender
            Dim gv As GridView = gc.MainView
            Dim point = gc.PointToClient(New Point(e.X, e.Y))
            'Dim hitInfo = gv.CalcHitInfo(e.X, e.Y)
            Dim hitInfo = gv.CalcHitInfo(point)
            If hitInfo.Column IsNot Nothing Then
                Dim viewInfo As GridViewInfo = gv.GetViewInfo
                Dim colInfo = viewInfo.ColumnsInfo(hitInfo.Column)
                Dim halfWidth As Integer = (colInfo.Bounds.Width / 2) + colInfo.Bounds.Left
                Dim isLeftSide As Boolean
                If point.X > halfWidth Then
                    isLeftSide = False
                Else
                    isLeftSide = True
                End If
                visibleIndex = If(isLeftSide, hitInfo.Column.VisibleIndex, hitInfo.Column.VisibleIndex + 1)
            End If
            If row IsNot Nothing Then AddColumn(row, visibleIndex) Else AddAllColumns(groupName, visibleIndex)
        End If
    End Sub

    Private Sub gvReportData_CustomDrawFooter(sender As Object, e As Views.Base.RowObjectCustomDrawEventArgs) Handles gvReportData.CustomDrawFooter

        For Each col As GridColumn In gvReportData.Columns
            If col.Summary.ActiveCount > 0 Then Exit Sub
        Next

        Dim backgroundBrush As Brush = e.Cache.GetGradientBrush(e.Bounds, Color.Lavender, Nothing, System.Drawing.Drawing2D.LinearGradientMode.Horizontal)
        e.Graphics.FillRectangle(backgroundBrush, e.Bounds)

        Dim brush As Brush = e.Cache.GetSolidBrush(Color.LightCoral)
        Dim b = e.Bounds
        b.X = (b.Size.Width / 2) - 150
        b.Y += 5
        e.Painter.DrawCaption(e.Info, "Right Click To Add A Column Summary", New System.Drawing.Font("Times New Roman", 14), brush, b, New StringFormat())
        e.Handled = True
    End Sub

    Private Sub btnNewBlankReport_Click(sender As Object, e As EventArgs) Handles btnNewBlankReport.Click
        RemoveAllColumns()
    End Sub

    Private Sub btnReportFilter_Click(sender As Object, e As EventArgs) Handles btnReportFilter.Click
        FlyoutPanel1.ShowBeakForm()
    End Sub

    Private Sub btnAddCustomColumn_Click(sender As Object, e As EventArgs) Handles btnAddCustomColumn.Click
        Try
            Dim result = expressionHelper.ShowExpressionEditor("")
            If result.IsNotNullOrWhiteSpace Then
                Dim colAlias = GetColumnAlias()
                Dim r = XtraInputBox.Show("New Column Name", "Enter the column display name", "Expr")
                If r.IsNotNullOrWhiteSpace Then
                    AddColumn(New Column(result, colAlias, r))
                    ucOrderGroup.VerifyAggregateColumns()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in btnAddCustomColumn_Click", ex)
        End Try
    End Sub

    Private Function GetColumnType(col As CustomReportColumnTemplate, Optional _caption As String = Nothing, Optional _fieldName As String = Nothing) As DevExpress.XtraEditors.Filtering.UnboundFilterColumn
        Dim caption = If(_caption, col.Caption)
        Dim fieldName = If(_fieldName, $"`{caption}`")
        Select Case col.DataType?.ToLower
            Case "string", "varchar"
                Return New DevExpress.XtraEditors.Filtering.UnboundFilterColumn(caption, fieldName, GetType(String), Nothing, DevExpress.Data.Filtering.Helpers.FilterColumnClauseClass.String)
            Case "numeric"
                Return New DevExpress.XtraEditors.Filtering.UnboundFilterColumn(caption, fieldName, GetType(Integer), Nothing, DevExpress.Data.Filtering.Helpers.FilterColumnClauseClass.Generic)
            Case "decimal"
                Return New DevExpress.XtraEditors.Filtering.UnboundFilterColumn(caption, fieldName, GetType(Decimal), Nothing, DevExpress.Data.Filtering.Helpers.FilterColumnClauseClass.Generic)
            Case "date"
                Return New DevExpress.XtraEditors.Filtering.UnboundFilterColumn(caption, fieldName, GetType(DateTime), New RepositoryItemDateEdit(), DevExpress.Data.Filtering.Helpers.FilterColumnClauseClass.DateTime)
            Case Else
                ReportLogger.Error("{DataType} is not mapped to a .net data type.", col.DataType)
        End Select
        Return New DevExpress.XtraEditors.Filtering.UnboundFilterColumn(caption, fieldName, GetType(String), Nothing, DevExpress.Data.Filtering.Helpers.FilterColumnClauseClass.Generic)
    End Function

    Private Sub availableColumn_AddColumn(sender As Object, e As ColumnEventArgs) Handles availableColumn.AddColumn
        AddColumn(e._ColumnTemplate)
    End Sub

    Private Sub availableColumn_AddColumnsGroup(sender As Object, e As ColumnGroupEventArgs) Handles availableColumn.AddColumnsGroup
        AddAllColumns(e.GroupName)
    End Sub

    Private Sub availableColumn_RemoveColumnGroup(sender As Object, e As ColumnGroupEventArgs) Handles availableColumn.RemoveColumnGroup
        RemoveAllColumns(e.GroupName)
    End Sub

    Private Sub availableColumn_ReloadAvailableColumns(sender As Object, e As EventArgs) Handles availableColumn.ReloadAvailableColumns
        LoadAvailableColumns()
    End Sub

    Private Sub availableColumn_HighlighColumn(sender As Object, e As ColumnEventArgs) Handles availableColumn.HighlighColumn
        HighlighColumnAsync(e._ColumnTemplate)
    End Sub

    Public Async Function GetScheduledReport(Conum As Decimal, FileName As String) As Task(Of Boolean)
        Me.SelectedCoNum = Conum
        Me.Show()
        'Me.pceCoNum.EditValue = Conum
        'ucSearchCompany.SetCompany(pceCoNum, Conum)
        Await LoadReportDataAsync(False)
        'Me.ShowDialog()
        Using stream = New FileStream(FileName, FileMode.Create)
            Me.gvReportData.ExportToXls(stream)
        End Using
        Return True
    End Function

End Class

Partial Class CustomReportColumnTemplate

    Public Function GetTableAndFieldName(Optional useQuotes As Boolean = False) As String
        If Not useQuotes Then
            Return $"{Table.ActualName}{If(Table.ActualName.IsNotNullOrWhiteSpace, ".", "")}{FieldName}"
        Else
            Return $"{Table.ActualName()}{If(Table.ActualName.IsNotNullOrWhiteSpace, """.""", "")}{FieldName}"
        End If
    End Function

    Public ReadOnly Property Table As Table
        Get
            Return New Table(CustomReportTable.Name, CustomReportTable.Alias)
        End Get
    End Property

    Public ReadOnly Property Caption As String
        Get
            If DefaultColumnCaption.IsNullOrWhiteSpace Then
                Return ColumnDescription
            Else
                Return DefaultColumnCaption
            End If
        End Get
    End Property

    Public Overrides Function ToString() As String
        Return $"{ColumnSortOrder} - {FieldName} - {ColumnDescription}"
    End Function
End Class

Partial Class CustomReport
    Public ReadOnly Property FilterByDateOrPrNum As Boolean
        Get
            Return If(PrNumParam, False) AndAlso If(DateRangeParam, False)
        End Get
    End Property
End Class