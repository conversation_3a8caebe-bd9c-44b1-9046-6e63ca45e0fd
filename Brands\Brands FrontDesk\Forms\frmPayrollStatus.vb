﻿Imports Brands_FrontDesk.QueuePayrollProcessor.modPayrollUtilities
Imports DevExpress.XtraEditors
Imports Microsoft.EntityFrameworkCore

Public Class frmPayrollStatus

    Dim CoNum As Decimal
    Dim PrNum As Decimal
    Dim DB As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
    Dim AdminDB As dbEPDataDataContext
    Dim IsManager As Boolean
    Dim CurPayrl As PAYROLL
    Dim Errors As List(Of Errors) = New List(Of Errors)
    Dim allowSave As Boolean = True

    Public Sub New(_CoNum As Integer?)
        InitializeComponent()
        If Not IsNothing(_CoNum) Then
            CoNum = _CoNum
            LoadCompany()
        End If
        btnSearch.Select()
        Me.btnDeletePayroll.Enabled = Permissions.AllowPayrollDeletion
    End Sub

    Private Sub frmPayrollStatus_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        IsManager = Permissions.Manager.GetValueOrDefault
        gvPayrollStatus.ActiveFilterString = "[Notes] <> ''"
    End Sub

    Private Sub LoadCompany()
        DB = New dbEPDataDataContext(GetConnectionString)
        AdminDB = New dbEPDataDataContext(GetAdminConnectionString)
        Try
            Errors.Clear()
            allowSave = True
            Dim comp = DB.COMPANies.SingleOrDefault(Function(c) c.CONUM.Equals(CoNum))
            If comp Is Nothing Then Exit Sub
            Me.teCompany.Text = comp.CONUM
            Me.teCoName.Text = comp.CO_NAME
            lueTakePayrollTo.Properties.DataSource = Nothing

            Dim chk = DB.vMaxCkDates.Single(Function(c) c.CONUM.Equals(CoNum))
            PrNum = chk.vMaxPrnum
            CurPayrl = DB.PAYROLLs.Single(Function(p) p.PRNUM.Equals(chk.vMaxPrnum) AndAlso p.CONUM.Equals(CoNum))
            bsPayroll.DataSource = CurPayrl

            cbePrStatus.Properties.Items.Clear()
            cbePrStatus.Properties.Items.Add(CurPayrl.PAYROLL_STATUS)
            If CurPayrl.PAYROLL_STATUS <> "Entering Checks" Then cbePrStatus.Properties.Items.Add("Entering Checks")
            If CurPayrl.PAYROLL_STATUS <> "Finished Checks" AndAlso CurPayrl.PAYROLL_STATUS <> "Entering Checks" Then cbePrStatus.Properties.Items.Add("Finished Checks")

            lueTakePayrollTo.Properties.DataSource = (From A In DB.FrontDeskPermissions.ToList() Where A.ShowInTakePayrollList.GetValueOrDefault Order By A.UserName).ToList.Select(Function(u) u.UserName)

            teSubmited.DateTime = DB.ExecuteQuery(Of DateTime)(String.Format("SELECT ISNULL((SELECT MAX(ps.PayrollSbmittedDate) FROM custom.PayrollSubmmited ps WHERE ps.CONUM = {0} AND ps.PRNUM = {1}),'')", CoNum, PrNum)).First

            Dim payrollstat As List(Of prc_ReopenPayrollResult) = AdminDB.prc_ReopenPayroll(CoNum, CurPayrl.PRNUM).ToList
            gcPayrollStatus.DataSource = payrollstat
            gvPayrollStatus.BestFitColumns()

            Errors.Add(New Errors() With {.ErrorMsg = String.Join(vbCrLf, payrollstat.Where(Function(p) Not p.Notes.Equals("")).Select(Function(p) p.Notes))})
            'errormsg.AppendLine(String.Join(vbCrLf, payrollstat.Where(Function(p) Not p.Notes.Equals("")).Select(Function(p) p.Notes)))
            allowSave = payrollstat.All(Function(p) p.AllowReopen.Equals("ALL")) OrElse Permissions.Manager.GetValueOrDefault OrElse payrollstat.Count = 0

            Dim user = DB.FrontDeskOptions.Single.BankingHoldBy
            If Not user.IsNullOrWhiteSpace Then
                Errors.Add(New Errors With {.ErrorMsg = "Banking Files Processing now By {0}, Consult Banking Department for Assistance".FormatWith(user)})
                ' errormsg.AppendLine("Banking Files Processing now By {0}, Consult Banking Department for Assistance".FormatWith(user))
                allowSave = False
            End If

            If CurPayrl.PAYROLL_STATUS = "in-Bill-Stack" Or CurPayrl.BILL_ACH_STAT.Contains("Stack") Or CurPayrl.TOTALTAX_STATUS.Contains("Stack") Then
                Errors.Add(New Errors With {.ErrorMsg = "Payroll Status in Banking Stack, Consult Banking Department for Assistance"})
                allowSave = False
            End If

            Dim RiskManagementStatus As String = Query(Of String)($"SELECT rma.Status FROM custom.RiskManagementAlerts rma WHERE rma.CoNum = {CoNum} AND rma.PrNum = {PrNum}").FirstOrDefault()
            If RiskManagementStatus = "Delayed DD" OrElse RiskManagementStatus = "Wire Requested" OrElse RiskManagementStatus = "Wire Received" Then
                Errors.Add(New Errors With {.ErrorMsg = $"Payroll has risk management status '{RiskManagementStatus}', Consult Risk Management Department for Assistance"})
                allowSave = False
            End If

            lcErrors.Visible = Errors.Count > 0



            For Each col As DevExpress.XtraGrid.Columns.GridColumn In gvPayrollStatus.Columns
                Select Case col.FieldName
                    Case "CONUM", "PRNUM", "PAYROLL_STATUS", "BILL_ACH_STAT", "TOTALTAX_STATUS", "Ach Amt", "Action", "IncludeAttachement", "Row"
                        col.Visible = False
                    Case "AchTyp"
                        col.Caption = "Action Type"
                    Case "File Sent"
                        col.VisibleIndex = gvPayrollStatus.Columns("AchStatus").VisibleIndex + 1
                    Case "ActionDate"
                        col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                        col.DisplayFormat.FormatString = "g"
                    Case "AchStatus"
                        col.Caption = "Status"
                End Select
            Next

            If allowSave Then
                btnSave.Enabled = Errors.Count = 0
                cbePrStatus.Enabled = True
                lueTakePayrollTo.Enabled = True
                cbeInvoiceAchStatus.Enabled = allowSave AndAlso IsManager
                cbeTotalTaxStatus.Enabled = allowSave AndAlso IsManager
            Else
                btnSave.Enabled = False
                cbePrStatus.Enabled = False
                lueTakePayrollTo.Enabled = False
                cbeInvoiceAchStatus.Enabled = False
                cbeTotalTaxStatus.Enabled = False
            End If
            btnReprintPayroll.Visible = CurPayrl.PAYROLL_STATUS <> "Finished Checks" AndAlso cbePrStatus.Properties.Items.Contains("Finished Checks")
            btnReopenThePayroll.Visible = CurPayrl.PAYROLL_STATUS <> "Entering Checks" AndAlso cbePrStatus.Properties.Items.Contains("Entering Checks")


            gvPayrollStatus.BestFitColumns()
            gcErrors.DataSource = Errors
            gvErrors.BestFitColumns()

            'lueTakePayrollTo.SelectedText = CurPayrl.OPNAME
            lueTakePayrollTo.EditValue = CurPayrl.OPNAME
            lueTakePayrollTo.ClosePopup()
        Catch ex As Exception
            DisplayErrorMessage("Error loading company", ex)
            Close()
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If lueTakePayrollTo.Text.IsNullOrWhiteSpace AndAlso CurPayrl.PAYROLL_STATUS = "Entering Checks" Then
            XtraMessageBox.Show("Please fill Take Payroll To from the available users.")
            Exit Sub
        End If
        'check if only changed take payroll to. 
        Dim origCurPayrlDB = New dbEPDataDataContext(GetConnectionString).PAYROLLs.Single(Function(p) p.PRNUM.Equals(PrNum) AndAlso p.CONUM.Equals(CoNum))
        If origCurPayrlDB.PAYROLL_STATUS <> CurPayrl.PAYROLL_STATUS OrElse origCurPayrlDB.TOTALTAX_STATUS <> CurPayrl.TOTALTAX_STATUS OrElse origCurPayrlDB.BILL_ACH_STAT <> CurPayrl.BILL_ACH_STAT Then
            Dim frm = New frmPayrollStatusNotes(CoNum, PrNum)
            If frm.ShowDialog <> System.Windows.Forms.DialogResult.OK Then Exit Sub
            DB.PayrollReopenedNotes.InsertOnSubmit(frm.GetPayrollReopenedNote())
            FaxAndEmailUtilities.CloseReopenPayrollEmail(CoNum)
            DB.SaveChanges()
        End If

        ResendDocuments()
        EmailShippingDepatment()
        If SaveChanges() Then DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub

    Private Sub EmailShippingDepatment()
        Try
            'add op owner from payroll table 
            Dim isPaperless = DB.fn_GetIsPaperless(CoNum)
            If isPaperless = "Paperless" OrElse isPaperless = "WebPost" Then
                Logger.Debug($"Co#: {CoNum} is a paperless client, not sending notification to print room.")
                Exit Sub
            End If

            Dim payroll = DB.PAYROLLs.Single(Function(p) p.CONUM = CoNum AndAlso p.PRNUM = PrNum)
            Dim coName = DB.COMPANies.Single(Function(c) c.CONUM = CoNum).CO_NAME
            Dim ToAdresses = New List(Of String)
            ToAdresses.AddRange(GetUdfValue("PayrollReopenedNotification-ShippingDept").Split(";"))
            Using MM As New Net.Mail.MailMessage
                MM.From = ReportFunctions.GetSenderAddress(DB)
                MM.Subject = $"Reopened Payroll - Co#: {CoNum} Payroll#: {PrNum}"
                MM.IsBodyHtml = True
                MM.Body = $"Please Note.<br/>Payroll for Co#: {CoNum} - {coName}<br/>Pr#: {PrNum}<br/>Was reopen by: {UserName}<br/>On: {DateTime.Now:g}<br/>OpOwner: {payroll.OPNAME}"
                For Each item In ToAdresses
                    If item.Length > 0 AndAlso item.Contains("@") Then
                        MM.To.Add(item)
                    End If
                Next
                Using Client As New Net.Mail.SmtpClient()
                    Client.Send(MM)
                End Using
            End Using
        Catch ex As Exception
            Logger.Error(ex, "Error sending email to Print Room of reopened payroll")
            DisplayErrorMessage("Error notifying Print Room of payroll reopened", ex)
        End Try
    End Sub

    Private Sub ResendDocuments()
        Dim dbAdmin As dbEPDataDataContext = New dbEPDataDataContext(GetAdminConnectionString)
        Dim logs = dbAdmin.ExtractLogs.Where(Function(e) e.CoNum = CoNum AndAlso e.PrNum = PrNum AndAlso e.IsVoid.GetValueOrDefault = False)
        If logs.Count > 0 Then
            Dim ExportCount = logs.Where(Function(l) l.Action = "Export").Count

            If XtraMessageBox.Show("{0} files has already bean Processed. would you like to reprocess it?".FormatWith(ExportCount), "Reprocess ?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> System.Windows.Forms.DialogResult.Yes Then
                Exit Sub
            End If

            For Each log In logs
                log.IsVoid = True
            Next
        End If
        dbAdmin.SaveChanges()
    End Sub

    Private Function SaveChanges() As Boolean
        If cbePrStatus.Text = "Entering Checks" Then
            Dim pr_in_proc = DB.pr_batch_in_processes.SingleOrDefault(Function(p) p.CoNum.Equals(CoNum) AndAlso p.PrNum.Equals(PrNum) AndAlso Not p.IsDeleted.GetValueOrDefault)
            If pr_in_proc IsNot Nothing Then
                pr_in_proc.CompleteBy = lueTakePayrollTo.Text
                pr_in_proc.LastOpenedBy = lueTakePayrollTo.Text
            End If
        End If
        Return DB.SaveChanges()
    End Function

    Private Sub slueCompany_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs)
        If DB IsNot Nothing AndAlso DB.GetChangeSet.Updates.Count > 0 Then
            If XtraMessageBox.Show("Save Changes?", "Save?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.Yes AndAlso Not SaveChanges() Then
                e.AcceptValue = False
                Exit Sub
            End If
        End If
        CoNum = e.Value
        LoadCompany()
    End Sub


    Private Sub btnEmailRequestToBankingDep_Click(sender As Object, e As EventArgs) Handles btnEmailRequestToBankingDep.Click
        If bsPayroll.Current Is Nothing Then
            XtraMessageBox.Show("Please select a company.")
            Exit Sub
        End If
        cbeEmailTo.Properties.Items.Clear()
        clbcEmailCC.Items.Clear()
        Dim list = (From users In DB.FrontDeskPermissions Join emails In DB.DBUSERs On users.UserName Equals emails.name
                    Where users.BankingDep.HasValue
                    Order By users.BankingDep
                    Select String.Format("{0} ({1});", users.UserName, emails.email)).ToArray
        cbeEmailTo.Properties.Items.AddRange(list)
        cbeEmailTo.SelectedItem = (From f In DB.FrontDeskPermissions Join emails In DB.DBUSERs On f.UserName Equals emails.name Where f.BankingDep = 1 Select String.Format("{0} ({1});", f.UserName, emails.email)).FirstOrDefault
        clbcEmailCC.Items.AddRange(list)
        ShowEmailPanel(True)
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        If DB IsNot Nothing AndAlso DB.GetChangeSet.Updates.Count > 0 Then
            If XtraMessageBox.Show("Discard Changes?", "Discard Changes?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.No Then
                Exit Sub
            Else
                DB = New dbEPDataDataContext(GetConnectionString())
            End If
        End If
        Dim frm = New frmCompanyList
        Dim results = frm.ShowDialog()
        If results = System.Windows.Forms.DialogResult.OK Then
            Me.CoNum = frm.SelectedCompany.CONUM
            SuspendLayout()
            Try
                Me.LoadCompany()
            Finally
                ResumeLayout()
            End Try
        End If
        frm.Dispose()
        btnSearch.Focus()
    End Sub



    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        ShowEmailPanel(False)
    End Sub

    Private Sub ShowEmailPanel(value As Boolean)
        If value Then panelEmail.Location = New Point((Size.Width - panelEmail.Width) / 2, (Size.Height - panelEmail.Height) / 2)
        panelEmail.Visible = value
        GroupControl1.Enabled = Not value
        GroupControl2.Enabled = Not value
        GroupControl3.Enabled = Not value
    End Sub

    'Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
    '    If cbeEmailTo.Text.IsNullOrWhiteSpace Then
    '        XtraMessageBox.Show("Please select a 'To' email address")
    '        Exit Sub
    '    End If
    '    ShowEmailPanel(False)
    '    Dim cur As PAYROLL = bsPayroll.Current
    '    Dim _email = New OutlookEmail
    '    Dim msg = "Reopen Payroll Request For CoNum: {0} {1} PrNum: {2}".FormatWith(teCompany.Text, teCoName.Text, tePayrollNum.Text)
    '    _email.Subject = msg
    '    _email.Body = msg
    '    _email.ToEmail.Add(cbeEmailTo.Text)
    '    _email.CcEmail.AddRange(clbcEmailCC.Items.GetCheckedValues().Cast(Of String).ToArray)
    '    _email.Show()
    '    '_email.Send(msg, msg, cbeEmailTo.Text, clbcEmailCC.Items.GetCheckedValues().Cast(Of String).ToArray)
    'End Sub

    Private Sub gvErrors_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles gvErrors.CellValueChanged
        btnSave.Enabled = allowSave AndAlso Errors.All(Function(er) er.Checked)
    End Sub

    Private Sub riCeDone_CheckedChanged(sender As Object, e As EventArgs) Handles riCeDone.CheckedChanged
        gvErrors.PostEditor()
        btnSave.Enabled = allowSave AndAlso Errors.All(Function(er) er.Checked)
    End Sub

    Private Sub cbePrStatus_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles cbePrStatus.CloseUp
        If e.CloseMode = PopupCloseMode.Normal AndAlso e.Value <> CurPayrl.PAYROLL_STATUS Then
            e.AcceptValue = XtraMessageBox.Show("Are you sure you would like to change the {0}?{1} If you're proceeding, enter password in next screen surrounded with '*'".FormatWith(CType(sender, Control).Tag, vbCrLf),
                                          "Change?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Sub cbeInvoiceAchStatus_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles cbeInvoiceAchStatus.CloseUp
        If e.CloseMode = PopupCloseMode.Normal AndAlso e.Value <> CurPayrl.BILL_ACH_STAT Then
            e.AcceptValue = XtraMessageBox.Show("Are you sure you would like to change the {0}?{1} If you're proceeding, enter password in next screen surrounded with '*'".FormatWith(CType(sender, Control).Tag, vbCrLf),
                                          "Change?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Sub cbeTotalTaxStatus_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles cbeTotalTaxStatus.CloseUp
        If e.CloseMode = PopupCloseMode.Normal AndAlso e.Value <> CurPayrl.TOTALTAX_STATUS Then
            e.AcceptValue = XtraMessageBox.Show("Are you sure you would like to change the {0}?{1} If you're proceeding, enter password in next screen surrounded with '*'".FormatWith(CType(sender, Control).Tag, vbCrLf),
                                          "Change?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Sub btnReopenThePayroll_Click(sender As Object, e As EventArgs) Handles btnReopenThePayroll.Click
        If XtraMessageBox.Show("Are you sure you would like to change the Pr Status?{0} If you're proceeding, enter password in next screen surrounded with '*'".FormatWith(CType(sender, Control).Tag, vbCrLf),
                                            "Change?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK Then
            CurPayrl.PAYROLL_STATUS = "Entering Checks"
        End If
    End Sub

    Private Sub btnReprintPayroll_Click(sender As Object, e As EventArgs) Handles btnReprintPayroll.Click
        If XtraMessageBox.Show("Are you sure you would like to change the Pr Status?{0} If you're proceeding, enter password in next screen surrounded with '*'".FormatWith(CType(sender, Control).Tag, vbCrLf),
                                             "Change?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK Then
            CurPayrl.PAYROLL_STATUS = "Finished Checks"
        End If
    End Sub

    Private Async Sub btnDeletePayroll_Click(sender As Object, e As EventArgs) Handles btnDeletePayroll.Click
        Dim util = New DeletePayroll
        Dim Checkdate As Date
        If Not util.CanDeletePayroll(Me.CoNum, Me.PrNum, Checkdate) Then
            Return
        End If
        Try
            Me.btnDeletePayroll.Enabled = False
            AddHandler util.StatusMessage, Sub(message As String)
                                               If String.IsNullOrEmpty(message) Then
                                                   Me.CloseWaitForm
                                               Else
                                                   Me.ShowDefaultWaitForm("Please Wait", message)
                                               End If
                                           End Sub
            If Await util.DeletePayroll(Me.CoNum, Me.PrNum, True, Checkdate) Then
                Me.DialogResult = DialogResult.Abort
                Me.Close()
            End If
        Catch ex As Exception
            DisplayErrorMessage(ex.Message, ex)
        Finally
            Me.CloseWaitForm
        End Try
    End Sub
End Class

Public Class Errors
    Property ErrorMsg As String
    Property Checked As Boolean
End Class