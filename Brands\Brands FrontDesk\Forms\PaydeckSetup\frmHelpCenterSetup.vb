﻿Imports System.ComponentModel
Imports Microsoft.EntityFrameworkCore
Public Class frmHelpCenterSetup

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property _dataContextDB As dbEPDataDataContext

    Private Sub FrmHelpCenterSetup_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub BbiSave_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSave.ItemClick
        GridView1.PostEditor()
        Try
            If Not _dataContextDB.SaveChanges() Then
                DisplayErrorMessage("Unable to save changes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        End Try
    End Sub

    Private Sub LoadData()
        Try
            _dataContextDB = New dbEPDataDataContext(GetConnectionString)
            _dataContextDB.ResourcesLinkss.Load()
            GridControl1.DataSource = _dataContextDB.ResourcesLinkss.Local.ToBindingList()
            riPageNames.Items.Clear()
            riPageNames.Items.AddRange(GetUdfValueSplitted("HelpCenter_PageNames"))
        Catch ex As Exception
            DisplayErrorMessage("Error loading ResorcesLinks", ex)
        End Try
    End Sub

    Private Sub BbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delte Row", Sub()
                                                                                   DeleteRow(e.HitInfo.RowHandle)
                                                                               End Sub, My.Resources.deletelist_16x16))
        End If
    End Sub

    Private Sub DeleteRow(rowHandle As Integer)
        GridView1.DeleteRow(rowHandle)
    End Sub


End Class