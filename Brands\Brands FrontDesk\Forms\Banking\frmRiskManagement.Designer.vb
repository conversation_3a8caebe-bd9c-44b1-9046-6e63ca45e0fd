﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmRiskManagement
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
        Me.GridControlAlertEmails = New DevExpress.XtraGrid.GridControl()
        Me.GridViewAlertEmails = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.lueStatus = New DevExpress.XtraEditors.LookUpEdit()
        Me.UcCompInfo1 = New Brands_FrontDesk.ucCompInfo()
        Me.GridControlAlertsTrig = New DevExpress.XtraGrid.GridControl()
        Me.GridViewAlertsTrig = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.AccordionControl1 = New DevExpress.XtraBars.Navigation.AccordionControl()
        Me.aceStatus = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.teCoNum = New DevExpress.XtraEditors.TextEdit()
        Me.tePrNum = New DevExpress.XtraEditors.TextEdit()
        Me.teNote = New DevExpress.XtraEditors.TextEdit()
        Me.meStatusLog = New DevExpress.XtraEditors.MemoEdit()
        Me.GridControlAchTran = New DevExpress.XtraGrid.GridControl()
        Me.GridViewAchTran = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.meNotesLog = New DevExpress.XtraEditors.MemoEdit()
        Me.btnSave = New DevExpress.XtraEditors.SimpleButton()
        Me.lueDDStatus = New DevExpress.XtraEditors.LookUpEdit()
        Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.bbiRefresh = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiAutoEmails = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiAchSettings = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiEmailClient = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiOpenCo = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiOpenEmp = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiSetCoPassword = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiRemoveCoPass = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiViewNacha = New DevExpress.XtraBars.BarButtonItem()
        Me.PopupMenuOpenNacha = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.bbiOpenNachaDD = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiOpenNachaTax = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiOpenNachaAll = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiPlaid = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiRequestWire = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiLimit = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiDelayedDD = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItemRequestWireDD = New DevExpress.XtraBars.BarSubItem()
        Me.bbiRequestWireDDNewTicket = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiRequestWireDDAttach = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItemRequestWireTax = New DevExpress.XtraBars.BarSubItem()
        Me.bbiRequestWireTaxNewTicket = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiRequestWireTaxAttach = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiClearRisk = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiGlobalRiskSettings = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiBankingHold = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup3 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.lueTaxStatus = New DevExpress.XtraEditors.LookUpEdit()
        Me.chkShowLinkedCompanies = New DevExpress.XtraEditors.CheckEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciDDStatus = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciTaxStatus = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.PopupMenuRequestWire = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.RibbonPageGroup2 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.SplashScreenManager1 = New DevExpress.XtraSplashScreen.SplashScreenManager(Me, GetType(Global.Brands_FrontDesk.WaitForm1), True, True)
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.lcRoot.SuspendLayout()
        CType(Me.GridControlAlertEmails, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewAlertEmails, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControlAlertsTrig, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewAlertsTrig, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AccordionControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCoNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tePrNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teNote.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.meStatusLog.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControlAchTran, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewAchTran, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.meNotesLog.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueDDStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenuOpenNacha, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueTaxStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkShowLinkedCompanies.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciDDStatus, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciTaxStatus, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenuRequestWire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lcRoot
        '
        Me.lcRoot.Controls.Add(Me.GridControlAlertEmails)
        Me.lcRoot.Controls.Add(Me.lueStatus)
        Me.lcRoot.Controls.Add(Me.UcCompInfo1)
        Me.lcRoot.Controls.Add(Me.GridControlAlertsTrig)
        Me.lcRoot.Controls.Add(Me.GridControl1)
        Me.lcRoot.Controls.Add(Me.AccordionControl1)
        Me.lcRoot.Controls.Add(Me.teCoNum)
        Me.lcRoot.Controls.Add(Me.tePrNum)
        Me.lcRoot.Controls.Add(Me.teNote)
        Me.lcRoot.Controls.Add(Me.meStatusLog)
        Me.lcRoot.Controls.Add(Me.GridControlAchTran)
        Me.lcRoot.Controls.Add(Me.meNotesLog)
        Me.lcRoot.Controls.Add(Me.btnSave)
        Me.lcRoot.Controls.Add(Me.lueDDStatus)
        Me.lcRoot.Controls.Add(Me.lueTaxStatus)
        Me.lcRoot.Controls.Add(Me.chkShowLinkedCompanies)
        Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lcRoot.Location = New System.Drawing.Point(0, 142)
        Me.lcRoot.Name = "lcRoot"
        Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(1270, 191, 650, 400)
        Me.lcRoot.Root = Me.LayoutControlGroup1
        Me.lcRoot.Size = New System.Drawing.Size(1286, 821)
        Me.lcRoot.TabIndex = 0
        Me.lcRoot.Text = "LayoutControl1"
        '
        'GridControlAlertEmails
        '
        Me.GridControlAlertEmails.Location = New System.Drawing.Point(12, 601)
        Me.GridControlAlertEmails.MainView = Me.GridViewAlertEmails
        Me.GridControlAlertEmails.Name = "GridControlAlertEmails"
        Me.GridControlAlertEmails.Size = New System.Drawing.Size(765, 208)
        Me.GridControlAlertEmails.TabIndex = 16
        Me.GridControlAlertEmails.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewAlertEmails})
        '
        'GridViewAlertEmails
        '
        Me.GridViewAlertEmails.GridControl = Me.GridControlAlertEmails
        Me.GridViewAlertEmails.Name = "GridViewAlertEmails"
        Me.GridViewAlertEmails.OptionsBehavior.Editable = False
        Me.GridViewAlertEmails.OptionsView.ColumnAutoWidth = False
        Me.GridViewAlertEmails.OptionsView.ShowGroupPanel = False
        Me.GridViewAlertEmails.OptionsView.ShowViewCaption = True
        Me.GridViewAlertEmails.ViewCaption = "Alert Emails Sent"
        '
        'lueStatus
        '
        Me.lueStatus.Location = New System.Drawing.Point(892, 381)
        Me.lueStatus.Name = "lueStatus"
        Me.lueStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueStatus.Properties.NullText = "Choose"
        Me.lueStatus.Size = New System.Drawing.Size(358, 20)
        Me.lueStatus.StyleController = Me.lcRoot
        Me.lueStatus.TabIndex = 13
        '
        'UcCompInfo1
        '
        Me.UcCompInfo1.CompanyInfoTabbedControlVisible = False
        Me.UcCompInfo1.Location = New System.Drawing.Point(822, 23)
        Me.UcCompInfo1.Name = "UcCompInfo1"
        Me.UcCompInfo1.Size = New System.Drawing.Size(440, 298)
        Me.UcCompInfo1.TabIndex = 12
        '
        'GridControlAlertsTrig
        '
        Me.GridControlAlertsTrig.Location = New System.Drawing.Point(12, 407)
        Me.GridControlAlertsTrig.MainView = Me.GridViewAlertsTrig
        Me.GridControlAlertsTrig.Name = "GridControlAlertsTrig"
        Me.GridControlAlertsTrig.Size = New System.Drawing.Size(765, 190)
        Me.GridControlAlertsTrig.TabIndex = 10
        Me.GridControlAlertsTrig.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewAlertsTrig})
        '
        'GridViewAlertsTrig
        '
        Me.GridViewAlertsTrig.GridControl = Me.GridControlAlertsTrig
        Me.GridViewAlertsTrig.Name = "GridViewAlertsTrig"
        Me.GridViewAlertsTrig.OptionsBehavior.Editable = False
        Me.GridViewAlertsTrig.OptionsView.ColumnAutoWidth = False
        Me.GridViewAlertsTrig.OptionsView.ShowGroupPanel = False
        Me.GridViewAlertsTrig.OptionsView.ShowViewCaption = True
        Me.GridViewAlertsTrig.ViewCaption = "Alerts Payroll Stop"
        Me.GridViewAlertsTrig.ViewCaptionHeight = 0
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(171, 12)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(606, 391)
        Me.GridControl1.TabIndex = 2
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'AccordionControl1
        '
        Me.AccordionControl1.AllowItemSelection = True
        Me.AccordionControl1.Elements.AddRange(New DevExpress.XtraBars.Navigation.AccordionControlElement() {Me.aceStatus})
        Me.AccordionControl1.Location = New System.Drawing.Point(12, 12)
        Me.AccordionControl1.Name = "AccordionControl1"
        Me.AccordionControl1.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.[Auto]
        Me.AccordionControl1.ShowGroupExpandButtons = False
        Me.AccordionControl1.Size = New System.Drawing.Size(155, 391)
        Me.AccordionControl1.StyleController = Me.lcRoot
        Me.AccordionControl1.TabIndex = 4
        Me.AccordionControl1.Text = "AccordionControl1"
        '
        'aceStatus
        '
        Me.aceStatus.Name = "aceStatus"
        Me.aceStatus.Text = "Status"
        '
        'teCoNum
        '
        Me.teCoNum.Location = New System.Drawing.Point(892, 357)
        Me.teCoNum.Name = "teCoNum"
        Me.teCoNum.Properties.ReadOnly = True
        Me.teCoNum.Size = New System.Drawing.Size(176, 20)
        Me.teCoNum.StyleController = Me.lcRoot
        Me.teCoNum.TabIndex = 4
        '
        'tePrNum
        '
        Me.tePrNum.Location = New System.Drawing.Point(1130, 357)
        Me.tePrNum.Name = "tePrNum"
        Me.tePrNum.Properties.ReadOnly = True
        Me.tePrNum.Size = New System.Drawing.Size(120, 20)
        Me.tePrNum.StyleController = Me.lcRoot
        Me.tePrNum.TabIndex = 5
        '
        'teNote
        '
        Me.teNote.Location = New System.Drawing.Point(892, 405)
        Me.teNote.Name = "teNote"
        Me.teNote.Size = New System.Drawing.Size(358, 20)
        Me.teNote.StyleController = Me.lcRoot
        Me.teNote.TabIndex = 6
        '
        'meStatusLog
        '
        Me.meStatusLog.Location = New System.Drawing.Point(892, 429)
        Me.meStatusLog.Name = "meStatusLog"
        Me.meStatusLog.Properties.LinesCount = 2
        Me.meStatusLog.Properties.ReadOnly = True
        Me.meStatusLog.Size = New System.Drawing.Size(358, 30)
        Me.meStatusLog.StyleController = Me.lcRoot
        Me.meStatusLog.TabIndex = 8
        '
        'GridControlAchTran
        '
        Me.GridControlAchTran.Location = New System.Drawing.Point(822, 601)
        Me.GridControlAchTran.MainView = Me.GridViewAchTran
        Me.GridControlAchTran.Name = "GridControlAchTran"
        Me.GridControlAchTran.Size = New System.Drawing.Size(440, 196)
        Me.GridControlAchTran.TabIndex = 9
        Me.GridControlAchTran.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewAchTran})
        '
        'GridViewAchTran
        '
        Me.GridViewAchTran.GridControl = Me.GridControlAchTran
        Me.GridViewAchTran.Name = "GridViewAchTran"
        Me.GridViewAchTran.OptionsBehavior.Editable = False
        Me.GridViewAchTran.OptionsView.ColumnAutoWidth = False
        Me.GridViewAchTran.OptionsView.ShowGroupPanel = False
        Me.GridViewAchTran.OptionsView.ShowViewCaption = True
        Me.GridViewAchTran.ViewCaption = "NSF History"
        '
        'meNotesLog
        '
        Me.meNotesLog.Location = New System.Drawing.Point(892, 463)
        Me.meNotesLog.Name = "meNotesLog"
        Me.meNotesLog.Properties.ReadOnly = True
        Me.meNotesLog.Size = New System.Drawing.Size(358, 49)
        Me.meNotesLog.StyleController = Me.lcRoot
        Me.meNotesLog.TabIndex = 14
        '
        'btnSave
        '
        Me.btnSave.Location = New System.Drawing.Point(1181, 540)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Size = New System.Drawing.Size(69, 22)
        Me.btnSave.StyleController = Me.lcRoot
        Me.btnSave.TabIndex = 15
        Me.btnSave.Text = "Save"
        '
        'lueDDStatus
        '
        Me.lueDDStatus.Location = New System.Drawing.Point(892, 516)
        Me.lueDDStatus.MenuManager = Me.RibbonControl1
        Me.lueDDStatus.Name = "lueDDStatus"
        Me.lueDDStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueDDStatus.Properties.NullText = ""
        Me.lueDDStatus.Size = New System.Drawing.Size(150, 20)
        Me.lueDDStatus.StyleController = Me.lcRoot
        Me.lueDDStatus.TabIndex = 17
        '
        'RibbonControl1
        '
        Me.RibbonControl1.ExpandCollapseItem.Id = 0
        Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.RibbonControl1.SearchEditItem, Me.bbiRefresh, Me.bbiAutoEmails, Me.bbiAchSettings, Me.bbiEmailClient, Me.bbiOpenCo, Me.bbiOpenEmp, Me.bbiSetCoPassword, Me.bbiRemoveCoPass, Me.bbiViewNacha, Me.bbiPlaid, Me.bbiRequestWire, Me.bbiLimit, Me.bbiDelayedDD, Me.BarSubItemRequestWireDD, Me.BarSubItemRequestWireTax, Me.bbiRequestWireDDNewTicket, Me.bbiRequestWireDDAttach, Me.bbiRequestWireTaxNewTicket, Me.bbiRequestWireTaxAttach, Me.bbiOpenNachaDD, Me.bbiOpenNachaTax, Me.bbiOpenNachaAll, Me.bbiClearRisk, Me.bbiGlobalRiskSettings, Me.bbiBankingHold})
        Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl1.MaxItemId = 26
        Me.RibbonControl1.Name = "RibbonControl1"
        Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
        Me.RibbonControl1.Size = New System.Drawing.Size(1286, 142)
        '
        'bbiRefresh
        '
        Me.bbiRefresh.Caption = "Refresh"
        Me.bbiRefresh.Id = 1
        Me.bbiRefresh.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.refresh
        Me.bbiRefresh.Name = "bbiRefresh"
        '
        'bbiAutoEmails
        '
        Me.bbiAutoEmails.Caption = "Auto Emails"
        Me.bbiAutoEmails.Id = 2
        Me.bbiAutoEmails.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.emailtemplate_32x32
        Me.bbiAutoEmails.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.emailtemplate_32x32
        Me.bbiAutoEmails.Name = "bbiAutoEmails"
        Me.bbiAutoEmails.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'bbiAchSettings
        '
        Me.bbiAchSettings.Caption = "Settings"
        Me.bbiAchSettings.Id = 3
        Me.bbiAchSettings.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.technology_32x32
        Me.bbiAchSettings.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.technology_32x32
        Me.bbiAchSettings.Name = "bbiAchSettings"
        Me.bbiAchSettings.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'bbiEmailClient
        '
        Me.bbiEmailClient.Caption = "Email Client"
        Me.bbiEmailClient.Id = 4
        Me.bbiEmailClient.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.mail_16x16
        Me.bbiEmailClient.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.mail_32x32
        Me.bbiEmailClient.Name = "bbiEmailClient"
        '
        'bbiOpenCo
        '
        Me.bbiOpenCo.Caption = "Open Co"
        Me.bbiOpenCo.Id = 5
        Me.bbiOpenCo.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.OpenCo
        Me.bbiOpenCo.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.OpenCoLarge
        Me.bbiOpenCo.Name = "bbiOpenCo"
        '
        'bbiOpenEmp
        '
        Me.bbiOpenEmp.Caption = "Open Emp"
        Me.bbiOpenEmp.Enabled = False
        Me.bbiOpenEmp.Id = 6
        Me.bbiOpenEmp.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.customer_32x32
        Me.bbiOpenEmp.Name = "bbiOpenEmp"
        Me.bbiOpenEmp.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'bbiSetCoPassword
        '
        Me.bbiSetCoPassword.Caption = "Set Co Pass"
        Me.bbiSetCoPassword.Id = 7
        Me.bbiSetCoPassword.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.lock
        Me.bbiSetCoPassword.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.Lock_32x32
        Me.bbiSetCoPassword.Name = "bbiSetCoPassword"
        Me.bbiSetCoPassword.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'bbiRemoveCoPass
        '
        Me.bbiRemoveCoPass.Caption = "Remove Co Pass"
        Me.bbiRemoveCoPass.Id = 8
        Me.bbiRemoveCoPass.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.Unlcok_32x32
        Me.bbiRemoveCoPass.Name = "bbiRemoveCoPass"
        Me.bbiRemoveCoPass.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'bbiViewNacha
        '
        Me.bbiViewNacha.ActAsDropDown = True
        Me.bbiViewNacha.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown
        Me.bbiViewNacha.Caption = "Open Nacha"
        Me.bbiViewNacha.DropDownControl = Me.PopupMenuOpenNacha
        Me.bbiViewNacha.Id = 9
        Me.bbiViewNacha.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.currency_16x16
        Me.bbiViewNacha.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.currency_32x32
        Me.bbiViewNacha.Name = "bbiViewNacha"
        '
        'PopupMenuOpenNacha
        '
        Me.PopupMenuOpenNacha.ItemLinks.Add(Me.bbiOpenNachaDD)
        Me.PopupMenuOpenNacha.ItemLinks.Add(Me.bbiOpenNachaTax)
        Me.PopupMenuOpenNacha.ItemLinks.Add(Me.bbiOpenNachaAll)
        Me.PopupMenuOpenNacha.Name = "PopupMenuOpenNacha"
        Me.PopupMenuOpenNacha.Ribbon = Me.RibbonControl1
        '
        'bbiOpenNachaDD
        '
        Me.bbiOpenNachaDD.Caption = "Direct Deposit"
        Me.bbiOpenNachaDD.Id = 20
        Me.bbiOpenNachaDD.Name = "bbiOpenNachaDD"
        '
        'bbiOpenNachaTax
        '
        Me.bbiOpenNachaTax.Caption = "Tax"
        Me.bbiOpenNachaTax.Id = 21
        Me.bbiOpenNachaTax.Name = "bbiOpenNachaTax"
        '
        'bbiOpenNachaAll
        '
        Me.bbiOpenNachaAll.Caption = "All Nacha"
        Me.bbiOpenNachaAll.Id = 22
        Me.bbiOpenNachaAll.Name = "bbiOpenNachaAll"
        '
        'bbiPlaid
        '
        Me.bbiPlaid.Caption = "Plaid"
        Me.bbiPlaid.Id = 10
        Me.bbiPlaid.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.bank_svg
        Me.bbiPlaid.Name = "bbiPlaid"
        '
        'bbiRequestWire
        '
        Me.bbiRequestWire.Caption = "Request Wire"
        Me.bbiRequestWire.Id = 11
        Me.bbiRequestWire.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.RequestWire2
        Me.bbiRequestWire.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.RequestWire2
        Me.bbiRequestWire.Name = "bbiRequestWire"
        '
        'bbiLimit
        '
        Me.bbiLimit.Caption = "Client Risk Settings"
        Me.bbiLimit.Id = 12
        Me.bbiLimit.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.limit
        Me.bbiLimit.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.limit
        Me.bbiLimit.Name = "bbiLimit"
        '
        'bbiDelayedDD
        '
        Me.bbiDelayedDD.Caption = "Delayed DD / Holds"
        Me.bbiDelayedDD.Id = 13
        Me.bbiDelayedDD.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.delayed
        Me.bbiDelayedDD.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.delayed
        Me.bbiDelayedDD.Name = "bbiDelayedDD"
        '
        'BarSubItemRequestWireDD
        '
        Me.BarSubItemRequestWireDD.Caption = "Direct Deposit"
        Me.BarSubItemRequestWireDD.Id = 14
        Me.BarSubItemRequestWireDD.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireDDNewTicket), New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireDDAttach)})
        Me.BarSubItemRequestWireDD.Name = "BarSubItemRequestWireDD"
        '
        'bbiRequestWireDDNewTicket
        '
        Me.bbiRequestWireDDNewTicket.Caption = "Start Ticket In Zendesk"
        Me.bbiRequestWireDDNewTicket.Id = 16
        Me.bbiRequestWireDDNewTicket.Name = "bbiRequestWireDDNewTicket"
        '
        'bbiRequestWireDDAttach
        '
        Me.bbiRequestWireDDAttach.Caption = "Attach To Ticket In Zendesk"
        Me.bbiRequestWireDDAttach.Id = 17
        Me.bbiRequestWireDDAttach.Name = "bbiRequestWireDDAttach"
        '
        'BarSubItemRequestWireTax
        '
        Me.BarSubItemRequestWireTax.Caption = "Tax"
        Me.BarSubItemRequestWireTax.Id = 15
        Me.BarSubItemRequestWireTax.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireTaxNewTicket), New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireTaxAttach)})
        Me.BarSubItemRequestWireTax.Name = "BarSubItemRequestWireTax"
        '
        'bbiRequestWireTaxNewTicket
        '
        Me.bbiRequestWireTaxNewTicket.Caption = "Start Ticket In Zendesk"
        Me.bbiRequestWireTaxNewTicket.Id = 18
        Me.bbiRequestWireTaxNewTicket.Name = "bbiRequestWireTaxNewTicket"
        '
        'bbiRequestWireTaxAttach
        '
        Me.bbiRequestWireTaxAttach.Caption = "Attach To Ticket In Zendesk"
        Me.bbiRequestWireTaxAttach.Id = 19
        Me.bbiRequestWireTaxAttach.Name = "bbiRequestWireTaxAttach"
        '
        'bbiClearRisk
        '
        Me.bbiClearRisk.Caption = "Clear Risk"
        Me.bbiClearRisk.Id = 23
        Me.bbiClearRisk.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.RiskCleared
        Me.bbiClearRisk.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.RiskCleared
        Me.bbiClearRisk.Name = "bbiClearRisk"
        '
        'bbiGlobalRiskSettings
        '
        Me.bbiGlobalRiskSettings.Caption = "Global Risk Settings"
        Me.bbiGlobalRiskSettings.Id = 24
        Me.bbiGlobalRiskSettings.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.GlobalRiskSettings
        Me.bbiGlobalRiskSettings.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.GlobalRiskSettings
        Me.bbiGlobalRiskSettings.Name = "bbiGlobalRiskSettings"
        '
        'bbiBankingHold
        '
        Me.bbiBankingHold.Caption = "Banking Hold"
        Me.bbiBankingHold.Id = 25
        Me.bbiBankingHold.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.BankingHold
        Me.bbiBankingHold.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.BankingHold
        Me.bbiBankingHold.Name = "bbiBankingHold"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1, Me.RibbonPageGroup3})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRefresh)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiAutoEmails)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiAchSettings)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiViewNacha)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiPlaid)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiClearRisk)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRequestWire)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiBankingHold)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiLimit)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiGlobalRiskSettings)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiDelayedDD)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.Text = "Actions"
        '
        'RibbonPageGroup3
        '
        Me.RibbonPageGroup3.ItemLinks.Add(Me.bbiEmailClient)
        Me.RibbonPageGroup3.ItemLinks.Add(Me.bbiOpenCo)
        Me.RibbonPageGroup3.ItemLinks.Add(Me.bbiOpenEmp)
        Me.RibbonPageGroup3.ItemLinks.Add(Me.bbiSetCoPassword)
        Me.RibbonPageGroup3.ItemLinks.Add(Me.bbiRemoveCoPass)
        Me.RibbonPageGroup3.Name = "RibbonPageGroup3"
        Me.RibbonPageGroup3.Text = "Co Options"
        '
        'lueTaxStatus
        '
        Me.lueTaxStatus.Location = New System.Drawing.Point(1104, 516)
        Me.lueTaxStatus.MenuManager = Me.RibbonControl1
        Me.lueTaxStatus.Name = "lueTaxStatus"
        Me.lueTaxStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueTaxStatus.Properties.NullText = ""
        Me.lueTaxStatus.Size = New System.Drawing.Size(146, 20)
        Me.lueTaxStatus.StyleController = Me.lcRoot
        Me.lueTaxStatus.TabIndex = 18
        '
        'chkShowLinkedCompanies
        '
        Me.chkShowLinkedCompanies.EditValue = True
        Me.chkShowLinkedCompanies.Location = New System.Drawing.Point(822, 578)
        Me.chkShowLinkedCompanies.MenuManager = Me.RibbonControl1
        Me.chkShowLinkedCompanies.Name = "chkShowLinkedCompanies"
        Me.chkShowLinkedCompanies.Properties.Caption = "Show NSF Related Companies"
        Me.chkShowLinkedCompanies.Size = New System.Drawing.Size(440, 19)
        Me.chkShowLinkedCompanies.StyleController = Me.lcRoot
        Me.chkShowLinkedCompanies.TabIndex = 19
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AllowCustomizeChildren = False
        Me.LayoutControlGroup1.AllowHide = False
        Me.LayoutControlGroup1.AppearanceGroup.Options.UseTextOptions = True
        Me.LayoutControlGroup1.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem10, Me.LayoutControlGroup2, Me.SplitterItem1, Me.LayoutControlItem15})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1286, 821)
        Me.LayoutControlGroup1.TextLocation = DevExpress.Utils.Locations.Left
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.AccordionControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.MaxSize = New System.Drawing.Size(159, 0)
        Me.LayoutControlItem1.MinSize = New System.Drawing.Size(159, 20)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(159, 395)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.GridControl1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(159, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(610, 395)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.GridControlAlertsTrig
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 395)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(769, 194)
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem10.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.AppearanceGroup.Options.UseTextOptions = True
        Me.LayoutControlGroup2.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlGroup2.CustomizationFormText = "Company Info"
        Me.LayoutControlGroup2.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted
        Me.LayoutControlGroup2.ExpandButtonVisible = True
        Me.LayoutControlGroup2.ExpandOnDoubleClick = True
        Me.LayoutControlGroup2.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem9, Me.LayoutControlGroup3, Me.LayoutControlItem12, Me.LayoutControlItem14})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(777, 0)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(489, 801)
        Me.LayoutControlGroup2.Text = "Company Info"
        Me.LayoutControlGroup2.TextLocation = DevExpress.Utils.Locations.Left
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.GridControlAchTran
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 578)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(444, 200)
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.LayoutControlItem6, Me.LayoutControlItem8, Me.LayoutControlItem5, Me.LayoutControlItem11, Me.LayoutControlItem7, Me.LayoutControlItem13, Me.lciDDStatus, Me.lciTaxStatus, Me.EmptySpaceItem2})
        Me.LayoutControlGroup3.Location = New System.Drawing.Point(0, 302)
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(444, 253)
        Me.LayoutControlGroup3.Text = "Alert Detail"
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.teCoNum
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(238, 24)
        Me.LayoutControlItem4.Text = "CoNum:"
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(55, 13)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.teNote
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(420, 24)
        Me.LayoutControlItem6.Text = "Note:"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(55, 13)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.meStatusLog
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(420, 34)
        Me.LayoutControlItem8.Text = "Status Log:"
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(55, 13)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.tePrNum
        Me.LayoutControlItem5.Location = New System.Drawing.Point(238, 0)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(182, 24)
        Me.LayoutControlItem5.Text = "PrNum:"
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(55, 13)
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.lueStatus
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(420, 24)
        Me.LayoutControlItem11.Text = "Status:"
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(55, 13)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.meNotesLog
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 106)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(420, 53)
        Me.LayoutControlItem7.Text = "Notes Log:"
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(55, 13)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.btnSave
        Me.LayoutControlItem13.Location = New System.Drawing.Point(347, 183)
        Me.LayoutControlItem13.MaxSize = New System.Drawing.Size(73, 26)
        Me.LayoutControlItem13.MinSize = New System.Drawing.Size(73, 26)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(73, 26)
        Me.LayoutControlItem13.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem13.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem13.TextVisible = False
        '
        'lciDDStatus
        '
        Me.lciDDStatus.Control = Me.lueDDStatus
        Me.lciDDStatus.CustomizationFormText = "DD Status"
        Me.lciDDStatus.Location = New System.Drawing.Point(0, 159)
        Me.lciDDStatus.Name = "lciDDStatus"
        Me.lciDDStatus.Size = New System.Drawing.Size(212, 24)
        Me.lciDDStatus.Text = "DD Status"
        Me.lciDDStatus.TextSize = New System.Drawing.Size(55, 13)
        '
        'lciTaxStatus
        '
        Me.lciTaxStatus.Control = Me.lueTaxStatus
        Me.lciTaxStatus.Location = New System.Drawing.Point(212, 159)
        Me.lciTaxStatus.Name = "lciTaxStatus"
        Me.lciTaxStatus.Size = New System.Drawing.Size(208, 24)
        Me.lciTaxStatus.Text = "Tax Status"
        Me.lciTaxStatus.TextSize = New System.Drawing.Size(55, 13)
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 183)
        Me.EmptySpaceItem2.MinSize = New System.Drawing.Size(104, 24)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(347, 26)
        Me.EmptySpaceItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.UcCompInfo1
        Me.LayoutControlItem12.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem12.MaxSize = New System.Drawing.Size(0, 302)
        Me.LayoutControlItem12.MinSize = New System.Drawing.Size(444, 302)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(444, 302)
        Me.LayoutControlItem12.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem12.TextVisible = False
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.chkShowLinkedCompanies
        Me.LayoutControlItem14.Location = New System.Drawing.Point(0, 555)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(444, 23)
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem14.TextVisible = False
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.Location = New System.Drawing.Point(769, 0)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(8, 801)
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.GridControlAlertEmails
        Me.LayoutControlItem15.Location = New System.Drawing.Point(0, 589)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(769, 212)
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem15.TextVisible = False
        '
        'PopupMenuRequestWire
        '
        Me.PopupMenuRequestWire.ItemLinks.Add(Me.BarSubItemRequestWireDD)
        Me.PopupMenuRequestWire.ItemLinks.Add(Me.BarSubItemRequestWireTax)
        Me.PopupMenuRequestWire.Name = "PopupMenuRequestWire"
        Me.PopupMenuRequestWire.Ribbon = Me.RibbonControl1
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(434, 272)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'RibbonPageGroup2
        '
        Me.RibbonPageGroup2.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.[False]
        Me.RibbonPageGroup2.Name = "RibbonPageGroup2"
        Me.RibbonPageGroup2.Text = "Actions"
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        '
        'SplashScreenManager1
        '
        Me.SplashScreenManager1.ClosingDelay = 500
        '
        'frmRiskManagement
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1286, 963)
        Me.Controls.Add(Me.lcRoot)
        Me.Controls.Add(Me.RibbonControl1)
        Me.Name = "frmRiskManagement"
        Me.Text = "RiskManagement"
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.lcRoot.ResumeLayout(False)
        CType(Me.GridControlAlertEmails, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewAlertEmails, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControlAlertsTrig, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewAlertsTrig, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AccordionControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCoNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tePrNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teNote.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.meStatusLog.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControlAchTran, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewAchTran, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.meNotesLog.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueDDStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenuOpenNacha, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueTaxStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkShowLinkedCompanies.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciDDStatus, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciTaxStatus, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenuRequestWire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents AccordionControl1 As DevExpress.XtraBars.Navigation.AccordionControl
    Friend WithEvents aceStatus As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GridControlAchTran As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridViewAchTran As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridControlAlertsTrig As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridViewAlertsTrig As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teCoNum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tePrNum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teNote As DevExpress.XtraEditors.TextEdit
    Friend WithEvents meStatusLog As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents UcCompInfo1 As ucCompInfo
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents lueStatus As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents meNotesLog As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents btnSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GridControlAlertEmails As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridViewAlertEmails As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bbiRefresh As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiAutoEmails As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup2 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bbiAchSettings As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiEmailClient As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiOpenCo As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup3 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bbiOpenEmp As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiSetCoPassword As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiRemoveCoPass As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Timer1 As Timer
    Friend WithEvents SplashScreenManager1 As DevExpress.XtraSplashScreen.SplashScreenManager
    Friend WithEvents bbiViewNacha As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiPlaid As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiRequestWire As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiLimit As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiDelayedDD As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenuRequestWire As DevExpress.XtraBars.PopupMenu
    Friend WithEvents BarSubItemRequestWireDD As DevExpress.XtraBars.BarSubItem
    Friend WithEvents bbiRequestWireDDNewTicket As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiRequestWireDDAttach As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarSubItemRequestWireTax As DevExpress.XtraBars.BarSubItem
    Friend WithEvents bbiRequestWireTaxNewTicket As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiRequestWireTaxAttach As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenuOpenNacha As DevExpress.XtraBars.PopupMenu
    Friend WithEvents bbiOpenNachaDD As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiOpenNachaTax As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents lciDDStatus As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lciTaxStatus As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lueDDStatus As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueTaxStatus As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents chkShowLinkedCompanies As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents bbiOpenNachaAll As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiClearRisk As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiGlobalRiskSettings As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiBankingHold As DevExpress.XtraBars.BarButtonItem
End Class
