﻿Imports System.ComponentModel
Public Class frmManualBillingDraftEdit

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EditRow As DD_Manual_Trans_Log

    Private Sub frmManulaBillingDraftEdit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.NachaTaxDraftsBindingSource.DataSource = EditRow
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub

End Class