﻿'Imports Microsoft.Office.Interop
'Imports System.Reflection
'Imports HtmlAgilityPack
'Imports DevExpress.XtraEditors
'Imports System.Linq
'Imports System.Runtime.InteropServices
'Imports System.ComponentModel

'Public Class OutlookEmail
'    Implements IDisposable
'    Dim _OutlookApplication As Microsoft.Office.Interop.Outlook.Application = New Outlook.Application()
'    Dim oNS As Microsoft.Office.Interop.Outlook.NameSpace = _OutlookApplication.GetNamespace("mapi")
'    Dim _OutlookMailItem As Microsoft.Office.Interop.Outlook.MailItem
'    Property Subject As String
'    Property Body As String
'    Property FromEmail As String
'    Property ToEmail As List(Of String) = New List(Of String)
'    Property CcEmail As List(Of String) = New List(Of String)
'    Property BccEmail As List(Of String) = New List(Of String)
'    Property Attachment As List(Of String) = New List(Of String)

'    Public Sub Show()
'        SetUp()
'        _OutlookMailItem.Display()
'    End Sub

'    Public Sub SetUp()
'        oNS.Logon(Missing.Value, Missing.Value, True, True)
'        _OutlookMailItem = CType(_OutlookApplication.CreateItem(Outlook.OlItemType.olMailItem), Outlook.MailItem)
'        _OutlookMailItem.Subject = Subject

'        If Body.StartsWith("<html") OrElse Body.StartsWith("<!DOCTYPE") OrElse Body.StartsWith("<div") Then
'            _OutlookMailItem.HTMLBody = Body
'        Else
'            _OutlookMailItem.Body = Body
'        End If

'        If Not FromEmail.IsNullOrWhiteSpace() Then
'            Try
'                _OutlookMailItem.SendUsingAccount = _OutlookApplication.Session.Accounts(FromEmail)
'            Catch ex As Exception
'                DisplayErrorMessage("Error. Account: {0} was not found in your Outlook application. (Please make sure you have restarted Outlook after adding the account".FormatWith(FromEmail), ex)
'                Exit Sub
'            End Try
'        End If

'        For Each _email As String In ToEmail
'            If (_email.IsNotNullOrWhiteSpace) Then _OutlookMailItem.Recipients.Add(_email).Resolve()
'        Next

'        If CcEmail IsNot Nothing Then
'            For Each _email As String In CcEmail
'                _OutlookMailItem.CC += ";" + _email
'            Next
'        End If

'        If BccEmail IsNot Nothing Then
'            For Each _email As String In BccEmail
'                _OutlookMailItem.BCC += ";" + _email
'            Next
'        End If

'        For Each file In Attachment
'            _OutlookMailItem.Attachments.Add(file, Outlook.OlAttachmentType.olByValue)
'        Next
'    End Sub

'    Public Sub Send()
'        SetUp()
'        _OutlookMailItem.Send()
'    End Sub

'    Public Sub CreateReply(origFromEmail As String, origToEmail As String, sentDate As DateTime)
'        Subject = If(Subject IsNot Nothing AndAlso Subject.StartsWith("RE:", StringComparison.CurrentCultureIgnoreCase), Subject, "Re: {0}".FormatWith(Subject))
'        Body = ReplyBodyFormate(origFromEmail, sentDate, origToEmail, Subject)
'    End Sub

'    Public Sub CreateFwd(origFromEmail As String, origToEmail As String, sentDate As DateTime)
'        Subject = If(Subject IsNot Nothing AndAlso Subject.StartsWith("FW:", StringComparison.CurrentCultureIgnoreCase), Subject, "Fw: {0}".FormatWith(Subject))
'        Body = ReplyBodyFormate(origFromEmail, sentDate, origToEmail, Subject)
'    End Sub

'    Private Function ReplyBodyFormate(fromEmail As String, sentDate As DateTime, toEmail As String, subject As String) As String
'        If Body.StartsWith("<html") OrElse Body.StartsWith("<!DOCTYPE") OrElse Body.StartsWith("<div") Then
'            Dim doc = New HtmlDocument
'            Dim replyHtml As String = <![CDATA[
'<br/>
'<br/>
'{0}
'<hr/>
'<b>From: </b>{1}
'<br/>
'<b>Sent: </b>{2:f}
'<br/>
'<b>To: </b>{3}
'<br/>
'<b>Subject: </b>{4}
'<br/>
'<br/>
'    ]]>.Value().FormatWith(StrConv(UserName.Replace(".", " "), VbStrConv.ProperCase), fromEmail, sentDate, toEmail, subject)
'            doc.LoadHtml(Body)
'            Dim obj = doc.DocumentNode.SelectSingleNode("//body")
'            obj.InnerHtml = replyHtml & obj.InnerHtml
'            Return doc.DocumentNode.OuterHtml
'        Else
'            Return String.Format(<![CDATA[
'{0}{1}
'--------------------------------------------------------
'From: {2}
'Sent: {3}
'To: {4}
'Subject: {5} {6}{7}{8}]]>.Value, vbCrLf & vbCrLf, StrConv(UserName.Replace(".", " "), VbStrConv.ProperCase), vbCrLf, fromEmail, sentDate, toEmail, subject, vbCrLf, Body)
'        End If
'    End Function

'    Public Sub SetReplyMessage(val As String)
'        If Body.StartsWith("<html") OrElse Body.StartsWith("<!DOCTYPE") OrElse Body.StartsWith("<div") Then
'            Dim doc = New HtmlDocument
'            doc.LoadHtml(Body)
'            Dim obj = doc.DocumentNode.SelectSingleNode("//body")
'            obj.InnerHtml = "{0}<br/><br/>{1}<br/><hr/>".FormatWith(val, StrConv(UserName.Replace(".", " "), VbStrConv.ProperCase)) & obj.InnerHtml
'            Body = doc.DocumentNode.OuterHtml
'        End If
'    End Sub

'    Sub Dispose() Implements IDisposable.Dispose
'        Marshal.ReleaseComObject(_OutlookApplication)
'        Marshal.ReleaseComObject(oNS)
'        Marshal.ReleaseComObject(_OutlookMailItem)
'    End Sub


'End Class
