﻿Imports System.ComponentModel

Public Class ucCompanyEmails

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Conum As Decimal

    Private Sub ucCompanyEmails_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        UcEmailsSearch1.LoadData(Conum, Nothing)
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        UcEmailsSearch1.LoadData(Conum, Nothing)
    End Sub

    Private Sub btnSearchByEmail_Click(sender As Object, e As EventArgs) Handles btnSearchByEmail.Click
        Dim frm = MainForm.GetForms(Of frmSearchEmployeeOrEmail).FirstOrDefault
        If frm Is Nothing Then
            frm = New frmSearchEmployeeOrEmail
            frm.rgFindBy.SelectedIndex = 4
            MainForm.ShowForm(frm)
        Else
            frm.rgFindBy.SelectedIndex = 4
            MainForm.ActivateForm(frm)
        End If
    End Sub
End Class
