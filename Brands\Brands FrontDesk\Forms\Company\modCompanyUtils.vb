﻿Imports DevExpress.XtraEditors

Module modCompanyUtils
    Public Async Function NoMorePayroll(coNum As Decimal) As Task
        Try
            Logger.Information("Entering NoMorePayroll for Co#: {CoNum}", coNum)
            Dim db = New dbEPDataDataContext(GetConnectionString)

            Dim CalendarData = (From A In db.CALENDARs Order By A.check_date, A.period_id, A.cal_id
                                Where A.conum = coNum _
                                AndAlso A.check_date >= Today.AddMonths(-3) _
                                AndAlso A.check_date <= Today.AddMonths(3)).ToList

            Dim _SelectedSets = New List(Of CALENDAR)
            Dim pSets = (From A In CalendarData Group By A.period_id Into Group Select period_id).ToList

            For Each pSet In pSets
                Dim Cal = (From A In CalendarData Where A.check_date >= Today AndAlso A.period_id = pSet AndAlso A.completed <> "YES").FirstOrDefault
                If Cal IsNot Nothing Then
                    _SelectedSets.Add(Cal)
                End If
            Next

            If _SelectedSets.Count > 1 Then
                If XtraMessageBox.Show("There's more than one calander availible for this company." & vbCrLf & "Would you like to delete all calendar's ?", "Delete all calendar's", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.No Then
                    Dim frmCals As New frmStartNewPayrollMultiSet(_SelectedSets, frmStartNewPayrollMultiSet.FormType.NoMorePayroll)
                    Dim results = frmCals.ShowDialog
                    If results = System.Windows.Forms.DialogResult.Cancel Then
                        Exit Function
                    Else
                        _SelectedSets = (From A In frmCals.CalendarData Where A.IsSelectedForPowergrid = True Order By A.period_id).ToList
                    End If

                    For Each c In _SelectedSets
                        Await UpdateSqlAsync("EXEC [custom].[prc_InactivateCalendar] {0},{1}".FormatWith(coNum, c.period_id), Nothing)
                    Next
                    Exit Function
                End If
            End If

            Dim frm = New frmCalendarNoMorePayroll(coNum)
            frm.ShowDialog()
        Catch ex As Exception
            Logger.Error(ex, "Error in NoMorePayroll for Co#: {CoNum}", coNum)
            Throw
        End Try
    End Function
End Module
