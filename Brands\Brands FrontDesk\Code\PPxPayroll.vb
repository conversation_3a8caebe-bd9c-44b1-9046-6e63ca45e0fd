﻿'Imports Execupay.DataModel
'Imports Execupay.Core.BusinessObject
'Imports Execupay.Foundation.Payroll.Batch
'Imports Execupay

Public Enum PPxPayrollSteps As Integer
    ''' <summary>
    ''' No Payroll Status
    ''' </summary>
    None = 0
    ' 00000
    ''' <summary>
    ''' New Payroll Can be Started
    ''' </summary>
    Start = 1
    ' 00001
    ''' <summary>
    ''' Current Payroll in Edit State
    ''' </summary>
    Edit = 2
    ' 00010
    ''' <summary>
    ''' Powergrid was Processed and Checks were Calculated, Totals might be calculated/refreshed again
    ''' </summary>
    Complete = 4
    ' 00100
    ''' <summary>
    ''' Payroll Submitted for review or PrintRun
    ''' </summary>
    Submit = 16
    ' 01000
    ''' <summary>
    ''' Payroll was Finished/Done
    ''' </summary>
    Finished = 32
    ' 10000
End Enum

'' Make this serializable if you are planning to store object reference in Session/Cache
'' [Serializable()]
'Public Class PPxPayroll
'    Implements IDisposable, PPxPayrollInterface
'#Region "Private Variable Declaration"
'    ''' <summary>
'    ''' Catalog ID for PPx PowerGrid, *** DONT CHANGE THESE SETTINGS
'    ''' </summary>
'    Private _isDisposed As Boolean = False
'    Private _ppxPayrollWebRtnKey As Integer = -1
'    Private _ppxPowerGridCatalog As Integer = 16
'    Private _ppxApplicationName As String = "PPx"
'    Private _CurrentUserName As String
'    Private _PayrollNotes As String
'    Private _PPxPayrollStatus As String
'    'Private _ppxPayrollEntryType As String = "Xpress"
'    'Private _ppxPowerGridName As String = "PPxPowerGrid"
'#End Region

'#Region "Private Properties"
'    'Private Property AnyPowerGridPendingToProcess As Boolean
'    'Private Property CurrentUserName As String
'    'Private Property EmployeeID As Decimal
'    'Private Property IsPowerGridProcessing As Boolean
'    'Private Property LastModified As DateTime
'    'Private Property PayrollNotes As String
'    'Private Property PayrollTotal As Foundation.Payroll.Engine.PayrollTotalInfo
'    'Private Property PPxPayrollStatus As String
'    'Private Property PPxPowerGridSchemaID As Guid
'#End Region

'#Region "Public Properties"
'    Public Property BatchMessages As List(Of Execupay.DataModel.pr_batch_msg)
'    Public Property CalendarID As Integer Implements PPxPayrollInterface.CalendarID
'    Public Property CompanyID As Decimal Implements PPxPayrollInterface.CompanyID
'    Public Property PayrollCalendar As PPxPayrollCalendar Implements PPxPayrollInterface.PayrollCalendar
'    Public Property PayrollException As Exception Implements PPxPayrollInterface.PayrollException
'    Public Property PayrollID As Decimal Implements PPxPayrollInterface.PayrollID
'    Public Property PayrollStatus As String Implements PPxPayrollInterface.PayrollStatus
'    Public Property PowerGridBatchID As Guid Implements PPxPayrollInterface.PowerGridBatchID
'    Public Property ppxPayrollEntryType As String = "BrandsXpress" Implements PPxPayrollInterface.ppxPayrollEntryType
'    Public Property PPxPayrollStep As PPxPayrollSteps Implements PPxPayrollInterface.PPxPayrollStep
'    Public Property ProviderID As Decimal Implements PPxPayrollInterface.ProviderID

'#End Region

'#Region "Payroll & PowerGrid API Reference"
'    Public _payrollAPI As WCF.Payroll.WCFPayrollInfo
'    Public _powerGridAPI As WCF.Payroll.PowerGrid.WCFPowerGrid

'    ' Returns reference to Payroll WCF API
'    Public ReadOnly Property PayrollAPI() As WCF.Payroll.WCFPayrollInfo
'        Get
'            If _payrollAPI Is Nothing Then
'                _payrollAPI = New WCF.Payroll.WCFPayrollInfo()
'                ' Start a Payroll Session
'                If Not _payrollAPI.SessionStart(_ppxApplicationName, CDbl(Me.ProviderID), True) Then
'                    WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "PPxPayroll.PayrollAPI", _CurrentUserName)
'                    Throw New Exception("Cannot start PayrollAPI")
'                End If
'                _payrollAPI.ConnectionProfile.Autocommit = False
'            End If
'            Return _payrollAPI
'        End Get
'    End Property

'    ' Returns reference to PowerGrid WCF API
'    Public ReadOnly Property PowerGridAPI() As WCF.Payroll.PowerGrid.WCFPowerGrid
'        Get
'            If _powerGridAPI Is Nothing Then
'                _powerGridAPI = New WCF.Payroll.PowerGrid.WCFPowerGrid()
'                ' Start a PowerGrid Session
'                If Not _powerGridAPI.SessionStart(_ppxApplicationName, CDbl(Me.ProviderID), True) Then
'                    WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "PPxPayroll.PowerGridAPI", _CurrentUserName)
'                    Throw New Exception("Cannot start PowerGridAPI")
'                End If
'                _powerGridAPI.ConnectionProfile.Autocommit = False
'            End If
'            Return _powerGridAPI
'        End Get
'    End Property

'#End Region

'#Region "Object Instance"

'    Private Shared _ppxPayroll As PPxPayroll

'    ''' <summary>
'    ''' Get/Set Instance in Session Object
'    ''' </summary>
'    Public Shared Property Instance() As PPxPayroll
'        Get
'            Return _ppxPayroll
'        End Get
'        Set(ByVal value As PPxPayroll)
'            _ppxPayroll = value
'        End Set
'    End Property

'    'Public Shared Sub ClearSession()
'    '    If Instance IsNot Nothing Then
'    '        ' Clear other settings and object references
'    '        ' ...

'    '        ' dispose the object
'    '        Instance.Dispose()
'    '    End If
'    'End Sub
'#End Region

'#Region "Public Constructors"
'    ''' <summary>
'    ''' 
'    ''' </summary>
'    ''' <param name="providerID"></param>
'    ''' <param name="companyID"></param>
'    ''' <param name="employeeID"></param>
'    ''' <param name="username"></param>
'    Public Sub New(ByVal providerID As Decimal, ByVal companyID As Decimal, ByVal employeeID As Decimal, ByVal username As String)
'        Me.ProviderID = providerID
'        Me.CompanyID = companyID
'        'Me.EmployeeID = employeeID
'        Me._CurrentUserName = username

'        ' Initialize Defaults
'        Me.PayrollStatus = String.Empty
'        Me._PPxPayrollStatus = String.Empty
'        Me.PPxPayrollStep = PPxPayrollSteps.None
'    End Sub
'#End Region

'#Region "Destructor and Dispose Methods - (Memory Cleanup)"
'    ''' <summary>
'    ''' Class Destructor
'    ''' </summary>
'    Protected Overrides Sub Finalize()
'        Try
'            ' call Dispose with false.  Since we're in the
'            ' destructor call, the managed resources will be
'            ' disposed of anyways.
'            Me.Dispose(False)
'        Finally
'            MyBase.Finalize()
'        End Try
'    End Sub

'    Public Sub Dispose() Implements IDisposable.Dispose

'        ' dispose of the managed and unmanaged resources
'        Me.Dispose(True)

'        ' tell the GC that the Finalize process no longer needs to be run for this object.
'        GC.SuppressFinalize(Me)
'    End Sub

'    Protected Overridable Sub Dispose(ByVal disposeManagedResources As Boolean)
'        ' process only if mananged and unmanaged resources have not been disposed of.
'        If Not Me._isDisposed Then
'            'Trace.WriteLine("ClassBeingTested: Resources not disposed");
'            If disposeManagedResources Then
'                'Trace.WriteLine("PPxPayroll: Disposing managed resources");
'                ' dispose managed resources

'                BatchMessages = Nothing
'                PayrollCalendar = Nothing

'                PayrollException = Nothing
'            End If
'            ' dispose unmanaged resources
'            If _powerGridAPI IsNot Nothing Then
'                _powerGridAPI.SessionEnd()
'                _powerGridAPI = Nothing
'            End If
'            If _payrollAPI IsNot Nothing Then
'                _payrollAPI.SessionEnd()
'                _payrollAPI = Nothing
'            End If

'            'Trace.WriteLine("PPxPayroll: Disposing unmanaged resouces");
'            _isDisposed = True
'            'Trace.WriteLine("PPxPayroll: Resources already disposed");
'        Else
'        End If
'    End Sub


'#End Region

'#Region "Payroll Methods"
'#Region "Not Used"
'    '''' <summary>
'    '''' Create New Payroll
'    '''' </summary>
'    'Public Function CreateNewPayroll() As Boolean
'    '    Dim currentStatus As String = String.Empty
'    '    Dim canCreate As Boolean = False
'    '    Dim canEdit As Boolean = False
'    '    Dim errorID As Double = 0
'    '    Dim errorText As String = Nothing
'    '    Dim payrollPassword As String = Nothing
'    '    ' in case if payroll password is setup at company level
'    '    Try
'    '        Dim payrollNumber As Double = PayrollAPI.CheckPayrollStatus(currentStatus, canCreate, canEdit, payrollPassword, errorID, errorText,
'    '         CDbl(Me.ProviderID), CDbl(Me.CompanyID), _ppxPayrollWebRtnKey)

'    '        If canCreate Then
'    '            'check last payroll for open liabilities
'    '            If PayrollID > 0 Then
'    '                'Using db As New dbEPDataDataContext
'    '                '    Dim PayrollsNotClosed = (From A In db.PAYROLLs
'    '                '                             Where A.CONUM = Me.CompanyID And A.CHECK_DATE <= Me.And(A.FEDDEP Is Nothing Or A.STDEP Is Nothing Or A.LOCDEP Is Nothing)).FirstOrDefault
'    '                '    If PayrollsNotClosed IsNot Nothing Then
'    '                '        Dim Msg = 
'    '                '    End If
'    '                'End Using
'    '            End If

'    '            ' status of last payroll
'    '            Me.PayrollStatus = currentStatus
'    '            Me.PayrollID = CDec(payrollNumber)

'    '            Me.PPxPayrollStep = PPxPayrollSteps.Start
'    '            ' PPx Payroll Status == Start
'    '            Me._PPxPayrollStatus = PPxPayrollStatusType.StartingPayroll
'    '            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.StartingPayroll & " :: PPxPayroll.CreateNewPayrollFromCalendar()", _CurrentUserName)

'    '            errorText = Nothing
'    '            payrollNumber = PayrollAPI.CreatePayroll(CDbl(Me.ProviderID), CDbl(Me.CompanyID), Me._CurrentUserName, "NO", "YES", "YES",
'    '             "YES", "YES", "YES", "YES", _ppxPayrollWebRtnKey, errorText,
'    '             payrollPassword, ppxPayrollEntryType, False)

'    '            If CDec(payrollNumber) > Me.PayrollID Then
'    '                ' only if new payroll number is greater than the previous
'    '                Me.PayrollID = CDec(payrollNumber)
'    '                ' assign the new payroll number
'    '                Me.PayrollStatus = currentStatus
'    '                ' actual status on Payroll Record in database (shld be [Entering Checks])
'    '                Me.PPxPayrollStep = PPxPayrollSteps.Edit

'    '                Me._PPxPayrollStatus = PPxPayrollStatusType.PayrollCreated
'    '                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.PayrollCreated & " :: PPxPayroll.CreateNewPayrollFromCalendar(Payroll#" & payrollNumber.ToString & ")", _CurrentUserName)

'    '                'CreateNewPowerGridForUser()
'    '            Else
'    '                Me.PayrollID = CDec(payrollNumber)
'    '                ' assign the new payroll number
'    '                Me.PayrollStatus = currentStatus
'    '                ' actual status on Payroll Record in database (shld be [Entering Checks])
'    '                ' in case error Occurred or payroll was not create for some reason (payroll API returns same PayrollID)
'    '                Throw New PayrollException("Error Occurred while Creating Payroll(# " & payrollNumber.ToString & "). " & errorText)

'    '            End If
'    '        ElseIf canEdit AndAlso payrollNumber > 0 Then
'    '            ' Logically in PPx this else condition should not be triggered. may be someone else created Payroll at the same time current user did.
'    '            Throw New PayrollException("Unhandled Exception Occurred while creating Poyroll(# " & payrollNumber.ToString & ", User:" & Me._CurrentUserName & "). ErrorText: " & errorText & "{" & errorID & "}")
'    '        End If
'    '        Return True
'    '    Catch ex As Exception
'    '        Me.PayrollException = ex
'    '        'throw ex; //new Exception("Error Occurred while Creating Payroll!", ex);
'    '        WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "Error Occurred while Creating Payroll for Company # " & Me.CompanyID.ToString, ex)
'    '        Return False
'    '    End Try
'    'End Function
'    '''' <summary>
'    '''' Method to Create a New PowerGrid
'    '''' </summary>
'    '''' <returns></returns>
'    'Public Function CreateNewPowerGridForUser() As Boolean
'    '    Dim schemaGuid As Guid = GetPPxTemplateID()

'    '    If schemaGuid <> Guid.Empty Then
'    '        Me._PPxPayrollStatus = PPxPayrollStatusType.CreatingPowerGrid
'    '        WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.CreatingPowerGrid & " :: PPxPayroll.CreateNewPayrollFromCalendar(Payroll#" & Me.PayrollID & ", Schema#" & schemaGuid.ToString & ")", _CurrentUserName)

'    '        Dim tempGuid As Guid = PowerGridAPI.CreateNewPowerGridWithSchema(_ppxPowerGridName, _ppxPowerGridCatalog, Me.CompanyID, 1, Me._CurrentUserName, Nothing,
'    '         "" & schemaGuid.ToString, False)
'    '        Dim resultSet As FunctionResult = PowerGridAPI.SaveChanges()

'    '        If resultSet.Status = FunctionResultStatus.Completed Then
'    '            Dim batch As DataModel.pr_batch_list = PowerGridAPI.GetPowerGrid()

'    '            If batch IsNot Nothing AndAlso batch.id <> Guid.Empty AndAlso PowerGridAPI.OpenPowerGrid(_ppxPowerGridCatalog, Me.CompanyID, batch.id, 1, Me._CurrentUserName, Nothing) Then
'    '                Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridCreated
'    '                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.PowerGridCreated & " :: PPxPayroll.CreateNewPayrollFromCalendar(PayrollID#" & Me.PayrollID & ", Batch#" & batch.id.ToString & ")", _CurrentUserName)

'    '                Me.PowerGridBatchID = batch.id
'    '                ' current batch
'    '                Return True
'    '            Else
'    '                Throw New PayrollException("Unable to open PowerGrid for Payroll#" & Me.PayrollID & "PowerGridID#" & tempGuid.ToString)
'    '            End If
'    '        Else
'    '            Throw New PayrollException("Unable to Save PowerGrid for Payroll#" & Me.PayrollID & "PowerGridID#" & tempGuid.ToString, resultSet.InnerException)
'    '        End If
'    '    Else
'    '        WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "No Powergrid Template Found for Company # " & Me.CompanyID.ToString)
'    '        Throw New PayrollException("Powergrid Template/Schema not found! (User# " & Me._CurrentUserName & ")")
'    '    End If

'    'End Function
'    '''' <summary>
'    '''' Get Batch Messages from Payroll+Powergrid API
'    '''' </summary>
'    'Public Sub LoadBatchMessages()
'    '    Try
'    '        Dim resultCollection As Global.Execupay.Core.BusinessObject.ResultsCollection = PowerGridAPI.GetProcessMessages(_ppxPowerGridCatalog, CompanyID, PayrollID)

'    '        Dim resultSet As FunctionResult = resultCollection.FirstOrDefault()
'    '        If resultSet.HasValue Then
'    '            BatchMessages = DirectCast(resultSet.Value, List(Of Execupay.DataModel.pr_batch_msg))
'    '        End If

'    '        If BatchMessages Is Nothing Then
'    '            BatchMessages = New List(Of Execupay.DataModel.pr_batch_msg)()
'    '        End If
'    '    Catch ex As Exception
'    '        WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], "Error Occurred :: PPxPayroll.LoadBatchMessages()", ex)
'    '    End Try
'    'End Sub
'    '''' <summary>
'    '''' Unprocess Powergrid, delete checks created by the powergrid and reset payroll
'    '''' </summary>
'    '''' <returns></returns>
'    'Public Function UndoPayroll(ByVal deletePowerGrid As Boolean) As Boolean
'    '    Dim _powergridDeleted As Boolean = False
'    '    WebLogger.PayrollProcessStep = 1
'    '    Try
'    '        Dim resultCollection As Global.Execupay.Core.BusinessObject.ResultsCollection = PowerGridAPI.UnProcessPowerGrid(_ppxPowerGridCatalog, CompanyID, PayrollID, PowerGridBatchID, _CurrentUserName, deletePowerGrid)

'    '        Dim resultSet As FunctionResult = resultCollection.FirstOrDefault()
'    '        If resultSet.HasValue AndAlso resultSet.Status = FunctionResultStatus.Completed Then
'    '            _powergridDeleted = True
'    '            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PPxPayroll.UndoPayroll() : Done (PayrollID#" & Me.PayrollID & ", Emp#" & Me.EmployeeID & ")")
'    '        Else
'    '            _powergridDeleted = False
'    '            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PPxPayroll.UndoPayroll() : Error (PayrollID#" & Me.PayrollID & ", Emp#" & Me.EmployeeID & ")")

'    '        End If
'    '    Catch ex As Exception
'    '        WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], "PPxPayroll.UndoPayroll() : Error (PayrollID#" & Me.PayrollID & ", Emp#" & Me.EmployeeID & ")", ex)
'    '    End Try

'    '    Return _powergridDeleted
'    'End Function
'    '''' <summary>
'    '''' Run one step payroll
'    '''' </summary>
'    'Public Sub RunOneStepPayroll()
'    '    Try
'    '        Me.GetNextCalendarID()
'    '        ' get next available calender id
'    '        Me.CreateNewPayrollFromCalendar()
'    '        ' create payroll and powergrid
'    '        If Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridCreated Then
'    '            ' if powergrid was successfully created
'    '            ' process powergird
'    '            Me.ProcessPowerGrid()
'    '        Else
'    '            If Me.PayrollException IsNot Nothing Then
'    '                Throw Me.PayrollException
'    '            Else
'    '                Throw New PayrollException("Error Occurred during running One-Step Payroll(#" + Me.PayrollID + ")")
'    '            End If
'    '        End If
'    '    Catch ex As Exception
'    '        Me.PayrollException = ex
'    '        'throw ex;
'    '        WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, ex.Message, ex)
'    '    End Try
'    'End Sub
'    '''' <summary>
'    '''' Returns a list of All Calendar Items
'    '''' </summary>
'    '''' <returns></returns>
'    'Private Function GetAllCalendarItems() As List(Of DataModel.CALENDAR)
'    '    ' make sure to show only calendar items for check dat greater/equal to today
'    '    Return PayrollAPI.GetCalendarItems(CDbl(Me.ProviderID), CDbl(Me.CompanyID), 0, _ppxPayrollWebRtnKey).Where(Function(c) c.check_date >= DateTime.Today).OrderByDescending(Function(c) c.check_date).ToList()

'    'End Function

'    '''' <summary>
'    '''' Get the next available Calendar Item
'    '''' </summary>
'    '''' <returns></returns>
'    'Private Function GetNextCalendarID() As PPxPayrollCalendar
'    '    ' Returns Payroll Sets (MAX 3 based on company setup)
'    '    Dim payrollCalendar As Global.Execupay.DataModel.CALENDAR() = PayrollAPI.GetNextCalendar(CDbl(Me.ProviderID), CDbl(Me.CompanyID), _ppxPayrollWebRtnKey)

'    '    Dim ppxPayrollCalendar As New PPxPayrollCalendar()
'    '    CalendarID = 0
'    '    ppxPayrollCalendar.CalendarID = 0

'    '    If payrollCalendar IsNot Nothing AndAlso payrollCalendar.Count() > 0 Then
'    '        Dim currentCalendar As DataModel.CALENDAR = payrollCalendar.OrderBy(Function(c) c.period_id = 1).FirstOrDefault()

'    '        If currentCalendar IsNot Nothing Then
'    '            ppxPayrollCalendar.CalendarID = currentCalendar.cal_id
'    '            ppxPayrollCalendar.Title = currentCalendar.title

'    '            ppxPayrollCalendar.StartDate = currentCalendar.start_date
'    '            ppxPayrollCalendar.EndDate = currentCalendar.end_date
'    '            ppxPayrollCalendar.ProcessDate = currentCalendar.process_date
'    '            ppxPayrollCalendar.CheckDate = currentCalendar.check_date
'    '            ' pay date
'    '            Me.CalendarID = ppxPayrollCalendar.CalendarID

'    '            Me.PayrollCalendar = ppxPayrollCalendar
'    '        End If
'    '    End If

'    '    Return ppxPayrollCalendar
'    'End Function

'    '''' <summary>
'    '''' Return the PPx Schema GuidID
'    '''' </summary>
'    '''' <returns></returns>
'    'Private Function GetPPxTemplateID() As Guid
'    '    Dim schemaGuid As Guid = Guid.Empty

'    '    ' return employee specific schema if exist, or will return all schema collection for company
'    '    Dim allSchema = PowerGridAPI.GetSchemaList(_ppxPowerGridCatalog, Me.CompanyID, Me.CompanyID, Me.EmployeeID)

'    '    ' returns all schema, will skip employee specific schema selection
'    '    'DataModel.Payroll.Batch.SchemaListColection allSchema = PowerGridAPI.GetSchemaList(_ppxPowerGridCatalog, this.CompanyID, null, null);

'    '    'if (allSchema != null && allSchema.Count() > 0 && allSchema.Values.Where(c => c.show_to_web == "YES").Count() > 0) // make sure the schema exists and is configured to show for web
'    '    If allSchema IsNot Nothing AndAlso allSchema.Count() > 0 AndAlso allSchema.Any(Function(c) c.Value.show_to_web = "YES") Then
'    '        ' make sure the schema exists and is configured to show for web
'    '        'schemaGuid = allSchema.Values.Where(c => c.show_to_web == "YES").FirstOrDefault().schema_id;
'    '        schemaGuid = allSchema.Where(Function(c) c.Value.show_to_web = "YES").FirstOrDefault().Key
'    '        'Me.PPxPowerGridSchemaID = schemaGuid
'    '    End If

'    '    Return schemaGuid
'    'End Function
'#End Region
'    ''' <summary>
'    ''' Calcualte checks
'    ''' </summary>
'    Public Function CalculateTotals() As Boolean Implements PPxPayrollInterface.CalculateTotals
'        Dim RetryCount As Integer = 1
'        Try
'            WebLogger.PayrollProcessStep = 3
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.CalculatingTotals + " :: PPxPayroll.CalculatePayrollTotals(" + PowerGridBatchID.ToString + ")", _CurrentUserName)

'            'lock (this.PPxPayrollStatus) 
'            If True Then
'                Me._PPxPayrollStatus = PPxPayrollStatusType.CalculatingTotals
'            End If
'            ' Powergrid Processing was Complete Successfully. display return results in a popup and provide a linq to view Totals in next page
'            ' show how many checks are created and other information in that popup.
'A:
'            Dim payrollTotal As New Foundation.Payroll.Engine.PayrollTotalInfo()
'            If PayrollAPI.CalculatePayrollTotals(CDbl(ProviderID), CDbl(CompanyID), CDbl(PayrollID), _ppxPayrollWebRtnKey, False, payrollTotal) Then
'                'Me.PayrollTotal = payrollTotal
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.TotalsCalculated & " :: PPxPayroll.CalculatePayrollTotals(" + PayrollID.ToString + ")", _CurrentUserName)
'                Dim pInfo = PayrollAPI.GetSinglePayrollInfo(Me.ProviderID, Me.CompanyID, Me.PayrollID, -1)
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, String.Format("Checks Count: {0}, Total Gross: {1}", pInfo.TotalChecks.GetValueOrDefault, pInfo.TotalGross.GetValueOrDefault.ToString("c")))
'                'lock (this.PPxPayrollStatus) 
'                If True Then
'                    Me._PPxPayrollStatus = PPxPayrollStatusType.TotalsCalculated
'                End If
'            Else
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], "Error Processing :: PayrollAPI.CalculatePayrollTotals(" & PayrollID & ")", _CurrentUserName)
'                Throw New Exception("Error while calculating payroll totals")
'            End If
'            Return True
'        Catch ex As Exception
'            Logger.Error(ex, "Error in CalculateTotals API {Conum} {Prnum}", CompanyID, PayrollID)
'            If ex.Message.ToLower.Contains("object reference not set to an instance of an object") Then
'                If RetryCount < 3 Then
'                    RetryCount += 1
'                    GoTo A
'                End If
'            End If
'            'GC.Collect(); // force cleanup memory
'            GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced)
'            ' this will cleanup any Open connections/cursors by PPJ.Runtime
'            Me.PayrollException = ex
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], ex.Message, ex)
'            'Throw ex
'            Return False
'        End Try
'    End Function

'    ''' <summary>
'    ''' Checks Current Payroll Status
'    ''' </summary>
'    Public Sub CheckPayrollStatus() Implements PPxPayrollInterface.CheckPayrollStatus
'        Dim currentStatus As String = Nothing
'        Dim canCreate As Boolean
'        Dim canEdit As Boolean
'        Dim errorID As Double
'        Dim errorText As String = Nothing
'        Dim payrollPassword As String = Nothing

'        Dim payrollNumber As Double = PayrollAPI.CheckPayrollStatus(currentStatus, canCreate, canEdit, payrollPassword, errorID, errorText,
'         CDbl(Me.ProviderID), CDbl(Me.CompanyID), _ppxPayrollWebRtnKey)

'        If canCreate Then
'            ' status of last payroll
'            Me.PayrollStatus = currentStatus
'            Me.PayrollID = CDec(payrollNumber)

'            Me.PPxPayrollStep = PPxPayrollSteps.Start
'            ' PPx Payroll Status == Start
'            Me._PPxPayrollStatus = PPxPayrollStatusType.CanStartPayroll
'        ElseIf canEdit AndAlso payrollNumber > 0 Then
'            ' Payroll is created and is in Edit Mode
'            Me.PayrollID = CDec(payrollNumber)

'            Me.PayrollStatus = currentStatus
'            ' actual status on Payroll Record in database (Entering Checks)
'            Me.PPxPayrollStep = PPxPayrollSteps.Edit

'            Me.PowerGridBatchID = Guid.Empty
'            ' reset Powergrid Batch ID
'            ' check PowerGrid Status
'            Dim powerGridInfo As PowerGridPayrollInfo = PowerGridAPI.GetPayrollInfo(Me.CompanyID, Me.PayrollID, Me._CurrentUserName, False)
'            If powerGridInfo IsNot Nothing Then
'                If Not PowerGridAPI.IsMultiGridEnable(_ppxPowerGridCatalog, Me.CompanyID) Then
'                    ' if single common schema company
'                    If powerGridInfo.ProcessedAllBatches.Count() >= 1 Then
'                        ' Powergrid was Processed and Checks were Created
'                        ' show Total Screen, may have to recalculate Totals 
'                        ' (Processed state does not guarranty if the totals are upto date
'                        ' possibly if someone edited or added new batch or manual checks)
'                        Me.PPxPayrollStep = PPxPayrollSteps.Complete

'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridProcessed
'                        'Me.AnyPowerGridPendingToProcess = False

'                        Me.PowerGridBatchID = Guid.Parse(powerGridInfo.ProcessedAllBatches.FirstOrDefault().Key.ToString & "")
'                    ElseIf powerGridInfo.CompletedAllBatches.Count() > 0 Then
'                        ' powergrid is edited by someone else and put into Completed Status and not processed yet 
'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridCompleted
'                        'Me.AnyPowerGridPendingToProcess = True

'                        Me.PowerGridBatchID = Guid.Parse(powerGridInfo.CompletedAllBatches.FirstOrDefault().Key.ToString & "")
'                    ElseIf powerGridInfo.EditedAllBatches.Count() > 0 Then
'                        ' if powergrid is in Edit status (In Progress), also check the run date for the batch
'                        ' if powergrid was processed and not finished, possibly run_date will not be null
'                        ' if run_date is null set PowerGrid to Edit Status and show Edit Grid
'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridEditing
'                        'Me.AnyPowerGridPendingToProcess = True

'                        Me.PowerGridBatchID = Guid.Parse(powerGridInfo.EditedAllBatches.FirstOrDefault().Key.ToString & "")
'                        Dim batchInfo As String = Nothing
'                        'IsPowerGridProcessing = False
'                        If powerGridInfo.EditedAllBatches.TryGetValue(Me.PowerGridBatchID, batchInfo) Then
'                            Dim batchArgs As String() = batchInfo.Split(New String() {"||"}, StringSplitOptions.RemoveEmptyEntries)
'                            Dim batchrunDate As DateTime = DateTime.MinValue
'                            If batchArgs.Length >= 3 AndAlso DateTime.TryParse(batchArgs(2), batchrunDate) Then
'                                'IsPowerGridProcessing = True
'                                ' powergrid is currently processing
'                                Me._PPxPayrollStatus = PPxPayrollStatusType.ProcessingPowerGrid
'                            End If

'                        End If
'                    Else
'                        ' possibily no powergrid was created for current user, reset status back to PayrollCreated
'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PayrollCreated
'                    End If
'                Else
'                    '' if company is setup for multiple powergrid for each users. 
'                    '' (users needs specific permissions or common template will use to 
'                    '' create powergrid for each user loggin in to EditPayroll)
'                    If powerGridInfo.EditedUserBatches.Count() > 0 Then
'                        ' if powergrid is in Edit status (In Progress), also check the run date for the batch
'                        ' if powergrid was processed and not finished, possibly run_date will not be null
'                        ' if run_date is null set PowerGrid to Edit Status and show Edit Grid
'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridEditing
'                        'Me.AnyPowerGridPendingToProcess = True

'                        Me.PowerGridBatchID = Guid.Parse(powerGridInfo.EditedUserBatches.FirstOrDefault().Key.ToString & "")
'                        Dim batchInfo As String = Nothing
'                        'IsPowerGridProcessing = False
'                        If powerGridInfo.EditedUserBatches.TryGetValue(Me.PowerGridBatchID, batchInfo) Then
'                            Dim batchArgs As String() = batchInfo.Split(New String() {"||"}, StringSplitOptions.RemoveEmptyEntries)
'                            Dim batchrunDate As DateTime = DateTime.MinValue
'                            If batchArgs.Length >= 3 AndAlso DateTime.TryParse(batchArgs(2), batchrunDate) Then
'                                'IsPowerGridProcessing = True
'                                ' powergrid is currently processing
'                                Me._PPxPayrollStatus = PPxPayrollStatusType.ProcessingPowerGrid
'                            End If

'                        End If
'                    ElseIf powerGridInfo.CompletedUserBatches.Count() > 0 Then
'                        ' powergrid is edited by someone else and put into Completed Status and not processed yet 
'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridCompleted
'                        'Me.AnyPowerGridPendingToProcess = True

'                        Me.PowerGridBatchID = Guid.Parse(powerGridInfo.CompletedUserBatches.FirstOrDefault().Key.ToString & "")
'                    ElseIf powerGridInfo.ProcessedUserBatches.Count() >= 1 Then
'                        ' Powergrid was Processed and Checks were Created
'                        ' show Total Screen, may have to recalculate Totals 
'                        ' (Processed state does not guarranty if the totals are upto date
'                        ' possibly if someone edited or added new batch or manual checks)
'                        Me.PPxPayrollStep = PPxPayrollSteps.Complete

'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PowerGridProcessed

'                        Me.PowerGridBatchID = Guid.Parse(powerGridInfo.ProcessedUserBatches.FirstOrDefault().Key.ToString & "")

'                        ' check if any other powergrid pending to be processed in edit/completed mode.
'                        If powerGridInfo.EditedAllBatches.Count() > 0 OrElse powerGridInfo.CompletedAllBatches.Count() > 0 Then
'                            'Me.AnyPowerGridPendingToProcess = True
'                        Else
'                            'Me.AnyPowerGridPendingToProcess = False
'                        End If
'                    Else
'                        ' possibily no powergrid was created for current user, reset status back to PayrollCreated
'                        Me._PPxPayrollStatus = PPxPayrollStatusType.PayrollCreated
'                    End If
'                End If
'            Else
'                ' no powergrid exists for current user, reset status back to PayrollCreated
'                Me._PPxPayrollStatus = PPxPayrollStatusType.PayrollCreated
'            End If
'        Else
'            'if (currentStatus != "Entering Checks")
'            Me.PPxPayrollStep = PPxPayrollSteps.None
'            Me._PPxPayrollStatus = PPxPayrollStatusType.NONE
'            Me.PayrollStatus = currentStatus
'            Me.PayrollID = CDec(payrollNumber)
'        End If
'    End Sub

'    Public Function CheckPayrollStatus(CoNum As Decimal) As PayrollStatusResult Implements PPxPayrollInterface.CheckPayrollStatus
'        CompanyID = CoNum
'        CheckPayrollStatus()
'    End Function

'    ''' <summary>
'    ''' Create New Payroll, CalendarID must be set before calling this method
'    ''' </summary>
'    Public Function CreateNewPayrollFromCalendar() As Boolean Implements PPxPayrollInterface.CreateNewPayrollFromCalendar
'        Dim currentStatus As String = String.Empty
'        Dim canCreate As Boolean = False
'        Dim canEdit As Boolean = False
'        Dim errorID As Double = 0
'        Dim errorText As String = Nothing
'        Dim payrollPassword As String = Nothing
'        ' in case if payroll password is setup at company level
'        Try
'            Dim payrollNumber As Double = PayrollAPI.CheckPayrollStatus(currentStatus, canCreate, canEdit, payrollPassword, errorID, errorText,
'             CDbl(Me.ProviderID), CDbl(Me.CompanyID), _ppxPayrollWebRtnKey)

'            If canCreate Then
'                'check last payroll for open liabilities
'                If PayrollID > 0 Then
'                    'Using db As New dbEPDataDataContext
'                    '    Dim PayrollsNotClosed = (From A In db.PAYROLLs
'                    '                             Where A.CONUM = Me.CompanyID And A.CHECK_DATE <= Me.And(A.FEDDEP Is Nothing Or A.STDEP Is Nothing Or A.LOCDEP Is Nothing)).FirstOrDefault
'                    '    If PayrollsNotClosed IsNot Nothing Then
'                    '        Dim Msg = 
'                    '    End If
'                    'End Using
'                End If

'                ' status of last payroll
'                Me.PayrollStatus = currentStatus
'                Me.PayrollID = CDec(payrollNumber)

'                Me.PPxPayrollStep = PPxPayrollSteps.Start
'                ' PPx Payroll Status == Start
'                Me._PPxPayrollStatus = PPxPayrollStatusType.StartingPayroll
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.StartingPayroll & " :: PPxPayroll.CreateNewPayrollFromCalendar()", _CurrentUserName)

'                errorText = Nothing
'                payrollNumber = PayrollAPI.CreatePayrollWithCalendar(CDbl(Me.ProviderID), CDbl(Me.CompanyID), Me._CurrentUserName, "YES", "YES", "YES",
'                 "YES", "YES", "YES", "YES", _ppxPayrollWebRtnKey, errorText,
'                 payrollPassword, ppxPayrollEntryType, Me.CalendarID, False)

'                If CDec(payrollNumber) > Me.PayrollID Then
'                    ' only if new payroll number is greater than the previous
'                    Me.PayrollID = CDec(payrollNumber)
'                    ' assign the new payroll number
'                    Me.PayrollStatus = currentStatus
'                    ' actual status on Payroll Record in database (shld be [Entering Checks])
'                    Me.PPxPayrollStep = PPxPayrollSteps.Edit

'                    Me._PPxPayrollStatus = PPxPayrollStatusType.PayrollCreated
'                    WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.PayrollCreated & " :: PPxPayroll.CreateNewPayrollFromCalendar(Payroll#" & payrollNumber.ToString & ")", _CurrentUserName)

'                    'CreateNewPowerGridForUser()
'                Else
'                    Me.PayrollID = CDec(payrollNumber)
'                    ' assign the new payroll number
'                    Me.PayrollStatus = currentStatus
'                    ' actual status on Payroll Record in database (shld be [Entering Checks])
'                    ' in case error Occurred or payroll was not create for some reason (payroll API returns same PayrollID)
'                    Throw New PayrollException("Error Occurred while Creating Payroll(# " & payrollNumber.ToString & "). " & errorText)

'                End If
'            ElseIf canEdit AndAlso payrollNumber > 0 Then
'                ' Logically in PPx this else condition should not be triggered. may be someone else created Payroll at the same time current user did.
'                Throw New PayrollException("Unhandled Exception Occurred while creating Poyroll(# " & payrollNumber.ToString & ", User:" & Me._CurrentUserName & "). ErrorText: " & errorText & "{" & errorID & "}")
'            End If
'            Return True
'        Catch ex As Exception
'            Logger.Error(ex, "Error Occurred in CreateNewPayrollFromCalendar while Creating Payroll for Co# {CoNum} ", Me.CompanyID.ToString)
'            Me.PayrollException = ex
'            'throw ex; //new Exception("Error Occurred while Creating Payroll!", ex);
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "Error Occurred while Creating Payroll for Company # " & Me.CompanyID.ToString, ex)
'            Return False
'        End Try
'    End Function

'    ''' <summary>
'    ''' Process Edited Powergrid
'    ''' </summary>
'    Public Function ProcessPowerGrid() As Boolean
'        Try
'            WebLogger.PayrollProcessStep = 1
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.ProcessingPowerGrid & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)

'            'lock (this.PPxPayrollStatus) 
'            If True Then
'                Me._PPxPayrollStatus = PPxPayrollStatusType.ProcessingPowerGrid
'            End If

'            ' Processing PowerGrid and Calculating Checks
'            Dim resultCollection As Global.Execupay.Core.BusinessObject.ResultsCollection = PowerGridAPI.ProcessPowerGrid(_ppxPowerGridCatalog, CompanyID, PayrollID, PowerGridBatchID, _CurrentUserName, True)

'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PowerGrid Processing Done :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)
'            Dim resultSet As FunctionResult = resultCollection.FirstOrDefault()
'            BatchMessages = DirectCast(resultCollection.LastOrDefault().Value, List(Of Execupay.DataModel.pr_batch_msg))

'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PowerGrid Processing Results " & resultSet.Status.ToString & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)

'            If resultSet.Action = FunctionResultAction.Calculation AndAlso resultSet.Status = FunctionResultStatus.Completed Then
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "Checks Completed " & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)
'                'lock (this.PPxPayrollStatus) 
'                If True Then
'                    Me._PPxPayrollStatus = PPxPayrollStatusType.ChecksCompleted
'                End If
'            Else
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "Error :: " & PPxPayrollStatusType.ProcessingPowerGrid & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)
'                ' Powergrid processing caused errors. display all errors in a grid
'                If resultSet IsNot Nothing AndAlso resultSet.InnerException IsNot Nothing Then
'                    Throw New PayrollException("Error occurred while processing PowerGrid(PayrollID# " & Me.PayrollID.ToString & ", BatchID# " & Me.PowerGridBatchID.ToString & "). Error: " & resultSet.InnerException.Message, resultSet.InnerException)
'                Else
'                    Throw New PayrollException("Error occurred while processing PowerGrid(PayrollID# " & Me.PayrollID & ", BatchID# " & Me.PowerGridBatchID.ToString & ")")
'                End If
'            End If
'            Return True
'        Catch ex As Exception
'            Me.PayrollException = ex
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, ex.Message, ex)

'            If ex.InnerException IsNot Nothing And TypeOf (ex.InnerException) Is DivideByZeroException Then
'                DisplayErrorMessage(ex.InnerException.Message, ex)
'                'you can add here code how to handle if it was a divide by zero exception 
'            End If

'            If ex.InnerException IsNot Nothing And TypeOf (ex.InnerException) Is SqlException Then
'                Dim sqlEx As SqlException = CType(ex, SqlException)

'                If sqlEx.ErrorCode = 1205 Then

'                    If MessageBox.Show("There was a dead lock error," + vbCrLf + " Error is:" + ex.Message + vbCrLf + "Before retrying report to Hershy, Would you like to retry ?", "Dead Lock Error", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.Yes Then
'                        ProcessPowerGrid()
'                    End If

'                End If

'                MessageBox.Show(ex.InnerException.Message)
'                'you can add here code how to handle if it was a divide by ziro exception 
'            End If

'            Return False
'        Finally
'            'LastModified = DateTime.Now
'        End Try
'    End Function

'    ''' <summary>
'    ''' Submit a Payroll
'    ''' </summary>
'    Public Function SubmitPayroll(ByVal submitType As Short) As Boolean Implements PPxPayrollInterface.SubmitPayroll
'        ' Submit Type
'        '  0 - Phone Call
'        '  1 - Instructions
'        '  2 - Submit Now (to Print Run)
'        If Me.PPxPayrollStep = PPxPayrollSteps.Complete Then
'            Return PayrollAPI.SubmitPayroll(CDbl(Me.ProviderID), CDbl(Me.CompanyID), CDbl(Me.PayrollID), _ppxPayrollWebRtnKey, _PayrollNotes, submitType,
'             Me._CurrentUserName)
'        Else
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "Error :: PayrollAPI.SubmitPayrollForReview(Pr: " + Me.PayrollID.ToString + ", Status: " + Me.PayrollStatus + ", PPxStatus:" + Me._PPxPayrollStatus + ")", _CurrentUserName)
'            Throw New PayrollException("Payroll cannot be submitted. Check Payroll status or contact a Payroll specialist")
'        End If
'    End Function
'#End Region

'#Region "Methods fallback"
'    Public Sub DeleteManualCheck(CoNum As Decimal, PrNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal) Implements PPxPayrollInterface.DeleteManualCheck
'        PayrollAPI.DeleteManualCheck(ProviderID, CoNum, PrNum, EmpNum, ChkCounter, -1)
'    End Sub

'    Public Function CalculateManualCheck(CoNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal, pay_freq As String, check_date As Date, errors As String) As Boolean Implements PPxPayrollInterface.CalculateManualCheck
'        Return PayrollAPI.CalculateManualCheck(ProviderID, CoNum, EmpNum, ChkCounter, pay_freq, check_date, -1, UserName, errors)
'    End Function

'    Public Function CalculateSingleCheck(CoNum As Decimal, PrNum As Decimal, EmpNum As Decimal?, ChkCntr As Decimal?) As Boolean Implements PPxPayrollInterface.CalculateSingleCheck
'        Return PayrollAPI.CalculateSingleCheck(ProviderID, CoNum, PrNum, EmpNum, ChkCntr, "", -1)
'    End Function

'    Public Function CalculateTotals(CoNum As Decimal, PrNum As Decimal) As Boolean Implements PPxPayrollInterface.CalculateTotals
'        Return CalculateTotals()
'    End Function

'    Public Function CreateNewPayrollFromCalendar(CoNum As Decimal) As Boolean Implements PPxPayrollInterface.CreateNewPayrollFromCalendar
'        Return CreateNewPayrollFromCalendar()
'    End Function

'    Public Function ProcessPowerGrid(CoNum As Decimal, PrNum As Decimal, PowerGridID As String) As Boolean Implements PPxPayrollInterface.ProcessPowerGrid
'        Return ProcessPowerGrid()
'    End Function

'    Public Function SubmitPayroll(CoNum As Decimal, PrNum As Decimal, submitType As Short, PayrollNotes As String) As Boolean Implements PPxPayrollInterface.SubmitPayroll
'        Return SubmitPayroll(submitType)
'    End Function

'    Public Function UndoPayroll(CoNum As Decimal, PrNum As Decimal, deletePayrollGrid As Boolean) As Object Implements PPxPayrollInterface.UndoPayroll
'        Dim _powergridDeleted As Boolean = False
'        WebLogger.PayrollProcessStep = 1
'        Try
'            Dim resultCollection As Global.Execupay.Core.BusinessObject.ResultsCollection = PowerGridAPI.UnProcessPowerGrid(_ppxPowerGridCatalog, CompanyID, PayrollID, PowerGridBatchID, _CurrentUserName, deletePayrollGrid)

'            Dim resultSet As FunctionResult = resultCollection.FirstOrDefault()
'            If resultSet.HasValue AndAlso resultSet.Status = FunctionResultStatus.Completed Then
'                _powergridDeleted = True
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PPxPayroll.UndoPayroll() : Done (PayrollID#" & Me.PayrollID & ")")
'            Else
'                _powergridDeleted = False
'                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PPxPayroll.UndoPayroll() : Error (PayrollID#" & Me.PayrollID & ")")

'            End If
'        Catch ex As Exception
'            WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], "PPxPayroll.UndoPayroll() : Error (PayrollID#" & Me.PayrollID & ")", ex)
'        End Try

'        Return _powergridDeleted
'    End Function
'#End Region

'#Region "methods not implemented"
'    Public Function GetSinglePayrollInfo(CoNum As Decimal, PrNum As Decimal) As PayrollInfo Implements PPxPayrollInterface.GetSinglePayrollInfo
'        Throw New NotImplementedException()
'    End Function

'    Public Function UndoPayroll(ByVal CoNum As Decimal, ByVal PrNum As Decimal, deletePowerGrid As Boolean, PowerGridBatchID As Guid) Implements PPxPayrollInterface.UndoPayroll
'        Throw New NotImplementedException()
'    End Function
'#End Region
'End Class

' This class will help getting specific status realted to Payroll
Public Class PPxPayrollStatusType
    Public Const NONE As String = "None"

    Public Const CalculatingTotals As String = "Calculating Totals"
    Public Const CanStartPayroll As String = "Can Start Payroll"
    Public Const ChecksCompleted As String = "ChecksCompleted"
    Public Const CreatingChecks As String = "Creating Checks"
    Public Const CreatingPowerGrid As String = "Creating PowerGrid"
    Public Const Payroll As String = "Submitting Payroll"
    Public Const PayrollCreated As String = "Payroll Created"
    Public Const PayrollSubmited As String = "PayrollSubmited"
    Public Const PowerGridCompleted As String = "PowerGrid Completed"
    Public Const PowerGridCreated As String = "PowerGrid Created"
    Public Const PowerGridEditing As String = "PowerGrid Editing"
    Public Const PowerGridProcessed As String = "PowerGrid Processed"
    Public Const PreparingTotals As String = "Preparing Totals"
    Public Const ProcessingOtherPowergrid As String = "Other PowerGrid in Process"
    Public Const ProcessingPowerGrid As String = "Processing PowerGrid"
    Public Const StartingPayroll As String = "Starting Payroll"
    Public Const TotalsCalculated As String = "Totals Calculated"
End Class

' Object Class to show Calendars as dropdown or in the grid
' CalendarID: this is imporatant, this id will be used to create a new payroll. (Make sure, Calendar is already generated for this company)
Public Class PPxPayrollCalendar
    Public Property CalendarID As Integer
    Public Property CheckDate As System.Nullable(Of DateTime)
    Public Property EndDate As System.Nullable(Of DateTime)
    Public Property Frequency As String
    Public Property ProcessDate As System.Nullable(Of DateTime)
    Public Property StartDate As System.Nullable(Of DateTime)
    Public Property Title As String
End Class

Public Class WebLogger

    Private Shared _Instance As WebLogger
    Public Event ActionLog(ByVal sender As Object, ByVal e As ActionLogTypeEventArgs)
    Public ShowMsgBoxOnError As Boolean = True

    Public Shared PayrollProcessStep As Decimal

    Friend Shared Function GetInstance() As WebLogger
        If _Instance Is Nothing Then
            _Instance = New WebLogger
        End If
        Return _Instance
    End Function

    Public Enum LogLevel
        Fatal
        Info
        [Error]
    End Enum

    Sub Log(ByVal logLevel As LogLevel, ByVal Message As String, ByVal UserName As String)
        RaiseEvent ActionLog(Me, New ActionLogTypeEventArgs With {.LogLevel = logLevel, .Message = Message, .PayrollProcessStep = PayrollProcessStep})
        Application.DoEvents()
        If logLevel <> WebLogger.LogLevel.Info AndAlso ShowMsgBoxOnError Then
            DisplayMessageBox(Message)
        End If
    End Sub

    Sub Log(ByVal logLevel As LogLevel, ByVal Message As String, ByVal ex As Exception)
        RaiseEvent ActionLog(Me, New ActionLogTypeEventArgs With {.LogLevel = logLevel, .Message = Message, .Ex = ex, .PayrollProcessStep = PayrollProcessStep})
        Application.DoEvents()
        If logLevel <> WebLogger.LogLevel.Info AndAlso ShowMsgBoxOnError Then
            DisplayMessageBox(Message)
        End If
    End Sub

    Sub Log(ByVal logLevel As LogLevel, ByVal Message As String)
        Try
            RaiseEvent ActionLog(Me, New ActionLogTypeEventArgs With {.LogLevel = logLevel, .Message = Message, .PayrollProcessStep = PayrollProcessStep})
            Application.DoEvents()
            If logLevel <> WebLogger.LogLevel.Info AndAlso ShowMsgBoxOnError Then
                DisplayMessageBox(Message)
            End If
        Catch ex As Exception
            If ShowMsgBoxOnError Then DisplayErrorMessage("Error on showing log", ex)
        End Try
    End Sub

End Class

Public Class ActionLogTypeEventArgs
    Inherits EventArgs

    Property LogLevel As WebLogger.LogLevel
    Property Message As String
    Property Ex As Exception
    Property PayrollProcessStep As Decimal
End Class

Class PayrollException
    Inherits Exception

    Property _Messge As String

    Public Overrides ReadOnly Property Message As String
        Get
            Return _Messge
        End Get
    End Property

    Sub New(ByVal Message As String)
        ' TODO: Complete member initialization 
        DisplayMessageBox(Message)
        _Messge = Message
    End Sub

    Sub New(ByVal Message As String, ByVal exception As Exception)
        ' TODO: Complete member initialization 
        MyBase.New(Message, exception)
        _Messge = Message
        DisplayMessageBox(Message)
    End Sub

End Class

