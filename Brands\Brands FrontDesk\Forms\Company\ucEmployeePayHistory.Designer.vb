﻿Imports DevExpress.XtraGrid

<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucEmployeePayHistory
    Inherits DevExpress.XtraEditors.XtraUserControl

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim GridLevelNode1 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridLevelNode2 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridLevelNode3 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridLevelNode4 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Me.gvDeductions = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAmount2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riTextEditAmount = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.gcPayrollDeductionsAndTaxes = New DevExpress.XtraGrid.GridControl()
        Me.gvERTaxes = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTaxable = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCapped = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColAmount1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.gvEETaxes = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colDescription2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.gvPayrollDeductionsAndTaxes = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCaption = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.gvPayHistory = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEarnings = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDepartment = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colWP = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colJob = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBeginDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEndDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colComment = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHours1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAmount = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.gcPayHistory = New DevExpress.XtraGrid.GridControl()
        Me.gvPayHistory1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCaption2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.dePayHistoryFromTo = New DevExpress.XtraEditors.DateEdit()
        Me.dePayHistoryFromDate = New DevExpress.XtraEditors.DateEdit()
        Me.rgDateFilter = New DevExpress.XtraEditors.RadioGroup()
        Me.gcCheckMaster = New DevExpress.XtraGrid.GridControl()
        Me.gvCheckMaster = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCheckDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCheckType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHours = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colGross = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDeds = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTaxes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDirDeposit = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNet = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCheckAmount = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCheckNumber = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPrNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCheckCounter = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem2 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciFromDate = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciToDate = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.taxesBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.popupCopyFrom = New DevExpress.XtraEditors.PopupContainerControl()
        Me.GroupControl10 = New DevExpress.XtraEditors.GroupControl()
        Me.txtEmpNum = New DevExpress.XtraEditors.TextEdit()
        Me.txtCoName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.slueEmployeeCopyFrom = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView3 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colEmpNum2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEmpName2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl13 = New DevExpress.XtraEditors.LabelControl()
        Me.btnOk = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancelCopyToNewCompany = New DevExpress.XtraEditors.SimpleButton()
        Me.slueCompanyCopyFrom = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCONUM1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.gvDeductions, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riTextEditAmount, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcPayrollDeductionsAndTaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvERTaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvEETaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvPayrollDeductionsAndTaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvPayHistory, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcPayHistory, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvPayHistory1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.dePayHistoryFromTo.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dePayHistoryFromTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dePayHistoryFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dePayHistoryFromDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.rgDateFilter.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcCheckMaster, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvCheckMaster, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciFromDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciToDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.taxesBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.popupCopyFrom, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.popupCopyFrom.SuspendLayout()
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl10.SuspendLayout()
        CType(Me.txtEmpNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtCoName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueEmployeeCopyFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueCompanyCopyFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'gvDeductions
        '
        Me.gvDeductions.Appearance.FooterPanel.BackColor = System.Drawing.Color.White
        Me.gvDeductions.Appearance.FooterPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.gvDeductions.Appearance.FooterPanel.Options.UseBackColor = True
        Me.gvDeductions.Appearance.FooterPanel.Options.UseFont = True
        Me.gvDeductions.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Bold)
        Me.gvDeductions.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.Navy
        Me.gvDeductions.Appearance.HeaderPanel.Options.UseFont = True
        Me.gvDeductions.Appearance.HeaderPanel.Options.UseForeColor = True
        Me.gvDeductions.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colDescription, Me.colAmount2})
        Me.gvDeductions.GridControl = Me.gcPayrollDeductionsAndTaxes
        Me.gvDeductions.Name = "gvDeductions"
        Me.gvDeductions.OptionsBehavior.ReadOnly = True
        Me.gvDeductions.OptionsCustomization.AllowFilter = False
        Me.gvDeductions.OptionsCustomization.AllowGroup = False
        Me.gvDeductions.OptionsMenu.EnableColumnMenu = False
        Me.gvDeductions.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvDeductions.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.gvDeductions.OptionsView.ShowFooter = True
        Me.gvDeductions.OptionsView.ShowGroupPanel = False
        Me.gvDeductions.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvDeductions.OptionsView.ShowIndicator = False
        Me.gvDeductions.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'colDescription
        '
        Me.colDescription.Caption = "Deduction"
        Me.colDescription.FieldName = "Description"
        Me.colDescription.Name = "colDescription"
        Me.colDescription.Visible = True
        Me.colDescription.VisibleIndex = 0
        '
        'colAmount2
        '
        Me.colAmount2.AppearanceHeader.Options.UseTextOptions = True
        Me.colAmount2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.colAmount2.ColumnEdit = Me.riTextEditAmount
        Me.colAmount2.DisplayFormat.FormatString = "N2"
        Me.colAmount2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colAmount2.FieldName = "Amount"
        Me.colAmount2.Name = "colAmount2"
        Me.colAmount2.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Amount", "{0:0.##}")})
        Me.colAmount2.Visible = True
        Me.colAmount2.VisibleIndex = 1
        '
        'riTextEditAmount
        '
        Me.riTextEditAmount.AutoHeight = False
        Me.riTextEditAmount.DisplayFormat.FormatString = "N2"
        Me.riTextEditAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.riTextEditAmount.EditFormat.FormatString = "N2"
        Me.riTextEditAmount.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.riTextEditAmount.Name = "riTextEditAmount"
        '
        'gcPayrollDeductionsAndTaxes
        '
        GridLevelNode1.LevelTemplate = Me.gvDeductions
        GridLevelNode1.RelationName = "Deductions"
        GridLevelNode2.LevelTemplate = Me.gvERTaxes
        GridLevelNode2.RelationName = "ERTaxes"
        GridLevelNode3.LevelTemplate = Me.gvEETaxes
        GridLevelNode3.RelationName = "EETaxes"
        Me.gcPayrollDeductionsAndTaxes.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode1, GridLevelNode2, GridLevelNode3})
        Me.gcPayrollDeductionsAndTaxes.Location = New System.Drawing.Point(858, 416)
        Me.gcPayrollDeductionsAndTaxes.MainView = Me.gvPayrollDeductionsAndTaxes
        Me.gcPayrollDeductionsAndTaxes.Name = "gcPayrollDeductionsAndTaxes"
        Me.gcPayrollDeductionsAndTaxes.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riTextEditAmount})
        Me.gcPayrollDeductionsAndTaxes.Size = New System.Drawing.Size(395, 276)
        Me.gcPayrollDeductionsAndTaxes.TabIndex = 31
        Me.gcPayrollDeductionsAndTaxes.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvERTaxes, Me.gvEETaxes, Me.gvPayrollDeductionsAndTaxes, Me.gvDeductions})
        '
        'gvERTaxes
        '
        Me.gvERTaxes.Appearance.FooterPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.gvERTaxes.Appearance.FooterPanel.Options.UseFont = True
        Me.gvERTaxes.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Bold)
        Me.gvERTaxes.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.Navy
        Me.gvERTaxes.Appearance.HeaderPanel.Options.UseFont = True
        Me.gvERTaxes.Appearance.HeaderPanel.Options.UseForeColor = True
        Me.gvERTaxes.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.colTaxable, Me.colCapped, Me.ColAmount1})
        Me.gvERTaxes.GridControl = Me.gcPayrollDeductionsAndTaxes
        Me.gvERTaxes.Name = "gvERTaxes"
        Me.gvERTaxes.OptionsBehavior.ReadOnly = True
        Me.gvERTaxes.OptionsCustomization.AllowFilter = False
        Me.gvERTaxes.OptionsCustomization.AllowGroup = False
        Me.gvERTaxes.OptionsMenu.EnableColumnMenu = False
        Me.gvERTaxes.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvERTaxes.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.gvERTaxes.OptionsView.ShowFooter = True
        Me.gvERTaxes.OptionsView.ShowGroupPanel = False
        Me.gvERTaxes.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvERTaxes.OptionsView.ShowIndicator = False
        Me.gvERTaxes.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "Employer"
        Me.GridColumn1.FieldName = "Description"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        '
        'colTaxable
        '
        Me.colTaxable.AppearanceHeader.Options.UseTextOptions = True
        Me.colTaxable.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.colTaxable.ColumnEdit = Me.riTextEditAmount
        Me.colTaxable.DisplayFormat.FormatString = "N2"
        Me.colTaxable.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colTaxable.FieldName = "Taxable"
        Me.colTaxable.Name = "colTaxable"
        Me.colTaxable.Visible = True
        Me.colTaxable.VisibleIndex = 1
        '
        'colCapped
        '
        Me.colCapped.AppearanceHeader.Options.UseTextOptions = True
        Me.colCapped.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.colCapped.ColumnEdit = Me.riTextEditAmount
        Me.colCapped.DisplayFormat.FormatString = "N2"
        Me.colCapped.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colCapped.FieldName = "Capped"
        Me.colCapped.Name = "colCapped"
        Me.colCapped.Visible = True
        Me.colCapped.VisibleIndex = 2
        Me.colCapped.Width = 29
        '
        'ColAmount1
        '
        Me.ColAmount1.AppearanceHeader.Options.UseTextOptions = True
        Me.ColAmount1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.ColAmount1.ColumnEdit = Me.riTextEditAmount
        Me.ColAmount1.DisplayFormat.FormatString = "N2"
        Me.ColAmount1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.ColAmount1.FieldName = "Amount"
        Me.ColAmount1.Name = "ColAmount1"
        Me.ColAmount1.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Amount", "{0:0.##}")})
        Me.ColAmount1.Visible = True
        Me.ColAmount1.VisibleIndex = 3
        Me.ColAmount1.Width = 31
        '
        'gvEETaxes
        '
        Me.gvEETaxes.Appearance.FooterPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.gvEETaxes.Appearance.FooterPanel.Options.UseFont = True
        Me.gvEETaxes.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Bold)
        Me.gvEETaxes.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.Navy
        Me.gvEETaxes.Appearance.HeaderPanel.Options.UseFont = True
        Me.gvEETaxes.Appearance.HeaderPanel.Options.UseForeColor = True
        Me.gvEETaxes.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colDescription2, Me.colTaxable, Me.colCapped, Me.ColAmount1})
        Me.gvEETaxes.GridControl = Me.gcPayrollDeductionsAndTaxes
        Me.gvEETaxes.Name = "gvEETaxes"
        Me.gvEETaxes.OptionsBehavior.ReadOnly = True
        Me.gvEETaxes.OptionsCustomization.AllowFilter = False
        Me.gvEETaxes.OptionsCustomization.AllowGroup = False
        Me.gvEETaxes.OptionsMenu.EnableColumnMenu = False
        Me.gvEETaxes.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvEETaxes.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.gvEETaxes.OptionsView.ShowFooter = True
        Me.gvEETaxes.OptionsView.ShowGroupPanel = False
        Me.gvEETaxes.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvEETaxes.OptionsView.ShowIndicator = False
        Me.gvEETaxes.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'colDescription2
        '
        Me.colDescription2.Caption = "Taxes"
        Me.colDescription2.FieldName = "Description"
        Me.colDescription2.Name = "colDescription2"
        Me.colDescription2.Visible = True
        Me.colDescription2.VisibleIndex = 0
        '
        'gvPayrollDeductionsAndTaxes
        '
        Me.gvPayrollDeductionsAndTaxes.Appearance.Empty.BackColor = System.Drawing.Color.White
        Me.gvPayrollDeductionsAndTaxes.Appearance.Empty.Options.UseBackColor = True
        Me.gvPayrollDeductionsAndTaxes.Appearance.FixedLine.BackColor = System.Drawing.Color.White
        Me.gvPayrollDeductionsAndTaxes.Appearance.FixedLine.Options.UseBackColor = True
        Me.gvPayrollDeductionsAndTaxes.Appearance.FooterPanel.BackColor = System.Drawing.Color.White
        Me.gvPayrollDeductionsAndTaxes.Appearance.FooterPanel.Options.UseBackColor = True
        Me.gvPayrollDeductionsAndTaxes.Appearance.HeaderPanel.BackColor = System.Drawing.Color.White
        Me.gvPayrollDeductionsAndTaxes.Appearance.HeaderPanel.Options.UseBackColor = True
        Me.gvPayrollDeductionsAndTaxes.Appearance.Row.BackColor = System.Drawing.Color.White
        Me.gvPayrollDeductionsAndTaxes.Appearance.Row.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.gvPayrollDeductionsAndTaxes.Appearance.Row.ForeColor = System.Drawing.Color.Blue
        Me.gvPayrollDeductionsAndTaxes.Appearance.Row.Options.UseBackColor = True
        Me.gvPayrollDeductionsAndTaxes.Appearance.Row.Options.UseFont = True
        Me.gvPayrollDeductionsAndTaxes.Appearance.Row.Options.UseForeColor = True
        Me.gvPayrollDeductionsAndTaxes.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCaption})
        Me.gvPayrollDeductionsAndTaxes.GridControl = Me.gcPayrollDeductionsAndTaxes
        Me.gvPayrollDeductionsAndTaxes.LevelIndent = 0
        Me.gvPayrollDeductionsAndTaxes.Name = "gvPayrollDeductionsAndTaxes"
        Me.gvPayrollDeductionsAndTaxes.OptionsBehavior.Editable = False
        Me.gvPayrollDeductionsAndTaxes.OptionsDetail.AllowZoomDetail = False
        Me.gvPayrollDeductionsAndTaxes.OptionsDetail.DetailMode = DevExpress.XtraGrid.Views.Grid.DetailMode.Embedded
        Me.gvPayrollDeductionsAndTaxes.OptionsDetail.ShowDetailTabs = False
        Me.gvPayrollDeductionsAndTaxes.OptionsDetail.ShowEmbeddedDetailIndent = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvPayrollDeductionsAndTaxes.OptionsDetail.SmartDetailExpandButtonMode = DevExpress.XtraGrid.Views.Grid.DetailExpandButtonMode.AlwaysEnabled
        Me.gvPayrollDeductionsAndTaxes.OptionsDetail.SmartDetailHeight = True
        Me.gvPayrollDeductionsAndTaxes.OptionsPrint.ExpandAllDetails = True
        Me.gvPayrollDeductionsAndTaxes.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvPayrollDeductionsAndTaxes.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.gvPayrollDeductionsAndTaxes.OptionsView.ShowColumnHeaders = False
        Me.gvPayrollDeductionsAndTaxes.OptionsView.ShowDetailButtons = False
        Me.gvPayrollDeductionsAndTaxes.OptionsView.ShowGroupPanel = False
        Me.gvPayrollDeductionsAndTaxes.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvPayrollDeductionsAndTaxes.OptionsView.ShowIndicator = False
        Me.gvPayrollDeductionsAndTaxes.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'colCaption
        '
        Me.colCaption.Caption = "GridColumn1"
        Me.colCaption.FieldName = "Caption"
        Me.colCaption.Name = "colCaption"
        Me.colCaption.Visible = True
        Me.colCaption.VisibleIndex = 0
        '
        'gvPayHistory
        '
        Me.gvPayHistory.Appearance.FooterPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.gvPayHistory.Appearance.FooterPanel.Options.UseFont = True
        Me.gvPayHistory.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gvPayHistory.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.Navy
        Me.gvPayHistory.Appearance.HeaderPanel.Options.UseFont = True
        Me.gvPayHistory.Appearance.HeaderPanel.Options.UseForeColor = True
        Me.gvPayHistory.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colType, Me.colEarnings, Me.colDepartment, Me.colWP, Me.colJob, Me.colBeginDate, Me.colEndDate, Me.colComment, Me.colHours1, Me.colRate, Me.colAmount})
        Me.gvPayHistory.GridControl = Me.gcPayHistory
        Me.gvPayHistory.GroupFormat = "-"
        Me.gvPayHistory.GroupRowHeight = 0
        Me.gvPayHistory.Name = "gvPayHistory"
        Me.gvPayHistory.OptionsBehavior.AutoExpandAllGroups = True
        Me.gvPayHistory.OptionsBehavior.Editable = False
        Me.gvPayHistory.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvPayHistory.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.gvPayHistory.OptionsView.ShowFooter = True
        Me.gvPayHistory.OptionsView.ShowGroupExpandCollapseButtons = False
        Me.gvPayHistory.OptionsView.ShowGroupPanel = False
        Me.gvPayHistory.OptionsView.ShowIndicator = False
        Me.gvPayHistory.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'colType
        '
        Me.colType.FieldName = "Type"
        Me.colType.Name = "colType"
        Me.colType.Visible = True
        Me.colType.VisibleIndex = 0
        '
        'colEarnings
        '
        Me.colEarnings.FieldName = "Earnings"
        Me.colEarnings.Name = "colEarnings"
        Me.colEarnings.Visible = True
        Me.colEarnings.VisibleIndex = 1
        '
        'colDepartment
        '
        Me.colDepartment.FieldName = "Department"
        Me.colDepartment.Name = "colDepartment"
        Me.colDepartment.Visible = True
        Me.colDepartment.VisibleIndex = 2
        '
        'colWP
        '
        Me.colWP.FieldName = "WP"
        Me.colWP.Name = "colWP"
        Me.colWP.Visible = True
        Me.colWP.VisibleIndex = 3
        '
        'colJob
        '
        Me.colJob.FieldName = "Job"
        Me.colJob.Name = "colJob"
        Me.colJob.Visible = True
        Me.colJob.VisibleIndex = 4
        '
        'colBeginDate
        '
        Me.colBeginDate.FieldName = "BeginDate"
        Me.colBeginDate.Name = "colBeginDate"
        Me.colBeginDate.Visible = True
        Me.colBeginDate.VisibleIndex = 5
        '
        'colEndDate
        '
        Me.colEndDate.FieldName = "EndDate"
        Me.colEndDate.Name = "colEndDate"
        Me.colEndDate.Visible = True
        Me.colEndDate.VisibleIndex = 6
        '
        'colComment
        '
        Me.colComment.FieldName = "Comment"
        Me.colComment.Name = "colComment"
        Me.colComment.Visible = True
        Me.colComment.VisibleIndex = 7
        '
        'colHours1
        '
        Me.colHours1.DisplayFormat.FormatString = "N2"
        Me.colHours1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colHours1.FieldName = "Hours"
        Me.colHours1.Name = "colHours1"
        Me.colHours1.Visible = True
        Me.colHours1.VisibleIndex = 8
        '
        'colRate
        '
        Me.colRate.DisplayFormat.FormatString = "N4"
        Me.colRate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colRate.FieldName = "Rate"
        Me.colRate.Name = "colRate"
        Me.colRate.Visible = True
        Me.colRate.VisibleIndex = 9
        '
        'colAmount
        '
        Me.colAmount.DisplayFormat.FormatString = "N2"
        Me.colAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colAmount.FieldName = "Amount"
        Me.colAmount.Name = "colAmount"
        Me.colAmount.Visible = True
        Me.colAmount.VisibleIndex = 10
        '
        'gcPayHistory
        '
        GridLevelNode4.LevelTemplate = Me.gvPayHistory
        GridLevelNode4.RelationName = "Details"
        Me.gcPayHistory.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode4})
        Me.gcPayHistory.Location = New System.Drawing.Point(2, 416)
        Me.gcPayHistory.MainView = Me.gvPayHistory1
        Me.gcPayHistory.Name = "gcPayHistory"
        Me.gcPayHistory.Size = New System.Drawing.Size(852, 276)
        Me.gcPayHistory.TabIndex = 17
        Me.gcPayHistory.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvPayHistory1, Me.gvPayHistory})
        '
        'gvPayHistory1
        '
        Me.gvPayHistory1.Appearance.Empty.BackColor = System.Drawing.Color.White
        Me.gvPayHistory1.Appearance.Empty.Options.UseBackColor = True
        Me.gvPayHistory1.Appearance.Row.BackColor = System.Drawing.Color.White
        Me.gvPayHistory1.Appearance.Row.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.gvPayHistory1.Appearance.Row.ForeColor = System.Drawing.Color.Blue
        Me.gvPayHistory1.Appearance.Row.Options.UseBackColor = True
        Me.gvPayHistory1.Appearance.Row.Options.UseFont = True
        Me.gvPayHistory1.Appearance.Row.Options.UseForeColor = True
        Me.gvPayHistory1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCaption2})
        Me.gvPayHistory1.GridControl = Me.gcPayHistory
        Me.gvPayHistory1.LevelIndent = 0
        Me.gvPayHistory1.Name = "gvPayHistory1"
        Me.gvPayHistory1.OptionsBehavior.Editable = False
        Me.gvPayHistory1.OptionsDetail.AllowZoomDetail = False
        Me.gvPayHistory1.OptionsDetail.DetailMode = DevExpress.XtraGrid.Views.Grid.DetailMode.Embedded
        Me.gvPayHistory1.OptionsDetail.ShowDetailTabs = False
        Me.gvPayHistory1.OptionsDetail.ShowEmbeddedDetailIndent = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvPayHistory1.OptionsDetail.SmartDetailExpandButtonMode = DevExpress.XtraGrid.Views.Grid.DetailExpandButtonMode.AlwaysEnabled
        Me.gvPayHistory1.OptionsDetail.SmartDetailHeight = True
        Me.gvPayHistory1.OptionsPrint.ExpandAllDetails = True
        Me.gvPayHistory1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvPayHistory1.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.gvPayHistory1.OptionsView.ShowColumnHeaders = False
        Me.gvPayHistory1.OptionsView.ShowDetailButtons = False
        Me.gvPayHistory1.OptionsView.ShowGroupPanel = False
        Me.gvPayHistory1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvPayHistory1.OptionsView.ShowIndicator = False
        Me.gvPayHistory1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'colCaption2
        '
        Me.colCaption2.Caption = "GridColumn3"
        Me.colCaption2.FieldName = "Caption"
        Me.colCaption2.Name = "colCaption2"
        Me.colCaption2.Visible = True
        Me.colCaption2.VisibleIndex = 0
        '
        'LayoutControl1
        '
        Me.LayoutControl1.AllowCustomization = False
        Me.LayoutControl1.Controls.Add(Me.gcPayrollDeductionsAndTaxes)
        Me.LayoutControl1.Controls.Add(Me.dePayHistoryFromTo)
        Me.LayoutControl1.Controls.Add(Me.dePayHistoryFromDate)
        Me.LayoutControl1.Controls.Add(Me.rgDateFilter)
        Me.LayoutControl1.Controls.Add(Me.gcPayHistory)
        Me.LayoutControl1.Controls.Add(Me.gcCheckMaster)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Margin = New System.Windows.Forms.Padding(0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(62, 211, 1334, 650)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1255, 694)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'dePayHistoryFromTo
        '
        Me.dePayHistoryFromTo.EditValue = Nothing
        Me.dePayHistoryFromTo.Location = New System.Drawing.Point(1135, 381)
        Me.dePayHistoryFromTo.Name = "dePayHistoryFromTo"
        Me.dePayHistoryFromTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.dePayHistoryFromTo.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.dePayHistoryFromTo.Size = New System.Drawing.Size(115, 20)
        Me.dePayHistoryFromTo.StyleController = Me.LayoutControl1
        Me.dePayHistoryFromTo.TabIndex = 30
        '
        'dePayHistoryFromDate
        '
        Me.dePayHistoryFromDate.EditValue = Nothing
        Me.dePayHistoryFromDate.Location = New System.Drawing.Point(1135, 357)
        Me.dePayHistoryFromDate.Name = "dePayHistoryFromDate"
        Me.dePayHistoryFromDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.dePayHistoryFromDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.dePayHistoryFromDate.Size = New System.Drawing.Size(115, 20)
        Me.dePayHistoryFromDate.StyleController = Me.LayoutControl1
        Me.dePayHistoryFromDate.TabIndex = 29
        '
        'rgDateFilter
        '
        Me.rgDateFilter.Location = New System.Drawing.Point(1104, 25)
        Me.rgDateFilter.Name = "rgDateFilter"
        Me.rgDateFilter.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI Semibold", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rgDateFilter.Properties.Appearance.Options.UseFont = True
        Me.rgDateFilter.Size = New System.Drawing.Size(146, 328)
        Me.rgDateFilter.StyleController = Me.LayoutControl1
        Me.rgDateFilter.TabIndex = 15
        '
        'gcCheckMaster
        '
        Me.gcCheckMaster.Location = New System.Drawing.Point(2, 2)
        Me.gcCheckMaster.MainView = Me.gvCheckMaster
        Me.gcCheckMaster.Name = "gcCheckMaster"
        Me.gcCheckMaster.Size = New System.Drawing.Size(1095, 402)
        Me.gcCheckMaster.TabIndex = 7
        Me.gcCheckMaster.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvCheckMaster})
        '
        'gvCheckMaster
        '
        Me.gvCheckMaster.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCheckDate, Me.colPrNum, Me.colCheckType, Me.colHours, Me.colGross, Me.colDeds, Me.colTaxes, Me.colDirDeposit, Me.colNet, Me.colCheckAmount, Me.colCheckNumber, Me.colCheckCounter})
        Me.gvCheckMaster.GridControl = Me.gcCheckMaster
        Me.gvCheckMaster.Name = "gvCheckMaster"
        Me.gvCheckMaster.OptionsBehavior.Editable = False
        Me.gvCheckMaster.OptionsSelection.CheckBoxSelectorColumnWidth = 35
        Me.gvCheckMaster.OptionsSelection.MultiSelect = True
        Me.gvCheckMaster.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect
        Me.gvCheckMaster.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.[True]
        Me.gvCheckMaster.OptionsView.ColumnAutoWidth = False
        Me.gvCheckMaster.OptionsView.ShowAutoFilterRow = True
        Me.gvCheckMaster.OptionsView.ShowFooter = True
        Me.gvCheckMaster.OptionsView.ShowGroupPanel = False
        '
        'colCheckDate
        '
        Me.colCheckDate.DisplayFormat.FormatString = "d"
        Me.colCheckDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colCheckDate.FieldName = "CheckDate"
        Me.colCheckDate.Name = "colCheckDate"
        Me.colCheckDate.Visible = True
        Me.colCheckDate.VisibleIndex = 1
        '
        'colCheckType
        '
        Me.colCheckType.FieldName = "CheckType"
        Me.colCheckType.Name = "colCheckType"
        Me.colCheckType.Visible = True
        Me.colCheckType.VisibleIndex = 3
        '
        'colHours
        '
        Me.colHours.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colHours.FieldName = "Hours"
        Me.colHours.Name = "colHours"
        Me.colHours.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Hours", "{0:N2}")})
        Me.colHours.Visible = True
        Me.colHours.VisibleIndex = 4
        '
        'colGross
        '
        Me.colGross.FieldName = "Gross"
        Me.colGross.Name = "colGross"
        Me.colGross.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Gross", "{0:N2}")})
        Me.colGross.Visible = True
        Me.colGross.VisibleIndex = 5
        '
        'colDeds
        '
        Me.colDeds.FieldName = "Deds"
        Me.colDeds.Name = "colDeds"
        Me.colDeds.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Deds", "{0:N2}")})
        Me.colDeds.Visible = True
        Me.colDeds.VisibleIndex = 6
        '
        'colTaxes
        '
        Me.colTaxes.FieldName = "Taxes"
        Me.colTaxes.Name = "colTaxes"
        Me.colTaxes.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Taxes", "{0:N2}")})
        Me.colTaxes.Visible = True
        Me.colTaxes.VisibleIndex = 7
        '
        'colDirDeposit
        '
        Me.colDirDeposit.FieldName = "DirDeposit"
        Me.colDirDeposit.Name = "colDirDeposit"
        Me.colDirDeposit.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "DirDeposit", "{0:N2}")})
        Me.colDirDeposit.Visible = True
        Me.colDirDeposit.VisibleIndex = 8
        '
        'colNet
        '
        Me.colNet.FieldName = "Net"
        Me.colNet.Name = "colNet"
        Me.colNet.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Net", "{0:N2}")})
        Me.colNet.Visible = True
        Me.colNet.VisibleIndex = 9
        '
        'colCheckAmount
        '
        Me.colCheckAmount.FieldName = "CheckAmount"
        Me.colCheckAmount.Name = "colCheckAmount"
        Me.colCheckAmount.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "CheckAmout", "{0:N2}")})
        Me.colCheckAmount.Visible = True
        Me.colCheckAmount.VisibleIndex = 10
        '
        'colCheckNumber
        '
        Me.colCheckNumber.FieldName = "CheckNumber"
        Me.colCheckNumber.Name = "colCheckNumber"
        Me.colCheckNumber.Visible = True
        Me.colCheckNumber.VisibleIndex = 11
        '
        'colPrNum
        '
        Me.colPrNum.Caption = "Pr#"
        Me.colPrNum.FieldName = "Prnum"
        Me.colPrNum.Name = "colPrNum"
        Me.colPrNum.Visible = True
        Me.colPrNum.VisibleIndex = 2
        Me.colPrNum.Width = 40
        '
        'colCheckCounter
        '
        Me.colCheckCounter.Caption = "Check Counter"
        Me.colCheckCounter.FieldName = "CheckCounter"
        Me.colCheckCounter.Name = "colCheckCounter"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.SplitterItem2, Me.LayoutControlItem6, Me.LayoutControlGroup2, Me.LayoutControlItem2})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1255, 694)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.gcCheckMaster
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(1099, 406)
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'SplitterItem2
        '
        Me.SplitterItem2.AllowHotTrack = True
        Me.SplitterItem2.Location = New System.Drawing.Point(0, 406)
        Me.SplitterItem2.Name = "SplitterItem2"
        Me.SplitterItem2.Size = New System.Drawing.Size(1255, 8)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.gcPayHistory
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 414)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(856, 280)
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.lciFromDate, Me.lciToDate})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(1099, 0)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(156, 406)
        Me.LayoutControlGroup2.Text = "Date Filters"
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.rgDateFilter
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(150, 332)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'lciFromDate
        '
        Me.lciFromDate.Control = Me.dePayHistoryFromDate
        Me.lciFromDate.Location = New System.Drawing.Point(0, 332)
        Me.lciFromDate.Name = "lciFromDate"
        Me.lciFromDate.Size = New System.Drawing.Size(150, 24)
        Me.lciFromDate.Text = "From:"
        Me.lciFromDate.TextSize = New System.Drawing.Size(28, 13)
        '
        'lciToDate
        '
        Me.lciToDate.Control = Me.dePayHistoryFromTo
        Me.lciToDate.Location = New System.Drawing.Point(0, 356)
        Me.lciToDate.Name = "lciToDate"
        Me.lciToDate.Size = New System.Drawing.Size(150, 24)
        Me.lciToDate.Text = "To:"
        Me.lciToDate.TextSize = New System.Drawing.Size(28, 13)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.gcPayrollDeductionsAndTaxes
        Me.LayoutControlItem2.Location = New System.Drawing.Point(856, 414)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(399, 280)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'taxesBindingSource
        '
        Me.taxesBindingSource.DataSource = GetType(Brands_FrontDesk.ucEmployeeInfo.Taxes)
        '
        'popupCopyFrom
        '
        Me.popupCopyFrom.Controls.Add(Me.GroupControl10)
        Me.popupCopyFrom.Location = New System.Drawing.Point(604, 268)
        Me.popupCopyFrom.Name = "popupCopyFrom"
        Me.popupCopyFrom.Size = New System.Drawing.Size(405, 160)
        Me.popupCopyFrom.TabIndex = 12
        '
        'GroupControl10
        '
        Me.GroupControl10.Controls.Add(Me.txtEmpNum)
        Me.GroupControl10.Controls.Add(Me.txtCoName)
        Me.GroupControl10.Controls.Add(Me.LabelControl14)
        Me.GroupControl10.Controls.Add(Me.slueEmployeeCopyFrom)
        Me.GroupControl10.Controls.Add(Me.LabelControl13)
        Me.GroupControl10.Controls.Add(Me.btnOk)
        Me.GroupControl10.Controls.Add(Me.btnCancelCopyToNewCompany)
        Me.GroupControl10.Controls.Add(Me.slueCompanyCopyFrom)
        Me.GroupControl10.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl10.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl10.LookAndFeel.SkinName = "Office 2010 Blue"
        Me.GroupControl10.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl10.Name = "GroupControl10"
        Me.GroupControl10.Size = New System.Drawing.Size(405, 160)
        Me.GroupControl10.TabIndex = 11
        Me.GroupControl10.Text = "Select Source Company && Employee "
        '
        'txtEmpNum
        '
        Me.txtEmpNum.Location = New System.Drawing.Point(144, 62)
        Me.txtEmpNum.Name = "txtEmpNum"
        Me.txtEmpNum.Properties.ReadOnly = True
        Me.txtEmpNum.Size = New System.Drawing.Size(232, 20)
        Me.txtEmpNum.TabIndex = 7
        '
        'txtCoName
        '
        Me.txtCoName.Location = New System.Drawing.Point(144, 35)
        Me.txtCoName.Name = "txtCoName"
        Me.txtCoName.Properties.ReadOnly = True
        Me.txtCoName.Size = New System.Drawing.Size(232, 20)
        Me.txtCoName.TabIndex = 6
        '
        'LabelControl14
        '
        Me.LabelControl14.Location = New System.Drawing.Point(13, 65)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(35, 13)
        Me.LabelControl14.TabIndex = 5
        Me.LabelControl14.Text = "Emp #:"
        '
        'slueEmployeeCopyFrom
        '
        Me.slueEmployeeCopyFrom.EditValue = ""
        Me.slueEmployeeCopyFrom.Location = New System.Drawing.Point(51, 62)
        Me.slueEmployeeCopyFrom.Name = "slueEmployeeCopyFrom"
        Me.slueEmployeeCopyFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueEmployeeCopyFrom.Properties.DisplayMember = "EMPNUM"
        Me.slueEmployeeCopyFrom.Properties.NullText = ""
        Me.slueEmployeeCopyFrom.Properties.PopupFormSize = New System.Drawing.Size(250, 0)
        Me.slueEmployeeCopyFrom.Properties.PopupView = Me.GridView3
        Me.slueEmployeeCopyFrom.Properties.ValueMember = "EMPNUM"
        Me.slueEmployeeCopyFrom.Size = New System.Drawing.Size(87, 20)
        Me.slueEmployeeCopyFrom.TabIndex = 1
        '
        'GridView3
        '
        Me.GridView3.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colEmpNum2, Me.colEmpName2})
        Me.GridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView3.Name = "GridView3"
        Me.GridView3.OptionsFind.FindDelay = 100
        Me.GridView3.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView3.OptionsView.ColumnAutoWidth = False
        Me.GridView3.OptionsView.ShowGroupPanel = False
        '
        'colEmpNum2
        '
        Me.colEmpNum2.FieldName = "EMPNUM"
        Me.colEmpNum2.Name = "colEmpNum2"
        Me.colEmpNum2.Visible = True
        Me.colEmpNum2.VisibleIndex = 0
        '
        'colEmpName2
        '
        Me.colEmpName2.FieldName = "EmpName"
        Me.colEmpName2.Name = "colEmpName2"
        Me.colEmpName2.Visible = True
        Me.colEmpName2.VisibleIndex = 1
        Me.colEmpName2.Width = 150
        '
        'LabelControl13
        '
        Me.LabelControl13.Location = New System.Drawing.Point(13, 38)
        Me.LabelControl13.Name = "LabelControl13"
        Me.LabelControl13.Size = New System.Drawing.Size(32, 13)
        Me.LabelControl13.TabIndex = 3
        Me.LabelControl13.Text = "Co. #:"
        '
        'btnOk
        '
        Me.btnOk.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.Confirm
        Me.btnOk.Location = New System.Drawing.Point(201, 112)
        Me.btnOk.Name = "btnOk"
        Me.btnOk.Size = New System.Drawing.Size(73, 23)
        Me.btnOk.TabIndex = 2
        Me.btnOk.Text = "Ok"
        '
        'btnCancelCopyToNewCompany
        '
        Me.btnCancelCopyToNewCompany.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.Action_Delete
        Me.btnCancelCopyToNewCompany.Location = New System.Drawing.Point(104, 112)
        Me.btnCancelCopyToNewCompany.Name = "btnCancelCopyToNewCompany"
        Me.btnCancelCopyToNewCompany.Size = New System.Drawing.Size(70, 23)
        Me.btnCancelCopyToNewCompany.TabIndex = 3
        Me.btnCancelCopyToNewCompany.Text = "Cancel"
        '
        'slueCompanyCopyFrom
        '
        Me.slueCompanyCopyFrom.EditValue = ""
        Me.slueCompanyCopyFrom.Location = New System.Drawing.Point(51, 35)
        Me.slueCompanyCopyFrom.Name = "slueCompanyCopyFrom"
        Me.slueCompanyCopyFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueCompanyCopyFrom.Properties.DisplayMember = "CONUM"
        Me.slueCompanyCopyFrom.Properties.NullText = ""
        Me.slueCompanyCopyFrom.Properties.PopupFormSize = New System.Drawing.Size(350, 0)
        Me.slueCompanyCopyFrom.Properties.PopupView = Me.GridView1
        Me.slueCompanyCopyFrom.Properties.ValueMember = "CONUM"
        Me.slueCompanyCopyFrom.Size = New System.Drawing.Size(87, 20)
        Me.slueCompanyCopyFrom.TabIndex = 0
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM1, Me.GridColumn5, Me.GridColumn6})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsFind.FindDelay = 100
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colCONUM1
        '
        Me.colCONUM1.FieldName = "CONUM"
        Me.colCONUM1.Name = "colCONUM1"
        Me.colCONUM1.Visible = True
        Me.colCONUM1.VisibleIndex = 0
        '
        'GridColumn5
        '
        Me.GridColumn5.FieldName = "CO_NAME"
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 1
        Me.GridColumn5.Width = 150
        '
        'GridColumn6
        '
        Me.GridColumn6.FieldName = "FED_ID"
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 2
        '
        'ucEmployeePayHistory
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.popupCopyFrom)
        Me.Name = "ucEmployeePayHistory"
        Me.Size = New System.Drawing.Size(1255, 694)
        CType(Me.gvDeductions, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riTextEditAmount, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcPayrollDeductionsAndTaxes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvERTaxes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvEETaxes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvPayrollDeductionsAndTaxes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvPayHistory, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcPayHistory, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvPayHistory1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.dePayHistoryFromTo.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dePayHistoryFromTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dePayHistoryFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dePayHistoryFromDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.rgDateFilter.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcCheckMaster, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvCheckMaster, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciFromDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciToDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.taxesBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.popupCopyFrom, System.ComponentModel.ISupportInitialize).EndInit()
        Me.popupCopyFrom.ResumeLayout(False)
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl10.ResumeLayout(False)
        Me.GroupControl10.PerformLayout()
        CType(Me.txtEmpNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtCoName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueEmployeeCopyFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueCompanyCopyFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents gcCheckMaster As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvCheckMaster As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colCheckDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCheckType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHours As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colGross As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDeds As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTaxes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDirDeposit As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNet As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCheckAmount As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCheckNumber As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents gcPayHistory As GridControl
    Friend WithEvents gvPayHistory As Views.Grid.GridView
    Friend WithEvents colType As Columns.GridColumn
    Friend WithEvents colEarnings As Columns.GridColumn
    Friend WithEvents colDepartment As Columns.GridColumn
    Friend WithEvents colWP As Columns.GridColumn
    Friend WithEvents colJob As Columns.GridColumn
    Friend WithEvents colBeginDate As Columns.GridColumn
    Friend WithEvents colEndDate As Columns.GridColumn
    Friend WithEvents colComment As Columns.GridColumn
    Friend WithEvents colHours1 As Columns.GridColumn
    Friend WithEvents colRate As Columns.GridColumn
    Friend WithEvents colAmount As Columns.GridColumn
    Friend WithEvents colTaxable As Columns.GridColumn
    Friend WithEvents colCapped As Columns.GridColumn
    Friend WithEvents ColAmount1 As Columns.GridColumn
    'Friend WithEvents colCategory As Columns.GridColumn
    Friend WithEvents colPrNum As Columns.GridColumn
    Friend WithEvents taxesBindingSource As BindingSource
    Friend WithEvents colDescription As Columns.GridColumn
    Friend WithEvents colAmount2 As Columns.GridColumn
    Friend WithEvents GroupControl10 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtEmpNum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtCoName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents slueEmployeeCopyFrom As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView3 As Views.Grid.GridView
    Friend WithEvents colEmpNum2 As Columns.GridColumn
    Friend WithEvents colEmpName2 As Columns.GridColumn
    Friend WithEvents LabelControl13 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnOk As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancelCopyToNewCompany As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents slueCompanyCopyFrom As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView1 As Views.Grid.GridView
    Friend WithEvents colCONUM1 As Columns.GridColumn
    Friend WithEvents GridColumn5 As Columns.GridColumn
    Friend WithEvents GridColumn6 As Columns.GridColumn
    Friend WithEvents popupCopyFrom As DevExpress.XtraEditors.PopupContainerControl
    Friend WithEvents rgDateFilter As DevExpress.XtraEditors.RadioGroup
    Friend WithEvents dePayHistoryFromTo As DevExpress.XtraEditors.DateEdit
    Friend WithEvents dePayHistoryFromDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem2 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lciFromDate As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lciToDate As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents gvDeductions As Views.Grid.GridView
    Friend WithEvents gvPayrollDeductionsAndTaxes As Views.Grid.GridView
    Friend WithEvents colCaption As Columns.GridColumn
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents gcPayrollDeductionsAndTaxes As GridControl
    Friend WithEvents gvERTaxes As Views.Grid.GridView
    Friend WithEvents gvEETaxes As Views.Grid.GridView
    Friend WithEvents riTextEditAmount As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents GridColumn1 As Columns.GridColumn
    Friend WithEvents gvPayHistory1 As Views.Grid.GridView
    Friend WithEvents colCaption2 As Columns.GridColumn
    Friend WithEvents colDescription2 As Columns.GridColumn
    Friend WithEvents colCheckCounter As Columns.GridColumn
End Class
