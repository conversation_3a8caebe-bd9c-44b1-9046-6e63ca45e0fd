﻿Imports System.ComponentModel
Imports System.Configuration
Imports System.Data
Imports System.Environment
Imports DevExpress.Skins
'Imports System.Deployment.Application
Imports DevExpress.XtraEditors
Imports PwdEnc

Public Class frmLogin

    ' TODO: Insert code to perform custom authentication using the provided username and password 
    ' (See http://go.microsoft.com/fwlink/?LinkId=35339).  
    ' The custom principal can then be attached to the current thread's principal as follows: 
    '     My.User.CurrentPrincipal = CustomPrincipal
    ' where CustomPrincipal is the IPrincipal implementation used to perform authentication. 
    ' Subsequently, My.User will return identity information encapsulated in the CustomPrincipal object
    ' such as the username, display name, etc.

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property RetrunLogInResults As Boolean = False
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property LoggedInUserName As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Server As String
    Private Property DownloadDllsTask As Threading.Tasks.Task

    Public Sub New()
        InitializeComponent()
        DevExpress.UserSkins.BonusSkins.Register()
        SkinManager.EnableFormSkins()
        SkinManager.EnableMdiFormSkins()
        SetLookAndFeel()
        WindowsFormsSettings.AnimationMode = AnimationMode.EnableAll
        WindowsFormsSettings.ColumnFilterPopupMode = ColumnFilterPopupMode.Excel
        Logger.Information("Application started on {Date}", DateTime.Now)
    End Sub

    Public Sub New(_RetrunLogInResults As Boolean, _LoggedInUserName As String)
        InitializeComponent()
        RetrunLogInResults = _RetrunLogInResults
        LoggedInUserName = _LoggedInUserName
    End Sub

    Public Function GetClickOnceActivationArguments() As String()
        Dim activationData As New List(Of String)()
        Dim index As Integer = 0

        While True
            Dim envVarName As String = $"ClickOnce_ActivationData_{index}"
            Dim argValue As String = GetEnvironmentVariable(envVarName)

            If String.IsNullOrEmpty(argValue) Then
                ' Stop when the environment variable for this index is empty or not set
                Exit While
            End If

            activationData.Add(argValue)
            index += 1
        End While

        Return activationData.ToArray()
    End Function

    Private Sub frmLogin_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim debugFile As String = "C:\apps\FD_Debug.txt"
        If IO.File.Exists(debugFile) Then
            Try
                Dim content As String = IO.File.ReadAllText(debugFile)
                If Environment.GetCommandLineArgs.Count = 1 AndAlso content.Contains("AllowAttcachDebug: 1") Then
                    Threading.Thread.Sleep(30000)
                ElseIf content.Contains("AllowAttcachDebug: 2") Then
                    Threading.Thread.Sleep(60000)
                End If
            Catch ex As Exception
                Logger.Debug(ex, "Error when getting debug file info")
            End Try
        End If

        '#If DEBUG Then
        '        If System.Net.Dns.GetHostName().ToLower() = "brands116" And LoggedInUserName Is Nothing Then
        '            Dim cs = PwdEncr.DecUI(ConfigurationManager.AppSettings("uidp")).Split(New Char() {"=", ";"}, 4)
        '            UserName = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(1), "")
        '            Password = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(3), "")
        '            Me.PasswordTextBox.Text = Password

        '            'Dim db = New dbEPDataDataContext(GetConnectionString)

        '            'Dim YearEnd As Date = New Date(2022, 12, 31)

        '            'Dim SelectedCals = Enumerable.Range(0, 1).Select(Function(a) (New RunAutoStatus With {.CalID = -1,
        '            '                                               .CoNum = 46,
        '            '                                               .CoName = "abc",
        '            '                                               .Status = "Waiting",
        '            '                                               .CheckDate = New Date(2022, 3, 31),
        '            '                                               .IsYearEnd = True,
        '            '                                               .RunDate = New Date(2022, 3, 31),
        '            '                                               .PrDesc = "Zero Returns"
        '            '                                              }
        '            '                                           )).ToList

        '            'Dim frm = New frmRunAutos With {.RunAutoList = SelectedCals, .YearEndBilling = New frmYearEndDecPayroll}
        '            'Dim results = frm.ShowDialog()


        '            Dim frm1 As Form
        '            frm1 = New frmClientLimits()
        '            frm1.ShowDialog()
        '            frm1 = New frmRiskManagement()
        '            frm1.ShowDialog()
        '            Me.Close()
        '            Application.Exit()

        '        End If
        '#End If

        Dim b As Boolean = False
        '        'will not be fired.  enable to cross out login screen
        If b Then
            Dim cs = PwdEncr.DecUI(ConfigurationManager.AppSettings("uidp")).Split(New Char() {"=", ";"}, 4)
            UserName = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(1), "")
            Password = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(3), "")
            Me.PasswordTextBox.Text = Password
            'Dim frm1 As New frmTestConcurrency()
            'frm1.ShowDialog()

            Dim year? As Decimal
            Dim conumw As Decimal = 51
            Dim db As New dbEPDataDataContext(GetConnectionString)

            ''RabbitRpcCrystal.RpcClientCrystal.Call($"c:\temp\brandslogo.rpt")
            'RabbitRpcCrystal.RpcClassesCrystal.ex()
            'Dim rpcCls As New RabbitRpc.RpcClasses()
            'rpcCls.CalculateManualCheck(CoNum, EmpNum, ChkCounter, pay_freq, check_date, Errors)
            'Dim _AchSettings = Newtonsoft.Json.JsonConvert.DeserializeObject(Of Ach.AchSettings)(GetUdfValue("AchSettingsJson"))
            'Dim _AchEmailService = New Ach.AchEmailService(_AchSettings, True)
            '_AchEmailService.EmailClient(51, "Ach Email", False, "", True, Nothing).GetAwaiter()

            Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
            Dim Errors As String = Nothing
            Dim params As List(Of String) = Nothing
            'Dim status = rpcCls.GetCrystalReportParams($"c:\temp\brandslogo.rpt", params, Errors)
            Dim status = rpcCls.GetCrystalReportParams($"I:\solomon\NewW2EarningsQtrly.rpt", params, Errors)
            Dim st = status



            Dim frm As Form
            frm = New frmTemp
            frm.ShowDialog()
            'Return
            frm = New frmTaxNotices
            frm.ShowDialog()
            frm = New frmGlobalRiskSettings()
            frm.ShowDialog()
            frm = New frmBankingFileTracker()
            frm.ShowDialog()
            frm = New frmClientLimits()
            frm.ShowDialog()
            'frm = New frmDelayDD
            'frm.ShowDialog()
            frm = New frm1059UserRelationships
            frm.ShowDialog()
            frm = New frmPlaid(51, "test")
            frm.ShowDialog()
            'frm = New frmEncrypt()
            'frm.ShowDialog()
            'frm = New frmManualBilling()
            frm = New frmOrderSupplies(51)
            CType(frm, frmOrderSupplies).Company = New COMPANY With {.CONUM = 51, .CO_NAME = "test"}
            frm.ShowDialog()
            frm = New frmAudit()
            frm.ShowDialog()
            frm = New frmCovidEmailNotify()
            frm.Show()
            Me.Close()
            Return
        End If

        If Not LoggedInUserName.IsNullOrWhiteSpace Then
            UsernameTextBox.Enabled = False
            UsernameTextBox.Text = LoggedInUserName
            PasswordTextBox.Focus()
            PasswordTextBox.Select()
            Exit Sub
        End If

        Try
            Me.ShowWaitForm
            Me.Enabled = False
            If System.Diagnostics.Debugger.IsAttached Then
                modGlobals.Version = " Debugging"
            Else
                Dim isNetworkDeployed As Boolean = False
                Dim envValue As String = Environment.GetEnvironmentVariable("ClickOnce_IsNetworkDeployed")
                Boolean.TryParse(envValue, isNetworkDeployed)

                If isNetworkDeployed Then
                    Logger.Information("ApplicationDeployment {IsNetworkDeployed}", isNetworkDeployed)
                    Dim AssemblyVersionClickOnce = GetEnvironmentVariable("ClickOnce_CurrentVersion")
                    Dim AssemblyVersion As New Version(AssemblyVersionClickOnce)
                    modGlobals.Version = " ({0}.{1}.{2}.{3})".FormatWith(AssemblyVersion.Major, AssemblyVersion.Minor, AssemblyVersion.Build, AssemblyVersion.Revision)
                End If
                Logger.Information("MainForm opened, With version {Version}", Version)
            End If

            If My.Settings.EPDATAConnectionString.ToUpper.Contains("SQL910\DEV") Then
                modGlobals.Version += " (dev environment)"
            End If
        Catch ex As Exception
            Me.CloseWaitForm
            DisplayErrorMessage("Error Getting App Version Number", ex)
        Finally
            Me.Enabled = True
            Me.CloseWaitForm
        End Try

        If Not LoggedInUserName.IsNullOrWhiteSpace Then
            UsernameTextBox.Enabled = False
            UsernameTextBox.Text = LoggedInUserName
            PasswordTextBox.Focus()
            PasswordTextBox.Select()
        End If

        Dim CommandLineArgs = Environment.GetCommandLineArgs
        Dim cma As String() = CommandLineArgs.Clone()
        If cma.Length >= 3 Then
            cma(2) = ""
        End If

        Logger.Debug("CommandLineArgs {CommandLineArgs}", cma)
        If CommandLineArgs.Length <= 2 Then
            Try
                Dim Args As New List(Of String)
                Args.Add("Path")

                If CommandLineArgs.Length = 1 Then
                    Dim clickOnceArgs = GetClickOnceActivationArguments()
                    'If AppDomain.CurrentDomain.SetupInformation.ActivationArguments IsNot Nothing Then
                    If clickOnceArgs IsNot Nothing AndAlso clickOnceArgs.Length() <> 0 Then
                        'Dim data = AppDomain.CurrentDomain.SetupInformation.ActivationArguments.ActivationData
                        Dim data = clickOnceArgs
                        Logger.Debug("ActivationData {data}", data)
                        If data IsNot Nothing AndAlso data.Length > 0 Then
                            If data(0).ToLower().StartsWith("moti.e,") Then
                                Args.AddRange(data(0).Split(","))
                                CommandLineArgs = Args.ToArray
                            Else
                                Dim ar = data(0).Split(New String() {"~!~split_me~!~"}, Nothing)
                                Dim cs = PwdEncr.DecUI(ar(0)).Split(New Char() {"=", ";"}, 4)
                                If cs IsNot Nothing AndAlso cs.Length = 4 Then
                                    Args.Add(cs(1))
                                    Args.Add(cs(3))
                                End If

                                If ar.Length > 1 Then
                                    Args.Add(ar(1))
                                End If
                                If ar.Length > 2 Then
                                    Args.Add(ar(2))
                                End If
                            End If

                            CommandLineArgs = Args.ToArray
                        End If
                    End If
                ElseIf CommandLineArgs.Length = 2 Then
                    Dim cs = PwdEncr.DecUI(CommandLineArgs(1)).Split(New Char() {"=", ";"}, 4)
                    If cs IsNot Nothing AndAlso cs.Length = 4 Then
                        Args.Add(cs(1))
                        Args.Add(cs(3))
                    End If
                    CommandLineArgs = Args.ToArray
                End If

                cma = CommandLineArgs.Clone()
                If cma.Length >= 3 Then
                    cma(2) = ""
                End If
                Logger.Debug("Setting CommandLineArgs To {Args}", cma)
            Catch ex As Exception
                Logger.Error(ex, "Error auto logging In")
            End Try
        End If

        modGlobals.CommandLineArgs = CommandLineArgs
        If CommandLineArgs.Length >= 3 Then
            UsernameTextBox.Text = CommandLineArgs(1)
            PasswordTextBox.Text = CommandLineArgs(2)
            OK_ClickAsync(sender, e)
        End If

        Try
            Dim regKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(Application.ProductName)
            Dim regUserName = regKey.GetValue("UserName", String.Empty)
            If regUserName IsNot Nothing AndAlso regUserName.ToString().IsNotNullOrWhiteSpace() Then
                UsernameTextBox.Text = regUserName
                PasswordTextBox.Select()
            End If
        Catch ex As Exception
            DisplayMessageBox("Error loading username from registry")
        End Try

        Try
            Dim a As ExecupayAssemblyResolver = ExecupayAssemblyResolver.Instance
            If a.IsUpdateAvailable Then
                Using frm = New frmExistingProcessList()
                    If Not Environment.MachineName.StartsWith("BRND-AVD") AndAlso frm.GetProcesses.Any Then
                        Logger.Information("Showing frmExistingProcessList")
                        If frm.ShowDialog <> DialogResult.OK Then
                            Close()
                        End If
                    End If
                End Using
                DownloadDllsTask = Threading.Tasks.Task.Run(Sub() a.DownloadLatestDlls())
            Else
                Logger.Information("No Execupay update Is available")
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error downloading required dll's")
        End Try
    End Sub

    Private Async Sub OK_ClickAsync(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLogin.Click
        If String.IsNullOrEmpty(Me.UsernameTextBox.Text.Trim) OrElse String.IsNullOrEmpty(Me.PasswordTextBox.Text.Trim) Then
            Exit Sub
        Else
            If RetrunLogInResults AndAlso LoggedInUserName <> "" Then
                Me.PasswordTextBox.Text = Me.PasswordTextBox.Text.Remove(0, 1)
                If Me.PasswordTextBox.Text.Length > 2 Then Me.PasswordTextBox.Text = Me.PasswordTextBox.Text.Remove(Me.PasswordTextBox.Text.Length - 1, 1)
            End If
            Globals.UserName = Me.UsernameTextBox.Text
            Globals.Password = Me.PasswordTextBox.Text
            Dim Conn As New SqlClient.SqlConnection(GetConnectionString)
            Try
                Me.ShowWaitForm
                Conn.Open()
                Conn.Close()

                ' Try to create the Entity Framework context
                Dim db As dbEPDataDataContext = Nothing
                Try
                    db = New dbEPDataDataContext(GetConnectionString())
                Catch efEx As Exception
                    ' If Entity Framework fails, try clearing the cache and retry once
                    Logger?.Warning(efEx, "Failed to create Entity Framework context during login for user {UserName}. Clearing cache and retrying.", Globals.UserName)
                    Try
                        dbEPDataDataContext.ClearOptionsCache()
                        db = New dbEPDataDataContext(GetConnectionString())
                        Logger?.Information("Successfully created Entity Framework context after clearing cache for user {UserName}", Globals.UserName)
                    Catch retryEx As Exception
                        Logger?.Error(retryEx, "Failed to create Entity Framework context even after clearing cache for user {UserName}", Globals.UserName)
                        ' Don't throw - just skip the user tracking update
                    End Try
                End Try
                ' Update user login tracking if Entity Framework context was created successfully
                If db IsNot Nothing Then
                    Try
                        'Dim fdp1 = db.FrontDeskPermissions.ToList().Where(Function(f) f.UserName = "").FirstOrDefault()
                        'Dim fdp4 = db.FrontDeskPermissions.Where(Function(f) f.UserName <> Nothing AndAlso f.UserName = "").FirstOrDefault()
                        'Dim fdp5 = db.FrontDeskPermissions.Where(Function(f) f.UserName IsNot Nothing AndAlso f.UserName = "").FirstOrDefault()
                        'Dim fdp5b = db.FrontDeskPermissions.Where(Function(f) f.UserName <> Nothing AndAlso f.UserName = "").FirstOrDefault()

                        'Dim fdp4b = db.FrontDeskPermissions.Where(Function(f) f.UserName <> Nothing).FirstOrDefault()
                        'Dim fdp4c = db.FrontDeskPermissions.Where(Function(f) f.UserName IsNot Nothing).FirstOrDefault()
                        'Dim fdp4d = db.FrontDeskPermissions.Where(Function(f) Not f.UserName = Nothing).FirstOrDefault()
                        'Dim fdp5c = db.FrontDeskPermissions.Where(Function(f) Not String.IsNullOrEmpty(f.UserName)).FirstOrDefault()
                        'Dim fdp65 = db.FrontDeskPermissions.Where(Function(f) f.UserName = Nothing AndAlso f.UserName = "").FirstOrDefault()

                        'Dim fdp652 = db.FrontDeskPermissions.Where(Function(f) f.UserName <> "" AndAlso f.UserName = "").FirstOrDefault()
                        'Dim fdp6522a = db.FrontDeskPermissions.Where(Function(f) f.UserName > "b").FirstOrDefault()
                        'Dim fdp6522b = db.FrontDeskPermissions.Where(Function(f) f.UserName > "" AndAlso f.UserName = "").FirstOrDefault()

                        'Dim fdp3 = db.FrontDeskPermissions.Where(Function(f) f.UserName = "").FirstOrDefault()
                        'Dim fdp33 = db.FrontDeskPermissions.Where(Function(f) f.UserName = "solomon.w").FirstOrDefault()

                        'Dim fdp2 = db.FrontDeskPermissions.Where(Function(f) f.UserName = String.Empty).FirstOrDefault()
                        'fdp1 = db.FrontDeskPermissions.Where(Function(f) f.UserName.Equals("")).FirstOrDefault()
                        Dim asd As REPORT
                        Dim fdp = db.FrontDeskPermissions.Where(Function(f) f.UserName = Globals.UserName).FirstOrDefault()
                        If fdp IsNot Nothing Then
                            fdp.LastVersion = Version
                            fdp.LastLogin = DateTime.Now.ToEST()
                            db.SaveChanges()
                        End If
                    Catch trackingEx As Exception
                        ' Log the error but don't prevent login
                        Logger?.Warning(trackingEx, "Failed to update user login tracking for {UserName}", Globals.UserName)
                    End Try
                End If
                If RetrunLogInResults Then
                    Me.DialogResult = System.Windows.Forms.DialogResult.OK
                    Exit Sub
                End If
                'Me.DialogResult = System.Windows.Forms.DialogResult.OK
                SetUpLogger()

                Logger.Information("Successfully logged in on {Date}", DateTime.Now)
                Dim regKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(Application.ProductName)
                regKey.SetValue("UserName", UserName)
                If DownloadDllsTask IsNot Nothing Then Await DownloadDllsTask
                If modGlobals.CommandLineArgs.Count = 4 AndAlso CommandLineArgs(3) = "test" Then
                    Dim frm = New frmTest()
                    frm.Show()
                Else
                    My.Forms.frmMain.Show()
                End If
                Me.Close()
            Catch ex As Exception
                Me.CloseWaitForm
                Application.DoEvents()
                Me.PasswordTextBox.Text = ""
                Me.PasswordTextBox.Select()
                Me.PasswordTextBox.Focus()
                DisplayErrorMessage("Login Error", ex)
            Finally
                Me.Enabled = True
                Me.CloseWaitForm
            End Try
        End If
    End Sub

    Private Sub Cancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Public Function ShowLogIn(Optional BlankUser As Boolean = False) As DialogResult
        Dim UserNameToPass = If(BlankUser, "", UserName)
        Dim result = New frmLogin(True, UserNameToPass).ShowDialog()

        If Not BlankUser AndAlso Permissions?.LookAndFeel.IsNotNullOrWhiteSpace Then
            DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(Permissions.LookAndFeel)
        End If
        Return result
    End Function

    Public Sub SetLookAndFeel()
        Try
            Dim regKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(Application.ProductName)
            Dim regUserLookAndFeel = regKey.GetValue("UserLookAndFeel", "Lilian")
            Dim regSkinSvgPalette = regKey.GetValue("SkinSvgPalette", "")
            DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(regUserLookAndFeel, regSkinSvgPalette)
        Catch ex As Exception
            DisplayMessageBox("Error loading username from registry")
        End Try
    End Sub
End Class
