﻿Imports System.ComponentModel
Public Class frmCalendarLinkToPayroll

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CalRecord As view_Calendar

    Dim DB As dbEPDataDataContext

    Private Sub frmCalendarLinkToPayroll_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.View_CalendarBindingSource.DataSource = CalRecord

        DB = New dbEPDataDataContext(GetConnectionString)
        Dim Payrolls = DB.prc_GetPayrollsNotAttachedToCalenders(CalRecord.CoNum).ToList

        Me.PayrollBindingSource.DataSource = Payrolls
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        If Me.gridViewPayrolls.FocusedRowHandle < 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("No Payroll Selected", "", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        Dim SelectedRow As prc_GetPayrollsNotAttachedToCalendersResult = Me.gridViewPayrolls.GetRow(Me.gridViewPayrolls.FocusedRowHandle)
        Dim Msg = "You are about to link the above calendar to payroll #: " & SelectedRow.PRNUM & vbCrLf &
                    vbCrLf & "Calendar Check Date: " & CalRecord.CheckDate.Value.ToShortDateString &
                    vbCrLf & "Payroll Check Date: " & SelectedRow.CHECK_DATE & vbCrLf & vbCrLf
        If SelectedRow.cal_id.HasValue Then
            Msg &= "Payroll is currently linked to another calendar, make sure you want to override." & vbCrLf & vbCrLf
        End If
        Msg &= "Continue?"
        If DevExpress.XtraEditors.XtraMessageBox.Show(Msg, "Confirm Link", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes Then
            Dim CalEnt As CALENDAR
            If SelectedRow.cal_id.HasValue Then
                CalEnt = (From A In DB.CALENDARs Where A.cal_id = SelectedRow.cal_id AndAlso A.payroll_num = SelectedRow.PRNUM).Single
                CalEnt.payroll_num = Nothing
                CalEnt.completed = "NO"
            End If
            CalEnt = (From A In DB.CALENDARs Where A.cal_id = CalRecord.CalID).Single
            CalEnt.payroll_num = SelectedRow.PRNUM
            CalEnt.completed = "YES"
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save calendar link due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
    End Sub
End Class