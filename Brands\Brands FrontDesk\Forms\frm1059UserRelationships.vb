﻿Public Class frm1059UserRelationships
    Private DB As dbEPDataDataContext
    Private Sub frm1059UserRelationships_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DB = New dbEPDataDataContext(GetConnectionString)
        LoadData()
    End Sub

    Sub LoadData()
        Try
            GridControl1.DataSource = DB._1059UsersRealtionships.ToList()
            Dim readOnlyCol = GridView1.Columns.Where(Function(f) f.FieldName = "ID" OrElse f.FieldName = "EmpNum").ToList()

            For Each col In readOnlyCol
                col.OptionsColumn.ReadOnly = True
            Next

        Catch ex As Exception
            DisplayErrorMessage("Error in load data", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save changes due to a conflict with another user's changes. Please refresh and try again.", New Exception("Concurrency conflict"))
                Return
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in save", ex)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub
End Class