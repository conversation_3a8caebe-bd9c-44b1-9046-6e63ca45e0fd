﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAudit
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.chkToday = New DevExpress.XtraEditors.CheckEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.lueTables = New DevExpress.XtraEditors.LookUpEdit()
        Me.TablePanel1 = New DevExpress.Utils.Layout.TablePanel()
        Me.txtModifiedBy = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.ceModBy = New DevExpress.XtraEditors.CheckEdit()
        Me.SpinEditRows = New DevExpress.XtraEditors.SpinEdit()
        Me.lcFilter10 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter9 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter8 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter7 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter6 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter5 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter3 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter2 = New DevExpress.XtraEditors.LabelControl()
        Me.lcFilter1 = New DevExpress.XtraEditors.LabelControl()
        Me.lueValue10 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue9 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue8 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue7 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue6 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue5 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue4 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue3 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue2 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueValue1 = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueActionFilter = New DevExpress.XtraEditors.LookUpEdit()
        Me.lueColumn = New DevExpress.XtraEditors.LookUpEdit()
        Me.txtAppName = New DevExpress.XtraEditors.TextEdit()
        Me.txtHostName = New DevExpress.XtraEditors.TextEdit()
        Me.deDateTo = New DevExpress.XtraEditors.DateEdit()
        Me.deDateFrom = New DevExpress.XtraEditors.DateEdit()
        Me.ceFilter10 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter9 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter8 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter7 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter6 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter5 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter4 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter3 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceFilter1 = New DevExpress.XtraEditors.CheckEdit()
        Me.lcFilter4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.lueColumnFilter = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.lblHostName = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ceFilter2 = New DevExpress.XtraEditors.CheckEdit()
        Me.ceRows = New DevExpress.XtraEditors.CheckEdit()
        Me.ceAction = New DevExpress.XtraEditors.CheckEdit()
        Me.ceColumn = New DevExpress.XtraEditors.CheckEdit()
        Me.ceAppName = New DevExpress.XtraEditors.CheckEdit()
        Me.ceHost = New DevExpress.XtraEditors.CheckEdit()
        Me.cdDateTo = New DevExpress.XtraEditors.CheckEdit()
        Me.ceDateFrom = New DevExpress.XtraEditors.CheckEdit()
        Me.btnSearch = New DevExpress.XtraEditors.SimpleButton()
        Me.btnExport = New DevExpress.XtraEditors.SimpleButton()
        Me.btnApply = New DevExpress.XtraEditors.SimpleButton()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.SimpleSeparator1 = New DevExpress.XtraLayout.SimpleSeparator()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem11 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lciSearch = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciTableChoose = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciGrid = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem12 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciApply = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem8 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lciExport = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.chkToday.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueTables.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TablePanel1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TablePanel1.SuspendLayout()
        CType(Me.txtModifiedBy.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceModBy.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SpinEditRows.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue10.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue9.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue8.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue7.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueValue1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueActionFilter.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueColumn.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtAppName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHostName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deDateTo.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deDateTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deDateFrom.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deDateFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter10.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter9.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter8.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter7.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceFilter2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceRows.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceAction.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceColumn.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceAppName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceHost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cdDateTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceDateFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleSeparator1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciSearch, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciTableChoose, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciApply, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciExport, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.chkToday)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Controls.Add(Me.lueTables)
        Me.LayoutControl1.Controls.Add(Me.TablePanel1)
        Me.LayoutControl1.Controls.Add(Me.btnSearch)
        Me.LayoutControl1.Controls.Add(Me.btnExport)
        Me.LayoutControl1.Controls.Add(Me.btnApply)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(343, 4, 650, 400)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1620, 698)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'chkToday
        '
        Me.chkToday.Location = New System.Drawing.Point(181, 36)
        Me.chkToday.Name = "chkToday"
        Me.chkToday.Properties.Caption = "Today"
        Me.chkToday.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkToday.Size = New System.Drawing.Size(77, 20)
        Me.chkToday.StyleController = Me.LayoutControl1
        Me.chkToday.TabIndex = 21
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(360, 13)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(1220, 622)
        Me.GridControl1.TabIndex = 18
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.ReadOnly = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'lueTables
        '
        Me.lueTables.Location = New System.Drawing.Point(87, 12)
        Me.lueTables.Name = "lueTables"
        Me.lueTables.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueTables.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("object_id", "object_id", 20, DevExpress.Utils.FormatType.None, "", False, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("TableName", "TableName")})
        Me.lueTables.Properties.NullText = ""
        Me.lueTables.Size = New System.Drawing.Size(259, 20)
        Me.lueTables.StyleController = Me.LayoutControl1
        Me.lueTables.TabIndex = 17
        '
        'TablePanel1
        '
        Me.TablePanel1.Columns.AddRange(New DevExpress.Utils.Layout.TablePanelColumn() {New DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 6.0!), New DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 26.0!), New DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Relative, 60.14!)})
        Me.TablePanel1.Controls.Add(Me.txtModifiedBy)
        Me.TablePanel1.Controls.Add(Me.LabelControl6)
        Me.TablePanel1.Controls.Add(Me.ceModBy)
        Me.TablePanel1.Controls.Add(Me.SpinEditRows)
        Me.TablePanel1.Controls.Add(Me.lcFilter10)
        Me.TablePanel1.Controls.Add(Me.lcFilter9)
        Me.TablePanel1.Controls.Add(Me.lcFilter8)
        Me.TablePanel1.Controls.Add(Me.lcFilter7)
        Me.TablePanel1.Controls.Add(Me.lcFilter6)
        Me.TablePanel1.Controls.Add(Me.lcFilter5)
        Me.TablePanel1.Controls.Add(Me.lcFilter3)
        Me.TablePanel1.Controls.Add(Me.lcFilter2)
        Me.TablePanel1.Controls.Add(Me.lcFilter1)
        Me.TablePanel1.Controls.Add(Me.lueValue10)
        Me.TablePanel1.Controls.Add(Me.lueValue9)
        Me.TablePanel1.Controls.Add(Me.lueValue8)
        Me.TablePanel1.Controls.Add(Me.lueValue7)
        Me.TablePanel1.Controls.Add(Me.lueValue6)
        Me.TablePanel1.Controls.Add(Me.lueValue5)
        Me.TablePanel1.Controls.Add(Me.lueValue4)
        Me.TablePanel1.Controls.Add(Me.lueValue3)
        Me.TablePanel1.Controls.Add(Me.lueValue2)
        Me.TablePanel1.Controls.Add(Me.lueValue1)
        Me.TablePanel1.Controls.Add(Me.lueActionFilter)
        Me.TablePanel1.Controls.Add(Me.lueColumn)
        Me.TablePanel1.Controls.Add(Me.txtAppName)
        Me.TablePanel1.Controls.Add(Me.txtHostName)
        Me.TablePanel1.Controls.Add(Me.deDateTo)
        Me.TablePanel1.Controls.Add(Me.deDateFrom)
        Me.TablePanel1.Controls.Add(Me.ceFilter10)
        Me.TablePanel1.Controls.Add(Me.ceFilter9)
        Me.TablePanel1.Controls.Add(Me.ceFilter8)
        Me.TablePanel1.Controls.Add(Me.ceFilter7)
        Me.TablePanel1.Controls.Add(Me.ceFilter6)
        Me.TablePanel1.Controls.Add(Me.ceFilter5)
        Me.TablePanel1.Controls.Add(Me.ceFilter4)
        Me.TablePanel1.Controls.Add(Me.ceFilter3)
        Me.TablePanel1.Controls.Add(Me.ceFilter1)
        Me.TablePanel1.Controls.Add(Me.lcFilter4)
        Me.TablePanel1.Controls.Add(Me.LabelControl10)
        Me.TablePanel1.Controls.Add(Me.LabelControl9)
        Me.TablePanel1.Controls.Add(Me.lueColumnFilter)
        Me.TablePanel1.Controls.Add(Me.LabelControl7)
        Me.TablePanel1.Controls.Add(Me.lblHostName)
        Me.TablePanel1.Controls.Add(Me.LabelControl5)
        Me.TablePanel1.Controls.Add(Me.LabelControl4)
        Me.TablePanel1.Controls.Add(Me.LabelControl3)
        Me.TablePanel1.Controls.Add(Me.LabelControl2)
        Me.TablePanel1.Controls.Add(Me.LabelControl1)
        Me.TablePanel1.Controls.Add(Me.ceFilter2)
        Me.TablePanel1.Controls.Add(Me.ceRows)
        Me.TablePanel1.Controls.Add(Me.ceAction)
        Me.TablePanel1.Controls.Add(Me.ceColumn)
        Me.TablePanel1.Controls.Add(Me.ceAppName)
        Me.TablePanel1.Controls.Add(Me.ceHost)
        Me.TablePanel1.Controls.Add(Me.cdDateTo)
        Me.TablePanel1.Controls.Add(Me.ceDateFrom)
        Me.TablePanel1.Location = New System.Drawing.Point(12, 139)
        Me.TablePanel1.Name = "TablePanel1"
        Me.TablePanel1.Rows.AddRange(New DevExpress.Utils.Layout.TablePanelRow() {New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!), New DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26.0!)})
        Me.TablePanel1.Size = New System.Drawing.Size(334, 496)
        Me.TablePanel1.TabIndex = 12
        '
        'txtModifiedBy
        '
        Me.TablePanel1.SetColumn(Me.txtModifiedBy, 2)
        Me.txtModifiedBy.Location = New System.Drawing.Point(119, 133)
        Me.txtModifiedBy.Name = "txtModifiedBy"
        Me.TablePanel1.SetRow(Me.txtModifiedBy, 5)
        Me.txtModifiedBy.Size = New System.Drawing.Size(212, 20)
        Me.txtModifiedBy.TabIndex = 57
        '
        'LabelControl6
        '
        Me.LabelControl6.Appearance.Options.UseTextOptions = True
        Me.LabelControl6.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl6.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl6, 1)
        Me.LabelControl6.Location = New System.Drawing.Point(25, 134)
        Me.LabelControl6.Name = "LabelControl6"
        Me.TablePanel1.SetRow(Me.LabelControl6, 5)
        Me.LabelControl6.Size = New System.Drawing.Size(88, 17)
        Me.LabelControl6.TabIndex = 56
        Me.LabelControl6.Text = "Modified by:"
        '
        'ceModBy
        '
        Me.TablePanel1.SetColumn(Me.ceModBy, 0)
        Me.ceModBy.Location = New System.Drawing.Point(3, 133)
        Me.ceModBy.Name = "ceModBy"
        Me.ceModBy.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceModBy, 5)
        Me.ceModBy.Size = New System.Drawing.Size(16, 20)
        Me.ceModBy.TabIndex = 55
        '
        'SpinEditRows
        '
        Me.TablePanel1.SetColumn(Me.SpinEditRows, 2)
        Me.SpinEditRows.EditValue = New Decimal(New Integer() {1000, 0, 0, 0})
        Me.SpinEditRows.Location = New System.Drawing.Point(119, 211)
        Me.SpinEditRows.Name = "SpinEditRows"
        Me.SpinEditRows.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.SpinEditRows.Properties.IsFloatValue = False
        Me.SpinEditRows.Properties.Mask.EditMask = "N00"
        Me.SpinEditRows.Properties.MaxValue = New Decimal(New Integer() {5000, 0, 0, 0})
        Me.SpinEditRows.Properties.MinValue = New Decimal(New Integer() {1, 0, 0, 0})
        Me.TablePanel1.SetRow(Me.SpinEditRows, 8)
        Me.SpinEditRows.Size = New System.Drawing.Size(212, 20)
        Me.SpinEditRows.TabIndex = 54
        '
        'lcFilter10
        '
        Me.lcFilter10.Appearance.Options.UseTextOptions = True
        Me.lcFilter10.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter10, 1)
        Me.lcFilter10.Location = New System.Drawing.Point(25, 473)
        Me.lcFilter10.Name = "lcFilter10"
        Me.TablePanel1.SetRow(Me.lcFilter10, 18)
        Me.lcFilter10.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter10.TabIndex = 53
        Me.lcFilter10.Text = "Filter By"
        Me.lcFilter10.Visible = False
        '
        'lcFilter9
        '
        Me.lcFilter9.Appearance.Options.UseTextOptions = True
        Me.lcFilter9.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter9, 1)
        Me.lcFilter9.Location = New System.Drawing.Point(25, 447)
        Me.lcFilter9.Name = "lcFilter9"
        Me.TablePanel1.SetRow(Me.lcFilter9, 17)
        Me.lcFilter9.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter9.TabIndex = 52
        Me.lcFilter9.Text = "Filter By"
        Me.lcFilter9.Visible = False
        '
        'lcFilter8
        '
        Me.lcFilter8.Appearance.Options.UseTextOptions = True
        Me.lcFilter8.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter8.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter8, 1)
        Me.lcFilter8.Location = New System.Drawing.Point(25, 421)
        Me.lcFilter8.Name = "lcFilter8"
        Me.TablePanel1.SetRow(Me.lcFilter8, 16)
        Me.lcFilter8.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter8.TabIndex = 51
        Me.lcFilter8.Text = "Filter By"
        Me.lcFilter8.Visible = False
        '
        'lcFilter7
        '
        Me.lcFilter7.Appearance.Options.UseTextOptions = True
        Me.lcFilter7.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter7, 1)
        Me.lcFilter7.Location = New System.Drawing.Point(25, 395)
        Me.lcFilter7.Name = "lcFilter7"
        Me.TablePanel1.SetRow(Me.lcFilter7, 15)
        Me.lcFilter7.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter7.TabIndex = 50
        Me.lcFilter7.Text = "Filter By"
        Me.lcFilter7.Visible = False
        '
        'lcFilter6
        '
        Me.lcFilter6.Appearance.Options.UseTextOptions = True
        Me.lcFilter6.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter6.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter6, 1)
        Me.lcFilter6.Location = New System.Drawing.Point(25, 369)
        Me.lcFilter6.Name = "lcFilter6"
        Me.TablePanel1.SetRow(Me.lcFilter6, 14)
        Me.lcFilter6.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter6.TabIndex = 49
        Me.lcFilter6.Text = "Filter By"
        Me.lcFilter6.Visible = False
        '
        'lcFilter5
        '
        Me.lcFilter5.Appearance.Options.UseTextOptions = True
        Me.lcFilter5.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter5, 1)
        Me.lcFilter5.Location = New System.Drawing.Point(25, 343)
        Me.lcFilter5.Name = "lcFilter5"
        Me.TablePanel1.SetRow(Me.lcFilter5, 13)
        Me.lcFilter5.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter5.TabIndex = 48
        Me.lcFilter5.Text = "Filter By"
        Me.lcFilter5.Visible = False
        '
        'lcFilter3
        '
        Me.lcFilter3.Appearance.Options.UseTextOptions = True
        Me.lcFilter3.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter3, 1)
        Me.lcFilter3.Location = New System.Drawing.Point(25, 291)
        Me.lcFilter3.Name = "lcFilter3"
        Me.TablePanel1.SetRow(Me.lcFilter3, 11)
        Me.lcFilter3.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter3.TabIndex = 47
        Me.lcFilter3.Text = "Filter By"
        Me.lcFilter3.Visible = False
        '
        'lcFilter2
        '
        Me.lcFilter2.Appearance.Options.UseTextOptions = True
        Me.lcFilter2.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter2, 1)
        Me.lcFilter2.Location = New System.Drawing.Point(25, 265)
        Me.lcFilter2.Name = "lcFilter2"
        Me.TablePanel1.SetRow(Me.lcFilter2, 10)
        Me.lcFilter2.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter2.TabIndex = 46
        Me.lcFilter2.Text = "Filter By"
        Me.lcFilter2.Visible = False
        '
        'lcFilter1
        '
        Me.lcFilter1.Appearance.Options.UseTextOptions = True
        Me.lcFilter1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter1, 1)
        Me.lcFilter1.Location = New System.Drawing.Point(25, 239)
        Me.lcFilter1.Name = "lcFilter1"
        Me.TablePanel1.SetRow(Me.lcFilter1, 9)
        Me.lcFilter1.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter1.TabIndex = 45
        Me.lcFilter1.Text = "Filter By"
        Me.lcFilter1.Visible = False
        '
        'lueValue10
        '
        Me.TablePanel1.SetColumn(Me.lueValue10, 2)
        Me.lueValue10.Location = New System.Drawing.Point(119, 471)
        Me.lueValue10.Name = "lueValue10"
        Me.lueValue10.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue10.Properties.NullText = ""
        Me.lueValue10.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue10, 18)
        Me.lueValue10.Size = New System.Drawing.Size(212, 20)
        Me.lueValue10.TabIndex = 44
        Me.lueValue10.Visible = False
        '
        'lueValue9
        '
        Me.TablePanel1.SetColumn(Me.lueValue9, 2)
        Me.lueValue9.Location = New System.Drawing.Point(119, 445)
        Me.lueValue9.Name = "lueValue9"
        Me.lueValue9.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue9.Properties.NullText = ""
        Me.lueValue9.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue9, 17)
        Me.lueValue9.Size = New System.Drawing.Size(212, 20)
        Me.lueValue9.TabIndex = 43
        Me.lueValue9.Visible = False
        '
        'lueValue8
        '
        Me.TablePanel1.SetColumn(Me.lueValue8, 2)
        Me.lueValue8.Location = New System.Drawing.Point(119, 419)
        Me.lueValue8.Name = "lueValue8"
        Me.lueValue8.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue8.Properties.NullText = ""
        Me.lueValue8.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue8, 16)
        Me.lueValue8.Size = New System.Drawing.Size(212, 20)
        Me.lueValue8.TabIndex = 42
        Me.lueValue8.Visible = False
        '
        'lueValue7
        '
        Me.TablePanel1.SetColumn(Me.lueValue7, 2)
        Me.lueValue7.Location = New System.Drawing.Point(119, 393)
        Me.lueValue7.Name = "lueValue7"
        Me.lueValue7.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue7.Properties.NullText = ""
        Me.lueValue7.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue7, 15)
        Me.lueValue7.Size = New System.Drawing.Size(212, 20)
        Me.lueValue7.TabIndex = 41
        Me.lueValue7.Visible = False
        '
        'lueValue6
        '
        Me.TablePanel1.SetColumn(Me.lueValue6, 2)
        Me.lueValue6.Location = New System.Drawing.Point(119, 367)
        Me.lueValue6.Name = "lueValue6"
        Me.lueValue6.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue6.Properties.NullText = ""
        Me.lueValue6.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue6, 14)
        Me.lueValue6.Size = New System.Drawing.Size(212, 20)
        Me.lueValue6.TabIndex = 40
        Me.lueValue6.Visible = False
        '
        'lueValue5
        '
        Me.TablePanel1.SetColumn(Me.lueValue5, 2)
        Me.lueValue5.Location = New System.Drawing.Point(119, 341)
        Me.lueValue5.Name = "lueValue5"
        Me.lueValue5.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue5.Properties.NullText = ""
        Me.lueValue5.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue5, 13)
        Me.lueValue5.Size = New System.Drawing.Size(212, 20)
        Me.lueValue5.TabIndex = 39
        Me.lueValue5.Visible = False
        '
        'lueValue4
        '
        Me.TablePanel1.SetColumn(Me.lueValue4, 2)
        Me.lueValue4.Location = New System.Drawing.Point(119, 315)
        Me.lueValue4.Name = "lueValue4"
        Me.lueValue4.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue4.Properties.NullText = ""
        Me.lueValue4.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue4, 12)
        Me.lueValue4.Size = New System.Drawing.Size(212, 20)
        Me.lueValue4.TabIndex = 38
        Me.lueValue4.Visible = False
        '
        'lueValue3
        '
        Me.TablePanel1.SetColumn(Me.lueValue3, 2)
        Me.lueValue3.Location = New System.Drawing.Point(119, 289)
        Me.lueValue3.Name = "lueValue3"
        Me.lueValue3.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue3.Properties.NullText = ""
        Me.lueValue3.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue3, 11)
        Me.lueValue3.Size = New System.Drawing.Size(212, 20)
        Me.lueValue3.TabIndex = 37
        Me.lueValue3.Visible = False
        '
        'lueValue2
        '
        Me.TablePanel1.SetColumn(Me.lueValue2, 2)
        Me.lueValue2.Location = New System.Drawing.Point(119, 263)
        Me.lueValue2.Name = "lueValue2"
        Me.lueValue2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue2.Properties.NullText = ""
        Me.lueValue2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue2, 10)
        Me.lueValue2.Size = New System.Drawing.Size(212, 20)
        Me.lueValue2.TabIndex = 36
        Me.lueValue2.Visible = False
        '
        'lueValue1
        '
        Me.TablePanel1.SetColumn(Me.lueValue1, 2)
        Me.lueValue1.Location = New System.Drawing.Point(119, 237)
        Me.lueValue1.Name = "lueValue1"
        Me.lueValue1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueValue1.Properties.NullText = ""
        Me.lueValue1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.TablePanel1.SetRow(Me.lueValue1, 9)
        Me.lueValue1.Size = New System.Drawing.Size(212, 20)
        Me.lueValue1.TabIndex = 35
        Me.lueValue1.Visible = False
        '
        'lueActionFilter
        '
        Me.TablePanel1.SetColumn(Me.lueActionFilter, 2)
        Me.lueActionFilter.Location = New System.Drawing.Point(119, 185)
        Me.lueActionFilter.Name = "lueActionFilter"
        Me.lueActionFilter.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueActionFilter.Properties.NullText = ""
        Me.TablePanel1.SetRow(Me.lueActionFilter, 7)
        Me.lueActionFilter.Size = New System.Drawing.Size(212, 20)
        Me.lueActionFilter.TabIndex = 33
        '
        'lueColumn
        '
        Me.TablePanel1.SetColumn(Me.lueColumn, 2)
        Me.lueColumn.EditValue = ""
        Me.lueColumn.Location = New System.Drawing.Point(119, 159)
        Me.lueColumn.Name = "lueColumn"
        Me.lueColumn.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueColumn.Properties.NullText = ""
        Me.TablePanel1.SetRow(Me.lueColumn, 6)
        Me.lueColumn.Size = New System.Drawing.Size(212, 20)
        Me.lueColumn.TabIndex = 32
        '
        'txtAppName
        '
        Me.TablePanel1.SetColumn(Me.txtAppName, 2)
        Me.txtAppName.Location = New System.Drawing.Point(119, 107)
        Me.txtAppName.Name = "txtAppName"
        Me.TablePanel1.SetRow(Me.txtAppName, 4)
        Me.txtAppName.Size = New System.Drawing.Size(212, 20)
        Me.txtAppName.TabIndex = 31
        '
        'txtHostName
        '
        Me.TablePanel1.SetColumn(Me.txtHostName, 2)
        Me.txtHostName.Location = New System.Drawing.Point(119, 81)
        Me.txtHostName.Name = "txtHostName"
        Me.TablePanel1.SetRow(Me.txtHostName, 3)
        Me.txtHostName.Size = New System.Drawing.Size(212, 20)
        Me.txtHostName.TabIndex = 30
        '
        'deDateTo
        '
        Me.TablePanel1.SetColumn(Me.deDateTo, 2)
        Me.deDateTo.EditValue = Nothing
        Me.deDateTo.Location = New System.Drawing.Point(119, 55)
        Me.deDateTo.Name = "deDateTo"
        Me.deDateTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deDateTo.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TablePanel1.SetRow(Me.deDateTo, 2)
        Me.deDateTo.Size = New System.Drawing.Size(212, 20)
        Me.deDateTo.TabIndex = 29
        '
        'deDateFrom
        '
        Me.TablePanel1.SetColumn(Me.deDateFrom, 2)
        Me.deDateFrom.EditValue = Nothing
        Me.deDateFrom.Location = New System.Drawing.Point(119, 29)
        Me.deDateFrom.Name = "deDateFrom"
        Me.deDateFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deDateFrom.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TablePanel1.SetRow(Me.deDateFrom, 1)
        Me.deDateFrom.Size = New System.Drawing.Size(212, 20)
        Me.deDateFrom.TabIndex = 28
        '
        'ceFilter10
        '
        Me.TablePanel1.SetColumn(Me.ceFilter10, 0)
        Me.ceFilter10.Location = New System.Drawing.Point(3, 471)
        Me.ceFilter10.Name = "ceFilter10"
        Me.ceFilter10.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter10, 18)
        Me.ceFilter10.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter10.TabIndex = 27
        Me.ceFilter10.Visible = False
        '
        'ceFilter9
        '
        Me.TablePanel1.SetColumn(Me.ceFilter9, 0)
        Me.ceFilter9.Location = New System.Drawing.Point(3, 445)
        Me.ceFilter9.Name = "ceFilter9"
        Me.ceFilter9.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter9, 17)
        Me.ceFilter9.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter9.TabIndex = 26
        Me.ceFilter9.Visible = False
        '
        'ceFilter8
        '
        Me.TablePanel1.SetColumn(Me.ceFilter8, 0)
        Me.ceFilter8.Location = New System.Drawing.Point(3, 419)
        Me.ceFilter8.Name = "ceFilter8"
        Me.ceFilter8.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter8, 16)
        Me.ceFilter8.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter8.TabIndex = 25
        Me.ceFilter8.Visible = False
        '
        'ceFilter7
        '
        Me.TablePanel1.SetColumn(Me.ceFilter7, 0)
        Me.ceFilter7.Location = New System.Drawing.Point(3, 393)
        Me.ceFilter7.Name = "ceFilter7"
        Me.ceFilter7.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter7, 15)
        Me.ceFilter7.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter7.TabIndex = 24
        Me.ceFilter7.Visible = False
        '
        'ceFilter6
        '
        Me.TablePanel1.SetColumn(Me.ceFilter6, 0)
        Me.ceFilter6.Location = New System.Drawing.Point(3, 367)
        Me.ceFilter6.Name = "ceFilter6"
        Me.ceFilter6.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter6, 14)
        Me.ceFilter6.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter6.TabIndex = 23
        Me.ceFilter6.Visible = False
        '
        'ceFilter5
        '
        Me.TablePanel1.SetColumn(Me.ceFilter5, 0)
        Me.ceFilter5.Location = New System.Drawing.Point(3, 341)
        Me.ceFilter5.Name = "ceFilter5"
        Me.ceFilter5.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter5, 13)
        Me.ceFilter5.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter5.TabIndex = 22
        Me.ceFilter5.Visible = False
        '
        'ceFilter4
        '
        Me.TablePanel1.SetColumn(Me.ceFilter4, 0)
        Me.ceFilter4.Location = New System.Drawing.Point(3, 315)
        Me.ceFilter4.Name = "ceFilter4"
        Me.ceFilter4.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter4, 12)
        Me.ceFilter4.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter4.TabIndex = 21
        Me.ceFilter4.Visible = False
        '
        'ceFilter3
        '
        Me.TablePanel1.SetColumn(Me.ceFilter3, 0)
        Me.ceFilter3.Location = New System.Drawing.Point(3, 289)
        Me.ceFilter3.Name = "ceFilter3"
        Me.ceFilter3.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter3, 11)
        Me.ceFilter3.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter3.TabIndex = 20
        Me.ceFilter3.Visible = False
        '
        'ceFilter1
        '
        Me.TablePanel1.SetColumn(Me.ceFilter1, 0)
        Me.ceFilter1.Location = New System.Drawing.Point(3, 237)
        Me.ceFilter1.Name = "ceFilter1"
        Me.ceFilter1.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter1, 9)
        Me.ceFilter1.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter1.TabIndex = 19
        Me.ceFilter1.Visible = False
        '
        'lcFilter4
        '
        Me.lcFilter4.Appearance.Options.UseTextOptions = True
        Me.lcFilter4.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcFilter4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lcFilter4, 1)
        Me.lcFilter4.Location = New System.Drawing.Point(25, 317)
        Me.lcFilter4.Name = "lcFilter4"
        Me.TablePanel1.SetRow(Me.lcFilter4, 12)
        Me.lcFilter4.Size = New System.Drawing.Size(88, 14)
        Me.lcFilter4.TabIndex = 18
        Me.lcFilter4.Text = "Filter By"
        Me.lcFilter4.Visible = False
        '
        'LabelControl10
        '
        Me.LabelControl10.Appearance.Options.UseTextOptions = True
        Me.LabelControl10.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl10, 1)
        Me.LabelControl10.Location = New System.Drawing.Point(25, 213)
        Me.LabelControl10.Name = "LabelControl10"
        Me.TablePanel1.SetRow(Me.LabelControl10, 8)
        Me.LabelControl10.Size = New System.Drawing.Size(88, 14)
        Me.LabelControl10.TabIndex = 17
        Me.LabelControl10.Text = "Max Rows:"
        '
        'LabelControl9
        '
        Me.LabelControl9.Appearance.Options.UseTextOptions = True
        Me.LabelControl9.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl9, 1)
        Me.LabelControl9.Location = New System.Drawing.Point(25, 187)
        Me.LabelControl9.Name = "LabelControl9"
        Me.TablePanel1.SetRow(Me.LabelControl9, 7)
        Me.LabelControl9.Size = New System.Drawing.Size(88, 14)
        Me.LabelControl9.TabIndex = 16
        Me.LabelControl9.Text = "Action type:"
        '
        'lueColumnFilter
        '
        Me.lueColumnFilter.Appearance.Options.UseTextOptions = True
        Me.lueColumnFilter.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lueColumnFilter.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lueColumnFilter, 1)
        Me.lueColumnFilter.Location = New System.Drawing.Point(25, 161)
        Me.lueColumnFilter.Name = "lueColumnFilter"
        Me.TablePanel1.SetRow(Me.lueColumnFilter, 6)
        Me.lueColumnFilter.Size = New System.Drawing.Size(88, 14)
        Me.lueColumnFilter.TabIndex = 15
        Me.lueColumnFilter.Text = "Column name:"
        '
        'LabelControl7
        '
        Me.LabelControl7.Appearance.Options.UseTextOptions = True
        Me.LabelControl7.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl7, 1)
        Me.LabelControl7.Location = New System.Drawing.Point(25, 109)
        Me.LabelControl7.Name = "LabelControl7"
        Me.TablePanel1.SetRow(Me.LabelControl7, 4)
        Me.LabelControl7.Size = New System.Drawing.Size(88, 14)
        Me.LabelControl7.TabIndex = 14
        Me.LabelControl7.Text = "App name:"
        '
        'lblHostName
        '
        Me.lblHostName.Appearance.Options.UseTextOptions = True
        Me.lblHostName.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lblHostName.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.lblHostName, 1)
        Me.lblHostName.Location = New System.Drawing.Point(25, 83)
        Me.lblHostName.Name = "lblHostName"
        Me.TablePanel1.SetRow(Me.lblHostName, 3)
        Me.lblHostName.Size = New System.Drawing.Size(88, 14)
        Me.lblHostName.TabIndex = 13
        Me.lblHostName.Text = "Host name:"
        '
        'LabelControl5
        '
        Me.LabelControl5.Appearance.Options.UseTextOptions = True
        Me.LabelControl5.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl5, 1)
        Me.LabelControl5.Location = New System.Drawing.Point(25, 57)
        Me.LabelControl5.Name = "LabelControl5"
        Me.TablePanel1.SetRow(Me.LabelControl5, 2)
        Me.LabelControl5.Size = New System.Drawing.Size(88, 14)
        Me.LabelControl5.TabIndex = 12
        Me.LabelControl5.Text = "Date Modify To:"
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Options.UseTextOptions = True
        Me.LabelControl4.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl4, 1)
        Me.LabelControl4.Location = New System.Drawing.Point(25, 31)
        Me.LabelControl4.Name = "LabelControl4"
        Me.TablePanel1.SetRow(Me.LabelControl4, 1)
        Me.LabelControl4.Size = New System.Drawing.Size(88, 14)
        Me.LabelControl4.TabIndex = 11
        Me.LabelControl4.Text = "Date Modify From:"
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Options.UseTextOptions = True
        Me.LabelControl3.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl3, 2)
        Me.LabelControl3.Location = New System.Drawing.Point(119, 6)
        Me.LabelControl3.Name = "LabelControl3"
        Me.TablePanel1.SetRow(Me.LabelControl3, 0)
        Me.LabelControl3.Size = New System.Drawing.Size(212, 14)
        Me.LabelControl3.TabIndex = 10
        Me.LabelControl3.Text = "Value"
        '
        'LabelControl2
        '
        Me.TablePanel1.SetColumn(Me.LabelControl2, 0)
        Me.LabelControl2.Location = New System.Drawing.Point(3, 6)
        Me.LabelControl2.Name = "LabelControl2"
        Me.TablePanel1.SetRow(Me.LabelControl2, 0)
        Me.LabelControl2.Size = New System.Drawing.Size(16, 13)
        Me.LabelControl2.TabIndex = 9
        Me.LabelControl2.Text = "Set"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Options.UseTextOptions = True
        Me.LabelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TablePanel1.SetColumn(Me.LabelControl1, 1)
        Me.LabelControl1.Location = New System.Drawing.Point(25, 6)
        Me.LabelControl1.Name = "LabelControl1"
        Me.TablePanel1.SetRow(Me.LabelControl1, 0)
        Me.LabelControl1.Size = New System.Drawing.Size(88, 14)
        Me.LabelControl1.TabIndex = 8
        Me.LabelControl1.Text = "Filter By"
        '
        'ceFilter2
        '
        Me.TablePanel1.SetColumn(Me.ceFilter2, 0)
        Me.ceFilter2.Location = New System.Drawing.Point(3, 263)
        Me.ceFilter2.Name = "ceFilter2"
        Me.ceFilter2.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceFilter2, 10)
        Me.ceFilter2.Size = New System.Drawing.Size(16, 20)
        Me.ceFilter2.TabIndex = 7
        Me.ceFilter2.Visible = False
        '
        'ceRows
        '
        Me.TablePanel1.SetColumn(Me.ceRows, 0)
        Me.ceRows.EditValue = True
        Me.ceRows.Enabled = False
        Me.ceRows.Location = New System.Drawing.Point(3, 211)
        Me.ceRows.Name = "ceRows"
        Me.ceRows.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceRows, 8)
        Me.ceRows.Size = New System.Drawing.Size(16, 20)
        Me.ceRows.TabIndex = 6
        '
        'ceAction
        '
        Me.TablePanel1.SetColumn(Me.ceAction, 0)
        Me.ceAction.Location = New System.Drawing.Point(3, 185)
        Me.ceAction.Name = "ceAction"
        Me.ceAction.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceAction, 7)
        Me.ceAction.Size = New System.Drawing.Size(16, 20)
        Me.ceAction.TabIndex = 5
        '
        'ceColumn
        '
        Me.TablePanel1.SetColumn(Me.ceColumn, 0)
        Me.ceColumn.Location = New System.Drawing.Point(3, 159)
        Me.ceColumn.Name = "ceColumn"
        Me.ceColumn.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceColumn, 6)
        Me.ceColumn.Size = New System.Drawing.Size(16, 20)
        Me.ceColumn.TabIndex = 4
        '
        'ceAppName
        '
        Me.TablePanel1.SetColumn(Me.ceAppName, 0)
        Me.ceAppName.Location = New System.Drawing.Point(3, 107)
        Me.ceAppName.Name = "ceAppName"
        Me.ceAppName.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceAppName, 4)
        Me.ceAppName.Size = New System.Drawing.Size(16, 20)
        Me.ceAppName.TabIndex = 3
        '
        'ceHost
        '
        Me.TablePanel1.SetColumn(Me.ceHost, 0)
        Me.ceHost.Location = New System.Drawing.Point(3, 81)
        Me.ceHost.Name = "ceHost"
        Me.ceHost.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceHost, 3)
        Me.ceHost.Size = New System.Drawing.Size(16, 20)
        Me.ceHost.TabIndex = 2
        '
        'cdDateTo
        '
        Me.TablePanel1.SetColumn(Me.cdDateTo, 0)
        Me.cdDateTo.EditValue = True
        Me.cdDateTo.Enabled = False
        Me.cdDateTo.Location = New System.Drawing.Point(3, 55)
        Me.cdDateTo.Name = "cdDateTo"
        Me.cdDateTo.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.cdDateTo, 2)
        Me.cdDateTo.Size = New System.Drawing.Size(16, 20)
        Me.cdDateTo.TabIndex = 1
        '
        'ceDateFrom
        '
        Me.TablePanel1.SetColumn(Me.ceDateFrom, 0)
        Me.ceDateFrom.EditValue = True
        Me.ceDateFrom.Enabled = False
        Me.ceDateFrom.Location = New System.Drawing.Point(3, 29)
        Me.ceDateFrom.Name = "ceDateFrom"
        Me.ceDateFrom.Properties.Caption = ""
        Me.TablePanel1.SetRow(Me.ceDateFrom, 1)
        Me.ceDateFrom.Size = New System.Drawing.Size(16, 20)
        Me.ceDateFrom.TabIndex = 0
        '
        'btnSearch
        '
        Me.btnSearch.Location = New System.Drawing.Point(272, 36)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Size = New System.Drawing.Size(74, 22)
        Me.btnSearch.StyleController = Me.LayoutControl1
        Me.btnSearch.TabIndex = 14
        Me.btnSearch.Text = "Search"
        '
        'btnExport
        '
        Me.btnExport.Location = New System.Drawing.Point(12, 36)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Size = New System.Drawing.Size(77, 22)
        Me.btnExport.StyleController = Me.LayoutControl1
        Me.btnExport.TabIndex = 19
        Me.btnExport.Text = "Export"
        '
        'btnApply
        '
        Me.btnApply.Location = New System.Drawing.Point(103, 36)
        Me.btnApply.Name = "btnApply"
        Me.btnApply.Size = New System.Drawing.Size(74, 22)
        Me.btnApply.StyleController = Me.LayoutControl1
        Me.btnApply.TabIndex = 20
        Me.btnApply.Text = "Apply"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.EmptySpaceItem1, Me.SimpleSeparator1, Me.SplitterItem1, Me.LayoutControlItem8, Me.EmptySpaceItem11, Me.lciSearch, Me.lciTableChoose, Me.lciGrid, Me.EmptySpaceItem12, Me.EmptySpaceItem2, Me.LayoutControlItem1, Me.lciApply, Me.EmptySpaceItem8, Me.lciExport})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1620, 698)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(1572, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(28, 678)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'SimpleSeparator1
        '
        Me.SimpleSeparator1.AllowHotTrack = False
        Me.SimpleSeparator1.Location = New System.Drawing.Point(348, 0)
        Me.SimpleSeparator1.Name = "SimpleSeparator1"
        Me.SimpleSeparator1.Size = New System.Drawing.Size(1224, 1)
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.Location = New System.Drawing.Point(338, 0)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(10, 627)
        Me.SplitterItem1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.TablePanel1
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 127)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(338, 500)
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'EmptySpaceItem11
        '
        Me.EmptySpaceItem11.AllowHotTrack = False
        Me.EmptySpaceItem11.Location = New System.Drawing.Point(0, 50)
        Me.EmptySpaceItem11.Name = "EmptySpaceItem11"
        Me.EmptySpaceItem11.Size = New System.Drawing.Size(338, 77)
        Me.EmptySpaceItem11.TextSize = New System.Drawing.Size(0, 0)
        '
        'lciSearch
        '
        Me.lciSearch.Control = Me.btnSearch
        Me.lciSearch.Location = New System.Drawing.Point(260, 24)
        Me.lciSearch.MaxSize = New System.Drawing.Size(78, 26)
        Me.lciSearch.MinSize = New System.Drawing.Size(78, 26)
        Me.lciSearch.Name = "lciSearch"
        Me.lciSearch.Size = New System.Drawing.Size(78, 26)
        Me.lciSearch.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.lciSearch.TextSize = New System.Drawing.Size(0, 0)
        Me.lciSearch.TextVisible = False
        '
        'lciTableChoose
        '
        Me.lciTableChoose.Control = Me.lueTables
        Me.lciTableChoose.Location = New System.Drawing.Point(0, 0)
        Me.lciTableChoose.Name = "lciTableChoose"
        Me.lciTableChoose.Size = New System.Drawing.Size(338, 24)
        Me.lciTableChoose.Text = "Choose Table: "
        Me.lciTableChoose.TextSize = New System.Drawing.Size(72, 13)
        '
        'lciGrid
        '
        Me.lciGrid.Control = Me.GridControl1
        Me.lciGrid.Location = New System.Drawing.Point(348, 1)
        Me.lciGrid.Name = "lciGrid"
        Me.lciGrid.Size = New System.Drawing.Size(1224, 626)
        Me.lciGrid.TextSize = New System.Drawing.Size(0, 0)
        Me.lciGrid.TextVisible = False
        '
        'EmptySpaceItem12
        '
        Me.EmptySpaceItem12.AllowHotTrack = False
        Me.EmptySpaceItem12.Location = New System.Drawing.Point(0, 627)
        Me.EmptySpaceItem12.MinSize = New System.Drawing.Size(104, 24)
        Me.EmptySpaceItem12.Name = "EmptySpaceItem12"
        Me.EmptySpaceItem12.Size = New System.Drawing.Size(1572, 51)
        Me.EmptySpaceItem12.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.EmptySpaceItem12.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(250, 24)
        Me.EmptySpaceItem2.MaxSize = New System.Drawing.Size(10, 26)
        Me.EmptySpaceItem2.MinSize = New System.Drawing.Size(10, 26)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(10, 26)
        Me.EmptySpaceItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.chkToday
        Me.LayoutControlItem1.Location = New System.Drawing.Point(169, 24)
        Me.LayoutControlItem1.MaxSize = New System.Drawing.Size(81, 26)
        Me.LayoutControlItem1.MinSize = New System.Drawing.Size(81, 26)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(81, 26)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'lciApply
        '
        Me.lciApply.Control = Me.btnApply
        Me.lciApply.Location = New System.Drawing.Point(91, 24)
        Me.lciApply.MaxSize = New System.Drawing.Size(78, 26)
        Me.lciApply.MinSize = New System.Drawing.Size(78, 26)
        Me.lciApply.Name = "lciApply"
        Me.lciApply.Size = New System.Drawing.Size(78, 26)
        Me.lciApply.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.lciApply.TextSize = New System.Drawing.Size(0, 0)
        Me.lciApply.TextVisible = False
        '
        'EmptySpaceItem8
        '
        Me.EmptySpaceItem8.AllowHotTrack = False
        Me.EmptySpaceItem8.Location = New System.Drawing.Point(81, 24)
        Me.EmptySpaceItem8.MaxSize = New System.Drawing.Size(10, 26)
        Me.EmptySpaceItem8.MinSize = New System.Drawing.Size(10, 26)
        Me.EmptySpaceItem8.Name = "EmptySpaceItem8"
        Me.EmptySpaceItem8.Size = New System.Drawing.Size(10, 26)
        Me.EmptySpaceItem8.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.EmptySpaceItem8.TextSize = New System.Drawing.Size(0, 0)
        '
        'lciExport
        '
        Me.lciExport.Control = Me.btnExport
        Me.lciExport.Location = New System.Drawing.Point(0, 24)
        Me.lciExport.MaxSize = New System.Drawing.Size(81, 26)
        Me.lciExport.MinSize = New System.Drawing.Size(81, 26)
        Me.lciExport.Name = "lciExport"
        Me.lciExport.Size = New System.Drawing.Size(81, 26)
        Me.lciExport.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.lciExport.TextSize = New System.Drawing.Size(0, 0)
        Me.lciExport.TextVisible = False
        '
        'frmAudit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1620, 698)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "frmAudit"
        Me.Text = "Audit"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.chkToday.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueTables.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TablePanel1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TablePanel1.ResumeLayout(False)
        Me.TablePanel1.PerformLayout()
        CType(Me.txtModifiedBy.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceModBy.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SpinEditRows.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue10.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue9.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue8.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue7.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueValue1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueActionFilter.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueColumn.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtAppName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHostName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deDateTo.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deDateTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deDateFrom.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deDateFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter10.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter9.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter8.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter7.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceFilter2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceRows.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceAction.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceColumn.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceAppName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceHost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cdDateTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceDateFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleSeparator1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciSearch, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciTableChoose, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciGrid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciApply, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciExport, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents SimpleSeparator1 As DevExpress.XtraLayout.SimpleSeparator
    Friend WithEvents EmptySpaceItem8 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents TablePanel1 As DevExpress.Utils.Layout.TablePanel
    Friend WithEvents ceFilter2 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceRows As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceAction As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceColumn As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceAppName As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceHost As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents cdDateTo As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceDateFrom As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents btnSearch As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem11 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents lciSearch As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lueColumnFilter As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblHostName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lueTables As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lciTableChoose As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lcFilter10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lcFilter1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lueValue10 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue9 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue8 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue7 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue6 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue5 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue4 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue3 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue2 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueValue1 As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueActionFilter As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lueColumn As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents txtAppName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtHostName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents deDateTo As DevExpress.XtraEditors.DateEdit
    Friend WithEvents deDateFrom As DevExpress.XtraEditors.DateEdit
    Friend WithEvents ceFilter10 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter9 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter8 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter7 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter6 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter5 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter4 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter3 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ceFilter1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents EmptySpaceItem12 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents lciGrid As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnExport As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lciExport As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SpinEditRows As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents btnApply As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lciApply As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents txtModifiedBy As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ceModBy As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents chkToday As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
End Class
