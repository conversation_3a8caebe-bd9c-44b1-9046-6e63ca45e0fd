﻿Imports System.ComponentModel
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Imports System.Net.Mail
Imports System.Text.RegularExpressions
Imports CsvHelper
Imports CsvHelper.Configuration
Imports DevExpress.Spreadsheet
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports Newtonsoft.Json

Namespace Ach

    Public Class frmAchTransactions
        Dim DB As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
        Dim CodeAndReasonDropDown As List(Of String) = New List(Of String)
        Dim frmLogger As Serilog.ILogger = Logger.ForContext(Of frmAchTransactions)
        Private Property _AchSettings As AchSettings
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property _AchEmailService As AchEmailService
        Private Property _AchTransactionDailyLog As AchTransactionDailyLog
        Private RelatedCompanies As DataTable

        Sub New()
            InitializeComponent()
        End Sub

        Private Sub frmAchTransactions_Load(sender As Object, e As EventArgs) Handles MyBase.Load
            riSlueCoNum.DataSource = DB.view_CompanySumarries.ToList
            GridView1.SetGridLayoutAndAddMenues("frmAchTransactions-MainGrid")
            LoadSettings()
            LoadData()
        End Sub

        Async Sub LoadData()
            Try
                lcRoot.ShowProgessPanel
                'DB.LogToConsole
                Dim Q = From A In DB.AchTransactionsLogs Where Not A.IsDeleted
                If ccbeStatusFilter.GetCheckedItems().Any Then
                    Q = Q.Where(Function(a) ccbeStatusFilter.GetCheckedItems().Contains(a.Status))
                End If

                If deFromDate.EditValue IsNot Nothing Then
                    Dim d As DateTime = deFromDate.EditValue
                    Q = Q.Where(Function(a) a.DateReceived > d)
                End If

                If deToDate.EditValue IsNot Nothing Then
                    Dim d As DateTime = deToDate.EditValue
                    Q = Q.Where(Function(a) a.DateReceived < d)
                End If

                Me.bsAch.DataSource = Await Threading.Tasks.Task.Run(Function() Q.ToList)
                GridView1.BestFitColumns()
                'Me.ErrorLogBindingSource.DataSource = List
                lcRoot.HideProgressPanel
            Catch ex As Exception
                lcRoot.HideProgressPanel
                DisplayErrorMessage("Error loading data", ex)
            End Try
        End Sub

        Private Sub LoadSettings()
            _AchEmailService = New AchEmailService(_AchSettings, True)
            _AchSettings = JsonConvert.DeserializeObject(Of AchSettings)(GetUdfValue("AchSettingsJson"))

            ccbeStatusFilter.Properties.DataSource = _AchSettings.StatusList
            ccbeStatusFilter.EditValue = _AchSettings.StatusDefaultFilter
            ccbeStatusFilter.RefreshEditValue()

            riStatus.Items.Clear()
            riStatus.Items.AddRange(_AchSettings.StatusList)

            DB = New dbEPDataDataContext(GetConnectionString)

            riCbeEmailTemplates.Items.Clear()
            riCbeEmailTemplates.Items.Add("No Template")
            For Each emlTemplate In DB.ReportEmailTeplates.Where(Function(t) t.Tag = "Ach")
                Dim bbi = New BarButtonItem(RibbonControl1.Manager, emlTemplate.Name) With {.Tag = emlTemplate}
                AddHandler bbi.ItemClick, AddressOf bbiEmailClient_ItemClick
                riCbeEmailTemplates.Items.Add(emlTemplate.Name)
            Next

            riCbeDesc.Items.Clear()
            riCbeDesc.Items.AddRange(GetUdfValue("AchTransactions-EntryDescTypeList").Replace(vbCrLf, "~").Split("~").ToArray)
            riCbeCode.Items.Clear()
            CodeAndReasonDropDown = GetUdfValue("AchTransactions-CodeAndReasonDropDown").Replace(vbCrLf, "~").Split("~").ToList
            riCbeCode.Items.AddRange(CodeAndReasonDropDown.ToArray)
            riCbeActions.Items.Clear()
            riCbeActions.Items.AddRange(GetUdfValue("AchTransactions-ActionsDropDown").Replace(vbCrLf, "~").Split("~").ToArray)
            riCbeAmounts.Items.Clear()
            riCbeAmounts.Items.AddRange(GetUdfValue("AchTransactions-AmountDropDown").Replace(vbCrLf, "~").Split("~").ToArray)
        End Sub

        Sub LoadRelatedCompanies(CoNum As Decimal)
            RelatedCompanies = New DataTable

            Using conn As New SqlConnection(GetConnectionString)
                conn.Open()
                Dim cmd = conn.CreateCommand
                cmd.Parameters.AddWithValue("@CoCode", CoNum)
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT CONUM, CO_NAME FROM COMPANY 
WHERE CONUM IN (SELECT col1 FROM dbo.fn_TableFromString((SELECT ISNULL(udf22_data, '-1') FROM COUSERDEFS WHERE conum = @CoCode)) t WHERE TRY_CONVERT(decimal(6,0), t.col1) IS NOT NULL) 
AND CO_STATUS = 'Active Status'
AND CONUM <> @CoCode
ORDER BY 1"
                Dim reader = cmd.ExecuteReader()
                RelatedCompanies.Load(reader)
                reader.Close()

                conn.Close()
            End Using

            Me.GridControlRelatedCompanies.DataSource = RelatedCompanies
            Me.GridViewRelatedCompanies.BestFitColumns()
        End Sub

        Private Sub ccbeStatusFilter_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles ccbeStatusFilter.CloseUp
            LoadData()
        End Sub

        Private Sub GridView1_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridView1.RowUpdated
            Dim TranRow As AchTransactionsLog = e.Row
            TranRow.ChgUser = UserName
            TranRow.ChgDateTime = DateTime.Now
            Dim props = modGlobals.GetDataTimePropertiesWithMinValue(TranRow)
            If props.Any() Then
                Dim msg = GetDataTimePropertiesWithMinValue(TranRow, props)
                DisplayMessageBox(msg)
                Exit Sub
            End If
            DB.SaveChanges()

            Try
                BindingSource1.Remove(e.Row)
                If BindingSource1.Count = 0 Then
                    If _AchTransactionDailyLog IsNot Nothing AndAlso XtraMessageBox.Show($"Is all return for {_AchTransactionDailyLog.FileType} - {_AchTransactionDailyLog.TransactionsDate:d} completed?", "Completed", MessageBoxButtons.YesNo, DevExpress.Utils.DefaultBoolean.True) = DialogResult.Yes Then
                        UpdateDailyLogCompleteStaus(_AchTransactionDailyLog, True)
                    End If
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error removing item from imported list", ex)
            End Try
        End Sub

        Private Sub GridView1_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridView1.ValidateRow
            Dim CurrentRow As AchTransactionsLog = Me.GridView1.GetRow(Me.GridView1.FocusedRowHandle)
            If CurrentRow.CoNum = 0 Then
                e.Valid = False
                e.ErrorText = "Pleas select a Co#." & vbCrLf
            ElseIf Not CurrentRow.DateReceived.HasValue Then
                e.Valid = False
                e.ErrorText = "Pleas select the date received." & vbCrLf
            ElseIf CurrentRow.EmailTemplate.IsNullOrWhiteSpace Then
                e.Valid = False
                e.ErrorText = "Please select a email template"
            ElseIf (CurrentRow.EmailTemplate = "Ach DD PreNote" OrElse CurrentRow.EmailTemplate = "Ach Direct Deposit Issue") AndAlso Not CurrentRow.EmpNum.HasValue Then
                e.Valid = False
                e.ErrorText = "Please select an employee number."
            ElseIf CurrentRow.Status.IsNullOrWhiteSpace Then
                e.Valid = False
                e.ErrorText = "Please select a status."
            End If

            If e.Valid AndAlso CurrentRow.ID = 0 Then 'New Row
                DB.AchTransactionsLogs.InsertOnSubmit(CurrentRow)
            End If
        End Sub

        Private Sub bbiRefresh_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiRefresh.ItemClick
            LoadSettings()
            LoadData()
        End Sub

        Private Sub GridView1_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
            If e.HitInfo.InRow Then
                e.Menu.Items.Add(New DXMenuItem("Delete", Sub()
                                                              DeleteRow(GridView1.GetRow(e.HitInfo.RowHandle))
                                                          End Sub, My.Resources.delete_16x16))
                e.Menu.Items.Add(New DXMenuItem("Email Selected Only", Sub()
                                                                           EmailClient(True)
                                                                       End Sub, My.Resources.emailtemplate_16x16))
            End If
        End Sub

        Private Sub DeleteRow(TranRow As AchTransactionsLog)
            If XtraMessageBox.Show("Are you sure you want to delete this row?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.No Then
                Exit Sub
            End If
            If TranRow IsNot Nothing AndAlso TranRow.ID > 0 Then
                TranRow.IsDeleted = True
                DB.SaveChanges()
                LoadData()
            End If
        End Sub

        Private Sub GridView1_CustomRowCellEditForEditing(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEditForEditing
            Try
                If e.Column Is colCoNum AndAlso e.RowHandle = DevExpress.XtraGrid.GridControl.AutoFilterRowHandle Then
                    e.RepositoryItem = riTeCoNmAutoFilter
                ElseIf e.Column Is colPrNum AndAlso e.RowHandle <> DevExpress.XtraGrid.GridControl.AutoFilterRowHandle Then
                    Dim TranRow As AchTransactionsLog = bsAch.Current
                    riLuePrNum.DataSource = Nothing
                    If TranRow IsNot Nothing AndAlso TranRow.CoNum <> 0 Then
                        riLuePrNum.DataSource = DB.PAYROLLs.Where(Function(p) p.CONUM = TranRow.CoNum AndAlso p.CHECK_DATE.HasValue AndAlso p.CHECK_DATE.Value > Today.AddYears(-1)) _
                        .Select(Function(p) New With {.PrNum = p.PRNUM, .CheckDate = p.CHECK_DATE, .Display = $"{p.PRNUM} - {p.CHECK_DATE}"}) _
                        .OrderByDescending(Function(r) r.PrNum).ToList()
                        riLuePrNum.DisplayMember = "PrNum"
                        riLuePrNum.ValueMember = "PrNum"
                        e.RepositoryItem = riLuePrNum
                    End If
                ElseIf e.Column Is colEmpNum AndAlso e.RowHandle <> DevExpress.XtraGrid.GridControl.AutoFilterRowHandle Then
                    Dim TranRow As AchTransactionsLog = bsAch.Current
                    riEmpNum.DataSource = Nothing
                    If TranRow IsNot Nothing AndAlso TranRow.CoNum <> 0 Then
                        riEmpNum.DataSource = DB.EMPLOYEEs.Where(Function(em) em.CONUM = TranRow.CoNum).ToList()
                        e.RepositoryItem = riEmpNum
                    End If
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error loading Payroll list for client.", ex)
            End Try
        End Sub

        Private Sub riBeInformation_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riBeInformation.ButtonClick
            Dim TranRow As AchTransactionsLog = bsAch.Current
            If TranRow IsNot Nothing Then
                Dim frm = New frmEditNotes(TranRow.Notes)
                If frm.ShowDialog = DialogResult.OK Then
                    TranRow.Notes = frm.GetNote
                    DB.SaveChanges()
                End If
            End If
        End Sub

        Private Sub bbiEmailClient_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiEmailClient.ItemClick
            EmailClient(False)
        End Sub

        Private Async Sub EmailClient(selectedOnly As Boolean)
            Try
                lcRoot.ShowProgessPanel
                Dim TranRow As AchTransactionsLog = bsAch.Current
                DevExpress.Utils.Guard.ArgumentNotNull(TranRow, "Please select a TranRow.")
                If TranRow.EmailTemplate = "No Template" Then
                    Throw New Exception("No email template was selected")
                End If
                Await _AchEmailService.EmailClient(TranRow.CoNum, TranRow.EmailTemplate, False, TranRow.EntryDescType, True, IIf(Not selectedOnly, Nothing, TranRow.ID))
            Catch ex As Exception
                DisplayErrorMessage("Error sending email.", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub BindingSource2_CurrentChanged(sender As Object, e As EventArgs) Handles bsAch.CurrentChanged
            RowChanged()
        End Sub

        Private Sub RowChanged()
            Try
                Dim _db = New dbEPDataDataContext(GetConnectionString)
                Dim TranRow As AchTransactionsLog = bsAch.Current
                If TranRow Is Nothing OrElse TranRow.CoNum = 0 Then
                    MemoEdit1.Text = String.Empty
                    UcCompInfo1.Clean()
                    Exit Sub
                End If
                Dim _empsCount = _db.EMPLOYEEs.Where(Function(em) em.CONUM = TranRow.CoNum AndAlso Not em.TERM_DATE.HasValue).Count
                Dim _coOptions = _db.COOPTIONs.FirstOrDefault(Function(c) c.CONUM = TranRow.CoNum)
                Dim comp = _db.COMPANies.Single(Function(c) c.CONUM = TranRow.CoNum)
                Me.UcCompInfo1.LoadDate(comp, _empsCount, _coOptions)
                MemoEdit1.Text = TranRow.Notes
                _db.Dispose()
                If TranRow IsNot Nothing Then
                    If TranRow.AchTransactionDailyLog Is Nothing Then
                        AchTransactionDailyLogBindingSource.DataSource = GetType(AchTransactionDailyLog)
                    Else
                        AchTransactionDailyLogBindingSource.DataSource = TranRow.AchTransactionDailyLog
                    End If
                    LoadRelatedCompanies(TranRow.CoNum)
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error loading co# details.", ex)
            End Try
        End Sub

        Private Sub bbiOpenCo_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiOpenCo.ItemClick
            Dim TranRow As AchTransactionsLog = bsAch.Current
            If TranRow Is Nothing OrElse TranRow.CoNum = 0 Then Exit Sub
            MainForm.OpenCompForm(TranRow.CoNum)
        End Sub

        Private Sub bbiOpenEmp_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiOpenEmp.ItemClick
            Dim TranRow As AchTransactionsLog = bsAch.Current
            If TranRow Is Nothing OrElse TranRow.CoNum = 0 Then Exit Sub
            MainForm.OpenCompForm(TranRow.CoNum, empNum:=TranRow.EmpNum)
        End Sub

        Private Sub riLuePrNum_EditValueChanged(sender As Object, e As EventArgs) Handles riLuePrNum.EditValueChanged
            'Dim TranRow As AchTransactionsLog = BindingSource2.Current
            'GridView1.PostEditor()
            'Dim conum As Decimal = GridView1.GetRowCellValue(GridView1.FocusedRowHandle, colCoNum)
            'Dim prnum As Decimal = GridView1.GetRowCellValue(GridView1.FocusedRowHandle, colPrNum)
            'Dim edit As Repository.RepositoryItemTextEdit = colPrNum.ColumnEdit

            'Dim pr = DB.PAYROLLs.SingleOrDefault(Function(p) p.CONUM = conum AndAlso p.PRNUM = prnum)
            'If pr IsNot Nothing Then
            '    'TranRow.CheckDate = pr.CHECK_DATE
            '    GridView1.SetRowCellValue(GridView1.FocusedRowHandle, colCheckDate, pr.CHECK_DATE)
            'End If
        End Sub

        Private Sub bbiSetCoPassword_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiSetCoPassword.ItemClick
            If Not (XtraMessageBox.Show("Are you sure you want to add password?", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes) Then
                Return
            End If

            Dim TranRow As AchTransactionsLog = bsAch.Current
            If TranRow Is Nothing Then
                XtraMessageBox.Show("Please select a row.")
                Exit Sub
            End If
            _AchEmailService.SetCoPassword(TranRow.CoNum, TranRow.EntryDescType)
        End Sub

        Private Sub bbiRemoveCoPass_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiRemoveCoPass.ItemClick
            If Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
                Return
            End If

            Dim TranRow As AchTransactionsLog = Nothing
            Try
                TranRow = bsAch.Current
                If TranRow Is Nothing Then
                    XtraMessageBox.Show("Please select a row.")
                    Exit Sub
                End If

                Dim curPwd = _AchEmailService.GetCurrentPassword(TranRow.CoNum)
                If curPwd Is Nothing OrElse curPwd = "" Then
                    XtraMessageBox.Show("Company does not have a password.")
                    Exit Sub
                End If

                Dim nsfRows = (From A In DB.AchTransactionsLogs Where Not A.IsDeleted AndAlso A.CoNum = TranRow.CoNum AndAlso A.EmpNum Is Nothing AndAlso A.DebitAmount IsNot Nothing).FirstOrDefault()
                If nsfRows Is Nothing Then
                    If Not (XtraMessageBox.Show($"No NSF history exists for Co# {TranRow.CoNum}, Proceeding to remove hold {curPwd} password for conum: {TranRow.CoNum}", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes) Then
                        frmLogger.Debug("Removing Password. Co#: {CoNum} RowId: {RowId}", TranRow.CoNum, TranRow.ID)
                        _AchEmailService.RemoveCoPassword(TranRow.CoNum)
                        RowChanged()
                        EmailSend(TranRow.CoNum)
                    End If
                Else
                    Dim frm As New frmRemovePwd(TranRow.CoNum)
                    frm.ShowDialog()
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error removing password!!", ex)
            End Try
        End Sub

        Public Sub EmailSend(Conum As Decimal)
            Dim payrollInProcessCount = (From A In DB.view_PayrollInProcesses Where A.ProcessStatus = "Ready" AndAlso A.CoNum = Conum).Count
            Dim openTicketsCount = DB.view_FaxAndEmails.Where(Function(t) t.CoNum = Conum AndAlso t.Category IsNot Nothing AndAlso t.FaxCategory.Bin = "Payroll" AndAlso Not (t.IsDone)).Count
            If payrollInProcessCount > 0 OrElse openTicketsCount > 0 Then
                If XtraMessageBox.Show($"There's an open Payroll for this company ({Conum}). Would you like to notify CS that the NFS hold has bean removed.", "Send Email", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                    Exit Sub
                End If
                Dim company = DB.COMPANies.Single(Function(c) c.CONUM = Conum)
                Dim MM As New MailMessage
                MM.From = New MailAddress(UserInfo.email, UserInfo.name)
                MM.To.Add(New MailAddress("<EMAIL>"))
                MM.Subject = $"NSF Closed - Co #: {Conum}; {company.CO_NAME}; Cat: Payroll"
                MM.Body = $"The Bank/Tax department has removed the hold of Co#: {Conum} {company.CO_NAME}.
Please process any open payroll that is in either the payroll bin or in payroll in process"
                Dim MC As New SmtpClient
                MC.Send(MM)
                frmMain.AlertControl.Show(Me, "Email Sent", "An Email was sent to the payroll bin to process the open payrolls", True)
            End If

        End Sub

        Private Sub bbiQtrEndHold_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiQtrEndHold.ItemClick
            Dim TranRow As AchTransactionsLog = bsAch.Current
            If TranRow Is Nothing Then
                XtraMessageBox.Show("Please select a row.")
                Exit Sub
            End If
            Dim frm = New frmUpdateQtrEndHold(TranRow.CoNum)
            frm.ShowDialog()
        End Sub

        Private Sub GridView1_EditFormPrepared(sender As Object, e As EditFormPreparedEventArgs) Handles GridView1.EditFormPrepared
            Dim prNumCol As LookUpEdit = e.BindableControls(colPrNum)
            Dim checkDate As DateEdit = e.BindableControls(colCheckDate)
            Dim coNumCol As SearchLookUpEdit = e.BindableControls(colCoNum)
            Dim empNum As LookUpEdit = e.BindableControls(colEmpNum)
            Dim ctx = e.Panel.BindingContext
            'Dim b As AchTransactionsLog = e.Panel.DataBindings
            AddHandler prNumCol.EditValueChanged, Sub()
                                                      Dim coNum As Decimal = coNumCol.EditValue
                                                      Dim prNum As Decimal = prNumCol.EditValue
                                                      Dim pr = DB.PAYROLLs.SingleOrDefault(Function(p) p.CONUM = coNum AndAlso p.PRNUM = prNum)
                                                      If pr IsNot Nothing Then
                                                          'TranRow.CheckDate = pr.CHECK_DATE
                                                          'GridView1.SetRowCellValue(GridView1.FocusedRowHandle, colCheckDate, pr.CHECK_DATE)
                                                          checkDate.DateTime = pr.CHECK_DATE
                                                          GridView1.SetRowCellValue(e.RowHandle, colCheckDate, pr.CHECK_DATE)
                                                      End If
                                                  End Sub

            AddHandler coNumCol.EditValueChanged, Sub()
                                                      Dim coNum As Decimal? = coNumCol.EditValue
                                                      prNumCol.Properties.DataSource = DB.PAYROLLs.Where(Function(p) p.CONUM = coNum AndAlso p.CHECK_DATE.HasValue AndAlso p.CHECK_DATE.Value > Today.AddYears(-1)) _
                        .Select(Function(p) New With {.PrNum = p.PRNUM, .CheckDate = p.CHECK_DATE, .Display = $"{p.PRNUM} - {p.CHECK_DATE}"}) _
                        .OrderByDescending(Function(r) r.PrNum).ToList()
                                                      prNumCol.Properties.DisplayMember = "PrNum"
                                                      prNumCol.Properties.ValueMember = "PrNum"
                                                      empNum.Properties.DataSource = DB.EMPLOYEEs.Where(Function(em) em.CONUM = coNum).ToList()
                                                  End Sub

        End Sub

        Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As Views.Base.FocusedRowObjectChangedEventArgs) Handles GridView1.FocusedRowObjectChanged
            Try
                Dim co As Decimal = 0
                Dim TranRow As AchTransactionsLog = Nothing
                If e.Row IsNot Nothing Then
                    co = CType(e.Row, AchTransactionsLog).CoNum
                    TranRow = e.Row
                End If
                UcFaxAndEmailGrid1._GridFilter = $"[CoNum] = '{co}'"
                Me.rpgCoOptions.Enabled = co <> 0
                Me.bbiOpenEmp.Enabled = (TranRow?.EmpNum.HasValue).GetValueOrDefault

                bbiRequestWire.Enabled = If(TranRow?.DebitAmount Is Nothing, False, TranRow?.DebitAmount > 0)
            Catch ex As Exception
                DisplayErrorMessage("Error Integer Focus changed", ex)
            End Try
        End Sub

        Private Class AchCsvMap
            Inherits ClassMap(Of AchTransactionsLog)
            Public Sub New()
                Map(Function(p) p.EntryDescType).Name("Company Entry Description")
                Map(Function(p) p.Name).Name("Name")
                Map(Function(p) p.ImportedRoutingNumber).Name("Routing Number")
                'Map(Function(p) p.ImportedAccountNumber).ConvertUsing(Function(r) r.GetField("Account Number").Replace("X", ""))
                Map(Function(m) m.ImportedAccountNumber).Convert(Function(args As ConvertFromStringArgs) args.Row.GetField("Account Number").Replace("X", ""))
                Map(Function(p) p.ImportedAccountType).Name("Account Type")
                Map(Function(p) p.ImportedAction).Name("Action")
                Map(Function(p) p.EffectiveDate).Name("Effective Date")
                Map(Function(p) p.RejectionCode).Name("Reason Code")
                'Map(Function(p) p.CorrectedData).Name("Corrected Data")
                'Map(Function(p) p.DebitAmount).ConvertUsing(Function(r) IIf(r.GetField("Action") = "Debit", r.GetField(Of Decimal)("Amount"), Nothing))
                Map(Function(p) p.DebitAmount).Convert(Function(args As ConvertFromStringArgs) IIf(args.Row.GetField("Action") = "Debit", args.Row.GetField(Of Decimal)("Amount"), Nothing))
                'Map(Function(p) p.CreditAmount).ConvertUsing(Function(r) IIf(r.GetField("Action") = "Credit", r.GetField(Of Decimal)("Amount"), Nothing))
                Map(Function(p) p.CreditAmount).Convert(Function(args As ConvertFromStringArgs) IIf(args.Row.GetField("Action") = "Credit", args.Row.GetField(Of Decimal)("Amount"), Nothing))
            End Sub
        End Class

        Private Class NatPayCsvMap
            Inherits ClassMap(Of AchTransactionsLog)
            Public Sub New()
                Map(Function(p) p.DateReceived).Name("Returns Date")
                Map(Function(p) p.EffectiveDate).Name("Transaction Posting Date")
                'Map(Function(p) p.DebitAmount).ConvertUsing(Function(r) IIf(r.GetField("Credit or Debit") = "D", r.GetField(Of Decimal)("Original Item amount"), Nothing))
                Map(Function(p) p.DebitAmount).Convert(Function(args As ConvertFromStringArgs) IIf(args.Row.GetField("Credit or Debit") = "D", args.Row.GetField(Of Decimal)("Original Item amount"), Nothing))
                'Map(Function(p) p.CreditAmount).ConvertUsing(Function(r) IIf(r.GetField("Credit or Debit") = "C", r.GetField(Of Decimal)("Original Item amount"), Nothing))
                Map(Function(p) p.DebitAmount).Convert(Function(args As ConvertFromStringArgs) IIf(args.Row.GetField("Action") = "Debit", args.Row.GetField(Of Decimal)("Amount"), Nothing))
                'Map(Function(p) p.ImportedAccountNumber).ConvertUsing(Function(r) r.GetField("Original Item Bank Account").RemoveFromStart("'"))
                Map(Function(m) m.ImportedAccountNumber).Convert(Function(args As ConvertFromStringArgs) args.Row.GetField("Original Item Bank Account").RemoveFromStart("'"))
                'Map(Function(p) p.ImportedRoutingNumber).ConvertUsing(Function(r) r.GetField("OriginalItem R/T").RemoveFromStart("'"))
                Map(Function(m) m.ImportedAccountNumber).Convert(Function(args As ConvertFromStringArgs) args.Row.GetField("riginalItem R/T").RemoveFromStart("'"))
                Map(Function(p) p.ImportedAccountType).Name("Original Item Checking or Savings")
                Map(Function(p) p.EntryDescType).Name("Original Item Batch Description")
                Map(Function(p) p.RejectionCode).Name("Return Reason Code")
                'Map(Function(p) p.CorrectedData).ConvertUsing(Function(r) r.GetField("NOC Bank Account").RemoveFromStart("'"))
                Map(Function(m) m.ImportedAccountNumber).Convert(Function(args As ConvertFromStringArgs) args.Row.GetField("NOC Bank Account").RemoveFromStart("'"))
                'Map(Function(p) p.Name).ConvertUsing(Function(r) $"{r.GetField("Original Item Name")} {r.GetField("")}")
                Map(Function(m) m.ImportedAccountNumber).Convert(Function(args As ConvertFromStringArgs) $"{args.Row.GetField("Original Item Name")} {args.Row.GetField("")}")
                'Map(Function(p) p.ImportedAction).ConvertUsing(Function(r) IIf(r.GetField("Credit or Debit") = "C", "Credit", "Debit"))
                Map(Function(p) p.DebitAmount).Convert(Function(args As ConvertFromStringArgs) IIf(args.Row.GetField("Credit or Debit") = "C", "Credit", "Debit"))
            End Sub
        End Class

        Private Sub gvImportedData_DoubleClick(sender As Object, e As EventArgs) Handles gvImportedData.DoubleClick
            Try
                ach = BindingSource1.Current
                If ach IsNot Nothing Then
                    ach.AchTransactionDailyLog = _AchTransactionDailyLog
                    Dim frm = New frmGetDDDrafts(ach, Me)
                    MainForm.ShowForm(frm)
                    frm.btnGo.PerformClick()
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error searching in DD Drafts imported row", ex)
            End Try
        End Sub

        Private Sub bsAch_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles bsAch.AddingNew
            Try
                If ach IsNot Nothing Then
                    SyncLock ach
                        ach.AddUser = UserName
                        ach.AddDateTime = Now
                        ach.DateReceived = DateTime.Now
                        ach.IsClientFault = True
                        If ach.RejectionCode.IsNotNullOrWhiteSpace Then ach.RejectionCode = CodeAndReasonDropDown.FirstOrDefault(Function(s) s.StartsWith(ach.RejectionCode))
                        If ach.EmpNum = -1 Then ach.EmpNum = Nothing
                        Dim prNumForRecon As String = nz(ach.PrNum, "NULL")
                        If ach.EmpNum.HasValue Then
                            UpdateEmployeeBankInfo(ach)
                            If ach.CreditAmount > 0 Then
                                ach.EmailTemplate = "Ach Direct Deposit Issue"

                                DB.ExecuteCommand($"EXEC custom.prc_CreateACHReconEntry 
                                @AccountKey = 11, @CoNum = {ach.CoNum}, @PayrollNum = {prNumForRecon}, @Payment = NULL, @Deposit = {ach.CreditAmount}, @tran_num = 'FAIL DD', 
                                @EffectiveDate = '{ach.DateReceived}', @payee_descr = '{ach.CoNum} - EE {ach.EmpNum}', @CatID = 1267, @CatCashID = 6, @Descr = 'FAILED DD {ach.CoNum} - EE {ach.EmpNum}'")
                            ElseIf ach.DebitAmount > 0 Then
                                Dim company = DB.COMPANies.Single(Function(c) c.CONUM = ach.CoNum)
                                ach.EmailTemplate = "Ach Direct Deposit Issue"

                                DB.ExecuteCommand($"EXEC custom.prc_CreateACHReconEntry 
                                @AccountKey = 11, @CoNum = {ach.CoNum}, @PayrollNum = {prNumForRecon}, @Payment = {ach.DebitAmount}, @Deposit = NULL, @tran_num = 'NSF', 
                                @EffectiveDate = '{ach.DateReceived}', @payee_descr = '{ach.CoNum} - {company.CO_NAME}', @CatID = 1287, @CatCashID = 6, @Descr = 'NSF {ach.EntryDescType} {prNumForRecon}'")
                            Else
                                ach.EmailTemplate = "Ach DD PreNote"
                            End If
                            'Solomon added on May 13, '25 per Mendy
                        ElseIf ach.RejectionCode.StartsWith("R23 ") Then
                            ach.EmailTemplate = "Ach Direct Deposit Issue"
                        Else
                            ach.EmailTemplate = "Ach Email"

                            If ach.DebitAmount > 0 Then
                                Dim company = DB.COMPANies.Single(Function(c) c.CONUM = ach.CoNum)

                                DB.ExecuteCommand($"EXEC custom.prc_CreateACHReconEntry 
                                @AccountKey = 11, @CoNum = {ach.CoNum}, @PayrollNum = {prNumForRecon}, @Payment = {ach.DebitAmount}, @Deposit = NULL, @tran_num = 'NSF', 
                                @EffectiveDate = '{ach.DateReceived}', @payee_descr = '{ach.CoNum} - {company.CO_NAME.Replace("'", "''")}', @CatID = 1287, @CatCashID = 6, @Descr = 'NSF {ach.EntryDescType} {prNumForRecon}'")
                            End If
                        End If
                        e.NewObject = ach
                    End SyncLock
                    ach = Nothing
                Else
                    Dim newRow = New AchTransactionsLog With {.DateReceived = Now}
                    newRow.AddUser = UserName
                    newRow.AddDateTime = Now
                    newRow.IsClientFault = True
                    e.NewObject = newRow
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error adding new row", ex)
            End Try
        End Sub

        Dim ach As AchTransactionsLog

        Public Sub AddNewRow()
            GridView1.AddNewRow()
            TabbedControlGroup1.SelectedTabPageIndex = 0
            MainForm.ActivateForm(Me)
            GridView1.ShowEditForm()
        End Sub

        Private Sub UpdateEmployeeBankInfo(TranRow As AchTransactionsLog)
            Dim _db = New dbEPDataDataContext(GetConnectionString)
            Dim emp = _db.EMPLOYEEs.Where(Function(c) c.CONUM = TranRow.CoNum AndAlso c.EMPNUM = TranRow.EmpNum).SingleOrDefault
            If emp IsNot Nothing Then
                Dim match = GetEmpMatchedBankInfo(TranRow, emp)
                Dim log As String = $"Updating Employee ({emp.EMPNUM}) Direct Deposit Info, "
                Select Case match
                    Case "First"
                        log &= $"First Account: DD_ACC_TYPE_1 '{emp.DD_ACC_TYPE_1}' > 'None', DD_STATUS_1 '{emp.DD_STATUS_1}' > 'Pre-Note'"
                        emp.DD_ACC_TYPE_1 = "None"
                        emp.DD_STATUS_1 = "Pre-Note"
                    Case "Second"
                        log &= $"Second Account: DD_ACC_TYPE_2 '{emp.DD_ACC_TYPE_2}' > 'None', DD_STATUS_2 '{emp.DD_STATUS_2}' > 'Pre-Note'"
                        emp.DD_ACC_TYPE_2 = "None"
                        emp.DD_STATUS_2 = "Pre-Note"
                    Case "Third"
                        log &= $"Third Account: DD_ACC_TYPE_3 '{emp.DD_ACC_TYPE_3}' > 'None', DD_STATUS_3 '{emp.DD_STATUS_3}' > 'Pre-Note'"
                        emp.DD_ACC_TYPE_3 = "None"
                        emp.DD_STATUS_3 = "Pre-Note"
                    Case "Fourth"
                        log &= $"Fourth Account: DD_ACC_TYPE_4 '{emp.DD_ACC_TYPE_4}' > 'None', DD_STATUS_4 '{emp.DD_STATUS_4}' > 'Pre-Note'"
                        emp.DD_ACC_TYPE_4 = "None"
                        emp.DD_STATUS_4 = "Pre-Note"
                    Case "None"
                        log &= "No matching account was found."
                    Case Else
                        log &= "Multiple accounts matches this resuls."
                End Select
                frmLogger.Debug("Updating Co#: {CoNum} Emp#: {EmpNum} Matched Bank Info {Match}. {notes}", TranRow.CoNum, emp.EMPNUM, match, log)
                Dim frm = New frmEditNotes(TranRow.Log, log)
                TranRow.Log = frm.GetNote
                _db.SubmitChanges()
            End If
        End Sub

        Private Function GetEmpMatchedBankInfo(TranRow As AchTransactionsLog, emp As EMPLOYEE)
            Dim result As String = ""
            If emp.DD_ACC_NO_1 = TranRow.AccountNumber AndAlso emp.DD_BANK_RT_1 = TranRow.RoutingNumber AndAlso emp.DD_ACC_TYPE_1 = TranRow.AccountType Then
                result &= "First"
            End If
            If emp.DD_ACC_NO_2 = TranRow.AccountNumber AndAlso emp.DD_BANK_RT_2 = TranRow.RoutingNumber AndAlso emp.DD_ACC_TYPE_2 = TranRow.AccountType Then
                result &= "Second"
            End If
            If emp.DD_ACC_NO_3 = TranRow.AccountNumber AndAlso emp.DD_BANK_RT_3 = TranRow.RoutingNumber AndAlso emp.DD_ACC_TYPE_3 = TranRow.AccountType Then
                result &= "Third"
            End If
            If emp.DD_ACC_NO_4 = TranRow.AccountNumber AndAlso emp.DD_BANK_RT_4 = TranRow.RoutingNumber AndAlso emp.DD_ACC_TYPE_4 = TranRow.AccountType Then
                result &= "Fourth"
            End If
            If result.IsNullOrWhiteSpace Then
                result = "None"
            End If
            Return result
        End Function

        Private Sub bbiSetEmpDDType_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiSetEmpDDType.ItemClick
            Try
                Dim TranRow As AchTransactionsLog = bsAch.Current
                If TranRow IsNot Nothing Then
                    If TranRow.EmpNum.HasValue Then
                        UpdateEmployeeBankInfo(TranRow)
                    End If
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error updating employee DD Account Type", ex)
            End Try
        End Sub

        Private Sub beiFromDate_EditValueChanged(sender As Object, e As EventArgs)
            LoadData()
        End Sub

        Private Sub bbiAutoEmails_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiAutoEmails.ItemClick
            Using frm = New frmAchAutoEmails(_AchSettings)
                If frm.ShowDialog() = DialogResult.OK Then

                End If
            End Using
        End Sub

        Private Sub bbiAchSettings_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiAchSettings.ItemClick
            Using frm = New frmAchSettings(_AchSettings)
                If frm.ShowDialog = DialogResult.OK Then
                    SetUdfValue("AchSettingsJson", JsonConvert.SerializeObject(_AchSettings))
                    LoadSettings()
                    LoadData()
                End If
            End Using
        End Sub

        Private Sub btnImport_Click(sender As Object, e As EventArgs) Handles btnImport.Click, btnNoReturnsExist.Click
            Try
                Dim btn As DevExpress.XtraEditors.SimpleButton = sender
                BindingSource1.Clear()
                If btn Is btnNoReturnsExist Then beFilePath.Text = Nothing
                If btn Is btnImport AndAlso beFilePath.Text.IsNullOrWhiteSpace Then
                    DisplayMessageBox("Please select a file")
                ElseIf cbeFileType.EditValue Is Nothing Then
                    DisplayMessageBox("Please select a File Type")
                ElseIf deTransactionsDate.Text.IsNullOrWhiteSpace OrElse deTransactionsDate.DateTime = DateTime.MinValue Then
                    DisplayMessageBox("Please select a Valid Date")
                Else
                    If IsMissingImportDates(cbeFileType.Text, deTransactionsDate.DateTime) Then
                        Exit Sub
                    End If
                    If btn Is btnImport Then
                        Using _db As New dbEPDataDataContext(GetConnectionString)
                            If _db.AchTransactionDailyLogs.Any(Function(d) d.FilePath = beFilePath.Text) Then
                                If XtraMessageBox.Show("This file was already imported. Would you like to import it again?.", "Duplicate Import", MessageBoxButtons.YesNo) = DialogResult.No Then
                                    Exit Sub
                                End If
                            End If
                        End Using
                        Using csv = New CsvHelper.CsvReader(New StreamReader(beFilePath.Text), Globalization.CultureInfo.InvariantCulture)
                            If cbeFileType.Text = "Ach" Then
                                'csv.Configuration.RegisterClassMap(Of AchCsvMap)()
                                csv.Context.RegisterClassMap(Of AchCsvMap)()
                            ElseIf cbeFileType.Text = "NatPay" Then
                                'csv.Configuration.RegisterClassMap(Of NatPayCsvMap)()
                                csv.Context.RegisterClassMap(Of NatPayCsvMap)()
                            Else
                                Throw New Exception("Unknown File Type")
                            End If
                            Dim list = csv.GetRecords(Of AchTransactionsLog).ToList
                            BindingSource1.DataSource = list
                            gvImportedData.BestFitColumns()
                        End Using
                    End If
                    Using _db As New dbEPDataDataContext(GetConnectionString)
                        Dim log As AchTransactionDailyLog = New AchTransactionDailyLog With {.FilePath = beFilePath.Text, .FileType = cbeFileType.Text, .TransactionsDate = deTransactionsDate.DateTime, .AddUser = UserName, .AddDateTime = DateTime.Now}
                        If btn Is btnNoReturnsExist Then log.IsCompleted = True
                        _db.AchTransactionDailyLogs.InsertOnSubmit(log)
                        _db.SubmitChanges()
                        _AchTransactionDailyLog = log
                    End Using
                    deTransactionsDate.EditValue = Nothing
                    cbeFileType.EditValue = Nothing
                    beFilePath.Text = Nothing
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error processing file.", ex)
            End Try
        End Sub

        Private Function IsMissingImportDates(fileType As String, transactionDate As Date) As Boolean
            Using _db As New dbEPDataDataContext(GetConnectionString)
                Dim lastDate = _db.AchTransactionDailyLogs.Where(Function(d) d.FileType = fileType AndAlso d.IsCompleted).OrderByDescending(Function(d) d.TransactionsDate).First.TransactionsDate
                If DateDiff("d", lastDate, transactionDate) > 1 Then
                    DisplayMessageBox($"Please handle all days in sequential order. Please start from {lastDate.AddDays(1):d}{vbCrLf}And make sure you import the file and it should be marked completed")
                    Return True
                End If
            End Using
            Return False
        End Function

        Private Sub beFilePath_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles beFilePath.ButtonClick
            Try
                Using fd As New OpenFileDialog
                    fd.Filter = "(*.csv)|*.csv"
                    fd.InitialDirectory = _AchSettings.InitialDirectoryForFiles
                    If fd.ShowDialog = DialogResult.OK Then
                        beFilePath.Text = fd.FileName
                        Dim reg = Regex.Matches(beFilePath.Text, "[0-9]{1,2}\-([0-9]{1,2})\-[0-9]{4}")
                        If reg.Count = 1 Then
                            Dim fileDate As Date
                            If Date.TryParse(reg(0).Value, fileDate) Then
                                deTransactionsDate.DateTime = fileDate
                            Else
                                deTransactionsDate.EditValue = Nothing
                            End If
                        Else
                            deTransactionsDate.EditValue = Nothing
                        End If
                        If beFilePath.Text.Contains("ACH Archive") Then
                            cbeFileType.EditValue = "Ach"
                        ElseIf beFilePath.Text.Contains("NatPay Archive") Then
                            cbeFileType.EditValue = "NatPay"
                        Else
                            cbeFileType.EditValue = Nothing
                        End If
                    End If
                End Using
            Catch ex As Exception
                DisplayErrorMessage("Error loading File", ex)
            End Try

        End Sub

        Private Sub btnRefreshImportLog_Click(sender As Object, e As EventArgs) Handles btnRefreshImportLog.Click
            Try
                Using _db As New dbEPDataDataContext(GetConnectionString)
                    achTransactionDailyLogsBindingSource.DataSource = _db.AchTransactionDailyLogs.OrderByDescending(Function(d) d.TransactionsDate).ToList()
                End Using
            Catch ex As Exception
                DisplayErrorMessage("Error loading Import Log", ex)
            End Try
        End Sub

        Private Sub GridView2_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridView2.PopupMenuShowing
            Dim TranRow As AchTransactionDailyLog = GridView2.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DXMenuItem("Mark Complete", Sub()
                                                                 UpdateDailyLogCompleteStaus(TranRow, True)
                                                             End Sub))
            e.Menu.Items.Add(New DXMenuItem("Mark Uncomplete", Sub()
                                                                   UpdateDailyLogCompleteStaus(TranRow, False)
                                                               End Sub))
        End Sub

        Private Sub UpdateDailyLogCompleteStaus(TranRow As AchTransactionDailyLog, completed As Boolean)
            Try
                Using _db As New dbEPDataDataContext(GetConnectionString)
                    TranRow = _db.AchTransactionDailyLogs.Single(Function(d) d.Id = TranRow.Id)
                    TranRow.IsCompleted = completed
                    _db.SaveChanges
                    btnRefreshImportLog_Click(Nothing, Nothing)
                End Using
            Catch ex As Exception
                DisplayErrorMessage("Error saving changes.", ex)
            End Try
        End Sub

        Private Async Sub BarButtonItem_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiRequestWireNewTicket.ItemClick, bbiRequestWireAttach.ItemClick
            Dim TranRow As AchTransactionsLog = GridView1.GetFocusedRow
            If TranRow Is Nothing OrElse TranRow.CoNum = 0 Then Exit Sub

            'If e.Item Is bbiRequestWireDDAttach Then
            '    RequestWire(row, "DD", True)
            'ElseIf e.Item Is bbiRequestWireDDNewTicket Then
            '    RequestWire(row, "DD", False)
            'ElseIf e.Item Is bbiRequestWireTaxAttach Then
            '    RequestWire(row, "Tax", True)
            'ElseIf e.Item Is bbiRequestWireTaxNewTicket Then
            '    RequestWire(row, "Tax", False)
            'End If
            Dim AchType As String = If(TranRow.EntryDescType.StartsWith("TAX"), "Tax", "DD")
            If e.Item Is bbiRequestWireAttach Then
                RequestWire(TranRow, AchType, True)
            ElseIf e.Item Is bbiRequestWireNewTicket Then
                RequestWire(TranRow, AchType, False)
            End If
        End Sub
        Private Async Sub RequestWire(TranRow As AchTransactionsLog, WireFor As String, Attach As Boolean)
            Try
                If TranRow.DebitAmount Is Nothing OrElse TranRow.DebitAmount = 0 Then
                    Return
                End If

                If Attach = False Then
                    Await CanCreateTicketAsync()
                    Dim Comp = DB.COMPANies.Where(Function(f) f.CONUM = TranRow.CoNum).First()
                    Dim REPORT = DB.ReportEmailTeplates.Where(Function(f) f.ID = 461).First()
                    Await modZendeskIntegrationClient.CreateTicketAsync("", Comp, REPORT, $"Urgent: Action Required for Co #{TranRow.CoNum} Payroll #{TranRow.PrNum}", New List(Of String)())
                Else
                    ProcessReportAsync(TranRow, WireFor, Attach)
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error in request wire", ex)
            End Try
        End Sub

        Private Async Sub ProcessReportAsync(TranRow As AchTransactionsLog, AchType As String, AttachToExistingTicket As Boolean)
            Dim Comp = DB.COMPANies.Where(Function(f) f.CONUM = TranRow.CoNum).First()
            Dim REPORT = DB.ReportEmailTeplates.Where(Function(f) f.ID = 461).First()

            Dim UcReportEmailTemplate1 As ucReportEmailTemplate
            UcReportEmailTemplate1 = New ucReportEmailTemplate With {.CoNum = TranRow.CoNum}

            Try
                Application.DoEvents()
                Dim processor = New ReportProcessor(Comp, REPORT, FileType.Pdf) With {.showInRecentReports = False}
                processor.DefaultParamValues = New List(Of KeyValuePair)
                processor.DefaultParamValues.Add(New KeyValuePair("CoNum", TranRow.CoNum))
                processor.DefaultParamValues.Add(New KeyValuePair("@PrnumWithIssue", If(TranRow.PrNum Is Nothing, 0, TranRow.PrNum)))
                processor.DefaultParamValues.Add(New KeyValuePair("@WireAmount", TranRow.DebitAmount))
                processor.DefaultParamValues.Add(New KeyValuePair("@AchType", AchType))
                Dim result = processor.ProcessReport()
                If Not (result.Cancalled OrElse result.AllFileExist) Then
                    DisplayMessageBox("No records returned for the parameters values entered")
                ElseIf Not result.Cancalled Then
                    If Not AttachToExistingTicket Then
                        Dim sender = New ReportSender(result)
                        Await sender.CreateTicketAsync()
                    Else
                        Dim attachments As New List(Of String)
                        attachments.Add(result.Paths(0))

                        Using frmAttachments = New frmDraggableAttachments(attachments)
                            frmAttachments.ShowDialog()
                        End Using
                    End If
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error processing report: {0}".FormatWith(REPORT.Name), ex)
            Finally
                UcReportEmailTemplate1.Dispose()
            End Try
        End Sub

        Private Sub riButtonEditCoNum_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riButtonEditCoNum.ButtonClick
            Dim TranRow = Me.GridViewRelatedCompanies.GetFocusedDataRow
            MainForm.OpenCompForm(TranRow("CONUM"))
        End Sub
    End Class
End Namespace