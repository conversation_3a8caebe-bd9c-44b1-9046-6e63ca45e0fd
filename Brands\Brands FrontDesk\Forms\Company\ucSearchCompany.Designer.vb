﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucSearchCompany
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.BindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPR_CONTACT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_PHONE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_MODEM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_FAX = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFED_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_EMAIL = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riTePhone = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
        Me.btnRefresh = New DevExpress.XtraEditors.SimpleButton()
        Me.ccbeColumns = New DevExpress.XtraEditors.CheckedComboBoxEdit()
        Me.ceSearchFullWord = New DevExpress.XtraEditors.CheckEdit()
        Me.btnClear = New DevExpress.XtraEditors.SimpleButton()
        Me.ceActiveOnly = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riTePhone, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.lcRoot.SuspendLayout()
        CType(Me.ccbeColumns.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceSearchFullWord.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceActiveOnly.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.BindingSource1
        Me.GridControl1.Location = New System.Drawing.Point(4, 30)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riTePhone})
        Me.GridControl1.Size = New System.Drawing.Size(979, 469)
        Me.GridControl1.TabIndex = 0
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'BindingSource1
        '
        Me.BindingSource1.DataSource = GetType(Brands_FrontDesk.view_CompanySumarry)
        '
        'GridView1
        '
        Me.GridView1.Appearance.FocusedRow.Font = New System.Drawing.Font("Tahoma", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.Appearance.FocusedRow.Options.UseFont = True
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM, Me.colCO_NAME, Me.colPR_CONTACT, Me.colCO_PHONE, Me.colCO_MODEM, Me.colCO_FAX, Me.colFED_ID, Me.colCO_EMAIL, Me.colCO_STATUS})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsBehavior.ReadOnly = True
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsSelection.EnableAppearanceHideSelection = False
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowIndicator = False
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.MinWidth = 65
        Me.colCONUM.Name = "colCONUM"
        Me.colCONUM.Visible = True
        Me.colCONUM.VisibleIndex = 0
        Me.colCONUM.Width = 66
        '
        'colCO_NAME
        '
        Me.colCO_NAME.FieldName = "CO_NAME"
        Me.colCO_NAME.MinWidth = 125
        Me.colCO_NAME.Name = "colCO_NAME"
        Me.colCO_NAME.Visible = True
        Me.colCO_NAME.VisibleIndex = 1
        Me.colCO_NAME.Width = 125
        '
        'colPR_CONTACT
        '
        Me.colPR_CONTACT.FieldName = "PR_CONTACT"
        Me.colPR_CONTACT.MinWidth = 150
        Me.colPR_CONTACT.Name = "colPR_CONTACT"
        Me.colPR_CONTACT.Visible = True
        Me.colPR_CONTACT.VisibleIndex = 2
        Me.colPR_CONTACT.Width = 150
        '
        'colCO_PHONE
        '
        Me.colCO_PHONE.FieldName = "CO_PHONE"
        Me.colCO_PHONE.MinWidth = 85
        Me.colCO_PHONE.Name = "colCO_PHONE"
        Me.colCO_PHONE.Visible = True
        Me.colCO_PHONE.VisibleIndex = 3
        Me.colCO_PHONE.Width = 85
        '
        'colCO_MODEM
        '
        Me.colCO_MODEM.FieldName = "CO_MODEM"
        Me.colCO_MODEM.MinWidth = 85
        Me.colCO_MODEM.Name = "colCO_MODEM"
        Me.colCO_MODEM.Visible = True
        Me.colCO_MODEM.VisibleIndex = 4
        Me.colCO_MODEM.Width = 85
        '
        'colCO_FAX
        '
        Me.colCO_FAX.FieldName = "CO_FAX"
        Me.colCO_FAX.MinWidth = 85
        Me.colCO_FAX.Name = "colCO_FAX"
        Me.colCO_FAX.Visible = True
        Me.colCO_FAX.VisibleIndex = 5
        Me.colCO_FAX.Width = 85
        '
        'colFED_ID
        '
        Me.colFED_ID.FieldName = "FED_ID"
        Me.colFED_ID.MinWidth = 70
        Me.colFED_ID.Name = "colFED_ID"
        Me.colFED_ID.Visible = True
        Me.colFED_ID.VisibleIndex = 6
        Me.colFED_ID.Width = 70
        '
        'colCO_EMAIL
        '
        Me.colCO_EMAIL.FieldName = "CO_EMAIL"
        Me.colCO_EMAIL.MinWidth = 225
        Me.colCO_EMAIL.Name = "colCO_EMAIL"
        Me.colCO_EMAIL.Visible = True
        Me.colCO_EMAIL.VisibleIndex = 7
        Me.colCO_EMAIL.Width = 225
        '
        'colCO_STATUS
        '
        Me.colCO_STATUS.FieldName = "CO_STATUS"
        Me.colCO_STATUS.MinWidth = 135
        Me.colCO_STATUS.Name = "colCO_STATUS"
        Me.colCO_STATUS.Visible = True
        Me.colCO_STATUS.VisibleIndex = 8
        Me.colCO_STATUS.Width = 135
        '
        'riTePhone
        '
        Me.riTePhone.AutoHeight = False
        Me.riTePhone.Mask.EditMask = "(*************"
        Me.riTePhone.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.riTePhone.Mask.UseMaskAsDisplayFormat = True
        Me.riTePhone.Name = "riTePhone"
        '
        'lcRoot
        '
        Me.lcRoot.Controls.Add(Me.btnRefresh)
        Me.lcRoot.Controls.Add(Me.ccbeColumns)
        Me.lcRoot.Controls.Add(Me.ceSearchFullWord)
        Me.lcRoot.Controls.Add(Me.btnClear)
        Me.lcRoot.Controls.Add(Me.ceActiveOnly)
        Me.lcRoot.Controls.Add(Me.TextEdit1)
        Me.lcRoot.Controls.Add(Me.GridControl1)
        Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lcRoot.Location = New System.Drawing.Point(0, 0)
        Me.lcRoot.Name = "lcRoot"
        Me.lcRoot.Root = Me.LayoutControlGroup1
        Me.lcRoot.Size = New System.Drawing.Size(987, 529)
        Me.lcRoot.TabIndex = 1
        Me.lcRoot.Text = "LayoutControl1"
        '
        'btnRefresh
        '
        Me.btnRefresh.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.refresh_16x16
        Me.btnRefresh.Location = New System.Drawing.Point(903, 503)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Size = New System.Drawing.Size(80, 22)
        Me.btnRefresh.StyleController = Me.lcRoot
        Me.btnRefresh.TabIndex = 8
        Me.btnRefresh.Text = "Refresh"
        '
        'ccbeColumns
        '
        Me.ccbeColumns.Location = New System.Drawing.Point(584, 4)
        Me.ccbeColumns.Name = "ccbeColumns"
        Me.ccbeColumns.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ccbeColumns.Size = New System.Drawing.Size(197, 20)
        Me.ccbeColumns.StyleController = Me.lcRoot
        Me.ccbeColumns.TabIndex = 7
        '
        'ceSearchFullWord
        '
        Me.ceSearchFullWord.EditValue = True
        Me.ceSearchFullWord.Location = New System.Drawing.Point(867, 4)
        Me.ceSearchFullWord.Name = "ceSearchFullWord"
        Me.ceSearchFullWord.Properties.Caption = "Optimize Search"
        Me.ceSearchFullWord.Size = New System.Drawing.Size(116, 20)
        Me.ceSearchFullWord.StyleController = Me.lcRoot
        Me.ceSearchFullWord.TabIndex = 6
        '
        'btnClear
        '
        Me.btnClear.Location = New System.Drawing.Point(4, 503)
        Me.btnClear.Name = "btnClear"
        Me.btnClear.Size = New System.Drawing.Size(37, 22)
        Me.btnClear.StyleController = Me.lcRoot
        Me.btnClear.TabIndex = 5
        Me.btnClear.Text = "Clear"
        '
        'ceActiveOnly
        '
        Me.ceActiveOnly.Location = New System.Drawing.Point(785, 4)
        Me.ceActiveOnly.Name = "ceActiveOnly"
        Me.ceActiveOnly.Properties.Caption = "Active Only"
        Me.ceActiveOnly.Size = New System.Drawing.Size(78, 20)
        Me.ceActiveOnly.StyleController = Me.lcRoot
        Me.ceActiveOnly.TabIndex = 4
        '
        'TextEdit1
        '
        Me.TextEdit1.Location = New System.Drawing.Point(47, 4)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI Semibold", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit1.Properties.Appearance.Options.UseFont = True
        Me.TextEdit1.Size = New System.Drawing.Size(490, 22)
        Me.TextEdit1.StyleController = Me.lcRoot
        Me.TextEdit1.TabIndex = 3
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.EmptySpaceItem1, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.LayoutControlItem7})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(987, 529)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 26)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(983, 473)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.TextEdit1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(537, 26)
        Me.LayoutControlItem2.Text = "Search: "
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(40, 13)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.ceActiveOnly
        Me.LayoutControlItem3.Location = New System.Drawing.Point(781, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(82, 26)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.btnClear
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 499)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(41, 26)
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(41, 499)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(858, 26)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.ceSearchFullWord
        Me.LayoutControlItem5.Location = New System.Drawing.Point(863, 0)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(120, 26)
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.ccbeColumns
        Me.LayoutControlItem6.Location = New System.Drawing.Point(537, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(244, 26)
        Me.LayoutControlItem6.Text = "Columns"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(40, 13)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.btnRefresh
        Me.LayoutControlItem7.Location = New System.Drawing.Point(899, 499)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(84, 26)
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem7.TextVisible = False
        '
        'ucSearchCompany
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.lcRoot)
        Me.Name = "ucSearchCompany"
        Me.Size = New System.Drawing.Size(987, 529)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riTePhone, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.lcRoot.ResumeLayout(False)
        CType(Me.ccbeColumns.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceSearchFullWord.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceActiveOnly.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents BindingSource1 As System.Windows.Forms.BindingSource
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPR_CONTACT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_PHONE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_FAX As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFED_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_EMAIL As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ceActiveOnly As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents ccbeColumns As DevExpress.XtraEditors.CheckedComboBoxEdit
    Friend WithEvents ceSearchFullWord As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents riTePhone As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents btnRefresh As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colCO_MODEM As DevExpress.XtraGrid.Columns.GridColumn

End Class
