﻿Imports System.Collections.ObjectModel
Imports DevExpress.XtraEditors

Public Class frmOrdersBilling

    Private Property OrdersList As ObservableCollection(Of view_OrderItemInvoice)
    Private OrderId As Int32

    Sub New()
        InitializeComponent()
        deFromDate.DateTime = Today.AddDays(-30)
        deToDate.DateTime = Today.AddDays(1)
        pceCompanyFilter.AddClearButton
        ucSearchCompany.BindPopupContainerEdit(pceCompanyFilter)
    End Sub

    Sub New(OrderId As Int32)
        InitializeComponent()

        Me.OrderId = OrderId
    End Sub

    Public Sub InvoiceOrder()
        If view_OrderItemInvoicesBindingSource.Current Is Nothing Then
            LoadData()
        End If

        Dim order As view_OrderItemInvoice = view_OrderItemInvoicesBindingSource.Current
        Dim items = OrdersList.Where(Function(o) o.OrderId = order.OrderId).ToList

        'if we opened the form with an orderid (background run), then don't show message
        If OrderId = Nothing AndAlso XtraMessageBox.Show($"Are you sure you would like to invoice this order?{vbCrLf}Order #: {order.OrderId}{vbCrLf}Company: {order.ConumAndName}", "Invoice Order?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If

        If items.Count > 1 OrElse (items.Count = 1 AndAlso items.First.ITEM_NAME <> "Delivery") Then
            If Not order.SendWithNextPayroll OrElse GetShippingCharge(items) > 0 Then
                Dim delvDesc = ""
                If items.Count = 1 AndAlso items.SingleOrDefault(Function(i) Not i.ITEM_NUM.HasValue) IsNot Nothing Then
                    delvDesc = $"~{GetInitials(items.SingleOrDefault(Function(i) Not i.ITEM_NUM.HasValue).ITEM_NAME)}"
                End If
                items.Add(New view_OrderItemInvoice With {.CoNum = order.CoNum,
            .OrderDate = order.OrderDate,
            .ITEM_CAT = $"Delivery{delvDesc}",
            .ITEM_NUM = 0,
            .TotalPrice = GetShippingCharge(items),
            .TAXABLE = "YES",
            .PRNUM = 0,
            .INV_SECTION = "Delivery",
            .ITEM_TYPE = "Manual", _ '.ITEM_NAME = "Delivery",
            .ITEM_NAME = "Delivery~Order#" + order.OrderId.ToString(),
            .DIVNUM = 0,
            .ITEM_FREQ_TYPE = order.ITEM_FREQ_TYPE,
            .GenByManBilling = order.GenByManBilling}) 'GenByManBilling added by Solomon on Dec 3, '20
            End If
        End If

        If InvoiceOrder(items) Then
            For Each item In items
                view_OrderItemInvoicesBindingSource.Remove(item)
                'OrdersList.Remove(item)
            Next
        End If
    End Sub

    Private Sub frmOrdersBilling_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
        AddHandler cbeInvoiceStatus.EditValueChanged, AddressOf Filters_EditValueChanged
        AddHandler cbeOrderStatus.EditValueChanged, AddressOf Filters_EditValueChanged
        AddHandler deFromDate.EditValueChanged, AddressOf Filters_EditValueChanged
        AddHandler deToDate.EditValueChanged, AddressOf Filters_EditValueChanged

        For Each PrinterName In Printing.PrinterSettings.InstalledPrinters
            Me.cbPrinters.Properties.Items.Add(PrinterName)
        Next
        Dim defaultPrinting = New Printing.PrinterSettings
        Me.cbPrinters.EditValue = defaultPrinting.PrinterName
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub

    Private Sub Filters_EditValueChanged(sender As Object, e As EventArgs) Handles pceCompanyFilter.EditValueChanged
        If IsInitializing Then Exit Sub
        lcgDateFilters.Visibility = (cbeInvoiceStatus.Text <> "Uninvoiced").ToBarItemVisibility
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            Dim ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim list As IEnumerable(Of view_OrderItemInvoice) = Nothing

            If OrderId = Nothing Then
                view_CompanySumarriesBindingSource.DataSource = ctxDB.view_CompanySumarries.ToList()

                list = ctxDB.view_OrderItemInvoices
                If cbeInvoiceStatus.Text = "Uninvoiced" Then
                    list = list.Where(Function(oi) (oi.IsInvoiced = "NO" OrElse oi.IsInvoiced Is Nothing OrElse oi.IsInvoiced = ""))
                ElseIf cbeInvoiceStatus.Text = "Invoiced" Then
                    list = list.Where(Function(oi) oi.IsInvoiced = "YES")
                ElseIf cbeInvoiceStatus.Text = "N/A" Then
                    list = list.Where(Function(oi) oi.IsInvoiced = "N/A")
                End If

                If cbeOrderStatus.Text = "In Progress" Then
                    list = list.Where(Function(oi) oi.Status.StartsWith("In Progress"))
                ElseIf cbeOrderStatus.Text <> "All" Then
                    list = list.Where(Function(oi) oi.Status = cbeOrderStatus.Text)
                End If

                If lcgDateFilters.Visible Then
                    list = list.Where(Function(oi) oi.OrderDate >= deFromDate.DateTime AndAlso oi.OrderDate <= deToDate.DateTime.Date)
                End If

                If pceCompanyFilter.EditValue IsNot Nothing AndAlso pceCompanyFilter.EditValue.ToString.IsNotNullOrWhiteSpace Then
                    list = list.Where(Function(c) c.CoNum = pceCompanyFilter.EditValue)
                End If
            Else
                list = ctxDB.view_OrderItemInvoices.Where(Function(f) f.OrderId = OrderId)
            End If

            OrdersList = New ObservableCollection(Of view_OrderItemInvoice)(list.ToList)
            view_OrderItemInvoicesBindingSource.DataSource = OrdersList
            GridView1.ExpandAllGroups()
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error getting orders data", ex)
        End Try
    End Sub

    Private Sub riWaivCharge_EditValueChanging(sender As Object, e As Controls.ChangingEventArgs) Handles riWaivCharge.EditValueChanging
        Dim row As view_OrderItemInvoice = GridView1.GetFocusedRow
        If row IsNot Nothing Then
            Dim waiveCharge As Boolean = e.NewValue
            If waiveCharge Then
                Using frm = New frmWaiveChargeReason
                    If frm.ShowDialog = DialogResult.OK Then
                        row.WaiveChargeReason = frm.meWaiveChargeReason.Text
                    Else
                        e.Cancel = True
                    End If
                End Using
            End If
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                Dim item = ctxDB.SuppliesOrderItems.Single(Function(oi) oi.ID = row.OrderItemId)
                item.WaiveCharge = waiveCharge
                item.WaiveChargeReason = row.WaiveChargeReason
                ctxDB.SubmitChanges()
            End Using
        End If
    End Sub

    Private Sub view_OrderItemInvoicesBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles view_OrderItemInvoicesBindingSource.CurrentChanged
        LoadOrderDetails()
    End Sub

    Private Sub LoadOrderDetails()
        Try
            Dim order As view_OrderItemInvoice = view_OrderItemInvoicesBindingSource.Current
            If order Is Nothing Then
                lcgOrderDetails.Tag = Nothing
                lcgOrderDetails.Text = "Order Details"
                teOrderId.Text = Nothing
                sleuComapny.EditValue = Nothing
                lciOrderAlreadyInvoiced.Visibility = False.ToBarItemVisibility
                lciOrderNotCompleted.Visibility = False.ToBarItemVisibility
                btnInvoiceOrder.Enabled = False
                btnInvoiceOrder.Text = "Invoice Order"
                teShippingCharge.Text = Nothing
                teItemsTotal.Text = Nothing
                LayoutControlItem12.Visibility = False.ToBarItemVisibility
                meOrderNotes.Text = Nothing
                meBillingNotes.Text = Nothing
                lcgClientHasPriceOverride.Visibility = False.ToBarItemVisibility
                Exit Sub
            End If
            If lcgOrderDetails.Tag <> order.OrderId Then
                teShippingCharge.Text = Nothing
            End If
            lcgOrderDetails.Tag = order.OrderId
            lcgOrderDetails.Text = $"Order #: {order.OrderId}"
            teOrderId.Text = order.OrderId
            sleuComapny.EditValue = order.CoNum
            meOrderNotes.Text = order.Notes
            meBillingNotes.Text = order.BillingMessage
            lcgClientHasPriceOverride.Visibility = order.HasPriceOverride.ToBarItemVisibility

            lciOrderAlreadyInvoiced.Visibility = (order.IsInvoiced = "YES" OrElse order.IsInvoiced = "N/A").ToBarItemVisibility
            lcOrderAlreadyInvoiced.Text = $"Order was already {IIf(order.IsInvoiced = "N/A", "removed", "invoiced")}!"
            lciOrderNotCompleted.Visibility = (order.Status <> "Completed").ToBarItemVisibility
            LayoutControlItem12.Visibility = (order.SendWithNextPayroll).ToBarItemVisibility

            btnInvoiceOrder.Enabled = lciOrderAlreadyInvoiced.Visible = False AndAlso lciOrderNotCompleted.Visible = False
            btnRemoveOrder.Enabled = lciOrderAlreadyInvoiced.Visible = False AndAlso lciOrderNotCompleted.Visible = False

            Dim items = OrdersList.Where(Function(o) o.OrderId = order.OrderId).ToList

            Dim shippingCharge = GetShippingCharge(items)
            Dim itemsTotal = items.Where(Function(o) Not o.WaiveCharge).Select(Function(o) o.TotalPrice).Sum()

            btnInvoiceOrder.Text = $"Invoice Order (${shippingCharge + itemsTotal})"

            teShippingCharge.Text = shippingCharge
            teItemsTotal.Text = itemsTotal
        Catch ex As Exception
            DisplayErrorMessage("Error loading order details", ex)
        End Try
    End Sub

    Private Sub btnInvoiceOrder_Click(sender As Object, e As EventArgs) Handles btnInvoiceOrder.Click
        InvoiceOrder()
    End Sub

    Public Function GetInitials(name As String) As String
        Dim initials = ""
        For Each n In name.Split(" ")
            initials &= n.First
        Next
        Return initials.ToUpper
    End Function

    Private Function GetShippingCharge(items As List(Of view_OrderItemInvoice)) As Decimal
        If teShippingCharge.Text.IsNotNullOrWhiteSpace Then
            Return teShippingCharge.Text
        ElseIf items.First.SendWithNextPayroll Then
            Return 0
        Else
            Dim baseShippingPrice As Decimal = 0
            If items.Count = 1 AndAlso items.Single.StartingShippingCharge.HasValue Then
                baseShippingPrice = items.Single.StartingShippingCharge.Value
            Else
                Dim max = items.Select(Function(i) i.StartingShippingCharge).Max()
                If max.HasValue Then baseShippingPrice = max.Value Else baseShippingPrice = GetUdfValue_AsDecimal("BaseShippingPrice")
            End If

            Dim itemsShippingPrice As Decimal = 0
            If items.Select(Function(i) i.Qty * i.QtyPerBox).Sum > 1 Then
                Dim item = items.First
                itemsShippingPrice = ((item.Qty * item.QtyPerBox) - 1) * If(item.ShippingPrice.HasValue, item.ShippingPrice, 2)
                itemsShippingPrice += items.Skip(1).Sum(Function(o) (o.Qty * o.QtyPerBox) * If(o.ShippingPrice.HasValue, o.ShippingPrice, 2))
                'If itemsShippingPrice >= 2 Then itemsShippingPrice = itemsShippingPrice - 2
            End If
            Return baseShippingPrice + itemsShippingPrice
        End If
    End Function

    Private Function InvoiceOrder(orderItems As List(Of view_OrderItemInvoice)) As Boolean
        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                Dim order = ctxDB.SuppliesOrders.Single(Function(o) o.ID = orderItems.First.OrderId)
                If order.IsInvoiced = "YES" OrElse order.IsInvoiced = "N/A" Then
                    DisplayMessageBox("Order was already invoiced!")
                    Return False
                End If
                If order.Status <> "Completed" Then
                    DisplayMessageBox("You can only invoice Completed orders")
                    Return False
                End If
                Logger.Debug("Invoicing order {OrderId} {Conum}", order.ID, order.CoNum)
                'Dim invNum = ctx.fn_GetInvoiceNumForCo(order.CoNum, order.Date)
                Dim ExInv = Billing.BillingUtilities.GetInvoiceNumForCo(order.CoNum, 0, order.Date, 0)
                Dim CO = ctxDB.COOPTIONs.Single(Function(c) c.CONUM = order.CoNum)
                Dim paymentTerms As Nullable(Of Integer) = Query(Of Integer)("SELECT glt.cat_id FROM global_lists glt WHERE glt.list_name = 'Invoice Payment Terms' AND glt.list_value = 'Payment Due Upon Receipt'").FirstOrDefault()

                Dim sort As Int16 = 1

                If ExInv Is Nothing Then
                    ExInv = New invoice_master() With {
                            .invoice_key = Guid.NewGuid, .conum = order.CoNum, .invoice_number = 0, .invoice_date = order.Date, .divnum = 0,
                            .invoice_gen_freq = CO.CO_BILL_FREQ, .prnum = 0, .inv_payment_terms_id = nz(paymentTerms, 0), .inv_payment_method = "Direct Deposit",
                            .inv_salestax_exempt = "NO", .inv_display_type = "Detailed", .inv_summ_desc = "Payroll Processing", .invoice_exported = 2,
                            .created_by = UserName, .created_date = DateTime.Now, .rowguid = Guid.NewGuid
                        }
                    ctxDB.invoice_masters.InsertOnSubmit(ExInv)
                Else
                    Dim lastItem = (From d In ctxDB.invoice_item_details.Where(Function(f) f.conum = ExInv.conum AndAlso f.invoice_key = ExInv.invoice_key AndAlso (f.item_deleted = 0 OrElse f.item_deleted Is Nothing)) Select d).OrderByDescending(Function(o) o.item_section_sort_order).FirstOrDefault()
                    If Not lastItem Is Nothing Then
                        sort = lastItem.item_section_sort_order + 1
                    End If
                End If

                Dim section = ctxDB.global_lists.Where(Function(f) f.list_name = "Invoice Group Sections" AndAlso f.list_value = "Miscellaneous").FirstOrDefault()
                Dim category = ctxDB.global_lists.Where(Function(f) f.list_name = "Invoice Report Categories" AndAlso f.list_value = "Miscellaneous").FirstOrDefault()

                For Each item In orderItems
                    'Solomon added on Dec 3, '20.  Skip shipping 0 charge if genearated by manual billing
                    'If Not item.ITEM_NUM.HasValue OrElse (item.GenByManBilling = True AndAlso item.ITEM_NAME = "Lazer Ship" And item.TotalPrice = 0) Then Continue For
                    If Not item.ITEM_NUM.HasValue OrElse (item.GenByManBilling = True AndAlso item.ITEM_NUM = 0 AndAlso item.TotalPrice = 0) Then Continue For
                    'Dim invoice = New INVOICE_xLOG With {
                    '   .CONUM = item.CoNum,
                    '   .ITEM_DATE = item.OrderDate,
                    '   .ITEM_CAT = item.ITEM_CAT,
                    '   .ITEM_NUM = item.ITEM_NUM,
                    '   .PRICE = IIf(item.WaiveCharge, 0D, item.TotalPrice),
                    '   .CHK_ENTRIES = item.Qty,
                    '   .TAXABLE = item.TAXABLE,
                    '   .INVOICE_NUM = invNum,
                    '   .PRNUM = item.PRNUM,
                    '   .INV_SECTION = item.INV_SECTION,
                    '   .ITEM_TYPE = item.ITEM_TYPE,
                    '   .ITEM_NAME = item.ITEM_NAME + IIf(item.WaiveCharge, $"~(${item.TotalPrice})", ""),
                    '   .DIVNUM = item.DIVNUM,
                    '   .ITEM_FREQ_TYPE = item.ITEM_FREQ_TYPE, ' not sure 
                    '    .rowguid = Guid.NewGuid()}
                    'ctx.INVOICE_xLOGs.InsertOnSubmit(invoice)

                    Dim NewDetail = New invoice_item_detail With {
                        .detail_key = Guid.NewGuid,
                        .conum = order.CoNum, .invoice_key = ExInv.invoice_key, .item_date = order.Date, .item_category_id = category.cat_id, .item_num = item.ITEM_NUM,
                        .item_type = "Manual", .item_sales_taxable = "YES",
                        .item_intial_price = IIf(item.WaiveCharge, 0D, item.TotalPrice), .item_final_price = IIf(item.WaiveCharge, 0D, item.TotalPrice),
                        .item_count = item.Qty,
                        .item_prnum = ExInv.prnum, .item_group_section_id = section.cat_id, .item_discount_amt = 0,
                        .item_name = item.ITEM_NAME + IIf(item.WaiveCharge, $"~(${item.TotalPrice})", ""),
                        .item_section_sort_order = sort, .exclude_from_invminmax = "NO", .item_minxmax_applied = 0, .item_deleted = 0, .created_by = UserName,
                        .created_date = Now, .rowguid = Guid.NewGuid
                    }
                    ctxDB.invoice_item_details.InsertOnSubmit(NewDetail)
                    sort += 1
                Next

                order.IsInvoiced = IIf(orderItems.All(Function(i) i.WaiveCharge), "N/A", "YES")
                ctxDB.SubmitChanges()
                Return True
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error invoicing order", ex)
        End Try
        Return False
    End Function

    Private Sub teShippingCharge_EditValueChanged(sender As Object, e As EventArgs) Handles teShippingCharge.EditValueChanged
        LoadOrderDetails()
    End Sub

    Private Sub riWaivCharge_EditValueChanged(sender As Object, e As EventArgs) Handles riWaivCharge.EditValueChanged
        GridView1.PostEditor()
        LoadOrderDetails()
    End Sub

    Private Sub bbiSetupProducts_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSetupProducts.ItemClick
        Using frm = New frmSetupProducts
            If frm.ShowDialog = DialogResult.OK Then

            End If
        End Using
    End Sub

    Private Sub btnRemoveOrder_Click(sender As Object, e As EventArgs) Handles btnRemoveOrder.Click
        Try
            Dim order As view_OrderItemInvoice = view_OrderItemInvoicesBindingSource.Current
            Dim items = OrdersList.Where(Function(o) o.OrderId = order.OrderId).ToList

            If order.IsInvoiced = "YES" OrElse order.IsInvoiced = "N/A" Then
                DisplayMessageBox($"Order was already {IIf(order.IsInvoiced = "N/A", "removed", "invoiced")}!")
                Exit Sub
            End If
            If order.Status <> "Completed" Then
                DisplayMessageBox("You can only remove Completed orders")
                Exit Sub
            End If

            If XtraMessageBox.Show($"Are you sure you would like to remove this order?{vbCrLf}Order #: {order.OrderId}{vbCrLf}Company: {order.ConumAndName}", "Remove Order?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Exit Sub
            End If

            Logger.Debug("Removing order {OrderId} {Conum}", order.OrderId, order.CoNum)

            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                Dim o = ctxDB.SuppliesOrders.Single(Function(ord) ord.ID = order.OrderId)
                o.IsInvoiced = "N/A"
                ctxDB.SubmitChanges()
            End Using
            For Each item In items
                view_OrderItemInvoicesBindingSource.Remove(item)
            Next
        Catch ex As Exception
            Logger.Error(ex, "Error removing order")
            DisplayErrorMessage("Error removing order", ex)
        End Try
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        Dim reportName = "Payroll Cover Sheet"
        Dim report = New ReportProcessor(sleuComapny.EditValue, reportName, FileType.Pdf) With {
            .showParametersForm = False,
            .showInRecentReports = False
        }
        report.DefaultParamValues = New List(Of KeyValuePair)()
        report.DefaultParamValues.Add(New KeyValuePair("PrNum", teOrderId.Text))
        Dim result = report.ProcessReport()
        Using pdfDocumentProcessor As New DevExpress.Pdf.PdfDocumentProcessor()
            pdfDocumentProcessor.LoadDocument(result.Paths.Single, True)
            Dim pdfPrinterSettings As New DevExpress.Pdf.PdfPrinterSettings()
            pdfPrinterSettings.Settings.PrinterName = cbPrinters.EditValue
            pdfDocumentProcessor.Print(pdfPrinterSettings)
        End Using
    End Sub
End Class