﻿Public Class frmPhoneDataComments
    Public Sub New(_phoneNumber As String)
        InitializeComponent()
        ucSearchCompany.BindPopupContainerEdit(pceCompany)
        BindingSource1.DataSource = New _1059PhoneDataComment With {.PhoneNumber = _phoneNumber}
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                db._1059PhoneDataComments.InsertOnSubmit(BindingSource1.DataSource)
                db.SubmitChanges()
            End Using
            DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub
End Class