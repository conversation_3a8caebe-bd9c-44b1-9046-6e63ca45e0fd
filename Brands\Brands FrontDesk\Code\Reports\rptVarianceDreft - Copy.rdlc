<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Textbox Name="Textbox1">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Brand’s Tax Service</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value> Navy Yard BLDG 27</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>63 Flushing Unit 106, Brooklyn, NY 11205</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>T: 718-625-1800 F: 718-625-1802</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox1</rd:DefaultName>
        <Height>0.98958in</Height>
        <Width>6.5in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox2">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Urgent</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>18pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Quarter-End Variance </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>18pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=IIf(Parameters!IsDebit.Value, "Debit Request Form", "Credit Refund Notification")</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>18pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox2</rd:DefaultName>
        <Top>1.14583in</Top>
        <Height>1.00333in</Height>
        <Width>6.5in</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox3">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Date: </Value>
                <Style />
              </TextRun>
              <TextRun>
                <Value>=Today()</Value>
                <Style>
                  <Format>D</Format>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox3</rd:DefaultName>
        <Top>2.3125in</Top>
        <Left>0.11125in</Left>
        <Height>0.25in</Height>
        <Width>2.42708in</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>4.23958in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.73958in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Rectangle Name="Rectangle2">
                      <ReportItems>
                        <Textbox Name="CONUM">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CONUM.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CONUM</rd:DefaultName>
                          <Height>0.25in</Height>
                          <Width>0.67708in</Width>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Textbox>
                        <Textbox Name="CO_NAME">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CO_NAME.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CO_STREET.Value</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CO_CITY.Value &amp; ", " &amp; Fields!CO_STATE.Value &amp; " " &amp; Fields!CO_ZIP.Value</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>P:  </Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!CO_PHONE.Value</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value xml:space="preserve"> </Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=IIf(Fields!CO_EXTENSION.Value &lt;&gt; "", "Ext. " &amp; Fields!CO_EXTENSION.Value, "")</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>    F: </Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!CO_FAX.Value</Value>
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CO_NAME</rd:DefaultName>
                          <Left>0.74653in</Left>
                          <Height>0.71042in</Height>
                          <Width>3.46875in</Width>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Textbox>
                      </ReportItems>
                      <KeepTogether>true</KeepTogether>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </Rectangle>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>dsCompany</DataSetName>
        <Top>2.70833in</Top>
        <Left>0.11125in</Left>
        <Height>0.73958in</Height>
        <Width>4.23958in</Width>
        <ZIndex>3</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox10">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>The </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=Parameters!Year.Value</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value xml:space="preserve"> </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=Code.FormatQuarter(Parameters!Quarter.Value)</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> quarter-end reconciliation has been completed and </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=IIf(Parameters!IsDebit.Value, "additional taxes are due", "you have a credit balance")</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>.</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Following is the breakdown of </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=IIf(Parameters!IsDebit.Value, "amounts due", "your credit")</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>.</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox10</rd:DefaultName>
        <Top>3.88542in</Top>
        <Height>0.40625in</Height>
        <Width>6.5in</Width>
        <ZIndex>4</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.78124in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.16667in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.19792in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.21875in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.5in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox11">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox11</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox13">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Quarterly Liabilities</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox13</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox15">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Periodic </Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Liabilities</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox15</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox17">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Quarter-End Variance</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>11pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox17</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.26042in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cat_descr">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Code.TaxDescription(Fields!cat_subdescr.Value, Fields!cat_descr.Value) &amp; " " &amp; Fields!loc_id.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cat_descr</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TDebits">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!TDebits.Value</Value>
                              <Style>
                                <Format>#,0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>TDebits</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TCredits">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!TCredits.Value</Value>
                              <Style>
                                <Format>#,0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>TCredits</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="split_due">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!split_due.Value</Value>
                              <Style>
                                <Format>#,0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>split_due</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.16667in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox4">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox4</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>None</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>4</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox19">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Totals</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox19</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox20">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!TDebits.Value)</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Format>#,0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox20</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox21">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!TCredits.Value)</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Format>#,0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox21</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="tbTotalDue">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!split_due.Value)</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                                <Format>#,0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details1" />
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>Before</KeepWithGroup>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>dsDetails</DataSetName>
        <Filters>
          <Filter>
            <FilterExpression>=Left(Fields!TempDesc.Value,3)</FilterExpression>
            <Operator>NotEqual</Operator>
            <FilterValues>
              <FilterValue>Ref</FilterValue>
            </FilterValues>
          </Filter>
          <Filter>
            <FilterExpression>=Fields!cat_descr.Value</FilterExpression>
            <Operator>NotEqual</Operator>
            <FilterValues>
              <FilterValue>Cash</FilterValue>
            </FilterValues>
          </Filter>
        </Filters>
        <Top>4.44792in</Top>
        <Height>1.17709in</Height>
        <Width>6.36458in</Width>
        <ZIndex>5</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox27">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=IIf(Parameters!IsDebit.Value, "Amount Due", "Credit Balance")</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value xml:space="preserve">                           </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>= ReportItems!tbTotalDue.Value</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                  <Format>#,0.00</Format>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Right</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox27</rd:DefaultName>
        <Top>5.69445in</Top>
        <Left>3.58333in</Left>
        <Height>0.25in</Height>
        <Width>2.78125in</Width>
        <ZIndex>6</ZIndex>
        <Style>
          <Border>
            <Style>Solid</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox28">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>The </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=IIf(Parameters!IsDebit.Value, "amount due above", "above amount")</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> will be reflected and submitted with your Quarterly return.</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value />
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Please be advised that </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=IIf(Parameters!IsDebit.Value, "we will debit your account", "the above amount will be credited to your account")</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> on </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>=Parameters!EffectiveDate.Value</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <Format>M/d/yyyy</Format>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> if we do not receive a response,</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value />
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox28</rd:DefaultName>
        <Top>6.14583in</Top>
        <Height>0.69084in</Height>
        <Width>6.5in</Width>
        <ZIndex>7</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Textbox Name="Textbox29">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>If applicable; Please respond with one of the below answers</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value />
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>q</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Wingdings</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value xml:space="preserve"> </Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value>I do not agree with this debit, please contact me to discuss</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>q</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Wingdings</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> Do NOT debit my account, I will wire in the funds on Date: ________</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>q</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Wingdings</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
              <TextRun>
                <Value> Please debit my account for the additional taxes, the funds will be available on___________</Value>
                <Style>
                  <FontStyle>Normal</FontStyle>
                  <FontFamily>Calibri</FontFamily>
                  <FontSize>11pt</FontSize>
                  <FontWeight>Normal</FontWeight>
                  <TextDecoration>None</TextDecoration>
                  <Color>#000000</Color>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Left</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox28</rd:DefaultName>
        <Top>6.85056in</Top>
        <Height>1.01375in</Height>
        <Width>6.5in</Width>
        <ZIndex>8</ZIndex>
        <Visibility>
          <Hidden>=Parameters!IsDebit.Value = False</Hidden>
        </Visibility>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Rectangle Name="Rectangle1">
        <ReportItems>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>0.94791in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.71875in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.625in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.18in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Cur Rate</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Prev Rate</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.17708in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!cat_descr.Value &amp; " " &amp; Fields!cat_subdescr.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value> Rate</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CurRate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CurRate.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <Format>0.0000%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CurRate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PrevRate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PrevRate.Value</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                    <Format>0.0000%</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PrevRate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details2" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>dsDetails</DataSetName>
            <Filters>
              <Filter>
                <FilterExpression>=IIf(Fields!CurRate.Value Is Nothing, "NO", "YES")</FilterExpression>
                <Operator>Equal</Operator>
                <FilterValues>
                  <FilterValue>YES</FilterValue>
                </FilterValues>
              </Filter>
            </Filters>
            <Top>0.01375in</Top>
            <Height>0.35708in</Height>
            <Width>2.29166in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <FontSize>8pt</FontSize>
            </Style>
          </Tablix>
        </ReportItems>
        <KeepTogether>true</KeepTogether>
        <Top>2.69458in</Top>
        <Left>4.3682in</Left>
        <Height>0.75333in</Height>
        <Width>2.29166in</Width>
        <ZIndex>9</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Rectangle>
    </ReportItems>
    <Height>7.875in</Height>
    <Style />
  </Body>
  <Width>6.65986in</Width>
  <Page>
    <LeftMargin>1in</LeftMargin>
    <RightMargin>0.25in</RightMargin>
    <TopMargin>0.5in</TopMargin>
    <BottomMargin>0.25in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsData">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>2f0879ae-79e1-4f70-b8aa-10ef592a7931</rd:DataSourceID>
    </DataSource>
    <DataSource Name="Brands_FrontDesk">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c829115f-98e3-45e4-aac8-57a3e85b67e4</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="dsCompany">
      <Query>
        <DataSourceName>dsData</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CONUM">
          <DataField>CONUM</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CO_NAME">
          <DataField>CO_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_STREET">
          <DataField>CO_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_CITY">
          <DataField>CO_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_STATE">
          <DataField>CO_STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_ZIP">
          <DataField>CO_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_PHONE">
          <DataField>CO_PHONE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_EXTENSION">
          <DataField>CO_EXTENSION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_FAX">
          <DataField>CO_FAX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="dsDetails">
      <Query>
        <DataSourceName>Brands_FrontDesk</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="cat_descr">
          <DataField>cat_descr</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cat_glnum">
          <DataField>cat_glnum</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cat_subdescr">
          <DataField>cat_subdescr</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="catid">
          <DataField>catid</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="Credit">
          <DataField>Credit</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CurRate">
          <DataField>CurRate</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="Debit">
          <DataField>Debit</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="loc_id">
          <DataField>loc_id</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="Num">
          <DataField>Num</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PrevRate">
          <DataField>PrevRate</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="split_due">
          <DataField>split_due</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="split_paymentregkey">
          <DataField>split_paymentregkey</DataField>
          <rd:TypeName>System.Nullable`1[[System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="TCredits">
          <DataField>TCredits</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="TDebits">
          <DataField>TDebits</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="TempDesc">
          <DataField>TempDesc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>Brands_FrontDesk</rd:DataSetName>
        <rd:TableName>prc_TaxReconDetailsResult</rd:TableName>
        <rd:ObjectDataSourceType>Brands_FrontDesk.prc_TaxReconDetailsResult, Code.dbEPData.designer.vb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="Year">
      <DataType>Integer</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="Quarter">
      <DataType>Integer</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="EffectiveDate">
      <DataType>DateTime</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="IsDebit">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>True</Value>
        </Values>
      </DefaultValue>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Code>Function FormatQuarter(ByVal Quarter As Integer) As String
        Select Case Quarter
            Case 1
                Return "1st"
            Case 2
                Return "2nd"
            Case 3
                Return "3rd"
            Case 4
                Return "4th"
            Case Else
                Return ""
        End Select
End Function

Function TaxDescription(ByVal TaxCode As String, StateCode As String) As String
        Select Case TaxCode
            Case "FUI"
                Return "Federal Unemployment Taxes"
            Case "SUI"
                Return StateCode &amp; " State Unemployment Taxes"
            Case "DIS"
                Return StateCode &amp; " Disability Tax"
            Case "FLI"
                Return StateCode &amp; " Family Leave Tax"
            Case "MTA"
                Return StateCode &amp; " MTA Tax"
            Case Else
                Return StateCode &amp; " " &amp; TaxCode
        End Select
End Function</Code>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>d36aae2a-e286-48f2-969a-6aede4b327f4</rd:ReportID>
</Report>