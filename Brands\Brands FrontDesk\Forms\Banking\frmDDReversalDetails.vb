﻿Imports DevExpress.XtraEditors

Public Class frmDDReversalDetails
    Private Property db As dbEPDataDataContext
    Private Property DD As DD_Reversal_Log
    Sub New(id As Guid)
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        DD = db.DD_Reversal_Logs.Single(Function(d) d.EE_Reversal_NACHA_ID = id)
        If dd.Reversal_Status <> "Drafted EE" Then
            XtraMessageBox.Show("Warning! The Status of the Transaction cannot be changed to Reversal Failed because the current status is not on Drafted EE, Please Evaluate the matter and take the necessary action")
            btnUpdateStatus.Enabled = False
        End If

        dD_Reversal_LogsBindingSource.DataSource = dd
    End Sub

    Private Sub btnUpdateStatus_Click(sender As Object, e As EventArgs) Handles btnUpdateStatus.Click
        Try
            Logger.Debug("DD REVERSAL FAILED Update status: {ID}", DD.ID)
            Dim nextBusDay = Query(Of Date)("SELECT custom.fn_GetNextBusDay(GETDATE(), 1)").FirstOrDefault()
            db.ExecuteCommand($"EXEC custom.prc_CreateACHReconEntry 
                                @AccountKey = 11, @CoNum = {DD.CONUM}, @PayrollNum = {DD.Payroll_num}, @Payment = {DD.AMOUNT}, @Deposit = NULL, @tran_num = 'DD REV FAIL', 
                                @EffectiveDate = '{nextBusDay}', @payee_descr = '{DD.CONUM} - EE {DD.EMPNUM}', @CatID = 1287, @CatCashID = 6, @Descr = 'DD REVERSAL FAILED {DD.CONUM} - EE {DD.EMPNUM}'")

            DD.Reversal_Status = "Reversal Failed"
            If db.SaveChanges Then
                DialogResult = DialogResult.OK
            End If
            SendEmail()
        Catch ex As Exception
            DisplayErrorMessage("Error in update status", ex)
        End Try
    End Sub

    Public Sub SendEmail()
        Try
            Logger.Debug("Entering SendEmail for DD_Reversal_Log ID: {ID}", DD.ID)
            Dim processor = New ReportProcessor(DD.CONUM, "DD Reversal Failed Email", FileType.Pdf) With {.showInRecentReports = False, .showParametersForm = False, .DefaultParamValues = New List(Of KeyValuePair)()}
            processor.DefaultParamValues.Add(New KeyValuePair("@ID", DD.ID))

            Dim result = processor.ProcessReport()
            Dim reportSender = New ReportSender(result) With {.showEmailList = False, .showWebPost = False}
            reportSender.EmailReport()
        Catch ex As Exception
            Logger.Error(ex, "Entering Sending email for DD_Reversal_Log ID: {ID}", DD.ID)
            DisplayErrorMessage($"Entering Sending email for DD_Reversal_Log ID: {DD?.ID}", ex)
        End Try
    End Sub
End Class