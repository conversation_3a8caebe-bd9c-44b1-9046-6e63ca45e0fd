﻿Imports System.ComponentModel
Imports Microsoft.EntityFrameworkCore
Imports Serilog.Context

Public Class frmBrandsPowerGridList

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    Friend PrNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CalendarID As Integer
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property BatchListID As Guid
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property SelectedCalendarRec As view_Calendar
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CallBackForm As Form

    Dim BatchList As pr_batch_list
    Dim NotesData As List(Of NOTE)
    Dim IsNew As Boolean
    Dim PayrollForm As frmBrandsPowerGrid
    Dim DB As dbEPDataDataContext
    'Dim OpenBatchList As List(Of pr_batch_list)
    Dim OpenBatchList As List(Of prc_GetPrOpenBatch_Result)
    Dim _HasNotes As Boolean

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub frmBrandsPowerGridList_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            Me.lblCoNum.Text = "Co #: " & Me.CoNum
            Me.Text = "Brands Power Grid - " & Me.lblCoNum.Text
            LoadData(True)
            SetDisplay(IsNew)
        Catch ex As Exception
            DisplayErrorMessage("Error loading list", ex)
        End Try
    End Sub

    Sub LoadData(ByVal LoadAll As Boolean)
        DB = New dbEPDataDataContext(GetConnectionString)
        DB.CommandTimeout = 300

        If LoadAll Then
            'Dim Schem = (From S In DB.pr_batch_grid_schemas Where S.conum = Me.CoNum And S.name = "Brands").SingleOrDefault
            'If Schem Is Nothing Then
            '    DB.prc_InsBrandsPowerGridTemplate(Me.CoNum)
            '    Schem = (From S In DB.pr_batch_grid_schemas Where S.conum = Me.CoNum And S.name = "Brands").Single
            'End If
            'Dim Q = From BT In DB.pr_batch_totals Where {"In Progress", "Completed"}.Contains(BT.status) And BT.schema_id = Schem.schema_id

            'Dim Q = From BT In DB.pr_batch_totals
            '        Join S In DB.pr_batch_grid_schemas On BT.schema_id Equals S.schema_id
            '        Where BT.conum = Me.CoNum And S.name = "Brands" And {"In Progress", "Completed"}.Contains(BT.status)
            '        Select BT

            OpenBatchList = DB.prc_GetPrOpenBatch(Me.CoNum).ToList ' Q.ToList
            Me.gridBatchLists.DataSource = OpenBatchList

            If OpenBatchList.Count > 0 Then
                BatchListID = OpenBatchList(0).id
            End If
            Me.btnDeletePowerGrid.Enabled = OpenBatchList.Count > 0
            Me.btnEditPowerGrid.Enabled = OpenBatchList.Count > 0
        End If

        Dim pNotes As New List(Of String)

        'Load header
        If BatchListID.Equals(Guid.Empty) Then
            IsNew = True
            'Create New
            Dim Cal = EP_API.PayrollCalendar
            Dim Payroll = (From A In DB.PAYROLLs Where A.CONUM = EP_API.CompanyID AndAlso A.PRNUM = EP_API.PayrollID).Single

            Dim psetEndDate As Date? = Nothing
            'If Not UsePPxLibrary Then
            '    Dim ppx = CType(EP_API(), PPxPayroll)
            '    Dim PayrollInfo = ppx.PayrollAPI.GetSinglePayrollInfo(EP_API.ProviderID, EP_API.CompanyID, EP_API.PayrollID, -1)
            '    Dim pSet = (From A In PayrollInfo.PaySetList Where A.ID = PayrollInfo.FED_UCI.Value.ToString.Substring(0, 1)).FirstOrDefault
            '    If pSet IsNot Nothing Then psetEndDate = pSet.EndDate
            'Else
            Dim PayrollInfo = EP_API.GetSinglePayrollInfo(CoNum, PrNum)
            psetEndDate = PayrollInfo.EndDate
            'End If

            BatchList = New pr_batch_list With {.run_add_checks = "NO",
                                                .run_auto_hours = "NO",
                                                .run_auto_pays = "YES",
                                                .run_auto_deds = "YES",
                                                .run_auto_memos = "YES",
                                                .run_sick = "YES",
                                                .run_vacation = "YES",
                                                .run_personal = "YES",
                                                .run_pay_salary = "YES",
                                                .run_dd_flag = "YES",
                                                .run_override_rate = "DM",
                                                .conum = Me.CoNum,
                                                .status = "In Progress",
                                                .obj_ref = 101,
                                                .provider_id = 0,
                                                .name = String.Format("{0}_{1}_{2}_{3}", Me.CoNum, EP_API.PayrollID, Cal.EndDate.Value.ToString("MM-dd-yyyy"), Cal.CheckDate.Value.ToString("MM-dd-yyyy"))
                                               }
            'BatchList.pr_batch_rows = New List(Of pr_batch_row)
            If Payroll.PR_DESCR & "" = "Bonus Run" Then
                BatchList.run_auto_pays = "NO"
                BatchList.run_auto_deds = "NO"
                BatchList.run_auto_memos = "NO"
                BatchList.run_auto_hours = "NO"
                BatchList.run_add_checks = "NO"
                BatchList.run_personal = "NO"
                BatchList.run_sick = "NO"
                BatchList.run_vacation = "NO"
                Dim BonusOptions = (From A In DB.pr_batch_notes Where A.Conum = Payroll.CONUM AndAlso A.PrNum = Payroll.PRNUM AndAlso A.EmployeeID = -998).SingleOrDefault
                If BonusOptions IsNot Nothing AndAlso BonusOptions.Note.Contains("Turn Off DD: YES") Then
                    BatchList.run_dd_flag = "NO"
                End If
            End If
            If Cal IsNot Nothing Then
                If Cal.CheckDate.Value <> Payroll.CHECK_DATE.Value Then
                    pNotes.Add("Check Date changed from " & Cal.CheckDate.Value.ToShortDateString)
                End If
                'Dim pSet = (From A In PayrollInfo.PaySetList Where A.ID = PayrollInfo.FED_UCI.Value.ToString.Substring(0, 1)).FirstOrDefault
                'If pSet IsNot Nothing AndAlso Cal.EndDate.Value <> pSet.EndDate Then
                If psetEndDate.HasValue AndAlso Cal.EndDate <> psetEndDate Then
                    pNotes.Add("End Date changed from " & Cal.EndDate.Value.ToShortDateString)
                End If
            End If
        Else
            ' LINQ to SQL automatically loads related data when accessed, no Include() needed
            BatchList = (From A In DB.pr_batch_lists Where A.id = BatchListID).Single
        End If
        Me.Pr_batch_listBindingSource.DataSource = BatchList
        Me.TextEdit_PRNum.EditValue = EP_API.PayrollID

        'Disable all options
        For Each cntrl In Me.LayoutControl.Controls
            If TypeOf cntrl Is DevExpress.XtraEditors.BaseEdit Then
                Dim BE As DevExpress.XtraEditors.BaseEdit = cntrl
                If BE.Tag = "PGEditableOptions" Then
                    BE.Properties.ReadOnly = Not IsNew
                End If
            End If
        Next

        If LoadAll Then
            'Load Notes 
            NotesData = (From A In DB.NOTEs Where A.conum = Me.CoNum _
                             AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) _
                             AndAlso {"Employee", "Company", "Payroll"}.Contains(A.category)).ToList
            Me.NOTEBindingSource.DataSource = (From A In NotesData Where A.category <> "Employee"
                                               Order By If(A.priority.StartsWith("3"), 99, 1)
                                               ).ToList
            Dim NotesCount = (From A As NOTE In Me.NOTEBindingSource.List Where Not A.priority.StartsWith("3")).Count
            If NotesCount > 0 Then
                Me.btnLoadEmployees.BackColor = Color.Red
            Else
                Me.btnLoadEmployees.BackColor = Color.Green
            End If
            GridViewEPNotes.BestFitColumns()
        End If
        'If Not BatchListID = Guid.Empty Then
        Me.PrBatchNotes_BindingSource.DataSource = (From A In DB.pr_batch_notes Where A.ListID = Me.BatchListID AndAlso A.EmployeeID Is Nothing).ToList
        ' End If

        For Each n In pNotes
            Dim NewNote As pr_batch_note = Me.PrBatchNotes_BindingSource.AddNew
            NewNote.Note = n
            NewNote.Priority = "3-Low"
            NewNote.rowguid = Guid.NewGuid
        Next

    End Sub

    Private Sub btnLoadEmployees_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLoadEmployees.Click
        Logger.Information("form frmBrandsPowerGridList clicking next with Co#: {CoNum} Pr#: {PrNum}", Me.CoNum, Me.PrNum)
        If Me.PayrollForm Is Nothing Then
            Using LogContext.PushProperty("Conum", Me.CoNum)
                LogContext.PushProperty("Prnum", Me.PrNum)
                Me.PayrollForm = New frmBrandsPowerGrid With {.CoNum = Me.CoNum, .PrNum = Me.PrNum, .BatchList = Me.BatchList, .DatesAndOptions = Me, .CalanderID = CalendarID, .CallBackForm = Me.CallBackForm}
            End Using
            'Me.PayrollForm = New frmBrandsPowerGrid With {.CoNum = Me.CoNum, .BatchList = Me.BatchList, .DatesAndOptions = Me, .CalanderID = EP_API.PayrollCalendar.CalendarID}
        End If
        Me.PayrollForm.LoadData()
        MainForm.ShowForm(PayrollForm)
        'OpenMDIChild(Me.PayrollForm)
        Me.Hide()
    End Sub

    Private Sub frmBrandsPowerGridList_FormClosing(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        If Me.PayrollForm IsNot Nothing Then
            Me.PayrollForm.Dispose()
        End If
    End Sub

    Private Sub gridBatchLists_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gridBatchLists.MouseDoubleClick
        Dim hi = Me.gridViewBatchLists.CalcHitInfo(e.Location)
        If hi.RowHandle >= 0 Then
            EditPowerGrid(sender, e)
        End If
    End Sub

    Sub EditPowerGrid(ByVal sender As System.Object, ByVal e As EventArgs)
        Dim Row As prc_GetPrOpenBatch_Result = Me.gridViewBatchLists.GetRow(Me.gridViewBatchLists.FocusedRowHandle)
        'If Row.id <> Me.BatchListID Then
        Me.BatchListID = Row.id
        LoadData(False)
        If Me.PayrollForm IsNot Nothing Then
            Me.PayrollForm.Dispose()
            Me.PayrollForm = Nothing
        End If
        'End If
        btnLoadEmployees_Click(sender, e)
    End Sub

    Private Sub btnNewPayroll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNewPayroll.Click
        SetDisplay(True)
        BatchListID = Guid.Empty
        If Me.PayrollForm IsNot Nothing Then
            Me.PayrollForm.Dispose()
            Me.PayrollForm = Nothing
        End If
        LoadData(False)
    End Sub

    Sub SetDisplay(ByVal IsNew As Boolean)
        Me.btnNewPayroll.Enabled = Not IsNew
        Me.SplitContainerControl1.Panel1.Enabled = Not IsNew
        Me.btnLoadEmployees.Visible = IsNew
        Me.btnCancelNew.Text = If(IsNew, "Cancel", "Exit")
    End Sub

    Private Sub gridViewNotes_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gridViewNotes.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete Note", AddressOf OnDeleteRowClick, ImageCollection1.Images(0))
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As pr_batch_note = Me.gridViewNotes.GetRow(rowHandle)
        Me.gridViewNotes.DeleteRow(rowHandle)
        If Row IsNot Nothing AndAlso Row.NoteID > 0 Then
            DB.pr_batch_notes.DeleteOnSubmit(Row)
        End If
    End Sub

    Private Sub btnCancelnew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancelNew.Click
        Dim btn As Control = sender
        If btn.Text = "Exit" Then
            Me.Close()
            Me.Dispose()
        Else
            SetDisplay(False)
        End If
    End Sub

    Private Sub btnEditPowerGrid_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEditPowerGrid.Click
        EditPowerGrid(sender, e)
    End Sub

    Private Sub btnDeletePowerGrid_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeletePowerGrid.Click
        Dim Row As prc_GetPrOpenBatch_Result = Me.gridViewBatchLists.GetRow(Me.gridViewBatchLists.FocusedRowHandle)
        If Row IsNot Nothing Then
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to deleted this Power Grid?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                Exit Sub
            End If
            Dim DB As New dbEPDataDataContext(GetConnectionString)
            Dim Ent = (From A In DB.pr_batch_lists Where A.id = Row.id).Single
            Ent.status = "Deleted"
            If DB.SaveChanges() Then
                LoadData(True)
            End If
        End If
    End Sub

    Private Sub btnDateSelection_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDateSelection.Click
        Me.Hide()
        MainForm.OpenPowerGridPayroll(New OpenPowerGridPayrollOptions(Me.CoNum))
    End Sub

    Private Sub btnEditNotes_Click(sender As Object, e As EventArgs) Handles btnEditNotes.Click
        Dim frm = New frmNotes(CoNum, Nothing, "Employee")
        If frm.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
            LoadData(True)
        End If
    End Sub
End Class