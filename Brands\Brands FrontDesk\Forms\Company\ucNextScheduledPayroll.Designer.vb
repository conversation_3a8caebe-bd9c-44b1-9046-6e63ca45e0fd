﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucNextScheduledPayroll
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim GridFormatRule1 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleValue1 As DevExpress.XtraEditors.FormatConditionRuleValue = New DevExpress.XtraEditors.FormatConditionRuleValue()
        Me.colcompleted = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.BindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colfrequency = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colentry_type = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colprocess_date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colend_date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colcheck_date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colProcessTime = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.panelChangeProcessTime = New DevExpress.XtraEditors.PanelControl()
        Me.gcChangeProcessTime = New DevExpress.XtraEditors.GroupControl()
        Me.cbeProcessTime = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.btnCloseProcessTimePopUp = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSetProcessTime = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.panelChangeProcessTime, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.panelChangeProcessTime.SuspendLayout()
        CType(Me.gcChangeProcessTime, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gcChangeProcessTime.SuspendLayout()
        CType(Me.cbeProcessTime.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'colcompleted
        '
        Me.colcompleted.Caption = "Completed"
        Me.colcompleted.FieldName = "completed"
        Me.colcompleted.Name = "colcompleted"
        Me.colcompleted.Visible = True
        Me.colcompleted.VisibleIndex = 6
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.BindingSource1
        Me.GridControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControl1.Location = New System.Drawing.Point(0, 0)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(781, 510)
        Me.GridControl1.TabIndex = 0
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'BindingSource1
        '
        Me.BindingSource1.DataSource = GetType(Brands_FrontDesk.fn_NextScheduledPayrollResult)
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colfrequency, Me.colentry_type, Me.colprocess_date, Me.colend_date, Me.colcheck_date, Me.colProcessTime, Me.colcompleted})
        GridFormatRule1.ApplyToRow = True
        GridFormatRule1.Column = Me.colcompleted
        GridFormatRule1.Name = "Format0"
        FormatConditionRuleValue1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        FormatConditionRuleValue1.Appearance.Options.HighPriority = True
        FormatConditionRuleValue1.Appearance.Options.UseBackColor = True
        FormatConditionRuleValue1.Condition = DevExpress.XtraEditors.FormatCondition.Equal
        FormatConditionRuleValue1.Value1 = "YES"
        GridFormatRule1.Rule = FormatConditionRuleValue1
        Me.GridView1.FormatRules.Add(GridFormatRule1)
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsBehavior.ReadOnly = True
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colfrequency
        '
        Me.colfrequency.Caption = "Frequency"
        Me.colfrequency.FieldName = "frequency"
        Me.colfrequency.Name = "colfrequency"
        Me.colfrequency.Visible = True
        Me.colfrequency.VisibleIndex = 0
        '
        'colentry_type
        '
        Me.colentry_type.Caption = "Entry Type"
        Me.colentry_type.FieldName = "entry_type"
        Me.colentry_type.Name = "colentry_type"
        Me.colentry_type.Visible = True
        Me.colentry_type.VisibleIndex = 1
        '
        'colprocess_date
        '
        Me.colprocess_date.Caption = "Process Date"
        Me.colprocess_date.FieldName = "process_date"
        Me.colprocess_date.Name = "colprocess_date"
        Me.colprocess_date.Visible = True
        Me.colprocess_date.VisibleIndex = 2
        '
        'colend_date
        '
        Me.colend_date.Caption = "End Date"
        Me.colend_date.FieldName = "end_date"
        Me.colend_date.Name = "colend_date"
        Me.colend_date.Visible = True
        Me.colend_date.VisibleIndex = 3
        '
        'colcheck_date
        '
        Me.colcheck_date.Caption = "Check Date"
        Me.colcheck_date.FieldName = "check_date"
        Me.colcheck_date.Name = "colcheck_date"
        Me.colcheck_date.Visible = True
        Me.colcheck_date.VisibleIndex = 4
        '
        'colProcessTime
        '
        Me.colProcessTime.FieldName = "ProcessTime"
        Me.colProcessTime.Name = "colProcessTime"
        Me.colProcessTime.Visible = True
        Me.colProcessTime.VisibleIndex = 5
        '
        'panelChangeProcessTime
        '
        Me.panelChangeProcessTime.Controls.Add(Me.gcChangeProcessTime)
        Me.panelChangeProcessTime.Location = New System.Drawing.Point(275, 203)
        Me.panelChangeProcessTime.Name = "panelChangeProcessTime"
        Me.panelChangeProcessTime.Size = New System.Drawing.Size(199, 105)
        Me.panelChangeProcessTime.TabIndex = 4
        Me.panelChangeProcessTime.Visible = False
        '
        'gcChangeProcessTime
        '
        Me.gcChangeProcessTime.Controls.Add(Me.cbeProcessTime)
        Me.gcChangeProcessTime.Controls.Add(Me.LabelControl6)
        Me.gcChangeProcessTime.Controls.Add(Me.btnCloseProcessTimePopUp)
        Me.gcChangeProcessTime.Controls.Add(Me.btnSetProcessTime)
        Me.gcChangeProcessTime.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gcChangeProcessTime.Location = New System.Drawing.Point(2, 2)
        Me.gcChangeProcessTime.Name = "gcChangeProcessTime"
        Me.gcChangeProcessTime.Size = New System.Drawing.Size(195, 101)
        Me.gcChangeProcessTime.TabIndex = 3
        Me.gcChangeProcessTime.Text = "Chnage Process Time"
        '
        'cbeProcessTime
        '
        Me.cbeProcessTime.Location = New System.Drawing.Point(51, 31)
        Me.cbeProcessTime.Name = "cbeProcessTime"
        Me.cbeProcessTime.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeProcessTime.Properties.Items.AddRange(New Object() {"07:00AM", "07:30AM", "08:00AM", "08:30AM", "09:00AM", "09:30AM", "10:00AM", "10:30AM", "11:00AM", "11:30AM", "12:00PM", "12:30PM", "01:00PM", "01:30PM", "02:00PM", "02:30PM", "03:00PM", "03:30PM", "04:00PM"})
        Me.cbeProcessTime.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeProcessTime.Size = New System.Drawing.Size(139, 20)
        Me.cbeProcessTime.TabIndex = 54
        '
        'LabelControl6
        '
        Me.LabelControl6.Location = New System.Drawing.Point(16, 34)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(29, 13)
        Me.LabelControl6.TabIndex = 53
        Me.LabelControl6.Text = "Time: "
        '
        'btnCloseProcessTimePopUp
        '
        Me.btnCloseProcessTimePopUp.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.btnCloseProcessTimePopUp.Appearance.ForeColor = System.Drawing.Color.Green
        Me.btnCloseProcessTimePopUp.Appearance.Options.UseFont = True
        Me.btnCloseProcessTimePopUp.Appearance.Options.UseForeColor = True
        Me.btnCloseProcessTimePopUp.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.btnCloseProcessTimePopUp.Location = New System.Drawing.Point(172, -2)
        Me.btnCloseProcessTimePopUp.Name = "btnCloseProcessTimePopUp"
        Me.btnCloseProcessTimePopUp.Size = New System.Drawing.Size(25, 22)
        Me.btnCloseProcessTimePopUp.TabIndex = 52
        Me.btnCloseProcessTimePopUp.Text = "X"
        '
        'btnSetProcessTime
        '
        Me.btnSetProcessTime.Location = New System.Drawing.Point(70, 74)
        Me.btnSetProcessTime.Name = "btnSetProcessTime"
        Me.btnSetProcessTime.Size = New System.Drawing.Size(120, 22)
        Me.btnSetProcessTime.TabIndex = 47
        Me.btnSetProcessTime.Text = "Change Process Time"
        '
        'ucNextScheduledPayroll
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.panelChangeProcessTime)
        Me.Controls.Add(Me.GridControl1)
        Me.Name = "ucNextScheduledPayroll"
        Me.Size = New System.Drawing.Size(781, 510)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.panelChangeProcessTime, System.ComponentModel.ISupportInitialize).EndInit()
        Me.panelChangeProcessTime.ResumeLayout(False)
        CType(Me.gcChangeProcessTime, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gcChangeProcessTime.ResumeLayout(False)
        Me.gcChangeProcessTime.PerformLayout()
        CType(Me.cbeProcessTime.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents BindingSource1 As System.Windows.Forms.BindingSource
    Friend WithEvents colfrequency As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colentry_type As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colprocess_date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colcheck_date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colProcessTime As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colend_date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents panelChangeProcessTime As DevExpress.XtraEditors.PanelControl
    Friend WithEvents gcChangeProcessTime As DevExpress.XtraEditors.GroupControl
    Friend WithEvents cbeProcessTime As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnCloseProcessTimePopUp As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSetProcessTime As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents colcompleted As DevExpress.XtraGrid.Columns.GridColumn
End Class
