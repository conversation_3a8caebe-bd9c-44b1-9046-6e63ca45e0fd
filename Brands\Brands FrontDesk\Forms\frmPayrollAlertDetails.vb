﻿Imports System.ComponentModel

Public Class frmPayrollAlertDetails

    'Property AlertID As Integer
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PayrollNum As Decimal
    Private _Reason As String

    Dim DB As dbEPDataDataContext
    Dim AlertEnt As PayrollAlert
    Dim PayrollAlerts As List(Of PayrollAlert)

    Private Sub frmPayrollAlertDetails_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler DescriptionTextBox.DataBindings("Text").Format, AddressOf FormatDescription
        DB = New dbEPDataDataContext(GetConnectionString)
        Me.CoNameTextBox.Text = (From A In DB.COMPANies Where A.CONUM = CoNum Select A.CO_NAME).SingleOrDefault
        BindList()
    End Sub

    Sub BindList()
        PayrollAlerts = (From A In DB.PayrollAlerts Where A.CoNum = CoNum AndAlso A.PrNum = PayrollNum Order By If(A.ApprovedBy Is Nothing, 1, 2), If(A.EmpNum.HasValue, 1, 2), A.Date).ToList
        Me.PayrollAlertBindingSource.DataSource = PayrollAlerts
        Me.PayrollAlertBindingSource.Position = 0
    End Sub

    Private Sub btnApprove_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnApprove.Click
        If String.IsNullOrEmpty(Me.ApprovedDescriptionTextBox.Text) Then
            MessageBox.Show("Please enter a reason")
            Exit Sub
        End If

        Me.Invalidate()
        AlertEnt.ApprovedBy = UserName
        AlertEnt.ApprovedOn = Now
        _Reason = Me.ApprovedDescriptionTextBox.Text
        DB.SubmitChanges()
        BindList()
        If (From A In PayrollAlerts Where A.ApprovedBy Is Nothing).Count = 0 Then
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
            Me.Close()
        End If
    End Sub

    Private Sub gridAlerts_CellFormatting(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) Handles gridAlerts.CellFormatting
        Dim data As PayrollAlert = gridAlerts.Rows(e.RowIndex).DataBoundItem
        If data IsNot Nothing Then
            If data.ApprovedBy IsNot Nothing Then
                e.CellStyle.BackColor = Color.LightGreen
            ElseIf data.Alert = "Neg Net or less than $1" AndAlso data.Description.Contains("-") Then
                e.CellStyle.BackColor = Color.Yellow
            End If
        End If
    End Sub

    Private Sub FormatDescription(ByVal sender As Object, ByVal e As ConvertEventArgs)
        If e.Value IsNot Nothing Then
            e.Value = e.Value.ToString.Replace("~", vbNewLine)
        End If
    End Sub

    Private Sub PayrollAlertBindingSource_CurrentChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PayrollAlertBindingSource.CurrentChanged
        AlertEnt = PayrollAlertBindingSource.Current
        If AlertEnt.EmpNum.HasValue Then
            Dim EmpName = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.EMPNUM = AlertEnt.EmpNum
                           Select A.L_NAME, A.F_NAME, A.M_NAME).SingleOrDefault
            Me.EmpNameTextBox.Text = EmpName.L_NAME & ", " & EmpName.F_NAME & " " & EmpName.M_NAME
        Else
            Me.EmpNameTextBox.Text = ""
        End If

        Dim B = AlertEnt.Alert = "Payroll Total out of last 4 average" Or AlertEnt.Alert = "Tax liability out of last 4 average"
        Me.ExcludePayrollFromFutureCalculationsCheckBox.Visible = B
        Me.ExcludeTaxFromFutureCalculationsCheckBox.Visible = B
        Me.lblExclude1.Visible = B
        Me.lblExclude2.Visible = B
    End Sub

    Private Sub btnMove_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMovePrev.Click, btnMoveNext.Click
        Dim frm As frmPayrollAlerts = MainForm.GetDocument(GetType(frmPayrollAlerts)).Control ' MainForm.PageManager.Pages(frmPayrollAlerts).MdiChild ' My.Forms.frmMain.Panel1.Controls(0)
        Dim data = frm.PayrollAlertBindingSource
        If CType(sender, Button).Name = "btnMovePrev" Then
            data.MovePrevious()
        Else
            data.MoveNext()
        End If
        _Reason = String.Empty
        Dim row As view_PayrollAletsGrouped = data.Current
        Me.PayrollNum = row.PrNum
        Me.CoNum = row.CoNum
        Me.CoNameTextBox.Text = row.CO_NAME()
        Me.BindList()
    End Sub

    Private Sub PayrollAlertBindingSource_BindingComplete(ByVal sender As System.Object, ByVal e As System.Windows.Forms.BindingCompleteEventArgs) Handles PayrollAlertBindingSource.BindingComplete
        If String.IsNullOrEmpty(Me.ApprovedDescriptionTextBox.Text) AndAlso Not String.IsNullOrEmpty(_Reason) Then _
                        Me.ApprovedDescriptionTextBox.Text = _Reason
    End Sub
End Class