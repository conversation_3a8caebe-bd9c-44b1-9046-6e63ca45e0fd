<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="Brands_FrontDesk">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>d8d42b7e-f37d-4fc2-b95b-54687321de06</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="dsOrder">
      <Fields>
        <Field Name="ByUser">
          <DataField>ByUser</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CoNum">
          <DataField>CoNum</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="Date">
          <DataField>Date</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ID">
          <DataField>ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="NextPayroll">
          <DataField>NextPayroll</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SendWithNextPayroll">
          <DataField>SendWithNextPayroll</DataField>
          <rd:TypeName>System.Nullable`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="Notes">
          <DataField>Notes</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Brands_FrontDesk</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>Brands_FrontDesk</rd:DataSetName>
        <rd:TableName>SuppliesOrder</rd:TableName>
        <rd:ObjectDataSourceType>Brands_FrontDesk.SuppliesOrder, dbEPData.designer.vb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="dsCompany">
      <Fields>
        <Field Name="BA_ALTPAYEE">
          <DataField>BA_ALTPAYEE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_CITY">
          <DataField>BA_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_EXTENSION">
          <DataField>BA_EXTENSION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_FAX">
          <DataField>BA_FAX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_MODEM">
          <DataField>BA_MODEM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_NAME">
          <DataField>BA_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_PHONE">
          <DataField>BA_PHONE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_ROUTING_NUM">
          <DataField>BA_ROUTING_NUM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_STATE">
          <DataField>BA_STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_STREET">
          <DataField>BA_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BA_ZIP">
          <DataField>BA_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BAPAYEE_CITY">
          <DataField>BAPAYEE_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BAPAYEE_NAME">
          <DataField>BAPAYEE_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BAPAYEE_ST">
          <DataField>BAPAYEE_ST</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BAPAYEE_STREET">
          <DataField>BAPAYEE_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BAPAYEE_ZIP">
          <DataField>BAPAYEE_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_CITY">
          <DataField>BI_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_EXTENSION">
          <DataField>BI_EXTENSION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_FAX">
          <DataField>BI_FAX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_MODEM">
          <DataField>BI_MODEM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_NAME">
          <DataField>BI_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_PHONE">
          <DataField>BI_PHONE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_STATE">
          <DataField>BI_STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_STREET">
          <DataField>BI_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BI_ZIP">
          <DataField>BI_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BILLSAME">
          <DataField>BILLSAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CASHPR">
          <DataField>CASHPR</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CH_CITY">
          <DataField>CH_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CH_NAME">
          <DataField>CH_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CH_STATE">
          <DataField>CH_STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CH_STREET">
          <DataField>CH_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CH_ZIP">
          <DataField>CH_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CHKSAME">
          <DataField>CHKSAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_ACCT_TYPE">
          <DataField>CO_ACCT_TYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_CITY">
          <DataField>CO_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_COUNTY">
          <DataField>CO_COUNTY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_DBA">
          <DataField>CO_DBA</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_EMAIL">
          <DataField>CO_EMAIL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_EXTENSION">
          <DataField>CO_EXTENSION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_FAX">
          <DataField>CO_FAX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_MODEM">
          <DataField>CO_MODEM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_MSG">
          <DataField>CO_MSG</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_NAME">
          <DataField>CO_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_PAY_PD1">
          <DataField>CO_PAY_PD1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_PAY_PD2">
          <DataField>CO_PAY_PD2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_PAY_PD3">
          <DataField>CO_PAY_PD3</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_PHONE">
          <DataField>CO_PHONE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_SICCODES">
          <DataField>CO_SICCODES</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_STATE">
          <DataField>CO_STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_STATUS">
          <DataField>CO_STATUS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_STREET">
          <DataField>CO_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CO_ZIP">
          <DataField>CO_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="colockout">
          <DataField>colockout</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CONUM">
          <DataField>CONUM</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CUR_PRNUM">
          <DataField>CUR_PRNUM</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_CHK_DT_1">
          <DataField>CURPRD_CHK_DT_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_CHK_DT_2">
          <DataField>CURPRD_CHK_DT_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_CHK_DT_3">
          <DataField>CURPRD_CHK_DT_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_END_DT_1">
          <DataField>CURPRD_END_DT_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_END_DT_2">
          <DataField>CURPRD_END_DT_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_END_DT_3">
          <DataField>CURPRD_END_DT_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_STRT_DT_1">
          <DataField>CURPRD_STRT_DT_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_STRT_DT_2">
          <DataField>CURPRD_STRT_DT_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="CURPRD_STRT_DT_3">
          <DataField>CURPRD_STRT_DT_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="DD_CMP">
          <DataField>DD_CMP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DEL_INST">
          <DataField>DEL_INST</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DELVDESC">
          <DataField>DELVDESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DT_RATE">
          <DataField>DT_RATE</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="eelockout">
          <DataField>eelockout</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ENTRY_TYPE">
          <DataField>ENTRY_TYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EOM_FG_1">
          <DataField>EOM_FG_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="EOM_FG_2">
          <DataField>EOM_FG_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="EOM_FG_3">
          <DataField>EOM_FG_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="EstAnnRev">
          <DataField>EstAnnRev</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="FED_CHK">
          <DataField>FED_CHK</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FED_DEP_STAT">
          <DataField>FED_DEP_STAT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FED_ID">
          <DataField>FED_ID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FED_WH_EXE_FGC">
          <DataField>FED_WH_EXE_FGC</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="FEDUCI">
          <DataField>FEDUCI</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FISCAL_DATE">
          <DataField>FISCAL_DATE</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="FUTA_EXE_FGC">
          <DataField>FUTA_EXE_FGC</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="lease_id">
          <DataField>lease_id</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="LOC_CHK">
          <DataField>LOC_CHK</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEDICARE_EXE_FGC">
          <DataField>MEDICARE_EXE_FGC</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="MEET_MIN">
          <DataField>MEET_MIN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OASDI_EXE_FGC">
          <DataField>OASDI_EXE_FGC</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="office_id">
          <DataField>office_id</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="oktoemailpwd">
          <DataField>oktoemailpwd</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OP_OWNER">
          <DataField>OP_OWNER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OT_RATE">
          <DataField>OT_RATE</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PR_ACCT_NUM">
          <DataField>PR_ACCT_NUM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PR_CONTACT">
          <DataField>PR_CONTACT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PR_PASSWORD">
          <DataField>PR_PASSWORD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRD1">
          <DataField>PRD1</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRD2">
          <DataField>PRD2</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRD3">
          <DataField>PRD3</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_CHK_DT_1">
          <DataField>PRVPRD_CHK_DT_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_CHK_DT_2">
          <DataField>PRVPRD_CHK_DT_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_CHK_DT_3">
          <DataField>PRVPRD_CHK_DT_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_END_DT_1">
          <DataField>PRVPRD_END_DT_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_END_DT_2">
          <DataField>PRVPRD_END_DT_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_END_DT_3">
          <DataField>PRVPRD_END_DT_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_STRT_DT_1">
          <DataField>PRVPRD_STRT_DT_1</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_STRT_DT_2">
          <DataField>PRVPRD_STRT_DT_2</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="PRVPRD_STRT_DT_3">
          <DataField>PRVPRD_STRT_DT_3</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="QTR_NO1">
          <DataField>QTR_NO1</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="QTR_NO2">
          <DataField>QTR_NO2</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="QTR_NO3">
          <DataField>QTR_NO3</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="rowguid">
          <DataField>rowguid</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="SALES_TAX_EXE">
          <DataField>SALES_TAX_EXE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SALESPERSON">
          <DataField>SALESPERSON</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SETUP_DATE">
          <DataField>SETUP_DATE</DataField>
          <rd:TypeName>System.Nullable`1[[System.DateTime, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="SH_CITY">
          <DataField>SH_CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_CONFOR">
          <DataField>SH_CONFOR</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_EXTENSION">
          <DataField>SH_EXTENSION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_FAX">
          <DataField>SH_FAX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_MODEM">
          <DataField>SH_MODEM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_NAME">
          <DataField>SH_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_PHONE">
          <DataField>SH_PHONE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_STATE">
          <DataField>SH_STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_STREET">
          <DataField>SH_STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SH_ZIP">
          <DataField>SH_ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SHIPSAME">
          <DataField>SHIPSAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ST_CHK">
          <DataField>ST_CHK</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="START_CHK_NUM">
          <DataField>START_CHK_NUM</DataField>
          <rd:TypeName>System.Nullable`1[[System.Decimal, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="STUCI">
          <DataField>STUCI</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="timestamp">
          <DataField>timestamp</DataField>
          <rd:TypeName>System.Data.Linq.Binary</rd:TypeName>
        </Field>
        <Field Name="TRAN_BANK_BILL">
          <DataField>TRAN_BANK_BILL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TRAN_BANK_DD">
          <DataField>TRAN_BANK_DD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TRAN_BANK_TTF">
          <DataField>TRAN_BANK_TTF</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="web_co_msg">
          <DataField>web_co_msg</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Brands_FrontDesk</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>Brands_FrontDesk</rd:DataSetName>
        <rd:TableName>COMPANY</rd:TableName>
        <rd:ObjectDataSourceType>Brands_FrontDesk.COMPANY, dbEPData.designer.vb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="dsItems">
      <Fields>
        <Field Name="ID">
          <DataField>ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="OrderID">
          <DataField>OrderID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ProductID">
          <DataField>ProductID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Qty">
          <DataField>Qty</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SuppliesOrder">
          <DataField>SuppliesOrder</DataField>
          <rd:TypeName>Brands_FrontDesk.SuppliesOrder</rd:TypeName>
        </Field>
        <Field Name="Supply">
          <DataField>Supply</DataField>
          <rd:TypeName>Brands_FrontDesk.Supply</rd:TypeName>
        </Field>
        <Field Name="UnitPrice">
          <DataField>UnitPrice</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="UOM">
          <DataField>UOM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ProductName">
          <DataField>ProductName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Brands_FrontDesk</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>Brands_FrontDesk</rd:DataSetName>
        <rd:TableName>SuppliesOrderItem</rd:TableName>
        <rd:ObjectDataSourceType>Brands_FrontDesk.SuppliesOrderItem, dbEPData.designer.vb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.94792in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>1.26388in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Rectangle Name="Rectangle1">
                      <ReportItems>
                        <Textbox Name="CO_NAME">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIf(Fields!SHIPSAME.Value = "YES", Fields!CO_NAME.Value, Fields!SH_NAME.Value)</Value>
                                  <Style>
                                    <FontFamily>Tahoma</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CO_NAME</rd:DefaultName>
                          <Top>0.46527in</Top>
                          <Height>0.25in</Height>
                          <Width>2.94792in</Width>
                          <Style>
                            <Border>
                              <Color>#e5e5e5</Color>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="CO_STREET">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(Fields!SHIPSAME.Value = "YES", Fields!CO_STREET.Value, Fields!SH_STREET.Value)</Value>
                                  <Style>
                                    <FontFamily>Tahoma</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(Fields!SHIPSAME.Value = "YES", Fields!CO_CITY.Value &amp; ", " &amp; Fields!CO_STATE.Value &amp; " " &amp; Fields!CO_ZIP.Value, Fields!SH_CITY.Value &amp; ", " &amp; Fields!SH_STATE.Value &amp; " " &amp; Fields!SH_ZIP.Value)</Value>
                                  <Style>
                                    <FontFamily>Tahoma</FontFamily>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CO_STREET</rd:DefaultName>
                          <Top>0.75694in</Top>
                          <Height>0.4375in</Height>
                          <Width>2.94792in</Width>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <Border>
                              <Color>#e5e5e5</Color>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="CONUM2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CONUM.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CONUM</rd:DefaultName>
                          <Top>0.19444in</Top>
                          <Height>0.20139in</Height>
                          <Width>2.94792in</Width>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </ReportItems>
                      <KeepTogether>true</KeepTogether>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </Rectangle>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details1" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>dsCompany</DataSetName>
        <Top>0.80054in</Top>
        <Left>0.03125in</Left>
        <Height>1.26388in</Height>
        <Width>2.94792in</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox57">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>SUPPLIES ORDER</Value>
                <Style>
                  <FontSize>12pt</FontSize>
                  <FontWeight>Bold</FontWeight>
                </Style>
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox57</rd:DefaultName>
        <Top>0.18417in</Top>
        <Height>0.25in</Height>
        <Width>3.46875in</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Tablix Name="Tablix4">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>3.46875in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.53805in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Rectangle Name="Rectangle2">
                      <ReportItems>
                        <Textbox Name="Notes">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Notes.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Notes</rd:DefaultName>
                          <Height>0.53805in</Height>
                          <Width>3.46875in</Width>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </ReportItems>
                      <KeepTogether>true</KeepTogether>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                      </Style>
                    </Rectangle>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details3" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>dsOrder</DataSetName>
        <Top>1.94276in</Top>
        <Left>3.52083in</Left>
        <Height>0.53805in</Height>
        <Width>3.46875in</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>1.34375in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.57292in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox6">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Order #</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox6</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#949ca4</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox8">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ID.Value</Value>
                              <Style>
                                <Color>#4d4d4d</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox8</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#e5e5e5</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25347in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox38">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Received By</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Bold</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox38</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#949ca4</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ByUser">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ByUser.Value</Value>
                              <Style>
                                <Color>#4d4d4d</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ByUser</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#e5e5e5</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25347in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox5">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Order Date</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox5</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#949ca4</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Date">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Date.Value</Value>
                              <Style>
                                <Color>#4d4d4d</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Date</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#e5e5e5</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25347in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox9">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>With Next Payroll</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox9</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#949ca4</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="SendWithNextPayroll">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=IIf(Fields!NextPayroll.Value.ToString().StartsWith("Paperless"), "N/A", IIf(Fields!SendWithNextPayroll.Value, "Yes", "No"))</Value>
                              <Style>
                                <Color>#4d4d4d</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>SendWithNextPayroll</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#e5e5e5</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25347in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox7">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Next Payroll</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox7</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#949ca4</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="NextPayroll">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!NextPayroll.Value</Value>
                              <Style>
                                <Color>#4d4d4d</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>NextPayroll</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>#e5e5e5</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>dsOrder</DataSetName>
        <Top>0.4811in</Top>
        <Left>3.5in</Left>
        <Height>1.26388in</Height>
        <Width>2.91667in</Width>
        <ZIndex>3</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Tablix Name="Tablix3">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>3.375in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.46875in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.54167in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox17">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Product Name</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox17</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox19">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Qty</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox19</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox21">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Unit</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox21</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Unit Price</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox3">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Amount</Value>
                              <Style>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox3</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>Yellow</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.25in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ProductName">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ProductName.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ProductName</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Qty">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Qty.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Qty</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="UOM">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!UOM.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>UOM</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="UnitPrice">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!UnitPrice.Value</Value>
                              <Style>
                                <Format>'$'0.00;('$'0.00)</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>UnitPrice</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <rd:FormatSymbolCulture>en-US</rd:FormatSymbolCulture>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox4">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Qty.Value * Fields!UnitPrice.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox4</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details2" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>dsItems</DataSetName>
        <Top>2.81249in</Top>
        <Left>0.03125in</Left>
        <Height>0.5in</Height>
        <Width>6.38542in</Width>
        <ZIndex>4</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox2">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>="Shipping  - " &amp; First(Fields!DELVDESC.Value, "dsCompany")</Value>
                <Style />
              </TextRun>
            </TextRuns>
            <Style />
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox2</rd:DefaultName>
        <Top>3.38193in</Top>
        <Left>0.03125in</Left>
        <Height>0.25in</Height>
        <Width>6.38542in</Width>
        <ZIndex>5</ZIndex>
        <Visibility>
          <Hidden>=First(Fields!SendWithNextPayroll.Value, "dsOrder")</Hidden>
        </Visibility>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
    </ReportItems>
    <Height>6.62318in</Height>
    <Style />
  </Body>
  <Width>6.98958in</Width>
  <Page>
    <LeftMargin>1in</LeftMargin>
    <RightMargin>1in</RightMargin>
    <TopMargin>0.25in</TopMargin>
    <BottomMargin>0.25in</BottomMargin>
    <Style />
  </Page>
  <rd:ReportID>f04e05dd-34c9-4251-aa3f-a69f4a456d29</rd:ReportID>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
</Report>