﻿Public Class frmUpdateNYDependentHealth

    Private _coNum As Decimal
    Dim db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
    End Sub

    Sub New(coNum As Decimal)
        InitializeComponent()
        _coNum = coNum
    End Sub

    Private Sub frmUpdateNYDependentHealth_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        bsCompanyList.DataSource = db.view_CompanySumarries.ToList()
        SearchLookUpEdit1.EditValue = _coNum
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click, btnSaveAndClose.Click
        If db.SaveChanges Then
            SearchLookUpEdit1.EditValue = Nothing
            db = New dbEPDataDataContext(GetConnectionString)
            If sender Is btnSaveAndClose Then
                DialogResult = DialogResult.OK
            End If
        End If
    End Sub

    Private Sub SearchLookUpEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles SearchLookUpEdit1.EditValueChanged
        If SearchLookUpEdit1.EditValue IsNot Nothing AndAlso SearchLookUpEdit1.Text.IsNotNullOrWhiteSpace Then
            db = New dbEPDataDataContext(GetConnectionString)
            Dim co As Decimal = SearchLookUpEdit1.EditValue
            Dim udf = (From u In db.CO_UDFs Where u.CONUM = co AndAlso u.UDF_DESCR = "%NYDependentHealth").FirstOrDefault
            If udf Is Nothing Then
                Text = "Add NY Dependent Health"
                udf = New CO_UDF With {.CONUM = SearchLookUpEdit1.EditValue, .UDF_DESCR = "%NYDependentHealth"}
                db.CO_UDFs.InsertOnSubmit(udf)
            Else
                Text = "Update NY Dependent Health"
            End If
            bsCoUdf.DataSource = udf
            GroupBox1.Enabled = True
        Else
            GroupBox1.Enabled = False
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub
End Class