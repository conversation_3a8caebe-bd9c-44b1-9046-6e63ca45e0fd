﻿Imports System.Collections.ObjectModel
Imports System.ComponentModel
Public Class frmShareReport

    ReadOnly _reportLayoutId As Integer
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ExistingUsers As List(Of CustomReportLayoutShare)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AvailableUsers As ObservableCollection(Of String)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AddedUsers As ObservableCollection(Of String)
    Public Sub New(_reportLayoutId As Integer)
        Me._reportLayoutId = _reportLayoutId
        InitializeComponent()
    End Sub

    Private Sub frmShareReport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            ExistingUsers = db.CustomReportLayoutShares.Where(Function(s) s.ReportLayoutId = _reportLayoutId).ToList
            Dim allEpUsers = db.view_epsusers.ToList()
            AddedUsers = New ObservableCollection(Of String)(db.view_epsusers.Where(Function(u) ExistingUsers.Select(Function(un) un.username).Contains(u.username)).Select(Function(u) u.username).ToList)
            AvailableUsers = New ObservableCollection(Of String)(allEpUsers.Select(Function(u) u.username).Except(ExistingUsers.Select(Function(un) un.username)).ToList)

            lbcAvailableUsers.DataSource = AvailableUsers
            lbcAddedUsers.DataSource = AddedUsers
        Catch ex As Exception
            Logger.Error(ex, "Error loading users")
            DisplayMessageBox("Error loading users")
            Close()
        End Try
    End Sub

    Private Sub btnAddUser_Click(sender As Object, e As EventArgs) Handles btnAddUser.Click
        Dim username = lbcAvailableUsers.SelectedItem
        If username IsNot Nothing Then
            AvailableUsers.Remove(username)
            AddedUsers.Add(username)
        End If
    End Sub

    Private Sub btnRemoveUser_Click(sender As Object, e As EventArgs) Handles btnRemoveUser.Click
        Dim username = lbcAddedUsers.SelectedItem
        If username IsNot Nothing Then
            AddedUsers.Remove(username)
            AvailableUsers.Add(username)
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Using ctxDB = New dbEPDataDataContext(GetAdminConnectionString)
                Dim usersToAdd = AddedUsers.Where(Function(u) Not ExistingUsers.Select(Function(l) l.username).Contains(u)).ToList
                Dim usersToRemove = AvailableUsers.Where(Function(u) ExistingUsers.Select(Function(l) l.username).Contains(u)).ToList

                For Each user In usersToAdd
                    ctxDB.CustomReportLayoutShares.InsertOnSubmit(New CustomReportLayoutShare() With {.ReportLayoutId = _reportLayoutId, .username = user})
                Next

                For Each user In usersToRemove
                    Dim share = ctxDB.CustomReportLayoutShares.Single(Function(u) u.ReportLayoutId = _reportLayoutId AndAlso u.username = user)
                    ctxDB.CustomReportLayoutShares.DeleteOnSubmit(share)
                Next

                If ctxDB.SaveChanges Then
                    DialogResult = DialogResult.OK
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error saving report tables links", ex)
        End Try
    End Sub
End Class