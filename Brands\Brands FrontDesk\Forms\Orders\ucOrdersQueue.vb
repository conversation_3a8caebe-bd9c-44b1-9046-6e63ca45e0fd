﻿Imports System.Collections.ObjectModel
Imports System.Data
Imports System.IO
Imports Humanizer

Public Class ucOrdersQueue

    Private Property OrderList As ObservableCollection(Of SuppliesOrder)

    Private ReadOnly Property CurrentOrder As SuppliesOrder
        Get
            Return bsOrders.Current
        End Get
    End Property

    Public Sub New()
        InitializeComponent()
        TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        LayoutControl1.Dock = DockStyle.Fill
        AddHandler modSignalRClient.OnSuppliesOrderUpdated, AddressOf SignalRUpdates
    End Sub

    Private Sub SignalRUpdates(sender As Object, e As EventArgs)
        Try
            If IsDisposed OrElse Disposing Then
                RemoveHandler modSignalRClient.OnSuppliesOrderUpdated, AddressOf SignalRUpdates
                Exit Sub
            End If

            If sender Is Nothing Then
                LoadData()
                Exit Sub
            End If

            If Visible = False Then
                Exit Sub
            End If

            Dim order As SuppliesOrder = sender
            Logger.Information("Entering SignalRUpdates OrderId: {OrderId}", order.ID)
            If order.Status = "Open" OrElse order.Status = $"In Progress - {Environment.MachineName}" Then
                If Not OrderList.Contains(order) Then
                    Dim lastOrder = OrderList.FirstOrDefault(Function(o) o.Date > order.Date)
                    If lastOrder IsNot Nothing Then
                        OrderList.Insert(OrderList.IndexOf(lastOrder), order)
                    Else
                        OrderList.Add(order)
                    End If
                Else
                    OrderList(OrderList.IndexOf(order)) = order
                End If
            Else
                OrderList.Remove(order)
            End If

            bsOrders.ResetBindings(False)
            LoadPendingOrdersCount()
            If OrderList.Count = 1 Then
                bsOrders.Position = 0
                'My.Computer.Audio.Play(My.Resources._Error, AudioPlayMode.Background)
                Media.SystemSounds.Exclamation.Play()
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in SignalRUpdates")
        End Try
    End Sub

    Private Sub ucOrdersQueue_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If DesignMode Then Exit Sub
        LoadData()
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                ctxDB.ObjectTrackingEnabled = False
                Dim companies = ctxDB.view_CompanySumarries.ToList
                slueCoNum.Properties.DataSource = companies
                riOrderCo.DataSource = companies
                Dim orders = ctxDB.SuppliesOrders.Where(Function(o) o.Status = "Open" OrElse o.Status = $"In Progress - {Environment.MachineName}").OrderBy(Function(d) d.Date).ToList
                OrderList = New ObservableCollection(Of SuppliesOrder)(orders)
                bsOrders.DataSource = OrderList
                TabbedControlGroup1.SelectedTabPageIndex = IIf(orders.Any(), 0, 1)
                RepositoryItemLookUpEdit1.DataSource = ctxDB.Supplies.ToList
                SetDefaultPrinters()
                LoadPendingOrdersCount()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error loading orders", ex)
        End Try
    End Sub

    Private Sub LoadPendingOrdersCount()
        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                Badge1.Properties.Text = ctxDB.SuppliesOrders.Count(Function(o) o.Status = "Pending")
            End Using
        Catch ex As Exception
            Logger.Error(ex, "Error loading pending orders count")
        End Try
    End Sub

    Private Sub SetDefaultPrinters()
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim defaultPrinting = New Printing.PrinterSettings
            Dim installedPrinters = Printing.PrinterSettings.InstalledPrinters.Cast(Of String).ToList
            cbeReportsPrinter.Properties.Items.Clear()
            cbeReportsPrinter.Properties.Items.AddRange(installedPrinters)
            cbeCheckPrinter.Properties.Items.Clear()
            cbeCheckPrinter.Properties.Items.AddRange(installedPrinters)
            cbePsChecksPrinter.Properties.Items.Clear()
            cbePsChecksPrinter.Properties.Items.AddRange(installedPrinters)

            Dim reportSettings = ctxDB.settings.SingleOrDefault(Function(s) s.username = UserName AndAlso s.path = "\ReportsPrinter" AndAlso s.type = "Execupay.Foundation.UserPrinters")
            If reportSettings IsNot Nothing AndAlso reportSettings.PrinterName.IsNotNullOrWhiteSpace() AndAlso installedPrinters.Contains(reportSettings.PrinterName) Then
                cbeReportsPrinter.EditValue = reportSettings.PrinterName
            Else
                cbeReportsPrinter.EditValue = defaultPrinting.PrinterName
            End If

            reportSettings = ctxDB.settings.SingleOrDefault(Function(s) s.username = UserName AndAlso s.path = "\ChecksPrinter" AndAlso s.type = "Execupay.Foundation.UserPrinters")
            If reportSettings IsNot Nothing AndAlso reportSettings.PrinterName.IsNotNullOrWhiteSpace() AndAlso installedPrinters.Contains(reportSettings.PrinterName) Then
                cbeCheckPrinter.EditValue = reportSettings.PrinterName
            Else
                cbeCheckPrinter.EditValue = defaultPrinting.PrinterName
            End If

            reportSettings = ctxDB.settings.SingleOrDefault(Function(s) s.username = UserName AndAlso s.path = "\ChecksPressPrinter" AndAlso s.type = "Execupay.Foundation.UserPrinters")
            If reportSettings IsNot Nothing AndAlso reportSettings.PrinterName.IsNotNullOrWhiteSpace() AndAlso installedPrinters.Contains(reportSettings.PrinterName) Then
                cbePsChecksPrinter.EditValue = reportSettings.PrinterName
            Else
                cbePsChecksPrinter.EditValue = defaultPrinting.PrinterName
            End If
        End Using
    End Sub

    Private Sub bsOrders_CurrentChanged(sender As Object, e As EventArgs) Handles bsOrders.CurrentChanged
        Try
            Dim order As SuppliesOrder = bsOrders.Current
            If order Is Nothing Then
                TabbedControlGroup1.SelectedTabPageIndex = 1
                bbiMarkPending.Enabled = False
                bbiMarkPendingAndMoveToNextPrNum.Enabled = False
            Else
                bbiMarkPending.Enabled = True
                bbiMarkPendingAndMoveToNextPrNum.Enabled = True
                bbiMarkPending.Visibility = (Not order.SendWithNextPayroll).ToBarItemVisibility
                bbiMarkPendingAndMoveToNextPrNum.Visibility = (order.SendWithNextPayroll).ToBarItemVisibility
                TabbedControlGroup1.SelectedTabPageIndex = 0
                lciPrintOrderSlipCompleted.Visibility = (order.Status = $"In Progress - {Environment.MachineName}").ToBarItemVisibility
                lciPrintDocsCecksCompleted.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                lciMarkCompleted.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                teOrderDate.Text = $"{order.Date} - {order.Date.Humanize(False)}"
                lcgOrderDetails.Text = $"Order Details - {order.ID}"

                Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                    ctxDB.ObjectTrackingEnabled = False
                    Dim items = ctxDB.SuppliesOrderItems.Where(Function(o) o.OrderID = order.ID).ToList
                    gcOrderItems.DataSource = items
                    lciPrintDocsChecks.Visibility = (items.Any(Function(i) i.document_Id.HasValue)).ToBarItemVisibility
                    If order.SendWithPrNum.HasValue Then
                        Dim pr = ctxDB.PAYROLLs.SingleOrDefault(Function(p) p.CONUM = order.CoNum AndAlso p.PRNUM = order.SendWithPrNum)
                        teSendWithPrnum.Text = $"{order.SendWithPrNum} - {If(pr IsNot Nothing, pr.PAYROLL_STATUS, "Future Payroll")}"
                    Else
                        teSendWithPrnum.Text = String.Empty
                    End If

                    Dim delivery = ctxDB.view_Deliveries.FirstOrDefault(Function(d) d.CoNum = order.CoNum AndAlso d.PayrollNum = order.SendWithPrNum)
                    If delivery IsNot Nothing AndAlso delivery.Message.IsNotNullOrWhiteSpace Then
                        lciMessage.Visibility = True.ToBarItemVisibility
                        meMessage.Text = delivery.Message
                        'btnMarkPendingAndMoveToNextPrNum.LookAndFeel.UseDefaultLookAndFeel = False
                        'btnMarkPendingAndMoveToNextPrNum.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
                        'btnMarkPendingAndMoveToNextPrNum.Appearance.BackColor = Color.Yellow
                    Else
                        lciMessage.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                        'btnMarkPendingAndMoveToNextPrNum.LookAndFeel.UseDefaultLookAndFeel = True
                    End If
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading order details", ex)
        End Try
    End Sub

    Private Sub btnPrintOrderSlip_Click(sender As Object, e As EventArgs) Handles btnPrintOrderSlip.Click
        Try
            LayoutControl1.ShowProgessPanel
            Application.DoEvents()
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                UpdateOrder(CurrentOrder.ID, $"In Progress - {Environment.MachineName}", UserName, Now)
                Dim report = New ReportProcessor(CurrentOrder.CoNum, "Payroll Cover Sheet", FileType.Pdf) With {.showParametersForm = False, .showInRecentReports = False, .LastPrNum = CurrentOrder.ID}
                Dim result = report.ProcessReport()
                Using pdfDocumentProcessor As New DevExpress.Pdf.PdfDocumentProcessor()
                    pdfDocumentProcessor.LoadDocument(result.Paths.Single, True)
                    Dim pdfPrinterSettings As New DevExpress.Pdf.PdfPrinterSettings()
                    pdfPrinterSettings.Settings.PrinterName = cbeReportsPrinter.EditValue
                    pdfDocumentProcessor.Print(pdfPrinterSettings)
                End Using
                lciPrintOrderSlipCompleted.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error Printing order slip", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Sub btnPrintDocsChecks_Click(sender As Object, e As EventArgs) Handles btnPrintDocsChecks.Click
        Try
            LayoutControl1.ShowProgessPanel
            Dim items As List(Of SuppliesOrderItem) = gcOrderItems.DataSource
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                For Each item In items.Where(Function(i) i.document_Id.HasValue)
                    Dim dt = modGlobals.Query("select blob, keywords from document_storage where document_id = @P0", New SqlClient.SqlParameter("@P0", item.document_Id))
                    Dim byteArray As Byte() = dt.Rows(0)(0)
                    Using pdfDocumentProcessor As New DevExpress.Pdf.PdfDocumentProcessor()
                        pdfDocumentProcessor.LoadDocument(New MemoryStream(byteArray))
                        Dim pdfPrinterSettings As New DevExpress.Pdf.PdfPrinterSettings()
                        Dim keyword = dt.Rows(0).Field(Of String)("keywords")
                        pdfPrinterSettings.Settings.PrinterName = GetPrinterName(keyword)
                        If keyword = "\ChecksPressPrinter" Then
                            Dim fileName = FileNameValidData
                            System.IO.File.WriteAllBytes(fileName, byteArray)
                            For Each s As Printing.PaperSize In pdfPrinterSettings.Settings.PaperSizes
                                If s.Kind = Printing.PaperKind.Legal Then
                                    pdfPrinterSettings.Settings.DefaultPageSettings.PaperSize = s
                                    pdfPrinterSettings.ScaleMode = DevExpress.Pdf.PdfPrintScaleMode.ActualSize
                                    Logger.Information("Setting printer PaperSize to Legal")
                                End If
                            Next
                        End If
                        Logger.Information("Printer PaperSize is: {PaperSize}", pdfPrinterSettings.Settings.DefaultPageSettings.PaperSize)
                        pdfDocumentProcessor.Print(pdfPrinterSettings)
                    End Using
                Next
            End Using
            lciPrintDocsCecksCompleted.Visibility = True.ToBarItemVisibility
        Catch ex As Exception
            DisplayErrorMessage("Error printing Docs/Checks", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Async Sub btnMarkComplete_Click(sender As Object, e As EventArgs) Handles btnMarkComplete.Click
        Try
            Dim order = UpdateOrder(CurrentOrder.ID, "Completed", UserName, Now)
            Dim ClockItem As SuppliesOrderItem
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                ClockItem = (From a In ctxDB.SuppliesOrderItems Join s In ctxDB.Supplies On s.ProductID Equals a.ProductID Where a.OrderID = order.ID AndAlso s.IsClock = True Select a).FirstOrDefault()
            End Using

            'if no notes than create invoice out of it.  No need to stop at billing.
            'Also, when order is set for pickup (SendWithPrNum = 0) then don't prompt for delivery note.
            'If clock item then stop by delivery
            If nz(order.BillingMessage, "") = "" AndAlso (nz(order.Notes, "") = "" OrElse order.SendWithPrNum = 0) AndAlso ClockItem Is Nothing Then
                Try
                    Dim OrdersBilling As New frmOrdersBilling(order.ID)
                    OrdersBilling.InvoiceOrder()
                Catch ex As Exception
                    DisplayErrorMessage("Error saving invoice", ex)
                End Try
            End If

            bsOrders.RemoveCurrent()
            lcOrderCompleted.Text = $"Order {order.ID} Completed!"
            FlyoutPanelOrderCompleted.ShowPopup()
            Await Task.Delay(1000 * 5)
            FlyoutPanelOrderCompleted.HidePopup()
        Catch ex As Exception
            DisplayErrorMessage("Error completeing the order", ex)
        End Try
    End Sub

    Private Function UpdateOrder(orderId As Integer, status As String, completedBy As String, completedOn As DateTime?) As SuppliesOrder
        Logger.Information("Updating Order: {OrderId} Status: {Status}", orderId, status)
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim order = ctxDB.SuppliesOrders.Single(Function(o) o.ID = orderId)
            order.Status = status
            order.CompletedBy = completedBy
            order.CompletedOn = completedOn
            ctxDB.SubmitChanges()
            modSignalRClient.SuppliesOrderUpdated(order)
            Return order
        End Using
    End Function

    Public Function GetPrinterName(keywords As String) As String
        If keywords = "\ChecksPrinter" Then
            Return cbeCheckPrinter.EditValue
        ElseIf keywords = "\ChecksPressPrinter" Then
            Return cbePsChecksPrinter.EditValue
        Else
            Throw New Exception($"There's no mapping for Keyword: {keywords}")
        End If
    End Function

    Private Sub btnShowOrderList_Click(sender As Object, e As EventArgs) Handles btnShowOrderList.Click
        Try
            Dim DB = New dbEPDataDataContext(GetConnectionString)
            'Solomon modified on Jul 20, '20.  Was very slow query

            'GridControl2.DataSource = Query("select top 250 s.*, d.ScannedDate, d.DeliverBy, 
            'NextPrDate = 'Pr#: ' + convert(varchar(50), s.SendWithPrNum) + ' - ' + ISNULL(p.PAYROLL_STATUS, 'Future Payroll') + CASE WHEN c.process_date IS NULL THEN '' ELSE ', Process Date: ' END + ISNULL(c.process_date,'')
            'from custom.SuppliesOrders s
            'left outer join custom.view_Deliveries d on s.ID = d.PayrollNum
            'LEFT OUTER JOIN dbo.PAYROLL p ON s.SendWithPrNum = p.PRNUM AND s.CoNum = p.CONUM
            'OUTER APPLY (SELECT TOP 1 CONVERT(nvarchar(50), fnsp.process_date, 1) process_date FROM dbo.fn_NextScheduledPayroll(s.conum) fnsp WHERE fnsp.completed = 'NO') c
            'order by s.date DESC")
            GridControl2.DataSource = Query((From u In DB.UDFs Where u.name = "Printing-OrdersHistory-SelectSql").First.value)
            GridControl2.ForceInitialize()
            GridView2.BestFitColumns()
            FlyoutPanel1.Width = (Me.Width - (Me.Width / 10))
            FlyoutPanel1.ShowPopup()
        Catch ex As Exception
            DisplayErrorMessage("Error loading order history", ex)
        End Try
    End Sub

    Private Sub btnHideOrderList_Click(sender As Object, e As EventArgs) Handles btnHideOrderList.Click
        FlyoutPanel1.HidePopup()
    End Sub

    Private Sub GridView2_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView2.PopupMenuShowing
        Dim order As DataRowView = GridView2.GetFocusedRow
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Reopen Order", Sub()
                                                                                  If {"YES", "N/A"}.Contains(order.Row.Field(Of String)("IsInvoiced")) Then
                                                                                      DisplayMessageBox("Cannot reopen an order after it's invoiced.")
                                                                                      Exit Sub
                                                                                  End If
                                                                                  UpdateOrder(order.Row.Field(Of Integer)("ID"), "Open", Nothing, Nothing)
                                                                                  order.Row.SetField(Of String)("Status", "Open")
                                                                              End Sub, My.Resources.open_16x16))

        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Reprint Cover Sheet", Sub() ReprintCoverSheet(order), My.Resources.print_16x16))
    End Sub

    Private Async Sub ReprintCoverSheet(order As DataRowView)
        FlyoutPanel1.HidePopup()
        Await Task.Delay(750)
        Dim conum As Decimal = order.Row.Field(Of Decimal)("CoNum")
        Dim ordernum As Integer = order.Row.Field(Of Integer)("ID")
        Dim frm = New frmReprintCoverSheet(conum, ordernum)
        frm.ShowDialog()
    End Sub

    Private Async Sub riOrderNumber_Click(sender As Object, e As EventArgs) Handles riOrderNumber.Click
        Dim orderNumber As Integer = GridView2.GetRowCellValue(GridView2.FocusedRowHandle, colID)
        FlyoutPanel1.HidePopup()
        Await Task.Delay(750)
        Dim frm = New frmOrderHistoryDetails(orderNumber)
        frm.ShowDialog()
    End Sub

    Private Sub bbiMarkPendingAndMoveToNextPrNum_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiMarkPendingAndMoveToNextPrNum.ItemClick
        Try
            If CurrentOrder Is Nothing Then Exit Sub
            If Not CurrentOrder.SendWithNextPayroll Then
                DisplayMessageBox("Order is not set to ship with payroll")
                Exit Sub
            End If

            Using DB = New dbEPDataDataContext(GetConnectionString)
                Dim nextPrNum = CurrentOrder.SendWithPrNum + 1
                Dim order = DB.SuppliesOrders.Single(Function(o) o.ID = CurrentOrder.ID)
                order.SendWithPrNum = nextPrNum
                DB.SubmitChanges()
                Dim payroll = DB.PAYROLLs.FirstOrDefault(Function(p) p.CONUM = CurrentOrder.CoNum AndAlso p.PRNUM = nextPrNum)
                If payroll Is Nothing OrElse payroll.PAYROLL_STATUS = "Entering Checks" Then
                    UpdateOrder(CurrentOrder.ID, "Pending", Nothing, Nothing)
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error in btnMarkPendingAndMoveToNextPrNum", ex)
        End Try
    End Sub

    Private Sub bbiMarkPending_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiMarkPending.ItemClick
        Try
            UpdateOrder(CurrentOrder.ID, "Pending", Nothing, Nothing)
        Catch ex As Exception
            DisplayMessageBox("Error marking order as pending")
        End Try
    End Sub

    Private Async Sub bbiCancelOrder_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCancelOrder.ItemClick
        Try
            Dim order = UpdateOrder(CurrentOrder.ID, "Cancelled", UserName, Now)
            bsOrders.RemoveCurrent()
            lcOrderCompleted.Text = $"Order {order.ID} Cancelled"
            FlyoutPanelOrderCompleted.ShowPopup()
            Await Task.Delay(1000 * 5)
            FlyoutPanelOrderCompleted.HidePopup()
        Catch ex As Exception
            DisplayErrorMessage("Error completeing the order", ex)
        End Try
    End Sub

    Private Sub GridView2_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridView2.CustomRowCellEditForEditing
        If e.RowHandle = -2147483646 Then ' DevExpress.Xpf.Grid.GridControl.AutoFilterRowHandle Then
            If e.Column.FieldName = "CoNum" Then
                e.RepositoryItem = riTeCoNmAutoFilter
            End If
        End If
    End Sub
End Class


Partial Public Class setting
    Public ReadOnly Property PrinterName As String
        Get
            Dim xdoc As XDocument = XDocument.Parse(value)
            Dim lastElement As String = DirectCast((xdoc.Root.FirstNode()), System.[Xml].Linq.XText).Value
            Return lastElement
        End Get
    End Property
End Class

Partial Class SuppliesOrder

    Public Overrides Function GetHashCode() As Integer
        Return ID.GetHashCode
    End Function

    Public Overrides Function Equals(obj As Object) As Boolean
        Dim order As SuppliesOrder = obj
        If order Is Nothing Then Return False
        Return order.ID = ID
    End Function
End Class
