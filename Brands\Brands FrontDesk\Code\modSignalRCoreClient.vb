﻿Imports System.Net.Http
Imports Brands.DAL.DomainModels.App
Imports Brands.DAL.DomainModels.App.Zendesk
Imports Microsoft.AspNetCore.Http.Connections.Client
Imports Microsoft.AspNetCore.SignalR.Client
Imports Serilog

Module modSignalRCoreClient
    Public Event OnPrInProcessUpdate As EventHandler
    Public Event OnAutoFixPayrollLog As EventHandler

    Private Property Logger As ILogger

    Sub New()
        Logger = modGlobals.Logger.ForContext(GetType(modSignalRCoreClient))
    End Sub

    Property connection As HubConnection
    Property connectionImpersonate As HubConnection
    Property _CurrentConnection As FrontDeskSignalRConnection

    Public Async Function InitializeAsync(Optional Impersonate As FrontDeskPermission = Nothing) As Threading.Tasks.Task
        Dim qs = String.Format("UserName={0}&MachineName={1}&Version={2}", If(Impersonate IsNot Nothing, Impersonate.UserName, UserName), Environment.MachineName, Version)
        Dim url = "https://zendeskapi.brandspayroll.com/FrontDeskHub"
        If System.Diagnostics.Debugger.IsAttached Then
            'url = "https://localhost:44337/FrontDeskHub"
        End If
        Dim con As HubConnection = If(Impersonate IsNot Nothing, connection, connectionImpersonate)
        Logger.Debug("Attempting to connect to: {Url} QueryString: {QueryString}", url, qs)
        con = New HubConnectionBuilder().WithUrl($"{url}?{qs}", Sub(options) ConfigureHttpConnection(options)).Build()
        AddHandler con.Closed, AddressOf ConnectionClosedAsync

        con.On("OpenCompany", Sub(conum As Decimal)
                                  MainForm?.TryInvoke(Sub() MainForm?.OpenCompForm(conum))
                                  MainForm?.TryBringToFront()
                              End Sub)
        con.On("StartNewPayroll", Sub(conum As Decimal, ticketId As Decimal)
                                      MainForm?.TryInvoke(Sub()
                                                              _Logger.Debug("Entering StartNewPayroll. {Conum} {TicketId}", conum, ticketId)
                                                              MainForm?.TryBringToFront()
                                                              Dim options As OpenPowerGridPayrollOptions = New OpenPowerGridPayrollOptions(conum) With {
                                                                                 ._ZendeskTicketId = ticketId,
                                                                                 .CenterToScreen = Screen.FromControl(MainForm)}
                                                              MainForm.OpenPowerGridPayroll(options)
                                                          End Sub)
                                  End Sub)

        con.On("SetTicketConum", Sub(ticketInfo As SetTicketConumInfo)
                                     MainForm?.TryInvoke(Sub()
                                                             _Logger.Debug("Entering SetTicketConum. {@ticketInfo}", ticketInfo)
                                                             MainForm?.TryBringToFront()
                                                             Dim frm = New frmZendeskTicket
                                                             frm.SetTicketInfo(ticketInfo)
                                                             MainForm.ShowForm(frm)
                                                         End Sub)
                                 End Sub)

        con.On("OpenForm", Sub(conum As Decimal, args As String)
                               MainForm?.TryBringToFront()
                               If args = "Open Company" Then
                                   MainForm?.TryInvoke(Sub() MainForm?.OpenCompForm(conum))
                               ElseIf args = "Order Supplies" Then
                                   MainForm?.TryInvoke(Async Sub()
                                                           Dim frm = Await MainForm?.OpenCompFormAsync(conum)
                                                           frm.bbiOrderSupplies.PerformClick()
                                                       End Sub)
                               ElseIf args = "Manuals And Voids" Then
                                   MainForm?.TryInvoke(Async Sub()
                                                           Dim frm = Await MainForm?.OpenCompFormAsync(conum)
                                                           frm.bbiManualChecks.PerformClick()
                                                       End Sub)
                               ElseIf args = "Reopen Payroll" Then
                                   MainForm?.TryInvoke(Async Sub()
                                                           Dim frm = Await MainForm?.OpenCompFormAsync(conum)
                                                           frm.bbiPayrollStatus.PerformClick()
                                                       End Sub)
                               End If
                           End Sub)

        con.On("OpenTicketInFD", Sub(ticketInfo As SetTicketConumInfo) OpenTicketInFd(ticketInfo))

        con.On("ConnectionUpdated", Sub(connection As FrontDeskSignalRConnection)
                                        _CurrentConnection = connection
                                        SetStatus(True, connection.IsDefault, connection.AppNumber)
                                    End Sub)

        con.On("FrontDeskNotification", Sub(notification As Brands.DAL.DomainModels.App.FrontDeskNotification)
                                            MainForm.TryInvoke(Sub() FrontDeskNotification(notification))
                                        End Sub)

        con.On("PrInProcessUpdate", Sub(payrollInProcessUpdate As Brands.DAL.DomainModels.App.PayrollInProcessUpdate)
                                        RaiseEvent OnPrInProcessUpdate(payrollInProcessUpdate, Nothing)
                                    End Sub)

        con.On("sendReports", Sub(conum As Decimal, args2 As String)
                                  MainForm?.TryInvoke(Async Sub()
                                                          MainForm?.TryBringToFront()
                                                          Dim frm = Await MainForm?.OpenCompFormAsync(conum, frmCompanySumarry.CompanyTabPage.ReportBrowser)
                                                          'frm.bbiReportBrowser.PerformClick()
                                                          Await frm.frmTask.Task
                                                          Dim UcReportEmailTemplate1 As ucReportEmailTemplate = frm.DataPanel.Controls(0)
                                                          UcReportEmailTemplate1.SetUpdateZendeskTicketId(args2)
                                                      End Sub)
                              End Sub)
        con.On("sendManualsAndVoids", Sub(conum As Decimal, ticketId As String)
                                          MainForm?.TryInvoke(Async Sub()
                                                                  MainForm?.TryBringToFront()
                                                                  Dim frm = Await MainForm?.OpenCompFormAsync(conum)
                                                                  'frm.bbiReportBrowser.PerformClick()
                                                                  Await frm.frmTask.Task
                                                                  Dim result As Long
                                                                  If Long.TryParse(ticketId, result) Then
                                                                      frm.bbiManualChecks.Tag = result
                                                                  End If
                                                                  frm.bbiManualChecks.PerformClick()
                                                              End Sub)
                                      End Sub)

        con.On("AutoFixPayrollLog", Sub(log As AutoFixPayrollLogMessage)
                                        RaiseEvent OnAutoFixPayrollLog(log, Nothing)
                                    End Sub)

        If Impersonate IsNot Nothing Then
            connectionImpersonate = con
        Else
            connection = con
        End If

        Await StartConnectionAsync()
    End Function

    Private Sub ConfigureHttpConnection(options As HttpConnectionOptions)
        options.UseDefaultCredentials = True
        options.HttpMessageHandlerFactory = Function(msg)
                                                Dim clientHandler As HttpClientHandler = TryCast(msg, HttpClientHandler)
                                                If clientHandler IsNot Nothing AndAlso My.Settings.BypassSSLCertificate Then
                                                    'bypass SSL certificate
                                                    clientHandler.ServerCertificateCustomValidationCallback = Function(sender, certificate, chain, sslPolicyErrors) True
                                                End If
                                                Return msg
                                            End Function
    End Sub

    Private Sub FrontDeskNotification(notification As Brands.DAL.DomainModels.App.FrontDeskNotification)
        Try
            Logger.Debug("Entering FrontDeskNotification. {@FrontDeskNotification}", notification)
            Dim action = Sub()
                             If notification.CommandName = "PayrollStatus" Then
                                 MainForm.AddOrActivateForm(Of frmPayrollsInProcessList)()
                             End If
                         End Sub
            MainForm.ShowNotification(notification.Caption, notification.Text, Nothing, action)
        Catch ex As Exception
            Logger.Error(ex, "Error in FrontDeskNotification")
        End Try
    End Sub


    Private Sub OpenTicketInFd(ticketInfo As SetTicketConumInfo)
        MainForm?.TryInvoke(Async Sub()
                                Try
                                    _Logger.Debug("Entering OpenTicketInFD. {@ticketInfo}", ticketInfo)
                                    MainForm?.TryBringToFront()
                                    Dim applyMacroResult As Task = Nothing

                                    If ticketInfo.Mode = "newPayroll" Then
                                        Dim payrollMacroId = GetUdfValue("Zendesk_NewPayroll_MacroId")
                                        If payrollMacroId.IsNotNullOrWhiteSpace Then
                                            applyMacroResult = GetUpdateTicketService.ApplyMacro(ticketInfo.TicketId, payrollMacroId)
                                        End If
                                    End If

                                    If ticketInfo.Mode = "setCo" OrElse ticketInfo.TotalAttachments > 0 Then
                                        Dim frm = New frmZendeskTicket
                                        frm.SetTicketInfo(ticketInfo)
                                        If ticketInfo.Mode = "setCo" Then
                                            MainForm.ShowForm(frm)
                                        Else
                                            frm.Show()
                                            Dim screens = Screen.AllScreens
                                            If screens.Count() > 1 Then
                                                Dim primaryScreen = Screen.FromControl(MainForm)
                                                Dim seconderyScreen As Screen = screens.First(Function(s) s.DeviceName <> primaryScreen.DeviceName)
                                                frm.CenterToScreeen(seconderyScreen)
                                                frm.WindowState = FormWindowState.Maximized
                                            End If
                                        End If
                                    End If

                                    If ticketInfo.Mode = "newPayroll" Then
                                        Dim options As OpenPowerGridPayrollOptions = New OpenPowerGridPayrollOptions(ticketInfo.Conum) With {
                                            ._ZendeskTicketId = ticketInfo.TicketId,
                                            .CenterToScreen = Screen.FromControl(MainForm)}
                                        MainForm.OpenPowerGridPayroll(options)
                                    End If

                                    If applyMacroResult IsNot Nothing Then
                                        If Not applyMacroResult.IsCompleted Then Await applyMacroResult
                                        If applyMacroResult.Exception IsNot Nothing Then Throw New Exception("Error Applying Macro", applyMacroResult.Exception)
                                    End If
                                Catch ex As Exception
                                    DisplayErrorMessage("Error in OpenTicketInFd", ex)
                                End Try
                            End Sub)
    End Sub

    Private Async Function ConnectionClosedAsync(arg As Exception) As Task
        Logger.Error(arg, "Error in ConnectionClosedAsync")
        SetStatus(False, False)
        Await StartConnectionAsync()
    End Function

    Private Async Function StartConnectionAsync() As Task
        While True
            Try
                If connection IsNot Nothing AndAlso connection.State = HubConnectionState.Disconnected Then
                    Await connection.StartAsync()
                End If

                If connectionImpersonate IsNot Nothing AndAlso connectionImpersonate.State = HubConnectionState.Disconnected Then
                    Await connectionImpersonate.StartAsync()
                End If

                SetStatus(True, Nothing, _CurrentConnection?.AppNumber)
                Exit While
            Catch ex As Exception
                Logger.Error(ex, "Error in StartConnectionAsync")
            End Try
            Await Task.Delay(TimeSpan.FromSeconds(30))
        End While
    End Function

    Private Sub SetStatus(isConnected As Boolean, Optional isDefault As Boolean? = Nothing, Optional appNumber As Integer? = Nothing)
        MainForm?.TryInvoke(Sub()
                                Dim appNumberDisplay = If(appNumber.HasValue, $" (App - {appNumber})", "")
                                MainForm.bsiSignalRStatus.Caption = If(isConnected, $"Connected{appNumberDisplay}", "Disconnected")
                                MainForm.bsiSignalRStatus.Enabled = isConnected
                                If isDefault.HasValue Then
                                    MainForm.bsiSignalRStatus.Tag = isDefault
                                    MainForm.bsiSignalRStatus.Checked = isDefault
                                End If
                            End Sub)
    End Sub

    Public Async Function ChangeDefault() As Task
        Try
            If connectionImpersonate IsNot Nothing Then
                Await connectionImpersonate.InvokeAsync("UpdateIsDefaultAsync", MainForm.bsiSignalRStatus.Checked)
            Else
                Await connection.InvokeAsync("UpdateIsDefaultAsync", MainForm.bsiSignalRStatus.Checked)
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in ChangeDefault")
        End Try
    End Function
End Module
