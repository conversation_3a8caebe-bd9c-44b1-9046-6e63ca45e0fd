﻿Public Class frmCompanyActivityLog
    Private ReadOnly _conum As Decimal

    Sub New(conum As Decimal)
        InitializeComponent()
        _conum = conum
    End Sub

    Private Sub LoadData()
        Try
            Dim db = New dbEPDataDataContext(GetConnectionString)
            BindingSource1.DataSource = (From l In db.CompanyActivityLogs Where l.CoNum = _conum _
                                                                              AndAlso l.OpenDate >= deFromDate.DateTime _
                                                                              AndAlso l.OpenDate <= deToDate.DateTime).ToList()
        Catch ex As Exception
            DisplayErrorMessage($"Error Loading company activity for Co#: {_conum}", ex)
        End Try
    End Sub

    Private Sub frmCompanyActivityLog_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        deFromDate.DateTime = Today.AddDays(-14)
        deToDate.DateTime = Today.AddDays(1)
        LoadData()
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        LoadData()
    End Sub
End Class