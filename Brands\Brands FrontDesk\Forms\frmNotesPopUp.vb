﻿
Imports System.ComponentModel.DataAnnotations.Schema

Public Class frmNotesPopUp
    Dim DB As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
    Dim _conum As Decimal
    Dim _empnum As Decimal?
    Dim _notes As List(Of note)


    Public Sub LoadNotes(ByVal CoNum As Decimal, ByVal SearchTerm As String, deleteOnly As Boolean, Optional ByVal EmpNum As Decimal? = Nothing)
        Me.DB = DB
        Me._conum = CoNum
        _empnum = EmpNum
        _notes = DB.NOTEs.Where(Function(n) n.conum.Equals(_conum) AndAlso (n.expiration_date Is Nothing OrElse n.expiration_date.Value > Date.Today)).OrderByDescending(Function(n) n.modify_date).ToList
        _notes = _notes.Where(Function(n) (n.title.ToLower.Contains(SearchTerm.ToLower) OrElse (Not IsNothing(n.note) AndAlso n.note.ToLower.Contains(SearchTerm.ToLower)))).ToList
        If Not IsNothing(_empnum) Then
            _notes = _notes.Where(Function(n) n.empnum.HasValue AndAlso n.empnum.Value = _empnum.Value).ToList
        End If
        NotesBindingSource.DataSource = _notes
        gvNotes.BestFitColumns()
        If deleteOnly Then
            ceAddNewNote.Checked = False
            teTitle.Text = ""
            recNote.Text = ""
        End If

    End Sub

    Private Sub ceAnndNewNote_CheckedChanged(sender As Object, e As EventArgs)

    End Sub

    Private Sub ceAddNewNote_CheckedChanged(sender As Object, e As EventArgs) Handles ceAddNewNote.CheckedChanged
        gcNewNote.Enabled = ceAddNewNote.Checked
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ceAddNewNote.Checked Then
            Dim _note = New note With {.note_id = Guid.NewGuid, .conum = _conum, .create_date = DateTime.Now, .created_by = UserName, .modify_date = DateTime.Now, .empnum = _empnum,
                                  .category = "Employee", .priority = "3-Low", .show2client = "NO", .show2emp = "NO", .title = teTitle.Text, .note = recNote.RtfText}
            DB.NOTEs.InsertOnSubmit(_note)
        End If
        For Each item As note In gvNotes.DataSource
            If item.Delete Then
                DB.NOTEs.DeleteOnSubmit(item)
            End If
        Next
        If DB.SaveChanges() Then
            Close()
        End If
    End Sub
End Class

Partial Public Class NOTE
    <NotMapped>
    Public Property Delete As Boolean = True
End Class