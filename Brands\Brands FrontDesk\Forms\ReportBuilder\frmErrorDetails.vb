﻿Public Class frmErrorDetails
    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Close()
    End Sub

    Private Sub btnOpenSqlInSsms_Click(sender As Object, e As EventArgs) Handles btnOpenSqlInSsms.Click
        Try
            Dim newFileName = System.IO.Path.GetTempFileName()
            newFileName = System.IO.Path.ChangeExtension(newFileName, "sql")
            System.IO.File.WriteAllText(newFileName, meSql.Text)
            'System.Diagnostics.Process.Start(newFileName)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = newFileName
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error opening file", ex)
        End Try
    End Sub
End Class