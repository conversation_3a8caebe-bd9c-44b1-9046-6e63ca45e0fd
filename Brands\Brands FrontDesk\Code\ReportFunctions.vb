﻿Class ReportFunctions

    Public Shared Function GetReportAsAttachment(ByVal viewer As Microsoft.Reporting.WinForms.ReportViewer, _
                                ByVal FileName As String) As Net.Mail.Attachment

        Dim mimeType As String = Nothing
        Dim MS = GetReportPDFStream(viewer)

        Dim Attach = New Net.Mail.Attachment(MS, FileName, mimeType)
        Return Attach
    End Function

    Public Shared Function GetReportPDFStream(ByVal viewer As Microsoft.Reporting.WinForms.ReportViewer, Optional ByRef mimeType As String = Nothing) As IO.MemoryStream
        Try
            Dim warnings As Microsoft.Reporting.WinForms.Warning() = Nothing
            Dim streamids As String() = Nothing

            Dim encoding As String = Nothing
            Dim extension As String = Nothing

            Dim Output As Byte()
            Output = viewer.LocalReport.Render("PDF", Nothing, mimeType, encoding, extension, streamids, warnings)
            Dim MS As New IO.MemoryStream(Output)
            MS.Position = 0
            Return MS
        Catch ex As Exception
            Throw
        End Try
    End Function

    Public Shared Function GetSenderAddress(ByRef DB As dbEPDataDataContext) As Net.Mail.MailAddress
        Dim From = (From A In DB.DBUSERs Where A.name.ToLower = UserName.ToLower).SingleOrDefault
        Return New Net.Mail.MailAddress(From.email, From.name)
    End Function

End Class
