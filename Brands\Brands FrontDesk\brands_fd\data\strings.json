strings = 
{
  "Active": "Active",
  "AggregateIssues": "Aggregate Issues",
  "Categories": "Categories",
  "Complex": "Complex",
  "ComplexDescription": "The changes required for the migration task are complex, but have a documented solution.",
  "Component": "Component",
  "Components": "Components",
  "Dashboard": "Dashboard",
  "Description": "Description",
  "defaultReportTitle": "Upgrade Analysis Report",
  "feedback": "Feedback",
  "File": "File",
  "generatedBy": "Generated by ",
  "Help": "Help",
  "Help Contents": "Help Contents",
  "HowStoryPointsCalculated": "How are Story Points calculated?",
  "Incidents": "Incidents",
  "incidents": "incidents",
  "IncidentDescription1": "Incident is an example of detected rule at a specific location (code file, binary etc). There could be many incidents of a given issue.",
  "IncidentDescription2": "Each incident contains an issue ID, location and (most of the times) snippet that caused the incident to be detected.",
  "Information": "Information",
  "InformationDescription": "The issue was raised only for informational purpose and is not required to be resolved.",
  "Issue": "Issue",
  "Issues": "Issues",
  "issues": "issues",
  "IssuesCategoriesChart": "Issues Categories Chart: ",
  "IssueDescription1": "Issues are detected unique encounters of rules that might have to be addressed to re-platform an application to Azure. Each issue (rule) has its own unique ID, severity and story points.",
  "IssueDescription2": "There could be multiple incidents for each issue in one or more locations like code files or binaries.",
  "IssueSeverity": "Issue Severity",
  "IssuesSeverityChart": "Issues Severity Chart: ",
  "IssueSeverityDescription1": "In addition to the Story points, migration tasks is assigned a severity that indicates whether the task must be completed or can be postponed.",
  "Kind": "Kind",
  "LeftNavigationPanel": "Left navigation panel",
  "LevelOfEffort": "LevelOfEffort",
  "Mandatory": "Mandatory",
  "MandatoryDescription": "The issue has to be resolved for the migration to be successful.",
  "N/A": "N/A",
  "Optional": "Optional",
  "OptionalDescription": "The issue discovered is real issue fixing which could improve the app after migration, however it is not blocking, it could be resolved or not.",
  "Potential": "Potential",
  "PotentialDescription": "We were not sure if it is necessarily a blocking problem, but just in case raised your attention.",
  "Protected": "Protected",
  "PrivacyMode": "Privacy mode: ",
  "Project": "Project",
  "Projects": "Projects",
  "projects": "projects",
  "Rearchitecture": "Re-architecture",
  "RearchitectureDescription": "The migration requires a complete re-architecture of the component or subsystem.",
  "Redesign": "Redesign",
  "RedesignDescription": "The migration task requires a redesign or a complete library change, with significant API changes.",
  "Resolved": "Resolved",
  "Restricted": "Restricted",
  "Severity": "Severity",
  "SeverityLevel": "Severity level",
  "SeverityLevels": "Severity levels",
  "ShowLessInformation": "Show Less Information",
  "ShowMoreInformation": "Show More Information",
  "Source": "Source",
  "State": "State",
  "Story Points": "Story Points",
  "StoryPoints": "Story Points",
  "storyPoints": "story points",
  "StoryPointsDescription1": "Story Points are an abstract metric commonly used in Scrum Agile software development methodology to estimate the level of effort needed to implement a feature or change. They are based on a modified Fibonacci sequence.",
  "StoryPointsDescription2": "In a similar manner, Windup uses story points to express the level of effort needed to migrate particular application constructs, and in a sum, the application as a whole. It does not necessarily translate to man-hours, but the value should be consistent across tasks.",
  "StoryPointsDescription3": "Story points are a unit of measure for expressing an issue's overall size. They help teams estimate how much work is required to complete a task. The higher the story point value, the more effort the team believes the issue will take to complete.",
  "summary": "Summary",
  "Trivial": "Trivial",
  "TrivialDescription": "The migration is a trivial change or a simple library swap with no or minimal API changes.",
  "TopAdditionalInfoPanel": "Top title panel",
  "TopTitlePanel": "Top additional information panel",
  "Unknown": "Unknown",
  "UnknownDescription": "The migration solution is not known and may need a complete rewrite.",
  "Unrestricted": "Unrestricted",
  "WhatAreStoryPoints": "What are Story Points?",
  "WhatAreIncidents": "What are incidents?",
  "WhatAreIssues": "What are issues?",
  "WhatIsAnIncident": "What is an incident?",
  "WhatIsAnIssue": "What is an issue?",
  "WhatIsAnIssueSeverity": "What is an issue severity?",
  "WhatIsPrivacyMode": "What is privacy mode?"
}

