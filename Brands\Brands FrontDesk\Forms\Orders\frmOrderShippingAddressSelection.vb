﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors

Public Class frmOrderShippingAddressSelection

    Private Property conum As Decimal
    Private ReadOnly Property SendWithNextPayroll As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property ShippingAddressOverride As ShipAddressOverride

    Public Sub New(_conum As Decimal, _sendWithNextPayroll As Boolean)
        InitializeComponent()
        conum = _conum
        SendWithNextPayroll = _sendWithNextPayroll
        TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        TabbedControlGroup1.SelectedTabPageIndex = 0
        ShippingAddressOverride = New ShipAddressOverride With {.CONUM = _conum, .AddUser = UserName, .AddDateTime = Now}
        shipAddressOverridesBindingSource.DataSource = ShippingAddressOverride
    End Sub

    Private Sub frmOrderShippingAddressSelection_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            GridControl1.DataSource = ctxDB.prc_GetShippingAddress(conum)
            Dim States = ctxDB.ZIPS.Select(Function(z) z.STATE).Distinct().ToArray
            cbeStatesOverrideShipAddress.Properties.Items.AddRange(States)
            Dim deliveryOptions As String() = ctxDB.dbo_DELIVERies.Select(Function(d) d.DELDESC).ToArray
            cbeDeliveryMethodOverride.Properties.Items.AddRange(deliveryOptions)
        End Using
    End Sub

    Private Sub teZipOverride_Validated(sender As Object, e As EventArgs) Handles teZipOverride.Validated
        If Not Me.teZipOverride.HasValue Then Return
        If Me.teZipOverride.EditValue = Me.teZipOverride.OldEditValue & "" Then Return
        Dim DB = New dbEPDataDataContext(GetConnectionString)
        Dim Zip = (From A In DB.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.teZipOverride.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            ShippingAddressOverride.SHIP_STATE = Zip.STATE
            ShippingAddressOverride.SHIP_CITY = Zip.CITY
        End If
    End Sub

    Private Sub teDivisionZip_Enter(sender As Object, e As EventArgs) Handles teZipOverride.Enter
        DirectCast(sender, TextEdit).SelectAll()
    End Sub

    Private Sub teDivisionZip_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teZipOverride.PreviewKeyDown
        If e.KeyCode = Keys.Enter Then DirectCast(sender, TextEdit).DoValidate()
    End Sub

    Private Sub rgShipTypeSelection_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgShipTypeSelection.SelectedIndexChanged
        TabbedControlGroup1.SelectedTabPageIndex = rgShipTypeSelection.SelectedIndex
        If rgShipTypeSelection.SelectedIndex = 1 Then
            If SendWithNextPayroll Then
                DisplayMessageBox("When shipping with a payroll, you cannot set a specific address, it will use the shipping address from the payroll.")
                btnSubmit.Enabled = False
            End If
        Else
            btnSubmit.Enabled = True
        End If
    End Sub

    Private Sub btnSubmit_Click(sender As Object, e As EventArgs) Handles btnSubmit.Click
        If rgShipTypeSelection.SelectedIndex = 0 Then
            If GridView1.GetFocusedRow Is Nothing Then
                DisplayMessageBox("Please select an address from the list")
                Exit Sub
            End If
        Else
            If Not DxValidationProvider1.Validate() Then Exit Sub
        End If
        DialogResult = DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Public ReadOnly Property IsDivionNumberSelected As Boolean
        Get
            Return rgShipTypeSelection.SelectedIndex = 0
        End Get
    End Property

    Public ReadOnly Property DivionNumber As Decimal
        Get
            Dim row As prc_GetShippingAddressResult = GridView1.GetFocusedRow
            Return row.DDIVNUM
        End Get
    End Property

End Class

Partial Class prc_GetShippingAddressResult
    Public Property Address As String
        Get
            Return $"{ShipCo}{vbCrLf}{ShipAddress}{vbCrLf}{ShipCity}, {ShipState}, {ShipZip} {vbTab} {ShipPhone}"
        End Get
        Set
        End Set
    End Property
End Class