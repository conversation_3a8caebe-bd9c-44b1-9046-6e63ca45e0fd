﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmManualBillingDraft
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.deFromDate = New DevExpress.XtraEditors.DateEdit()
        Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.bbiRefresh = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiCancelDraft = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiAddDraft = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.dD_Manual_Trans_LogsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTransType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDDIVNAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCOUNT_SOURCE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colROUTING_NUMBER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCOUNT_NUMBER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAMOUNT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPriority_Lvl = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCOUNT_ORDER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACH_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTrans_Note = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRequested_Effective_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBilling_Dept_Notes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTransaction_Status = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDate_Requested = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRequested_By = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTransaction_Effective_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colProcess_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_Routing = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_Account = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_ACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_CONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCredit_EMPNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTransaction_NACHA_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCanNotBeCanceled = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChangeLog = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.RibbonPage2 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.deFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deFromDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dD_Manual_Trans_LogsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.deFromDate)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 148)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1242, 597)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'deFromDate
        '
        Me.deFromDate.EditValue = Nothing
        Me.deFromDate.Location = New System.Drawing.Point(72, 12)
        Me.deFromDate.MenuManager = Me.RibbonControl1
        Me.deFromDate.Name = "deFromDate"
        Me.deFromDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deFromDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deFromDate.Size = New System.Drawing.Size(235, 20)
        Me.deFromDate.StyleController = Me.LayoutControl1
        Me.deFromDate.TabIndex = 5
        '
        'RibbonControl1
        '
        Me.RibbonControl1.ExpandCollapseItem.Id = 0
        Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.RibbonControl1.SearchEditItem, Me.bbiRefresh, Me.bbiCancelDraft, Me.bbiAddDraft})
        Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl1.MaxItemId = 1
        Me.RibbonControl1.Name = "RibbonControl1"
        Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
        Me.RibbonControl1.Size = New System.Drawing.Size(1242, 148)
        '
        'bbiRefresh
        '
        Me.bbiRefresh.Caption = "Refresh"
        Me.bbiRefresh.Id = 1
        Me.bbiRefresh.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.refresh
        Me.bbiRefresh.Name = "bbiRefresh"
        '
        'bbiCancelDraft
        '
        Me.bbiCancelDraft.Caption = "Cancel Draft"
        Me.bbiCancelDraft.Id = 2
        Me.bbiCancelDraft.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.close
        Me.bbiCancelDraft.ItemAppearance.Hovered.Font = New System.Drawing.Font("Segoe UI Semibold", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bbiCancelDraft.ItemAppearance.Hovered.ForeColor = System.Drawing.Color.Red
        Me.bbiCancelDraft.ItemAppearance.Hovered.Options.UseFont = True
        Me.bbiCancelDraft.ItemAppearance.Hovered.Options.UseForeColor = True
        Me.bbiCancelDraft.ItemAppearance.Normal.Font = New System.Drawing.Font("Segoe UI Semibold", 8.25!, System.Drawing.FontStyle.Bold)
        Me.bbiCancelDraft.ItemAppearance.Normal.Options.UseFont = True
        Me.bbiCancelDraft.Name = "bbiCancelDraft"
        '
        'bbiAddDraft
        '
        Me.bbiAddDraft.Caption = "Add Draft"
        Me.bbiAddDraft.Id = 3
        Me.bbiAddDraft.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.add_32x32
        Me.bbiAddDraft.Name = "bbiAddDraft"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.[False]
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRefresh)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiAddDraft)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiCancelDraft)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.Text = "Options"
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.dD_Manual_Trans_LogsBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(12, 36)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.MenuManager = Me.RibbonControl1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(1218, 549)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'dD_Manual_Trans_LogsBindingSource
        '
        Me.dD_Manual_Trans_LogsBindingSource.DataSource = GetType(Brands_FrontDesk.DD_Manual_Trans_Log)
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colID, Me.colTransType, Me.colCONUM, Me.colDIVNUM, Me.colDDIVNAME, Me.colACCOUNT_SOURCE, Me.colROUTING_NUMBER, Me.colACCOUNT_NUMBER, Me.colACCT_TYPE, Me.colAMOUNT, Me.colPriority_Lvl, Me.colACCOUNT_ORDER, Me.colACH_NAME, Me.colTrans_Note, Me.colRequested_Effective_Date, Me.colBilling_Dept_Notes, Me.colTransaction_Status, Me.colDate_Requested, Me.colRequested_By, Me.colTransaction_Effective_Date, Me.colProcess_Date, Me.colCredit_Routing, Me.colCredit_Account, Me.colCredit_ACCT_TYPE, Me.colCredit_CONUM, Me.colCredit_EMPNUM, Me.colTransaction_NACHA_ID, Me.colCanNotBeCanceled, Me.colChangeLog})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowFooter = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colID
        '
        Me.colID.FieldName = "ID"
        Me.colID.Name = "colID"
        Me.colID.Visible = True
        Me.colID.VisibleIndex = 0
        '
        'colTransType
        '
        Me.colTransType.FieldName = "TransType"
        Me.colTransType.Name = "colTransType"
        Me.colTransType.Visible = True
        Me.colTransType.VisibleIndex = 1
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        Me.colCONUM.Visible = True
        Me.colCONUM.VisibleIndex = 2
        '
        'colDIVNUM
        '
        Me.colDIVNUM.FieldName = "DIVNUM"
        Me.colDIVNUM.Name = "colDIVNUM"
        Me.colDIVNUM.Visible = True
        Me.colDIVNUM.VisibleIndex = 3
        '
        'colDDIVNAME
        '
        Me.colDDIVNAME.FieldName = "DDIVNAME"
        Me.colDDIVNAME.Name = "colDDIVNAME"
        Me.colDDIVNAME.Visible = True
        Me.colDDIVNAME.VisibleIndex = 4
        '
        'colACCOUNT_SOURCE
        '
        Me.colACCOUNT_SOURCE.FieldName = "ACCOUNT_SOURCE"
        Me.colACCOUNT_SOURCE.Name = "colACCOUNT_SOURCE"
        Me.colACCOUNT_SOURCE.Visible = True
        Me.colACCOUNT_SOURCE.VisibleIndex = 5
        '
        'colROUTING_NUMBER
        '
        Me.colROUTING_NUMBER.FieldName = "ROUTING_NUMBER"
        Me.colROUTING_NUMBER.Name = "colROUTING_NUMBER"
        Me.colROUTING_NUMBER.Visible = True
        Me.colROUTING_NUMBER.VisibleIndex = 6
        '
        'colACCOUNT_NUMBER
        '
        Me.colACCOUNT_NUMBER.FieldName = "ACCOUNT_NUMBER"
        Me.colACCOUNT_NUMBER.Name = "colACCOUNT_NUMBER"
        Me.colACCOUNT_NUMBER.Visible = True
        Me.colACCOUNT_NUMBER.VisibleIndex = 7
        '
        'colACCT_TYPE
        '
        Me.colACCT_TYPE.FieldName = "ACCT_TYPE"
        Me.colACCT_TYPE.Name = "colACCT_TYPE"
        Me.colACCT_TYPE.Visible = True
        Me.colACCT_TYPE.VisibleIndex = 8
        '
        'colAMOUNT
        '
        Me.colAMOUNT.FieldName = "AMOUNT"
        Me.colAMOUNT.Name = "colAMOUNT"
        Me.colAMOUNT.Visible = True
        Me.colAMOUNT.VisibleIndex = 9
        '
        'colPriority_Lvl
        '
        Me.colPriority_Lvl.FieldName = "Priority_Lvl"
        Me.colPriority_Lvl.Name = "colPriority_Lvl"
        Me.colPriority_Lvl.Visible = True
        Me.colPriority_Lvl.VisibleIndex = 10
        '
        'colACCOUNT_ORDER
        '
        Me.colACCOUNT_ORDER.FieldName = "ACCOUNT_ORDER"
        Me.colACCOUNT_ORDER.Name = "colACCOUNT_ORDER"
        Me.colACCOUNT_ORDER.Visible = True
        Me.colACCOUNT_ORDER.VisibleIndex = 11
        '
        'colACH_NAME
        '
        Me.colACH_NAME.FieldName = "ACH_NAME"
        Me.colACH_NAME.Name = "colACH_NAME"
        Me.colACH_NAME.Visible = True
        Me.colACH_NAME.VisibleIndex = 12
        '
        'colTrans_Note
        '
        Me.colTrans_Note.FieldName = "Trans_Note"
        Me.colTrans_Note.Name = "colTrans_Note"
        Me.colTrans_Note.Visible = True
        Me.colTrans_Note.VisibleIndex = 13
        '
        'colRequested_Effective_Date
        '
        Me.colRequested_Effective_Date.FieldName = "Requested_Effective_Date"
        Me.colRequested_Effective_Date.Name = "colRequested_Effective_Date"
        Me.colRequested_Effective_Date.Visible = True
        Me.colRequested_Effective_Date.VisibleIndex = 14
        '
        'colBilling_Dept_Notes
        '
        Me.colBilling_Dept_Notes.FieldName = "Billing_Dept_Notes"
        Me.colBilling_Dept_Notes.Name = "colBilling_Dept_Notes"
        Me.colBilling_Dept_Notes.Visible = True
        Me.colBilling_Dept_Notes.VisibleIndex = 15
        '
        'colTransaction_Status
        '
        Me.colTransaction_Status.FieldName = "Transaction_Status"
        Me.colTransaction_Status.Name = "colTransaction_Status"
        Me.colTransaction_Status.Visible = True
        Me.colTransaction_Status.VisibleIndex = 16
        '
        'colDate_Requested
        '
        Me.colDate_Requested.FieldName = "Date_Requested"
        Me.colDate_Requested.Name = "colDate_Requested"
        Me.colDate_Requested.Visible = True
        Me.colDate_Requested.VisibleIndex = 17
        '
        'colRequested_By
        '
        Me.colRequested_By.FieldName = "Requested_By"
        Me.colRequested_By.Name = "colRequested_By"
        Me.colRequested_By.Visible = True
        Me.colRequested_By.VisibleIndex = 18
        '
        'colTransaction_Effective_Date
        '
        Me.colTransaction_Effective_Date.FieldName = "Transaction_Effective_Date"
        Me.colTransaction_Effective_Date.Name = "colTransaction_Effective_Date"
        Me.colTransaction_Effective_Date.Visible = True
        Me.colTransaction_Effective_Date.VisibleIndex = 19
        '
        'colProcess_Date
        '
        Me.colProcess_Date.FieldName = "Process_Date"
        Me.colProcess_Date.Name = "colProcess_Date"
        Me.colProcess_Date.Visible = True
        Me.colProcess_Date.VisibleIndex = 20
        '
        'colCredit_Routing
        '
        Me.colCredit_Routing.FieldName = "Credit_Routing"
        Me.colCredit_Routing.Name = "colCredit_Routing"
        Me.colCredit_Routing.Visible = True
        Me.colCredit_Routing.VisibleIndex = 21
        '
        'colCredit_Account
        '
        Me.colCredit_Account.FieldName = "Credit_Account"
        Me.colCredit_Account.Name = "colCredit_Account"
        Me.colCredit_Account.Visible = True
        Me.colCredit_Account.VisibleIndex = 22
        '
        'colCredit_ACCT_TYPE
        '
        Me.colCredit_ACCT_TYPE.FieldName = "Credit_ACCT_TYPE"
        Me.colCredit_ACCT_TYPE.Name = "colCredit_ACCT_TYPE"
        Me.colCredit_ACCT_TYPE.Visible = True
        Me.colCredit_ACCT_TYPE.VisibleIndex = 23
        '
        'colCredit_CONUM
        '
        Me.colCredit_CONUM.FieldName = "Credit_CONUM"
        Me.colCredit_CONUM.Name = "colCredit_CONUM"
        Me.colCredit_CONUM.Visible = True
        Me.colCredit_CONUM.VisibleIndex = 24
        '
        'colCredit_EMPNUM
        '
        Me.colCredit_EMPNUM.FieldName = "Credit_EMPNUM"
        Me.colCredit_EMPNUM.Name = "colCredit_EMPNUM"
        Me.colCredit_EMPNUM.Visible = True
        Me.colCredit_EMPNUM.VisibleIndex = 25
        '
        'colTransaction_NACHA_ID
        '
        Me.colTransaction_NACHA_ID.FieldName = "Transaction_NACHA_ID"
        Me.colTransaction_NACHA_ID.Name = "colTransaction_NACHA_ID"
        Me.colTransaction_NACHA_ID.Visible = True
        Me.colTransaction_NACHA_ID.VisibleIndex = 26
        '
        'colCanNotBeCanceled
        '
        Me.colCanNotBeCanceled.FieldName = "CanNotBeCanceled"
        Me.colCanNotBeCanceled.Name = "colCanNotBeCanceled"
        Me.colCanNotBeCanceled.Visible = True
        Me.colCanNotBeCanceled.VisibleIndex = 27
        '
        'colChangeLog
        '
        Me.colChangeLog.FieldName = "ChangeLog"
        Me.colChangeLog.Name = "colChangeLog"
        Me.colChangeLog.Visible = True
        Me.colChangeLog.VisibleIndex = 28
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.EmptySpaceItem1})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1242, 597)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(1222, 553)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.deFromDate
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(299, 24)
        Me.LayoutControlItem2.Text = "From Date: "
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(57, 13)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(299, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(923, 24)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'RibbonPage2
        '
        Me.RibbonPage2.Name = "RibbonPage2"
        Me.RibbonPage2.Text = "RibbonPage2"
        '
        'frmManualBillingDraft
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1242, 745)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.RibbonControl1)
        Me.IconOptions.ShowIcon = False
        Me.Name = "frmManualBillingDraft"
        Me.Ribbon = Me.RibbonControl1
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Manual Billing Drafts"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.deFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deFromDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dD_Manual_Trans_LogsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPage2 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents bbiRefresh As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiCancelDraft As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiAddDraft As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents deFromDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents dD_Manual_Trans_LogsBindingSource As BindingSource
    Friend WithEvents colID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDIVNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDDIVNAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCOUNT_SOURCE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colROUTING_NUMBER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCOUNT_NUMBER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPriority_Lvl As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCOUNT_ORDER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACH_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTrans_Note As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRequested_Effective_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBilling_Dept_Notes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTransaction_Status As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDate_Requested As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRequested_By As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTransaction_Effective_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colProcess_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_Routing As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_Account As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_ACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_CONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCredit_EMPNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTransaction_NACHA_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colAMOUNT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCanNotBeCanceled As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChangeLog As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTransType As DevExpress.XtraGrid.Columns.GridColumn
End Class
