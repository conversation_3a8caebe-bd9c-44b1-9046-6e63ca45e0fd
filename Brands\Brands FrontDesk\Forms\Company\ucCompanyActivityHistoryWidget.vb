﻿Public Class ucCompanyActivityHistoryWidget


    Sub New(data As List(Of view_CompanyActivityHistory))
        InitializeComponent()
        GridControl1.DataSource = data
    End Sub

    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles GridView1.DoubleClick
        Dim row As view_CompanyActivityHistory = GridView1.GetFocusedRow
        If row Is Nothing Then Exit Sub

        If row.RowType = "Email" OrElse row.RowType = "Fax" OrElse row.RowType = "FrontDeskTicket" Then
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim ticket = db.view_FaxAndEmails.Single(Function(t) t.ID = row.ID)
                ticket.OpenFaxOrEmailDetails()
            End Using
        ElseIf row.RowType = "EmailOutbound" Then
            Using frm = New frmSystemEmailSent(row.ID)
                frm.ShowDialog()
            End Using
        End If
    End Sub
End Class
