﻿Imports System.ComponentModel
Imports System.Data
Imports Brands.DAL
Imports DevExpress.Pdf
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports Microsoft.EntityFrameworkCore

Public Class frmReportEmailTemplateAddOrEdit

    Private Property db As dbEPDataDataContext
    Private Property ctx As EPDATAContext
    Private Property _ReportEmailTeplate As ReportEmailTeplate
    Private Property _Parameters As List(Of SqlClient.SqlParameter)
    Private Property _AvavilibleColumns As List(Of String) = New List(Of String)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property reportParameters As BindingList(Of Brands.DAL.ReportEmailTemplateParameter)

    Private isLoading As Boolean = True

    Public Sub New()
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        ctx = New EPDATAContext(GetConnectionString())
        _ReportEmailTeplate = New ReportEmailTeplate With {.PasswordProtect = True}
        db.ReportEmailTeplates.InsertOnSubmit(_ReportEmailTeplate)
        cbeCategory.Properties.Items.AddRange(db.ReportEmailTeplates.Select(Function(r) r.Category).Distinct().ToArray())
        SpinEdit1.AddClearButton()
        seApplyMacroToTicket.AddClearButton
        seExecuteMacroId.AddClearButton
    End Sub

    Public Sub New(reportName As String)
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        ctx = New EPDATAContext(GetConnectionString())
        _ReportEmailTeplate = db.ReportEmailTeplates.Single(Function(e) e.Name = reportName)
        cbeCategory.Properties.Items.AddRange(db.ReportEmailTeplates.Select(Function(r) r.Category).Distinct().ToArray())
    End Sub

    Private Sub frmNewReportEmailTemplate_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            lcgPublicWebsiteSettings.Visibility = Permissions.AllowWebAppSetup.ToBarItemVisibility
            lcgReportParameters.Visibility = Permissions.AllowWebAppSetup.ToBarItemVisibility
            lueReportCategory.AddClearButton
            tcgReportType.SelectedTabPage = lcgSingleReport
            ReportEmailTeplateBindingSource.DataSource = _ReportEmailTeplate

            ctx.ReportEmailTemplateParameters.Where(Function(r) r.ReportId = _ReportEmailTeplate.ID).Load()
            reportParameters = ctx.ReportEmailTemplateParameters.Local.ToBindingList()
            GridControl1.DataSource = reportParameters

            lueReportCategory.Properties.DataSource = ctx.ReportEmailTemplateCategories.ToList()

            ccbeEpReportsParameters.Properties.DataSource = db.ReportEPParamMaps.OrderBy(Function(o) o.Order).ToList()
            ccbeEpReportsParameters.Properties.DisplayMember = "Description"
            ccbeEpReportsParameters.Properties.ValueMember = "Param"
            ccbeEpReportsParameters.RefreshEditValue()

            riCcbeEpReportsParameters.DataSource = db.ReportEPParamMaps.OrderBy(Function(o) o.Order).ToList()
            riCcbeEpReportsParameters.DisplayMember = "Description"
            riCcbeEpReportsParameters.ValueMember = "Param"

            slueReportList.Properties.DataSource = Query("SELECT t.REPORT_ID, t.REPORT_NAME,t.DESCR,t.GROUP1 FROM [EPDATA].[dbo].[REPORT_LIST] t")

            SubFilesBindingSource.DataSource = _ReportEmailTeplate.ReportEmailTemplateFiles
            tcgReportType.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False

            lueLiveReport.Properties.ValueMember = "id"
            lueLiveReport.Properties.DisplayMember = "name"
            lueLiveReport.Properties.DataSource = Query("SELECT id, lr.name, lr.description, lr.category FROM live_report lr 
                WHERE lr.mark_as_deleted = 0 AND lr.is_draft = 0
                AND lr.id IN (SELECT ra.parent_id FROM live_report_access ra WHERE ra.name = 'Client Visibility' AND ra.value IN ('PPx Pro', 'PPx') AND ra.mark_as_deleted = 0)
                UNION SELECT 0x, '', '', ''
                ORDER BY lr.name")

            isLoading = False
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            DxErrorProvider1.ClearErrors()
            If NameTextEdit.Text.IsNullOrWhiteSpace() Then
                DxErrorProvider1.SetError(NameTextEdit, "Required")
            ElseIf _ReportEmailTeplate.ReportType.IsNullOrWhiteSpace() Then
                DxErrorProvider1.SetError(ReportTypeComboBoxEdit, "Required")
            End If
            If DxErrorProvider1.HasErrors Then
                Console.Beep()
                Exit Sub
            End If

            ctx.SaveChanges()
            If db.SaveChanges() Then Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        End Try
    End Sub

    Private Sub PathButtonEdit_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles PathButtonEdit.ButtonClick, riBeReportPath.ButtonClick
        Dim fd = New OpenFileDialog() With {.Filter = "Crystal Reports (*.rpt)|*.rpt|Crystal Reports Secure (*.rptr)|*.rptr|Pdf (*.pdf)|*.pdf|All files (*.*)|*.*"}
        If fd.ShowDialog = System.Windows.Forms.DialogResult.OK Then
            TryCast(sender, DevExpress.XtraEditors.ButtonEdit).Text = fd.FileName
        End If
    End Sub

    Private Async Sub bePdfFieldsMapping_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles bePdfFieldsMapping.ButtonClick, riBePdfFieldMap.ButtonClick
        lcRoot.ShowProgessPanel()
        Try
            If _ReportEmailTeplate.ReportType = "Multi" Then
                Dim report As ReportEmailTemplateFile = SubFilesBindingSource.Current
                If report.ReportType = "StaticFile" Then
                    DisplayMessageBox("Static Files does not support fields mapping feature.")
                    Return
                End If
            End If
            If Me.meSql.Text.IsNullOrWhiteSpace Then
                DisplayMessageBox("Please enter a Sql statement.")
                Exit Sub
            End If
            Dim data As Tuple(Of List(Of SqlClient.SqlParameter), DataTable) = Await modReports.PreviewData(meSql.Text, False, _Parameters)
            _Parameters = data.Item1
            _AvavilibleColumns.Clear()
            For Each col As System.Data.DataColumn In data.Item2.Columns
                _AvavilibleColumns.Add(String.Format("{{{0}}}", col.ColumnName))
            Next

            Using pdf As PdfDocumentProcessor = New PdfDocumentProcessor
                Dim fieldList As List(Of KeyValuePair) = New List(Of KeyValuePair)
                If _ReportEmailTeplate.ReportType = "Multi" Then
                    Dim report As ReportEmailTemplateFile = SubFilesBindingSource.Current
                    pdf.LoadDocument(report.ReportPath)
                    fieldList = modReports.GetKeyValueParameters(pdf, report.FieldsMap)
                Else
                    pdf.LoadDocument(_ReportEmailTeplate.ReportPath)
                    fieldList = modReports.GetKeyValueParameters(pdf, _ReportEmailTeplate.FieldsMap)
                End If
                Dim frm = New frmReportsParameters(fieldList, _AvavilibleColumns, True) With {.UseCustomEditors = False}
                If frm.ShowDialog() = DialogResult.OK Then
                    If _ReportEmailTeplate.ReportType = "Multi" Then
                        Dim report As ReportEmailTemplateFile = SubFilesBindingSource.Current
                        report.FieldsMap = frm.GetKeyValuePairs().Where(Function(kv) kv.Value.IsNotNullOrWhiteSpace()).ToStringMap()
                    Else
                        _ReportEmailTeplate.FieldsMap = frm.GetKeyValuePairs().Where(Function(kv) kv.Value.IsNotNullOrWhiteSpace()).ToStringMap()
                    End If
                    'TryCast(sender, DevExpress.XtraEditors.ButtonEdit).Text = frm.GetKeyValuePairs().Where(Function(kv) kv.Value.IsNotNullOrWhiteSpace()).ToStringMap()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error in PDF field mapping", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Sub


    Private Sub ReportTypeComboBoxEdit_SelectedValueChanged(sender As Object, e As EventArgs) Handles ReportTypeComboBoxEdit.SelectedValueChanged
        lciEpReportsParameter.Enabled = ReportTypeComboBoxEdit.HasValue AndAlso ReportTypeComboBoxEdit.EditValue = "EP"
        If ReportTypeComboBoxEdit.EditValue = "Multi" Then
            tcgReportType.SelectedTabPage = lcgMultiReports
        Else
            tcgReportType.SelectedTabPage = lcgSingleReport
        End If
        If ReportTypeComboBoxEdit.EditValue = "Eml" Then
            _ReportEmailTeplate.EmailBodySource = "EmailTemplate"
            cbeEmailBodySource.EditValue = "EmailTemplate"
        End If
        cbeEmailBodySource.Enabled = ReportTypeComboBoxEdit.EditValue <> "Eml" AndAlso ReportTypeComboBoxEdit.EditValue <> "Zendesk"
        If ReportTypeComboBoxEdit.EditValue = "Zendesk" Then
            _ReportEmailTeplate.EmailBodySource = "Macro"
            cbeEmailBodySource.EditValue = "Macro"
        End If
    End Sub

    Private Sub gvSubReports_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvSubReports.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub()
                                                                                If SubFilesBindingSource.Current IsNot Nothing Then
                                                                                    db.ReportEmailTemplateFiles.DeleteOnSubmit(SubFilesBindingSource.Current)
                                                                                    SubFilesBindingSource.RemoveCurrent()
                                                                                End If
                                                                            End Sub, My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub GridView1_InitNewRow(sender As Object, e As DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs) Handles GridView1.InitNewRow
        Dim view As GridView = CType(sender, GridView)
        view.SetRowCellValue(e.RowHandle, colReportId, _ReportEmailTeplate.ID)
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() GridView1.DeleteSelectedRows(), My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub btnLoadReportParameters_Click(sender As Object, e As EventArgs) Handles btnLoadReportParameters.Click
        Try
            Dim processor = New ReportProcessor(812, _ReportEmailTeplate, FileType.Pdf)
            Dim parameters = processor.GetReportParameters()
            For Each p In parameters
                Dim parameter = reportParameters.SingleOrDefault(Function(rp) rp.Name = p.Name)
                If parameter IsNot Nothing Then
                    parameter.DataType = p.DataType
                    parameter.DefaultValue = p.DefaultValue
                Else
                    p.ReportId = _ReportEmailTeplate.ID
                    reportParameters.Add(p)
                End If
            Next

            Dim parametersNotInReport = reportParameters.Where(Function(p) Not parameters.Select(Function(rp) rp.Name).Contains(p.Name))
            For Each p In parametersNotInReport
                If XtraMessageBox.Show($"Parameter {p.Name} does not exist in the report, would you like to remove it?", "Remove unused parameter?", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                    reportParameters.Remove(p)
                End If
            Next

            GridControl1.RefreshDataSource()
        Catch ex As Exception
            DisplayErrorMessage("Error loading report parameters", ex)
        End Try
    End Sub

    Private Sub riDropOptionsEditor_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles riDropOptionsEditor.ButtonClick
        Using frm = New frmParamDropDownEditor
            If GridView1.GetFocusedRowCellDisplayText("DropDownOptionsJson").IsNotNullOrWhiteSpace Then
                frm.SetParameterOptions(GridView1.GetFocusedRowCellDisplayText("DropDownOptionsJson"))
            End If
            If frm.ShowDialog = DialogResult.OK Then
                GridView1.SetFocusedRowCellValue(colDropDownOptionsJson, frm.GetJsonResults())
            End If
        End Using
    End Sub

    Private Sub HyperLinkEdit1_OpenLink(sender As Object, e As Controls.OpenLinkEventArgs) Handles HyperLinkEdit1.OpenLink
        e.EditValue = "https://support.brandspaycheck.com/hc/en-us/articles/360034754893"
    End Sub

    Private Sub TabbedControlGroup1_SelectedPageChanged(sender As Object, e As DevExpress.XtraLayout.LayoutTabPageChangedEventArgs) Handles TabbedControlGroup1.SelectedPageChanged
        If e.Page Is lcgEmailPreview Then
            If ceAutoAddHeaderAndFooter.Checked Then
                RichEditControl1.HtmlText = GetUdfValue("EmailTemplate").Replace("<!-- [Text To Be Replaced] -->", EmailBodyMemoEdit.Text)
            Else
                RichEditControl1.HtmlText = EmailBodyMemoEdit.Text
            End If
        End If
    End Sub

    Private Sub cbeEmailBodySource_SelectedValueChanged(sender As Object, e As EventArgs) Handles cbeEmailBodySource.SelectedValueChanged
        lcgEmailBody.Enabled = cbeEmailBodySource.HasValue AndAlso cbeEmailBodySource.EditValue = "EmailTemplate"
    End Sub

    Private Sub seExecuteMacroId_KeyDown(sender As Object, e As KeyEventArgs) Handles seExecuteMacroId.KeyDown, seApplyMacroToTicket.KeyDown
        If e.Control AndAlso e.KeyCode = Keys.Delete Then
            CType(sender, SpinEdit).EditValue = Nothing
            CType(sender, SpinEdit).IsModified = False
            e.Handled = True
        End If
    End Sub

    Private Sub lueLiveReport_EditValueChanged(sender As Object, e As EventArgs) Handles lueLiveReport.EditValueChanged
        If isLoading Then
            Return
        End If

        If Not lueLiveReport.Text = "" Then
            _ReportEmailTeplate.LiveReportId = lueLiveReport.EditValue
            _ReportEmailTeplate.ReportType = "LiveRpt"
        Else
            _ReportEmailTeplate.LiveReportId = Nothing
            _ReportEmailTeplate.ReportType = Nothing
            ReportTypeComboBoxEdit.EditValue = Nothing
        End If
    End Sub
End Class