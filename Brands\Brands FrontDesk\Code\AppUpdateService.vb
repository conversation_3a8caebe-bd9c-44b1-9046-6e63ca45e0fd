﻿Public Class AppUpdateService

    Private updateLogger As Serilog.ILogger

    Public Sub New()
        updateLogger = Logger.ForContext(Of AppUpdateService)
    End Sub


    Public Function IsUpdateAvailibe() As Boolean
        'Dim updateAvailible As Boolean = False
        'Dim lastVersion As String = String.Empty
        'Using mgr = New UpdateManager("I:\FrontDesk\Releases")
        '    SquirrelAwareApp.HandleEvents(Sub() mgr.CreateShortcutForThisExe(), Sub() mgr.CreateShortcutForThisExe(), onAppUninstall:=Sub() mgr.RemoveShortcutForThisExe())
        '    Dim ver = mgr.CurrentlyInstalledVersion
        '    modGlobals.Version = $" ({ver.Version.ToString()})"
        '    Logger.Information("Checking for updates., Current {Version}", ver)
        '    Dim re = Await mgr.CheckForUpdate()
        '    Logger.Information("Finished checking for updates")
        '    If re IsNot Nothing AndAlso re.FutureReleaseEntry IsNot Nothing AndAlso re.FutureReleaseEntry.Version IsNot Nothing Then
        '        lastVersion = re.FutureReleaseEntry.Version.Version.ToString()
        '        If re.FutureReleaseEntry.Version > ver Then
        '            Logger.Information("Updating App, New {Version}", re.FutureReleaseEntry.Version)
        '            Dim r As ReleaseEntry = Await mgr.UpdateApp()
        '            Logger.Information("App is updated")
        '            updateAvailible = True
        '        Else
        '            Logger.Information("App is up to date")
        '            If showMsg Then XtraMessageBox.Show("Application is up to date.")
        '        End If
        '    End If
        'End Using
        'If updateAvailible Then
        '    If DevExpress.XtraEditors.XtraMessageBox.Show("The Application was updated. Would you like to restart Now?", "Update App", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
        '        frm.Cursor = Cursors.WaitCursor
        '        Dim path = System.Reflection.Assembly.GetExecutingAssembly().Location
        '        Dim dir = System.IO.Path.GetDirectoryName(path)
        '        Dim upDir = System.IO.Path.GetDirectoryName(dir)
        '        ' Await System.Threading.Tasks.Task.Delay(1000 * 3)
        '        Logger.Information("Restarting App")
        '        Dim exePath = System.IO.Path.Combine(upDir, $"app-{lastVersion}", "Brands FrontDesk.exe")
        '        System.Diagnostics.Process.Start(exePath)
        '        UpdateManager.RestartApp()
        '    End If
        'End If
    End Function

End Class
