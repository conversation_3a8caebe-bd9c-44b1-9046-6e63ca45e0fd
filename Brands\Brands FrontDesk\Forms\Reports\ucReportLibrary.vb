﻿Imports System.ComponentModel
Imports System.Data
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Grid

Public Class ucReportLibrary

    Private Property db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Comp As COMPANY
    Private Property frmLogger As Serilog.ILogger

    Public Sub New()
        frmLogger = Logger.ForContext(Of ucReportLibrary)()
        InitializeComponent()
    End Sub

    Private Sub ucPayrollReports_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            UcRecentReports1.Comp = Comp
            If Not DesignMode Then
                XtraTabControl1.SelectedTabPageIndex = 0
                db = New dbEPDataDataContext(GetConnectionString)
                AddHandler XtraTabControl1.SelectedPageChanged, Sub()
                                                                    LoadDate()
                                                                End Sub
                LoadDate()
                teQuarterEndHold.Text = db.COOPTIONs.Single(Function(c) c.CONUM.Equals(Comp.CONUM)).QYEND_HOLD_TYPE
                If teQuarterEndHold.Text.ToLower.Contains("billing") Then
                    lciQuarterEndHold.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                End If
                teReportPassword.Text = GetComPassword(Comp.CONUM)
            End If
            UcRecentReports1.LoadRecentReports()
            riLueReportType.DataSource = New List(Of KeyValuePair)(New KeyValuePair() {New KeyValuePair("1", "Paper"), New KeyValuePair("2", "Paperless")})
            riLueReportType.ValueMember = "Name"
            riLueReportType.DisplayMember = "Value"
        Catch ex As Exception
            DisplayErrorMessage("Error loading Report Library", ex)
        End Try
    End Sub
    Private Sub LoadDate()
        Try
            lcRoot.ShowProgessPanel()
            Dim list As IEnumerable(Of view_REPORTS_UNION) = Nothing '(From r In db.view_REPORTS_UNIONs Where r.CONUM = Comp.CONUM)
            Dim exclude = New Integer() {975, 976, 977, 978, 979, 980, 981, 982, 985, 55001, 55002, 55003, 55004, 55005, 55008, 55009, 55010, 55011, 55012, 55013, 55015, 55055}
            Dim _dateFilter = GetYearFilter()

            'Dim DataSource = Query("select top 0 from company")

            Dim i = XtraTabControl1.SelectedTabPageIndex + 1
            If i = 1 Then
                'list = (From l In list Where l.REPORT_TYPE = 1 AndAlso l.PERSONAL = "NO")
                list = (From l In db.view_REPORTS_UNIONs Where l.CONUM = Comp.CONUM AndAlso l.REPORT_TYPE = 1 AndAlso l.PERSONAL = "NO" AndAlso l.CHECK_DATE > _dateFilter AndAlso Not exclude.Contains(l.RPT_ID))
                'DataSource = Query($"EXEC custom.prc_RptReportsUnion @CoNum = {Comp.CONUM}
                ',@CheckDateStart = '{_dateFilter}'
                ',@CheckDateEnd = '{Today.AddDays(100)}'
                ',@Personal = 'NO'
                ',@ReportType = 1")
                GridControl1.MainView = gvPayrollReports
            ElseIf i = 2 Then
                'list = (From l In list Where l.REPORT_TYPE = 1 AndAlso l.PERSONAL = "YES")
                list = (From l In db.view_REPORTS_UNIONs Where l.CONUM = Comp.CONUM AndAlso l.REPORT_TYPE = 1 AndAlso l.PERSONAL = "YES" AndAlso l.CHECK_DATE > _dateFilter AndAlso Not exclude.Contains(l.RPT_ID))
                'DataSource = Query($"EXEC custom.prc_RptReportsUnion @CoNum = {Comp.CONUM}
                ',@CheckDateStart = '{_dateFilter}'
                ',@CheckDateEnd = '{Today.AddDays(100)}'
                ',@Personal = 'YES'
                ',@ReportType = 1")
                GridControl1.MainView = gvDistributedPayStubs
            ElseIf i = 3 Then
                'list = (From l In list Where l.REPORT_TYPE = 2 AndAlso l.PERSONAL = "NO" AndAlso (l.JOB_TYPE = "Client" OrElse (l.JOB_TYPE = "Etax" AndAlso Permissions.TaxDepartment)))
                list = (From l In db.view_REPORTS_UNIONs Where l.CONUM = Comp.CONUM AndAlso l.REPORT_TYPE = 2 AndAlso l.PERSONAL = "NO" AndAlso (l.JOB_TYPE = "Client" OrElse (l.JOB_TYPE = "Etax" AndAlso Permissions.TaxDepartment)) AndAlso (l.PRT_YEAR > _dateFilter.Year OrElse
                                (l.PRT_YEAR = _dateFilter.Year AndAlso l.PRT_QTR >= modGlobals.GetQuarter())) AndAlso Not exclude.Contains(l.RPT_ID))
                gvPeriodEndReports.ClearSorting()
                gvPeriodEndReports.SortInfo.Insert(2, New GridColumnSortInfo(colJOB_TYPE1, DevExpress.Data.ColumnSortOrder.Descending))
                gvPeriodEndReports.SortInfo.Insert(3, New GridColumnSortInfo(colRPT_ORDER1, DevExpress.Data.ColumnSortOrder.Ascending))
                colRPT_COUNTER1.GroupIndex = 2
                GridControl1.MainView = gvPeriodEndReports
            ElseIf i = 4 Then
                'list = (From l In list Where l.REPORT_TYPE = 2 AndAlso l.PERSONAL = "YES")
                list = (From l In db.view_REPORTS_UNIONs Where l.CONUM = Comp.CONUM AndAlso l.REPORT_TYPE = 2 AndAlso l.PERSONAL = "YES" AndAlso (l.PRT_YEAR > _dateFilter.Year OrElse
                                (l.PRT_YEAR = _dateFilter.Year AndAlso l.PRT_QTR >= modGlobals.GetQuarter())) AndAlso Not exclude.Contains(l.RPT_ID))
                GridControl1.MainView = gvDistributedW2s
            End If

            'If i = 1 OrElse i = 2 Then
            '    list = (From l In list Where l.CHECK_DATE > _dateFilter)
            'End If

            'If i = 3 OrElse i = 4 Then
            '    list = (From l In list Where (l.PRT_YEAR > _dateFilter.Year OrElse
            '                    (l.PRT_YEAR = _dateFilter.Year AndAlso l.PRT_QTR >= modGlobals.GetQuarter())))
            'End If

            'list = list.Where(Function(r) Not exclude.Contains(r.RPT_ID))

            GridControl1.DataSource = list.ToList()

            Dim gv = CType(GridControl1.MainView, GridView)
            gv.ClearColumnsFilter()
            gv.BestFitColumns()
            Dim col = gv.Columns("RPT_STATUS")
            gv.ActiveFilterString = "[{0}] = 'Completed' OR [{0}] = 'Unprocessed'".FormatWith(col.FieldName)
            gv.ExpandAllGroups()
        Catch ex As Exception
            DisplayErrorMessage("Error loading Payroll Reports", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Sub

    Private Function GetYearFilter() As DateTime
        If cbeYearFilter.Text = "One Year" Then
            Return DateTime.Now.AddYears(-1)
        ElseIf cbeYearFilter.Text = "Two Years" Then
            Return DateTime.Now.AddYears(-2)
        ElseIf cbeYearFilter.Text = "Three Years" Then
            Return DateTime.Now.AddYears(-3)
        ElseIf cbeYearFilter.Text = "Four Years" Then
            Return DateTime.Now.AddYears(-4)
        ElseIf cbeYearFilter.Text = "All Years" Then
            Return DateTime.Now.AddYears(-50)
        Else
            Throw New Exception("{0} is not recognized as a valid Year filter".FormatWith(cbeYearFilter.Text))
        End If
    End Function

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvPayrollReports.PopupMenuShowing, gvPeriodEndReports.PopupMenuShowing, gvDistributedW2s.PopupMenuShowing, gvDistributedPayStubs.PopupMenuShowing
        If e.Menu Is Nothing Then Return

        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim gv As GridView = sender
            If gv.GetSelectedRows().Count = 0 _
                    AndAlso Not ceAllowClientOnHold.Checked _
                    AndAlso {2, 3}.Contains(XtraTabControl1.SelectedTabPageIndex) Then
                'Solomon modified static msg "when billing on hold" to NeedApproval value
                Dim msgNeedApproval = CType(gv.GetRow(e.HitInfo.RowHandle), view_REPORTS_UNION).NeedApproval
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Process selected - (You don't have permission to send when '" + msgNeedApproval + "')") With {.Enabled = False})
            Else
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Process Selected", Sub()
                                                                                              ProcessAndOpen(e.HitInfo.RowHandle, CType(sender, GridView))
                                                                                          End Sub))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Process && Send (Zendesk)", Sub() ProcessAndSendZendesk(e.HitInfo.RowHandle, CType(sender, GridView))))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Process && Attach (Zendesk)", Sub() ProcessAndAttachToZendesk(e.HitInfo.RowHandle, CType(sender, GridView))))
            End If
        End If
    End Sub

    Async Sub ProcessAndSendZendesk(rowHandle As Integer, gv As GridView)
        Try
            Await CanCreateTicketAsync()
            Dim filePath = ProcessReports(rowHandle, gv)
            If filePath Is Nothing Then Exit Sub
            Dim passProtectedPath = New String() {PasswordProtectFiles(filePath, Comp.CONUM)}.ToList()
            Await modZendeskIntegrationClient.CreateTicketAsync("", Comp, Nothing, modReports.EmailSubject, passProtectedPath)
        Catch ex As Exception
            DisplayErrorMessage("Error creating zendesk ticket", ex)
        End Try
    End Sub

    Sub ProcessAndAttachToZendesk(rowHandle As Integer, gv As GridView)
        Dim filePath = ProcessReports(rowHandle, gv)
        If filePath Is Nothing Then Exit Sub
        Dim passProtectedPath = New String() {PasswordProtectFiles(filePath, Comp.CONUM)}.ToList()
        Using frmAttachments = New frmDraggableAttachments(passProtectedPath)
            frmAttachments.ShowDialog()
        End Using
    End Sub

    Sub ProcessAndOpen(rowHandle As Integer, gv As GridView)
        Dim filePath = ProcessReports(rowHandle, gv)
        If filePath Is Nothing Then Exit Sub
        If File.Exists(filePath) AndAlso DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to open this file ?", "Export", MessageBoxButtons.YesNo) = DialogResult.Yes Then
            'System.Diagnostics.Process.Start(filePath)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = filePath
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        End If
    End Sub

    Private Function ProcessReports(rowHandle As Integer, gv As GridView) As String
        Try
            Dim newPdf = New DevExpress.Pdf.PdfDocumentProcessor
            newPdf.CreateEmptyDocument()
            Dim fileName = If(gv.SelectedRowsCount = 1, CType(gv.GetRow(rowHandle), view_REPORTS_UNION).REPORT_NAME, "Payroll Reports")
            fileName = fileName.Replace("/", "_")
            Dim filePath = Path.Combine(modReports.GetCrystalReportsFolder(), modReports.GetFileName(Comp, fileName, "pdf"))
            Dim row As view_REPORTS_UNION = Nothing
            For Each i In gv.GetSelectedRows
                row = gv.GetRow(i)
                Try
                    Dim dt = modGlobals.Query("SELECT ru.REPORT_FILE FROM dbo.REPORTS_UNION ru WHERE ru.id = @P0 AND ru.CONUM = @P1", New SqlClient.SqlParameter("@P0", row.ID), New SqlClient.SqlParameter("@P1", row.CONUM))
                    Dim byteArray As Byte() = dt.Rows(0)(0)
                    Dim pdf = New DevExpress.Pdf.PdfDocumentProcessor()
                    pdf.LoadDocument(New MemoryStream(byteArray))
                    If pdf.Document.Pages.Count = 0 AndAlso row.RPT_ID = 206 Then
                        Continue For
                    End If
                    PdfUtilities.CopyPages(pdf, newPdf, row.REPORT_NAME)
                Catch ex As Exception
                    Logger.Error(ex, "Error creating PDF file for {CoNum} {ReportName} CheckDate: {CheckDate}", Comp.CONUM, row.REPORT_NAME, row.CHECK_DATE)
                    If XtraMessageBox.Show("Error creating PDF for Report: {0} Would you like to continue without it?".FormatWith(row.REPORT_NAME), "Skip Report", MessageBoxButtons.YesNo) = DialogResult.No Then
                        Return Nothing
                    End If
                End Try
            Next
            If newPdf.Document.Pages.Count = 0 Then
                DisplayMessageBox("The document is empty.")
                Return Nothing
            End If
            newPdf.SaveDocument(filePath)
            UcRecentReports1.LoadRecentReports()
            Return filePath
        Catch ex As Exception
            DisplayErrorMessage("Error creating PDF for selected Payroll Reports.", ex)
            frmLogger.Error(ex, "Error creating PDF for selected Payroll Reports {CoNum}", Comp.CONUM)
        End Try
        Return Nothing
    End Function

    Private Function PasswordProtectFiles(filePath As String, conum As Decimal) As String
        Dim coOptions = db.CoOptions_Payrolls.SingleOrDefault(Function(c) c.CoNum = conum)

        If coOptions Is Nothing OrElse coOptions.SendReportsAsZip.GetValueOrDefault Then
            Return ReportsExtensions.SetPdfPassword(filePath, GetComPassword(conum))
        Else
            Dim savePath = Path.ChangeExtension(System.IO.Path.Combine(modReports.GetSecureCrystalReportsFolder(), "{0}_Reports_{1}".FormatWith(conum, modReports.FormatedDate())), ".zip")
            Return modReports.ZipFiles(New String() {filePath}.ToList(), GetComPassword(conum), savePath)
        End If
    End Function

    Private Sub cbeYearFilter_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cbeYearFilter.SelectedIndexChanged
        LoadDate()
    End Sub

    Private Sub rgReportType_SelectedIndexChanged(sender As Object, e As EventArgs)
        LoadDate()
    End Sub

    Private Sub ceAllowClientsOnHold_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles ceAllowClientOnHold.EditValueChanging
        If Not Permissions.AllowSendQtrEndEmailOnBillindHold Then
            DisplayMessageBox("You do not have permission to send to clients with billing hold status")
            e.Cancel = True
        ElseIf e.OldValue = False Then
            e.Cancel = Not (XtraMessageBox.Show("You are about to send clients on hold, Continue?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Send?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK)
        End If
    End Sub

    Private Sub gv_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles gvPeriodEndReports.SelectionChanged, gvDistributedW2s.SelectionChanged
        If e Is Nothing Then Exit Sub
        Dim gv As GridView = sender
        If Not ceAllowClientOnHold.Checked Then
            For Each i In gv.GetSelectedRows
                Dim row As view_REPORTS_UNION = gv.GetRow(i)
                If row.NeedApproval.IsNotNullOrWhiteSpace() AndAlso Not row.NeedApproval.StartsWith("seasonal", StringComparison.CurrentCultureIgnoreCase) _
                    AndAlso Not row.NeedApproval.StartsWith("new co code", StringComparison.CurrentCultureIgnoreCase) Then
                    gv.UnselectRow(i)
                End If
            Next
        End If
    End Sub

    Private Sub teReportPassword_Properties_Click(sender As Object, e As Controls.ButtonPressedEventArgs) Handles teReportPassword.Properties.ButtonClick

    End Sub

    Private Sub teReportPassword_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles teReportPassword.ButtonClick
        If e.Button.Caption = "Change Password" Then
            Dim frm = New frmChangePassword(Comp.CONUM)
            If frm.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                teReportPassword.Text = frm.teNewPass.Text
            End If
        ElseIf e.Button.Caption = "Email Password" Then
            EmailPassword(Comp.CONUM, True)
        End If
    End Sub
End Class
