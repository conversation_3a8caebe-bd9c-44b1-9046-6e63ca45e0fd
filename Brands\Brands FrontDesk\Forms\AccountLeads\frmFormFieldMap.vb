﻿Imports System.ComponentModel
Imports Brands.Core.AccountLeads.Models

Public Class frmFormFieldMap
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property FieldMappings As List(Of FieldMap)

    Private Sub frmFormFieldMap_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            teApiKey.Text = GetUdfValue("JotForms Api")
            teFormId.Text = GetUdfValue("JotForms FormId")
            Dim json = GetUdfValue("JotForms Field Mapping")
            FieldMappings = Newtonsoft.Json.JsonConvert.DeserializeObject(Of List(Of FieldMap))(json)
            GridControl1.DataSource = FieldMappings
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
            btnSave.Enabled = False
        End Try
    End Sub

    Private Async Sub btnAddNewMap_ClickAsync(sender As Object, e As EventArgs) Handles btnAddNewMap.Click
        Try
            Dim addFieldMap = Sub(field As FieldMap)
                                  FieldMappings.Add(field)
                                  GridView1.RefreshData()
                              End Sub
            Using frm = New frmNewFormFieldMap(False, addFieldMap)
                Dim api = New Brands.Core.AccountLeads.AccountLeadsApi(teApiKey.Text, Logger)
                Dim questions = Await api.GetFormQuestionAsync(teFormId.EditValue)
                frm.SetQuestions(questions)
                frm.ShowDialog()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error adding new map", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            SetUdfValue("JotForms Api", teApiKey.Text)
            SetUdfValue("JotForms FormId", teFormId.Text)
            SetUdfValue("JotForms Field Mapping", Newtonsoft.Json.JsonConvert.SerializeObject(FieldMappings))
            DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving data", ex)
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete Row", Sub() GridView1.DeleteRow(e.HitInfo.RowHandle)))
        End If
    End Sub
End Class