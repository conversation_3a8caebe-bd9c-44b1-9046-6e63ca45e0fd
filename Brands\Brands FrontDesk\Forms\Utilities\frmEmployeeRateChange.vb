﻿Imports System.Data
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Columns

Public Class frmEmployeeRateChange
    Sub New()
        InitializeComponent()
        deHireDateAfter.AddClearButton
    End Sub

    Private _db As dbEPDataDataContext
    Private Property CoNum As Decimal
    Private Property EmployeesDataTable As DataTable

    Private Sub frmEmployeeRateChange_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            _db = New dbEPDataDataContext(GetConnectionString)
            view_CompanySumarriesBindingSource.DataSource = _db.view_CompanySumarries.ToList()
            lcgEmployess.Enabled = False
        Catch ex As Exception
            DisplayErrorMessage("Error loading data.", ex)
        End Try
    End Sub

    Private Sub SearchLookUpEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles slueCoNum.EditValueChanged
        Try
            If Decimal.TryParse(nz(slueCoNum.EditValue, ""), CoNum) Then
                LoadDate()
                lcgEmployess.Enabled = True
            Else
                lcgEmployess.Enabled = False
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading employees", ex)
        End Try
    End Sub

    Private Sub LoadDate()
        EmployeesDataTable = Query("WITH emps AS(
                        SELECT
                        CONVERT(BIT,0) IsChecked,
                        e.CONUM,
                        e.EMPNUM, 
                        CONVERT(VARCHAR(6), e.DIVNUM) + ' - ' + d.DDIVNAME DivNumName, 
                        CONVERT(VARCHAR(6), e.DEPTNUM) + ' - ' + dp.DEPT_DESC DepNumName,
                        e.EMP_TYPE,
                        e.TERM_DATE,
                        e.START_DATE HireDate,
                        e.F_NAME,
                        e.M_NAME,
                        e.L_NAME
                        FROM dbo.EMPLOYEE e
                        LEFT OUTER JOIN dbo.DIVISION d ON d.CONUM = e.CONUM AND d.DDIVNUM = e.DIVNUM
                        LEFT OUTER JOIN dbo.DEPARTMENT dp ON dp.CONUM = e.CONUM AND dp.DIVNUMD = d.DDIVNUM AND dp.DEPTNUM = e.DEPTNUM
                        WHERE e.CONUM = @CoNum)
                        SELECT * FROM
                        (
                        SELECT e.*, 'Rate 1' Rate, e1.RATE_1 RateAmount FROM emps e
                        INNER JOIN dbo.EMPLOYEE e1 ON e1.CONUM = e.CONUM AND e.EMPNUM = e1.EMPNUM
                        UNION ALL
                        SELECT e.*, 'Rate 2', e1.RATE_2 FROM emps e
                        INNER JOIN dbo.EMPLOYEE e1 ON e1.CONUM = e.CONUM AND e.EMPNUM = e1.EMPNUM
                        UNION ALL
                        SELECT e.*, 'Rate 3', e1.RATE_3 FROM emps e
                        INNER JOIN dbo.EMPLOYEE e1 ON e1.CONUM = e.CONUM AND e.EMPNUM = e1.EMPNUM
                        UNION ALL
                        SELECT e.*, 'Salary', e1.SALARY_AMT FROM emps e
                        INNER JOIN dbo.EMPLOYEE e1 ON e1.CONUM = e.CONUM AND e.EMPNUM = e1.EMPNUM
                        ) a
                        ORDER BY a.EMPNUM, a.Rate", New SqlParameter("CoNum", CoNum))
        EmployeesDataTable.Columns.Add(New DataColumn("EffectiveDate", GetType(DateTime)))
        EmployeesDataTable.Columns.Add(New DataColumn("NewRate", GetType(Decimal)))
        BindingSource1.DataSource = EmployeesDataTable
        For Each col As GridColumn In GridView1.Columns
            If Not {"IsChecked", "NewRate", "EffectiveDate"}.Contains(col.FieldName) Then
                col.OptionsColumn.AllowEdit = False
            End If
            col.OptionsFilter.FilterPopupMode = FilterPopupMode.CheckedList

            If col.FieldName = "IsChecked" Then
                col.ColumnEdit = riCeIsChecked
                col.Caption = "Update"
            End If

            If col.FieldName = "EffectiveDate" Then
                col.AppearanceHeader.Font = New Font(col.AppearanceHeader.Font, FontStyle.Bold)
            End If
        Next

        Dim NewRatecol = GridView1.Columns.ColumnByFieldName("NewRate")
        NewRatecol.AppearanceHeader.Font = New Font(NewRatecol.AppearanceHeader.Font, FontStyle.Bold)
        NewRatecol.ColumnEdit = riTeNewRate
        GridView1.BestFitColumns()
        cbeEmpStatus.Text = "Active Only"
        SetDataTableFilter()
    End Sub

    Private Sub SetDataTableFilter()
        Dim filter As String = String.Empty
        Try
            If cbeEmpStatus.Text = "Active Only" Then
                filter = "TERM_DATE IS NULL "
            ElseIf cbeEmpStatus.Text = "Terminated Only" Then
                filter = "TERM_DATE IS NOT NULL "
            End If

            If Not ceShowBlankRates.Checked Then
                If filter.IsNotNullOrWhiteSpace Then filter &= " AND "
                filter &= " (RateAmount IS NOT NULL AND RateAmount > 0)"
            End If

            If cbeEmployeeType.Text.IsNotNullOrWhiteSpace Then
                If filter.IsNotNullOrWhiteSpace Then filter &= " AND "
                filter &= $"EMP_TYPE = '{cbeEmployeeType.Text}' "
            End If

            If cbeRateFilter.Text.IsNotNullOrWhiteSpace Then
                If filter.IsNotNullOrWhiteSpace Then filter &= " AND "
                filter &= $"Rate = '{cbeRateFilter.Text}' "
            End If

            If deHireDateAfter.DateTime <> DateTime.MinValue Then
                If filter.IsNotNullOrWhiteSpace Then filter &= " AND "
                filter &= $"HireDate {IIf(cbeHireDateFilter.SelectedIndex = 0, ">", "<")} '{deHireDateAfter.DateTime}' "
            End If
            BindingSource1.Filter = filter
        Catch ex As Exception
            DisplayErrorMessage("Error filtering results", ex)
        End Try
    End Sub

    Private Sub hllcCheckAll_Click(sender As Object, e As EventArgs) Handles hllcCheckAll.Click
        For index = 0 To GridView1.RowCount
            GridView1.SetRowCellValue(index, "IsChecked", True)
        Next
    End Sub

    Private Sub hllcUncheckAll_Click(sender As Object, e As EventArgs) Handles hllcUncheckVisibleOnly.Click
        For index = 0 To GridView1.RowCount
            GridView1.SetRowCellValue(index, "IsChecked", False)
        Next
    End Sub

    Private Sub hllUncheckAll_Click(sender As Object, e As EventArgs) Handles hllUncheckAll.Click
        For Each r As DataRow In EmployeesDataTable.Rows
            r("IsChecked") = False
        Next
    End Sub

    Private Sub cbeEmpStatus_EditValueChanged(sender As Object, e As EventArgs) Handles cbeEmpStatus.EditValueChanged, ceShowBlankRates.CheckedChanged, cbeEmployeeType.EditValueChanged, cbeRateFilter.EditValueChanged, deHireDateAfter.EditValueChanged, cbeHireDateFilter.EditValueChanged
        SetDataTableFilter()
    End Sub

    Private Sub btnApply_Click(sender As Object, e As EventArgs) Handles btnPreviewInGrid.Click
        For index = 0 To GridView1.RowCount
            GridView1.SetRowCellValue(index, "IsChecked", True)
            If rgIncreaseBy.SelectedIndex = 2 Then
                GridView1.SetRowCellValue(index, "NewRate", seAmount.Value)
            Else
                Dim amount As Decimal
                Dim value = GridView1.GetRowCellValue(index, "RateAmount")
                If value IsNot DBNull.Value AndAlso Decimal.TryParse(nz(value, ""), amount) Then
                    If rgIncreaseBy.SelectedIndex = 0 Then
                        Dim newRate = value + ((amount * seAmount.Value) / 100)
                        GridView1.SetRowCellValue(index, "NewRate", newRate)
                    Else
                        Dim newRate = value + seAmount.Value
                        GridView1.SetRowCellValue(index, "NewRate", newRate)
                    End If
                End If
            End If
        Next
    End Sub

    Private Sub riTeNewRate_EditValueChanged(sender As Object, e As EventArgs) Handles riTeNewRate.EditValueChanged
        Dim row As DataRowView = BindingSource1.Current
        Dim editor As TextEdit = sender
        If editor.Text.IsNotNullOrWhiteSpace Then
            row("IsChecked") = True
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnUpdateAll.Click, btnUpdateVisible.Click
        Try
            lcRoot.ShowProgessPanel
            Dim rows As List(Of DataRow) = Nothing
            If sender Is btnUpdateAll Then
                rows = EmployeesDataTable.AsEnumerable().Where(Function(r) r.Field(Of Boolean)("IsChecked") = True).ToList
            Else
                rows = New List(Of DataRow)
                For index = 0 To GridView1.RowCount - 1
                    Dim row As DataRowView = GridView1.GetRow(index)
                    If row.Row.Field(Of Boolean)("IsChecked") = True Then rows.Add(row.Row)
                Next
            End If

            If rows.Count = 0 Then
                DisplayMessageBox("No records selected to update")
                Exit Sub
            End If

            Dim checkedRowsWithoutDate = rows.Where(Function(r) IsDBNull(r("EffectiveDate")))
            If checkedRowsWithoutDate.Count() > 0 Then
                DisplayMessageBox($"{checkedRowsWithoutDate.Count()} Rows are missing Effective Date. Please fix and try again.")
                Exit Sub
            End If

            Dim rowsWithBlankOrZeroAmount = rows.Where(Function(r) (IsDBNull(r("NewRate")) OrElse r.Field(Of Decimal)("NewRate") = 0))
            If rowsWithBlankOrZeroAmount.Count() > 0 Then
                Dim result = XtraMessageBox.Show($"You have selected to update {rowsWithBlankOrZeroAmount.Count()} recorrd(s) with a blank or 0 amount, would you like to save a blank rate?", "Save blank?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
                If result = DialogResult.Cancel Then Exit Sub
                If result = DialogResult.No Then
                    rows = rows.Where(Function(r) (Not IsDBNull(r("NewRate")) AndAlso r.Field(Of Decimal)("NewRate") > 0)).ToList
                End If
            End If

            Using db = New dbEPDataDataContext(GetConnectionString)
                For Each row In rows
                    Dim coNum As Decimal = row("CoNum")
                    Dim empNum As Decimal = row("EMPNUM")
                    Dim Rate As String = row("Rate")
                    Dim amount As Decimal = IIf(IsDBNull(row("NewRate")), 0, row("NewRate"))
                    Dim EffectiveDate As DateTime = row("EffectiveDate")
                    If EffectiveDate > DateTime.Today Then
                        Dim empRow = New employee_rate With {.conum = coNum,
                                                 .empnum = empNum,
                                                 .effective_date = EffectiveDate,
                                                 .rate = Rate,
                                                 .amount = amount,
                                                 .history = "NO"}
                        db.employee_rates.InsertOnSubmit(empRow)
                    Else
                        Dim emp = db.EMPLOYEEs.Single(Function(em) em.CONUM = coNum AndAlso em.EMPNUM = empNum)
                        Select Case Rate
                            Case "Rate 1"
                                emp.RATE_1 = amount
                            Case "Rate 2"
                                emp.RATE_2 = amount
                            Case "Rate 3"
                                emp.RATE_3 = amount
                            Case "Salary"
                                emp.SALARY_AMT = amount
                        End Select
                    End If
                Next
                db.SubmitChanges()
                XtraMessageBox.Show($"{rows.Count} records updated.")
                Close()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub rgIncreaseBy_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgIncreaseBy.SelectedIndexChanged
        Dim format = ""
        If rgIncreaseBy.SelectedIndex = 0 Then
            format = "{0} %"
        Else
            format = "C"
        End If
        seAmount.Properties.DisplayFormat.FormatString = format
        seAmount.Properties.EditFormat.FormatString = format
    End Sub

    Private Sub DateEdit1_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles DateEdit1.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Plus Then
            If DateEdit1.DateTime = DateTime.MinValue Then
                For index = 0 To GridView1.RowCount
                    GridView1.SetRowCellValue(index, "EffectiveDate", DBNull.Value)
                Next
            Else
                For index = 0 To GridView1.RowCount
                    GridView1.SetRowCellValue(index, "EffectiveDate", DateEdit1.DateTime)
                Next
            End If
        End If
    End Sub


End Class