﻿Public Class frmReprintCoverSheet
    Private conum As Decimal?
    Private ordernum As Integer?
    Private _isW2CoverSheet As Boolean

    Sub New()
        InitializeComponent()
    End Sub

    Public Sub New(conum As Decimal, ordernum As Integer)
        InitializeComponent()
        Me.conum = conum
        Me.ordernum = ordernum
    End Sub

    Public Sub New(_isW2CoverSheet As Boolean)
        InitializeComponent()
        Me._isW2CoverSheet = _isW2CoverSheet
        deQtrEndDate.EditValue = GetLastQuarterEndDate()
        Text = "Print W2 Cover Sheet"
        LabelControl2.Text = "Qtr End Date: "
        tePrOrOrderNum.Visible = False
        deQtrEndDate.Visible = True
    End Sub

    Private Sub frmReprintCoverSheet_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim ctxDB = New dbEPDataDataContext(GetConnectionString)
        view_CompanySumarriesBindingSource.DataSource = ctxDB.view_CompanySumarries.ToList()
        If conum.HasValue Then slueConum.EditValue = conum
        If ordernum.HasValue Then tePrOrOrderNum.EditValue = ordernum

        Dim defaultPrinting = New Printing.PrinterSettings
        Dim installedPrinters = Printing.PrinterSettings.InstalledPrinters.Cast(Of String).ToList
        cbePrinter.Properties.Items.AddRange(installedPrinters)

        Dim reportSettings = ctxDB.settings.SingleOrDefault(Function(s) s.username = UserName AndAlso s.path = "\ReportsPrinter" AndAlso s.type = "Execupay.Foundation.UserPrinters")
        If reportSettings IsNot Nothing AndAlso reportSettings.PrinterName.IsNotNullOrWhiteSpace() AndAlso installedPrinters.Contains(reportSettings.PrinterName) Then
            cbePrinter.EditValue = reportSettings.PrinterName
        Else
            cbePrinter.EditValue = defaultPrinting.PrinterName
        End If
    End Sub

    Private Sub btnPrintCoverSheet_Click(sender As Object, e As EventArgs) Handles btnPrintCoverSheet.Click
        Dim reportName = If(_isW2CoverSheet, "W2CoverSheetByConum", "Payroll Cover Sheet")
        Dim report = New ReportProcessor(slueConum.EditValue, reportName, FileType.Pdf) With {
            .showParametersForm = False,
            .showInRecentReports = False,
            .LastPrNum = Convert.ToDecimal(tePrOrOrderNum.EditValue)}
        If _isW2CoverSheet Then
            report.DefaultParamValues = New List(Of KeyValuePair)()
            report.DefaultParamValues.Add(New KeyValuePair("QtrEndDate", deQtrEndDate.DateTime))
        End If
        Dim result = report.ProcessReport()
        Using pdfDocumentProcessor As New DevExpress.Pdf.PdfDocumentProcessor()
            pdfDocumentProcessor.LoadDocument(result.Paths.Single, True)
            Dim pdfPrinterSettings As New DevExpress.Pdf.PdfPrinterSettings()
            pdfPrinterSettings.Settings.PrinterName = cbePrinter.EditValue
            pdfDocumentProcessor.Print(pdfPrinterSettings)
        End Using
        Close()
    End Sub
End Class