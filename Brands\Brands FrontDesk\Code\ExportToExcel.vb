﻿'Imports Microsoft.Office.Interop
Imports System.IO
Imports OfficeOpenXml


'Public Class ExportToExcel

'    Public Shared Sub Export(GridView As DevExpress.XtraGrid.Views.Grid.GridView)
'        Dim ExcelApp As New Excel.Application With {.SheetsInNewWorkbook = 1}
'        Dim ExcelBooks As Excel.Workbooks = ExcelApp.Workbooks
'        Dim ExcelBook As Excel.Workbook = ExcelBooks.Add
'        Dim Sheet As Excel.Worksheet = ExcelBook.ActiveSheet
'        Dim Range As Excel.Range

'        'Title
'        Range = Sheet.Range("A1", "E1")
'        Range.Merge()
'        Range.Value = GridView.Tag
'        Range.Font.Bold = True
'        Range.Font.Size = 14
'        Range.Font.Italic = True

'        'Headers
'        Range = Sheet.Range("A2", Reflection.Missing.Value)
'        Range = Range.Resize(1, GridView.Columns.Count)
'        Range.Value = (From A As DevExpress.XtraGrid.Columns.GridColumn In GridView.Columns Select A.GetCaption).ToArray

'        'Format as string
'        Dim ColLetter As Char = Chr(64)
'        For ColNum = 0 To GridView.Columns.Count - 1
'            ColLetter = Chr(Asc(ColLetter) + 1)
'            If GridView.Columns(ColNum).ColumnType = Type.GetType("System.String") Then
'                Range = Sheet.Columns(String.Format("{0}:{0}", ColLetter))
'                Range.NumberFormat = "@"
'            End If
'        Next

'        Dim ArrayValues(GridView.DataRowCount - 1, GridView.Columns.Count - 1) As Object
'        Dim CellValue As Object
'        For RowNum = 0 To GridView.DataRowCount - 1
'            For ColNum = 0 To GridView.Columns.Count - 1
'                CellValue = GridView.GetRowCellValue(RowNum, GridView.Columns(ColNum))
'                If TypeOf CellValue Is String Then CellValue = CellValue.ToString.Replace(vbCrLf, " ").Trim
'                ArrayValues(RowNum, ColNum) = CellValue
'            Next
'        Next
'        Range.Font.Bold = True
'        Range.Font.Underline = 2

'        Range = Sheet.Range("A3", Reflection.Missing.Value)
'        Range = Range.Resize(GridView.DataRowCount, GridView.Columns.Count)
'        Range.Value = ArrayValues
'        Range.EntireColumn.AutoFit()

'        ExcelApp.UserControl = True
'        ExcelApp.Visible = True

'        NAR(Range)
'        NAR(Sheet)
'        NAR(ExcelBook)
'        NAR(ExcelBooks)
'        'ExcelApp.Quit()
'        NAR(ExcelApp)
'    End Sub

'    Public Shared Sub Export(Grid As DataGridView)
'        Dim ExcelApp As New Excel.Application With {.SheetsInNewWorkbook = 1}
'        Dim ExcelBooks As Excel.Workbooks = ExcelApp.Workbooks
'        Dim ExcelBook As Excel.Workbook = ExcelBooks.Add
'        Dim Sheet As Excel.Worksheet = ExcelBook.ActiveSheet
'        Dim Range As Excel.Range

'        'Title
'        Range = Sheet.Range("A1", "E1")
'        Range.Merge()
'        Range.Value = Grid.Tag
'        Range.Font.Bold = True
'        Range.Font.Size = 14
'        Range.Font.Italic = True

'        'Headers
'        Range = Sheet.Range("A2", Reflection.Missing.Value)
'        Range = Range.Resize(1, Grid.ColumnCount)
'        Range.Value = (From A As DataGridViewColumn In Grid.Columns Select A.HeaderText).ToArray

'        'Format as string
'        Dim ColLetter As Char = Chr(64)
'        For ColNum = 0 To Grid.ColumnCount - 1
'            ColLetter = Chr(Asc(ColLetter) + 1)
'            If Grid.Columns(ColNum).ValueType = Type.GetType("System.String") Then
'                Range = Sheet.Columns(String.Format("{0}:{0}", ColLetter))
'                Range.NumberFormat = "@"
'            End If
'        Next

'        Dim ArrayValues(Grid.RowCount - 1, Grid.ColumnCount - 1) As Object
'        Dim CellValue As Object
'        For RowNum = 0 To Grid.RowCount - 1
'            For ColNum = 0 To Grid.ColumnCount - 1
'                CellValue = Grid.Item(ColNum, RowNum).Value
'                If TypeOf CellValue Is String Then CellValue = CellValue.ToString.Replace(vbCrLf, " ").Trim
'                ArrayValues(RowNum, ColNum) = CellValue
'            Next
'        Next
'        Range.Font.Bold = True
'        Range.Font.Underline = 2

'        Range = Sheet.Range("A3", Reflection.Missing.Value)
'        Range = Range.Resize(Grid.RowCount, Grid.ColumnCount)
'        Range.Value = ArrayValues
'        Range.EntireColumn.AutoFit()

'        ExcelApp.UserControl = True
'        ExcelApp.Visible = True

'        NAR(Range)
'        NAR(Sheet)
'        NAR(ExcelBook)
'        NAR(ExcelBooks)
'        'ExcelApp.Quit()
'        NAR(ExcelApp)
'    End Sub

'    Private Shared Sub NAR(ByVal o As Object)
'        Try
'            System.Runtime.InteropServices.Marshal.ReleaseComObject(o)
'        Catch
'        Finally
'            o = Nothing
'        End Try
'    End Sub
'End Class

Public Class ExportToExcel

    Public Shared Function GetDownloadsFolderPath() As String
        Dim userProfilePath As String = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile)
        Dim downloadsFolderPath As String = Path.Combine(userProfilePath, "Downloads")
        Return downloadsFolderPath
    End Function

    Public Shared Sub Export(GridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Dim filePath As String = GetDownloadsFolderPath() & "\ExportedData.xlsx"
        ' If you use EPPlus in a noncommercial context
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial

        Using package As New ExcelPackage()
            Dim sheet As ExcelWorksheet = package.Workbook.Worksheets.Add("Sheet1")

            ' Title
            sheet.Cells("A1:E1").Merge = True
            sheet.Cells("A1").Value = GridView.Tag
            sheet.Cells("A1").Style.Font.Bold = True
            sheet.Cells("A1").Style.Font.Size = 14
            sheet.Cells("A1").Style.Font.Italic = True

            ' Headers
            Dim headers As Object() = GridView.Columns.Cast(Of DevExpress.XtraGrid.Columns.GridColumn)().[Select](Function(col) col.GetCaption()).ToArray()
            sheet.Cells("A2").LoadFromArrays(New Object()() {headers})

            ' Format as string
            Dim colLetter As Char = Chr(64)
            For colNum As Integer = 0 To GridView.Columns.Count - 1
                If GridView.Columns(colNum).ColumnType Is GetType(String) Then
                    sheet.Column(colNum + 1).Style.Numberformat.Format = "@"
                End If
            Next

            '' Data
            'Dim arrayValues(GridView.DataRowCount - 1, GridView.Columns.Count - 1) As Object
            'For rowNum As Integer = 0 To GridView.DataRowCount - 1
            '    For colNum As Integer = 0 To GridView.Columns.Count - 1
            '        Dim cellValue As Object = GridView.GetRowCellValue(rowNum, GridView.Columns(colNum))
            '        If TypeOf cellValue Is String Then
            '            cellValue = cellValue.ToString().Replace(vbCrLf, " ").Trim()
            '        End If
            '        arrayValues(rowNum, colNum) = cellValue
            '    Next
            'Next

            'sheet.Cells("A3").LoadFromArrays(New Object()() {arrayValues})

            ' Data
            Dim arrayValues(GridView.DataRowCount - 1, GridView.Columns.Count - 1) As Object
            ' ... (populate arrayValues as before)

            ' Create an IEnumerable of one-dimensional arrays
            Dim dataForEPPlus As New List(Of Object())
            For rowNum As Integer = 0 To GridView.DataRowCount - 1
                Dim rowArray(GridView.Columns.Count - 1) As Object
                For colNum As Integer = 0 To GridView.Columns.Count - 1
                    Dim cellValue As Object = GridView.GetRowCellValue(rowNum, GridView.Columns(colNum))
                    If TypeOf cellValue Is String Then
                        cellValue = cellValue.ToString().Replace(vbCrLf, " ").Trim()
                    End If
                    rowArray(colNum) = cellValue
                Next
                dataForEPPlus.Add(rowArray)
            Next

            ' Load the data using LoadFromArrays
            sheet.Cells("A3").LoadFromArrays(dataForEPPlus)
            sheet.Cells.AutoFitColumns()

            ' Save the Excel file
            package.SaveAs(New FileInfo(filePath))
        End Using
    End Sub

    Public Shared Sub Export(Grid As System.Windows.Forms.DataGridView)
        Dim filePath As String = GetDownloadsFolderPath() & "\ExportedData.xlsx"
        ' If you use EPPlus in a noncommercial context
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial

        Using package As New ExcelPackage()
            Dim sheet As ExcelWorksheet = package.Workbook.Worksheets.Add("Sheet1")

            ' Title
            sheet.Cells("A1:E1").Merge = True
            sheet.Cells("A1").Value = Grid.Tag
            sheet.Cells("A1").Style.Font.Bold = True
            sheet.Cells("A1").Style.Font.Size = 14
            sheet.Cells("A1").Style.Font.Italic = True

            ' Headers
            Dim headers As Object() = Grid.Columns.Cast(Of System.Windows.Forms.DataGridViewColumn)().[Select](Function(col) col.HeaderText).ToArray()
            sheet.Cells("A2").LoadFromArrays(New Object()() {headers})

            ' Format as string
            Dim colLetter As Char = Chr(64)
            For colNum As Integer = 0 To Grid.ColumnCount - 1
                If Grid.Columns(colNum).ValueType Is GetType(String) Then
                    sheet.Column(colNum + 1).Style.Numberformat.Format = "@"
                End If
            Next

            '' Data
            'Dim arrayValues(Grid.RowCount - 1, Grid.ColumnCount - 1) As Object
            'For rowNum As Integer = 0 To Grid.RowCount - 1
            '    For colNum As Integer = 0 To Grid.ColumnCount - 1
            '        Dim cellValue As Object = Grid.Rows(rowNum).Cells(colNum).Value
            '        If TypeOf cellValue Is String Then
            '            cellValue = cellValue.ToString().Replace(vbCrLf, " ").Trim()
            '        End If
            '        arrayValues(rowNum, colNum) = cellValue
            '    Next
            'Next

            'sheet.Cells("A3").LoadFromArrays(New Object()() {arrayValues})

            ' Data
            Dim dataForEPPlus As New List(Of Object())()
            For rowNum As Integer = 0 To Grid.RowCount - 1
                Dim rowArray(Grid.ColumnCount - 1) As Object
                For colNum As Integer = 0 To Grid.ColumnCount - 1
                    Dim cellValue As Object = Grid.Rows(rowNum).Cells(colNum).Value
                    If TypeOf cellValue Is String Then
                        cellValue = cellValue.ToString().Replace(vbCrLf, " ").Trim()
                    End If
                    rowArray(colNum) = cellValue
                Next
                dataForEPPlus.Add(rowArray)
            Next

            ' Load the data using LoadFromArrays
            sheet.Cells("A3").LoadFromArrays(dataForEPPlus)
            sheet.Cells.AutoFitColumns()

            ' Save the Excel file
            package.SaveAs(New FileInfo(filePath))
        End Using
    End Sub

    ' The NAR function is no longer needed when using EPPlus
    ' because EPPlus is a managed library and doesn't require
    ' explicit COM object release.
End Class