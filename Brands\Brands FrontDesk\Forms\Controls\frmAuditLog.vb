﻿Imports System.ComponentModel
Imports Brands_FrontDesk

Public Class frmAuditLog
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _View_FaxAndEmail As view_FaxAndEmail
    Private ReadOnly auditLogLegacy As String

    Sub New(view_FaxAndEmail As view_FaxAndEmail, auditLogLegacy As String)
        InitializeComponent()
        _View_FaxAndEmail = view_FaxAndEmail
        Me.auditLogLegacy = auditLogLegacy
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Private Sub frmAuditLog_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                auditLogsBindingSource.DataSource = db.AuditLogs.Where(Function(a) (a.TableName = _View_FaxAndEmail.Source AndAlso a.RowId = _View_FaxAndEmail.ID) OrElse (a.TableName = "Ticket" AndAlso a.RowId = _View_FaxAndEmail.TicketNum)).ToList()
            End Using
            If auditLogLegacy.IsNotNullOrWhiteSpace Then
                meAuditLog.Text = auditLogLegacy
            Else
                TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
            End If
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error loading audit logs", ex)
        End Try
    End Sub
End Class