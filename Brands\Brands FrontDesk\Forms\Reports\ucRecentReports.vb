﻿Imports System.ComponentModel
Imports System.IO
Public Class ucRecentReports

    Private frmLogger As Serilog.ILogger
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Comp As COMPANY

    Private Sub ucRecentReports_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        frmLogger = Logger.ForContext(Me.GetType())
    End Sub

    Public Sub LoadRecentReports()
        Try
            If Not Directory.Exists(ReportsExtensions.CrystalReportsFolder) Then Exit Sub
            Dim files = (New DirectoryInfo(ReportsExtensions.CrystalReportsFolder)).GetFiles()
            gcRecentReports.DataSource = Nothing
            gcRecentReports.DataSource = files.Select(Function(f) New RecentFiles With {
                                                          .FileName = f.Name,
                                                          .FilePath = f.FullName,
                                                          .Extension = f.Extension,
                                                          .CreateDate = f.LastWriteTime,
                                                          .Checked = False}).OrderByDescending(Function(o) o.CreateDate).ToList()
            gvRecentReports.BestFitColumns()
            gvRecentReports.RefreshData()
        Catch ex As Exception
            frmLogger.Error(ex, "Error loading recent reports")
            DisplayErrorMessage("Error loading recent reports", ex)
        End Try
    End Sub

    Private Sub btnFax_Click(sender As Object, e As EventArgs) Handles btnFax.Click
        Try
            Dim recentFile = CType(gcRecentReports.DataSource, List(Of RecentFiles))
            Dim checkedFiles = recentFile.Where(Function(f) f.Checked)
            Dim newFileName = System.IO.Path.Combine(ReportsExtensions.GetCrystalReportsFolder(), ReportsExtensions.GetFileName(Comp, "Faxed", ".pdf"))
            PdfUtilities.CombinePdfsNew(newFileName, checkedFiles.Select(Function(f) f.FilePath).ToArray())
            Dim frm = New frmPhoneFax("", newFileName, Comp, "Reports Requested")
            frm.Show(Me)
        Catch ex As Exception
            DisplayErrorMessage("Error faxing report", ex)
        End Try
    End Sub



    Private Sub gvRecentReports_DoubleClick(sender As Object, e As EventArgs) Handles gvRecentReports.DoubleClick
        Dim file = CType(gvRecentReports.GetFocusedRow, RecentFiles)
        If file IsNot Nothing Then
            Try
                'System.Diagnostics.Process.Start(file.FilePath)
                Dim psi As New System.Diagnostics.ProcessStartInfo()
                psi.FileName = file.FilePath
                psi.UseShellExecute = True
                System.Diagnostics.Process.Start(psi)
            Catch ex As Exception
                DisplayErrorMessage("Error opening file: {0}, {1}Error is: {2}".FormatWith(file.FileName, vbCrLf, ex.Message), ex)
            End Try
        End If
    End Sub

    Private Sub riCeChecked_CheckedChanged(sender As Object, e As EventArgs) Handles riCeChecked.CheckedChanged, riCeChecked.CheckedChanged
        gvRecentReports.PostEditor()
        SetSendRecentFilesVisibility()
    End Sub

    Private Sub SetSendRecentFilesVisibility()
        Dim recentFile = CType(gcRecentReports.DataSource, List(Of RecentFiles))
        Dim checkedFiles = recentFile.Where(Function(f) f.Checked)
        lcgRecentFilesSendBtn.Visibility = If(checkedFiles.Any(), DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
    End Sub

    Private Sub gvRecentReports_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvRecentReports.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Rename", Sub()
                                                                                RenameFile(gvRecentReports.GetRow(e.HitInfo.RowHandle))
                                                                            End Sub))
        End If

    End Sub

    Private Sub RenameFile(file As RecentFiles)
        Dim newFileName As String = String.Empty
        Try
            Dim s = InputBox("Rename File:{0}".FormatWith(file.FileName), "Rename File", file.ReportName)
            If s.IsNotNullOrWhiteSpace Then
                Dim dir = IO.Directory.GetParent(file.FilePath)
                newFileName = file.FileName.Replace(file.ReportName, s)
                IO.File.Move(file.FilePath, IO.Path.Combine(dir.FullName, newFileName))
                LoadRecentReports()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error renaming file from: {0} to: {1}".FormatWith(file.ReportName, newFileName), ex)
        End Try
    End Sub

    Public Class RecentFiles
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property FileName As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CreateDate As DateTime
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Checked As Boolean
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property FilePath As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Extension As String

        Public ReadOnly Property ReportName As String
            Get
                Dim split = FileName.Split("_")
                If split.Count() >= 3 Then
                    Dim dt As DateTime
                    If split.Count() >= 5 AndAlso DateTime.TryParse(split(4), dt) Then
                        Return String.Format("{0}_{1}", split(2), split(3))
                    Else
                        Return split(2)
                    End If
                Else
                    Return ""
                End If
            End Get
        End Property
        Public ReadOnly Property CoNum As Decimal?
            Get
                Dim split = FileName.Split("_")(0)
                If IsNumeric(split) Then
                    Return Convert.ToDecimal(split)
                Else
                    Return Nothing
                End If
            End Get
        End Property

        Public ReadOnly Property CoNumDef As Decimal
            Get
                Return CoNum.GetValueOrDefault
            End Get
        End Property
    End Class

    Private Sub btnSendAsZip_Click(sender As Object, e As EventArgs) Handles btnSendAsZip.Click
        Try
            Dim recentFile = CType(gcRecentReports.DataSource, List(Of RecentFiles))
            Dim checkedFiles As IEnumerable(Of RecentFiles) = recentFile.Where(Function(f) f.Checked).ToList
            SendMultipleFiles(checkedFiles, Comp, True)
        Catch ex As Exception
            DisplayErrorMessage("Error sending Recent Report", ex)
        End Try
    End Sub


    Private Sub btnRecentFilesSendBtn_Click(sender As Object, e As EventArgs) Handles btnRecentFilesSendBtn.Click
        Try
            Dim recentFile = CType(gcRecentReports.DataSource, List(Of RecentFiles))
            Dim checkedFiles As IEnumerable(Of RecentFiles) = recentFile.Where(Function(f) f.Checked).ToList
            SendMultipleFiles(checkedFiles, Comp, False)
        Catch ex As Exception
            DisplayErrorMessage("Error sending Recent Report", ex)
        End Try
    End Sub

    Private Async Sub btnSendViaZendesk_Click(sender As Object, e As EventArgs) Handles btnSendViaZendesk.Click
        Try
            Await CanCreateTicketAsync()
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim recentFile = CType(gcRecentReports.DataSource, List(Of RecentFiles))
                Dim checkedFiles As IEnumerable(Of RecentFiles) = recentFile.Where(Function(f) f.Checked).ToList
                Dim report As ReportEmailTeplate = Nothing
                If checkedFiles.Count = 1 Then
                    Dim reportName = checkedFiles.Single.ReportName
                    Dim rpt = db.ReportEmailTeplates.SingleOrDefault(Function(r) r.Name = reportName)
                    If rpt IsNot Nothing Then report = rpt
                End If
                Await modZendeskIntegrationClient.CreateTicketAsync("", Comp, report, modReports.EmailSubject, PasswordProtectFiles(checkedFiles, db))
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error creating zendesk ticket", ex)
        End Try
    End Sub

    Public Sub SendMultipleFiles(files As List(Of ucRecentReports.RecentFiles), Comp As COMPANY, SendAsZipFile As Boolean)
        Dim emailRecipience As List(Of EmployeeEmail) = DisplayEmailAddressForm(Comp, True, True)
        If emailRecipience Is Nothing Then Exit Sub
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim securedFiles = New List(Of String)

        If Not SendAsZipFile Then
            'if any contacts is web post, will upload all non pdf reports to the web and send each contact a email with all links to the reports. all other reports they w
            SendWebPost(files, Comp, emailRecipience, db)

            'if only web post contacts was selected we can exit sub 
            If emailRecipience.Count > 0 AndAlso Not emailRecipience.Any(Function(e) Not e.IsWebPost) Then Exit Sub
            securedFiles.AddRange(PasswordProtectFiles(files, db))
        Else
            Dim savePath = System.IO.Path.Combine(modReports.GetSecureCrystalReportsFolder(), "Reports_{0}.Zip".FormatWith(modReports.FormatedDate()))
            securedFiles.Add(modReports.ZipFiles(files.Select(Function(f) f.FilePath).ToList, GetComPassword(Comp.CONUM), savePath))
        End If


        Dim properties = EmailHelpers.ComposeEmailWithTicket(Comp.CONUM)
        properties.ToEmail.AddRange(emailRecipience.Where(Function(e) Not e.IsWebPost).Select(Function(e) e.Email).ToList())
        properties.Subject = modReports.EmailSubject
        properties.Body = modReports.EmailBody.FormatWith(Greeting(), "Valued Client")
        properties.Attachments.AddRange(securedFiles)
        Dim frm = New frmComposeEmail(properties)
        frm.ShowDialog()
    End Sub

    Private Function PasswordProtectFiles(files As List(Of RecentFiles), db As dbEPDataDataContext) As List(Of String)
        Dim securedFiles As List(Of String) = New List(Of String)()
        securedFiles.AddRange(files.Where(Function(f) Not f.CoNum.HasValue OrElse Not PasswordProtect(f.ReportName)).Select(Function(f) f.FilePath))

        For Each g In files.Where(Function(f) f.CoNum.HasValue AndAlso PasswordProtect(f.ReportName)).GroupBy(Function(f) f.CoNum)
            Dim coOptions = db.CoOptions_Payrolls.SingleOrDefault(Function(c) c.CoNum = g.Key)
            If g.All(Function(f) f.Extension.ToLower = ".pdf") AndAlso (coOptions Is Nothing OrElse Not coOptions.SendReportsAsZip.HasValue OrElse Not coOptions.SendReportsAsZip.Value) Then
                securedFiles.AddRange(g.Select(Function(f) ReportsExtensions.SetPdfPassword(f.FilePath, GetComPassword(f.CoNum.Value))))
            Else
                Dim savePath = Path.ChangeExtension(System.IO.Path.Combine(modReports.GetSecureCrystalReportsFolder(), "{0}_Reports_{1}".FormatWith(g.Key, modReports.FormatedDate())), ".zip")
                securedFiles.Add(modReports.ZipFiles(g.Select(Function(f) f.FilePath).ToList, GetComPassword(g.Key), savePath))
            End If
        Next
        Return securedFiles
    End Function

    Private ReportEmailTemplateList As List(Of ReportEmailTeplate)
    Public Function GetReportEmailTemplateList() As List(Of ReportEmailTeplate)
        If ReportEmailTemplateList Is Nothing Then
            Dim db = New dbEPDataDataContext(GetConnectionString)
            ReportEmailTemplateList = db.ReportEmailTeplates.ToList
        End If
        Return ReportEmailTemplateList
    End Function

    Public Function PasswordProtect(name As String) As Boolean
        If name.IsNullOrWhiteSpace Then
            Return False
        Else
            Dim report = GetReportEmailTemplateList.SingleOrDefault(Function(f) f.Name = name)
            If report Is Nothing Then
                Return True
            Else
                Return report.PasswordProtect
            End If
        End If
    End Function

    Private Sub SendWebPost(files As List(Of RecentFiles), Comp As COMPANY, emailRecipience As List(Of EmployeeEmail), db As dbEPDataDataContext)
        Dim webPostFilesToAttach As List(Of ucRecentReports.RecentFiles) = New List(Of ucRecentReports.RecentFiles)
        For Each emp In emailRecipience.Where(Function(e) e.IsWebPost)
            Dim webPostLinks As String = ""
            For Each rpt In files
                Dim repTemplate = GetReportEmailTemplateList().FirstOrDefault(Function(rr) rr.Name = rpt.ReportName)
                If repTemplate Is Nothing OrElse repTemplate.ReportType = "PDF" Then
                    webPostFilesToAttach.Add(rpt)
                    Continue For
                End If
                If repTemplate.EmailBody.IsNotNullOrWhiteSpace Then
                    Throw New Exception("You tried sending a report that has a email body, but we found an employee that is WebPost please report to Hershy.")
                End If
                Dim r = PostReportToWeb(repTemplate.WebPostReportId, emp.CoNum, Nothing, rpt.FilePath, emp.EmpNum, emp.EmpCoNum)
                webPostLinks &= String.Format("{0}https://ess.brandspayroll.com/Reports/viewReport.aspx?c={1}&e={2}&i={3}", IIf(webPostLinks.IsNotNullOrWhiteSpace(), vbCrLf, ""), emp.CoNum, emp.EmpNum, r.REQUEST_ID)
            Next

            Dim emlProperties = EmailHelpers.ComposeEmailWithTicket(emp.CoNum)
            emlProperties.Subject = EmailSubject
            emlProperties.ToEmail.Add(emp.Email)
            emlProperties.Body = EmailBodyWeb.FormatWith(Greeting(), Comp.PR_CONTACT, webPostLinks)
            Dim frm1 = New frmComposeEmail(emlProperties)
            frm1.ShowDialog()
        Next

        'here will send one email with reports attached for reports that was not uploaded to the web. 
        If webPostFilesToAttach.Any() Then
            Dim webPostSecuredFiles = New List(Of String)
            webPostSecuredFiles.AddRange(webPostFilesToAttach.Where(Function(f) Not f.CoNum.HasValue OrElse Not PasswordProtect(f.ReportName)).Select(Function(f) f.FilePath))

            For Each g In webPostFilesToAttach.Where(Function(f) f.CoNum.HasValue AndAlso PasswordProtect(f.ReportName)).GroupBy(Function(f) f.CoNum)
                Dim coOptions = db.CoOptions_Payrolls.SingleOrDefault(Function(c) c.CoNum = g.Key)
                If g.All(Function(f) f.Extension.ToLower = ".pdf") AndAlso (coOptions Is Nothing OrElse Not coOptions.SendReportsAsZip.HasValue OrElse Not coOptions.SendReportsAsZip.Value) Then
                    webPostSecuredFiles.AddRange(g.Select(Function(f) ReportsExtensions.SetPdfPassword(f.FilePath, GetComPassword(f.CoNum.Value))))
                Else
                    Dim savePath = Path.ChangeExtension(System.IO.Path.Combine(modReports.GetSecureCrystalReportsFolder(), "{0}_Reports_{1}".FormatWith(g.Key, modReports.FormatedDate())), ".zip")
                    webPostSecuredFiles.Add(modReports.ZipFiles(g.Select(Function(f) f.FilePath).ToList, GetComPassword(g.Key), savePath))
                    'send all in one zip file
                End If
            Next

            Dim emlProperties = EmailHelpers.ComposeEmailWithTicket(Comp.CONUM)
            emlProperties.ToEmail.AddRange(emailRecipience.Where(Function(e) e.IsWebPost).Select(Function(e) e.Email).ToList())
            emlProperties.Subject = modReports.EmailSubject
            emlProperties.Body = modReports.EmailBody.FormatWith(Greeting(), "Valued Client")
            emlProperties.Attachments.AddRange(webPostSecuredFiles)
            Dim frm1 = New frmComposeEmail(emlProperties)
            frm1.ShowDialog()

        End If
    End Sub

    Private Sub btnPasswordProtect_Click(sender As Object, e As EventArgs) Handles btnPasswordProtect.Click
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim recentFile = CType(gcRecentReports.DataSource, List(Of RecentFiles))
                Dim checkedFiles As IEnumerable(Of RecentFiles) = recentFile.Where(Function(f) f.Checked).ToList
                Dim attachments = PasswordProtectFiles(checkedFiles, db)
                If attachments.Any() Then
                    Using frmAttachments = New frmDraggableAttachments(attachments)
                        frmAttachments.ShowDialog()
                    End Using
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error password protecting files", ex)
        End Try
    End Sub
End Class
