﻿Imports System.Data
Imports DevExpress.Pdf
Imports DevExpress.XtraEditors

Public Class frmReportsMassEmailAddOrEdit

    Private Property db As dbEPDataDataContext
    Private Property _ReportMassEmailTemplate As ReportMassEmailTemplate
    Private Property _Parameters As List(Of SqlClient.SqlParameter)
    Private Property _AvavilibleColumns As List(Of String) = New List(Of String)

    Public Sub New()
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        _ReportMassEmailTemplate = New ReportMassEmailTemplate With {.Author = UserName, .AddDateTime = Now}
        ReportMassEmailTemplateBindingSource.DataSource = _ReportMassEmailTemplate
        db.ReportMassEmailTemplates.InsertOnSubmit(_ReportMassEmailTemplate)
    End Sub

    Public Sub New(id As Integer)
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        _ReportMassEmailTemplate = db.ReportMassEmailTemplates.Single(Function(e) e.ID = id)
        ReportMassEmailTemplateBindingSource.DataSource = _ReportMassEmailTemplate
    End Sub

    Private Sub frmReportsNewMassEmail_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If _ReportMassEmailTemplate.AvailibleColumns IsNot Nothing Then _AvavilibleColumns.AddRange(_ReportMassEmailTemplate.AvailibleColumns.Split(",").ToList)
        If _ReportMassEmailTemplate.StaticFiles.IsNotNullOrWhiteSpace() Then lbcStaticFiles.Items.AddRange((_ReportMassEmailTemplate.StaticFiles.Split(",")))
        gcColumns.DataSource = _AvavilibleColumns
        cbeFaxCategories.Properties.Items.AddRange(db.FaxCategories.Select(Function(c) c.Category).ToArray())

        riLueReportEmailTemplate.DataSource = db.ReportEmailTeplates.ToList()
        bsReportFiles.DataSource = _ReportMassEmailTemplate.ReportMassEmailFiles
        bsPdfFiles.DataSource = _ReportMassEmailTemplate.ReportMassEmailPdfs
        Dim cats = db.ReportMassEmailTemplates.Where(Function(t) t.Category IsNot Nothing).Select(Function(t) t.Category).Distinct().ToList
        cbeCategory.Properties.Items.AddRange(cats.ToArray)
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        Try
            lcRoot.ShowProgessPanel()
            'Dim dt As DataTable = Await Task.Run(Function() Query(SqlMemoEdit.Text))
            'If Not dt.Columns.Contains("user_email") Then
            '    XtraMessageBox.Show("the query must return a column 'user_email', (which will be used as To email.")
            '    Exit Sub
            'End If

            If _ReportMassEmailTemplate.ID = 0 Then
                Dim maxSortNumber = db.ReportMassEmailTemplates.Where(Function(s) s.Category = _ReportMassEmailTemplate.Category).Select(Function(s) s.SortOrder).ToList()
                If maxSortNumber.Any Then
                    _ReportMassEmailTemplate.SortOrder = maxSortNumber.Max + 1
                Else
                    _ReportMassEmailTemplate.SortOrder = 0
                End If
            End If

            _ReportMassEmailTemplate.AvailibleColumns = String.Join(",", _AvavilibleColumns)
            Dim list = New List(Of String)
            For Each item In lbcStaticFiles.Items
                list.Add(item)
            Next
            _ReportMassEmailTemplate.StaticFiles = String.Join(",", list)
            db.SubmitChanges()
            Close()
            'Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Sub

    Private Sub btnPreview_Click(sender As Object, e As EventArgs) Handles btnPreview.Click
        PreviewData(True)
    End Sub

    Private Sub lcRefresh_Click(sender As Object, e As EventArgs) Handles lcRefresh.Click
        PreviewData(False)
    End Sub

    Private Async Sub PreviewData(showForm As Boolean)
        Try
            lcRoot.ShowProgessPanel()
            'If _ReportMassEmailTemplate.ParametersJson.IsNotNullOrWhiteSpace Then
            '    Dim params = frmDeclareSqlParameters.ToSqlParams(_ReportMassEmailTemplate.ParametersJson)
            '    If _Parameters Is Nothing Then _Parameters = New List(Of SqlClient.SqlParameter)
            '    For Each p In params
            '        _Parameters.Add(p.ToSqlParameter)
            '    Next
            'End If
            Dim data As Tuple(Of List(Of SqlClient.SqlParameter), DataTable) = Await modReports.PreviewData(SqlMemoEdit.Text, showForm, _Parameters, _ReportMassEmailTemplate.ParametersJson)
            _Parameters = data.Item1

            _AvavilibleColumns.Clear()
            For Each col As System.Data.DataColumn In data.Item2.Columns
                _AvavilibleColumns.Add("{{" & col.ColumnName & "}}")
            Next
            gcColumns.RefreshDataSource()
        Catch ex As Exception
            DisplayErrorMessage("Error preview data", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Sub


    Private Sub gvReportFiles_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvReportFiles.PopupMenuShowing
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub()
                                                                            If XtraMessageBox.Show("Are you sure you would like to delete this ?", "Delete?", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
                                                                                Try
                                                                                    db.ReportMassEmailFiles.DeleteOnSubmit(CType(bsReportFiles.Current, ReportMassEmailFile))
                                                                                Catch ex As Exception
                                                                                    DisplayErrorMessage("Error removing file", ex)
                                                                                End Try
                                                                                bsReportFiles.RemoveCurrent()
                                                                            End If
                                                                        End Sub, My.Resources.delete_16x16))
    End Sub

    Private Sub riBeParametersMap_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riBeParametersMap.ButtonClick
        Dim row = CType(gvReportFiles.GetFocusedRow(), ReportMassEmailFile)
        If row Is Nothing Then Exit Sub
        'Dim crystalReport = Await db.ReportEmailTeplates.Single(Function(r) r.Name = row.CrystalReport).ToCrystalReportAsync()
        Dim existingParameters = modReports.StringToKeyValues(row.ParametersMap)
        'Dim paramaters = crystalReport.GetParameters().Select(Function(r) New KeyValuePair With {.Name = r.Name, .Value = GetExistingParameterValue(existingParameters, r.Name)}).ToList()
        'If paramaters.Count > 0 Then
        '    Dim frm = New frmReportsParameters(paramaters, _AvavilibleColumns, True) With {.UseCustomEditors = False}
        '    If frm.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
        '        row.ParametersMap = frm.GetKeyValuePairs().ToStringMap()
        '        gvReportFiles.RefreshRow(gvReportFiles.FocusedRowHandle)
        '    End If
        'End If

        Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
        Dim Errors As String = Nothing
        Dim paramsInput As ParameterField() = Nothing
        Dim params As ParameterField() = Nothing
        'Dim status = rpcCls.GetCrystalReportParamsFull(db.ReportEmailTeplates.Single(Function(r) r.Name = row.CrystalReport).ReportPath, params, Errors)
        Dim retrn = rpcCls.GetCrystalReportParamsFull(db.ReportEmailTeplates.Single(Function(r) r.Name = row.CrystalReport).ReportPath, params, Errors)
        Dim status = retrn.Status
        params = retrn.Result.Item2
        Dim paramaters = params.Select(Function(r) New KeyValuePair With {.Name = r.Name, .Value = GetExistingParameterValue(existingParameters, r.Name)}).ToList()

        If paramaters.Count > 0 Then
            Dim frm = New frmReportsParameters(paramaters, _AvavilibleColumns, True) With {.UseCustomEditors = False}
            If frm.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
                row.ParametersMap = frm.GetKeyValuePairs().ToStringMap()
                gvReportFiles.RefreshRow(gvReportFiles.FocusedRowHandle)
            End If
        End If
    End Sub

    Private Function GetExistingParameterValue(existingParameters As List(Of KeyValuePair), name As String) As String
        Dim existing = existingParameters.FirstOrDefault(Function(p) p.Name = name)
        If existing IsNot Nothing Then
            Return existing.Value
        Else
            Return String.Empty
        End If
    End Function

    Private Sub btnAddFile_Click(sender As Object, e As EventArgs) Handles btnAddFile.Click
        Dim fd = New OpenFileDialog
        If fd.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
            lbcStaticFiles.Items.Add(fd.FileName)
        End If
    End Sub

    Private Sub btnRemoveFile_Click(sender As Object, e As EventArgs) Handles btnRemoveFile.Click
        If lbcStaticFiles.SelectedItem IsNot Nothing Then
            lbcStaticFiles.Items.Remove(lbcStaticFiles.SelectedItem)
        End If
    End Sub

    Private Sub riBeOpenFileDialog_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riBeOpenFileDialog.ButtonClick
        Dim ofd = New OpenFileDialog
        ofd.Filter = "PDF Files (*.pdf)|*.pdf"
        ofd.FilterIndex = 1
        If ofd.ShowDialog = System.Windows.Forms.DialogResult.OK Then
            Dim row = TryCast(gvPdfFiles.GetFocusedRow(), ReportMassEmailPdf)
            If row Is Nothing Then
                row = bsPdfFiles.AddNew()
            End If
            row.FilePath = ofd.FileName
        End If
    End Sub

    Private Sub riBePdfFieldsMap_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riBePdfFieldsMap.ButtonClick
        Dim row = TryCast(gvPdfFiles.GetFocusedRow(), ReportMassEmailPdf)
        If row Is Nothing Then Exit Sub
        Dim pdf As PdfDocumentProcessor = New PdfDocumentProcessor
        pdf.LoadDocument(row.FilePath)
        Dim fieldList = modReports.GetKeyValueParameters(pdf, row.ParametersMap)
        Dim frm = New frmReportsParameters(fieldList, _AvavilibleColumns, True) With {.UseCustomEditors = False}
        If frm.ShowDialog() = DialogResult.OK Then
            row.ParametersMap = frm.GetKeyValuePairs().Where(Function(kv) kv.Value.IsNotNullOrWhiteSpace()).ToStringMap()
        End If
    End Sub

    Private Sub gvPdfFiles_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvPdfFiles.PopupMenuShowing
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub()
                                                                            If XtraMessageBox.Show("Are you sure you would like to delete this ?", "Delete?", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
                                                                                Try
                                                                                    db.ReportMassEmailPdfs.DeleteOnSubmit(CType(bsPdfFiles.Current, ReportMassEmailPdf))
                                                                                Catch ex As Exception
                                                                                    DisplayErrorMessage("Error deleting mass email template", ex)
                                                                                End Try
                                                                                bsPdfFiles.RemoveCurrent()
                                                                            End If
                                                                        End Sub, My.Resources.delete_16x16))
    End Sub

    Private Sub hlcDeclareParameters_Click(sender As Object, e As EventArgs) Handles hlcDeclareParameters.Click
        Dim frm = New frmDeclareSqlParameters(_ReportMassEmailTemplate.ParametersJson)
        If frm.ShowDialog = DialogResult.OK Then
            _ReportMassEmailTemplate.ParametersJson = frm.GetJson
        End If
    End Sub
End Class