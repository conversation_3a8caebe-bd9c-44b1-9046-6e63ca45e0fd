﻿Imports System.ComponentModel
Imports System.Data
Imports System.IO
'Imports CrystalDecisions.CrystalReports.Engine
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.BandedGrid
Imports DevExpress.XtraGrid.Views.Base
Imports QueueBuilder

Public Class frmBrandsPowerGrid

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PrNum As Decimal
    Private _BatchList As pr_batch_list
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property BatchList As pr_batch_list
        Get
            Return _BatchList
        End Get
        Set(value As pr_batch_list)
            _BatchList = value
        End Set
    End Property
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CallBackForm As Form
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property RunHidden As Boolean = False 'if run from backend ex. win service/web app then hide progress bar

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property GridSrc As String = "Brands"

    Dim TblPayrollData As DataTable
    Dim IsNew As Boolean
    Dim NotesData As List(Of NOTE)
    Dim PayLineNotes As List(Of pr_batch_note)
    Dim RTFNotes As New DevExpress.XtraRichEdit.RichEditControl
    Dim EmployeeList As List(Of EMPLOYEE)
    Dim StateInfo As Dictionary(Of Decimal, Tuple(Of Decimal?, Decimal?)) 'EmpNum, Extra, Fixed
    Dim HasAutos As List(Of Decimal)
    Dim CellWatermarkValues As Dictionary(Of String, Specialized.NameValueCollection)
    Dim HasP99 As Dictionary(Of Decimal, Decimal)
    Dim UDF21 As String
    Public ProcessingStatusForm As frmBrandsPowerGridProcess
    Dim CheckOverrides As List(Of pr_batch_overrides_setup)
    Dim CoPayrollOptions As CoOptions_Payroll
    Dim PayFieldFor1099 As String
    Dim PayFieldForBonus As String
    Dim EmployeeChangeLog As List(Of pr_batch_employee_change)

    Dim AvailCols As List(Of prc_BrandsPowerGridColumnsResult)
    Dim _SGroups As List(Of pr_batch_grid_group)
    Dim _SColumns As List(Of pr_batch_grid_column)

    Dim _PayrollEnt As PAYROLL
    Dim _BonusOptions As pr_batch_note
    Dim _DefaultValues As Dictionary(Of String, Decimal)
    Dim _Autos As List(Of prc_GetAutoPaysForPayrollResult)
    Dim DicDepartments As Dictionary(Of String, String)

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CalanderID As Integer
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property DatesAndOptions As frmBrandsPowerGridList
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsAuto As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoIsProcessed As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoIsSubmitted As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CalInfo As List(Of CalendarInfo)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsReadOnly As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property SecondChecksOnly As Boolean

    Dim HasLayoutChanges As Boolean
    Dim TaxOverridesColumns As String() = {"FicaOnly", "FedOverride", "STOverride", "LOCOverride", "DBOverride", "FLIOverride", "TaxFrequency", "ChkType", "ChkNumber", "OASDIOverride", "MedicareOverride", "WrkStateOverride", "ResStateOverride", "UIStateOverride"}

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property DB As dbEPDataDataContext
    Private Property frmLogger As Serilog.ILogger

    Public Sub New()
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmBrandsPowerGrid)()

        If Me.Visible = False AndAlso System.Environment.UserInteractive() AndAlso Application.ProductName <> "Brands FrontDesk" Then
            Me.Show()
            Me.WindowState = FormWindowState.Minimized
        End If
    End Sub

    Private Sub frmBrandsPowerGrid_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            frmLogger.Debug("Opening Form {Name} for CoNum: {CoNum} PrNum: {PrNum}", Me.Name, CoNum, PrNum)
            Me.Text = $"{GridSrc} Power Grid - " & Me.CoNum
            Me.bbiProcessUserWillSubmit.Caption = "Process, " & UserName & " Will Submit"

            Me.BandedGridView1.OptionsBehavior.ReadOnly = IsReadOnly
            Me.lblReadOnly.Visible = IsReadOnly

            AddHandler riJobsLookup.KeyDown, AddressOf ClearEditor

            bciProcessInBackground.Visibility = BarItemVisibility.Never
            bciProcessInBackground.Enabled = False
            bciProcessInBackground.Checked = False
            Try
                Dim isInUserList = GetUdfValueSplitted("ShowQueueOption_Users").Contains(UserName.ToLower)
                Dim udfName As Object = IIf(isInUserList, "bciProcessInBackground_ConfigValue_Users", "bciProcessInBackground_ConfigValue_All")
                Dim configValue As Integer = GetUdfValue(udfName).Trim()
                frmLogger.Debug("bciProcessInBackground: isInUserList: {isInUserList} udfName: {udfName} configValue: {configValue}", isInUserList, udfName, configValue)

                bciProcessInBackground.Enabled = (configValue And 1) > 0
                bciProcessInBackground.Visibility = ((configValue And 2) > 0).ToBarItemVisibility
                bciProcessInBackground.Checked = (configValue And 4) > 0

                frmLogger.Debug("bciProcessInBackground Enabled: {Enabled} Visibility: {Visibility} Checked: {Checked}", bciProcessInBackground.Enabled, bciProcessInBackground.Visibility, bciProcessInBackground.Checked)
            Catch ex As Exception
                frmLogger.Error(ex, "Error in ShowQueueOption")
            End Try
        Catch ex As Exception
            DisplayErrorMessage("Error loading form frmBrandsPowerGrid Co#: {0}".FormatWith(CoNum), ex)
        End Try
    End Sub

    Private Async Sub RibbonControl1_ItemClick(sender As Object, e As ItemClickEventArgs) Handles RibbonControl1.ItemClick
        Try
            BandedGridView1.PostEditor()
            BandedGridView1.UpdateCurrentRow()

            teForFocus.Visible = True
            teForFocus.Focus()
            teForFocus.Select()
            teForFocus.Visible = False

            frmLogger.Information("Clicking On {Item}, Co#: {CoNum}, Pr#: {PrNum}", e.Item.Name, CoNum, PrNum)
            If e.Item Is bbiSaveGrid Then
                Save()
                LoadData()
            ElseIf e.Item Is bbiCancelGrid Then
                CancelGrid()
            ElseIf e.Item Is bbiSaveGridForEditingLaterAndExit Then
                Save()
                Me.Close()
            ElseIf e.Item Is bbiSortGridToDefault Then
                SortGridToDefault()
            ElseIf e.Item Is bciInputIsMinutes OrElse e.Item Is bciAutoOvertime Then
                Dim Chk As BarCheckItem = e.Item
                Chk.ItemAppearance.Normal.BackColor = If(Chk.Checked, Color.Orange, Color.Transparent)
            ElseIf e.Item Is bciShowNetColumn Then
                Me.BandedGridView1.Columns("Net").Visible = bciShowNetColumn.Checked
            ElseIf e.Item Is bciShowTaxOverrideColumns Then
                ShowtaxOverridesChanged()
            ElseIf e.Item Is bbiManualChecks Then
                Dim frm As New frmManualChecks With {.CoNum = Me.CoNum, .CheckDate = Me._PayrollEnt.CHECK_DATE}
                Dim results = frm.ShowDialog
                frm.Dispose()
            ElseIf e.Item Is bbiExportExcel Then
                ExportExcel()
            ElseIf e.Item Is bbiImportExcel Then
                ImportExcel()
            ElseIf e.Item Is bbiCompanyOptions Then
                Dim CoFrm As New frmCoOptionsPayroll With {.CoNum = Me.CoNum}
                If CoFrm.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
                    LoadCoOptions(Nothing)
                End If
                CoFrm.Dispose()
            ElseIf e.Item Is bbiMarkGridComplete Then
                MarkGridComplete()
            ElseIf e.Item Is bbiProcessAndSubmit Then
                TryShowWaitForm(True)
                Await ProcessAndSubmit()
                TryShowWaitForm(False)
            ElseIf e.Item Is bbiProcessUserWillSubmit Then
                TryShowWaitForm(True)
                Await ProcessAndTake()
                TryShowWaitForm(False)
            ElseIf e.Item Is bbiProcessAndSendToAudit Then
                TryShowWaitForm(True)
                Await ProcessAndSendToAudit()
                TryShowWaitForm(False)
            ElseIf e.Item Is bbiPrintNotes Then
                PrintComments()
            ElseIf e.Item Is bbiOpenCompany Then
                MainForm.OpenCompForm(CoNum)
            ElseIf e.Item Is bbiRecover Then
                RecoverPayroll()
            End If
        Catch ex As Exception
            DisplayErrorMessage($"Error. User clicked on button {e.Item.Caption}{vbCrLf}{ex.Message}", ex)
        Finally
            If Me.ProcessingStatusForm Is Nothing Then
                rpgSubmitGrid.Enabled = True
                Me.LayoutControl.Enabled = True
            End If
        End Try
    End Sub

    Sub LoadLayout(ByVal DB As dbEPDataDataContext)
        'Load Columns
        If GridSrc = "Brands" Then
            AvailCols = DB.prc_BrandsPowerGridColumns(Me.CoNum).ToList

            If AvailCols.Count = 0 Then
                'Create template
                Dim Results = DB.prc_InsBrandsPowerGridTemplate(Me.CoNum)
                AvailCols = DB.prc_BrandsPowerGridColumns(Me.CoNum).ToList
            End If
        ElseIf GridSrc = "PPX" Then
            AvailCols = DB.ExecuteQuery(Of prc_BrandsPowerGridColumnsResult)($"EXEC custom.prc_PPXPowerGridColumns {CoNum}").ToList()

            If AvailCols.Count = 0 Then
                'Create template
                DB.ExecuteCommand($"exec custom.prc_InsPowerImportPayrollGridTemplate {CoNum}")
                AvailCols = DB.ExecuteQuery(Of prc_BrandsPowerGridColumnsResult)($"EXEC custom.prc_PPXPowerGridColumns {CoNum}").ToList()
            End If

        End If

        If _PayrollEnt.PR_DESCR = "Bonus Run" Then
            Dim HideCols = (From A In AvailCols
                            Where {"P", "D", "M"}.Contains(A.type) AndAlso A.sql_column <> PayFieldForBonus).ToList
            For Each col In HideCols
                col.Show = False
                col.hide = "YES"
            Next
        End If
        Me.PrcBrandsPowerGridColumnsResultBindingSource.DataSource = AvailCols

        Dim Schema = (From A In DB.pr_batch_grid_schemas Where A.schema_id = Me.AvailCols(0).schema_id).Single
        _SGroups = Schema.pr_batch_grid_groups.ToList
        _SColumns = Schema.pr_batch_grid_columns.ToList

        SetupGrid()
        If _PayrollEnt.PR_DESCR = "Bonus Run" Then
            MakeColVisible(PayFieldForBonus, False)
        End If

        'check if payroll has a fax id
        Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = Me.PrNum AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
        ceMarkFaxDone.Visibility = (PayRec IsNot Nothing AndAlso (PayRec.FaxId.HasValue OrElse PayRec.EmailNum.HasValue)).ToBarItemVisibility
        '(PayRec.FaxId IsNot Nothing Or PayRec.EmailId IsNot Nothing) AndAlso (PayRec.FaxId <> 0 Or PayRec.EmailId <> 0)
    End Sub

    Sub LoadData(Optional IsImport As Boolean = False)
        Try
            DB = New dbEPDataDataContext(GetConnectionString)
            CellWatermarkValues = New Dictionary(Of String, Specialized.NameValueCollection)
            _DefaultValues = New Dictionary(Of String, Decimal)

            LoadCoOptions(DB)

            _PayrollEnt = (From A In DB.PAYROLLs Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum).Single
            If _PayrollEnt.PR_DESCR = "Bonus Run" Then
                _BonusOptions = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = _PayrollEnt.PRNUM AndAlso A.EmployeeID = -996).SingleOrDefault
            End If

            IsNew = BatchList.id.Equals(Guid.Empty)

            'Load Notes 
            NotesData = (From A In DB.NOTEs Where A.conum = Me.CoNum _
                             AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) _
                             AndAlso A.category = "Employee").ToList

            PayLineNotes = (From A In DB.pr_batch_notes Where A.ListID = BatchList.id AndAlso A.EmployeeID IsNot Nothing AndAlso A.Conum = Me.CoNum AndAlso A.PrNum = Me.PrNum).ToList

            LoadLayout(DB)

            UDF21 = (From A In DB.COUSERDEFs Where A.conum = Me.CoNum Select A.udf21_data).SingleOrDefault

            Dim CalData = (From A In DB.CALENDARs Where A.conum = _PayrollEnt.CONUM AndAlso A.payroll_num = _PayrollEnt.PRNUM).ToList
            If CalData.Count = 0 AndAlso Me.CalanderID > 0 Then
                CalData = (From A In DB.CALENDARs Where A.conum = _PayrollEnt.CONUM AndAlso A.cal_id = Me.CalanderID).ToList
            End If
            CalInfo = New List(Of CalendarInfo)
            For X = 0 To CalData.Count - 1
                Dim cal = CalData(X)
                Dim NextCal = (From A In DB.CALENDARs
                               Where A.conum = Me.CoNum AndAlso A.period_id = cal.period_id AndAlso A.process_date > cal.process_date
                               Order By A.process_date).FirstOrDefault
                Dim PrevCal = (From A In DB.CALENDARs
                               Where A.conum = Me.CoNum AndAlso A.period_id = cal.period_id AndAlso A.process_date < cal.process_date
                               Order By A.process_date Descending).FirstOrDefault
                Dim IsLastOfMonth = NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Month <> cal.check_date.GetValueOrDefault.Month
                Dim IsLastOfQtr = NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Quarter <> cal.check_date.GetValueOrDefault.Quarter
                Dim IsFirstOfQtr = PrevCal Is Nothing OrElse PrevCal.check_date.GetValueOrDefault.Quarter <> cal.check_date.GetValueOrDefault.Quarter
                CalInfo.Add(New CalendarInfo With {.CalendarSet = cal.period_id,
                                                   .Prd = cal.prd,
                                                   .IsSupp = _PayrollEnt.SUPP_PR = "YES",
                                                   .LastOfMonth = If(IsLastOfMonth, "Last Of Month", ""),
                                                   .Frequency = cal.frequency,
                                                   .EndDate = cal.end_date,
                                                   .IsLastOfQtr = IsLastOfQtr,
                                                   .IsFirstOfQtr = IsFirstOfQtr})
            Next
            Me.CalendarInfoBindingSource.DataSource = CalInfo

            'Load Employees
            LoadEmployees(IsImport)

            Me.lblSpecialMsg.Visible = False

            If Not _PayrollEnt.PR_DESCR = "Bonus Run" Then
                If CoPayrollOptions.InputAsMinutes Then 'UDF21.Contains("B50") Then
                    Me.bciInputIsMinutes.Checked = True
                    Me.chkConvertHours_CheckedChanged(Me.bciInputIsMinutes, New EventArgs)
                End If
                If CoPayrollOptions.AutoOvertime Then 'UDF21.Contains("B54") Then
                    Me.bciAutoOvertime.Checked = True
                    Me.chkConvertHours_CheckedChanged(Me.bciAutoOvertime, New EventArgs)
                End If
                'If p30 employees, make it visible
                If CoPayrollOptions.ParsonageAll OrElse (From A In EmployeeList Where A.USERDEF21 IsNot Nothing AndAlso A.USERDEF21.ToUpper.Contains("P30")).Count > 0 Then
                    Dim p30Cols = (From A In AvailCols Where {"ph30", "po30", "pp30"}.Contains(A.sql_column) AndAlso Not A.Show).ToList
                    If p30Cols.Count > 0 Then
                        p30Cols.ForEach(Sub(p) p.Show = True)
                        SetupGrid()
                    End If
                End If
            Else
                If _BonusOptions IsNot Nothing Then
                    If _BonusOptions.Note.Contains("Amounts Are Net: YES") Then
                        Me.bciShowNetColumn.Checked = True
                        ShowNetCol_CheckedChanged()
                    End If
                    Me.bciShowTaxOverrideColumns.Checked = True
                    ShowtaxOverridesChanged()
                    If _BonusOptions.Note.Contains("Fica Only: YES") Then
                        Me.chkFicaOnly.Checked = True
                        'chkFicaOnly_CheckedChanged(chkFicaOnly, New EventArgs)
                    Else
                        Me.txtDBOverride.EditValue = 0.0
                    End If
                End If
                Me.lblSpecialMsg.Text = "Bonus Run"
                Me.lblSpecialMsg.Visible = True
            End If

            Dim HasManuals = (From A In DB.MAN_CHK_MASTs Where A.CONUM = Me.CoNum AndAlso {"New", "For Review"}.Contains(A.mpwStatus)).Any
            Me.bbiManualChecks.Enabled = HasManuals
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        End Try
    End Sub

    Sub SetupGrid()
        Me.BandedGridView1.BeginUpdate()

        Me.BandedGridView1.Columns.Clear()

        Me.BandedGridView1.Bands.Clear()
        Dim Groups = (From A In AvailCols Order By A.GroupOrder, A.code Group By A.GroupName, A.type, A.code Into Group Select GroupName, type, code, Columns = Group.ToList).ToList
        Dim gIX As Integer = -1
        For Each G In Groups
            Dim IsVisible = (From A In G.Columns Where A.Show.GetValueOrDefault = True).Count > 0
            Dim GName = System.Text.RegularExpressions.Regex.Replace(G.GroupName, "\W", "")
            Dim Band = Me.BandedGridView1.Bands.Add(New GridBand With {.Name = "band" & GName,
                                                                       .Caption = G.GroupName,
                                                                       .Visible = IsVisible,
                                                                       .Tag = G.type & "|" & G.code})
            If Band.Name = "bandEmployeeInfo" Then
                Band.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left
            ElseIf G.type = "P" OrElse G.type = "D" OrElse G.type = "M" Then
                Band.AppearanceHeader.Font = New Font(Band.AppearanceHeader.Font, FontStyle.Italic)
            ElseIf G.type = "D" Then
                Band.AppearanceHeader.Font = New Font(Band.AppearanceHeader.Font, FontStyle.Italic)
            End If
            If IsVisible Then
                gIX += 1
                Me.BandedGridView1.Bands.MoveTo(gIX, Band)
            End If

            For Each Col In G.Columns.OrderBy(Function(p) p.ColumnOrder).ToList
                Dim BCol As New BandedGridColumn With {.Name = "col" & Col.sql_column,
                                                        .Caption = Col.ColumnName,
                                                        .Visible = Col.Show,
                                                        .FieldName = Col.sql_column,
                                                        .OwnerBand = Band}
                BCol.OptionsColumn.ReadOnly = (Col.read_only & "").ToUpper = "YES"
                If G.type = "P" Then
                    Select Case Col.ColumnName
                        Case "Reg Hours"
                            BCol.AppearanceCell.BackColor = Color.FromArgb(253, 233, 217)
                            BCol.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
                        Case "OT Hours"
                            BCol.AppearanceCell.BackColor = Color.FromArgb(219, 229, 241)
                            BCol.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
                        Case "Amount"
                            BCol.AppearanceCell.BackColor = Color.FromArgb(234, 243, 221)
                            BCol.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                            BCol.DisplayFormat.FormatString = "{0:N}"
                    End Select
                End If
                If Col.ColumnName.ToLower.Contains("date") Then BCol.ColumnEdit = riDateEdit
                If G.code = -1 Then
                    BCol.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList
                ElseIf G.code >= 0 Then
                    If {"Reg Hours", "OT Hours", "Amount"}.Contains(Col.ColumnName) Then
                        BCol.Summary.Add(DevExpress.Data.SummaryItemType.Custom)
                    End If
                    If Col.ColumnName.Contains("Amount") Then
                        BCol.ColumnEdit = riPaysAndDedsAmounts
                    End If
                End If
                If {"chknum", "pay"}.Contains(BCol.FieldName) Then
                    BCol.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Custom
                End If
                If BCol.FieldName.ToLower.Contains("date") Then
                    BCol.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                    BCol.DisplayFormat.FormatString = "d"
                End If
                Me.BandedGridView1.Columns.Add(BCol)
            Next
            If G.code = -1 Then
                Dim BCol = New BandedGridColumn With {.Name = "colPayFrequency", .Visible = False, .FieldName = "PAY_FREQ", .Caption = "Frequency", .OwnerBand = Band}
                BCol.OptionsColumn.ReadOnly = True
                BCol = New BandedGridColumn With {.Name = "colRateCode", .Visible = False, .FieldName = "RateCode", .OwnerBand = Band}
                BCol = New BandedGridColumn With {.Name = "colNet", .Visible = False, .FieldName = "Net", .OwnerBand = Band, .VisibleIndex = 100}
                BCol.OptionsColumn.ReadOnly = False

                Dim DeptDivCol = New BandedGridColumn With {.Name = "colDeptDiv", .Visible = False, .FieldName = "DeptDiv", .Caption = "Department", .OwnerBand = Band,
                    .UnboundType = DevExpress.Data.UnboundColumnType.String, .UnboundExpression = "[division] + '-' + [department]"}
                DeptDivCol.ColumnEdit = riDeptDivLookup
                Me.BandedGridView1.Columns.Add(DeptDivCol)
            End If
        Next

        '========================================================================================
        'Tax Overrides Band
        Dim Band1 = Me.BandedGridView1.Bands.Add(New GridBand With {.Name = "bandTaxOverrides",
                                                                    .Caption = "Tax Overrides",
                                                                    .Visible = False})
        Dim BCol1 As BandedGridColumn
        For Each fld In TaxOverridesColumns
            BCol1 = New BandedGridColumn With {.Name = $"col{fld}", .FieldName = fld, .OwnerBand = Band1}
        Next
        Me.BandedGridView1.Columns("FicaOnly").ColumnEdit = riFicaOnlyCheckBox
        Me.BandedGridView1.Columns("TaxFrequency").ColumnEdit = riTaxFrequencyComboBox
        Me.BandedGridView1.Columns("ChkType").ColumnEdit = riCheckTypeComboBox

        '========================================================================================

        Dim HasNotes = True '(From A In NotesData Where A.category = "Employee").Count > 0
        If HasNotes Then
            Dim BCol As New BandedGridColumn With {.Name = "colNotes",
                                                    .Caption = "Notes",
                                                    .Visible = True,
                                                    .Width = 25,
                                                    .FieldName = "HasNotes1",
                                                    .UnboundType = DevExpress.Data.UnboundColumnType.Integer,
                                                    .UnboundExpression = "[HasNotes]",
                                                    .OwnerBand = BandedGridView1.Bands("bandEmployeeInfo")
                                                  }
            BCol.OptionsColumn.ReadOnly = True
            BCol.OptionsColumn.AllowEdit = False
            BCol.ColumnEdit = riNotesImageComboBox
            'BCol.OptionsColumn.AllowSize = False
            Me.BandedGridView1.Columns.Add(BCol)
            'BCol.BestFit()

            BCol = New BandedGridColumn With {.Name = "colImportNotes", .FieldName = "ImportNotes", .OwnerBand = BandedGridView1.Bands("bandEmployeeInfo")}
            BCol.OptionsColumn.ReadOnly = True
            BCol.Visible = PayLineNotes IsNot Nothing AndAlso (From A In PayLineNotes Where A.EnteredBy = "Import").Any
            Me.BandedGridView1.Columns.Add(BCol)
        End If
        If Not _PayrollEnt.PR_DESCR = "Bonus Run" Then
            Dim AutoPays = True '(From A In NotesData Where A.category = "Employee").Count > 0
            If AutoPays Then
                Dim BCol As New BandedGridColumn With {.Name = "colOP",
                                                        .Caption = "OP",
                                                        .Visible = True,
                                                        .Width = 20,
                                                        .FieldName = "AutoPays",
                                                        .UnboundType = DevExpress.Data.UnboundColumnType.String,
                                                        .OwnerBand = BandedGridView1.Bands("bandEmployeeInfo")
                                                      }
                BCol.OptionsColumn.ReadOnly = True
                BCol.OptionsColumn.AllowEdit = False
                'BCol.OptionsColumn.AllowSize = False
                Me.BandedGridView1.Columns.Add(BCol)
                'BCol.BestFit()
            End If
        End If
        If True Then
            Dim BCol As New BandedGridColumn With {.Name = "colEdits",
                                                    .Caption = "Edits",
                                                    .Visible = True,
                                                    .Width = 25,
                                                    .FieldName = "EditLog",
                                                    .UnboundType = DevExpress.Data.UnboundColumnType.String,
                                                    .OwnerBand = BandedGridView1.Bands("bandEmployeeInfo")
                                                  }
            BCol.OptionsColumn.ReadOnly = True
            BCol.OptionsColumn.AllowEdit = False
            Me.BandedGridView1.Columns.Add(BCol)
        End If

        Me.BandedGridView1.Columns("linerate").ColumnEdit = riMasterRate
        Me.BandedGridView1.Columns("pay").ColumnEdit = riPay
        Me.BandedGridView1.Columns("salary").ColumnEdit = riSalaryAmt
        If Me.BandedGridView1.Columns("pr99") IsNot Nothing Then
            Me.BandedGridView1.Columns("pr99").ColumnEdit = riMasterRate
        End If

        'Format conditions
        Me.BandedGridView1.FormatConditions.Clear()

        Dim Cond = New DevExpress.XtraGrid.StyleFormatCondition()
        Cond.Appearance.BackColor = Color.Yellow
        Cond.Appearance.Options.UseBackColor = True
        Cond.Column = Me.BandedGridView1.Columns("PAY_FREQ")
        Cond.Condition = DevExpress.XtraGrid.FormatConditionEnum.NotEqual
        Cond.Tag = "Freq"
        Me.BandedGridView1.FormatConditions.Add(Cond)

        Cond = New DevExpress.XtraGrid.StyleFormatCondition()
        Cond.Appearance.BackColor = Color.Yellow
        Cond.Appearance.Options.UseBackColor = True
        Cond.Column = Me.BandedGridView1.Columns("name")
        Cond.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression
        Cond.Expression = "EndsWith([name], '(1099)')"
        Me.BandedGridView1.FormatConditions.Add(Cond)

        Cond = New DevExpress.XtraGrid.StyleFormatCondition()
        Cond.Appearance.BackColor = Color.Yellow
        Cond.Appearance.Options.UseBackColor = True
        Cond.Column = Me.BandedGridView1.Columns("ImportNotes")
        Cond.Condition = DevExpress.XtraGrid.FormatConditionEnum.NotEqual
        Cond.Value1 = System.DBNull.Value
        Me.BandedGridView1.FormatConditions.Add(Cond)

        For Each col As BandedGridColumn In Me.BandedGridView1.Columns
            If Not col.FieldName = "empnum" Then
                col.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False
            End If
        Next

        Me.BandedGridView1.EndUpdate()

        ShowtaxOverridesChanged()
        ShowNetCol_CheckedChanged()

        Dim colJobs = Me.BandedGridView1.Columns("job")
        If colJobs IsNot Nothing AndAlso colJobs.Visible Then
            colJobs.ColumnEdit = riJobsLookup
            If Me.COJOBBindingSource.Count = 0 Then
                Dim jobList = (From A In DB.CO_JOBS Where A.conum = Me.CoNum AndAlso A.active = "YES").ToList
                jobList.Insert(0, New CO_JOB)
                Me.COJOBBindingSource.DataSource = jobList
            End If
        End If

        Dim colDiv = Me.BandedGridView1.Columns("division")
        If colDiv IsNot Nothing AndAlso colDiv.Visible Then
            colDiv.ColumnEdit = riDivisionLookup
            If Me.DIVISIONBindingSource.Count = 0 Then
                Me.DIVISIONBindingSource.DataSource = (From A In DB.DIVISIONs Where A.CONUM = Me.CoNum).ToList
            End If
        End If

        Dim colDepts = Me.BandedGridView1.Columns("department")
        If colDepts IsNot Nothing AndAlso colDepts.Visible Then
            'colDepts.ColumnEdit = riDepartmentsLookup
            colDepts.AppearanceCell.GetTextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
            If Me.DEPARTMENTBindingSource.Count = 0 Then
                Me.DEPARTMENTBindingSource.DataSource = (From A In DB.DEPARTMENTs Where A.CONUM = Me.CoNum).ToList
                DicDepartments = (From A As DEPARTMENT In Me.DEPARTMENTBindingSource.List).ToDictionary(Function(p) p.DeptKey, Function(p) p.DeptNumDesc)
            End If
        End If
    End Sub

    Sub viewAvailColumns_CustomColumnSort(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CustomColumnSortEventArgs) Handles gridViewAvailColumns.CustomColumnSort
        If e.Column.FieldName = "GroupName" Then
            Dim dr1 As prc_BrandsPowerGridColumnsResult = e.RowObject1
            Dim dr2 As prc_BrandsPowerGridColumnsResult = e.RowObject2
            e.Handled = True
            Dim S1 = dr1.GroupOrder.GetValueOrDefault  'GetGroupOrder(dr1) 
            Dim S2 = dr2.GroupOrder.GetValueOrDefault  'GetGroupOrder(dr2) 
            If S1.Equals(S2) Then
                Dim Types = {"P", "D", "M"}
                e.Result = (Array.IndexOf(Types, dr1.type) + dr1.code).CompareTo(Array.IndexOf(Types, dr2.type) + dr2.code)
            Else
                e.Result = S1.CompareTo(S2)
            End If
        ElseIf e.Column.FieldName = "TypeDesc" Then
            Dim dr1 As prc_BrandsPowerGridColumnsResult = e.RowObject1
            Dim dr2 As prc_BrandsPowerGridColumnsResult = e.RowObject2
            e.Handled = True
            Dim Types = {"P", "D", "M", "F"}
            e.Result = (Array.IndexOf(Types, dr1.type)).CompareTo(Array.IndexOf(Types, dr2.type))
        Else
            Stop
        End If
    End Sub

    Private Sub EditShowCol_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RepositoryItemCheckEdit1.EditValueChanged
        Dim chk As DevExpress.XtraEditors.CheckEdit = sender
        If Not chk.EditValue.Equals(chk.OldEditValue) Then
            Me.gridViewAvailColumns.PostEditor()
            Me.gridViewAvailColumns.UpdateCurrentRow()
            SetupGrid()
            Dim CurrentRow As prc_BrandsPowerGridColumnsResult = Me.gridViewAvailColumns.GetRow(Me.gridViewAvailColumns.FocusedRowHandle)
            Dim Rec = (From A In _SColumns Where A.sql_column = CurrentRow.sql_column).Single
            Rec.hide = (Not CurrentRow.Show.GetValueOrDefault).ToYesNoString
            HasLayoutChanges = True
        End If
    End Sub

    Private Sub BandedGridView1_CustomDrawBandHeader(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.BandedGrid.BandHeaderCustomDrawEventArgs)
        If e.Band.Tag.ToString.StartsWith("P") Then
            e.Band.AppearanceHeader.BackColor = Color.FromArgb(234, 243, 221)
        ElseIf e.Band.Tag.ToString.StartsWith("D") Then
            e.Band.AppearanceHeader.BackColor = Color.FromArgb(242, 221, 220)
        End If
    End Sub

    Sub LoadEmployeeList()
        DB = New dbEPDataDataContext(GetConnectionString)

        'Create Payroll-Data table
        CreatePayrollDataTable(True)

        CellWatermarkValues = New Dictionary(Of String, Specialized.NameValueCollection)

        Dim EmpList = (From A In DB.EMPLOYEEs
                       Where A.CONUM = Me.CoNum AndAlso A.TERM_DATE Is Nothing
                       Order By A.L_NAME, A.F_NAME
                       Select A.EMPNUM, A.L_NAME, A.M_NAME, A.F_NAME, A.RATE_1, A.RATE_2, A.RATE_3, A.SALARY_AMT, A.SSN, A.DIVNUM, A.DEPTNUM,
                       A.job1, A.job2, A.job3, A.job4, A.job5, A.TERM_DATE,
                       A.DD_ACC_TYPE_1, A.DD_ACC_TYPE_2, A.DD_ACC_TYPE_3, A.DD_ACC_TYPE_4,
                       A.PAY_FREQ, A.USERDEF1, A.USERDEF21, A.CONUM, A.AUTO_SAL_HRS, A.default_hours, A.EMP_TYPE, A.UCI_STATE,
                       A.STREET, A.CITY, A.ADDR_STATE, A.ZIP, A.FED_STATUS, A.FED_DEPS, A.FED_WH_EXTRA, A.FED_WH_FIXED
                      ).ToList

        EmployeeList = (From A In EmpList Select New EMPLOYEE With {
                                                     .EMPNUM = A.EMPNUM,
                                                     .L_NAME = A.L_NAME,
                                                     .F_NAME = A.F_NAME,
                                                     .M_NAME = A.M_NAME,
                                                     .RATE_1 = A.RATE_1,
                                                     .RATE_2 = A.RATE_2,
                                                     .RATE_3 = A.RATE_3,
                                                     .SALARY_AMT = A.SALARY_AMT,
                                                     .SSN = A.SSN,
                                                     .DIVNUM = A.DIVNUM,
                                                     .DEPTNUM = A.DEPTNUM,
                                                     .job1 = A.job1,
                                                     .job2 = A.job2,
                                                     .job3 = A.job3,
                                                     .job4 = A.job4,
                                                     .job5 = A.job5,
                                                     .TERM_DATE = A.TERM_DATE,
                                                     .DD_ACC_TYPE_1 = A.DD_ACC_TYPE_1,
                                                     .DD_ACC_TYPE_2 = A.DD_ACC_TYPE_2,
                                                     .DD_ACC_TYPE_3 = A.DD_ACC_TYPE_3,
                                                     .DD_ACC_TYPE_4 = A.DD_ACC_TYPE_4,
                                                     .PAY_FREQ = A.PAY_FREQ,
                                                     .USERDEF1 = A.USERDEF1,
                                                     .USERDEF21 = A.USERDEF21,
                                                     .CONUM = A.CONUM,
                                                     .AUTO_SAL_HRS = A.AUTO_SAL_HRS,
                                                     .default_hours = A.default_hours,
                                                     .EMP_TYPE = A.EMP_TYPE,
                                                     .UCI_STATE = A.UCI_STATE,
                                                     .STREET = A.STREET,
                                                     .CITY = A.CITY,
                                                     .ADDR_STATE = A.ADDR_STATE,
                                                     .ZIP = A.ZIP,
                                                     .FED_STATUS = A.FED_STATUS,
                                                     .FED_DEPS = A.FED_DEPS,
                                                     .FED_WH_EXTRA = A.FED_WH_EXTRA, .FED_WH_FIXED = A.FED_WH_FIXED
                                                    }).ToList


        StateInfo = (From A In DB.STATE_EE_INFOs
                     Where A.CONUM = Me.CoNum AndAlso (A.ST_WH_EXTRA IsNot Nothing OrElse A.ST_WH_FIXED IsNot Nothing)
                     Group By A.EMPNUM
                     Into Extra = Sum(A.ST_WH_EXTRA), Fixed = Sum(A.ST_WH_FIXED)
                    ).ToDictionary(Function(p) p.EMPNUM, Function(p) Tuple.Create(p.Extra, p.Fixed))
    End Sub

    Sub LoadEmployees(Optional IsImport As Boolean = False)
        frmLogger.Information("Entering LoadEmployees with IsImport: {IsImport}", IsImport)

        LoadEmployeeList()

        'check for employees with empty divnum 
        If EmployeeList.Any(Function(em) Not em.DIVNUM.HasValue) Then
            Dim emps = String.Join(",", EmployeeList.Where(Function(em) Not em.DIVNUM.HasValue).Select(Function(em) em.EMPNUM).ToArray())
            DisplayMessageBox("Employee(s) ({0}) are missing Division Number, Please go to Employee setup and make sure Employee is ALL setup correctly.".FormatWith(emps))
        End If

        If EmployeeList.Any(Function(em) Not em.DEPTNUM.HasValue) Then
            Dim emps = String.Join(",", EmployeeList.Where(Function(em) Not em.DEPTNUM.HasValue).Select(Function(em) em.EMPNUM).ToArray())
            DisplayMessageBox("Employee(s) ({0}) are missing Department Number, Please go to Employee setup and make sure Employee is ALL setup correctly.".FormatWith(emps))
        End If

        If Not _PayrollEnt.PR_DESCR = "Bonus Run" Then
            HasAutos = (From A In DB.EMP_OPS Where A.CONUM = CoNum Select A.EMPNUM
                            ).Union(
                            From A In DB.EMP_DEDS Where A.CONUM = CoNum Select A.EMPNUM
                            ).Distinct.ToList

            _Autos = DB.prc_GetAutoPaysForPayroll(Me.CoNum, Me.PrNum, Nothing, Me.CoPayrollOptions.LoadAllUnscheduledPaysDeds.GetValueOrDefault, Me.IsAuto).ToList
            Dim AutoPaysAndDeds = (From A In _Autos Where A.IsUnscheduled = False
                                   Join E In EmployeeList On A.EmpNum Equals E.EMPNUM
                                   Select New EmpAutoPay With {.Amount = A.Amount,
                                                                .ChkCounter = A.CHK_COUNTER.GetValueOrDefault(1),
                                                                .CL_Code = A.Code,
                                                                .CL_DEPT = A.CL_DEPT,
                                                                .DedCalc = A.DedCalc,
                                                                .EmpNum = A.EmpNum,
                                                                .Rate = A.Rate,
                                                                .StartDate = A.StartDate,
                                                                .TTOOverride = A.TTOOverride,
                                                                .Type = A.Type}
                                                            ).ToList
            For Each itm In AutoPaysAndDeds
                Dim rate As Decimal = If(itm.CL_Code = 0 AndAlso itm.Rate.GetValueOrDefault <> 0, itm.Rate, -1)
                If itm.Amount.HasValue Then
                    If Me.CoPayrollOptions.ShowAllColumnsForAutoPaysDeds.GetValueOrDefault Then MakeColVisible(GetColumnName(itm.Type, itm.CL_Code, ColumnName.amount), False)
                    SetCellWatermark(itm.EmpNum, itm.ChkCounter, rate, GetColumnName(itm.Type, itm.CL_Code, ColumnName.amount), "Auto-" & If(itm.TTOOverride.HasValue, itm.FormattedTTOOverride, itm.FormattedAmount))
                End If
                If itm.Rate.HasValue Then
                    If Me.CoPayrollOptions.ShowAllColumnsForAutoPaysDeds.GetValueOrDefault Then MakeColVisible(GetColumnName(itm.Type, itm.CL_Code, ColumnName.pay_rate), False)
                    SetCellWatermark(itm.EmpNum, itm.ChkCounter, rate, GetColumnName(itm.Type, itm.CL_Code, ColumnName.pay_rate), "Auto-" & itm.Rate)
                End If
            Next
            'Dim rateCols = (From A In AutoPaysAndDeds Where A.Rate.HasValue Select A.Type, A.CL_Code).Distinct.ToList
            'For Each rCol In rateCols
            '    MakeColVisible(GetColumnName(rCol.Type, rCol.CL_Code, ColumnName.pay_rate), False)
            'Next
            'If (From A In AutoPaysAndDeds Where A.CL_Code = 9 And A.Type = "D").Count > 0 Then
            '    MakeColVisible("df9", False)
            'End If

            Dim P99Emp = (From A In DB.EMP_OPS
                          Join B In DB.EMPLOYEEs On A.CONUM Equals B.CONUM And A.EMPNUM Equals B.EMPNUM Where B.TERM_DATE Is Nothing AndAlso A.CONUM = CoNum AndAlso A.OPS_NUM = 99 AndAlso A.op_rate <> 0
                          Select Empnum = A.EMPNUM, A.op_rate).ToList

            HasP99 = (From A In P99Emp
                      Group By A.Empnum Into Group
                      Select Empnum, Rate = Group.First.op_rate
                      ).ToDictionary(Function(p) p.Empnum, Function(p) p.Rate.Value)

            If HasP99.Count > 0 Then
                MakeColVisible("pr99", False)
                MakeColVisible("ph99", False)
                MakeColVisible("pp99", False)
                Dim band = Me.BandedGridView1.Columns("pr99").OwnerBand
                Me.BandedGridView1.Bands.MoveTo(2, band)
            End If
            CheckOverrides = (From A In DB.pr_batch_overrides_setups Where A.CoNum = CoNum).ToList
        Else
            HasAutos = New List(Of Decimal)
            HasP99 = New Dictionary(Of Decimal, Decimal)
            CheckOverrides = New List(Of pr_batch_overrides_setup)
        End If

        Dim ExistingData As List(Of pr_batch_row)
        Dim LoadFromP As Boolean = IsNew

        TblPayrollData.BeginLoadData()

        Dim CalenderCount = (From A In DB.CALENDAR_RULES Where A.conum = CoNum AndAlso A.status = "Active" Select A.frequency).ToList
        Dim CalFrequency = (From A In Me.CalInfo Select A.Frequency).ToList

        If Not IsNew Then
            ExistingData = BatchList.pr_batch_rows.ToList
            If ExistingData.Count = 0 AndAlso Not IsImport Then
                LoadFromP = True
            Else
                'Load from existing
                ExistingData = (From A In ExistingData Order By A.number).ToList
                Dim OverrideRecs = (From A In DB.pr_batch_overrides Where A.CONUM = CoNum AndAlso A.PRNUM = Me.PrNum).ToList
                For X = 0 To ExistingData.Count - 1
                    Dim Row = ExistingData(X)
                    Dim PayrollRow = TblPayrollData.Select(String.Format("empnum = {0}  AND chknum = {1} AND linerate = {2}", Row.empnum, Row.run_import_id, Row.line_rate.GetValueOrDefault)).FirstOrDefault
A:
                    If PayrollRow Is Nothing Then
                        PayrollRow = TblPayrollData.NewRow
                        PayrollRow("empnum") = Row.empnum
                        PayrollRow("number") = Row.number
                        Dim EmpRow = (From A In EmployeeList Where A.EMPNUM = Row.empnum).FirstOrDefault
                        If EmpRow IsNot Nothing Then
                            If EmpRow.F_NAME IsNot Nothing Then PayrollRow("first") = EmpRow.F_NAME
                            If EmpRow.L_NAME IsNot Nothing Then PayrollRow("last") = EmpRow.L_NAME
                            PayrollRow("name") = EmpRow.F_NAME & " " & If(Not String.IsNullOrEmpty(EmpRow.M_NAME), EmpRow.M_NAME & " ", "") & EmpRow.L_NAME
                            If EmpRow.EMP_TYPE = "CONTRACT" Then
                                PayrollRow("last") = PayrollRow("last") & " (1099)"
                                PayrollRow("name") = PayrollRow("name") & " (1099)"
                            End If
                            PayrollRow("ssn") = If(String.IsNullOrEmpty(EmpRow.SSN), "", If(EmpRow.SSN.Length >= 10, New String("*", 6) & EmpRow.SSN.Substring(EmpRow.SSN.Length - 4), EmpRow.SSN))

                            Dim RatesList As New List(Of Decimal?)({EmpRow.RATE_1, EmpRow.RATE_2, EmpRow.RATE_3})
                            RatesList = (From A In RatesList Where A.HasValue).ToList
                            PayrollRow("HasMultiRates") = RatesList.Count > 1

                            PayrollRow("PAY_FREQ") = EmpRow.PAY_FREQ
                            PayrollRow("PAY_FREQ_SORT") = GetFrequencySort(EmpRow.PAY_FREQ)
                            PayrollRow("USERDEF1") = GetUserDef1Sort(EmpRow)
                            PayrollRow("USERDEF21") = EmpRow.USERDEF21
                            PayrollRow("DefaultSort") = GetEmpName(EmpRow) & EmpRow.EMPNUM.ToString.PadLeft(5)
                            PayrollRow("UCIState") = EmpRow.UCI_STATE
                            PayrollRow("EE_DEPTNUM") = EmpRow.DEPTNUM
                            If EmpRow.DIVNUM IsNot Nothing Then PayrollRow("EE_DIVNUM") = EmpRow.DIVNUM

                            PayrollRow("STREET") = EmpRow.STREET
                            PayrollRow("CITY") = EmpRow.CITY
                            PayrollRow("ADDR_STATE") = EmpRow.ADDR_STATE
                            PayrollRow("ZIP") = EmpRow.ZIP
                            PayrollRow("FED_STATUS") = Microsoft.VisualBasic.Left(EmpRow.FED_STATUS, 1) & "-" & EmpRow.FED_DEPS.GetValueOrDefault

                            PayrollRow("FedExtra") = nz(EmpRow.FED_WH_EXTRA, DBNull.Value)
                            PayrollRow("FedFixed") = nz(EmpRow.FED_WH_FIXED, DBNull.Value)
                        End If

                        If Me.StateInfo.ContainsKey(Row.empnum) Then
                            PayrollRow("ST_Extra") = nz(Me.StateInfo(Row.empnum).Item1, DBNull.Value)
                            PayrollRow("FedFixed") = nz(Me.StateInfo(Row.empnum).Item2, DBNull.Value)
                        End If

                        If Row.line_rate IsNot Nothing Then PayrollRow("linerate") = Row.line_rate
                        If Row.divnum IsNot Nothing Then PayrollRow("division") = Row.divnum
                        If Row.deptnum IsNot Nothing Then PayrollRow("department") = Row.deptnum
                        If Row.job1 IsNot Nothing Then PayrollRow("job") = Row.job1
                        If Row.job2 IsNot Nothing Then PayrollRow("job2") = Row.job2
                        If Row.job3 IsNot Nothing Then PayrollRow("job3") = Row.job3
                        If Row.job4 IsNot Nothing Then PayrollRow("job4") = Row.job4
                        If Row.job5 IsNot Nothing Then PayrollRow("job5") = Row.job5
                        If Row.term_date IsNot Nothing Then PayrollRow("TermDate") = Row.term_date
                        If Row.date.HasValue Then PayrollRow("WorkDate") = Row.date.Value
                        PayrollRow("conum") = CoNum
                        PayrollRow("chknum") = Row.run_import_id.GetValueOrDefault(1)
                        Dim ChkLineNum = TblPayrollData.Select(String.Format("empnum = {0}  AND chknum = {1}", Row.empnum, Row.run_import_id)).Count
                        PayrollRow("CheckLineNumber") = ChkLineNum + 1
                        PayrollRow("dd") = Row.run_dd_flag.FromYesNoString
                        PayrollRow("hrs") = Row.run_auto_hours.FromYesNoString
                        PayrollRow("pays") = Row.run_auto_pays.FromYesNoString
                        PayrollRow("deds") = Row.run_auto_deds.FromYesNoString
                        PayrollRow("memos") = Row.run_auto_memos.FromYesNoString
                        PayrollRow("sick") = Row.run_sick.FromYesNoString
                        PayrollRow("vac") = Row.run_vacation.FromYesNoString
                        PayrollRow("per") = Row.run_personal.FromYesNoString
                        PayrollRow("hrs") = Row.run_auto_hours.FromYesNoString
                        PayrollRow("hrs") = Row.run_auto_hours.FromYesNoString
                        If Row.chk_memo IsNot Nothing Then PayrollRow("checkmemo") = Row.chk_memo
                        If Row.term_date IsNot Nothing Then PayrollRow("TermDate") = Row.term_date

                        Dim LineNotes = (From A In PayLineNotes Where A.EmployeeID = Row.empnum AndAlso A.CheckNum = Row.run_import_id AndAlso (A.LineNumber Is Nothing OrElse A.LineNumber = Row.number) Order By If(A.EnteredBy = "Import", 1, 2))
                        If LineNotes.Count > 0 Then
                            PayrollRow("HasNotes") = 2
                            If LineNotes(0).EnteredBy = "Import" Then
                                PayrollRow("ImportNotes") = LineNotes(0).Note
                            End If
                        Else
                            Dim EmpNotes = (From A In NotesData Where A.empnum = Row.empnum Order By A.priority Descending Select A.priority.Substring(0, 1)).Distinct.ToList
                            If EmpNotes.Count > 0 Then
                                PayrollRow("HasNotes") = EmpNotes.First
                            End If
                        End If

                        PayrollRow("HasAutos") = HasAutos.Contains(Row.empnum) AndAlso TblPayrollData.Compute("COUNT(number)", "empnum = " & Row.empnum) = 0
                        TblPayrollData.Rows.Add(PayrollRow)

                        Dim OverrideRec = (From A In OverrideRecs Where A.EMPNUM = Row.empnum AndAlso A.CHK_COUNTER = Row.run_import_id.GetValueOrDefault(1)).FirstOrDefault
                        If OverrideRec IsNot Nothing Then
                            If OverrideRec.NetOverrideAmount.HasValue Then
                                PayrollRow("Net") = OverrideRec.NetOverrideAmount
                                PayrollRow("NetOverrideType") = OverrideRec.NetOverrideAdjustType
                                If OverrideRec.NetOverrideDedNum.HasValue Then PayrollRow("NetOverrideDedNum") = OverrideRec.NetOverrideDedNum
                                bciShowNetColumn.Checked = True
                                ShowNetCol_CheckedChanged()
                            End If
                            If OverrideRec.FedOverrideAmount.HasValue Then PayrollRow("FedOverride") = OverrideRec.FedOverrideAmount
                            If OverrideRec.STOverrideAmount.HasValue Then PayrollRow("STOverride") = OverrideRec.STOverrideAmount
                            If OverrideRec.LOCOverrideAmount.HasValue Then PayrollRow("LOCOverride") = OverrideRec.LOCOverrideAmount
                            If OverrideRec.DBOverrideAmount.HasValue Then PayrollRow("DBOverride") = OverrideRec.DBOverrideAmount
                            If OverrideRec.FLIOverrideAmount.HasValue Then PayrollRow("FLIOverride") = OverrideRec.FLIOverrideAmount
                            If OverrideRec.TaxFrequency IsNot Nothing Then PayrollRow("TaxFrequency") = OverrideRec.TaxFrequency
                            If OverrideRec.ChkType IsNot Nothing Then PayrollRow("ChkType") = OverrideRec.ChkType
                            If OverrideRec.OASDIOverrideAmount.HasValue Then PayrollRow("OASDIOverride") = OverrideRec.OASDIOverrideAmount
                            If OverrideRec.MedicareOverrideAmount.HasValue Then PayrollRow("MedicareOverride") = OverrideRec.MedicareOverrideAmount
                            If OverrideRec.ManualCheckNumber.HasValue Then PayrollRow("ChkNumber") = OverrideRec.ManualCheckNumber
                            If OverrideRec.WrkState IsNot Nothing Then PayrollRow("WrkStateOverride") = OverrideRec.WrkState
                            If OverrideRec.ResState IsNot Nothing Then PayrollRow("ResStateOverride") = OverrideRec.ResState
                            If OverrideRec.UIState IsNot Nothing Then PayrollRow("UIStateOverride") = OverrideRec.UIState
                            If OverrideRec.FedOverrideAmount.HasValue OrElse OverrideRec.STOverrideAmount.HasValue OrElse OverrideRec.LOCOverrideAmount.HasValue OrElse OverrideRec.DBOverrideAmount.HasValue OrElse OverrideRec.FLIOverrideAmount.HasValue OrElse OverrideRec.TaxFrequency IsNot Nothing OrElse OverrideRec.ChkType IsNot Nothing OrElse OverrideRec.OASDIOverrideAmount.HasValue OrElse OverrideRec.MedicareOverrideAmount.HasValue OrElse OverrideRec.ManualCheckNumber.HasValue Then
                                bciShowTaxOverrideColumns.Checked = True
                                ShowtaxOverridesChanged()
                            End If
                        End If
                    End If
                    If Row.type = "P" Then
                        If Row.code = 0 Then
                            If PayrollRow("reghrs") & PayrollRow("othrs") & PayrollRow("salary") & "" <> "" Then
                                PayrollRow = Nothing
                                GoTo A
                            End If
                            If Row.pay_rate IsNot Nothing Then PayrollRow("rate") = Row.pay_rate
                            If Row.rate_num IsNot Nothing Then PayrollRow("ratenum") = Row.rate_num
                            If Row.regular_hours IsNot Nothing Then PayrollRow("reghrs") = If(Not bciInputIsMinutes.Checked, Row.regular_hours, ConvertDecimalToHours(Row.regular_hours))
                            If Row.overtime_hours IsNot Nothing Then PayrollRow("othrs") = If(Not bciInputIsMinutes.Checked, Row.overtime_hours, ConvertDecimalToHours(Row.overtime_hours))
                            If Row.amount IsNot Nothing Then PayrollRow("salary") = Row.amount
                        Else
                            If PayrollRow("ph" & Row.code) & PayrollRow("po" & Row.code) & PayrollRow("pp" & Row.code) & "" <> "" Then
                                PayrollRow = Nothing
                                GoTo A
                            End If
                            If Row.pay_rate IsNot Nothing Then
                                PayrollRow("pr" & Row.code) = Row.pay_rate
                                MakeColVisible("pr" & Row.code, False)
                            End If
                            If Row.rate_num IsNot Nothing Then
                                PayrollRow("pn" & Row.code) = Row.rate_num
                                MakeColVisible("pn" & Row.code, False)
                            End If
                            If Row.regular_hours IsNot Nothing Then
                                PayrollRow("ph" & Row.code) = If(Not bciInputIsMinutes.Checked, Row.regular_hours, ConvertDecimalToHours(Row.regular_hours))
                                MakeColVisible("ph" & Row.code, False)
                            End If
                            If Row.overtime_hours IsNot Nothing Then
                                PayrollRow("po" & Row.code) = If(Not bciInputIsMinutes.Checked, Row.overtime_hours, ConvertDecimalToHours(Row.overtime_hours))
                                MakeColVisible("po" & Row.code, False)
                            End If
                            If Row.amount IsNot Nothing Then
                                PayrollRow("pp" & Row.code) = Row.amount
                                MakeColVisible("pp" & Row.code, False)
                            End If
                        End If
                    Else
                        If PayrollRow(Row.type & "f" & Row.code) & "" <> "" Then
                            PayrollRow = Nothing
                            GoTo A
                        End If
                        PayrollRow(Row.type & "f" & Row.code) = Row.amount
                        MakeColVisible("df" & Row.code, False)
                    End If
                Next

                Me.EmployeeChangeLog = (From A In DB.pr_batch_employee_changes Where A.batch_id = BatchList.id).ToList
            End If
        End If

        If LoadFromP Then
            'Load Payroll Data
            If Me.SecondChecksOnly Then
                Dim PaidEmployees = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum AndAlso A.GROSS > 0 Select A.EMPNUM).ToList
                EmployeeList = (From A In EmployeeList Where PaidEmployees.Contains(A.EMPNUM)).ToList
            End If
            Dim weeks As List(Of Date) = Nothing
            Dim mCal = (From A In Me.CalInfo Where A.Frequency = "Monthly").FirstOrDefault
            If Not String.IsNullOrEmpty(Me.CoPayrollOptions.DoCheckForEachWeekday) AndAlso mCal IsNot Nothing Then
                weeks = New List(Of Date)
                Dim wDay As DayOfWeek = [Enum].Parse(GetType(DayOfWeek), Me.CoPayrollOptions.DoCheckForEachWeekday)
                Dim d = New Date(mCal.EndDate.Year, mCal.EndDate.Month, 1)
                While d.Month = mCal.EndDate.Month
                    If d.DayOfWeek = wDay Then weeks.Add(d)
                    d = d.AddDays(1)
                End While
                Me.bciShowTaxOverrideColumns.Checked = True
                ShowtaxOverridesChanged()
            End If
            For x = 0 To EmployeeList.Count - 1
                Dim Row = EmployeeList(x)

                'if multiple calendars then select by frequency
                If Not CoPayrollOptions.LoadAllFrequencies AndAlso CalFrequency.Count > 0 AndAlso Not CalFrequency.Contains(Row.PAY_FREQ) AndAlso CalenderCount.Contains(Row.PAY_FREQ) Then
                    Continue For
                End If
                Dim PayOnlyOn = (From A In CheckOverrides Where A.EmpNum = Row.EMPNUM AndAlso A.RecordType = "Pay Only On").FirstOrDefault
                If PayOnlyOn IsNot Nothing Then
                    'Dim CalInfo = Me.CalInfo.First
                    Dim IsPay = (From A In Me.CalInfo Where (A.Prd = 1 AndAlso PayOnlyOn.Prd1) _
                                                     OrElse (A.Prd = 2 AndAlso PayOnlyOn.Prd2) _
                                                     OrElse (A.Prd = 3 AndAlso PayOnlyOn.Prd3) _
                                                     OrElse (A.Prd = 4 AndAlso PayOnlyOn.Prd4) _
                                                     OrElse (A.Prd = 5 AndAlso PayOnlyOn.Prd5) _
                                                     OrElse (Not String.IsNullOrEmpty(A.LastOfMonth) AndAlso PayOnlyOn.LastOfMonth) _
                                                     OrElse (A.IsLastOfQtr AndAlso PayOnlyOn.LastOfQtr.GetValueOrDefault) _
                                                     OrElse (A.IsFirstOfQtr AndAlso PayOnlyOn.FirstOfQtr.GetValueOrDefault)).Any

                    If Not String.IsNullOrEmpty(PayOnlyOn.FilterToPayFreq) AndAlso Not CalInfo.Any(Function(p) p.Frequency = PayOnlyOn.FilterToPayFreq) Then
                        IsPay = False
                    End If

                    If Not IsPay Then
                        Continue For
                    End If
                End If
                If Not Me.SecondChecksOnly Then
                    If weeks IsNot Nothing AndAlso Row.PAY_FREQ.ToUpper = "WEEKLY" Then
                        Dim counter As Integer = 1
                        For Each d In weeks
                            LoadEmployeeRecord(Row, Nothing, d, counter)
                            counter += 1
                        Next
                    Else
                        LoadEmployeeRecord(Row)
                    End If
                End If
                'Second Check
                Dim SecondChecks = (From A In CheckOverrides Where A.EmpNum = Row.EMPNUM AndAlso A.RecordType = "Second Check").ToList
                If SecondChecks.Count > 0 Then
                    For Each check In SecondChecks
                        Dim IsPay = (From A In Me.CalInfo Where (A.Prd = 1 AndAlso check.Prd1) _
                                                         OrElse (A.Prd = 2 AndAlso check.Prd2) _
                                                         OrElse (A.Prd = 3 AndAlso check.Prd3) _
                                                         OrElse (A.Prd = 4 AndAlso check.Prd4) _
                                                         OrElse (A.Prd = 5 AndAlso check.Prd5) _
                                                         OrElse (Not String.IsNullOrEmpty(A.LastOfMonth) AndAlso check.LastOfMonth) _
                                                         OrElse (A.IsLastOfQtr AndAlso check.LastOfQtr.GetValueOrDefault) _
                                                         OrElse (A.IsFirstOfQtr AndAlso check.FirstOfQtr.GetValueOrDefault)).Any

                        If Not String.IsNullOrEmpty(check.FilterToPayFreq) AndAlso Not CalInfo.Any(Function(p) p.Frequency = check.FilterToPayFreq) Then
                            IsPay = False
                        End If

                        If IsPay OrElse check.AllPrds.GetValueOrDefault Then
                            LoadEmployeeRecord(Row, check)
                        End If
                    Next
                End If

                'Unscheduled pays/
                If Me.CoPayrollOptions.LoadAllUnscheduledPaysDeds = True AndAlso _Autos IsNot Nothing AndAlso _Autos.Count > 0 Then
                    'Code 0 is already loaded in the LoadEmployeerecord function
                    Dim UnscheduledPays = (From A In _Autos
                                           Where A.EmpNum = Row.EMPNUM AndAlso A.IsUnscheduled = True AndAlso A.Code <> 0
                                           Group By A.DIVNUM, A.CL_DEPT Into Group).ToList

                    If UnscheduledPays.Count > 0 Then
                        Dim existing = (From A In TblPayrollData Where A.Field(Of Decimal)("empnum") = Row.EMPNUM
                                        Select chknum = A.Field(Of Decimal)("chknum"), division = A.Field(Of Decimal)("division")).ToList
                        'Solomon added on Mar 1, '23
                        If existing.Count = 0 Then
                            Continue For
                        End If

                        Dim num = 0
                        For Each g In UnscheduledPays
                            num += 1
                            Dim existingCounter = (From A In existing Where A.division = g.DIVNUM Order By A.chknum Descending).FirstOrDefault
                            Dim counter = If(existingCounter IsNot Nothing, existingCounter.chknum, existing.Max(Function(p) p.chknum) + 1)
                            Dim rate = New RateCode With {.RateCode = "A" & num, .Amount = 0, .Department = g.CL_DEPT, .Division = g.DIVNUM}
                            Dim tr = LoadEmployeeRecord(Row, Nothing, Nothing, counter, rate)
                            tr("division") = g.DIVNUM
                            tr("department") = g.CL_DEPT
                            For Each code In g.Group
                                Dim AmountCol = GetColumnName(code.Type, code.Code, ColumnName.amount)
                                Dim RateCol = GetColumnName(code.Type, code.Code, ColumnName.pay_rate)
                                If code.Amount.HasValue Then tr(AmountCol) = code.Amount
                                If code.Type = "P" AndAlso code.Rate.HasValue Then tr(RateCol) = code.Rate

                                If code.Amount.HasValue Then
                                    If Me.CoPayrollOptions.ShowAllColumnsForAutoPaysDeds.GetValueOrDefault Then MakeColVisible(AmountCol, False)
                                    SetCellWatermark(code.EmpNum, tr("chknum"), -(num + 1), AmountCol, "Auto-" & code.Amount)
                                End If
                                If code.Type = "P" AndAlso code.Rate.HasValue Then
                                    If Me.CoPayrollOptions.ShowAllColumnsForAutoPaysDeds.GetValueOrDefault Then MakeColVisible(RateCol, False)
                                    SetCellWatermark(code.EmpNum, tr("chknum"), -(num + 1), RateCol, "Auto-" & code.Rate)
                                End If

                            Next
                            counter += 1
                        Next
                    End If
                End If
            Next
            Me.EmployeeChangeLog = New List(Of pr_batch_employee_change)
        End If

        If UDF21.Contains("T01") Then 'Get Department Descriptions for sorting
            Dim Dept = (From A In DB.DEPARTMENTs Where A.CONUM = Me.CoNum Select A.DEPTNUM, A.DIVNUMD, A.DEPT_DESC).ToList
            For Each row In TblPayrollData.Rows
                Dim pRow = row
                Dim DeptDesc As String
                If IsDBNull(pRow("department")) Then
                    Throw New Exception("Employee {0} does Not have a Department Number setup. Please go To Employee setup And make sure Employee Is ALL setup correctly.".FormatWith(pRow("empnum")))
                End If

                If UDF21.Contains("T30") Then
                    DeptDesc = (From A In Dept Where A.DIVNUMD = pRow("EE_DIVNUM") AndAlso A.DEPTNUM = pRow("EE_DEPTNUM") Select A.DEPT_DESC).FirstOrDefault
                Else
                    DeptDesc = (From A In Dept Where A.DIVNUMD = pRow("division") AndAlso A.DEPTNUM = pRow("department") Select A.DEPT_DESC).FirstOrDefault
                End If
                row("DEPT_DESC") = DeptDesc
            Next
        End If

        Dim EmpFreq = (From A In TblPayrollData Select PAY_FREQ = A.Field(Of String)("PAY_FREQ")
                       Select PAY_FREQ).Distinct.ToList

        If EmpFreq.Count > 0 Then
            MakeColVisible("PAY_FREQ", False)
            Me.BandedGridView1.FormatConditions(0).Value1 = CalFrequency
        End If

        TblPayrollData.EndLoadData()
        TblPayrollData.AcceptChanges()

        SortGridToDefault()
    End Sub

    Function LoadEmployeeRecord(ByVal EmployeeEnt As EMPLOYEE, Optional ByVal SecondCheck As pr_batch_overrides_setup = Nothing, Optional WorkDate As Date? = Nothing,
                                Optional chknum As Integer = 1, Optional Rate As RateCode = Nothing) As DataRow
        If _DefaultValues Is Nothing Then _DefaultValues = New Dictionary(Of String, Decimal)
        Dim RateAmounts As New List(Of RateCode)
        If Rate IsNot Nothing Then
            RateAmounts.Add(Rate)
        ElseIf SecondCheck Is Nothing AndAlso Not _PayrollEnt.PR_DESCR = "Bonus Run" Then
            If EmployeeEnt.RATE_1.GetValueOrDefault <> 0 Then RateAmounts.Add(New RateCode With {.RateCode = "R1", .Amount = EmployeeEnt.RATE_1, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})
            If EmployeeEnt.RATE_2.GetValueOrDefault <> 0 Then RateAmounts.Add(New RateCode With {.RateCode = "R2", .Amount = EmployeeEnt.RATE_2, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})
            If EmployeeEnt.RATE_3.GetValueOrDefault <> 0 Then RateAmounts.Add(New RateCode With {.RateCode = "R3", .Amount = EmployeeEnt.RATE_3, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})
            If EmployeeEnt.SALARY_AMT.GetValueOrDefault <> 0 Then RateAmounts.Add(New RateCode With {.RateCode = "S", .Amount = EmployeeEnt.SALARY_AMT, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})

            'rates from auto
            Dim AutoRates = (From A In _Autos Where A.EmpNum = EmployeeEnt.EMPNUM AndAlso A.Code = 0).ToList
            For x = 0 To AutoRates.Count - 1
                If AutoRates(x).Rate.GetValueOrDefault <> 0 Then
                    RateAmounts.Add(New RateCode With {.RateCode = "R" & x + 4, .Amount = AutoRates(x).Rate, .Division = AutoRates(x).DIVNUM, .Department = AutoRates(x).CL_DEPT})
                    SetCellWatermark(EmployeeEnt.EMPNUM, chknum, AutoRates(x).Rate, "rate", "Auto-" & AutoRates(x).Rate.Value.ToString("N2"))
                ElseIf AutoRates(x).Amount.GetValueOrDefault <> 0 Then
                    'changed 3/19/2017
                    'if it's a scheduled amount do not add another line here.
                    If AutoRates(x).IsUnscheduled.GetValueOrDefault AndAlso Me.CoPayrollOptions.LoadAllUnscheduledPaysDeds.GetValueOrDefault Then
                        RateAmounts.Add(New RateCode With {.RateCode = "S" & x + 4, .Amount = AutoRates(x).Amount, .Division = AutoRates(x).DIVNUM, .Department = AutoRates(x).CL_DEPT})
                    End If
                    SetCellWatermark(EmployeeEnt.EMPNUM, chknum, -(x + 4), "salary", "Auto-" & AutoRates(x).Amount.Value.ToString("c"))
                End If
            Next

            If RateAmounts.Count = 0 Then RateAmounts.Add(New RateCode With {.RateCode = "R1", .Amount = 0.0, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})
        ElseIf SecondCheck IsNot Nothing Then
            RateAmounts.Add(New RateCode With {.RateCode = "S2", .Amount = 0, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})
        Else
            RateAmounts.Add(New RateCode With {.RateCode = "Bonus", .Amount = 0, .Division = EmployeeEnt.DIVNUM, .Department = EmployeeEnt.DEPTNUM})
        End If

        Dim LineNum As Integer
        Dim TR As DataRow
        For Each RateAmount In RateAmounts
            LineNum += 1
            TR = TblPayrollData.NewRow
            TR("RateCode") = RateAmount.RateCode
            TR("empnum") = EmployeeEnt.EMPNUM
            If EmployeeEnt.F_NAME IsNot Nothing Then TR("first") = EmployeeEnt.F_NAME
            If EmployeeEnt.L_NAME IsNot Nothing Then TR("last") = EmployeeEnt.L_NAME
            TR("name") = EmployeeEnt.F_NAME & " " & If(Not String.IsNullOrEmpty(EmployeeEnt.M_NAME), EmployeeEnt.M_NAME & " ", "") & EmployeeEnt.L_NAME
            If EmployeeEnt.EMP_TYPE = "CONTRACT" Then
                TR("last") = TR("last") & " (1099)"
                TR("name") = TR("name") & " (1099)"
            End If
            TR("UCIState") = EmployeeEnt.UCI_STATE
            If EmployeeEnt.DEPTNUM.HasValue Then TR("EE_DEPTNUM") = EmployeeEnt.DEPTNUM
            If EmployeeEnt.DIVNUM IsNot Nothing Then TR("EE_DIVNUM") = EmployeeEnt.DIVNUM
            TR("ssn") = If(String.IsNullOrEmpty(EmployeeEnt.SSN), "", If(EmployeeEnt.SSN.Length >= 10, New String("*", 6) & EmployeeEnt.SSN.Substring(EmployeeEnt.SSN.Length - 4), EmployeeEnt.SSN))
            If RateAmount.Division IsNot Nothing Then TR("division") = RateAmount.Division
            If RateAmount.Department IsNot Nothing Then TR("department") = RateAmount.Department
            If SecondCheck IsNot Nothing Then
                If SecondCheck.DivNum.HasValue Then TR("division") = SecondCheck.DivNum
                If SecondCheck.DeptNum.HasValue Then TR("department") = SecondCheck.DeptNum
            End If
            If EmployeeEnt.job1 IsNot Nothing Then TR("job") = EmployeeEnt.job1
            If EmployeeEnt.job2 IsNot Nothing Then TR("job2") = EmployeeEnt.job2
            If EmployeeEnt.job3 IsNot Nothing Then TR("job3") = EmployeeEnt.job3
            If EmployeeEnt.job4 IsNot Nothing Then TR("job4") = EmployeeEnt.job4
            If EmployeeEnt.job5 IsNot Nothing Then TR("job5") = EmployeeEnt.job5
            If EmployeeEnt.TERM_DATE IsNot Nothing Then TR("TermDate") = EmployeeEnt.TERM_DATE
            TR("STREET") = EmployeeEnt.STREET
            TR("CITY") = EmployeeEnt.CITY
            TR("ADDR_STATE") = EmployeeEnt.ADDR_STATE
            TR("ZIP") = EmployeeEnt.ZIP
            TR("FED_STATUS") = Microsoft.VisualBasic.Left(EmployeeEnt.FED_STATUS, 1) & "-" & EmployeeEnt.FED_DEPS.GetValueOrDefault

            TR("FedExtra") = nz(EmployeeEnt.FED_WH_EXTRA, DBNull.Value)
            TR("FedFixed") = nz(EmployeeEnt.FED_WH_FIXED, DBNull.Value)
            If Me.StateInfo.ContainsKey(EmployeeEnt.EMPNUM) Then
                TR("ST_Extra") = nz(Me.StateInfo(EmployeeEnt.EMPNUM).Item1, DBNull.Value)
                TR("ST_Fixed") = nz(Me.StateInfo(EmployeeEnt.EMPNUM).Item2, DBNull.Value)
            End If

            Dim IsParsonage = (EmployeeEnt.USERDEF21 & "").ToUpper.Contains("P30") OrElse
                (CoPayrollOptions.ParsonageAll AndAlso
                 (CoPayrollOptions.ParsonageDivisions Is Nothing OrElse
                  (From A In CoPayrollOptions.ParsonageDivisions.Split(",") Select CDec(A)).Contains(EmployeeEnt.DIVNUM.GetValueOrDefault)
                  )
              )

            If RateAmount.RateCode.Substring(0, 1) = "R" OrElse RateAmount.RateCode.Substring(0, 1) = "A" Then
                TR("linerate") = RateAmount.Amount.GetValueOrDefault
            ElseIf Not RateAmount.RateCode = "Bonus" Then
                TR("linerate") = 0
                If SecondCheck Is Nothing Then
                    If Not IsParsonage Then
                        TR("salary") = RateAmount.Amount
                        If RateAmount.RateCode = "S" AndAlso EmployeeEnt.SALARY_AMT.HasValue Then _DefaultValues(EmployeeEnt.EMPNUM & "|salary") = EmployeeEnt.SALARY_AMT
                    Else
                        TR("pp30") = RateAmount.Amount
                        If RateAmount.RateCode = "S" AndAlso EmployeeEnt.SALARY_AMT.HasValue Then _DefaultValues(EmployeeEnt.EMPNUM & "|pp30") = EmployeeEnt.SALARY_AMT
                    End If
                End If
            End If
            TR("CheckLineNumber") = LineNum
            If SecondCheck Is Nothing AndAlso LineNum = 1 AndAlso Not _PayrollEnt.PR_DESCR = "Bonus Run" AndAlso EmployeeEnt.AUTO_SAL_HRS & "" = "YES" AndAlso (EmployeeEnt.default_hours.HasValue OrElse CoPayrollOptions.AutoDefaultHours.HasValue) Then
                Dim defHours = If(CoPayrollOptions.AutoDefaultHours.GetValueOrDefault <> 0, CoPayrollOptions.AutoDefaultHours.Value, EmployeeEnt.default_hours)
                If Not IsParsonage Then
                    TR("reghrs") = defHours
                    If defHours.HasValue Then _DefaultValues(EmployeeEnt.EMPNUM & "|reghrs") = defHours
                Else
                    TR("ph30") = defHours
                    If defHours.HasValue Then _DefaultValues(EmployeeEnt.EMPNUM & "|ph30") = defHours
                End If
            End If
            If SecondCheck Is Nothing AndAlso LineNum = 1 AndAlso Not _PayrollEnt.PR_DESCR = "Bonus Run" AndAlso EmployeeEnt.AUTO_SAL_HRS & "" = "YES" AndAlso CheckOverrides IsNot Nothing Then
                Dim AutoOTHours = (From A In CheckOverrides Where A.EmpNum = EmployeeEnt.EMPNUM AndAlso A.RecordType = "Auto OT Hours").FirstOrDefault
                If AutoOTHours IsNot Nothing AndAlso AutoOTHours.OTHours.HasValue Then
                    If Not IsParsonage Then
                        TR("othrs") = AutoOTHours.OTHours
                        _DefaultValues(EmployeeEnt.EMPNUM & "|othrs") = AutoOTHours.OTHours
                    Else
                        TR("po30") = AutoOTHours.OTHours
                        _DefaultValues(EmployeeEnt.EMPNUM & "|po30") = AutoOTHours.OTHours
                    End If
                End If
            End If
            TR("dd") = BatchList.run_dd_flag = "YES" AndAlso
                                    Not (EmployeeEnt.DD_ACC_TYPE_1 = "None" AndAlso EmployeeEnt.DD_ACC_TYPE_2 = "None" AndAlso EmployeeEnt.DD_ACC_TYPE_3 = "None" AndAlso EmployeeEnt.DD_ACC_TYPE_4 = "None") AndAlso
                                    Not (_BonusOptions IsNot Nothing AndAlso _BonusOptions.Note.Contains("Turn Off DD: YES"))
            TR("conum") = CoNum
            If SecondCheck Is Nothing Then
                TR("chknum") = chknum
                Dim EmpNotes = (From A In NotesData Where A.empnum = EmployeeEnt.EMPNUM Order By A.priority Descending Select A.priority.Substring(0, 1)).Distinct.ToList
                If EmpNotes.Count > 0 Then
                    TR("HasNotes") = EmpNotes.First
                End If

                TR("HasAutos") = HasAutos.Contains(EmployeeEnt.EMPNUM)
                Dim hasMulti = RateAmounts.Count > 1
                If Rate IsNot Nothing AndAlso hasMulti = False Then
                    hasMulti = TblPayrollData.Select("empnum = " & EmployeeEnt.EMPNUM & " AND chknum = " & chknum).Length > 1
                End If
                TR("HasMultiRates") = hasMulti
            Else
                TR("IsSecondCheck") = True
                Dim ExistingChecks As New List(Of Decimal)
                If Me.SecondChecksOnly Then
                    ExistingChecks.AddRange((From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum AndAlso A.EMPNUM = SecondCheck.EmpNum Select A.CHK_COUNTER))
                End If
                ExistingChecks.AddRange(From A In TblPayrollData Where A.Field(Of Decimal)("empnum") = SecondCheck.EmpNum Select A.Field(Of Decimal)("chknum"))
                Dim MaxNum = ExistingChecks.Max
                If MaxNum = 1 Then
                    TR("chknum") = 2
                ElseIf ExistingChecks.Contains(SecondCheck.CheckCounter.GetValueOrDefault) Then
                    TR("chknum") = MaxNum
                Else
                    TR("chknum") = MaxNum + 1
                End If

                If SecondCheck.PayDedType = "P" Then
                    If SecondCheck.PayDedCode = 0 Then
                        TR("salary") = SecondCheck.PayDedAmount
                    Else
                        Dim ColName = "pp" & SecondCheck.PayDedCode
                        If Not TblPayrollData.Columns.Contains(ColName) Then
                            AddColumn("P", SecondCheck.PayDedCode)
                        End If
                        TR(ColName) = SecondCheck.PayDedAmount
                        MakeColVisible(ColName, False)
                    End If
                Else
                    Dim ColName = "df" & SecondCheck.PayDedCode
                    If Not TblPayrollData.Columns.Contains(ColName) Then
                        AddColumn("D", SecondCheck.PayDedCode)
                    End If
                    TR(ColName) = SecondCheck.PayDedAmount
                    MakeColVisible(ColName, False)
                End If
                If SecondCheck.NetOverrideAmount.HasValue Then
                    TR("Net") = SecondCheck.NetOverrideAmount
                    TR("NetOverrideType") = SecondCheck.NetOverrideAdjustType
                    If SecondCheck.NetOverrideDedNum.HasValue Then TR("NetOverrideDedNum") = SecondCheck.NetOverrideDedNum
                    bciShowNetColumn.Checked = True
                    ShowNetCol_CheckedChanged()
                End If

                If SecondCheck.FedOverrideAmount.HasValue Then TR("FedOverride") = SecondCheck.FedOverrideAmount
                If SecondCheck.STOverrideAmount.HasValue Then TR("STOverride") = SecondCheck.STOverrideAmount
                If SecondCheck.LOCOverrideAmount.HasValue Then TR("LOCOverride") = SecondCheck.LOCOverrideAmount
                If SecondCheck.DBOverrideAmount.HasValue Then TR("DBOverride") = SecondCheck.DBOverrideAmount
                If SecondCheck.FLIOverrdieAmount.HasValue Then TR("FLIOverride") = SecondCheck.FLIOverrdieAmount
                If SecondCheck.TaxFrequency IsNot Nothing Then TR("TaxFrequency") = SecondCheck.TaxFrequency
                If SecondCheck.CheckType IsNot Nothing Then
                    If SecondCheck.CheckType.ToUpper = "NORMAL" Then
                        TR("dd") = False
                    Else
                        TR("ChkType") = SecondCheck.CheckType
                    End If
                End If
                If SecondCheck.OASDIOverrideAmount.HasValue Then TR("OASDIOverride") = SecondCheck.OASDIOverrideAmount
                If SecondCheck.MedicareOverrideAmount.HasValue Then TR("MedicareOverride") = SecondCheck.MedicareOverrideAmount
                If SecondCheck.ManualCheckNumber.HasValue Then TR("ChkNumber") = SecondCheck.ManualCheckNumber
                If SecondCheck.WrkState IsNot Nothing Then TR("WrkStateOverride") = SecondCheck.WrkState
                If SecondCheck.ResState IsNot Nothing Then TR("ResStateOverride") = SecondCheck.ResState
                If SecondCheck.UIState IsNot Nothing Then TR("UIStateOverride") = SecondCheck.UIState
                If SecondCheck.FedOverrideAmount.HasValue OrElse SecondCheck.STOverrideAmount.HasValue OrElse SecondCheck.LOCOverrideAmount.HasValue OrElse SecondCheck.DBOverrideAmount.HasValue OrElse SecondCheck.FLIOverrdieAmount.HasValue OrElse SecondCheck.TaxFrequency IsNot Nothing OrElse SecondCheck.CheckType IsNot Nothing OrElse SecondCheck.OASDIOverrideAmount.HasValue OrElse SecondCheck.MedicareOverrideAmount.HasValue Then
                    Me.bciShowTaxOverrideColumns.Checked = True
                End If

                TR("HasNotes") = 0
                TR("HasAutos") = False
                TR("HasMultiRates") = False
            End If

            If HasP99.ContainsKey(EmployeeEnt.EMPNUM) Then
                TR("pr99") = HasP99(EmployeeEnt.EMPNUM)
            End If

            If EmployeeEnt.EMP_TYPE = "CONTRACT" AndAlso Not PayFieldFor1099 = "pp0" Then
                If PayFieldFor1099 = "df9" Then
                    SetCellWatermark(EmployeeEnt.EMPNUM, nz(TR("ChkNum"), 1), nz(TR("linerate"), 0), "salary", "D9 - Contract labor")
                Else
                    SetCellWatermark(EmployeeEnt.EMPNUM, nz(TR("ChkNum"), 1), nz(TR("linerate"), 0), "salary", "1099 - " & IIf(PayFieldFor1099.First = "p", "Pay ", "Ded ") & PayFieldFor1099.Substring(2, PayFieldFor1099.Length - 2))
                End If
            ElseIf IsParsonage Then
                Dim p = If((EmployeeEnt.USERDEF21 & "").ToUpper.Contains("P30"), "Employee", "Company")
                SetCellWatermark(EmployeeEnt.EMPNUM, nz(TR("ChkNum"), 1), nz(TR("linerate"), 0), "reghrs", "P30 - Parsonage " & p)
                SetCellWatermark(EmployeeEnt.EMPNUM, nz(TR("ChkNum"), 1), nz(TR("linerate"), 0), "othrs", "P30 - Parsonage " & p)
                SetCellWatermark(EmployeeEnt.EMPNUM, nz(TR("ChkNum"), 1), nz(TR("linerate"), 0), "salary", "P30 - Parsonage " & p)
            End If

            TblPayrollData.Rows.Add(TR)

            TR("PAY_FREQ") = EmployeeEnt.PAY_FREQ
            TR("PAY_FREQ_SORT") = GetFrequencySort(EmployeeEnt.PAY_FREQ)
            TR("USERDEF1") = GetUserDef1Sort(EmployeeEnt)
            TR("USERDEF21") = EmployeeEnt.USERDEF21
            TR("DefaultSort") = GetEmpName(EmployeeEnt) & EmployeeEnt.EMPNUM.ToString.PadLeft(5)
            If WorkDate.HasValue Then
                TR("workDate") = WorkDate
                If EmployeeEnt.UCI_STATE = "NY" Then
                    TR("DBOverride") = 0.6
                End If
            End If

            Dim NetOverride As pr_batch_overrides_setup = Nothing
            If SecondCheck Is Nothing Then
                NetOverride = (From A In CheckOverrides Where A.EmpNum = EmployeeEnt.EMPNUM AndAlso A.RecordType = "Net Override").FirstOrDefault
            ElseIf SecondCheck.NetOverrideAmount.GetValueOrDefault <> 0 Then
                NetOverride = SecondCheck
            End If
            If NetOverride IsNot Nothing Then
                If RateAmount.RateCode.Substring(0, 1) = "S" OrElse (NetOverride.NetOverrideAdjustType.StartsWith("Deduction")) Then
                    TR("Net") = NetOverride.NetOverrideAmount
                    TR("NetOverrideType") = NetOverride.NetOverrideAdjustType
                    If NetOverride.NetOverrideDedNum.HasValue Then TR("NetOverrideDedNum") = NetOverride.NetOverrideDedNum
                    bciShowNetColumn.Checked = True
                    ShowNetCol_CheckedChanged()
                End If
            End If
        Next
        Return TR
    End Function

    Private Sub frmBrandsPowerGrid_FormClosing(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        If TblPayrollData Is Nothing Then Exit Sub
        Dim ChangeTbl = TblPayrollData.GetChanges
        If ChangeTbl IsNot Nothing AndAlso ChangeTbl.Rows.Count > 0 Then
            If MessageBox.Show("Discard all changes?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.No Then
                e.Cancel = True
                Exit Sub
            End If
        End If
        If Me.DatesAndOptions IsNot Nothing Then
            Me.DatesAndOptions.Dispose()
        End If
    End Sub

    Private Sub ToolTipController1_GetActiveObjectInfo(ByVal sender As System.Object, ByVal e As DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs) Handles ToolTipController1.GetActiveObjectInfo
        If Not e.SelectedControl Is Me.GridControl1 Then Return
        Dim info As DevExpress.Utils.ToolTipControlInfo = Nothing
        Dim superTip As DevExpress.Utils.SuperToolTip = Nothing
        Try
            Dim hi = Me.BandedGridView1.CalcHitInfo(e.ControlMousePosition)
            If hi.RowHandle < 0 Then Return
            Dim dr = Me.BandedGridView1.GetDataRow(hi.RowHandle)
            If hi.InRowCell AndAlso hi.Column.Name = "colNotes" Then
                Dim EmployeeID As Decimal = dr("empnum")
                Dim CheckNum As Integer = dr("chknum")
                Dim sNotes = New System.Text.StringBuilder
                Dim Notes = (From A In NotesData Where A.empnum = EmployeeID).ToList
                For Each n In Notes
                    Me.RTFNotes.RtfText = n.note
                    sNotes.AppendLine(n.title & ": " & Me.RTFNotes.Text)
                Next
                Dim LineNotes = (From A In PayLineNotes Where A.EmployeeID = EmployeeID AndAlso A.CheckNum = CheckNum).ToList
                For Each n In LineNotes
                    sNotes.AppendLine(n.Note)
                Next
                If sNotes.Length > 0 Then
                    info = New DevExpress.Utils.ToolTipControlInfo(hi.RowHandle.ToString() & hi.Column.FieldName & "A",
                                                                    sNotes.ToString,
                                                                    DevExpress.Utils.ToolTipIconType.Information)
                End If
            ElseIf hi.InRowCell AndAlso hi.Column.Name = "colOP" Then
                If dr("HasAutos") Then
                    info = New DevExpress.Utils.ToolTipControlInfo(hi.RowHandle.ToString() & hi.Column.FieldName & "B",
                                                                   "Has Auto Pays/Deductions",
                                                                   DevExpress.Utils.ToolTipIconType.Information)
                End If
            ElseIf hi.InRowCell AndAlso hi.Column.Name = "colEdits" Then
                Dim Changes = (From A In Me.EmployeeChangeLog Where A.empnum = dr("empnum")).FirstOrDefault
                If Changes IsNot Nothing Then
                    info = New DevExpress.Utils.ToolTipControlInfo(hi.RowHandle.ToString & hi.Column.Name, Changes.GetFormattedString, DevExpress.Utils.ToolTipIconType.Information)
                    'superTip = New DevExpress.Utils.SuperToolTip
                    'superTip.MaxWidth = 600
                    'Dim args As New DevExpress.Utils.SuperToolTipSetupArgs
                    'args.Title.Text = "Employee Changes"
                    'args.Contents.Font = New Font("Courier New", 8, FontStyle.Regular)
                    'args.Contents.Text = ("Table").PadRight(30) & ("Key").PadRight(20) & ("Field").PadRight(30) & ("Old Value").PadRight(50) & ("New Value").PadRight(50) & vbCrLf
                    'args.Contents.Text &= Changes.change_log
                    'superTip.Setup(args)
                End If
            ElseIf hi.Column IsNot Nothing Then
                Dim wm = GetColumnWatermark(dr, hi.Column.FieldName)
                If Not String.IsNullOrEmpty(wm) Then
                    info = New DevExpress.Utils.ToolTipControlInfo(hi.RowHandle.ToString() + hi.Column.FieldName,
                                                                                       wm,
                                                                                       DevExpress.Utils.ToolTipIconType.Information)
                End If
            End If
        Finally
            e.Info = info
            If e.Info IsNot Nothing AndAlso superTip IsNot Nothing Then
                CType(e.Info, DevExpress.Utils.ToolTipControlInfo).SuperTip = superTip
            End If
        End Try
    End Sub

    Private Sub BandedGridView1_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles BandedGridView1.CustomUnboundColumnData
        If e.IsGetData Then
            If e.Column.FieldName.Contains("Auto_") Then
                Dim Row As DataRowView = e.Row
                If Row Is Nothing Then Exit Sub
                Dim wm = GetColumnWatermark(Row.Row, e.Column.Tag)
                If Not String.IsNullOrEmpty(wm) Then
                    e.Value = wm.Replace("Auto-", "")
                End If
            End If
        End If
    End Sub

    Private Sub BandedGridView1_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles BandedGridView1.PopupMenuShowing
        If e.HitInfo Is Nothing Then
            frmLogger.Debug("Hit info is nothing")
            Return
        End If
        Dim mItem As DevExpress.Utils.Menu.DXMenuItem
        If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            mItem = New DevExpress.Utils.Menu.DXMenuItem("Duplicate Selected Row - Add to current check", AddressOf DuplicateRow) With {.Tag = e.HitInfo.RowHandle}
            e.Menu.Items.Add(mItem)
            mItem = New DevExpress.Utils.Menu.DXMenuItem("Duplicate Selected Row - Add new check", AddressOf DuplicateRow) With {.Tag = e.HitInfo.RowHandle}
            e.Menu.Items.Add(mItem)
            mItem = New DevExpress.Utils.Menu.DXMenuItem("Edit Selected Employee Info", AddressOf EditEmployee) With {.Tag = e.HitInfo.RowHandle, .BeginGroup = True}
            e.Menu.Items.Add(mItem)

            mItem = New DevExpress.Utils.Menu.DXMenuItem("Add EXISTING Employee To Grid", click:=Sub() pccAddEmployee.Show())
            e.Menu.Items.Add(mItem)
            mItem = New DevExpress.Utils.Menu.DXMenuItem("Add NEW Employee", AddressOf EditEmployee) With {.Tag = "Add"}
            e.Menu.Items.Add(mItem)
            mItem = New DevExpress.Utils.Menu.DXMenuItem("Enter Hours For Record Only", AddressOf EnterHours) With {.Tag = e.HitInfo.RowHandle, .BeginGroup = True}
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Sub DuplicateRow(ByVal sender As DevExpress.Utils.Menu.DXMenuItem, ByVal e As EventArgs)
        Dim SourceRowIX As Integer = sender.Tag
        Dim Row = Me.BandedGridView1.GetDataRow(SourceRowIX)
        Dim NewRow = TblPayrollData.NewRow
        NewRow.ItemArray = Row.ItemArray
        For Each col In AvailCols
            If {"D", "P", "M"}.Contains(col.type) Then
                NewRow(col.sql_column) = DBNull.Value
            End If
        Next
        For Each col In TaxOverridesColumns.Union({"Net", "CheckLineNumber"})
            NewRow(col) = DBNull.Value
        Next
        NewRow("HasAutos") = False
        If sender.Caption.Contains("new check") Then
            Dim MaxNum = TblPayrollData.Compute("MAX(chknum)", "empnum=" & Row("empnum"))
            NewRow("chknum") = Integer.Parse(MaxNum) + 1
            NewRow("CheckLineNumber") = 1
        End If
        TblPayrollData.Rows.InsertAt(NewRow, SourceRowIX + 1)
        Me.BandedGridView1.RefreshData()
        Me.BandedGridView1.FocusedRowHandle = SourceRowIX + 1
    End Sub

    Sub EditEmployee(ByVal sender As DevExpress.Utils.Menu.DXMenuItem, ByVal e As EventArgs)
        Try
            Dim IsAddEmployee = sender IsNot Nothing AndAlso sender.Tag.ToString = "Add"
            Dim SourceRowIX As Integer = Me.BandedGridView1.FocusedRowHandle
            Dim Row As DataRow = Nothing
            'Dim EmpForm As New frmEmployeeInfo With {.CoNum = Me.CoNum}
            Dim EditEmpNum As Decimal?
            If Not IsAddEmployee Then
                Row = Me.BandedGridView1.GetDataRow(SourceRowIX)
                EditEmpNum = Row("empnum")
            End If

            Using frm = New frmEmployeeAddOrEdit(CoNum, EditEmpNum)
                If frm.ShowDialog = DialogResult.OK Then
                    EndEmployeeEdit(frm.EndEditResults.emp, frm.EndEditResults.IsNewEmployee, frm.EndEditResults.Changes)
                End If
            End Using
        Catch ex As Exception
            frmLogger.Error(ex, "Error in EditEmployee")
            DisplayErrorMessage("Error editing or adding an employee", ex)
        End Try
    End Sub

    Sub EndEmployeeEdit(EmployeeEnt As EMPLOYEE, IsNewEmployee As Boolean, changes As String)
        If Not IsNewEmployee Then
            Dim SourceRowIX As Integer = Me.BandedGridView1.FocusedRowHandle
            Dim Row = Me.BandedGridView1.GetDataRow(SourceRowIX)
            Row("empnum") = EmployeeEnt.EMPNUM
            Row("first") = EmployeeEnt.F_NAME
            Row("last") = EmployeeEnt.L_NAME
            Row("division") = EmployeeEnt.DIVNUM
            Row("department") = EmployeeEnt.DEPTNUM
            Row("TermDate") = EmployeeEnt.TERM_DATE
            'If EmpForm.EmployeeEnt.SALARY_AMT.HasValue Then Row("salary") = EmpForm.EmployeeEnt.SALARY_AMT
            Me.BandedGridView1.RefreshRow(SourceRowIX)
        Else
            LoadEmployeeRecord(EmployeeEnt)
            Me.BandedGridView1.RefreshData()
            Dim NewRowIX = Me.BandedGridView1.LocateByValue("empnum", EmployeeEnt.EMPNUM, Nothing)
            If NewRowIX >= 0 Then
                Me.BandedGridView1.FocusedRowHandle = NewRowIX
            End If
        End If
        If (From A In EmployeeList Where A.EMPNUM = EmployeeEnt.EMPNUM).FirstOrDefault Is Nothing Then
            EmployeeList.Add(EmployeeEnt)
        End If

        If Not String.IsNullOrEmpty(changes) Then
            Dim ChangeRec = (From A In Me.EmployeeChangeLog Where A.empnum = EmployeeEnt.EMPNUM).FirstOrDefault
            If ChangeRec Is Nothing Then
                ChangeRec = New pr_batch_employee_change With {.empnum = EmployeeEnt.EMPNUM}
                Me.EmployeeChangeLog.Add(ChangeRec)
            Else
                ChangeRec.change_log &= vbCrLf
            End If
            ChangeRec.change_log &= changes
        End If
        Me.Enabled = True
    End Sub

    Private Sub BandedGridView1_CustomDrawCell(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs) Handles BandedGridView1.CustomDrawCell
        Dim Row = Me.BandedGridView1.GetDataRow(e.RowHandle)
        If IsDisposed OrElse e Is Nothing Then Exit Sub
        If Me.ImageList1.Images.Count = 0 Then Exit Sub
        Try
            If e.Column.Name = "collinerate" Then
                If nz(Row("HasMultiRates"), False) Then
                    'Fill background
                    e.Graphics.FillRectangle(Brushes.Yellow, e.Bounds)

                    'Set up some background calculation variables.
                    Dim backgroundRect = e.Bounds
                    backgroundRect.Inflate(1, 1)
                    Dim state As DevExpress.XtraGrid.Views.Base.GridRowCellState = CType(e.Cell, DevExpress.XtraGrid.Views.Grid.ViewInfo.GridCellInfo).State
                    'Draw the selection background if needed.
                    If state = DevExpress.XtraGrid.Views.Base.GridRowCellState.Selected Then
                        e.Graphics.FillRectangle(e.Appearance.GetBackBrush(e.Cache), backgroundRect)
                    End If
                    'Draw the focus rectangle if needed.
                    If state = DevExpress.XtraGrid.Views.Base.GridRowCellState.Focused Then
                        e.Cache.Paint.DrawFocusRectangle(e.Graphics, backgroundRect, e.Appearance.ForeColor, e.Appearance.BackColor)
                    End If
                    'Draw the icon
                    If Me.ImageList1.Images.Count > 0 Then
                        e.Graphics.DrawImage(Me.ImageList1.Images(1), e.Bounds.Location)
                    End If
                    'Draw the cell value
                    Dim R = e.Bounds
                    R.Width -= 18
                    R.X += 18
                    e.Appearance.DrawString(e.Cache, e.DisplayText, R, Brushes.Black)
                    e.Handled = True
                ElseIf nz(Row("linerate"), 0) = 0 AndAlso nz(Row("salary"), 0) = 0 Then
                    e.Graphics.FillRectangle(Brushes.Orange, e.Bounds)
                End If
            ElseIf e.Column.Name = "colNotes" Then
                'changed to image c
                'Dim bounds = e.Bounds
                'bounds.Width = Me.ImageList1.ImageSize.Width
                'Dim HasNotes = nz(Row("HasNotes"), 0)
                'If HasNotes = 1 Or HasNotes = 2 AndAlso Me.ImageList1.Images("Notes") IsNot Nothing Then
                '    e.Graphics.DrawImage(Me.ImageList1.Images("Notes"), bounds)
                'ElseIf HasNotes = 3 AndAlso Me.ImageList1.Images("NotesG") IsNot Nothing Then
                '    e.Graphics.DrawImage(Me.ImageList1.Images("NotesG"), bounds)
                'End If
                'e.Handled = True
            ElseIf e.Column.Name = "colOP" Then
                Dim bounds = e.Bounds
                bounds.Width = Me.ImageList1.ImageSize.Width
                If Row("HasAutos") AndAlso Me.ImageList1.Images("AutoPays") IsNot Nothing Then
                    e.Graphics.DrawImage(Me.ImageList1.Images("AutoPays"), bounds)
                End If
                e.Handled = True
            ElseIf e.Column.Name = "colEdits" Then
                Dim bounds = e.Bounds
                bounds.Width = Me.ImageList1.ImageSize.Width
                Dim HasEdits = nz(Row("chknum"), 0) = 1 AndAlso (From A In Me.EmployeeChangeLog Where A.empnum = Row("EmpNum")).Any
                If HasEdits AndAlso Me.ImageList1.Images("EmpEdit") IsNot Nothing Then
                    e.Graphics.DrawImage(Me.ImageList1.Images("EmpEdit"), bounds)
                End If
                e.Handled = True
            Else
                If e.CellValue IsNot Nothing AndAlso Not IsDBNull(e.CellValue) Then Exit Sub
                Dim wm = GetColumnWatermark(Row, e.Column.FieldName)
                If Not String.IsNullOrEmpty(wm) Then
                    If Row(e.Column.FieldName) & "" = "" Then
                        e.DisplayText = wm
                        If e.Column.FieldName = "df9" Then
                            e.Appearance.ForeColor = Color.Blue
                        Else
                            e.Appearance.ForeColor = Color.Gray
                        End If
                    End If
                End If
            End If
        Catch ex As Exception
            frmLogger.Error(ex, "Error in BandedGridView1_CustomDrawCell")
        End Try
    End Sub

    Private Sub SortGridToDefault()
        Me.BandedGridView1.ClearSorting()
        Dim SortColumns As New List(Of String)

        If Not String.IsNullOrEmpty(Me.CoPayrollOptions.PowerGridDefaultSort) Then
            SortColumns.AddRange(Me.CoPayrollOptions.PowerGridDefaultSort.Split(","))
        Else
            If UDF21.Contains("T09") Then SortColumns.Add("division") '@TopSort
            If UDF21.Contains("T18") Then SortColumns.Add("PAY_FREQ_SORT") '@Sort3



            If UDF21.Contains("T01") Then '@Sort2
                SortColumns.Add("DEPT_DESC")
            ElseIf UDF21.Contains("T11") Then
                If UDF21.Contains("T30") Then
                    SortColumns.Add("EE_DEPTNUM")
                Else
                    SortColumns.Add("department")
                End If
            End If


            'Skipped "T19" based on old employee id, as no longer in use by any 1 @Sort1
            If UDF21.Contains("G01") Then '@Sort1
                SortColumns.Add("USERDEF1")
            ElseIf UDF21.Contains("T12") Then
                SortColumns.Add("empnum")
            Else
                SortColumns.Add("DefaultSort")
            End If
        End If
        If SortColumns.Count = 0 Then SortColumns.Add("DefaultSort")
        SortColumns.Add("chknum")
        Me.TblPayrollData.DefaultView.Sort = String.Join(",", SortColumns)
        Me.GridControl1.DataSource = New BindingSource(TblPayrollData, Nothing)
        Me.SetupDisplayCount()
    End Sub

    Function GetFrequencySort(ByVal PAY_FREQ As String) As Integer
        Select Case PAY_FREQ
            Case "WEEKLY"
                Return 1
            Case "BI-WEEKLY"
                Return 2
            Case "SEMI-MONTHLY"
                Return 3
            Case "MONTHLY"
                Return 4
            Case "QUARTERLY"
                Return 5
            Case "ANNUALLY"
                Return 6
            Case Else
                Return 9
        End Select
    End Function

    Function GetUserDef1Sort(ByVal EmpRow As EMPLOYEE) As String
        Dim R = (EmpRow.USERDEF1 & "").PadRight(5) & GetEmpName(EmpRow) & EmpRow.EMPNUM.ToString.PadLeft(5)
        Return R
    End Function

    Function GetEmpName(ByVal EmpRow As EMPLOYEE) As String
        If String.IsNullOrEmpty(EmpRow.L_NAME) Then
            Return EmpRow.F_NAME & " " & EmpRow.M_NAME
        ElseIf String.IsNullOrEmpty(EmpRow.F_NAME) Then
            Return EmpRow.L_NAME
        Else
            Return EmpRow.L_NAME & ", " & EmpRow.F_NAME & " " & EmpRow.M_NAME
        End If
    End Function

    Sub SetupDisplayCount()
        Dim IsFiltered = Me.BandedGridView1.ActiveFilterCriteria IsNot Nothing
        Me.pnlHiddenCount.Visible = IsFiltered
        Me.pnlVisibleRecords.Visible = IsFiltered
        Dim AllCount = Me.TblPayrollData.Rows.Count
        Dim VisibleCount = Me.BandedGridView1.RowCount
        Dim HiddenCount = AllCount - VisibleCount
        Me.lblAllRecCount.Text = String.Format("{0} line{1}", AllCount, If(AllCount = 1, "", "s"))
        Me.lblVisibleRecCount.Text = String.Format("{0} line{1}", VisibleCount, If(VisibleCount = 1, "", "s"))
        Me.lblHiddenRecCount.Text = String.Format("{0} line{1}", HiddenCount, If(HiddenCount = 1, "", "s"))
    End Sub

    Private Sub BandedGridView1_ColumnFilterChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BandedGridView1.ColumnFilterChanged
        SetupDisplayCount()
    End Sub

    Private Sub riMasterRate_Validating(ByVal sender As Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles riMasterRate.Validating
        Dim Is1099 = Me.BandedGridView1.GetRowCellValue(Me.BandedGridView1.FocusedRowHandle, "name").ToString.EndsWith("(1099)") AndAlso Not PayFieldFor1099 = "pp0"
        If Is1099 Then Exit Sub
        Dim Editor = Me.BandedGridView1.ActiveEditor
        Dim NewValue = Editor.EditValue
        Dim OldValue = Editor.OldEditValue
        Dim PayCode As Decimal = 0
        If Me.BandedGridView1.FocusedColumn.FieldName <> "linerate" Then
            PayCode = Me.BandedGridView1.FocusedColumn.FieldName.Substring(2)
        End If
        If PayCode = 99 Then
            If Not HasP99.ContainsKey(Me.BandedGridView1.GetRowCellValue(Me.BandedGridView1.FocusedRowHandle, "empnum")) Then
                Exit Sub
            End If
        End If
        If Not IsDBNull(NewValue) AndAlso Not String.IsNullOrEmpty(NewValue) Then
            Dim EmpNum As Integer = Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle)("empnum")
            Dim empRow = (From A In Me.EmployeeList Where A.EMPNUM = EmpNum).SingleOrDefault
            Dim frm = New frmChangeRate With {.EmpRow = empRow}
            frm.NewRate.EditValue = NewValue
            frm.OldRate = nz(OldValue, Nothing)
            frm.PayCode = PayCode
            Dim Results = frm.ShowDialog
            frm.Dispose()
        End If
    End Sub

    Private Sub riSalaryAmt_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles riSalaryAmt.Validating
        Dim Is1099 = Me.BandedGridView1.GetRowCellValue(Me.BandedGridView1.FocusedRowHandle, "name").ToString.EndsWith("(1099)") AndAlso Not PayFieldFor1099 = "pp0"
        If Is1099 Then Exit Sub
        Dim wm = GetColumnWatermark(Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle), Me.BandedGridView1.FocusedColumn.FieldName)
        If Not String.IsNullOrEmpty(wm) AndAlso (wm.Contains("Parsonage")) Then
            Exit Sub
        End If
        Dim NewValue = Me.BandedGridView1.ActiveEditor.EditValue
        If Not IsDBNull(NewValue) AndAlso Not String.IsNullOrEmpty(NewValue) Then
            Dim EmpNum As Integer = Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle)("empnum")
            Dim empRow = (From A In Me.EmployeeList Where A.EMPNUM = EmpNum).SingleOrDefault
            Dim frm = New frmChangeRate With {.EmpRow = empRow, .IsSalary = True}
            frm.NewRate.EditValue = NewValue
            Dim Results = frm.ShowDialog
            frm.Dispose()
        End If
    End Sub

    Private Sub BandedGridView1_ValidatingEditor(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs) Handles BandedGridView1.ValidatingEditor
        'Convert to null
        If Not IsDBNull(e.Value) AndAlso TypeOf e.Value Is String AndAlso e.Value = "" Then
            e.Value = DBNull.Value
        ElseIf Me.BandedGridView1.FocusedColumn.FieldName = "pay" Then
            Dim OldValue As Boolean = Me.BandedGridView1.ActiveEditor.OldEditValue
            Dim NewValue As Boolean = e.Value
            If OldValue AndAlso Not NewValue Then
                Dim Row = Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle)
                Dim EmpNum As Decimal = Row("EmpNum")
                Dim CheckCounter As Decimal = Row("chknum")
                Dim nDB As New dbEPDataDataContext(GetConnectionString)
                Dim RelatedOverrideAutos = (From A In nDB.pr_batch_override_autos Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum AndAlso A.EMPNUM = EmpNum AndAlso A.CHK_COUNTER = CheckCounter).ToList
                Dim RelatedOverrides = (From A In nDB.pr_batch_overrides Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum AndAlso A.EMPNUM = EmpNum AndAlso A.CHK_COUNTER = CheckCounter).ToList
                If RelatedOverrideAutos.Count > 0 OrElse RelatedOverrides.Count > 0 Then
                    If DevExpress.XtraEditors.XtraMessageBox.Show("This action will delete the overrides entered for this check. Continue?", "Confirm Delete Overrides", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.No Then
                        e.Value = True
                    Else
                        nDB.pr_batch_override_autos.DeleteAllOnSubmit(RelatedOverrideAutos)
                        nDB.pr_batch_overrides.DeleteAllOnSubmit(RelatedOverrides)
                        nDB.SubmitChanges()
                    End If
                End If
                nDB.Dispose()
            End If
        ElseIf Me.bciInputIsMinutes.Checked AndAlso Not IsDBNull(e.Value) Then
            Dim HCols = (From A In AvailCols Where A.ColumnName = "Reg Hours" OrElse A.ColumnName = "OT Hours" Select A.sql_column).ToList
            If HCols.Contains(Me.BandedGridView1.FocusedColumn.FieldName) Then
                Dim d As Decimal
                If Decimal.TryParse(nz(e.Value, ""), d) Then
                    d = Math.Abs(d)
                    Dim F = d - Decimal.Floor(d)
                    If F > 0.59 Then
                        e.Valid = False
                        e.ErrorText = "Minutes are too much"
                    End If
                End If
            End If
        End If
    End Sub

    Private Sub CancelGrid()
        Try
            DB = New dbEPDataDataContext(GetConnectionString)
            Dim i = (From BT In DB.pr_batch_totals
                     Join S In DB.pr_batch_grid_schemas On BT.schema_id Equals S.schema_id
                     Where BT.conum = Me.CoNum AndAlso S.name = $"{GridSrc}" AndAlso {"In Progress", "Completed"}.Contains(BT.status)
                     Select BT).Count

            If i > 0 Then
                ShowList(True)
            Else

                If MessageBox.Show("Would You Like To Delete This Payroll ?", "Delete Payroll", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.Yes Then
                    If (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum).Count > 0 Then
                        MessageBox.Show("Payroll Contains checks, See administrator.")
                        ShowList(False)
                    Else
                        Dim del = (From d In DB.PR_DELIVERies Where d.PRNUM = PrNum AndAlso d.CONUM = CoNum).ToList
                        For Each d In del
                            DB.PR_DELIVERies.DeleteOnSubmit(d)
                        Next

                        Dim calList = (From c In DB.CALENDARs Where c.conum = CoNum AndAlso c.payroll_num = PrNum).ToList
                        For Each c In calList
                            c.payroll_num = Nothing
                        Next

                        Dim CalWasCompleted = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = PrNum AndAlso A.EmployeeID = -998).ToList
                        If CalWasCompleted.Count > 0 Then
                            ResetCalendar()

                            DB.pr_batch_notes.DeleteAllOnSubmit(CalWasCompleted)
                            DB.SubmitChanges()
                        End If

                        Dim pay = (From p In DB.PAYROLLs Where p.CONUM = CoNum AndAlso p.PRNUM = PrNum).Single
                        Dim pay_ext = pay.payroll_ext_ts
                        If pay_ext IsNot Nothing Then
                            DB.payroll_ext_ts.DeleteOnSubmit(pay_ext)
                        End If
                        DB.PAYROLLs.DeleteOnSubmit(pay)
                        DB.SubmitChanges()
                    End If
                End If
                Me.Close()
                Me.Dispose()

                If CallBackForm IsNot Nothing Then
                    If CallBackForm.GetType() = GetType(frmCalendar) Then
                        DirectCast(CallBackForm, frmCalendar).LoadData(True)
                    End If
                    MainForm.ActivateForm(CallBackForm)
                    'MainForm.PageManager.SelectedPage = MainForm.PageManager.Pages(CallBackForm)
                End If

            End If
        Catch ex As Exception
            DisplayErrorMessage("Error on trying to delete payroll", ex)
            ShowList(False)
        End Try
    End Sub

    Private Sub ResetCalendar()
        Dim CalWasCompleted = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = PrNum AndAlso A.EmployeeID = -998).ToList
        If CalWasCompleted.Count > 0 Then
            For Each note In CalWasCompleted
                Dim Values = System.Text.RegularExpressions.Regex.Matches(note.Note, ".*?<(.*?):(.*?)\>.*?", System.Text.RegularExpressions.RegexOptions.Compiled)
                Dim CalID As Integer? = Nothing, PrNum As Decimal? = Nothing, CompletedDate As Date? = Nothing
                Dim v As String
                For x = 0 To Values.Count - 1
                    Select Case Values(x).Groups(1).Value
                        Case "cal_id"
                            CalID = Values(x).Groups(2).Value
                        Case "payroll_num"
                            PrNum = Values(x).Groups(2).Value
                        Case "completed_date"
                            v = Values(x).Groups(2).Value
                            If v.IsNotNullOrWhiteSpace Then
                                CompletedDate = v
                            End If
                    End Select
                Next
                Dim CalEnt = (From A In DB.CALENDARs Where A.cal_id = CalID).SingleOrDefault
                If CalEnt IsNot Nothing Then
                    CalEnt.payroll_num = PrNum
                    CalEnt.completed = "YES"
                    CalEnt.completed_date = CompletedDate
                End If
            Next
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save calendar changes due to a conflict with another user's changes. Please refresh and try again.", New Exception("Concurrency conflict"))
                Return
            End If
        End If
    End Sub

    Private Sub MarkGridComplete()
        Save()
        DB = New dbEPDataDataContext(GetConnectionString)
        BatchList = (From A In DB.pr_batch_lists Where A.id = BatchList.id).Single
        BatchList.status = "Completed"
        If DB.SaveChanges() Then
            ShowList(True)
        End If
    End Sub

    Sub ShowList(ByVal Refresh As Boolean)
        Me.DatesAndOptions.Show()
        If Refresh Then
            Me.DatesAndOptions.LoadData(True)
        End If
        Me.Hide()
    End Sub

    Private Sub DisplayLog(ByVal sender As Object, ByVal e As QueuePayrollProcessor.ActionLogTypeEventArgs)
        If ProcessingStatusForm IsNot Nothing Then ProcessingStatusForm.AddMessage(e)
    End Sub

    Private Sub SetProcessStatus(ByVal ActionNumber As Decimal, ByVal Status As String)
        If Not IsAuto Then
            ProcessingStatusForm.SetStatus(ActionNumber, Status)
        End If
    End Sub

    Private Sub DisplayLog(ByVal sender As Object, ByVal e As ActionLogTypeEventArgs)
        If ProcessingStatusForm IsNot Nothing Then ProcessingStatusForm.AddMessage(e)
    End Sub

    Private Function GetPayRec() As pr_batch_in_process
        Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = Me.PrNum AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
        'Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = API.PayrollID AndAlso A.IsDeleted.GetValueOrDefault = False).FirstOrDefault
        If PayRec Is Nothing Then
            PayRec = New pr_batch_in_process With {.CoNum = CoNum,
                                                   .PrNum = Me.PrNum,
                                                   .ProcessedDate = Now,
                                                   .ProcessedBy = UserName,
                                                   .rowguid = Guid.NewGuid,
                                                   .CalendarID = Me.CalanderID,
                                                   .ProcessStatus = "Ready"}
            DB.pr_batch_in_processes.InsertOnSubmit(PayRec)
        ElseIf Me.IsReadOnly Then
            PayRec.ResubmittedFromStuckInProcess = PayRec.ResubmittedFromStuckInProcess.GetValueOrDefault + 1
        End If
        PayRec.PowerGridID = BatchList.id
        PayRec.CalendarID = Me.CalanderID
        DB.SubmitChanges()

        Return PayRec
    End Function

    Private Sub BandedGridView1_CellValueChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles BandedGridView1.CellValueChanged
        Dim row = Me.BandedGridView1.GetDataRow(e.RowHandle)
        If Not IsDBNull(e.Value) AndAlso Not e.Value Is Nothing AndAlso {"reghrs", "othrs", "salary", "net", "ficaonly", "df9"}.Contains(e.Column.FieldName.ToLower) Then
            If e.Column.FieldName.ToLower = "net" Then
                If Not _PayrollEnt.PR_DESCR = "Bonus Run" Then
                    If IsDBNull(Me.BandedGridView1.GetRowCellValue(e.RowHandle, "salary")) Then
                        Me.BandedGridView1.SetRowCellValue(e.RowHandle, "salary", CalculateFicaAdd(e.Value))
                    End If
                Else
                    If IsDBNull(Me.BandedGridView1.GetRowCellValue(e.RowHandle, PayFieldForBonus)) Then
                        row(PayFieldForBonus) = CalculateFicaAdd(e.Value)
                    End If
                    'Me.BandedGridView1.SetRowCellValue(e.RowHandle, PayFieldForBonus, CalculateFicaAdd(e.Value))
                End If
            End If
            Dim IsParsonage = (row("USERDEF21") & "").ToString.ToUpper.Contains("P30") OrElse
                (CoPayrollOptions.ParsonageAll AndAlso
                 (CoPayrollOptions.ParsonageDivisions Is Nothing OrElse
                  (From A In CoPayrollOptions.ParsonageDivisions.Split(",") Select CDec(A)).Contains(row("division"))
                  )
              )
            If IsParsonage AndAlso {"reghrs", "othrs", "salary"}.Contains(e.Column.FieldName.ToLower) Then
                Select Case e.Column.FieldName
                    Case "reghrs"
                        Me.BandedGridView1.SetRowCellValue(e.RowHandle, "ph30", e.Value)
                    Case "othrs"
                        Me.BandedGridView1.SetRowCellValue(e.RowHandle, "po30", e.Value)
                    Case "salary"
                        Me.BandedGridView1.SetRowCellValue(e.RowHandle, "pp30", e.Value)
                End Select
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, DBNull.Value)
            End If

            If (row("name") & "").EndsWith("(1099)") AndAlso
                    (row("last") & "").EndsWith("(1099)") AndAlso
                    e.Column.FieldName.ToLower = "salary" AndAlso Not PayFieldFor1099 = "pp0" Then

                Me.BandedGridView1.SetRowCellValue(e.RowHandle, PayFieldFor1099, e.Value)
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, DBNull.Value)
                MakeColVisible(PayFieldFor1099, False)
            End If

            If Not (row("name") & "").EndsWith("(1099)") AndAlso e.Column.FieldName.ToLower = "df9" Then
                DisplayMessageBox("D9 is only allowed for 1099 employee")
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, DBNull.Value)
            End If

            If e.Column.FieldName.ToLower = "othrs" AndAlso HasP99.ContainsKey(row("empnum")) Then
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, "ph99", e.Value)
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, DBNull.Value)
            End If

            If e.Column.FieldName.ToLower = "reghrs" AndAlso Me.bciAutoOvertime.Checked Then
                Dim RegHrs As Decimal
                If CoPayrollOptions.AutoOvertimeAfterHours.GetValueOrDefault <> 0 Then
                    RegHrs = CoPayrollOptions.AutoOvertimeAfterHours
                Else
                    RegHrs = CalculateDefaultHours(row("PAY_FREQ"))
                End If
                If e.Value > RegHrs Then
                    Dim OTHrs = e.Value - RegHrs
                    Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, RegHrs)
                    Me.BandedGridView1.SetRowCellValue(e.RowHandle, "othrs", OTHrs)
                End If
            End If

            If e.Column.FieldName = "FicaOnly" Then
                row("FedOverride") = If(e.Value = True, 0.0, DBNull.Value)
                row("STOverride") = If(e.Value = True, 0.0, DBNull.Value)
                row("LOCOverride") = If(e.Value = True, 0.0, DBNull.Value)
                row("DBOverride") = If(e.Value = True, 0.0, DBNull.Value)
                row("FLIOverride") = If(e.Value = True, 0.0, DBNull.Value)
                Me.BandedGridView1.RefreshRow(e.RowHandle)
            End If
        ElseIf Not IsDBNull(e.Value) AndAlso e.Column.FieldName = PayFieldForBonus AndAlso Me.bciShowNetColumn.Checked Then
            If MessageBox.Show("Dou you want to use the net column instead?", "Use Net?", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, DBNull.Value)
                Me.BandedGridView1.SetRowCellValue(e.RowHandle, "Net", e.Value)
            End If

        ElseIf e.Column.FieldName = "ChkType" Then
            If e.Value & "" = "" Then
                If Not row("IsSecondCheck") Then
                    Me.BandedGridView1.SetRowCellValue(e.RowHandle, "OASDIOverride", DBNull.Value)
                    Me.BandedGridView1.SetRowCellValue(e.RowHandle, "MedicareOverride", DBNull.Value)
                    Me.BandedGridView1.SetRowCellValue(e.RowHandle, "ChkNumber", DBNull.Value)
                End If
            Else
                If row("HasAutos") Then
                    UpdateAutoPay(row, "-None-", "All0")
                End If
            End If
        ElseIf Not IsDBNull(e.Value) AndAlso Not e.Value Is Nothing Then
            Dim wm = GetColumnWatermark(row, e.Column.FieldName)
            If Not String.IsNullOrEmpty(wm) AndAlso (wm.StartsWith("Auto")) Then
                Dim Dec As Decimal
                Decimal.TryParse(nz(wm.Replace("Auto-", ""), ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, Dec)
                If Dec <> 0 AndAlso Not Dec = e.Value Then
                    Dim CheckRows = TblPayrollData.Select(String.Format("empnum={0} AND chknum={1}", row("empnum"), row("chknum")))
                    If Array.IndexOf(CheckRows, row) > 0 Then
                        DisplayMessageBox("You are adding an additional amount to an Auto Item. If you would like to edit the auto item then double click in this cell")
                    Else
                        Me.BandedGridView1.SetRowCellValue(e.RowHandle, e.Column, Nothing)
                        UpdateAutoPay(row, e.Column.FieldName, e.Value)
                    End If
                End If
            End If
        End If
    End Sub

    Private Sub GridControl1_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles GridControl1.MouseDoubleClick
        Dim hi = Me.BandedGridView1.CalcHitInfo(e.Location)
        If hi.RowHandle < 0 Then Return
        If hi.Column Is Nothing Then Return
        Dim Row = Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle)
        If hi.Column.Name = "colNotes" Then
            Dim frm = New frmEmployeeNotes
            Dim EmpNum As Decimal = Row("empnum")
            Dim CheckNum As Integer = Row("chknum")
            'frm.EmpNotes = (From A In NotesData Where A.empnum = EmpNum).ToList
            frm.EmployeeID = EmpNum
            frm.CoNum = Me.CoNum
            If EP_API() IsNot Nothing Then
                frm.PrNum = Me.PrNum
            End If
            frm.CheckNum = CheckNum
            frm.ListID = Me.BatchList.id
            Dim results = frm.ShowDialog()
            If results = System.Windows.Forms.DialogResult.OK Then
                Dim HasNotes = frm.LineNotes.Count > 0 OrElse frm.EmpNotes.Count > 0
                Row("HasNotes") = If(HasNotes, 2, 0)
                Me.BandedGridView1.SetRowCellValue(Me.BandedGridView1.FocusedRowHandle, "hasNotes", If(HasNotes, 2, 0))
                Me.BandedGridView1.UpdateCurrentRow()
                Me.BandedGridView1.RefreshRow(Me.BandedGridView1.FocusedRowHandle)
            End If
            frm.Dispose()
        ElseIf hi.Column.Name = "colOP" Then
            If Row("HasAutos") Then
                Dim frm = New frmEmployeeAutoPaysAndDeds With {.CoNum = CoNum,
                                                               .EmployeeNum = Row("empnum"),
                                                               .PayrollNum = Me.PrNum,
                                                               .CheckCounter = Row("chknum"),
                                                               .PowerGridForm = Me}
                Dim results = frm.ShowDialog()
                frm.Dispose()
            End If
        Else
            Dim wm = GetColumnWatermark(Row, hi.Column.FieldName)
            If Not String.IsNullOrEmpty(wm) AndAlso wm.StartsWith("Auto") Then
                UpdateAutoPay(Row, hi.Column.FieldName)
            End If
        End If
    End Sub

    Private Sub chkConvertHours_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim Chk As BarCheckItem = sender
        Chk.ItemAppearance.Normal.BackColor = If(Chk.Checked, Color.Orange, Color.Transparent)
    End Sub

    Private Sub BandedGridView1_ValidateRow(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles BandedGridView1.ValidateRow
        Dim CurrentRow As DataRowView = e.Row
        Dim AllHoursColumns = (From A In AvailCols Where A.code >= 0 AndAlso A.ColumnName.Contains("Hours") Select A.sql_column).ToList
        Dim TotalHours As Decimal
        For Each hrsColumn In AllHoursColumns
            TotalHours += nz(CurrentRow(hrsColumn), 0)
        Next
        Dim DefaultHours = CalculateDefaultHours(CurrentRow("PAY_FREQ"))
        If TotalHours > DefaultHours * 2 Then
            e.Valid = MessageBox.Show("Total hours for this line seems to be too high. Continue anyway?.", "Warning",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes
        End If

        If nz(CurrentRow("rate"), 0) = 0 AndAlso nz(CurrentRow("linerate"), 0) = 0 AndAlso nz(CurrentRow("ratenum"), 0) = 0 AndAlso nz(CurrentRow("salary"), 0) = 0 AndAlso nz(CurrentRow("reghrs"), 0) + nz(CurrentRow("othrs"), 0) > 0 Then
            e.Valid = MessageBox.Show("This line has hours but no rate and no salary. Continue anyway?", "Warning",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes
        End If

        If Not IsDBNull(CurrentRow("OASDIOverride")) OrElse Not IsDBNull(CurrentRow("MedicareOverride")) Then
            Dim Gross As Decimal
            Dim Groups = (From A In AvailCols Where {"P", "D", "M"}.Contains(A.type)
                          Group By A.GroupName, A.type, A.code Into Group
                          Select GroupName, type, code, Columns = Group.ToList).ToList

            For Each G In Groups
                Dim Rate As Decimal = 0
                If G.type = "P" Then
                    If G.code = 0 Then
                        Rate = nz(CurrentRow("rate"), 0)
                        If Rate = 0 Then Rate = nz(CurrentRow("linerate"), 0)
                        If Not bciInputIsMinutes.Checked Then
                            Gross += (nz(CurrentRow("reghrs"), 0) * Rate) + (nz(CurrentRow("othrs"), 0) * (Rate * 1.5))
                        Else
                            Gross += (ConvertHoursToDecimal(nz(CurrentRow("reghrs"), 0)) * Rate) + (ConvertHoursToDecimal(nz(CurrentRow("othrs"), 0)) * (Rate * 1.5))
                        End If
                        Gross += nz(CurrentRow("salary"), 0)
                    Else
                        Rate = nz(CurrentRow("pr" & G.code), 0)
                        If Not bciInputIsMinutes.Checked Then
                            Gross += nz(CurrentRow("ph" & G.code), 0) * Rate
                            Gross += nz(CurrentRow("po" & G.code), 0) * (Rate * 1.5)
                        Else
                            Gross += ConvertHoursToDecimal(nz(CurrentRow("ph" & G.code), 0)) * Rate
                            Gross += ConvertHoursToDecimal(nz(CurrentRow("po" & G.code), 0)) * (Rate * 1.5)
                        End If
                        Gross += nz(CurrentRow("pp" & G.code), 0)
                    End If
                    'Else
                    '    Gross += nz(CurrentRow(G.type & "f" & G.code), 0)
                End If
            Next 'Pay Code
            Dim Msg As String = ""
            If nz(CurrentRow("OASDIOverride"), 0) <> 0 Then
                Dim Rate = If(CurrentRow("ChkType") = "SICK", My.Settings.OASDI_ER_Rate, My.Settings.OASDI_Rate)
                If Math.Abs((Gross * Rate) - CurrentRow("OASDIOverride")) > 0.05 Then
                    'Msg = "OASDI Override amount does Not match the OASDI Rate " & vbCrLf & vbTab & String.Format("(Estimated Gross:  {0} * Rate: {1} = {2}. Diff({3}) )", Gross.ToString("N2"), Rate, (Gross * Rate).ToString("N2"), Format((Gross * Rate) - CurrentRow("OASDIOverride"), "0.00").ToString) & vbCrLf & vbCrLf
                    Msg = String.Format("You entered an OASDI amount that does not match the OASDI Rate,{4}Difference is {3}{4}{5}(Estimated Gross: {0} * Rate: {1} = {2}){4}{4}", Gross.ToString("N2"), Rate, (Gross * Rate).ToString("N2"), Format((Gross * Rate) - CurrentRow("OASDIOverride"), "0.00").ToString, vbCrLf, vbTab)
                End If
            End If
            If nz(CurrentRow("MedicareOverride"), 0) <> 0 Then
                Dim Rate = If(CurrentRow("ChkType") = "SICK", My.Settings.Medicare_ER_Rate, My.Settings.Medicare_Rate)
                If Math.Abs((Gross * Rate) - CurrentRow("MedicareOverride")) > 0.05 Then
                    'Msg &= "Medicare Override amount does not match the Medicare Rate " & vbCrLf & vbTab & String.Format("(Estimated Gross: {0} * Rate: {1} = {2}. Diff({3}) )", Gross.ToString("N2"), Rate, (Gross * Rate).ToString("N2"), Format((Gross * Rate) - CurrentRow("MedicareOverride"), "0.00").ToString) & vbCrLf & vbCrLf
                    Msg &= String.Format("You entered a Medicare amount that does not match the Medicare Rate,{4}Difference is {3}{4}{5}(Estimated Gross: {0} * Rate: {1} = {2}){4}{4}", Gross.ToString("N2"), Rate, (Gross * Rate).ToString("N2"), Format((Gross * Rate) - CurrentRow("MedicareOverride"), "0.00").ToString, vbCrLf, vbTab)
                End If
            End If
            If Not IsDBNull(CurrentRow("DBOverride")) Then
                Dim MaxDB = NYMaxDB(CurrentRow("PAY_FREQ"))
                If nz(CurrentRow("UCIState"), "") = "NY" AndAlso CurrentRow("DBOverride") > MaxDB Then
                    Msg &= "DB Override for NY exceeds the maximum of " & MaxDB & " per " & CurrentRow("PAY_FREQ") & vbCrLf & vbCrLf
                End If
            End If
            If Not String.IsNullOrEmpty(Msg) Then
                e.Valid = MessageBox.Show(Msg & "Continue anyway?", "Warning",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes
            End If

            If CurrentRow("ChkType") & "" <> "" AndAlso CurrentRow("ChkNumber") & "" = "" Then
                DisplayMessageBox("Manual check requires a check number.")
                e.Valid = False
            End If
        End If
    End Sub

    Private Sub BandedGridView1_InvalidRowException(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs) Handles BandedGridView1.InvalidRowException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction
    End Sub

    Private Sub GridViewCalendarInfo_CustomDrawColumnHeader(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.ColumnHeaderCustomDrawEventArgs) Handles GridViewCalendarInfo.CustomDrawColumnHeader
        e.Appearance.DrawString(e.Cache, e.Info.Caption, e.Info.CaptionRect)
        e.Handled = True
    End Sub

    Private Sub BandedGridView1_RowStyle(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs) Handles BandedGridView1.RowStyle
        If e.RowHandle < 0 Then Exit Sub
        'Exit Sub
        Dim row = Me.BandedGridView1.GetDataRow(e.RowHandle)
        If Not IsDBNull(row("TermDate")) Then
            e.Appearance.ForeColor = Color.Red
            e.HighPriority = True
        End If
    End Sub

    Private Sub BandedGridView1_CustomDrawFooterCell(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.FooterCellCustomDrawEventArgs) Handles BandedGridView1.CustomDrawFooterCell
        Try
            If e.Column.FieldName = "pay" Then
                Dim PayCount = TblPayrollData.Select("pay = True").Count
                e.Info.DisplayText = PayCount
            ElseIf e.Column.FieldName = "chknum" Then
                Dim Checks As New List(Of String)
                For RowIX = 0 To Me.BandedGridView1.RowCount - 1
                    Dim Row = Me.BandedGridView1.GetDataRow(RowIX)
                    If Not Row("pay") Then Continue For
                    Dim Key = Row("empnum") & "-" & Row("chknum")
                    If Checks.Contains(Key) Then Continue For
                    Dim Groups = (From A In AvailCols Where {"P", "D", "M"}.Contains(A.type) AndAlso Not (A.type = "P" AndAlso A.code = 0)
                                  Order By A.GroupOrder, A.code
                                  Group By A.GroupName, A.type, A.code Into Group
                                  Select GroupName, type, code, Columns = Group.ToList).ToList

                    Dim HasData As Boolean = False
                    Dim Data = nz(Row("reghrs"), "") & nz(Row("othrs"), "") & nz(Row("salary"), "")
                    If Data <> "" Then
                        HasData = True
                    Else
                        'Check other codes
                        For Each G In Groups
                            For Each Col In G.Columns
                                If Not IsDBNull(Row(Col.sql_column)) Then
                                    HasData = True
                                    Exit For
                                End If
                            Next
                        Next 'Pay Code
                    End If
                    If HasData Then
                        Checks.Add(Key)
                    End If
                Next 'Grid Rows
                e.Info.DisplayText = Checks.Count
            ElseIf {"Reg Hours", "OT Hours", "Amount"}.Contains(e.Column.Caption) Then
                Dim Sum = TblPayrollData.Compute("SUM(" & e.Column.FieldName & ")", "pay = True")
                If Not IsDBNull(Sum) Then
                    e.Info.DisplayText = CType(Sum, Decimal).ToString("N2")
                End If
            Else
                Exit Sub
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in BrandsPowerGrid", ex)
        End Try
    End Sub

    Private Sub ShowNetCol_CheckedChanged()
        Me.BandedGridView1.Columns("Net").Visible = bciShowNetColumn.Checked
    End Sub

    Private Sub ShowtaxOverridesChanged()
        Dim band = Me.BandedGridView1.Bands("bandTaxOverrides")
        band.Visible = bciShowTaxOverrideColumns.Checked
        If band.Visible Then
            For Each col As DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn In band.Columns
                col.Visible = True
            Next
        End If
        'pnlTaxOverrides.Visible = bciShowTaxOverrideColumns.Checked
        Me.LayoutControlItemTaxOverrides.ContentVisible = bciShowTaxOverrideColumns.Checked
    End Sub

    Function ActivatePayDed(Type As String, Code As Decimal) As Boolean
        Dim db2 = New dbEPDataDataContext(GetConnectionString)
        If Type.ToLower = "p" Then
            Dim OP = (From A In db2.OTHER_PAYS
                      Where A.CONUM = CoNum AndAlso (A.OACTIVE = "NO" OrElse A.ops_webshow = "NO") AndAlso A.OTH_PAY_NUM = Code).FirstOrDefault
            If OP Is Nothing Then Return False
            OP.OACTIVE = "YES"
            OP.ops_webshow = "YES"
        ElseIf Type.ToLower = "d" Then
            Dim D = (From A In db2.DEDUCTIONs
                     Where A.CONUM = CoNum AndAlso (A.DACTIVE = "NO" OrElse A.deds_webshow = "NO") AndAlso A.DED_NUM = Code).FirstOrDefault
            If D Is Nothing Then Return False
            D.DACTIVE = "YES"
            D.deds_webshow = "YES"
        End If
        db2.SaveChanges()
        Return True
    End Function

    Sub MakeColVisible(ByVal FieldName As String, ByVal IsPermenent As Boolean)
        Dim Col = Me.BandedGridView1.Columns(FieldName)
        If Col Is Nothing Then
            Dim ChangedData As DataTable = Nothing
            If TblPayrollData IsNot Nothing Then ChangedData = TblPayrollData.GetChanges
            If Not Me.IsAuto Then DisplayMessageBox("Column " & FieldName & " is not displayed because it's not active.")

            'problem is that if we activate new column, then whole table is re-created, and all data will be lost.
            If ChangedData Is Nothing Then
                If IsAuto Then
                    Dim Rec = (From A In AvailCols Where A.sql_column = FieldName).Single
                    If ActivatePayDed(Rec.type, Rec.code) Then
                        'Load Columns
                        DB = New dbEPDataDataContext(GetConnectionString)
                        LoadLayout(DB)

                        'Add new columns to data table
                        CreatePayrollDataTable(False)

                        'Move it to the correct position
                        If Rec.type.ToLower = "p" Then
                            MoveGroupToCorrectPosition("P", Rec.code)
                        ElseIf Rec.type.ToLower = "d" Then
                            MoveGroupToCorrectPosition("D", Rec.code)
                            SetupGrid()
                        End If
                    End If
                Else
                    btnActivatePayDeds_Click(Me, New EventArgs)
                End If
                Col = Me.BandedGridView1.Columns(FieldName)
                If Col Is Nothing Then
                    DisplayMessageBox("Column " & FieldName & " is not displayed because it's not active.")
                    Exit Sub
                End If
            End If
        End If
        If Col Is Nothing Then Exit Sub
        If Not Col.OwnerBand.Visible Then
            If 1 = 2 Then ' Not IsPermenent Then
                Col.OwnerBand.Visible = True
                If Col.OwnerBand.Tag IsNot Nothing Then
                    If Col.OwnerBand.VisibleIndex >= Me.BandedGridView1.Bands("bandOthers").VisibleIndex Then
                        Me.BandedGridView1.Bands.MoveTo(Me.BandedGridView1.Bands("bandOthers").VisibleIndex, Col.OwnerBand)
                    End If
                End If
            Else
                Dim Rec1 = (From A In AvailCols Where A.sql_column = FieldName).Single
                Rec1.Show = True
                Dim Rec = (From A In _SColumns Where A.sql_column = FieldName).Single
                Rec.hide = False.ToYesNoString
                MoveGroupToCorrectPosition(Rec.type, Rec.code)
                HasLayoutChanges = HasLayoutChanges OrElse IsPermenent
                SetupGrid()
            End If
        End If
        Col.Visible = True
    End Sub

    Private Sub chkFicaOnly_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkFicaOnly.CheckedChanged
        Me.txtFedOverride.EditValue = If(chkFicaOnly.Checked, "0.0", Nothing)
        Me.txtSTOverride.EditValue = If(chkFicaOnly.Checked, "0.0", Nothing)
        Me.txtLOCOverride.EditValue = If(chkFicaOnly.Checked, "0.0", Nothing)
        Me.txtDBOverride.EditValue = If(chkFicaOnly.Checked, "0.0", Nothing)
        Me.txtFLIOverride.EditValue = If(chkFicaOnly.Checked, "0.0", Nothing)
        Me.txtFedOverride.DoValidate()
        Me.txtSTOverride.DoValidate()
        Me.txtLOCOverride.DoValidate()
        Me.txtDBOverride.DoValidate()
        Me.txtFLIOverride.DoValidate()
        Me.txtFedOverride_Validated(Nothing, e)
    End Sub

    Dim OverrideExisting As Boolean?
    Private Sub txtFedOverride_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSTOverride.Validated, txtLOCOverride.Validated, txtFedOverride.Validated, txtDBOverride.Validated, txtWorkDate.Validated, txtFLIOverride.Validated
        Dim TextEdit As DevExpress.XtraEditors.TextEdit = sender
        Dim FieldNames = New List(Of String)
        Dim ExistingData As Integer
        If TextEdit IsNot Nothing Then
            FieldNames.Add(TextEdit.Name.Substring(3))
        Else
            FieldNames.Add("FedOverride")
            FieldNames.Add("STOverride")
            FieldNames.Add("LOCOverride")
            FieldNames.Add("DBOverride")
            FieldNames.Add("FLIOverride")
        End If
        ExistingData = TblPayrollData.Select(String.Join(" OR ", FieldNames.Select(Function(n) $"{n} IS NOT NULL"))).Count
        'If Not OverrideExisting.HasValue Then
        If ExistingData > 0 Then
            Dim results = MessageBox.Show("Some rows already contains tax overrides. Do you want to replace them?", "Override Existing?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Stop)
            If results = System.Windows.Forms.DialogResult.Yes Then
                OverrideExisting = True
            ElseIf results = System.Windows.Forms.DialogResult.No Then
                OverrideExisting = False
            Else
                Return
            End If
        End If
        'End If
        For Each row As DataRow In TblPayrollData.Rows
            For Each FieldName In FieldNames
                If OverrideExisting.GetValueOrDefault = False Then
                    If Not IsDBNull(row(FieldName, DataRowVersion.Current)) Then
                        Continue For
                    End If
                End If
                Dim control As TextEdit = Controls.Find($"txt{FieldName}", True).Single
                row(FieldName) = If(control.HasValue, control.EditValue, DBNull.Value)
            Next
        Next
        Me.BandedGridView1.RefreshData()
    End Sub

    Private Sub riFicaOnlyCheckBox_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riFicaOnlyCheckBox.CheckedChanged
        Me.BandedGridView1.PostEditor()
    End Sub

    Private Sub btnActivatePayDeds_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnActivatePayDeds.Click
        If HasLayoutChanges AndAlso Not IsAuto Then
            SaveLayout(Nothing)
        End If
        Dim frm As New frmActivatePayDeds With {.CoNum = Me.CoNum}
        Dim results = frm.ShowDialog
        If results = System.Windows.Forms.DialogResult.OK Then
            'Load Columns
            DB = New dbEPDataDataContext(GetConnectionString)
            LoadLayout(DB)

            'Add new columns to data table
            CreatePayrollDataTable(False)

            'Move it to the correct position
            For Each pCode In frm.ActivatedPays
                MoveGroupToCorrectPosition("P", pCode)
            Next

            For Each dCode In frm.ActivatedDeds
                MoveGroupToCorrectPosition("D", dCode)
            Next

            SetupGrid()
            Me.gridViewAvailColumns.RefreshData()
        End If
        frm.Dispose()
    End Sub

    Sub MoveGroupToCorrectPosition(ByVal Type As String, ByVal Code As Decimal)
        If Type = "P" Then
            Dim position = (From A In _SGroups Where A.type = "P" AndAlso A.code < Code Order By A.order).Last
            Dim NewGroup = (From A In _SGroups Where A.type = "P" AndAlso A.code = Code).Single
            If NewGroup.order = position.order + 1 Then
                'already at correct position
                Exit Sub
            End If
            NewGroup.order = position.order + 1
            Dim aRows = (From A In AvailCols Where A.type = "P" AndAlso A.code = Code).ToList
            aRows.ForEach(Sub(p) p.GroupOrder = NewGroup.order)
            Dim RemGroups = (From A In _SGroups Where A.order > position.order AndAlso Not (A.code = Code AndAlso A.type = "P")
                             Order By A.order).ToList
            For x = 0 To RemGroups.Count - 1
                Dim grp = RemGroups(x)
                grp.order += 1
                aRows = (From A In AvailCols Where A.type = grp.type AndAlso A.code = grp.code).ToList
                aRows.ForEach(Sub(p) p.GroupOrder = grp.order)
            Next
        Else
            Dim position = (From A In _SGroups Where A.type = "D" AndAlso A.code < Code Order By A.order).LastOrDefault
            If position Is Nothing Then
                position = (From A In _SGroups Where A.type = "P" Order By A.order).LastOrDefault
            End If
            Dim NewGroup = (From A In _SGroups Where A.type = "D" AndAlso A.code = Code).Single
            If NewGroup.order = position.order + 1 Then
                'already at correct position
                Exit Sub
            End If
            NewGroup.order = position.order + 1
            Dim aRows = (From A In AvailCols Where A.type = "D" AndAlso A.code = Code).ToList
            aRows.ForEach(Sub(p) p.GroupOrder = NewGroup.order)
            Dim RemGroups = (From A In _SGroups Where A.order > position.order AndAlso Not (A.code = Code AndAlso A.type = "D")
                             Order By A.order).ToList
            For x = 0 To RemGroups.Count - 1
                Dim grp = RemGroups(x)
                grp.order += 1
                aRows = (From A In AvailCols Where A.type = grp.type AndAlso A.code = grp.code).ToList
                aRows.ForEach(Sub(p) p.GroupOrder = grp.order)
            Next
        End If
    End Sub

    Private Sub btnMove_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveUp.Click, btnMoveDown.Click
        Dim RowIX = Me.gridViewAvailColumns.FocusedRowHandle
        Dim MoveTo = If(CType(sender, Control).Name = "btnMoveUp", -1, 1)
        If Me.gridViewAvailColumns.IsGroupRow(RowIX) Then
            Dim GroupValue As String = Me.gridViewAvailColumns.GetGroupRowValue(RowIX)
            Dim GroupRow = (From A In AvailCols Where A.GroupName = GroupValue).ToList
            Dim SelectedGroup = (From A In _SGroups Where A.type = GroupRow.First.type AndAlso A.code = GroupRow.First.code).Single
            If SelectedGroup.order = 1 AndAlso MoveTo < 1 Then Exit Sub ' already on top
            Dim ChangeGroup As pr_batch_grid_group
            If MoveTo < 1 Then
                ChangeGroup = (From A In _SGroups Where A.order < SelectedGroup.order Order By A.order).Last
                ChangeGroup.order += 1
                SelectedGroup.order = ChangeGroup.order - 1
            Else
                ChangeGroup = (From A In _SGroups Where A.order > SelectedGroup.order Order By A.order).FirstOrDefault
                If ChangeGroup Is Nothing Then Exit Sub 'already on bottom 
                ChangeGroup.order -= 1
                SelectedGroup.order += 1
            End If
            GroupRow.ForEach(Sub(p) p.GroupOrder = SelectedGroup.order)
            If ChangeGroup IsNot Nothing Then
                Dim bGroupRow = (From A In AvailCols Where A.type = ChangeGroup.type AndAlso A.code = ChangeGroup.code).ToList
                bGroupRow.ForEach(Sub(p) p.GroupOrder = ChangeGroup.order)
            End If
        ElseIf RowIX > 0 Then
            Dim aRow As prc_BrandsPowerGridColumnsResult = Me.gridViewAvailColumns.GetRow(RowIX)
            Dim SelectCol = (From A In _SColumns Where A.sql_column = aRow.sql_column).Single
            If SelectCol.order = 0 AndAlso MoveTo < 1 Then Exit Sub
            Dim ChangeRow As pr_batch_grid_column
            If MoveTo < 1 Then
                ChangeRow = (From A In _SColumns Where A.code = aRow.code AndAlso A.order < SelectCol.order Order By A.order).Last
                ChangeRow.order += 1
                SelectCol.order -= 1
            Else
                ChangeRow = (From A In _SColumns Where A.code = aRow.code AndAlso A.order > SelectCol.order Order By A.order).FirstOrDefault
                If ChangeRow Is Nothing Then Exit Sub
                ChangeRow.order -= 1
                SelectCol.order += 1
            End If
            aRow.ColumnOrder = SelectCol.order
            If ChangeRow IsNot Nothing Then
                Dim bRow = (From a In AvailCols Where a.sql_column = ChangeRow.sql_column).Single
                bRow.ColumnOrder = ChangeRow.order
            End If
        Else
            Exit Sub
        End If
        'Dim sortInfo As DevExpress.XtraGrid.Columns.GridColumnSortInfoCollection = Me.gridViewAvailColumns.SortInfo
        'Dim savedInfo(sortInfo.Count - 1) As DevExpress.XtraGrid.Columns.GridColumnSortInfo
        'CType(New ArrayList(sortInfo), ArrayList).ToArray().CopyTo(savedInfo, 0) 'save

        'Me.gridViewAvailColumns.BeginUpdate()
        'gridViewAvailColumns.SortInfo.AddRange(savedInfo) ' restore
        'Me.gridViewAvailColumns.EndUpdate()

        Me.gridViewAvailColumns.RefreshData()
        SetupGrid()
        HasLayoutChanges = True
    End Sub

    Sub SaveLayout(ByVal DB As dbEPDataDataContext)
        Try
            If _PayrollEnt.PR_DESCR = "Bonus Run" Then Exit Sub
            If DB Is Nothing Then DB = New dbEPDataDataContext(GetConnectionString)
            'Get current data
            Dim Schema = (From A In DB.pr_batch_grid_schemas Where A.schema_id = Me.AvailCols(0).schema_id).Single
            Dim SGroups = Schema.pr_batch_grid_groups.ToList
            Dim SColumns = Schema.pr_batch_grid_columns.ToList
            Dim CodeGroupCount = (From A In SGroups Where A.code >= 0).Count
            For Each group In SGroups
                Dim code = group.code
                Dim type = group.type
                Dim rec = (From A In _SGroups Where A.type = type AndAlso A.code = code).Single
                If code = -1 Then
                    group.order = 1
                ElseIf code < -1 Then
                    group.order = CodeGroupCount + Math.Abs(rec.code)
                Else
                    group.order = rec.order
                End If
            Next
            For Each col In SColumns
                Dim sqlcol = col.sql_column
                Dim rec = (From A In _SColumns Where A.sql_column = sqlcol).Single
                col.order = rec.order
                col.hide = rec.hide
            Next
            Dim HasChanges = DB.GetChangeSet.Updates.Count > 0
            If HasChanges Then
                If MessageBox.Show("Do you want to save the power-grid layout for next time?", "Save Layout?", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes Then
                    If Not DB.SaveChanges() Then
                        Return ' Exit if save failed due to concurrency conflict
                    End If
                Else
                    HasLayoutChanges = False
                    DB = New dbEPDataDataContext(GetConnectionString)
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving layout", ex)
        End Try
    End Sub

    Sub SetCellWatermark(ByVal EmpNum As Decimal, ByVal ChkCounter As Integer, Rate As Decimal, ByVal ColumnName As String, ByVal Value As String, Optional ByVal IsOverride As Boolean = False)
        Dim key = EmpNum & "-" & ChkCounter & "-" & Rate.ToString("N4")
        Dim nvc As Specialized.NameValueCollection = Nothing
        CellWatermarkValues.TryGetValue(key, nvc)
        If nvc Is Nothing Then
            nvc = New Specialized.NameValueCollection
            CellWatermarkValues.Add(key, nvc)
        End If
        If Not IsOverride Then
            nvc.Add(ColumnName, Value)
        Else
            nvc(ColumnName) = Value
        End If
    End Sub

    Function GetColumnWatermark(ByVal Row As DataRow, ByVal FieldName As String) As String
        Dim RowKey = Row("EmpNum") & "-" & nz(Row("chknum"), 1) & "-" & Convert.ToDecimal(nz(Row("linerate"), 0)).ToString("N4")
        Dim nvc As Specialized.NameValueCollection = Nothing
        If Not CellWatermarkValues.TryGetValue(RowKey, nvc) OrElse Not nvc.AllKeys.Contains(FieldName) Then
            Dim Rate As Decimal
            Dim RateCode As String = Row("RateCode") & ""
            If RateCode.StartsWith("A") Then
                Rate = -(Convert.ToDecimal(Row("RateCode").ToString.Substring(1)) + 1)
            ElseIf RateCode.StartsWith("S") AndAlso RateCode.Length > 1 Then
                Rate = -(Convert.ToDecimal(Row("RateCode").ToString.Substring(1)))
            Else
                Rate = -1
            End If
            RowKey = Row("EmpNum") & "-" & nz(Row("chknum"), 1) & "-" & Rate.ToString("N4")
            CellWatermarkValues.TryGetValue(RowKey, nvc)
        End If

        If nvc IsNot Nothing Then
            Dim wm = nvc(FieldName)
            Return wm
        End If
        Return Nothing
    End Function

    Function GetColumnName(ByVal Type As String, ByVal Code As Decimal, ByVal Column As ColumnName) As String
        If Type = "P" Then
            If Code = 0 Then
                Select Case Column
                    Case ColumnName.pay_rate
                        Return "rate"
                    Case ColumnName.rate_num
                        Return "ratenum"
                    Case ColumnName.regular_hours
                        Return "reghrs"
                    Case ColumnName.overtime_hours
                        Return "othrs"
                    Case ColumnName.amount
                        Return "salary"
                End Select
            Else
                Select Case Column
                    Case ColumnName.pay_rate
                        Return "pr" & Code
                    Case ColumnName.rate_num
                        Return "pn" & Code
                    Case ColumnName.regular_hours
                        Return "ph" & Code
                    Case ColumnName.overtime_hours
                        Return "po" & Code
                    Case ColumnName.amount
                        Return "pp" & Code
                End Select
            End If
        Else
            Return Type.ToLower & "f" & Code
        End If
        Return Nothing
    End Function

    Enum ColumnName
        pay_rate
        rate_num
        regular_hours
        overtime_hours
        amount
    End Enum

    Sub UpdateAutoPay(ByVal Row As DataRow, ByVal FieldName As String, Optional ByVal EditValue As String = Nothing)
        Dim EmpNum As Decimal = Row("empnum")

        'if paid under different employee id, ignore
        Dim ChangeEmployeeCode = (From A In CheckOverrides Where A.EmpNum = EmpNum AndAlso A.PayUnderEmpNum.HasValue Select A.PayUnderEmpNum).FirstOrDefault
        If ChangeEmployeeCode.HasValue Then
            Exit Sub
        End If

        Dim frm = New frmEmployeeAutoPaysAndDeds With {.CoNum = CoNum,
                                                        .EmployeeNum = EmpNum,
                                                        .PayrollNum = Me.PrNum,
                                                        .CheckCounter = Row("chknum"),
                                                        .PowerGridForm = Me}
        Dim Col = (From A In AvailCols Where A.sql_column = FieldName).FirstOrDefault
        If Not String.IsNullOrEmpty(EditValue) AndAlso EditValue = "All0" Then
            frm.SetAll0 = True
        Else
            If EditValue IsNot Nothing Then
                frm.Edit = New frmEmployeeAutoPaysAndDeds.StartEdit With {.Code = Col.code, .Type = Col.type, .ChkCounter = nz(Row("chknum"), 1), .EditValue = EditValue}
            End If
        End If
        Dim results = frm.ShowDialog()
        If Col IsNot Nothing AndAlso results = System.Windows.Forms.DialogResult.OK Then
            If Col.type = "P" Then
                Dim Rec = (From A In frm.Pays Where A.CL_Code = Col.code AndAlso (A.ChkCounter = nz(Row("ChkNum"), 1) OrElse (A.ChkCounter = 0 AndAlso nz(Row("ChkNum"), 1) = 1))).FirstOrDefault
                If Rec IsNot Nothing Then
                    SetCellWatermark(Row("EmpNum"), nz(Row("ChkNum"), 1), nz(Row("linerate"), 0), FieldName, "Auto-" & If(Rec.TTOOverride.HasValue, Rec.FormattedTTOOverride, Rec.FormattedAmount), True)
                End If
            Else
                Dim Rec = (From A In frm.Deds Where A.CL_Code = Col.code AndAlso (A.ChkCounter = nz(Row("ChkNum"), 1) OrElse (A.ChkCounter = 0 AndAlso nz(Row("ChkNum"), 1) = 1))).FirstOrDefault
                If Rec IsNot Nothing Then
                    SetCellWatermark(Row("EmpNum"), nz(Row("ChkNum"), 1), nz(Row("linerate"), 0), FieldName, "Auto-" & If(Rec.TTOOverride.HasValue, Rec.FormattedTTOOverride, Rec.FormattedAmount), True)
                End If
            End If
        Else
            For Each rec In frm.Pays
                FieldName = GetColumnName(rec.Type, rec.CL_Code, ColumnName.amount)
                SetCellWatermark(Row("EmpNum"), nz(Row("ChkNum"), 1), nz(Row("linerate"), 0), FieldName, "Auto-" & If(rec.TTOOverride.HasValue, rec.FormattedTTOOverride, rec.FormattedAmount), True)
            Next
        End If
        frm.Dispose()
    End Sub

    Private Sub riPaysAndDedsAmounts_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles riPaysAndDedsAmounts.DoubleClick
        Dim Row = Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle)
        Dim FieldName = Me.BandedGridView1.FocusedColumn.FieldName
        Dim wm = GetColumnWatermark(Row, FieldName)
        If Not String.IsNullOrEmpty(wm) AndAlso wm.StartsWith("Auto") Then
            'Dim Dec As Decimal
            Dim ev As String = Nothing
            'If Decimal.TryParse(wm.Replace("Auto-", ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, Dec) Then
            '    ev = Dec
            'End If
            UpdateAutoPay(Row, FieldName, ev)
        End If
    End Sub

    Sub AddColumn(ByVal PayType As String, ByVal PayCode As Decimal)
        DB = New dbEPDataDataContext(GetConnectionString)
        If PayType = "P" Then
            Dim Row = (From A In DB.OTHER_PAYS Where A.CONUM = Me.CoNum AndAlso A.OTH_PAY_NUM = PayCode).SingleOrDefault
            Row.OACTIVE = "YES"
            Row.ops_webshow = "YES"
        Else
            Dim Row = (From A In DB.DEDUCTIONs Where A.CONUM = Me.CoNum AndAlso A.DED_NUM = PayCode).SingleOrDefault
            Row.DACTIVE = "YES"
            Row.deds_webshow = "YES"
        End If
        If Not DB.SaveChanges() Then
            Return ' Exit if save failed due to concurrency conflict
        End If

        LoadLayout(DB)

        'Add new columns to data table
        CreatePayrollDataTable(False)

        MoveGroupToCorrectPosition(PayType, PayCode)
    End Sub

    Private Sub BandedGridView1_ShowingEditor(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles BandedGridView1.ShowingEditor
        'Exit Sub
        Dim Cols = {"FicaOnly", "FedOverride", "STOverride", "LOCOverride", "DBOverride", "FLIOverride", "TaxFrequency", "Net", "ChkType", "OASDIOverride", "MedicareOverride", "ChkNumber"}
        If Cols.Contains(Me.BandedGridView1.FocusedColumn.FieldName) Then
            Dim TR = Me.BandedGridView1.GetDataRow(Me.BandedGridView1.FocusedRowHandle)
            If Not nz(TR("CheckLineNumber"), 100) = 1 Then
                e.Cancel = True
            ElseIf {"OASDIOverride", "MedicareOverride", "ChkNumber"}.Contains(Me.BandedGridView1.FocusedColumn.FieldName) Then
                If TR("ChkType") & "" = "" AndAlso Not TR("IsSecondCheck") Then
                    e.Cancel = True
                End If
            End If
        End If
    End Sub

    Sub LoadCoOptions(ByVal DB As dbEPDataDataContext)
        If DB Is Nothing Then DB = New dbEPDataDataContext(GetConnectionString)
        CoPayrollOptions = (From A In DB.CoOptions_Payrolls Where A.CoNum = CoNum).SingleOrDefault
        Dim SeperateChecks = (From A In DB.CoOptions_SecondCheckPayCodes Where A.CoNum = CoNum).Count
        If CoPayrollOptions Is Nothing Then
            CoPayrollOptions = New CoOptions_Payroll
        End If
        If CoPayrollOptions.PaycodeFor1099.HasValue Then
            PayFieldFor1099 = "pp" & CoPayrollOptions.PaycodeFor1099.Value
        ElseIf CoPayrollOptions.DedcodeFor1099.HasValue Then
            PayFieldFor1099 = "df" & CoPayrollOptions.DedcodeFor1099.Value
        Else
            PayFieldFor1099 = "df9"
        End If
        If CoPayrollOptions.PaycodeForBonus.HasValue Then
            PayFieldForBonus = "pp" & CoPayrollOptions.PaycodeForBonus.Value
        Else
            PayFieldForBonus = "pp13"
        End If
    End Sub

    Private Sub BandedGridView1_RowCellStyle(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs) Handles BandedGridView1.RowCellStyle
        'Exit Sub
        Dim Row = Me.BandedGridView1.GetDataRow(e.RowHandle)
        If Not IsDBNull(e.CellValue) AndAlso {"salary", "reghrs", "othrs", "pp30", "ph30", "po30"}.Contains(e.Column.FieldName) AndAlso nz(Row("CheckLineNumber"), 2) = 1 Then
            Dim defaultVal As Decimal
            Dim EmpNum As Decimal = Row("empnum")
            If _DefaultValues.TryGetValue(EmpNum & "|" & e.Column.FieldName, defaultVal) Then
                If defaultVal = e.CellValue Then
                    e.Appearance.Font = New Font(e.Appearance.Font, FontStyle.Bold Or FontStyle.Italic)
                End If
            End If
        End If
        If {"first", "last", "pay"}.Contains(e.Column.FieldName) AndAlso Row("pay") = False Then
            e.Appearance.BorderColor = Color.Red
            e.Appearance.BackColor = Color.LightGray
        End If
    End Sub

    Private Sub EnterHours(ByVal sender As DevExpress.Utils.Menu.DXMenuItem, ByVal e As EventArgs)
        Dim SourceRowIX As Integer = sender.Tag
        Dim Row = Me.BandedGridView1.GetDataRow(SourceRowIX)
        Dim frm = New frmHoursOnly With {.CoNum = CoNum,
                                         .EmpNum = Row("empnum"),
                                         .PrNum = Me.PrNum,
                                         .DeptNum = Row("department")}
        Dim results = frm.ShowDialog()
        frm.Dispose()
    End Sub

    Private Sub ExportExcel()
        Try
            Dim Tempfile = My.Computer.FileSystem.GetTempFileName
            Tempfile = IO.Path.ChangeExtension(Tempfile, ".xls")

            ExportToExcel(Tempfile)
            OpenFile(Tempfile)
        Catch ex As Exception
            DisplayErrorMessage("Error while exporting to Excel", ex)
        End Try
    End Sub

    Sub ExportToExcel(FileName As String)
        Me.GridControl1.ForceInitialize()

        Dim Lic As New Aspose.Cells.License()
        Lic.SetLicense("Aspose.Cells.lic")

        'Hide unnecessary columns
        Dim bandTaxOverrides = Me.BandedGridView1.Bands("bandTaxOverrides")
        bandTaxOverrides.Visible = CoPayrollOptions.PGExportTaxOverrideColumns.GetValueOrDefault
        If bandTaxOverrides.Visible AndAlso Not Me.bciShowTaxOverrideColumns.Checked Then
            Me.bciShowTaxOverrideColumns.Checked = True
        End If
        Me.ShowtaxOverridesChanged()
        Dim taxOverrideVisible = bandTaxOverrides.Visible

        Dim HideColumns As New Dictionary(Of String, Boolean) From {{"HasNotes1", True}, {"AutoPays", True}, {"EditLog", True}, {"FicaOnly", True}}
        For Each Col In HideColumns.Keys.ToList
            Dim bCol = Me.BandedGridView1.Columns(Col)
            If bCol Is Nothing Then
                HideColumns(Col) = True
            Else
                HideColumns(Col) = bCol.Visible
                bCol.Visible = False
            End If
        Next
        Dim NetCol = Me.BandedGridView1.Columns("Net")
        NetCol.OwnerBand = Me.BandedGridView1.Bands("bandOthers")
        Dim ImportNotesCol = Me.BandedGridView1.Columns("ImportNotes")
        Dim ImportNotesColVisible = ImportNotesCol.Visible
        ImportNotesCol.Visible = True

        For x = 0 To Me.BandedGridView1.VisibleColumns.Count - 1
            Dim Bcol = Me.BandedGridView1.VisibleColumns(x)
            If Bcol.ColumnEdit IsNot Nothing Then Continue For
            Dim Col = Me.TblPayrollData.Columns(Bcol.FieldName)
            If Col IsNot Nothing AndAlso Col.DataType = Type.GetType("System.Boolean") Then
                Bcol.ColumnEdit = riCheckEdit
            End If
        Next

        If CoPayrollOptions.PGExportRateCodeColumn.GetValueOrDefault Then
            Me.BandedGridView1.Columns("RateCode").Visible = True
            Me.BandedGridView1.Columns("RateCode").VisibleIndex = Me.BandedGridView1.Columns("linerate").VisibleIndex - 1
        End If

        'Address band
        Dim DisplayAddress As Boolean = CoPayrollOptions.PGExportAddress.GetValueOrDefault
        Dim bandAddress As GridBand = Nothing
        If DisplayAddress Then
            bandAddress = Me.BandedGridView1.Bands.Add(New GridBand With {.Name = "bandAddress",
                                                                          .Caption = "Address"})
            For Each Fld In {"STREET", "CITY", "ADDR_STATE", "ZIP", "FED_STATUS"}
                Dim BCol As BandedGridColumn = New BandedGridColumn With {
                    .Name = String.Format("col{0}", Fld),
                    .FieldName = Fld,
                    .OwnerBand = bandAddress,
                    .Visible = True
                }
                BCol.AppearanceCell.ForeColor = Color.Black
                BCol.AppearanceCell.BackColor = Color.Lavender
                Me.BandedGridView1.Columns.Add(BCol)
            Next
        End If

        'Extra/fixed tax overrides band
        Dim DisplayExtra As Boolean = CoPayrollOptions.PGExportExtraFixedTax.GetValueOrDefault
        Dim bandExtra As GridBand = Nothing
        If DisplayExtra Then
            bandExtra = Me.BandedGridView1.Bands.Add(New GridBand With {.Name = "bandExtra",
                                                                          .Caption = "Extra/Fixed Tax Overrides"})
            Dim riText = New Repository.RepositoryItemTextEdit 'With {.ExportMode = Repository.ExportMode.DisplayText}
            For Each Fld In {"FedExtra", "FedFixed", "ST_Extra", "ST_Fixed"}
                'check if it has any data
                Dim HasData = Me.TblPayrollData.Select(String.Format("ISNULL({0}, 0) > 0", Fld)).Length > 0
                If HasData Then
                    Dim BCol As BandedGridColumn = New BandedGridColumn With {
                                        .Name = String.Format("col{0}", Fld),
                                        .FieldName = Fld,
                                        .OwnerBand = bandExtra,
                                        .Visible = True
                                    }
                    BCol.AppearanceCell.ForeColor = Color.Black
                    BCol.AppearanceCell.BackColor = Color.Lavender
                    BCol.ColumnEdit = riText
                    Me.BandedGridView1.Columns.Add(BCol)
                End If
            Next
            If bandExtra.Columns.Count = 0 Then
                bandExtra.Visible = False
            End If
        End If

        'Autos band
        Dim bandAutos As GridBand = Nothing
        If CoPayrollOptions.PGExportAutoColumns.GetValueOrDefault AndAlso _Autos IsNot Nothing AndAlso _Autos.Count > 0 Then
            Dim AutoTypes = (From A In _Autos Where A.Amount.HasValue Select A.Type, A.Code Order By Type Descending, Code).Distinct.ToList
            If AutoTypes.Count > 0 Then
                bandAutos = Me.BandedGridView1.Bands.Add(New GridBand With {.Name = "bandAllAutos",
                                                                        .Caption = "Auto Pays"})
                For Each t In AutoTypes
                    Dim BCol As BandedGridColumn = New BandedGridColumn With {
                    .Name = String.Format("colAuto{0}{1}", t.Type, t.Code),
                    .FieldName = String.Format("Auto_{0}_{1}", t.Type, t.Code),
                    .Caption = String.Format("{0} {1}", If(t.Type = "P", "Pay", "Ded"), t.Code),
                    .OwnerBand = bandAutos,
                    .UnboundType = DevExpress.Data.UnboundColumnType.String,
                    .Visible = True,
                    .Tag = GetColumnName(t.Type, t.Code, ColumnName.amount)
                }
                    BCol.AppearanceCell.ForeColor = Color.Black
                    BCol.AppearanceCell.BackColor = Color.LightGray
                    Me.BandedGridView1.Columns.Add(BCol)
                Next
            End If
        End If
        If Me.Visible = False AndAlso System.Environment.UserInteractive() Then
            Me.Show()
            Me.WindowState = FormWindowState.Minimized
        End If
        'department
        Dim RestoreColDept As Boolean
        Dim ColDept = Me.BandedGridView1.Columns("department")
        If ColDept IsNot Nothing AndAlso ColDept.Visible = True Then
            RestoreColDept = True
            Dim colDeptDiv As BandedGridColumn = Me.BandedGridView1.Columns("DeptDiv")
            colDeptDiv.Visible = True
            While colDeptDiv.VisibleIndex > 0
                If colDeptDiv.VisibleIndex < ColDept.VisibleIndex + 1 Then Exit While
                colDeptDiv.OwnerBand.Columns.MoveTo(colDeptDiv.VisibleIndex - 1, colDeptDiv)
            End While
            'colDeptDiv.OwnerBand.Columns.MoveTo(0, colDeptDiv)
            'colDeptDiv.OwnerBand.Columns.MoveTo(ColDept.VisibleIndex + 2, colDeptDiv)
            'colDeptDiv.VisibleIndex = ColDept.VisibleIndex
            ColDept.Visible = False
        End If


        Me.BandedGridView1.OptionsPrint.AutoWidth = False
        DevExpress.Export.ExportSettings.DefaultExportType = DevExpress.Export.ExportType.WYSIWYG

        If RunHidden Then
            Me.BandedGridView1.OptionsPrint.ShowPrintExportProgress = False
        End If

        Me.BandedGridView1.ExportToXls(FileName)

        Dim Cells As New Aspose.Cells.Workbook(FileName)
        Dim Sheet = Cells.Worksheets(0)
        Dim ValueSheet = Cells.Worksheets.Add("xx")
        ValueSheet.IsVisible = False
        Dim YNRange = ValueSheet.Cells.CreateRange("A2", "A3")
        YNRange.Name = "YNrange"
        YNRange(0, 0).Value = "Y"
        YNRange(1, 0).Value = "N"

        Dim Validations = Sheet.Validations
        'Yes/No 
        Dim YNValidation = Validations(Validations.Add)
        YNValidation.Type = Aspose.Cells.ValidationType.List
        YNValidation.Operator = Aspose.Cells.OperatorType.None
        YNValidation.InCellDropDown = True
        YNValidation.IgnoreBlank = False
        YNValidation.Formula1 = "=YNRange"
        YNValidation.AlertStyle = Aspose.Cells.ValidationAlertType.Stop
        YNValidation.ErrorMessage = "Please enter Y for Yes or N for No"

        With Sheet.Cells
            'add row for field names
            .InsertRow(2)
            For Each col As BandedGridColumn In Me.BandedGridView1.VisibleColumns
                Dim fldName = col.FieldName
                If fldName = "DeptDiv" Then fldName = "department"
                .Item(2, col.VisibleIndex).Value = fldName

                If col.ColumnEdit IsNot Nothing Then
                    If TypeOf col.ColumnEdit Is DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit Then
                        'Dim area As Aspose.Cells.CellArea
                        Dim area As New Aspose.Cells.CellArea()
                        area.StartRow = 3 : area.EndRow = .MaxDataRow : area.StartColumn = col.VisibleIndex : area.EndColumn = col.VisibleIndex
                        'YNValidation.AreaList.Add(area)
                        YNValidation.AddArea(area)
                    ElseIf TypeOf col.ColumnEdit Is DevExpress.XtraEditors.Repository.RepositoryItemComboBox OrElse TypeOf col.ColumnEdit Is DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit Then
                        Dim Items As New List(Of String)
                        If TypeOf col.ColumnEdit Is DevExpress.XtraEditors.Repository.RepositoryItemComboBox Then
                            Dim comboBox As DevExpress.XtraEditors.Repository.RepositoryItemComboBox = col.ColumnEdit
                            For x = 0 To comboBox.Items.Count - 1
                                Items.Add(comboBox.Items(x).ToString)
                            Next
                        Else
                            Dim lookup As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit = col.ColumnEdit
                            Dim dataSource As BindingSource = lookup.DataSource
                            For x = 0 To dataSource.Count - 1
                                Items.Add(dataSource.Item(x).GetType.GetProperty(lookup.DisplayMember).GetValue(dataSource.Item(x), Nothing))
                            Next
                        End If

                        Dim range = ValueSheet.Cells.CreateRange(1, col.VisibleIndex, Items.Count, 1)
                        range.Name = col.FieldName
                        For x = 0 To Items.Count - 1
                            range(x, 0).PutValue(Items(x)?.ToString)
                        Next

                        Dim validation = Validations(Validations.Add)
                        validation.Type = Aspose.Cells.ValidationType.List
                        validation.Operator = Aspose.Cells.OperatorType.None
                        validation.InCellDropDown = True
                        validation.IgnoreBlank = False
                        validation.Formula1 = "=" & col.FieldName
                        validation.AlertStyle = Aspose.Cells.ValidationAlertType.Stop
                        validation.ErrorMessage = "Please select a value from the list"

                        'Dim area As Aspose.Cells.CellArea
                        Dim area As New Aspose.Cells.CellArea()
                        area.StartRow = 3 : area.EndRow = .MaxDataRow : area.StartColumn = col.VisibleIndex : area.EndColumn = col.VisibleIndex
                        'validation.AreaList.Add(area)
                        YNValidation.AddArea(area)
                    End If
                End If
                If col.FieldName = "FedExtra" OrElse col.FieldName = "FedFixed" OrElse col.FieldName = "ST_Extra" OrElse col.FieldName = "ST_Fixed" Then
                    Dim colIX = col.VisibleIndex
                    Dim flags = New Aspose.Cells.StyleFlag With {.NumberFormat = True}
                    For RowIX = 3 To .MaxDataRow
                        Dim cell = .Item(RowIX, colIX)
                        If cell.Value IsNot Nothing Then
                            Dim style = cell.GetStyle()
                            Dim v As Decimal = cell.Value
                            If v > 0 AndAlso v < 1 Then
                                style.Number = 9
                            Else
                                style.Number = 7
                            End If
                            cell.SetStyle(style, flags)
                        End If
                    Next
                End If
            Next
            .HideRow(2)

            'Remove total row
            .DeleteRow(.MaxDataRow + 1)
        End With

        Sheet.AutoFitColumns(0, Sheet.Cells.MaxDataColumn)
        Sheet.AutoFitRows(0, 1)
        Sheet.FreezePanes(3, 0, 3, 0)
        Cells.Save(FileName)
        Cells = Nothing

        'Restore grid
        bandTaxOverrides.Visible = taxOverrideVisible
        For Each Col In HideColumns
            If Col.Value Then
                Dim col1 = Me.BandedGridView1.Columns(Col.Key)
                If col1 IsNot Nothing Then col1.Visible = True
            End If
        Next
        NetCol.OwnerBand = Me.BandedGridView1.Bands("bandEmployeeInfo")
        If bandAutos IsNot Nothing Then
            Dim cols As GridBandColumnCollection = bandAutos.Columns
            For x = cols.Count - 1 To 0 Step -1
                Me.BandedGridView1.Columns.Remove(cols(x))
            Next
            Me.BandedGridView1.Bands.Remove(bandAutos)
        End If
        If bandAddress IsNot Nothing Then
            Dim cols As GridBandColumnCollection = bandAddress.Columns
            For x = cols.Count - 1 To 0 Step -1
                Me.BandedGridView1.Columns.Remove(cols(x))
            Next
            Me.BandedGridView1.Bands.Remove(bandAddress)
        End If
        If bandExtra IsNot Nothing Then
            Dim cols As GridBandColumnCollection = bandExtra.Columns
            For x = cols.Count - 1 To 0 Step -1
                Me.BandedGridView1.Columns.Remove(cols(x))
            Next
            Me.BandedGridView1.Bands.Remove(bandExtra)
        End If
        Me.BandedGridView1.Columns("RateCode").Visible = False
        ImportNotesCol.Visible = ImportNotesColVisible

        If RestoreColDept Then
            Dim colDeptDiv = Me.BandedGridView1.Columns("DeptDiv")
            ColDept.Visible = False
            ColDept.VisibleIndex = colDeptDiv.VisibleIndex
            colDeptDiv.Visible = False
        End If
    End Sub

    Public Sub ImportExcel(Optional FileName As String = "")
        Try
            If Not RunHidden Then
                Dim FD As New OpenFileDialog With {.Title = "Select File To Import"}
                FD.Filter = "Excel files (*.xls, *.xlsx)|*.xls;*.xlsx;"
                If Not FD.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                    Return
                Else
                    FileName = FD.FileName
                End If
            End If

            If RunHidden Then
                Me.BandedGridView1.OptionsPrint.ShowPrintExportProgress = False
            End If

            If FileName <> "" Then
                Dim Book As New Aspose.Cells.Workbook(FileName)
                Dim Sheet = Book.Worksheets(0)

                'Solomon changed on May 23, '21.  Changed from 4 to 3.  Its rowIndex with 3 totaling 4 rows (2 headers, 1 hidden and 1 datarow)
                If Sheet.Cells.MaxDataRow < 3 Then
                    If RunHidden Then
                        Throw New Exception("The first sheet of the selected file has no data" & vbCrLf & "Make sure you put the import data in the first sheet of the Excel file")
                    Else
                        DisplayMessageBox("The first sheet of the selected file has no data" & vbCrLf & "Make sure you put the import data in the first sheet of the Excel file")
                    End If
                    Exit Sub
                Else
                    Me.TblPayrollData.Clear()
                    With Sheet.Cells
                        Dim Columns As New List(Of String)
                        For X = 0 To .MaxDataColumn
                            Dim V = .Item(2, X).StringValue
                            If Not String.IsNullOrEmpty(V) AndAlso Not V.StartsWith("Auto_") AndAlso Not {"FedExtra", "FedFixed", "ST_Extra", "ST_Fixed"}.Contains(V) Then
                                If TblPayrollData.Columns(V) IsNot Nothing Then
                                    Columns.Add(V)
                                Else
                                    If RunHidden Then
                                        Throw New Exception(String.Format("Invalid column name <{0}> at column {1}", V, .Item(2, X).Name))
                                    Else
                                        DisplayMessageBox(String.Format("Invalid column name <{0}> at column {1}", V, .Item(2, X).Name))
                                    End If
                                    Exit Sub
                                End If
                            End If
                        Next
                        Dim dec As Decimal
                        For iRow = 3 To .MaxDataRow
                            Try
                                Dim allData As String = ""
                                Dim nRow = TblPayrollData.NewRow
                                For iCol = 0 To Columns.Count - 1
                                    Dim V = .Item(iRow, iCol).StringValue
                                    If V IsNot Nothing Then
                                        V = V.Trim
                                        allData &= V
                                    End If
                                    Dim colType = TblPayrollData.Columns(Columns(iCol)).DataType
                                    If String.IsNullOrEmpty(V) AndAlso Not colType = GetType(Boolean) Then
                                        Continue For
                                    Else
                                        If TblPayrollData.Columns(Columns(iCol)).DataType = Type.GetType("System.Decimal") Then
                                            If V.Contains(":") Then
                                                Dim Parts = V.Split(":")
                                                If {"division", "department"}.Contains(Columns(iCol).ToLower) Then
                                                    V = Parts(0)
                                                Else
                                                    V = Parts(0) & "." & Parts(1)
                                                End If
                                            End If
                                            If Columns(iCol).ToLower = "job" Then
                                                If Me.COJOBBindingSource.Count = 0 Then
                                                    Dim jobList = (From A In DB.CO_JOBS Where A.conum = Me.CoNum AndAlso A.active = "YES").ToList
                                                    jobList.Insert(0, New CO_JOB)
                                                    Me.COJOBBindingSource.DataSource = jobList
                                                End If
                                                Dim selectedJob = (From A As CO_JOB In Me.COJOBBindingSource.List Where A.job_descr = V).FirstOrDefault
                                                If selectedJob IsNot Nothing Then
                                                    V = selectedJob.job_id
                                                End If
                                            End If
                                            If Decimal.TryParse(nz(V, ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, dec) Then
                                                nRow(Columns(iCol)) = dec
                                            Else
                                                nRow(Columns(iCol)) = V
                                            End If
                                        ElseIf TblPayrollData.Columns(Columns(iCol)).DataType = Type.GetType("System.Boolean") Then
                                            If {"YES", "NO"}.Contains(V.ToUpper) Then
                                                nRow(Columns(iCol)) = FromYesNoString(V)
                                            ElseIf {"Y", "N"}.Contains(V.ToUpper) Then
                                                V = If(V.ToUpper = "Y", "YES", "NO")
                                                nRow(Columns(iCol)) = FromYesNoString(V)
                                            ElseIf V.ToUpper = "CHECKED" Then
                                                nRow(Columns(iCol)) = True
                                            ElseIf V.ToUpper = "UNCHECKED" Then
                                                nRow(Columns(iCol)) = False
                                            ElseIf Not Boolean.TryParse(V, nRow(Columns(iCol))) Then
                                                'nRow(Columns(iCol)) = V
                                            End If
                                        Else
                                            nRow(Columns(iCol)) = V
                                        End If
                                    End If
                                Next
                                'Dim EmpNotes = (From A In NotesData Where A.empnum = nRow("empnum") Order By A.priority Descending Select A.priority.Substring(0, 1)).Distinct.ToList
                                'If EmpNotes.Count > 0 Then
                                '    nRow("HasNotes") = EmpNotes.First
                                'End If
                                If allData = "" Then Continue For
                                If IsDBNull(nRow("empnum")) Then
                                    If RunHidden Then
                                        Throw New Exception("Missing employee number on line # " & iRow + 1)
                                    Else
                                        DisplayMessageBox("Missing employee number on line # " & iRow + 1)
                                    End If
                                Else
                                    nRow("HasAutos") = HasAutos.Contains(nRow("empnum")) AndAlso TblPayrollData.Compute("COUNT(number)", "empnum = " & nRow("empnum")) = 0
                                    TblPayrollData.Rows.Add(nRow)
                                End If
                                If Not IsDBNull(nRow("Net")) AndAlso IsDBNull(nRow("Salary")) Then
                                    nRow("Salary") = CalculateFicaAdd(nRow("Net"))
                                End If
                                nRow("chknum") = nz(nRow("chknum"), 1)
                            Catch ex As Exception
                                If RunHidden Then
                                    Throw
                                Else
                                    DisplayErrorMessage("Error importing excel file", ex)
                                End If
                            End Try
                        Next
                        Me.BandedGridView1.RefreshData()

                        'Dim Notes = (From A In TblPayrollData
                        '             Where Not IsDBNull(A("ImportNotes"))
                        '             Select EmpNum = A("empnum"),
                        '                    ImportNotes = A("ImportNotes"),
                        '                    CheckNum = A("chknum"),
                        '                    LineRate = A("linerate")).ToList
                        Save(False, False)

                        If Not RunHidden Then
                            LoadData(True)

                            XtraMessageBox.Show("Import Completed", "Completed", MessageBoxButtons.OK, MessageBoxIcon.Asterisk)
                            ShowImportNotes()
                        End If
                    End With
                End If
                Sheet = Nothing
            End If
        Catch ex As Exception
            frmLogger.Error(ex, "error in importing excel.")
            If RunHidden Then
                Throw
            Else
                DisplayErrorMessage("Error importing excel", ex)
            End If
        End Try
    End Sub

    Private Sub ShowImportNotes(Optional ShowAll As Boolean = False)
        If Not ShowAll Then
            Dim ImportNotes = (From A In PayLineNotes Where A.EnteredBy = "Import" AndAlso (A.Priority Is Nothing OrElse A.Priority <> "3-Low")).ToList
            If ImportNotes.Count > 0 Then
                DisplayMessageBox("Please review import notes", "Import Notes")
            Else
                Return
            End If
        End If
        Dim frm = New frmEmployeeBatchNotesAll With {.CoCode = Me.CoNum, .PayrollNumber = Me.PrNum}
        Dim results = frm.ShowDialog
        frm.Dispose()
        If results = DialogResult.OK Then
            Try
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, PayLineNotes)
            Catch ex As Exception
                'ignore
            End Try
            'PayLineNotes = (From A In DB.pr_batch_notes Where A.ListID = BatchList.id And A.EmployeeID IsNot Nothing And A.Conum = Me.CoNum And A.PrNum = Me.PrNum).ToList
        End If
    End Sub

    Private Sub btnCancelCopyToNewCompany_Click(sender As Object, e As EventArgs) Handles btnCancelCopyToNewCompany.Click
        pccDuplicateFax.Hide()
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        Try
            If slueCompany.EditValue Is Nothing Then Exit Sub
            Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = Me.PrNum AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
            Dim ticket = New Ticket With {.AssignToUser = UserName, .OpenedBy = UserName, .OpenedOn = Now}
            DB.Tickets.InsertOnSubmit(ticket)
            If PayRec.FaxId IsNot Nothing AndAlso PayRec.FaxId <> 0 Then
                Dim existingFax = (From f In DB.Faxes Where f.FaxID.Equals(PayRec.FaxId)).Single
                existingFax.IsMultiCompany = True
                Dim newFileName = "{0}_{1}{2}".FormatWith(Path.Combine(Path.GetDirectoryName(existingFax.FileName), Path.GetFileNameWithoutExtension(existingFax.FileName)), slueCompany.EditValue, Path.GetExtension(existingFax.FileName))
                If File.Exists(newFileName) Then
                    XtraMessageBox.Show("Fax was already coped to this company.")
                    Exit Sub
                End If
                File.Copy(existingFax.FileName, newFileName)
                Dim faxEnt = New Fax()
                With faxEnt
                    .Category = existingFax.Category
                    .CoNum = slueCompany.EditValue
                    .Date = existingFax.Date
                    .FaxCategory = existingFax.FaxCategory
                    .FileName = newFileName
                    .IsDone = False
                    .IsMultiCompany = True
                    .Notes = existingFax.Notes
                    .Size = existingFax.Size
                    .TicketNum = existingFax.TicketNum
                End With
                ticket.Category = faxEnt.Category
                ticket.CoNum = faxEnt.CoNum
                ticket.Faxes.Add(faxEnt)
                If DB.SaveChanges() Then pccDuplicateFax.Hide()
            ElseIf PayRec.EmailNum IsNot Nothing AndAlso PayRec.EmailNum <> 0 Then
                Dim existingEmail = (From em In DB.Emails Where em.EmailNum.Equals(PayRec.EmailNum)).Single
                Dim id = Guid.NewGuid().ToString
                existingEmail.IsMultiCompany = True
                Dim newEmail = New Email()
                With newEmail
                    .Body = existingEmail.Body
                    .Category = existingEmail.Category
                    .CC = existingEmail.CC
                    .CoNum = slueCompany.EditValue
                    .DateReceived = existingEmail.DateReceived
                    .DateSent = existingEmail.DateSent
                    .EmailAttachments.AddRange(existingEmail.EmailAttachments.Select(Function(ea) New EmailAttachment With {.AttachmentID = ea.AttachmentID, .EmailID = id, .FileName = ea.FileName, .OriginalFileName = ea.OriginalFileName, .Size = ea.Size}))
                    .EmailNotificationSentOn = existingEmail.EmailNotificationSentOn
                    .EmailID = id
                    .From = existingEmail.From
                    .IsDone = False
                    .IsMultiCompany = True
                    .Notes = existingEmail.Notes
                    .Subject = existingEmail.Subject
                    .To = existingEmail.To
                    .Source = existingEmail.Source
                    .TicketNum = existingEmail.TicketNum
                End With
                ticket.CoNum = newEmail.CoNum
                ticket.Category = newEmail.Category
                ticket.Emails.Add(newEmail)
                If DB.SaveChanges() Then pccDuplicateFax.Hide()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error duplicating email or fax", ex)
        End Try
    End Sub

    Private Sub riPay_EditValueChanged(sender As Object, e As EventArgs) Handles riPay.EditValueChanged
        Me.BandedGridView1.PostEditor()
    End Sub

    Private Sub btnManualChecks_Click(sender As Object, e As EventArgs)
        Dim frm As New frmManualChecks With {.CoNum = Me.CoNum, .CheckDate = Me._PayrollEnt.CHECK_DATE}
        Dim results = frm.ShowDialog
        frm.Dispose()
    End Sub




    Private Sub TryShowWaitForm(show As Boolean)
        Try
            If show AndAlso Not SplashScreenManager1.IsSplashFormVisible Then
                SplashScreenManager1.ShowWaitForm()
            End If

            If Not show AndAlso SplashScreenManager1.IsSplashFormVisible Then
                SplashScreenManager1.CloseWaitForm()
            End If
        Catch ex As Exception
            frmLogger.Error(ex, "Error Showing splash screen")
        End Try
    End Sub

    Private Sub BarCheckItem2_CheckedChanged(sender As Object, e As ItemClickEventArgs) Handles ceMarkFaxDone.CheckedChanged
        If Not ceMarkFaxDone.Checked Then

            slueCompany.Properties.DataSource = (From A In DB.COMPANies
                                                 Order By A.CONUM
                                                 Select New CompanySummary With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                                                                                 .CO_DBA = A.CO_DBA, .CO_PHONE = A.CO_PHONE, .CO_FAX = A.CO_FAX,
                                                                                 .CO_EMAIL = A.CO_EMAIL, .CO_STATUS = A.CO_STATUS}
                                            ).ToList
            slueCompany.Properties.DisplayMember = "CONUM"
            slueCompany.Properties.ValueMember = "CONUM"
            pccDuplicateFax.Left = (Me.Width - pccDuplicateFax.Width) / 2
            pccDuplicateFax.Top = 200
            Me.pccDuplicateFax.Show()
        End If
    End Sub

    Sub FocusRegPayColumn()
        Try
            Dim CurRow = Me.BandedGridView1.GetFocusedDataRow
            If CurRow Is Nothing Then Exit Sub
            Dim IsSalary = nz(CurRow("salary"), 0) <> 0
            If IsSalary AndAlso Me.BandedGridView1.Columns("salary").Visible Then
                Me.BandedGridView1.FocusedColumn = Me.BandedGridView1.Columns("salary")
            ElseIf Me.BandedGridView1.Columns("reghrs").Visible Then
                Me.BandedGridView1.FocusedColumn = Me.BandedGridView1.Columns("reghrs")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in FocusRegPayColumn", ex)
        End Try
    End Sub

    Private Sub GridControl1_ProcessGridKey(sender As Object, e As KeyEventArgs) Handles GridControl1.ProcessGridKey
        If e.KeyCode = Keys.Down AndAlso Not e.Modifiers = Keys.Control Then
            Try
                Me.BandedGridView1.MoveNext()
                e.Handled = True
                FocusRegPayColumn()
            Catch ex As Exception
                'ignore validation errors
            End Try
        ElseIf e.KeyCode = Keys.Up AndAlso Not e.Modifiers = Keys.Control Then
            Try
                Me.BandedGridView1.MovePrev()
                e.Handled = True
                FocusRegPayColumn()
            Catch ex As Exception
                'ignore validation errors
            End Try
        ElseIf e.Modifiers = Keys.Control Then
            Select Case e.KeyCode
                Case Keys.E
                    EditEmployee(Nothing, e)
                Case Keys.P
                    Me.BandedGridView1.SetFocusedRowCellValue("pay", True)
                Case Keys.N
                    Me.BandedGridView1.SetFocusedRowCellValue("pay", False)
                    Me.BandedGridView1.MoveNext()
                    FocusRegPayColumn()
                    e.Handled = True
                Case Keys.Down
                    Me.BandedGridView1.MoveNext()
                    e.Handled = True
                Case Keys.A
                    Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Add new employee", AddressOf EditEmployee)
                    mItem.Tag = "Add"
                    EditEmployee(mItem, Nothing)
            End Select
        End If
    End Sub

    Private Sub GridControl1_KeyPress(sender As Object, e As KeyPressEventArgs) Handles GridControl1.KeyPress
        If Control.ModifierKeys AndAlso Keys.Control = Keys.Control Then
            e.Handled = True
        End If
    End Sub

    Private Sub txtWorkDate_Properties_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles txtWorkDate.Properties.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Plus Then
            Me.txtWorkDate.DoValidate()
        End If
    End Sub

    Public Sub ApplyFilter(filterText As String)
        Me.BandedGridView1.ActiveFilterString = filterText
    End Sub

    Private Sub BandedGridView1_CustomColumnDisplayText(sender As Object, e As CustomColumnDisplayTextEventArgs) Handles BandedGridView1.CustomColumnDisplayText
        Try
            Select Case e.Column.FieldName
                Case "FedExtra", "FedFixed", "ST_Extra", "ST_Fixed"
                    If e.Value IsNot Nothing AndAlso Not IsDBNull(e.Value) Then
                        Dim v As Decimal = e.Value
                        If v > 0 AndAlso v < 1 Then
                            e.DisplayText = v.ToString("p")
                        Else
                            e.DisplayText = v.ToString("c")
                        End If
                    End If
                Case "department"
                    Dim div = Me.BandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "division")
                    Dim dept As Decimal? = e.Value
                    If Not IsDBNull(div) Then
                        Me.DicDepartments.TryGetValue(div & "-" & dept, e.DisplayText)
                        'e.DisplayText = Me.DicDepartments(div & "-" & dept)
                    End If
            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Sub BandedGridView1_ShownEditor(sender As Object, e As EventArgs) Handles BandedGridView1.ShownEditor
        Try
            If Me.BandedGridView1.FocusedColumn.FieldName = "department" Then
                Dim div = Me.BandedGridView1.GetFocusedRowCellValue("division")
                If Not IsDBNull(div) Then
                    Dim DivNum As Decimal = div
                    Dim lookup As LookUpEdit = Me.BandedGridView1.ActiveEditor
                    lookup.Properties.DataSource = (From A As DEPARTMENT In Me.DEPARTMENTBindingSource.List Where A.DIVNUMD = DivNum Order By A.DEPT_DESC).ToList
                End If
            End If

        Catch ex As Exception
            DisplayErrorMessage("Error in BandedGridView1_ShownEditor", ex)
        End Try
    End Sub

    Private Sub BandedGridView1_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles BandedGridView1.CustomRowCellEditForEditing
        If e.Column.FieldName = "department" Then e.RepositoryItem = riDepartmentsLookup
    End Sub

    Private Sub frmBrandsPowerGrid_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        Try
            Me.LayoutControlGroupCols.Expanded = False
            Me.LayoutControlGroupCols.Width = 750

            Me.GridControl1.Select()
            Me.BandedGridView1.Focus()
            FocusRegPayColumn()
            Me.BandedGridView1.ShowEditor()
        Catch ex As Exception
            frmLogger.Error(ex, "Error in frmBrandsPowerGrid_Shown")
        End Try
    End Sub

    Private Sub PrintComments()
        If TblPayrollData Is Nothing OrElse TblPayrollData.Rows.Count = 0 Then Return
        Me.ShowImportNotes(True)
        Return
        Me.GridControl1.MainView = Me.gridViewComments
        Me.gridViewComments.ActiveFilterString = "!IsNull([ImportNotes])"
        If Me.gridViewComments.DataRowCount = 0 Then
            Me.GridControl1.MainView = Me.BandedGridView1
            DisplayMessageBox("No Comments")
            Return
        End If
        Using ps As New DevExpress.XtraPrinting.PrintingSystem()
            Dim link = New DevExpress.XtraPrinting.PrintableComponentLink With {.Component = Me.GridControl1}
            ps.Links.Add(link)
            Dim CoNum = String.Format("Co #: {0} - PR #: {1}", Me.CoNum, Me.PrNum)
            link.PageHeaderFooter = New DevExpress.XtraPrinting.PageHeaderFooter(
            New DevExpress.XtraPrinting.PageHeaderArea(New String() {CoNum, "", "Comments"},
                                                       New System.Drawing.Font("Arial", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte)), DevExpress.XtraPrinting.BrickAlignment.Near),
            New DevExpress.XtraPrinting.PageFooterArea(New String() {"[Page # of Pages #]", "", "[Date Printed] [Time Printed]"}, New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte)), DevExpress.XtraPrinting.BrickAlignment.Near))
            link.CreateDocument()
            link.ShowPreviewDialog()
        End Using
        Me.GridControl1.MainView = Me.BandedGridView1
    End Sub

    Private Sub btnCancelAddEmployee_Click(sender As Object, e As EventArgs) Handles btnCancelAddEmployee.Click
        Me.pccAddEmployee.Hide()
    End Sub

    Private Sub btnAddEmployee_Click(sender As Object, e As EventArgs) Handles btnAddEmployee.Click
        If Me.SearchEmployee.HasValue Then
            Dim EmpNum As Decimal = SearchEmployee.EditValue
            Dim EmpRecord = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.EMPNUM = EmpNum).FirstOrDefault
            LoadEmployeeRecord(EmpRecord)
            EmployeeList.Add(EmpRecord)
            Me.BandedGridView1.RefreshData()
            Dim NewRowIX = Me.BandedGridView1.LocateByValue("empnum", EmpNum, Nothing)
            If NewRowIX >= 0 Then
                Me.BandedGridView1.FocusedRowHandle = NewRowIX
            End If
            Me.pccAddEmployee.Hide()
        Else
            DisplayMessageBox("No employee selected")
        End If
    End Sub

    Private Sub btnAddNew_Click(sender As Object, e As EventArgs) Handles btnAddNew.Click
        Me.pccAddEmployee.Hide()
        EditEmployee(New DevExpress.Utils.Menu.DXMenuItem("Add NEW employee", AddressOf EditEmployee) With {.Tag = "Add"}, e)
    End Sub

    Private Sub pccAddEmployee_VisibleChanged(sender As Object, e As EventArgs) Handles pccAddEmployee.VisibleChanged
        If Me.pccAddEmployee.Visible AndAlso Me.SearchEmployee.Properties.DataSource Is Nothing Then
            LoadEmployeeSearch()
        End If
        Me.LayoutControl.Enabled = Not pccAddEmployee.Visible
        Me.SearchEmployee.EditValue = Nothing
    End Sub

    Sub LoadEmployeeSearch()
        Dim ShowTerm As Boolean = Me.chkShowTerminated.Checked
        Dim EmpList = (
                From A In DB.EMPLOYEEs
                Where A.CONUM = CoNum AndAlso (ShowTerm OrElse A.TERM_DATE Is Nothing)
                Order By A.EMPNUM
                Select A.EMPNUM, A.L_NAME, A.F_NAME, A.M_NAME, A.SSN, A.TIN, A.TERM_DATE).ToList
        Me.SearchEmployee.Properties.DataSource = EmpList
    End Sub

    Private Sub chkShowTerminated_CheckedChanged(sender As Object, e As EventArgs) Handles chkShowTerminated.CheckedChanged
        LoadEmployeeSearch()
    End Sub

    Private Sub btnPayAllHidden_LinkClicked(ByVal sender As System.Object, ByVal e As EventArgs) _
                                                                Handles btnPayAllHidden.LinkClicked,
                                                                        btnPayNoneHidden.Click,
                                                                        btnPayAll.Click,
                                                                        btnPayNone.Click,
                                                                        btnPayAllVisible.Click,
                                                                        btnPayNoneVisisble.Click

        Dim button = CType(sender, Control)
        Dim hidden = button.Name.Contains("Hidden")
        Dim visible = button.Name.Contains("Visible")
        Dim value = button.Name.Contains("PayAll")
        Dim filter = DevExpress.Data.Filtering.CriteriaToWhereClauseHelper.GetDataSetWhere(Me.BandedGridView1.ActiveFilterCriteria)
        Dim data = New DataView(TblPayrollData)
        If visible Then
            data.RowFilter = filter
        ElseIf hidden Then
            data.RowFilter = "NOT " & filter
        End If
        For Each row As DataRowView In data
            row("pay") = value
        Next
        Me.BandedGridView1.RefreshData()
        SetupDisplayCount()
    End Sub


    Public Shared Function PayrollApproval(coNum As Decimal, prNum As Decimal, addMsg As Boolean, LastCheckDate As DateTime) As String
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim status = db.PAYROLLs.Where(Function(f) f.CONUM = coNum AndAlso f.PRNUM = prNum).First()?.PAYROLL_STATUS
        If status <> "Entering Checks" AndAlso status <> "Submitted" Then Return "Payroll not in Entering Checks or Submitted"

        Dim result = QueuePayrollProcessor.modPayrollUtilities.SendPayrollReports(coNum, prNum, LastCheckDate)

        If addMsg Then
            Dim msg As New pr_batch_note With {
                .ListID = Guid.NewGuid(),
                .rowguid = Guid.NewGuid(),
                .Conum = coNum,
                .PrNum = prNum,
                .Note = result,
                .EnteredBy = UserName,
                .DateEntered = Date.Now,
                .Priority = "3-Low"}
            db.pr_batch_notes.InsertOnSubmit(msg)
            db.SubmitChanges()
        End If
        Return result
        'End If
    End Function

    Private Async Sub CloseZendeskTicket(ticketId As Long)
        Try
            frmLogger.Information("Entering CloseZendeskTicket TicketId: {TicketId}", ticketId)
            Dim updateTicketService = New Brands.Core.ZendeskServices.UpdateTicketService(New Brands.DAL.EPDATAContext(modGlobals.GetConnectionString()), frmLogger, GetZendeskApi)
            Await updateTicketService.ApplyMacro(ticketId, Convert.ToInt64(GetUdfValue("Zendesk_PayrollComplete_MacroId")))
        Catch ex As Exception
            frmLogger.Error(ex, "Error in CloseZendeskTicket.")
        End Try
    End Sub

    Private Async Function ProcessAndSubmit() As Task
        If MessageBox.Show("Are you sure you want to submit this power-grid?", "Confirm Submitting", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
            Return
        End If
        Try
            If bciProcessInBackground.Checked Then
                SaveAndQueue(False)
            Else
                Await Process(False)
            End If
        Catch ex As Exception
            TryShowWaitForm(False)
            MessageBox.Show(ex.Message)
        End Try
    End Function

    Private Async Function ProcessAndSendToAudit() As Task
        Try
            If bciProcessInBackground.Checked Then
                SaveAndQueue(True)
            Else
                Await Process(True)
            End If
        Catch ex As Exception
            TryShowWaitForm(False)
            DisplayErrorMessage("Error in process and send to audit", ex)
        End Try
    End Function

    Private Async Function ProcessAndTake() As Task
        Try
            If bciProcessInBackground.Checked Then
                SaveAndQueue(True)
            Else
                Dim id = Await Process(True)
                TakePayroll(id)
            End If
        Catch ex As Exception
            TryShowWaitForm(False)
            DisplayErrorMessage("Error in process and take", ex)
        End Try
    End Function

    Public Sub TakePayroll(ByVal ID As Integer)
        Dim DB As dbEPDataDataContext
        DB = New dbEPDataDataContext(GetConnectionString)
        Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = ID).Single
        If Rec.CompleteBy IsNot Nothing AndAlso Rec.CompleteBy <> UserName Then
            DisplayMessageBox("Payroll taken by '" & Rec.CompleteBy & "' on '" & "{0:g}".FormatWith(Rec.CompleteDate) & "'")
            Exit Sub
        End If
        Rec.LastOpenedBy = UserName
        Rec.LastOpenedDate = Now
        Rec.CompleteBy = UserName
        Rec.CompleteDate = Now

        If Not DB.SaveChanges() Then
            DisplayErrorMessage("Unable to save changes due to a conflict with another user's changes. Please refresh and try again.", New Exception("Concurrency conflict"))
            Return
        End If
    End Sub

    Async Sub SaveAndQueue(sendPayrollToAudit As Boolean)
        Try
            frmLogger.Debug("Entering SaveAndQueue")

            If sendPayrollToAudit AndAlso Not SaveHoldReason() Then
                Return
            End If

            'If CoPayrollOptions IsNot Nothing AndAlso CoPayrollOptions.SendRegisterBeforSubmitting.IsNotNullOrWhiteSpace Then
            '    DisplayErrorMessage("Queueing payrolls that require approval is not yet supported, please process regularly", New Exception("queueing payrolls that require approval is not yet supported, please process regularly"))
            '    Exit Sub
            'End If

            TryShowWaitForm(True)
            Save()

            Dim payRec = GetPayRec()

            Dim queueBuilder = GetQueueBuilder()
            queueBuilder.WithConum(Me.CoNum).WithDescription("Process Payroll")
            queueBuilder.EnqueuePayroll(Me.PrNum, Not sendPayrollToAudit)

            If Not sendPayrollToAudit Then
                If CoPayrollOptions IsNot Nothing AndAlso CoPayrollOptions.SendRegisterBeforSubmitting.IsNotNullOrWhiteSpace Then
                    QueuePayrollProcessor.Initialize(GetConnectionString, Logger, Nothing, UserName)
                    QueuePayrollProcessor.modPayrollUtilities.QueuePayrollReports(queueBuilder, CoNum, PrNum, DateTime.Now, Nothing)
                End If
            End If

            'add close zendesk ticket
            queueBuilder.EnqueuePayrollStatusNotification(PrNum)

            Await queueBuilder.SaveAndPublishAsync()
            TryShowWaitForm(False)
            Me.Close()
        Catch ex As Exception
            TryShowWaitForm(False)
            frmLogger.Error(ex, "Error in SaveAndQueue")
            DisplayErrorMessage("Error in Save And Queue Payroll", ex)
        End Try
    End Sub

    Function SaveHoldReason() As Boolean
        Dim editor = New MemoEdit
        editor.Properties.LinesCount = 5

        Dim args = New XtraInputBoxArgs() With {
            .Caption = "Enter reason for not submitting the payroll",
            .Prompt = "Notes",
            .Editor = editor}

        Dim results = XtraInputBox.Show(args)
        If results Is Nothing Then
            XtraMessageBox.Show("Please enter a note.")
            Return False
        Else
            Dim msg As New pr_batch_note With {.rowguid = Guid.NewGuid,
                                            .Conum = Me.CoNum,
                                            .PrNum = Me.PrNum,
                                            .DateEntered = DateTime.Now,
                                            .EnteredBy = UserName,
                                            .Note = "Reason for not submitting payroll: " & results,
                                            .Priority = "1-High",
                                            .ListID = If(BatchList.id = Guid.Empty, Guid.NewGuid, Me.BatchList.id)}
            DB.pr_batch_notes.InsertOnSubmit(msg)
            DB.SubmitChanges()
            Return True
        End If
    End Function


    Sub Save(Optional ByVal Submit As Boolean = False, Optional Process As Boolean = True)
        If IsReadOnly Then Exit Sub
        BandedGridView1.PostEditor()
        BandedGridView1.UpdateCurrentRow()

        teForFocus.Visible = True
        teForFocus.Focus()
        teForFocus.Select()
        teForFocus.Visible = False
        DB = New dbEPDataDataContext(GetConnectionString)

        If IsNew Then
            BatchList.id = Guid.NewGuid
            BatchList.created_by = UserName
            BatchList.modified_by = UserName
            BatchList.created_date = Now
            BatchList.modify_date = Now
            BatchList.schema_id = AvailCols.First.schema_id
            DB.pr_batch_lists.InsertOnSubmit(BatchList)
        Else
            BatchList = (From A In DB.pr_batch_lists Where A.id = BatchList.id).Single
            BatchList.modified_by = UserName
            BatchList.modify_date = Now
            Dim ExistingData = BatchList.pr_batch_rows.ToList
            DB.pr_batch_rows.DeleteAllOnSubmit(ExistingData)
        End If

        'reset all check counters
        If Me.CoPayrollOptions.SeperateCheckForDiffDepartments.GetValueOrDefault Then
            Dim dataD = (
                From A In TblPayrollData
                Where A.Field(Of Boolean)("pay") = True
                Group By EmpNum = A.Field(Of Decimal)("empnum"), ChkNum = A.Field(Of Decimal)("chknum"), DivDept = (A("division") & "-" & A("Department")).ToString
                Into Group
                ).ToList
            Dim ToSplit = (From A In dataD
                           Group By A.EmpNum, A.ChkNum Into Group
                           Where Group.Count > 1).ToList
            For Each itm In ToSplit
                Dim NextCheckNum As Integer = TblPayrollData.Compute("MAX(chknum)", "empnum=" & itm.EmpNum & " AND pay = 1")
                For x = 1 To itm.Group.Count - 1
                    NextCheckNum += 1
                    For Each chk In itm.Group(x).Group
                        chk("chknum") = NextCheckNum
                    Next
                Next
            Next
        End If

        Dim data = (From A In TblPayrollData
                    Where A.Field(Of Boolean)("pay") = True
                    Group By EmpNum = A.Field(Of Decimal)("empnum"), ChkNum = A.Field(Of Decimal)("chknum")
                    Into Group
                    Order By EmpNum, ChkNum).ToList
        Dim dataG = (From A In data Group By A.EmpNum Into Group).ToList
        For Each g In dataG
            Dim ChkNum = g.Group.Min(Function(p) p.ChkNum)
            For Each grp In g.Group
                For Each chk In grp.Group
                    chk("chknum") = ChkNum
                Next
                ChkNum += 1
            Next
        Next
        'end reset all check counters

        Dim SeperateCheckCodes = (From A In DB.CoOptions_SecondCheckPayCodes Where A.CoNum = Me.CoNum).ToList
        Dim LinePayCodes As Dictionary(Of String, Integer) 'pay code, check number
        Dim OTHoursColumns = (From A In AvailCols Where A.code >= 0 AndAlso A.ColumnName.Contains("OT Hours") Select A.sql_column).ToList

        Dim BatchRows As New List(Of pr_batch_row)
        Dim OverrideRows As New List(Of pr_batch_override)

        Dim ImportNotesList As New List(Of pr_batch_note)

        For RowIX = 0 To TblPayrollData.Rows.Count - 1 ' Me.BandedGridView1.RowCount - 1
            Dim LineRows As New List(Of pr_batch_row)

            Dim Row = TblPayrollData.Rows(RowIX) 'Me.BandedGridView1.GetDataRow(RowIX)
            Dim OTRow As DataRow = Nothing

            If Not Row("pay") Then
                'Check for notes only
                If Not IsDBNull(Row("ImportNotes")) Then
                    Dim eRow = CreateBatchRow(Row, RowIX)
                    ImportNotesList.Add(New pr_batch_note With {
                                                     .Conum = Me.CoNum,
                                                     .PrNum = Me.PrNum,
                                                     .ListID = Me.BatchList.id,
                                                     .EmployeeID = eRow.empnum,
                                                     .Note = Row("ImportNotes"),
                                                     .EnteredBy = "Import",
                                                     .DateEntered = Now,
                                                     .rowguid = Guid.NewGuid})
                End If
                Continue For
            End If

            Dim EmpNum As Decimal = Row("empnum")
            Dim TotalOTHours As Decimal = 0, SeperateCheckForOT As Boolean = False, SeperateCheckForOTAfterHours As Decimal? = Nothing
            For Each hrsColumn In OTHoursColumns
                TotalOTHours += nz(Row(hrsColumn), 0)
            Next

            If nz(Row("ratenum"), 0) > 0 AndAlso IsDBNull(Row("linerate")) Then
                Dim rates = (From A In DB.EMPLOYEEs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = EmpNum Select A.RATE_1, A.RATE_2, A.RATE_3).FirstOrDefault
                Dim rateNumValue As Decimal? = rates.GetType.GetProperty("RATE_" & Row("ratenum")).GetValue(rates)
                Row("linerate") = rateNumValue.GetValueOrDefault
            End If

            If TotalOTHours > 0 Then
                Dim EmpOptions = (From A In CheckOverrides Where A.EmpNum = EmpNum AndAlso A.OTSeperateCheck = True).FirstOrDefault
                SeperateCheckForOT = EmpOptions IsNot Nothing OrElse CoPayrollOptions.OTSeperateCheck
                If EmpOptions IsNot Nothing AndAlso EmpOptions.OTSeperateCheckHoursMoreThan.HasValue Then
                    SeperateCheckForOTAfterHours = EmpOptions.OTSeperateCheckHoursMoreThan
                Else
                    SeperateCheckForOT = CoPayrollOptions.OTSeperateCheck
                End If
                If TotalOTHours < SeperateCheckForOTAfterHours.GetValueOrDefault Then
                    SeperateCheckForOT = False
                End If
            End If

            If SeperateCheckForOT Then
                OTRow = TblPayrollData.NewRow
                For Each col In AvailCols
                    If col.type = "F" OrElse col.ColumnName.Contains("OT Hours") OrElse col.ColumnName.Contains("Rate") Then
                        OTRow(col.sql_column) = Row(col.sql_column)
                        If col.ColumnName.Contains("OT Hours") Then Row(col.sql_column) = DBNull.Value
                    End If
                Next
                'For Each col In {"FicaOnly", "FedOverride", "STOverride", "LOCOverride", "DBOverride", "TaxFrequency", "Net", "CheckLineNumber", "ChkType", "OASDIOverride", "MedicareOverride", "ChkNumber"}
                '    NewRow(col) = DBNull.Value
                'Next
            End If

            Dim ChangeEmployeeCode As Decimal?
            If Submit Then
                ChangeEmployeeCode = (From A In CheckOverrides Where A.EmpNum = EmpNum AndAlso A.PayUnderEmpNum.HasValue Select A.PayUnderEmpNum).FirstOrDefault
            End If
ProcessRow:
            LinePayCodes = New Dictionary(Of String, Integer)
            Dim Groups = (From A In AvailCols Where {"P", "D", "M"}.Contains(A.type) AndAlso Not (A.type = "P" AndAlso A.code = 0)
                          Order By A.GroupOrder, A.code
                          Group By A.GroupName, A.type, A.code Into Group
                          Select GroupName, type, code, Columns = Group.ToList).ToList

            'Pay 0
            Dim BRow = CreateBatchRow(Row, RowIX)
            With BRow
                .type = "P"
                .code = 0
                .pay_rate = nz(Row("rate"), Nothing)
                .rate_num = nz(Row("ratenum"), Nothing)
                If Not bciInputIsMinutes.Checked Then
                    .regular_hours = nz(Row("reghrs"), Nothing)
                    .overtime_hours = nz(Row("othrs"), Nothing)
                Else
                    .regular_hours = ConvertHoursToDecimal(nz(Row("reghrs"), Nothing))
                    .overtime_hours = ConvertHoursToDecimal(nz(Row("othrs"), Nothing))
                End If
                .amount = nz(Row("salary"), Nothing)
                .line_rate = nz(Row("linerate"), .pay_rate)

                Dim AlreadyHasPay0 = (From A In BatchRows Where A.empnum = EmpNum AndAlso A.code = 0).Any

                If BRow.amount.HasValue OrElse
                            BRow.regular_hours.HasValue OrElse
                            BRow.overtime_hours.HasValue OrElse
                            (Not AlreadyHasPay0 AndAlso Row("HasAutos")) Then
                    If ChangeEmployeeCode.HasValue Then BRow.empnum = ChangeEmployeeCode
                    LineRows.Add(BRow)
                    LinePayCodes.Add("P0", CType(nz(Row("chknum"), 1), Integer))
                End If
            End With

            For Each G In Groups
                For Each Col In G.Columns
                    If Not IsDBNull(Row(Col.sql_column)) Then
                        LinePayCodes.Add(Col.type & Col.code, CType(nz(Row("chknum"), 1), Integer))
                        Exit For
                    End If
                Next
            Next

            'TODO: only if pay code 0 has amount or hours
            frmLogger.Information("Getting next CheckNumber for Exp#: {EmpNum}", EmpNum)
            Dim NextCheckNum As Integer = TblPayrollData.Compute("MAX(chknum)", "empnum = " & EmpNum & " AND pay = 1")
            If SeperateCheckCodes.Count > 0 Then
                Dim SeperateCheckCodesQ = (From A In SeperateCheckCodes
                                           Join B In LinePayCodes On "P" & A.PayCode Equals B.Key
                                           Where (A.EmpNum Is Nothing OrElse A.EmpNum = EmpNum)
                                           Select A).ToList
                If SeperateCheckCodesQ.Count > 0 Then
                    Dim AllKeys = (From A In SeperateCheckCodesQ Select "P" & A.PayCode).ToList
                    Dim OtherCount = (From A In LinePayCodes.Keys Where Not AllKeys.Contains(A)).Count
                    If OtherCount = 0 Then
                        OtherCount = (From A In BatchRows
                                      Where A.empnum = EmpNum AndAlso
                                          Not AllKeys.Contains(A.code) AndAlso
                                          (A.amount.GetValueOrDefault <> 0 OrElse A.regular_hours.GetValueOrDefault <> 0 OrElse A.overtime_hours.GetValueOrDefault <> 0) AndAlso
                                          A.run_import_id = NextCheckNum).Count
                    End If
                    If OtherCount > 0 Then
                        NextCheckNum += 1 'move all to new check
                        For Each code In AllKeys
                            LinePayCodes(code) = NextCheckNum
                        Next
                    End If
                End If
                For Each code In SeperateCheckCodesQ
                    If code.SeparateCheck Then
                        'if more paycodes
                        Dim dPayCode As Decimal = code.PayCode
                        If (From A In LinePayCodes Where A.Value = LinePayCodes("P" & dPayCode)).Count > 1 Then
                            NextCheckNum += 1
                            LinePayCodes("P" & code.PayCode) = NextCheckNum
                        End If
                    End If
                Next
            End If

            For Each G In Groups
                Dim HasData As Boolean = LinePayCodes.ContainsKey(G.type & G.code)
                If HasData Then
                    BRow = CreateBatchRow(Row, RowIX, LinePayCodes(G.type & G.code))
                    With BRow
                        .type = G.type
                        .code = G.code
                        If G.type = "P" Then
                            .pay_rate = nz(Row("pr" & G.code), Nothing)
                            If .pay_rate Is Nothing Then
                                Dim wm = GetColumnWatermark(Row, "pr" & G.code)
                                If Not String.IsNullOrEmpty(wm) AndAlso (wm.StartsWith("Auto")) Then
                                    Dim Dec As Decimal
                                    If Decimal.TryParse(nz(wm.Replace("Auto-", ""), ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, Dec) Then
                                        .pay_rate = Dec
                                    End If
                                End If
                            End If
                            .rate_num = nz(Row("pn" & G.code), Nothing)
                            If Not bciInputIsMinutes.Checked Then
                                .regular_hours = nz(Row("ph" & G.code), Nothing)
                                .overtime_hours = nz(Row("po" & G.code), Nothing)
                            Else
                                .regular_hours = ConvertHoursToDecimal(nz(Row("ph" & G.code), Nothing))
                                .overtime_hours = ConvertHoursToDecimal(nz(Row("po" & G.code), Nothing))
                            End If
                            .amount = nz(Row("pp" & G.code), Nothing)
                        Else
                            .amount = nz(Row(G.type & "f" & G.code), Nothing)
                        End If
                        .line_rate = nz(Row("linerate"), .pay_rate)
                    End With
                    If ChangeEmployeeCode.HasValue Then BRow.empnum = ChangeEmployeeCode
                    LineRows.Add(BRow)
                End If
            Next 'Pay Code

            If Not IsDBNull(Row("ImportNotes")) Then
                If LineRows.Count = 0 Then
                    'Add 0 line
                    BRow = CreateBatchRow(Row, RowIX)
                    With BRow
                        .type = "P"
                        .code = 0
                        .amount = 0
                        .line_rate = 0
                        If ChangeEmployeeCode.HasValue Then BRow.empnum = ChangeEmployeeCode
                        LineRows.Add(BRow)
                    End With
                End If
                LineRows.First.ImportNote &= Row("ImportNotes")
            End If
            BatchRows.AddRange(LineRows)

            If Not IsDBNull(Row("Net")) OrElse TaxOverridesColumns.Any(Function(p) Not IsDBNull(Row(p)) AndAlso Not (p = "FicaOnly" AndAlso Row(p) = False)) Then
                Dim OverrideEnt = (From A In DB.pr_batch_overrides
                                   Where A.CONUM = CoNum AndAlso A.EMPNUM = EmpNum _
                                                         AndAlso A.PRNUM = Me.PrNum _
                                                         AndAlso A.CHK_COUNTER = CType(nz(Row("chknum"), 1), Integer)).FirstOrDefault
                If OverrideEnt Is Nothing Then
                    OverrideEnt = (From A In OverrideRows Where A.EMPNUM = EmpNum AndAlso A.CHK_COUNTER = CType(nz(Row("chknum"), 1), Integer)).FirstOrDefault
                End If
                If OverrideEnt Is Nothing Then
                    OverrideEnt = New pr_batch_override With {.CONUM = CoNum,
                                                              .PRNUM = Me.PrNum,
                                                              .EMPNUM = EmpNum,
                                                              .CHK_COUNTER = CType(nz(Row("chknum"), 1), Integer)}
                    DB.pr_batch_overrides.InsertOnSubmit(OverrideEnt)
                    OverrideRows.Add(OverrideEnt)
                End If
                OverrideEnt.NetOverrideAmount = Row.GetValue("Net")
                If Not IsDBNull(Row("Net")) Then
                    OverrideEnt.NetOverrideAdjustType = nz(Row("NetOverrideType"), "Gross")
                    OverrideEnt.NetOverrideDedNum = nz(Row("NetOverrideDedNum"), Nothing)
                End If
                OverrideEnt.TaxFrequency = Row.GetValue("TaxFrequency")
                OverrideEnt.FedOverrideAmount = Row.GetValue("FedOverride")
                OverrideEnt.STOverrideAmount = Row.GetValue("STOverride")
                OverrideEnt.LOCOverrideAmount = Row.GetValue("LOCOverride")
                OverrideEnt.DBOverrideAmount = Row.GetValue("DBOverride")
                OverrideEnt.FLIOverrideAmount = Row.GetValue("FLIOverride")
                OverrideEnt.ChkType = Row.GetValue("ChkType")
                OverrideEnt.OASDIOverrideAmount = Row.GetValue("OASDIOverride")
                OverrideEnt.MedicareOverrideAmount = Row.GetValue("MedicareOverride")
                OverrideEnt.WrkState = Row.GetValue("WrkStateOverride")
                OverrideEnt.ResState = Row.GetValue("ResStateOverride")
                OverrideEnt.UIState = Row.GetValue("UIStateOverride")
                OverrideEnt.ManualCheckNumber = Row.GetValue("ChkNumber")
                If ChangeEmployeeCode.HasValue Then OverrideEnt.EMPNUM = ChangeEmployeeCode
            End If

            If OTRow IsNot Nothing Then
                NextCheckNum += 1
                OTRow("ChkNum") = NextCheckNum
                Row = OTRow
                OTRow = Nothing
                GoTo ProcessRow
            End If

            If ChangeEmployeeCode.HasValue Then
                'update override records
                Dim ChkOverrides = (From A In DB.pr_batch_overrides Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum AndAlso A.EMPNUM = EmpNum).ToList
                ChkOverrides.ForEach(Sub(p) p.EMPNUM = ChangeEmployeeCode.Value)

                'update override records
                Dim ChkOverrideAutos = (From A In DB.pr_batch_override_autos Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum AndAlso A.EMPNUM = EmpNum).ToList
                ChkOverrides.ForEach(Sub(p) p.EMPNUM = ChangeEmployeeCode.Value)
            End If
        Next 'Grid Rows

        'Remove empty rows
        Dim EmptyRows = (From A In BatchRows
                         Where A.amount.GetValueOrDefault = 0 AndAlso A.regular_hours.GetValueOrDefault = 0 _
                             AndAlso A.overtime_hours.GetValueOrDefault = 0 AndAlso A.code = 0 AndAlso (Process OrElse String.IsNullOrEmpty(A.ImportNote))
                         Group By A.empnum
                         Into Group
                         Select empnum, first_row = Group.OrderBy(Function(p) p.number).First
                         ).ToList
        For Ix = EmptyRows.Count - 1 To 0 Step -1
            Dim eRow = EmptyRows(Ix)
            Dim HasNetOverride = (From A In OverrideRows Where A.EMPNUM = eRow.empnum AndAlso A.CHK_COUNTER = eRow.first_row.run_import_id AndAlso A.NetOverrideAmount.GetValueOrDefault <> 0).Any
            If HasNetOverride Then EmptyRows.RemoveAt(Ix)
        Next

        For Each eRow In EmptyRows
            Dim HasAnotherRow = (From A In BatchRows Where A.empnum = eRow.empnum AndAlso A.code = 0 AndAlso A.number > eRow.first_row.number).Any
            If HasAnotherRow Then
                BatchRows.Remove(eRow.first_row)
            End If
        Next

        If EmptyRows.Count > 0 AndAlso Not SecondChecksOnly AndAlso OverrideRows.Count > 0 Then
            'Reset check counters to reflect empty rows
            For Each row In EmptyRows
                Dim eRows = (From A In BatchRows
                             Where A.empnum = row.empnum
                             Group By A.empnum, A.run_import_id
                             Into Group
                             Order By empnum, run_import_id).ToList
                Dim ChkNum = 1
                For Each grp In eRows
                    Dim OverrideRecs = (From A In OverrideRows Where A.EMPNUM = grp.empnum AndAlso A.CHK_COUNTER = grp.run_import_id).ToList
                    'TODO: this is causing an error if there is already an override row for this check counter.
                    OverrideRecs.ForEach(Sub(p) p.CHK_COUNTER = ChkNum)

                    For Each chk In grp.Group
                        chk.run_import_id = ChkNum
                    Next
                    ChkNum += 1
                Next
            Next
        End If

        Dim bRows = (From A In BatchRows Order By A.empnum, A.run_import_id).ToList
        For RowIX = 0 To bRows.Count - 1
            bRows(RowIX).number = RowIX
        Next

        BatchList.pr_batch_rows.AddRange(BatchRows)

        DB.SubmitChanges()

        'Import Notes
        Dim ImportNotes = (From A In BatchRows Where A.ImportNote IsNot Nothing).ToList
        If ImportNotes.Count > 0 Then
            For Each n In ImportNotes
                ImportNotesList.Add(New pr_batch_note With {.Conum = Me.CoNum, .PrNum = Me.PrNum, .ListID = Me.BatchList.id, .EmployeeID = n.empnum, .LineNumber = n.number,
                                                                         .CheckNum = n.run_import_id,
                                                                         .Note = n.ImportNote, .EnteredBy = "Import", .DateEntered = Now, .rowguid = Guid.NewGuid})
            Next
            Me.BandedGridView1.Columns("ImportNotes").Visible = True
        End If
        If ImportNotesList.Count > 0 Then
            Dim ExistingRecords = (From A In DB.pr_batch_notes Where A.Conum = Me.CoNum AndAlso A.PrNum = Me.PrNum).ToList
            For Each note In ImportNotesList
                Dim exist = (From A In ExistingRecords Where A.EmployeeID = note.EmployeeID AndAlso A.CheckNum.GetValueOrDefault = note.CheckNum.GetValueOrDefault AndAlso A.Note = note.Note).FirstOrDefault
                If exist Is Nothing Then
                    DB.pr_batch_notes.InsertOnSubmit(note)
                Else
                    exist.ListID = Me.BatchList.id
                End If
            Next
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save batch notes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
        End If
        'Save Notes
        If Me.DatesAndOptions IsNot Nothing Then
            Dim Notes As List(Of pr_batch_note) = Me.DatesAndOptions.PrBatchNotes_BindingSource.List
            Dim NotesEnt = (From A In DB.pr_batch_notes Where A.ListID = BatchList.id).ToList
            For X = 0 To Notes.Count - 1
                Dim N = Notes(X)
                If N.ListID = Guid.Empty Then
                    N.ListID = BatchList.id
                    N.Conum = CoNum
                    N.PrNum = Me.PrNum
                    N.EnteredBy = UserName
                    N.DateEntered = Now
                    N.rowguid = Guid.NewGuid
                    DB.pr_batch_notes.InsertOnSubmit(N)
                Else
                    Dim Ent = (From A In NotesEnt Where A.NoteID = N.NoteID).Single
                    Ent.Note = N.Note
                End If
            Next
            DB.SubmitChanges()
        End If

        'Save Employee Change Log
        Dim Records = (From A In DB.pr_batch_employee_changes Where A.batch_id = BatchList.id).ToList
        Me.EmployeeChangeLog.ForEach(Sub(p) p.batch_id = BatchList.id)
        For Each itm In Me.EmployeeChangeLog
            If itm.rowguid.Equals(Guid.Empty) Then
                itm.rowguid = Guid.NewGuid
                DB.pr_batch_employee_changes.InsertOnSubmit(itm)
            Else
                Dim Rec = (From A In Records Where A.rowguid = itm.rowguid).FirstOrDefault
                If Rec Is Nothing Then
                    DB.pr_batch_employee_changes.InsertOnSubmit(itm)
                Else
                    Rec.change_log = itm.change_log
                End If
            End If
        Next
        If Not DB.SaveChanges() Then
            Return ' Exit if save failed due to concurrency conflict
        End If

        'Save Layout
        If HasLayoutChanges AndAlso Not IsAuto Then
            SaveLayout(DB)
        End If

        'Add Note
        If Me.DatesAndOptions IsNot Nothing AndAlso Me.DatesAndOptions.SelectedCalendarRec IsNot Nothing Then
            Dim Cal = Me.DatesAndOptions.SelectedCalendarRec
            Dim NewNote = New CalendarNote With {.CalID = Cal.CalID, .CoNum = Cal.CoNum, .PeriodID = Cal.PeriodID,
                                                    .Note = If(Me.IsNew, "Payroll Entered", "Payroll Saved"),
                                                    .EnteredBy = UserName, .DateEntered = Now}
            DB.CalendarNotes.InsertOnSubmit(NewNote)
            If Not DB.SaveChanges() Then
                Return ' Exit if save failed due to concurrency conflict
            End If
        End If

        TblPayrollData.AcceptChanges()
        IsNew = False
        Dim AutoSaveFile As String = $"{IO.Path.GetTempPath}PowerGridAutoSave\{Me.CoNum}-{Me.PrNum}.xml"
        If IO.File.Exists(AutoSaveFile) Then
            IO.File.Delete(AutoSaveFile)
        End If
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Save()
        'ShowList(True)
        Me.Close()
    End Sub

    Function CreateBatchRow(ByVal Row As DataRow, ByVal Ix As Integer, Optional ByVal CheckNumber As Integer = -1) As pr_batch_row
        Dim BRow As New pr_batch_row

        If IsDBNull(Row("division")) OrElse IsDBNull(Row("department")) Then
            If Not EmployeeList?.Count > 0 Then
                LoadEmployeeList()
            End If
            Dim EmpNum As Decimal = Row.Field(Of Decimal)("empnum")
            Dim EmpRecord = (From A In EmployeeList Where A.EMPNUM = EmpNum).FirstOrDefault
            If EmpRecord IsNot Nothing Then
                If IsDBNull(Row("division")) Then Row("division") = nz(EmpRecord.DIVNUM, DBNull.Value)
                If IsDBNull(Row("department")) Then Row("department") = nz(EmpRecord.DEPTNUM, DBNull.Value)
            End If
        End If

        If IsDBNull(Row("division")) Then Throw New Exception("Employee {0} does not have a Division Number setup.".FormatWith(Row("empnum")))
        If IsDBNull(Row("department")) Then Throw New Exception("Employee {0} does not have a Department Number setup.".FormatWith(Row("empnum")))

        With BRow
            .id = Guid.NewGuid
            .conum = Me.CoNum
            .divnum = Row("division")
            .deptnum = Row("department")
            .empnum = Row("empnum")
            .number = Ix
            .job1 = nz(Row("job"), Nothing)
            .job2 = nz(Row("job2"), Nothing)
            .job3 = nz(Row("job3"), Nothing)
            .job4 = nz(Row("job4"), Nothing)
            .job5 = nz(Row("job5"), Nothing)
            Dim WorkDate As Nullable(Of Date) = Nothing
            If Not IsDBNull(Row("WorkDate")) Then
                Dim d As Date
                If Date.TryParse(Row("WorkDate"), d) Then
                    WorkDate = d
                End If
            End If
            .date = WorkDate
            .week_num = nz(Row("weeknum"), Nothing)
            .created_by = UserName
            .created_date = Now
            .modified_by = UserName
            .modify_date = Now
            .category = 2
            .active = "YES"
            .run_auto_hours = ToYesNoString(nz(Row("hrs"), False))
            .run_auto_pays = ToYesNoString(nz(Row("pays"), False))
            .run_auto_deds = ToYesNoString(nz(Row("deds"), False))
            .run_auto_memos = ToYesNoString(nz(Row("memos"), False))
            .run_sick = ToYesNoString(nz(Row("sick"), False))
            .run_vacation = ToYesNoString(nz(Row("vac"), False))
            .run_personal = ToYesNoString(nz(Row("per"), False))
            .run_dd_flag = ToYesNoString(nz(Row("dd"), False))
            If CheckNumber > 0 Then
                .run_import_id = CheckNumber
            Else
                .run_import_id = CType(nz(Row("chknum"), 1), Integer)
            End If
            .chk_memo = nz(Row("checkmemo"), Nothing)
            Dim TermDate As Nullable(Of Date) = Nothing
            If Not IsDBNull(Row("TermDate")) Then
                TermDate = Date.Parse(Row("TermDate"))
            End If
            .term_date = TermDate
            .distributed = ToYesNoString(nz(Row("distributed"), False))
        End With
        Return BRow
    End Function

    Function ColumnHasData(ByVal Column As BandedGridColumn) As Boolean
        For RowIX = 0 To Me.BandedGridView1.RowCount - 1
            Dim V = Me.BandedGridView1.GetRowCellValue(RowIX, Column)
            If V IsNot Nothing AndAlso Not IsDBNull(V) Then
                Return True
            End If
        Next
        Return False
    End Function

    Sub CreatePayrollDataTable(ByVal IsNew As Boolean)
        If IsNew Then _
            TblPayrollData = New DataTable("PayrollData")
        For Each ACol In AvailCols
            Dim Col As New DataColumn(ACol.sql_column)
            If TblPayrollData.Columns.Contains(Col.ColumnName) Then Continue For
            Select Case ACol.type
                Case "D", "M"
                    Col.DataType = Type.GetType("System.Decimal")
                Case "P"
                    Select Case ACol.ColumnName
                        Case "Rate #"
                            Col.DataType = Type.GetType("System.Int32")
                        Case Else
                            Col.DataType = Type.GetType("System.Decimal")
                    End Select
                Case "F"
                    Select Case ACol.sql_column.ToLower
                        Case "pay", "distribute", "hrs", "pays", "deds", "memos", "sick", "vac", "per", "dd", "sales", "distributed"
                            Col.DataType = Type.GetType("System.Boolean")
                        Case "empnum", "division", "department", "job", "chknum", "weeknum", "conum"
                            Col.DataType = Type.GetType("System.Decimal")
                        Case "workdate", "termate"
                            Col.DataType = GetType(Date)
                        Case "linerate"
                            Col.DataType = Type.GetType("System.Decimal")
                        Case Else
                            Col.DataType = Type.GetType("System.String")
                    End Select
            End Select
            Col.Caption = ACol.ColumnName
            TblPayrollData.Columns.Add(Col)
        Next
        If Not IsNew Then Exit Sub
        'Notes
        TblPayrollData.Columns.Add("HasNotes", Type.GetType("System.Int32"))
        TblPayrollData.Columns.Add("ImportNotes", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("HasAutos", Type.GetType("System.Boolean"))
        'Number
        TblPayrollData.Columns.Add("number", Type.GetType("System.Int32"))
        'CheckLineNum
        TblPayrollData.Columns.Add("CheckLineNumber", Type.GetType("System.Int32"))
        'Multi Rates
        TblPayrollData.Columns.Add("HasMultiRates", Type.GetType("System.Boolean"))
        TblPayrollData.Columns.Add("RateCode", Type.GetType("System.String"))
        'Dept Description - used for sorting
        TblPayrollData.Columns.Add("DEPT_DESC", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("EE_DEPTNUM", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("EE_DIVNUM", Type.GetType("System.Decimal"))
        'PAY_FREQ - used for sorting
        TblPayrollData.Columns.Add("PAY_FREQ", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("PAY_FREQ_SORT", Type.GetType("System.String"))
        'USERDEF1 - used for sorting
        TblPayrollData.Columns.Add("USERDEF1", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("DefaultSort", Type.GetType("System.String"))
        'USERDEF21 - used for p30
        TblPayrollData.Columns.Add("USERDEF21", Type.GetType("System.String"))
        'Net
        TblPayrollData.Columns.Add("Net", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("NetOverrideType", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("NetOverrideDedNum", Type.GetType("System.Decimal"))
        'Tax Overrides
        TblPayrollData.Columns.Add("FicaOnly", Type.GetType("System.Boolean"))
        TblPayrollData.Columns.Add("FedOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("STOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("LOCOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("DBOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("FLIOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("TaxFrequency", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("ChkType", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("ChkNumber", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("OASDIOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("MedicareOverride", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("WrkStateOverride", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("ResStateOverride", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("UIStateOverride", Type.GetType("System.String"))

        'Address
        TblPayrollData.Columns.Add("STREET", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("CITY", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("ADDR_STATE", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("ZIP", Type.GetType("System.String"))
        TblPayrollData.Columns.Add("FED_STATUS", Type.GetType("System.String"))

        'Extra/Fixed Tax overrides
        'Address
        TblPayrollData.Columns.Add("FedExtra", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("FedFixed", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("ST_Extra", Type.GetType("System.Decimal"))
        TblPayrollData.Columns.Add("ST_Fixed", Type.GetType("System.Decimal"))

        'SecondCheck
        TblPayrollData.Columns.Add("IsSecondCheck", Type.GetType("System.Boolean"))
        TblPayrollData.Columns.Add("UCIState", Type.GetType("System.String"))

        'Create defaults
        TblPayrollData.Columns("pay").DefaultValue = BatchList.run_pay_salary = "YES"
        TblPayrollData.Columns("pays").DefaultValue = BatchList.run_auto_pays = "YES"
        TblPayrollData.Columns("deds").DefaultValue = BatchList.run_auto_deds = "YES"
        TblPayrollData.Columns("memos").DefaultValue = BatchList.run_auto_memos = "YES"
        TblPayrollData.Columns("sick").DefaultValue = BatchList.run_sick = "YES"
        TblPayrollData.Columns("vac").DefaultValue = BatchList.run_vacation = "YES"
        TblPayrollData.Columns("per").DefaultValue = BatchList.run_vacation = "YES"
        TblPayrollData.Columns("dd").DefaultValue = BatchList.run_dd_flag = "YES"
        TblPayrollData.Columns("distribute").DefaultValue = False
        TblPayrollData.Columns("distributed").DefaultValue = False
        TblPayrollData.Columns("FicaOnly").DefaultValue = False
        TblPayrollData.Columns("IsSecondCheck").DefaultValue = False
    End Sub

    Async Function Process(sendPayrollToAudit As Boolean) As Threading.Tasks.Task(Of Integer)
        If Not SecondChecksOnly AndAlso sendPayrollToAudit AndAlso Not SaveHoldReason() Then
            Return 0
        End If

        rpgSubmitGrid.Enabled = False
        Me.LayoutControl.Enabled = False

        If Not IsAuto Then ShowImportNotes()
        Save(Not sendPayrollToAudit)

        Dim PayRec As pr_batch_in_process = GetPayRec()

        TryShowWaitForm(False)

        Dim intID As Integer

        Dim pp As ProcessPayroll = Nothing
        Dim pp2 As ProcessPayroll = Nothing

        'If Not UsePPxLibrary Then
        '    QueuePayrollProcessor.Initialize(GetConnectionString, frmLogger, Nothing, UserName)
        '    pp = New ProcessPayroll(CoNum, PrNum, sendPayrollToAudit)
        '    pp.SecondChecksOnly = Me.SecondChecksOnly
        '    pp.IsReadOnly = Me.IsReadOnly
        'Else
        pp2 = New ProcessPayroll(CoNum, PrNum, sendPayrollToAudit)
        pp2.SecondChecksOnly = Me.SecondChecksOnly
        pp2.IsReadOnly = Me.IsReadOnly
        'End If

        If Not IsAuto Then
            'If Not UsePPxLibrary Then
            '    AddHandler pp.Logger.ActionLog, AddressOf DisplayLog
            '    AddHandler pp.StatusChanged, AddressOf SetProcessStatus

            '    Dim ActionList = New List(Of ProcessPowergridStatus)
            '    Dim ppActionList = pp.ActionList.OrderBy(Function(p) p.StepNumber).ToList
            '    For Each action In ppActionList
            '        ActionList.Add(New ProcessPowergridStatus With {.Details = action.Details, .Status = action.Status, .StepName = action.StepName, .StepNumber = action.StepNumber, .SubStatus = action.SubStatus, .SubStep = action.SubStep})
            '    Next

            '    ProcessingStatusForm = New frmBrandsPowerGridProcess With {.Opener = Me, .ActionList = ActionList.OrderBy(Function(p) p.StepNumber).ToList, .CallBackForm = Me.CallBackForm}
            'Else
            AddHandler pp2.Logger.ActionLog, AddressOf DisplayLog
            AddHandler pp2.StatusChanged, AddressOf SetProcessStatus
            ProcessingStatusForm = New frmBrandsPowerGridProcess With {.Opener = Me, .ActionList = pp2.ActionList.OrderBy(Function(p) p.StepNumber).ToList, .CallBackForm = Me.CallBackForm}
            'End If

            AddHandler ProcessingStatusForm.FormClosing, Sub()
                                                             'rpgSubmitGrid.Enabled = True
                                                             'Me.LayoutControl.Enabled = False
                                                         End Sub

            Dim activeChanged As EventHandler = Sub(sender As Object, e As EventArgs)
                                                    ProcessingStatusForm.Visible = Me.IsActive
                                                End Sub

            AddHandler Me.Deactivate, activeChanged
            AddHandler Me.Activated, activeChanged

            Me.lcgHeader.Enabled = False
            ProcessingStatusForm.Show(Me)
        End If

        Try
            'If Not UsePPxLibrary Then
            '    intID = Await pp.Process(UserName)
            '    Me.AutoIsProcessed = pp.AutoIsProcessed
            '    Me.AutoIsSubmitted = pp.AutoIsSubmitted
            'Else
            intID = Await pp2.Process(UserName)
            Me.AutoIsProcessed = pp2.AutoIsProcessed
            Me.AutoIsSubmitted = pp2.AutoIsSubmitted
            'End If
        Catch ex As Exception
            frmLogger.Error(ex, "Error in processing payroll. Co#: {CoNum} Pr#: {PrNum}", CoNum, PrNum)
            DisplayErrorMessage("Error in processing payroll. Co#: {0} Pr#: {1}".FormatWith(CoNum, PrNum), ex)
        End Try
Finish:
        If Not IsAuto Then
            'If Not UsePPxLibrary Then
            '    RemoveHandler pp.Logger.ActionLog, AddressOf DisplayLog
            '    RemoveHandler pp.StatusChanged, AddressOf SetProcessStatus
            'Else
            RemoveHandler pp2.Logger.ActionLog, AddressOf DisplayLog
            RemoveHandler pp2.StatusChanged, AddressOf SetProcessStatus
            'End If

            ProcessingStatusForm.EnableOK()
        End If
        Return intID
    End Function

    Private Sub frmBrandsPowerGrid_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        Try
            If e.KeyCode = Keys.U AndAlso e.Control AndAlso e.Shift AndAlso e.Alt Then
                bciProcessInBackground.Checked = False
                frmLogger.Debug("User clicked on Keys.U AndAlso e.Control AndAlso e.Shift AndAlso e.Alt")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error In KeyDown", ex)
        End Try
    End Sub

    Private Sub bciProcessInBackground_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bciProcessInBackground.ItemClick
        frmLogger.Debug("User clicked on bciProcessInBackground. Checked: {Checked}", bciProcessInBackground.Checked)
    End Sub

    Private Sub AutoSave()
        Dim TempFolder = IO.Path.GetTempPath & "PowerGridAutoSave\"
        IO.Directory.CreateDirectory(TempFolder)

        Dim FileName As String = IO.Path.Combine(TempFolder, $"{Me.CoNum}-{Me.PrNum}.xml")
        TblPayrollData.WriteXml(FileName)
    End Sub

    Private Sub RecoverPayroll()
        Dim TempFolder = IO.Path.GetTempPath & "PowerGridAutoSave\"
        If Not IO.Directory.Exists(TempFolder) Then
            DisplayMessageBox("No Auto Saved Payrolls found on this computer")
            Return
        End If
        Dim dir = New DirectoryInfo(TempFolder)
        Dim AutoSaveFiles = dir.GetFiles($"{Me.CoNum}-*.xml")
        If AutoSaveFiles.Count = 0 Then
            DisplayMessageBox($"No Auto Saved Payrolls found for company {Me.CoNum}")
            Return
        End If
        Dim cb = New ImageComboBoxEdit
        For Each file In AutoSaveFiles
            Dim fileTime = If(file.LastWriteTime.Date = Today, file.LastWriteTime.ToShortTimeString, file.LastWriteTime.ToString("g"))
            cb.Properties.Items.Add(New Controls.ImageComboBoxItem($"{Path.GetFileNameWithoutExtension(file.Name)} - {fileTime}", file.FullName))
        Next
        Dim inputArgs = New XtraInputBoxArgs With {.Caption = "Recover Unsaved Payroll", .Prompt = "Select File", .Editor = cb}
        Dim input = XtraInputBox.Show(inputArgs)
        If input IsNot Nothing Then
            TblPayrollData.BeginLoadData()
            TblPayrollData.Clear()
            TblPayrollData.ReadXml(input)
            TblPayrollData.EndLoadData()
            Me.BandedGridView1.RefreshData()
        End If
    End Sub

    Private Sub BandedGridView1_RowUpdated(sender As Object, e As RowObjectEventArgs) Handles BandedGridView1.RowUpdated
        Task.Run(Sub()
                     Try
                         AutoSave()
                     Catch ex As Exception

                     End Try
                 End Sub)
    End Sub
End Class