﻿Imports System.ComponentModel

Public Class frmEmployeeAutoPaysAndDeds

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeeNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PayrollNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CheckCounter As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PowerGridForm As frmBrandsPowerGrid
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Edit As StartEdit
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property SetAll0 As Boolean

    Dim DB As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Pays As List(Of EmpAutoPay)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Deds As List(Of EmpAutoPay)

    Private _OriginalValue As Decimal?

    Private Sub frmEmployeeAutoPaysAndDeds_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
        Dim grid As DevExpress.XtraGrid.GridControl
        If Edit IsNot Nothing Then
            If Edit.Type = "P" Then
                grid = Me.GridPays
            Else
                grid = Me.GridDeds
            End If
            grid.Select()
            grid.Focus()
            grid.MainView.Focus()
        End If
    End Sub

    Private Sub frmEmployeeAutoPaysAndDeds_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            DB = New dbEPDataDataContext(GetConnectionString)

            Me.OTHERPAYBindingSource.DataSource = (From A In DB.OTHER_PAYS Where A.CONUM = CoNum AndAlso A.OACTIVE = "YES" Select A.OTH_PAY_DESC, A.OTH_PAY_NUM, A.CATEGORY).ToList

            Dim Data = DB.prc_GetAutoPaysForPayroll(Me.CoNum, Me.PayrollNum, Me.EmployeeNum, False, False).ToList
            Dim AutoPays = (From A In Data Select EmpAutoPay.From_SP_Results(A)).ToList
            Pays = (From A In AutoPays Where A.Type = "P").ToList
            Deds = (From A In AutoPays Where A.Type = "D").ToList

            Me.EmpAutoPaysBindingSource.DataSource = Pays
            Me.EmpAutoDedsBindingSource.DataSource = Deds
            GridViewPays.BestFitColumns()
            GridViewDeds.BestFitColumns()
            SetStartEdit()
        Catch ex As Exception
            DisplayErrorMessage("Error loading form", ex)
        End Try
    End Sub

    Public Sub SetStartEdit()
        Try
            If Me.Edit IsNot Nothing Then
                Dim view As DevExpress.XtraGrid.Views.Grid.GridView
                Dim Row As EmpAutoPay
                Dim Source As List(Of EmpAutoPay)

                Me.GridPays.ForceInitialize()
                Me.GridDeds.ForceInitialize()
                If Me.Edit.Type = "P" Then
                    view = Me.GridViewPays
                    Source = Me.Pays
                Else
                    view = Me.GridViewDeds
                    Source = Me.Deds
                End If
                Row = (From A In Source Where A.CL_Code = Me.Edit.Code AndAlso (A.ChkCounter = Me.Edit.ChkCounter OrElse (A.ChkCounter = 0 AndAlso Me.Edit.ChkCounter = 1))).First
                For X = 0 To view.RowCount - 1
                    If view.GetRow(X) Is Row Then
                        view.FocusedRowHandle = X
                        view.FocusedColumn = view.Columns("TTOOverride")
                        If view.FocusedColumn Is Nothing Then
                            view.FocusedColumn = view.Columns("FormattedTTOOverride")
                        End If
                        view.ShowEditor()
                        Dim editor As DevExpress.XtraEditors.TextEdit = view.ActiveEditor
                        If Not view.EditingValue = Me.Edit.EditValue Then
                            editor.EditValue = Me.Edit.EditValue
                            editor.IsModified = True
                            'view.EditingValue = Me.Edit.EditValue
                            'view.SetFocusedValue(Me.Edit.EditValue)
                        End If
                        editor.SelectionStart = 100
                        editor.Properties.ValidateOnEnterKey = True
                        Exit For
                    End If
                Next

                Me.AcceptButton = Me.btnSave
                Me.CancelButton = Me.btnCancel
            ElseIf SetAll0 Then
                For Each row In Me.Pays
                    row.TTOOverride = 0
                Next
                For Each row In Me.Deds
                    row.TTOOverride = 0
                Next
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error SetStartEditor", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        Dim PayRecords = (From A In DB.EMP_OPS Where A.CONUM = CoNum AndAlso A.EMPNUM = EmployeeNum).ToList
        Dim DedRecords = (From A In DB.EMP_DEDS Where A.CONUM = CoNum AndAlso A.EMPNUM = EmployeeNum).ToList
        Dim TTORecords = (From A In DB.pr_batch_override_autos Where A.CONUM = CoNum AndAlso A.EMPNUM = EmployeeNum AndAlso A.PRNUM = PayrollNum AndAlso A.CHK_COUNTER = CheckCounter).ToList
        For X = 0 To Pays.Count - 1
            Dim p = Pays(X)
            Dim TTO = (From A In TTORecords Where A.CL_CODE = p.CL_Code AndAlso A.CL_DEPT = p.CL_DEPT AndAlso A.TYPE = "P").FirstOrDefault
            Dim pEnt As EMP_OP
            Dim Employee = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.EMPNUM = EmployeeNum Select A.DIVNUM, A.DEPTNUM).Single
            If Not p.IsNewRec Then
                pEnt = (From A In PayRecords Where A.OPS_NUM = p.CL_Code AndAlso p.CL_DEPT = p.CL_DEPT).First
            Else
                pEnt = New EMP_OP With {.EMPNUM = EmployeeNum,
                                        .CONUM = CoNum,
                                        .OPS_NUM = p.CL_Code,
                                        .OPS_DIVNUM = Employee.DIVNUM,
                                        .OPS_DEPTNUM = Employee.DEPTNUM,
                                        .first_only = "NO", .supp_pr = "NO", .all_prds = "NO", .period1 = "NO", .period2 = "NO", .period3 = "NO", .period4 = "NO", .period5 = "NO"}
            End If
            pEnt.OPS_AMOUNT = p.Amount
            pEnt.op_rate = p.Rate

            If p.TTOOverride.HasValue AndAlso p.TTOOverride.GetValueOrDefault <> p.Amount.GetValueOrDefault Then
                If TTO Is Nothing Then
                    TTO = New pr_batch_override_auto With {.CONUM = CoNum,
                                                            .EMPNUM = EmployeeNum,
                                                            .PRNUM = PayrollNum,
                                                            .CHK_COUNTER = CheckCounter,
                                                            .TYPE = "P",
                                                            .CL_CODE = p.CL_Code,
                                                            .CL_DEPT = p.CL_DEPT,
                                                            .OriginalAmount = p.Amount,
                                                            .rowguid = Guid.NewGuid}
                    DB.pr_batch_override_autos.InsertOnSubmit(TTO)
                End If 'New Record
                TTO.AMOUNT = p.TTOOverride
            Else
                If TTO IsNot Nothing Then
                    DB.pr_batch_override_autos.DeleteOnSubmit(TTO)
                End If
            End If 'Has Value
        Next

        'Deductions
        For X = 0 To Deds.Count - 1
            Dim d = Deds(X)
            Dim TTO = (From A In TTORecords Where A.CL_CODE = d.CL_Code AndAlso A.CL_DEPT = d.CL_DEPT AndAlso A.TYPE = "D").FirstOrDefault
            Dim dEnt As EMP_DED
            If Not d.IsNewRec Then
                dEnt = (From A In DedRecords Where A.DED_NUM_E = d.CL_Code AndAlso A.DED_DEPTNUM = d.CL_DEPT).First
                If d.Amount <> dEnt.DED_AMOUNT_E Then
                    Dim scdl = (From A In DB.emp_deds_amount_schedules Where A.conum = CoNum AndAlso A.empnum = EmployeeNum AndAlso A.deduction_key = dEnt.deduction_key AndAlso A.end_date Is Nothing).First
                    scdl.end_date = d.StartDate.Value.AddDays(-1)
                    Dim nScdl = New emp_deds_amount_schedule With {.conum = CoNum, .empnum = EmployeeNum, .deduction_key = dEnt.deduction_key,
                                                                   .start_date = d.StartDate.Value, .amount = d.Amount}
                    DB.emp_deds_amount_schedules.InsertOnSubmit(nScdl)
                End If
            End If
            If d.TTOOverride.HasValue AndAlso d.TTOOverride.GetValueOrDefault <> d.Amount.GetValueOrDefault Then
                If TTO Is Nothing Then
                    TTO = New pr_batch_override_auto With {.CONUM = CoNum,
                                                            .EMPNUM = EmployeeNum,
                                                            .PRNUM = PayrollNum,
                                                            .CHK_COUNTER = CheckCounter,
                                                            .TYPE = "D",
                                                            .CL_CODE = d.CL_Code,
                                                            .CL_DEPT = d.CL_DEPT,
                                                            .IsPercent = (d.DedCalc & "").Contains("Percent"),
                                                            .OriginalAmount = d.Amount,
                                                            .rowguid = Guid.NewGuid}
                    DB.pr_batch_override_autos.InsertOnSubmit(TTO)
                End If 'New Record
                TTO.AMOUNT = d.TTOOverride
            Else
                If TTO IsNot Nothing Then
                    DB.pr_batch_override_autos.DeleteOnSubmit(TTO)
                End If
            End If 'Has Value
        Next
        DB.SubmitChanges()
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Edit = Nothing
        Me.frmEmployeeAutoPaysAndDeds_Load(sender, e)
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
    End Sub

    Private Sub btnAddAutoPay_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddAutoPay.Click
        Me.GridViewPays.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
    End Sub

    Private Sub btnAddAutoDeduction_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddAutoDeduction.Click
        Me.GridViewDeds.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
    End Sub

    Private Sub riLookupEarning_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riLookupEarning.EditValueChanged
        Dim editor As DevExpress.XtraEditors.LookUpEdit = CType(sender, DevExpress.XtraEditors.LookUpEdit)
        Dim row = editor.Properties.GetDataSourceRowByKeyValue(editor.EditValue)
        Me.GridViewPays.SetRowCellValue(Me.GridViewPays.FocusedRowHandle, "Category", row.GetType.GetProperty("CATEGORY").GetValue(row, Nothing))
    End Sub

    'Private Sub GridViewPays_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewPays.PopupMenuShowing
    '    If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
    '        e.Menu.Items.Clear()
    '        Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete", AddressOf OnDeleteRowClick)
    '        mItem.Tag = e.HitInfo.RowHandle
    '        e.Menu.Items.Add(mItem)
    '    End If
    'End Sub

    'Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
    '    If Not MessageBox.Show("Are you sure you want to delete this record?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes Then
    '        Exit Sub
    '    End If
    '    Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
    '    Dim rowHandle As Integer = item.Tag
    '    Dim Row As EmpAutoPays = Me.GridViewPays.GetRow(rowHandle)
    '    Me.GridViewPays.DeleteRow(rowHandle)
    'End Sub

    Private Sub GridView_ShowingEditor(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles GridViewPays.ShowingEditor, GridViewDeds.ShowingEditor
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        If view.FocusedColumn.FieldName = "CL_Code" AndAlso Not view.IsNewItemRow(view.FocusedRowHandle) Then
            e.Cancel = True
        End If
    End Sub

    Private Sub GridView_RowUpdated(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridViewPays.RowUpdated, GridViewDeds.RowUpdated
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        view.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.None
    End Sub

    Private Sub GridViewPays_CellValueChanging(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridViewPays.CellValueChanging
        If e.Column.FieldName = "TTOOverride" OrElse e.Column.FieldName = "FormattedTTOOverride" Then
            _OriginalValue = CType(e.Column.View.GetRow(e.RowHandle), EmpAutoPay).TTOOverride
        End If
    End Sub

    Protected Sub GridView_CellValueChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridViewPays.CellValueChanged, GridViewDeds.CellValueChanged
        If e.Value Is Nothing Then GoTo A
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        Dim isDeduction As Boolean = view.Name = GridViewDeds.Name
        Dim row As EmpAutoPay = view.GetRow(e.RowHandle)
        If e.Column.FieldName = "TTOOverride" OrElse e.Column.FieldName = "FormattedTTOOverride" Then
            If e.Column.FieldName = "FormattedTTOOverride" Then
                If row.FormattedAmount = e.Value Then GoTo A
            Else
                If row.Amount.GetValueOrDefault = e.Value Then GoTo A
            End If

            If (row.DedCalc & "").StartsWith("Percent") AndAlso e.Column.FieldName = "FormattedTTOOverride" Then

            End If
            Dim frm As New frmChangeRateAutoPay
            frm.lblPayType.Text = row.CodeDescription
            frm.tbOldRate.EditValue = row.FormattedAmount
            frm.NewRate.EditValue = e.Value

            If Not row.IsOpen Then
                frm.Cancel_Button.Enabled = False
                frm.lblMessage.Visible = True
            End If

            Dim results = frm.ShowDialog
            frm.Dispose()
            If results = System.Windows.Forms.DialogResult.OK Then
                If isDeduction Then
                    Dim frm2 = New frmChangeAutoDedStartDate
                    Dim results2 = frm2.ShowDialog
                    Dim StartDate As Date? = Nothing
                    If results2 = System.Windows.Forms.DialogResult.OK Then
                        StartDate = frm2.DateEdit1.EditValue
                    End If
                    frm2.Dispose()
                    If StartDate.HasValue Then
                        Dim dRow As EmpAutoPay = view.GetRow(e.RowHandle)
                        dRow.Amount = Decimal.Parse(e.Value, Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture)
                        dRow.StartDate = StartDate
                        view.RefreshRow(e.RowHandle)
                        'view.SetRowCellValue(e.RowHandle, "FormattedAmount", e.Value)
                        'view.SetRowCellValue(e.RowHandle, "StartDate", StartDate)
                    Else
                        view.SetRowCellValue(e.RowHandle, "FormattedTTOOverride", Nothing)
                    End If
                Else
                    'Dim OPRow = (From A In DB.EMP_OPs Where A.CONUM = Me.CoNum And A.EMPNUM = Me.EmployeeNum And A.OPS_NUM = row.CL_Code).FirstOrDefault
                    'If OPRow IsNot Nothing Then
                    '    OPRow.OPS_AMOUNT = Decimal.Parse(e.Value)
                    '    DB.SubmitChanges()
                    'End If
                    view.SetRowCellValue(e.RowHandle, "Amount", e.Value)
                End If
            ElseIf results = System.Windows.Forms.DialogResult.Abort Then
                view.SetRowCellValue(e.RowHandle, e.Column, _OriginalValue)
            End If
        End If
A:
        _OriginalValue = Nothing
    End Sub

    Private Sub GridView_CustomDrawCell(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs) Handles GridViewPays.CustomDrawCell, GridViewDeds.CustomDrawCell
        If e.RowHandle < 0 Then Exit Sub
        Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = sender
        Dim row As EmpAutoPay = gridView.GetRow(e.RowHandle)
        If Not row.IsOpen Then Exit Sub
        If Me.PowerGridForm Is Nothing Then Exit Sub
        Select Case e.Column.FieldName
            Case "SuppPrd"
                If (From A In Me.PowerGridForm.CalInfo Where A.IsSupp).Any AndAlso row.SuppPrd Then
                    e.Appearance.BackColor = Color.Lime
                End If
            Case "AllPeriods"
                If (From A In Me.PowerGridForm.CalInfo Where Not A.IsSupp).Any AndAlso row.AllPeriods Then
                    e.Appearance.BackColor = Color.Lime
                End If
            Case "Prd1", "Prd2", "Prd3", "Prd4", "Prd5"
                Dim Prd = Array.IndexOf({"Prd1", "Prd2", "Prd3", "Prd4", "Prd5"}, e.Column.FieldName) + 1
                If (From A In Me.PowerGridForm.CalInfo Where Not A.IsSupp AndAlso A.Prd = Prd AndAlso Not A.IsSupp AndAlso Not row.AllPeriods).Count > 0 Then
                    e.Appearance.BackColor = Color.Lime
                End If
        End Select
    End Sub

    Public Class StartEdit
        Property Code As Decimal
        Property Type As String
        Property ChkCounter As Integer
        Property EditValue As String
    End Class



End Class