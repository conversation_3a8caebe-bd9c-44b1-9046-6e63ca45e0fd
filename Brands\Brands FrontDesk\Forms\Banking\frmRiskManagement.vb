﻿Imports System.ComponentModel
Imports System.Data
Imports Brands_FrontDesk.Ach
Imports DevExpress.Utils
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Navigation
Imports DevExpress.XtraEditors
Imports Newtonsoft.Json

Public Class frmRiskManagement
    Private Property db As dbEPDataDataContext
    Private Property SelectedQueue As String
    Private Property _AchSettings As AchSettings
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _AchEmailService As AchEmailService
    Dim Logger As Serilog.ILogger

    Dim PayrollAlerts As List(Of prc_RptPayrollAlertsResult)

    Private Async Sub frmRiskManagement_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Logger = modGlobals.Logger.ForContext(Of frmRiskManagement)
            db = New dbEPDataDataContext(GetConnectionString)
            GridView1.ActiveFilterString = "[PAYROLL_STATUS] = 'DD Ready'"
            GridViewAlertEmails.ActiveFilterString = "IsLastMonth([Dtm]) Or IsThisMonth([Dtm])"
            GridViewAchTran.ActiveFilterString = "[EmpNum] Is Null And [DebitAmount] Is Not Null"
            'Await LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error loading form", ex)
        End Try
    End Sub

    Private Async Function LoadData() As Threading.Tasks.Task
        Try
            'Me.lcRoot.ShowProgessPanel()
            SplashScreenManager1.ShowWaitForm()
            lcRoot.Show()
            LoadSettings()
            'Query("SET ARITHABORT OFF;exec custom.prc_RptPayrollAlerts")
            PayrollAlerts = (Await QueryAsync(Of prc_RptPayrollAlertsResult)("SET ARITHABORT ON;exec custom.prc_RptPayrollAlerts")).ToList()
            Dim dtCols As DataTable
            dtCols = Query($"EXEC custom.prc_Rpt_FD_GridControlColumns 'RiskManagement', 1, ''")
            GridView1.Columns.Clear()
            GridView1.SetCols(dtCols)

            aceStatus.Elements.Clear()
            Dim queueList = GetUdfValueSplitted("RiskManagementStatus")

            lueStatus.Properties.DataSource = queueList
            lueDDStatus.Properties.DataSource = queueList
            lueTaxStatus.Properties.DataSource = queueList

            For Each queue In queueList
                aceStatus.Elements.Add(New AccordionControlElement(ElementStyle.Item) With {.Text = queue, .Tag = queue})
            Next
            aceStatus.Expanded = True
            AccordionControl1.SelectedElement = aceStatus.Elements(0)
            LoadCount()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        Finally
            'lcRoot.HideProgressPanel()
            SplashScreenManager1.CloseWaitForm()
        End Try
    End Function

    Private Sub LoadSettings()
        _AchEmailService = New AchEmailService(_AchSettings, True)
        _AchSettings = JsonConvert.DeserializeObject(Of AchSettings)(GetUdfValue("AchSettingsJson"))

        For Each emlTemplate In db.ReportEmailTeplates.Where(Function(t) t.Tag = "Ach")
            Dim bbi = New BarButtonItem(RibbonControl1.Manager, emlTemplate.Name) With {.Tag = emlTemplate}
            AddHandler bbi.ItemClick, AddressOf bbiEmailClient_ItemClick
        Next
    End Sub

    Private Sub bbiEmailClient_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiEmailClient.ItemClick
        EmailClient(False)
    End Sub

    Private Async Sub EmailClient(selectedOnly As Boolean)
        Try
            'lcRoot.ShowProgessPanel
            Dim row As AchTransactionsLog = GridViewAchTran.GetFocusedRow
            If row Is Nothing Then Return
            DevExpress.Utils.Guard.ArgumentNotNull(row, "Please select a row.")
            If row.EmailTemplate = "No Template" Then
                Throw New Exception("No email template was selected")
            End If
            Await _AchEmailService.EmailClient(row.CoNum, row.EmailTemplate, False, row.EntryDescType, True, IIf(Not selectedOnly, Nothing, row.ID))
        Catch ex As Exception
            DisplayErrorMessage("Error sending email.", ex)
        Finally
            'cRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub LoadCount()
        Try
            For Each item In aceStatus.Elements
                Dim queue As String = item.Tag
                If queue = "Unknown" Then
                    item.Text = $"{queue} ({PayrollAlerts.Where(Function(f) f.StatusFinal Is Nothing OrElse f.StatusFinal = "Unknown").Count()})"
                Else
                    item.Text = $"{queue} ({PayrollAlerts.Where(Function(f) f.StatusFinal = queue).Count()})"
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error loading counts", ex)
        End Try
    End Sub

    Private Sub LoadSelectedQueueData()
        Try
            GridControl1.DataSource = PayrollAlerts.Where(Function(d) d.StatusFinal = SelectedQueue OrElse (SelectedQueue = "Unknown" AndAlso d.StatusFinal Is Nothing)).ToList
            Dim column As DevExpress.XtraGrid.Columns.GridColumn
            For Each column In GridView1.Columns
                If "Tax,DD,Gross,AvgGross,TaxDdTtl,FlaggedLimit,FlaggedLimitVar,DdPerPrLimit,FlaggedTaxVar,FlaggedLimitVar".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    column.DisplayFormat.FormatType = FormatType.Numeric
                    column.DisplayFormat.FormatString = "c2"
                ElseIf "TaxDdTtlPctOfGrs,GrsPctAbvAvg".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    column.DisplayFormat.FormatType = FormatType.Numeric
                    column.DisplayFormat.FormatString = "{0:f2}%"
                ElseIf "IncreasePct,Status,StatusChangeLog,Notes,RUN_DATE".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    column.Visible = False
                ElseIf column.FieldName.ToLower = "achrisk" Then
                    column.VisibleIndex = 1
                End If
            Next
            GridView1.BestFitColumns()
            Call GridView1_FocusedRowChanged(Nothing, Nothing)
        Catch ex As Exception
            DisplayErrorMessage("Error loading data.", ex)
        End Try
    End Sub

    Private Sub AccordionControl1_SelectedElementChanged(sender As Object, e As SelectedElementChangedEventArgs) Handles AccordionControl1.SelectedElementChanged
        SelectedQueue = e.Element.Tag
        LoadSelectedQueueData()
    End Sub

    Private Sub GridView1_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles GridView1.FocusedRowChanged
        Dim CoNum As Decimal = 0
        Dim row As prc_RptPayrollAlertsResult = Nothing
        row = GridView1.GetFocusedRow()

        If row IsNot Nothing Then
            CoNum = row.CONUM
            teCoNum.EditValue = row.CONUM
            tePrNum.EditValue = row.PRNUM

            If row.StatusFinal Is Nothing Then
                lueStatus.EditValue = "Unknown"
            Else
                lueStatus.EditValue = row.StatusFinal
            End If

            teNote.EditValue = Nothing
            meNotesLog.EditValue = row.Notes
            meStatusLog.EditValue = row.StatusChangeLog
            lueDDStatus.EditValue = row.DDStatus
            lueTaxStatus.EditValue = row.TaxStatus

            Dim _empsCount = db.EMPLOYEEs.Where(Function(em) em.CONUM = CoNum AndAlso Not em.TERM_DATE.HasValue).Count
            Dim _coOptions = db.COOPTIONs.FirstOrDefault(Function(c) c.CONUM = CoNum)
            Dim comp = db.COMPANies.Single(Function(c) c.CONUM = CoNum)
            Me.UcCompInfo1.LoadDate(comp, _empsCount, _coOptions)
            UcCompInfo1.TabbedControlGroup1.HideToCustomization()
            LoadAchTran(CoNum)
            LoadEmails(CoNum)
            LoadRptRiskMgmtPrStopped(CoNum)

            Dim hasDDLimits As Boolean?, hasTaxLimits As Boolean?, hasLimits As Boolean?
            hasDDLimits = row.OverMax OrElse row.OverLimit
            hasTaxLimits = row.OverTaxMax OrElse row.OverTaxVar
            hasLimits = hasDDLimits OrElse hasTaxLimits

            If bbiViewNacha.Enabled <> If(hasLimits Is Nothing, False, hasLimits) Then
                bbiViewNacha.Enabled = If(hasLimits Is Nothing, False, hasLimits)
            End If

            If bbiViewNacha.Enabled = False AndAlso row.StatusFinal = "Wire Requested" Then
                bbiViewNacha.Enabled = True
            End If

            If bbiRequestWire.Enabled <> If(hasLimits Is Nothing AndAlso nz(row.NSF, False) = False, False, True) Then
                bbiRequestWire.Enabled = If(hasLimits Is Nothing AndAlso nz(row.NSF, False) = False, False, True)
            End If

            If BarSubItemRequestWireDD.Enabled <> If(hasDDLimits Is Nothing, False, hasDDLimits) Then
                BarSubItemRequestWireDD.Enabled = If(hasDDLimits Is Nothing, False, hasDDLimits)
            End If

            If BarSubItemRequestWireTax.Enabled <> If(hasTaxLimits Is Nothing, False, hasTaxLimits) Then
                BarSubItemRequestWireTax.Enabled = If(hasTaxLimits Is Nothing, False, hasTaxLimits)
            End If

            If bbiOpenNachaDD.Enabled <> If(hasDDLimits Is Nothing, False, hasDDLimits) Then
                bbiOpenNachaDD.Enabled = If(hasDDLimits Is Nothing, False, hasDDLimits)
            End If

            If bbiOpenNachaTax.Enabled <> If(hasTaxLimits Is Nothing, False, hasTaxLimits) Then
                bbiOpenNachaTax.Enabled = If(hasTaxLimits Is Nothing, False, hasTaxLimits)
            End If

            Me.bbiPlaid.Enabled = (row?.PlaidConnected.HasValue AndAlso row?.PlaidConnected = True).GetValueOrDefault
        End If
    End Sub

    Async Sub LoadAchTran(CoNum As Decimal)
        Try
            'LayoutControl1.ShowProgessPanel

            Me.GridControlAchTran.DataSource = Query(Of AchTransactionsLog)($"EXEC [custom].[prc_RptFDPrNumMulti] 'RiskManagementNSF', {CoNum}, NULL, '{If(chkShowLinkedCompanies.Checked, "ShowLinked", "")}'")

            If GridViewAchTran.SortInfo.Count = 0 Then
                If Me.GridViewAchTran.Columns("ID") IsNot Nothing AndAlso Me.GridViewAchTran.Columns("ID").Visible Then
                    Me.GridViewAchTran.Columns("ID").Visible = False
                End If

                Me.GridViewAchTran.Columns("Status").VisibleIndex = 4

                Me.GridViewAchTran.Columns("DateReceived").BestFit()
                Me.GridViewAchTran.Columns("CoNum").BestFit()
                Me.GridViewAchTran.Columns("EntryDescType").BestFit()
                Me.GridViewAchTran.Columns("RejectionCode").BestFit()
                Me.GridViewAchTran.Columns("Status").BestFit()
                Me.GridViewAchTran.Columns("DebitAmount").BestFit()
                Me.GridViewAchTran.Columns("CreditAmount").BestFit()

            End If

            'GridView1.BestFitColumns()
            'LayoutControl1.HideProgressPanel
        Catch ex As Exception
            'LayoutControl1.HideProgressPanel
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Async Sub LoadEmails(CoNum As Decimal)
        Try
            'LayoutControl1.ShowProgessPanel

            Dim sql As String = $"exec custom.prc_RptSecurityAlertIncident {CoNum}"

            Me.GridControlAlertEmails.DataSource = Await Threading.Tasks.Task.Run(Function() Query(sql))

            If Me.GridViewAlertEmails.Columns("Dtm") IsNot Nothing Then
                Me.GridViewAlertEmails.Columns("Dtm").DisplayFormat.FormatString = "G"
            End If

            If GridViewAlertEmails.SortInfo.Count = 0 Then
                GridViewAlertEmails.SortInfo.Add(New DevExpress.XtraGrid.Columns.GridColumnSortInfo(GridViewAlertEmails.Columns("Dtm"), DevExpress.Data.ColumnSortOrder.Descending))

                Me.GridViewAlertEmails.Columns("EmpNum").VisibleIndex = 2
                Me.GridViewAlertEmails.Columns("AlertDesc").VisibleIndex = 3
                Me.GridViewAlertEmails.Columns("AlertLevel").VisibleIndex = 4
            End If

            GridViewAlertEmails.BestFitColumns()
            'LayoutControl1.HideProgressPanel
        Catch ex As Exception
            'LayoutControl1.HideProgressPanel
            DisplayErrorMessage("Error Loading Emails", ex)
        End Try
    End Sub

    Async Sub LoadRptRiskMgmtPrStopped(CoNum As Decimal)
        Try
            'LayoutControl1.ShowProgessPanel

            Me.GridControlAlertsTrig.DataSource = Query($"exec custom.prc_RptRiskMgmtPrStopped {CoNum}, {tePrNum.EditValue}")
            GridViewAlertsTrig.BestFitColumns()
            'LayoutControl1.HideProgressPanel
        Catch ex As Exception
            'LayoutControl1.HideProgressPanel
            DisplayErrorMessage("Error Loading Emails", ex)
        End Try
    End Sub

    'to be used with request from David to save row before and after change, to see if user followed logic
    'Function outputParam(obj As Object) As String
    '    If IsDBNull(obj) Then
    '        Return "NULL"
    '    ElseIf obj = Nothing Then
    '        Return "NULL"
    '    ElseIf obj.GetType() Is GetType(String) Then
    '        Return "'" + obj.ToString().Replace("'", "''") + "'"
    '    ElseIf obj.GetType() Is GetType(DateTime) OrElse obj.GetType() Is GetType(Date) Then
    '        Return "'" + obj + "'"
    '    Else
    '        Return obj
    '    End If
    'End Function

    Private Async Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            If lueStatus.EditValue = "Delayed DD" OrElse lueStatus.EditValue = "Wire Received" Then
                XtraMessageBox.Show("This status cannot be changed in this form only by using appropriate action in Nacha form.", "Cannot change to status", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            Dim CoNum = teCoNum.EditValue
            Dim PrNum = tePrNum.EditValue
            Dim DDStatus = If(lueDDStatus.EditValue = Nothing, "NULL", "'" + lueDDStatus.EditValue + "'")
            Dim TaxStatus = If(lueTaxStatus.EditValue = Nothing, "NULL", "'" + lueTaxStatus.EditValue + "'")

            Dim row As prc_RptPayrollAlertsResult = Nothing
            row = Query(Of prc_RptPayrollAlertsResult)($"exec custom.prc_RptPayrollAlerts {CoNum}, {PrNum}").FirstOrDefault()

            If row.Status = lueStatus.EditValue AndAlso row.DDStatus?.ToString() = lueDDStatus.EditValue AndAlso row.TaxStatus?.ToString() = lueTaxStatus.EditValue Then
                Return
            End If

            If Not UserInRole("RiskManagementAdmin") Then
                XtraMessageBox.Show("Only available for members of role RiskManagementAdmin")
                Return
            End If

            Dim allowedPRStatusWhenRemoveDelayedDD = New List(Of String)(New String() {"DD Ready", "Entering Checks", "Submitted", "Finished Checks"})

            If (row.Status = "Delayed DD" OrElse row.Status = "Wire Received") AndAlso Not allowedPRStatusWhenRemoveDelayedDD.Contains(row.PAYROLL_STATUS) Then
                XtraMessageBox.Show($"This status of {row.Status} cannot be changed because the Payroll Status is {row.PAYROLL_STATUS}.", "Cannot change to status", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If


            If (row.Status = "Delayed DD" OrElse row.Status = "Wire Received") AndAlso row.PAYROLL_STATUS <> "Entering Checks" _
                    AndAlso XtraMessageBox.Show($"Are you sure you want to change status to {lueStatus.EditValue}, by doing so the Payroll Status will be set to Entering Checks?", "Change Status?", MessageBoxButtons.YesNo, MessageBoxIcon.Information) <> DialogResult.Yes Then
                Return
            ElseIf XtraMessageBox.Show("Are you sure you want to change status?", "Change Status?", MessageBoxButtons.YesNo, MessageBoxIcon.Information) <> DialogResult.Yes Then
                Return
            End If

            Dim status = lueStatus.EditValue
            Dim note = teNote.EditValue

            If note <> "" Then
                note = note + vbCrLf + vbCrLf + "User: " + UserName + vbCrLf + "Date: " + DateTime.Now + vbCrLf + "**********"
            End If

            Dim noExistingRec As Boolean = False
            If row Is Nothing OrElse row.Status Is Nothing AndAlso row.StatusChangeLog Is Nothing AndAlso row.Notes Is Nothing Then
                noExistingRec = True
            End If

            If noExistingRec AndAlso nz(status, "Unknown") = "" AndAlso nz(note, "") = "" Then
                Return
            ElseIf nz(status, "Unknown") = nz(row?.Status, "Unknown") AndAlso nz(note, "") = "" AndAlso nz(status, "Unknown") = nz(row.StatusFinal, "Unknown") _
                AndAlso nz(DDStatus, "Unknown") = nz(row?.DDStatus?.ToString(), "Unknown") AndAlso nz(TaxStatus, "Unknown") = nz(row?.TaxStatus?.ToString(), "Unknown") Then
                Return
            Else
                '                db.ExecuteCommand($"insert custom.RiskManageCapture (RefreshData, Plaid, CONUM, CO_NAME, Verified, PRNUM, PAYROLL_STATUS, CHECK_DATE
                ', DaysToNBD, DD, FlaggedLimit, DdPerPrLimit, FlaggedLimitVar, Tax, FlaggedTaxVar, Liab, LiabPct, GROSS, AvgGross
                ', IncreasePct, NoRouting, FirstDDPrNum, OverLimit, OverLimitVar, OverLimitSet, OverTaxVar, HasOpenTran, HasPrPwd
                ', IsUnscheduledPrl, NSF, PrlDaysBefCheckDate, Status, StatusChangeLog, Notes, OverMax, OverTaxMax, CoAlerts
                ', EmpAlerts, NachaCnt, PlaidConnected, HasReverseWire, RUN_DATE, PlaidBalance, DDOrTax, LowTTF, AchRisk, RiskStatus
                ', DDStatus, TaxStatus, DDhistory, Bill, LastNsf, ShortWith, AmtNeeded, StatusRec, StatusFinal, D7Issue)
                '	VALUES (0, {outputParam(row.Plaid)},{outputParam(row.CONUM)},{outputParam(row.CO_NAME)},{outputParam(row.Verified)}
                ',{outputParam(row.PRNUM)},{outputParam(row.PAYROLL_STATUS)},{outputParam(row.CHECK_DATE)},{outputParam(row.DaysToNBD)}
                ',{outputParam(row.DD)},{outputParam(row.FlaggedLimit)},{outputParam(row.DdPerPrLimit)}
                ',{outputParam(row.FlaggedLimitVar)},{outputParam(row.Tax)},{outputParam(row.FlaggedLimitVar)},{outputParam(row.Liab)}
                ',{outputParam(row.LiabPct)},{outputParam(row.GROSS)},{outputParam(row.AvgGross)},{outputParam(row.IncreasePct)}
                ',{outputParam(row.NoRouting)},{outputParam(row.FirstDDPrNum)},{outputParam(row.OverLimit)}
                ',{outputParam(row.OverLimitVar)},{outputParam(row.OverLimitSet)},{outputParam(row.OverTaxVar)}
                ',{outputParam(row.HasOpenTran)},{outputParam(row.HasPrPwd)},{outputParam(row.IsUnscheduledPrl)},{outputParam(row.NSF)}
                ',{outputParam(row.PrlDaysBefCheckDate)},{outputParam(row.Status)},{outputParam(row.StatusChangeLog)}
                ',{outputParam(row.Notes)},{outputParam(row.OverMax)},{outputParam(row.OverTaxMax)},{outputParam(row.CoAlerts)}
                ',{outputParam(row.EmpAlerts)},{outputParam(row.NachaCnt)},{outputParam(row.PlaidConnected)}
                ',{outputParam(row.HasReverseWire)},{outputParam(row.RUN_DATE)},{outputParam(row.PlaidBalance)}
                ',{outputParam(row.DDOrTax)},{outputParam(row.LowTTF)},{outputParam(row.AchRisk)},{outputParam(row.RiskStatus)}
                ',{outputParam(row.DDStatus)},{outputParam(row.TaxStatus)},{outputParam(row.DDhistory)},{outputParam(row.Bill)}
                ',{outputParam(row.LastNsf)},{outputParam(row.ShortWith)},{outputParam(row.AmtNeeded)},{outputParam(row.StatusRec)}
                ',{outputParam(row.StatusFinal)},{outputParam(row.D7Issue)});")

                If noExistingRec Then
                    db.ExecuteCommand($"INSERT custom.RiskManagementAlerts(CoNum, PrNum, Status, Notes, DDStatus, TaxStatus)
                                      SELECT {CoNum}, {PrNum}, '{status}', '{note}', {DDStatus}, {TaxStatus}")
                Else
                    'verify old note
                    Dim ExNote As String = nz(db.ExecuteQuery(Of String)($"SELECT Notes FROM custom.RiskManagementAlerts WHERE CoNum = {CoNum} AND PrNum = {PrNum}").FirstOrDefault(), "")
                    Dim newNote As String = ExNote + If(ExNote <> "", vbCrLf, "") + note
                    newNote = IIf(newNote <> "", "'" + newNote.Replace("'", "''") + "'", "NULL")

                    If db.Connection.State = ConnectionState.Closed Then
                        db.Connection.Open()
                    End If

                    db.Transaction = db.Connection.BeginTransaction()
                    db.ExecuteCommand($"UPDATE custom.RiskManagementAlerts
                                      SET Status = '{status}', Notes = {newNote}, DDStatus = {DDStatus}, TaxStatus = {TaxStatus}
                                      WHERE CoNum = {CoNum} AND PrNum = {PrNum}")

                    If (row.Status = "Delayed DD" OrElse row.Status = "Wire Received") AndAlso row.PAYROLL_STATUS <> "Entering Checks" Then
                        Dim payroll = db.PAYROLLs.Where(Function(p) p.CONUM = row.CONUM AndAlso p.PRNUM = row.PRNUM).First()
                        payroll.PAYROLL_STATUS = "Entering Checks"
                        db.SaveChanges()
                    End If

                    db.Transaction.Commit()
                    db.Connection.Close()
                End If
            End If
            Await ReloadRow(CoNum, PrNum)
        Catch ex As Exception
            If db.Transaction IsNot Nothing Then db.Transaction.Rollback()
            If db.Connection.State = ConnectionState.Open Then db.Connection.Close()
            DisplayErrorMessage("Error updating row", ex)
        End Try
    End Sub

    Public Async Function ReloadRow(CoNum As Decimal, PrNum As Decimal) As Threading.Tasks.Task
        Dim row = Query(Of prc_RptPayrollAlertsResult)($"exec custom.prc_RptPayrollAlerts {CoNum}, {PrNum}").FirstOrDefault()
        Dim ExRow = PayrollAlerts.Where(Function(f) f.CONUM = CoNum AndAlso f.PRNUM = PrNum).FirstOrDefault()
        PayrollAlerts.Remove(ExRow)
        PayrollAlerts.Add(row)
        PayrollAlerts = PayrollAlerts.OrderBy(Function(o) o.CHECK_DATE).ThenBy(Function(o) o.CONUM).ToList()
        LoadCount()
        LoadSelectedQueueData()
    End Function

    Private Sub GridView1_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub

        If e.Column.FieldName.ToUpper() = "CHECK_DATE" Then
            Dim daysToNBD As Int32? = GridView1.GetRowCellValue(e.RowHandle, "DaysToNBD")

            If daysToNBD >= 0 Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.ForeColor = Color.Black
            Else
                e.Appearance.BackColor = Color.White
                e.Appearance.ForeColor = Color.Black
            End If
        ElseIf e.Column.FieldName = "FlaggedLimit" Then
            Dim FlaggedLimit As Decimal? = GridView1.GetRowCellValue(e.RowHandle, "FlaggedLimit")
            Dim DdPerPrLimit As Decimal? = GridView1.GetRowCellValue(e.RowHandle, "DdPerPrLimit")
            Dim FlaggedLimitVar As Decimal? = GridView1.GetRowCellValue(e.RowHandle, "FlaggedLimitVar")
            Dim AchRisk As Boolean? = GridView1.GetRowCellValue(e.RowHandle, "AchRisk")

            If AchRisk Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.ForeColor = Color.Black
            ElseIf FlaggedLimit > 0 AndAlso DdPerPrLimit = FlaggedLimit AndAlso row.OverLimit Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.ForeColor = Color.Black
            ElseIf FlaggedLimit > 0 AndAlso FlaggedLimitVar = FlaggedLimit AndAlso row.OverLimitVar Then
                e.Appearance.BackColor = Color.Yellow
                e.Appearance.ForeColor = Color.Black
            Else
                e.Appearance.BackColor = Color.White
                e.Appearance.ForeColor = Color.Black
            End If
        ElseIf e.Column.FieldName = "FlaggedTaxVar" Then
            If row.FlaggedTaxVar > 0 AndAlso row.OverTaxVar Then
                e.Appearance.BackColor = Color.Red
                e.Appearance.ForeColor = Color.Black
            Else
                e.Appearance.BackColor = Color.White
                e.Appearance.ForeColor = Color.Black
            End If
        End If
    End Sub

    Private Sub GridViewAchTran_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GridViewAchTran.FocusedRowObjectChanged
        Dim co As Decimal = 0
        Dim row As AchTransactionsLog = GridViewAchTran.GetFocusedRow
        If e.Row IsNot Nothing Then
            co = CType(e.Row, AchTransactionsLog).CoNum
            row = e.Row
        End If
        Me.bbiOpenEmp.Enabled = (row?.EmpNum.HasValue).GetValueOrDefault
    End Sub

    Private Sub bbiOpenEmp_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiOpenEmp.ItemClick
        Dim row As AchTransactionsLog = GridViewAchTran.GetFocusedRow
        If row Is Nothing OrElse row.CoNum = 0 Then Exit Sub
        MainForm.OpenCompForm(row.CoNum, empNum:=row.EmpNum)
    End Sub

    Private Async Sub bbiRefresh_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiRefresh.ItemClick
        Await LoadData()
    End Sub

    Private Sub bbiAutoEmails_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiAutoEmails.ItemClick
        Using frm = New frmAchAutoEmails(_AchSettings)
            If frm.ShowDialog() = DialogResult.OK Then

            End If
        End Using
    End Sub

    Private Async Sub bbiAchSettings_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiAchSettings.ItemClick
        Using frm = New frmAchSettings(_AchSettings)
            If frm.ShowDialog = DialogResult.OK Then
                SetUdfValue("AchSettingsJson", JsonConvert.SerializeObject(_AchSettings))
                LoadSettings()
                Await LoadData()
            End If
        End Using
    End Sub

    Private Sub bbiOpenCo_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiOpenCo.ItemClick
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub
        MainForm.OpenCompForm(row.CONUM)
    End Sub

    Private Sub bbiSetCoPassword_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiSetCoPassword.ItemClick
        Dim row As AchTransactionsLog = GridViewAchTran.GetFocusedRow
        If row Is Nothing Then
            XtraMessageBox.Show("Please select a ACH Transaction row.")
            Exit Sub
        End If
        _AchEmailService.SetCoPassword(row.CoNum, row.EntryDescType)
    End Sub



    Private Async Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        Timer1.Enabled = False
        Await LoadData()
    End Sub


    Private Sub bbiPlaid_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiPlaid.ItemClick
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub

        Dim plaid = New frmPlaid(row.CONUM, row.CO_NAME)
        plaid.ShowDialog()
    End Sub

    Private Async Sub ProcessReportAsync(row As prc_RptPayrollAlertsResult)
        Dim Comp = db.COMPANies.Where(Function(f) f.CONUM = row.CONUM).First()
        Dim REPORT = db.ReportEmailTeplates.Where(Function(f) f.ID = 461).First()

        Dim UcReportEmailTemplate1 As ucReportEmailTemplate
        UcReportEmailTemplate1 = New ucReportEmailTemplate With {.CoNum = row.CONUM}

        Try
            Application.DoEvents()
            Dim processor = New ReportProcessor(Comp, REPORT, FileType.Pdf) With {.showInRecentReports = False}
            processor.DefaultParamValues = New List(Of KeyValuePair)
            processor.DefaultParamValues.Add(New KeyValuePair("CoNum", row.CONUM))
            processor.DefaultParamValues.Add(New KeyValuePair("@PrnumWithIssue", row.PRNUM))
            Dim processingFee = db.ACT_ITEMS.Where(Function(i) i.ITEM_NUM = 39).FirstOrDefault?.STD_PRICE
            processor.DefaultParamValues.Add(New KeyValuePair("@WireAmount", nz(row.Tax, 0) + nz(row.DD, 0) + nz(row.Bill, 0) + nz(processingFee, 0)))
            processor.DefaultParamValues.Add(New KeyValuePair("@DDAmount", nz(row.DD, 0)))
            processor.DefaultParamValues.Add(New KeyValuePair("@TaxAmount", nz(row.Tax, 0)))
            processor.DefaultParamValues.Add(New KeyValuePair("@InvoiceAmount", nz(row.Bill, 0)))
            Dim result = processor.ProcessReport()
            If Not (result.Cancalled OrElse result.AllFileExist) Then
                DisplayMessageBox("No records returned for the parameters values entered")
            ElseIf Not result.Cancalled Then
                Dim attachments As New List(Of String)
                attachments.Add(result.Paths(0))

                Using frmAttachments = New frmDraggableAttachments(attachments)
                    frmAttachments.ShowDialog()
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error processing report: {0}".FormatWith(REPORT.Name), ex)
        Finally
            UcReportEmailTemplate1.Dispose()
        End Try
    End Sub

    Private Sub bbiLimit_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiLimit.ItemClick
        If Not UserInRole("RiskManagementSetLimit") Then
            XtraMessageBox.Show("Only available for members of role RiskManagementSetLimit")
            Return
        ElseIf Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
            Return
        End If

        Dim frm As frmClientLimits
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then
            frm = New frmClientLimits()
        Else
            frm = New frmClientLimits(row.CONUM)
        End If

        frm.ShowDialog()
    End Sub

    Private Sub bbiDelayedDD_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiDelayedDD.ItemClick
        Dim BankingForm As frmBankingFileTracker = CType(MainForm.GetForm(frmBankingFileTracker.GetType()), frmBankingFileTracker)
        If BankingForm IsNot Nothing Then
            MainForm.CloseForm(BankingForm)
        End If
        BankingForm = New frmBankingFileTracker()
        MainForm.ShowForm(BankingForm)

        'Dim frm = New frmDelayDD()
        'frm.ShowDialog()
    End Sub

    Private Async Sub BarButtonItem_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiRequestWireTaxNewTicket.ItemClick, bbiRequestWireTaxAttach.ItemClick, bbiRequestWireDDNewTicket.ItemClick, bbiRequestWireDDAttach.ItemClick, bbiOpenNachaTax.ItemClick, bbiOpenNachaDD.ItemClick, bbiRequestWire.ItemClick, bbiOpenNachaAll.ItemClick
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub

        If e.Item Is bbiRequestWireDDAttach Then
            RequestWire(row)
        ElseIf e.Item Is bbiRequestWireDDNewTicket Then
            RequestWire(row)
        ElseIf e.Item Is bbiRequestWireTaxAttach Then
            RequestWire(row)
        ElseIf e.Item Is bbiRequestWireTaxNewTicket Then
            RequestWire(row)
        ElseIf e.Item Is bbiRequestWire Then
            RequestWire(row)
        ElseIf e.Item Is bbiOpenNachaDD Then
            OpenNacha(row, "DD")
        ElseIf e.Item Is bbiOpenNachaTax Then
            OpenNacha(row, "Tax")
        ElseIf e.Item Is bbiOpenNachaAll Then
            OpenNacha(row, "ALL")
        End If
    End Sub

    Private Async Sub RequestWire(row As prc_RptPayrollAlertsResult)
        Try
            Await CanCreateTicketAsync()
            Dim Comp = db.COMPANies.Where(Function(f) f.CONUM = row.CONUM).First()
            Dim REPORT = db.ReportEmailTeplates.Where(Function(f) f.ID = 461).First()
            Await modZendeskIntegrationClient.CreateTicketAsync("", Comp, REPORT, $"Urgent: Action Required for Co #{row.CONUM} Payroll #{row.PRNUM}", New List(Of String)())
            ProcessReportAsync(row)
        Catch ex As Exception
            DisplayErrorMessage("Error in request wire", ex)
        End Try
    End Sub

    Private Sub OpenNacha(row As prc_RptPayrollAlertsResult, NachaType As String)
        Dim nacha As New frmNacha(row.CONUM, row.PRNUM, row.StatusFinal, NachaType, Me)
        MainForm.ShowForm(nacha)
    End Sub

    Private Async Sub chkShowLinkedCompanies_CheckedChanged(sender As Object, e As EventArgs) Handles chkShowLinkedCompanies.CheckedChanged
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub

        LoadAchTran(row.CONUM)
    End Sub

    Private Sub bbiClearRisk_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiClearRisk.ItemClick
        Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
        If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub

        Dim args = New XtraInputBoxArgs With {.Caption = "Clear Risk Reason", .Prompt = "Please enter the Clear Risk - Reason"}
        args.Editor = New MemoEdit
        Dim result = XtraInputBox.Show(args)
        If result Is Nothing Then
            DisplayMessageBox("Must provide reason for Clear Risk", "Risk Clear - Failed")
        Else
            Try
                Me.lueStatus.EditValue = "Cleared Risk"
                Me.teNote.EditValue = IIf(Me.teNote.EditValue = Nothing, "", ".  ") + "Clear Risk Reason: " + result
                btnSave.PerformClick()
            Catch ex As Exception
                DisplayErrorMessage("Error Clearing Risk", ex)
            End Try
        End If
    End Sub

    Private Sub bbiGlobalRiskSettings_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiGlobalRiskSettings.ItemClick
        If Not UserInRole("RiskManagementSetLimit") Then
            XtraMessageBox.Show("Only available for members of role RiskManagementSetLimit")
            Return
        ElseIf Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
            Return
        End If

        Dim frm As New frmGlobalRiskSettings()
        frm.ShowDialog()
    End Sub

    Private Sub bbiBankingHold_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiBankingHold.ItemClick
        Try
            Dim row As prc_RptPayrollAlertsResult = GridView1.GetFocusedRow
            If row Is Nothing OrElse row.CONUM = 0 Then Exit Sub

            If XtraMessageBox.Show($"Are you sure you want to put hold on this payroll?", "Confirm Banking Hold", MessageBoxButtons.YesNo, MessageBoxIcon.Information) = DialogResult.Yes Then
                If db.BankingFileHolds.Where(Function(b) b.CoNum = row.CONUM AndAlso row.PRNUM = row.PRNUM).Count > 0 Then
                    DisplayMessageBox("PrNum is already on hold", "Cannot put on hold")
                Else
                    Dim bankingHold = New BankingFileHold With {.CoNum = row.CONUM, .FileType = "DD", .CO_NAME = row.CO_NAME, .HoldDate = Today, .PlacedBy = UserName.Substring(0, IIf(UserName.Length >= 10, 10, UserName.Length)), .PrNum = row.PRNUM, .Processor = "ACH"}
                    db.BankingFileHolds.InsertOnSubmit(bankingHold)
                    db.SubmitChanges()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error putting banking hold", ex)
        End Try
    End Sub

    Sub GridView1_ColumnFilterChanged() Handles GridView1.ColumnFilterChanged
        GridView1.BestFitColumns()
    End Sub
End Class