﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucShippingAddress
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ucShippingAddress))
        Dim ConditionValidationRule1 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule2 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule3 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule4 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule5 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule6 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule7 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule8 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule9 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule10 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule11 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule12 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule13 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule14 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule15 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Me.colQtrEndDate = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_6 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.meNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.BindingSourceShipAddressOverride = New System.Windows.Forms.BindingSource(Me.components)
        Me.btnTemporaryNote = New DevExpress.XtraEditors.SimpleButton()
        Me.lcCurrentShipSource = New DevExpress.XtraEditors.LabelControl()
        Me.lcTempChangePrStatus = New DevExpress.XtraEditors.LabelControl()
        Me.gcOverrides = New DevExpress.XtraGrid.GridControl()
        Me.LayoutView1 = New DevExpress.XtraGrid.Views.Layout.LayoutView()
        Me.colId = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colId = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colCONUM1 = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colCONUM1 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colDDIVNUM1 = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colDDIVNUM1 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colPRNUM = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colPRNUM = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colExpirationDate = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colExpirationDate = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_NAME = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_NAME = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_STREET = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_STREET = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_CITY = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_CITY = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_STATE = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_STATE = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_ZIP = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_ZIP = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_PHONE = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_PHONE = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_FAX = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_FAX = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_EXTENSION = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_EXTENSION = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colSHIP_MODEM = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colSHIP_MODEM = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colDPR_CONTACT = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colDPR_CONTACT = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colDDELVDESC = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_colDDELVDESC = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colNotes = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colPermanent = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_1 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colAddDateTime = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_2 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colAddUser = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_3 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colAddedAfterPayrollPrint = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_4 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.AddedAfterPayrollPrintMsg = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_5 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.colQtrEndDateDisp = New DevExpress.XtraGrid.Columns.LayoutViewColumn()
        Me.layoutViewField_LayoutViewColumn1_7 = New DevExpress.XtraGrid.Views.Layout.LayoutViewField()
        Me.LayoutViewCard1 = New DevExpress.XtraGrid.Views.Layout.LayoutViewCard()
        Me.btnCancelTemporarilyOverride = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSaveTempChange = New DevExpress.XtraEditors.SimpleButton()
        Me.btnTemporarilyChange = New DevExpress.XtraEditors.SimpleButton()
        Me.btnPermanentlyChange = New DevExpress.XtraEditors.SimpleButton()
        Me.seOverridePayrollNum = New DevExpress.XtraEditors.SpinEdit()
        Me.deOverrideExpirationDate = New DevExpress.XtraEditors.DateEdit()
        Me.rgExpirationMode = New DevExpress.XtraEditors.RadioGroup()
        Me.btnCanelPermanentlyOverride = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSavePermChange = New DevExpress.XtraEditors.SimpleButton()
        Me.cbeStates = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.BindingSourceDivision = New System.Windows.Forms.BindingSource(Me.components)
        Me.TextEdit18 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit13 = New DevExpress.XtraEditors.TextEdit()
        Me.teDivisionZip = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit10 = New DevExpress.XtraEditors.TextEdit()
        Me.teDivisionStreet = New DevExpress.XtraEditors.TextEdit()
        Me.tePermChangeName = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit16 = New DevExpress.XtraEditors.TextEdit()
        Me.ceShowInactiveDivisions = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEdit6 = New DevExpress.XtraEditors.TextEdit()
        Me.BindingSourceCurrentShippingInfo = New System.Windows.Forms.BindingSource(Me.components)
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TextEdit4 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.gcDivisions = New DevExpress.XtraGrid.GridControl()
        Me.gvDivisions = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDDIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDDIVNAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivShip = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoShip = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipCo = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipAddress = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipCity = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipState = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipZip = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipContact = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipPhone = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipExtension = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipModem = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipMethod = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipCharge = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEEcnt = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivAddress = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.TextEdit20 = New DevExpress.XtraEditors.TextEdit()
        Me.teOverrideStreet = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit24 = New DevExpress.XtraEditors.TextEdit()
        Me.cbeStatesOverrideShipAddress = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.teZipOverride = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit23 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit26 = New DevExpress.XtraEditors.TextEdit()
        Me.cbeDivisionDeliveryMethod = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.cbeDeliveryMethodOverride = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.beShippingPhone = New DevExpress.XtraEditors.ButtonEdit()
        Me.beShppingModem = New DevExpress.XtraEditors.ButtonEdit()
        Me.bePerChangePhone = New DevExpress.XtraEditors.ButtonEdit()
        Me.bePerChangeModem = New DevExpress.XtraEditors.ButtonEdit()
        Me.beTempChangePhone = New DevExpress.XtraEditors.ButtonEdit()
        Me.beTempChangeModem = New DevExpress.XtraEditors.ButtonEdit()
        Me.cbeCurrentShipDelvMethod = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TextEdit11 = New DevExpress.XtraEditors.TextEdit()
        Me.BindingSourcePermCoChange = New System.Windows.Forms.BindingSource(Me.components)
        Me.teStreetPermCoChange = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit9 = New DevExpress.XtraEditors.TextEdit()
        Me.cbeStatePermCoChange = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.teZipPermCoChange = New DevExpress.XtraEditors.TextEdit()
        Me.bePhonePermCoChange = New DevExpress.XtraEditors.ButtonEdit()
        Me.TextEdit12 = New DevExpress.XtraEditors.TextEdit()
        Me.beModemPermCoChange = New DevExpress.XtraEditors.ButtonEdit()
        Me.cbeDelvMethodPermCoChange = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btnSavePermCoChange = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancelPermCoChange = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEdit7 = New DevExpress.XtraEditors.TextEdit()
        Me.btnCopyTemporary = New DevExpress.XtraEditors.SimpleButton()
        Me.deQeYeDate = New DevExpress.XtraEditors.DateEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.lcgCurrentShippingInfo = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem54 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem4 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lcgPermanentlyChange = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem22 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem33 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem34 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgPermanentlyCoChange = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem42 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem43 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem44 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem45 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem46 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem47 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem48 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem49 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem50 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem51 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem5 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem52 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem53 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgTemporarilyChange = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem35 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem40 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem41 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem6 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lciOverrideExpireDate = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciOverridePayrollNum = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem37 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgTemporarilyChangeAddressFields = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem23 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem24 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem25 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem26 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem27 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem28 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem29 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem30 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem32 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem31 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem56 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciQeYeDate = New DevExpress.XtraLayout.LayoutControlItem()
        Me.sliQeYeNote = New DevExpress.XtraLayout.SimpleLabelItem()
        Me.LayoutControlItem39 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem38 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem36 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem55 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem57 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.DxValidationProviderPermChange = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        Me.DxValidationProviderTempChange = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        Me.DxValidationProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        Me.DxValidationProvider2 = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        Me.RepositoryItemMemoEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit()
        CType(Me.layoutViewField_LayoutViewColumn1_6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.meNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSourceShipAddressOverride, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcOverrides, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colId, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colCONUM1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colDDIVNUM1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colPRNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colExpirationDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_NAME, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_STREET, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_CITY, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_STATE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_ZIP, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_PHONE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_FAX, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_EXTENSION, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colSHIP_MODEM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colDPR_CONTACT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_colDDELVDESC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1_1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1_2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1_3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1_4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1_5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.layoutViewField_LayoutViewColumn1_7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutViewCard1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.seOverridePayrollNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deOverrideExpirationDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deOverrideExpirationDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.rgExpirationMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStates.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSourceDivision, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit18.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit13.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teDivisionZip.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit10.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teDivisionStreet.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tePermChangeName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit16.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceShowInactiveDivisions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSourceCurrentShippingInfo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcDivisions, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvDivisions, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit20.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teOverrideStreet.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit24.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStatesOverrideShipAddress.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teZipOverride.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit23.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit26.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeDivisionDeliveryMethod.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeDeliveryMethodOverride.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beShippingPhone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beShppingModem.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bePerChangePhone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bePerChangeModem.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beTempChangePhone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beTempChangeModem.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeCurrentShipDelvMethod.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit11.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSourcePermCoChange, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teStreetPermCoChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit9.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStatePermCoChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teZipPermCoChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bePhonePermCoChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit12.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beModemPermCoChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeDelvMethodPermCoChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit7.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deQeYeDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deQeYeDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgCurrentShippingInfo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem54, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgPermanentlyChange, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem33, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem34, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgPermanentlyCoChange, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem42, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem43, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem44, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem45, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem46, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem47, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem48, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem49, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem50, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem51, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem52, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem53, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgTemporarilyChange, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem35, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem40, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem41, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciOverrideExpireDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciOverridePayrollNum, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem37, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgTemporarilyChangeAddressFields, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem28, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem29, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem30, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem32, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem31, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem56, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciQeYeDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.sliQeYeNote, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem39, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem38, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem36, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem55, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem57, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProviderPermChange, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProviderTempChange, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProvider2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'colQtrEndDate
        '
        Me.colQtrEndDate.Caption = "Qtr End Date"
        Me.colQtrEndDate.FieldName = "QtrEndDate"
        Me.colQtrEndDate.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_6
        Me.colQtrEndDate.Name = "colQtrEndDate"
        '
        'layoutViewField_LayoutViewColumn1_6
        '
        Me.layoutViewField_LayoutViewColumn1_6.EditorPreferredWidth = 10
        Me.layoutViewField_LayoutViewColumn1_6.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_LayoutViewColumn1_6.Name = "layoutViewField_LayoutViewColumn1_6"
        Me.layoutViewField_LayoutViewColumn1_6.Size = New System.Drawing.Size(510, 261)
        Me.layoutViewField_LayoutViewColumn1_6.TextSize = New System.Drawing.Size(82, 13)
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.meNotes)
        Me.LayoutControl1.Controls.Add(Me.btnTemporaryNote)
        Me.LayoutControl1.Controls.Add(Me.lcCurrentShipSource)
        Me.LayoutControl1.Controls.Add(Me.lcTempChangePrStatus)
        Me.LayoutControl1.Controls.Add(Me.gcOverrides)
        Me.LayoutControl1.Controls.Add(Me.btnCancelTemporarilyOverride)
        Me.LayoutControl1.Controls.Add(Me.btnSaveTempChange)
        Me.LayoutControl1.Controls.Add(Me.btnTemporarilyChange)
        Me.LayoutControl1.Controls.Add(Me.btnPermanentlyChange)
        Me.LayoutControl1.Controls.Add(Me.seOverridePayrollNum)
        Me.LayoutControl1.Controls.Add(Me.deOverrideExpirationDate)
        Me.LayoutControl1.Controls.Add(Me.rgExpirationMode)
        Me.LayoutControl1.Controls.Add(Me.btnCanelPermanentlyOverride)
        Me.LayoutControl1.Controls.Add(Me.btnSavePermChange)
        Me.LayoutControl1.Controls.Add(Me.cbeStates)
        Me.LayoutControl1.Controls.Add(Me.TextEdit18)
        Me.LayoutControl1.Controls.Add(Me.TextEdit13)
        Me.LayoutControl1.Controls.Add(Me.teDivisionZip)
        Me.LayoutControl1.Controls.Add(Me.TextEdit10)
        Me.LayoutControl1.Controls.Add(Me.teDivisionStreet)
        Me.LayoutControl1.Controls.Add(Me.tePermChangeName)
        Me.LayoutControl1.Controls.Add(Me.TextEdit16)
        Me.LayoutControl1.Controls.Add(Me.ceShowInactiveDivisions)
        Me.LayoutControl1.Controls.Add(Me.TextEdit6)
        Me.LayoutControl1.Controls.Add(Me.ComboBoxEdit1)
        Me.LayoutControl1.Controls.Add(Me.TextEdit4)
        Me.LayoutControl1.Controls.Add(Me.TextEdit3)
        Me.LayoutControl1.Controls.Add(Me.TextEdit2)
        Me.LayoutControl1.Controls.Add(Me.TextEdit1)
        Me.LayoutControl1.Controls.Add(Me.gcDivisions)
        Me.LayoutControl1.Controls.Add(Me.TextEdit20)
        Me.LayoutControl1.Controls.Add(Me.teOverrideStreet)
        Me.LayoutControl1.Controls.Add(Me.TextEdit24)
        Me.LayoutControl1.Controls.Add(Me.cbeStatesOverrideShipAddress)
        Me.LayoutControl1.Controls.Add(Me.teZipOverride)
        Me.LayoutControl1.Controls.Add(Me.TextEdit23)
        Me.LayoutControl1.Controls.Add(Me.TextEdit26)
        Me.LayoutControl1.Controls.Add(Me.cbeDivisionDeliveryMethod)
        Me.LayoutControl1.Controls.Add(Me.cbeDeliveryMethodOverride)
        Me.LayoutControl1.Controls.Add(Me.beShippingPhone)
        Me.LayoutControl1.Controls.Add(Me.beShppingModem)
        Me.LayoutControl1.Controls.Add(Me.bePerChangePhone)
        Me.LayoutControl1.Controls.Add(Me.bePerChangeModem)
        Me.LayoutControl1.Controls.Add(Me.beTempChangePhone)
        Me.LayoutControl1.Controls.Add(Me.beTempChangeModem)
        Me.LayoutControl1.Controls.Add(Me.cbeCurrentShipDelvMethod)
        Me.LayoutControl1.Controls.Add(Me.TextEdit11)
        Me.LayoutControl1.Controls.Add(Me.teStreetPermCoChange)
        Me.LayoutControl1.Controls.Add(Me.TextEdit9)
        Me.LayoutControl1.Controls.Add(Me.cbeStatePermCoChange)
        Me.LayoutControl1.Controls.Add(Me.teZipPermCoChange)
        Me.LayoutControl1.Controls.Add(Me.bePhonePermCoChange)
        Me.LayoutControl1.Controls.Add(Me.TextEdit12)
        Me.LayoutControl1.Controls.Add(Me.beModemPermCoChange)
        Me.LayoutControl1.Controls.Add(Me.cbeDelvMethodPermCoChange)
        Me.LayoutControl1.Controls.Add(Me.btnSavePermCoChange)
        Me.LayoutControl1.Controls.Add(Me.btnCancelPermCoChange)
        Me.LayoutControl1.Controls.Add(Me.TextEdit7)
        Me.LayoutControl1.Controls.Add(Me.btnCopyTemporary)
        Me.LayoutControl1.Controls.Add(Me.deQeYeDate)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(232, 229, 807, 701)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1344, 794)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'meNotes
        '
        Me.meNotes.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "Notes", True))
        Me.meNotes.Location = New System.Drawing.Point(420, 542)
        Me.meNotes.Name = "meNotes"
        Me.meNotes.Size = New System.Drawing.Size(582, 35)
        Me.meNotes.StyleController = Me.LayoutControl1
        Me.meNotes.TabIndex = 40
        '
        'BindingSourceShipAddressOverride
        '
        Me.BindingSourceShipAddressOverride.DataSource = GetType(Brands_FrontDesk.ShipAddressOverride)
        '
        'btnTemporaryNote
        '
        Me.btnTemporaryNote.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.notes_16x161
        Me.btnTemporaryNote.Location = New System.Drawing.Point(410, 223)
        Me.btnTemporaryNote.Name = "btnTemporaryNote"
        Me.btnTemporaryNote.Size = New System.Drawing.Size(154, 22)
        Me.btnTemporaryNote.StyleController = Me.LayoutControl1
        Me.btnTemporaryNote.TabIndex = 39
        Me.btnTemporaryNote.Text = "Temporary Note"
        '
        'lcCurrentShipSource
        '
        Me.lcCurrentShipSource.Appearance.Font = New System.Drawing.Font("Arial", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lcCurrentShipSource.Appearance.Options.UseFont = True
        Me.lcCurrentShipSource.Appearance.Options.UseTextOptions = True
        Me.lcCurrentShipSource.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcCurrentShipSource.Location = New System.Drawing.Point(315, 39)
        Me.lcCurrentShipSource.Name = "lcCurrentShipSource"
        Me.lcCurrentShipSource.Size = New System.Drawing.Size(687, 24)
        Me.lcCurrentShipSource.StyleController = Me.LayoutControl1
        Me.lcCurrentShipSource.TabIndex = 38
        Me.lcCurrentShipSource.Text = "LabelControl1"
        '
        'lcTempChangePrStatus
        '
        Me.lcTempChangePrStatus.AllowHtmlString = True
        Me.lcTempChangePrStatus.Location = New System.Drawing.Point(936, 368)
        Me.lcTempChangePrStatus.Name = "lcTempChangePrStatus"
        Me.lcTempChangePrStatus.Size = New System.Drawing.Size(66, 13)
        Me.lcTempChangePrStatus.StyleController = Me.LayoutControl1
        Me.lcTempChangePrStatus.TabIndex = 37
        Me.lcTempChangePrStatus.Text = "LabelControl1"
        '
        'gcOverrides
        '
        Me.gcOverrides.Location = New System.Drawing.Point(306, 646)
        Me.gcOverrides.MainView = Me.LayoutView1
        Me.gcOverrides.Name = "gcOverrides"
        Me.gcOverrides.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemMemoEdit1})
        Me.gcOverrides.Size = New System.Drawing.Size(705, 133)
        Me.gcOverrides.TabIndex = 36
        Me.gcOverrides.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.LayoutView1})
        '
        'LayoutView1
        '
        Me.LayoutView1.Appearance.CardCaption.Font = New System.Drawing.Font("Arial Narrow", 9.0!, System.Drawing.FontStyle.Bold)
        Me.LayoutView1.Appearance.CardCaption.Options.UseFont = True
        Me.LayoutView1.Appearance.FieldCaption.Font = New System.Drawing.Font("Arial Narrow", 9.0!, System.Drawing.FontStyle.Bold)
        Me.LayoutView1.Appearance.FieldCaption.Options.UseFont = True
        Me.LayoutView1.CardMinSize = New System.Drawing.Size(518, 290)
        Me.LayoutView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.LayoutViewColumn() {Me.colId, Me.colCONUM1, Me.colDDIVNUM1, Me.colPRNUM, Me.colExpirationDate, Me.colSHIP_NAME, Me.colSHIP_STREET, Me.colSHIP_CITY, Me.colSHIP_STATE, Me.colSHIP_ZIP, Me.colSHIP_PHONE, Me.colSHIP_FAX, Me.colSHIP_EXTENSION, Me.colSHIP_MODEM, Me.colDPR_CONTACT, Me.colDDELVDESC, Me.colNotes, Me.colPermanent, Me.colAddDateTime, Me.colAddUser, Me.colAddedAfterPayrollPrint, Me.AddedAfterPayrollPrintMsg, Me.colQtrEndDate, Me.colQtrEndDateDisp})
        Me.LayoutView1.GridControl = Me.gcOverrides
        Me.LayoutView1.HiddenItems.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.layoutViewField_colId, Me.layoutViewField_colCONUM1, Me.layoutViewField_colDDIVNUM1, Me.layoutViewField_colSHIP_FAX, Me.layoutViewField_LayoutViewColumn1_1, Me.layoutViewField_LayoutViewColumn1_4, Me.layoutViewField_LayoutViewColumn1_6})
        Me.LayoutView1.Name = "LayoutView1"
        Me.LayoutView1.OptionsBehavior.AllowExpandCollapse = False
        Me.LayoutView1.OptionsBehavior.AllowRuntimeCustomization = False
        Me.LayoutView1.OptionsBehavior.Editable = False
        Me.LayoutView1.OptionsBehavior.ReadOnly = True
        Me.LayoutView1.OptionsCustomization.AllowFilter = False
        Me.LayoutView1.OptionsCustomization.AllowSort = False
        Me.LayoutView1.OptionsMultiRecordMode.StretchCardToViewHeight = True
        Me.LayoutView1.OptionsMultiRecordMode.StretchCardToViewWidth = True
        Me.LayoutView1.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.AnimateAllContent
        Me.LayoutView1.OptionsView.CardArrangeRule = DevExpress.XtraGrid.Views.Layout.LayoutCardArrangeRule.AllowPartialCards
        Me.LayoutView1.OptionsView.ShowCardExpandButton = False
        Me.LayoutView1.OptionsView.ShowCardFieldBorders = True
        Me.LayoutView1.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never
        Me.LayoutView1.OptionsView.ShowHeaderPanel = False
        Me.LayoutView1.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.Column
        Me.LayoutView1.TemplateCard = Me.LayoutViewCard1
        '
        'colId
        '
        Me.colId.FieldName = "Id"
        Me.colId.LayoutViewField = Me.layoutViewField_colId
        Me.colId.Name = "colId"
        '
        'layoutViewField_colId
        '
        Me.layoutViewField_colId.EditorPreferredWidth = 20
        Me.layoutViewField_colId.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_colId.Name = "layoutViewField_colId"
        Me.layoutViewField_colId.Size = New System.Drawing.Size(510, 261)
        Me.layoutViewField_colId.TextSize = New System.Drawing.Size(89, 13)
        '
        'colCONUM1
        '
        Me.colCONUM1.FieldName = "CONUM"
        Me.colCONUM1.LayoutViewField = Me.layoutViewField_colCONUM1
        Me.colCONUM1.Name = "colCONUM1"
        '
        'layoutViewField_colCONUM1
        '
        Me.layoutViewField_colCONUM1.EditorPreferredWidth = 20
        Me.layoutViewField_colCONUM1.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_colCONUM1.Name = "layoutViewField_colCONUM1"
        Me.layoutViewField_colCONUM1.Size = New System.Drawing.Size(510, 261)
        Me.layoutViewField_colCONUM1.TextSize = New System.Drawing.Size(89, 13)
        '
        'colDDIVNUM1
        '
        Me.colDDIVNUM1.FieldName = "DDIVNUM"
        Me.colDDIVNUM1.LayoutViewField = Me.layoutViewField_colDDIVNUM1
        Me.colDDIVNUM1.Name = "colDDIVNUM1"
        '
        'layoutViewField_colDDIVNUM1
        '
        Me.layoutViewField_colDDIVNUM1.EditorPreferredWidth = 20
        Me.layoutViewField_colDDIVNUM1.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_colDDIVNUM1.Name = "layoutViewField_colDDIVNUM1"
        Me.layoutViewField_colDDIVNUM1.Size = New System.Drawing.Size(510, 261)
        Me.layoutViewField_colDDIVNUM1.TextSize = New System.Drawing.Size(89, 13)
        '
        'colPRNUM
        '
        Me.colPRNUM.FieldName = "PRNUM"
        Me.colPRNUM.LayoutViewField = Me.layoutViewField_colPRNUM
        Me.colPRNUM.Name = "colPRNUM"
        '
        'layoutViewField_colPRNUM
        '
        Me.layoutViewField_colPRNUM.EditorPreferredWidth = 45
        Me.layoutViewField_colPRNUM.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_colPRNUM.Name = "layoutViewField_colPRNUM"
        Me.layoutViewField_colPRNUM.Size = New System.Drawing.Size(136, 24)
        Me.layoutViewField_colPRNUM.TextSize = New System.Drawing.Size(82, 13)
        '
        'colExpirationDate
        '
        Me.colExpirationDate.FieldName = "ExpirationDate"
        Me.colExpirationDate.LayoutViewField = Me.layoutViewField_colExpirationDate
        Me.colExpirationDate.Name = "colExpirationDate"
        '
        'layoutViewField_colExpirationDate
        '
        Me.layoutViewField_colExpirationDate.EditorPreferredWidth = 93
        Me.layoutViewField_colExpirationDate.Location = New System.Drawing.Point(136, 0)
        Me.layoutViewField_colExpirationDate.Name = "layoutViewField_colExpirationDate"
        Me.layoutViewField_colExpirationDate.Size = New System.Drawing.Size(184, 24)
        Me.layoutViewField_colExpirationDate.TextSize = New System.Drawing.Size(82, 13)
        '
        'colSHIP_NAME
        '
        Me.colSHIP_NAME.Caption = "Name"
        Me.colSHIP_NAME.FieldName = "SHIP_NAME"
        Me.colSHIP_NAME.LayoutViewField = Me.layoutViewField_colSHIP_NAME
        Me.colSHIP_NAME.Name = "colSHIP_NAME"
        '
        'layoutViewField_colSHIP_NAME
        '
        Me.layoutViewField_colSHIP_NAME.EditorPreferredWidth = 419
        Me.layoutViewField_colSHIP_NAME.Location = New System.Drawing.Point(0, 24)
        Me.layoutViewField_colSHIP_NAME.Name = "layoutViewField_colSHIP_NAME"
        Me.layoutViewField_colSHIP_NAME.Size = New System.Drawing.Size(510, 24)
        Me.layoutViewField_colSHIP_NAME.TextSize = New System.Drawing.Size(82, 13)
        '
        'colSHIP_STREET
        '
        Me.colSHIP_STREET.Caption = "Street"
        Me.colSHIP_STREET.FieldName = "SHIP_STREET"
        Me.colSHIP_STREET.LayoutViewField = Me.layoutViewField_colSHIP_STREET
        Me.colSHIP_STREET.Name = "colSHIP_STREET"
        '
        'layoutViewField_colSHIP_STREET
        '
        Me.layoutViewField_colSHIP_STREET.EditorPreferredWidth = 419
        Me.layoutViewField_colSHIP_STREET.Location = New System.Drawing.Point(0, 48)
        Me.layoutViewField_colSHIP_STREET.Name = "layoutViewField_colSHIP_STREET"
        Me.layoutViewField_colSHIP_STREET.Size = New System.Drawing.Size(510, 24)
        Me.layoutViewField_colSHIP_STREET.TextSize = New System.Drawing.Size(82, 13)
        '
        'colSHIP_CITY
        '
        Me.colSHIP_CITY.Caption = "City"
        Me.colSHIP_CITY.FieldName = "SHIP_CITY"
        Me.colSHIP_CITY.LayoutViewField = Me.layoutViewField_colSHIP_CITY
        Me.colSHIP_CITY.Name = "colSHIP_CITY"
        '
        'layoutViewField_colSHIP_CITY
        '
        Me.layoutViewField_colSHIP_CITY.EditorPreferredWidth = 163
        Me.layoutViewField_colSHIP_CITY.Location = New System.Drawing.Point(0, 72)
        Me.layoutViewField_colSHIP_CITY.Name = "layoutViewField_colSHIP_CITY"
        Me.layoutViewField_colSHIP_CITY.Size = New System.Drawing.Size(254, 24)
        Me.layoutViewField_colSHIP_CITY.TextSize = New System.Drawing.Size(82, 13)
        '
        'colSHIP_STATE
        '
        Me.colSHIP_STATE.Caption = "State"
        Me.colSHIP_STATE.FieldName = "SHIP_STATE"
        Me.colSHIP_STATE.LayoutViewField = Me.layoutViewField_colSHIP_STATE
        Me.colSHIP_STATE.Name = "colSHIP_STATE"
        '
        'layoutViewField_colSHIP_STATE
        '
        Me.layoutViewField_colSHIP_STATE.EditorPreferredWidth = 104
        Me.layoutViewField_colSHIP_STATE.Location = New System.Drawing.Point(254, 72)
        Me.layoutViewField_colSHIP_STATE.Name = "layoutViewField_colSHIP_STATE"
        Me.layoutViewField_colSHIP_STATE.Size = New System.Drawing.Size(143, 24)
        Me.layoutViewField_colSHIP_STATE.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.layoutViewField_colSHIP_STATE.TextSize = New System.Drawing.Size(30, 13)
        Me.layoutViewField_colSHIP_STATE.TextToControlDistance = 5
        '
        'colSHIP_ZIP
        '
        Me.colSHIP_ZIP.Caption = "Zip"
        Me.colSHIP_ZIP.FieldName = "SHIP_ZIP"
        Me.colSHIP_ZIP.LayoutViewField = Me.layoutViewField_colSHIP_ZIP
        Me.colSHIP_ZIP.Name = "colSHIP_ZIP"
        '
        'layoutViewField_colSHIP_ZIP
        '
        Me.layoutViewField_colSHIP_ZIP.EditorPreferredWidth = 86
        Me.layoutViewField_colSHIP_ZIP.Location = New System.Drawing.Point(397, 72)
        Me.layoutViewField_colSHIP_ZIP.Name = "layoutViewField_colSHIP_ZIP"
        Me.layoutViewField_colSHIP_ZIP.Size = New System.Drawing.Size(113, 24)
        Me.layoutViewField_colSHIP_ZIP.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.layoutViewField_colSHIP_ZIP.TextSize = New System.Drawing.Size(18, 13)
        Me.layoutViewField_colSHIP_ZIP.TextToControlDistance = 5
        '
        'colSHIP_PHONE
        '
        Me.colSHIP_PHONE.Caption = "Phone"
        Me.colSHIP_PHONE.FieldName = "SHIP_PHONE"
        Me.colSHIP_PHONE.LayoutViewField = Me.layoutViewField_colSHIP_PHONE
        Me.colSHIP_PHONE.Name = "colSHIP_PHONE"
        '
        'layoutViewField_colSHIP_PHONE
        '
        Me.layoutViewField_colSHIP_PHONE.EditorPreferredWidth = 305
        Me.layoutViewField_colSHIP_PHONE.Location = New System.Drawing.Point(0, 96)
        Me.layoutViewField_colSHIP_PHONE.Name = "layoutViewField_colSHIP_PHONE"
        Me.layoutViewField_colSHIP_PHONE.Size = New System.Drawing.Size(396, 24)
        Me.layoutViewField_colSHIP_PHONE.TextSize = New System.Drawing.Size(82, 13)
        '
        'colSHIP_FAX
        '
        Me.colSHIP_FAX.FieldName = "SHIP_FAX"
        Me.colSHIP_FAX.LayoutViewField = Me.layoutViewField_colSHIP_FAX
        Me.colSHIP_FAX.Name = "colSHIP_FAX"
        '
        'layoutViewField_colSHIP_FAX
        '
        Me.layoutViewField_colSHIP_FAX.EditorPreferredWidth = 20
        Me.layoutViewField_colSHIP_FAX.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_colSHIP_FAX.Name = "layoutViewField_colSHIP_FAX"
        Me.layoutViewField_colSHIP_FAX.Size = New System.Drawing.Size(510, 261)
        Me.layoutViewField_colSHIP_FAX.TextSize = New System.Drawing.Size(89, 13)
        '
        'colSHIP_EXTENSION
        '
        Me.colSHIP_EXTENSION.Caption = "Ext"
        Me.colSHIP_EXTENSION.FieldName = "SHIP_EXTENSION"
        Me.colSHIP_EXTENSION.LayoutViewField = Me.layoutViewField_colSHIP_EXTENSION
        Me.colSHIP_EXTENSION.Name = "colSHIP_EXTENSION"
        '
        'layoutViewField_colSHIP_EXTENSION
        '
        Me.layoutViewField_colSHIP_EXTENSION.EditorPreferredWidth = 85
        Me.layoutViewField_colSHIP_EXTENSION.Location = New System.Drawing.Point(396, 96)
        Me.layoutViewField_colSHIP_EXTENSION.Name = "layoutViewField_colSHIP_EXTENSION"
        Me.layoutViewField_colSHIP_EXTENSION.Size = New System.Drawing.Size(114, 24)
        Me.layoutViewField_colSHIP_EXTENSION.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.layoutViewField_colSHIP_EXTENSION.TextSize = New System.Drawing.Size(20, 13)
        Me.layoutViewField_colSHIP_EXTENSION.TextToControlDistance = 5
        '
        'colSHIP_MODEM
        '
        Me.colSHIP_MODEM.Caption = "Modem"
        Me.colSHIP_MODEM.FieldName = "SHIP_MODEM"
        Me.colSHIP_MODEM.LayoutViewField = Me.layoutViewField_colSHIP_MODEM
        Me.colSHIP_MODEM.Name = "colSHIP_MODEM"
        '
        'layoutViewField_colSHIP_MODEM
        '
        Me.layoutViewField_colSHIP_MODEM.EditorPreferredWidth = 419
        Me.layoutViewField_colSHIP_MODEM.Location = New System.Drawing.Point(0, 120)
        Me.layoutViewField_colSHIP_MODEM.Name = "layoutViewField_colSHIP_MODEM"
        Me.layoutViewField_colSHIP_MODEM.Size = New System.Drawing.Size(510, 24)
        Me.layoutViewField_colSHIP_MODEM.TextSize = New System.Drawing.Size(82, 13)
        '
        'colDPR_CONTACT
        '
        Me.colDPR_CONTACT.Caption = "Confidential For"
        Me.colDPR_CONTACT.FieldName = "DPR_CONTACT"
        Me.colDPR_CONTACT.LayoutViewField = Me.layoutViewField_colDPR_CONTACT
        Me.colDPR_CONTACT.Name = "colDPR_CONTACT"
        '
        'layoutViewField_colDPR_CONTACT
        '
        Me.layoutViewField_colDPR_CONTACT.EditorPreferredWidth = 163
        Me.layoutViewField_colDPR_CONTACT.Location = New System.Drawing.Point(0, 144)
        Me.layoutViewField_colDPR_CONTACT.Name = "layoutViewField_colDPR_CONTACT"
        Me.layoutViewField_colDPR_CONTACT.Size = New System.Drawing.Size(254, 24)
        Me.layoutViewField_colDPR_CONTACT.TextSize = New System.Drawing.Size(82, 13)
        '
        'colDDELVDESC
        '
        Me.colDDELVDESC.Caption = "Delivery Method"
        Me.colDDELVDESC.FieldName = "DDELVDESC"
        Me.colDDELVDESC.LayoutViewField = Me.layoutViewField_colDDELVDESC
        Me.colDDELVDESC.Name = "colDDELVDESC"
        '
        'layoutViewField_colDDELVDESC
        '
        Me.layoutViewField_colDDELVDESC.EditorPreferredWidth = 165
        Me.layoutViewField_colDDELVDESC.Location = New System.Drawing.Point(254, 144)
        Me.layoutViewField_colDDELVDESC.Name = "layoutViewField_colDDELVDESC"
        Me.layoutViewField_colDDELVDESC.Size = New System.Drawing.Size(256, 24)
        Me.layoutViewField_colDDELVDESC.TextSize = New System.Drawing.Size(82, 13)
        '
        'colNotes
        '
        Me.colNotes.Caption = "Notes"
        Me.colNotes.FieldName = "Notes"
        Me.colNotes.LayoutViewField = Me.layoutViewField_LayoutViewColumn1
        Me.colNotes.Name = "colNotes"
        '
        'layoutViewField_LayoutViewColumn1
        '
        Me.layoutViewField_LayoutViewColumn1.EditorPreferredWidth = 419
        Me.layoutViewField_LayoutViewColumn1.Location = New System.Drawing.Point(0, 168)
        Me.layoutViewField_LayoutViewColumn1.Name = "layoutViewField_LayoutViewColumn1"
        Me.layoutViewField_LayoutViewColumn1.Size = New System.Drawing.Size(510, 24)
        Me.layoutViewField_LayoutViewColumn1.TextSize = New System.Drawing.Size(82, 13)
        '
        'colPermanent
        '
        Me.colPermanent.Caption = "Permanent"
        Me.colPermanent.FieldName = "Permanent"
        Me.colPermanent.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_1
        Me.colPermanent.Name = "colPermanent"
        '
        'layoutViewField_LayoutViewColumn1_1
        '
        Me.layoutViewField_LayoutViewColumn1_1.EditorPreferredWidth = 20
        Me.layoutViewField_LayoutViewColumn1_1.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_LayoutViewColumn1_1.Name = "layoutViewField_LayoutViewColumn1_1"
        Me.layoutViewField_LayoutViewColumn1_1.Size = New System.Drawing.Size(510, 261)
        Me.layoutViewField_LayoutViewColumn1_1.TextSize = New System.Drawing.Size(82, 13)
        '
        'colAddDateTime
        '
        Me.colAddDateTime.Caption = "Date Changed"
        Me.colAddDateTime.DisplayFormat.FormatString = "g"
        Me.colAddDateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colAddDateTime.FieldName = "AddDateTime"
        Me.colAddDateTime.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_2
        Me.colAddDateTime.Name = "colAddDateTime"
        '
        'layoutViewField_LayoutViewColumn1_2
        '
        Me.layoutViewField_LayoutViewColumn1_2.EditorPreferredWidth = 163
        Me.layoutViewField_LayoutViewColumn1_2.Location = New System.Drawing.Point(0, 192)
        Me.layoutViewField_LayoutViewColumn1_2.Name = "layoutViewField_LayoutViewColumn1_2"
        Me.layoutViewField_LayoutViewColumn1_2.Size = New System.Drawing.Size(254, 24)
        Me.layoutViewField_LayoutViewColumn1_2.TextSize = New System.Drawing.Size(82, 13)
        '
        'colAddUser
        '
        Me.colAddUser.Caption = "Changed By"
        Me.colAddUser.FieldName = "AddUser"
        Me.colAddUser.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_3
        Me.colAddUser.Name = "colAddUser"
        '
        'layoutViewField_LayoutViewColumn1_3
        '
        Me.layoutViewField_LayoutViewColumn1_3.EditorPreferredWidth = 165
        Me.layoutViewField_LayoutViewColumn1_3.Location = New System.Drawing.Point(254, 192)
        Me.layoutViewField_LayoutViewColumn1_3.Name = "layoutViewField_LayoutViewColumn1_3"
        Me.layoutViewField_LayoutViewColumn1_3.Size = New System.Drawing.Size(256, 24)
        Me.layoutViewField_LayoutViewColumn1_3.TextSize = New System.Drawing.Size(82, 13)
        '
        'colAddedAfterPayrollPrint
        '
        Me.colAddedAfterPayrollPrint.Caption = "AddedAfterPayrollPrint"
        Me.colAddedAfterPayrollPrint.FieldName = "AddedAfterPayrollPrint"
        Me.colAddedAfterPayrollPrint.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_4
        Me.colAddedAfterPayrollPrint.Name = "colAddedAfterPayrollPrint"
        '
        'layoutViewField_LayoutViewColumn1_4
        '
        Me.layoutViewField_LayoutViewColumn1_4.EditorPreferredWidth = 10
        Me.layoutViewField_LayoutViewColumn1_4.Location = New System.Drawing.Point(0, 0)
        Me.layoutViewField_LayoutViewColumn1_4.MaxSize = New System.Drawing.Size(0, 24)
        Me.layoutViewField_LayoutViewColumn1_4.MinSize = New System.Drawing.Size(97, 24)
        Me.layoutViewField_LayoutViewColumn1_4.Name = "layoutViewField_LayoutViewColumn1_4"
        Me.layoutViewField_LayoutViewColumn1_4.Size = New System.Drawing.Size(510, 40)
        Me.layoutViewField_LayoutViewColumn1_4.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.layoutViewField_LayoutViewColumn1_4.TextSize = New System.Drawing.Size(78, 20)
        '
        'AddedAfterPayrollPrintMsg
        '
        Me.AddedAfterPayrollPrintMsg.Caption = "Alert"
        Me.AddedAfterPayrollPrintMsg.CustomizationCaption = " "
        Me.AddedAfterPayrollPrintMsg.FieldName = "AddedAfterPayrollPrintMsg"
        Me.AddedAfterPayrollPrintMsg.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_5
        Me.AddedAfterPayrollPrintMsg.Name = "AddedAfterPayrollPrintMsg"
        Me.AddedAfterPayrollPrintMsg.UnboundExpression = resources.GetString("AddedAfterPayrollPrintMsg.UnboundExpression")
        Me.AddedAfterPayrollPrintMsg.UnboundType = DevExpress.Data.UnboundColumnType.[String]
        '
        'layoutViewField_LayoutViewColumn1_5
        '
        Me.layoutViewField_LayoutViewColumn1_5.EditorPreferredWidth = 419
        Me.layoutViewField_LayoutViewColumn1_5.Location = New System.Drawing.Point(0, 216)
        Me.layoutViewField_LayoutViewColumn1_5.Name = "layoutViewField_LayoutViewColumn1_5"
        Me.layoutViewField_LayoutViewColumn1_5.Size = New System.Drawing.Size(510, 24)
        Me.layoutViewField_LayoutViewColumn1_5.TextSize = New System.Drawing.Size(82, 13)
        '
        'colQtrEndDateDisp
        '
        Me.colQtrEndDateDisp.Caption = "Qtr End Date"
        Me.colQtrEndDateDisp.FieldName = "QtrEndDateDisp"
        Me.colQtrEndDateDisp.LayoutViewField = Me.layoutViewField_LayoutViewColumn1_7
        Me.colQtrEndDateDisp.Name = "colQtrEndDateDisp"
        '
        'layoutViewField_LayoutViewColumn1_7
        '
        Me.layoutViewField_LayoutViewColumn1_7.EditorPreferredWidth = 99
        Me.layoutViewField_LayoutViewColumn1_7.Location = New System.Drawing.Point(320, 0)
        Me.layoutViewField_LayoutViewColumn1_7.Name = "layoutViewField_LayoutViewColumn1_7"
        Me.layoutViewField_LayoutViewColumn1_7.Size = New System.Drawing.Size(190, 24)
        Me.layoutViewField_LayoutViewColumn1_7.TextSize = New System.Drawing.Size(82, 13)
        '
        'LayoutViewCard1
        '
        Me.LayoutViewCard1.CustomizationFormText = "TemplateCard"
        Me.LayoutViewCard1.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText
        Me.LayoutViewCard1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.layoutViewField_colPRNUM, Me.layoutViewField_colSHIP_NAME, Me.layoutViewField_colSHIP_STREET, Me.layoutViewField_colSHIP_CITY, Me.layoutViewField_colSHIP_PHONE, Me.layoutViewField_colSHIP_MODEM, Me.layoutViewField_colDDELVDESC, Me.layoutViewField_colExpirationDate, Me.layoutViewField_colSHIP_STATE, Me.layoutViewField_colSHIP_ZIP, Me.layoutViewField_colSHIP_EXTENSION, Me.layoutViewField_LayoutViewColumn1, Me.layoutViewField_colDPR_CONTACT, Me.layoutViewField_LayoutViewColumn1_2, Me.layoutViewField_LayoutViewColumn1_3, Me.layoutViewField_LayoutViewColumn1_5, Me.layoutViewField_LayoutViewColumn1_7})
        Me.LayoutViewCard1.Name = "LayoutViewCard1"
        Me.LayoutViewCard1.OptionsItemText.TextToControlDistance = 5
        Me.LayoutViewCard1.Padding = New DevExpress.XtraLayout.Utils.Padding(3, 3, 3, 3)
        Me.LayoutViewCard1.ShowInCustomizationForm = False
        Me.LayoutViewCard1.Text = "TemplateCard"
        '
        'btnCancelTemporarilyOverride
        '
        Me.btnCancelTemporarilyOverride.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.cancel_16x161
        Me.btnCancelTemporarilyOverride.Location = New System.Drawing.Point(783, 581)
        Me.btnCancelTemporarilyOverride.Name = "btnCancelTemporarilyOverride"
        Me.btnCancelTemporarilyOverride.Size = New System.Drawing.Size(94, 22)
        Me.btnCancelTemporarilyOverride.StyleController = Me.LayoutControl1
        Me.btnCancelTemporarilyOverride.TabIndex = 35
        Me.btnCancelTemporarilyOverride.Text = "Cancel"
        '
        'btnSaveTempChange
        '
        Me.btnSaveTempChange.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.save_16x16
        Me.btnSaveTempChange.Location = New System.Drawing.Point(881, 581)
        Me.btnSaveTempChange.Name = "btnSaveTempChange"
        Me.btnSaveTempChange.Size = New System.Drawing.Size(121, 22)
        Me.btnSaveTempChange.StyleController = Me.LayoutControl1
        Me.btnSaveTempChange.TabIndex = 34
        Me.btnSaveTempChange.Text = "Save"
        '
        'btnTemporarilyChange
        '
        Me.btnTemporarilyChange.Location = New System.Drawing.Point(568, 223)
        Me.btnTemporarilyChange.Name = "btnTemporarilyChange"
        Me.btnTemporarilyChange.Size = New System.Drawing.Size(105, 22)
        Me.btnTemporarilyChange.StyleController = Me.LayoutControl1
        Me.btnTemporarilyChange.TabIndex = 33
        Me.btnTemporarilyChange.Text = "Temporarily Change"
        '
        'btnPermanentlyChange
        '
        Me.btnPermanentlyChange.Location = New System.Drawing.Point(905, 223)
        Me.btnPermanentlyChange.Name = "btnPermanentlyChange"
        Me.btnPermanentlyChange.Size = New System.Drawing.Size(109, 22)
        Me.btnPermanentlyChange.StyleController = Me.LayoutControl1
        Me.btnPermanentlyChange.TabIndex = 32
        Me.btnPermanentlyChange.Text = "Permanently Change"
        '
        'seOverridePayrollNum
        '
        Me.seOverridePayrollNum.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "PRNUM", True))
        Me.seOverridePayrollNum.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.seOverridePayrollNum.Location = New System.Drawing.Point(579, 368)
        Me.seOverridePayrollNum.Name = "seOverridePayrollNum"
        Me.seOverridePayrollNum.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.seOverridePayrollNum.Properties.MaxValue = New Decimal(New Integer() {100000, 0, 0, 0})
        Me.seOverridePayrollNum.Size = New System.Drawing.Size(50, 20)
        Me.seOverridePayrollNum.StyleController = Me.LayoutControl1
        Me.seOverridePayrollNum.TabIndex = 31
        '
        'deOverrideExpirationDate
        '
        Me.deOverrideExpirationDate.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "ExpirationDate", True))
        Me.deOverrideExpirationDate.EditValue = Nothing
        Me.deOverrideExpirationDate.Location = New System.Drawing.Point(420, 368)
        Me.deOverrideExpirationDate.Name = "deOverrideExpirationDate"
        Me.deOverrideExpirationDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deOverrideExpirationDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deOverrideExpirationDate.Size = New System.Drawing.Size(50, 20)
        Me.deOverrideExpirationDate.StyleController = Me.LayoutControl1
        Me.deOverrideExpirationDate.TabIndex = 30
        '
        'rgExpirationMode
        '
        Me.rgExpirationMode.Location = New System.Drawing.Point(420, 339)
        Me.rgExpirationMode.Name = "rgExpirationMode"
        Me.rgExpirationMode.Properties.Columns = 3
        Me.rgExpirationMode.Properties.Items.AddRange(New DevExpress.XtraEditors.Controls.RadioGroupItem() {New DevExpress.XtraEditors.Controls.RadioGroupItem(Nothing, "Payroll #"), New DevExpress.XtraEditors.Controls.RadioGroupItem(Nothing, "QE / YE"), New DevExpress.XtraEditors.Controls.RadioGroupItem(Nothing, "Expiration Date")})
        Me.rgExpirationMode.Size = New System.Drawing.Size(582, 25)
        Me.rgExpirationMode.StyleController = Me.LayoutControl1
        Me.rgExpirationMode.TabIndex = 29
        '
        'btnCanelPermanentlyOverride
        '
        Me.btnCanelPermanentlyOverride.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.cancel_16x161
        Me.btnCanelPermanentlyOverride.Location = New System.Drawing.Point(621, 393)
        Me.btnCanelPermanentlyOverride.Name = "btnCanelPermanentlyOverride"
        Me.btnCanelPermanentlyOverride.Size = New System.Drawing.Size(61, 22)
        Me.btnCanelPermanentlyOverride.StyleController = Me.LayoutControl1
        Me.btnCanelPermanentlyOverride.TabIndex = 28
        Me.btnCanelPermanentlyOverride.Text = "Cancel"
        '
        'btnSavePermChange
        '
        Me.btnSavePermChange.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.save_16x16
        Me.btnSavePermChange.Location = New System.Drawing.Point(686, 393)
        Me.btnSavePermChange.Name = "btnSavePermChange"
        Me.btnSavePermChange.Size = New System.Drawing.Size(89, 22)
        Me.btnSavePermChange.StyleController = Me.LayoutControl1
        Me.btnSavePermChange.TabIndex = 27
        Me.btnSavePermChange.Text = "Save"
        '
        'cbeStates
        '
        Me.cbeStates.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_STATE", True))
        Me.cbeStates.Location = New System.Drawing.Point(563, 297)
        Me.cbeStates.Name = "cbeStates"
        Me.cbeStates.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeStates.Size = New System.Drawing.Size(77, 20)
        Me.cbeStates.StyleController = Me.LayoutControl1
        Me.cbeStates.TabIndex = 26
        Me.cbeStates.TabStop = False
        ConditionValidationRule1.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule1.ErrorText = "State is required"
        Me.DxValidationProviderPermChange.SetValidationRule(Me.cbeStates, ConditionValidationRule1)
        '
        'BindingSourceDivision
        '
        Me.BindingSourceDivision.DataSource = GetType(Brands_FrontDesk.DIVISION)
        '
        'TextEdit18
        '
        Me.TextEdit18.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DPR_CONTACT", True))
        Me.TextEdit18.Location = New System.Drawing.Point(387, 369)
        Me.TextEdit18.Name = "TextEdit18"
        Me.TextEdit18.Size = New System.Drawing.Size(149, 20)
        Me.TextEdit18.StyleController = Me.LayoutControl1
        Me.TextEdit18.TabIndex = 25
        '
        'TextEdit13
        '
        Me.TextEdit13.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_EXTENSION", True))
        Me.TextEdit13.Location = New System.Drawing.Point(669, 321)
        Me.TextEdit13.Name = "TextEdit13"
        Me.TextEdit13.Size = New System.Drawing.Size(106, 20)
        Me.TextEdit13.StyleController = Me.LayoutControl1
        Me.TextEdit13.TabIndex = 21
        '
        'teDivisionZip
        '
        Me.teDivisionZip.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_ZIP", True))
        Me.teDivisionZip.Location = New System.Drawing.Point(670, 297)
        Me.teDivisionZip.Name = "teDivisionZip"
        Me.teDivisionZip.Size = New System.Drawing.Size(105, 20)
        Me.teDivisionZip.StyleController = Me.LayoutControl1
        Me.teDivisionZip.TabIndex = 19
        ConditionValidationRule2.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule2.ErrorText = "Zip is required"
        Me.DxValidationProviderPermChange.SetValidationRule(Me.teDivisionZip, ConditionValidationRule2)
        '
        'TextEdit10
        '
        Me.TextEdit10.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_CITY", True))
        Me.TextEdit10.Location = New System.Drawing.Point(387, 297)
        Me.TextEdit10.Name = "TextEdit10"
        Me.TextEdit10.Size = New System.Drawing.Size(149, 20)
        Me.TextEdit10.StyleController = Me.LayoutControl1
        Me.TextEdit10.TabIndex = 19
        Me.TextEdit10.TabStop = False
        ConditionValidationRule3.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule3.ErrorText = "City is required"
        Me.DxValidationProviderPermChange.SetValidationRule(Me.TextEdit10, ConditionValidationRule3)
        '
        'teDivisionStreet
        '
        Me.teDivisionStreet.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_STREET", True))
        Me.teDivisionStreet.Location = New System.Drawing.Point(387, 273)
        Me.teDivisionStreet.Name = "teDivisionStreet"
        Me.teDivisionStreet.Size = New System.Drawing.Size(388, 20)
        Me.teDivisionStreet.StyleController = Me.LayoutControl1
        Me.teDivisionStreet.TabIndex = 18
        ConditionValidationRule4.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule4.ErrorText = "Street is required"
        Me.DxValidationProviderPermChange.SetValidationRule(Me.teDivisionStreet, ConditionValidationRule4)
        '
        'tePermChangeName
        '
        Me.tePermChangeName.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_NAME", True))
        Me.tePermChangeName.Location = New System.Drawing.Point(387, 249)
        Me.tePermChangeName.Name = "tePermChangeName"
        Me.tePermChangeName.Size = New System.Drawing.Size(388, 20)
        Me.tePermChangeName.StyleController = Me.LayoutControl1
        Me.tePermChangeName.TabIndex = 17
        ConditionValidationRule5.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule5.ErrorText = "Name is required"
        Me.DxValidationProviderPermChange.SetValidationRule(Me.tePermChangeName, ConditionValidationRule5)
        '
        'TextEdit16
        '
        Me.TextEdit16.Location = New System.Drawing.Point(399, 187)
        Me.TextEdit16.Name = "TextEdit16"
        Me.TextEdit16.Properties.ReadOnly = True
        Me.TextEdit16.Size = New System.Drawing.Size(246, 20)
        Me.TextEdit16.StyleController = Me.LayoutControl1
        Me.TextEdit16.TabIndex = 16
        '
        'ceShowInactiveDivisions
        '
        Me.ceShowInactiveDivisions.Location = New System.Drawing.Point(12, 12)
        Me.ceShowInactiveDivisions.Name = "ceShowInactiveDivisions"
        Me.ceShowInactiveDivisions.Properties.Caption = "Show Inactive Divisions"
        Me.ceShowInactiveDivisions.Size = New System.Drawing.Size(133, 19)
        Me.ceShowInactiveDivisions.StyleController = Me.LayoutControl1
        Me.ceShowInactiveDivisions.TabIndex = 14
        '
        'TextEdit6
        '
        Me.TextEdit6.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipExtension", True))
        Me.TextEdit6.Location = New System.Drawing.Point(842, 139)
        Me.TextEdit6.Name = "TextEdit6"
        Me.TextEdit6.Properties.ReadOnly = True
        Me.TextEdit6.Size = New System.Drawing.Size(160, 20)
        Me.TextEdit6.StyleController = Me.LayoutControl1
        Me.TextEdit6.TabIndex = 11
        '
        'BindingSourceCurrentShippingInfo
        '
        Me.BindingSourceCurrentShippingInfo.DataSource = GetType(Brands_FrontDesk.prc_GetShippingAddressResult)
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipState", True))
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(672, 115)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.ReadOnly = True
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(141, 20)
        Me.ComboBoxEdit1.StyleController = Me.LayoutControl1
        Me.ComboBoxEdit1.TabIndex = 9
        '
        'TextEdit4
        '
        Me.TextEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipCity", True))
        Me.TextEdit4.Location = New System.Drawing.Point(399, 115)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Properties.ReadOnly = True
        Me.TextEdit4.Size = New System.Drawing.Size(246, 20)
        Me.TextEdit4.StyleController = Me.LayoutControl1
        Me.TextEdit4.TabIndex = 8
        '
        'TextEdit3
        '
        Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipZip", True))
        Me.TextEdit3.Location = New System.Drawing.Point(840, 115)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.ReadOnly = True
        Me.TextEdit3.Size = New System.Drawing.Size(162, 20)
        Me.TextEdit3.StyleController = Me.LayoutControl1
        Me.TextEdit3.TabIndex = 7
        '
        'TextEdit2
        '
        Me.TextEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipAddress", True))
        Me.TextEdit2.Location = New System.Drawing.Point(399, 91)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.ReadOnly = True
        Me.TextEdit2.Size = New System.Drawing.Size(603, 20)
        Me.TextEdit2.StyleController = Me.LayoutControl1
        Me.TextEdit2.TabIndex = 6
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipCo", True))
        Me.TextEdit1.Location = New System.Drawing.Point(399, 67)
        Me.TextEdit1.MaximumSize = New System.Drawing.Size(388, 0)
        Me.TextEdit1.MinimumSize = New System.Drawing.Size(388, 0)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.ReadOnly = True
        Me.TextEdit1.Size = New System.Drawing.Size(388, 20)
        Me.TextEdit1.StyleController = Me.LayoutControl1
        Me.TextEdit1.TabIndex = 5
        '
        'gcDivisions
        '
        Me.gcDivisions.Location = New System.Drawing.Point(12, 35)
        Me.gcDivisions.MainView = Me.gvDivisions
        Me.gcDivisions.Name = "gcDivisions"
        Me.gcDivisions.Size = New System.Drawing.Size(279, 747)
        Me.gcDivisions.TabIndex = 4
        Me.gcDivisions.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvDivisions})
        '
        'gvDivisions
        '
        Me.gvDivisions.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM, Me.colCO_NAME, Me.colDDIVNUM, Me.colDDIVNAME, Me.colDivShip, Me.colCoShip, Me.colShipCo, Me.colShipAddress, Me.colShipCity, Me.colShipState, Me.colShipZip, Me.colShipContact, Me.colShipPhone, Me.colShipExtension, Me.colShipModem, Me.colShipMethod, Me.colShipCharge, Me.colEEcnt, Me.colDivAddress})
        Me.gvDivisions.GridControl = Me.gcDivisions
        Me.gvDivisions.Name = "gvDivisions"
        Me.gvDivisions.OptionsBehavior.Editable = False
        Me.gvDivisions.OptionsView.ShowGroupPanel = False
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        '
        'colCO_NAME
        '
        Me.colCO_NAME.FieldName = "CO_NAME"
        Me.colCO_NAME.Name = "colCO_NAME"
        '
        'colDDIVNUM
        '
        Me.colDDIVNUM.Caption = "Division"
        Me.colDDIVNUM.FieldName = "DDIVNUM"
        Me.colDDIVNUM.Name = "colDDIVNUM"
        Me.colDDIVNUM.Visible = True
        Me.colDDIVNUM.VisibleIndex = 0
        Me.colDDIVNUM.Width = 67
        '
        'colDDIVNAME
        '
        Me.colDDIVNAME.Caption = "Division Name"
        Me.colDDIVNAME.FieldName = "DDIVNAME"
        Me.colDDIVNAME.Name = "colDDIVNAME"
        Me.colDDIVNAME.Visible = True
        Me.colDDIVNAME.VisibleIndex = 1
        Me.colDDIVNAME.Width = 202
        '
        'colDivShip
        '
        Me.colDivShip.FieldName = "DivShip"
        Me.colDivShip.Name = "colDivShip"
        '
        'colCoShip
        '
        Me.colCoShip.FieldName = "CoShip"
        Me.colCoShip.Name = "colCoShip"
        '
        'colShipCo
        '
        Me.colShipCo.FieldName = "ShipCo"
        Me.colShipCo.Name = "colShipCo"
        '
        'colShipAddress
        '
        Me.colShipAddress.FieldName = "ShipAddress"
        Me.colShipAddress.Name = "colShipAddress"
        '
        'colShipCity
        '
        Me.colShipCity.FieldName = "ShipCity"
        Me.colShipCity.Name = "colShipCity"
        '
        'colShipState
        '
        Me.colShipState.FieldName = "ShipState"
        Me.colShipState.Name = "colShipState"
        '
        'colShipZip
        '
        Me.colShipZip.FieldName = "ShipZip"
        Me.colShipZip.Name = "colShipZip"
        '
        'colShipContact
        '
        Me.colShipContact.FieldName = "ShipContact"
        Me.colShipContact.Name = "colShipContact"
        '
        'colShipPhone
        '
        Me.colShipPhone.FieldName = "ShipPhone"
        Me.colShipPhone.Name = "colShipPhone"
        '
        'colShipExtension
        '
        Me.colShipExtension.FieldName = "ShipExtension"
        Me.colShipExtension.Name = "colShipExtension"
        '
        'colShipModem
        '
        Me.colShipModem.FieldName = "ShipModem"
        Me.colShipModem.Name = "colShipModem"
        '
        'colShipMethod
        '
        Me.colShipMethod.FieldName = "ShipMethod"
        Me.colShipMethod.Name = "colShipMethod"
        '
        'colShipCharge
        '
        Me.colShipCharge.FieldName = "ShipCharge"
        Me.colShipCharge.Name = "colShipCharge"
        '
        'colEEcnt
        '
        Me.colEEcnt.FieldName = "EEcnt"
        Me.colEEcnt.Name = "colEEcnt"
        '
        'colDivAddress
        '
        Me.colDivAddress.Caption = "Div Address"
        Me.colDivAddress.FieldName = "DivAddress"
        Me.colDivAddress.Name = "colDivAddress"
        Me.colDivAddress.Visible = True
        Me.colDivAddress.VisibleIndex = 2
        '
        'TextEdit20
        '
        Me.TextEdit20.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_NAME", True))
        Me.TextEdit20.Location = New System.Drawing.Point(420, 398)
        Me.TextEdit20.Name = "TextEdit20"
        Me.TextEdit20.Size = New System.Drawing.Size(582, 20)
        Me.TextEdit20.StyleController = Me.LayoutControl1
        Me.TextEdit20.TabIndex = 17
        ConditionValidationRule6.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule6.ErrorText = "Name is required"
        Me.DxValidationProviderTempChange.SetValidationRule(Me.TextEdit20, ConditionValidationRule6)
        '
        'teOverrideStreet
        '
        Me.teOverrideStreet.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_STREET", True))
        Me.teOverrideStreet.Location = New System.Drawing.Point(420, 422)
        Me.teOverrideStreet.Name = "teOverrideStreet"
        Me.teOverrideStreet.Size = New System.Drawing.Size(582, 20)
        Me.teOverrideStreet.StyleController = Me.LayoutControl1
        Me.teOverrideStreet.TabIndex = 18
        ConditionValidationRule7.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule7.ErrorText = "Street Is Required"
        Me.DxValidationProviderTempChange.SetValidationRule(Me.teOverrideStreet, ConditionValidationRule7)
        '
        'TextEdit24
        '
        Me.TextEdit24.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_CITY", True))
        Me.TextEdit24.Location = New System.Drawing.Point(420, 446)
        Me.TextEdit24.Name = "TextEdit24"
        Me.TextEdit24.Size = New System.Drawing.Size(236, 20)
        Me.TextEdit24.StyleController = Me.LayoutControl1
        Me.TextEdit24.TabIndex = 19
        Me.TextEdit24.TabStop = False
        ConditionValidationRule8.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule8.ErrorText = "City Is Required"
        Me.DxValidationProviderTempChange.SetValidationRule(Me.TextEdit24, ConditionValidationRule8)
        '
        'cbeStatesOverrideShipAddress
        '
        Me.cbeStatesOverrideShipAddress.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_STATE", True))
        Me.cbeStatesOverrideShipAddress.Location = New System.Drawing.Point(683, 446)
        Me.cbeStatesOverrideShipAddress.Name = "cbeStatesOverrideShipAddress"
        Me.cbeStatesOverrideShipAddress.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeStatesOverrideShipAddress.Size = New System.Drawing.Size(146, 20)
        Me.cbeStatesOverrideShipAddress.StyleController = Me.LayoutControl1
        Me.cbeStatesOverrideShipAddress.TabIndex = 26
        Me.cbeStatesOverrideShipAddress.TabStop = False
        ConditionValidationRule9.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule9.ErrorText = "State Is Required"
        Me.DxValidationProviderTempChange.SetValidationRule(Me.cbeStatesOverrideShipAddress, ConditionValidationRule9)
        '
        'teZipOverride
        '
        Me.teZipOverride.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_ZIP", True))
        Me.teZipOverride.Location = New System.Drawing.Point(859, 446)
        Me.teZipOverride.Name = "teZipOverride"
        Me.teZipOverride.Size = New System.Drawing.Size(143, 20)
        Me.teZipOverride.StyleController = Me.LayoutControl1
        Me.teZipOverride.TabIndex = 19
        ConditionValidationRule10.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule10.ErrorText = "Zip Is Required"
        Me.DxValidationProviderTempChange.SetValidationRule(Me.teZipOverride, ConditionValidationRule10)
        '
        'TextEdit23
        '
        Me.TextEdit23.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_EXTENSION", True))
        Me.TextEdit23.Location = New System.Drawing.Point(911, 470)
        Me.TextEdit23.Name = "TextEdit23"
        Me.TextEdit23.Size = New System.Drawing.Size(91, 20)
        Me.TextEdit23.StyleController = Me.LayoutControl1
        Me.TextEdit23.TabIndex = 22
        '
        'TextEdit26
        '
        Me.TextEdit26.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "PR_CONTACT", True))
        Me.TextEdit26.Location = New System.Drawing.Point(420, 518)
        Me.TextEdit26.Name = "TextEdit26"
        Me.TextEdit26.Size = New System.Drawing.Size(236, 20)
        Me.TextEdit26.StyleController = Me.LayoutControl1
        Me.TextEdit26.TabIndex = 25
        '
        'cbeDivisionDeliveryMethod
        '
        Me.cbeDivisionDeliveryMethod.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DDELVDESC", True))
        Me.cbeDivisionDeliveryMethod.Location = New System.Drawing.Point(624, 369)
        Me.cbeDivisionDeliveryMethod.Name = "cbeDivisionDeliveryMethod"
        Me.cbeDivisionDeliveryMethod.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeDivisionDeliveryMethod.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeDivisionDeliveryMethod.Size = New System.Drawing.Size(151, 20)
        Me.cbeDivisionDeliveryMethod.StyleController = Me.LayoutControl1
        Me.cbeDivisionDeliveryMethod.TabIndex = 24
        '
        'cbeDeliveryMethodOverride
        '
        Me.cbeDeliveryMethodOverride.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "DELVDESC", True))
        Me.cbeDeliveryMethodOverride.Location = New System.Drawing.Point(740, 518)
        Me.cbeDeliveryMethodOverride.Name = "cbeDeliveryMethodOverride"
        Me.cbeDeliveryMethodOverride.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeDeliveryMethodOverride.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeDeliveryMethodOverride.Size = New System.Drawing.Size(262, 20)
        Me.cbeDeliveryMethodOverride.StyleController = Me.LayoutControl1
        Me.cbeDeliveryMethodOverride.TabIndex = 24
        '
        'beShippingPhone
        '
        Me.beShippingPhone.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipPhone", True))
        Me.beShippingPhone.Location = New System.Drawing.Point(399, 139)
        Me.beShippingPhone.Name = "beShippingPhone"
        Me.beShippingPhone.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beShippingPhone.Properties.ReadOnly = True
        Me.beShippingPhone.Size = New System.Drawing.Size(414, 20)
        Me.beShippingPhone.StyleController = Me.LayoutControl1
        Me.beShippingPhone.TabIndex = 10
        '
        'beShppingModem
        '
        Me.beShppingModem.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipModem", True))
        Me.beShppingModem.Location = New System.Drawing.Point(399, 163)
        Me.beShppingModem.Name = "beShppingModem"
        Me.beShppingModem.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beShppingModem.Properties.ReadOnly = True
        Me.beShppingModem.Size = New System.Drawing.Size(603, 20)
        Me.beShppingModem.StyleController = Me.LayoutControl1
        Me.beShppingModem.TabIndex = 12
        '
        'bePerChangePhone
        '
        Me.bePerChangePhone.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_PHONE", True))
        Me.bePerChangePhone.Location = New System.Drawing.Point(387, 321)
        Me.bePerChangePhone.Name = "bePerChangePhone"
        Me.bePerChangePhone.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.bePerChangePhone.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.bePerChangePhone.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.bePerChangePhone.Size = New System.Drawing.Size(253, 20)
        Me.bePerChangePhone.StyleController = Me.LayoutControl1
        Me.bePerChangePhone.TabIndex = 20
        '
        'bePerChangeModem
        '
        Me.bePerChangeModem.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceDivision, "DSH_MODEM", True))
        Me.bePerChangeModem.Location = New System.Drawing.Point(387, 345)
        Me.bePerChangeModem.Name = "bePerChangeModem"
        Me.bePerChangeModem.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.bePerChangeModem.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.bePerChangeModem.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.bePerChangeModem.Size = New System.Drawing.Size(388, 20)
        Me.bePerChangeModem.StyleController = Me.LayoutControl1
        Me.bePerChangeModem.TabIndex = 22
        '
        'beTempChangePhone
        '
        Me.beTempChangePhone.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_PHONE", True))
        Me.beTempChangePhone.Location = New System.Drawing.Point(420, 470)
        Me.beTempChangePhone.Name = "beTempChangePhone"
        Me.beTempChangePhone.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beTempChangePhone.Properties.Mask.EditMask = "(*************"
        Me.beTempChangePhone.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.beTempChangePhone.Size = New System.Drawing.Size(462, 20)
        Me.beTempChangePhone.StyleController = Me.LayoutControl1
        Me.beTempChangePhone.TabIndex = 21
        '
        'beTempChangeModem
        '
        Me.beTempChangeModem.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "SHIP_MODEM", True))
        Me.beTempChangeModem.Location = New System.Drawing.Point(420, 494)
        Me.beTempChangeModem.Name = "beTempChangeModem"
        Me.beTempChangeModem.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beTempChangeModem.Properties.Mask.EditMask = "(*************"
        Me.beTempChangeModem.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.beTempChangeModem.Size = New System.Drawing.Size(582, 20)
        Me.beTempChangeModem.StyleController = Me.LayoutControl1
        Me.beTempChangeModem.TabIndex = 23
        '
        'cbeCurrentShipDelvMethod
        '
        Me.cbeCurrentShipDelvMethod.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceCurrentShippingInfo, "ShipMethod", True))
        Me.cbeCurrentShipDelvMethod.Location = New System.Drawing.Point(733, 187)
        Me.cbeCurrentShipDelvMethod.Name = "cbeCurrentShipDelvMethod"
        Me.cbeCurrentShipDelvMethod.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeCurrentShipDelvMethod.Properties.ReadOnly = True
        Me.cbeCurrentShipDelvMethod.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeCurrentShipDelvMethod.Size = New System.Drawing.Size(269, 20)
        Me.cbeCurrentShipDelvMethod.StyleController = Me.LayoutControl1
        Me.cbeCurrentShipDelvMethod.TabIndex = 15
        '
        'TextEdit11
        '
        Me.TextEdit11.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_NAME", True))
        Me.TextEdit11.Location = New System.Drawing.Point(387, 278)
        Me.TextEdit11.Name = "TextEdit11"
        Me.TextEdit11.Size = New System.Drawing.Size(388, 20)
        Me.TextEdit11.StyleController = Me.LayoutControl1
        Me.TextEdit11.TabIndex = 17
        ConditionValidationRule11.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule11.ErrorText = "Name is required"
        Me.DxValidationProvider2.SetValidationRule(Me.TextEdit11, ConditionValidationRule11)
        '
        'BindingSourcePermCoChange
        '
        Me.BindingSourcePermCoChange.DataSource = GetType(Brands_FrontDesk.COMPANY)
        '
        'teStreetPermCoChange
        '
        Me.teStreetPermCoChange.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_STREET", True))
        Me.teStreetPermCoChange.Location = New System.Drawing.Point(387, 302)
        Me.teStreetPermCoChange.Name = "teStreetPermCoChange"
        Me.teStreetPermCoChange.Size = New System.Drawing.Size(388, 20)
        Me.teStreetPermCoChange.StyleController = Me.LayoutControl1
        Me.teStreetPermCoChange.TabIndex = 18
        ConditionValidationRule12.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule12.ErrorText = "Street is required"
        Me.DxValidationProvider2.SetValidationRule(Me.teStreetPermCoChange, ConditionValidationRule12)
        '
        'TextEdit9
        '
        Me.TextEdit9.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_CITY", True))
        Me.TextEdit9.Location = New System.Drawing.Point(387, 326)
        Me.TextEdit9.Name = "TextEdit9"
        Me.TextEdit9.Size = New System.Drawing.Size(149, 20)
        Me.TextEdit9.StyleController = Me.LayoutControl1
        Me.TextEdit9.TabIndex = 19
        Me.TextEdit9.TabStop = False
        ConditionValidationRule13.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule13.ErrorText = "City is required"
        Me.DxValidationProvider2.SetValidationRule(Me.TextEdit9, ConditionValidationRule13)
        '
        'cbeStatePermCoChange
        '
        Me.cbeStatePermCoChange.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_STATE", True))
        Me.cbeStatePermCoChange.Location = New System.Drawing.Point(563, 326)
        Me.cbeStatePermCoChange.Name = "cbeStatePermCoChange"
        Me.cbeStatePermCoChange.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeStatePermCoChange.Size = New System.Drawing.Size(102, 20)
        Me.cbeStatePermCoChange.StyleController = Me.LayoutControl1
        Me.cbeStatePermCoChange.TabIndex = 26
        Me.cbeStatePermCoChange.TabStop = False
        ConditionValidationRule14.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule14.ErrorText = "State is required"
        Me.DxValidationProvider2.SetValidationRule(Me.cbeStatePermCoChange, ConditionValidationRule14)
        '
        'teZipPermCoChange
        '
        Me.teZipPermCoChange.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_ZIP", True))
        Me.teZipPermCoChange.Location = New System.Drawing.Point(695, 326)
        Me.teZipPermCoChange.Name = "teZipPermCoChange"
        Me.teZipPermCoChange.Size = New System.Drawing.Size(80, 20)
        Me.teZipPermCoChange.StyleController = Me.LayoutControl1
        Me.teZipPermCoChange.TabIndex = 19
        ConditionValidationRule15.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule15.ErrorText = "Zip is required"
        Me.DxValidationProvider2.SetValidationRule(Me.teZipPermCoChange, ConditionValidationRule15)
        '
        'bePhonePermCoChange
        '
        Me.bePhonePermCoChange.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_PHONE", True))
        Me.bePhonePermCoChange.Location = New System.Drawing.Point(387, 350)
        Me.bePhonePermCoChange.Name = "bePhonePermCoChange"
        Me.bePhonePermCoChange.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.bePhonePermCoChange.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.bePhonePermCoChange.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.bePhonePermCoChange.Size = New System.Drawing.Size(278, 20)
        Me.bePhonePermCoChange.StyleController = Me.LayoutControl1
        Me.bePhonePermCoChange.TabIndex = 20
        '
        'TextEdit12
        '
        Me.TextEdit12.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_EXTENSION", True))
        Me.TextEdit12.Location = New System.Drawing.Point(694, 350)
        Me.TextEdit12.Name = "TextEdit12"
        Me.TextEdit12.Size = New System.Drawing.Size(81, 20)
        Me.TextEdit12.StyleController = Me.LayoutControl1
        Me.TextEdit12.TabIndex = 21
        '
        'beModemPermCoChange
        '
        Me.beModemPermCoChange.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_MODEM", True))
        Me.beModemPermCoChange.Location = New System.Drawing.Point(387, 374)
        Me.beModemPermCoChange.Name = "beModemPermCoChange"
        Me.beModemPermCoChange.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beModemPermCoChange.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.beModemPermCoChange.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.beModemPermCoChange.Size = New System.Drawing.Size(388, 20)
        Me.beModemPermCoChange.StyleController = Me.LayoutControl1
        Me.beModemPermCoChange.TabIndex = 22
        '
        'cbeDelvMethodPermCoChange
        '
        Me.cbeDelvMethodPermCoChange.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "DELVDESC", True))
        Me.cbeDelvMethodPermCoChange.Location = New System.Drawing.Point(624, 398)
        Me.cbeDelvMethodPermCoChange.Name = "cbeDelvMethodPermCoChange"
        Me.cbeDelvMethodPermCoChange.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeDelvMethodPermCoChange.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeDelvMethodPermCoChange.Size = New System.Drawing.Size(151, 20)
        Me.cbeDelvMethodPermCoChange.StyleController = Me.LayoutControl1
        Me.cbeDelvMethodPermCoChange.TabIndex = 24
        '
        'btnSavePermCoChange
        '
        Me.btnSavePermCoChange.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.save_16x16
        Me.btnSavePermCoChange.Location = New System.Drawing.Point(686, 422)
        Me.btnSavePermCoChange.Name = "btnSavePermCoChange"
        Me.btnSavePermCoChange.Size = New System.Drawing.Size(89, 22)
        Me.btnSavePermCoChange.StyleController = Me.LayoutControl1
        Me.btnSavePermCoChange.TabIndex = 27
        Me.btnSavePermCoChange.Text = "Save"
        '
        'btnCancelPermCoChange
        '
        Me.btnCancelPermCoChange.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.cancel_16x161
        Me.btnCancelPermCoChange.Location = New System.Drawing.Point(616, 422)
        Me.btnCancelPermCoChange.Name = "btnCancelPermCoChange"
        Me.btnCancelPermCoChange.Size = New System.Drawing.Size(66, 22)
        Me.btnCancelPermCoChange.StyleController = Me.LayoutControl1
        Me.btnCancelPermCoChange.TabIndex = 28
        Me.btnCancelPermCoChange.Text = "Cancel"
        '
        'TextEdit7
        '
        Me.TextEdit7.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourcePermCoChange, "SH_CONFOR", True))
        Me.TextEdit7.Location = New System.Drawing.Point(387, 398)
        Me.TextEdit7.Name = "TextEdit7"
        Me.TextEdit7.Size = New System.Drawing.Size(149, 20)
        Me.TextEdit7.StyleController = Me.LayoutControl1
        Me.TextEdit7.TabIndex = 25
        '
        'btnCopyTemporary
        '
        Me.btnCopyTemporary.Location = New System.Drawing.Point(677, 223)
        Me.btnCopyTemporary.Name = "btnCopyTemporary"
        Me.btnCopyTemporary.Size = New System.Drawing.Size(224, 22)
        Me.btnCopyTemporary.StyleController = Me.LayoutControl1
        Me.btnCopyTemporary.TabIndex = 41
        Me.btnCopyTemporary.Text = "Copy Highlighted"
        '
        'deQeYeDate
        '
        Me.deQeYeDate.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSourceShipAddressOverride, "QtrEndDate", True))
        Me.deQeYeDate.EditValue = Nothing
        Me.deQeYeDate.Location = New System.Drawing.Point(738, 368)
        Me.deQeYeDate.MaximumSize = New System.Drawing.Size(90, 0)
        Me.deQeYeDate.MinimumSize = New System.Drawing.Size(90, 0)
        Me.deQeYeDate.Name = "deQeYeDate"
        Me.deQeYeDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deQeYeDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deQeYeDate.Size = New System.Drawing.Size(90, 20)
        Me.deQeYeDate.StyleController = Me.LayoutControl1
        Me.deQeYeDate.TabIndex = 30
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AppearanceGroup.Font = New System.Drawing.Font("Times New Roman", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LayoutControlGroup1.AppearanceGroup.Options.UseFont = True
        Me.LayoutControlGroup1.AppearanceItemCaption.Font = New System.Drawing.Font("Arial Narrow", 8.25!, System.Drawing.FontStyle.Bold)
        Me.LayoutControlGroup1.AppearanceItemCaption.Options.UseFont = True
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem11, Me.SplitterItem1, Me.EmptySpaceItem1, Me.LayoutControlGroup2})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1344, 794)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.gcDivisions
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 23)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(283, 751)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.ceShowInactiveDivisions
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(137, 23)
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem11.TextVisible = False
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.Location = New System.Drawing.Point(283, 0)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(8, 774)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(137, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(146, 23)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lcgCurrentShippingInfo, Me.EmptySpaceItem4, Me.lcgPermanentlyChange, Me.lcgPermanentlyCoChange, Me.lcgTemporarilyChange, Me.LayoutControlItem39, Me.LayoutControlItem38, Me.EmptySpaceItem3, Me.LayoutControlGroup3, Me.LayoutControlItem55, Me.LayoutControlItem57})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(291, 0)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(1033, 774)
        '
        'lcgCurrentShippingInfo
        '
        Me.lcgCurrentShippingInfo.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem9, Me.LayoutControlItem19, Me.LayoutControlItem20, Me.LayoutControlItem54})
        Me.lcgCurrentShippingInfo.Location = New System.Drawing.Point(0, 0)
        Me.lcgCurrentShippingInfo.Name = "lcgCurrentShippingInfo"
        Me.lcgCurrentShippingInfo.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.lcgCurrentShippingInfo.Padding = New DevExpress.XtraLayout.Utils.Padding(9, 9, 4, 9)
        Me.lcgCurrentShippingInfo.Size = New System.Drawing.Size(715, 211)
        Me.lcgCurrentShippingInfo.Tag = "Current Shipping Info"
        Me.lcgCurrentShippingInfo.Text = "Current Shipping Info"
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.TextEdit1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 28)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(691, 24)
        Me.LayoutControlItem2.Text = "Name: "
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.TextEdit2
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 52)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(691, 24)
        Me.LayoutControlItem3.Text = "Street: "
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.TextEdit3
        Me.LayoutControlItem4.Location = New System.Drawing.Point(502, 76)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(189, 24)
        Me.LayoutControlItem4.Text = "Zip:"
        Me.LayoutControlItem4.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(18, 15)
        Me.LayoutControlItem4.TextToControlDistance = 5
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.TextEdit4
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 76)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(334, 24)
        Me.LayoutControlItem5.Text = "City: "
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.ComboBoxEdit1
        Me.LayoutControlItem6.Location = New System.Drawing.Point(334, 76)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(168, 24)
        Me.LayoutControlItem6.Text = "St.: "
        Me.LayoutControlItem6.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(18, 15)
        Me.LayoutControlItem6.TextToControlDistance = 5
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.beShippingPhone
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 100)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(502, 24)
        Me.LayoutControlItem7.Text = "Phone: "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.TextEdit6
        Me.LayoutControlItem8.Location = New System.Drawing.Point(502, 100)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(189, 24)
        Me.LayoutControlItem8.Text = "Ext: "
        Me.LayoutControlItem8.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(20, 15)
        Me.LayoutControlItem8.TextToControlDistance = 5
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.beShppingModem
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 124)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(691, 24)
        Me.LayoutControlItem9.Text = "Modem: "
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.cbeCurrentShipDelvMethod
        Me.LayoutControlItem19.Location = New System.Drawing.Point(334, 148)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(357, 24)
        Me.LayoutControlItem19.Text = "Delivery Method: "
        Me.LayoutControlItem19.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.TextEdit16
        Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 148)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(334, 24)
        Me.LayoutControlItem20.Text = "Confidential For: "
        Me.LayoutControlItem20.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem54
        '
        Me.LayoutControlItem54.Control = Me.lcCurrentShipSource
        Me.LayoutControlItem54.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem54.MinSize = New System.Drawing.Size(70, 18)
        Me.LayoutControlItem54.Name = "LayoutControlItem54"
        Me.LayoutControlItem54.Size = New System.Drawing.Size(691, 28)
        Me.LayoutControlItem54.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem54.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem54.TextVisible = False
        '
        'EmptySpaceItem4
        '
        Me.EmptySpaceItem4.AllowHotTrack = False
        Me.EmptySpaceItem4.Location = New System.Drawing.Point(715, 0)
        Me.EmptySpaceItem4.Name = "EmptySpaceItem4"
        Me.EmptySpaceItem4.Size = New System.Drawing.Size(318, 774)
        Me.EmptySpaceItem4.TextSize = New System.Drawing.Size(0, 0)
        '
        'lcgPermanentlyChange
        '
        Me.lcgPermanentlyChange.Expanded = False
        Me.lcgPermanentlyChange.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem10, Me.LayoutControlItem12, Me.LayoutControlItem13, Me.LayoutControlItem22, Me.LayoutControlItem14, Me.LayoutControlItem15, Me.LayoutControlItem16, Me.LayoutControlItem17, Me.LayoutControlItem18, Me.LayoutControlItem33, Me.EmptySpaceItem2, Me.LayoutControlItem34, Me.LayoutControlItem21})
        Me.lcgPermanentlyChange.Location = New System.Drawing.Point(0, 237)
        Me.lcgPermanentlyChange.Name = "lcgPermanentlyChange"
        Me.lcgPermanentlyChange.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.lcgPermanentlyChange.Size = New System.Drawing.Size(715, 29)
        Me.lcgPermanentlyChange.Tag = "Permanently Change"
        Me.lcgPermanentlyChange.Text = "Permanently Change"
        Me.lcgPermanentlyChange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.tePermChangeName
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(476, 24)
        Me.LayoutControlItem10.Text = "Name: "
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.teDivisionStreet
        Me.LayoutControlItem12.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(476, 24)
        Me.LayoutControlItem12.Text = "Street: "
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.TextEdit10
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(237, 24)
        Me.LayoutControlItem13.Text = "City: "
        Me.LayoutControlItem13.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem22
        '
        Me.LayoutControlItem22.Control = Me.cbeStates
        Me.LayoutControlItem22.Location = New System.Drawing.Point(237, 48)
        Me.LayoutControlItem22.Name = "LayoutControlItem22"
        Me.LayoutControlItem22.Size = New System.Drawing.Size(104, 24)
        Me.LayoutControlItem22.Text = "St.: "
        Me.LayoutControlItem22.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem22.TextSize = New System.Drawing.Size(18, 15)
        Me.LayoutControlItem22.TextToControlDistance = 5
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.teDivisionZip
        Me.LayoutControlItem14.Location = New System.Drawing.Point(341, 48)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(135, 24)
        Me.LayoutControlItem14.Text = "Zip: "
        Me.LayoutControlItem14.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(21, 15)
        Me.LayoutControlItem14.TextToControlDistance = 5
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.bePerChangePhone
        Me.LayoutControlItem15.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(341, 24)
        Me.LayoutControlItem15.Text = "Phone: "
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.TextEdit13
        Me.LayoutControlItem16.Location = New System.Drawing.Point(341, 72)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(135, 24)
        Me.LayoutControlItem16.Text = "Ext: "
        Me.LayoutControlItem16.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(20, 15)
        Me.LayoutControlItem16.TextToControlDistance = 5
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.bePerChangeModem
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(476, 24)
        Me.LayoutControlItem17.Text = "Modem: "
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.cbeDivisionDeliveryMethod
        Me.LayoutControlItem18.Location = New System.Drawing.Point(237, 120)
        Me.LayoutControlItem18.Name = "LayoutControlItem18"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(239, 24)
        Me.LayoutControlItem18.Text = "Delivery Method: "
        Me.LayoutControlItem18.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem33
        '
        Me.LayoutControlItem33.Control = Me.btnSavePermChange
        Me.LayoutControlItem33.Location = New System.Drawing.Point(383, 144)
        Me.LayoutControlItem33.Name = "LayoutControlItem33"
        Me.LayoutControlItem33.Size = New System.Drawing.Size(93, 26)
        Me.LayoutControlItem33.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem33.TextVisible = False
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 144)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(318, 26)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem34
        '
        Me.LayoutControlItem34.Control = Me.btnCanelPermanentlyOverride
        Me.LayoutControlItem34.Location = New System.Drawing.Point(318, 144)
        Me.LayoutControlItem34.Name = "LayoutControlItem34"
        Me.LayoutControlItem34.Size = New System.Drawing.Size(65, 26)
        Me.LayoutControlItem34.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem34.TextVisible = False
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.TextEdit18
        Me.LayoutControlItem21.Location = New System.Drawing.Point(0, 120)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(237, 24)
        Me.LayoutControlItem21.Text = "Confidential For: "
        Me.LayoutControlItem21.TextSize = New System.Drawing.Size(81, 15)
        '
        'lcgPermanentlyCoChange
        '
        Me.lcgPermanentlyCoChange.CustomizationFormText = "Permanently Change"
        Me.lcgPermanentlyCoChange.Expanded = False
        Me.lcgPermanentlyCoChange.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem42, Me.LayoutControlItem43, Me.LayoutControlItem44, Me.LayoutControlItem45, Me.LayoutControlItem46, Me.LayoutControlItem47, Me.LayoutControlItem48, Me.LayoutControlItem49, Me.LayoutControlItem50, Me.LayoutControlItem51, Me.EmptySpaceItem5, Me.LayoutControlItem52, Me.LayoutControlItem53})
        Me.lcgPermanentlyCoChange.Location = New System.Drawing.Point(0, 266)
        Me.lcgPermanentlyCoChange.Name = "lcgPermanentlyCoChange"
        Me.lcgPermanentlyCoChange.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.lcgPermanentlyCoChange.Size = New System.Drawing.Size(715, 29)
        Me.lcgPermanentlyCoChange.Tag = "Permanently Change"
        Me.lcgPermanentlyCoChange.Text = "Permanently Change"
        Me.lcgPermanentlyCoChange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'LayoutControlItem42
        '
        Me.LayoutControlItem42.Control = Me.TextEdit11
        Me.LayoutControlItem42.CustomizationFormText = "Name: "
        Me.LayoutControlItem42.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem42.Name = "LayoutControlItem42"
        Me.LayoutControlItem42.Size = New System.Drawing.Size(476, 24)
        Me.LayoutControlItem42.Text = "Name: "
        Me.LayoutControlItem42.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem43
        '
        Me.LayoutControlItem43.Control = Me.teStreetPermCoChange
        Me.LayoutControlItem43.CustomizationFormText = "Street: "
        Me.LayoutControlItem43.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem43.Name = "LayoutControlItem43"
        Me.LayoutControlItem43.Size = New System.Drawing.Size(476, 24)
        Me.LayoutControlItem43.Text = "Street: "
        Me.LayoutControlItem43.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem44
        '
        Me.LayoutControlItem44.Control = Me.TextEdit9
        Me.LayoutControlItem44.CustomizationFormText = "City: "
        Me.LayoutControlItem44.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem44.Name = "LayoutControlItem44"
        Me.LayoutControlItem44.Size = New System.Drawing.Size(237, 24)
        Me.LayoutControlItem44.Text = "City: "
        Me.LayoutControlItem44.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem45
        '
        Me.LayoutControlItem45.Control = Me.cbeStatePermCoChange
        Me.LayoutControlItem45.CustomizationFormText = "St.: "
        Me.LayoutControlItem45.Location = New System.Drawing.Point(237, 48)
        Me.LayoutControlItem45.Name = "LayoutControlItem45"
        Me.LayoutControlItem45.Size = New System.Drawing.Size(129, 24)
        Me.LayoutControlItem45.Text = "St.: "
        Me.LayoutControlItem45.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem45.TextSize = New System.Drawing.Size(18, 15)
        Me.LayoutControlItem45.TextToControlDistance = 5
        '
        'LayoutControlItem46
        '
        Me.LayoutControlItem46.Control = Me.teZipPermCoChange
        Me.LayoutControlItem46.CustomizationFormText = "Zip: "
        Me.LayoutControlItem46.Location = New System.Drawing.Point(366, 48)
        Me.LayoutControlItem46.Name = "LayoutControlItem46"
        Me.LayoutControlItem46.Size = New System.Drawing.Size(110, 24)
        Me.LayoutControlItem46.Text = "Zip: "
        Me.LayoutControlItem46.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem46.TextSize = New System.Drawing.Size(21, 15)
        Me.LayoutControlItem46.TextToControlDistance = 5
        '
        'LayoutControlItem47
        '
        Me.LayoutControlItem47.Control = Me.bePhonePermCoChange
        Me.LayoutControlItem47.CustomizationFormText = "Phone: "
        Me.LayoutControlItem47.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem47.Name = "LayoutControlItem47"
        Me.LayoutControlItem47.Size = New System.Drawing.Size(366, 24)
        Me.LayoutControlItem47.Text = "Phone: "
        Me.LayoutControlItem47.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem48
        '
        Me.LayoutControlItem48.Control = Me.TextEdit12
        Me.LayoutControlItem48.CustomizationFormText = "Ext: "
        Me.LayoutControlItem48.Location = New System.Drawing.Point(366, 72)
        Me.LayoutControlItem48.Name = "LayoutControlItem48"
        Me.LayoutControlItem48.Size = New System.Drawing.Size(110, 24)
        Me.LayoutControlItem48.Text = "Ext: "
        Me.LayoutControlItem48.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem48.TextSize = New System.Drawing.Size(20, 15)
        Me.LayoutControlItem48.TextToControlDistance = 5
        '
        'LayoutControlItem49
        '
        Me.LayoutControlItem49.Control = Me.beModemPermCoChange
        Me.LayoutControlItem49.CustomizationFormText = "Modem: "
        Me.LayoutControlItem49.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem49.Name = "LayoutControlItem49"
        Me.LayoutControlItem49.Size = New System.Drawing.Size(476, 24)
        Me.LayoutControlItem49.Text = "Modem: "
        Me.LayoutControlItem49.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem50
        '
        Me.LayoutControlItem50.Control = Me.cbeDelvMethodPermCoChange
        Me.LayoutControlItem50.CustomizationFormText = "Delivery Method: "
        Me.LayoutControlItem50.Location = New System.Drawing.Point(237, 120)
        Me.LayoutControlItem50.Name = "LayoutControlItem50"
        Me.LayoutControlItem50.Size = New System.Drawing.Size(239, 24)
        Me.LayoutControlItem50.Text = "Delivery Method: "
        Me.LayoutControlItem50.TextSize = New System.Drawing.Size(81, 15)
        '
        'LayoutControlItem51
        '
        Me.LayoutControlItem51.Control = Me.btnSavePermCoChange
        Me.LayoutControlItem51.CustomizationFormText = "LayoutControlItem33"
        Me.LayoutControlItem51.Location = New System.Drawing.Point(383, 144)
        Me.LayoutControlItem51.Name = "LayoutControlItem51"
        Me.LayoutControlItem51.Size = New System.Drawing.Size(93, 26)
        Me.LayoutControlItem51.Text = "LayoutControlItem33"
        Me.LayoutControlItem51.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem51.TextVisible = False
        '
        'EmptySpaceItem5
        '
        Me.EmptySpaceItem5.AllowHotTrack = False
        Me.EmptySpaceItem5.CustomizationFormText = "EmptySpaceItem2"
        Me.EmptySpaceItem5.Location = New System.Drawing.Point(0, 144)
        Me.EmptySpaceItem5.Name = "EmptySpaceItem5"
        Me.EmptySpaceItem5.Size = New System.Drawing.Size(313, 26)
        Me.EmptySpaceItem5.Text = "EmptySpaceItem2"
        Me.EmptySpaceItem5.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem52
        '
        Me.LayoutControlItem52.Control = Me.btnCancelPermCoChange
        Me.LayoutControlItem52.CustomizationFormText = "LayoutControlItem34"
        Me.LayoutControlItem52.Location = New System.Drawing.Point(313, 144)
        Me.LayoutControlItem52.Name = "LayoutControlItem52"
        Me.LayoutControlItem52.Size = New System.Drawing.Size(70, 26)
        Me.LayoutControlItem52.Text = "LayoutControlItem34"
        Me.LayoutControlItem52.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem52.TextVisible = False
        '
        'LayoutControlItem53
        '
        Me.LayoutControlItem53.Control = Me.TextEdit7
        Me.LayoutControlItem53.CustomizationFormText = "Confidential For: "
        Me.LayoutControlItem53.Location = New System.Drawing.Point(0, 120)
        Me.LayoutControlItem53.Name = "LayoutControlItem53"
        Me.LayoutControlItem53.Size = New System.Drawing.Size(237, 24)
        Me.LayoutControlItem53.Text = "Confidential For: "
        Me.LayoutControlItem53.TextSize = New System.Drawing.Size(81, 15)
        '
        'lcgTemporarilyChange
        '
        Me.lcgTemporarilyChange.CustomizationFormText = "Temporarily Change"
        Me.lcgTemporarilyChange.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem35, Me.LayoutControlItem40, Me.LayoutControlItem41, Me.EmptySpaceItem6, Me.lciOverrideExpireDate, Me.lciOverridePayrollNum, Me.LayoutControlItem37, Me.lcgTemporarilyChangeAddressFields, Me.LayoutControlItem56, Me.lciQeYeDate, Me.sliQeYeNote})
        Me.lcgTemporarilyChange.Location = New System.Drawing.Point(0, 295)
        Me.lcgTemporarilyChange.Name = "lcgTemporarilyChange"
        Me.lcgTemporarilyChange.Size = New System.Drawing.Size(715, 312)
        Me.lcgTemporarilyChange.Tag = "Temporarily Change"
        Me.lcgTemporarilyChange.Text = "Temporarily Change"
        Me.lcgTemporarilyChange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'LayoutControlItem35
        '
        Me.LayoutControlItem35.Control = Me.rgExpirationMode
        Me.LayoutControlItem35.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem35.MaxSize = New System.Drawing.Size(0, 29)
        Me.LayoutControlItem35.MinSize = New System.Drawing.Size(138, 29)
        Me.LayoutControlItem35.Name = "LayoutControlItem35"
        Me.LayoutControlItem35.Size = New System.Drawing.Size(691, 29)
        Me.LayoutControlItem35.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem35.Text = "Valid For: "
        Me.LayoutControlItem35.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem35.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem35.TextToControlDistance = 5
        '
        'LayoutControlItem40
        '
        Me.LayoutControlItem40.Control = Me.btnSaveTempChange
        Me.LayoutControlItem40.Location = New System.Drawing.Point(566, 242)
        Me.LayoutControlItem40.Name = "LayoutControlItem40"
        Me.LayoutControlItem40.Size = New System.Drawing.Size(125, 26)
        Me.LayoutControlItem40.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem40.TextVisible = False
        '
        'LayoutControlItem41
        '
        Me.LayoutControlItem41.Control = Me.btnCancelTemporarilyOverride
        Me.LayoutControlItem41.Location = New System.Drawing.Point(468, 242)
        Me.LayoutControlItem41.Name = "LayoutControlItem41"
        Me.LayoutControlItem41.Size = New System.Drawing.Size(98, 26)
        Me.LayoutControlItem41.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem41.TextVisible = False
        '
        'EmptySpaceItem6
        '
        Me.EmptySpaceItem6.AllowHotTrack = False
        Me.EmptySpaceItem6.Location = New System.Drawing.Point(0, 242)
        Me.EmptySpaceItem6.Name = "EmptySpaceItem6"
        Me.EmptySpaceItem6.Size = New System.Drawing.Size(468, 26)
        Me.EmptySpaceItem6.TextSize = New System.Drawing.Size(0, 0)
        '
        'lciOverrideExpireDate
        '
        Me.lciOverrideExpireDate.Control = Me.deOverrideExpirationDate
        Me.lciOverrideExpireDate.Location = New System.Drawing.Point(0, 29)
        Me.lciOverrideExpireDate.Name = "lciOverrideExpireDate"
        Me.lciOverrideExpireDate.Size = New System.Drawing.Size(159, 24)
        Me.lciOverrideExpireDate.Text = "Expire date: "
        Me.lciOverrideExpireDate.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.lciOverrideExpireDate.TextSize = New System.Drawing.Size(100, 15)
        Me.lciOverrideExpireDate.TextToControlDistance = 5
        Me.lciOverrideExpireDate.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'lciOverridePayrollNum
        '
        Me.lciOverridePayrollNum.Control = Me.seOverridePayrollNum
        Me.lciOverridePayrollNum.Location = New System.Drawing.Point(159, 29)
        Me.lciOverridePayrollNum.Name = "lciOverridePayrollNum"
        Me.lciOverridePayrollNum.Size = New System.Drawing.Size(159, 24)
        Me.lciOverridePayrollNum.Text = "Pr#: "
        Me.lciOverridePayrollNum.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.lciOverridePayrollNum.TextSize = New System.Drawing.Size(100, 15)
        Me.lciOverridePayrollNum.TextToControlDistance = 5
        '
        'LayoutControlItem37
        '
        Me.LayoutControlItem37.Control = Me.lcTempChangePrStatus
        Me.LayoutControlItem37.Location = New System.Drawing.Point(621, 29)
        Me.LayoutControlItem37.Name = "LayoutControlItem37"
        Me.LayoutControlItem37.Size = New System.Drawing.Size(70, 24)
        Me.LayoutControlItem37.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem37.TextVisible = False
        '
        'lcgTemporarilyChangeAddressFields
        '
        Me.lcgTemporarilyChangeAddressFields.CustomizationFormText = "Address fields"
        Me.lcgTemporarilyChangeAddressFields.GroupBordersVisible = False
        Me.lcgTemporarilyChangeAddressFields.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem23, Me.LayoutControlItem24, Me.LayoutControlItem25, Me.LayoutControlItem26, Me.LayoutControlItem27, Me.LayoutControlItem28, Me.LayoutControlItem29, Me.LayoutControlItem30, Me.LayoutControlItem32, Me.LayoutControlItem31})
        Me.lcgTemporarilyChangeAddressFields.Location = New System.Drawing.Point(0, 53)
        Me.lcgTemporarilyChangeAddressFields.Name = "lcgTemporarilyChangeAddressFields"
        Me.lcgTemporarilyChangeAddressFields.Size = New System.Drawing.Size(691, 150)
        '
        'LayoutControlItem23
        '
        Me.LayoutControlItem23.Control = Me.TextEdit20
        Me.LayoutControlItem23.CustomizationFormText = "LayoutControlItem10"
        Me.LayoutControlItem23.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem23.Name = "LayoutControlItem23"
        Me.LayoutControlItem23.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 8, 2)
        Me.LayoutControlItem23.Size = New System.Drawing.Size(691, 30)
        Me.LayoutControlItem23.Text = "Name: "
        Me.LayoutControlItem23.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem23.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem23.TextToControlDistance = 5
        '
        'LayoutControlItem24
        '
        Me.LayoutControlItem24.Control = Me.teOverrideStreet
        Me.LayoutControlItem24.CustomizationFormText = "LayoutControlItem12"
        Me.LayoutControlItem24.Location = New System.Drawing.Point(0, 30)
        Me.LayoutControlItem24.Name = "LayoutControlItem24"
        Me.LayoutControlItem24.Size = New System.Drawing.Size(691, 24)
        Me.LayoutControlItem24.Text = "Street: "
        Me.LayoutControlItem24.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem24.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem24.TextToControlDistance = 5
        '
        'LayoutControlItem25
        '
        Me.LayoutControlItem25.Control = Me.TextEdit24
        Me.LayoutControlItem25.CustomizationFormText = "LayoutControlItem13"
        Me.LayoutControlItem25.Location = New System.Drawing.Point(0, 54)
        Me.LayoutControlItem25.Name = "LayoutControlItem25"
        Me.LayoutControlItem25.Size = New System.Drawing.Size(345, 24)
        Me.LayoutControlItem25.Text = "City: "
        Me.LayoutControlItem25.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem25.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem25.TextToControlDistance = 5
        '
        'LayoutControlItem26
        '
        Me.LayoutControlItem26.Control = Me.cbeStatesOverrideShipAddress
        Me.LayoutControlItem26.CustomizationFormText = "LayoutControlItem22"
        Me.LayoutControlItem26.Location = New System.Drawing.Point(345, 54)
        Me.LayoutControlItem26.Name = "LayoutControlItem26"
        Me.LayoutControlItem26.Size = New System.Drawing.Size(173, 24)
        Me.LayoutControlItem26.Text = "St:. "
        Me.LayoutControlItem26.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem26.TextSize = New System.Drawing.Size(18, 15)
        Me.LayoutControlItem26.TextToControlDistance = 5
        '
        'LayoutControlItem27
        '
        Me.LayoutControlItem27.Control = Me.teZipOverride
        Me.LayoutControlItem27.CustomizationFormText = "LayoutControlItem14"
        Me.LayoutControlItem27.Location = New System.Drawing.Point(518, 54)
        Me.LayoutControlItem27.Name = "LayoutControlItem27"
        Me.LayoutControlItem27.Size = New System.Drawing.Size(173, 24)
        Me.LayoutControlItem27.Text = "Zip: "
        Me.LayoutControlItem27.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem27.TextSize = New System.Drawing.Size(21, 15)
        Me.LayoutControlItem27.TextToControlDistance = 5
        '
        'LayoutControlItem28
        '
        Me.LayoutControlItem28.Control = Me.beTempChangePhone
        Me.LayoutControlItem28.CustomizationFormText = "LayoutControlItem15"
        Me.LayoutControlItem28.Location = New System.Drawing.Point(0, 78)
        Me.LayoutControlItem28.Name = "LayoutControlItem28"
        Me.LayoutControlItem28.Size = New System.Drawing.Size(571, 24)
        Me.LayoutControlItem28.Text = "Phone: "
        Me.LayoutControlItem28.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem28.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem28.TextToControlDistance = 5
        '
        'LayoutControlItem29
        '
        Me.LayoutControlItem29.Control = Me.TextEdit23
        Me.LayoutControlItem29.CustomizationFormText = "LayoutControlItem16"
        Me.LayoutControlItem29.Location = New System.Drawing.Point(571, 78)
        Me.LayoutControlItem29.Name = "LayoutControlItem29"
        Me.LayoutControlItem29.Size = New System.Drawing.Size(120, 24)
        Me.LayoutControlItem29.Text = "Ext: "
        Me.LayoutControlItem29.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem29.TextSize = New System.Drawing.Size(20, 15)
        Me.LayoutControlItem29.TextToControlDistance = 5
        '
        'LayoutControlItem30
        '
        Me.LayoutControlItem30.Control = Me.beTempChangeModem
        Me.LayoutControlItem30.CustomizationFormText = "LayoutControlItem17"
        Me.LayoutControlItem30.Location = New System.Drawing.Point(0, 102)
        Me.LayoutControlItem30.Name = "LayoutControlItem30"
        Me.LayoutControlItem30.Size = New System.Drawing.Size(691, 24)
        Me.LayoutControlItem30.Text = "Modem: "
        Me.LayoutControlItem30.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem30.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem30.TextToControlDistance = 5
        '
        'LayoutControlItem32
        '
        Me.LayoutControlItem32.Control = Me.TextEdit26
        Me.LayoutControlItem32.CustomizationFormText = "LayoutControlItem21"
        Me.LayoutControlItem32.Location = New System.Drawing.Point(0, 126)
        Me.LayoutControlItem32.Name = "LayoutControlItem32"
        Me.LayoutControlItem32.Size = New System.Drawing.Size(345, 24)
        Me.LayoutControlItem32.Text = "Confidential For: "
        Me.LayoutControlItem32.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem32.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem32.TextToControlDistance = 5
        '
        'LayoutControlItem31
        '
        Me.LayoutControlItem31.Control = Me.cbeDeliveryMethodOverride
        Me.LayoutControlItem31.CustomizationFormText = "LayoutControlItem18"
        Me.LayoutControlItem31.Location = New System.Drawing.Point(345, 126)
        Me.LayoutControlItem31.Name = "LayoutControlItem31"
        Me.LayoutControlItem31.Size = New System.Drawing.Size(346, 24)
        Me.LayoutControlItem31.Text = "Delivery Method:"
        Me.LayoutControlItem31.TextSize = New System.Drawing.Size(77, 15)
        '
        'LayoutControlItem56
        '
        Me.LayoutControlItem56.Control = Me.meNotes
        Me.LayoutControlItem56.Location = New System.Drawing.Point(0, 203)
        Me.LayoutControlItem56.Name = "LayoutControlItem56"
        Me.LayoutControlItem56.Size = New System.Drawing.Size(691, 39)
        Me.LayoutControlItem56.Text = "Notes: "
        Me.LayoutControlItem56.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.LayoutControlItem56.TextSize = New System.Drawing.Size(100, 15)
        Me.LayoutControlItem56.TextToControlDistance = 5
        '
        'lciQeYeDate
        '
        Me.lciQeYeDate.Control = Me.deQeYeDate
        Me.lciQeYeDate.ControlAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.lciQeYeDate.CustomizationFormText = "QE / YE date:"
        Me.lciQeYeDate.Location = New System.Drawing.Point(318, 29)
        Me.lciQeYeDate.Name = "lciQeYeDate"
        Me.lciQeYeDate.Size = New System.Drawing.Size(199, 24)
        Me.lciQeYeDate.Text = "QE / YE date:"
        Me.lciQeYeDate.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.lciQeYeDate.TextSize = New System.Drawing.Size(100, 15)
        Me.lciQeYeDate.TextToControlDistance = 5
        Me.lciQeYeDate.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'sliQeYeNote
        '
        Me.sliQeYeNote.AllowHotTrack = False
        Me.sliQeYeNote.Location = New System.Drawing.Point(517, 29)
        Me.sliQeYeNote.Name = "sliQeYeNote"
        Me.sliQeYeNote.Size = New System.Drawing.Size(104, 24)
        Me.sliQeYeNote.Text = "Leave empty to make it permanent"
        Me.sliQeYeNote.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.sliQeYeNote.TextSize = New System.Drawing.Size(100, 15)
        Me.sliQeYeNote.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'LayoutControlItem39
        '
        Me.LayoutControlItem39.Control = Me.btnTemporarilyChange
        Me.LayoutControlItem39.Location = New System.Drawing.Point(265, 211)
        Me.LayoutControlItem39.MaxSize = New System.Drawing.Size(109, 26)
        Me.LayoutControlItem39.MinSize = New System.Drawing.Size(109, 26)
        Me.LayoutControlItem39.Name = "LayoutControlItem39"
        Me.LayoutControlItem39.Size = New System.Drawing.Size(109, 26)
        Me.LayoutControlItem39.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem39.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem39.TextVisible = False
        '
        'LayoutControlItem38
        '
        Me.LayoutControlItem38.Control = Me.btnPermanentlyChange
        Me.LayoutControlItem38.Location = New System.Drawing.Point(602, 211)
        Me.LayoutControlItem38.MaxSize = New System.Drawing.Size(113, 26)
        Me.LayoutControlItem38.MinSize = New System.Drawing.Size(113, 26)
        Me.LayoutControlItem38.Name = "LayoutControlItem38"
        Me.LayoutControlItem38.Size = New System.Drawing.Size(113, 26)
        Me.LayoutControlItem38.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem38.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem38.TextVisible = False
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(0, 211)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(107, 26)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.AppearanceGroup.BackColor = System.Drawing.Color.Blue
        Me.LayoutControlGroup3.AppearanceGroup.Font = New System.Drawing.Font("Rockwell", 9.75!, CType((System.Drawing.FontStyle.Bold Or System.Drawing.FontStyle.Underline), System.Drawing.FontStyle), System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LayoutControlGroup3.AppearanceGroup.Options.UseBackColor = True
        Me.LayoutControlGroup3.AppearanceGroup.Options.UseFont = True
        Me.LayoutControlGroup3.AppearanceGroup.Options.UseTextOptions = True
        Me.LayoutControlGroup3.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlGroup3.CaptionImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.historyitem_16x16
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem36})
        Me.LayoutControlGroup3.Location = New System.Drawing.Point(0, 607)
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(715, 167)
        Me.LayoutControlGroup3.Text = "Address Change History"
        '
        'LayoutControlItem36
        '
        Me.LayoutControlItem36.Control = Me.gcOverrides
        Me.LayoutControlItem36.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem36.Name = "LayoutControlItem36"
        Me.LayoutControlItem36.Size = New System.Drawing.Size(709, 137)
        Me.LayoutControlItem36.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem36.TextVisible = False
        '
        'LayoutControlItem55
        '
        Me.LayoutControlItem55.Control = Me.btnTemporaryNote
        Me.LayoutControlItem55.Location = New System.Drawing.Point(107, 211)
        Me.LayoutControlItem55.Name = "LayoutControlItem55"
        Me.LayoutControlItem55.Size = New System.Drawing.Size(158, 26)
        Me.LayoutControlItem55.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem55.TextVisible = False
        '
        'LayoutControlItem57
        '
        Me.LayoutControlItem57.Control = Me.btnCopyTemporary
        Me.LayoutControlItem57.Location = New System.Drawing.Point(374, 211)
        Me.LayoutControlItem57.Name = "LayoutControlItem57"
        Me.LayoutControlItem57.Size = New System.Drawing.Size(228, 26)
        Me.LayoutControlItem57.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem57.TextVisible = False
        '
        'DxValidationProviderPermChange
        '
        Me.DxValidationProviderPermChange.ValidationMode = DevExpress.XtraEditors.DXErrorProvider.ValidationMode.Manual
        '
        'DxValidationProviderTempChange
        '
        Me.DxValidationProviderTempChange.ValidationMode = DevExpress.XtraEditors.DXErrorProvider.ValidationMode.Manual
        '
        'DxValidationProvider1
        '
        Me.DxValidationProvider1.ValidationMode = DevExpress.XtraEditors.DXErrorProvider.ValidationMode.Manual
        '
        'DxValidationProvider2
        '
        Me.DxValidationProvider2.ValidationMode = DevExpress.XtraEditors.DXErrorProvider.ValidationMode.Manual
        '
        'RepositoryItemMemoEdit1
        '
        Me.RepositoryItemMemoEdit1.Name = "RepositoryItemMemoEdit1"
        '
        'ucShippingAddress
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "ucShippingAddress"
        Me.Size = New System.Drawing.Size(1344, 794)
        CType(Me.layoutViewField_LayoutViewColumn1_6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.meNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSourceShipAddressOverride, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcOverrides, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colId, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colCONUM1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colDDIVNUM1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colPRNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colExpirationDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_NAME, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_STREET, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_CITY, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_STATE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_ZIP, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_PHONE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_FAX, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_EXTENSION, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colSHIP_MODEM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colDPR_CONTACT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_colDDELVDESC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1_1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1_2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1_3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1_4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1_5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.layoutViewField_LayoutViewColumn1_7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutViewCard1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.seOverridePayrollNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deOverrideExpirationDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deOverrideExpirationDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.rgExpirationMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStates.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSourceDivision, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit18.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit13.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teDivisionZip.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit10.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teDivisionStreet.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tePermChangeName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit16.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceShowInactiveDivisions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSourceCurrentShippingInfo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcDivisions, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvDivisions, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit20.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teOverrideStreet.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit24.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStatesOverrideShipAddress.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teZipOverride.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit23.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit26.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeDivisionDeliveryMethod.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeDeliveryMethodOverride.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beShippingPhone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beShppingModem.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bePerChangePhone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bePerChangeModem.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beTempChangePhone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beTempChangeModem.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeCurrentShipDelvMethod.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit11.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSourcePermCoChange, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teStreetPermCoChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit9.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStatePermCoChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teZipPermCoChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bePhonePermCoChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit12.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beModemPermCoChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeDelvMethodPermCoChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit7.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deQeYeDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deQeYeDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgCurrentShippingInfo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem54, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgPermanentlyChange, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem33, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem34, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgPermanentlyCoChange, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem42, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem43, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem44, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem45, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem46, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem47, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem48, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem49, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem50, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem51, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem52, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem53, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgTemporarilyChange, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem35, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem40, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem41, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciOverrideExpireDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciOverridePayrollNum, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem37, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgTemporarilyChangeAddressFields, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem28, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem29, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem30, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem32, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem31, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem56, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciQeYeDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.sliQeYeNote, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem39, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem38, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem36, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem55, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem57, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProviderPermChange, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProviderTempChange, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProvider2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents ceShowInactiveDivisions As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEdit6 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents gcDivisions As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvDivisions As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lcgCurrentShippingInfo As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDDIVNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDDIVNAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDivShip As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoShip As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipCo As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipAddress As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipCity As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipState As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipZip As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipContact As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipPhone As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipExtension As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipModem As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipMethod As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipCharge As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEEcnt As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents BindingSourceCurrentShippingInfo As BindingSource
    Friend WithEvents TextEdit16 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BindingSourceShipAddressOverride As BindingSource
    Friend WithEvents cbeStates As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TextEdit18 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit13 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teDivisionZip As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit10 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teDivisionStreet As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tePermChangeName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lcgPermanentlyChange As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem22 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TextEdit20 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teOverrideStreet As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit24 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cbeStatesOverrideShipAddress As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents teZipOverride As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit23 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit26 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lcgTemporarilyChange As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem23 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem24 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem25 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem26 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem27 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem28 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem29 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem30 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem31 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem32 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnCanelPermanentlyOverride As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSavePermChange As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem33 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem34 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents rgExpirationMode As DevExpress.XtraEditors.RadioGroup
    Friend WithEvents LayoutControlItem35 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents seOverridePayrollNum As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents deOverrideExpirationDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents lciOverrideExpireDate As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lciOverridePayrollNum As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnTemporarilyChange As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnPermanentlyChange As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem38 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem39 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BindingSourceDivision As BindingSource
    Friend WithEvents cbeDivisionDeliveryMethod As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnCancelTemporarilyOverride As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSaveTempChange As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem40 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem41 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem6 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents cbeDeliveryMethodOverride As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents gcOverrides As DevExpress.XtraGrid.GridControl
    Friend WithEvents LayoutControlItem36 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutView1 As DevExpress.XtraGrid.Views.Layout.LayoutView
    Friend WithEvents colId As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colCONUM1 As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colDDIVNUM1 As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colPRNUM As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colExpirationDate As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_NAME As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_STREET As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_CITY As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_STATE As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_ZIP As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_PHONE As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_FAX As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_EXTENSION As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colSHIP_MODEM As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colDPR_CONTACT As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colDDELVDESC As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents beShippingPhone As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents beShppingModem As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents bePerChangePhone As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents bePerChangeModem As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents beTempChangePhone As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents beTempChangeModem As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents DxValidationProviderPermChange As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
    Friend WithEvents DxValidationProviderTempChange As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
    Friend WithEvents lcTempChangePrStatus As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LayoutControlItem37 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents cbeCurrentShipDelvMethod As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DxValidationProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
    Friend WithEvents TextEdit11 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DxValidationProvider2 As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
    Friend WithEvents teStreetPermCoChange As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit9 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cbeStatePermCoChange As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents teZipPermCoChange As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bePhonePermCoChange As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents TextEdit12 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents beModemPermCoChange As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents cbeDelvMethodPermCoChange As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btnSavePermCoChange As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancelPermCoChange As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEdit7 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lcgPermanentlyCoChange As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem42 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem43 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem44 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem45 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem46 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem47 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem48 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem49 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem50 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem51 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem5 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem52 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem53 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BindingSourcePermCoChange As BindingSource
    Friend WithEvents lcCurrentShipSource As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LayoutControlItem54 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colDivAddress As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EmptySpaceItem4 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents btnTemporaryNote As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem55 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents meNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents lcgTemporarilyChangeAddressFields As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem56 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colNotes As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colPermanent As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colAddDateTime As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colAddUser As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colAddedAfterPayrollPrint As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents AddedAfterPayrollPrintMsg As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents btnCopyTemporary As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem57 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents deQeYeDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents lciQeYeDate As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents sliQeYeNote As DevExpress.XtraLayout.SimpleLabelItem
    Friend WithEvents colQtrEndDate As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents colQtrEndDateDisp As DevExpress.XtraGrid.Columns.LayoutViewColumn
    Friend WithEvents layoutViewField_LayoutViewColumn1_6 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colId As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colCONUM1 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colDDIVNUM1 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colPRNUM As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colExpirationDate As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_NAME As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_STREET As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_CITY As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_STATE As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_ZIP As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_PHONE As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_FAX As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_EXTENSION As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colSHIP_MODEM As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colDPR_CONTACT As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_colDDELVDESC As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1_1 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1_2 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1_3 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1_4 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1_5 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents layoutViewField_LayoutViewColumn1_7 As DevExpress.XtraGrid.Views.Layout.LayoutViewField
    Friend WithEvents LayoutViewCard1 As DevExpress.XtraGrid.Views.Layout.LayoutViewCard
    Friend WithEvents RepositoryItemMemoEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit
End Class
