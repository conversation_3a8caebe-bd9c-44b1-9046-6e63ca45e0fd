﻿Imports QueueBuilder

Public Class SubmitPayroll

    Public Async Function SubmitAsync(batchInProcessId As Integer, conum As Decimal, prnum As Decimal, requestedBy As String) As Task(Of Tuple(Of Boolean, String))
        Try
            Dim DB = New dbEPDataDataContext(GetConnectionString)
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = batchInProcessId).Single
            If Rec.CompleteBy IsNot Nothing AndAlso Rec.CompleteBy <> UserName Then
                Logger.Error("Payroll taken by {CompleteBy} on {CompleteDate}", Rec.CompleteBy, Rec.CompleteDate)
                Return Tuple.Create(False, $"Payroll taken by {Rec.CompleteBy} on { Rec.CompleteDate}")
            End If

            Dim payroll = (From A In DB.PAYROLLs Where A.CONUM = conum AndAlso A.PRNUM = prnum).Single

            Logger.Debug("Processing Payroll for Co#: {CoNum} Pr#: {PrNum} {PrStatus}", <PERSON><PERSON><PERSON>, Rec.PrNum, payroll.PAYROLL_STATUS)

            If payroll.PAYROLL_STATUS = "Submitted" Then
                If Not IsLastPayroll(conum, prnum, DB) Then
                    Logger.Debug("Changing PrStatus from: {Old_PrStatus} to: {New_PrStatus} Co#: {CoNum} Pr#: {PrNum}", payroll.PAYROLL_STATUS, "Finished Checks", payroll.CONUM, payroll.PRNUM)
                    payroll.PAYROLL_STATUS = "Finished Checks"
                    DB.SaveChanges()
                    Return Tuple.Create(False, "")
                Else
                    Logger.Debug("Changing PrStatus from: {Old_PrStatus} to: {New_PrStatus} Co#: {CoNum} Pr#: {PrNum}", payroll.PAYROLL_STATUS, "Entering Checks", payroll.CONUM, payroll.PRNUM)
                    payroll.PAYROLL_STATUS = "Entering Checks"
                End If
                DB.SaveChanges()
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, payroll)

            ElseIf payroll.PAYROLL_STATUS <> "Entering Checks" Then
                Logger.Error("Payroll status is: " & payroll.PAYROLL_STATUS)
                Return Tuple.Create(False, $"Payroll status is: {payroll.PAYROLL_STATUS}")
            End If

            Logger.Debug("Starting CheckPayrollStatus")
            Dim API = EP_API()
            API.CompanyID = conum
            API.CheckPayrollStatus()
            Logger.Debug("Finished CheckPayrollStatus")

            Dim obj As New PayrollUtilityImport(conum)
            obj.ProcessImport(prnum)
            Logger.Debug("Finished ProcessImport")

            If Not API.CalculateTotals() Then
                Logger.Error("Error occurred. " & vbCrLf & API.PayrollException.Message)
                Return Tuple.Create(False, $"Error occurred. Api Message: {API.PayrollException.Message}")
            End If
            Logger.Debug("Finished CalculateTotals. PPxPayrollStep: {PPxPayrollStep}", API.PPxPayrollStep)
            If API.PPxPayrollStep = PPxPayrollSteps.Edit Then
                API.PPxPayrollStep = PPxPayrollSteps.Complete
            End If
            If API.SubmitPayroll(2) Then
                Logger.Debug("Finished.")
                Try
                    UpdatePayrollSubmitType(DB, Rec, payroll)
                Catch ex As Exception
                    Logger.Error(ex, "Error in payroll submit type")
                    Return Tuple.Create(False, "Error updating payroll submit type")
                End Try

                Try
                    If GetUdfValue_AsLong("Zendesk_Payroll_Approval_Solved_MacroId").HasValue AndAlso Rec.EmailRegisterZendeskTicketId.HasValue Then
                        Dim queueBuilder = QueuePayrollProcessor.modPayrollUtilities.GetQueueBuilder(requestedBy)
                        queueBuilder.WithConum(Rec.CoNum).WithDescription("Close PR Approval Ticket")
                        'add a custom zendesk userid
                        'add a private note on the ticket.
                        Dim queueEmail = New Brands.DAL.QueueEmail With {
                                                                .ApplyMacroToTicket = GetUdfValue_AsLong("Zendesk_Payroll_Approval_Solved_MacroId"),
                                                                .ZendeskTicketId = Rec.EmailRegisterZendeskTicketId}
                        If requestedBy = "Queue" Then
                            queueEmail.OverrideRequestedBy = GetUdfValue("AutoFixPayroll OverrideRequestedBy")
                        End If
                        queueEmail.EmailBody = $"Payroll submitted by: {requestedBy}"
                        queueEmail.EmailBodyType = "TEXT"
                        queueEmail.ZendeskPrivateComment = True
                        queueBuilder.EnqueueZendeskUpdateTicket(queueEmail)
                        Await queueBuilder.SaveAndPublishAsync()
                    End If
                Catch ex As Exception
                    Logger.Error(ex, "Error Close PR Approval Ticket")
                End Try

                'close zendesk ticket
                Return Tuple.Create(True, "")
            Else
                Logger.Error("Error occurred. Payroll not submitted")
                Return Tuple.Create(False, $"Error occurred. Payroll not submitted: {payroll.PAYROLL_STATUS}")
                Return Tuple.Create(False, $"Error occurred - Payroll not submitted. Api Message: {API.PayrollException.Message}")
            End If
            Logger.Debug("Finished SubmitPayroll")
        Catch ex As Exception
            Logger.Error(ex, "Error while submitting payroll")
            Throw
        End Try
    End Function

    Private Sub UpdatePayrollSubmitType(DB As dbEPDataDataContext, Rec As pr_batch_in_process, payroll As PAYROLL)
        If Rec.IgnoreIssues = 100 Then
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, payroll)
            If payroll.PAYROLL_STATUS = "Submitted" AndAlso payroll.payroll_ext_ts.submit_type <> 3 Then
                Logger.Warning("Updating payroll submit type to 3. Co#: {Conum} Pr#: {PrNum} pr_batch_in_processesId: {pr_batch_in_processesZId}", payroll.CONUM, payroll.PRNUM, Rec.ID)
                payroll.payroll_ext_ts.submit_type = 3
                DB.SaveChanges()
            End If
        End If
    End Sub

    Private Function IsLastPayroll(coNum As Decimal, prNum As Decimal, db As dbEPDataDataContext) As Boolean
        Dim maxPayrollNum = db.PAYROLLs.Where(Function(p) p.CONUM = coNum).Select(Function(p) p.PRNUM).Max()
        Logger.Debug("Current Payroll is {PrNum} Last Payroll for Co#: {CoNum} Is Pr#: {PrNum}", prNum, coNum, maxPayrollNum)
        Return maxPayrollNum = prNum
    End Function
End Class
