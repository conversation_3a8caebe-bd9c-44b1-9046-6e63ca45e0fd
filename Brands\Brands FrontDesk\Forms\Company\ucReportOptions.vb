﻿Imports System.ComponentModel
Imports Brands_FrontDesk
Imports DevExpress.XtraEditors

Public Class ucReportOptions

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property _report As ReportEmailTeplate
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property _conum As Decimal
    Public Sub SetReportId(report As ReportEmailTeplate, conum As Decimal)
        _report = report
        _conum = conum
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            If Not _report?.SRPT_ID.HasValue Then
                prc_ReportOptionsResultBindingSource.Clear()
            Else
                Using db As New dbEPDataDataContext(GetConnectionString)
                    prc_ReportOptionsResultBindingSource.DataSource = db.prc_ReportOptions(_conum, _report?.SRPT_ID, Nothing).ToList()
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading report options", ex)
        End Try
    End Sub

    Private Sub gvReportOptions_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvReportOptions.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As prc_ReportOptionsResult = gvReportOptions.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add Code", Sub() AddOptionToReport(_report, _conum, row), My.Resources.addtext_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Remove Code", Sub() RemoveOptionToReport(_report, _conum, row), My.Resources.remove_16x16))
        End If
    End Sub

    Private Sub RemoveOptionToReport(report As ReportEmailTeplate, conum As Decimal, row As prc_ReportOptionsResult)
        ModifyUdf(row.rptOption, True)
    End Sub

    Private Sub AddOptionToReport(report As ReportEmailTeplate, conum As Decimal, row As prc_ReportOptionsResult)
        ModifyUdf(row.rptOption, False)
    End Sub

    Private Sub ModifyUdf(code As String, remove As Boolean)
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim coUserDef = db.COUSERDEFs.SingleOrDefault(Function(c) c.conum = _conum)
                If coUserDef Is Nothing Then
                    coUserDef = New COUSERDEF With {.conum = _conum}
                    db.COUSERDEFs.InsertOnSubmit(coUserDef)
                End If
                If remove Then
                    Dim origLength = coUserDef.udf21_data.Length
                    coUserDef.udf21_data = coUserDef.udf21_data.Replace(code, "")
                    If coUserDef.udf21_data.Length <> (origLength - code.Length) Then
                        Throw New Exception($"Error while updating UDF21_DATA for Co#: {_conum}. (Data lost might occur)")
                    End If
                ElseIf coUserDef.udf21_data.Contains(code) Then
                    XtraMessageBox.Show("Code already added.")
                Else
                    coUserDef.udf21_data &= code
                End If
                db.SubmitChanges()
                LoadData()
            End Using
        Catch ex As Exception

        End Try
    End Sub
End Class
