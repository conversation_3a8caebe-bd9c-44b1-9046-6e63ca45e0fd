﻿Public Class ExecupayAssemblyResolver

    Public Property ServerVersionNumber As Decimal?

    Private Sub New()
        VerifyLocalBaseDirectoryExist()
        VerifyServerBaseDirectoryExist()
        ServerVersionNumber = GetServerVersionNumber()
    End Sub

    Private Shared _instance As ExecupayAssemblyResolver
    Private Shared Logger As Serilog.ILogger

    Public Shared Function Instance() As ExecupayAssemblyResolver
        If _instance Is Nothing Then
            Logger = modGlobals.Logger.ForContext(Of ExecupayAssemblyResolver)
            _instance = New ExecupayAssemblyResolver
        End If
        Return _instance
    End Function

    Public Function IsUpdateAvailable()
        If Not GetLocalInstalledVersion().HasValue Then
            Return True
        End If
        If ServerVersionNumber <> GetLocalInstalledVersion() Then
            Return True
        End If
        Return False
    End Function

    Public Sub DownloadLatestDlls()
        Logger.Information("Setting Override_AssemblyResolverPath: {Override_AssemblyResolverPath}, Override_AssemblyResolverPath_AutoFallbackIfNotFound {Override_AssemblyResolverPath_AutoFallbackIfNotFound}", My.Settings.Override_AssemblyResolverPath, My.Settings.Override_AssemblyResolverPath_AutoFallbackIfNotFound)

        Dim localCurrentVersion = GetLocalInstalledVersion()

        If Not localCurrentVersion.HasValue AndAlso Not ServerVersionNumber.HasValue Then
            Throw New Exception("ERROR. The payroll API assemblies could not be resolved.")
        End If

        If ServerVersionNumber.HasValue Then
            Dim targetDirExist = System.IO.Directory.Exists(System.IO.Path.Combine(My.Settings.AssemblyResolverPath, ServerVersionNumber))
            If Not targetDirExist Then Logger.Debug("Error!!! Target directory for Version: {Version} could not be resolved.", ServerVersionNumber)
            If targetDirExist AndAlso (ServerVersionNumber <> localCurrentVersion OrElse Not localCurrentVersion.HasValue) Then
                CleanBaseDirectory()
                CopyAllFiles(System.IO.Path.Combine(My.Settings.AssemblyResolverPath, ServerVersionNumber), LocalBaseDirectoryPath)
                SetCurrentVersionNumber(ServerVersionNumber.Value)
            End If
        End If
    End Sub

    Public Sub SetCurrentVersionNumber(value As Integer)
        WriteVersionNumberToFile(System.IO.Path.Combine(LocalBaseDirectoryPath, "CurrentVersion.txt"), value)
    End Sub

    Public Function GetLocalInstalledVersion() As Integer?
        Return GetVersionNumberFromFile(LocalBaseDirectoryPath, "CurrentVersion.txt")
    End Function

    Public Function GetServerVersionNumber() As Integer?
        Dim result = GetVersionNumberFromFile(My.Settings.AssemblyResolverPath, "CurrentVersion.txt")
        If Not result.HasValue Then Logger.Debug("Error!!! Target Version could not be resolved.")
        Return result
    End Function

    Private Sub VerifyLocalBaseDirectoryExist()
        Dim appDataPath = LocalBaseDirectoryPath()
        Try
            If Not System.IO.Directory.Exists(appDataPath) Then
                Logger.Debug("Base directory does not exist, going to create it. Path: {Path}", appDataPath)
                System.IO.Directory.CreateDirectory(appDataPath)
            Else
                Logger.Debug("Base directory already exist. Path: {Path}", appDataPath)
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error trying to create the base directory. Path: {Path}", appDataPath)
            Throw New Exception($"Error while creating local directory for assembly storage. Please contact IT department.", ex)
        End Try
    End Sub

    Private Sub CleanBaseDirectory()
        For Each file In System.IO.Directory.GetFiles(LocalBaseDirectoryPath)
            If Not file.EndsWith("StartingVersion.txt") AndAlso Not file.EndsWith("CurrentVersion.txt") Then
                Try
                    System.IO.File.Delete(file)
                Catch ex As Exception
                    Logger.Error(ex, "Error deleting file: {File}", file)
                End Try
            End If
        Next
    End Sub

    Private Sub CopyAllFiles(sourcePath As String, targetPath As String)
        For Each file In System.IO.Directory.GetFiles(sourcePath)
            Dim destPath = System.IO.Path.Combine(targetPath, System.IO.Path.GetFileName(file))
            Try
                System.IO.File.Copy(file, destPath)
            Catch ex As Exception
                Logger.Error(ex, "Error copying file {SourcePath} {DestPath}", file, destPath)
            End Try
        Next
    End Sub

    Private Function VerifyServerBaseDirectoryExist() As Boolean
        If Not System.IO.Directory.Exists(My.Settings.AssemblyResolverPath) Then
            Logger.Debug("ERROR. Server base directory does not exist or can't be reached. Path: {Path}", My.Settings.AssemblyResolverPath)
            Return False
        End If
        Return True
    End Function

    Public Sub WriteVersionNumberToFile(path As String, number As Integer)
        Logger.Debug("Setting Version Number. Path: {Path} Version #: {VersionNumber}", path, number)
        System.IO.File.WriteAllText(path, number)
    End Sub

    Private Function GetVersionNumberFromFile(path As String, name As String) As Integer?
        Dim fullPath = System.IO.Path.Combine(path, name)
        If Not System.IO.File.Exists(fullPath) Then
            Logger.Debug("ERROR. {FileName} version file does not exist or can't be reached. Path: {Path}", name, fullPath)
        Else
            Dim text = System.IO.File.ReadAllText(fullPath).Trim
            Logger.Information("Parsing File. Path: {Path} Text: {Text}", text, fullPath)
            If text.IsNullOrWhiteSpace Then
                Logger.Debug("ERROR. {FileName} version file is empty. Path: {Path}", name, fullPath)
            ElseIf Not IsNumeric(text) Then
                Logger.Debug("ERROR. {FileName} version file is not a valid number. Path: {Path}", name, fullPath)
            Else
                Dim number As Integer = 0
                If Integer.TryParse(text, number) Then
                    Logger.Information("Parsing Version Number From File. File: {FileName} Version #: {Version} Path: {Path}", name, number, fullPath)
                    Return number
                Else
                    Logger.Debug("error Parsing text: {Text} to Integer Path: {Path}", text, fullPath)
                End If
            End If
        End If
    End Function

    Public ReadOnly Property LocalBaseDirectoryPath As String
        Get
            Dim appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData)
            appDataPath &= "\BrandsFrontDesk_Assemblies"
            Return appDataPath
        End Get
    End Property

    Public Function IsUsingLatestExecupayVersion() As String
        Dim installed = ExecupayApiManagement.GetLatestInstalledExecupayVersion()
        Dim lastVersion = ExecupayApiManagement.GetLatestExecupayVersion()
        Logger.Debug("ProgramFilesX86 Installed Version {ProgramFilesX86Version}, DB Version: {DbVersion}", installed, lastVersion)
        Dim message = $"Execupay Installed Version: {installed?.FormatedVersionNumber}{vbCrLf}Lastest Version: {lastVersion}.{vbCrLf}{vbCrLf}"
        If installed?.FormatedVersionNumber <> lastVersion Then
            Return $"{Message}(Can't check if FD is using the latest version, until you update Execupay)"
        End If
        Logger.Debug("Local Execupay Installation is up to date!")
        Dim fd_version = FileVersionInfo.GetVersionInfo(System.IO.Path.Combine(LocalBaseDirectoryPath, "Execupay.Core.dll"))
        Dim ep_version = FileVersionInfo.GetVersionInfo(System.IO.Path.Combine(ExecupayApiManagement.GetLatestInstalledExecupayVersion().ExeupayPath, "Execupay.Core.dll"))
        Logger.Debug("Comparing Execupay.Core.dll Versions. fd_version: {fd_version} ep_version: {ep_version}", fd_version.FileVersion, ep_version.FileVersion)
        If fd_version.FileVersion = ep_version.FileVersion Then
            Return Nothing
        End If
        Return $"{message}Comparing Execupay.Core.dll Versions.{vbCrLf}fd_version: {fd_version.FileVersion}{vbCrLf}ep_version: {ep_version.FileVersion}"
    End Function

End Class
