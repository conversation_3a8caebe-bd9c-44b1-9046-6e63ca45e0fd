﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Base

Public Class frmImportW2HealthCare

    Private db As dbEPDataDataContext
    Private Property Data As List(Of ImportW2HealthCare)
    Private emp_list As List(Of EmpList)
    Private _EmpNum As Decimal
    Private _FullName As String

    Private Sub frmImportW2HealthCare_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            db = New dbEPDataDataContext(GetConnectionString())
            Data = New List(Of ImportW2HealthCare)()
            BindingSource1.DataSource = Data

            If Today.Month > 9 Then
                teYearToReport.EditValue = Today.Year
            Else
                teYearToReport.EditValue = Today.AddYears(-1).Year
            End If

            deDateEntered.DateTime = Now
            lueConum.DisplayMember = "Name"
            lueConum.ValueMember = "CoNum"
            lueConum.DataSource = (From c In db.view_CompanySumarries Select New With {.CoNum = c.CONUM, .Name = c.CoNumAndName}).ToList()
        Catch ex As Exception
            DisplayErrorMessage("error in frmImportW2HealthCare_Load", ex)
        End Try
    End Sub

    Private Sub btnClearList_Click(sender As Object, e As EventArgs) Handles btnClearList.Click
        Data = New List(Of ImportW2HealthCare)()
        BindingSource1.DataSource = Data
    End Sub

    Private Sub btnPastDataFromClipboard_Click(sender As Object, e As EventArgs) Handles btnPastDataFromClipboard.Click
        Try
            Dim clipboardText = Clipboard.GetText()
            Clipboard.SetText(clipboardText.Replace(vbCrLf, vbTab & "0" & vbCrLf))
            GridView1.PasteFromClipboard()

            For Each r In Data
                If r.EmployeeExist = False Then
                    GetEmpInfo(r)
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error pasting data from clipboard.", ex)
        End Try
    End Sub

    Sub GetEmpInfo(ByVal r As ImportW2HealthCare)
        Dim emp = (From en In db.EMPLOYEEs Where en.CONUM = r.Conum AndAlso en.EMPNUM = r.Empnum Select New EmpList With {.EmpNum = en.EMPNUM, .FullName = "{0} - {1} {2}".FormatWith(en.EMPNUM, en.F_NAME, en.L_NAME)}).FirstOrDefault()

        If emp IsNot Nothing Then
            r.FullName = emp.FullName
            r.EmployeeExist = True
        Else
            r.FullName = "Employee does not exists"
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If GridView1.RowCount < 2 Then  'first is empty row
            MessageBox.Show("Nothing found to add")
            Return
        ElseIf MessageBox.Show($"Are you sure you want to upload {GridView1.RowCount - 1} rows", "Confirm", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1) <> DialogResult.Yes Then
            Return
        End If

        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                For Each item In Data
                    If item.EmployeeExist Then
                        ctxDB.W2ErSponsHlths.InsertOnSubmit(New W2ErSponsHlth With {.CoNum = item.Conum,
                                                      .EmpNum = item.Empnum,
                                                      .TotalValue = item.TotalValue,
                                                      .DateEntered = deDateEntered.DateTime,
                                                      .YearToReport = teYearToReport.EditValue})
                    End If
                Next
                ctxDB.SaveChanges()
                XtraMessageBox.Show("successfully saved changes")
                'Close()
                Data = New List(Of ImportW2HealthCare)()
                BindingSource1.DataSource = Data
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error saving data", ex)
        End Try
    End Sub

    Public Class ImportW2HealthCare
        Public Property Conum As Decimal
        Public Property Empnum As Decimal
        Public Property FullName As String
        Public Property TotalValue As Decimal
        Public Property EmployeeExist As Boolean
    End Class

    Class EmpList
        Property EmpNum As Decimal
        Property FullName As String
    End Class

    Private Sub BindingSource1_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles BindingSource1.AddingNew
        Dim obj = New ImportW2HealthCare
        Data.Add(obj)
        e.NewObject = obj
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Remove Selected Rows", Sub()
                                                                                          GridView1.DeleteSelectedRows()
                                                                                      End Sub, My.Resources.remove_16x16))
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Remove Row", Sub()
                                                                                GridView1.DeleteRow(GridView1.FocusedRowHandle)
                                                                            End Sub, My.Resources.remove_16x16))
    End Sub

    Sub GridView1_ShownEditor(sender As Object, e As EventArgs) Handles GridView1.ShownEditor
        Try
            Dim view As ColumnView = DirectCast(sender, ColumnView)
            If view.FocusedColumn.FieldName = "Empnum" Then
                Dim editor As LookUpEdit = CType(view.ActiveEditor, LookUpEdit)
                Dim CoNum As Decimal = Convert.ToDecimal(view.GetFocusedRowCellValue("Conum"))

                Dim list = (New List(Of EMPLOYEE)(New EMPLOYEE() {New EMPLOYEE With {.EMPNUM = -1, .L_NAME = "", .F_NAME = "(ALL Employees)"}}))
                Dim AllEmployees = (From A In list.Union(db.EMPLOYEEs.Where(Function(c) c.CONUM = CoNum).ToList())
                                    Select New EmpList With {.EmpNum = A.EMPNUM, .FullName = "{0} - {1} {2}".FormatWith(A.EMPNUM, A.F_NAME, A.L_NAME)}).ToList
                emp_list = AllEmployees

                lueEmpNum.DisplayMember = "FullName"
                lueEmpNum.ValueMember = "EmpNum"
                editor.Properties.DataSource = AllEmployees
            End If
        Catch ex As Exception
            DisplayErrorMessage("error in GridView1_ShownEditor", ex)
        End Try
    End Sub

    Private Sub GridView1_CustomColumnDisplayText(sender As Object, e As CustomColumnDisplayTextEventArgs) Handles GridView1.CustomColumnDisplayText
        Try
            If e.Column.FieldName = "Empnum" And e.Value <> Nothing Then
                Dim FullName = GridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "FullName")
                If FullName <> Nothing Then
                    e.DisplayText = FullName
                    'Else
                    '    Dim Row = CType(GridView1.GetRow(e.ListSourceRowIndex), ImportW2HealthCare)

                    '    If Row.Conum = 0 Then
                    '        e.DisplayText = "Wrong paste"
                    '        Return
                    '    End If

                    '    Dim emp = (From en In db.EMPLOYEEs Where en.CONUM = Row.Conum AndAlso en.EMPNUM = Row.Empnum Select New EmpList With {.EmpNum = en.EMPNUM, .FullName = "{0} - {1} {2}".FormatWith(en.EMPNUM, en.F_NAME, en.L_NAME)}).FirstOrDefault()

                    '    If emp IsNot Nothing Then
                    '        Row.FullName = emp.FullName
                    '    Else
                    '        Dim EmpNum = GridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "Empnum")
                    '        e.DisplayText = EmpNum
                    '    End If
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("error in GridView1_CustomColumnDisplayText", ex)
        End Try
    End Sub

    'Private Sub lueEmpNum_CloseUp(ByVal sender As Object, ByVal e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles lueEmpNum.CloseUp
    '    Try
    '        If e.Value Is Nothing Then
    '            Return
    '        End If

    '        _FullName = (From r In emp_list Where r.EmpNum = e.Value).FirstOrDefault()?.FullName
    '        Dim Row = CType(GridView1.GetRow(GridView1.FocusedRowHandle), ImportW2HealthCare)
    '        Row.FullName = _FullName
    '    Catch ex As Exception
    '        DisplayErrorMessage("error in lueEmpNum_CloseUp", ex)
    '    End Try
    'End Sub

    Private Sub lueConum_EditValueChanged(ByVal sender As Object, ByVal e As EventArgs) Handles lueConum.EditValueChanged
        Try
            GridView1.PostEditor()
            GetEmpInfo(GridView1.GetRow(GridView1.FocusedRowHandle))
            'Dim Row = CType(GridView1.GetRow(GridView1.FocusedRowHandle), ImportW2HealthCare)
            'Row.Empnum = Nothing
            'Row.FullName = Nothing
        Catch ex As Exception
            DisplayErrorMessage("lueConum_EditValueChanged", ex)
        End Try
    End Sub

    Private Sub lueEmpnum_EditValueChanged(ByVal sender As Object, ByVal e As EventArgs) Handles lueEmpNum.EditValueChanged
        Try
            GridView1.PostEditor()
            GetEmpInfo(GridView1.GetRow(GridView1.FocusedRowHandle))
        Catch ex As Exception
            DisplayErrorMessage("lueEmpnum_EditValueChanged", ex)
        End Try
    End Sub

    Private Sub hllcUpdateLogs_Click(sender As Object, e As EventArgs) Handles hllcUpdateLogs.Click
        Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
        If sqlFrm IsNot Nothing Then
            frmMain.CloseForm(sqlFrm)
        End If
        frmMain.AddOrActivateForm(Of frmSqlScripts)()
        sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
        sqlFrm.FindAndFocusRecord(528)
    End Sub

    Private Sub GridView1_RowCountChanged(sender As Object, e As EventArgs) Handles GridView1.RowCountChanged
        btnSave.Text = $"Save {GridView1.RowCount - 1} records"
    End Sub
End Class