﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucEmployeeList
    Inherits System.Windows.Forms.UserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim StyleFormatCondition1 As DevExpress.XtraGrid.StyleFormatCondition = New DevExpress.XtraGrid.StyleFormatCondition()
        Dim StyleFormatCondition2 As DevExpress.XtraGrid.StyleFormatCondition = New DevExpress.XtraGrid.StyleFormatCondition()
        Dim GridFormatRule1 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleExpression1 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
        Me.GridColumn27 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn36 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.UcEmployeeInfo1 = New Brands_FrontDesk.ucEmployeeInfo()
        Me.gcEmployeeList = New DevExpress.XtraGrid.GridControl()
        Me.EmployeeListBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.gvEmployeeList = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riDivision = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.DIVISIONBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.colDepNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riDepartments = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.DEPARTMENTBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.colStartDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn8 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colHasChanges = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.SearchControl1 = New DevExpress.XtraEditors.SearchControl()
        Me.ddbFilter = New DevExpress.XtraEditors.DropDownButton()
        Me.pmFilter = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.bciAllEmployees = New DevExpress.XtraBars.BarCheckItem()
        Me.bciActiveOnly = New DevExpress.XtraBars.BarCheckItem()
        Me.bciTerminatedOnly = New DevExpress.XtraBars.BarCheckItem()
        Me.bciRecentlyAdded = New DevExpress.XtraBars.BarCheckItem()
        Me.bsiColumnChooser = New DevExpress.XtraBars.BarSubItem()
        Me.BarManager1 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.BarDockControl1 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl2 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl3 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl4 = New DevExpress.XtraBars.BarDockControl()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup14 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroupEmployees = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem25 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem26 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem27 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem4 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.btnSaveAndCopyPays = New DevExpress.XtraEditors.SimpleButton()
        Me.GridDeds = New DevExpress.XtraGrid.GridControl()
        Me.GridView3 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn21 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn22 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn23 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn24 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn25 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn26 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn28 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn29 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.btnEditAutoDeduction = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl18 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddAutoDeduction = New DevExpress.XtraEditors.SimpleButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.btnEditAutoPay = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAddAutoPay = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl17 = New DevExpress.XtraEditors.LabelControl()
        Me.GridPays = New DevExpress.XtraGrid.GridControl()
        Me.GridView4 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn30 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemLookUpEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.GridColumn31 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn32 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn33 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn34 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn35 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn37 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn38 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnCopy = New DevExpress.XtraEditors.SimpleButton()
        Me.btnNewEmployee = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSave = New DevExpress.XtraEditors.SimpleButton()
        Me.gcPayrollDeds = New DevExpress.XtraGrid.GridControl()
        Me.GridView5 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn39 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn40 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.gcPayrollTaxes = New DevExpress.XtraGrid.GridControl()
        Me.GridView6 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn41 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn42 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn43 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn44 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn45 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.gcPayHistory = New DevExpress.XtraGrid.GridControl()
        Me.GridView7 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn46 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn47 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn48 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn49 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn50 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn51 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn52 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn53 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn54 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn55 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn56 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl()
        Me.btnYear = New DevExpress.XtraEditors.SimpleButton()
        Me.btnLastYear = New DevExpress.XtraEditors.SimpleButton()
        Me.btnQ4 = New DevExpress.XtraEditors.SimpleButton()
        Me.btnQ3 = New DevExpress.XtraEditors.SimpleButton()
        Me.btnQ2 = New DevExpress.XtraEditors.SimpleButton()
        Me.btnQ1 = New DevExpress.XtraEditors.SimpleButton()
        Me.PanelControl2 = New DevExpress.XtraEditors.PanelControl()
        Me.btnParsonage = New DevExpress.XtraEditors.CheckButton()
        Me.pnlTerminate = New DevExpress.XtraEditors.PanelControl()
        Me.lblEmployeeStatus = New DevExpress.XtraEditors.LabelControl()
        Me.btnTerminate = New DevExpress.XtraEditors.SimpleButton()
        Me.PanelControl8 = New DevExpress.XtraEditors.PanelControl()
        Me.EMPNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.Default_hoursTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.SALARY_AMTTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.RATE_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.RATE_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.RATE_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PAY_FREQTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.AUTO_SAL_HRSTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEdit4 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit2 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.DateEdit()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.GroupControl11 = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl16 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl15 = New DevExpress.XtraEditors.LabelControl()
        Me.ComboBoxEdit2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.SSNLabelControl = New DevExpress.XtraEditors.LabelControl()
        Me.TINTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.SSNTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.M_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CITYTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ADDR_STATETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ZIPTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Contact_pagerTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.GENDERLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.Contact_homephoneTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.B_DAYDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.F_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Contact_homeemailTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.User_emailTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.STREETTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.L_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TINLabel = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.START_DATEDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.DEPTNUMLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.DIVNUMLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.EMP_TYPETextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TERM_DATEDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.PanelControlPayrollTax = New DevExpress.XtraEditors.PanelControl()
        Me.GroupControl9 = New DevExpress.XtraEditors.GroupControl()
        Me.RadioGroup1 = New DevExpress.XtraEditors.RadioGroup()
        Me.LabelControl12 = New DevExpress.XtraEditors.LabelControl()
        Me.LocalActiveTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Local_statusLabel = New System.Windows.Forms.Label()
        Me.Fixed_whTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Extra_whTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DependentsLabel = New System.Windows.Forms.Label()
        Me.DependentsTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Alternate_w4Label = New System.Windows.Forms.Label()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddLocal = New DevExpress.XtraEditors.SimpleButton()
        Me.btnRemoveLocal = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.lstSelectedLocals = New DevExpress.XtraEditors.ListBoxControl()
        Me.lstAvailLocals = New DevExpress.XtraEditors.ListBoxControl()
        Me.Alternate_w4TextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Local_exemptTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Local_statusTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.GroupControl5 = New DevExpress.XtraEditors.GroupControl()
        Me.FLIEXELabel = New System.Windows.Forms.Label()
        Me.FLIEXECheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddState = New DevExpress.XtraEditors.SimpleButton()
        Me.btnRemoveState = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.lstSelectedStates = New DevExpress.XtraEditors.ListBoxControl()
        Me.lstAvailableStates = New DevExpress.XtraEditors.ListBoxControl()
        Me.ST_WH_FIXEDLabel = New System.Windows.Forms.Label()
        Me.ST_WH_FIXEDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ST_WH_EXTRALabel = New System.Windows.Forms.Label()
        Me.ST_WH_EXTRATextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.SDIEXELabel = New System.Windows.Forms.Label()
        Me.ST_DEPSLabel = New System.Windows.Forms.Label()
        Me.ST_DEPSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ST_STATUSLabel = New System.Windows.Forms.Label()
        Me.ST_WH_EXE_FGELabel = New System.Windows.Forms.Label()
        Me.ALTW4Label = New System.Windows.Forms.Label()
        Me.DEFAULT_WORKLabel = New System.Windows.Forms.Label()
        Me.DEFAULT_RESLabel = New System.Windows.Forms.Label()
        Me.UCI_STATETextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DEFAULT_RESTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.DEFAULT_WORKTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ALTW4TextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ST_WH_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ST_STATUSTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.SDIEXETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.SUTA_EXE_FGTextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControl4 = New DevExpress.XtraEditors.GroupControl()
        Me.FED_WH_FIXEDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FED_WH_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.OASDI_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.FED_WH_EXTRATextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FUTA_EXE_FGETextEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.FED_DEPSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FED_STATUSTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.PanelControl4 = New DevExpress.XtraEditors.PanelControl()
        Me.GroupControl7 = New DevExpress.XtraEditors.GroupControl()
        Me.GridControlCoOptionsSecondCheckPay = New DevExpress.XtraGrid.GridControl()
        Me.GridView8 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn57 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemLookUpEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.GridColumn58 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnDelete = New DevExpress.XtraEditors.SimpleButton()
        Me.btnAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.pnlOverrideGroups = New System.Windows.Forms.Panel()
        Me.pnlGeneralOptions = New System.Windows.Forms.Panel()
        Me.ExcludeFromUtilityImportCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControl8 = New DevExpress.XtraEditors.GroupControl()
        Me.OTSeperateCheckHoursMoreThanTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.OTSeperateCheckCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ddlPayUnderEmpNum = New DevExpress.XtraEditors.LookUpEdit()
        Me.grpSecondCheckTaxes = New DevExpress.XtraEditors.GroupControl()
        Me.UIStateComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ResStateComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.WrkStateComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.FLIOverrdieAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.MedicareOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.OASDIOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FedOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CheckTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TaxFrequencyTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DBOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.STOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.LOCOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.tbManualCheckNumber = New DevExpress.XtraEditors.TextEdit()
        Me.grpScheduling = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.LastOfMonthCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd5CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd4CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd3CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd2CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.Prd1CheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.AllPrdsCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.pnlNetOverride = New DevExpress.XtraEditors.GroupControl()
        Me.NetOverrideDedNumTextEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.NetOverrideAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.NetOverrideAdjustTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.pnlOTHours = New System.Windows.Forms.Panel()
        Me.OTHoursTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.pnlSecondCheck = New System.Windows.Forms.Panel()
        Me.DeptNum2ndCheck = New DevExpress.XtraEditors.LookUpEdit()
        Me.DivNum2ndCheck = New DevExpress.XtraEditors.LookUpEdit()
        Me.PayDedCodeLookUpEdit = New DevExpress.XtraEditors.LookUpEdit()
        Me.CheckCounterTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PayDedAmountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PayDedTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.pnlRecordType = New System.Windows.Forms.Panel()
        Me.RecordTypeTextEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView9 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn59 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn60 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.PanelControl5 = New DevExpress.XtraEditors.PanelControl()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.DD_STATUS_4ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_AMT_4TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_STATUS_3ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_MET_4ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_AMT_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_NO_4TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_SPLIT_MET_3ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_BANK_RT_4TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_NO_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_TYPE_4ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_BANK_RT_3TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_TYPE_3ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.DD_STATUS_2ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_AMT_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_SPLIT_MET_2ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_ACC_NO_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_BANK_RT_2TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_ACC_TYPE_2ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_STATUS_1ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_SPLIT_MET_1ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_ACC_TYPE_1ComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DD_ACC_NO_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_BANK_RT_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DD_SPLIT_AMT_1TextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PanelControl6 = New DevExpress.XtraEditors.PanelControl()
        Me.UcNotes1 = New Brands_FrontDesk.ucNotes()
        Me.lcEmpName = New DevExpress.XtraEditors.LabelControl()
        Me.gcCheckMaster = New DevExpress.XtraGrid.GridControl()
        Me.GridView10 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn61 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn62 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn63 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn64 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn65 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn66 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn67 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn68 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn69 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn70 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn71 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
        Me.LayoutControlGroup5 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup6 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup7 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup8 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup9 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup10 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlGroup11 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem2 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem3 = New DevExpress.XtraLayout.SplitterItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem4 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup12 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup13 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.popupCopyFrom = New DevExpress.XtraEditors.PopupContainerControl()
        Me.GroupControl10 = New DevExpress.XtraEditors.GroupControl()
        Me.txtEmpNum = New DevExpress.XtraEditors.TextEdit()
        Me.txtCoName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.slueEmployeeCopyFrom = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView11 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn72 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn73 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl13 = New DevExpress.XtraEditors.LabelControl()
        Me.btnOk = New DevExpress.XtraEditors.SimpleButton()
        Me.btnCancelCopyToNewCompany = New DevExpress.XtraEditors.SimpleButton()
        Me.slueCompanyCopyFrom = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView12 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn74 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn75 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn76 = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.gcEmployeeList, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmployeeListBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvEmployeeList, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riDivision, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DIVISIONBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riDepartments, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEPARTMENTBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pmFilter, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroupEmployees, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.GridDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.GridPays, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemLookUpEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcPayrollDeds, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcPayrollTaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcPayHistory, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.PanelControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl2.SuspendLayout()
        CType(Me.pnlTerminate, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTerminate.SuspendLayout()
        CType(Me.PanelControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl8.SuspendLayout()
        CType(Me.EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.Default_hoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SALARY_AMTTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RATE_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RATE_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RATE_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PAY_FREQTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AUTO_SAL_HRSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.GroupControl11, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl11.SuspendLayout()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TINTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SSNTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.M_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CITYTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ADDR_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ZIPTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Contact_pagerTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GENDERLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Contact_homephoneTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.B_DAYDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.B_DAYDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.F_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Contact_homeemailTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.User_emailTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.STREETTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.L_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.START_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.START_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEPTNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DIVNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMP_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TERM_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TERM_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControlPayrollTax, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControlPayrollTax.SuspendLayout()
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl9.SuspendLayout()
        CType(Me.RadioGroup1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LocalActiveTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Fixed_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Extra_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DependentsTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstSelectedLocals, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstAvailLocals, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Alternate_w4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Local_exemptTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Local_statusTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl5.SuspendLayout()
        CType(Me.FLIEXECheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstSelectedStates, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lstAvailableStates, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UCI_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEFAULT_RESTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DEFAULT_WORKTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ALTW4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ST_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SDIEXETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SUTA_EXE_FGTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl4.SuspendLayout()
        CType(Me.FED_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OASDI_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FUTA_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FED_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl4.SuspendLayout()
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl7.SuspendLayout()
        CType(Me.GridControlCoOptionsSecondCheckPay, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemLookUpEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlOverrideGroups.SuspendLayout()
        Me.pnlGeneralOptions.SuspendLayout()
        CType(Me.ExcludeFromUtilityImportCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl8.SuspendLayout()
        CType(Me.OTSeperateCheckHoursMoreThanTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OTSeperateCheckCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ddlPayUnderEmpNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.grpSecondCheckTaxes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpSecondCheckTaxes.SuspendLayout()
        CType(Me.UIStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ResStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.WrkStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FLIOverrdieAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.MedicareOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OASDIOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FedOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TaxFrequencyTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DBOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.STOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LOCOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tbManualCheckNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.grpScheduling, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.grpScheduling.SuspendLayout()
        CType(Me.LastOfMonthCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd5CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd4CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd3CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd2CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Prd1CheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AllPrdsCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pnlNetOverride, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlNetOverride.SuspendLayout()
        CType(Me.NetOverrideDedNumTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NetOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NetOverrideAdjustTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlOTHours.SuspendLayout()
        CType(Me.OTHoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSecondCheck.SuspendLayout()
        CType(Me.DeptNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DivNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PayDedCodeLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckCounterTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PayDedAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PayDedTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlRecordType.SuspendLayout()
        CType(Me.RecordTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl5.SuspendLayout()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl6.SuspendLayout()
        CType(Me.DD_STATUS_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_STATUS_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_STATUS_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_STATUS_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_MET_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_TYPE_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_ACC_NO_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_BANK_RT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DD_SPLIT_AMT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl6.SuspendLayout()
        CType(Me.gcCheckMaster, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.popupCopyFrom, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.popupCopyFrom.SuspendLayout()
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl10.SuspendLayout()
        CType(Me.txtEmpNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtCoName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueEmployeeCopyFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueCompanyCopyFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView12, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GridColumn27
        '
        Me.GridColumn27.Caption = "Limit"
        Me.GridColumn27.DisplayFormat.FormatString = "c"
        Me.GridColumn27.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn27.FieldName = "Limit"
        Me.GridColumn27.Name = "GridColumn27"
        Me.GridColumn27.OptionsColumn.ReadOnly = True
        Me.GridColumn27.Visible = True
        Me.GridColumn27.VisibleIndex = 6
        Me.GridColumn27.Width = 88
        '
        'GridColumn36
        '
        Me.GridColumn36.Caption = "Limit"
        Me.GridColumn36.DisplayFormat.FormatString = "c"
        Me.GridColumn36.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn36.FieldName = "Limit"
        Me.GridColumn36.Name = "GridColumn36"
        Me.GridColumn36.OptionsColumn.ReadOnly = True
        Me.GridColumn36.Visible = True
        Me.GridColumn36.VisibleIndex = 6
        Me.GridColumn36.Width = 89
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.UcEmployeeInfo1)
        Me.LayoutControl1.Controls.Add(Me.gcEmployeeList)
        Me.LayoutControl1.Controls.Add(Me.SearchControl1)
        Me.LayoutControl1.Controls.Add(Me.ddbFilter)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(651, 247, 1216, 687)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1268, 722)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'UcEmployeeInfo1
        '
        Me.UcEmployeeInfo1.AutoPaysAndDeds = Nothing
        Me.UcEmployeeInfo1.Changes = Nothing
        Me.UcEmployeeInfo1.CheckOverrides = Nothing
        Me.UcEmployeeInfo1.CoNum = New Decimal(New Integer() {0, 0, 0, 0})
        Me.UcEmployeeInfo1.EmployeeEnt = Nothing
        Me.UcEmployeeInfo1.EmployeeID = New Decimal(New Integer() {0, 0, 0, 0})
        Me.UcEmployeeInfo1.LocalInfoEntList = Nothing
        Me.UcEmployeeInfo1.Location = New System.Drawing.Point(308, 12)
        Me.UcEmployeeInfo1.Name = "UcEmployeeInfo1"
        Me.UcEmployeeInfo1.Size = New System.Drawing.Size(948, 698)
        Me.UcEmployeeInfo1.StateInfoEnt = Nothing
        Me.UcEmployeeInfo1.TabIndex = 3
        '
        'gcEmployeeList
        '
        Me.gcEmployeeList.DataSource = Me.EmployeeListBindingSource
        Me.gcEmployeeList.Location = New System.Drawing.Point(15, 69)
        Me.gcEmployeeList.MainView = Me.gvEmployeeList
        Me.gcEmployeeList.Name = "gcEmployeeList"
        Me.gcEmployeeList.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riDepartments, Me.riDivision})
        Me.gcEmployeeList.Size = New System.Drawing.Size(281, 638)
        Me.gcEmployeeList.TabIndex = 2
        Me.gcEmployeeList.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvEmployeeList})
        '
        'EmployeeListBindingSource
        '
        Me.EmployeeListBindingSource.DataSource = GetType(Brands_FrontDesk.ucEmployeeInfo.EmployeeList)
        '
        'gvEmployeeList
        '
        Me.gvEmployeeList.Appearance.HeaderPanel.Font = New System.Drawing.Font("Segoe UI Semibold", 10.0!, System.Drawing.FontStyle.Bold)
        Me.gvEmployeeList.Appearance.HeaderPanel.Options.UseFont = True
        Me.gvEmployeeList.Appearance.Row.Font = New System.Drawing.Font("Arial Narrow", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gvEmployeeList.Appearance.Row.Options.UseFont = True
        Me.gvEmployeeList.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5, Me.colDivNum, Me.colDepNum, Me.colStartDate, Me.GridColumn8, Me.colHasChanges})
        Me.gvEmployeeList.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.gvEmployeeList.GridControl = Me.gcEmployeeList
        Me.gvEmployeeList.Name = "gvEmployeeList"
        Me.gvEmployeeList.OptionsBehavior.Editable = False
        Me.gvEmployeeList.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.gvEmployeeList.OptionsSelection.EnableAppearanceHideSelection = False
        Me.gvEmployeeList.OptionsView.ColumnAutoWidth = False
        Me.gvEmployeeList.OptionsView.ShowGroupPanel = False
        Me.gvEmployeeList.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.gvEmployeeList.OptionsView.ShowIndicator = False
        Me.gvEmployeeList.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn1.Caption = "Emp #"
        Me.GridColumn1.FieldName = "EmpNum"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 50
        '
        'GridColumn2
        '
        Me.GridColumn2.Caption = "Name"
        Me.GridColumn2.FieldName = "colEmpName"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.UnboundExpression = "[LastName] + ', ' + [FirstName] + ' ' + [MiddleName]"
        Me.GridColumn2.UnboundType = DevExpress.Data.UnboundColumnType.[String]
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 150
        '
        'GridColumn3
        '
        Me.GridColumn3.Caption = "L Name"
        Me.GridColumn3.FieldName = "LastName"
        Me.GridColumn3.Name = "GridColumn3"
        '
        'GridColumn4
        '
        Me.GridColumn4.Caption = "F Name"
        Me.GridColumn4.FieldName = "FirstName"
        Me.GridColumn4.Name = "GridColumn4"
        '
        'GridColumn5
        '
        Me.GridColumn5.Caption = "M Name"
        Me.GridColumn5.FieldName = "MiddleName"
        Me.GridColumn5.Name = "GridColumn5"
        '
        'colDivNum
        '
        Me.colDivNum.Caption = "Division"
        Me.colDivNum.ColumnEdit = Me.riDivision
        Me.colDivNum.FieldName = "DivNum"
        Me.colDivNum.Name = "colDivNum"
        Me.colDivNum.Visible = True
        Me.colDivNum.VisibleIndex = 2
        '
        'riDivision
        '
        Me.riDivision.AutoHeight = False
        Me.riDivision.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riDivision.DataSource = Me.DIVISIONBindingSource
        Me.riDivision.DisplayMember = "Descr"
        Me.riDivision.Name = "riDivision"
        Me.riDivision.ValueMember = "DDIVNUM"
        '
        'DIVISIONBindingSource
        '
        Me.DIVISIONBindingSource.DataSource = GetType(Brands_FrontDesk.DIVISION)
        '
        'colDepNum
        '
        Me.colDepNum.Caption = "Department"
        Me.colDepNum.ColumnEdit = Me.riDepartments
        Me.colDepNum.FieldName = "DepNum"
        Me.colDepNum.Name = "colDepNum"
        Me.colDepNum.Visible = True
        Me.colDepNum.VisibleIndex = 3
        '
        'riDepartments
        '
        Me.riDepartments.AutoHeight = False
        Me.riDepartments.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riDepartments.DataSource = Me.DEPARTMENTBindingSource
        Me.riDepartments.DisplayMember = "Descr"
        Me.riDepartments.Name = "riDepartments"
        Me.riDepartments.ValueMember = "DEPTNUM"
        '
        'DEPARTMENTBindingSource
        '
        Me.DEPARTMENTBindingSource.DataSource = GetType(Brands_FrontDesk.DEPARTMENT)
        '
        'colStartDate
        '
        Me.colStartDate.Caption = "Start Date"
        Me.colStartDate.DisplayFormat.FormatString = "MM/dd/yyyy"
        Me.colStartDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom
        Me.colStartDate.FieldName = "StartDate"
        Me.colStartDate.Name = "colStartDate"
        Me.colStartDate.Visible = True
        Me.colStartDate.VisibleIndex = 4
        '
        'GridColumn8
        '
        Me.GridColumn8.FieldName = "DisplayName"
        Me.GridColumn8.Name = "GridColumn8"
        Me.GridColumn8.OptionsColumn.ReadOnly = True
        Me.GridColumn8.OptionsColumn.ShowInCustomizationForm = False
        '
        'colHasChanges
        '
        Me.colHasChanges.Caption = "Has Changes"
        Me.colHasChanges.FieldName = "HasChanges"
        Me.colHasChanges.Name = "colHasChanges"
        Me.colHasChanges.OptionsColumn.AllowShowHide = False
        Me.colHasChanges.OptionsColumn.ShowInCustomizationForm = False
        Me.colHasChanges.OptionsColumn.ShowInExpressionEditor = False
        '
        'SearchControl1
        '
        Me.SearchControl1.Client = Me.gcEmployeeList
        Me.SearchControl1.Location = New System.Drawing.Point(15, 33)
        Me.SearchControl1.Name = "SearchControl1"
        Me.SearchControl1.Properties.Appearance.Font = New System.Drawing.Font("Tahoma", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SearchControl1.Properties.Appearance.Options.UseFont = True
        Me.SearchControl1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Repository.ClearButton(), New DevExpress.XtraEditors.Repository.SearchButton()})
        Me.SearchControl1.Properties.Client = Me.gcEmployeeList
        Me.SearchControl1.Properties.FindDelay = 250
        Me.SearchControl1.Size = New System.Drawing.Size(216, 30)
        Me.SearchControl1.StyleController = Me.LayoutControl1
        Me.SearchControl1.TabIndex = 0
        '
        'ddbFilter
        '
        Me.ddbFilter.AllowFocus = False
        Me.ddbFilter.Appearance.Font = New System.Drawing.Font("Segoe UI", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ddbFilter.Appearance.Options.UseFont = True
        Me.ddbFilter.AppearanceHovered.Font = New System.Drawing.Font("Segoe UI", 14.25!)
        Me.ddbFilter.AppearanceHovered.ForeColor = System.Drawing.Color.Blue
        Me.ddbFilter.AppearanceHovered.Options.UseFont = True
        Me.ddbFilter.AppearanceHovered.Options.UseForeColor = True
        Me.ddbFilter.AppearancePressed.Font = New System.Drawing.Font("Segoe UI", 14.25!)
        Me.ddbFilter.AppearancePressed.ForeColor = System.Drawing.Color.Blue
        Me.ddbFilter.AppearancePressed.Options.UseFont = True
        Me.ddbFilter.AppearancePressed.Options.UseForeColor = True
        Me.ddbFilter.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show
        Me.ddbFilter.DropDownControl = Me.pmFilter
        Me.ddbFilter.Location = New System.Drawing.Point(235, 33)
        Me.ddbFilter.Name = "ddbFilter"
        Me.ddbFilter.Size = New System.Drawing.Size(61, 32)
        Me.ddbFilter.StyleController = Me.LayoutControl1
        Me.ddbFilter.TabIndex = 1
        Me.ddbFilter.Text = "Filter"
        '
        'pmFilter
        '
        Me.pmFilter.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.bciAllEmployees), New DevExpress.XtraBars.LinkPersistInfo(Me.bciActiveOnly), New DevExpress.XtraBars.LinkPersistInfo(Me.bciTerminatedOnly), New DevExpress.XtraBars.LinkPersistInfo(Me.bciRecentlyAdded), New DevExpress.XtraBars.LinkPersistInfo(Me.bsiColumnChooser, True)})
        Me.pmFilter.Manager = Me.BarManager1
        Me.pmFilter.MenuCaption = "Filter"
        Me.pmFilter.Name = "pmFilter"
        Me.pmFilter.ShowCaption = True
        '
        'bciAllEmployees
        '
        Me.bciAllEmployees.Caption = "All Employees"
        Me.bciAllEmployees.Id = 0
        Me.bciAllEmployees.Name = "bciAllEmployees"
        '
        'bciActiveOnly
        '
        Me.bciActiveOnly.BindableChecked = True
        Me.bciActiveOnly.Caption = "Active Only"
        Me.bciActiveOnly.Checked = True
        Me.bciActiveOnly.Id = 1
        Me.bciActiveOnly.Name = "bciActiveOnly"
        '
        'bciTerminatedOnly
        '
        Me.bciTerminatedOnly.Caption = "Terminated Only"
        Me.bciTerminatedOnly.Id = 3
        Me.bciTerminatedOnly.Name = "bciTerminatedOnly"
        '
        'bciRecentlyAdded
        '
        Me.bciRecentlyAdded.Caption = "Recently Added"
        Me.bciRecentlyAdded.Id = 4
        Me.bciRecentlyAdded.Name = "bciRecentlyAdded"
        '
        'bsiColumnChooser
        '
        Me.bsiColumnChooser.Caption = "Column Chooser"
        Me.bsiColumnChooser.Id = 5
        Me.bsiColumnChooser.Name = "bsiColumnChooser"
        '
        'BarManager1
        '
        Me.BarManager1.DockControls.Add(Me.BarDockControl1)
        Me.BarManager1.DockControls.Add(Me.BarDockControl2)
        Me.BarManager1.DockControls.Add(Me.BarDockControl3)
        Me.BarManager1.DockControls.Add(Me.BarDockControl4)
        Me.BarManager1.Form = Me
        Me.BarManager1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.bciAllEmployees, Me.bciActiveOnly, Me.bciTerminatedOnly, Me.bciRecentlyAdded, Me.bsiColumnChooser})
        Me.BarManager1.MaxItemId = 6
        '
        'BarDockControl1
        '
        Me.BarDockControl1.CausesValidation = False
        Me.BarDockControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.BarDockControl1.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl1.Manager = Me.BarManager1
        Me.BarDockControl1.Size = New System.Drawing.Size(1268, 0)
        '
        'BarDockControl2
        '
        Me.BarDockControl2.CausesValidation = False
        Me.BarDockControl2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.BarDockControl2.Location = New System.Drawing.Point(0, 722)
        Me.BarDockControl2.Manager = Me.BarManager1
        Me.BarDockControl2.Size = New System.Drawing.Size(1268, 0)
        '
        'BarDockControl3
        '
        Me.BarDockControl3.CausesValidation = False
        Me.BarDockControl3.Dock = System.Windows.Forms.DockStyle.Left
        Me.BarDockControl3.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl3.Manager = Me.BarManager1
        Me.BarDockControl3.Size = New System.Drawing.Size(0, 722)
        '
        'BarDockControl4
        '
        Me.BarDockControl4.CausesValidation = False
        Me.BarDockControl4.Dock = System.Windows.Forms.DockStyle.Right
        Me.BarDockControl4.Location = New System.Drawing.Point(1268, 0)
        Me.BarDockControl4.Manager = Me.BarManager1
        Me.BarDockControl4.Size = New System.Drawing.Size(0, 722)
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup14})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1268, 722)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlGroup14
        '
        Me.LayoutControlGroup14.CustomizationFormText = "LayoutControlGroup2"
        Me.LayoutControlGroup14.GroupBordersVisible = False
        Me.LayoutControlGroup14.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroupEmployees, Me.LayoutControlItem21, Me.SplitterItem4})
        Me.LayoutControlGroup14.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup14.Name = "LayoutControlGroup14"
        Me.LayoutControlGroup14.Size = New System.Drawing.Size(1248, 702)
        Me.LayoutControlGroup14.Text = "LayoutControlGroup2"
        '
        'LayoutControlGroupEmployees
        '
        Me.LayoutControlGroupEmployees.CustomizationFormText = "Employees"
        Me.LayoutControlGroupEmployees.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem25, Me.LayoutControlItem26, Me.LayoutControlItem27})
        Me.LayoutControlGroupEmployees.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroupEmployees.Name = "LayoutControlGroupEmployees"
        Me.LayoutControlGroupEmployees.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroupEmployees.Size = New System.Drawing.Size(291, 702)
        Me.LayoutControlGroupEmployees.Text = "Employees"
        '
        'LayoutControlItem25
        '
        Me.LayoutControlItem25.Control = Me.gcEmployeeList
        Me.LayoutControlItem25.CustomizationFormText = "LayoutControlItem14"
        Me.LayoutControlItem25.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem25.Name = "LayoutControlItem25"
        Me.LayoutControlItem25.Size = New System.Drawing.Size(285, 642)
        Me.LayoutControlItem25.Text = "LayoutControlItem14"
        Me.LayoutControlItem25.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem25.TextVisible = False
        '
        'LayoutControlItem26
        '
        Me.LayoutControlItem26.Control = Me.SearchControl1
        Me.LayoutControlItem26.CustomizationFormText = "LayoutControlItem1"
        Me.LayoutControlItem26.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem26.Name = "LayoutControlItem26"
        Me.LayoutControlItem26.Size = New System.Drawing.Size(220, 36)
        Me.LayoutControlItem26.Text = "LayoutControlItem1"
        Me.LayoutControlItem26.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem26.TextVisible = False
        '
        'LayoutControlItem27
        '
        Me.LayoutControlItem27.Control = Me.ddbFilter
        Me.LayoutControlItem27.CustomizationFormText = "LayoutControlItem2"
        Me.LayoutControlItem27.Location = New System.Drawing.Point(220, 0)
        Me.LayoutControlItem27.Name = "LayoutControlItem27"
        Me.LayoutControlItem27.Size = New System.Drawing.Size(65, 36)
        Me.LayoutControlItem27.Text = "LayoutControlItem2"
        Me.LayoutControlItem27.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem27.TextVisible = False
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.UcEmployeeInfo1
        Me.LayoutControlItem21.Location = New System.Drawing.Point(296, 0)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(952, 702)
        Me.LayoutControlItem21.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem21.TextVisible = False
        '
        'SplitterItem4
        '
        Me.SplitterItem4.AllowHotTrack = True
        Me.SplitterItem4.IsCollapsible = DevExpress.Utils.DefaultBoolean.[True]
        Me.SplitterItem4.Location = New System.Drawing.Point(291, 0)
        Me.SplitterItem4.Name = "SplitterItem4"
        Me.SplitterItem4.Size = New System.Drawing.Size(5, 702)
        '
        'LayoutControl2
        '
        Me.LayoutControl2.AllowCustomization = False
        Me.LayoutControl2.Controls.Add(Me.btnSaveAndCopyPays)
        Me.LayoutControl2.Controls.Add(Me.GridDeds)
        Me.LayoutControl2.Controls.Add(Me.Panel2)
        Me.LayoutControl2.Controls.Add(Me.Panel1)
        Me.LayoutControl2.Controls.Add(Me.GridPays)
        Me.LayoutControl2.Controls.Add(Me.btnCopy)
        Me.LayoutControl2.Controls.Add(Me.btnNewEmployee)
        Me.LayoutControl2.Controls.Add(Me.btnCancel)
        Me.LayoutControl2.Controls.Add(Me.btnSave)
        Me.LayoutControl2.Controls.Add(Me.gcPayrollDeds)
        Me.LayoutControl2.Controls.Add(Me.gcPayrollTaxes)
        Me.LayoutControl2.Controls.Add(Me.gcPayHistory)
        Me.LayoutControl2.Controls.Add(Me.PanelControl1)
        Me.LayoutControl2.Controls.Add(Me.PanelControl2)
        Me.LayoutControl2.Controls.Add(Me.PanelControlPayrollTax)
        Me.LayoutControl2.Controls.Add(Me.PanelControl4)
        Me.LayoutControl2.Controls.Add(Me.PanelControl5)
        Me.LayoutControl2.Controls.Add(Me.PanelControl6)
        Me.LayoutControl2.Controls.Add(Me.lcEmpName)
        Me.LayoutControl2.Controls.Add(Me.gcCheckMaster)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(395, 340, 609, 400)
        Me.LayoutControl2.Root = Me.LayoutControlGroup3
        Me.LayoutControl2.Size = New System.Drawing.Size(1255, 694)
        Me.LayoutControl2.TabIndex = 0
        Me.LayoutControl2.Text = "LayoutControl1"
        '
        'btnSaveAndCopyPays
        '
        Me.btnSaveAndCopyPays.Location = New System.Drawing.Point(741, 12)
        Me.btnSaveAndCopyPays.Name = "btnSaveAndCopyPays"
        Me.btnSaveAndCopyPays.Size = New System.Drawing.Size(161, 22)
        Me.btnSaveAndCopyPays.StyleController = Me.LayoutControl2
        Me.btnSaveAndCopyPays.TabIndex = 19
        Me.btnSaveAndCopyPays.Text = "Save && Copy Pays/Deductions"
        '
        'GridDeds
        '
        Me.GridDeds.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridDeds.Location = New System.Drawing.Point(291, 339)
        Me.GridDeds.MainView = Me.GridView3
        Me.GridDeds.Name = "GridDeds"
        Me.GridDeds.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemTextEdit1})
        Me.GridDeds.Size = New System.Drawing.Size(875, 207)
        Me.GridDeds.TabIndex = 17
        Me.GridDeds.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView3})
        Me.GridDeds.Visible = False
        '
        'GridView3
        '
        Me.GridView3.Appearance.HeaderPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GridView3.Appearance.HeaderPanel.Options.UseFont = True
        Me.GridView3.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn21, Me.GridColumn22, Me.GridColumn23, Me.GridColumn24, Me.GridColumn25, Me.GridColumn26, Me.GridColumn27, Me.GridColumn28, Me.GridColumn29})
        StyleFormatCondition1.Appearance.ForeColor = System.Drawing.Color.Silver
        StyleFormatCondition1.Appearance.Options.HighPriority = True
        StyleFormatCondition1.Appearance.Options.UseForeColor = True
        StyleFormatCondition1.ApplyToRow = True
        StyleFormatCondition1.Column = Me.GridColumn27
        StyleFormatCondition1.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression
        StyleFormatCondition1.Expression = "[IsOpen]  !=  True"
        StyleFormatCondition2.Appearance.BackColor = System.Drawing.Color.Yellow
        StyleFormatCondition2.Appearance.Options.UseBackColor = True
        StyleFormatCondition2.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression
        StyleFormatCondition2.Expression = "Not IsNull([TTOOverride])"
        Me.GridView3.FormatConditions.AddRange(New DevExpress.XtraGrid.StyleFormatCondition() {StyleFormatCondition1, StyleFormatCondition2})
        Me.GridView3.GridControl = Me.GridDeds
        Me.GridView3.Name = "GridView3"
        Me.GridView3.OptionsBehavior.ReadOnly = True
        Me.GridView3.OptionsView.ShowGroupPanel = False
        Me.GridView3.ViewCaption = "Auto Deductions"
        '
        'GridColumn21
        '
        Me.GridColumn21.Caption = "Deduction"
        Me.GridColumn21.FieldName = "CodeDescription"
        Me.GridColumn21.Name = "GridColumn21"
        Me.GridColumn21.OptionsColumn.ReadOnly = True
        Me.GridColumn21.Visible = True
        Me.GridColumn21.VisibleIndex = 0
        Me.GridColumn21.Width = 144
        '
        'GridColumn22
        '
        Me.GridColumn22.FieldName = "DedCalc"
        Me.GridColumn22.Name = "GridColumn22"
        Me.GridColumn22.OptionsColumn.ReadOnly = True
        Me.GridColumn22.Visible = True
        Me.GridColumn22.VisibleIndex = 1
        Me.GridColumn22.Width = 86
        '
        'GridColumn23
        '
        Me.GridColumn23.Caption = "Scheduling"
        Me.GridColumn23.FieldName = "Scheduling"
        Me.GridColumn23.Name = "GridColumn23"
        Me.GridColumn23.Visible = True
        Me.GridColumn23.VisibleIndex = 2
        Me.GridColumn23.Width = 73
        '
        'GridColumn24
        '
        Me.GridColumn24.Caption = "1st Only"
        Me.GridColumn24.FieldName = "FirstCheckOnly"
        Me.GridColumn24.Name = "GridColumn24"
        Me.GridColumn24.OptionsColumn.ReadOnly = True
        Me.GridColumn24.Visible = True
        Me.GridColumn24.VisibleIndex = 3
        Me.GridColumn24.Width = 40
        '
        'GridColumn25
        '
        Me.GridColumn25.Caption = "Aup Pr"
        Me.GridColumn25.FieldName = "SuppPrd"
        Me.GridColumn25.Name = "GridColumn25"
        Me.GridColumn25.Visible = True
        Me.GridColumn25.VisibleIndex = 4
        Me.GridColumn25.Width = 52
        '
        'GridColumn26
        '
        Me.GridColumn26.DisplayFormat.FormatString = "d"
        Me.GridColumn26.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.GridColumn26.FieldName = "StartDate"
        Me.GridColumn26.Name = "GridColumn26"
        Me.GridColumn26.OptionsColumn.ReadOnly = True
        Me.GridColumn26.Visible = True
        Me.GridColumn26.VisibleIndex = 5
        Me.GridColumn26.Width = 88
        '
        'GridColumn28
        '
        Me.GridColumn28.Caption = "Balance"
        Me.GridColumn28.DisplayFormat.FormatString = "c"
        Me.GridColumn28.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn28.FieldName = "Balance"
        Me.GridColumn28.Name = "GridColumn28"
        Me.GridColumn28.OptionsColumn.ReadOnly = True
        Me.GridColumn28.Visible = True
        Me.GridColumn28.VisibleIndex = 7
        Me.GridColumn28.Width = 88
        '
        'GridColumn29
        '
        Me.GridColumn29.Caption = "Sch Amount"
        Me.GridColumn29.FieldName = "FormattedAmount"
        Me.GridColumn29.Name = "GridColumn29"
        Me.GridColumn29.OptionsColumn.ReadOnly = True
        Me.GridColumn29.Visible = True
        Me.GridColumn29.VisibleIndex = 8
        Me.GridColumn29.Width = 98
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Mask.EditMask = "f2"
        Me.RepositoryItemTextEdit1.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.btnEditAutoDeduction)
        Me.Panel2.Controls.Add(Me.LabelControl18)
        Me.Panel2.Controls.Add(Me.btnAddAutoDeduction)
        Me.Panel2.Location = New System.Drawing.Point(291, 305)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(875, 30)
        Me.Panel2.TabIndex = 27
        Me.Panel2.Visible = False
        '
        'btnEditAutoDeduction
        '
        Me.btnEditAutoDeduction.Location = New System.Drawing.Point(279, 4)
        Me.btnEditAutoDeduction.Name = "btnEditAutoDeduction"
        Me.btnEditAutoDeduction.Size = New System.Drawing.Size(125, 23)
        Me.btnEditAutoDeduction.TabIndex = 21
        Me.btnEditAutoDeduction.Text = "Edit Auto Deduction"
        '
        'LabelControl18
        '
        Me.LabelControl18.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!)
        Me.LabelControl18.Appearance.Options.UseFont = True
        Me.LabelControl18.Location = New System.Drawing.Point(22, 5)
        Me.LabelControl18.Name = "LabelControl18"
        Me.LabelControl18.Size = New System.Drawing.Size(117, 19)
        Me.LabelControl18.TabIndex = 18
        Me.LabelControl18.Text = "Auto Deductions"
        '
        'btnAddAutoDeduction
        '
        Me.btnAddAutoDeduction.Location = New System.Drawing.Point(146, 4)
        Me.btnAddAutoDeduction.Name = "btnAddAutoDeduction"
        Me.btnAddAutoDeduction.Size = New System.Drawing.Size(127, 23)
        Me.btnAddAutoDeduction.TabIndex = 19
        Me.btnAddAutoDeduction.Text = "Add Auto Deduction"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.btnEditAutoPay)
        Me.Panel1.Controls.Add(Me.btnAddAutoPay)
        Me.Panel1.Controls.Add(Me.LabelControl17)
        Me.Panel1.Location = New System.Drawing.Point(291, 84)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(875, 33)
        Me.Panel1.TabIndex = 26
        Me.Panel1.Visible = False
        '
        'btnEditAutoPay
        '
        Me.btnEditAutoPay.Location = New System.Drawing.Point(253, 7)
        Me.btnEditAutoPay.Name = "btnEditAutoPay"
        Me.btnEditAutoPay.Size = New System.Drawing.Size(101, 23)
        Me.btnEditAutoPay.TabIndex = 20
        Me.btnEditAutoPay.Text = "Edit Auto Pay"
        '
        'btnAddAutoPay
        '
        Me.btnAddAutoPay.Location = New System.Drawing.Point(146, 7)
        Me.btnAddAutoPay.Name = "btnAddAutoPay"
        Me.btnAddAutoPay.Size = New System.Drawing.Size(101, 23)
        Me.btnAddAutoPay.TabIndex = 16
        Me.btnAddAutoPay.Text = "Add Auto Pay"
        '
        'LabelControl17
        '
        Me.LabelControl17.Appearance.Font = New System.Drawing.Font("Tahoma", 12.0!)
        Me.LabelControl17.Appearance.Options.UseFont = True
        Me.LabelControl17.Location = New System.Drawing.Point(22, 7)
        Me.LabelControl17.Name = "LabelControl17"
        Me.LabelControl17.Size = New System.Drawing.Size(71, 19)
        Me.LabelControl17.TabIndex = 15
        Me.LabelControl17.Text = "Auto Pays"
        '
        'GridPays
        '
        Me.GridPays.Location = New System.Drawing.Point(291, 121)
        Me.GridPays.MainView = Me.GridView4
        Me.GridPays.Name = "GridPays"
        Me.GridPays.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemLookUpEdit1})
        Me.GridPays.Size = New System.Drawing.Size(875, 180)
        Me.GridPays.TabIndex = 14
        Me.GridPays.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView4})
        Me.GridPays.Visible = False
        '
        'GridView4
        '
        Me.GridView4.Appearance.HeaderPanel.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GridView4.Appearance.HeaderPanel.Options.UseFont = True
        Me.GridView4.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn30, Me.GridColumn31, Me.GridColumn32, Me.GridColumn33, Me.GridColumn34, Me.GridColumn35, Me.GridColumn36, Me.GridColumn37, Me.GridColumn38})
        GridFormatRule1.ApplyToRow = True
        GridFormatRule1.Column = Me.GridColumn36
        GridFormatRule1.Name = "Format0"
        FormatConditionRuleExpression1.Appearance.ForeColor = System.Drawing.Color.Silver
        FormatConditionRuleExpression1.Appearance.Options.HighPriority = True
        FormatConditionRuleExpression1.Appearance.Options.UseForeColor = True
        FormatConditionRuleExpression1.Expression = "[IsOpen]  !=  True"
        GridFormatRule1.Rule = FormatConditionRuleExpression1
        Me.GridView4.FormatRules.Add(GridFormatRule1)
        Me.GridView4.GridControl = Me.GridPays
        Me.GridView4.Name = "GridView4"
        Me.GridView4.OptionsBehavior.ReadOnly = True
        Me.GridView4.OptionsView.ShowGroupPanel = False
        Me.GridView4.ViewCaption = "Auto Pays"
        '
        'GridColumn30
        '
        Me.GridColumn30.Caption = "Earning"
        Me.GridColumn30.ColumnEdit = Me.RepositoryItemLookUpEdit1
        Me.GridColumn30.FieldName = "CL_Code"
        Me.GridColumn30.Name = "GridColumn30"
        Me.GridColumn30.OptionsColumn.ReadOnly = True
        Me.GridColumn30.Visible = True
        Me.GridColumn30.VisibleIndex = 0
        Me.GridColumn30.Width = 144
        '
        'RepositoryItemLookUpEdit1
        '
        Me.RepositoryItemLookUpEdit1.AutoHeight = False
        Me.RepositoryItemLookUpEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemLookUpEdit1.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_DESC", "Earning", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CATEGORY", "Category", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.RepositoryItemLookUpEdit1.DisplayMember = "OTH_PAY_DESC"
        Me.RepositoryItemLookUpEdit1.Name = "RepositoryItemLookUpEdit1"
        Me.RepositoryItemLookUpEdit1.NullText = ""
        Me.RepositoryItemLookUpEdit1.ValueMember = "OTH_PAY_NUM"
        '
        'GridColumn31
        '
        Me.GridColumn31.FieldName = "Category"
        Me.GridColumn31.Name = "GridColumn31"
        Me.GridColumn31.OptionsColumn.ReadOnly = True
        Me.GridColumn31.Visible = True
        Me.GridColumn31.VisibleIndex = 1
        Me.GridColumn31.Width = 86
        '
        'GridColumn32
        '
        Me.GridColumn32.Caption = "Scheduling"
        Me.GridColumn32.FieldName = "Scheduling"
        Me.GridColumn32.Name = "GridColumn32"
        Me.GridColumn32.OptionsColumn.ReadOnly = True
        Me.GridColumn32.Visible = True
        Me.GridColumn32.VisibleIndex = 2
        Me.GridColumn32.Width = 70
        '
        'GridColumn33
        '
        Me.GridColumn33.Caption = "1st Only"
        Me.GridColumn33.FieldName = "FirstCheckOnly"
        Me.GridColumn33.Name = "GridColumn33"
        Me.GridColumn33.OptionsColumn.ReadOnly = True
        Me.GridColumn33.Visible = True
        Me.GridColumn33.VisibleIndex = 3
        Me.GridColumn33.Width = 40
        '
        'GridColumn34
        '
        Me.GridColumn34.Caption = "Sup Pr"
        Me.GridColumn34.FieldName = "SuppPrd"
        Me.GridColumn34.Name = "GridColumn34"
        Me.GridColumn34.OptionsColumn.ReadOnly = True
        Me.GridColumn34.Visible = True
        Me.GridColumn34.VisibleIndex = 4
        Me.GridColumn34.Width = 52
        '
        'GridColumn35
        '
        Me.GridColumn35.FieldName = "Rate"
        Me.GridColumn35.Name = "GridColumn35"
        Me.GridColumn35.OptionsColumn.ReadOnly = True
        Me.GridColumn35.Visible = True
        Me.GridColumn35.VisibleIndex = 5
        Me.GridColumn35.Width = 89
        '
        'GridColumn37
        '
        Me.GridColumn37.Caption = "Balance"
        Me.GridColumn37.DisplayFormat.FormatString = "c"
        Me.GridColumn37.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn37.FieldName = "Balance"
        Me.GridColumn37.Name = "GridColumn37"
        Me.GridColumn37.OptionsColumn.ReadOnly = True
        Me.GridColumn37.Visible = True
        Me.GridColumn37.VisibleIndex = 7
        Me.GridColumn37.Width = 89
        '
        'GridColumn38
        '
        Me.GridColumn38.Caption = "Sch Amount"
        Me.GridColumn38.DisplayFormat.FormatString = "c"
        Me.GridColumn38.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn38.FieldName = "Amount"
        Me.GridColumn38.Name = "GridColumn38"
        Me.GridColumn38.OptionsColumn.ReadOnly = True
        Me.GridColumn38.Visible = True
        Me.GridColumn38.VisibleIndex = 8
        Me.GridColumn38.Width = 98
        '
        'btnCopy
        '
        Me.btnCopy.Location = New System.Drawing.Point(1128, 12)
        Me.btnCopy.Name = "btnCopy"
        Me.btnCopy.Size = New System.Drawing.Size(115, 22)
        Me.btnCopy.StyleController = Me.LayoutControl2
        Me.btnCopy.TabIndex = 11
        Me.btnCopy.Text = "Copy Emp From..."
        '
        'btnNewEmployee
        '
        Me.btnNewEmployee.Location = New System.Drawing.Point(1026, 12)
        Me.btnNewEmployee.Name = "btnNewEmployee"
        Me.btnNewEmployee.Size = New System.Drawing.Size(98, 22)
        Me.btnNewEmployee.StyleController = Me.LayoutControl2
        Me.btnNewEmployee.TabIndex = 18
        Me.btnNewEmployee.Text = "New Employee"
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.Location = New System.Drawing.Point(962, 12)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(60, 22)
        Me.btnCancel.StyleController = Me.LayoutControl2
        Me.btnCancel.TabIndex = 16
        Me.btnCancel.Text = "Cancel"
        '
        'btnSave
        '
        Me.btnSave.Location = New System.Drawing.Point(906, 12)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Size = New System.Drawing.Size(52, 22)
        Me.btnSave.StyleController = Me.LayoutControl2
        Me.btnSave.TabIndex = 17
        Me.btnSave.Text = "Save"
        '
        'gcPayrollDeds
        '
        Me.gcPayrollDeds.Location = New System.Drawing.Point(1029, 408)
        Me.gcPayrollDeds.MainView = Me.GridView5
        Me.gcPayrollDeds.Name = "gcPayrollDeds"
        Me.gcPayrollDeds.Size = New System.Drawing.Size(201, 112)
        Me.gcPayrollDeds.TabIndex = 19
        Me.gcPayrollDeds.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView5})
        Me.gcPayrollDeds.Visible = False
        '
        'GridView5
        '
        Me.GridView5.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn39, Me.GridColumn40})
        Me.GridView5.GridControl = Me.gcPayrollDeds
        Me.GridView5.Name = "GridView5"
        Me.GridView5.OptionsBehavior.Editable = False
        Me.GridView5.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView5.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.GridView5.OptionsView.ShowFooter = True
        Me.GridView5.OptionsView.ShowGroupPanel = False
        Me.GridView5.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView5.OptionsView.ShowIndicator = False
        Me.GridView5.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'GridColumn39
        '
        Me.GridColumn39.Caption = "Deduction"
        Me.GridColumn39.FieldName = "Description"
        Me.GridColumn39.Name = "GridColumn39"
        Me.GridColumn39.Visible = True
        Me.GridColumn39.VisibleIndex = 0
        '
        'GridColumn40
        '
        Me.GridColumn40.FieldName = "Amount"
        Me.GridColumn40.Name = "GridColumn40"
        Me.GridColumn40.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Amount", "{0:0.##}")})
        Me.GridColumn40.Visible = True
        Me.GridColumn40.VisibleIndex = 1
        '
        'gcPayrollTaxes
        '
        Me.gcPayrollTaxes.Location = New System.Drawing.Point(1029, 529)
        Me.gcPayrollTaxes.MainView = Me.GridView6
        Me.gcPayrollTaxes.Name = "gcPayrollTaxes"
        Me.gcPayrollTaxes.Size = New System.Drawing.Size(201, 140)
        Me.gcPayrollTaxes.TabIndex = 18
        Me.gcPayrollTaxes.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView6})
        Me.gcPayrollTaxes.Visible = False
        '
        'GridView6
        '
        Me.GridView6.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial Narrow", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView6.Appearance.HeaderPanel.Options.UseFont = True
        Me.GridView6.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn41, Me.GridColumn42, Me.GridColumn43, Me.GridColumn44, Me.GridColumn45})
        Me.GridView6.GridControl = Me.gcPayrollTaxes
        Me.GridView6.GroupCount = 1
        Me.GridView6.GroupFormat = "{1} Taxes"
        Me.GridView6.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Amount", Me.GridColumn45, "{0:N2}")})
        Me.GridView6.Name = "GridView6"
        Me.GridView6.OptionsBehavior.AutoExpandAllGroups = True
        Me.GridView6.OptionsBehavior.Editable = False
        Me.GridView6.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView6.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.GridView6.OptionsView.ShowGroupExpandCollapseButtons = False
        Me.GridView6.OptionsView.ShowGroupPanel = False
        Me.GridView6.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView6.OptionsView.ShowIndicator = False
        Me.GridView6.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView6.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.GridColumn41, DevExpress.Data.ColumnSortOrder.Ascending)})
        '
        'GridColumn41
        '
        Me.GridColumn41.Caption = "Category"
        Me.GridColumn41.FieldName = "Category"
        Me.GridColumn41.Name = "GridColumn41"
        '
        'GridColumn42
        '
        Me.GridColumn42.FieldName = "Description"
        Me.GridColumn42.Name = "GridColumn42"
        Me.GridColumn42.Visible = True
        Me.GridColumn42.VisibleIndex = 0
        Me.GridColumn42.Width = 110
        '
        'GridColumn43
        '
        Me.GridColumn43.DisplayFormat.FormatString = "N2"
        Me.GridColumn43.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn43.FieldName = "Taxable"
        Me.GridColumn43.Name = "GridColumn43"
        Me.GridColumn43.Visible = True
        Me.GridColumn43.VisibleIndex = 1
        '
        'GridColumn44
        '
        Me.GridColumn44.DisplayFormat.FormatString = "N2"
        Me.GridColumn44.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn44.FieldName = "Capped"
        Me.GridColumn44.Name = "GridColumn44"
        Me.GridColumn44.Visible = True
        Me.GridColumn44.VisibleIndex = 2
        Me.GridColumn44.Width = 29
        '
        'GridColumn45
        '
        Me.GridColumn45.DisplayFormat.FormatString = "N2"
        Me.GridColumn45.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn45.FieldName = "Amount"
        Me.GridColumn45.Name = "GridColumn45"
        Me.GridColumn45.Visible = True
        Me.GridColumn45.VisibleIndex = 3
        Me.GridColumn45.Width = 31
        '
        'gcPayHistory
        '
        Me.gcPayHistory.Location = New System.Drawing.Point(291, 408)
        Me.gcPayHistory.MainView = Me.GridView7
        Me.gcPayHistory.Name = "gcPayHistory"
        Me.gcPayHistory.Size = New System.Drawing.Size(709, 261)
        Me.gcPayHistory.TabIndex = 17
        Me.gcPayHistory.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView7})
        Me.gcPayHistory.Visible = False
        '
        'GridView7
        '
        Me.GridView7.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial Narrow", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView7.Appearance.HeaderPanel.Options.UseFont = True
        Me.GridView7.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn46, Me.GridColumn47, Me.GridColumn48, Me.GridColumn49, Me.GridColumn50, Me.GridColumn51, Me.GridColumn52, Me.GridColumn53, Me.GridColumn54, Me.GridColumn55, Me.GridColumn56})
        Me.GridView7.GridControl = Me.gcPayHistory
        Me.GridView7.Name = "GridView7"
        Me.GridView7.OptionsBehavior.Editable = False
        Me.GridView7.OptionsView.ShowFooter = True
        Me.GridView7.OptionsView.ShowGroupPanel = False
        Me.GridView7.OptionsView.ShowIndicator = False
        Me.GridView7.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        '
        'GridColumn46
        '
        Me.GridColumn46.FieldName = "Type"
        Me.GridColumn46.Name = "GridColumn46"
        Me.GridColumn46.Visible = True
        Me.GridColumn46.VisibleIndex = 0
        '
        'GridColumn47
        '
        Me.GridColumn47.FieldName = "Earnings"
        Me.GridColumn47.Name = "GridColumn47"
        Me.GridColumn47.Visible = True
        Me.GridColumn47.VisibleIndex = 1
        '
        'GridColumn48
        '
        Me.GridColumn48.FieldName = "Department"
        Me.GridColumn48.Name = "GridColumn48"
        Me.GridColumn48.Visible = True
        Me.GridColumn48.VisibleIndex = 2
        '
        'GridColumn49
        '
        Me.GridColumn49.FieldName = "WP"
        Me.GridColumn49.Name = "GridColumn49"
        Me.GridColumn49.Visible = True
        Me.GridColumn49.VisibleIndex = 3
        '
        'GridColumn50
        '
        Me.GridColumn50.FieldName = "Job"
        Me.GridColumn50.Name = "GridColumn50"
        Me.GridColumn50.Visible = True
        Me.GridColumn50.VisibleIndex = 4
        '
        'GridColumn51
        '
        Me.GridColumn51.FieldName = "BeginDate"
        Me.GridColumn51.Name = "GridColumn51"
        Me.GridColumn51.Visible = True
        Me.GridColumn51.VisibleIndex = 5
        '
        'GridColumn52
        '
        Me.GridColumn52.FieldName = "EndDate"
        Me.GridColumn52.Name = "GridColumn52"
        Me.GridColumn52.Visible = True
        Me.GridColumn52.VisibleIndex = 6
        '
        'GridColumn53
        '
        Me.GridColumn53.FieldName = "Comment"
        Me.GridColumn53.Name = "GridColumn53"
        Me.GridColumn53.Visible = True
        Me.GridColumn53.VisibleIndex = 7
        '
        'GridColumn54
        '
        Me.GridColumn54.DisplayFormat.FormatString = "N2"
        Me.GridColumn54.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn54.FieldName = "Hours"
        Me.GridColumn54.Name = "GridColumn54"
        Me.GridColumn54.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Hours", "{0:0.##}")})
        Me.GridColumn54.Visible = True
        Me.GridColumn54.VisibleIndex = 8
        '
        'GridColumn55
        '
        Me.GridColumn55.DisplayFormat.FormatString = "N4"
        Me.GridColumn55.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn55.FieldName = "Rate"
        Me.GridColumn55.Name = "GridColumn55"
        Me.GridColumn55.Visible = True
        Me.GridColumn55.VisibleIndex = 9
        '
        'GridColumn56
        '
        Me.GridColumn56.DisplayFormat.FormatString = "N2"
        Me.GridColumn56.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn56.FieldName = "Amount"
        Me.GridColumn56.Name = "GridColumn56"
        Me.GridColumn56.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "Amount", "{0:0.##}")})
        Me.GridColumn56.Visible = True
        Me.GridColumn56.VisibleIndex = 10
        '
        'PanelControl1
        '
        Me.PanelControl1.Controls.Add(Me.btnYear)
        Me.PanelControl1.Controls.Add(Me.btnLastYear)
        Me.PanelControl1.Controls.Add(Me.btnQ4)
        Me.PanelControl1.Controls.Add(Me.btnQ3)
        Me.PanelControl1.Controls.Add(Me.btnQ2)
        Me.PanelControl1.Controls.Add(Me.btnQ1)
        Me.PanelControl1.Location = New System.Drawing.Point(1129, 84)
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(101, 315)
        Me.PanelControl1.TabIndex = 16
        Me.PanelControl1.Visible = False
        '
        'btnYear
        '
        Me.btnYear.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnYear.Location = New System.Drawing.Point(8, 300)
        Me.btnYear.Name = "btnYear"
        Me.btnYear.Size = New System.Drawing.Size(88, 22)
        Me.btnYear.TabIndex = 14
        Me.btnYear.Text = "Year"
        '
        'btnLastYear
        '
        Me.btnLastYear.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnLastYear.Location = New System.Drawing.Point(8, 251)
        Me.btnLastYear.Name = "btnLastYear"
        Me.btnLastYear.Size = New System.Drawing.Size(88, 22)
        Me.btnLastYear.TabIndex = 13
        Me.btnLastYear.Text = "Last Year"
        '
        'btnQ4
        '
        Me.btnQ4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnQ4.Location = New System.Drawing.Point(8, 188)
        Me.btnQ4.Name = "btnQ4"
        Me.btnQ4.Size = New System.Drawing.Size(88, 22)
        Me.btnQ4.TabIndex = 12
        Me.btnQ4.Text = "Q4"
        '
        'btnQ3
        '
        Me.btnQ3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnQ3.Location = New System.Drawing.Point(8, 125)
        Me.btnQ3.Name = "btnQ3"
        Me.btnQ3.Size = New System.Drawing.Size(88, 22)
        Me.btnQ3.TabIndex = 11
        Me.btnQ3.Text = "Q3"
        '
        'btnQ2
        '
        Me.btnQ2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnQ2.Location = New System.Drawing.Point(8, 64)
        Me.btnQ2.Name = "btnQ2"
        Me.btnQ2.Size = New System.Drawing.Size(88, 22)
        Me.btnQ2.TabIndex = 10
        Me.btnQ2.Text = "Q2"
        '
        'btnQ1
        '
        Me.btnQ1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnQ1.Location = New System.Drawing.Point(8, 9)
        Me.btnQ1.Name = "btnQ1"
        Me.btnQ1.Size = New System.Drawing.Size(88, 22)
        Me.btnQ1.TabIndex = 9
        Me.btnQ1.Text = "Q1"
        '
        'PanelControl2
        '
        Me.PanelControl2.Controls.Add(Me.btnParsonage)
        Me.PanelControl2.Controls.Add(Me.pnlTerminate)
        Me.PanelControl2.Controls.Add(Me.PanelControl8)
        Me.PanelControl2.Controls.Add(Me.GroupControl3)
        Me.PanelControl2.Controls.Add(Me.GroupControl1)
        Me.PanelControl2.Controls.Add(Me.GroupControl2)
        Me.PanelControl2.Location = New System.Drawing.Point(291, 84)
        Me.PanelControl2.Name = "PanelControl2"
        Me.PanelControl2.Size = New System.Drawing.Size(939, 585)
        Me.PanelControl2.TabIndex = 20
        Me.PanelControl2.Visible = False
        '
        'btnParsonage
        '
        Me.btnParsonage.Location = New System.Drawing.Point(711, 98)
        Me.btnParsonage.Name = "btnParsonage"
        Me.btnParsonage.Size = New System.Drawing.Size(150, 23)
        Me.btnParsonage.TabIndex = 10
        Me.btnParsonage.Text = "Mark Emp as Parsonage"
        '
        'pnlTerminate
        '
        Me.pnlTerminate.Controls.Add(Me.lblEmployeeStatus)
        Me.pnlTerminate.Controls.Add(Me.btnTerminate)
        Me.pnlTerminate.Location = New System.Drawing.Point(711, 57)
        Me.pnlTerminate.Name = "pnlTerminate"
        Me.pnlTerminate.Size = New System.Drawing.Size(175, 28)
        Me.pnlTerminate.TabIndex = 7
        '
        'lblEmployeeStatus
        '
        Me.lblEmployeeStatus.Location = New System.Drawing.Point(76, 7)
        Me.lblEmployeeStatus.Name = "lblEmployeeStatus"
        Me.lblEmployeeStatus.Size = New System.Drawing.Size(30, 13)
        Me.lblEmployeeStatus.TabIndex = 1
        Me.lblEmployeeStatus.Text = "Active"
        '
        'btnTerminate
        '
        Me.btnTerminate.Location = New System.Drawing.Point(5, 5)
        Me.btnTerminate.Name = "btnTerminate"
        Me.btnTerminate.Size = New System.Drawing.Size(65, 19)
        Me.btnTerminate.TabIndex = 0
        Me.btnTerminate.Text = "Terminate"
        '
        'PanelControl8
        '
        Me.PanelControl8.Controls.Add(Me.EMPNUMTextEdit)
        Me.PanelControl8.Location = New System.Drawing.Point(711, 15)
        Me.PanelControl8.Name = "PanelControl8"
        Me.PanelControl8.Size = New System.Drawing.Size(175, 28)
        Me.PanelControl8.TabIndex = 6
        '
        'EMPNUMTextEdit
        '
        Me.EMPNUMTextEdit.Location = New System.Drawing.Point(90, 4)
        Me.EMPNUMTextEdit.Name = "EMPNUMTextEdit"
        Me.EMPNUMTextEdit.Size = New System.Drawing.Size(75, 20)
        Me.EMPNUMTextEdit.TabIndex = 2
        '
        'GroupControl3
        '
        Me.GroupControl3.Controls.Add(Me.Default_hoursTextEdit)
        Me.GroupControl3.Controls.Add(Me.SALARY_AMTTextEdit)
        Me.GroupControl3.Controls.Add(Me.RATE_3TextEdit)
        Me.GroupControl3.Controls.Add(Me.RATE_2TextEdit)
        Me.GroupControl3.Controls.Add(Me.RATE_1TextEdit)
        Me.GroupControl3.Controls.Add(Me.PAY_FREQTextEdit)
        Me.GroupControl3.Controls.Add(Me.AUTO_SAL_HRSTextEdit)
        Me.GroupControl3.Controls.Add(Me.TextEdit4)
        Me.GroupControl3.Controls.Add(Me.TextEdit3)
        Me.GroupControl3.Controls.Add(Me.TextEdit2)
        Me.GroupControl3.Controls.Add(Me.TextEdit1)
        Me.GroupControl3.Location = New System.Drawing.Point(359, 215)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.Size = New System.Drawing.Size(335, 301)
        Me.GroupControl3.TabIndex = 5
        Me.GroupControl3.Text = "Pay"
        '
        'Default_hoursTextEdit
        '
        Me.Default_hoursTextEdit.Location = New System.Drawing.Point(256, 31)
        Me.Default_hoursTextEdit.Name = "Default_hoursTextEdit"
        Me.Default_hoursTextEdit.Properties.Mask.EditMask = "f2"
        Me.Default_hoursTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Default_hoursTextEdit.Size = New System.Drawing.Size(58, 20)
        Me.Default_hoursTextEdit.TabIndex = 11
        '
        'SALARY_AMTTextEdit
        '
        Me.SALARY_AMTTextEdit.Location = New System.Drawing.Point(93, 134)
        Me.SALARY_AMTTextEdit.Name = "SALARY_AMTTextEdit"
        Me.SALARY_AMTTextEdit.Properties.Mask.EditMask = "c"
        Me.SALARY_AMTTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.SALARY_AMTTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.SALARY_AMTTextEdit.TabIndex = 7
        '
        'RATE_3TextEdit
        '
        Me.RATE_3TextEdit.Location = New System.Drawing.Point(93, 108)
        Me.RATE_3TextEdit.Name = "RATE_3TextEdit"
        Me.RATE_3TextEdit.Size = New System.Drawing.Size(107, 20)
        Me.RATE_3TextEdit.TabIndex = 5
        '
        'RATE_2TextEdit
        '
        Me.RATE_2TextEdit.Location = New System.Drawing.Point(93, 82)
        Me.RATE_2TextEdit.Name = "RATE_2TextEdit"
        Me.RATE_2TextEdit.Size = New System.Drawing.Size(107, 20)
        Me.RATE_2TextEdit.TabIndex = 3
        '
        'RATE_1TextEdit
        '
        Me.RATE_1TextEdit.Location = New System.Drawing.Point(93, 56)
        Me.RATE_1TextEdit.Name = "RATE_1TextEdit"
        Me.RATE_1TextEdit.Properties.Mask.EditMask = "f4"
        Me.RATE_1TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.RATE_1TextEdit.Size = New System.Drawing.Size(107, 20)
        Me.RATE_1TextEdit.TabIndex = 1
        '
        'PAY_FREQTextEdit
        '
        Me.PAY_FREQTextEdit.Location = New System.Drawing.Point(93, 30)
        Me.PAY_FREQTextEdit.Name = "PAY_FREQTextEdit"
        Me.PAY_FREQTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.PAY_FREQTextEdit.Properties.Items.AddRange(New Object() {"Weekly", "Bi-Weekly", "Semi-Monthly", "Monthly", "Quarterly", "Annually"})
        Me.PAY_FREQTextEdit.Size = New System.Drawing.Size(107, 20)
        Me.PAY_FREQTextEdit.TabIndex = 9
        '
        'AUTO_SAL_HRSTextEdit
        '
        Me.AUTO_SAL_HRSTextEdit.EditValue = Nothing
        Me.AUTO_SAL_HRSTextEdit.Location = New System.Drawing.Point(131, 164)
        Me.AUTO_SAL_HRSTextEdit.Name = "AUTO_SAL_HRSTextEdit"
        Me.AUTO_SAL_HRSTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.AUTO_SAL_HRSTextEdit.Properties.Caption = ""
        Me.AUTO_SAL_HRSTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.AUTO_SAL_HRSTextEdit.Properties.ValueChecked = "YES"
        Me.AUTO_SAL_HRSTextEdit.Properties.ValueUnchecked = "NO"
        Me.AUTO_SAL_HRSTextEdit.Size = New System.Drawing.Size(22, 19)
        Me.AUTO_SAL_HRSTextEdit.TabIndex = 13
        '
        'TextEdit4
        '
        Me.TextEdit4.EditValue = Nothing
        Me.TextEdit4.Location = New System.Drawing.Point(210, 57)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit4.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit4.Properties.Mask.EditMask = "f4"
        Me.TextEdit4.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEdit4.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit4.TabIndex = 15
        '
        'TextEdit3
        '
        Me.TextEdit3.EditValue = Nothing
        Me.TextEdit3.Location = New System.Drawing.Point(210, 82)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit3.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit3.Properties.Mask.EditMask = ""
        Me.TextEdit3.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit3.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit3.TabIndex = 17
        '
        'TextEdit2
        '
        Me.TextEdit2.EditValue = Nothing
        Me.TextEdit2.Location = New System.Drawing.Point(210, 108)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit2.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit2.Properties.Mask.EditMask = ""
        Me.TextEdit2.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None
        Me.TextEdit2.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit2.TabIndex = 19
        '
        'TextEdit1
        '
        Me.TextEdit1.EditValue = Nothing
        Me.TextEdit1.Location = New System.Drawing.Point(210, 134)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit1.Properties.Mask.EditMask = "c"
        Me.TextEdit1.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEdit1.Size = New System.Drawing.Size(104, 20)
        Me.TextEdit1.TabIndex = 21
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.GroupControl11)
        Me.GroupControl1.Controls.Add(Me.SSNLabelControl)
        Me.GroupControl1.Controls.Add(Me.TINTextEdit)
        Me.GroupControl1.Controls.Add(Me.SSNTextEdit)
        Me.GroupControl1.Controls.Add(Me.M_NAMETextEdit)
        Me.GroupControl1.Controls.Add(Me.CITYTextEdit)
        Me.GroupControl1.Controls.Add(Me.ADDR_STATETextEdit)
        Me.GroupControl1.Controls.Add(Me.ZIPTextEdit)
        Me.GroupControl1.Controls.Add(Me.Contact_pagerTextEdit)
        Me.GroupControl1.Controls.Add(Me.GENDERLookUpEdit)
        Me.GroupControl1.Controls.Add(Me.Contact_homephoneTextEdit)
        Me.GroupControl1.Controls.Add(Me.B_DAYDateEdit)
        Me.GroupControl1.Controls.Add(Me.F_NAMETextEdit)
        Me.GroupControl1.Controls.Add(Me.Contact_homeemailTextEdit)
        Me.GroupControl1.Controls.Add(Me.User_emailTextEdit)
        Me.GroupControl1.Controls.Add(Me.STREETTextEdit)
        Me.GroupControl1.Controls.Add(Me.L_NAMETextEdit)
        Me.GroupControl1.Controls.Add(Me.TINLabel)
        Me.GroupControl1.Location = New System.Drawing.Point(14, 15)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(329, 501)
        Me.GroupControl1.TabIndex = 3
        Me.GroupControl1.Text = "Personal"
        '
        'GroupControl11
        '
        Me.GroupControl11.Controls.Add(Me.LabelControl16)
        Me.GroupControl11.Controls.Add(Me.LabelControl15)
        Me.GroupControl11.Controls.Add(Me.ComboBoxEdit2)
        Me.GroupControl11.Controls.Add(Me.ComboBoxEdit1)
        Me.GroupControl11.Location = New System.Drawing.Point(25, 367)
        Me.GroupControl11.Name = "GroupControl11"
        Me.GroupControl11.Size = New System.Drawing.Size(285, 104)
        Me.GroupControl11.TabIndex = 35
        Me.GroupControl11.Text = "ACA Status"
        '
        'LabelControl16
        '
        Me.LabelControl16.Location = New System.Drawing.Point(13, 53)
        Me.LabelControl16.Name = "LabelControl16"
        Me.LabelControl16.Size = New System.Drawing.Size(62, 13)
        Me.LabelControl16.TabIndex = 3
        Me.LabelControl16.Text = "ACA Status: "
        '
        'LabelControl15
        '
        Me.LabelControl15.Location = New System.Drawing.Point(13, 26)
        Me.LabelControl15.Name = "LabelControl15"
        Me.LabelControl15.Size = New System.Drawing.Size(59, 13)
        Me.LabelControl15.TabIndex = 2
        Me.LabelControl15.Text = "Pay Status: "
        '
        'ComboBoxEdit2
        '
        Me.ComboBoxEdit2.Location = New System.Drawing.Point(78, 50)
        Me.ComboBoxEdit2.Name = "ComboBoxEdit2"
        Me.ComboBoxEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit2.Properties.Items.AddRange(New Object() {"Full-Time", "Part-Time", "Seasonal", "Variable"})
        Me.ComboBoxEdit2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit2.Size = New System.Drawing.Size(194, 20)
        Me.ComboBoxEdit2.TabIndex = 1
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(78, 23)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.Items.AddRange(New Object() {"Hourly", "Salary", "Salary Non-Exempt"})
        Me.ComboBoxEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(194, 20)
        Me.ComboBoxEdit1.TabIndex = 0
        '
        'SSNLabelControl
        '
        Me.SSNLabelControl.Location = New System.Drawing.Point(60, 200)
        Me.SSNLabelControl.Name = "SSNLabelControl"
        Me.SSNLabelControl.Size = New System.Drawing.Size(26, 13)
        Me.SSNLabelControl.TabIndex = 27
        Me.SSNLabelControl.Text = "SSN: "
        '
        'TINTextEdit
        '
        Me.TINTextEdit.Location = New System.Drawing.Point(92, 192)
        Me.TINTextEdit.Name = "TINTextEdit"
        Me.TINTextEdit.Properties.Mask.EditMask = "00-0000000"
        Me.TINTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.TINTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TINTextEdit.Properties.ValidateOnEnterKey = True
        Me.TINTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.TINTextEdit.TabIndex = 25
        '
        'SSNTextEdit
        '
        Me.SSNTextEdit.Location = New System.Drawing.Point(91, 197)
        Me.SSNTextEdit.Name = "SSNTextEdit"
        Me.SSNTextEdit.Properties.Mask.EditMask = "***********"
        Me.SSNTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.SSNTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.SSNTextEdit.Properties.ValidateOnEnterKey = True
        Me.SSNTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.SSNTextEdit.TabIndex = 5
        '
        'M_NAMETextEdit
        '
        Me.M_NAMETextEdit.Location = New System.Drawing.Point(281, 28)
        Me.M_NAMETextEdit.Name = "M_NAMETextEdit"
        Me.M_NAMETextEdit.Properties.Mask.EditMask = "[A-Z]{1}\."
        Me.M_NAMETextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.M_NAMETextEdit.Properties.ValidateOnEnterKey = True
        Me.M_NAMETextEdit.Size = New System.Drawing.Size(29, 20)
        Me.M_NAMETextEdit.TabIndex = 1
        '
        'CITYTextEdit
        '
        Me.CITYTextEdit.Location = New System.Drawing.Point(91, 159)
        Me.CITYTextEdit.Name = "CITYTextEdit"
        Me.CITYTextEdit.Properties.ValidateOnEnterKey = True
        Me.CITYTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.CITYTextEdit.TabIndex = 11
        '
        'ADDR_STATETextEdit
        '
        Me.ADDR_STATETextEdit.Location = New System.Drawing.Point(197, 159)
        Me.ADDR_STATETextEdit.Name = "ADDR_STATETextEdit"
        Me.ADDR_STATETextEdit.Properties.Mask.BeepOnError = True
        Me.ADDR_STATETextEdit.Properties.Mask.EditMask = "\p{Lu}{2}"
        Me.ADDR_STATETextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ADDR_STATETextEdit.Properties.MaxLength = 2
        Me.ADDR_STATETextEdit.Properties.ValidateOnEnterKey = True
        Me.ADDR_STATETextEdit.Size = New System.Drawing.Size(27, 20)
        Me.ADDR_STATETextEdit.TabIndex = 12
        '
        'ZIPTextEdit
        '
        Me.ZIPTextEdit.Location = New System.Drawing.Point(230, 159)
        Me.ZIPTextEdit.Name = "ZIPTextEdit"
        Me.ZIPTextEdit.Properties.Mask.EditMask = "\d{5}|(\d{5}-\d{4})"
        Me.ZIPTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.ZIPTextEdit.Properties.Mask.ShowPlaceHolders = False
        Me.ZIPTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.ZIPTextEdit.Properties.ValidateOnEnterKey = True
        Me.ZIPTextEdit.Size = New System.Drawing.Size(80, 20)
        Me.ZIPTextEdit.TabIndex = 4
        '
        'Contact_pagerTextEdit
        '
        Me.Contact_pagerTextEdit.Location = New System.Drawing.Point(91, 337)
        Me.Contact_pagerTextEdit.Name = "Contact_pagerTextEdit"
        Me.Contact_pagerTextEdit.Properties.ValidateOnEnterKey = True
        Me.Contact_pagerTextEdit.Size = New System.Drawing.Size(217, 20)
        Me.Contact_pagerTextEdit.TabIndex = 10
        '
        'GENDERLookUpEdit
        '
        Me.GENDERLookUpEdit.Location = New System.Drawing.Point(91, 90)
        Me.GENDERLookUpEdit.Name = "GENDERLookUpEdit"
        Me.GENDERLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.GENDERLookUpEdit.Properties.DisplayMember = "Value"
        Me.GENDERLookUpEdit.Properties.NullText = ""
        Me.GENDERLookUpEdit.Properties.SearchMode = DevExpress.XtraEditors.Controls.SearchMode.AutoComplete
        Me.GENDERLookUpEdit.Properties.ShowHeader = False
        Me.GENDERLookUpEdit.Properties.ValidateOnEnterKey = True
        Me.GENDERLookUpEdit.Properties.ValueMember = "Key"
        Me.GENDERLookUpEdit.Size = New System.Drawing.Size(100, 20)
        Me.GENDERLookUpEdit.TabIndex = 2
        '
        'Contact_homephoneTextEdit
        '
        Me.Contact_homephoneTextEdit.Location = New System.Drawing.Point(91, 309)
        Me.Contact_homephoneTextEdit.Name = "Contact_homephoneTextEdit"
        Me.Contact_homephoneTextEdit.Properties.ValidateOnEnterKey = True
        Me.Contact_homephoneTextEdit.Size = New System.Drawing.Size(217, 20)
        Me.Contact_homephoneTextEdit.TabIndex = 9
        '
        'B_DAYDateEdit
        '
        Me.B_DAYDateEdit.EditValue = Nothing
        Me.B_DAYDateEdit.Location = New System.Drawing.Point(91, 223)
        Me.B_DAYDateEdit.Name = "B_DAYDateEdit"
        Me.B_DAYDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.B_DAYDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.B_DAYDateEdit.Properties.ValidateOnEnterKey = True
        Me.B_DAYDateEdit.Size = New System.Drawing.Size(100, 20)
        Me.B_DAYDateEdit.TabIndex = 6
        '
        'F_NAMETextEdit
        '
        Me.F_NAMETextEdit.Location = New System.Drawing.Point(91, 28)
        Me.F_NAMETextEdit.Name = "F_NAMETextEdit"
        Me.F_NAMETextEdit.Properties.ValidateOnEnterKey = True
        Me.F_NAMETextEdit.Size = New System.Drawing.Size(159, 20)
        Me.F_NAMETextEdit.TabIndex = 0
        '
        'Contact_homeemailTextEdit
        '
        Me.Contact_homeemailTextEdit.Location = New System.Drawing.Point(91, 283)
        Me.Contact_homeemailTextEdit.Name = "Contact_homeemailTextEdit"
        Me.Contact_homeemailTextEdit.Properties.ValidateOnEnterKey = True
        Me.Contact_homeemailTextEdit.Size = New System.Drawing.Size(217, 20)
        Me.Contact_homeemailTextEdit.TabIndex = 8
        '
        'User_emailTextEdit
        '
        Me.User_emailTextEdit.Location = New System.Drawing.Point(91, 257)
        Me.User_emailTextEdit.Name = "User_emailTextEdit"
        Me.User_emailTextEdit.Properties.ValidateOnEnterKey = True
        Me.User_emailTextEdit.Size = New System.Drawing.Size(218, 20)
        Me.User_emailTextEdit.TabIndex = 7
        '
        'STREETTextEdit
        '
        Me.STREETTextEdit.Location = New System.Drawing.Point(91, 133)
        Me.STREETTextEdit.Name = "STREETTextEdit"
        Me.STREETTextEdit.Properties.ValidateOnEnterKey = True
        Me.STREETTextEdit.Size = New System.Drawing.Size(219, 20)
        Me.STREETTextEdit.TabIndex = 3
        '
        'L_NAMETextEdit
        '
        Me.L_NAMETextEdit.Location = New System.Drawing.Point(91, 54)
        Me.L_NAMETextEdit.Name = "L_NAMETextEdit"
        Me.L_NAMETextEdit.Properties.ValidateOnEnterKey = True
        Me.L_NAMETextEdit.Size = New System.Drawing.Size(219, 20)
        Me.L_NAMETextEdit.TabIndex = 1
        '
        'TINLabel
        '
        Me.TINLabel.Location = New System.Drawing.Point(49, 195)
        Me.TINLabel.Name = "TINLabel"
        Me.TINLabel.Size = New System.Drawing.Size(36, 13)
        Me.TINLabel.TabIndex = 30
        Me.TINLabel.Text = "Tax ID:"
        Me.TINLabel.Visible = False
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.START_DATEDateEdit)
        Me.GroupControl2.Controls.Add(Me.DEPTNUMLookUpEdit)
        Me.GroupControl2.Controls.Add(Me.DIVNUMLookUpEdit)
        Me.GroupControl2.Controls.Add(Me.EMP_TYPETextEdit)
        Me.GroupControl2.Controls.Add(Me.TERM_DATEDateEdit)
        Me.GroupControl2.Location = New System.Drawing.Point(359, 15)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(335, 190)
        Me.GroupControl2.TabIndex = 4
        Me.GroupControl2.Text = "Status"
        '
        'START_DATEDateEdit
        '
        Me.START_DATEDateEdit.EditValue = Nothing
        Me.START_DATEDateEdit.Location = New System.Drawing.Point(93, 129)
        Me.START_DATEDateEdit.Name = "START_DATEDateEdit"
        Me.START_DATEDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.START_DATEDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.START_DATEDateEdit.Properties.ValidateOnEnterKey = True
        Me.START_DATEDateEdit.Size = New System.Drawing.Size(100, 20)
        Me.START_DATEDateEdit.TabIndex = 7
        '
        'DEPTNUMLookUpEdit
        '
        Me.DEPTNUMLookUpEdit.Location = New System.Drawing.Point(92, 80)
        Me.DEPTNUMLookUpEdit.Name = "DEPTNUMLookUpEdit"
        Me.DEPTNUMLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DEPTNUMLookUpEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "Descr", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DEPTNUMLookUpEdit.Properties.DisplayMember = "Descr"
        Me.DEPTNUMLookUpEdit.Properties.NullText = ""
        Me.DEPTNUMLookUpEdit.Properties.ShowHeader = False
        Me.DEPTNUMLookUpEdit.Properties.ValidateOnEnterKey = True
        Me.DEPTNUMLookUpEdit.Properties.ValueMember = "DEPTNUM"
        Me.DEPTNUMLookUpEdit.Size = New System.Drawing.Size(100, 20)
        Me.DEPTNUMLookUpEdit.TabIndex = 5
        '
        'DIVNUMLookUpEdit
        '
        Me.DIVNUMLookUpEdit.Location = New System.Drawing.Point(92, 54)
        Me.DIVNUMLookUpEdit.Name = "DIVNUMLookUpEdit"
        Me.DIVNUMLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DIVNUMLookUpEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DIVNUMLookUpEdit.Properties.DisplayMember = "Descr"
        Me.DIVNUMLookUpEdit.Properties.NullText = ""
        Me.DIVNUMLookUpEdit.Properties.ShowHeader = False
        Me.DIVNUMLookUpEdit.Properties.ValidateOnEnterKey = True
        Me.DIVNUMLookUpEdit.Properties.ValueMember = "DDIVNUM"
        Me.DIVNUMLookUpEdit.Size = New System.Drawing.Size(100, 20)
        Me.DIVNUMLookUpEdit.TabIndex = 3
        '
        'EMP_TYPETextEdit
        '
        Me.EMP_TYPETextEdit.Location = New System.Drawing.Point(92, 27)
        Me.EMP_TYPETextEdit.Name = "EMP_TYPETextEdit"
        Me.EMP_TYPETextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EMP_TYPETextEdit.Properties.Items.AddRange(New Object() {"REGULAR", "CONTRACT"})
        Me.EMP_TYPETextEdit.Properties.ValidateOnEnterKey = True
        Me.EMP_TYPETextEdit.Size = New System.Drawing.Size(100, 20)
        Me.EMP_TYPETextEdit.TabIndex = 1
        '
        'TERM_DATEDateEdit
        '
        Me.TERM_DATEDateEdit.EditValue = Nothing
        Me.TERM_DATEDateEdit.Location = New System.Drawing.Point(93, 155)
        Me.TERM_DATEDateEdit.Name = "TERM_DATEDateEdit"
        Me.TERM_DATEDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TERM_DATEDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.TERM_DATEDateEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.TERM_DATEDateEdit.Size = New System.Drawing.Size(100, 20)
        Me.TERM_DATEDateEdit.TabIndex = 9
        '
        'PanelControlPayrollTax
        '
        Me.PanelControlPayrollTax.Controls.Add(Me.GroupControl9)
        Me.PanelControlPayrollTax.Controls.Add(Me.GroupControl5)
        Me.PanelControlPayrollTax.Controls.Add(Me.GroupControl4)
        Me.PanelControlPayrollTax.Location = New System.Drawing.Point(291, 84)
        Me.PanelControlPayrollTax.Name = "PanelControlPayrollTax"
        Me.PanelControlPayrollTax.Size = New System.Drawing.Size(939, 585)
        Me.PanelControlPayrollTax.TabIndex = 21
        '
        'GroupControl9
        '
        Me.GroupControl9.Controls.Add(Me.RadioGroup1)
        Me.GroupControl9.Controls.Add(Me.LabelControl12)
        Me.GroupControl9.Controls.Add(Me.LocalActiveTextEdit)
        Me.GroupControl9.Controls.Add(Me.Local_statusLabel)
        Me.GroupControl9.Controls.Add(Me.Fixed_whTextEdit)
        Me.GroupControl9.Controls.Add(Me.Extra_whTextEdit)
        Me.GroupControl9.Controls.Add(Me.DependentsLabel)
        Me.GroupControl9.Controls.Add(Me.DependentsTextEdit)
        Me.GroupControl9.Controls.Add(Me.Alternate_w4Label)
        Me.GroupControl9.Controls.Add(Me.LabelControl9)
        Me.GroupControl9.Controls.Add(Me.btnAddLocal)
        Me.GroupControl9.Controls.Add(Me.btnRemoveLocal)
        Me.GroupControl9.Controls.Add(Me.LabelControl10)
        Me.GroupControl9.Controls.Add(Me.lstSelectedLocals)
        Me.GroupControl9.Controls.Add(Me.lstAvailLocals)
        Me.GroupControl9.Controls.Add(Me.Alternate_w4TextEdit)
        Me.GroupControl9.Controls.Add(Me.Local_exemptTextEdit)
        Me.GroupControl9.Controls.Add(Me.Local_statusTextEdit)
        Me.GroupControl9.Location = New System.Drawing.Point(15, 308)
        Me.GroupControl9.Name = "GroupControl9"
        Me.GroupControl9.Size = New System.Drawing.Size(560, 180)
        Me.GroupControl9.TabIndex = 5
        Me.GroupControl9.Text = "Local Taxes"
        '
        'RadioGroup1
        '
        Me.RadioGroup1.Location = New System.Drawing.Point(306, 23)
        Me.RadioGroup1.Name = "RadioGroup1"
        Me.RadioGroup1.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.RadioGroup1.Properties.Appearance.Options.UseBackColor = True
        Me.RadioGroup1.Properties.Columns = 6
        Me.RadioGroup1.Properties.Items.AddRange(New DevExpress.XtraEditors.Controls.RadioGroupItem() {New DevExpress.XtraEditors.Controls.RadioGroupItem(0, "None"), New DevExpress.XtraEditors.Controls.RadioGroupItem(1, "1"), New DevExpress.XtraEditors.Controls.RadioGroupItem(2, "2"), New DevExpress.XtraEditors.Controls.RadioGroupItem(3, "3"), New DevExpress.XtraEditors.Controls.RadioGroupItem(4, "4"), New DevExpress.XtraEditors.Controls.RadioGroupItem(5, "5")})
        Me.RadioGroup1.Size = New System.Drawing.Size(264, 20)
        Me.RadioGroup1.TabIndex = 43
        '
        'LabelControl12
        '
        Me.LabelControl12.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl12.Appearance.ForeColor = System.Drawing.Color.Black
        Me.LabelControl12.Appearance.Options.UseFont = True
        Me.LabelControl12.Appearance.Options.UseForeColor = True
        Me.LabelControl12.LineColor = System.Drawing.Color.Black
        Me.LabelControl12.LineLocation = DevExpress.XtraEditors.LineLocation.Center
        Me.LabelControl12.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal
        Me.LabelControl12.LineStyle = System.Drawing.Drawing2D.DashStyle.Dash
        Me.LabelControl12.LineVisible = True
        Me.LabelControl12.Location = New System.Drawing.Point(201, 42)
        Me.LabelControl12.Name = "LabelControl12"
        Me.LabelControl12.Size = New System.Drawing.Size(348, 13)
        Me.LabelControl12.TabIndex = 29
        Me.LabelControl12.Text = "---------------------------------------------------------------------------------" &
    "------"
        '
        'LocalActiveTextEdit
        '
        Me.LocalActiveTextEdit.Location = New System.Drawing.Point(201, 23)
        Me.LocalActiveTextEdit.Name = "LocalActiveTextEdit"
        Me.LocalActiveTextEdit.Properties.Caption = ""
        Me.LocalActiveTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.LocalActiveTextEdit.Properties.ValueChecked = "YES"
        Me.LocalActiveTextEdit.Properties.ValueUnchecked = "NO"
        Me.LocalActiveTextEdit.Size = New System.Drawing.Size(25, 19)
        Me.LocalActiveTextEdit.TabIndex = 42
        '
        'Local_statusLabel
        '
        Me.Local_statusLabel.AutoSize = True
        Me.Local_statusLabel.Location = New System.Drawing.Point(199, 93)
        Me.Local_statusLabel.Name = "Local_statusLabel"
        Me.Local_statusLabel.Size = New System.Drawing.Size(69, 13)
        Me.Local_statusLabel.TabIndex = 38
        Me.Local_statusLabel.Text = "Local Status:"
        '
        'Fixed_whTextEdit
        '
        Me.Fixed_whTextEdit.Location = New System.Drawing.Point(464, 116)
        Me.Fixed_whTextEdit.Name = "Fixed_whTextEdit"
        Me.Fixed_whTextEdit.Size = New System.Drawing.Size(83, 20)
        Me.Fixed_whTextEdit.TabIndex = 37
        '
        'Extra_whTextEdit
        '
        Me.Extra_whTextEdit.Location = New System.Drawing.Point(464, 90)
        Me.Extra_whTextEdit.Name = "Extra_whTextEdit"
        Me.Extra_whTextEdit.Size = New System.Drawing.Size(83, 20)
        Me.Extra_whTextEdit.TabIndex = 36
        '
        'DependentsLabel
        '
        Me.DependentsLabel.AutoSize = True
        Me.DependentsLabel.Location = New System.Drawing.Point(204, 119)
        Me.DependentsLabel.Name = "DependentsLabel"
        Me.DependentsLabel.Size = New System.Drawing.Size(64, 13)
        Me.DependentsLabel.TabIndex = 34
        Me.DependentsLabel.Text = "Allowances:"
        '
        'DependentsTextEdit
        '
        Me.DependentsTextEdit.Location = New System.Drawing.Point(274, 116)
        Me.DependentsTextEdit.Name = "DependentsTextEdit"
        Me.DependentsTextEdit.Properties.ReadOnly = True
        Me.DependentsTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.DependentsTextEdit.TabIndex = 35
        '
        'Alternate_w4Label
        '
        Me.Alternate_w4Label.AutoSize = True
        Me.Alternate_w4Label.Location = New System.Drawing.Point(384, 63)
        Me.Alternate_w4Label.Name = "Alternate_w4Label"
        Me.Alternate_w4Label.Size = New System.Drawing.Size(163, 13)
        Me.Alternate_w4Label.TabIndex = 33
        Me.Alternate_w4Label.Text = "Filed Separate W4 for Local WH:"
        '
        'LabelControl9
        '
        Me.LabelControl9.Location = New System.Drawing.Point(113, 25)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(41, 13)
        Me.LabelControl9.TabIndex = 33
        Me.LabelControl9.Text = "Selected"
        '
        'btnAddLocal
        '
        Me.btnAddLocal.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnAddLocal.Location = New System.Drawing.Point(92, 62)
        Me.btnAddLocal.Name = "btnAddLocal"
        Me.btnAddLocal.Size = New System.Drawing.Size(16, 22)
        Me.btnAddLocal.TabIndex = 32
        Me.btnAddLocal.ToolTip = "Add Selected State"
        '
        'btnRemoveLocal
        '
        Me.btnRemoveLocal.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnRemoveLocal.Location = New System.Drawing.Point(92, 90)
        Me.btnRemoveLocal.Name = "btnRemoveLocal"
        Me.btnRemoveLocal.Size = New System.Drawing.Size(16, 22)
        Me.btnRemoveLocal.TabIndex = 31
        Me.btnRemoveLocal.ToolTip = "Remove Selected State"
        '
        'LabelControl10
        '
        Me.LabelControl10.Location = New System.Drawing.Point(10, 26)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(43, 13)
        Me.LabelControl10.TabIndex = 30
        Me.LabelControl10.Text = "Availalbe"
        '
        'lstSelectedLocals
        '
        Me.lstSelectedLocals.DisplayMember = "LocalDescription"
        Me.lstSelectedLocals.HorizontalScrollbar = True
        Me.lstSelectedLocals.Location = New System.Drawing.Point(113, 42)
        Me.lstSelectedLocals.Name = "lstSelectedLocals"
        Me.lstSelectedLocals.Size = New System.Drawing.Size(78, 95)
        Me.lstSelectedLocals.TabIndex = 29
        Me.lstSelectedLocals.ValueMember = "local_id"
        '
        'lstAvailLocals
        '
        Me.lstAvailLocals.DisplayMember = "Value"
        Me.lstAvailLocals.HorizontalScrollbar = True
        Me.lstAvailLocals.Location = New System.Drawing.Point(10, 42)
        Me.lstAvailLocals.Name = "lstAvailLocals"
        Me.lstAvailLocals.Size = New System.Drawing.Size(76, 95)
        Me.lstAvailLocals.TabIndex = 28
        Me.lstAvailLocals.ValueMember = "Key"
        '
        'Alternate_w4TextEdit
        '
        Me.Alternate_w4TextEdit.EditValue = Nothing
        Me.Alternate_w4TextEdit.Location = New System.Drawing.Point(360, 60)
        Me.Alternate_w4TextEdit.Name = "Alternate_w4TextEdit"
        Me.Alternate_w4TextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.Alternate_w4TextEdit.Properties.Caption = ""
        Me.Alternate_w4TextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.Alternate_w4TextEdit.Properties.ValueChecked = "YES"
        Me.Alternate_w4TextEdit.Properties.ValueUnchecked = "NO"
        Me.Alternate_w4TextEdit.Size = New System.Drawing.Size(24, 19)
        Me.Alternate_w4TextEdit.TabIndex = 34
        '
        'Local_exemptTextEdit
        '
        Me.Local_exemptTextEdit.EditValue = Nothing
        Me.Local_exemptTextEdit.Location = New System.Drawing.Point(223, 60)
        Me.Local_exemptTextEdit.Name = "Local_exemptTextEdit"
        Me.Local_exemptTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.Local_exemptTextEdit.Properties.Caption = ""
        Me.Local_exemptTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.Local_exemptTextEdit.Properties.ValueChecked = 1
        Me.Local_exemptTextEdit.Properties.ValueUnchecked = 0
        Me.Local_exemptTextEdit.Size = New System.Drawing.Size(24, 19)
        Me.Local_exemptTextEdit.TabIndex = 38
        '
        'Local_statusTextEdit
        '
        Me.Local_statusTextEdit.Location = New System.Drawing.Point(274, 90)
        Me.Local_statusTextEdit.Name = "Local_statusTextEdit"
        Me.Local_statusTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Local_statusTextEdit.Properties.ReadOnly = True
        Me.Local_statusTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.Local_statusTextEdit.TabIndex = 39
        '
        'GroupControl5
        '
        Me.GroupControl5.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat
        Me.GroupControl5.Controls.Add(Me.FLIEXELabel)
        Me.GroupControl5.Controls.Add(Me.FLIEXECheckEdit)
        Me.GroupControl5.Controls.Add(Me.LabelControl11)
        Me.GroupControl5.Controls.Add(Me.LabelControl8)
        Me.GroupControl5.Controls.Add(Me.btnAddState)
        Me.GroupControl5.Controls.Add(Me.btnRemoveState)
        Me.GroupControl5.Controls.Add(Me.LabelControl7)
        Me.GroupControl5.Controls.Add(Me.lstSelectedStates)
        Me.GroupControl5.Controls.Add(Me.lstAvailableStates)
        Me.GroupControl5.Controls.Add(Me.ST_WH_FIXEDLabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_FIXEDTextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXTRALabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXTRATextEdit)
        Me.GroupControl5.Controls.Add(Me.SDIEXELabel)
        Me.GroupControl5.Controls.Add(Me.ST_DEPSLabel)
        Me.GroupControl5.Controls.Add(Me.ST_DEPSTextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_STATUSLabel)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXE_FGELabel)
        Me.GroupControl5.Controls.Add(Me.ALTW4Label)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_WORKLabel)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_RESLabel)
        Me.GroupControl5.Controls.Add(Me.UCI_STATETextEdit)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_RESTextEdit)
        Me.GroupControl5.Controls.Add(Me.DEFAULT_WORKTextEdit)
        Me.GroupControl5.Controls.Add(Me.ALTW4TextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_WH_EXE_FGETextEdit)
        Me.GroupControl5.Controls.Add(Me.ST_STATUSTextEdit)
        Me.GroupControl5.Controls.Add(Me.SDIEXETextEdit)
        Me.GroupControl5.Controls.Add(Me.SUTA_EXE_FGTextEdit)
        Me.GroupControl5.Location = New System.Drawing.Point(15, 122)
        Me.GroupControl5.Name = "GroupControl5"
        Me.GroupControl5.Size = New System.Drawing.Size(560, 178)
        Me.GroupControl5.TabIndex = 4
        Me.GroupControl5.Text = "State Taxes"
        '
        'FLIEXELabel
        '
        Me.FLIEXELabel.AutoSize = True
        Me.FLIEXELabel.Location = New System.Drawing.Point(334, 149)
        Me.FLIEXELabel.Name = "FLIEXELabel"
        Me.FLIEXELabel.Size = New System.Drawing.Size(65, 13)
        Me.FLIEXELabel.TabIndex = 29
        Me.FLIEXELabel.Text = "FLI Exempt:"
        '
        'FLIEXECheckEdit
        '
        Me.FLIEXECheckEdit.EditValue = Nothing
        Me.FLIEXECheckEdit.Location = New System.Drawing.Point(314, 146)
        Me.FLIEXECheckEdit.Name = "FLIEXECheckEdit"
        Me.FLIEXECheckEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.FLIEXECheckEdit.Properties.Caption = ""
        Me.FLIEXECheckEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.FLIEXECheckEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.FLIEXECheckEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.FLIEXECheckEdit.Size = New System.Drawing.Size(26, 19)
        Me.FLIEXECheckEdit.TabIndex = 30
        '
        'LabelControl11
        '
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.ForeColor = System.Drawing.Color.Black
        Me.LabelControl11.Appearance.Options.UseFont = True
        Me.LabelControl11.Appearance.Options.UseForeColor = True
        Me.LabelControl11.LineColor = System.Drawing.Color.Black
        Me.LabelControl11.LineLocation = DevExpress.XtraEditors.LineLocation.Center
        Me.LabelControl11.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal
        Me.LabelControl11.LineStyle = System.Drawing.Drawing2D.DashStyle.Dash
        Me.LabelControl11.LineVisible = True
        Me.LabelControl11.Location = New System.Drawing.Point(200, 41)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(348, 13)
        Me.LabelControl11.TabIndex = 28
        Me.LabelControl11.Text = "---------------------------------------------------------------------------------" &
    "------"
        '
        'LabelControl8
        '
        Me.LabelControl8.Location = New System.Drawing.Point(113, 24)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(41, 13)
        Me.LabelControl8.TabIndex = 27
        Me.LabelControl8.Text = "Selected"
        '
        'btnAddState
        '
        Me.btnAddState.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnAddState.Location = New System.Drawing.Point(92, 66)
        Me.btnAddState.Name = "btnAddState"
        Me.btnAddState.Size = New System.Drawing.Size(16, 22)
        Me.btnAddState.TabIndex = 26
        Me.btnAddState.ToolTip = "Add Selected State"
        '
        'btnRemoveState
        '
        Me.btnRemoveState.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter
        Me.btnRemoveState.Location = New System.Drawing.Point(92, 94)
        Me.btnRemoveState.Name = "btnRemoveState"
        Me.btnRemoveState.Size = New System.Drawing.Size(16, 22)
        Me.btnRemoveState.TabIndex = 25
        Me.btnRemoveState.ToolTip = "Remove Selected State"
        '
        'LabelControl7
        '
        Me.LabelControl7.Location = New System.Drawing.Point(10, 24)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(43, 13)
        Me.LabelControl7.TabIndex = 24
        Me.LabelControl7.Text = "Availalbe"
        '
        'lstSelectedStates
        '
        Me.lstSelectedStates.Cursor = System.Windows.Forms.Cursors.Default
        Me.lstSelectedStates.DisplayMember = "StateName"
        Me.lstSelectedStates.Location = New System.Drawing.Point(113, 41)
        Me.lstSelectedStates.Name = "lstSelectedStates"
        Me.lstSelectedStates.Size = New System.Drawing.Size(78, 95)
        Me.lstSelectedStates.TabIndex = 23
        Me.lstSelectedStates.ValueMember = "STATE"
        '
        'lstAvailableStates
        '
        Me.lstAvailableStates.DisplayMember = "Value"
        Me.lstAvailableStates.Location = New System.Drawing.Point(10, 41)
        Me.lstAvailableStates.Name = "lstAvailableStates"
        Me.lstAvailableStates.Size = New System.Drawing.Size(76, 95)
        Me.lstAvailableStates.TabIndex = 22
        Me.lstAvailableStates.ValueMember = "Key"
        '
        'ST_WH_FIXEDLabel
        '
        Me.ST_WH_FIXEDLabel.AutoSize = True
        Me.ST_WH_FIXEDLabel.Location = New System.Drawing.Point(380, 110)
        Me.ST_WH_FIXEDLabel.Name = "ST_WH_FIXEDLabel"
        Me.ST_WH_FIXEDLabel.Size = New System.Drawing.Size(78, 13)
        Me.ST_WH_FIXEDLabel.TabIndex = 18
        Me.ST_WH_FIXEDLabel.Text = "Fixed St. W/H:"
        '
        'ST_WH_FIXEDTextEdit
        '
        Me.ST_WH_FIXEDTextEdit.Location = New System.Drawing.Point(464, 107)
        Me.ST_WH_FIXEDTextEdit.Name = "ST_WH_FIXEDTextEdit"
        Me.ST_WH_FIXEDTextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_WH_FIXEDTextEdit.Size = New System.Drawing.Size(83, 20)
        Me.ST_WH_FIXEDTextEdit.TabIndex = 19
        '
        'ST_WH_EXTRALabel
        '
        Me.ST_WH_EXTRALabel.AutoSize = True
        Me.ST_WH_EXTRALabel.Location = New System.Drawing.Point(380, 87)
        Me.ST_WH_EXTRALabel.Name = "ST_WH_EXTRALabel"
        Me.ST_WH_EXTRALabel.Size = New System.Drawing.Size(78, 13)
        Me.ST_WH_EXTRALabel.TabIndex = 16
        Me.ST_WH_EXTRALabel.Text = "Extra St. W/H:"
        '
        'ST_WH_EXTRATextEdit
        '
        Me.ST_WH_EXTRATextEdit.Location = New System.Drawing.Point(464, 84)
        Me.ST_WH_EXTRATextEdit.Name = "ST_WH_EXTRATextEdit"
        Me.ST_WH_EXTRATextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_WH_EXTRATextEdit.Size = New System.Drawing.Size(83, 20)
        Me.ST_WH_EXTRATextEdit.TabIndex = 17
        '
        'SDIEXELabel
        '
        Me.SDIEXELabel.AutoSize = True
        Me.SDIEXELabel.Location = New System.Drawing.Point(452, 149)
        Me.SDIEXELabel.Name = "SDIEXELabel"
        Me.SDIEXELabel.Size = New System.Drawing.Size(92, 13)
        Me.SDIEXELabel.TabIndex = 14
        Me.SDIEXELabel.Text = "Disability Exempt:"
        '
        'ST_DEPSLabel
        '
        Me.ST_DEPSLabel.Location = New System.Drawing.Point(192, 110)
        Me.ST_DEPSLabel.Name = "ST_DEPSLabel"
        Me.ST_DEPSLabel.Size = New System.Drawing.Size(94, 13)
        Me.ST_DEPSLabel.TabIndex = 12
        Me.ST_DEPSLabel.Text = "Total Allowances:"
        Me.ST_DEPSLabel.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'ST_DEPSTextEdit
        '
        Me.ST_DEPSTextEdit.Location = New System.Drawing.Point(289, 107)
        Me.ST_DEPSTextEdit.Name = "ST_DEPSTextEdit"
        Me.ST_DEPSTextEdit.Properties.ReadOnly = True
        Me.ST_DEPSTextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_DEPSTextEdit.Size = New System.Drawing.Size(82, 20)
        Me.ST_DEPSTextEdit.TabIndex = 13
        '
        'ST_STATUSLabel
        '
        Me.ST_STATUSLabel.Location = New System.Drawing.Point(192, 87)
        Me.ST_STATUSLabel.Name = "ST_STATUSLabel"
        Me.ST_STATUSLabel.Size = New System.Drawing.Size(94, 13)
        Me.ST_STATUSLabel.TabIndex = 10
        Me.ST_STATUSLabel.Text = "State Status:"
        Me.ST_STATUSLabel.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'ST_WH_EXE_FGELabel
        '
        Me.ST_WH_EXE_FGELabel.AutoSize = True
        Me.ST_WH_EXE_FGELabel.Location = New System.Drawing.Point(243, 56)
        Me.ST_WH_EXE_FGELabel.Name = "ST_WH_EXE_FGELabel"
        Me.ST_WH_EXE_FGELabel.Size = New System.Drawing.Size(100, 13)
        Me.ST_WH_EXE_FGELabel.TabIndex = 8
        Me.ST_WH_EXE_FGELabel.Text = "State W/H Exempt:"
        '
        'ALTW4Label
        '
        Me.ALTW4Label.AutoSize = True
        Me.ALTW4Label.Location = New System.Drawing.Point(384, 56)
        Me.ALTW4Label.Name = "ALTW4Label"
        Me.ALTW4Label.Size = New System.Drawing.Size(165, 13)
        Me.ALTW4Label.TabIndex = 6
        Me.ALTW4Label.Text = "Filed Separate W4 for State WH:"
        '
        'DEFAULT_WORKLabel
        '
        Me.DEFAULT_WORKLabel.AutoSize = True
        Me.DEFAULT_WORKLabel.Location = New System.Drawing.Point(225, 28)
        Me.DEFAULT_WORKLabel.Name = "DEFAULT_WORKLabel"
        Me.DEFAULT_WORKLabel.Size = New System.Drawing.Size(123, 13)
        Me.DEFAULT_WORKLabel.TabIndex = 4
        Me.DEFAULT_WORKLabel.Text = "Default Work WH State:"
        '
        'DEFAULT_RESLabel
        '
        Me.DEFAULT_RESLabel.AutoSize = True
        Me.DEFAULT_RESLabel.Location = New System.Drawing.Point(384, 28)
        Me.DEFAULT_RESLabel.Name = "DEFAULT_RESLabel"
        Me.DEFAULT_RESLabel.Size = New System.Drawing.Size(140, 13)
        Me.DEFAULT_RESLabel.TabIndex = 2
        Me.DEFAULT_RESLabel.Text = "Default Resident WH State:"
        '
        'UCI_STATETextEdit
        '
        Me.UCI_STATETextEdit.Location = New System.Drawing.Point(71, 146)
        Me.UCI_STATETextEdit.Name = "UCI_STATETextEdit"
        Me.UCI_STATETextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.UCI_STATETextEdit.Properties.PopupSizeable = True
        Me.UCI_STATETextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.UCI_STATETextEdit.Properties.ValidateOnEnterKey = True
        Me.UCI_STATETextEdit.Size = New System.Drawing.Size(68, 20)
        Me.UCI_STATETextEdit.TabIndex = 1
        '
        'DEFAULT_RESTextEdit
        '
        Me.DEFAULT_RESTextEdit.EditValue = Nothing
        Me.DEFAULT_RESTextEdit.Location = New System.Drawing.Point(361, 25)
        Me.DEFAULT_RESTextEdit.Name = "DEFAULT_RESTextEdit"
        Me.DEFAULT_RESTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.DEFAULT_RESTextEdit.Properties.Caption = ""
        Me.DEFAULT_RESTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.DEFAULT_RESTextEdit.Properties.ValueChecked = "YES"
        Me.DEFAULT_RESTextEdit.Properties.ValueUnchecked = "NO"
        Me.DEFAULT_RESTextEdit.Size = New System.Drawing.Size(26, 19)
        Me.DEFAULT_RESTextEdit.TabIndex = 3
        '
        'DEFAULT_WORKTextEdit
        '
        Me.DEFAULT_WORKTextEdit.EditValue = Nothing
        Me.DEFAULT_WORKTextEdit.Location = New System.Drawing.Point(201, 25)
        Me.DEFAULT_WORKTextEdit.Name = "DEFAULT_WORKTextEdit"
        Me.DEFAULT_WORKTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.DEFAULT_WORKTextEdit.Properties.Caption = ""
        Me.DEFAULT_WORKTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.DEFAULT_WORKTextEdit.Properties.ValueChecked = "YES"
        Me.DEFAULT_WORKTextEdit.Properties.ValueUnchecked = "NO"
        Me.DEFAULT_WORKTextEdit.Size = New System.Drawing.Size(24, 19)
        Me.DEFAULT_WORKTextEdit.TabIndex = 5
        '
        'ALTW4TextEdit
        '
        Me.ALTW4TextEdit.EditValue = Nothing
        Me.ALTW4TextEdit.Location = New System.Drawing.Point(361, 54)
        Me.ALTW4TextEdit.Name = "ALTW4TextEdit"
        Me.ALTW4TextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.ALTW4TextEdit.Properties.Caption = ""
        Me.ALTW4TextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.ALTW4TextEdit.Properties.ValueChecked = "YES"
        Me.ALTW4TextEdit.Properties.ValueUnchecked = "NO"
        Me.ALTW4TextEdit.Size = New System.Drawing.Size(23, 19)
        Me.ALTW4TextEdit.TabIndex = 7
        '
        'ST_WH_EXE_FGETextEdit
        '
        Me.ST_WH_EXE_FGETextEdit.EditValue = Nothing
        Me.ST_WH_EXE_FGETextEdit.Location = New System.Drawing.Point(223, 53)
        Me.ST_WH_EXE_FGETextEdit.Name = "ST_WH_EXE_FGETextEdit"
        Me.ST_WH_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.ST_WH_EXE_FGETextEdit.Properties.Caption = ""
        Me.ST_WH_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.ST_WH_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.ST_WH_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.ST_WH_EXE_FGETextEdit.Size = New System.Drawing.Size(23, 19)
        Me.ST_WH_EXE_FGETextEdit.TabIndex = 9
        '
        'ST_STATUSTextEdit
        '
        Me.ST_STATUSTextEdit.Location = New System.Drawing.Point(289, 84)
        Me.ST_STATUSTextEdit.Name = "ST_STATUSTextEdit"
        Me.ST_STATUSTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ST_STATUSTextEdit.Properties.Items.AddRange(New Object() {"Single", "Married"})
        Me.ST_STATUSTextEdit.Properties.ReadOnly = True
        Me.ST_STATUSTextEdit.Properties.ValidateOnEnterKey = True
        Me.ST_STATUSTextEdit.Size = New System.Drawing.Size(82, 20)
        Me.ST_STATUSTextEdit.TabIndex = 11
        '
        'SDIEXETextEdit
        '
        Me.SDIEXETextEdit.EditValue = Nothing
        Me.SDIEXETextEdit.Location = New System.Drawing.Point(432, 146)
        Me.SDIEXETextEdit.Name = "SDIEXETextEdit"
        Me.SDIEXETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.SDIEXETextEdit.Properties.Caption = ""
        Me.SDIEXETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.SDIEXETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.SDIEXETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.SDIEXETextEdit.Size = New System.Drawing.Size(26, 19)
        Me.SDIEXETextEdit.TabIndex = 15
        '
        'SUTA_EXE_FGTextEdit
        '
        Me.SUTA_EXE_FGTextEdit.EditValue = Nothing
        Me.SUTA_EXE_FGTextEdit.Location = New System.Drawing.Point(152, 146)
        Me.SUTA_EXE_FGTextEdit.Name = "SUTA_EXE_FGTextEdit"
        Me.SUTA_EXE_FGTextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.SUTA_EXE_FGTextEdit.Properties.Caption = ""
        Me.SUTA_EXE_FGTextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.SUTA_EXE_FGTextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.SUTA_EXE_FGTextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.SUTA_EXE_FGTextEdit.Size = New System.Drawing.Size(25, 19)
        Me.SUTA_EXE_FGTextEdit.TabIndex = 21
        '
        'GroupControl4
        '
        Me.GroupControl4.Controls.Add(Me.FED_WH_FIXEDTextEdit)
        Me.GroupControl4.Controls.Add(Me.FED_WH_EXE_FGETextEdit)
        Me.GroupControl4.Controls.Add(Me.OASDI_EXE_FGETextEdit)
        Me.GroupControl4.Controls.Add(Me.FED_WH_EXTRATextEdit)
        Me.GroupControl4.Controls.Add(Me.FUTA_EXE_FGETextEdit)
        Me.GroupControl4.Controls.Add(Me.FED_DEPSTextEdit)
        Me.GroupControl4.Controls.Add(Me.FED_STATUSTextEdit)
        Me.GroupControl4.Location = New System.Drawing.Point(15, 15)
        Me.GroupControl4.Name = "GroupControl4"
        Me.GroupControl4.Size = New System.Drawing.Size(560, 97)
        Me.GroupControl4.TabIndex = 3
        Me.GroupControl4.Text = "Federal Taxes"
        '
        'FED_WH_FIXEDTextEdit
        '
        Me.FED_WH_FIXEDTextEdit.Location = New System.Drawing.Point(343, 72)
        Me.FED_WH_FIXEDTextEdit.Name = "FED_WH_FIXEDTextEdit"
        Me.FED_WH_FIXEDTextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_WH_FIXEDTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.FED_WH_FIXEDTextEdit.TabIndex = 7
        '
        'FED_WH_EXE_FGETextEdit
        '
        Me.FED_WH_EXE_FGETextEdit.EditValue = Nothing
        Me.FED_WH_EXE_FGETextEdit.Location = New System.Drawing.Point(54, 22)
        Me.FED_WH_EXE_FGETextEdit.Name = "FED_WH_EXE_FGETextEdit"
        Me.FED_WH_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.FED_WH_EXE_FGETextEdit.Properties.Caption = ""
        Me.FED_WH_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.FED_WH_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.FED_WH_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.FED_WH_EXE_FGETextEdit.Size = New System.Drawing.Size(22, 19)
        Me.FED_WH_EXE_FGETextEdit.TabIndex = 1
        '
        'OASDI_EXE_FGETextEdit
        '
        Me.OASDI_EXE_FGETextEdit.EditValue = Nothing
        Me.OASDI_EXE_FGETextEdit.Location = New System.Drawing.Point(453, 49)
        Me.OASDI_EXE_FGETextEdit.Name = "OASDI_EXE_FGETextEdit"
        Me.OASDI_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.OASDI_EXE_FGETextEdit.Properties.Caption = ""
        Me.OASDI_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.OASDI_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.OASDI_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.OASDI_EXE_FGETextEdit.Size = New System.Drawing.Size(23, 19)
        Me.OASDI_EXE_FGETextEdit.TabIndex = 3
        '
        'FED_WH_EXTRATextEdit
        '
        Me.FED_WH_EXTRATextEdit.Location = New System.Drawing.Point(343, 49)
        Me.FED_WH_EXTRATextEdit.Name = "FED_WH_EXTRATextEdit"
        Me.FED_WH_EXTRATextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_WH_EXTRATextEdit.Size = New System.Drawing.Size(100, 20)
        Me.FED_WH_EXTRATextEdit.TabIndex = 5
        '
        'FUTA_EXE_FGETextEdit
        '
        Me.FUTA_EXE_FGETextEdit.EditValue = Nothing
        Me.FUTA_EXE_FGETextEdit.Location = New System.Drawing.Point(453, 72)
        Me.FUTA_EXE_FGETextEdit.Name = "FUTA_EXE_FGETextEdit"
        Me.FUTA_EXE_FGETextEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.FUTA_EXE_FGETextEdit.Properties.Caption = ""
        Me.FUTA_EXE_FGETextEdit.Properties.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        Me.FUTA_EXE_FGETextEdit.Properties.ValueChecked = New Decimal(New Integer() {1, 0, 0, 0})
        Me.FUTA_EXE_FGETextEdit.Properties.ValueUnchecked = New Decimal(New Integer() {0, 0, 0, 0})
        Me.FUTA_EXE_FGETextEdit.Size = New System.Drawing.Size(23, 19)
        Me.FUTA_EXE_FGETextEdit.TabIndex = 5
        '
        'FED_DEPSTextEdit
        '
        Me.FED_DEPSTextEdit.Location = New System.Drawing.Point(120, 72)
        Me.FED_DEPSTextEdit.Name = "FED_DEPSTextEdit"
        Me.FED_DEPSTextEdit.Properties.Mask.EditMask = "\d{0,2}"
        Me.FED_DEPSTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx
        Me.FED_DEPSTextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_DEPSTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.FED_DEPSTextEdit.TabIndex = 3
        '
        'FED_STATUSTextEdit
        '
        Me.FED_STATUSTextEdit.Location = New System.Drawing.Point(120, 49)
        Me.FED_STATUSTextEdit.Name = "FED_STATUSTextEdit"
        Me.FED_STATUSTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.FED_STATUSTextEdit.Properties.Items.AddRange(New Object() {"Single", "Married"})
        Me.FED_STATUSTextEdit.Properties.ValidateOnEnterKey = True
        Me.FED_STATUSTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.FED_STATUSTextEdit.TabIndex = 1
        '
        'PanelControl4
        '
        Me.PanelControl4.Controls.Add(Me.GroupControl7)
        Me.PanelControl4.Controls.Add(Me.btnDelete)
        Me.PanelControl4.Controls.Add(Me.btnAdd)
        Me.PanelControl4.Controls.Add(Me.pnlOverrideGroups)
        Me.PanelControl4.Controls.Add(Me.GridControl1)
        Me.PanelControl4.Location = New System.Drawing.Point(291, 84)
        Me.PanelControl4.Name = "PanelControl4"
        Me.PanelControl4.Size = New System.Drawing.Size(939, 585)
        Me.PanelControl4.TabIndex = 22
        Me.PanelControl4.Visible = False
        '
        'GroupControl7
        '
        Me.GroupControl7.Controls.Add(Me.GridControlCoOptionsSecondCheckPay)
        Me.GroupControl7.Location = New System.Drawing.Point(17, 316)
        Me.GroupControl7.Name = "GroupControl7"
        Me.GroupControl7.Size = New System.Drawing.Size(191, 180)
        Me.GroupControl7.TabIndex = 25
        Me.GroupControl7.Text = "Following pays on second check:"
        '
        'GridControlCoOptionsSecondCheckPay
        '
        Me.GridControlCoOptionsSecondCheckPay.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControlCoOptionsSecondCheckPay.Location = New System.Drawing.Point(2, 20)
        Me.GridControlCoOptionsSecondCheckPay.MainView = Me.GridView8
        Me.GridControlCoOptionsSecondCheckPay.Name = "GridControlCoOptionsSecondCheckPay"
        Me.GridControlCoOptionsSecondCheckPay.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemLookUpEdit2})
        Me.GridControlCoOptionsSecondCheckPay.Size = New System.Drawing.Size(187, 158)
        Me.GridControlCoOptionsSecondCheckPay.TabIndex = 18
        Me.GridControlCoOptionsSecondCheckPay.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView8})
        '
        'GridView8
        '
        Me.GridView8.Appearance.ViewCaption.Font = New System.Drawing.Font("Tahoma", 8.24!)
        Me.GridView8.Appearance.ViewCaption.ForeColor = System.Drawing.Color.Black
        Me.GridView8.Appearance.ViewCaption.Options.UseFont = True
        Me.GridView8.Appearance.ViewCaption.Options.UseForeColor = True
        Me.GridView8.Appearance.ViewCaption.Options.UseTextOptions = True
        Me.GridView8.Appearance.ViewCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridView8.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn57, Me.GridColumn58})
        Me.GridView8.GridControl = Me.GridControlCoOptionsSecondCheckPay
        Me.GridView8.Name = "GridView8"
        Me.GridView8.OptionsCustomization.AllowGroup = False
        Me.GridView8.OptionsMenu.EnableColumnMenu = False
        Me.GridView8.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom
        Me.GridView8.OptionsView.ShowGroupPanel = False
        Me.GridView8.ViewCaption = "Following pays on second check:"
        '
        'GridColumn57
        '
        Me.GridColumn57.ColumnEdit = Me.RepositoryItemLookUpEdit2
        Me.GridColumn57.FieldName = "PayCode"
        Me.GridColumn57.Name = "GridColumn57"
        Me.GridColumn57.Visible = True
        Me.GridColumn57.VisibleIndex = 0
        Me.GridColumn57.Width = 150
        '
        'RepositoryItemLookUpEdit2
        '
        Me.RepositoryItemLookUpEdit2.AutoHeight = False
        Me.RepositoryItemLookUpEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemLookUpEdit2.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_NUM", "Pay Code", 75, DevExpress.Utils.FormatType.Numeric, "", True, DevExpress.Utils.HorzAlignment.Center, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_DESC", "Description", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.RepositoryItemLookUpEdit2.DisplayMember = "OTH_PAY_DESC"
        Me.RepositoryItemLookUpEdit2.Name = "RepositoryItemLookUpEdit2"
        Me.RepositoryItemLookUpEdit2.NullText = ""
        Me.RepositoryItemLookUpEdit2.PopupFormMinSize = New System.Drawing.Size(225, 0)
        Me.RepositoryItemLookUpEdit2.ValueMember = "OTH_PAY_NUM"
        '
        'GridColumn58
        '
        Me.GridColumn58.Caption = "SepChk"
        Me.GridColumn58.FieldName = "SeparateCheck"
        Me.GridColumn58.Name = "GridColumn58"
        Me.GridColumn58.Visible = True
        Me.GridColumn58.VisibleIndex = 1
        '
        'btnDelete
        '
        Me.btnDelete.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnDelete.Location = New System.Drawing.Point(85, 17)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Size = New System.Drawing.Size(62, 23)
        Me.btnDelete.TabIndex = 24
        Me.btnDelete.Text = "Delete"
        '
        'btnAdd
        '
        Me.btnAdd.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat
        Me.btnAdd.Location = New System.Drawing.Point(17, 17)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Size = New System.Drawing.Size(62, 23)
        Me.btnAdd.TabIndex = 23
        Me.btnAdd.Text = "Add"
        '
        'pnlOverrideGroups
        '
        Me.pnlOverrideGroups.AutoScroll = True
        Me.pnlOverrideGroups.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.pnlOverrideGroups.Controls.Add(Me.pnlGeneralOptions)
        Me.pnlOverrideGroups.Controls.Add(Me.grpSecondCheckTaxes)
        Me.pnlOverrideGroups.Controls.Add(Me.grpScheduling)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlNetOverride)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlOTHours)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlSecondCheck)
        Me.pnlOverrideGroups.Controls.Add(Me.pnlRecordType)
        Me.pnlOverrideGroups.Location = New System.Drawing.Point(214, 17)
        Me.pnlOverrideGroups.Name = "pnlOverrideGroups"
        Me.pnlOverrideGroups.Size = New System.Drawing.Size(550, 479)
        Me.pnlOverrideGroups.TabIndex = 22
        '
        'pnlGeneralOptions
        '
        Me.pnlGeneralOptions.AutoScroll = True
        Me.pnlGeneralOptions.Controls.Add(Me.ExcludeFromUtilityImportCheckEdit)
        Me.pnlGeneralOptions.Controls.Add(Me.GroupControl8)
        Me.pnlGeneralOptions.Controls.Add(Me.ddlPayUnderEmpNum)
        Me.pnlGeneralOptions.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlGeneralOptions.Location = New System.Drawing.Point(0, 475)
        Me.pnlGeneralOptions.Name = "pnlGeneralOptions"
        Me.pnlGeneralOptions.Size = New System.Drawing.Size(548, 112)
        Me.pnlGeneralOptions.TabIndex = 27
        '
        'ExcludeFromUtilityImportCheckEdit
        '
        Me.ExcludeFromUtilityImportCheckEdit.Location = New System.Drawing.Point(156, 86)
        Me.ExcludeFromUtilityImportCheckEdit.Name = "ExcludeFromUtilityImportCheckEdit"
        Me.ExcludeFromUtilityImportCheckEdit.Properties.Caption = ""
        Me.ExcludeFromUtilityImportCheckEdit.Size = New System.Drawing.Size(27, 19)
        Me.ExcludeFromUtilityImportCheckEdit.TabIndex = 24
        '
        'GroupControl8
        '
        Me.GroupControl8.Appearance.BackColor = System.Drawing.Color.White
        Me.GroupControl8.Appearance.Options.UseBackColor = True
        Me.GroupControl8.AppearanceCaption.BackColor = System.Drawing.Color.White
        Me.GroupControl8.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GroupControl8.AppearanceCaption.Options.UseBackColor = True
        Me.GroupControl8.AppearanceCaption.Options.UseFont = True
        Me.GroupControl8.Controls.Add(Me.OTSeperateCheckHoursMoreThanTextEdit)
        Me.GroupControl8.Controls.Add(Me.OTSeperateCheckCheckEdit)
        Me.GroupControl8.Dock = System.Windows.Forms.DockStyle.Top
        Me.GroupControl8.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl8.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.GroupControl8.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl8.Name = "GroupControl8"
        Me.GroupControl8.Size = New System.Drawing.Size(548, 53)
        Me.GroupControl8.TabIndex = 21
        Me.GroupControl8.Text = "Overtime Options"
        '
        'OTSeperateCheckHoursMoreThanTextEdit
        '
        Me.OTSeperateCheckHoursMoreThanTextEdit.Enabled = False
        Me.OTSeperateCheckHoursMoreThanTextEdit.Location = New System.Drawing.Point(307, 25)
        Me.OTSeperateCheckHoursMoreThanTextEdit.Name = "OTSeperateCheckHoursMoreThanTextEdit"
        Me.OTSeperateCheckHoursMoreThanTextEdit.Properties.Mask.EditMask = "n2"
        Me.OTSeperateCheckHoursMoreThanTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.OTSeperateCheckHoursMoreThanTextEdit.Size = New System.Drawing.Size(44, 22)
        Me.OTSeperateCheckHoursMoreThanTextEdit.TabIndex = 15
        '
        'OTSeperateCheckCheckEdit
        '
        Me.OTSeperateCheckCheckEdit.Location = New System.Drawing.Point(137, 25)
        Me.OTSeperateCheckCheckEdit.Name = "OTSeperateCheckCheckEdit"
        Me.OTSeperateCheckCheckEdit.Properties.Caption = ""
        Me.OTSeperateCheckCheckEdit.Size = New System.Drawing.Size(24, 17)
        Me.OTSeperateCheckCheckEdit.TabIndex = 14
        '
        'ddlPayUnderEmpNum
        '
        Me.ddlPayUnderEmpNum.Location = New System.Drawing.Point(136, 59)
        Me.ddlPayUnderEmpNum.Name = "ddlPayUnderEmpNum"
        Me.ddlPayUnderEmpNum.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ddlPayUnderEmpNum.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("EMPNUM", "Emp #", 50, DevExpress.Utils.FormatType.Numeric, "", True, DevExpress.Utils.HorzAlignment.Far, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("LastName", "Last", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("FirstName", "First", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("MiddleName", "MI", 20, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.ddlPayUnderEmpNum.Properties.DisplayMember = "EMPNUM"
        Me.ddlPayUnderEmpNum.Properties.NullText = ""
        Me.ddlPayUnderEmpNum.Properties.ValueMember = "EMPNUM"
        Me.ddlPayUnderEmpNum.Size = New System.Drawing.Size(215, 20)
        Me.ddlPayUnderEmpNum.TabIndex = 22
        '
        'grpSecondCheckTaxes
        '
        Me.grpSecondCheckTaxes.Appearance.BackColor = System.Drawing.Color.White
        Me.grpSecondCheckTaxes.Appearance.Options.UseBackColor = True
        Me.grpSecondCheckTaxes.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.grpSecondCheckTaxes.AppearanceCaption.Options.UseFont = True
        Me.grpSecondCheckTaxes.Controls.Add(Me.UIStateComboBoxEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.ResStateComboBoxEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.WrkStateComboBoxEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.FLIOverrdieAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.MedicareOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.OASDIOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.FedOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.CheckTypeTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.TaxFrequencyTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.DBOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.STOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.LOCOverrideAmountTextEdit)
        Me.grpSecondCheckTaxes.Controls.Add(Me.tbManualCheckNumber)
        Me.grpSecondCheckTaxes.Dock = System.Windows.Forms.DockStyle.Top
        Me.grpSecondCheckTaxes.Location = New System.Drawing.Point(0, 334)
        Me.grpSecondCheckTaxes.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.grpSecondCheckTaxes.LookAndFeel.UseDefaultLookAndFeel = False
        Me.grpSecondCheckTaxes.Name = "grpSecondCheckTaxes"
        Me.grpSecondCheckTaxes.Size = New System.Drawing.Size(548, 141)
        Me.grpSecondCheckTaxes.TabIndex = 16
        Me.grpSecondCheckTaxes.Text = "Tax Overrides (Optional)"
        '
        'UIStateComboBoxEdit
        '
        Me.UIStateComboBoxEdit.Location = New System.Drawing.Point(433, 117)
        Me.UIStateComboBoxEdit.Name = "UIStateComboBoxEdit"
        Me.UIStateComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.UIStateComboBoxEdit.Properties.PopupSizeable = True
        Me.UIStateComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.UIStateComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.UIStateComboBoxEdit.Size = New System.Drawing.Size(68, 22)
        Me.UIStateComboBoxEdit.TabIndex = 42
        '
        'ResStateComboBoxEdit
        '
        Me.ResStateComboBoxEdit.Location = New System.Drawing.Point(433, 95)
        Me.ResStateComboBoxEdit.Name = "ResStateComboBoxEdit"
        Me.ResStateComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ResStateComboBoxEdit.Properties.PopupSizeable = True
        Me.ResStateComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ResStateComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.ResStateComboBoxEdit.Size = New System.Drawing.Size(68, 22)
        Me.ResStateComboBoxEdit.TabIndex = 40
        '
        'WrkStateComboBoxEdit
        '
        Me.WrkStateComboBoxEdit.Location = New System.Drawing.Point(433, 73)
        Me.WrkStateComboBoxEdit.Name = "WrkStateComboBoxEdit"
        Me.WrkStateComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.WrkStateComboBoxEdit.Properties.PopupSizeable = True
        Me.WrkStateComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.WrkStateComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.WrkStateComboBoxEdit.Size = New System.Drawing.Size(68, 22)
        Me.WrkStateComboBoxEdit.TabIndex = 38
        '
        'FLIOverrdieAmountTextEdit
        '
        Me.FLIOverrdieAmountTextEdit.Location = New System.Drawing.Point(241, 117)
        Me.FLIOverrdieAmountTextEdit.Name = "FLIOverrdieAmountTextEdit"
        Me.FLIOverrdieAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.FLIOverrdieAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.FLIOverrdieAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.FLIOverrdieAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.FLIOverrdieAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.FLIOverrdieAmountTextEdit.TabIndex = 35
        '
        'MedicareOverrideAmountTextEdit
        '
        Me.MedicareOverrideAmountTextEdit.Location = New System.Drawing.Point(82, 95)
        Me.MedicareOverrideAmountTextEdit.Name = "MedicareOverrideAmountTextEdit"
        Me.MedicareOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.MedicareOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.MedicareOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.MedicareOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.MedicareOverrideAmountTextEdit.TabIndex = 7
        '
        'OASDIOverrideAmountTextEdit
        '
        Me.OASDIOverrideAmountTextEdit.Location = New System.Drawing.Point(82, 73)
        Me.OASDIOverrideAmountTextEdit.Name = "OASDIOverrideAmountTextEdit"
        Me.OASDIOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.OASDIOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.OASDIOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.OASDIOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.OASDIOverrideAmountTextEdit.TabIndex = 5
        '
        'FedOverrideAmountTextEdit
        '
        Me.FedOverrideAmountTextEdit.Location = New System.Drawing.Point(82, 51)
        Me.FedOverrideAmountTextEdit.Name = "FedOverrideAmountTextEdit"
        Me.FedOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.FedOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.FedOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.FedOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.FedOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.FedOverrideAmountTextEdit.TabIndex = 3
        '
        'CheckTypeTextEdit
        '
        Me.CheckTypeTextEdit.Location = New System.Drawing.Point(286, 21)
        Me.CheckTypeTextEdit.Name = "CheckTypeTextEdit"
        Me.CheckTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CheckTypeTextEdit.Properties.Items.AddRange(New Object() {"", "MANUAL"})
        Me.CheckTypeTextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.CheckTypeTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.CheckTypeTextEdit.TabIndex = 1
        '
        'TaxFrequencyTextEdit
        '
        Me.TaxFrequencyTextEdit.Location = New System.Drawing.Point(82, 20)
        Me.TaxFrequencyTextEdit.Name = "TaxFrequencyTextEdit"
        Me.TaxFrequencyTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TaxFrequencyTextEdit.Properties.Items.AddRange(New Object() {"", "Weekly", "Bi-Weekly", "Semi-Monthly", "Monthly", "Quarterly", "Annually"})
        Me.TaxFrequencyTextEdit.Size = New System.Drawing.Size(125, 22)
        Me.TaxFrequencyTextEdit.TabIndex = 0
        '
        'DBOverrideAmountTextEdit
        '
        Me.DBOverrideAmountTextEdit.Location = New System.Drawing.Point(241, 95)
        Me.DBOverrideAmountTextEdit.Name = "DBOverrideAmountTextEdit"
        Me.DBOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.DBOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.DBOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.DBOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.DBOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.DBOverrideAmountTextEdit.TabIndex = 8
        '
        'STOverrideAmountTextEdit
        '
        Me.STOverrideAmountTextEdit.Location = New System.Drawing.Point(241, 51)
        Me.STOverrideAmountTextEdit.Name = "STOverrideAmountTextEdit"
        Me.STOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.STOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.STOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.STOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.STOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.STOverrideAmountTextEdit.TabIndex = 4
        '
        'LOCOverrideAmountTextEdit
        '
        Me.LOCOverrideAmountTextEdit.Location = New System.Drawing.Point(241, 73)
        Me.LOCOverrideAmountTextEdit.Name = "LOCOverrideAmountTextEdit"
        Me.LOCOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.LOCOverrideAmountTextEdit.Properties.Mask.EditMask = "f2"
        Me.LOCOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.LOCOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.LOCOverrideAmountTextEdit.Size = New System.Drawing.Size(86, 22)
        Me.LOCOverrideAmountTextEdit.TabIndex = 6
        '
        'tbManualCheckNumber
        '
        Me.tbManualCheckNumber.Location = New System.Drawing.Point(433, 21)
        Me.tbManualCheckNumber.Name = "tbManualCheckNumber"
        Me.tbManualCheckNumber.Size = New System.Drawing.Size(86, 22)
        Me.tbManualCheckNumber.TabIndex = 2
        '
        'grpScheduling
        '
        Me.grpScheduling.Appearance.BackColor = System.Drawing.Color.White
        Me.grpScheduling.Appearance.Options.UseBackColor = True
        Me.grpScheduling.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.grpScheduling.AppearanceCaption.Options.UseFont = True
        Me.grpScheduling.Controls.Add(Me.LabelControl2)
        Me.grpScheduling.Controls.Add(Me.LastOfMonthCheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd5CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd4CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd3CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd2CheckEdit)
        Me.grpScheduling.Controls.Add(Me.Prd1CheckEdit)
        Me.grpScheduling.Controls.Add(Me.AllPrdsCheckEdit)
        Me.grpScheduling.Dock = System.Windows.Forms.DockStyle.Top
        Me.grpScheduling.Location = New System.Drawing.Point(0, 264)
        Me.grpScheduling.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.grpScheduling.LookAndFeel.UseDefaultLookAndFeel = False
        Me.grpScheduling.Name = "grpScheduling"
        Me.grpScheduling.Size = New System.Drawing.Size(548, 70)
        Me.grpScheduling.TabIndex = 25
        Me.grpScheduling.Text = "Scheduling"
        '
        'LabelControl2
        '
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.Blue
        Me.LabelControl2.Appearance.Options.UseForeColor = True
        Me.LabelControl2.Location = New System.Drawing.Point(87, 28)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(38, 13)
        Me.LabelControl2.TabIndex = 14
        Me.LabelControl2.Text = "OR Prd:"
        '
        'LastOfMonthCheckEdit
        '
        Me.LastOfMonthCheckEdit.Location = New System.Drawing.Point(6, 45)
        Me.LastOfMonthCheckEdit.Name = "LastOfMonthCheckEdit"
        Me.LastOfMonthCheckEdit.Properties.Caption = "Last Of Month"
        Me.LastOfMonthCheckEdit.Size = New System.Drawing.Size(99, 17)
        Me.LastOfMonthCheckEdit.TabIndex = 13
        '
        'Prd5CheckEdit
        '
        Me.Prd5CheckEdit.Location = New System.Drawing.Point(260, 25)
        Me.Prd5CheckEdit.Name = "Prd5CheckEdit"
        Me.Prd5CheckEdit.Properties.Caption = "5"
        Me.Prd5CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd5CheckEdit.TabIndex = 11
        '
        'Prd4CheckEdit
        '
        Me.Prd4CheckEdit.Location = New System.Drawing.Point(226, 25)
        Me.Prd4CheckEdit.Name = "Prd4CheckEdit"
        Me.Prd4CheckEdit.Properties.Caption = "4"
        Me.Prd4CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd4CheckEdit.TabIndex = 9
        '
        'Prd3CheckEdit
        '
        Me.Prd3CheckEdit.Location = New System.Drawing.Point(192, 25)
        Me.Prd3CheckEdit.Name = "Prd3CheckEdit"
        Me.Prd3CheckEdit.Properties.Caption = "3"
        Me.Prd3CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd3CheckEdit.TabIndex = 7
        '
        'Prd2CheckEdit
        '
        Me.Prd2CheckEdit.Location = New System.Drawing.Point(158, 25)
        Me.Prd2CheckEdit.Name = "Prd2CheckEdit"
        Me.Prd2CheckEdit.Properties.Caption = "2"
        Me.Prd2CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd2CheckEdit.TabIndex = 5
        '
        'Prd1CheckEdit
        '
        Me.Prd1CheckEdit.Location = New System.Drawing.Point(124, 25)
        Me.Prd1CheckEdit.Name = "Prd1CheckEdit"
        Me.Prd1CheckEdit.Properties.Caption = "1"
        Me.Prd1CheckEdit.Size = New System.Drawing.Size(31, 17)
        Me.Prd1CheckEdit.TabIndex = 3
        '
        'AllPrdsCheckEdit
        '
        Me.AllPrdsCheckEdit.Location = New System.Drawing.Point(6, 25)
        Me.AllPrdsCheckEdit.Name = "AllPrdsCheckEdit"
        Me.AllPrdsCheckEdit.Properties.Caption = "All Periods"
        Me.AllPrdsCheckEdit.Size = New System.Drawing.Size(75, 17)
        Me.AllPrdsCheckEdit.TabIndex = 1
        '
        'pnlNetOverride
        '
        Me.pnlNetOverride.Appearance.BackColor = System.Drawing.Color.White
        Me.pnlNetOverride.Appearance.Options.UseBackColor = True
        Me.pnlNetOverride.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.pnlNetOverride.AppearanceCaption.Options.UseFont = True
        Me.pnlNetOverride.Controls.Add(Me.NetOverrideDedNumTextEdit)
        Me.pnlNetOverride.Controls.Add(Me.NetOverrideAmountTextEdit)
        Me.pnlNetOverride.Controls.Add(Me.NetOverrideAdjustTypeTextEdit)
        Me.pnlNetOverride.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlNetOverride.Location = New System.Drawing.Point(0, 217)
        Me.pnlNetOverride.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.pnlNetOverride.LookAndFeel.UseDefaultLookAndFeel = False
        Me.pnlNetOverride.Name = "pnlNetOverride"
        Me.pnlNetOverride.Size = New System.Drawing.Size(548, 47)
        Me.pnlNetOverride.TabIndex = 13
        Me.pnlNetOverride.Text = "Net Override"
        '
        'NetOverrideDedNumTextEdit
        '
        Me.NetOverrideDedNumTextEdit.Location = New System.Drawing.Point(416, 19)
        Me.NetOverrideDedNumTextEdit.Name = "NetOverrideDedNumTextEdit"
        Me.NetOverrideDedNumTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.NetOverrideDedNumTextEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("DED_DESC", "DED_DESC", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.NetOverrideDedNumTextEdit.Properties.DisplayMember = "DED_DESC"
        Me.NetOverrideDedNumTextEdit.Properties.NullText = ""
        Me.NetOverrideDedNumTextEdit.Properties.PopupFormMinSize = New System.Drawing.Size(150, 0)
        Me.NetOverrideDedNumTextEdit.Properties.ShowHeader = False
        Me.NetOverrideDedNumTextEdit.Properties.ValueMember = "DED_NUM"
        Me.NetOverrideDedNumTextEdit.Size = New System.Drawing.Size(103, 22)
        Me.NetOverrideDedNumTextEdit.TabIndex = 8
        '
        'NetOverrideAmountTextEdit
        '
        Me.NetOverrideAmountTextEdit.Location = New System.Drawing.Point(105, 20)
        Me.NetOverrideAmountTextEdit.Name = "NetOverrideAmountTextEdit"
        Me.NetOverrideAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.NetOverrideAmountTextEdit.Properties.Mask.EditMask = "c"
        Me.NetOverrideAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.NetOverrideAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.NetOverrideAmountTextEdit.Size = New System.Drawing.Size(84, 22)
        Me.NetOverrideAmountTextEdit.TabIndex = 1
        '
        'NetOverrideAdjustTypeTextEdit
        '
        Me.NetOverrideAdjustTypeTextEdit.Location = New System.Drawing.Point(258, 20)
        Me.NetOverrideAdjustTypeTextEdit.Name = "NetOverrideAdjustTypeTextEdit"
        Me.NetOverrideAdjustTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.NetOverrideAdjustTypeTextEdit.Properties.Items.AddRange(New Object() {"Gross", "Fed", "Deduction", "Deduction After 'Credit To Net'"})
        Me.NetOverrideAdjustTypeTextEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.NetOverrideAdjustTypeTextEdit.Size = New System.Drawing.Size(103, 22)
        Me.NetOverrideAdjustTypeTextEdit.TabIndex = 3
        '
        'pnlOTHours
        '
        Me.pnlOTHours.Controls.Add(Me.OTHoursTextEdit)
        Me.pnlOTHours.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlOTHours.Location = New System.Drawing.Point(0, 189)
        Me.pnlOTHours.Name = "pnlOTHours"
        Me.pnlOTHours.Size = New System.Drawing.Size(548, 28)
        Me.pnlOTHours.TabIndex = 26
        '
        'OTHoursTextEdit
        '
        Me.OTHoursTextEdit.Location = New System.Drawing.Point(105, 3)
        Me.OTHoursTextEdit.Name = "OTHoursTextEdit"
        Me.OTHoursTextEdit.Properties.Mask.EditMask = "n3"
        Me.OTHoursTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.OTHoursTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.OTHoursTextEdit.TabIndex = 1
        '
        'pnlSecondCheck
        '
        Me.pnlSecondCheck.Controls.Add(Me.DeptNum2ndCheck)
        Me.pnlSecondCheck.Controls.Add(Me.DivNum2ndCheck)
        Me.pnlSecondCheck.Controls.Add(Me.PayDedCodeLookUpEdit)
        Me.pnlSecondCheck.Controls.Add(Me.CheckCounterTextEdit)
        Me.pnlSecondCheck.Controls.Add(Me.PayDedAmountTextEdit)
        Me.pnlSecondCheck.Controls.Add(Me.PayDedTypeTextEdit)
        Me.pnlSecondCheck.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlSecondCheck.Location = New System.Drawing.Point(0, 27)
        Me.pnlSecondCheck.Name = "pnlSecondCheck"
        Me.pnlSecondCheck.Size = New System.Drawing.Size(548, 162)
        Me.pnlSecondCheck.TabIndex = 12
        '
        'DeptNum2ndCheck
        '
        Me.DeptNum2ndCheck.Location = New System.Drawing.Point(105, 136)
        Me.DeptNum2ndCheck.Name = "DeptNum2ndCheck"
        Me.DeptNum2ndCheck.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DeptNum2ndCheck.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "Descr", 100, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DeptNum2ndCheck.Properties.DisplayMember = "Descr"
        Me.DeptNum2ndCheck.Properties.NullText = ""
        Me.DeptNum2ndCheck.Properties.ShowHeader = False
        Me.DeptNum2ndCheck.Properties.ValidateOnEnterKey = True
        Me.DeptNum2ndCheck.Properties.ValueMember = "DEPTNUM"
        Me.DeptNum2ndCheck.Size = New System.Drawing.Size(100, 20)
        Me.DeptNum2ndCheck.TabIndex = 15
        '
        'DivNum2ndCheck
        '
        Me.DivNum2ndCheck.Location = New System.Drawing.Point(105, 110)
        Me.DivNum2ndCheck.Name = "DivNum2ndCheck"
        Me.DivNum2ndCheck.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DivNum2ndCheck.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Descr", "", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.DivNum2ndCheck.Properties.DisplayMember = "Descr"
        Me.DivNum2ndCheck.Properties.NullText = ""
        Me.DivNum2ndCheck.Properties.ShowHeader = False
        Me.DivNum2ndCheck.Properties.ValidateOnEnterKey = True
        Me.DivNum2ndCheck.Properties.ValueMember = "DDIVNUM"
        Me.DivNum2ndCheck.Size = New System.Drawing.Size(100, 20)
        Me.DivNum2ndCheck.TabIndex = 13
        '
        'PayDedCodeLookUpEdit
        '
        Me.PayDedCodeLookUpEdit.Location = New System.Drawing.Point(105, 31)
        Me.PayDedCodeLookUpEdit.Name = "PayDedCodeLookUpEdit"
        Me.PayDedCodeLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.PayDedCodeLookUpEdit.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("OTH_PAY_DESC", "OTH_PAY_DESC", 150, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.PayDedCodeLookUpEdit.Properties.DisplayMember = "OTH_PAY_DESC"
        Me.PayDedCodeLookUpEdit.Properties.NullText = ""
        Me.PayDedCodeLookUpEdit.Properties.PopupFormMinSize = New System.Drawing.Size(150, 0)
        Me.PayDedCodeLookUpEdit.Properties.ShowHeader = False
        Me.PayDedCodeLookUpEdit.Properties.ValueMember = "OTH_PAY_NUM"
        Me.PayDedCodeLookUpEdit.Size = New System.Drawing.Size(200, 20)
        Me.PayDedCodeLookUpEdit.TabIndex = 6
        '
        'CheckCounterTextEdit
        '
        Me.CheckCounterTextEdit.Location = New System.Drawing.Point(105, 83)
        Me.CheckCounterTextEdit.Name = "CheckCounterTextEdit"
        Me.CheckCounterTextEdit.Properties.Mask.EditMask = "f0"
        Me.CheckCounterTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CheckCounterTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.CheckCounterTextEdit.TabIndex = 11
        '
        'PayDedAmountTextEdit
        '
        Me.PayDedAmountTextEdit.Location = New System.Drawing.Point(105, 57)
        Me.PayDedAmountTextEdit.Name = "PayDedAmountTextEdit"
        Me.PayDedAmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.PayDedAmountTextEdit.Properties.Mask.EditMask = "c"
        Me.PayDedAmountTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.PayDedAmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.PayDedAmountTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.PayDedAmountTextEdit.TabIndex = 8
        '
        'PayDedTypeTextEdit
        '
        Me.PayDedTypeTextEdit.Location = New System.Drawing.Point(105, 5)
        Me.PayDedTypeTextEdit.Name = "PayDedTypeTextEdit"
        Me.PayDedTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.PayDedTypeTextEdit.Properties.Items.AddRange(New Object() {"P", "D"})
        Me.PayDedTypeTextEdit.Size = New System.Drawing.Size(48, 20)
        Me.PayDedTypeTextEdit.TabIndex = 4
        '
        'pnlRecordType
        '
        Me.pnlRecordType.Controls.Add(Me.RecordTypeTextEdit)
        Me.pnlRecordType.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlRecordType.Location = New System.Drawing.Point(0, 0)
        Me.pnlRecordType.Name = "pnlRecordType"
        Me.pnlRecordType.Size = New System.Drawing.Size(548, 27)
        Me.pnlRecordType.TabIndex = 1
        '
        'RecordTypeTextEdit
        '
        Me.RecordTypeTextEdit.Location = New System.Drawing.Point(105, 3)
        Me.RecordTypeTextEdit.Name = "RecordTypeTextEdit"
        Me.RecordTypeTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RecordTypeTextEdit.Properties.Items.AddRange(New Object() {"Second Check", "Net Override", "Auto OT Hours", "Pay Only On", "General Options"})
        Me.RecordTypeTextEdit.Size = New System.Drawing.Size(100, 20)
        Me.RecordTypeTextEdit.TabIndex = 2
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(17, 46)
        Me.GridControl1.MainView = Me.GridView9
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(191, 264)
        Me.GridControl1.TabIndex = 21
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView9})
        '
        'GridView9
        '
        Me.GridView9.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn59, Me.GridColumn60})
        Me.GridView9.GridControl = Me.GridControl1
        Me.GridView9.Name = "GridView9"
        Me.GridView9.OptionsBehavior.Editable = False
        Me.GridView9.OptionsCustomization.AllowColumnMoving = False
        Me.GridView9.OptionsCustomization.AllowGroup = False
        Me.GridView9.OptionsMenu.EnableColumnMenu = False
        Me.GridView9.OptionsView.ShowGroupPanel = False
        '
        'GridColumn59
        '
        Me.GridColumn59.FieldName = "RecordType"
        Me.GridColumn59.Name = "GridColumn59"
        Me.GridColumn59.Visible = True
        Me.GridColumn59.VisibleIndex = 0
        Me.GridColumn59.Width = 100
        '
        'GridColumn60
        '
        Me.GridColumn60.Caption = "Chk Cnt"
        Me.GridColumn60.FieldName = "CheckCounter"
        Me.GridColumn60.Name = "GridColumn60"
        Me.GridColumn60.Visible = True
        Me.GridColumn60.VisibleIndex = 1
        Me.GridColumn60.Width = 50
        '
        'PanelControl5
        '
        Me.PanelControl5.Controls.Add(Me.GroupControl6)
        Me.PanelControl5.Location = New System.Drawing.Point(291, 84)
        Me.PanelControl5.Name = "PanelControl5"
        Me.PanelControl5.Size = New System.Drawing.Size(939, 585)
        Me.PanelControl5.TabIndex = 23
        Me.PanelControl5.Visible = False
        '
        'GroupControl6
        '
        Me.GroupControl6.Controls.Add(Me.LabelControl6)
        Me.GroupControl6.Controls.Add(Me.LabelControl5)
        Me.GroupControl6.Controls.Add(Me.DD_STATUS_4ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_AMT_4TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_STATUS_3ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_MET_4ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_AMT_3TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_NO_4TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_MET_3ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_BANK_RT_4TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_NO_3TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_TYPE_4ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_BANK_RT_3TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_TYPE_3ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.LabelControl4)
        Me.GroupControl6.Controls.Add(Me.LabelControl3)
        Me.GroupControl6.Controls.Add(Me.DD_STATUS_2ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_AMT_2TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_MET_2ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_NO_2TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_BANK_RT_2TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_TYPE_2ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_STATUS_1ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_MET_1ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_TYPE_1ComboBoxEdit)
        Me.GroupControl6.Controls.Add(Me.DD_ACC_NO_1TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_BANK_RT_1TextEdit)
        Me.GroupControl6.Controls.Add(Me.DD_SPLIT_AMT_1TextEdit)
        Me.GroupControl6.Location = New System.Drawing.Point(14, 17)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(423, 471)
        Me.GroupControl6.TabIndex = 2
        '
        'LabelControl6
        '
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl6.Appearance.Options.UseFont = True
        Me.LabelControl6.Location = New System.Drawing.Point(266, 229)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(86, 13)
        Me.LabelControl6.TabIndex = 59
        Me.LabelControl6.Text = "Fourth Account"
        '
        'LabelControl5
        '
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.Options.UseFont = True
        Me.LabelControl5.Location = New System.Drawing.Point(116, 229)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(78, 13)
        Me.LabelControl5.TabIndex = 59
        Me.LabelControl5.Text = "Third Account"
        '
        'DD_STATUS_4ComboBoxEdit
        '
        Me.DD_STATUS_4ComboBoxEdit.Location = New System.Drawing.Point(266, 378)
        Me.DD_STATUS_4ComboBoxEdit.Name = "DD_STATUS_4ComboBoxEdit"
        Me.DD_STATUS_4ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_4ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_4ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_4ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_4ComboBoxEdit.TabIndex = 58
        '
        'DD_SPLIT_AMT_4TextEdit
        '
        Me.DD_SPLIT_AMT_4TextEdit.Location = New System.Drawing.Point(266, 352)
        Me.DD_SPLIT_AMT_4TextEdit.Name = "DD_SPLIT_AMT_4TextEdit"
        Me.DD_SPLIT_AMT_4TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_4TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_4TextEdit.TabIndex = 57
        '
        'DD_STATUS_3ComboBoxEdit
        '
        Me.DD_STATUS_3ComboBoxEdit.Location = New System.Drawing.Point(116, 378)
        Me.DD_STATUS_3ComboBoxEdit.Name = "DD_STATUS_3ComboBoxEdit"
        Me.DD_STATUS_3ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_3ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_3ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_3ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_3ComboBoxEdit.TabIndex = 58
        '
        'DD_SPLIT_MET_4ComboBoxEdit
        '
        Me.DD_SPLIT_MET_4ComboBoxEdit.Location = New System.Drawing.Point(266, 326)
        Me.DD_SPLIT_MET_4ComboBoxEdit.Name = "DD_SPLIT_MET_4ComboBoxEdit"
        Me.DD_SPLIT_MET_4ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_4ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_4ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_4ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_4ComboBoxEdit.TabIndex = 56
        '
        'DD_SPLIT_AMT_3TextEdit
        '
        Me.DD_SPLIT_AMT_3TextEdit.Location = New System.Drawing.Point(116, 352)
        Me.DD_SPLIT_AMT_3TextEdit.Name = "DD_SPLIT_AMT_3TextEdit"
        Me.DD_SPLIT_AMT_3TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_3TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_3TextEdit.TabIndex = 57
        '
        'DD_ACC_NO_4TextEdit
        '
        Me.DD_ACC_NO_4TextEdit.Location = New System.Drawing.Point(266, 300)
        Me.DD_ACC_NO_4TextEdit.Name = "DD_ACC_NO_4TextEdit"
        Me.DD_ACC_NO_4TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_4TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_4TextEdit.TabIndex = 55
        '
        'DD_SPLIT_MET_3ComboBoxEdit
        '
        Me.DD_SPLIT_MET_3ComboBoxEdit.Location = New System.Drawing.Point(116, 326)
        Me.DD_SPLIT_MET_3ComboBoxEdit.Name = "DD_SPLIT_MET_3ComboBoxEdit"
        Me.DD_SPLIT_MET_3ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_3ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_3ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_3ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_3ComboBoxEdit.TabIndex = 56
        '
        'DD_BANK_RT_4TextEdit
        '
        Me.DD_BANK_RT_4TextEdit.Location = New System.Drawing.Point(266, 274)
        Me.DD_BANK_RT_4TextEdit.Name = "DD_BANK_RT_4TextEdit"
        Me.DD_BANK_RT_4TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_4TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_4TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_4TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_4TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_4TextEdit.TabIndex = 54
        '
        'DD_ACC_NO_3TextEdit
        '
        Me.DD_ACC_NO_3TextEdit.Location = New System.Drawing.Point(116, 300)
        Me.DD_ACC_NO_3TextEdit.Name = "DD_ACC_NO_3TextEdit"
        Me.DD_ACC_NO_3TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_3TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_3TextEdit.TabIndex = 55
        '
        'DD_ACC_TYPE_4ComboBoxEdit
        '
        Me.DD_ACC_TYPE_4ComboBoxEdit.Location = New System.Drawing.Point(266, 248)
        Me.DD_ACC_TYPE_4ComboBoxEdit.Name = "DD_ACC_TYPE_4ComboBoxEdit"
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_4ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_4ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_4ComboBoxEdit.TabIndex = 53
        '
        'DD_BANK_RT_3TextEdit
        '
        Me.DD_BANK_RT_3TextEdit.Location = New System.Drawing.Point(116, 274)
        Me.DD_BANK_RT_3TextEdit.Name = "DD_BANK_RT_3TextEdit"
        Me.DD_BANK_RT_3TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_3TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_3TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_3TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_3TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_3TextEdit.TabIndex = 54
        '
        'DD_ACC_TYPE_3ComboBoxEdit
        '
        Me.DD_ACC_TYPE_3ComboBoxEdit.Location = New System.Drawing.Point(116, 248)
        Me.DD_ACC_TYPE_3ComboBoxEdit.Name = "DD_ACC_TYPE_3ComboBoxEdit"
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_3ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_3ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_3ComboBoxEdit.TabIndex = 53
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.Options.UseFont = True
        Me.LabelControl4.Location = New System.Drawing.Point(116, 34)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(74, 13)
        Me.LabelControl4.TabIndex = 52
        Me.LabelControl4.Text = "First Account"
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.Options.UseFont = True
        Me.LabelControl3.Location = New System.Drawing.Point(266, 34)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(90, 13)
        Me.LabelControl3.TabIndex = 51
        Me.LabelControl3.Text = "Second Account"
        '
        'DD_STATUS_2ComboBoxEdit
        '
        Me.DD_STATUS_2ComboBoxEdit.Location = New System.Drawing.Point(266, 183)
        Me.DD_STATUS_2ComboBoxEdit.Name = "DD_STATUS_2ComboBoxEdit"
        Me.DD_STATUS_2ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_2ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_2ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_2ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_2ComboBoxEdit.TabIndex = 50
        '
        'DD_SPLIT_AMT_2TextEdit
        '
        Me.DD_SPLIT_AMT_2TextEdit.Location = New System.Drawing.Point(266, 157)
        Me.DD_SPLIT_AMT_2TextEdit.Name = "DD_SPLIT_AMT_2TextEdit"
        Me.DD_SPLIT_AMT_2TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_2TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_2TextEdit.TabIndex = 49
        '
        'DD_SPLIT_MET_2ComboBoxEdit
        '
        Me.DD_SPLIT_MET_2ComboBoxEdit.Location = New System.Drawing.Point(266, 131)
        Me.DD_SPLIT_MET_2ComboBoxEdit.Name = "DD_SPLIT_MET_2ComboBoxEdit"
        Me.DD_SPLIT_MET_2ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_2ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_2ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_2ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_2ComboBoxEdit.TabIndex = 48
        '
        'DD_ACC_NO_2TextEdit
        '
        Me.DD_ACC_NO_2TextEdit.Location = New System.Drawing.Point(266, 105)
        Me.DD_ACC_NO_2TextEdit.Name = "DD_ACC_NO_2TextEdit"
        Me.DD_ACC_NO_2TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_2TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_2TextEdit.TabIndex = 47
        '
        'DD_BANK_RT_2TextEdit
        '
        Me.DD_BANK_RT_2TextEdit.Location = New System.Drawing.Point(266, 79)
        Me.DD_BANK_RT_2TextEdit.Name = "DD_BANK_RT_2TextEdit"
        Me.DD_BANK_RT_2TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_2TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_2TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_2TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_2TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_2TextEdit.TabIndex = 46
        '
        'DD_ACC_TYPE_2ComboBoxEdit
        '
        Me.DD_ACC_TYPE_2ComboBoxEdit.Location = New System.Drawing.Point(266, 53)
        Me.DD_ACC_TYPE_2ComboBoxEdit.Name = "DD_ACC_TYPE_2ComboBoxEdit"
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_2ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_2ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_2ComboBoxEdit.TabIndex = 45
        '
        'DD_STATUS_1ComboBoxEdit
        '
        Me.DD_STATUS_1ComboBoxEdit.Location = New System.Drawing.Point(116, 183)
        Me.DD_STATUS_1ComboBoxEdit.Name = "DD_STATUS_1ComboBoxEdit"
        Me.DD_STATUS_1ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_STATUS_1ComboBoxEdit.Properties.Items.AddRange(New Object() {"Pre-Note", "Active"})
        Me.DD_STATUS_1ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_STATUS_1ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_STATUS_1ComboBoxEdit.TabIndex = 44
        '
        'DD_SPLIT_MET_1ComboBoxEdit
        '
        Me.DD_SPLIT_MET_1ComboBoxEdit.Location = New System.Drawing.Point(116, 131)
        Me.DD_SPLIT_MET_1ComboBoxEdit.Name = "DD_SPLIT_MET_1ComboBoxEdit"
        Me.DD_SPLIT_MET_1ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_SPLIT_MET_1ComboBoxEdit.Properties.Items.AddRange(New Object() {"", "Flat Split", "Percent Split"})
        Me.DD_SPLIT_MET_1ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_MET_1ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_MET_1ComboBoxEdit.TabIndex = 43
        '
        'DD_ACC_TYPE_1ComboBoxEdit
        '
        Me.DD_ACC_TYPE_1ComboBoxEdit.Location = New System.Drawing.Point(116, 53)
        Me.DD_ACC_TYPE_1ComboBoxEdit.Name = "DD_ACC_TYPE_1ComboBoxEdit"
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.Items.AddRange(New Object() {"None", "Checking", "Savings"})
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DD_ACC_TYPE_1ComboBoxEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_TYPE_1ComboBoxEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_TYPE_1ComboBoxEdit.TabIndex = 42
        '
        'DD_ACC_NO_1TextEdit
        '
        Me.DD_ACC_NO_1TextEdit.Location = New System.Drawing.Point(116, 105)
        Me.DD_ACC_NO_1TextEdit.Name = "DD_ACC_NO_1TextEdit"
        Me.DD_ACC_NO_1TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_ACC_NO_1TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_ACC_NO_1TextEdit.TabIndex = 33
        '
        'DD_BANK_RT_1TextEdit
        '
        Me.DD_BANK_RT_1TextEdit.Location = New System.Drawing.Point(116, 79)
        Me.DD_BANK_RT_1TextEdit.Name = "DD_BANK_RT_1TextEdit"
        Me.DD_BANK_RT_1TextEdit.Properties.Mask.EditMask = "*********"
        Me.DD_BANK_RT_1TextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.DD_BANK_RT_1TextEdit.Properties.MaxLength = 9
        Me.DD_BANK_RT_1TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_BANK_RT_1TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_BANK_RT_1TextEdit.TabIndex = 32
        '
        'DD_SPLIT_AMT_1TextEdit
        '
        Me.DD_SPLIT_AMT_1TextEdit.Location = New System.Drawing.Point(116, 157)
        Me.DD_SPLIT_AMT_1TextEdit.Name = "DD_SPLIT_AMT_1TextEdit"
        Me.DD_SPLIT_AMT_1TextEdit.Properties.ValidateOnEnterKey = True
        Me.DD_SPLIT_AMT_1TextEdit.Size = New System.Drawing.Size(133, 20)
        Me.DD_SPLIT_AMT_1TextEdit.TabIndex = 23
        '
        'PanelControl6
        '
        Me.PanelControl6.Controls.Add(Me.UcNotes1)
        Me.PanelControl6.Location = New System.Drawing.Point(291, 84)
        Me.PanelControl6.Name = "PanelControl6"
        Me.PanelControl6.Size = New System.Drawing.Size(939, 585)
        Me.PanelControl6.TabIndex = 24
        Me.PanelControl6.Visible = False
        '
        'UcNotes1
        '
        Me.UcNotes1.CoNum = New Decimal(New Integer() {0, 0, 0, 0})
        Me.UcNotes1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.UcNotes1.Location = New System.Drawing.Point(2, 2)
        Me.UcNotes1.Name = "UcNotes1"
        Me.UcNotes1.Size = New System.Drawing.Size(935, 581)
        Me.UcNotes1.TabIndex = 1
        '
        'lcEmpName
        '
        Me.lcEmpName.Appearance.Font = New System.Drawing.Font("Tahoma", 20.0!)
        Me.lcEmpName.Appearance.ForeColor = System.Drawing.Color.Blue
        Me.lcEmpName.Appearance.Options.UseFont = True
        Me.lcEmpName.Appearance.Options.UseForeColor = True
        Me.lcEmpName.Location = New System.Drawing.Point(278, 12)
        Me.lcEmpName.Name = "lcEmpName"
        Me.lcEmpName.Size = New System.Drawing.Size(449, 33)
        Me.lcEmpName.StyleController = Me.LayoutControl2
        Me.lcEmpName.TabIndex = 8
        Me.lcEmpName.Text = "LabelControl1"
        '
        'gcCheckMaster
        '
        Me.gcCheckMaster.Location = New System.Drawing.Point(291, 84)
        Me.gcCheckMaster.MainView = Me.GridView10
        Me.gcCheckMaster.Name = "gcCheckMaster"
        Me.gcCheckMaster.Size = New System.Drawing.Size(834, 315)
        Me.gcCheckMaster.TabIndex = 7
        Me.gcCheckMaster.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView10})
        Me.gcCheckMaster.Visible = False
        '
        'GridView10
        '
        Me.GridView10.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn61, Me.GridColumn62, Me.GridColumn63, Me.GridColumn64, Me.GridColumn65, Me.GridColumn66, Me.GridColumn67, Me.GridColumn68, Me.GridColumn69, Me.GridColumn70, Me.GridColumn71})
        Me.GridView10.GridControl = Me.gcCheckMaster
        Me.GridView10.Name = "GridView10"
        Me.GridView10.OptionsBehavior.Editable = False
        Me.GridView10.OptionsSelection.CheckBoxSelectorColumnWidth = 35
        Me.GridView10.OptionsSelection.MultiSelect = True
        Me.GridView10.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect
        Me.GridView10.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView10.OptionsView.ColumnAutoWidth = False
        Me.GridView10.OptionsView.ShowAutoFilterRow = True
        Me.GridView10.OptionsView.ShowGroupPanel = False
        '
        'GridColumn61
        '
        Me.GridColumn61.DisplayFormat.FormatString = "d"
        Me.GridColumn61.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.GridColumn61.FieldName = "CheckDate"
        Me.GridColumn61.Name = "GridColumn61"
        Me.GridColumn61.Visible = True
        Me.GridColumn61.VisibleIndex = 1
        '
        'GridColumn62
        '
        Me.GridColumn62.FieldName = "CheckType"
        Me.GridColumn62.Name = "GridColumn62"
        Me.GridColumn62.Visible = True
        Me.GridColumn62.VisibleIndex = 2
        '
        'GridColumn63
        '
        Me.GridColumn63.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.GridColumn63.FieldName = "Hours"
        Me.GridColumn63.Name = "GridColumn63"
        Me.GridColumn63.Visible = True
        Me.GridColumn63.VisibleIndex = 3
        '
        'GridColumn64
        '
        Me.GridColumn64.FieldName = "Gross"
        Me.GridColumn64.Name = "GridColumn64"
        Me.GridColumn64.Visible = True
        Me.GridColumn64.VisibleIndex = 4
        '
        'GridColumn65
        '
        Me.GridColumn65.FieldName = "Deds"
        Me.GridColumn65.Name = "GridColumn65"
        Me.GridColumn65.Visible = True
        Me.GridColumn65.VisibleIndex = 5
        '
        'GridColumn66
        '
        Me.GridColumn66.FieldName = "Taxes"
        Me.GridColumn66.Name = "GridColumn66"
        Me.GridColumn66.Visible = True
        Me.GridColumn66.VisibleIndex = 6
        '
        'GridColumn67
        '
        Me.GridColumn67.FieldName = "DirDeposit"
        Me.GridColumn67.Name = "GridColumn67"
        Me.GridColumn67.Visible = True
        Me.GridColumn67.VisibleIndex = 7
        '
        'GridColumn68
        '
        Me.GridColumn68.FieldName = "Net"
        Me.GridColumn68.Name = "GridColumn68"
        Me.GridColumn68.Visible = True
        Me.GridColumn68.VisibleIndex = 8
        '
        'GridColumn69
        '
        Me.GridColumn69.FieldName = "CheckAmout"
        Me.GridColumn69.Name = "GridColumn69"
        Me.GridColumn69.Visible = True
        Me.GridColumn69.VisibleIndex = 9
        '
        'GridColumn70
        '
        Me.GridColumn70.FieldName = "CheckNumber"
        Me.GridColumn70.Name = "GridColumn70"
        Me.GridColumn70.Visible = True
        Me.GridColumn70.VisibleIndex = 10
        '
        'GridColumn71
        '
        Me.GridColumn71.Caption = "Pr#"
        Me.GridColumn71.FieldName = "PrNum"
        Me.GridColumn71.Name = "GridColumn71"
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup3.GroupBordersVisible = False
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.SplitterItem1, Me.LayoutControlGroup4, Me.LayoutControlItem16, Me.LayoutControlItem17, Me.LayoutControlItem18, Me.LayoutControlItem19, Me.EmptySpaceItem4, Me.LayoutControlItem20, Me.LayoutControlGroup12})
        Me.LayoutControlGroup3.Name = "Root"
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(1255, 694)
        Me.LayoutControlGroup3.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.lcEmpName
        Me.LayoutControlItem1.Location = New System.Drawing.Point(266, 0)
        Me.LayoutControlItem1.MinSize = New System.Drawing.Size(167, 37)
        Me.LayoutControlItem1.Name = "LayoutControlItem5"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(453, 37)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.IsCollapsible = DevExpress.Utils.DefaultBoolean.[True]
        Me.SplitterItem1.Location = New System.Drawing.Point(261, 0)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(5, 674)
        '
        'LayoutControlGroup4
        '
        Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.TabbedControlGroup1})
        Me.LayoutControlGroup4.Location = New System.Drawing.Point(266, 37)
        Me.LayoutControlGroup4.Name = "LayoutControlGroupTabs"
        Me.LayoutControlGroup4.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup4.Size = New System.Drawing.Size(969, 637)
        Me.LayoutControlGroup4.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup4.TextVisible = False
        '
        'TabbedControlGroup1
        '
        Me.TabbedControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
        Me.TabbedControlGroup1.SelectedTabPage = Me.LayoutControlGroup5
        Me.TabbedControlGroup1.SelectedTabPageIndex = 1
        Me.TabbedControlGroup1.Size = New System.Drawing.Size(967, 635)
        Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup6, Me.LayoutControlGroup5, Me.LayoutControlGroup7, Me.LayoutControlGroup8, Me.LayoutControlGroup9, Me.LayoutControlGroup10, Me.LayoutControlGroup11})
        '
        'LayoutControlGroup5
        '
        Me.LayoutControlGroup5.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2})
        Me.LayoutControlGroup5.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup5.Name = "LayoutControlGroupPayrollTax"
        Me.LayoutControlGroup5.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup5.Text = "Payroll / Tax Info"
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.PanelControlPayrollTax
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem9"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlGroup6
        '
        Me.LayoutControlGroup6.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3})
        Me.LayoutControlGroup6.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup6.Name = "LayoutControlGroup4"
        Me.LayoutControlGroup6.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup6.Text = "Primary Info"
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.PanelControl2
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlGroup7
        '
        Me.LayoutControlGroup7.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4})
        Me.LayoutControlGroup7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup7.Name = "layou"
        Me.LayoutControlGroup7.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup7.Text = "Check Overrides Setup"
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.PanelControl4
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem10"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlGroup8
        '
        Me.LayoutControlGroup8.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem5})
        Me.LayoutControlGroup8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup8.Name = "LayoutControlGroup7"
        Me.LayoutControlGroup8.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup8.Text = "Direct Deposit"
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.PanelControl5
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem5.Name = "LayoutControlItem11"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlGroup9
        '
        Me.LayoutControlGroup9.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem6})
        Me.LayoutControlGroup9.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup9.Name = "LayoutControlGroup8"
        Me.LayoutControlGroup9.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup9.Text = "Notes"
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.PanelControl6
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem12"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlGroup10
        '
        Me.LayoutControlGroup10.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem9, Me.LayoutControlItem10, Me.EmptySpaceItem1, Me.EmptySpaceItem2})
        Me.LayoutControlGroup10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup10.Name = "tabAutoPaysAndDeds"
        Me.LayoutControlGroup10.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup10.Text = "Auto Pays / Deds"
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.Panel1
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem7.MaxSize = New System.Drawing.Size(0, 37)
        Me.LayoutControlItem7.MinSize = New System.Drawing.Size(104, 37)
        Me.LayoutControlItem7.Name = "LayoutControlItem19"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(879, 37)
        Me.LayoutControlItem7.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.GridPays
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 37)
        Me.LayoutControlItem8.Name = "LayoutControlItem20"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(879, 184)
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.Panel2
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 221)
        Me.LayoutControlItem9.MaxSize = New System.Drawing.Size(0, 34)
        Me.LayoutControlItem9.MinSize = New System.Drawing.Size(104, 34)
        Me.LayoutControlItem9.Name = "LayoutControlItem21"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(879, 34)
        Me.LayoutControlItem9.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.GridDeds
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 255)
        Me.LayoutControlItem10.Name = "LayoutControlItem22"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(879, 211)
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem10.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 466)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem4"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(879, 123)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(879, 0)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(64, 589)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlGroup11
        '
        Me.LayoutControlGroup11.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem11, Me.LayoutControlItem12, Me.SplitterItem2, Me.LayoutControlItem13, Me.EmptySpaceItem3, Me.LayoutControlItem14, Me.LayoutControlItem15, Me.SplitterItem3})
        Me.LayoutControlGroup11.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup11.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup11.Size = New System.Drawing.Size(943, 589)
        Me.LayoutControlGroup11.Text = "Pay History"
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.gcCheckMaster
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem11.Name = "LayoutControlItem4"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(838, 319)
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem11.TextVisible = False
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.PanelControl1
        Me.LayoutControlItem12.Location = New System.Drawing.Point(838, 0)
        Me.LayoutControlItem12.Name = "LayoutControlItem13"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(105, 319)
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem12.TextVisible = False
        '
        'SplitterItem2
        '
        Me.SplitterItem2.AllowHotTrack = True
        Me.SplitterItem2.Location = New System.Drawing.Point(0, 319)
        Me.SplitterItem2.Name = "SplitterItem2"
        Me.SplitterItem2.Size = New System.Drawing.Size(943, 5)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.gcPayHistory
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 324)
        Me.LayoutControlItem13.Name = "LayoutControlItem6"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(713, 265)
        Me.LayoutControlItem13.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem13.TextVisible = False
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(713, 324)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(25, 265)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.gcPayrollDeds
        Me.LayoutControlItem14.CustomizationFormText = "LayoutControlItemDeductions"
        Me.LayoutControlItem14.Location = New System.Drawing.Point(738, 324)
        Me.LayoutControlItem14.Name = "LayoutControlItemDeductions"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(205, 116)
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem14.TextVisible = False
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.gcPayrollTaxes
        Me.LayoutControlItem15.Location = New System.Drawing.Point(738, 445)
        Me.LayoutControlItem15.Name = "LayoutControlItem7"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(205, 144)
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem15.TextVisible = False
        '
        'SplitterItem3
        '
        Me.SplitterItem3.AllowHotTrack = True
        Me.SplitterItem3.Location = New System.Drawing.Point(738, 440)
        Me.SplitterItem3.Name = "SplitterItem3"
        Me.SplitterItem3.Size = New System.Drawing.Size(205, 5)
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.btnSave
        Me.LayoutControlItem16.Location = New System.Drawing.Point(894, 0)
        Me.LayoutControlItem16.Name = "LayoutControlItem15"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(56, 37)
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem16.TextVisible = False
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.btnCancel
        Me.LayoutControlItem17.Location = New System.Drawing.Point(950, 0)
        Me.LayoutControlItem17.Name = "LayoutControlItem16"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(64, 37)
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem17.TextVisible = False
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.btnNewEmployee
        Me.LayoutControlItem18.Location = New System.Drawing.Point(1014, 0)
        Me.LayoutControlItem18.Name = "LayoutControlItem17"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(102, 37)
        Me.LayoutControlItem18.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem18.TextVisible = False
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.btnCopy
        Me.LayoutControlItem19.Location = New System.Drawing.Point(1116, 0)
        Me.LayoutControlItem19.Name = "LayoutControlItem18"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(119, 37)
        Me.LayoutControlItem19.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem19.TextVisible = False
        '
        'EmptySpaceItem4
        '
        Me.EmptySpaceItem4.AllowHotTrack = False
        Me.EmptySpaceItem4.Location = New System.Drawing.Point(719, 0)
        Me.EmptySpaceItem4.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem4.Size = New System.Drawing.Size(10, 37)
        Me.EmptySpaceItem4.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.btnSaveAndCopyPays
        Me.LayoutControlItem20.Location = New System.Drawing.Point(729, 0)
        Me.LayoutControlItem20.Name = "btnSaveAndCopyPaysLI"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(165, 37)
        Me.LayoutControlItem20.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem20.TextVisible = False
        '
        'LayoutControlGroup12
        '
        Me.LayoutControlGroup12.GroupBordersVisible = False
        Me.LayoutControlGroup12.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup13})
        Me.LayoutControlGroup12.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup12.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup12.Size = New System.Drawing.Size(261, 674)
        '
        'LayoutControlGroup13
        '
        Me.LayoutControlGroup13.AppearanceGroup.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold)
        Me.LayoutControlGroup13.AppearanceGroup.Options.UseFont = True
        Me.LayoutControlGroup13.AppearanceGroup.Options.UseTextOptions = True
        Me.LayoutControlGroup13.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlGroup13.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup13.Name = "LayoutControlGroupEmployees"
        Me.LayoutControlGroup13.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup13.Size = New System.Drawing.Size(261, 674)
        Me.LayoutControlGroup13.Text = "Employees"
        '
        'popupCopyFrom
        '
        Me.popupCopyFrom.Controls.Add(Me.GroupControl10)
        Me.popupCopyFrom.Location = New System.Drawing.Point(604, 268)
        Me.popupCopyFrom.Name = "popupCopyFrom"
        Me.popupCopyFrom.Size = New System.Drawing.Size(405, 160)
        Me.popupCopyFrom.TabIndex = 12
        '
        'GroupControl10
        '
        Me.GroupControl10.Controls.Add(Me.txtEmpNum)
        Me.GroupControl10.Controls.Add(Me.txtCoName)
        Me.GroupControl10.Controls.Add(Me.LabelControl14)
        Me.GroupControl10.Controls.Add(Me.slueEmployeeCopyFrom)
        Me.GroupControl10.Controls.Add(Me.LabelControl13)
        Me.GroupControl10.Controls.Add(Me.btnOk)
        Me.GroupControl10.Controls.Add(Me.btnCancelCopyToNewCompany)
        Me.GroupControl10.Controls.Add(Me.slueCompanyCopyFrom)
        Me.GroupControl10.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl10.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl10.LookAndFeel.SkinName = "Office 2010 Blue"
        Me.GroupControl10.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl10.Name = "GroupControl10"
        Me.GroupControl10.Size = New System.Drawing.Size(405, 160)
        Me.GroupControl10.TabIndex = 11
        Me.GroupControl10.Text = "Select Source Company && Employee "
        '
        'txtEmpNum
        '
        Me.txtEmpNum.Location = New System.Drawing.Point(144, 62)
        Me.txtEmpNum.Name = "txtEmpNum"
        Me.txtEmpNum.Properties.ReadOnly = True
        Me.txtEmpNum.Size = New System.Drawing.Size(232, 20)
        Me.txtEmpNum.TabIndex = 7
        '
        'txtCoName
        '
        Me.txtCoName.Location = New System.Drawing.Point(144, 35)
        Me.txtCoName.Name = "txtCoName"
        Me.txtCoName.Properties.ReadOnly = True
        Me.txtCoName.Size = New System.Drawing.Size(232, 20)
        Me.txtCoName.TabIndex = 6
        '
        'LabelControl14
        '
        Me.LabelControl14.Location = New System.Drawing.Point(13, 65)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(35, 13)
        Me.LabelControl14.TabIndex = 5
        Me.LabelControl14.Text = "Emp #:"
        '
        'slueEmployeeCopyFrom
        '
        Me.slueEmployeeCopyFrom.EditValue = ""
        Me.slueEmployeeCopyFrom.Location = New System.Drawing.Point(51, 62)
        Me.slueEmployeeCopyFrom.Name = "slueEmployeeCopyFrom"
        Me.slueEmployeeCopyFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueEmployeeCopyFrom.Properties.DisplayMember = "EMPNUM"
        Me.slueEmployeeCopyFrom.Properties.NullText = ""
        Me.slueEmployeeCopyFrom.Properties.PopupFormSize = New System.Drawing.Size(250, 0)
        Me.slueEmployeeCopyFrom.Properties.PopupView = Me.GridView11
        Me.slueEmployeeCopyFrom.Properties.ValueMember = "EMPNUM"
        Me.slueEmployeeCopyFrom.Size = New System.Drawing.Size(87, 20)
        Me.slueEmployeeCopyFrom.TabIndex = 1
        '
        'GridView11
        '
        Me.GridView11.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn72, Me.GridColumn73})
        Me.GridView11.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView11.Name = "GridView11"
        Me.GridView11.OptionsFind.FindDelay = 100
        Me.GridView11.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView11.OptionsView.ColumnAutoWidth = False
        Me.GridView11.OptionsView.ShowGroupPanel = False
        '
        'GridColumn72
        '
        Me.GridColumn72.FieldName = "EMPNUM"
        Me.GridColumn72.Name = "GridColumn72"
        Me.GridColumn72.Visible = True
        Me.GridColumn72.VisibleIndex = 0
        '
        'GridColumn73
        '
        Me.GridColumn73.FieldName = "EmpName"
        Me.GridColumn73.Name = "GridColumn73"
        Me.GridColumn73.Visible = True
        Me.GridColumn73.VisibleIndex = 1
        Me.GridColumn73.Width = 150
        '
        'LabelControl13
        '
        Me.LabelControl13.Location = New System.Drawing.Point(13, 38)
        Me.LabelControl13.Name = "LabelControl13"
        Me.LabelControl13.Size = New System.Drawing.Size(32, 13)
        Me.LabelControl13.TabIndex = 3
        Me.LabelControl13.Text = "Co. #:"
        '
        'btnOk
        '
        Me.btnOk.Location = New System.Drawing.Point(201, 112)
        Me.btnOk.Name = "btnOk"
        Me.btnOk.Size = New System.Drawing.Size(73, 23)
        Me.btnOk.TabIndex = 2
        Me.btnOk.Text = "Ok"
        '
        'btnCancelCopyToNewCompany
        '
        Me.btnCancelCopyToNewCompany.Location = New System.Drawing.Point(104, 112)
        Me.btnCancelCopyToNewCompany.Name = "btnCancelCopyToNewCompany"
        Me.btnCancelCopyToNewCompany.Size = New System.Drawing.Size(70, 23)
        Me.btnCancelCopyToNewCompany.TabIndex = 3
        Me.btnCancelCopyToNewCompany.Text = "Cancel"
        '
        'slueCompanyCopyFrom
        '
        Me.slueCompanyCopyFrom.EditValue = ""
        Me.slueCompanyCopyFrom.Location = New System.Drawing.Point(51, 35)
        Me.slueCompanyCopyFrom.Name = "slueCompanyCopyFrom"
        Me.slueCompanyCopyFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueCompanyCopyFrom.Properties.DisplayMember = "CONUM"
        Me.slueCompanyCopyFrom.Properties.NullText = ""
        Me.slueCompanyCopyFrom.Properties.PopupFormSize = New System.Drawing.Size(350, 0)
        Me.slueCompanyCopyFrom.Properties.PopupView = Me.GridView12
        Me.slueCompanyCopyFrom.Properties.ValueMember = "CONUM"
        Me.slueCompanyCopyFrom.Size = New System.Drawing.Size(87, 20)
        Me.slueCompanyCopyFrom.TabIndex = 0
        '
        'GridView12
        '
        Me.GridView12.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn74, Me.GridColumn75, Me.GridColumn76})
        Me.GridView12.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView12.Name = "GridView12"
        Me.GridView12.OptionsFind.FindDelay = 100
        Me.GridView12.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView12.OptionsView.ColumnAutoWidth = False
        Me.GridView12.OptionsView.ShowGroupPanel = False
        '
        'GridColumn74
        '
        Me.GridColumn74.FieldName = "CONUM"
        Me.GridColumn74.Name = "GridColumn74"
        Me.GridColumn74.Visible = True
        Me.GridColumn74.VisibleIndex = 0
        '
        'GridColumn75
        '
        Me.GridColumn75.FieldName = "CO_NAME"
        Me.GridColumn75.Name = "GridColumn75"
        Me.GridColumn75.Visible = True
        Me.GridColumn75.VisibleIndex = 1
        Me.GridColumn75.Width = 150
        '
        'GridColumn76
        '
        Me.GridColumn76.FieldName = "FED_ID"
        Me.GridColumn76.Name = "GridColumn76"
        Me.GridColumn76.Visible = True
        Me.GridColumn76.VisibleIndex = 2
        '
        'ucEmployeeList
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.BarDockControl3)
        Me.Controls.Add(Me.BarDockControl4)
        Me.Controls.Add(Me.BarDockControl2)
        Me.Controls.Add(Me.BarDockControl1)
        Me.Name = "ucEmployeeList"
        Me.Size = New System.Drawing.Size(1268, 722)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.gcEmployeeList, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmployeeListBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvEmployeeList, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riDivision, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DIVISIONBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riDepartments, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEPARTMENTBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pmFilter, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroupEmployees, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.GridDeds, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.GridPays, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemLookUpEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcPayrollDeds, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcPayrollTaxes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcPayHistory, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        CType(Me.PanelControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl2.ResumeLayout(False)
        CType(Me.pnlTerminate, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTerminate.ResumeLayout(False)
        Me.pnlTerminate.PerformLayout()
        CType(Me.PanelControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl8.ResumeLayout(False)
        CType(Me.EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        CType(Me.Default_hoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SALARY_AMTTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RATE_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RATE_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RATE_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PAY_FREQTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AUTO_SAL_HRSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.GroupControl11, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl11.ResumeLayout(False)
        Me.GroupControl11.PerformLayout()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TINTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SSNTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.M_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CITYTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ADDR_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ZIPTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Contact_pagerTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GENDERLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Contact_homephoneTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.B_DAYDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.B_DAYDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.F_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Contact_homeemailTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.User_emailTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.STREETTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.L_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        CType(Me.START_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.START_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEPTNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DIVNUMLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMP_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TERM_DATEDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TERM_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControlPayrollTax, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControlPayrollTax.ResumeLayout(False)
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl9.ResumeLayout(False)
        Me.GroupControl9.PerformLayout()
        CType(Me.RadioGroup1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LocalActiveTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Fixed_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Extra_whTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DependentsTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstSelectedLocals, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstAvailLocals, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Alternate_w4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Local_exemptTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Local_statusTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl5.ResumeLayout(False)
        Me.GroupControl5.PerformLayout()
        CType(Me.FLIEXECheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstSelectedStates, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lstAvailableStates, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UCI_STATETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEFAULT_RESTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DEFAULT_WORKTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ALTW4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ST_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SDIEXETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SUTA_EXE_FGTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl4.ResumeLayout(False)
        CType(Me.FED_WH_FIXEDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_WH_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OASDI_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_WH_EXTRATextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FUTA_EXE_FGETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_DEPSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FED_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl4.ResumeLayout(False)
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl7.ResumeLayout(False)
        CType(Me.GridControlCoOptionsSecondCheckPay, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemLookUpEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlOverrideGroups.ResumeLayout(False)
        Me.pnlGeneralOptions.ResumeLayout(False)
        CType(Me.ExcludeFromUtilityImportCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl8.ResumeLayout(False)
        CType(Me.OTSeperateCheckHoursMoreThanTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OTSeperateCheckCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ddlPayUnderEmpNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.grpSecondCheckTaxes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpSecondCheckTaxes.ResumeLayout(False)
        CType(Me.UIStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ResStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.WrkStateComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FLIOverrdieAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.MedicareOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OASDIOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FedOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TaxFrequencyTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DBOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.STOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LOCOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tbManualCheckNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.grpScheduling, System.ComponentModel.ISupportInitialize).EndInit()
        Me.grpScheduling.ResumeLayout(False)
        Me.grpScheduling.PerformLayout()
        CType(Me.LastOfMonthCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd5CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd4CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd3CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd2CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Prd1CheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AllPrdsCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pnlNetOverride, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlNetOverride.ResumeLayout(False)
        CType(Me.NetOverrideDedNumTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NetOverrideAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NetOverrideAdjustTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlOTHours.ResumeLayout(False)
        CType(Me.OTHoursTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSecondCheck.ResumeLayout(False)
        CType(Me.DeptNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DivNum2ndCheck.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PayDedCodeLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckCounterTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PayDedAmountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PayDedTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlRecordType.ResumeLayout(False)
        CType(Me.RecordTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl5.ResumeLayout(False)
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl6.ResumeLayout(False)
        Me.GroupControl6.PerformLayout()
        CType(Me.DD_STATUS_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_STATUS_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_4TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_4ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_3TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_3ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_STATUS_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_2TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_2ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_STATUS_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_MET_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_TYPE_1ComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_ACC_NO_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_BANK_RT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DD_SPLIT_AMT_1TextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl6.ResumeLayout(False)
        CType(Me.gcCheckMaster, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.popupCopyFrom, System.ComponentModel.ISupportInitialize).EndInit()
        Me.popupCopyFrom.ResumeLayout(False)
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl10.ResumeLayout(False)
        Me.GroupControl10.PerformLayout()
        CType(Me.txtEmpNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtCoName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueEmployeeCopyFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueCompanyCopyFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView12, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents gcEmployeeList As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvEmployeeList As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDepNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colStartDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn8 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colHasChanges As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents SearchControl1 As DevExpress.XtraEditors.SearchControl
    Friend WithEvents ddbFilter As DevExpress.XtraEditors.DropDownButton
    Friend WithEvents pmFilter As DevExpress.XtraBars.PopupMenu
    Friend WithEvents bciAllEmployees As DevExpress.XtraBars.BarCheckItem
    Friend WithEvents bciActiveOnly As DevExpress.XtraBars.BarCheckItem
    Friend WithEvents bciTerminatedOnly As DevExpress.XtraBars.BarCheckItem
    Friend WithEvents bciRecentlyAdded As DevExpress.XtraBars.BarCheckItem
    Friend WithEvents BarManager1 As DevExpress.XtraBars.BarManager
    Friend WithEvents BarDockControl1 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl2 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl3 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl4 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents LayoutControlGroup14 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroupEmployees As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem25 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem26 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem27 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem4 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents btnSaveAndCopyPays As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridDeds As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView3 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn21 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn22 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn23 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn24 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn25 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn26 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn27 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn28 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn29 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents Panel2 As Panel
    Friend WithEvents btnEditAutoDeduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl18 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAddAutoDeduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Panel1 As Panel
    Friend WithEvents btnEditAutoPay As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAddAutoPay As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl17 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GridPays As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView4 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn30 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemLookUpEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents GridColumn31 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn32 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn33 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn34 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn35 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn36 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn37 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn38 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnCopy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnNewEmployee As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents gcPayrollDeds As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView5 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn39 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn40 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents gcPayrollTaxes As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView6 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn41 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn42 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn43 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn44 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn45 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents gcPayHistory As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView7 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn46 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn47 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn48 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn49 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn50 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn51 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn52 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn53 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn54 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn55 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn56 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents btnYear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnLastYear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnQ4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnQ3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnQ2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnQ1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PanelControl2 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents btnParsonage As DevExpress.XtraEditors.CheckButton
    Friend WithEvents pnlTerminate As DevExpress.XtraEditors.PanelControl
    Friend WithEvents lblEmployeeStatus As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnTerminate As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PanelControl8 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents EMPNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Default_hoursTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SALARY_AMTTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RATE_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RATE_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RATE_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PAY_FREQTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents AUTO_SAL_HRSTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GroupControl11 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl16 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl15 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ComboBoxEdit2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents SSNLabelControl As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TINTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SSNTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents M_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CITYTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ADDR_STATETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ZIPTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Contact_pagerTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GENDERLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents Contact_homephoneTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents B_DAYDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents F_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Contact_homeemailTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents User_emailTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents STREETTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents L_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TINLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents START_DATEDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents DEPTNUMLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents DIVNUMLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents EMP_TYPETextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TERM_DATEDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents PanelControlPayrollTax As DevExpress.XtraEditors.PanelControl
    Friend WithEvents GroupControl9 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents RadioGroup1 As DevExpress.XtraEditors.RadioGroup
    Friend WithEvents LabelControl12 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LocalActiveTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Local_statusLabel As Label
    Friend WithEvents Fixed_whTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Extra_whTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DependentsLabel As Label
    Friend WithEvents DependentsTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Alternate_w4Label As Label
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAddLocal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnRemoveLocal As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lstSelectedLocals As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents lstAvailLocals As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents Alternate_w4TextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Local_exemptTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Local_statusTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents GroupControl5 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents FLIEXELabel As Label
    Friend WithEvents FLIEXECheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAddState As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnRemoveState As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lstSelectedStates As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents lstAvailableStates As DevExpress.XtraEditors.ListBoxControl
    Friend WithEvents ST_WH_FIXEDLabel As Label
    Friend WithEvents ST_WH_FIXEDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ST_WH_EXTRALabel As Label
    Friend WithEvents ST_WH_EXTRATextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SDIEXELabel As Label
    Friend WithEvents ST_DEPSLabel As Label
    Friend WithEvents ST_DEPSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ST_STATUSLabel As Label
    Friend WithEvents ST_WH_EXE_FGELabel As Label
    Friend WithEvents ALTW4Label As Label
    Friend WithEvents DEFAULT_WORKLabel As Label
    Friend WithEvents DEFAULT_RESLabel As Label
    Friend WithEvents UCI_STATETextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DEFAULT_RESTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents DEFAULT_WORKTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ALTW4TextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ST_WH_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ST_STATUSTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents SDIEXETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents SUTA_EXE_FGTextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl4 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents FED_WH_FIXEDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FED_WH_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents OASDI_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FED_WH_EXTRATextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FUTA_EXE_FGETextEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents FED_DEPSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FED_STATUSTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents PanelControl4 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents GroupControl7 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridControlCoOptionsSecondCheckPay As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView8 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn57 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemLookUpEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents GridColumn58 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnDelete As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents pnlOverrideGroups As Panel
    Friend WithEvents pnlGeneralOptions As Panel
    Friend WithEvents ExcludeFromUtilityImportCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl8 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents OTSeperateCheckHoursMoreThanTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents OTSeperateCheckCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ddlPayUnderEmpNum As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents grpSecondCheckTaxes As DevExpress.XtraEditors.GroupControl
    Friend WithEvents UIStateComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ResStateComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents WrkStateComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents FLIOverrdieAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents MedicareOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents OASDIOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FedOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TaxFrequencyTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DBOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents STOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LOCOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tbManualCheckNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents grpScheduling As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LastOfMonthCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd5CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd4CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd3CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd2CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Prd1CheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents AllPrdsCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents pnlNetOverride As DevExpress.XtraEditors.GroupControl
    Friend WithEvents NetOverrideDedNumTextEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents NetOverrideAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents NetOverrideAdjustTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents pnlOTHours As Panel
    Friend WithEvents OTHoursTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents pnlSecondCheck As Panel
    Friend WithEvents DeptNum2ndCheck As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents DivNum2ndCheck As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents PayDedCodeLookUpEdit As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents CheckCounterTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PayDedAmountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PayDedTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents pnlRecordType As Panel
    Friend WithEvents RecordTypeTextEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView9 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn59 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn60 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents PanelControl5 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents DD_STATUS_4ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_AMT_4TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_STATUS_3ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_MET_4ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_AMT_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_NO_4TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_SPLIT_MET_3ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_BANK_RT_4TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_NO_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_TYPE_4ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_BANK_RT_3TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_TYPE_3ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents DD_STATUS_2ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_AMT_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_SPLIT_MET_2ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_ACC_NO_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_BANK_RT_2TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_ACC_TYPE_2ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_STATUS_1ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_SPLIT_MET_1ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_ACC_TYPE_1ComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DD_ACC_NO_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_BANK_RT_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DD_SPLIT_AMT_1TextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PanelControl6 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents UcNotes1 As ucNotes
    Friend WithEvents lcEmpName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents gcCheckMaster As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView10 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn61 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn62 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn63 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn64 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn65 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn66 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn67 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn68 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn69 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn70 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn71 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
    Friend WithEvents LayoutControlGroup5 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup6 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup7 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup8 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup9 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup10 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlGroup11 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem2 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem3 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem4 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup12 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup13 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents popupCopyFrom As DevExpress.XtraEditors.PopupContainerControl
    Friend WithEvents GroupControl10 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtEmpNum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtCoName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents slueEmployeeCopyFrom As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView11 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn72 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn73 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LabelControl13 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnOk As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnCancelCopyToNewCompany As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents slueCompanyCopyFrom As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView12 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn74 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn75 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn76 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents EmployeeListBindingSource As BindingSource
    Friend WithEvents UcEmployeeInfo1 As ucEmployeeInfo
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents riDepartments As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents DEPARTMENTBindingSource As BindingSource
    Friend WithEvents colDivNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents riDivision As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents DIVISIONBindingSource As BindingSource
    Friend WithEvents bsiColumnChooser As DevExpress.XtraBars.BarSubItem
End Class
