﻿<?xml version="1.0" encoding="utf-8"?>
<!-- https://go.microsoft.com/fwlink/?LinkID=208121. -->
<Project>
  <PropertyGroup>
    <ApplicationRevision>5</ApplicationRevision>
    <ApplicationVersion>1.0.0.*</ApplicationVersion>
    <BootstrapperEnabled>True</BootstrapperEnabled>
    <Configuration>Release</Configuration>
    <CreateWebPageOnPublish>False</CreateWebPageOnPublish>
    <GenerateManifests>true</GenerateManifests>
    <Install>True</Install>
    <InstallFrom>Unc</InstallFrom>
    <InstallUrl>\\brands.local\dfs\Execupay\FrontDeskNetCore\</InstallUrl>
    <IsRevisionIncremented>True</IsRevisionIncremented>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <MapFileExtensions>True</MapFileExtensions>
    <OpenBrowserOnPublish>False</OpenBrowserOnPublish>
    <Platform>Any CPU</Platform>
    <PublishDir>bin\Release\net9.0-windows7.0\win-x64\app.publish\</PublishDir>
    <PublishUrl>\\brands.local\dfs\Execupay\FrontDeskNetCore\</PublishUrl>
    <PublishProtocol>ClickOnce</PublishProtocol>
    <PublishReadyToRun>False</PublishReadyToRun>
    <PublishSingleFile>False</PublishSingleFile>
    <SelfContained>False</SelfContained>
    <SignatureAlgorithm>(none)</SignatureAlgorithm>
    <SignManifests>False</SignManifests>
    <SkipPublishVerification>false</SkipPublishVerification>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <UpdateEnabled>True</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateRequired>False</UpdateRequired>
    <UpdateUrl>\\brands.local\dfs\Execupay\FrontDeskNetCore\</UpdateUrl>
    <WebPageFileName>Publish.html</WebPageFileName>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <History>True|2025-07-09T23:24:14.5857087Z||;False|2025-07-09T18:13:43.9226391-04:00||;</History>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.NetCore.DesktopRuntime.9.0.x64">
      <Install>true</Install>
      <ProductName>.NET Desktop Runtime 9.0.6 (x64)</ProductName>
    </BootstrapperPackage>
  </ItemGroup>
</Project>