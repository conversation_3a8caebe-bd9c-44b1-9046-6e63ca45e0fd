﻿Imports DevExpress.XtraEditors

Public Class frmSqlScriptsParamAutoRun
    Private db As dbEPDataDataContext
    Private ScriptID As Int32
    Private ScriptParams As List(Of String)

    Sub New(ScriptID As Int32, ScriptParams As List(Of String))

        ' This call is required by the designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        Me.ScriptID = ScriptID
        Me.ScriptParams = ScriptParams
        Me.labelScript.Text = ScriptID
    End Sub

    Private Sub frmSqlScriptsParamAutoRun_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            Dim sqlScriptRow = Query(Of SqlScript)($"SELECT TOP 1 * FROM custom.SqlScripts WHERE ID = {ScriptID}").FirstOrDefault()
            Me.labelScript.Text = ScriptID.ToString() + " - " + sqlScriptRow.Name
            checkAutoRn.Checked = sqlScriptRow.AutoRun
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in frmSqlScriptsParamAutoRun_Load", ex)
        End Try
    End Sub

    Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString())
            GridControl1.DataSource = db.SqlScriptsParamAutoRuns.Where(Function(p) p.ScriptID = ScriptID).ToList()
            GridView1.BestFitColumns()
            RepositoryItemLookUpExpr.DataSource = (From s In db.SqlScriptsExpressions Where s.Tested = True Select s.Expr).ToList()
            RepositoryItemGridLookUpEditScriptParam.DataSource = ScriptParams
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Close()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Dim sqlScriptRow = Query(Of SqlScript)($"SELECT TOP 1 * FROM custom.SqlScripts WHERE ID = {ScriptID}").FirstOrDefault()
            sqlScriptRow.AutoRun = checkAutoRn.Checked
            db.SaveChanges()
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in Save", ex)
        End Try
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub RepositoryItemButtonEdit1_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit1.Click
        Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsParamAutoRun)
        If row Is Nothing OrElse row.SqlScriptsExpression Is Nothing Then Return

        Dim Sql = row.SqlScriptsExpression.SqlString

        Try
            Dim results = Query(Of String)(Sql)
            If results.Count <> 1 Then
                Throw New Exception("Expr does not return 1 result")
            End If

            DisplayMessageBox(results.First, "Result")
        Catch ex As Exception
            DisplayErrorMessage(ex.Message, ex, "Error testing SQL")
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        Try
            Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsParamAutoRun)
            If row Is Nothing Then Return

            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(row), My.Resources.delete_16x16))
        Catch ex As Exception
            DisplayErrorMessage("Error in GridView1_PopupMenuShowing", ex)
        End Try
    End Sub

    Private Sub DeleteRow(row As SqlScriptsParamAutoRun)
        If XtraMessageBox.Show($"Are you sure you would like to delete row?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If
        Try
            db.SqlScriptsParamAutoRuns.DeleteOnSubmit(row)
            db.SaveChanges
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error when deleting row", ex)
        End Try
    End Sub

    Private Sub GridView1_BeforeLeaveRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowAllowEventArgs) Handles GridView1.BeforeLeaveRow
        Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsParamAutoRun)
        If row Is Nothing Then Return

        If row.ScriptID = Nothing Then
            row.ScriptID = ScriptID
        End If
    End Sub

    Private Sub bbiScriptParams_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiScriptParams.ItemClick
        Dim frm As New frmSqlScriptsExpr
        MainForm.ShowForm(frm)
    End Sub

    Private Sub GridView1_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridView1.ValidateRow
        Try
            Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsParamAutoRun)
            If row Is Nothing Then Return

            If row.Expr = "" Then
                row.Expr = Nothing
            End If

            If row.StaticValue = "" Then
                row.StaticValue = Nothing
            End If

            If row.ScriptID = Nothing Then
                row.ScriptID = ScriptID
            ElseIf row.Expr <> Nothing AndAlso row.StaticValue <> Nothing Then
                e.Valid = False
                'e.ErrorText = "Must have either Expression or Static Text"
            ElseIf row.StaticValue = Nothing AndAlso row.Expr = Nothing Then
                e.Valid = False
                'e.ErrorText = "Must have either Expression or Static Text"
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in Validate Row", ex)
        End Try
    End Sub

    Private Sub RepositoryItemLookUpExpr_EditValueChanging(sender As Object, e As Controls.ChangingEventArgs) Handles RepositoryItemLookUpExpr.EditValueChanging
        Try
            Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsParamAutoRun)
            If e.NewValue = Nothing Then
                row.SqlScriptsExpression = Nothing
            ElseIf e.NewValue <> e.OldValue Then
                Dim ExprRow = (From exp In db.SqlScriptsExpressions Where exp.Expr = e.NewValue.ToString()).Single
                row.SqlScriptsExpression = ExprRow
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in RepositoryItemLookUpExpr_EditValueChanging", ex)
        End Try
    End Sub
End Class