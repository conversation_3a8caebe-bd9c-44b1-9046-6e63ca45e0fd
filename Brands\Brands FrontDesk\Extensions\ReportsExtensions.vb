﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Net.Mail
Imports System.Runtime.CompilerServices
'Imports CrystalDecisions.CrystalReports.Engine
'Imports CrystalDecisions.ReportAppServer.DataDefModel

Public Module ReportsExtensions

    'Const ReportServer As String = ""
    'Const ReportDatabase As String = ""
    'Const ReportUser As String = ""
    'Const ReportPassword As String = ""
    'Const ReportProvider As String = ""

    '<Extension>
    'Public Sub SetReportLogOn(report As ReportDocument)
    '    'If Not My.Settings.UseOldCryatalReportLogin Then
    '    Dim reportLogin = New CrystalReportLogin()
    '    reportLogin.SetReportLogin(report, GetConnectionString)
    '    Exit Sub
    '    'End If
    '    Dim logonAttributes = New PropertyBag()
    '    Dim connectionAttributes = New PropertyBag()

    '    logonAttributes.Add("Data Source", ReportServer)
    '    logonAttributes.Add("Use DSN Default Properties", False)
    '    logonAttributes.Add("Initial Catalog", ReportDatabase)
    '    logonAttributes.Add("User ID", ReportUser)
    '    logonAttributes.Add("Password", ReportPassword)
    '    logonAttributes.Add("Provider", ReportProvider)

    '    connectionAttributes.Add("Database DLL", "crdb_ado.dll")
    '    connectionAttributes.Add("QE_DatabaseType", "OLE DB (ADO)")
    '    connectionAttributes.Add("QE_LogonProperties", logonAttributes)
    '    connectionAttributes.Add("QE_ServerDescription", ReportServer)
    '    connectionAttributes.Add("QE_SQLDB", 1)
    '    connectionAttributes.Add("QE_DatabaseName", ReportDatabase)
    '    connectionAttributes.Add("SSO Enabled", False)
    '    Dim infos = report.ReportClientDocument.DatabaseController.GetConnectionInfos(Nothing)

    '    For Each currentConnectionInfo As CrystalDecisions.ReportAppServer.DataDefModel.ConnectionInfo In infos
    '        Dim newConnectionInfo As New CrystalDecisions.ReportAppServer.DataDefModel.ConnectionInfo() With
    '            {
    '                .Attributes = connectionAttributes,
    '                .UserName = ReportUser,
    '                .Password = ReportPassword,
    '                .Kind = CrConnectionInfoKindEnum.crConnectionInfoKindCRQE
    '            }
    '        report.ReportClientDocument.DatabaseController.ReplaceConnection(currentConnectionInfo, newConnectionInfo, Nothing, CrDBOptionsEnum.crDBOptionIgnoreCurrentTableQualifiers)
    '    Next
    '    Dim subnames As CrystalDecisions.ReportAppServer.DataDefModel.Strings = report.ReportClientDocument.SubreportController.GetSubreportNames()
    '    For Each name As [String] In subnames
    '        infos = report.ReportClientDocument.SubreportController.GetSubreport(name).DatabaseController.GetConnectionInfos(Nothing)
    '        For Each currentConnectionInfo As CrystalDecisions.ReportAppServer.DataDefModel.ConnectionInfo In infos
    '            Dim newConnectionInfo As New CrystalDecisions.ReportAppServer.DataDefModel.ConnectionInfo()

    '            newConnectionInfo.Attributes = connectionAttributes
    '            newConnectionInfo.UserName = ReportUser
    '            newConnectionInfo.Password = ReportPassword
    '            newConnectionInfo.Kind = CrConnectionInfoKindEnum.crConnectionInfoKindCRQE
    '            report.ReportClientDocument.SubreportController.GetSubreport(name).DatabaseController.ReplaceConnection(currentConnectionInfo, newConnectionInfo, Nothing, CrDBOptionsEnum.crDBOptionIgnoreCurrentTableQualifiers)
    '        Next
    '    Next
    'End Sub

    '<Extension>
    'Public Async Function ToCrystalReportAsync(report As ReportEmailTeplate) As Task(Of ReportDocument)
    '    Try
    '        Dim cryRpt As ReportDocument = New ReportDocument()
    '        Await Task.Run(Sub() cryRpt.Load(report.ReportPath))
    '        Return cryRpt
    '    Catch ex As Exception
    '        Logger.Error(ex, "Error Loading crystal report {ReportPath}", report.ReportPath)
    '        Throw New Exception($"Error Loading crystal report {report.ReportPath}", ex)
    '    End Try
    'End Function

    '<Extension>
    'Public Function ToCrystalReport(report As ReportEmailTeplate) As ReportDocument
    '    Try
    '        Dim cryRpt As ReportDocument = New ReportDocument()
    '        cryRpt.Load(report.ReportPath)
    '        Return cryRpt
    '    Catch ex As Exception
    '        Throw New Exception("Error creating ReportDocument from ReportEmailTeplate", ex)
    '    End Try
    'End Function

    <Extension>
    Public Sub CheckIfReportExist(report As ReportEmailTeplate)
        If report.ReportPath.IsNotNullOrWhiteSpace Then
            If Not File.Exists(report.ReportPath) Then
                Throw New Exception($"Error proccessing report {report.Name}. The path [{report.ReportPath}] does not exist.{Environment.NewLine}Please make sure the {Path.GetPathRoot(report.ReportPath)} Drive is mapped correctly.")
            End If
        End If
    End Sub

    '<Extension>
    'Public Sub TrySetParameterValue(report As ReportDocument, name As String, value As Object)
    '    If report.ParameterFields.Count > 0 Then
    '        Dim parameterList = New List(Of CrystalDecisions.Shared.ParameterField)(report.ParameterFields.Cast(Of CrystalDecisions.Shared.ParameterField))
    '        Dim coParam = parameterList.FirstOrDefault(Function(c) c.Name.Equals(name, StringComparison.InvariantCultureIgnoreCase))
    '        'Solomon added on Oct 14, '20.  had issue with stub range report, when it had PrNum in inside subreports but not in main report
    '        'fails with exception "Operation illegal on linked parameters"
    '        'https://docs.microsoft.com/en-us/previous-versions/ms226025(v%3Dvs.90)
    '        'ParameterFieldDefinition.ReportName Property Gets the report name the parameter field is in. If it is a main report, the report name is empty

    '        If coParam IsNot Nothing AndAlso coParam.ReportName = "" Then
    '            report.SetParameterValue(name, value)
    '        End If
    '    End If
    'End Sub

    '<Extension>
    'Public Sub SetInitialParameterValues(_reportDocument As ReportDocument)
    '    Dim initialParams = _reportDocument.ReportClientDocument.DataDefController.DataDefinition.ParameterFields
    '    For i As Integer = 0 To initialParams.Count - 1
    '        Dim ip As CrystalDecisions.ReportAppServer.DataDefModel.ISCRParameterField = initialParams(i)
    '        If ip.InitialValues.Count > 0 Then
    '            Dim v As CrystalDecisions.ReportAppServer.DataDefModel.ParameterFieldDiscreteValue = ip.InitialValues.Item(0)
    '            Dim value As Object
    '            If v.Value = "DateTime(1900,01,01,00,00,00)" Then
    '                value = New DateTime(1900, 1, 1)
    '            Else
    '                value = v.Value
    '            End If
    '            _reportDocument.TrySetParameterValue(ip.Name, value)
    '        End If
    '    Next
    'End Sub

    '<Extension>
    'Public Sub TrySetParameterValue(report As ReportDocument, paramterType As ParameterValueKind, value As Object)
    '    Dim parameterList = New List(Of CrystalDecisions.Shared.ParameterField)(report.ParameterFields.Cast(Of CrystalDecisions.Shared.ParameterField))
    '    For Each param As CrystalDecisions.Shared.ParameterField In parameterList.Where(Function(p) p.ParameterValueType = paramterType AndAlso p.ReportName.IsNullOrWhiteSpace())
    '        report.SetParameterValue(param.Name, value)
    '    Next
    'End Sub

    '<Extension>
    'Public Function GetParameters(report As ReportDocument) As List(Of CrystalDecisions.Shared.ParameterField)
    '    Return New List(Of CrystalDecisions.Shared.ParameterField) _
    '        (report.ParameterFields.Cast(Of CrystalDecisions.Shared.ParameterField)) _
    '        .Where(Function(r) r.ReportName.IsNullOrWhiteSpace() AndAlso r.PromptText.IsNotNullOrWhiteSpace).ToList
    'End Function

    '<Extension>
    'Public Function GetEPParameter(report As ReportEmailTeplate) As List(Of CrystalDecisions.Shared.ParameterField)
    '    Dim Params = New List(Of CrystalDecisions.Shared.ParameterField)
    '    Dim ParamList = New List(Of String)

    '    If report.ReportParameters.IsNotNullOrWhiteSpace() Then
    '        ParamList = (From A In report.ReportParameters.Split({","c, ";"c, ":"c}) Select A.Trim).ToList()
    '    End If

    '    Dim db = New dbEPDataDataContext(GetConnectionString)
    '    For Each p In ParamList
    '        Dim paramMap = (From pm In db.ReportEPParamMaps Where pm.Param = p).SingleOrDefault
    '        If paramMap Is Nothing Then Throw New Exception("Error resolving parameter {0} from EP report")
    '        Dim param = New CrystalDecisions.Shared.ParameterField With {.Name = paramMap.Description}
    '        Params.Add(param)
    '    Next
    '    Return Params
    'End Function

    <Extension>
    Public Function GetEPParameter(report As ReportEmailTeplate) As List(Of ParameterField)
        Dim Params = New List(Of ParameterField)
        Dim ParamList = New List(Of String)

        If report.ReportParameters.IsNotNullOrWhiteSpace() Then
            ParamList = (From A In report.ReportParameters.Split({","c, ";"c, ":"c}) Select A.Trim).ToList()
        End If

        Dim db = New dbEPDataDataContext(GetConnectionString)
        For Each p In ParamList
            Dim paramMap = (From pm In db.ReportEPParamMaps Where pm.Param = p).SingleOrDefault
            If paramMap Is Nothing Then Throw New Exception("Error resolving parameter {0} from EP report")
            'Dim param = New CrystalDecisions.Shared.ParameterField With {.Name = paramMap.Description}
            Dim param = New ParameterField With {.Name = paramMap.Description}
            Params.Add(param)
        Next
        Return Params
    End Function

    '<Extension>
    'Public Function ToKeyValuePairs(params As List(Of CrystalDecisions.Shared.ParameterField)) As List(Of KeyValuePair)
    '    Dim list = New List(Of KeyValuePair)
    '    For Each item In params
    '        list.Add(New KeyValuePair With {.Name = item.Name, .Value = item.GetFirstValue()})
    '    Next
    '    Return list
    'End Function

    <Extension>
    Public Function ToKeyValuePairs(params As List(Of ParameterField)) As List(Of KeyValuePair)
        Dim list = New List(Of KeyValuePair)
        If params IsNot Nothing Then
            For Each item In params
                list.Add(New KeyValuePair With {.Name = item.Name, .Value = item.Value})
            Next
        End If
        Return list
    End Function

    '<Extension>
    'Public Function GetFirstValue(param As CrystalDecisions.Shared.ParameterField) As Object
    '    If param Is Nothing OrElse param.CurrentValues.Count = 0 Then
    '        Return Nothing
    '    Else
    '        Return CType(param.CurrentValues(0), CrystalDecisions.Shared.ParameterDiscreteValue).Value
    '    End If
    'End Function

    Public Property CrystalReportsFolder = Path.Combine(Path.GetTempPath(), "CrystalReportsFolder")
    Private Property CrystalReportsFolderName = "CrystalReportsFolder"
    Public Property CrystalReportsFolderSecure = System.IO.Path.Combine(CrystalReportsFolder, "Password Protected")
    Public Property FaxesFolder = System.IO.Path.Combine(CrystalReportsFolder, "Faxes")

    Public Function GetFileName(Comp As COMPANY, Name As String, extension As String) As String
        Dim newName = If(Comp IsNot Nothing, String.Format("{0}_{1}_", Comp.CONUM, Comp.CO_NAME.Take(5).ToNewString().Trim()), "") + Name
        Dim reportPath = Path.ChangeExtension("{0}_{1:yyyy-MM-dd_hh-mm-ss-tt}".FormatWith(newName, DateTime.Now), extension)
        Return reportPath
    End Function

    '<Extension>
    'Public Async Function ExportToPDFAsync(report As ReportDocument, name As String, Optional password As String = "") As Task(Of String)
    '    Dim reportPath As String
    '    If password.IsNullOrWhiteSpace Then
    '        reportPath = Path.Combine(GetCrystalReportsFolder(), name.Replace("\", " "))
    '        Await Task.Run(Sub() report.ExportToDisk(ExportFormatType.PortableDocFormat, reportPath))
    '    Else
    '        reportPath = Path.Combine(GetSecureCrystalReportsFolder(), name.Replace("\", " "))
    '        Await Task.Run(Sub() PdfUtilities.StreamToPdf(report.ExportToStream(ExportFormatType.PortableDocFormat), reportPath, password))
    '    End If

    '    Return reportPath
    'End Function


    '<Extension>
    'Public Function ExportToPDF(report As ReportDocument, name As String, Optional password As String = "") As String
    '    Dim reportPath As String
    '    If password.IsNullOrWhiteSpace Then
    '        reportPath = Path.ChangeExtension(Path.Combine(GetCrystalReportsFolder(), "{0}_{1:yyyy-MM-dd_hh-mm-ss-tt}".FormatWith(name.Replace("\", " "), DateTime.Now)), ".pdf")
    '    Else
    '        reportPath = Path.ChangeExtension(Path.Combine(GetSecureCrystalReportsFolder(), "{0}_{1:yyyy-MM-dd_hh-mm-ss-tt}".FormatWith(name.Replace("\", " "), DateTime.Now)), ".pdf")
    '    End If
    '    report.ExportToDisk(ExportFormatType.PortableDocFormat, reportPath)

    '    Return reportPath
    'End Function

    Public Function SetPdfPassword(path As String, password As String) As String
        If Not System.IO.Directory.Exists(CrystalReportsFolderSecure) Then
            Directory.CreateDirectory(CrystalReportsFolderSecure)
        End If

        Dim newPdf = System.IO.Path.Combine(CrystalReportsFolderSecure, System.IO.Path.GetFileName(path))
        PdfUtilities.SetPassword(path, newPdf, password)
        Return newPdf
    End Function

    Public Function GetCrystalReportsFolder() As String
        If Not Directory.Exists(CrystalReportsFolder) Then
            Directory.CreateDirectory(CrystalReportsFolder)
        End If
        Return CrystalReportsFolder
    End Function

    Public Function GetSecureCrystalReportsFolder() As String
        If Not Directory.Exists(CrystalReportsFolderSecure) Then
            Directory.CreateDirectory(CrystalReportsFolderSecure)
        End If
        Return CrystalReportsFolderSecure
    End Function

    <Extension()>
    Public Function ToStringMap(val As IEnumerable(Of KeyValuePair)) As String
        Return String.Join("; ", val.Select(Function(kv) kv.ToString()))
    End Function

    <Extension()>
    Public Function ToSqlParameters(val As IEnumerable(Of KeyValuePair)) As List(Of SqlParameter)
        Return val.Select(Function(p) New SqlParameter(p.Name, p.Value)).ToList
    End Function

    <Extension()>
    Public Function GetEmailBody(mm As MailMessage)
        Try
            If mm.Body.IsNotNullOrWhiteSpace Then
                Return mm.Body
            End If

            If mm.AlternateViews.Count > 1 Then
                Throw New Exception("Please be aware that the email you're sending has multiple AlternateViews. This is currently not supported by the tickting system.")
            ElseIf mm.AlternateViews.Count = 1 Then
                mm.AlternateViews.Single.ContentStream.Position = 0
                Dim ms = New StreamReader(mm.AlternateViews.Single.ContentStream)
                Dim body = ms.ReadToEnd
                mm.AlternateViews.Single.ContentStream.Position = 0
                Return body
            End If

            Return String.Empty
        Catch ex As Exception
            Logger.Error(ex, "Error extracting email body.")
            Throw
        End Try
    End Function

End Module