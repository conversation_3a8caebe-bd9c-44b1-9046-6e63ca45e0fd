﻿Imports DevExpress.XtraBars.Docking2010.Views

Public Class ucCompanySummary
    Private _ucCompInfo As ucCompInfo
    Private _nextScheduledPayroll As ucNextScheduledPayroll
    Private _ucMostRecentPayroll As ucMostRecentPayroll
    Public coNum As Decimal


    Public Sub New(data As CompanySummaryData)
        InitializeComponent()
        coNum = data.conum
        WidgetView1.AddDocument(New ucCompInfo(data.company, data.empsCount, data._CoOptions) With {.CompanyInfoGroupBordersVisible = False})
        _nextScheduledPayroll = New Brands_FrontDesk.ucNextScheduledPayroll(coNum, data.nextPay)
        WidgetView1.AddDocument(_nextScheduledPayroll)
        _ucMostRecentPayroll = New ucMostRecentPayroll(coNum, data.payrolls, data._IsPaperless, data._Transit)
        WidgetView1.AddDocument(_ucMostRecentPayroll)
        WidgetView1.AddDocument(New Brands_FrontDesk.ucCompanyActivityHistoryWidget(data.tickets))
        'WidgetView1.AddDocument(New Brands_FrontDesk.ucCompNotes(coNum, notes))
        Dim servicesDoc As Widget.Document = WidgetView1.AddDocument(New Brands_FrontDesk.ucCompanyServices(coNum, data.Services?.ToList))
        WidgetView1.AddDocument(New Brands_FrontDesk.ucEmailAlerts(data.company))
        WidgetView1.AddDocument(New Brands_FrontDesk.ucUserLogs(data.company))
        AddHandler servicesDoc.Maximized, AddressOf servicesDoc_Maximized
        AddHandler servicesDoc.Restored, AddressOf servicesDoc_Maximized
    End Sub

    Private Sub servicesDoc_Maximized(sender As Object, e As EventArgs)
        Dim doc As Widget.Document = sender
        Dim cntrl As ucCompanyServices = doc.Control
        cntrl.ShowServices(doc.IsMaximized)
    End Sub

    Public Function GetCurrentPayroll() As PAYROLL
        Return _ucMostRecentPayroll.GetCurrentPayroll
    End Function

    Private Sub ucNextScheduledPayrollDocument_CustomButtonClick(sender As Object, e As DevExpress.XtraBars.Docking2010.ButtonEventArgs) Handles ucNextScheduledPayrollDocument.CustomButtonChecked, ucNextScheduledPayrollDocument.CustomButtonUnchecked
        _nextScheduledPayroll.ShowRecent(e.Button.IsChecked)
    End Sub


    Public Class CompanySummaryData
        Property conum As Decimal
        Property company As COMPANY
        Property empsCount As Decimal
        Property nextPay As List(Of fn_NextScheduledPayrollResult)
        Property payrolls As List(Of PAYROLL)
        Property _IsPaperless As String
        Property tickets As List(Of view_CompanyActivityHistory)
        Property notes As List(Of NOTE)
        Property _CoOptions As COOPTION
        Property _Transit As String
        Public Property Services As List(Of prc_GetCompanyServicesResult)
    End Class

End Class
