﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmBillingCredits
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBillingCredits))
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.InvoiceBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colITEM_DATE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colITEM_CAT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPRICE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCHK_ENTRIES = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colINVOICE_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPRNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colINV_SECTION = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colITEM_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colITEM_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colITEM_FREQ_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colITEM_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTAXABLE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSrc = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.RibbonPage2 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.ImageCollection1 = New DevExpress.Utils.ImageCollection(Me.components)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.InvoiceBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ImageCollection1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(920, 546)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.InvoiceBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(12, 12)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(896, 522)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM, Me.colCO_NAME, Me.colITEM_DATE, Me.colITEM_CAT, Me.colPRICE, Me.colCHK_ENTRIES, Me.colINVOICE_NUM, Me.colPRNUM, Me.colINV_SECTION, Me.colITEM_TYPE, Me.colITEM_NAME, Me.colDIVNUM, Me.colITEM_FREQ_TYPE, Me.colITEM_NUM, Me.colTAXABLE, Me.colSrc})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsFind.AlwaysVisible = True
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        Me.GridView1.OptionsView.ShowFooter = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colCONUM
        '
        Me.colCONUM.Caption = "Co #"
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        Me.colCONUM.OptionsColumn.AllowEdit = False
        Me.colCONUM.Visible = True
        Me.colCONUM.VisibleIndex = 1
        '
        'colCO_NAME
        '
        Me.colCO_NAME.Caption = "Company"
        Me.colCO_NAME.FieldName = "CO_NAME"
        Me.colCO_NAME.Name = "colCO_NAME"
        Me.colCO_NAME.OptionsColumn.AllowEdit = False
        Me.colCO_NAME.Visible = True
        Me.colCO_NAME.VisibleIndex = 2
        '
        'colITEM_DATE
        '
        Me.colITEM_DATE.Caption = "Date"
        Me.colITEM_DATE.DisplayFormat.FormatString = "d"
        Me.colITEM_DATE.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colITEM_DATE.FieldName = "ITEM_DATE"
        Me.colITEM_DATE.Name = "colITEM_DATE"
        Me.colITEM_DATE.Visible = True
        Me.colITEM_DATE.VisibleIndex = 3
        '
        'colITEM_CAT
        '
        Me.colITEM_CAT.Caption = "Category"
        Me.colITEM_CAT.FieldName = "ITEM_CAT"
        Me.colITEM_CAT.Name = "colITEM_CAT"
        Me.colITEM_CAT.OptionsColumn.AllowEdit = False
        Me.colITEM_CAT.Visible = True
        Me.colITEM_CAT.VisibleIndex = 4
        '
        'colPRICE
        '
        Me.colPRICE.Caption = "Price"
        Me.colPRICE.DisplayFormat.FormatString = "C2"
        Me.colPRICE.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colPRICE.FieldName = "PRICE"
        Me.colPRICE.Name = "colPRICE"
        Me.colPRICE.Visible = True
        Me.colPRICE.VisibleIndex = 5
        '
        'colCHK_ENTRIES
        '
        Me.colCHK_ENTRIES.Caption = "Entries"
        Me.colCHK_ENTRIES.FieldName = "CHK_ENTRIES"
        Me.colCHK_ENTRIES.Name = "colCHK_ENTRIES"
        Me.colCHK_ENTRIES.Visible = True
        Me.colCHK_ENTRIES.VisibleIndex = 6
        '
        'colINVOICE_NUM
        '
        Me.colINVOICE_NUM.Caption = "Inv #"
        Me.colINVOICE_NUM.FieldName = "INVOICE_NUM"
        Me.colINVOICE_NUM.Name = "colINVOICE_NUM"
        Me.colINVOICE_NUM.OptionsColumn.AllowEdit = False
        Me.colINVOICE_NUM.Visible = True
        Me.colINVOICE_NUM.VisibleIndex = 7
        '
        'colPRNUM
        '
        Me.colPRNUM.Caption = "Payroll #"
        Me.colPRNUM.FieldName = "PRNUM"
        Me.colPRNUM.Name = "colPRNUM"
        Me.colPRNUM.Visible = True
        Me.colPRNUM.VisibleIndex = 8
        '
        'colINV_SECTION
        '
        Me.colINV_SECTION.Caption = "Section"
        Me.colINV_SECTION.FieldName = "INV_SECTION"
        Me.colINV_SECTION.Name = "colINV_SECTION"
        Me.colINV_SECTION.OptionsColumn.AllowEdit = False
        Me.colINV_SECTION.Visible = True
        Me.colINV_SECTION.VisibleIndex = 9
        '
        'colITEM_TYPE
        '
        Me.colITEM_TYPE.Caption = "Item Type"
        Me.colITEM_TYPE.FieldName = "ITEM_TYPE"
        Me.colITEM_TYPE.Name = "colITEM_TYPE"
        Me.colITEM_TYPE.OptionsColumn.AllowEdit = False
        Me.colITEM_TYPE.Visible = True
        Me.colITEM_TYPE.VisibleIndex = 10
        '
        'colITEM_NAME
        '
        Me.colITEM_NAME.Caption = "Item Name"
        Me.colITEM_NAME.FieldName = "ITEM_NAME"
        Me.colITEM_NAME.Name = "colITEM_NAME"
        Me.colITEM_NAME.Visible = True
        Me.colITEM_NAME.VisibleIndex = 11
        '
        'colDIVNUM
        '
        Me.colDIVNUM.Caption = "Division #"
        Me.colDIVNUM.FieldName = "DIVNUM"
        Me.colDIVNUM.Name = "colDIVNUM"
        Me.colDIVNUM.Visible = True
        Me.colDIVNUM.VisibleIndex = 12
        '
        'colITEM_FREQ_TYPE
        '
        Me.colITEM_FREQ_TYPE.Caption = "Frequency"
        Me.colITEM_FREQ_TYPE.FieldName = "ITEM_FREQ_TYPE"
        Me.colITEM_FREQ_TYPE.Name = "colITEM_FREQ_TYPE"
        Me.colITEM_FREQ_TYPE.OptionsColumn.AllowEdit = False
        Me.colITEM_FREQ_TYPE.Visible = True
        Me.colITEM_FREQ_TYPE.VisibleIndex = 0
        '
        'colITEM_NUM
        '
        Me.colITEM_NUM.FieldName = "ITEM_NUM"
        Me.colITEM_NUM.Name = "colITEM_NUM"
        '
        'colTAXABLE
        '
        Me.colTAXABLE.FieldName = "TAXABLE"
        Me.colTAXABLE.Name = "colTAXABLE"
        '
        'colSrc
        '
        Me.colSrc.FieldName = "Src"
        Me.colSrc.Name = "colSrc"
        Me.colSrc.OptionsColumn.AllowEdit = False
        Me.colSrc.Visible = True
        Me.colSrc.VisibleIndex = 13
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(920, 546)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(900, 526)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'RibbonPage2
        '
        Me.RibbonPage2.Name = "RibbonPage2"
        Me.RibbonPage2.Text = "RibbonPage2"
        '
        'ImageCollection1
        '
        Me.ImageCollection1.ImageStream = CType(resources.GetObject("ImageCollection1.ImageStream"), DevExpress.Utils.ImageCollectionStreamer)
        Me.ImageCollection1.Images.SetKeyName(0, "Action_Delete.png")
        Me.ImageCollection1.Images.SetKeyName(1, "functionsfinancial_32x32.png")
        '
        'frmBillingCredits
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(920, 546)
        Me.Controls.Add(Me.LayoutControl1)
        Me.IconOptions.ShowIcon = False
        Me.Name = "frmBillingCredits"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Billing Credits"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.InvoiceBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ImageCollection1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents RibbonPage2 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents InvoiceBindingSource As BindingSource
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colITEM_DATE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colITEM_CAT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPRICE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCHK_ENTRIES As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colINVOICE_NUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPRNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colINV_SECTION As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colITEM_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colITEM_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDIVNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colITEM_FREQ_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colITEM_NUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTAXABLE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSrc As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ImageCollection1 As DevExpress.Utils.ImageCollection
End Class
