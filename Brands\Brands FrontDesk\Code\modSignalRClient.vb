﻿
Imports System.Net
Imports Brands_FrontDesk
Imports DevExpress.XtraBars.Alerter
Imports Microsoft.AspNet.SignalR.Client

Module modSignalRClient

    Dim _hub As IHubProxy
    Dim connection As HubConnection
    Dim _Timer As System.Timers.Timer
    Dim _logger As Serilog.ILogger
    Dim connectionChangedEvent = New Action(Of StateChange)(Sub(s As StateChange) ConnetionChanged(s))

    Sub New()
        _logger = Logger.ForContext(GetType(modSignalRClient))
    End Sub


    Public Event OnRefereshFaxes As EventHandler
    Public Event OnFaxOrEmailUpdated As EventHandler
    Public Event OnSuppliesOrderUpdated As EventHandler

    Public Async Function Initialize() As Task
        If connection IsNot Nothing Then
            Try
                connection.Dispose()
                connection = Nothing
            Catch ex As Exception
                _logger.Error(ex, "Error disposing connection.")
            End Try
        End If

        If Not MainForm.IsHandleCreated AndAlso (MainForm.Disposing OrElse MainForm.IsDisposed) Then
            Exit Function
        End If

        Dim url As String = GetUdfValue("SignalRServer")
        If url.IsNullOrWhiteSpace() Then Exit Function
        Dim qs = String.Format("UserName={0}&MachineName={1}&Version={2}", UserName, Environment.MachineName, Version)
        _logger.Debug("Attempting to connect to: {Url} QueryString: {QueryString}", url, qs)
        connection = New HubConnection(url, qs)
        ServicePointManager.DefaultConnectionLimit = 1000
        _hub = connection.CreateHubProxy("MainHub")
        _hub.JsonSerializer.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore

        AddHandler connection.StateChanged, connectionChangedEvent

        AddHandler connection.Error, Sub(ex As Exception)
                                         _logger.Error(ex, "SignalR Connection Error")
                                     End Sub

        _hub.On("NewUpdateAvailable", Sub()
                                          CheckForUpdate()
                                      End Sub)
        _hub.On("Disconnect", Sub()
                                  connection.Stop()
                              End Sub)
        _hub.On("RefreshFaxes", Sub()
                                    RefreshFaxes()
                                End Sub)
        _hub.On(Of view_FaxAndEmail)("FaxOrEmailUpdated", Sub(v As view_FaxAndEmail)
                                                              MainForm.TryInvoke(Sub()
                                                                                     RaiseEvent OnFaxOrEmailUpdated(v, Nothing)
                                                                                 End Sub)
                                                          End Sub)

        _hub.On("ShowPopUpMessage", Sub(msg As String)
                                        ShowPopUp(msg)
                                    End Sub)

        _hub.On("RefreshPermissions", Sub()
                                          RefreshPermissions()
                                      End Sub)

        _hub.On("SuppliesOrderUpdated", Sub(order As SuppliesOrder)
                                            MainForm.TryInvoke(Sub()
                                                                   _logger.Information("Entering SuppliesOrderUpdated {OrderId} {Conum} {ByUser}", order.ID, order.CoNum, UserName)
                                                                   RaiseEvent OnSuppliesOrderUpdated(order, Nothing)
                                                               End Sub)
                                        End Sub)

        _hub.On("RefreshTelebroadPushLogin", Async Sub()
                                                 _logger.Information("Entering RefreshTelebroadPushLogin")
                                                 Await modPhoneApi.InitializePhoneSystem(False)
                                             End Sub)

        _hub.On("OpenTicketsCountUpdated", Sub(counts As BadgeCounters)
                                               MainForm.RefreshBadgeCounter(counts)
                                           End Sub)

        _hub.On("TicketAssigned", Sub(ticket As Ticket, first_view As view_FaxAndEmail, byUser As String)
                                      MainForm?.TryInvoke(Sub() TicketAssigned(ticket, first_view, byUser))
                                  End Sub)

        '_hub.On("OpenCompany", Sub(conum As Decimal)
        '                           MainForm?.TryInvoke(Sub() MainForm?.OpenCompForm(conum))
        '                           MainForm?.TryBringToFront()
        '                       End Sub)
        '_hub.On("StartNewPayroll", Sub(conum As Decimal, ticketId As Decimal)
        '                               MainForm?.TryInvoke(Sub()
        '                                                       _logger.Debug("Entering StartNewPayroll. {Conum} {TicketId}", conum, ticketId)
        '                                                       MainForm?.TryBringToFront()
        '                                                       Dim options As OpenPowerGridPayrollOptions = New OpenPowerGridPayrollOptions(conum) With {
        '                                                                         ._ZendeskTicketId = ticketId,
        '                                                                         .CenterToScreen = Screen.FromControl(MainForm)}
        '                                                       MainForm.OpenPowerGridPayroll(options)
        '                                                   End Sub)
        '                           End Sub)

        '_hub.On("SetTicketConum", Sub(ticketInfo As DomainModels.App.Zendesk.SetTicketConumInfo)
        '                              MainForm?.TryInvoke(Sub()
        '                                                      _logger.Debug("Entering SetTicketConum. {@ticketInfo}", ticketInfo)
        '                                                      MainForm?.TryBringToFront()
        '                                                      Dim frm = New frmSetTicketOrg
        '                                                      frm.SetTicketInfo(ticketInfo)
        '                                                      MainForm.ShowForm(frm)
        '                                                  End Sub)
        '                          End Sub)
        Await IsConnected()
    End Function

    Private Sub TicketAssigned(ticket As Ticket, first_view As view_FaxAndEmail, byUser As String)
        Try
            Dim alertForm As AlertFormCore = Nothing
            Dim al = New DevExpress.XtraBars.Alerter.AlertControl With {
                .AllowHotTrack = True,
                .FormLocation = DevExpress.XtraBars.Alerter.AlertFormLocation.BottomRight,
                .FormShowingEffect = AlertFormShowingEffect.MoveVertical,
                .AutoFormDelay = TimeSpan.FromMinutes(1).TotalMilliseconds,
                .AutoHeight = True}

            AddHandler al.BeforeFormShow, Sub(s, ev)
                                              alertForm = ev.AlertForm
                                          End Sub

            AddHandler al.AlertClick, Sub()
                                          If alertForm IsNot Nothing Then alertForm.Close()
                                          Try
                                              first_view.OpenFaxOrEmailDetails()
                                          Catch ex As Exception
                                              DisplayErrorMessage("Error opening ticket", ex)
                                          End Try
                                      End Sub
            al.Show(MainForm, $"Ticket Assigned to you.", $"A Ticket has been assigned to you.{vbCrLf}By User: {byUser}{vbCrLf}Co#: {first_view.CoNameCoNum}{vbCrLf}Category: {first_view.Category}{vbCrLf}Subject: {first_view.FileName}", My.Resources.Alerts)
        Catch ex As Exception
            Logger.Error(ex, "Error in TicketAssigned")
        End Try
    End Sub

    Private Sub ConnetionChanged(s As StateChange)
        If s.NewState = ConnectionState.Disconnected Then
            StartReconnectTimer()
        ElseIf s.NewState = ConnectionState.Connected Then
            StopReconnectTimer()
        End If
        _logger.Information("SiganlR ConnectionState changed to: {ConnectionState}", connection?.State)
    End Sub

    Public Sub Dispose()
        If connection IsNot Nothing Then
            RemoveHandler connection.StateChanged, connectionChangedEvent
            connection.Stop()
            connection.Dispose()
            connection = Nothing
        End If
    End Sub

    Private Sub RefreshFaxes()
        MainForm.TryInvoke(Sub()
                               RaiseEvent OnRefereshFaxes(Nothing, Nothing)
                           End Sub)
    End Sub


    Public Sub CheckForUpdate()
        MainForm.TryInvoke(Sub()
                               _logger.Information("Entering CheckForUpdate")
                               modGlobals.CheckForUpdate(True, False)
                           End Sub)
    End Sub

    Private Sub ShowPopUp(msg As String)
        _logger.Information("Entering ShowPopUp")
        MainForm.TryInvoke(Sub()
                               Dim frm = New frmSignalrPopup(msg)
                               frm.ShowDialog()
                           End Sub)
    End Sub

    Private Sub RefreshPermissions()
        _logger.Information("Entering RefreshPermissions")
        MainForm.TryInvoke(Sub()
                               Using DB = New dbEPDataDataContext(GetConnectionString)
                                   ReloadPermissions()
                               End Using
                               MainForm.SetPermissions()
                           End Sub)
    End Sub

    Public Sub FaxOrEmailUpdated(fax As view_FaxAndEmail, message As String)
        PushFaxOrEmailUpdate(fax.Source, fax.ID, message)
    End Sub

    Public Async Sub PushFaxOrEmailUpdate(source As String, id As Integer, message As String)
        Try
            If connection Is Nothing Then Exit Sub
            _logger.Debug("Entering FaxOrEmailUpdated with {Source} Id: {ID} connection state: {State}", source, id, connection?.State)
            If connection.State <> ConnectionState.Connected Then
                If Not (Await IsConnected()) Then
                    _logger.Debug("Couldn't open connection, exiting sub")
                    Exit Sub
                End If
            End If
            Await _hub.Invoke("FaxOrEmailUpdated", source, id)
            _logger.Information("Notified server {Source} {Id} {Message}", source, id, message)
        Catch ex As Exception
            _logger.Error(ex, "Error in FaxOrEmailUpdated while sending {Source} {Id}", source, id)
        End Try
    End Sub

    Public Async Sub PushTicketAssignedToUpdate(ticket As Ticket)
        Try
            If connection Is Nothing Then Exit Sub
            _logger.Debug("Entering PushTicketAssignedToUpdate with TicketNum: {TicketNum} connection state: {State}", ticket.TicketNum, connection?.State)
            If connection.State <> ConnectionState.Connected Then
                If Not (Await IsConnected()) Then
                    _logger.Debug("Couldn't open connection, exiting sub")
                    Exit Sub
                End If
            End If
            Await _hub.Invoke("TicketAssignedToUpdated", ticket.TicketNum, UserName)
            _logger.Information("Notified server AssignedToUpdate. {TicketNum}", ticket.TicketNum)
        Catch ex As Exception
            _logger.Error(ex, "Error in FaxOrEmailUpdated while sending {TicketNum}", ticket.TicketNum)
        End Try
    End Sub

    Public Async Sub PushRefreshPermission(userId As String)
        Try
            _logger.Information("Entering PushRefreshPermission {UserId}", userId)
            Await _hub.Invoke("RefreshPermission", userId)
        Catch ex As Exception
            _logger.Error(ex, "Error in PushRefreshPermission {UserId}", userId)
        End Try
    End Sub

    Public Async Sub SuppliesOrderUpdated(order As SuppliesOrder)
        Try
            If connection Is Nothing Then Exit Sub
            _logger.Information("Entring SuppliesOrderUpdated {OrderId} {Conum} {ByUser}", order.ID, order.CoNum)
            If connection.State <> ConnectionState.Connected Then
                If Not (Await IsConnected()) Then
                    _logger.Debug("Couldn't open connection, exiting sub")
                    Exit Sub
                End If
            End If

            Await _hub.Invoke("SuppliesOrderUpdated", order.ID)
        Catch ex As Exception
            _logger.Error(ex, "Error in SuppliesOrderUpdated {OrderId} {Conum} {ByUser}", order.ID, order.CoNum)
        End Try
    End Sub

    Private Async Function IsConnected() As Task(Of Boolean)
        Try
            Await connection.Start()
            Return connection.State = ConnectionState.Connected
        Catch ex As Exception
            _logger.Error(ex, "Error starting SignalR connection")
            Return False
        End Try
    End Function

    Private lockingObject = New Object
    Private Sub StartReconnectTimer()
        SyncLock lockingObject
            If _Timer IsNot Nothing Then Exit Sub
            _Timer = New System.Timers.Timer() With {.Enabled = True, .Interval = 60 * 1000}
            AddHandler _Timer.Elapsed, Sub()
                                           Dim r = Initialize()
                                       End Sub
            _Timer.Start()
            _logger.Information("started reconnect SignalR timer")
        End SyncLock
    End Sub

    Private Sub StopReconnectTimer()
        SyncLock lockingObject
            If _Timer IsNot Nothing Then
                _Timer.Stop()
            End If
            _Timer = Nothing
            _logger.Information("stopped SignalR timer")
        End SyncLock
    End Sub
End Module
