﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucDeliveryHistory
    Inherits UserControl

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn7 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn8 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn9 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn10 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn11 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn12 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn13 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn14 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn15 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn16 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn17 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn18 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn19 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn20 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn21 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn22 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.slueCompany = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.view_CompanySumarriesBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.SearchLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCoNumAndName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colOP_OWNER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colBarCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colScannedDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDeliverBy = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipWith = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNote = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTicketID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colScannedBy = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPayrollNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsClosed = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsReturned = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivShip = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoShip = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipCo = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipAddress = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipCity = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipState = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipZip = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipContact = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colShipPhone = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueCompany.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.view_CompanySumarriesBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.GridControl2)
        Me.LayoutControl1.Controls.Add(Me.slueCompany)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(-1015, 193, 450, 400)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1015, 706)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'GridControl2
        '
        Me.GridControl2.Location = New System.Drawing.Point(24, 460)
        Me.GridControl2.MainView = Me.GridView2
        Me.GridControl2.Name = "GridControl2"
        Me.GridControl2.Size = New System.Drawing.Size(967, 222)
        Me.GridControl2.TabIndex = 7
        Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
        '
        'GridView2
        '
        Me.GridView2.Appearance.Preview.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView2.Appearance.Preview.Options.UseFont = True
        Me.GridView2.Appearance.Row.Font = New System.Drawing.Font("Segoe UI Semibold", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView2.Appearance.Row.Options.UseFont = True
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6, Me.GridColumn7, Me.GridColumn8, Me.GridColumn9, Me.GridColumn10, Me.GridColumn11, Me.GridColumn12, Me.GridColumn13, Me.GridColumn14, Me.GridColumn15, Me.GridColumn16, Me.GridColumn17, Me.GridColumn18, Me.GridColumn19, Me.GridColumn20, Me.GridColumn21, Me.GridColumn22})
        Me.GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView2.GridControl = Me.GridControl2
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsBehavior.Editable = False
        Me.GridView2.OptionsFind.SearchInPreview = True
        Me.GridView2.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView2.OptionsView.AutoCalcPreviewLineCount = True
        Me.GridView2.OptionsView.ColumnAutoWidth = False
        Me.GridView2.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView2.OptionsView.EnableAppearanceOddRow = True
        Me.GridView2.OptionsView.ShowAutoFilterRow = True
        Me.GridView2.OptionsView.ShowGroupPanel = False
        Me.GridView2.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsView.ShowIndicator = False
        Me.GridView2.OptionsView.ShowPreview = True
        Me.GridView2.OptionsView.ShowPreviewRowLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView2.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.PreviewFieldName = "Address"
        Me.GridView2.PreviewIndent = 25
        Me.GridView2.PreviewLineCount = 3
        '
        'GridColumn1
        '
        Me.GridColumn1.FieldName = "BarCode"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 77
        '
        'GridColumn2
        '
        Me.GridColumn2.Caption = "Pickup Date"
        Me.GridColumn2.DisplayFormat.FormatString = "g"
        Me.GridColumn2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.GridColumn2.FieldName = "ScannedDate"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 82
        '
        'GridColumn3
        '
        Me.GridColumn3.Caption = "Pickup By"
        Me.GridColumn3.FieldName = "DeliverBy"
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 2
        Me.GridColumn3.Width = 126
        '
        'GridColumn4
        '
        Me.GridColumn4.FieldName = "ShipWith"
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 3
        Me.GridColumn4.Width = 108
        '
        'GridColumn5
        '
        Me.GridColumn5.FieldName = "Note"
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 7
        Me.GridColumn5.Width = 169
        '
        'GridColumn6
        '
        Me.GridColumn6.FieldName = "ID"
        Me.GridColumn6.Name = "GridColumn6"
        '
        'GridColumn7
        '
        Me.GridColumn7.FieldName = "TicketID"
        Me.GridColumn7.Name = "GridColumn7"
        '
        'GridColumn8
        '
        Me.GridColumn8.FieldName = "ScannedBy"
        Me.GridColumn8.Name = "GridColumn8"
        '
        'GridColumn9
        '
        Me.GridColumn9.FieldName = "CoNum"
        Me.GridColumn9.Name = "GridColumn9"
        Me.GridColumn9.Width = 63
        '
        'GridColumn10
        '
        Me.GridColumn10.FieldName = "PayrollNum"
        Me.GridColumn10.Name = "GridColumn10"
        Me.GridColumn10.Visible = True
        Me.GridColumn10.VisibleIndex = 4
        Me.GridColumn10.Width = 64
        '
        'GridColumn11
        '
        Me.GridColumn11.FieldName = "DivNum"
        Me.GridColumn11.Name = "GridColumn11"
        Me.GridColumn11.Visible = True
        Me.GridColumn11.VisibleIndex = 5
        Me.GridColumn11.Width = 62
        '
        'GridColumn12
        '
        Me.GridColumn12.FieldName = "IsClosed"
        Me.GridColumn12.Name = "GridColumn12"
        '
        'GridColumn13
        '
        Me.GridColumn13.FieldName = "IsReturned"
        Me.GridColumn13.Name = "GridColumn13"
        '
        'GridColumn14
        '
        Me.GridColumn14.FieldName = "DivShip"
        Me.GridColumn14.Name = "GridColumn14"
        Me.GridColumn14.Visible = True
        Me.GridColumn14.VisibleIndex = 6
        Me.GridColumn14.Width = 71
        '
        'GridColumn15
        '
        Me.GridColumn15.FieldName = "CoShip"
        Me.GridColumn15.Name = "GridColumn15"
        Me.GridColumn15.Width = 71
        '
        'GridColumn16
        '
        Me.GridColumn16.FieldName = "ShipCo"
        Me.GridColumn16.Name = "GridColumn16"
        Me.GridColumn16.Width = 63
        '
        'GridColumn17
        '
        Me.GridColumn17.FieldName = "ShipAddress"
        Me.GridColumn17.Name = "GridColumn17"
        Me.GridColumn17.Width = 20
        '
        'GridColumn18
        '
        Me.GridColumn18.FieldName = "ShipCity"
        Me.GridColumn18.Name = "GridColumn18"
        Me.GridColumn18.Width = 20
        '
        'GridColumn19
        '
        Me.GridColumn19.FieldName = "ShipState"
        Me.GridColumn19.Name = "GridColumn19"
        Me.GridColumn19.Width = 20
        '
        'GridColumn20
        '
        Me.GridColumn20.FieldName = "ShipZip"
        Me.GridColumn20.Name = "GridColumn20"
        Me.GridColumn20.Width = 20
        '
        'GridColumn21
        '
        Me.GridColumn21.FieldName = "ShipContact"
        Me.GridColumn21.Name = "GridColumn21"
        Me.GridColumn21.Width = 20
        '
        'GridColumn22
        '
        Me.GridColumn22.FieldName = "ShipPhone"
        Me.GridColumn22.Name = "GridColumn22"
        Me.GridColumn22.Width = 20
        '
        'slueCompany
        '
        Me.slueCompany.Location = New System.Drawing.Point(55, 436)
        Me.slueCompany.Name = "slueCompany"
        Me.slueCompany.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueCompany.Properties.DataSource = Me.view_CompanySumarriesBindingSource
        Me.slueCompany.Properties.DisplayMember = "CoNumAndName"
        Me.slueCompany.Properties.NullText = "Search For Co#...."
        Me.slueCompany.Properties.PopupView = Me.SearchLookUpEdit1View
        Me.slueCompany.Properties.ValueMember = "CONUM"
        Me.slueCompany.Size = New System.Drawing.Size(936, 20)
        Me.slueCompany.StyleController = Me.LayoutControl1
        Me.slueCompany.TabIndex = 6
        '
        'view_CompanySumarriesBindingSource
        '
        Me.view_CompanySumarriesBindingSource.DataSource = GetType(Brands_FrontDesk.view_CompanySumarry)
        '
        'SearchLookUpEdit1View
        '
        Me.SearchLookUpEdit1View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNumAndName, Me.colCO_STATUS, Me.colOP_OWNER})
        Me.SearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.SearchLookUpEdit1View.Name = "SearchLookUpEdit1View"
        Me.SearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.SearchLookUpEdit1View.OptionsView.ShowGroupPanel = False
        '
        'colCoNumAndName
        '
        Me.colCoNumAndName.FieldName = "CoNumAndName"
        Me.colCoNumAndName.Name = "colCoNumAndName"
        Me.colCoNumAndName.Visible = True
        Me.colCoNumAndName.VisibleIndex = 0
        '
        'colCO_STATUS
        '
        Me.colCO_STATUS.FieldName = "CO_STATUS"
        Me.colCO_STATUS.Name = "colCO_STATUS"
        Me.colCO_STATUS.Visible = True
        Me.colCO_STATUS.VisibleIndex = 1
        '
        'colOP_OWNER
        '
        Me.colOP_OWNER.FieldName = "OP_OWNER"
        Me.colOP_OWNER.Name = "colOP_OWNER"
        Me.colOP_OWNER.Visible = True
        Me.colOP_OWNER.VisibleIndex = 2
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(12, 12)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(991, 373)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.Preview.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.Appearance.Preview.Options.UseFont = True
        Me.GridView1.Appearance.Row.Font = New System.Drawing.Font("Segoe UI Semibold", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.Appearance.Row.Options.UseFont = True
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colBarCode, Me.colScannedDate, Me.colDeliverBy, Me.colShipWith, Me.colNote, Me.colID, Me.colTicketID, Me.colScannedBy, Me.colCoNum, Me.colPayrollNum, Me.colDivNum, Me.colIsClosed, Me.colIsReturned, Me.colDivShip, Me.colCoShip, Me.colShipCo, Me.colShipAddress, Me.colShipCity, Me.colShipState, Me.colShipZip, Me.colShipContact, Me.colShipPhone})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsFind.SearchInPreview = True
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.AutoCalcPreviewLineCount = True
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowAutoFilterRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.OptionsView.ShowPreview = True
        Me.GridView1.OptionsView.ShowPreviewRowLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.PreviewFieldName = "Address"
        Me.GridView1.PreviewIndent = 25
        '
        'colBarCode
        '
        Me.colBarCode.FieldName = "BarCode"
        Me.colBarCode.Name = "colBarCode"
        Me.colBarCode.Visible = True
        Me.colBarCode.VisibleIndex = 0
        Me.colBarCode.Width = 77
        '
        'colScannedDate
        '
        Me.colScannedDate.Caption = "Pickup Date"
        Me.colScannedDate.DisplayFormat.FormatString = "g"
        Me.colScannedDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colScannedDate.FieldName = "ScannedDate"
        Me.colScannedDate.Name = "colScannedDate"
        Me.colScannedDate.Visible = True
        Me.colScannedDate.VisibleIndex = 1
        Me.colScannedDate.Width = 82
        '
        'colDeliverBy
        '
        Me.colDeliverBy.Caption = "Pickup By"
        Me.colDeliverBy.FieldName = "DeliverBy"
        Me.colDeliverBy.Name = "colDeliverBy"
        Me.colDeliverBy.Visible = True
        Me.colDeliverBy.VisibleIndex = 2
        Me.colDeliverBy.Width = 126
        '
        'colShipWith
        '
        Me.colShipWith.FieldName = "ShipWith"
        Me.colShipWith.Name = "colShipWith"
        Me.colShipWith.Visible = True
        Me.colShipWith.VisibleIndex = 3
        Me.colShipWith.Width = 108
        '
        'colNote
        '
        Me.colNote.FieldName = "Note"
        Me.colNote.Name = "colNote"
        Me.colNote.Visible = True
        Me.colNote.VisibleIndex = 7
        Me.colNote.Width = 169
        '
        'colID
        '
        Me.colID.FieldName = "ID"
        Me.colID.Name = "colID"
        '
        'colTicketID
        '
        Me.colTicketID.FieldName = "TicketID"
        Me.colTicketID.Name = "colTicketID"
        '
        'colScannedBy
        '
        Me.colScannedBy.FieldName = "ScannedBy"
        Me.colScannedBy.Name = "colScannedBy"
        '
        'colCoNum
        '
        Me.colCoNum.FieldName = "CoNum"
        Me.colCoNum.Name = "colCoNum"
        Me.colCoNum.Width = 63
        '
        'colPayrollNum
        '
        Me.colPayrollNum.FieldName = "PayrollNum"
        Me.colPayrollNum.Name = "colPayrollNum"
        Me.colPayrollNum.Visible = True
        Me.colPayrollNum.VisibleIndex = 4
        Me.colPayrollNum.Width = 64
        '
        'colDivNum
        '
        Me.colDivNum.FieldName = "DivNum"
        Me.colDivNum.Name = "colDivNum"
        Me.colDivNum.Visible = True
        Me.colDivNum.VisibleIndex = 5
        Me.colDivNum.Width = 62
        '
        'colIsClosed
        '
        Me.colIsClosed.FieldName = "IsClosed"
        Me.colIsClosed.Name = "colIsClosed"
        '
        'colIsReturned
        '
        Me.colIsReturned.FieldName = "IsReturned"
        Me.colIsReturned.Name = "colIsReturned"
        '
        'colDivShip
        '
        Me.colDivShip.FieldName = "DivShip"
        Me.colDivShip.Name = "colDivShip"
        Me.colDivShip.Visible = True
        Me.colDivShip.VisibleIndex = 6
        Me.colDivShip.Width = 71
        '
        'colCoShip
        '
        Me.colCoShip.FieldName = "CoShip"
        Me.colCoShip.Name = "colCoShip"
        Me.colCoShip.Width = 71
        '
        'colShipCo
        '
        Me.colShipCo.FieldName = "ShipCo"
        Me.colShipCo.Name = "colShipCo"
        Me.colShipCo.Width = 63
        '
        'colShipAddress
        '
        Me.colShipAddress.FieldName = "ShipAddress"
        Me.colShipAddress.Name = "colShipAddress"
        Me.colShipAddress.Width = 20
        '
        'colShipCity
        '
        Me.colShipCity.FieldName = "ShipCity"
        Me.colShipCity.Name = "colShipCity"
        Me.colShipCity.Width = 20
        '
        'colShipState
        '
        Me.colShipState.FieldName = "ShipState"
        Me.colShipState.Name = "colShipState"
        Me.colShipState.Width = 20
        '
        'colShipZip
        '
        Me.colShipZip.FieldName = "ShipZip"
        Me.colShipZip.Name = "colShipZip"
        Me.colShipZip.Width = 20
        '
        'colShipContact
        '
        Me.colShipContact.FieldName = "ShipContact"
        Me.colShipContact.Name = "colShipContact"
        Me.colShipContact.Width = 20
        '
        'colShipPhone
        '
        Me.colShipPhone.FieldName = "ShipPhone"
        Me.colShipPhone.Name = "colShipPhone"
        Me.colShipPhone.Width = 20
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlGroup2, Me.SplitterItem1})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1015, 706)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(995, 377)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.LayoutControlItem2})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 387)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(995, 299)
        Me.LayoutControlGroup2.Text = "Search By Company"
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.slueCompany
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(971, 24)
        Me.LayoutControlItem3.Text = "Co#: "
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(28, 13)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.GridControl2
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(971, 226)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.Inverted = True
        Me.SplitterItem1.IsCollapsible = DevExpress.Utils.DefaultBoolean.[True]
        Me.SplitterItem1.Location = New System.Drawing.Point(0, 377)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(995, 10)
        '
        'ucDeliveryHistory
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "ucDeliveryHistory"
        Me.Size = New System.Drawing.Size(1015, 706)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueCompany.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.view_CompanySumarriesBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTicketID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colScannedBy As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colScannedDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBarCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPayrollNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDivNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNote As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDeliverBy As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsClosed As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipWith As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsReturned As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDivShip As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoShip As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipCo As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipAddress As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipCity As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipState As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipZip As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipContact As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colShipPhone As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents slueCompany As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents SearchLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn7 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn8 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn9 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn10 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn11 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn12 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn13 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn14 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn15 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn16 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn17 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn18 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn19 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn20 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn21 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn22 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents view_CompanySumarriesBindingSource As BindingSource
    Friend WithEvents colCoNumAndName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colOP_OWNER As DevExpress.XtraGrid.Columns.GridColumn
End Class
