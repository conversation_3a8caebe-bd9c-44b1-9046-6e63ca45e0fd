﻿Imports Brands.DAL.DomainModels.App
Imports Flurl
Imports Flurl.Http

Public Class frmConnectedSessions
    Dim _timer As Timer

    Private Sub FrmConnectedSessions_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
        cbeSiteUrl.Properties.Items.Clear()
        cbeSiteUrl.Properties.Items.AddRange(modGlobals.GetUdfValueSplitted("PaydeckSiteUrls"))
    End Sub

    Private Sub BbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Async Sub LoadData()
        Try
            bbiRefresh.Enabled = False
            Dim connections = Await $"{cbeSiteUrl.Text}/api/HubConnections/MainHub".WithHeader("X-API-SECRET-HUBCONNECTIONS-KEY", GetUdfValue("X-API-SECRET-HUBCONNECTIONS-KEY")) _
                .GetJsonAsync(Of List(Of WebAppMainSignalRConnection))()
            GridControl1.DataSource = connections
            GridControl1.RefreshDataSource()
        Catch ex As Exception
            DisplayErrorMessage("Error getting connections", ex)
        Finally
            bbiRefresh.Enabled = True
        End Try
    End Sub

    Private Sub CeAutoRefresh_CheckedChanged(sender As Object, e As EventArgs) Handles ceAutoRefresh.CheckedChanged
        seAutoRefreshSeconds.Enabled = Not ceAutoRefresh.Checked
        If ceAutoRefresh.Checked Then
            _timer = New Timer()
            _timer.Enabled = True
            _timer.Interval = TimeSpan.FromSeconds(seAutoRefreshSeconds.Value).TotalMilliseconds
            AddHandler _timer.Tick, Sub()
                                        LoadData()
                                    End Sub
        Else
            _timer.Stop()
            _timer.Dispose()
            _timer = Nothing
        End If
    End Sub

    Private Sub FrmConnectedSessions_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If _timer IsNot Nothing Then
            _timer.Stop()
            _timer.Dispose()
            _timer = Nothing
        End If
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As WebAppMainSignalRConnection = GridView1.GetRow(e.HitInfo.RowHandle)
            If row IsNot Nothing Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Force Refresh", Sub() InvokeMethod(row.ConnectionId), My.Resources.refresh2_16x16))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Open Seq Logs", Sub() OpenLogs(row.IpAddress)))
            End If
        End If
    End Sub

    Private Async Sub InvokeMethod(connectionId As String)
        Dim connections = Await $"{cbeSiteUrl.Text}/api/HubConnections/InvokeMethod".SetQueryParams(New With {.connectionId = connectionId, .method = "ForceRefreshConnection"}) _
            .WithHeader("X-API-SECRET-HUBCONNECTIONS-KEY", GetUdfValue("X-API-SECRET-HUBCONNECTIONS-KEY")) _
            .PostStringAsync("")
    End Sub

    Private Sub OpenLogs(ip As String)
        Try
            'System.Diagnostics.Process.Start($"http://appserver:5341/#/events?signal=signal-3,signal-5,signal-162&filter=IPAddress%20%3D%20'{ip}'")
            Dim filePath = $"http://appserver:5341/#/events?signal=signal-3,signal-5,signal-162&filter=IPAddress%20%3D%20'{ip}'"
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = filePath
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error opening Seq logs", ex)
        End Try
    End Sub
End Class