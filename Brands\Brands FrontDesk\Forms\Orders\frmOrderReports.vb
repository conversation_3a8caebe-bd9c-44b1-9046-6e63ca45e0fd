﻿Imports DevExpress.XtraEditors

Public Class frmOrderReports
    Private ReadOnly _Conum As Decimal
    Private ReadOnly _Path As String
    Private ReadOnly _ShipPrinterName As String
    Private ReadOnly _ProductID As Integer
    Private ReadOnly _ReportName As String
    Private _FrmShipAddressSelection As frmOrderShippingAddressSelection
    Private _FrmShipAddressSelectionResult As DialogResult?
    Private _OrderSuppliesId As Integer = -1

    Public Sub New(_conum As Decimal, _path As String, _shipPrinterName As String, _reportName As String, _productID As Integer)
        InitializeComponent()
        Me._Conum = _conum
        Me._Path = _path
        Me._ShipPrinterName = _shipPrinterName
        Me._ProductID = _productID
        Me._ReportName = _reportName
        Text = $"New Order - {_reportName}"
        lcHeader.Text = $"{_reportName} Order For Co#: {_conum}"
    End Sub

    Private Sub frmOrderManualCheck_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        'check if open orders is available
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim openOrders = ctxDB.SuppliesOrders.Where(Function(o) o.CoNum = _Conum AndAlso {"Pending", "Open"}.Contains(o.Status))
            If openOrders.Any() Then
                btnAddToExistingOrder.Enabled = True
            Else
                btnAddToExistingOrder.Enabled = False
            End If
        End Using
        _FrmShipAddressSelection = New frmOrderShippingAddressSelection(_Conum, False)
    End Sub

    Private Sub btnAddToExistingOrder_Click(sender As Object, e As EventArgs) Handles btnAddToExistingOrder.Click
        Using frm = New frmCompanyOpenOrders(_Conum)
            If frm.ShowDialog() = DialogResult.OK Then
                _OrderSuppliesId = frm.SelectedOrderNumber
                btnSelectShippingAddress.Enabled = False
                lcNewOrExistingOrder.Text = $"Adding to existing order. Order #: {_OrderSuppliesId}"
                lcShippingAddress.Text = "Using the linked order Shipping address."
                lciShipAsNewOrder.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            End If
        End Using
    End Sub

    Private Sub btnShipAsNewOrder_Click(sender As Object, e As EventArgs) Handles btnShipAsNewOrder.Click
        _OrderSuppliesId = -1
        btnSelectShippingAddress.Enabled = True
        lcShippingAddress.Text = "No Shipping Address Selected"
        lciShipAsNewOrder.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        lcNewOrExistingOrder.Text = "Shipping as new order"
    End Sub

    Private Sub btnSelectShippingAddress_Click(sender As Object, e As EventArgs) Handles btnSelectShippingAddress.Click
        _FrmShipAddressSelectionResult = _FrmShipAddressSelection.ShowDialog()
        If _FrmShipAddressSelectionResult = DialogResult.OK Then
            If _FrmShipAddressSelection.IsDivionNumberSelected Then
                lcShippingAddress.Text = $"Shipping to Div#: {_FrmShipAddressSelection.DivionNumber}"
            Else
                lcShippingAddress.Text = "Shipping to custom address"
            End If
        End If
    End Sub

    Private Sub btnSubmit_Click(sender As Object, e As EventArgs) Handles btnSubmit.Click
        If _OrderSuppliesId = -1 AndAlso (_FrmShipAddressSelectionResult Is Nothing OrElse _FrmShipAddressSelectionResult <> DialogResult.OK) Then
            XtraMessageBox.Show("Please select a shipping address")
            Exit Sub
        End If
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim document_id = Guid.NewGuid()
            Dim document = New document_storage With {.storage_id = Guid.NewGuid(), .conum = _Conum, .created_by = UserName, .created_date = DateTime.Now, .name = _Path, .description = _ReportName, .document_id = document_id, .modify_date = DateTime.Now, .type = ".pdf", .keywords = _ShipPrinterName}
            document.blob = System.IO.File.ReadAllBytes(_Path)
            document.size = New System.IO.FileInfo(_Path).Length
            db.document_storages.InsertOnSubmit(document)
            Dim order As SuppliesOrder
            If _OrderSuppliesId = -1 Then
                order = New SuppliesOrder With {.CoNum = _Conum, .ByUser = UserName, .Date = DateTime.Now, .SendWithNextPayroll = False, .Status = "Open"}
                If _FrmShipAddressSelection.IsDivionNumberSelected Then order.DivNum = _FrmShipAddressSelection.DivionNumber
                db.SuppliesOrders.InsertOnSubmit(order)
            Else
                order = db.SuppliesOrders.Single(Function(o) o.ID = _OrderSuppliesId)
            End If

            Dim reportItem = db.Supplies.Single(Function(c) c.ProductID = _ProductID)
            order.SuppliesOrderItems.Add(New SuppliesOrderItem With {
                                             .ProductID = _ProductID,
                                             .Qty = 1,
                                             .UnitPrice = reportItem.PricePerPack,
                                             .UOM = "Pack",
                                             .document_Id = document_id,
                                             .WaiveCharge = ceWaiveCharge.Checked,
                                             .WaiveChargeReason = meWaiveChargeReason.Text})

            If _OrderSuppliesId = -1 AndAlso Not _FrmShipAddressSelection.IsDivionNumberSelected Then
                db.SubmitChanges()
                _FrmShipAddressSelection.ShippingAddressOverride.PRNUM = order.ID
                order.DivNum = 0
                db.ShipAddressOverrides.InsertOnSubmit(_FrmShipAddressSelection.ShippingAddressOverride)
            End If
            db.SubmitChanges()
            modSignalRClient.SuppliesOrderUpdated(order)
            If _OrderSuppliesId = -1 Then
                XtraMessageBox.Show($"New order submited sucsessfully.{vbCrLf}Order #: {order.ID}")
            Else
                XtraMessageBox.Show($"Order #: {order.ID} was updated sucsessfully.")
            End If
        End Using
        DialogResult = DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Private Sub ceWaiveCharge_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles ceWaiveCharge.EditValueChanging
        If e.NewValue Then
            Using frm = New frmWaiveChargeReason
                If frm.ShowDialog = DialogResult.OK Then
                    meWaiveChargeReason.Text = frm.meWaiveChargeReason.Text
                Else
                    e.Cancel = True
                End If
            End Using
        Else
            meWaiveChargeReason.Text = Nothing
        End If
    End Sub
End Class