﻿Imports DevExpress.XtraEditors

Public Class frmPhoneSetup

    Dim db As dbEPDataDataContext
    Dim selectedRow As FrontDeskPermission
    ReadOnly _username As String
    ReadOnly logger As Serilog.ILogger

    Public Sub New()
        InitializeComponent()
        logger = modGlobals.Logger.ForContext(Of frmPhoneSetup)()
    End Sub

    Public Sub New(username As String)
        Me.New
        _username = username
        If _username.IsNotNullOrWhiteSpace Then
            lcgUserList.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            SplitterItem1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            Width = 400
        End If
        logger.Debug("Openned form. Username: {Username}", _username)
    End Sub

    Private Sub LoadUsers()
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            Dim users = db.FrontDeskPermissions.ToList().Where(Function(u) _username.IsNullOrWhiteSpace() OrElse u.UserName.ToLower() = _username.ToLower).ToList()
            GridControl1.DataSource = users
            If _username.IsNotNullOrWhiteSpace Then
                If users.Count > 1 Then
                    logger.Warning("multiple users returned when username was set")

                End If
                If users.Count <> 1 Then
                    DisplayErrorMessage("User object not found", New Exception())
                    Close()
                End If
                selectedRow = users.Single()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading users", ex)
        End Try
    End Sub

    Private Sub frmPhoneSetup_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadUsers()
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        LoadUsers()
    End Sub

    Private Async Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Try
            lcRoot.ShowProgessPanel
            Dim api = New Brands.Core.TelebroadApi(GetUserName(teUsername.Text, selectedRow.UserName), modPhoneApi.GetUserPassword(tePassword.Text), Logger)
            Dim results = Await api.GetProfile()
            meTestResults.Text = results
        Catch ex As Exception
            DisplayErrorMessage("Error logging in", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Public Function GetUserName(tempUsername As String, username As String) As String
        If tempUsername.IsNotNullOrWhiteSpace Then Return tempUsername

        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim user As DBUSER
        user = (From u In db.DBUSERs Where u.name.ToLower = username).SingleOrDefault
        Return user.email
    End Function

    Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GridView1.FocusedRowObjectChanged
        Try
            meTestResults.Text = String.Empty
            selectedRow = e.Row
            teSnumber.Text = selectedRow?.Phonesnumber?.ToString
            teUsername.Text = selectedRow?.PhoneUserName
            tePassword.Text = selectedRow?.PhonePassword

            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim user As DBUSER
            user = (From u In db.DBUSERs Where u.name.ToLower = selectedRow.UserName).SingleOrDefault
            lcDefaultUsername.Text = user?.email
        Catch ex As Exception
            DisplayErrorMessage("Error in GridView1_FocusedRowObjectChanged", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            selectedRow.Phonesnumber = teSnumber.Text
            selectedRow.PhoneUserName = teUsername.Text
            selectedRow.PhonePassword = tePassword.Text
            db.SaveChanges()
            modSignalRClient.PushRefreshPermission(selectedRow.UserName)
            If _username.IsNotNullOrWhiteSpace Then
                DialogResult = DialogResult.OK
            Else
                XtraMessageBox.Show($"Success! Please ask {selectedRow.UserName} to try again.")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        End Try
    End Sub
End Class