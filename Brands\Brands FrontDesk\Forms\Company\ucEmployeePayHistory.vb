﻿Imports System.ComponentModel
Imports System.Data
Imports System.Data.SqlClient
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.XtraSplashScreen
Imports Serilog
Public Class ucEmployeePayHistory

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property CoNum As Decimal?
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Empnum As Decimal?
    Private Property IsInDesignMode As Boolean = False
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Logger As ILogger
    Private tblPayDetails As DataTable

    Sub New()
        If LicenseManager.UsageMode = LicenseUsageMode.Designtime Then IsInDesignMode = True
        InitializeComponent()
        Logger = modGlobals.Logger.ForContext(Of ucEmployeePayHistory)
        AddHandler gvCheckMaster.SelectionChanged, AddressOf gvCheckMaster_SelectionChanged
    End Sub

    Private Sub frmPayHistory_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If IsInDesignMode Then Exit Sub
        SetDateFilters()
        LoadPays(True)
    End Sub

    Private Sub SetDateFilters()
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(New DateRange(GetQuarterStartDate(Today.Year, 1), GetQuarterEndDate(Today.Year, 1)), $"Q1 - {Today.Year}"))
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(New DateRange(GetQuarterStartDate(Today.Year, 2), GetQuarterEndDate(Today.Year, 2)), $"Q2 - {Today.Year}"))
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(New DateRange(GetQuarterStartDate(Today.Year, 3), GetQuarterEndDate(Today.Year, 3)), $"Q3 - {Today.Year}"))
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(New DateRange(GetQuarterStartDate(Today.Year, 4), GetQuarterEndDate(Today.Year, 4)), $"Q4 - {Today.Year}"))
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(New DateRange(New DateTime(Today.Year - 1, 1, 1), New DateTime(Today.Year - 1, 12, DateTime.DaysInMonth(Today.Year - 1, 12))), Today.AddYears(-1).Year))
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(New DateRange(New DateTime(Today.Year, 1, 1), New DateTime(Today.Year, 12, DateTime.DaysInMonth(Today.Year, 12))), Today.Year))
        rgDateFilter.Properties.Items.Add(New RadioGroupItem(Nothing, "Custom"))
        rgDateFilter.SelectedIndex = 6
    End Sub

    Public Sub LoadPayHistory(_conum As Decimal, _empnum As Decimal)
        Logger.Debug("Loading pay history for Co#: {Conum} Emp#: {Empnum}", CoNum, Empnum)
        CoNum = _conum
        Empnum = _empnum
        LoadPayHistory()
    End Sub

    Private Sub LoadPayHistory()
        Try
            Dim FromDate, ToDate As Date?
            If dePayHistoryFromDate.HasValue Then FromDate = dePayHistoryFromDate.EditValue
            If dePayHistoryFromTo.HasValue Then ToDate = dePayHistoryFromTo.EditValue
            gcCheckMaster.DataSource = Query(Of CheckMaster)($"exec [custom].[ep_ReportPayrollChkMast] @Conum = {CoNum}, @EmpNum = {Empnum}, @FromDate = {FromDate.QuoteSQL()}, @ToDate = {ToDate.QuoteSQL()}")
            tblPayDetails = Nothing
            gcPayHistory.DataSource = Nothing
            gcPayrollDeductionsAndTaxes.DataSource = Nothing
            'rgDateFilter_EditValueChanged(Nothing, Nothing)
            LoadPays(False)
        Catch ex As Exception
            Logger.Error(ex, "Error loading pay history. Co#: {Conum} Emp#: {empnum}", CoNum, Empnum)
            DisplayErrorMessage("Error loading pay history", ex)
        End Try
    End Sub

    Sub LoadPays(Optional SchemaOnly As Boolean = False)
        Dim selectedRows = gvCheckMaster.GetSelectedRows(Of CheckMaster)
        If selectedRows.Count = 0 AndAlso Not SchemaOnly Then
            'ToDo
            gcPayHistory.DataSource = Nothing
            Return
        End If
        Dim tblResults As New DataTable
        Dim tblP As New DataTable()
        tblP.Columns.Add("Col1", GetType(System.Decimal))
        tblP.Columns.Add("Col2", GetType(System.Decimal))
        For Each itm In selectedRows
            tblP.Rows.Add(itm.Prnum, itm.CheckCounter)
        Next
        Using conn As New SqlConnection(GetConnectionString)
            Dim cmd = conn.CreateCommand
            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "[custom].[ep_ReportPayrollPays1]"
            cmd.Parameters.AddWithValue("@Conum", If(CoNum.HasValue, CoNum, DBNull.Value))
            cmd.Parameters.AddWithValue("@Empnum", If(Empnum.HasValue, Empnum.Value, DBNull.Value))
            cmd.Parameters.AddWithValue("@PrChkNums", tblP)

            conn.Open()
            Dim reader = cmd.ExecuteReader()
            tblResults.Load(reader)
            reader.Close()
            conn.Close()
        End Using

        tblPayDetails = tblResults

        If SchemaOnly Then
            gvPayHistory.Columns.Clear()
            gvPayHistory.PopulateColumns(tblResults)

            'Hide Pr and Chk Counter
            gvPayHistory.Columns("PrNum").Visible = False
            gvPayHistory.Columns("ChkCounter").Visible = False
            gvPayHistory.Columns("PayCode").Visible = False
            gvPayHistory.Columns("PayCode").SortOrder = DevExpress.Data.ColumnSortOrder.Ascending

            For Each col In {gvPayHistory.Columns("Amount"), gvPayHistory.Columns("Hours")}
                col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                col.DisplayFormat.FormatString = "N2"
                col.SummaryItem.DisplayFormat = "{0:N2}"
                col.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            Next
            For Each col As DataColumn In tblPayDetails.Columns
                Select Case Type.GetTypeCode(col.DataType)
                    Case TypeCode.Decimal, TypeCode.Int32, TypeCode.Int16, TypeCode.Double, TypeCode.Single, TypeCode.Int64
                        Dim gCol = Me.gvPayHistory.Columns(col.ColumnName)
                        gCol.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
                        gCol.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
                End Select
                'If col.ColumnType = GetType(Decimal) Then
            Next

            'Get Captions
            Dim CoJobLabels As List(Of CO_UDF)
            Using db = New dbEPDataDataContext(GetConnectionString)
                CoJobLabels = (From A In db.CO_UDFs Where A.CONUM = CoNum AndAlso A.UDF_DESCR.ToLower.Contains("job")).ToList
            End Using
            For x = 1 To 5
                Dim JobNum = x
                Dim jobCol = gvPayHistory.Columns("Job" & x)
                If jobCol IsNot Nothing Then
                    Dim JobLabel = (From A In CoJobLabels Where A.UDF_DESCR.ToLower.Contains("job " & JobNum) Select A.UDF_STRING).FirstOrDefault
                    If JobLabel IsNot Nothing Then
                        jobCol.Caption = JobLabel
                    Else
                        jobCol.Visible = False
                    End If
                End If
            Next
            Return
        End If

        DisplayPayDetails()
    End Sub

    Private Sub rgDateFilter_EditValueChanged(sender As Object, e As EventArgs) Handles rgDateFilter.EditValueChanged, dePayHistoryFromTo.EditValueChanged, dePayHistoryFromDate.EditValueChanged
        If Not Empnum.HasValue Then Exit Sub
        If Not CType(sender, DevExpress.XtraEditors.BaseEdit).IsModified Then Exit Sub

        Dim item As DateRange = rgDateFilter.EditValue
        If item IsNot Nothing Then
            dePayHistoryFromDate.EditValue = item.FromDate
            dePayHistoryFromTo.EditValue = item.ToDate
            'CheckRowsInDateRange(item.FromDate, item.ToDate)

            LoadPayHistory()
            CheckRowsInDateRange(dePayHistoryFromDate.DateTime, dePayHistoryFromTo.DateTime)
        End If

        lciFromDate.Enabled = (item Is Nothing) '.ToBarItemVisibility
        lciToDate.Enabled = (item Is Nothing) '.ToBarItemVisibility
    End Sub

    Private Sub dePayHistory_EditValueChanged(sender As Object, e As EventArgs) Handles dePayHistoryFromTo.EditValueChanged, dePayHistoryFromDate.EditValueChanged
        If Not Empnum.HasValue Then Exit Sub
        If Not CType(sender, DevExpress.XtraEditors.BaseEdit).IsModified Then Exit Sub

        If dePayHistoryFromDate.HasValue AndAlso dePayHistoryFromTo.HasValue AndAlso dePayHistoryFromTo.DateTime > dePayHistoryFromDate.DateTime Then
            LoadPayHistory()
            CheckRowsInDateRange(dePayHistoryFromDate.DateTime, dePayHistoryFromTo.DateTime)
        End If
    End Sub

    Private Sub CheckRowsInDateRange(startDate As DateTime, endDate As DateTime)
        RemoveHandler gvCheckMaster.SelectionChanged, AddressOf gvCheckMaster_SelectionChanged
        Try
            gvCheckMaster.ClearSelection()
            Dim lastIndex As Integer
            For index = 0 To gvCheckMaster.RowCount - 1
                Dim checkDate As DateTime = gvCheckMaster.GetRowCellValue(index, colCheckDate)
                If checkDate >= startDate AndAlso checkDate <= endDate Then
                    gvCheckMaster.SelectRow(index)
                    lastIndex = index
                End If
            Next
            gvCheckMaster.MakeRowVisible(lastIndex)
            gvCheckMaster_SelectionChanged(Nothing, Nothing)
        Finally
            AddHandler gvCheckMaster.SelectionChanged, AddressOf gvCheckMaster_SelectionChanged
        End Try
    End Sub

    Private Sub gvCheckMaster_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs)
        Try
            LayoutControl1.ShowProgessPanel
            Application.DoEvents()
            Dim list = gvCheckMaster.GetSelectedRows(Of CheckMaster)
            Dim prnums = String.Join(",", list.Select(Function(dr) dr.Prnum))
            Dim prnumsAndChkCounters = String.Join(",", list.Select(Function(dr) $"{dr.Prnum}-{dr.CheckCounter}"))
            Using dxDB = New dbEPDataDataContext(GetConnectionString)
                LoadPays()

                Dim dt As New List(Of PayrollDeductionsAndTaxes)
                Dim ded As New PayrollDeductionsAndTaxes With {.DetailType = "Deductions", .Caption = "Deductions"}
                Dim EEtax = New PayrollDeductionsAndTaxes With {.DetailType = "EETaxes", .Caption = "Taxes"}
                Dim ERtax = New PayrollDeductionsAndTaxes With {.DetailType = "EETaxes", .Caption = "Employer Taxes"}

                Dim result2 = dxDB.ep_ReportPayrollTaxes(CoNum, prnums, Empnum).ToList
                Dim groupedTaxes = result2.Where(Function(t) prnumsAndChkCounters.Contains($"{t.prnum}-{t.chk_counter}")) _
                    .GroupBy(Function(d) New With {Key d.descr, Key d.Cat}) _
                    .Select(Function(grp) New Taxes With {.Category = grp.Key.Cat,
                        .Description = grp.Key.descr,
                        .Amount = grp.Sum(Function(f) f.current),
                        .Capped = grp.Sum(Function(f) f.taxable),
                        .Taxable = grp.Sum(Function(f) f.Gross)}
                        )
                EEtax.Details = (From A In groupedTaxes Where A.Category = "EE" AndAlso A.Amount <> 0).ToList
                ERtax.Details = (From A In groupedTaxes Where A.Category = "ER" AndAlso A.Amount <> 0).ToList
                'gcPayrollTaxes.DataSource = groupedTaxes
                'gvPayrollTaxes.BestFitColumns()

                Dim result3 = dxDB.ep_ReportPayrollDeds(CoNum, prnums, Empnum)
                Dim groupedDeds = result3.Where(Function(t) prnumsAndChkCounters.Contains($"{t.PAYROLL_NUM}-{t.CHK_COUNTER}")) _
                    .GroupBy(Function(d) d.Deductions) _
                    .Select(Function(grp) New Taxes With {.Description = grp.Key,
                        .Amount = grp.Sum(Function(f) f.Amount)})
                ded.Details = groupedDeds.ToList
                'gcPayrollDeds.DataSource = groupedDeds

                If ded.Details.Count > 0 Then dt.Add(ded)
                If EEtax.Details.Count > 0 Then dt.Add(EEtax)
                If ERtax.Details.Count > 0 Then dt.Add(ERtax)
                Me.gcPayrollDeductionsAndTaxes.DataSource = dt

                gcPayrollDeductionsAndTaxes.ForceInitialize()
                gvPayrollDeductionsAndTaxes.SetMasterRowExpanded(0, True)
                gvPayrollDeductionsAndTaxes.SetMasterRowExpanded(1, True)
                gvPayrollDeductionsAndTaxes.SetMasterRowExpanded(2, True)
                Me.gvDeductions.BestFitColumns()
                Me.gvEETaxes.BestFitColumns()
                Me.gvERTaxes.BestFitColumns()

                'Dim info = gvPayrollDeds.GetViewInfo
                'Dim vhight = info.CalcRealViewHeight(New Rectangle(gcPayrollDeds.Location.X, gcPayrollDeds.Location.Y, gcPayrollDeds.Width, 10000)) + 20
                'Me.LayoutControlItemDeductions.Height = vhight
                'Me.gcPayrollDeds.Height = vhight
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error showing report", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub


    Sub DisplayPayDetails()
        If tblPayDetails Is Nothing OrElse tblPayDetails.Rows.Count = 0 Then Return
        Dim GroupColumns = (From A In Me.gvPayHistory.VisibleColumns Where Not A.FieldName = "Hours" AndAlso Not A.FieldName = "Amount").ToList
        Dim GroupExp = String.Join(" + ", (From A In GroupColumns Select "IsNull([" & A.FieldName & "],'')"))
        If tblPayDetails.Columns("GroupBy") Is Nothing Then tblPayDetails.Columns.Add("GroupBy", GetType(System.String))
        tblPayDetails.Columns("GroupBy").Expression = GroupExp
        Dim GroupedData = (From A In tblPayDetails Group By GroupValues = A("GroupBy") Into Group, Hours = Sum(A.Field(Of Decimal?)("Hours")), Amount = Sum(A.Field(Of Decimal?)("Amount"))).ToList

        Dim list As New DataTable
        For Each col In GroupColumns
            list.Columns.Add(col.FieldName)
        Next
        list.Columns.Add("Hours", GetType(System.Decimal))
        list.Columns.Add("Amount", GetType(System.Decimal))

        For Each gRow In GroupedData
            Dim newRow = list.NewRow
            For Each col In GroupColumns
                newRow(col.FieldName) = gRow.Group(0)(col.FieldName)
            Next
            If gRow.Hours.HasValue Then newRow("Hours") = gRow.Hours
            If gRow.Amount.HasValue Then newRow("Amount") = gRow.Amount
            list.Rows.Add(newRow)
        Next

        Dim dt As New List(Of PayrollDeductionsAndTaxes)
        Dim pays As New PayrollDeductionsAndTaxes With {.DetailType = "Details", .Caption = "Pay Details", .Details = list.AsDataView()}
        dt.Add(pays)

        gcPayHistory.DataSource = dt
        gvPayHistory1.SetMasterRowExpanded(0, True)
        gvPayHistory.BestFitColumns()
    End Sub

    Private Sub gvPayHistory_ColumnPositionChanged(sender As Object, e As EventArgs) Handles gvPayHistory.ColumnPositionChanged
        'when adding or removing columns
        DisplayPayDetails()
    End Sub

    Private Sub gvCheckMaster_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvCheckMaster.PopupMenuShowing
        If e.HitInfo.RowHandle >= 0 AndAlso e.Allow AndAlso e.HitInfo.InRow Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Void Check && DD Reversals", Sub() VoidSelectedChecks(), My.Resources.reverssort_16x16))

            Dim selectedchecks = gvCheckMaster.GetSelectedRows(Of CheckMaster)
            Dim selectedRowsCount = gvCheckMaster.SelectedRowsCount
            If selectedRowsCount = 0 Then selectedchecks.Add(gvCheckMaster.GetRow(e.HitInfo.RowHandle))
            Dim selectedText = IIf(selectedRowsCount > 1, $" - Selected Checks ({selectedRowsCount})", "")

            Dim printMenu = New DXSubMenuItem("Print Check") With {.Image = My.Resources.print_16x16, .BeginGroup = True}
            printMenu.Items.Add(New DXMenuItem($"Payroll Check {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Print", "R", Nothing)))
            printMenu.Items.Add(New DXMenuItem($"Payroll Check - Pressure Seal {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Print", "P", Nothing)))
            printMenu.Items.Add(New DXMenuItem($"Payroll Check Stubs {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Print", "S", Nothing)))
            e.Menu.Items.Add(printMenu)

            Dim zendeskBackgroundMenu = New DXSubMenuItem("Email Check (Via Zendesk - Queued)") With {.Image = My.Resources.Email}
            zendeskBackgroundMenu.Items.Add(New DXMenuItem($"Payroll Check {selectedText}", Sub() ProcessCheckReport(selectedchecks, "ZendeskQueue", "R", Nothing)))
            zendeskBackgroundMenu.Items.Add(New DXMenuItem($"Payroll Check - Pressure Seal {selectedText}", Sub() ProcessCheckReport(selectedchecks, "ZendeskQueue", "P", Nothing)))
            zendeskBackgroundMenu.Items.Add(New DXMenuItem($"Payroll Check Stubs {selectedText}", Sub() ProcessCheckReport(selectedchecks, "ZendeskQueue", "S", Nothing)))
            e.Menu.Items.Add(zendeskBackgroundMenu)

            Dim shipMenu = New DXSubMenuItem("Ship Check") With {.Image = My.Resources.print_16x16}
            shipMenu.Items.Add(New DXMenuItem($"Payroll Check {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Ship", "R", "\ChecksPrinter")))
            shipMenu.Items.Add(New DXMenuItem($"Payroll Check - Pressure Seal {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Ship", "P", "\ChecksPressPrinter")))
            shipMenu.Items.Add(New DXMenuItem($"Payroll Check Stubs {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Ship", "S", Nothing)))
            e.Menu.Items.Add(shipMenu)

            Dim moreMenue = New DXSubMenuItem("More")

            Dim zendeskMenu = New DXSubMenuItem("Email Check (Via Zendesk)") With {.Image = My.Resources.Email}
            zendeskMenu.Items.Add(New DXMenuItem($"Payroll Check {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Zendesk", "R", Nothing)))
            zendeskMenu.Items.Add(New DXMenuItem($"Payroll Check - Pressure Seal {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Zendesk", "P", Nothing)))
            zendeskMenu.Items.Add(New DXMenuItem($"Payroll Check Stubs {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Zendesk", "S", Nothing)))
            moreMenue.Items.Add(zendeskMenu)

            Dim emailMenu = New DXSubMenuItem("Email Check") With {.Image = My.Resources.Email}
            emailMenu.Items.Add(New DXMenuItem($"Payroll Check {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Email", "R", Nothing)))
            emailMenu.Items.Add(New DXMenuItem($"Payroll Check - Pressure Seal {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Email", "P", Nothing)))
            emailMenu.Items.Add(New DXMenuItem($"Payroll Check Stubs {selectedText}", Sub() ProcessCheckReport(selectedchecks, "Email", "S", Nothing)))
            moreMenue.Items.Add(emailMenu)


            e.Menu.Items.Add(moreMenue)
        End If
    End Sub

    Private Async Sub ProcessCheckReport(rows As List(Of CheckMaster), deliverVia As String, reportCode As String, shipPrinterName As String)
        Dim handle As IOverlaySplashScreenHandle = Nothing
        Try
            Logger.Debug("Entering ProcessCheckReport. deliverVia: {deliverVia} reportCde: {reportCode} shipPrinterName: {shipPrinterName}", deliverVia, reportCode, shipPrinterName)
            handle = SplashScreenManager.ShowOverlayForm(Me)
            'If shipPrinterName.IsNotNullOrWhiteSpace AndAlso rows.Select(Function(div) div.DIVNUM).Distinct().Count > 1 Then
            '    If XtraMessageBox.Show($"Attention! You have selected multiple checks to be shipped, but it can only be delivered to one shipping address, if you need to ship it to multiple addresses, please select that checks separately ", "Multiple Checks Selected", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            '        Exit Sub
            '    End If
            'End If

            Dim reportName = If(reportCode = "P", "Presure Seal Checks", "Regular Checks")
            If deliverVia = "ZendeskQueue" Then
                Using db = New dbEPDataDataContext(GetConnectionString)
                    Dim _report = db.ReportEmailTeplates.SingleOrDefault(Function(r) r.Name = reportName)
                    Dim queue = New ReportProcessorQueue(CoNum, Nothing, Nothing)

                    Using frm = New frmSelectNewOrExistingTicket
                        frm.ShowDialog()
                        If frm.cbUpdateTicket.Checked Then
                            queue.UpdateZendeskTicketId = frm.seZendeskTicketId.Value
                        End If
                    End Using

                    For Each check In rows
                        Dim defaultParamValues = New List(Of KeyValuePair)
                        defaultParamValues.Add(New KeyValuePair("CoNum", CoNum))
                        defaultParamValues.Add(New KeyValuePair("PrNum", check.Prnum))
                        defaultParamValues.Add(New KeyValuePair("EmpNum", check.Empnum))
                        defaultParamValues.Add(New KeyValuePair("ChkCounter", check.CheckCounter))

                        If reportCode = "S" Then
                            defaultParamValues.Add(New KeyValuePair("AllSpcReg_AorSorR", "V"))
                        End If

                        Await queue.AddReport(_report.ID, False, defaultParamValues)
                    Next

                    Await queue.SendReports("Payroll Check Requested", Nothing, _report)
                    Exit Sub
                End Using
            End If

            Dim result As ReportResults = Nothing
            Try
                Dim rptProcessor = New ReportProcessor(CoNum, reportName, FileType.Pdf) With {.showInRecentReports = True, .showParametersForm = False}
                For Each check In rows
                    rptProcessor.DefaultParamValues = New List(Of KeyValuePair)
                    rptProcessor.DefaultParamValues.Add(New KeyValuePair("PrNum", check.Prnum))
                    rptProcessor.DefaultParamValues.Add(New KeyValuePair("EmpNum", check.Empnum))
                    rptProcessor.DefaultParamValues.Add(New KeyValuePair("ChkCounter", check.CheckCounter))
                    If reportCode = "S" Then
                        rptProcessor.DefaultParamValues.Add(New KeyValuePair("AllSpcReg_AorSorR", "V"))
                    End If
                    Dim r = rptProcessor.ProcessReport()
                    If result Is Nothing Then
                        result = r
                    Else
                        result.Paths.AddRange(r.Paths)
                    End If
                Next
                Dim saveLocation = System.IO.Path.Combine(modReports.GetCrystalReportsFolder(), modReports.GetFileName(result.Comp, reportName, ".pdf"))
                PdfUtilities.CombinePdfsNew(saveLocation, result.Paths.ToArray)
                result.Paths.Clear()
                result.Paths.Add(saveLocation)
            Catch ex As Exception
                Logger.Error(ex, "Error processing manual check report.")
                Throw
            Finally

            End Try

            If Not result.Cancalled AndAlso Not result.AllFileExist Then
                DisplayMessageBox("No records returned for the parameters values entered")
                Exit Sub
            End If

            If deliverVia = "Email" Then
                Dim sender = New ReportSender(result) With {.sendAttachmentsAsZip = True, .showWebPost = False}
                sender.EmailReport()
            ElseIf deliverVia = "Zendesk" Then
                Dim sender = New ReportSender(result) With {.showWebPost = False}
                Await sender.CreateTicketAsync()
            ElseIf shipPrinterName.IsNotNullOrWhiteSpace Then
                Using frm = New frmOrderReports(CoNum, result.Paths.Single, shipPrinterName, reportName, 11)
                    frm.ShowDialog()
                End Using
            Else
                If Not result.Cancalled AndAlso XtraMessageBox.Show("Do you want to open this file ?", "Export", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                    result.Paths.ForEach(Function(r)
                                             'Process.Start(r)
                                             Dim psi As New System.Diagnostics.ProcessStartInfo()
                                             psi.FileName = r
                                             psi.UseShellExecute = True
                                             System.Diagnostics.Process.Start(psi)

                                         End Function)
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error processing Manual Check", ex)
        Finally
            SplashScreenManager.CloseOverlayForm(handle)
        End Try
    End Sub

    Private Sub VoidSelectedChecks()
        Dim rec As CheckMaster = gvCheckMaster.GetFocusedRow
        If rec IsNot Nothing Then
            Using frm = New frmVoidCheck(CoNum, rec)
                frm.ShowDialog()
            End Using
        End If
    End Sub

    Public Class Taxes
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Category As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Description As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Taxable As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Capped As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Amount As Decimal
    End Class

    Private Class DateRange
        Sub New(_from As DateTime, _to As DateTime)
            FromDate = _from
            ToDate = _to
        End Sub
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property FromDate As DateTime
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property ToDate As DateTime
    End Class

    Public Class CheckMaster
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Empnum As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Prnum As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CheckDate As DateTime
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CheckType As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Hours As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Gross As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Deds As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Taxes As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property DirDeposit As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Net As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CheckAmount As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CheckNumber As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CheckCounter As Decimal
    End Class

    Public Class PayrollDeductionsAndTaxes
        Property DetailType As String
        Property Caption As String
        Property Details As IList
    End Class

    Private Sub gvPayrollDeductionsAndTaxes_MasterRowEmpty(sender As Object, e As DevExpress.XtraGrid.Views.Grid.MasterRowEmptyEventArgs) Handles gvPayrollDeductionsAndTaxes.MasterRowEmpty, gvPayHistory1.MasterRowEmpty
        e.IsEmpty = False
    End Sub

    Private Sub gvPayrollDeductionsAndTaxes_MasterRowGetRelationCount(sender As Object, e As DevExpress.XtraGrid.Views.Grid.MasterRowGetRelationCountEventArgs) Handles gvPayrollDeductionsAndTaxes.MasterRowGetRelationCount, gvPayHistory1.MasterRowGetRelationCount
        e.RelationCount = 1
    End Sub

    Private Sub gvPayrollDeductionsAndTaxes_MasterRowGetRelationName(sender As Object, e As DevExpress.XtraGrid.Views.Grid.MasterRowGetRelationNameEventArgs) Handles gvPayrollDeductionsAndTaxes.MasterRowGetRelationName, gvPayHistory1.MasterRowGetRelationName
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        Dim row As PayrollDeductionsAndTaxes = view.GetRow(e.RowHandle)
        If row IsNot Nothing Then
            e.RelationName = row.DetailType
        End If
    End Sub

    Private Sub gvPayrollDeductionsAndTaxes_MasterRowGetChildList(sender As Object, e As DevExpress.XtraGrid.Views.Grid.MasterRowGetChildListEventArgs) Handles gvPayrollDeductionsAndTaxes.MasterRowGetChildList, gvPayHistory1.MasterRowGetChildList
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        Dim row As PayrollDeductionsAndTaxes = view.GetRow(e.RowHandle)
        e.ChildList = row.Details
    End Sub

    Private Sub gvPayrollDeductionsAndTaxes_CustomDrawCell(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs) Handles gvPayrollDeductionsAndTaxes.CustomDrawCell, gvPayHistory1.CustomDrawCell
        'e.Handled = True
    End Sub

    Private Sub gvDeductions_CustomDrawFooter(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectCustomDrawEventArgs) Handles gvDeductions.CustomDrawFooter, gvERTaxes.CustomDrawFooter, gvEETaxes.CustomDrawFooter, gvPayHistory.CustomDrawFooter
        Dim brush As New SolidBrush(Color.White)
        e.Graphics.FillRectangle(brush, e.Bounds)
        e.Handled = True
    End Sub

    Private Sub gvDeductions_CustomDrawFooterCell(sender As Object, e As DevExpress.XtraGrid.Views.Grid.FooterCellCustomDrawEventArgs) Handles gvDeductions.CustomDrawFooterCell, gvERTaxes.CustomDrawFooterCell, gvEETaxes.CustomDrawFooterCell, gvPayHistory.CustomDrawFooterCell
        If e.Info.DisplayText = String.Empty Then Exit Sub
        e.Appearance.DrawString(e.Cache, e.Info.DisplayText, e.Bounds)
        'e.Graphics.DrawLine()
        e.Handled = True

        Using vPen As New Pen(Color.Navy)
            'e.Graphics.DrawLine(vPen, New Point(e.Bounds.Right - 1, e.Bounds.Y), New Point(e.Bounds.Right - 1, e.Bounds.Bottom))
        End Using
        Using hPen As New Pen(Color.Navy)
            e.Graphics.DrawLine(hPen, e.Bounds.Location, New Point(e.Bounds.Right, e.Bounds.Y))
            'e.Graphics.DrawLine(hPen, New Point(e.Bounds.X, e.Bounds.Bottom - 1), New Point(e.Bounds.Right, e.Bounds.Bottom - 1))
        End Using
    End Sub

    Private Sub gvDeductions_CustomDrawColumnHeader(sender As Object, e As DevExpress.XtraGrid.Views.Grid.ColumnHeaderCustomDrawEventArgs) Handles gvERTaxes.CustomDrawColumnHeader, gvEETaxes.CustomDrawColumnHeader, gvDeductions.CustomDrawColumnHeader, gvPayHistory.CustomDrawColumnHeader
        e.Cache.FillRectangle(Color.White, e.Bounds)
        e.Appearance.DrawString(e.Cache, e.Info.Caption, e.Info.CaptionRect)
        Using vPen As New Pen(Color.Navy)
            'e.Graphics.DrawLine(vPen, New Point(e.Bounds.Right - 1, e.Bounds.Y), New Point(e.Bounds.Right - 1, e.Bounds.Bottom))
        End Using
        Using hPen As New Pen(Color.Navy)
            'e.Graphics.DrawLine(hPen, e.Bounds.Location, New Point(e.Bounds.Right, e.Bounds.Y))
            e.Graphics.DrawLine(hPen, New Point(e.Bounds.X, e.Bounds.Bottom - 1), New Point(e.Bounds.Right, e.Bounds.Bottom - 1))
        End Using
        e.Handled = True
    End Sub

End Class