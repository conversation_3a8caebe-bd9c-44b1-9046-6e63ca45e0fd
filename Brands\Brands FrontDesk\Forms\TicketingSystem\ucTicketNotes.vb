﻿Imports System.ComponentModel
Imports Brands_FrontDesk
Imports Humanizer

Public Class ucTicketNotes

    Public Delegate Sub NotesCountChangedHandler(sender As Object, e As NotesCountChangedEventArgs)
    Public Event NotesCountChangedEvent As NotesCountChangedHandler

    Sub New()
        InitializeComponent()
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _TicketNum As Integer
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property notes As List(Of TicketNote)

    Public Function LoadNotes(ticketNum As Int32) As Integer
        _TicketNum = ticketNum
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            notes = ctxDB.TicketNotes.Where(Function(n) n.TicketNum = ticketNum).OrderBy(Function(n) n.AddDateTime).ToList
            ticketNotesBindingSource.DataSource = notes
            ticketNotesBindingSource.MoveLast()
            meNewNote.Enabled = True
            GridView1.BestFitColumns()
        End Using
        RaiseEvent NotesCountChangedEvent(Me, New NotesCountChangedEventArgs(notes.Count))
        Return notes.Count()
    End Function

    Private Sub meNewNote_EditValueChanged(sender As Object, e As EventArgs) Handles meNewNote.EditValueChanged
        btnSend.Enabled = meNewNote.Text.IsNotNullOrWhiteSpace
    End Sub

    Private Sub btnSend_Click(sender As Object, e As EventArgs) Handles btnSend.Click
        Try
            SaveNote(meNewNote.Text)
            meNewNote.Text = Nothing
        Catch ex As Exception
            DisplayErrorMessage("Error saving note", ex)
        End Try
    End Sub

    Public Function SaveNote(text As String) As TicketNote
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim newNote = New TicketNote With {.TicketNum = _TicketNum, .AddDateTime = Now, .AddUser = UserName, .Note = text}
            ctxDB.TicketNotes.InsertOnSubmit(newNote)
            If Not ctxDB.SaveChanges() Then
                DisplayErrorMessage("Unable to save ticket note due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return Nothing
            End If
            ticketNotesBindingSource.Insert(ticketNotesBindingSource.Count, newNote)
            ticketNotesBindingSource.MoveLast()
            GridView1.ViewCaption = $"Notes ({ticketNotesBindingSource.Count})"
            RaiseEvent NotesCountChangedEvent(Me, New NotesCountChangedEventArgs(ticketNotesBindingSource.Count))
            Return newNote
        End Using
    End Function

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As TicketNote = GridView1.GetRow(e.HitInfo.RowHandle)
            If row IsNot Nothing Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Copy Note", Sub() Clipboard.SetText(row.Note), My.Resources.copy_16x16))
            End If
        End If
    End Sub

    Private Sub GridView1_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles GridView1.CustomUnboundColumnData
        If e.IsGetData AndAlso e.Column Is colDateDesc Then
            e.Value = DirectCast(e.Row, TicketNote).AddDateTime.Humanize(False)
        End If
    End Sub

    Friend Sub FocusNewNote()
        meNewNote.Focus()
    End Sub

    Public Class NotesCountChangedEventArgs
        Inherits EventArgs
        Public Sub New(count As Integer)
            Me.Count = count
        End Sub

        Public ReadOnly Property Count As Integer
    End Class
End Class
