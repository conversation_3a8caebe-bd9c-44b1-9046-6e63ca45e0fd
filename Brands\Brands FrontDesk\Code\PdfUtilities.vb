﻿Imports DevExpress.Pdf
Imports DevExpress.Spreadsheet

Public Module PdfUtilities

    Public Function CombinePdfsNew(newFileName As String, files() As String, Optional password As String = Nothing) As String
        Using pdfDocumentProcessor As New DevExpress.Pdf.PdfDocumentProcessor()
            pdfDocumentProcessor.LoadDocument(files(0))
            For index = 1 To files.Count - 1
                pdfDocumentProcessor.AppendDocument(files(index))
            Next
            If password.IsNotNullOrWhiteSpace Then
                pdfDocumentProcessor.SaveDocument(newFileName, GetPdfEncryption(password))
            Else
                pdfDocumentProcessor.SaveDocument(newFileName)
            End If
        End Using
        Return newFileName
    End Function

    Public Function CombinePdfs(newFileName As String, password As String, ParamArray files() As String) As String
        Dim newPdf = New PdfDocumentProcessor()
        newPdf.CreateEmptyDocument()
        For Each pdfFile As String In files
            Dim pdf = New PdfDocumentProcessor()
            pdf.LoadDocument(pdfFile)
            CopyPages(pdf, newPdf)
        Next

        If password.IsNotNullOrWhiteSpace() Then
            newPdf.SaveDocument(newFileName, GetPdfEncryption(password))
        Else
            newPdf.SaveDocument(newFileName)
        End If
        'newFileName = Path.Combine(Path.GetTempPath(), newFileName)
        Return newFileName
    End Function

    Private Sub CopyPages(fromPdf As PdfDocumentProcessor, toPdf As PdfDocumentProcessor)
        Dim origPageCount = toPdf.Document.Pages.Count
        For index = 0 To fromPdf.Document.Pages.Count - 1
            Dim sourcePage = fromPdf.Document.Pages(index)
            toPdf.Document.Pages.Add(sourcePage)
        Next

        Dim count = fromPdf.Document.Bookmarks.Count - 1
        For index = 0 To count
            Dim bookmark = fromPdf.Document.Bookmarks(index)
            'fromPdf.Document.Bookmarks.Remove(bookmark)
            Dim destination = If(bookmark.Destination Is Nothing, Nothing, toPdf.CreateDestination((bookmark.Destination.PageIndex + origPageCount) + 1))
            bookmark.Destination = Nothing
            toPdf.Document.Bookmarks.Add(bookmark)
            bookmark.Destination = destination
        Next
    End Sub

    Public Function SetPassword(filePath As String, destFilePath As String, password As String) As String
        Using pdf = New DevExpress.Pdf.PdfDocumentProcessor()
            pdf.LoadDocument(filePath)
            pdf.SaveDocument(destFilePath, GetPdfEncryption(password))
        End Using
        Return destFilePath
    End Function


    Public Function GetPdfEncryption(password As String) As DevExpress.Pdf.PdfSaveOptions
        Dim saveOptions = New DevExpress.Pdf.PdfSaveOptions
        'Dim encryptionOptions As DevExpress.Pdf.PdfEncryptionOptions = New DevExpress.Pdf.PdfEncryptionOptions() With {
        '    .PrintingPermissions = DevExpress.Pdf.PdfDocumentPrintingPermissions.Allowed,
        '    .DataExtractionPermissions = PdfDocumentDataExtractionPermissions.Allowed,
        '    .ModificationPermissions = PdfDocumentModificationPermissions.Allowed,
        '    .InteractivityPermissions = DevExpress.Pdf.PdfDocumentInteractivityPermissions.Allowed,
        '    .OwnerPasswordString = "az@-4SVcv##",
        '    .UserPasswordString = password
        '}
        Dim encryptionOptions As DevExpress.Pdf.PdfEncryptionOptions = New DevExpress.Pdf.PdfEncryptionOptions() With {.UserPasswordString = password}
        saveOptions.EncryptionOptions = encryptionOptions
        Return saveOptions
    End Function


    Public Sub StreamToPdf(stream As System.IO.Stream, destFilePath As String, password As String)
        Dim newPdf = New DevExpress.Pdf.PdfDocumentProcessor()
        newPdf.LoadDocument(stream)
        If password.IsNotNullOrWhiteSpace Then
            newPdf.SaveDocument(destFilePath, GetPdfEncryption(password))
        Else
            newPdf.SaveDocument(destFilePath)
        End If
    End Sub

    Public Sub BinaryToPdf(binary As Byte(), destFilePath As String, password As String)
        Using newPdf = New DevExpress.Pdf.PdfDocumentProcessor
            newPdf.LoadDocument(New System.IO.MemoryStream(binary))
            newPdf.SaveDocument(destFilePath)
        End Using
    End Sub

    Public Function SetPdfFields(path As String, fieldsMap As String, row As System.Data.DataRow, savePath As String) As String
        Dim pdf = New DevExpress.Pdf.PdfDocumentProcessor
        pdf.LoadDocument(path)
        Dim interactiveFields = pdf.GetFormData()
        For Each kv In modReports.StringToKeyValues(fieldsMap)
            interactiveFields(kv.Name).Value = modReports.StringReplaceByColName(kv.Value, row)
        Next
        pdf.ApplyFormData(interactiveFields)
        pdf.SaveDocument(savePath)
        pdf.CloseDocument()
        Return savePath
    End Function

    Public Function CountPages(path As String) As Integer
        Dim pdf = New DevExpress.Pdf.PdfDocumentProcessor
        pdf.LoadDocument(path)
        Dim result = pdf.Document.Pages.Count
        pdf.CloseDocument()
        Return result
    End Function

    Public Sub CopyPages(fromPdf As PdfDocumentProcessor, toPdf As PdfDocumentProcessor, rptName As String)
        For index = 0 To fromPdf.Document.Pages.Count - 1
            toPdf.Document.Pages.Add(fromPdf.Document.Pages(index))
        Next

        If rptName.IsNotNullOrWhiteSpace Then
            Dim outline = New PdfBookmark With {.Title = rptName, .Destination = toPdf.CreateDestination((toPdf.Document.Pages.Count - fromPdf.Document.Pages.Count) + 1), .IsInitiallyClosed = True}

            toPdf.Document.Bookmarks.Add(outline)
            If fromPdf.Document.Pages.Count > 1 Then
                For pageIndex = 1 To fromPdf.Document.Pages.Count - 1
                    outline.Children.Add(New PdfBookmark With {.Title = "Page {0}".FormatWith(pageIndex + 1), .Destination = toPdf.CreateDestination((toPdf.Document.Pages.Count - fromPdf.Document.Pages.Count) + pageIndex + 1), .IsInitiallyClosed = True})
                Next
            End If
        End If
    End Sub

    Public Sub SetXlsxPassword(fileName As String, password As String)
        Dim excelFile = New Workbook
        excelFile.LoadDocument(fileName)
        excelFile.DocumentSettings.Encryption.Type = EncryptionType.Strong
        excelFile.DocumentSettings.Encryption.Password = password
        excelFile.SaveDocument(fileName)
        'Workbook.SaveDocument("EncryptedDocument.xlsx", DocumentFormat.Xlsx)
    End Sub
End Module
