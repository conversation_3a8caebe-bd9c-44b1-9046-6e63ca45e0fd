﻿Public Class frmFaxesAdvancedSearch
    Public _FaxAdvancedSearch As frmFaxes.FaxAdvancedSearch

    Public Sub New(_FaxAdvancedSearch As frmFaxes.FaxAdvancedSearch)
        Me._FaxAdvancedSearch = _FaxAdvancedSearch
        InitializeComponent()
    End Sub

    Private Sub frmFaxesAdvancedSearch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim db = New dbEPDataDataContext(GetConnectionString)
        BindingSource1.DataSource = db.view_CompanySumarries.ToList()
        If _FaxAdvancedSearch Is Nothing Then
            _FaxAdvancedSearch = New frmFaxes.FaxAdvancedSearch()
        End If
        BindingSource2.DataSource = _FaxAdvancedSearch
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        DialogResult = DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub
End Class