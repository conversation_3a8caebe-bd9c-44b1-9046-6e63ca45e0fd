﻿'Imports CrystalDecisions.Shared
'Imports CrystalDecisions.CrystalReports.Engine
'Imports System.Linq
'Imports System.Collections.Generic
Imports System.ComponentModel
Imports DevExpress.XtraEditors.Repository
'Imports System.Data.SqlClient
Imports DXCustomControls
Imports DXCustomControls.CustomEditor

Public Class frmReportsSavedParameters

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property SubmitToQueue As Boolean

    Private Property logger As Serilog.ILogger

    ReadOnly _defaultParamValues As List(Of KeyValuePair)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ReportParameters As List(Of FrontDeskReportParameter)
    Dim db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property UseCustomEditors As Boolean = True
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property ReportProcessorQueue As ReportProcessorQueue

    Public Sub New(reportEmailTemplateId As Integer, conum As Decimal, lastPrNum As Decimal?, lastCheckDate As DateTime?, Optional defaultParamValues As List(Of KeyValuePair) = Nothing)
        InitializeComponent()
        seZendeskTicketId.AddClearButton
        _defaultParamValues = defaultParamValues
        logger = modGlobals.Logger.ForContext(Of frmReportsSavedParameters)

        Using dataContext As New Brands.DAL.EPDATAContext(GetConnectionString)
            Dim report = dataContext.ReportEmailTemplates.Single(Function(r) r.ID = reportEmailTemplateId)
            Dim parameters = dataContext.ReportEmailTemplateParameters.Where(Function(r) r.ReportId = reportEmailTemplateId).OrderBy(Function(p) p.Sort).ToList()

            If Not parameters.Any Then
                Dim processor = New ReportProcessor(812, db.ReportEmailTeplates.Single(Function(r) r.ID = reportEmailTemplateId), FileType.Pdf)
                parameters = processor.GetReportParameters()
                lciMissingParamsWarning.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            End If

            Dim list As List(Of FrontDeskReportParameter) = New List(Of FrontDeskReportParameter)

            For Each item In parameters
                Dim parameter = New FrontDeskReportParameter With {
                    .ParameterId = item.Id,
                    .Name = item.Name,
                    .Description = item.Description,
                    .DataType = item.DataType,
                    .ReportEmailTemplateParameters = item}
                If item.EditorType = "conum" Then
                    parameter.Value = conum
                ElseIf parameter.ReportEmailTemplateParameters.DefaultValue.IsNotNullOrWhiteSpace() Then
                    parameter.Value = parameter.ReportEmailTemplateParameters.DefaultValue
                End If
                list.Add(parameter)
            Next

            ReportParameters = list
        End Using

        If ReportParameters.Any(Function(p) p.Name = "df") AndAlso ReportParameters.Any(Function(p) p.Name = "dt") Then
            lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        ElseIf ReportParameters.Any(Function(p) p.Name = "From Date") AndAlso ReportParameters.Any(Function(p) p.Name = "To Date") Then
            lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        Else
            lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        End If

        SetDefaultParameterValues(lastPrNum, lastCheckDate)
    End Sub

    Private Sub frmReportsParameters_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        GridControl1.DataSource = ReportParameters
        lciZendeskTicketId.Visibility = If(ReportProcessorQueue IsNot Nothing, DevExpress.XtraLayout.Utils.LayoutVisibility.Always, DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
        seZendeskTicketId.EditValue = ReportProcessorQueue?.UpdateZendeskTicketId
    End Sub

    Private Sub SetDefaultParameterValues(lastPrNum As Decimal?, lastCheckDate As DateTime?)
        Try
            If lastPrNum.HasValue Then
                Dim param = ReportParameters.SingleOrDefault(Function(p) p.ReportEmailTemplateParameters.EditorType = "prnum")
                If param IsNot Nothing Then
                    param.Value = lastPrNum
                End If
            End If

            TrySetParameterValue("Year", DateTime.Now.Year)
            TrySetParameterValue("y", DateTime.Now.Year)
            TrySetParameterValue("Qtr", GetQuarter)
            TrySetParameterValue("Quarter", GetQuarter)
            If lastCheckDate.HasValue Then
                TrySetParameterValueByDataType("date", lastCheckDate.Value.ToString)
            End If
            If _defaultParamValues IsNot Nothing Then
                For Each p In _defaultParamValues
                    TrySetParameterValue(p.Name, p.Value)
                Next
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub TrySetParameterValue(name As String, value As String)
        Dim param = ReportParameters.SingleOrDefault(Function(p) p.Name.ToLower = name.ToLower)
        If param IsNot Nothing Then
            param.Value = value
        End If
    End Sub

    Private Sub TrySetParameterValueByDataType(dataType As String, value As String)
        Dim param = ReportParameters.SingleOrDefault(Function(p) p.DataType.ToLower = dataType.ToLower)
        If param IsNot Nothing Then
            param.Value = value
        End If
    End Sub

    Public Function GetReportParameters() As List(Of FrontDeskReportParameter)
        Return ReportParameters
    End Function

    Private Sub GridView1_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEditForEditing
        If e.Column Is colValue Then
            Dim row = CType(GridView1.GetRow(e.RowHandle), FrontDeskReportParameter)
            If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
            Dim rep = GetRepositoryItem(row)
            If rep IsNot Nothing Then
                e.RepositoryItem = rep
            End If
        End If
    End Sub

    Private Sub GridView1_CustomRowCellEdit(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEdit
        If e.Column Is colValue Then
            Dim row = CType(GridView1.GetRow(e.RowHandle), FrontDeskReportParameter)
            If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
            Dim rep = GetRepositoryItem(row)
            If rep IsNot Nothing Then
                e.RepositoryItem = rep
            End If
        End If
    End Sub



    Private Property _RepositoryItemsCache As Dictionary(Of String, RepositoryItem) = New Dictionary(Of String, RepositoryItem)
    Private Function GetRepositoryItem(parameter As FrontDeskReportParameter) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing
        Dim editorType = parameter.ReportEmailTemplateParameters.EditorType
        Dim parameterName = parameter.Name
        Try

            If Not _RepositoryItemsCache.TryGetValue(parameterName, repItem) Then

                If parameter Is Nothing Then
                    repItem = GetRepositoryItemByParamType(parameterName)
                End If

                'use custom dropdown if param name is CoNum or EmpNum
                If editorType = "conum" Then
                    Dim r = New RepositoryItemBaseSpinEdit With {.ReadOnly = True}
                    GridControl1.RepositoryItems.Add(r)
                    _RepositoryItemsCache.Add(parameterName, r)
                    Return r
                ElseIf editorType = "empnum" OrElse editorType = "empnum_to" Then
                    If GetSaveReportParameterConum().HasValue Then
                        Dim repo As RepositoryItemCustomEdit = GetRepositoryItemForEmpNum(parameterName, GetSaveReportParameterConum)
                        Return repo
                    End If
                ElseIf editorType = "prnum" Then
                    Dim Year As Decimal? = Nothing
                    If GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForPrNum(parameterName, GetSaveReportParameterConum, Year)
                        Return repo
                    End If
                ElseIf parameterName = "prnumsp" Then
                    If GetSaveReportParameterConum.HasValue Then
                        Dim CoNum As Decimal = GetSaveReportParameterConum.Value
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForPrNumSp(parameterName, CoNum, Nothing)
                        Return repo
                    End If
                ElseIf parameterName = "s" Then
                    Dim r = New RepositoryItemLookUpEdit With {.DisplayMember = "Name", .ValueMember = "Value"}
                    Dim list = New List(Of KeyValuePair)
                    list.Add(New KeyValuePair With {.Name = "Active Only", .Value = "-1"})
                    list.Add(New KeyValuePair With {.Name = "Terminated Only", .Value = "1"})
                    list.Add(New KeyValuePair With {.Name = "All Employees", .Value = "2"})
                    r.DataSource = list
                    r.NullText = String.Empty
                    GridControl1.RepositoryItems.Add(r)
                    _RepositoryItemsCache.Add(parameterName, r)
                    Return r
                ElseIf parameterName = "divnum" OrElse parameterName = "dv" Then
                    If GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForDivNum(parameterName, GetSaveReportParameterConum)
                        Return repo
                    End If
                ElseIf parameterName = "j" OrElse parameterName = "jobnum" Then
                    If GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForJobNum(parameterName, GetSaveReportParameterConum)
                        Return repo
                    End If
                ElseIf parameterName = "dp" OrElse parameterName = "depnum" Then
                    If GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForDepNum(parameterName, GetSaveReportParameterConum)
                        Return repo
                    End If
                    'Else
                    '    Dim paramMap = db.ReportEPParamMaps.SingleOrDefault(Function(p) p.Param = parameterName)
                    '    If paramMap IsNot Nothing AndAlso paramMap.AvailibleValues.IsNotNullOrWhiteSpace Then
                    '        Dim r = New RepositoryItemComboBox
                    '        r.Items.AddRange(paramMap.AvailibleValues.Split(","))
                    '        r.NullText = String.Empty
                    '        GridControl1.RepositoryItems.Add(r)
                    '        _RepositoryItemsCache.Add(parameterName, r)
                    '        Return r
                    '    End If
                End If

                If parameter IsNot Nothing Then
                    repItem = GetRepositoryItemByParamType(parameter)
                End If

            End If
        Catch ex As Exception
            DisplayErrorMessage($"Error getting editor for {parameterName}", ex)
        End Try
        Return repItem
    End Function

    Private Function GetSaveReportParameterConum() As Decimal?
        Dim pp = ReportParameters.SingleOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
        Dim c As Decimal
        If pp IsNot Nothing AndAlso Decimal.TryParse(nz(pp.Value, ""), c) Then
            Return c
        Else
            Return Nothing
        End If
    End Function

    Private Function GetRepositoryItemForEmpNum(ByVal name As String, ByVal coNum As Decimal) As DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim list As List(Of EMPLOYEE) = (New List(Of EMPLOYEE)(New EMPLOYEE() {New EMPLOYEE With {.EMPNUM = -1, .L_NAME = "", .F_NAME = "(ALL Employees)"}}))
        Dim AllEmployees = (From A In list.Union(db.EMPLOYEEs.Where(Function(c) c.CONUM = coNum).ToList())
                            Select New With {.EmpNum = A.EMPNUM, .LastName = A.L_NAME, .FistName = A.F_NAME, .TermDate = A.TERM_DATE, .FullName = "{0} - {1} {2}".FormatWith(A.EMPNUM, A.F_NAME, A.L_NAME)}).ToList
        Dim repo = New CustomEditor.RepositoryItemCustomEdit
        'AllEmployees.Add(CType(New With {.EmpNum = -1, .LastName = "", .FistName = "", .TermDate = "", .FullName = ""}, Object))
        repo.DataSource = AllEmployees
        repo.DisplayMember = "FullName"
        repo.ValueMember = "EmpNum"
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForCoNum(ByVal name As String) As RepositoryItem
        Try
            Dim companyListWithActiveStatus = (From A In db.COMPANies Select New With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                                                           .CO_FAX = A.CO_FAX, .FED_ID = A.FED_ID, .CO_EMAIL = A.CO_EMAIL, .CoNumAndName = "{0} - {1}".FormatWith(A.CONUM, A.CO_NAME), .Status = A.CO_STATUS}
                                                       ).ToList
            Dim compStatus = (From c In db.CO_UDFs Where c.UDF_DESCR.Equals("INACTIVE STATUS")).ToDictionary(Function(key) key.CONUM)

            For Each item In companyListWithActiveStatus.Where(Function(c) c.Status = "Active Status")
                Dim value As CO_UDF = Nothing
                If compStatus.TryGetValue(item.CONUM, value) Then
                    item.Status = value.UDF_STRING
                End If
            Next

            Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
            repo.DataSource = companyListWithActiveStatus
            repo.DisplayMember = "CoNumAndName"
            repo.ValueMember = "CONUM"
            AddHandler repo.EditValueChanged,
                Sub(s As Object, e As EventArgs)
                    Me.GridView1.PostEditor()
                    If _RepositoryItemsCache.ContainsKey("empnum") Then
                        _RepositoryItemsCache.Remove("empnum")
                        ClearParametersValue("empnum")
                        GridView1.RefreshData()
                    End If

                    If _RepositoryItemsCache.ContainsKey("prnum") Then
                        _RepositoryItemsCache.Remove("prnum")
                        ClearParametersValue("prnum")
                        GridView1.RefreshData()
                    End If
                    If _RepositoryItemsCache.ContainsKey("pr") Then
                        _RepositoryItemsCache.Remove("pr")
                        ClearParametersValue("pr")
                        GridView1.RefreshData()
                    End If
                End Sub
            GridControl1.RepositoryItems.Add(repo)
            _RepositoryItemsCache.Add(name, repo)
            Return repo
        Catch ex As Exception
            DisplayErrorMessage("Error getting repository item", ex)
        End Try
        Return Nothing
    End Function

    'added by solomon
    Private Function GetRepositoryItemForPrNumSp(ByVal name As String, ByVal CoNum As Decimal, ByVal Year As Decimal?) As RepositoryItemLookUpEdit
        Dim lstPrl As New List(Of PAYROLL)(New PAYROLL() {New PAYROLL() With {
    .PRNUM = -1,
    .CHECK_DATE = Nothing,
    .PR_DESCR = "(ALL)"
}})


        Dim list = (From A In lstPrl.Union(db.PAYROLLs.Where(Function(c) c.CONUM = CoNum AndAlso (Not Year.HasValue OrElse c.CHECK_DATE.GetValueOrDefault.Year = Year.Value))) Select New With {
        .PrNum = A.PRNUM,
    .CheckDate = A.CHECK_DATE,
    .PrDesc = A.PR_DESCR,
    .Value = [String].Format("{0}  {1} {2}", A.PRNUM, (If(A.CHECK_DATE.HasValue, A.CHECK_DATE.Value.ToShortDateString(), "")), A.PR_DESCR), .AllEmp = If(A.PRNUM = -1, -1, 0)
}).ToList().OrderBy(Function(s) s.AllEmp).ThenByDescending(Function(s) s.PrNum)

        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "PrNum", .ValueMember = "PrNum"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForPrNum(ByVal name As String, ByVal CoNum As Decimal, ByVal Year As Decimal?) As RepositoryItemLookUpEdit
        Dim CoPayrolls = (From A In db.PAYROLLs
                          Where A.CONUM = CoNum AndAlso (Not Year.HasValue OrElse A.CHECK_DATE.Value.Year = Year)
                          Select New With {.PrNum = A.PRNUM, .CheckDate = A.CHECK_DATE, .PrDescr = A.PR_DESCR}).OrderByDescending(Function(o) o.PrNum).ToList
        Dim list = (From A In CoPayrolls Select A.PrNum, A.CheckDate, Value = String.Format("{0}  {1} {2}", A.PrNum, If(A.CheckDate.HasValue, A.CheckDate.Value.ToShortDateString, ""), A.PrDescr)).ToList

        'Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "PrNum", .ValueMember = "PrNum"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemByParamType(ByVal name As String) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing

        If name.Contains("date") Then
            Dim repo = New RepositoryItemDateEdit
            GridControl1.RepositoryItems.Add(repo)
            repItem = repo
            _RepositoryItemsCache.Add(name, repo)
        End If

        Return repItem
    End Function

    Private Function GetRepositoryItemByParamType(param As FrontDeskReportParameter) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing
        Dim parameterName = param.Name
        Select Case param.ReportEmailTemplateParameters.DataType
            Case "int"
                Dim repo = New RepositoryItemSpinEdit
                GridControl1.RepositoryItems.Add(repo)
                repItem = repo
                _RepositoryItemsCache.Add(parameterName, repo)
            Case "string"
                If param.ReportEmailTemplateParameters.EditorType = "dropdown" Then
                    Dim repo = New RepositoryItemLookUpEdit
                    repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Key", "Key") With {.Visible = False})
                    repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", "Value"))
                    repo.DataSource = Newtonsoft.Json.JsonConvert.DeserializeObject(Of List(Of ReportParameterItems))(param.ReportEmailTemplateParameters.DropDownOptionsJson)
                    GridControl1.RepositoryItems.Add(repo)
                    repItem = repo
                    _RepositoryItemsCache.Add(parameterName, repo)
                Else
                    Dim repo = New RepositoryItemTextEdit
                    GridControl1.RepositoryItems.Add(repo)
                    repItem = repo
                    _RepositoryItemsCache.Add(parameterName, repo)
                End If

            Case "date"
                Dim repo = New RepositoryItemDateEdit
                GridControl1.RepositoryItems.Add(repo)
                repItem = repo
                _RepositoryItemsCache.Add(parameterName, repo)

                'Case ParameterValueKind.DateTimeParameter
                '    Dim repo = New RepositoryItemDateEdit With {.EditMask = "yyyy/MM/dd HH:mm"}
                '    repo.Mask.UseMaskAsDisplayFormat = True
                '    GridControl1.RepositoryItems.Add(repo)
                '    repItem = repo
                '    _RepositoryItemsCache.Add(parameterName, repo)
        End Select
        Return repItem
    End Function

    Private Function GetRepositoryItemForDivNum(ByVal name As String, ByVal CoNum As Decimal) As RepositoryItem
        Dim allDivs = (New List(Of DIVISION)(New DIVISION() {New DIVISION With {.DDIVNUM = -1, .DDIVNAME = "All Divisions"}}))
        Dim CoPayrolls = allDivs.Union((From A In db.DIVISIONs Where A.CONUM = CoNum).OrderByDescending(Function(o) o.DDIVNUM))
        Dim list = (From A In CoPayrolls Select DivNum = A.DDIVNUM, Value = A.DivNumName).ToList
        'Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "Value", .ValueMember = "DivNum"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForJobNum(ByVal name As String, ByVal CoNum As Decimal) As RepositoryItem
        Dim allJobs = (New List(Of CO_JOB)(New CO_JOB() {New CO_JOB With {.job_id = -1, .job_descr = "All Jobs"}}))
        Dim CoJobs = allJobs.Union((From A In db.CO_JOBS Where A.conum = CoNum).OrderByDescending(Function(o) o.job_id))
        Dim list = (From A In CoJobs Select JobId = A.job_id, Value = "{0}: {1}".FormatWith(A.job_id, A.job_descr)).ToList
        'Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "Value", .ValueMember = "JobId"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForDepNum(ByVal name As String, ByVal CoNum As Decimal) As RepositoryItem
        Dim list As List(Of DEPARTMENT) = (New List(Of DEPARTMENT)(New DEPARTMENT() {New DEPARTMENT With {.DEPTNUM = -1, .DEPT_DESC = "(ALL Departments)"}}))
        Dim AllDepartments = (From A In list.Union(db.DEPARTMENTs.Where(Function(c) c.CONUM = CoNum).ToList())
                              Select New With {.DivNum = A.DIVNUMD, .DepNum = A.DEPTNUM, .DeptDesc = A.DEPT_DESC, .Value = "{0}: {1}".FormatWith(A.DEPTNUM, A.DEPT_DESC)}).ToList
        Dim repo = New CustomEditor.RepositoryItemCustomEdit
        'AllEmployees.Add(CType(New With {.EmpNum = -1, .LastName = "", .FistName = "", .TermDate = "", .FullName = ""}, Object))
        repo.DataSource = AllDepartments
        repo.DisplayMember = "Value"
        repo.ValueMember = "DepNum"
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Sub frmReportsParameters_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.Control AndAlso e.KeyCode = Keys.Enter Then
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Sub cbeDateRange_SelectedValueChanged(sender As Object, e As EventArgs) Handles cbeDateRange.SelectedValueChanged
        Dim df As DateTime
        Dim dt As DateTime
        DateFunctions.GetDateRange(cbeDateRange.Text, df, dt)

        Dim dfp = ReportParameters.SingleOrDefault(Function(p) p.Name = "df")
        Dim dtp = ReportParameters.SingleOrDefault(Function(p) p.Name = "dt")
        If dfp Is Nothing AndAlso dtp Is Nothing Then
            dfp = ReportParameters.SingleOrDefault(Function(p) p.Name = "From Date")
            dtp = ReportParameters.SingleOrDefault(Function(p) p.Name = "To Date")
        End If
        dfp.Value = df
        dtp.Value = dt
        GridControl1.RefreshDataSource()
        Exit Sub

    End Sub

    Private Sub ClearParametersValue(name As String)

    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnSubmitToQueue.Click
        SubmitToQueue = True
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub

    Private Sub btnAddReport_Click(sender As Object, e As EventArgs) Handles btnAddReport.Click
        SubmitToQueue = False
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub

    Private Sub seZendeskTicketId_EditValueChanged(sender As Object, e As EventArgs) Handles seZendeskTicketId.EditValueChanged
        _ReportProcessorQueue.UpdateZendeskTicketId = seZendeskTicketId.Value
    End Sub
End Class

Public Class ReportParameterItems
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Key As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Value As String
End Class