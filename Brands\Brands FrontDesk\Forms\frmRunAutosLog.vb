﻿Public Class frmRunAutosLog

    Private Sub frmRunAutos_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        For Each PrinterName In Printing.PrinterSettings.InstalledPrinters
            Me.cbPrinters.Properties.Items.Add(PrinterName)
        Next
        Dim defaultPrinting = New Printing.PrinterSettings
        Me.cbPrinters.EditValue = defaultPrinting.PrinterName

        Me.DateEdit1.Properties.MaxValue = Today
        Me.DateEdit1.DateTime = Today
    End Sub

    Sub LoadData()
        Dim SelectedDate As Date = Me.DateEdit1.DateTime
        If SelectedDate = Date.MinValue Then Exit Sub
        Using db As New dbEPDataDataContext(GetConnectionString)
            Dim Records = (From A In db.AutoPayrollsLogs Where A.ProcessDate >= SelectedDate AndAlso A.ProcessDate < SelectedDate.AddDays(1) Order By A.CoCode).ToList
            Me.BindingSource1.DataSource = Records
        End Using
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim SelectedPrinter = Me.cbPrinters.EditValue
        If SelectedPrinter Is Nothing Then
            DisplayMessageBox("No Printer Selected")
            Exit Sub
        Else
            Me.printGridLink.Print(SelectedPrinter)
        End If
    End Sub

    Private Sub btnNextDate_Click(sender As Object, e As EventArgs) Handles btnNextDate.Click
        Me.DateEdit1.DateTime = Me.DateEdit1.DateTime.AddDays(1)
    End Sub

    Private Sub btnPreviousDate_Click(sender As Object, e As EventArgs) Handles btnPreviousDate.Click
        Me.DateEdit1.DateTime = Me.DateEdit1.DateTime.AddDays(-1)
    End Sub

    Private Sub DateEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles DateEdit1.EditValueChanged
        LoadData()
    End Sub

End Class