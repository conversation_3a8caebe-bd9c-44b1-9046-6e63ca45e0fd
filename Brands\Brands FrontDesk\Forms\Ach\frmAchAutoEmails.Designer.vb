﻿Namespace Ach
    <Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
    Partial Class frmAchAutoEmails
        Inherits DevExpress.XtraEditors.XtraForm

        'Form overrides dispose to clean up the component list.
        <System.Diagnostics.DebuggerNonUserCode()>
        Protected Overrides Sub Dispose(ByVal disposing As Boolean)
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
            MyBase.Dispose(disposing)
        End Sub

        'Required by the Windows Form Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Windows Form Designer
        'It can be modified using the Windows Form Designer.  
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()>
        Private Sub InitializeComponent()
            Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
            Me.btnSendEmail = New DevExpress.XtraEditors.SimpleButton()
            Me.btnRefresh = New DevExpress.XtraEditors.SimpleButton()
            Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
            Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colConum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colTemplate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCount = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.colShouldSetCoPass = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEntryDescType = New DevExpress.XtraGrid.Columns.GridColumn()
            CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.lcRoot.SuspendLayout()
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.SuspendLayout()
            '
            'lcRoot
            '
            Me.lcRoot.Controls.Add(Me.btnSendEmail)
            Me.lcRoot.Controls.Add(Me.btnRefresh)
            Me.lcRoot.Controls.Add(Me.GridControl1)
            Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
            Me.lcRoot.Location = New System.Drawing.Point(0, 0)
            Me.lcRoot.Name = "lcRoot"
            Me.lcRoot.Root = Me.LayoutControlGroup1
            Me.lcRoot.Size = New System.Drawing.Size(648, 380)
            Me.lcRoot.TabIndex = 0
            Me.lcRoot.Text = "LayoutControl1"
            '
            'btnSendEmail
            '
            Me.btnSendEmail.Enabled = False
            Me.btnSendEmail.Location = New System.Drawing.Point(12, 12)
            Me.btnSendEmail.Name = "btnSendEmail"
            Me.btnSendEmail.Size = New System.Drawing.Size(106, 22)
            Me.btnSendEmail.StyleController = Me.lcRoot
            Me.btnSendEmail.TabIndex = 6
            Me.btnSendEmail.Text = "Send (0) Emails"
            '
            'btnRefresh
            '
            Me.btnRefresh.Image = Global.Brands_FrontDesk.My.Resources.Resources.refresh_16x16
            Me.btnRefresh.Location = New System.Drawing.Point(534, 12)
            Me.btnRefresh.Name = "btnRefresh"
            Me.btnRefresh.Size = New System.Drawing.Size(102, 22)
            Me.btnRefresh.StyleController = Me.lcRoot
            Me.btnRefresh.TabIndex = 5
            Me.btnRefresh.Text = "Refresh"
            '
            'GridControl1
            '
            Me.GridControl1.Location = New System.Drawing.Point(12, 38)
            Me.GridControl1.MainView = Me.GridView1
            Me.GridControl1.Name = "GridControl1"
            Me.GridControl1.Size = New System.Drawing.Size(624, 330)
            Me.GridControl1.TabIndex = 4
            Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
            '
            'GridView1
            '
            Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colConum, Me.colTemplate, Me.colCount, Me.colShouldSetCoPass, Me.colEntryDescType})
            Me.GridView1.GridControl = Me.GridControl1
            Me.GridView1.Name = "GridView1"
            Me.GridView1.OptionsBehavior.Editable = False
            Me.GridView1.OptionsSelection.MultiSelect = True
            Me.GridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect
            Me.GridView1.OptionsView.ShowGroupPanel = False
            Me.GridView1.OptionsView.ShowIndicator = False
            '
            'colConum
            '
            Me.colConum.Caption = "Co#"
            Me.colConum.FieldName = "CoNum"
            Me.colConum.Name = "colConum"
            Me.colConum.Visible = True
            Me.colConum.VisibleIndex = 2
            '
            'colTemplate
            '
            Me.colTemplate.Caption = "Template"
            Me.colTemplate.FieldName = "EmailTemplate"
            Me.colTemplate.Name = "colTemplate"
            Me.colTemplate.Visible = True
            Me.colTemplate.VisibleIndex = 1
            '
            'colCount
            '
            Me.colCount.Caption = "Count"
            Me.colCount.FieldName = "EmailsCount"
            Me.colCount.Name = "colCount"
            Me.colCount.Visible = True
            Me.colCount.VisibleIndex = 5
            '
            'LayoutControlGroup1
            '
            Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
            Me.LayoutControlGroup1.GroupBordersVisible = False
            Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.EmptySpaceItem1, Me.LayoutControlItem3})
            Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
            Me.LayoutControlGroup1.Size = New System.Drawing.Size(648, 380)
            Me.LayoutControlGroup1.TextVisible = False
            '
            'LayoutControlItem1
            '
            Me.LayoutControlItem1.Control = Me.GridControl1
            Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 26)
            Me.LayoutControlItem1.Name = "LayoutControlItem1"
            Me.LayoutControlItem1.Size = New System.Drawing.Size(628, 334)
            Me.LayoutControlItem1.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem1.TextVisible = False
            '
            'LayoutControlItem2
            '
            Me.LayoutControlItem2.Control = Me.btnRefresh
            Me.LayoutControlItem2.Location = New System.Drawing.Point(522, 0)
            Me.LayoutControlItem2.Name = "LayoutControlItem2"
            Me.LayoutControlItem2.Size = New System.Drawing.Size(106, 26)
            Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem2.TextVisible = False
            '
            'EmptySpaceItem1
            '
            Me.EmptySpaceItem1.AllowHotTrack = False
            Me.EmptySpaceItem1.Location = New System.Drawing.Point(110, 0)
            Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
            Me.EmptySpaceItem1.Size = New System.Drawing.Size(412, 26)
            Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
            '
            'LayoutControlItem3
            '
            Me.LayoutControlItem3.Control = Me.btnSendEmail
            Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem3.Name = "LayoutControlItem3"
            Me.LayoutControlItem3.Size = New System.Drawing.Size(110, 26)
            Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem3.TextVisible = False
            '
            'colShouldSetCoPass
            '
            Me.colShouldSetCoPass.Caption = "Should Set Co Pass"
            Me.colShouldSetCoPass.FieldName = "shouldSetCoPass"
            Me.colShouldSetCoPass.Name = "colShouldSetCoPass"
            Me.colShouldSetCoPass.Visible = True
            Me.colShouldSetCoPass.VisibleIndex = 3
            '
            'colEntryDescType
            '
            Me.colEntryDescType.Caption = "Entry Desc Type"
            Me.colEntryDescType.FieldName = "EntryDescType"
            Me.colEntryDescType.Name = "colEntryDescType"
            Me.colEntryDescType.Visible = True
            Me.colEntryDescType.VisibleIndex = 4
            '
            'frmAchAutoEmails
            '
            Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(648, 380)
            Me.Controls.Add(Me.lcRoot)
            Me.Name = "frmAchAutoEmails"
            Me.ShowIcon = False
            Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
            Me.Text = "Ach Auto Emails"
            CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
            Me.lcRoot.ResumeLayout(False)
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
            Me.ResumeLayout(False)

        End Sub

        Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
        Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
        Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents btnRefresh As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents btnSendEmail As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents colConum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colTemplate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCount As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colShouldSetCoPass As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEntryDescType As DevExpress.XtraGrid.Columns.GridColumn
    End Class
End Namespace