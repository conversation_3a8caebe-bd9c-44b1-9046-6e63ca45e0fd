﻿Imports System.Collections.Concurrent
Imports System.Text
Imports RabbitMQ.Client
Imports RabbitMQ.Client.Events

Namespace RabbitRpcCrystal

    Public Class RpcClientCrystal
        Implements IDisposable

        Private ReadOnly connection As IConnection
        ' Fully qualify IModel to specify the RabbitMQ.Client version
        Private ReadOnly channel As RabbitMQ.Client.IModel
        Private ReadOnly replyQueueName As String
        Private ReadOnly consumer As EventingBasicConsumer
        Private ReadOnly respQueue As BlockingCollection(Of String) = New BlockingCollection(Of String)()
        Private ReadOnly props As IBasicProperties

        Private Sub New(connection As IConnection, channel As RabbitMQ.Client.IModel, replyQueueName As String, consumer As EventingBasicConsumer, props As IBasicProperties)
            Me.connection = connection
            Me.channel = channel
            Me.replyQueueName = replyQueueName
            Me.consumer = consumer
            Me.props = props

            AddHandler consumer.Received, AddressOf Consumer_Received
        End Sub

        Public Shared Function Create() As RpcClientCrystal
            Dim factory = New ConnectionFactory() With {
                .HostName = "appserver",
                .Port = 5672,
                .UserName = "WebUser",
                .Password = "TSvkJ5HC53ONFHmw"
            }
            '.DispatchConsumersAsync = True

            Dim connection As IConnection = factory.CreateConnection()

            ' Fully qualify IModel here as well
            Dim channel As RabbitMQ.Client.IModel = connection.CreateModel()

            Dim queueDeclareResult As QueueDeclareOk = channel.QueueDeclare(queue:="", exclusive:=True)
            Dim replyQueueName As String = queueDeclareResult.QueueName

            Dim consumer = New EventingBasicConsumer(channel)

            Dim props As IBasicProperties = channel.CreateBasicProperties()

            Dim corrId = Guid.NewGuid().ToString()
            props.CorrelationId = corrId
            props.Priority = 10
            props.ReplyTo = replyQueueName

            channel.BasicConsume(queue:=replyQueueName, autoAck:=True, consumer:=consumer)

            Return New RpcClientCrystal(connection, channel, replyQueueName, consumer, props)
        End Function

        Public Function [Call](ByVal message As String) As String
            Dim messageBytes = Encoding.UTF8.GetBytes(message)
            channel.BasicPublish(exchange:="", routingKey:="rpc_crystal_queue", basicProperties:=props, body:=messageBytes)
            Dim responseMessage As String = respQueue.Take()
            Return responseMessage
        End Function

        Private Sub Consumer_Received(ByVal model As Object, ByVal ea As BasicDeliverEventArgs)
            Dim response = Encoding.UTF8.GetString(ea.Body.ToArray())
            If ea.BasicProperties.CorrelationId = props.CorrelationId Then
                respQueue.Add(response)
            End If
        End Sub

        Public Sub Dispose() Implements IDisposable.Dispose
            channel?.Dispose()
            connection?.Dispose()
        End Sub

    End Class

End Namespace
