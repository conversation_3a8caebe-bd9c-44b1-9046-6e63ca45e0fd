﻿Public Class frmDDReversalStatusUpdate
    Private ReadOnly row As DD_Reversal_Log

    Public Sub New(row As DD_Reversal_Log)
        InitializeComponent()
        BindingSource1.DataSource = row
        Me.row = row
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        DialogResult = DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Private Sub frmDDReversalStatusUpdate_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim list = GetUdfValueSplitted("DD Reversal Fault")
        For Each item In list
            cbeFault.Properties.Items.Add(item)
        Next

        CheckEdit1.Enabled = GetUdfValueSplitted("DD Reversal No Charge Change Allowed").Contains(row.Reversal_Status)
    End Sub

    Private Sub meNotes_MouseDown(sender As Object, e As MouseEventArgs) Handles meNotes.MouseDown
        Dim frm = New frmEditNotes(meNotes.Text)
        If frm.ShowDialog = DialogResult.OK Then
            meNotes.Text = frm.GetNote()
        End If
    End Sub
End Class