﻿
Imports System.Reflection
Public Class ApplicationServices

    Public Shared Function AssemblyResolve(ByVal sender As Object, ByVal args As ResolveEventArgs) As Assembly
        Dim NewAssembly As Assembly = Nothing

        ' Get the DLL name from the argument. 
        Dim NameParts As String() = args.Name.Split(",")
        Dim DllName As String = NameParts(0)

        ' Load the assembly. 
        ' Assembly.Load() doesn't work here, as the previous failure to load the assembly 
        ' is cached by the CLR. LoadFrom() is not recommended. Use LoadFile() instead. 
        If DllName.Contains("resources") Then
            Return Nothing
        End If

        Dim path As String = ""

        Dim Override_AssemblyResolverPath As String, Override_AssemblyResolverPath_AutoFallbackIfNotFound As Boolean
        Dim CurrentExe = Assembly.GetEntryAssembly.Location
        Dim ThisExe = Assembly.GetExecutingAssembly.Location
        Dim config = Configuration.ConfigurationManager.OpenExeConfiguration(ThisExe)
        If CurrentExe <> ThisExe AndAlso Not {"System"}.Contains(DllName) AndAlso config.GetSectionGroup("userSettings") IsNot Nothing Then
            Dim userSettings As Configuration.ClientSettingsSection = config.GetSectionGroup("userSettings").Sections(0)
            Override_AssemblyResolverPath = userSettings.Settings.Get("Override_AssemblyResolverPath").Value.ValueXml.InnerText
            Override_AssemblyResolverPath_AutoFallbackIfNotFound = Boolean.Parse(userSettings.Settings.Get("Override_AssemblyResolverPath_AutoFallbackIfNotFound").Value.ValueXml.InnerText)
        Else
            Override_AssemblyResolverPath = My.Settings.Override_AssemblyResolverPath
            Override_AssemblyResolverPath_AutoFallbackIfNotFound = My.Settings.Override_AssemblyResolverPath_AutoFallbackIfNotFound
        End If
        Dim execupayPath = System.IO.Path.Combine(Override_AssemblyResolverPath, DllName) & ".dll"
        Dim localPath = System.IO.Path.Combine(ExecupayAssemblyResolver.Instance.LocalBaseDirectoryPath(), DllName) & ".dll"

        If Override_AssemblyResolverPath.IsNotNullOrWhiteSpace AndAlso System.IO.File.Exists(execupayPath) Then
            path = execupayPath
        End If

        If path.IsNullOrWhiteSpace AndAlso (Override_AssemblyResolverPath.IsNullOrWhiteSpace OrElse (Override_AssemblyResolverPath.IsNotNullOrWhiteSpace() AndAlso Override_AssemblyResolverPath_AutoFallbackIfNotFound)) Then
            If System.IO.File.Exists(localPath) Then
                path = localPath
            Else
                For Each folderPath In My.Settings.DllPaths.Split(";")
                    folderPath = folderPath.Trim
                    If folderPath = "" Then Continue For
                    Try
                        path = IO.Path.Combine(folderPath, DllName & ".dll")
                        If Not IO.File.Exists(path) Then
                            path = IO.Path.ChangeExtension(path, "exe")
                        End If
                        If IO.File.Exists(path) Then
                            Exit For
                        End If
                    Catch ex As Exception
                        DisplayErrorMessage(ex.Message, ex, False)
                        Throw (ex)
                    End Try
                Next
            End If
        End If

        If Not String.IsNullOrEmpty(path) AndAlso System.IO.File.Exists(path) Then
            Logger.Verbose("Resolving Assembly {Assembly} From {Path}", DllName, path)
            NewAssembly = Assembly.LoadFile(path)
            Return NewAssembly
        Else
            If Not DllName.StartsWith("expression_host_") Then Logger.Debug("Could not resolve assembly {Assembly}", DllName)
            Return Nothing
        End If
    End Function

End Class
