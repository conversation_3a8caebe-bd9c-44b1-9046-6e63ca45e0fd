﻿Imports System.Data.SqlClient
Imports DevExpress.XtraEditors

Public Class frmResendDDVoucher
    Public Sub New()
        InitializeComponent()
        ucSearchCompany.BindPopupContainerEdit(pceCompany)
        GridView1.ChangeCopyBehavior()
        gvQbLogRG.ChangeCopyBehavior
        gvQbLogPS.ChangeCopyBehavior
        gvQueueLog.ChangeCopyBehavior
        TabbedControlGroup1.SelectedTabPageIndex = 0
    End Sub

    Private Sub pceCompany_EditValueChanged(sender As Object, e As EventArgs) Handles pceCompany.EditValueChanged
        luePayrolls.EditValue = Nothing
        If pceCompany.EditValue IsNot Nothing AndAlso pceCompany.EditValue.ToString.IsNotNullOrWhiteSpace Then
            lciPrNum.Enabled = True
            Dim conum As Decimal = pceCompany.EditValue
            Using db As New dbEPDataDataContext(GetConnectionString)
                Dim payrolls = db.PAYROLLs.Where(Function(c) c.CONUM = conum).OrderByDescending(Function(p) p.PRNUM).ToList
                luePayrolls.Properties.DataSource = payrolls
                luePayrolls.Select()
                luePayrolls.EditValue = payrolls.FirstOrDefault()?.PRNUM
            End Using
        Else
            lciPrNum.Enabled = False
        End If
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            Dim conum As Decimal = pceCompany.EditValue
            Dim prnum As Decimal = luePayrolls.EditValue
            Dim sql = GetUdfValue("DD Voucher Email WebPost - Script")
            GridControl1.DataSource = Query(sql, New SqlParameter("conum", pceCompany.EditValue), New SqlParameter("prnum", luePayrolls.EditValue), New SqlParameter("reportId", 60), New SqlParameter("IncludeSendReports", ceIncludeSendReports.Checked))
            If GridView1.Columns(0).Summary.Count = 0 Then
                Dim sumItem = GridView1.Columns(0).Summary.Add()
                sumItem.SummaryType = DevExpress.Data.SummaryItemType.Count
                sumItem.DisplayFormat = "{0} rows"
            End If

            Using db As New dbEPDataDataContext(GetConnectionString)
                GridControl2.DataSource = db.prc_GetExportFormat(conum, prnum, 110).ToList()
                GridControl5.DataSource = db.prc_GetExportFormat(conum, prnum, 109).ToList()
                Dim export = db.CoOptions_Exports.SingleOrDefault(Function(e) e.CoCode = conum AndAlso e.ExportFormat = 110)
                ceIsSetupRG.Checked = export IsNot Nothing
                btnViewSetupRG.Enabled = ceIsSetupRG.Checked

                export = db.CoOptions_Exports.SingleOrDefault(Function(e) e.CoCode = conum AndAlso e.ExportFormat = 109)
                ceIsSetupPS.Checked = export IsNot Nothing
                btnViewSetupPS.Enabled = ceIsSetupPS.Checked

                Dim exportFormateMarkResend = db.prc_ExportFormatMarkResend(conum, prnum, Nothing, False)
                Dim result = exportFormateMarkResend.Single
                If result.rowsCount = 0 Then
                    LabelControl1.Text = "There's no QB Export logs available to mark for resend."
                    btnMarkForResend.Enabled = False
                ElseIf result.voidedAlready = 1 Then
                    LabelControl1.Text = "This job was already marked for resend."
                    btnMarkForResend.Enabled = False
                ElseIf result.voidedAlready = 0 Then
                    LabelControl1.Text = "Note. it can take up to 10 minutes to be reposted."
                    btnMarkForResend.Enabled = True
                End If
            End Using

            GridControl4.DataSource = Query("select * from custom.queue where conum = @conum and description = 'DD Voucher'", New SqlParameter("conum", pceCompany.EditValue))


        Catch ex As Exception
            DisplayErrorMessage("Error loading DD Vouchers", ex)
        End Try
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        Try
            'modified by Solomon on Jan 22, '20.  Delete by selection only

            'If XtraMessageBox.Show($"Are you sure you would like to delete ALL DD Vouchers for Co#: {pceCompany.EditValue} Pr#: {luePayrolls.EditValue}", "Delete DD Vouchers", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
            '    Dim deleted = modGlobals.UpdateSql("DELETE FROM dbo.PAYROLL_FILEARCHIVE WHERE CONUM = @conum AND PRNUM = @prnum AND RPT_ID = @reportId", New SqlParameter("conum", pceCompany.EditValue), New SqlParameter("prnum", luePayrolls.EditValue), New SqlParameter("reportId", 60))
            '    XtraMessageBox.Show($"Successfully deleted {deleted} Vouchers")
            'End If

            If GridView1.GetSelectedRows().Count = 0 Then
                XtraMessageBox.Show("Make your selection", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Return
            End If

            Dim lstIds As New List(Of String)
            Dim ids As String

            For Each h In GridView1.GetSelectedRows()
                lstIds.Add("~" + GridView1.GetRow(h).Item("id").ToString + "~")
            Next

            ids = String.Join(",", lstIds.ToArray())

            If XtraMessageBox.Show($"Are you sure you would like to delete the selected DD Vouchers for Co#: {pceCompany.EditValue} Pr#: {luePayrolls.EditValue}", "Delete DD Vouchers", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                Dim deleted = modGlobals.UpdateSql("DELETE FROM dbo.PAYROLL_FILEARCHIVE WHERE CONUM = @conum AND PRNUM = @prnum AND RPT_ID = @reportId AND @ids like '%~' + convert(varchar(50), id) + '~%'", New SqlParameter("conum", pceCompany.EditValue), New SqlParameter("prnum", luePayrolls.EditValue), New SqlParameter("reportId", 60), New SqlParameter("Ids", ids))
                XtraMessageBox.Show($"Successfully deleted {deleted} Vouchers")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting sent DD Vouchers", ex)
        End Try
    End Sub

    Private Sub luePayrolls_EditValueChanged(sender As Object, e As EventArgs) Handles luePayrolls.EditValueChanged
        LoadData()
    End Sub

    Private Sub ceIncludeSendReports_CheckedChanged(sender As Object, e As EventArgs) Handles ceIncludeSendReports.CheckedChanged
        LoadData()
    End Sub

    Private Sub btnViewSetupPS_Click(sender As Object, e As EventArgs) Handles btnViewSetupPS.Click
        OpenSetup(109)
    End Sub

    Private Sub btnViewSetupRG_Click(sender As Object, e As EventArgs) Handles btnViewSetupRG.Click
        OpenSetup(110)
    End Sub

    Private Sub OpenSetup(exportFormat As Integer)
        Dim conum As Decimal = pceCompany.EditValue
        Using db As New dbEPDataDataContext(GetConnectionString)
            Dim export = db.CoOptions_Exports.SingleOrDefault(Function(e) e.CoCode = conum AndAlso e.ExportFormat = exportFormat)
            Using frm = New frmQbExportSetup(export)
                frm.ShowDialog()
            End Using
        End Using
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim email As String = GridView1.GetRowCellValue(e.HitInfo.RowHandle, "Email")
            If email.IsNotNullOrWhiteSpace Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("View SendGrid Log", Sub()
                                                                                               Dim url = "https://app.sendgrid.com/email_activity?filters=%5B%7B%22val%22%3A%5B%22{0}%22%5D%2C%22selectedFieldName%22%3A%22to_email%22%2C%22comparisonType%22%3A%22Contains%22%2C%22isTextInputDisabled%22%3Afalse%7D%5D"
                                                                                               'System.Diagnostics.Process.Start(url.FormatWith(Uri.EscapeUriString(email)))
                                                                                               Dim psi As New System.Diagnostics.ProcessStartInfo()
                                                                                               psi.FileName = url.FormatWith(Uri.EscapeUriString(email))
                                                                                               psi.UseShellExecute = True
                                                                                               System.Diagnostics.Process.Start(psi)

                                                                                           End Sub))
            End If
        End If
    End Sub

    Private Sub btnMarkForResend_Click(sender As Object, e As EventArgs) Handles btnMarkForResend.Click
        Try
            Dim conum As Decimal = pceCompany.EditValue
            Dim prnum As Decimal = luePayrolls.EditValue
            Using db As New dbEPDataDataContext(GetConnectionString)
                Dim exportFormateMarkResend = db.prc_ExportFormatMarkResend(conum, prnum, Nothing, True)
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error marking for resend", ex)
        End Try
        LoadData()
    End Sub
End Class