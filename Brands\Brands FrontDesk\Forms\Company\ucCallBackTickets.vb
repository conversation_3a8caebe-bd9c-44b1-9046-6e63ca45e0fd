﻿Public Class ucCallBackTickets

    Private ReadOnly _coNum As Decimal
    Dim db As dbEPDataDataContext
    Public Sub New(coNum As Decimal, tickets As List(Of FrontDeskTicket))
        InitializeComponent()
        _coNum = coNum
        BindingSource1.DataSource = tickets
    End Sub

    'Private Async Sub ucCallBackTickets_Load(sender As Object, e As EventArgs) Handles MyBase.Load
    '    Try
    '        GridView1.ShowLoadingPanel()
    '        Await Task.Run(Sub()
    '                           db = New dbEPDataDataContext(GetConnectionString)
    '                           Dim tickets = (From A In db.FrontDeskTickets Where A.ByUser = UserName Order By A.ID Descending).Take(10)
    '                           BindingSource1.DataSource = Q
    '                       End Sub)
    '    Catch ex As Exception
    '        DisplayErrorMessage(ex, ex.Message)
    '    Finally
    '        GridView1.HideLoadingPanel()
    '    End Try
    'End Sub
End Class
