﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmDDReversal
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.dD_Reversal_LogsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.bbiRefresh = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiProcessTransactions = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.MemoEdit1 = New DevExpress.XtraEditors.MemoEdit()
        Me.AccordionControl1 = New DevExpress.XtraBars.Navigation.AccordionControl()
        Me.aceQueus = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.tePrnum = New DevExpress.XtraEditors.TextEdit()
        Me.teEmpNum = New DevExpress.XtraEditors.TextEdit()
        Me.btnChangeStatus = New DevExpress.XtraEditors.SimpleButton()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEMPNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPayroll_num = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCheck_date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCHK_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colGROSS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNET = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCHK_COUNTER = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCM_DIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNacha_DIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNacha_CHK_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEMP_ROUTING = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEMP_ACCTNUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAMOUNT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTRAN_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDebit_Routing = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDebit_Account = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colER_Tran_Type = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colER_ACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReason = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFault = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDate_Requested = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRequested_By = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBriefDesc = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReversal_Status = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEE_Reversal_Effective_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEE_Reversal_Process_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colER_Refund_Effective_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colER_Refund_Process_Date = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBanking_Dept_Notes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEE_Reversal_NACHA_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colER_Refund_NACHA_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChangeLog = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNoCharge = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.teConum = New DevExpress.XtraEditors.TextEdit()
        Me.cbeStatus = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
        Me.lcgDetails = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciPrNum = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.RibbonPage2 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dD_Reversal_LogsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AccordionControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tePrnum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teEmpNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teConum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgDetails, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciPrNum, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.TextEdit1)
        Me.LayoutControl1.Controls.Add(Me.MemoEdit1)
        Me.LayoutControl1.Controls.Add(Me.AccordionControl1)
        Me.LayoutControl1.Controls.Add(Me.TextEdit3)
        Me.LayoutControl1.Controls.Add(Me.tePrnum)
        Me.LayoutControl1.Controls.Add(Me.teEmpNum)
        Me.LayoutControl1.Controls.Add(Me.btnChangeStatus)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Controls.Add(Me.teConum)
        Me.LayoutControl1.Controls.Add(Me.cbeStatus)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 162)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(1047, 347, 650, 400)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1098, 471)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "BriefDesc", True))
        Me.TextEdit1.Location = New System.Drawing.Point(767, 266)
        Me.TextEdit1.MenuManager = Me.RibbonControl1
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.ReadOnly = True
        Me.TextEdit1.Size = New System.Drawing.Size(307, 20)
        Me.TextEdit1.StyleController = Me.LayoutControl1
        Me.TextEdit1.TabIndex = 13
        '
        'dD_Reversal_LogsBindingSource
        '
        Me.dD_Reversal_LogsBindingSource.DataSource = GetType(Brands_FrontDesk.DD_Reversal_Log)
        '
        'RibbonControl1
        '
        Me.RibbonControl1.ExpandCollapseItem.Id = 0
        Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.bbiRefresh, Me.bbiProcessTransactions})
        Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl1.MaxItemId = 3
        Me.RibbonControl1.Name = "RibbonControl1"
        Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
        Me.RibbonControl1.Size = New System.Drawing.Size(1098, 162)
        '
        'bbiRefresh
        '
        Me.bbiRefresh.Caption = "Refresh"
        Me.bbiRefresh.Id = 1
        Me.bbiRefresh.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.refresh
        Me.bbiRefresh.Name = "bbiRefresh"
        '
        'bbiProcessTransactions
        '
        Me.bbiProcessTransactions.Caption = "Process Transaction"
        Me.bbiProcessTransactions.Id = 2
        Me.bbiProcessTransactions.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.properties_16x161
        Me.bbiProcessTransactions.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.properties_32x32
        Me.bbiProcessTransactions.Name = "bbiProcessTransactions"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRefresh)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiProcessTransactions)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.Text = "Actions"
        '
        'MemoEdit1
        '
        Me.MemoEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ChangeLog", True))
        Me.MemoEdit1.Location = New System.Drawing.Point(700, 306)
        Me.MemoEdit1.MenuManager = Me.RibbonControl1
        Me.MemoEdit1.Name = "MemoEdit1"
        Me.MemoEdit1.Properties.ReadOnly = True
        Me.MemoEdit1.Size = New System.Drawing.Size(374, 141)
        Me.MemoEdit1.StyleController = Me.LayoutControl1
        Me.MemoEdit1.TabIndex = 12
        '
        'AccordionControl1
        '
        Me.AccordionControl1.AllowItemSelection = True
        Me.AccordionControl1.Elements.AddRange(New DevExpress.XtraBars.Navigation.AccordionControlElement() {Me.aceQueus})
        Me.AccordionControl1.Location = New System.Drawing.Point(12, 12)
        Me.AccordionControl1.Name = "AccordionControl1"
        Me.AccordionControl1.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.[Auto]
        Me.AccordionControl1.ShowGroupExpandButtons = False
        Me.AccordionControl1.Size = New System.Drawing.Size(158, 447)
        Me.AccordionControl1.StyleController = Me.LayoutControl1
        Me.AccordionControl1.TabIndex = 11
        Me.AccordionControl1.Text = "AccordionControl1"
        '
        'aceQueus
        '
        Me.aceQueus.Expanded = True
        Me.aceQueus.HeaderTemplate.AddRange(New DevExpress.XtraBars.Navigation.HeaderElementInfo() {New DevExpress.XtraBars.Navigation.HeaderElementInfo(DevExpress.XtraBars.Navigation.HeaderElementType.Text), New DevExpress.XtraBars.Navigation.HeaderElementInfo(DevExpress.XtraBars.Navigation.HeaderElementType.Image), New DevExpress.XtraBars.Navigation.HeaderElementInfo(DevExpress.XtraBars.Navigation.HeaderElementType.HeaderControl), New DevExpress.XtraBars.Navigation.HeaderElementInfo(DevExpress.XtraBars.Navigation.HeaderElementType.ContextButtons)})
        Me.aceQueus.Name = "aceQueus"
        Me.aceQueus.Text = "Transaction List"
        '
        'TextEdit3
        '
        Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "AMOUNT", True))
        Me.TextEdit3.Location = New System.Drawing.Point(767, 121)
        Me.TextEdit3.MenuManager = Me.RibbonControl1
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.ReadOnly = True
        Me.TextEdit3.Size = New System.Drawing.Size(307, 20)
        Me.TextEdit3.StyleController = Me.LayoutControl1
        Me.TextEdit3.TabIndex = 9
        '
        'tePrnum
        '
        Me.tePrnum.Location = New System.Drawing.Point(767, 97)
        Me.tePrnum.MenuManager = Me.RibbonControl1
        Me.tePrnum.Name = "tePrnum"
        Me.tePrnum.Properties.ReadOnly = True
        Me.tePrnum.Size = New System.Drawing.Size(307, 20)
        Me.tePrnum.StyleController = Me.LayoutControl1
        Me.tePrnum.TabIndex = 8
        '
        'teEmpNum
        '
        Me.teEmpNum.Location = New System.Drawing.Point(767, 73)
        Me.teEmpNum.MenuManager = Me.RibbonControl1
        Me.teEmpNum.Name = "teEmpNum"
        Me.teEmpNum.Properties.ReadOnly = True
        Me.teEmpNum.Size = New System.Drawing.Size(307, 20)
        Me.teEmpNum.StyleController = Me.LayoutControl1
        Me.teEmpNum.TabIndex = 7
        '
        'btnChangeStatus
        '
        Me.btnChangeStatus.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.status_16x161
        Me.btnChangeStatus.Location = New System.Drawing.Point(968, 169)
        Me.btnChangeStatus.Name = "btnChangeStatus"
        Me.btnChangeStatus.Size = New System.Drawing.Size(106, 22)
        Me.btnChangeStatus.StyleController = Me.LayoutControl1
        Me.btnChangeStatus.TabIndex = 6
        Me.btnChangeStatus.Text = "Change Status"
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.dD_Reversal_LogsBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(174, 12)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(500, 447)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colID, Me.colCONUM, Me.colEMPNUM, Me.colPayroll_num, Me.colCheck_date, Me.colCHK_NUM, Me.colGROSS, Me.colNET, Me.colCHK_COUNTER, Me.colCM_DIVNUM, Me.colNacha_DIVNUM, Me.colNacha_CHK_NUM, Me.colEMP_ROUTING, Me.colEMP_ACCTNUM, Me.colAMOUNT, Me.colACCT_TYPE, Me.colTRAN_TYPE, Me.colDebit_Routing, Me.colDebit_Account, Me.colER_Tran_Type, Me.colER_ACCT_TYPE, Me.colReason, Me.colFault, Me.colDate_Requested, Me.colRequested_By, Me.colBriefDesc, Me.colReversal_Status, Me.colEE_Reversal_Effective_Date, Me.colEE_Reversal_Process_Date, Me.colER_Refund_Effective_Date, Me.colER_Refund_Process_Date, Me.colBanking_Dept_Notes, Me.colEE_Reversal_NACHA_ID, Me.colER_Refund_NACHA_ID, Me.colChangeLog, Me.colNoCharge})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colID
        '
        Me.colID.FieldName = "ID"
        Me.colID.Name = "colID"
        Me.colID.Visible = True
        Me.colID.VisibleIndex = 0
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        Me.colCONUM.Visible = True
        Me.colCONUM.VisibleIndex = 1
        '
        'colEMPNUM
        '
        Me.colEMPNUM.FieldName = "EMPNUM"
        Me.colEMPNUM.Name = "colEMPNUM"
        Me.colEMPNUM.Visible = True
        Me.colEMPNUM.VisibleIndex = 2
        '
        'colPayroll_num
        '
        Me.colPayroll_num.FieldName = "Payroll_num"
        Me.colPayroll_num.Name = "colPayroll_num"
        Me.colPayroll_num.Visible = True
        Me.colPayroll_num.VisibleIndex = 3
        '
        'colCheck_date
        '
        Me.colCheck_date.FieldName = "Check_date"
        Me.colCheck_date.Name = "colCheck_date"
        Me.colCheck_date.Visible = True
        Me.colCheck_date.VisibleIndex = 4
        '
        'colCHK_NUM
        '
        Me.colCHK_NUM.FieldName = "CHK_NUM"
        Me.colCHK_NUM.Name = "colCHK_NUM"
        Me.colCHK_NUM.Visible = True
        Me.colCHK_NUM.VisibleIndex = 5
        '
        'colGROSS
        '
        Me.colGROSS.FieldName = "GROSS"
        Me.colGROSS.Name = "colGROSS"
        Me.colGROSS.Visible = True
        Me.colGROSS.VisibleIndex = 6
        '
        'colNET
        '
        Me.colNET.FieldName = "NET"
        Me.colNET.Name = "colNET"
        Me.colNET.Visible = True
        Me.colNET.VisibleIndex = 7
        '
        'colCHK_COUNTER
        '
        Me.colCHK_COUNTER.FieldName = "CHK_COUNTER"
        Me.colCHK_COUNTER.Name = "colCHK_COUNTER"
        Me.colCHK_COUNTER.Visible = True
        Me.colCHK_COUNTER.VisibleIndex = 8
        '
        'colCM_DIVNUM
        '
        Me.colCM_DIVNUM.FieldName = "CM_DIVNUM"
        Me.colCM_DIVNUM.Name = "colCM_DIVNUM"
        Me.colCM_DIVNUM.Visible = True
        Me.colCM_DIVNUM.VisibleIndex = 9
        '
        'colNacha_DIVNUM
        '
        Me.colNacha_DIVNUM.FieldName = "Nacha_DIVNUM"
        Me.colNacha_DIVNUM.Name = "colNacha_DIVNUM"
        Me.colNacha_DIVNUM.Visible = True
        Me.colNacha_DIVNUM.VisibleIndex = 10
        '
        'colNacha_CHK_NUM
        '
        Me.colNacha_CHK_NUM.FieldName = "Nacha_CHK_NUM"
        Me.colNacha_CHK_NUM.Name = "colNacha_CHK_NUM"
        Me.colNacha_CHK_NUM.Visible = True
        Me.colNacha_CHK_NUM.VisibleIndex = 11
        '
        'colEMP_ROUTING
        '
        Me.colEMP_ROUTING.FieldName = "EMP_ROUTING"
        Me.colEMP_ROUTING.Name = "colEMP_ROUTING"
        Me.colEMP_ROUTING.Visible = True
        Me.colEMP_ROUTING.VisibleIndex = 12
        '
        'colEMP_ACCTNUM
        '
        Me.colEMP_ACCTNUM.FieldName = "EMP_ACCTNUM"
        Me.colEMP_ACCTNUM.Name = "colEMP_ACCTNUM"
        Me.colEMP_ACCTNUM.Visible = True
        Me.colEMP_ACCTNUM.VisibleIndex = 13
        '
        'colAMOUNT
        '
        Me.colAMOUNT.FieldName = "AMOUNT"
        Me.colAMOUNT.Name = "colAMOUNT"
        Me.colAMOUNT.Visible = True
        Me.colAMOUNT.VisibleIndex = 14
        '
        'colACCT_TYPE
        '
        Me.colACCT_TYPE.FieldName = "ACCT_TYPE"
        Me.colACCT_TYPE.Name = "colACCT_TYPE"
        Me.colACCT_TYPE.Visible = True
        Me.colACCT_TYPE.VisibleIndex = 15
        '
        'colTRAN_TYPE
        '
        Me.colTRAN_TYPE.FieldName = "TRAN_TYPE"
        Me.colTRAN_TYPE.Name = "colTRAN_TYPE"
        Me.colTRAN_TYPE.Visible = True
        Me.colTRAN_TYPE.VisibleIndex = 16
        '
        'colDebit_Routing
        '
        Me.colDebit_Routing.FieldName = "Debit_Routing"
        Me.colDebit_Routing.Name = "colDebit_Routing"
        Me.colDebit_Routing.Visible = True
        Me.colDebit_Routing.VisibleIndex = 17
        '
        'colDebit_Account
        '
        Me.colDebit_Account.FieldName = "Debit_Account"
        Me.colDebit_Account.Name = "colDebit_Account"
        Me.colDebit_Account.Visible = True
        Me.colDebit_Account.VisibleIndex = 18
        '
        'colER_Tran_Type
        '
        Me.colER_Tran_Type.FieldName = "ER_Tran_Type"
        Me.colER_Tran_Type.Name = "colER_Tran_Type"
        Me.colER_Tran_Type.Visible = True
        Me.colER_Tran_Type.VisibleIndex = 19
        '
        'colER_ACCT_TYPE
        '
        Me.colER_ACCT_TYPE.FieldName = "ER_ACCT_TYPE"
        Me.colER_ACCT_TYPE.Name = "colER_ACCT_TYPE"
        Me.colER_ACCT_TYPE.Visible = True
        Me.colER_ACCT_TYPE.VisibleIndex = 20
        '
        'colReason
        '
        Me.colReason.FieldName = "Reason"
        Me.colReason.Name = "colReason"
        Me.colReason.Visible = True
        Me.colReason.VisibleIndex = 21
        '
        'colFault
        '
        Me.colFault.FieldName = "Fault"
        Me.colFault.Name = "colFault"
        Me.colFault.Visible = True
        Me.colFault.VisibleIndex = 22
        '
        'colDate_Requested
        '
        Me.colDate_Requested.FieldName = "Date_Requested"
        Me.colDate_Requested.Name = "colDate_Requested"
        Me.colDate_Requested.Visible = True
        Me.colDate_Requested.VisibleIndex = 23
        '
        'colRequested_By
        '
        Me.colRequested_By.FieldName = "Requested_By"
        Me.colRequested_By.Name = "colRequested_By"
        Me.colRequested_By.Visible = True
        Me.colRequested_By.VisibleIndex = 24
        '
        'colBriefDesc
        '
        Me.colBriefDesc.FieldName = "BriefDesc"
        Me.colBriefDesc.Name = "colBriefDesc"
        Me.colBriefDesc.Visible = True
        Me.colBriefDesc.VisibleIndex = 25
        '
        'colReversal_Status
        '
        Me.colReversal_Status.FieldName = "Reversal_Status"
        Me.colReversal_Status.Name = "colReversal_Status"
        Me.colReversal_Status.Visible = True
        Me.colReversal_Status.VisibleIndex = 26
        '
        'colEE_Reversal_Effective_Date
        '
        Me.colEE_Reversal_Effective_Date.FieldName = "EE_Reversal_Effective_Date"
        Me.colEE_Reversal_Effective_Date.Name = "colEE_Reversal_Effective_Date"
        Me.colEE_Reversal_Effective_Date.Visible = True
        Me.colEE_Reversal_Effective_Date.VisibleIndex = 27
        '
        'colEE_Reversal_Process_Date
        '
        Me.colEE_Reversal_Process_Date.FieldName = "EE_Reversal_Process_Date"
        Me.colEE_Reversal_Process_Date.Name = "colEE_Reversal_Process_Date"
        Me.colEE_Reversal_Process_Date.Visible = True
        Me.colEE_Reversal_Process_Date.VisibleIndex = 28
        '
        'colER_Refund_Effective_Date
        '
        Me.colER_Refund_Effective_Date.FieldName = "ER_Refund_Effective_Date"
        Me.colER_Refund_Effective_Date.Name = "colER_Refund_Effective_Date"
        Me.colER_Refund_Effective_Date.Visible = True
        Me.colER_Refund_Effective_Date.VisibleIndex = 29
        '
        'colER_Refund_Process_Date
        '
        Me.colER_Refund_Process_Date.FieldName = "ER_Refund_Process_Date"
        Me.colER_Refund_Process_Date.Name = "colER_Refund_Process_Date"
        Me.colER_Refund_Process_Date.Visible = True
        Me.colER_Refund_Process_Date.VisibleIndex = 30
        '
        'colBanking_Dept_Notes
        '
        Me.colBanking_Dept_Notes.FieldName = "Banking_Dept_Notes"
        Me.colBanking_Dept_Notes.Name = "colBanking_Dept_Notes"
        Me.colBanking_Dept_Notes.Visible = True
        Me.colBanking_Dept_Notes.VisibleIndex = 31
        '
        'colEE_Reversal_NACHA_ID
        '
        Me.colEE_Reversal_NACHA_ID.FieldName = "EE_Reversal_NACHA_ID"
        Me.colEE_Reversal_NACHA_ID.Name = "colEE_Reversal_NACHA_ID"
        Me.colEE_Reversal_NACHA_ID.Visible = True
        Me.colEE_Reversal_NACHA_ID.VisibleIndex = 32
        '
        'colER_Refund_NACHA_ID
        '
        Me.colER_Refund_NACHA_ID.FieldName = "ER_Refund_NACHA_ID"
        Me.colER_Refund_NACHA_ID.Name = "colER_Refund_NACHA_ID"
        Me.colER_Refund_NACHA_ID.Visible = True
        Me.colER_Refund_NACHA_ID.VisibleIndex = 33
        '
        'colChangeLog
        '
        Me.colChangeLog.FieldName = "ChangeLog"
        Me.colChangeLog.Name = "colChangeLog"
        Me.colChangeLog.Visible = True
        Me.colChangeLog.VisibleIndex = 34
        '
        'colNoCharge
        '
        Me.colNoCharge.FieldName = "NoCharge"
        Me.colNoCharge.Name = "colNoCharge"
        Me.colNoCharge.Visible = True
        Me.colNoCharge.VisibleIndex = 35
        '
        'teConum
        '
        Me.teConum.Location = New System.Drawing.Point(767, 49)
        Me.teConum.MenuManager = Me.RibbonControl1
        Me.teConum.Name = "teConum"
        Me.teConum.Properties.ReadOnly = True
        Me.teConum.Size = New System.Drawing.Size(307, 20)
        Me.teConum.StyleController = Me.LayoutControl1
        Me.teConum.TabIndex = 5
        '
        'cbeStatus
        '
        Me.cbeStatus.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Reversal_Status", True))
        Me.cbeStatus.Location = New System.Drawing.Point(767, 145)
        Me.cbeStatus.MenuManager = Me.RibbonControl1
        Me.cbeStatus.Name = "cbeStatus"
        Me.cbeStatus.Properties.ReadOnly = True
        Me.cbeStatus.Size = New System.Drawing.Size(307, 20)
        Me.cbeStatus.StyleController = Me.LayoutControl1
        Me.cbeStatus.TabIndex = 10
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.SplitterItem1, Me.lcgDetails, Me.LayoutControlItem8})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1098, 471)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(162, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(504, 451)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'SplitterItem1
        '
        Me.SplitterItem1.AllowHotTrack = True
        Me.SplitterItem1.Inverted = True
        Me.SplitterItem1.IsCollapsible = DevExpress.Utils.DefaultBoolean.[True]
        Me.SplitterItem1.Location = New System.Drawing.Point(666, 0)
        Me.SplitterItem1.Name = "SplitterItem1"
        Me.SplitterItem1.Size = New System.Drawing.Size(10, 451)
        '
        'lcgDetails
        '
        Me.lcgDetails.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.LayoutControlItem2, Me.LayoutControlItem4, Me.lciPrNum, Me.LayoutControlItem6, Me.LayoutControlItem7, Me.EmptySpaceItem1, Me.EmptySpaceItem2, Me.LayoutControlItem5, Me.LayoutControlItem9})
        Me.lcgDetails.Location = New System.Drawing.Point(676, 0)
        Me.lcgDetails.Name = "lcgDetails"
        Me.lcgDetails.Size = New System.Drawing.Size(402, 451)
        Me.lcgDetails.Text = "Transaction Details"
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.btnChangeStatus
        Me.LayoutControlItem3.Location = New System.Drawing.Point(268, 120)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(110, 26)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.teConum
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(378, 24)
        Me.LayoutControlItem2.Text = "Co#: "
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(64, 13)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.teEmpNum
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(378, 24)
        Me.LayoutControlItem4.Text = "Emp#: "
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(64, 13)
        '
        'lciPrNum
        '
        Me.lciPrNum.Control = Me.tePrnum
        Me.lciPrNum.Location = New System.Drawing.Point(0, 48)
        Me.lciPrNum.Name = "lciPrNum"
        Me.lciPrNum.Size = New System.Drawing.Size(378, 24)
        Me.lciPrNum.Text = "Pr#: "
        Me.lciPrNum.TextSize = New System.Drawing.Size(64, 13)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.TextEdit3
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(378, 24)
        Me.LayoutControlItem6.Text = "Amount: "
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(64, 13)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.cbeStatus
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(378, 24)
        Me.LayoutControlItem7.Text = "Status: "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(64, 13)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 146)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(378, 71)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 120)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(268, 26)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.MemoEdit1
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 241)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(378, 161)
        Me.LayoutControlItem5.Text = "Change Log: "
        Me.LayoutControlItem5.TextLocation = DevExpress.Utils.Locations.Top
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(64, 13)
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.TextEdit1
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 217)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(378, 24)
        Me.LayoutControlItem9.Text = "Brief Desc: "
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(64, 13)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.AccordionControl1
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(162, 451)
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'RibbonPage2
        '
        Me.RibbonPage2.Name = "RibbonPage2"
        Me.RibbonPage2.Text = "RibbonPage2"
        '
        'frmDDReversal
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1098, 633)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.RibbonControl1)
        Me.Name = "frmDDReversal"
        Me.Ribbon = Me.RibbonControl1
        Me.Text = "DD Reversal"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dD_Reversal_LogsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AccordionControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tePrnum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teEmpNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teConum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgDetails, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciPrNum, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents dD_Reversal_LogsBindingSource As BindingSource
    Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPage2 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents bbiRefresh As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents colID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEMPNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPayroll_num As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCheck_date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCHK_NUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colGROSS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNET As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCHK_COUNTER As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCM_DIVNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNacha_DIVNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNacha_CHK_NUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEMP_ROUTING As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEMP_ACCTNUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colAMOUNT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTRAN_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDebit_Routing As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDebit_Account As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colER_Tran_Type As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colER_ACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReason As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFault As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDate_Requested As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRequested_By As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBriefDesc As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReversal_Status As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEE_Reversal_Effective_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEE_Reversal_Process_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colER_Refund_Effective_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colER_Refund_Process_Date As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBanking_Dept_Notes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEE_Reversal_NACHA_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colER_Refund_NACHA_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents btnChangeStatus As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents bbiProcessTransactions As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents lcgDetails As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents AccordionControl1 As DevExpress.XtraBars.Navigation.AccordionControl
    Friend WithEvents aceQueus As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tePrnum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teEmpNum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lciPrNum As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teConum As DevExpress.XtraEditors.TextEdit
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents cbeStatus As DevExpress.XtraEditors.TextEdit
    Friend WithEvents colChangeLog As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNoCharge As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents MemoEdit1 As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
End Class
