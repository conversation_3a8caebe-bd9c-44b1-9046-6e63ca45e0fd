﻿Imports System.ComponentModel
'Imports CrystalDecisions.CrystalReports.Engine
'Imports CrystalDecisions.Shared
Imports System.IO
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors

Public Class ucReportEmailTemplate

    Sub New()
        InitializeComponent()
    End Sub

    Friend CoNum As Decimal
    Private Property db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Comp As COMPANY
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property LastPrNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property LastCheckDate As DateTime

    Private _reportEmailTempleteList As List(Of ReportEmailTeplate)
    Private frmLogger As Serilog.ILogger
    Private _reportProcessorQueue As ReportProcessorQueue

    Private Sub frmReportEmailTemplate_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            AddHandler UcReportQueuePending1.OnReportRemoved, Sub()

                                                              End Sub
            frmLogger = Logger.ForContext(Me.GetType())
            ucClientsQueue.Conum = CoNum
            If Not DesignMode Then
                teReportPassword.Text = GetComPassword(CoNum)
                LoadData()
                UcRecentReports1.LoadRecentReports()
                For Each PrinterName In Printing.PrinterSettings.InstalledPrinters
                    Me.cbPrinters.Properties.Items.Add(PrinterName)
                Next
                Dim defaultPrinting = New Printing.PrinterSettings
                Me.cbPrinters.EditValue = defaultPrinting.PrinterName
                If Not Permissions.Manager.HasValue OrElse Not Permissions.Manager.Value Then lciUseEPTestPath.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                btnQtrEndReports.Visible = Permissions.QuarterEndEmailReports
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading Reports", ex)
        End Try
    End Sub

    Private Sub btnAddReport_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        Try
            Dim frm = (New frmReportEmailTemplateAddOrEdit)
            frm.ShowDialog()
            LoadData()
            UcRecentReports1.LoadRecentReports()
        Catch ex As Exception
            DisplayErrorMessage("Error in btnAddReport_Click", ex)
        End Try
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Try
            LoadData()
            UcRecentReports1.LoadRecentReports()
        Catch ex As Exception
            DisplayErrorMessage("Error in btnRefresh_Click", ex)
        End Try
    End Sub

    Private Sub LoadData()
        btnRefresh.Enabled = False
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            Comp = (From c In db.COMPANies Where c.CONUM = CoNum).Single
            UcRecentReports1.Comp = Comp
            Dim i = If(GridView1.FocusedRowHandle = DevExpress.XtraGrid.GridControl.InvalidRowHandle, 0, GridView1.FocusedRowHandle)
            Dim data = (From r In db.ReportEmailTeplates
                        Where r.IsDeleted = False
                        Group Join s In db.SCHCORPT_LISTs On New With {r.SRPT_ID, CoNum} Equals New With {.SRPT_ID = CType(s.SRPT_ID, Integer?), s.CONUM} Into child = Group
                        From sl In child.DefaultIfEmpty()
                        Select New With {.Report = r, .Scheduled = sl.RPT_PRT})
            For Each item In data
                item.Report.IsSetOnCompany = item.Scheduled IsNot Nothing
            Next
            '_reportEmailTempleteList = db.ReportEmailTeplates.ToList()
            ReportEmailTeplateBindingSource.DataSource = data.Select(Function(d) d.Report).ToList
            GridView1.BestFitColumns()
            GridView1.FocusedRowHandle = i
            GridView1.MakeRowVisible(i)
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        Finally
            btnRefresh.Enabled = True
        End Try
    End Sub

    Private Function ProcessReportAsync(report As ReportEmailTeplate, showInRecentReports As Boolean, fileType As modReports.FileType) As ReportResults
        Try
            lcRoot.ShowProgessPanel()
            Dim processor = New ReportProcessor(Comp, report, fileType) With {.showInRecentReports = showInRecentReports, .LastPrNum = LastPrNum, .LastCheckDate = LastCheckDate, .useEPTestPath = ceUseEPTestPath.Checked}
            Dim result = processor.ProcessReport()
            UcRecentReports1.LoadRecentReports()
            Return result
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Function

    Private Sub btnEmailReport_Click(sender As Object, e As EventArgs) Handles btnProcessAndSend.Click
        Try
            Dim report = GetFocusedReport()
            ProcessAndEmailReport(report)
        Catch ex As Exception
            DisplayErrorMessage("Error in btnEmailReport_Click", ex)
        End Try
    End Sub

    Private Sub ProcessReport(report As ReportEmailTeplate, fileType As FileType)
        Try
            If report.ReportType = "Eml" Then
                DisplayMessageBox("This is a email only report and cannot be processed.")
                Exit Sub
            End If
            Dim ReportResults = ProcessReportAsync(report, True, fileType)

            If Not ReportResults.Cancalled AndAlso Not ReportResults.AllFileExist Then
                DisplayMessageBox("No records returned for the parameters values entered")
            ElseIf Not ReportResults.Cancalled AndAlso XtraMessageBox.Show("Do you want to open this file ?", "Export", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                For Each file In ReportResults.Paths
                    'System.Diagnostics.Process.Start(file)
                    Dim psi As New System.Diagnostics.ProcessStartInfo()
                    psi.FileName = file
                    psi.UseShellExecute = True
                    System.Diagnostics.Process.Start(psi)
                Next
            End If
            UcRecentReports1.LoadRecentReports()
        Catch ex As Exception
            DisplayErrorMessage("Error processing report", ex)
        End Try
    End Sub

    Private Sub ProcessAndEmailReport(report As ReportEmailTeplate)
        Try
            Dim reportPath = ProcessReportAsync(report, False, FileType.Pdf)
            If Not reportPath.Cancalled Then
                If report.ReportType <> "Eml" AndAlso Not reportPath.AllFileExist Then
                    DisplayMessageBox("No records returned for the parameters values entered.")
                    Exit Sub
                End If
                'modReports.EmailReport(Comp, report, reportPath.Path, True, reportPath.Row)
                Dim _ReporSender = New ReportSender(reportPath)
                _ReporSender.EmailReport()
            End If

            UcRecentReports1.LoadRecentReports()
            EmailPassword(Comp.CONUM)
        Catch ex As Exception
            DisplayErrorMessage("Error processing report: {0}".FormatWith(report.Name), ex)
        End Try
    End Sub

    Private Async Sub ProcessAndCreateZendeskTicket(report As ReportEmailTeplate)
        Try
            Await CanCreateTicketAsync()

            Dim reportPath = ProcessReportAsync(report, False, FileType.Pdf)
            If Not reportPath.Cancalled Then
                If report.ReportType <> "Eml" AndAlso Not reportPath.AllFileExist Then
                    DisplayMessageBox("No records returned for the parameters values entered.")
                    Exit Sub
                End If
                Dim _ReportSender = New ReportSender(reportPath)
                Await _ReportSender.CreateTicketAsync()
            End If

            UcRecentReports1.LoadRecentReports()
            EmailPassword(Comp.CONUM)
        Catch ex As Exception
            DisplayErrorMessage("Error processing report: {0}".FormatWith(report.Name), ex)
        End Try
    End Sub

    Private Sub btnPrintReport_Click(sender As Object, e As EventArgs) Handles btnPrintReport.Click
        Try
            If GetFocusedReport().ReportType = "Eml" Then
                DisplayMessageBox("This report is a Email only report and cannot be printed.")
                Exit Sub
            End If
            Dim reportResults = ProcessReportAsync(GetFocusedReport(), True, FileType.Pdf)
            If reportResults.Cancalled Then Exit Sub
            If Not reportResults.AllFileExist Then
                DisplayMessageBox("No records returned for the parameters values entered.")
                Exit Sub
            End If

            For Each file In reportResults.Paths
                Using pdfDocumentProcessor As New DevExpress.Pdf.PdfDocumentProcessor()
                    pdfDocumentProcessor.LoadDocument(file, True)
                    Dim pdfPrinterSettings As New DevExpress.Pdf.PdfPrinterSettings()
                    pdfPrinterSettings.Settings.PrinterName = cbPrinters.Text
                    pdfDocumentProcessor.Print(pdfPrinterSettings)
                End Using
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error printing report", ex)
        End Try
    End Sub
    Private Function GetFocusedReport() As ReportEmailTeplate
        Return CType(GridView1.GetFocusedRow(), ReportEmailTeplate)
    End Function

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        Try
            If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRowCell Then
                Dim row As ReportEmailTeplate = GridView1.GetRow(e.HitInfo.RowHandle)
                e.Menu.Items.Add(New DXMenuItem("Process && Send (Queued)", Sub() QueueReport(row)))

                e.Menu.Items.Add(New DXMenuItem("Process && Send (Zendesk)", Sub() ProcessAndCreateZendeskTicket(row)))

                e.Menu.Items.Add(New DXMenuItem("Process And Email", Sub()
                                                                         ProcessAndEmailReport(row)
                                                                     End Sub, My.Resources.emailtemplate_16x16))
                Dim subMenu = New DXSubMenuItem("Process")
                subMenu.Items.Add(New DXMenuItem("PDF", Sub() ProcessReport(row, FileType.Pdf), My.Resources.exporttopdf_16x16))
                subMenu.Items.Add(New DXMenuItem("TXT", Sub() ProcessReport(row, FileType.Txt), My.Resources.exporttotxt_16x16))
                subMenu.Items.Add(New DXMenuItem("XLS", Sub() ProcessReport(row, FileType.Xls), My.Resources.exporttoxls_16x16))
                e.Menu.Items.Add(subMenu)

                e.Menu.Items.Add(New DXMenuItem("Edit", Sub() EditReportEmailTeplate(row), My.Resources.edit) With {.BeginGroup = True})
                e.Menu.Items.Add(New DXMenuItem("Delete", Sub()
                                                              If XtraMessageBox.Show("Are you sure you would like to delete this ?", "Delete?", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
                                                                  db.ReportEmailTeplates.DeleteOnSubmit(GridView1.GetRow(e.HitInfo.RowHandle))
                                                                  If db.SaveChanges() = False Then
                                                                      'if reference does not allow to delete then get row, refresh, set to delete and reload
                                                                      Dim rowAttemptedToDelete As ReportEmailTeplate = GridView1.GetRow(e.HitInfo.RowHandle)
                                                                      LoadData()
                                                                      db.ReportEmailTeplates.Where(Function(r) r.ID = rowAttemptedToDelete.ID).FirstOrDefault().IsDeleted = True
                                                                      db.SaveChanges()
                                                                      LoadData()
                                                                  End If
                                                                  LoadData()
                                                              End If
                                                          End Sub, My.Resources.delete_16x16))
                e.Menu.Items.Add(New DXMenuItem("Copy Row", Sub()
                                                                DuplicateRow(GridView1.GetRow(e.HitInfo.RowHandle))
                                                            End Sub))
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in popup menu", ex)
        End Try
    End Sub

    Private Sub EditReportEmailTeplate(_reportEmailTemplate As ReportEmailTeplate)
        Try
            Using frm = New frmReportEmailTemplateAddOrEdit(_reportEmailTemplate.Name)
                frm.ShowDialog()
                LoadData()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error editing report email template", ex)
        End Try
    End Sub

    Private Sub DuplicateRow(row As ReportEmailTeplate)
        Try
            If row Is Nothing Then Exit Sub
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim newRow = row.CloneEntity
            newRow.ID = 0
            newRow.Name = row.Name & " - Copy"
            db.ReportEmailTeplates.InsertOnSubmit(newRow)
            For Each item In row.ReportEmailTemplateFiles
                Dim newItem = item.CloneEntity
                newItem.Id = 0
                newItem.ReportEmailTemplateId = 0
                newRow.ReportEmailTemplateFiles.Add(newItem)
            Next
            db.SaveChanges
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error copying row", ex)
        End Try
    End Sub

    Private Function PasswordProtect(name As String) As Boolean
        Try
            If name.IsNullOrWhiteSpace Then
                Return False
            Else
                Dim report = _reportEmailTempleteList.SingleOrDefault(Function(f) f.Name = name)
                If report Is Nothing Then
                    Return True
                Else
                    Return report.PasswordProtect
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in PasswordProtect", ex)
            Return False
        End Try
    End Function

    Private Sub btnEmailPassword_Click(sender As Object, e As EventArgs) Handles btnEmailPassword.Click
        Try
            EmailPassword(Comp.CONUM, True)
        Catch ex As Exception
            DisplayErrorMessage("Error in btnEmailPassword_Click", ex)
        End Try
    End Sub

    Private Sub teReportPassword_Properties_Click(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles teReportPassword.Properties.ButtonClick
        Try
            If e.Button.Caption = "Change Password" Then
                Dim frm = New frmChangePassword(Comp.CONUM)
                If frm.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                    teReportPassword.Text = frm.teNewPass.Text
                End If
            ElseIf e.Button.Caption = "Email Password" Then
                EmailPassword(Comp.CONUM, True)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in teReportPassword_Properties_Click", ex)
        End Try
    End Sub

    Private Sub ddbProcess_Click(sender As Object, e As EventArgs) Handles ddbProcess.Click
        Try
            If GetFocusedReport().ReportType = "Eml" Then
                DisplayMessageBox("This is a email only report and cannot be processed.")
                Exit Sub
            End If
            Dim ReportResults = ProcessReportAsync(GetFocusedReport(), True, FileType.Pdf)

            If Not ReportResults.Cancalled AndAlso Not ReportResults.AllFileExist Then
                DisplayMessageBox("No records returned for the parameters values entered")
            ElseIf Not ReportResults.Cancalled AndAlso XtraMessageBox.Show("Do you want to open this file ?", "Export", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                For Each file In ReportResults.Paths
                    'System.Diagnostics.Process.Start(file)
                    Dim psi As New System.Diagnostics.ProcessStartInfo()
                    psi.FileName = file
                    psi.UseShellExecute = True
                    System.Diagnostics.Process.Start(psi)
                Next
            End If

            UcRecentReports1.LoadRecentReports()
        Catch ex As Exception
            DisplayErrorMessage("Error processing template", ex)
        End Try
    End Sub

    Private Sub BarButtonItem1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem1.ItemClick, BarButtonItem2.ItemClick, BarButtonItem3.ItemClick
        Try
            ProcessReport(GetFocusedReport(), GetFileType(e.Item.Caption))
        Catch ex As Exception
            DisplayErrorMessage("Error in BarButtonItem1_ItemClick", ex)
        End Try
    End Sub

    Private Function GetFileType(val As String) As FileType
        If val.EndsWith("(pdf)") Then
            Return FileType.Pdf
        ElseIf val.EndsWith("(txt)") Then
            Return FileType.Txt
        ElseIf val.EndsWith("(xlsx)") Then
            Return FileType.Xls
        Else
            Throw New NotImplementedException("{0} is not defined.".FormatWith(val))
        End If
    End Function

    Private Sub GridView1_GroupRowExpanding(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowAllowEventArgs) Handles GridView1.GroupRowExpanding
        If GridView1.FindFilterText.IsNullOrWhiteSpace Then
            GridView1.CollapseAllGroups()
        End If
    End Sub

    Private Sub GridView1_ColumnFilterChanged(sender As Object, e As EventArgs) Handles GridView1.ColumnFilterChanged
        If GridView1.FindFilterText.IsNotNullOrWhiteSpace Then
            GridView1.ExpandAllGroups()
        Else
            GridView1.CollapseAllGroups()
            GridView1.MakeRowVisible(1)
        End If
    End Sub

    Private Sub btnQtrEndReports_Click(sender As Object, e As EventArgs) Handles btnQtrEndReports.Click
        Try
            Dim frm = New frmQuarterEndEmailReports(Comp.CONUM)
            frm.MdiParent = MainForm
            frm.Show()
        Catch ex As Exception
            DisplayErrorMessage("Error in btnQtrEndReports_Click", ex)
        End Try
    End Sub

    Private Sub ReportEmailTeplateBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles ReportEmailTeplateBindingSource.CurrentChanged
        Try
            Dim row As ReportEmailTeplate = ReportEmailTeplateBindingSource.Current
            If row Is Nothing Then
                UcReportOptions1.SetReportId(Nothing, CoNum)
            Else
                UcReportOptions1.SetReportId(row, CoNum)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading report options", ex)
        End Try
    End Sub

    Public Sub SetUpdateZendeskTicketId(ticketId As Long)
        Try
            _reportProcessorQueue = New ReportProcessorQueue(CoNum, LastPrNum, LastCheckDate) With {.UpdateZendeskTicketId = ticketId}
            RefreshPendingReports(Nothing, Nothing)
        Catch ex As Exception
            DisplayErrorMessage("Error in SetUpdateZendeskTicketId", ex)
        End Try
    End Sub

    Private Async Sub QueueReport(report As ReportEmailTeplate)
        Try
            Await CanCreateTicketAsync()

            If _reportProcessorQueue Is Nothing Then
                _reportProcessorQueue = New ReportProcessorQueue(CoNum, LastPrNum, LastCheckDate)
            End If

            Dim results = Await _reportProcessorQueue.AddReport(New ReportProcessorQueue.RunReportOptions With {.AllowQueue = True, .ReportId = report.ID, .ShowParametersForm = True})
            If results.Cancel Then Exit Sub

            If results.SubmitToQueue Then
                'If Not Await a.AddReport(report.ID, True) Then Exit Sub
                _reportProcessorQueue.SubmitReports()
                Dim macroId As Decimal? = If(Not report.ApplyMacroToTicket.HasValue OrElse report.ApplyMacroToTicket = 0, GetUdfValue_AsDecimal("Zendesk_Reports_Apply_MacroId_Default"), report.ApplyMacroToTicket)
                Await _reportProcessorQueue.SendReports(EmailSubject, macroId)
                ucMyQueue.LoadReportsQueue()
                TabbedControlGroup1.SelectedTabPageIndex = 1
            End If

            RefreshPendingReports(Nothing, Nothing)

        Catch ex As Exception
            DisplayErrorMessage("Error processing report: {0}".FormatWith(report.Name), ex)
        End Try
    End Sub

    Private Sub RefreshPendingReports(obj As Object, args As EventArgs) Handles UcReportQueuePending1.OnReportRemoved
        Try
            If _reportProcessorQueue.PendingReports.Count > 0 OrElse _reportProcessorQueue.UpdateZendeskTicketId Then
                lciQueueReportsPending.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                UcReportQueuePending1.SetReportProcessorQueue(_reportProcessorQueue)
            Else
                lciQueueReportsPending.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in RefreshPendingReports", ex)
        End Try
    End Sub

    Private Async Sub OnSendReportsClicked(obj As Object, args As EventArgs) Handles UcReportQueuePending1.OnSendReportButtonClick
        Try
            _reportProcessorQueue.SubmitReports()

            Dim macroId As Decimal?
            Dim applyMacroToTicket As IEnumerable(Of Long?) = _reportProcessorQueue.PendingReports.Select(Function(e) e.ReportEmailTemplate.ApplyMacroToTicket).Distinct
            If applyMacroToTicket.Count = 1 AndAlso applyMacroToTicket.Single.HasValue AndAlso applyMacroToTicket.Single <> 0 Then
                macroId = applyMacroToTicket.Single
            Else
                macroId = GetUdfValue_AsDecimal("Zendesk_Reports_Apply_MacroId_Default")
            End If

            Await _reportProcessorQueue.SendReports(EmailSubject, macroId)
            ucMyQueue.LoadReportsQueue()
            TabbedControlGroup1.SelectedTabPageIndex = 1

            RefreshPendingReports(Nothing, Nothing)
        Catch ex As Exception
            DisplayErrorMessage("Error in OnSendReportsClicked", ex)
        End Try
    End Sub
End Class