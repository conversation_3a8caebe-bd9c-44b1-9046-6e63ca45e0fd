﻿Public Class ucCompNotes

    Private ReadOnly _coNum As Decimal
    Dim db As dbEPDataDataContext
    Public Sub New(coNum As Decimal, notes As List(Of NOTE))
        _coNum = coNum
        InitializeComponent()
        bsNotes.DataSource = notes
    End Sub

    'Private Async Sub ucCompNotes_Load(sender As Object, e As EventArgs) Handles MyBase.Load
    '    Try
    '        gvNotes.ShowLoadingPanel()
    '        Await Task.Run(Sub()
    '                           db = New dbEPDataDataContext(GetConnectionString)
    '                           Dim comp = (From c In db.COMPANies Where c.CONUM = _coNum).Single
    '                           bsNotes.DataSource = (From A In comp.NOTEs.ToList Where (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) AndAlso A.category <> "Employee").ToList
    '                       End Sub)
    '    Catch ex As Exception
    '        DisplayErrorMessage(ex, ex.Message)
    '    Finally
    '        gvNotes.HideLoadingPanel()
    '    End Try
    'End Sub
End Class
