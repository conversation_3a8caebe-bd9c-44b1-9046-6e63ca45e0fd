﻿Public Class ucSelectEmployeeRange
    Private Property EmployeeList As IEnumerable(Of Emp)

    Private Sub ucSelectEmployeeRange_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Public Sub SetConum(conum As Decimal, year As Integer)
        EmployeeList = modGlobals.Query(Of Emp)("SELECT e.CONUM, e.EMPNUM, e.L_NAME Lname, e.F_NAME Fname, ROW_NUMBER() OVER(ORDER BY e.L_NAME) Rownum
FROM EMPLOYEE e INNER JOIN (
	SELECT CM.CONUM, CM.EMPNUM FROM CHK_MAST AS CM INNER JOIN PAYROLL AS P ON CM.CONUM = P.CONUM AND CM.PAYROLL_NUM = P.PRNUM
	WHERE year(p.CHECK_DATE) = @year 
	GROUP BY CM.CONUM, CM.EMPNUM
	HAVING SUM(ISNULL(CM.NONTIP_TAX,0) + ISNULL(CM.TIP_TAX,0) + ISNULL(CM.MEDTAXABLE,0) + ISNULL(CM.STWH_TAXABLE,0) + ISNULL(CM.STRESWH_TAXABLE,0) + ISNULL(CM.FEDWH_TAXABLE,0)) <> 0
) ee ON e.CONUM = ee.CONUM AND e.EMPNUM = ee.EMPNUM
WHERE e.CONUM = @Conum
ORDER BY rownum", New With {.Conum = conum, .Year = year})

        lbcFromEmployee.DataSource = EmployeeList

    End Sub


    Private Class Emp
        Property Conum As Decimal
        Property Empnum As Decimal
        Property Lname As String
        Property Fname As String
        Property Rownum As Integer

        Public Overrides Function ToString() As String
            Return $"{Empnum} - {Lname} {Fname}"
        End Function
    End Class

    Private Sub lbcFromEmployee_SelectedValueChanged(sender As Object, e As EventArgs) Handles lbcFromEmployee.SelectedValueChanged
        Dim row As Emp = lbcFromEmployee.SelectedItem
        If row IsNot Nothing Then
            lbcToEmployee.DataSource = EmployeeList.Where(Function(r) r.Rownum >= row.Rownum)
        End If
    End Sub

    Friend Function GetEmpnumTo() As Integer?
        Dim emp As Emp = lbcToEmployee.SelectedValue
        Return emp?.Empnum
    End Function

    Friend Function GetEmpnumFrom() As Integer?
        Dim emp As Emp = lbcFromEmployee.SelectedValue
        Return emp?.Empnum
    End Function
End Class
