﻿Imports System.ComponentModel

Public Class frmManualBillingDraftDetails

    Private Property db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Transaction As DD_Manual_Trans_Log

    Sub New(id As Guid)
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        Transaction = db.DD_Manual_Trans_Logs.Single(Function(d) d.Transaction_NACHA_ID = id)
        dD_Manual_Trans_LogsBindingSource.DataSource = Transaction
    End Sub

    Private Sub btnUpdateStatus_Click(sender As Object, e As EventArgs) Handles btnUpdateStatus.Click
        Transaction.Transaction_Status = "Draft Failed"
        If db.SaveChanges Then
            DialogResult = DialogResult.OK
        End If
    End Sub
End Class