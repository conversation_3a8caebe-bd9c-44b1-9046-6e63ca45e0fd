﻿Imports DevExpress.XtraEditors

Public Class frmCovidEmailNotify
    Dim db As dbEPDataDataContext
    Private Sub frmCovidEmailNotify_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Sub LoadData()
        Try
            If db Is Nothing Then db = New dbEPDataDataContext(GetConnectionString())

            GCCovidEmailNotify.DataSource = db.view_CovidEmailNotifies.ToList()
            rilueClassification.ValueMember = "Choose"
            rilueClassification.DisplayMember = "Choose"
            rilueClassification.DropDownRows = 10
            rilueClassification.NullText = ""

            rilueClassification.DataSource = Query("SELECT sp.value Choose FROM custom.udf CROSS APPLY (SELECT	* FROM	STRING_SPLIT(value, '~'))sp WHERE name = 'CovidEmailNotifyClassificationList' ORDER BY 1")
        Catch ex As Exception
            DisplayErrorMessage("Error loading CovidEmailNotifies", ex)
        End Try
    End Sub

    Sub LoadDetail(CoNum As Decimal)
        Try
            Dim comp = db.COMPANies.SingleOrDefault(Function(c) c.CONUM = CoNum)
            Dim _empsCount = db.EMPLOYEEs.Where(Function(em) em.CONUM = CoNum AndAlso Not em.TERM_DATE.HasValue).Count
            Dim _coOptions = db.COOPTIONs.FirstOrDefault(Function(c) c.CONUM = CoNum)
            Me.UcCompInfo1.LoadDate(comp, _empsCount, _coOptions)

            'FollowUpBindingSource.DataSource = db.CovidEmailNotifyFollowUps.Where(Function(f) f.CoNum = CoNum).OrderByDescending(Function(o) o.ContactDate)
            FollowUpBindingSource.DataSource = db.CovidEmailNotifyFollowUps.Where(Function(f) f.CoNum = CoNum).OrderByDescending(Function(o) o.ContactDate).ToList()
        Catch ex As Exception
            DisplayErrorMessage("Error loading detail", ex)
        End Try
    End Sub

    Private Sub GVCovidEmailNotify_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GVCovidEmailNotify.FocusedRowObjectChanged
        Try
            If e.Row IsNot Nothing AndAlso e.RowHandle >= 0 Then
                Dim row As view_CovidEmailNotify = e.Row
                Dim CoNum = row.CONUM
                LoadDetail(CoNum)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading detail", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            db.SaveChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        End Try
    End Sub

    Private Sub GVCovidEmailNotify_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GVCovidEmailNotify.RowUpdated
        Try
            db.SubmitChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error updating row", ex)
        End Try
    End Sub

    Private Sub GVCovidEmailNotify_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GVCovidEmailNotify.ValidateRow
        Dim CR As view_CovidEmailNotify = GVCovidEmailNotify.GetRow(GVCovidEmailNotify.FocusedRowHandle)
        If db.view_CovidEmailNotifies.Where(Function(f) f.CONUM = CR.CONUM).Count = 0 Then
            Dim entry = New CovidEmailNotify With {.Classification = CR.Classification, .Cnt = CR.EmpCnt, .CONUM = CR.CONUM, .EmailAddress = CR.EmailAddress, .EmailContact = CR.EmailContact, .EmailSentOn = CR.EmailSentOn, .EmailSentOn2 = CR.EmailSentOn2, .NotQualified = CR.NotQualified, .FollowUpDate = CR.FollowUpDate}
            db.CovidEmailNotifies.InsertOnSubmit(entry)
        End If
    End Sub

    Private Sub GVCovidEmailNotify_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GVCovidEmailNotify.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As view_CovidEmailNotify = GVCovidEmailNotify.GetFocusedRow
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(row.CONUM), My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub DeleteRow(conum As Integer)
        Try
            If XtraMessageBox.Show("Are you sure you would like to delete this reocrd?", "Delete?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                Dim db = New dbEPDataDataContext(GetConnectionString)
                Dim row = db.CovidEmailNotifies.Single(Function(a) a.CONUM = conum)
                db.CovidEmailNotifies.DeleteOnSubmit(row)
                db.SaveChanges
                CovidEmailNotifyBindingSource.RemoveCurrent()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting row", ex)
        End Try
    End Sub

    Private Sub DeleteDetailRow(ID As Integer)
        Try
            If XtraMessageBox.Show("Are you sure you would like to delete this reocrd?", "Delete?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                Dim db = New dbEPDataDataContext(GetConnectionString)
                Dim row = db.CovidEmailNotifyFollowUps.Single(Function(a) a.ID = ID)
                db.CovidEmailNotifyFollowUps.DeleteOnSubmit(row)
                db.SaveChanges
                FollowUpBindingSource.RemoveCurrent()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting detail row", ex)
        End Try
    End Sub

    Private Sub GVFollowUp_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GVFollowUp.ValidateRow
        Dim CR As CovidEmailNotifyFollowUp = GVFollowUp.GetRow(GVFollowUp.FocusedRowHandle)
        If CR.ID = 0 Then 'new row
            CR.CoNum = DirectCast(GVCovidEmailNotify.GetRow(GVCovidEmailNotify.FocusedRowHandle), view_CovidEmailNotify).CONUM
            CR.CreatedBy = UserName
            CR.CreatedOn = DateTime.Now
            db.CovidEmailNotifyFollowUps.InsertOnSubmit(CR)
        End If
    End Sub

    Private Sub GVFollowUp_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GVFollowUp.RowUpdated
        Try
            db.SubmitChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error updating detail row", ex)
        End Try
    End Sub

    Private Sub GVFollowUp_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GVFollowUp.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As CovidEmailNotifyFollowUp = GVFollowUp.GetFocusedRow
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteDetailRow(row.ID), My.Resources.delete_16x16))
        End If
    End Sub
End Class