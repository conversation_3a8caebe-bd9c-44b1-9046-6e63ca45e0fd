﻿Imports System.ServiceModel.Description
Imports System.ServiceModel.Dispatcher

Public Class InspectorBehavior
    Implements IEndpointBehavior
    Public ReadOnly Property LastRequestXML() As String
        Get
            Return myMessageInspector.LastRequestXML
        End Get
    End Property

    Public ReadOnly Property LastResponseXML() As String
        Get
            Return myMessageInspector.LastResponseXML
        End Get
    End Property


    Private myMessageInspector As New MyMessageInspector()
    Public Sub AddBindingParameters(endpoint As ServiceEndpoint, bindingParameters As System.ServiceModel.Channels.BindingParameterCollection) Implements IEndpointBehavior.AddBindingParameters

    End Sub

    Public Sub ApplyDispatchBehavior(endpoint As ServiceEndpoint, endpointDispatcher As EndpointDispatcher) Implements IEndpointBehavior.ApplyDispatchBehavior

    End Sub

    Public Sub Validate(endpoint As ServiceEndpoint) Implements IEndpointBehavior.Validate

    End Sub


    Public Sub ApplyClientBehavior(endpoint As ServiceEndpoint, clientRuntime As ClientRuntime) Implements IEndpointBehavior.ApplyClientBehavior
        clientRuntime.MessageInspectors.Add(myMessageInspector)
    End Sub
End Class





Public Class MyMessageInspector
    Implements IClientMessageInspector
    Public Property LastRequestXML() As String
        Get
            Return m_LastRequestXML
        End Get
        Private Set
            m_LastRequestXML = Value
        End Set
    End Property
    Private m_LastRequestXML As String
    Public Property LastResponseXML() As String
        Get
            Return m_LastResponseXML
        End Get
        Private Set
            m_LastResponseXML = Value
        End Set
    End Property
    Private m_LastResponseXML As String
    Public Sub AfterReceiveReply(ByRef reply As System.ServiceModel.Channels.Message, correlationState As Object) Implements IClientMessageInspector.AfterReceiveReply
        LastResponseXML = reply.ToString()
    End Sub

    Public Function BeforeSendRequest(ByRef request As System.ServiceModel.Channels.Message, channel As System.ServiceModel.IClientChannel) As Object Implements IClientMessageInspector.BeforeSendRequest
        LastRequestXML = request.ToString()
        Return request
    End Function
End Class