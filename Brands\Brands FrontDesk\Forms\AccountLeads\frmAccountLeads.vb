﻿Imports System.ComponentModel
Imports System.IO
Imports Brands_FrontDesk
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Navigation
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports Microsoft.EntityFrameworkCore

Public Class frmAccountLeads

    Dim frmLogger As Serilog.ILogger = Logger.ForContext(Of frmAccountLeads)()
    Dim FilesPath = "I:\Account Leads"
    Private ReadOnly _AccountLeadsDataService As AccountLeadsDataService

    Sub New()
        InitializeComponent()
        frmLogger.Verbose("Openned frmAccountLeads")
        _AccountLeadsDataService = New AccountLeadsDataService
    End Sub

    Private Sub frmAccountLeads_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If LicenseManager.UsageMode = LicenseUsageMode.Designtime OrElse IsInDesignMode Then Exit Sub
        LoadData(True)
        gvAccountLeads.SetGridLayoutAndAddMenues("AccountLeads")
        AccordionControl1.SelectedElement = aceAnonymousSignup
    End Sub

    Private Sub LoadCounts()
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim list = db.view_AccountLeads.Where(Function(al) Not al.IsDeleted)
            For Each item In aceEnrollmentWeb.Elements
                item.Text = $"{item.Text.Split("(")(0).TrimEnd()} ({FilterData(item, list).Count()})"
            Next

            For Each item In aceAccountLeads.Elements
                item.Text = $"{item.Text.Split("(")(0).TrimEnd()} ({FilterData(item, list).Count()})"
            Next

            For Each item In AceLeadsByStatus.Elements
                item.Text = $"{item.Text.Split("(")(0).TrimEnd()} ({FilterData(item, list).Count()})"
            Next
        End Using
    End Sub

    Public Sub LoadData(fullRefresh As Boolean, Optional element As AccordionControlElement = Nothing)
        If LicenseManager.UsageMode = LicenseUsageMode.Designtime OrElse IsInDesignMode Then Exit Sub
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                If fullRefresh Then
                    Dim availableStatuses = GetUdfValue("AccountLeadsStatusList").Replace(vbCrLf, "~").Split("~")
                    UcAccountLeadDetails1.SetAvailableStatus(availableStatuses)
                    cbeCategory.Properties.Items.Clear()
                    cbeCategory.Properties.Items.Add("")
                    cbeCategory.Properties.Items.AddRange(GetUdfValue("AccountLeadsCategoryList").Replace(vbCrLf, "~").Split("~"))
                    bsCompanies.DataSource = db.view_CompanySumarries.ToList()
                    pmEmails.ClearLinks()
                    For Each emlTemplate In db.ReportEmailTeplates.Where(Function(t) t.Tag = "Leads").Include("ReportEmailTemplateFiles").ToList()
                        Dim bbi = New BarButtonItem(RibbonControl1.Manager, emlTemplate.Name) With {.Tag = emlTemplate}
                        AddHandler bbi.ItemClick, AddressOf bbiEmailClient_ItemClick
                        pmEmails.AddItem(bbi)
                    Next
                    AceLeadsByStatus.Elements.Clear()
                    For Each i In availableStatuses
                        AceLeadsByStatus.Elements.Add(New AccordionControlElement(ElementStyle.Item) With {
                            .Text = i,
                            .Tag = i
                        })
                    Next
                    LoadCounts()
                End If

                If element Is Nothing Then element = AccordionControl1.SelectedElement
                If element IsNot Nothing Then
                    Dim list As IQueryable(Of view_AccountLead) = FilterData(element, db.view_AccountLeads.Where(Function(al) Not al.IsDeleted))
                    If cbeCategory.Text.IsNotNullOrWhiteSpace Then list = list.Where(Function(al) al.Category = cbeCategory.Text)
                    Dim l = list.ToList()
                    bsAccountLead.DataSource = l
                End If
            End Using
        Catch ex As Exception
            frmLogger.Error(ex, "Error loading Data")
            DisplayErrorMessage("Error Loading Account Leads", ex)
        End Try
    End Sub

    Private Sub AccordionControl1_SelectedElementChanged(sender As Object, e As DevExpress.XtraBars.Navigation.SelectedElementChangedEventArgs) Handles AccordionControl1.SelectedElementChanged
        LoadData(False, e.Element)
    End Sub

    Private Function FilterData(element As AccordionControlElement, list As IQueryable(Of view_AccountLead)) As IQueryable(Of view_AccountLead)
        Dim filterByStatus As String = Nothing
        If element Is aceAnonymousSignup Then
            filterByStatus = "Anonymous Signup"
        ElseIf element Is acePendingMatchVerification Then
            filterByStatus = "Pending Match Verification"
        ElseIf element Is aceSignupInProcess Then
            filterByStatus = "Sign up in Process"
        ElseIf element Is aceSignupCompleted Then
            filterByStatus = "Signup Completed"
        ElseIf element Is aceAll Then
        ElseIf element Is aceFollowUp Then
            list = list.Where(Function(al) al.NextFollowUpDate < Now)
        ElseIf element Is aceQualifiedLead Then
            filterByStatus = "Qualified Lead"
        ElseIf element Is aceProspect Then
            filterByStatus = "Prospect"
        ElseIf element Is aceReadyToProcess Then
            filterByStatus = "Ready to Process"
        ElseIf element Is aceSetupInProcess Then
            filterByStatus = "Setup In Process"
        ElseIf element Is aceSetupDone Then
            filterByStatus = "Setup Done"
        Else
            filterByStatus = element.Tag
        End If

        If filterByStatus.IsNotNullOrWhiteSpace Then list = list.Where(Function(al) al.Status = filterByStatus)
        Return list
    End Function

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        frmLogger.Verbose("Clicked on refresh btn")
        LoadData(True)
    End Sub

    Private Sub bbiAddLead_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiAddLead.ItemClick
        frmLogger.Verbose("Clicked on Add Lead btn")
        Dim frm = New frmAccountLeadAddOrEdit
        AddHandler frm.FormClosed, Sub(obj As Object, args As FormClosedEventArgs)
                                       If frm.DialogResult = DialogResult.OK Then
                                           LoadData(False)
                                       End If
                                   End Sub
        MainForm.ShowForm(frm)
    End Sub

    Private Sub gcAccountLeads_DoubleClick(sender As Object, e As EventArgs) Handles gcAccountLeads.DoubleClick
        Dim row As view_AccountLead = gvAccountLeads.GetFocusedRow
        If row Is Nothing Then Exit Sub
        frmLogger.Verbose("Double Clicked on {ID}", row.ID)
        Try
            If row Is Nothing Then Exit Sub
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim lead = db.AccountLeads.Single(Function(l) l.ID = row.ID)
                Dim frm = New frmAccountLeadAddOrEdit(lead)
                AddHandler frm.FormClosed, Sub(obj As Object, args As FormClosedEventArgs)
                                               If frm.DialogResult = DialogResult.OK Then
                                                   LoadData(False)
                                               End If
                                           End Sub
                MainForm.ShowForm(frm)
            End Using
        Catch ex As Exception
            DisplayErrorMessage($"Error editing Account Lead ID: {row.ID}", ex)
        End Try
    End Sub

    Private Sub bbiEmailClient_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiEmail.ItemClick
        Try
            If e.Item.Tag Is Nothing Then Exit Sub
            lcRoot.ShowProgessPanel
            Dim row As view_AccountLead = gvAccountLeads.GetFocusedRow
            If row Is Nothing Then
                XtraMessageBox.Show("Please select a row.")
                Exit Sub
            End If
            Dim emlTemplate As ReportEmailTeplate = e.Item.Tag
            Using db = New dbEPDataDataContext(GetConnectionString)
                emlTemplate = db.ReportEmailTeplates.Single(Function(r) r.ID = emlTemplate.ID)
                frmLogger.Verbose("Clicked on email client. {ClientID} {TemplateName}", row.ID, emlTemplate.Name)
                Dim email = New ReportProcessor(749, emlTemplate, FileType.Pdf)
                email.DefaultParamValues = New List(Of KeyValuePair)
                email.DefaultParamValues.Add(New KeyValuePair("@CoNum", 749))
                email.DefaultParamValues.Add(New KeyValuePair("@ID", row.ID))
                email.showParametersForm = False
                Dim result = email.ProcessReport
                If result.AllFileExist AndAlso Not result.Cancalled Then
                    Dim destPath = System.IO.Path.Combine(FilesPath, row.ID)
                    If Not System.IO.Directory.Exists(destPath) Then
                        System.IO.Directory.CreateDirectory(destPath)
                    End If
                    For Each f In result.Paths
                        System.IO.File.Copy(f, System.IO.Path.Combine(destPath, System.IO.Path.GetFileNameWithoutExtension(f) & FormatedDate() & Path.GetExtension(f)))
                        'LoadFiles(row)
                    Next
                End If
                Dim emlSender = New ReportSender(result) With {.showEmailList = row.CoNum.HasValue, .showWebPost = False, .sendToEmailAddress = row.CoEmail}
                Dim user = db.DBUSERs.SingleOrDefault(Function(u) u.name.ToLower = UserName.ToLower)
                If emlSender.EmailReport(True) Then
                    Dim frm = New frmEditNotes(row.Notes, $"Email ({emlTemplate.Name}) sent to client.")
                    row.Notes = frm.GetNote
                    'SaveRowNotes(row)
                End If
            End Using
        Catch ex As Exception
            frmLogger.Error(ex, "Error in Email Client")
            DisplayErrorMessage("Error sending email.", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub btnAddFromExistingCoOk_Click(sender As Object, e As EventArgs) Handles btnAddFromExistingCoOk.Click
        Try
            Dim coNum As Decimal? = slueAddFromExistingCo.EditValue
            If coNum.HasValue AndAlso coNum.Value <> 0 Then
                PopupControlContainer1.HidePopup()
                Dim db = New dbEPDataDataContext(GetConnectionString)
                Dim comp = db.COMPANies.Single(Function(c) c.CONUM = coNum)
                Dim row = New AccountLead With {.CoNum = comp.CONUM, .CoEmail = comp.CO_EMAIL, .CoFax = comp.CO_FAX, .CoName = comp.CO_NAME, .CoCellNumber = comp.CO_MODEM, .ContactName = comp.PR_CONTACT, .CoPhoneNumber = comp.CO_PHONE, .CoPhoneNubmerExt = comp.CO_EXTENSION}
                row.NumberOfEmployees = (From em In db.EMPLOYEEs Where em.CONUM = comp.CONUM AndAlso Not em.TERM_DATE.HasValue).Count
                Dim frm = New frmAccountLeadAddOrEdit(row, True)
                AddHandler frm.FormClosed, Sub(obj As Object, args As FormClosedEventArgs)
                                               If frm.DialogResult = DialogResult.OK Then
                                                   LoadData(False)
                                               End If
                                           End Sub
                MainForm.ShowForm(frm)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error adding lead from exiting company", ex)
        End Try
    End Sub

    Private Sub cbeStatus_EditValueChanged(sender As Object, e As EventArgs) Handles cbeCategory.EditValueChanged
        frmLogger.Verbose("Changed filter")
        LoadData(False)
    End Sub

    Private Sub gvAccountLeads_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvAccountLeads.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As view_AccountLead = gvAccountLeads.GetFocusedRow
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(row.ID), My.Resources.delete_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Update From JotForms", Sub() UpdateFromJotForm(row), My.Resources.download_16x16))
        End If
    End Sub

    Private Sub UpdateFromJotForm(row As view_AccountLead)
        If Not row.JotFormsSubmissionId.HasValue Then
            XtraMessageBox.Show("This record is not linked to jot forms")
            Return
        End If
        Using frm = New frmDownloadSubmittions(row.JotFormsSubmissionId)
            If frm.ShowDialog = DialogResult.OK Then
                LoadData(True)
            End If
        End Using
    End Sub

    Private Sub DeleteRow(id As Integer)
        frmLogger.Verbose("Entering delete row {ID}", id)
        If XtraMessageBox.Show("Are you sure you would like to delete this reocrd?", "Delete?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim row = db.AccountLeads.Single(Function(a) a.ID = id)
            row.IsDeleted = True
            row.ChgDate = DateTime.Now
            row.ChgUser = UserName
            db.SaveChanges
            bsAccountLead.RemoveCurrent()
        End If
    End Sub

    Private Sub bsAccountLead_CurrentChanged(sender As Object, e As EventArgs) Handles bsAccountLead.CurrentChanged
        UcAccountLeadDetails1.SetDate(bsAccountLead.Current)
        HandleMoveToNextStatusButton()
    End Sub

    Private Sub HandleMoveToNextStatusButton()
        bbiMoveToNextStatus.Tag = Nothing
        Dim row As view_AccountLead = bsAccountLead.Current
        If row Is Nothing Then
            bbiMoveToNextStatus.Tag = Nothing
        Else
            Dim nextStatus = If(row.Enrollment_Workflow <> "5 - Finish", "Sign up in Process", "Signup Completed")
            If row.Status = "Anonymous Signup" Then
                bbiMoveToNextStatus.Caption = $"Move To {nextStatus}"
                bbiMoveToNextStatus.Tag = New MoveToNextStatus With {.Status = nextStatus, .RemoveItem = Me.AccordionControl1.SelectedElement Is aceAnonymousSignup, .Row = row}
            ElseIf row.Status = "Pending Match Verification" Then
                bbiMoveToNextStatus.Caption = $"Move To {nextStatus}"
                bbiMoveToNextStatus.Tag = New MoveToNextStatus With {.Status = nextStatus, .RemoveItem = Me.AccordionControl1.SelectedElement Is acePendingMatchVerification, .Row = row}
            End If
        End If
        bbiMoveToNextStatus.Visibility = (bbiMoveToNextStatus.Tag IsNot Nothing).ToBarItemVisibility
    End Sub

    Private Sub bbiMoveToNextStatus_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiMoveToNextStatus.ItemClick
        Dim _moveToNextStatus As MoveToNextStatus = e.Item.Tag
        _AccountLeadsDataService.UpdateStatus(_moveToNextStatus.Row, _moveToNextStatus.Status)
        If _moveToNextStatus.RemoveItem Then
            bsAccountLead.Remove(_moveToNextStatus.Row)
        Else
            UcAccountLeadDetails1.SetDate(_moveToNextStatus.Row)
        End If
    End Sub

    Private Class MoveToNextStatus
        Property Row As view_AccountLead
        Property Status As String
        Property RemoveItem As Boolean
    End Class

    Private Sub BbiFieldsMapping_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiFieldsMapping.ItemClick
        Try
            Using frm = New frmFormFieldMap()
                frm.ShowDialog()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error opening Form Field Mapping", ex)
        End Try
    End Sub

    Private Sub BbiDownloadNewSubmittions_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiDownloadNewSubmittions.ItemClick
        Using frm = New frmDownloadSubmittions("")
            If frm.ShowDialog = DialogResult.OK Then
                LoadData(True)
            End If
        End Using
    End Sub
End Class