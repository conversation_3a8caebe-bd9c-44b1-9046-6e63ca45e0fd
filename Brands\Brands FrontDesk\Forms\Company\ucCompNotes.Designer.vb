﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucCompNotes
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.gcNotes = New DevExpress.XtraGrid.GridControl()
        Me.bsNotes = New System.Windows.Forms.BindingSource()
        Me.gvNotes = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colcategory = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.coltitle = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colnote = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.gcNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'gcNotes
        '
        Me.gcNotes.DataSource = Me.bsNotes
        Me.gcNotes.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gcNotes.Location = New System.Drawing.Point(0, 0)
        Me.gcNotes.MainView = Me.gvNotes
        Me.gcNotes.Name = "gcNotes"
        Me.gcNotes.Size = New System.Drawing.Size(637, 334)
        Me.gcNotes.TabIndex = 0
        Me.gcNotes.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvNotes})
        '
        'bsNotes
        '
        Me.bsNotes.DataSource = GetType(Brands_FrontDesk.NOTE)
        '
        'gvNotes
        '
        Me.gvNotes.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colcategory, Me.coltitle, Me.colnote})
        Me.gvNotes.GridControl = Me.gcNotes
        Me.gvNotes.Name = "gvNotes"
        Me.gvNotes.OptionsBehavior.Editable = False
        Me.gvNotes.OptionsBehavior.ReadOnly = True
        Me.gvNotes.OptionsView.ColumnAutoWidth = False
        Me.gvNotes.OptionsView.ShowGroupPanel = False
        '
        'colcategory
        '
        Me.colcategory.FieldName = "category"
        Me.colcategory.MinWidth = 60
        Me.colcategory.Name = "colcategory"
        Me.colcategory.Visible = True
        Me.colcategory.VisibleIndex = 0
        Me.colcategory.Width = 60
        '
        'coltitle
        '
        Me.coltitle.FieldName = "title"
        Me.coltitle.Name = "coltitle"
        Me.coltitle.Visible = True
        Me.coltitle.VisibleIndex = 1
        Me.coltitle.Width = 279
        '
        'colnote
        '
        Me.colnote.FieldName = "ParsedNote"
        Me.colnote.Name = "colnote"
        Me.colnote.Visible = True
        Me.colnote.VisibleIndex = 2
        Me.colnote.Width = 279
        '
        'ucCompNotes
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.gcNotes)
        Me.Name = "ucCompNotes"
        Me.Size = New System.Drawing.Size(637, 334)
        CType(Me.gcNotes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsNotes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvNotes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents gcNotes As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvNotes As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents bsNotes As System.Windows.Forms.BindingSource
    Friend WithEvents coltitle As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colnote As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colcategory As DevExpress.XtraGrid.Columns.GridColumn

End Class
