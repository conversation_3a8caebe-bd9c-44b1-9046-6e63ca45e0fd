﻿Imports System.ComponentModel
Public Class frmRenumberEmployees

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoCode As Decimal

    Dim db As New dbEPDataDataContext(GetConnectionString)
    Dim data As List(Of RenumberEmployee)

    Private Sub frmRenumberEmployees_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.lblCoNum.Text = "Co #: " & Me.CoCode
        data = (From A In db.RenumberEmployees Where A.CoNum = CoCode AndAlso A.IsProcessed = False).ToList
        For Each itm In data
            GetEmpName(itm)
        Next
        Me.RenumberEmployeeBindingSource.DataSource = data
        'Me.GridControl1.DataSource = data
    End Sub

    Sub GetEmpName(row As RenumberEmployee)
        Dim emp = (From A In db.EMPLOYEEs Where A.CONUM = Me.CoCode AndAlso A.EMPNUM = row.EmpNum Select A.F_NAME, <PERSON><PERSON>_<PERSON>, <PERSON><PERSON>_<PERSON>, <PERSON><PERSON>).FirstOrDefault
        If emp IsNot Nothing Then
            row.EmployeeName = emp.F_NAME & " " & If(emp.M_NAME.IsNullOrWhiteSpace, emp.M_NAME & " ", "") & emp.L_NAME
            row.SSN = emp.SSN
        Else
            row.EmployeeName = "<<< Employee Not Found >>>"
        End If
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", click:=Sub()
                                                                                       Dim row As RenumberEmployee = Me.GridView1.GetRow(e.HitInfo.RowHandle)
                                                                                       If row.ID > 0 Then
                                                                                           db.RenumberEmployees.DeleteOnSubmit(row)
                                                                                       End If
                                                                                       data.Remove(row)
                                                                                       Me.GridView1.RefreshData()
                                                                                   End Sub))
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        For Each row In Me.data
            If row.ID = 0 Then
                row.CoNum = Me.CoCode
                row.EnteredBy = UserName
                row.EnteredDate = Now
                db.RenumberEmployees.InsertOnSubmit(row)
            End If
        Next
        db.SaveChanges()
        Me.DialogResult = DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub GridView1_ShowingEditor(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles GridView1.ShowingEditor
        Dim row As RenumberEmployee = Me.GridView1.GetFocusedRow
        If (row Is Nothing OrElse row.EmpNum = 0) AndAlso Me.GridView1.FocusedColumn.FieldName <> "EmpNum" AndAlso Me.GridView1.FocusedColumn.FieldName <> "NewCoNum" Then
            e.Cancel = True
        End If
    End Sub

    Private Sub GridView1_ValidatingEditor(sender As Object, e As DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs) Handles GridView1.ValidatingEditor
        If Me.GridView1.FocusedColumn.FieldName = "NewEmpNum" AndAlso e.Value IsNot Nothing Then
            Dim NewNum As Decimal = e.Value
            Dim InCoNum As Decimal = Me.CoCode
            Dim NewCoNum As Decimal? = Me.GridView1.GetFocusedRowCellValue("NewCoNum")
            If NewCoNum.HasValue Then InCoNum = NewCoNum.Value
            Dim Exist = (From A In db.EMPLOYEEs Where A.CONUM = InCoNum AndAlso A.EMPNUM = NewNum).Any
            If Not Exist Then Exist = (From A In data Where A.NewEmpNum = NewNum).Any
            If Exist Then
                DisplayMessageBox("The employee number " & e.Value & " is already in use")
                e.Valid = False
            End If
        ElseIf Me.GridView1.FocusedColumn.FieldName = "NewCoNum" AndAlso e.Value IsNot Nothing Then
            Dim NewNum As Decimal = e.Value
            Dim Exist = (From A In db.COMPANies Where A.CONUM = NewNum).Any
            If Not Exist Then
                DisplayMessageBox("Company # " & e.Value & " not found") ', New Exception("Company # " & e.Value & " not found"))
                'e.Valid = False
            End If
        End If
    End Sub

    Private Sub GridView1_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridView1.ValidateRow
        Dim row As RenumberEmployee = Me.GridView1.GetFocusedRow
        If row.NewEmpNum = 0 OrElse row.EmpNum = 0 OrElse row.NewCoNum.GetValueOrDefault = 0 Then
            e.Valid = False
            e.ErrorText = ""
        End If
    End Sub

    Private Sub GridView1_InvalidRowException(sender As Object, e As DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs) Handles GridView1.InvalidRowException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore
    End Sub

    Private Sub GridView1_InvalidValueException(sender As Object, e As DevExpress.XtraEditors.Controls.InvalidValueExceptionEventArgs) Handles GridView1.InvalidValueException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction
    End Sub

    Private Sub GridView1_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridView1.CellValueChanged
        If e.Column.FieldName = "EmpNum" Then
            If e.Value IsNot Nothing AndAlso e.Value > 0 Then
                GetEmpName(Me.GridView1.GetFocusedRow)
            Else
                Me.GridView1.SetFocusedRowCellValue("EmployeeName", Nothing)
                Me.GridView1.SetFocusedRowCellValue("SSN", Nothing)
            End If
        ElseIf e.Column.FieldName = "NewCoNum" AndAlso e.Value IsNot Nothing Then
            Dim empnum = Me.GridView1.GetFocusedRowCellValue("EmpNum")
            If empnum IsNot Nothing Then
                Me.GridView1.SetFocusedRowCellValue("EmpNum", 0)
            End If
        End If
    End Sub

    Private Sub GridView1_InitNewRow(sender As Object, e As DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs) Handles GridView1.InitNewRow
        'Me.GridView1.SetFocusedRowCellValue("NewCoNum", Me.CoCode)
    End Sub

    Private Sub btnPasteFromExcel_Click(sender As Object, e As EventArgs) Handles btnPasteFromExcel.Click
        GridView1.OptionsClipboard.PasteMode = DevExpress.Export.PasteMode.Append
        GridView1.PasteFromClipboard()
    End Sub
End Class