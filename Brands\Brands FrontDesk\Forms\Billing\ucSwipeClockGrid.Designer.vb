﻿Namespace Billing
    <Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
    Partial Class ucSwipeClockGrid
        Inherits DevExpress.XtraEditors.XtraUserControl

        'UserControl overrides dispose to clean up the component list.
        <System.Diagnostics.DebuggerNonUserCode()>
        Protected Overrides Sub Dispose(ByVal disposing As Boolean)
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
            MyBase.Dispose(disposing)
        End Sub

        'Required by the Windows Form Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Windows Form Designer
        'It can be modified using the Windows Form Designer.  
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()>
        Private Sub InitializeComponent()
            Dim GridFormatRule1 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression1 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule2 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression2 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule3 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression3 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule4 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression4 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule5 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression5 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule6 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression6 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule7 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression7 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule8 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression8 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule9 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression9 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Dim GridFormatRule10 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
            Dim FormatConditionRuleExpression10 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
            Me.colTagInvNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAftClcMonInvNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colMarkup = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colNotes = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colRowType = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colModified = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.RepositoryItemCheckEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit()
            Me.colConum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
            Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colLineItem = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colItemDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colRate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colTaxRate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colExtDescription = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colClient = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEmployees = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colClocks = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAdditionalUsers = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPriceForEmployees = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPriceForClocks = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPriceForBaseFee = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPriceForAdditionalUsers = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colTotalPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colTotalAmount = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_BILL_FREQ = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colDescription = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsPriceForEmployeesInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsPriceForClocksInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsPriceForAdditionalUsersInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsAllInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.SuspendLayout()
            '
            'colTagInvNum
            '
            Me.colTagInvNum.Caption = "GridColumn1"
            Me.colTagInvNum.FieldName = "TagInvNum"
            Me.colTagInvNum.Name = "colTagInvNum"
            Me.colTagInvNum.OptionsColumn.AllowEdit = False
            '
            'colAftClcMonInvNum
            '
            Me.colAftClcMonInvNum.Caption = "MonInvNum"
            Me.colAftClcMonInvNum.FieldName = "AftClcMonInvNum"
            Me.colAftClcMonInvNum.Name = "colAftClcMonInvNum"
            Me.colAftClcMonInvNum.OptionsColumn.AllowEdit = False
            Me.colAftClcMonInvNum.Visible = True
            Me.colAftClcMonInvNum.VisibleIndex = 13
            '
            'colMarkup
            '
            Me.colMarkup.Caption = "Markup"
            Me.colMarkup.DisplayFormat.FormatString = "#0.00%"
            Me.colMarkup.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom
            Me.colMarkup.FieldName = "Markup"
            Me.colMarkup.Name = "colMarkup"
            Me.colMarkup.OptionsColumn.AllowEdit = False
            Me.colMarkup.Visible = True
            Me.colMarkup.VisibleIndex = 12
            '
            'colNotes
            '
            Me.colNotes.Caption = "Notes"
            Me.colNotes.FieldName = "Notes"
            Me.colNotes.Name = "colNotes"
            Me.colNotes.Visible = True
            Me.colNotes.VisibleIndex = 16
            '
            'colRowType
            '
            Me.colRowType.Caption = "RowType"
            Me.colRowType.FieldName = "RowType"
            Me.colRowType.Name = "colRowType"
            Me.colRowType.OptionsColumn.AllowEdit = False
            Me.colRowType.Visible = True
            Me.colRowType.VisibleIndex = 1
            Me.colRowType.Width = 60
            '
            'colModified
            '
            Me.colModified.Caption = "Modified"
            Me.colModified.ColumnEdit = Me.RepositoryItemCheckEdit1
            Me.colModified.FieldName = "Modified"
            Me.colModified.Name = "colModified"
            Me.colModified.OptionsColumn.AllowEdit = False
            Me.colModified.OptionsColumn.ReadOnly = True
            Me.colModified.Visible = True
            Me.colModified.VisibleIndex = 21
            '
            'RepositoryItemCheckEdit1
            '
            Me.RepositoryItemCheckEdit1.AutoHeight = False
            Me.RepositoryItemCheckEdit1.Name = "RepositoryItemCheckEdit1"
            '
            'colConum
            '
            Me.colConum.FieldName = "Conum"
            Me.colConum.Name = "colConum"
            Me.colConum.OptionsColumn.AllowEdit = False
            Me.colConum.Visible = True
            Me.colConum.VisibleIndex = 2
            '
            'GridControl1
            '
            Me.GridControl1.Dock = System.Windows.Forms.DockStyle.Fill
            Me.GridControl1.Location = New System.Drawing.Point(0, 0)
            Me.GridControl1.MainView = Me.GridView1
            Me.GridControl1.Name = "GridControl1"
            Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemCheckEdit1})
            Me.GridControl1.Size = New System.Drawing.Size(1013, 569)
            Me.GridControl1.TabIndex = 0
            Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
            '
            'GridView1
            '
            Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colRowType, Me.colConum, Me.colLineItem, Me.colItemDate, Me.colRate, Me.colTaxRate, Me.colExtDescription, Me.colClient, Me.colEmployees, Me.colClocks, Me.colAdditionalUsers, Me.colPriceForEmployees, Me.colPriceForClocks, Me.colPriceForBaseFee, Me.colPriceForAdditionalUsers, Me.colTotalPrice, Me.colTotalAmount, Me.colMarkup, Me.colAftClcMonInvNum, Me.colCO_BILL_FREQ, Me.colDescription, Me.colNotes, Me.colIsPriceForEmployeesInvoiced, Me.colIsPriceForClocksInvoiced, Me.colIsPriceForAdditionalUsersInvoiced, Me.colIsAllInvoiced, Me.colTagInvNum, Me.colModified})
            GridFormatRule1.Column = Me.colTagInvNum
            GridFormatRule1.ColumnApplyTo = Me.colAftClcMonInvNum
            GridFormatRule1.Name = "FormatInvoiceNum"
            FormatConditionRuleExpression1.Appearance.BackColor = System.Drawing.Color.Yellow
            FormatConditionRuleExpression1.Appearance.Options.UseBackColor = True
            FormatConditionRuleExpression1.Expression = "[TagInvNum] = True"
            GridFormatRule1.Rule = FormatConditionRuleExpression1
            GridFormatRule2.Column = Me.colTagInvNum
            GridFormatRule2.ColumnApplyTo = Me.colAftClcMonInvNum
            GridFormatRule2.Name = "FormatInvoiceNumDef"
            FormatConditionRuleExpression2.Expression = "[TagInvNum] = False"
            GridFormatRule2.Rule = FormatConditionRuleExpression2
            GridFormatRule3.Column = Me.colMarkup
            GridFormatRule3.ColumnApplyTo = Me.colMarkup
            GridFormatRule3.Name = "FormatMarkupRed"
            FormatConditionRuleExpression3.Appearance.BackColor = System.Drawing.Color.Red
            FormatConditionRuleExpression3.Appearance.Options.UseBackColor = True
            FormatConditionRuleExpression3.Expression = "[Markup] < 0"
            GridFormatRule3.Rule = FormatConditionRuleExpression3
            GridFormatRule3.StopIfTrue = True
            GridFormatRule4.Column = Me.colMarkup
            GridFormatRule4.ColumnApplyTo = Me.colMarkup
            GridFormatRule4.Name = "FormatMarkupPink"
            FormatConditionRuleExpression4.Appearance.BackColor = System.Drawing.Color.Pink
            FormatConditionRuleExpression4.Appearance.Options.UseBackColor = True
            FormatConditionRuleExpression4.Expression = "[Markup] < 2"
            GridFormatRule4.Rule = FormatConditionRuleExpression4
            GridFormatRule4.StopIfTrue = True
            GridFormatRule5.Column = Me.colMarkup
            GridFormatRule5.ColumnApplyTo = Me.colMarkup
            GridFormatRule5.Name = "FormatMarkupYellow"
            FormatConditionRuleExpression5.Appearance.BackColor = System.Drawing.Color.Yellow
            FormatConditionRuleExpression5.Appearance.Options.UseBackColor = True
            FormatConditionRuleExpression5.Expression = "[Markup] < 3"
            GridFormatRule5.Rule = FormatConditionRuleExpression5
            GridFormatRule5.StopIfTrue = True
            GridFormatRule6.Column = Me.colMarkup
            GridFormatRule6.ColumnApplyTo = Me.colMarkup
            GridFormatRule6.Name = "FormatMarkupDefault"
            FormatConditionRuleExpression6.Expression = "[Markup] >= 3"
            GridFormatRule6.Rule = FormatConditionRuleExpression6
            GridFormatRule7.Column = Me.colNotes
            GridFormatRule7.ColumnApplyTo = Me.colRowType
            GridFormatRule7.Name = "FormatHasNoNotes"
            FormatConditionRuleExpression7.Expression = "IsNullOrEmpty([Notes])"
            GridFormatRule7.Rule = FormatConditionRuleExpression7
            GridFormatRule7.StopIfTrue = True
            GridFormatRule8.Column = Me.colNotes
            GridFormatRule8.ColumnApplyTo = Me.colRowType
            GridFormatRule8.Name = "FormatHasNotes"
            FormatConditionRuleExpression8.Appearance.BackColor = System.Drawing.Color.Yellow
            FormatConditionRuleExpression8.Appearance.Options.UseBackColor = True
            FormatConditionRuleExpression8.Expression = "IsNullOrEmpty([Notes]) = False"
            GridFormatRule8.Rule = FormatConditionRuleExpression8
            GridFormatRule8.StopIfTrue = True
            GridFormatRule9.Column = Me.colModified
            GridFormatRule9.ColumnApplyTo = Me.colClient
            GridFormatRule9.Name = "FormatWasModified"
            FormatConditionRuleExpression9.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(0, Byte), Integer))
            FormatConditionRuleExpression9.Appearance.Options.UseBackColor = True
            FormatConditionRuleExpression9.Expression = "[Modified] = True"
            GridFormatRule9.Rule = FormatConditionRuleExpression9
            GridFormatRule9.StopIfTrue = True
            GridFormatRule10.Column = Me.colModified
            GridFormatRule10.ColumnApplyTo = Me.colClient
            GridFormatRule10.Name = "FormatWasNotModified"
            FormatConditionRuleExpression10.Expression = "[Modified] = False"
            GridFormatRule10.Rule = FormatConditionRuleExpression10
            GridFormatRule10.StopIfTrue = True
            Me.GridView1.FormatRules.Add(GridFormatRule1)
            Me.GridView1.FormatRules.Add(GridFormatRule2)
            Me.GridView1.FormatRules.Add(GridFormatRule3)
            Me.GridView1.FormatRules.Add(GridFormatRule4)
            Me.GridView1.FormatRules.Add(GridFormatRule5)
            Me.GridView1.FormatRules.Add(GridFormatRule6)
            Me.GridView1.FormatRules.Add(GridFormatRule7)
            Me.GridView1.FormatRules.Add(GridFormatRule8)
            Me.GridView1.FormatRules.Add(GridFormatRule9)
            Me.GridView1.FormatRules.Add(GridFormatRule10)
            Me.GridView1.GridControl = Me.GridControl1
            Me.GridView1.Name = "GridView1"
            Me.GridView1.OptionsMenu.ShowGroupSummaryEditorItem = True
            Me.GridView1.OptionsMenu.ShowSummaryItemMode = DevExpress.Utils.DefaultBoolean.[True]
            Me.GridView1.OptionsSelection.MultiSelect = True
            Me.GridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect
            Me.GridView1.OptionsView.ColumnAutoWidth = False
            Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
            Me.GridView1.OptionsView.EnableAppearanceOddRow = True
            Me.GridView1.OptionsView.ShowFooter = True
            Me.GridView1.OptionsView.ShowGroupPanel = False
            '
            'colLineItem
            '
            Me.colLineItem.FieldName = "LineItem"
            Me.colLineItem.Name = "colLineItem"
            Me.colLineItem.OptionsColumn.AllowEdit = False
            '
            'colItemDate
            '
            Me.colItemDate.FieldName = "ItemDate"
            Me.colItemDate.Name = "colItemDate"
            Me.colItemDate.OptionsColumn.AllowEdit = False
            '
            'colRate
            '
            Me.colRate.FieldName = "Rate"
            Me.colRate.Name = "colRate"
            Me.colRate.OptionsColumn.AllowEdit = False
            '
            'colTaxRate
            '
            Me.colTaxRate.FieldName = "TaxRate"
            Me.colTaxRate.Name = "colTaxRate"
            Me.colTaxRate.OptionsColumn.AllowEdit = False
            '
            'colExtDescription
            '
            Me.colExtDescription.FieldName = "ExtDescription"
            Me.colExtDescription.Name = "colExtDescription"
            Me.colExtDescription.OptionsColumn.AllowEdit = False
            '
            'colClient
            '
            Me.colClient.FieldName = "Client"
            Me.colClient.Name = "colClient"
            Me.colClient.OptionsColumn.AllowEdit = False
            Me.colClient.Visible = True
            Me.colClient.VisibleIndex = 3
            Me.colClient.Width = 89
            '
            'colEmployees
            '
            Me.colEmployees.FieldName = "Employees"
            Me.colEmployees.Name = "colEmployees"
            Me.colEmployees.OptionsColumn.AllowEdit = False
            Me.colEmployees.Visible = True
            Me.colEmployees.VisibleIndex = 4
            '
            'colClocks
            '
            Me.colClocks.FieldName = "Clocks"
            Me.colClocks.Name = "colClocks"
            Me.colClocks.OptionsColumn.AllowEdit = False
            Me.colClocks.Visible = True
            Me.colClocks.VisibleIndex = 5
            '
            'colAdditionalUsers
            '
            Me.colAdditionalUsers.FieldName = "AdditionalUsers"
            Me.colAdditionalUsers.Name = "colAdditionalUsers"
            Me.colAdditionalUsers.OptionsColumn.AllowEdit = False
            Me.colAdditionalUsers.Visible = True
            Me.colAdditionalUsers.VisibleIndex = 6
            '
            'colPriceForEmployees
            '
            Me.colPriceForEmployees.DisplayFormat.FormatString = "c"
            Me.colPriceForEmployees.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colPriceForEmployees.FieldName = "PriceForEmployees"
            Me.colPriceForEmployees.Name = "colPriceForEmployees"
            Me.colPriceForEmployees.OptionsColumn.AllowEdit = False
            Me.colPriceForEmployees.Visible = True
            Me.colPriceForEmployees.VisibleIndex = 7
            '
            'colPriceForClocks
            '
            Me.colPriceForClocks.DisplayFormat.FormatString = "c"
            Me.colPriceForClocks.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colPriceForClocks.FieldName = "PriceForClocks"
            Me.colPriceForClocks.Name = "colPriceForClocks"
            Me.colPriceForClocks.OptionsColumn.AllowEdit = False
            '
            'colPriceForBaseFee
            '
            Me.colPriceForBaseFee.DisplayFormat.FormatString = "c"
            Me.colPriceForBaseFee.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colPriceForBaseFee.FieldName = "PriceForBaseFee"
            Me.colPriceForBaseFee.Name = "colPriceForBaseFee"
            Me.colPriceForBaseFee.OptionsColumn.AllowEdit = False
            Me.colPriceForBaseFee.Visible = True
            Me.colPriceForBaseFee.VisibleIndex = 8
            '
            'colPriceForAdditionalUsers
            '
            Me.colPriceForAdditionalUsers.DisplayFormat.FormatString = "c"
            Me.colPriceForAdditionalUsers.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colPriceForAdditionalUsers.FieldName = "PriceForAdditionalUsers"
            Me.colPriceForAdditionalUsers.Name = "colPriceForAdditionalUsers"
            Me.colPriceForAdditionalUsers.OptionsColumn.AllowEdit = False
            Me.colPriceForAdditionalUsers.Visible = True
            Me.colPriceForAdditionalUsers.VisibleIndex = 9
            '
            'colTotalPrice
            '
            Me.colTotalPrice.DisplayFormat.FormatString = "c"
            Me.colTotalPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colTotalPrice.FieldName = "TotalPrice"
            Me.colTotalPrice.Name = "colTotalPrice"
            Me.colTotalPrice.OptionsColumn.AllowEdit = False
            Me.colTotalPrice.Visible = True
            Me.colTotalPrice.VisibleIndex = 10
            '
            'colTotalAmount
            '
            Me.colTotalAmount.Caption = "SC Billed"
            Me.colTotalAmount.FieldName = "TotalAmount"
            Me.colTotalAmount.Name = "colTotalAmount"
            Me.colTotalAmount.OptionsColumn.AllowEdit = False
            Me.colTotalAmount.Visible = True
            Me.colTotalAmount.VisibleIndex = 11
            '
            'colCO_BILL_FREQ
            '
            Me.colCO_BILL_FREQ.FieldName = "CO_BILL_FREQ"
            Me.colCO_BILL_FREQ.Name = "colCO_BILL_FREQ"
            Me.colCO_BILL_FREQ.OptionsColumn.AllowEdit = False
            Me.colCO_BILL_FREQ.Visible = True
            Me.colCO_BILL_FREQ.VisibleIndex = 14
            '
            'colDescription
            '
            Me.colDescription.FieldName = "Description"
            Me.colDescription.Name = "colDescription"
            Me.colDescription.OptionsColumn.AllowEdit = False
            Me.colDescription.Visible = True
            Me.colDescription.VisibleIndex = 15
            '
            'colIsPriceForEmployeesInvoiced
            '
            Me.colIsPriceForEmployeesInvoiced.FieldName = "EmployeesInvoiced"
            Me.colIsPriceForEmployeesInvoiced.Name = "colIsPriceForEmployeesInvoiced"
            Me.colIsPriceForEmployeesInvoiced.OptionsColumn.AllowEdit = False
            Me.colIsPriceForEmployeesInvoiced.Visible = True
            Me.colIsPriceForEmployeesInvoiced.VisibleIndex = 17
            '
            'colIsPriceForClocksInvoiced
            '
            Me.colIsPriceForClocksInvoiced.FieldName = "ClocksInvoiced"
            Me.colIsPriceForClocksInvoiced.Name = "colIsPriceForClocksInvoiced"
            Me.colIsPriceForClocksInvoiced.OptionsColumn.AllowEdit = False
            Me.colIsPriceForClocksInvoiced.Visible = True
            Me.colIsPriceForClocksInvoiced.VisibleIndex = 18
            '
            'colIsPriceForAdditionalUsersInvoiced
            '
            Me.colIsPriceForAdditionalUsersInvoiced.FieldName = "AdditionalUsersInvoiced"
            Me.colIsPriceForAdditionalUsersInvoiced.Name = "colIsPriceForAdditionalUsersInvoiced"
            Me.colIsPriceForAdditionalUsersInvoiced.OptionsColumn.AllowEdit = False
            Me.colIsPriceForAdditionalUsersInvoiced.Visible = True
            Me.colIsPriceForAdditionalUsersInvoiced.VisibleIndex = 19
            '
            'colIsAllInvoiced
            '
            Me.colIsAllInvoiced.FieldName = "AllInvoiced"
            Me.colIsAllInvoiced.Name = "colIsAllInvoiced"
            Me.colIsAllInvoiced.OptionsColumn.AllowEdit = False
            Me.colIsAllInvoiced.Visible = True
            Me.colIsAllInvoiced.VisibleIndex = 20
            '
            'ucSwipeClockGrid
            '
            Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.Controls.Add(Me.GridControl1)
            Me.Name = "ucSwipeClockGrid"
            Me.Size = New System.Drawing.Size(1013, 569)
            CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
            Me.ResumeLayout(False)

        End Sub

        Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
        Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents colConum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colLineItem As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colItemDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colDescription As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colRate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colTaxRate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colTotalAmount As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colExtDescription As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colClient As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEmployees As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colClocks As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colAdditionalUsers As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPriceForEmployees As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPriceForClocks As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPriceForBaseFee As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPriceForAdditionalUsers As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colTotalPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsPriceForEmployeesInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsPriceForClocksInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsPriceForAdditionalUsersInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsAllInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colRowType As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colAftClcMonInvNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_BILL_FREQ As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colTagInvNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colMarkup As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colNotes As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colModified As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents RepositoryItemCheckEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit
    End Class
End Namespace