﻿Class DateFunctions

    Public Shared Property DateRangeOptions As List(Of String) = New List(Of String)(New String() {"Month-To-Date", "Current Month", "Previous Month", "Quarter-To-Date", "Current Quarter", "Previous Quarter", "Year-To-Date", "Current Year", "Previous Year", "Custom"})
    Public Shared Sub GetDateRange(range As String, ByRef FromDate As DateTime, ByRef ToDate As DateTime)
        Dim curDate = DateTime.Now
        If range = "Month-To-Date" Then
            FromDate = New DateTime(curDate.Year, curDate.Month, 1)
            ToDate = curDate
        ElseIf range = "Current Month" Then
            FromDate = New DateTime(curDate.Year, curDate.Month, 1)
            ToDate = New DateTime(curDate.Year, curDate.Month, DateTime.DaysInMonth(curDate.Year, curDate.Month))
        ElseIf range = "Previous Month" Then
            If curDate.Month = 1 Then
                FromDate = New DateTime(curDate.Year - 1, 12, 1)
                ToDate = New DateTime(curDate.Year - 1, 12, DateTime.DaysInMonth(curDate.Year - 1, 12))
            Else
                FromDate = New DateTime(curDate.Year, curDate.Month - 1, 1)
                ToDate = New DateTime(curDate.Year, curDate.Month - 1, DateTime.DaysInMonth(curDate.Year, curDate.Month - 1))
            End If
        ElseIf range = "Quarter-To-Date" Then
            FromDate = modGlobals.GetQuarterStartDate
            ToDate = curDate
        ElseIf range = "Current Quarter" Then
            FromDate = modGlobals.GetQuarterStartDate
            ToDate = modGlobals.GetQuarterEndDate
        ElseIf range = "Previous Quarter" Then
            FromDate = GetQuarterStartDate(GetLastQuarterEndDate)
            ToDate = GetLastQuarterEndDate()
        ElseIf range = "Year-To-Date" Then
            FromDate = New DateTime(curDate.Year, 1, 1)
            ToDate = curDate
        ElseIf range = "Current Year" Then
            FromDate = New DateTime(curDate.Year, 1, 1)
            ToDate = New DateTime(curDate.Year, 12, DateTime.DaysInMonth(curDate.Year, 12))
        ElseIf range = "Previous Year" Then
            FromDate = New DateTime(curDate.Year - 1, 1, 1)
            ToDate = New DateTime(curDate.Year - 1, 12, DateTime.DaysInMonth(curDate.Year - 1, 12))
        ElseIf range = "Custom" OrElse range.IsNullOrWhiteSpace Then
            'nothing
        Else
            Throw New Exception("'{0}' is not recognized as a valid date range".FormatWith(range))
        End If
    End Sub

End Class
