﻿Imports System.Runtime.CompilerServices

Public Module ListExtensions
    <Extension>
    Public Iterator Function ChunkBy(Of T)(source As IEnumerable(Of T), chunksize As Integer) As IEnumerable(Of IEnumerable(Of T))
        While source.Any
            Yield source.Take(chunksize)
            source = source.Skip(chunksize)
        End While
    End Function

    <Extension>
    Public Sub AddRange(Of T)(list As List(Of T), ParamArray values() As T)
        For Each item In values
            list.Add(item)
        Next
    End Sub
End Module