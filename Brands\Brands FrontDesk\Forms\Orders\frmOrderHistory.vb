﻿Imports System.ComponentModel
Imports System.Data.SqlClient
Public Class frmOrderHistory

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoCode As Decimal

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property SelectedOrderNumber As Integer

    Dim DB As dbEPDataDataContext

    Private Sub frmOrderHistory_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        DB = New dbEPDataDataContext(GetConnectionString)
        GridControl2.DataSource = Query("select s.*, d.ScannedDate, d.DeliverBy, 
NextPrDate = 'Pr#: ' + convert(varchar(50), s.SendWithPr<PERSON>um) + ' - ' + ISNULL(p.PAYROLL_STATUS, 'Future Payroll') + CASE WHEN c.process_date IS NULL THEN '' ELSE ', Process Date: ' END + ISNULL(c.process_date,'')
from custom.SuppliesOrders s
left outer join custom.view_Deliveries d on s.ID = d.PayrollNum
LEFT OUTER JOIN dbo.PAYROLL p ON s.SendWithPrNum = p.PRNUM AND s.CoNum = p.CONUM
OUTER APPLY (SELECT TOP 1 CONVERT(nvarchar(50), fnsp.process_date, 1) process_date FROM dbo.fn_NextScheduledPayroll(s.conum) fnsp WHERE fnsp.completed = 'NO') c
WHERE s.conum = @Conum
order by s.date DESC", New SqlParameter("Conum", CoCode))
        GridView2.BestFitColumns()

        Dim CoName = (From A In DB.COMPANies Where A.CONUM = Me.CoCode Select A.CO_NAME).Single
        Me.Label1.Text = CoCode & " - " & CoName

    End Sub

    Private Sub riOrderNumber_Click(sender As Object, e As EventArgs) Handles riOrderNumber.Click
        Dim orderNumber As Integer = GridView2.GetRowCellValue(GridView2.FocusedRowHandle, colID)
        Dim frm = New frmOrderHistoryDetails(orderNumber)
        frm.ShowDialog()
    End Sub

End Class