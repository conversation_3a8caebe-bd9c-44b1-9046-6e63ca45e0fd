﻿Imports System.ComponentModel
Imports Brands_FrontDesk
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports DevExpress.XtraRichEdit

Public Class ucFaxAndEmailGrid
    Private db As dbEPDataDataContext
    Dim SpecialistOnly As Dictionary(Of Decimal, Boolean) = New Dictionary(Of Decimal, Boolean)
    Dim _GridSource As List(Of view_FaxAndEmail)

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property RefreshButtonAction As Action
    Private Property frmLogger As Serilog.ILogger
    Private Property binCategoriesList As List(Of FaxCategoryStruct)

    Private Property UsersInMyGroup As List(Of String)

    Public Sub New()
        frmLogger = Serilog.Log.Logger
        InitializeComponent()
    End Sub

    Private Async Sub ucFaxAndEmailGrid_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If Not IsInDesignMode Then
            frmLogger = Logger.ForContext(Of ucFaxAndEmailGrid)()
            Await Initialize()
            AddHandler modSignalRClient.OnRefereshFaxes, AddressOf LoadData
            AddHandler modSignalRClient.OnFaxOrEmailUpdated, AddressOf SignalRUpdates
        End If
    End Sub

    Private Async Function Initialize() As Task
        Try
            If _Category.IsNullOrWhiteSpace AndAlso _AssignedTo = AssignedTo.CS Then Exit Function
            db = New dbEPDataDataContext(GetConnectionString)
            If _FormType = FormType.Bin Then
                binCategoriesList = db.FaxCategories.Where(Function(f) f.Bin = _Category).ToList
                If Not binCategoriesList.Any Then
                    Throw New ArgumentException($"Bin name: {_Category} was not found.", NameOf(_Category))
                End If
            ElseIf _FormType = FormType.Category Then
                Dim category = db.FaxCategories.SingleOrDefault(Function(f) f.Category = _Category)
                If category Is Nothing Then
                    Throw New ArgumentException($"Category name: {_Category} was not found.", NameOf(_Category))
                End If
                binCategoriesList = New List(Of FaxCategoryStruct)
                binCategoriesList.Add(category)
            End If
            Await LoadData(False)
        Catch ex As Exception
            DisplayErrorMessage("Error Initilizing Fax And Email Grid", ex)
        End Try

        If rilueCoName.DataSource Is Nothing Then
            Dim CoList = db.COMPANies.Select(Function(c) New With {.CoName = c.CONUM & " - " & c.CO_NAME, .CoNum = c.CONUM, .Specialist = c.OP_OWNER, .Delivery = c.DELVDESC.Replace("Pickup - Call ", ""), .SpecialistOnly = ImageToByteArray(My.Resources.Warning_Small)})
            rilueCoName.DataSource = CoList
            rilueSpecialist.DataSource = CoList
            rilueDelivery.DataSource = CoList
        End If

        riluePrlCategory.DataSource = (From e In db.FaxCategories Where e.Category.StartsWith("Payroll")).ToList()
        riluePrlCategory.DisplayMember = "CatPayrollAbbrevReadOnly"
        riluePrlCategory.ValueMember = "Category"
    End Function

#Region "Form Properties"

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _FormType As FormType

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _AssignedTo As AssignedTo

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _GridFilter As String
        Get
            Return gvFaxAndEmail.ActiveFilterString
        End Get
        Set(value As String)
            gvFaxAndEmail.ActiveFilterString = value
        End Set
    End Property

    Private _privateCategory As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _Category As String
        Get
            Return _privateCategory
        End Get
        Set(value As String)
            If value <> _privateCategory Then
                Dim t = Initialize()
            End If
            _privateCategory = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ShowButtonOptions As Boolean
        Get
            Return lcgButtonOptions.Visible
        End Get
        Set(value As Boolean)
            lcgButtonOptions.Visibility = value.ToBarItemVisibility()
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ShowAcquireAndReleaseButtons As Boolean
        Get
            Return lcgAcquireAndReleaseButtons.Visible
        End Get
        Set(value As Boolean)
            lcgAcquireAndReleaseButtons.Visibility = value.ToBarItemVisibility
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ShowFilters As Boolean
        Get
            Return lcgFilters.Visible
        End Get
        Set(value As Boolean)
            lcgFilters.Visibility = value.ToBarItemVisibility
        End Set
    End Property


    Private _viewCaptionProperty As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ViewCaption As String
        Get
            Return _viewCaptionProperty
        End Get
        Set(value As String)
            _viewCaptionProperty = value
            gvFaxAndEmail.ViewCaption = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColTicketNumberVisible As Boolean
        Get
            Return colTicketNum.Visible
        End Get
        Set(value As Boolean)
            colTicketNum.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColCategoryVisible As Boolean
        Get
            Return colCategory.Visible
        End Get
        Set(value As Boolean)
            colCategory.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _CategoryAllowEdit As Boolean
        Get
            Return colCategory.OptionsColumn.AllowEdit
        End Get
        Set(value As Boolean)
            colCategory.OptionsColumn.AllowEdit = value
            If value Then colCategory.ColumnEdit = riluePrlCategory
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColLastSpecialistVisible As Boolean
        Get
            Return colLastSpecialist.Visible
        End Get
        Set(value As Boolean)
            colLastSpecialist.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColSpecialistOnlyVisible As Boolean
        Get
            Return colSpecialistOnly.Visible
        End Get
        Set(value As Boolean)
            colSpecialistOnly.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColDeliveryVisible As Boolean
        Get
            Return colDelivery.Visible
        End Get
        Set(value As Boolean)
            colDelivery.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColIsDoneVisible As Boolean
        Get
            Return colIsDone.Visible
        End Get
        Set(value As Boolean)
            colIsDone.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColIsOutboundVisible As Boolean
        Get
            Return colIsOutbound.Visible
        End Get
        Set(value As Boolean)
            colIsOutbound.Visible = value
        End Set
    End Property

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _ColSubjectVisible As Boolean
        Get
            Return colSubject.Visible
        End Get
        Set(value As Boolean)
            colSubject.Visible = value
        End Set
    End Property
#End Region
    Private Async Sub LoadData(obj As Object, e As EventArgs) Handles deToDate.EditValueChanged, deFromDate.EditValueChanged, ceShowDone.EditValueChanged
        If Not IsInDesignMode Then
            Await LoadData(True)
        End If
    End Sub

    Private Sub SignalRUpdates(obj As Object, e As EventArgs)
        Try
            If _GridSource Is Nothing Then Exit Sub
            Dim v As view_FaxAndEmail = obj
            Dim fax = _GridSource.SingleOrDefault(Function(f) f.Source = v.Source AndAlso f.ID = v.ID)
            If DoesFaxMatchFilter(v) Then
                If fax Is Nothing Then
                    If v.Category = "Payroll Call Back" Then
                        _GridSource.Insert(0, v)
                    Else
                        _GridSource.Add(v)
                    End If
                    CheckSpecialistOnly(v)
                Else
                    Dim idx = _GridSource.FindIndex(Function(f) f.Source = v.Source AndAlso f.ID = v.ID)
                    _GridSource(idx) = v
                End If
            Else
                _GridSource.Remove(fax)
            End If
            gcFaxAndEmail.RefreshDataSource()
            SetTicketsCount()
        Catch ex As Exception
            Logger.Error(ex, "Error in background SignalR updating view_FaxAndEmail")
        End Try
    End Sub

    Private Function DoesFaxMatchFilter(fax As view_FaxAndEmail) As Boolean
        If _FormType = FormType.AssignedTo Then
            If UsersInMyGroup IsNot Nothing AndAlso UsersInMyGroup.Any Then
                If Not UsersInMyGroup.Contains(fax.AssignToUser) Then Return False
            Else
                If fax.AssignToUser <> UserName Then Return False
            End If
        Else
            If fax.AssignToUser IsNot Nothing AndAlso fax.AssignToUser.IsNotNullOrWhiteSpace AndAlso fax.AssignToUser <> "CS.O" Then Return False
        End If

        If _FormType = FormType.Category Then
            If fax.Category <> _Category Then Return False
        Else
            If Not binCategoriesList.Select(Function(f) f.Category).Contains(fax.Category) Then Return False
        End If

        If fax.FollowUp > Now Then
            Return False
        End If

        If lcgFilters.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always Then
            If Not ceShowDone.Checked AndAlso fax.IsDone Then
                Return False
            End If

            If deFromDate.HasValue AndAlso fax.Date.Value <= deFromDate.DateTime Then
                Return False
            End If

            If deToDate.HasValue AndAlso fax.Date.Value >= deToDate.DateTime Then
                Return False
            End If
        Else
            Return Not fax.IsDone
        End If
        Return True
    End Function

    Private Sub CheckSpecialistOnly(fax As view_FaxAndEmail)
        If _ColSpecialistOnlyVisible AndAlso fax.CoNum.HasValue AndAlso SpecialistOnly IsNot Nothing Then
            If SpecialistOnly.ContainsKey(fax.CoNum.Value) Then
                Exit Sub
            End If
            Dim so As Boolean? = db.CoOptions_Payrolls.SingleOrDefault(Function(f) f.CoNum = fax.CoNum)?.SpecialistOnly.GetValueOrDefault()
            SpecialistOnly.Add(fax.CoNum, so.GetValueOrDefault)
        End If
    End Sub

    Dim counterFax As Integer
    Public Async Function LoadData(callInitilize As Boolean) As Task
        Try
            frmLogger.Information("Entering LoadDate in ucFaxAndEmailGrid {FormType}", _FormType)
            If IsDisposed Then Exit Function
            lcRoot.ShowProgessPanel()
            If callInitilize Then
                Await Initialize()
                Exit Function
            End If
            SpecialistOnly.Clear()

            db = New dbEPDataDataContext(GetConnectionString)

            Dim tickets As IQueryable(Of view_FaxAndEmail) = db.view_FaxAndEmails
            If lcgFilters.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always Then
                tickets = From t In tickets Where ceShowDone.Checked OrElse t.IsDone = False Select t
                If deFromDate.HasValue Then
                    tickets = From t In tickets Where t.Date.Value >= deFromDate.DateTime
                End If
                If deToDate.HasValue Then
                    tickets = From t In tickets Where t.Date.Value <= deToDate.DateTime
                End If
            Else
                tickets = tickets.Where(Function(t) t.IsDone = False)
            End If

            Select Case _FormType
                Case FormType.Bin
                    tickets = (From A In tickets Where A.FaxCategory.Bin = _Category).OrderBy(Function(f) f.FaxCategory.BinOrder).ThenBy(Function(f) f.Date)
                Case FormType.Category
                    tickets = (From a In tickets Where a.Category = _Category).OrderBy(Function(f) f.Date)
            End Select

            Select Case _AssignedTo
                Case AssignedTo.CS
                    tickets = (From a In tickets Where a.AssignToUser Is Nothing OrElse a.AssignToUser = "CS.O" OrElse a.AssignToUser = "")
                Case AssignedTo.CurrentUser
                    If Permissions.UserGroup.IsNotNullOrWhiteSpace Then
                        UsersInMyGroup = db.FrontDeskPermissions.Where(Function(u) u.UserGroup = Permissions.UserGroup).Select(Function(u) u.UserName).ToList()
                    End If
                    If UsersInMyGroup IsNot Nothing AndAlso UsersInMyGroup.Any Then
                        tickets = (From a In tickets Where UsersInMyGroup.Contains(a.AssignToUser))
                    Else
                        tickets = (From a In tickets Where a.AssignToUser = UserName)
                    End If
            End Select

            If _ColSpecialistOnlyVisible Then
                SpecialistOnly = Await Task.Run(Function() (From c In db.CoOptions_Payrolls Where tickets.Select(Function(t) t.CoNum).Contains(c.CoNum)) _
                    .ToDictionary(Function(k) k.CoNum, Function(v) v.SpecialistOnly.GetValueOrDefault))
            End If

            tickets = (From a In tickets Where Not a.FollowUp.HasValue OrElse a.FollowUp <= Today)

            _GridSource = Await Task.Run(Function() tickets.ToList())
            SetTicketsCount()

            'this is to set the last specialist -- will remove it from view. it's an unbound col so it will get set it not empty string from event 
            If colLastSpecialist.Visible Then
                Dim lastSpecialists = Await Task.Run(Function() db.Emails.Where(Function(t) t.IsOutbound AndAlso _GridSource.Select(Function(gs) gs.TicketNum) _
                                                                            .Contains(t.TicketNum)) _
                                                                            .GroupBy(Function(t) t.TicketNum, Function(key, ticket) ticket.OrderByDescending(Function(d) d.DateReceived) _
                                                                            .Select(Function(t) New With {.MarkedPayrollDoneBy = t.MarkedPayrollDoneBy, .TicketNum = t.TicketNum}).FirstOrDefault()).ToList())

                For Each t In _GridSource
                    If t.TicketNum.HasValue AndAlso t.Source = "Email" Then
                        Dim a = lastSpecialists.SingleOrDefault(Function(l) l.TicketNum = t.TicketNum)
                        If a IsNot Nothing Then
                            t.LastSpecialist = a.MarkedPayrollDoneBy
                        Else
                            t.LastSpecialist = ""
                        End If
                    Else
                        t.LastSpecialist = ""
                    End If
                Next
            End If

            Me.gcFaxAndEmail.DataSource = _GridSource
            gvFaxAndEmail.RefreshData()
            gvFaxAndEmail.BestFitColumns()
            counterFax = 0
            frmLogger.Information("Sucsesfully Loadded Data In ucFaxAndEmailGrid {FormType} (Category}", _FormType, _Category)
        Catch ex As Exception
            Logger.Error(ex, "Error loading data, Error Counter is: {Counter}", counterFax)
            counterFax += 1
            If counterFax > 150 Then DisplayErrorMessage("Error refreshing Fax Grid,{0}{1}{2}# of error's:({3})".FormatWith(vbCrLf, ex.ToString, vbCrLf, counterFax), ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Function

    Private Sub SetTicketsCount()
        gvFaxAndEmail.ViewCaption = $"{_viewCaptionProperty} - {_GridSource.Count}"

        Try
            If _FormType = FormType.AssignedTo Then
                Select Case _GridSource.Count
                    Case 0
                        MainForm.TaskbarAssistant1.OverlayIcon = Nothing
                    Case 1
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._1
                    Case 2
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._2
                    Case 3
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._3
                    Case 4
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._4
                    Case 5
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._5
                    Case 6
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._6
                    Case 7
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._7
                    Case 8
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._8
                    Case 9
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._9
                    Case Else
                        MainForm.TaskbarAssistant1.OverlayIcon = My.Resources._9_
                End Select
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error setting MainForm.TaskbarAssistant1.OverlayIcon {Count}", _GridSource.Count)
        End Try
    End Sub

    Private Sub btnRefreshFaxes_Click(sender As Object, e As EventArgs) Handles btnRefreshFaxes.Click
        frmLogger.Information("User clicked on Refresh button")
        Dim t = Initialize()
        If RefreshButtonAction IsNot Nothing Then
            RefreshButtonAction.Invoke()
        End If
    End Sub

    Enum FormType
        Bin = 0
        Category = 1
        AssignedTo = 2
    End Enum

    Enum AssignedTo
        CS = 0
        CurrentUser = 1
    End Enum

    Private Sub GridViewFaxedPayroll_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvFaxAndEmail.PopupMenuShowing
        If e.HitInfo.RowHandle < 0 OrElse e.MenuType <> GridMenuType.Row Then Exit Sub
        Try
            Dim gv As GridView = CType(sender, GridView)

            Dim Item As view_FaxAndEmail = gv.GetRow(e.HitInfo.RowHandle)
            db = New dbEPDataDataContext(GetConnectionString)
            e.Menu.Items.Clear()
            If Item.PayrollOpenedBy IsNot Nothing Then
                If Item.PayrollOpenedBy = UserName Then
                    e.Menu.Items.Add(New DXMenuItem("Release {0}".FormatWith(Item.Category), Sub()
                                                                                                 Dim ticket = db.Tickets.Single(Function(t) t.TicketNum = Item.TicketNum)
                                                                                                 ticket.Release
                                                                                             End Sub))
                Else
                    Dim menuText = "Take {0} - Error: {0} opened by {1} on {2}".FormatWith(Item.Category, Item.PayrollOpenedBy, Item.PayrollOpenedOn)
                    e.Menu.Items.Add(New DXMenuItem(menuText) With {.Enabled = False})
                    menuText = "Mark {0} Done - Error: {0} opened by {1} on {2}".FormatWith(Item.Category, Item.PayrollOpenedBy, Item.PayrollOpenedOn)
                    e.Menu.Items.Add(New DXMenuItem(menuText) With {.Enabled = False})
                End If
            Else
                e.Menu.Items.Add(New DXMenuItem("Take {0}".FormatWith(Item.Category), Sub() Item.TryAcquireLock))
            End If

            If Item.PayrollOpenedBy Is Nothing OrElse Item.PayrollOpenedBy = UserName Then
                e.Menu.Items.Add(New DXMenuItem("Mark {0} Done".FormatWith(Item.Category), Sub()
                                                                                               If Item.TryMarkDone Then gv.DeleteRow(e.HitInfo.RowHandle)
                                                                                           End Sub))
            End If

            If Not Item.IsUrgent Then
                e.Menu.Items.Add(New DXMenuItem("Mark Urgent", Sub()
                                                                   Item.SetIsUrgent(True)
                                                               End Sub, My.Resources.warning_16x16))
            Else
                e.Menu.Items.Add(New DXMenuItem("Clear Urgent Indicator", Sub()
                                                                              Item.SetIsUrgent(False)
                                                                          End Sub, My.Resources.clear_16x16))
            End If

            e.Menu.Items.Add(New DXMenuItem("Open in Read Only mode", Sub()
                                                                          Item.OpenFaxOrEmailDetails(True)
                                                                      End Sub))

        Catch ex As Exception
            DisplayErrorMessage("Error in popup menu", ex)
        End Try
    End Sub

    Private Sub GridViewFaxedPayroll_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles gvFaxAndEmail.RowCellStyle
        If e.Column Is colDate Then
            Dim dt As DateTime
            If DateTime.TryParse(e.CellValue, dt) Then
                If _FormType = FormType.Bin And _Category = "ReopenPayroll" Then
                    e.Appearance.BackColor = Color.LightCoral
                ElseIf DateTime.Now.AddHours(-2) > dt Then
                    e.Appearance.BackColor = Color.LightCoral
                ElseIf DateTime.Now.AddHours(-1) > dt Then
                    e.Appearance.BackColor = Color.FromArgb(&HFF, &HFF, &H99)
                    e.Appearance.ForeColor = Color.Black
                End If
            End If
        ElseIf e.Column Is colLastSpecialist Then
            If e.CellValue = UserName Then
                e.Appearance.BackColor = Color.LightCoral
            ElseIf e.CellValue Is Nothing OrElse e.CellValue.ToString.IsNullOrWhiteSpace Then
                e.Appearance.BackColor = Color.FromArgb(&HCF, &HF4, &HB9)
                e.Appearance.ForeColor = Color.Black
            Else
                e.Appearance.BackColor = Color.FromArgb(&HFF, &HFF, &H99)
                e.Appearance.ForeColor = Color.Black
            End If
        ElseIf e.Column Is colDelivery Then
            Dim row = gvFaxAndEmail.GetRowCellDisplayText(e.RowHandle, colDelivery)
            If (row = "L" OrElse row = "S") AndAlso (DateTime.Now.TimeOfDay.Hours < 12 AndAlso DateTime.Now.TimeOfDay.Minutes < 30) Then
                e.Appearance.BackColor = Color.LightCoral
            ElseIf row <> "No Delivery" Then
                e.Appearance.BackColor = Color.FromArgb(&HFF, &HFF, &H99)
                e.Appearance.ForeColor = Color.Black
            End If
        End If

        'If e.Column IsNot colDate Then Exit Sub
        'Dim View As GridView = sender
        'If (e.RowHandle >= 0) Then
        '    Dim category As view_FaxAndEmail = View.GetRow(e.RowHandle)
        '    If DateTime.Now.AddHours(-2) > category.Date Then
        '        e.Appearance.BackColor = Color.LightCoral
        '    ElseIf DateTime.Now.AddHours(-1) > category.Date Then
        '        e.Appearance.BackColor = Color.FromArgb(&HFF, &HFF, &H66)
        '    End If
        'End If
    End Sub

    Private Sub GridViewFaxedPayroll_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles gvFaxAndEmail.CustomUnboundColumnData
        If e.IsGetData Then
            Dim faxAndEmail As view_FaxAndEmail = e.Row
            If e.Column Is colSpecialistOnly Then
                Dim b As Boolean
                If faxAndEmail.CoNum.HasValue AndAlso SpecialistOnly.TryGetValue(faxAndEmail.CoNum, b) Then
                    e.Value = b
                End If
            ElseIf e.Column Is colLastSpecialist Then
                If faxAndEmail.LastSpecialist Is Nothing Then
                    faxAndEmail.SetLastSpecialist()
                    e.Value = faxAndEmail.LastSpecialist
                Else
                    e.Value = faxAndEmail.LastSpecialist
                End If
            End If
        End If
    End Sub

    Private Sub gcFaxAndEmail_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles gcFaxAndEmail.MouseDoubleClick
        Try
            Dim gc As GridControl = CType(sender, GridControl)
            Dim gv As GridView = gc.DefaultView
            Dim HI = gv.CalcHitInfo(e.Location)
            If HI.RowHandle >= 0 Then
                Dim Item As view_FaxAndEmail = gv.GetRow(HI.RowHandle)
                Item = db.view_FaxAndEmails.SingleOrDefault(Function(f) f.Source = Item.Source AndAlso f.ID = Item.ID)
                If Item Is Nothing Then Exit Sub
                OpenFaxOrEmailDetails(Item)
                gv.RefreshRow(HI.RowHandle)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error opening Fax or email", ex)
        End Try
    End Sub

    Dim rtb As New RichEditControl
    Private Sub ToolTipController_GetActiveObjectInfo(sender As Object, e As DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs) Handles ToolTipController.GetActiveObjectInfo
        Try
            If e.SelectedControl IsNot gcFaxAndEmail Then Exit Sub
            Dim view As GridView = gcFaxAndEmail.GetViewAt(e.ControlMousePosition)
            If view Is Nothing Then Return
            Dim hi As GridHitInfo = view.CalcHitInfo(e.ControlMousePosition)
            If hi.HitTest = GridHitTest.RowCell Then
                If hi.Column Is colSpecialistOnly Then
                    If gvFaxAndEmail.GetRowCellValue(hi.RowHandle, colSpecialistOnly) = True Then
                        Dim info As ToolTipControlInfo = Nothing
                        e.Info = New ToolTipControlInfo(hi.Column, "The specialist has marked this company as exclusive. ask specialist permission to process.")
                    End If
                ElseIf hi.Column Is colDate Then
                    If Permissions?.UseEmailBodyToolTip = False Then Exit Sub
                    Dim info = New ToolTipControlInfo(hi.RowHandle, String.Empty)
                    Dim row As view_FaxAndEmail = gvFaxAndEmail.GetRow(hi.RowHandle)
                    If row.Source = "Email" Then
                        Dim db = New dbEPDataDataContext(GetConnectionString)
                        Dim eml = db.Emails.SingleOrDefault(Function(em) em.EmailNum = row.ID)
                        If eml IsNot Nothing Then
                            info.ToolTipType = ToolTipType.SuperTip
                            Dim sToolTip = New SuperToolTip()
                            sToolTip.MaxWidth = 430
                            '//sToolTip.Padding = New System.Windows.Forms.Padding(14)

                            sToolTip.Items.AddTitle($"Subject: {eml.Subject}")
                            Dim item = New ToolTipItem()
                            rtb.HtmlText = eml.Body
                            item.Text = rtb.Text
                            item.AllowHtmlText = DefaultBoolean.True
                            item.Appearance.BackColor = Color.Blue

                            sToolTip.Items.Add(item)
                            info.SuperTip = sToolTip
                            e.Info = info
                        End If
                    End If
                End If
            End If
        Catch ex As Exception
            frmLogger.Error(ex, "Error in setting the tool tip item")
        End Try
    End Sub

    Private Sub GridViewFaxedPayroll_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles gvFaxAndEmail.FocusedRowChanged
        Try
            If e.FocusedRowHandle < 0 Then Exit Sub
            Dim row As view_FaxAndEmail = gvFaxAndEmail.GetRow(e.FocusedRowHandle)
            If row Is Nothing Then Exit Sub
            'db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, row)
            btnReleaseFax.Enabled = row.PayrollOpenedBy IsNot Nothing AndAlso row.PayrollOpenedBy = UserName
            btnTakeFax.Enabled = row.PayrollOpenedBy Is Nothing
            If row.PayrollOpenedBy IsNot Nothing Then
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error refrshing data on focused row changed, Error Counter is: {Counter}", counterFax)
            counterFax += 1
            If counterFax > 25 Then DisplayErrorMessage("Error refreshing row when changed ,{0}{1}{2}# of error's:({3})".FormatWith(vbCrLf, ex.ToString, vbCrLf, counterFax), ex)
        End Try
    End Sub

    Private Sub btnReleaseFax_Click(sender As Object, e As EventArgs) Handles btnReleaseFax.Click
        If gvFaxAndEmail.FocusedRowHandle < 0 Then Exit Sub
        Dim row As view_FaxAndEmail = gvFaxAndEmail.GetRow(gvFaxAndEmail.FocusedRowHandle)
        Dim ticket = db.Tickets.Single(Function(t) t.TicketNum = row.TicketNum)
        ticket.Release()
        btnReleaseFax.Enabled = False
        btnTakeFax.Enabled = True
        gvFaxAndEmail.RefreshData()
    End Sub

    Private Sub btnTakeFax_Click(sender As Object, e As EventArgs) Handles btnTakeFax.Click
        If gvFaxAndEmail.FocusedRowHandle < 0 Then Exit Sub
        Dim row As view_FaxAndEmail = gvFaxAndEmail.GetRow(gvFaxAndEmail.FocusedRowHandle)
        row.TryAcquireLock
        btnTakeFax.Enabled = False
        btnReleaseFax.Enabled = True
    End Sub

    Private Sub riluePrlCategory_EditValueChanged(sender As Object, e As EventArgs) Handles riluePrlCategory.EditValueChanged
        Try
            frmLogger.Information("Category in payrol bin updated")

            Dim lue As LookUpEdit = sender
            Dim row As view_FaxAndEmail = gvFaxAndEmail.GetFocusedRow

            Dim DbEntDB = New dbEPDataDataContext(GetConnectionString)

            Dim NewCat = (From f In DbEntDB.FaxCategories Where f.Category = lue.EditValue.ToString()).FirstOrDefault


            If row.Source = "Fax" Then
                Dim fax = (From f In DbEntDB.Faxes Where f.FaxID = row.ID Select f).FirstOrDefault
                fax.FaxCategory = NewCat
            ElseIf row.Source = "Email" Then
                Dim email = (From em In DbEntDB.Emails Where em.EmailNum = row.ID Select em).FirstOrDefault
                email.FaxCategory = NewCat
            End If

            DbEntDB.SaveChanges()

            LoadData(False)

            modSignalRClient.PushFaxOrEmailUpdate(row.Source, row.ID, "Payroll Category Updated.")
        Catch ex As Exception
            DisplayErrorMessage("Error changing category.", ex)
        End Try
    End Sub
End Class

