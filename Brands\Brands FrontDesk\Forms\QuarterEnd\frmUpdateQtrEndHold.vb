﻿Imports DevExpress.XtraEditors

Public Class frmUpdateQtrEndHold
    Private _isLoading As Boolean = True

    Private _coNum As Decimal?
    Private Property coOpt As COOPTION
    Private Property db As dbEPDataDataContext
    Private Property frmLogger As Serilog.ILogger

    Sub New()
        InitializeComponent()
    End Sub

    Sub New(coNum As Decimal)
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmUpdateQtrEndHold)()
        _coNum = coNum
        tsReportsWebVisible.Enabled = Permissions.AllowBilling.GetValueOrDefault()
    End Sub

    Private Sub frmUpdateQtrEndHold_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        BindingSource1.DataSource = db.view_CompanySumarries.ToList()
        cbeQtrHoldStatus.Properties.Items.AddRange(GetUdfValue("QtrEndHoldStatusList").Split(",").Where(Function(v) v.ToLower() <> "linked").ToArray)
        If _coNum.HasValue Then
            sluCoNum.EditValue = _coNum
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            If coOpt IsNot Nothing Then
                If cbeQtrHoldStatus.EditValue Is Nothing OrElse cbeQtrHoldStatus.EditValue.ToString().IsNullOrWhiteSpace Then
                    DisplayMessageBox("Please set the Qtr hold status.")
                    Exit Sub
                End If
                frmLogger.Information("Setting Co#: {CoNum} QYEND_HOLD_TYPE: {QYEND_HOLD_TYPE}", coOpt.CONUM, cbeQtrHoldStatus.EditValue)
                If coOpt.QYEND_HOLD_TYPE = "Billing" AndAlso cbeQtrHoldStatus.EditValue = "None" Then
                    QuarterEndUtils.ResetReportsDisplayDate(_coNum, 0)
                ElseIf cbeQtrHoldStatus.EditValue = "Billing" Then
                    QuarterEndUtils.ResetReportsDisplayDate(_coNum, 1)
                End If
                coOpt.QYEND_HOLD_TYPE = cbeQtrHoldStatus.EditValue
                If db.SaveChanges Then sluCoNum.EditValue = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error updating company status", ex)
        End Try
    End Sub

    Private Sub sluCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles sluCoNum.EditValueChanged
        Try
            _isLoading = True
            If sluCoNum.EditValue IsNot Nothing AndAlso sluCoNum.EditValue.ToString().IsNotNullOrWhiteSpace AndAlso sluCoNum.Text.IsNotNullOrWhiteSpace Then
                _coNum = GetLinkedCoNum(sluCoNum.EditValue)
                coOpt = (From c In db.COOPTIONs Where c.CONUM = _coNum).Single
                cbeQtrHoldStatus.EditValue = coOpt.QYEND_HOLD_TYPE
                teQtrEndType.Text = If(coOpt.QYEND_STATUS, "")
                frmLogger.Debug("Loading Co#: {CoNum} QYEND_HOLD_TYPE: {QYEND_HOLD_TYPE}", _coNum, coOpt.QYEND_HOLD_TYPE)
                cbeQtrHoldStatus.Enabled = coOpt.QYEND_HOLD_TYPE <> "Billing" OrElse Permissions.AllowBilling
                Dim hasAnyHiddenReports = (db.SCHFED_FORMs.Any(Function(r) r.CONUM = _coNum AndAlso r.PRT_COMPLETEDDATE > Today))
                tsReportsWebVisible.IsOn = Not hasAnyHiddenReports
            Else
                cbeQtrHoldStatus.EditValue = Nothing
                teQtrEndType.Text = ""
                _coNum = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading company.", ex)
        Finally
            _isLoading = False
        End Try
    End Sub

    Private Sub btnMiscHold_Click(sender As Object, e As EventArgs) Handles btnMiscHold.Click
        If Not _coNum.HasValue Then
            DisplayMessageBox("Please select a company.")
            Exit Sub
        End If
        Dim frm = New frmQuarterEndHoldLog(_coNum.Value)
        If frm.ShowDialog = DialogResult.OK Then
            db = New dbEPDataDataContext(GetConnectionString)
            sluCoNum_EditValueChanged(Nothing, Nothing)
        End If
    End Sub


    Private Function GetLinkedCoNum(conum As Decimal) As Decimal
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim company = db.COMPANies.Single(Function(c) c.CONUM = conum)
            Dim linkedCo = db.COMPANies.SingleOrDefault(Function(c) c.CONUM > 900 And c.CONUM < 999 AndAlso c.FED_ID = company.FED_ID AndAlso c.CO_STATUS = "Active Status")
            If linkedCo IsNot Nothing Then
                lcLinkedConumMessage.Text = $"Showing linked Co#: {linkedCo.CONUM}"
                lcLinkedConumMessage.Visible = True
                Return linkedCo.CONUM
            Else
                lcLinkedConumMessage.Visible = False
                Return conum
            End If
        End Using
    End Function

    Private Sub btnResetReportDisplayDate_Click(sender As Object, e As EventArgs)
        Try
            If Not _coNum.HasValue Then
                DisplayMessageBox("Please select a company.")
                Exit Sub
            End If
            QuarterEndUtils.ResetReportsDisplayDate(_coNum.Value, 1)
            XtraMessageBox.Show("Done!")
        Catch ex As Exception
            DisplayErrorMessage("Error updating reports display date", ex)
        End Try
    End Sub

    Private Sub tsReportsWebVisible_Toggled(sender As Object, e As EventArgs) Handles tsReportsWebVisible.Toggled
        If _isLoading Then Exit Sub
        Try
            If Not _coNum.HasValue Then
                DisplayMessageBox("Please select a company.")
                Exit Sub
            End If
            QuarterEndUtils.ResetReportsDisplayDate(_coNum, If(tsReportsWebVisible.IsOn, 1, 0))
        Catch ex As Exception

        End Try
    End Sub
End Class