﻿Imports DevExpress.XtraEditors

Public Class frmManualBillingDraftAdd

    Dim SelectedCoNum As Decimal
    Dim db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
    End Sub

    Private Sub frmManualBillingDraft_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ucSearchCompany.BindPopupContainerEdit(pceConum)
    End Sub

    Private Sub pceConum_EditValueChanged(sender As Object, e As EventArgs) Handles pceConum.EditValueChanged
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            Dim num As Decimal
            If Decimal.TryParse(nz(pceConum.EditValue, ""), num) Then
                SelectedCoNum = num
            Else
                SelectedCoNum = Nothing
            End If
            manual_Trans_Account_listsBindingSource.DataSource = db.Manual_Trans_Account_lists.Where(Function(c) c.CONUM = SelectedCoNum).ToList()
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub manual_Trans_Account_listsBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles manual_Trans_Account_listsBindingSource.CurrentChanged
        Dim row As Manual_Trans_Account_list = manual_Trans_Account_listsBindingSource.Current
        teAccountNumber.Enabled = row.ACCOUNT_NUMBER.IsNullOrWhiteSpace
        teRoutingNumber.Enabled = row.ROUTING_NUMBER.IsNullOrWhiteSpace
        row.Requested_Effective_Date = Today.AddDays(1)
    End Sub

    Private Sub btnSubmit_Click(sender As Object, e As EventArgs) Handles btnSubmit.Click
        Try
            Dim row As Manual_Trans_Account_list = manual_Trans_Account_listsBindingSource.Current
            If row.ACCOUNT_NUMBER.IsNullOrWhiteSpace OrElse row.ROUTING_NUMBER.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("Account # & Routing # is required.")
                Exit Sub
            End If
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                If ctxDB.fn_ValidRoutingNumbers(row.ROUTING_NUMBER) = "False" Then
                    XtraMessageBox.Show("Invalid routing #")
                    Exit Sub
                End If
                Dim newRow = New DD_Manual_Trans_Log
                newRow.ACCOUNT_NUMBER = row.ACCOUNT_NUMBER
                newRow.ACCOUNT_ORDER = row.ACCOUNT_ORDER
                newRow.ACCOUNT_SOURCE = row.ACCOUNT_SOURCE
                newRow.ACCT_TYPE = row.ACCT_TYPE
                newRow.ACH_NAME = row.ACH_NAME
                newRow.AMOUNT = row.AMOUNT
                newRow.Billing_Dept_Notes = row.Billing_Dept_Notes
                newRow.CONUM = row.CONUM
                newRow.Credit_Account = row.Credit_Account
                newRow.Credit_ACCT_TYPE = row.Credit_ACCT_TYPE
                newRow.Credit_CONUM = row.Credit_CONUM
                newRow.Credit_EMPNUM = row.Credit_EMPNUM
                newRow.Credit_Routing = row.Credit_Routing
                newRow.Date_Requested = row.Date_Requested
                newRow.DDIVNAME = row.DDIVNAME
                newRow.DIVNUM = row.DDIVNUM
                newRow.Priority_Lvl = row.Priority_Lvl
                newRow.Process_Date = Now
                newRow.Requested_By = UserName
                newRow.Requested_Effective_Date = row.Requested_Effective_Date
                newRow.ROUTING_NUMBER = row.ROUTING_NUMBER
                newRow.Transaction_Status = row.TRANSACTION_Status
                newRow.Trans_Note = row.TRANs_Note
                newRow.TransType = "Charge"
                ctxDB.DD_Manual_Trans_Logs.InsertOnSubmit(newRow)
                ctxDB.SubmitChanges()
            End Using
            DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving manual transaction", ex)
        End Try
    End Sub
End Class