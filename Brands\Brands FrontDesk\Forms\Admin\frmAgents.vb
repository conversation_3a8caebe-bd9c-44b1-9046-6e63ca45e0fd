﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
'Imports Execupay.BBLib

Public Class frmAgents
    Private _db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property agentParameters As BindingList(Of _1059MeasureEmpSetup)

    Dim newRow As _1059MeasureEmpSetup

    ReadOnly Property DB As dbEPDataDataContext
        Get
            If _db Is Nothing Then _db = New dbEPDataDataContext(GetConnectionString)
            Return _db
        End Get
    End Property

    Private Sub frmAgents_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Dim depts = (From a In DB.fn_1059Dept).ToList()
            depts.Add(New fn_1059DeptResult With {.Dept = "None"})

            ccbeDept.Properties.DataSource = depts
            ccbeDept.Properties.DisplayMember = "Dept"
            ccbeDept.Properties.DropDownRows = 10
            ccbeDept.Properties.NullText = ""
            ccbeDept.Properties.ValueMember = "Dept"

            DxErrorProvider1.DataSource = agentParameters

            Dim matrix = (From m In DB._1059Measures Select m.MeasureId, m.MeasureName).Distinct().OrderBy(Function(o) o.MeasureName).ToList()
            rilueMatrixId.DataSource = matrix
            rilueMatrixId.DisplayMember = "MeasureName"
            rilueMatrixId.DropDownRows = 10
            rilueMatrixId.NullText = ""
            rilueMatrixId.ValueMember = "MeasureId"

            rilueCompare.DataSource = New List(Of String)() From {"=", "<>", "<", "<=", ">", ">="}
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub ccbeDept_EditValueChanged(sender As Object, e As EventArgs) Handles ccbeDept.EditValueChanged
        Try
            Dim emps = (From emp In DB.fn_1059MeasureUsersByDept(ccbeDept.EditValue) Select emp.EmpNum, emp.Agent Order By Agent).ToList()
            lueAgent.Properties.DataSource = emps
            lueAgent.Properties.DisplayMember = "Agent"
            lueAgent.Properties.DropDownRows = 10
            lueAgent.Properties.NullText = ""
            lueAgent.Properties.ValueMember = "EmpNum"

            If emps.Count = 0 Then
                gcMatrix.Visible = False
            Else
                emps.Insert(0, New With {Key .EmpNum = CType(-1, Int32?), Key .Agent = "(Choose)"})
                lueAgent.ItemIndex = 0
                GridView1.OptionsView.NewItemRowPosition = NewItemRowPosition.None
            End If
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub lueAgent_EditValueChanged(sender As Object, e As EventArgs) Handles lueAgent.EditValueChanged
        Try
            If Not IsAgentSelected() Then
                gcMatrix.Visible = False
                Exit Sub
            End If

            Dim en As Integer = lueAgent.EditValue
            agentParameters = New BindingList(Of _1059MeasureEmpSetup)(DB._1059MeasureEmpSetups.Where(Function(dd) dd.EmpNum = en).ToList())
            gcMatrix.DataSource = agentParameters

            If gcMatrix.Visible = False Then
                gcMatrix.Visible = True
            ElseIf GridView1.OptionsView.NewItemRowPosition = NewItemRowPosition.None AndAlso lueAgent.EditValue <> -1 Then
                GridView1.OptionsView.NewItemRowPosition = NewItemRowPosition.Bottom
            ElseIf GridView1.OptionsView.NewItemRowPosition = NewItemRowPosition.Bottom AndAlso lueAgent.EditValue = -1 Then
                GridView1.OptionsView.NewItemRowPosition = NewItemRowPosition.None
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error selecting agent", ex)
        End Try
    End Sub

    Private Function IsAgentSelected() As Boolean
        If lueAgent.EditValue Is Nothing OrElse lueAgent.EditValue.ToString().IsNullOrWhiteSpace Then
            XtraMessageBox.Show("Please select an agent")
            Return False
        End If
        Return True
    End Function

    Private Sub GridView1_InitNewRow(sender As Object, e As DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs) Handles GridView1.InitNewRow
        newRow = GridView1.GetRow(e.RowHandle)
        newRow.EmpNum = lueAgent.EditValue
        DB._1059MeasureEmpSetups.InsertOnSubmit(newRow)
    End Sub

    Private Sub sbSave_Click(sender As Object, e As EventArgs) Handles sbSave.Click
        Try
            'If DB.GetChangeSet().Inserts.Any(Function(ani) TryCast(ani, _1059MeasureEmpSetup) IsNot Nothing) _
            '    OrElse DB.GetChangeSet().Updates.Any(Function(ani) TryCast(ani, _1059MeasureEmpSetup) IsNot Nothing) Then
            '    MessageBox.Show("updates exists")

            '    'DB.DiscardPendingChanges()

            'End If

            DB.SaveChanges()
            XtraMessageBox.Show("Success")
        Catch ex As Exception
            DisplayErrorMessage("Error saving Agents", ex)
        End Try
    End Sub

    Private Sub GridView1_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridView1.ValidateRow
        GridView1.ClearColumnErrors()
        Dim rec As _1059MeasureEmpSetup = GridView1.GetFocusedRow()
        If rec.Goal = "" Then rec.Goal = Nothing

        If rec.MeasureId = Nothing Then
            GridView1.SetColumnError(GridView1.Columns("MeasureId"), "Required")
        End If
        If rec.Compare Is Nothing Then
            GridView1.SetColumnError(GridView1.Columns("Compare"), "Required")
        End If
        e.Valid = Not GridView1.HasColumnErrors
    End Sub

    Private Sub GridView1_InvalidRowException(sender As Object, e As DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs) Handles GridView1.InvalidRowException
        DisplayMessageBox("Please correct the invalid values, or hit escape to cancel")
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction
    End Sub

    Private Sub GridView1_ValidatingEditor(sender As Object, e As Controls.BaseContainerValidateEditorEventArgs) Handles GridView1.ValidatingEditor
        If GridView1.FocusedColumn.FieldName.ToUpper = "COMPARE" Then
            If e.Value Is Nothing Then
                e.Valid = False
            End If
        ElseIf GridView1.FocusedColumn.FieldName.ToUpper = "MEASUREID" Then
            If e.Value Is Nothing Then
                e.Value = False
            Else
                Dim r As _1059MeasureEmpSetup
                For Each r In GridView1.DataSource
                    If r.MeasureId = e.Value Then
                        e.Valid = False
                        Exit For
                    End If
                Next
            End If
        End If

    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.MenuType = GridMenuType.Row AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete Row", click:=Sub()
                                                                                           If XtraMessageBox.Show("Are you sure you want to delete row", "Confim Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = DialogResult.No Then
                                                                                               Return
                                                                                           End If

                                                                                           Dim row As _1059MeasureEmpSetup = GridView1.GetRow(e.HitInfo.RowHandle)
                                                                                           DB._1059MeasureEmpSetups.DeleteOnSubmit(row)
                                                                                           Me.GridView1.DeleteRow(e.HitInfo.RowHandle)
                                                                                       End Sub))
        End If
    End Sub
End Class



'Module DataContextExtensions
'    <Extension()>
'    Sub DiscardPendingChanges(ByVal context As DataContext)
'        context.RefreshPendingChanges(RefreshMode.OverwriteCurrentValues)
'        Dim changeSet As ChangeSet = context.GetChangeSet()

'        If changeSet IsNot Nothing Then

'            For Each objToInsert As Object In changeSet.Inserts
'                context.GetTable(objToInsert.[GetType]()).DeleteOnSubmit(objToInsert)
'            Next

'            For Each objToDelete As Object In changeSet.Deletes
'                context.GetTable(objToDelete.[GetType]()).InsertOnSubmit(objToDelete)
'            Next
'        End If
'    End Sub

'    <Extension()>
'    Sub RefreshPendingChanges(ByVal context As DataContext, ByVal refreshMode As RefreshMode)
'        Dim changeSet As ChangeSet = context.GetChangeSet()

'        If changeSet IsNot Nothing Then
'            context.Refresh(refreshMode, changeSet.Deletes)
'            context.Refresh(refreshMode, changeSet.Updates)
'        End If
'    End Sub
'End Module
