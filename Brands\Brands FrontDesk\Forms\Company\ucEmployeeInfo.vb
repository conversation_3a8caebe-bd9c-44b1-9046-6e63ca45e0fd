﻿Imports System.ComponentModel
Imports System.Data
Imports System.Data.SqlClient
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraLayout
Imports Microsoft.EntityFrameworkCore
Imports Serilog

Public Class ucEmployeeInfo

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property CoNum As Decimal
    Private Property SelectedEmployee As EmployeeList
    Private Property IsInDesignMode As Boolean = False
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Logger As ILogger
    Private tblPayDetails As DataTable
    Private ToBeValidated As Boolean
    Private Alerts As New EmpInfoAlerts()

    Dim utilsMenu As New DevExpress.Utils.Menu.DXPopupMenu


    Sub New()
        If LicenseManager.UsageMode = LicenseUsageMode.Designtime Then IsInDesignMode = True
        InitializeComponent()
        Logger = modGlobals.Logger.ForContext(Of ucEmployeeInfo)

        utilsMenu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Renumber Employees", click:=Sub()
                                                                                                  If Me.CoNum = 0 Then Return
                                                                                                  Dim frm As New frmRenumberEmployees With {.CoCode = Me.CoNum}
                                                                                                  Dim results = frm.ShowDialog
                                                                                                  frm.Dispose()
                                                                                              End Sub))
        utilsMenu.Items.Add(New DXMenuItem("Print W2", Sub() PrintW2Report()))

        Me.btnUtilities.DropDownControl = utilsMenu
    End Sub

    Private Class DateRange
        Sub New(_from As DateTime, _to As DateTime)
            FromDate = _from
            ToDate = _to
        End Sub
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property FromDate As DateTime
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property ToDate As DateTime
    End Class

    Public Sub SetDataNavigator(bs As BindingSource)
        DataNavigator1.DataSource = bs
        lciDataNavigator.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
    End Sub

    Private Sub frmPayHistory_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If IsInDesignMode Then Exit Sub
        LoadEmployeeSecreen(sender, e)
    End Sub

    Public Function HasChanges(Optional tmpSelectedEmployee As EmployeeList = Nothing) As Boolean
        If tmpSelectedEmployee Is Nothing Then tmpSelectedEmployee = SelectedEmployee
        EMPLOYEEBindingSource.EndEdit()
        If EmployeeEnt IsNot Nothing AndAlso rgrp_w4Style.EditValue <> EmployeeEnt.w4_style.GetValueOrDefault(0) Then EmployeeEnt.w4_style = rgrp_w4Style.EditValue
        Return EmployeeID <> 0 AndAlso tmpSelectedEmployee IsNot Nothing AndAlso DB.GetChangeSet.Inserts.Count + DB.GetChangeSet.Updates.Count + DB.GetChangeSet.Deletes.Count > 0
    End Function

    Public Function CheckPendingChanges() As DialogResult
        If HasAlerts() Then
            HandleAlerts()
        End If

        If HasChanges() Then
            Dim Changes = New pr_batch_employee_change With {.change_log = Me.GetChanges}
            Dim result = XtraMessageBox.Show($"Save Employee changes?{vbCrLf}{Changes.GetFormattedString}", "Save changes?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
            If result = DialogResult.Yes Then
                If Not Save() Then Return DialogResult.Cancel
            End If
            Return result
        End If
        Return DialogResult.None
    End Function

#Region "Employee form code"

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeeID As Decimal
    'Property CoNum As Decimal

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeeEnt As EMPLOYEE
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property StateInfoEnt As List(Of STATE_EE_INFO)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property LocalInfoEntList As List(Of LOCAL_EE_INFO)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CheckOverrides As List(Of pr_batch_overrides_setup)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoPaysAndDeds As List(Of EmpAutoPay)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Changes As String

    Dim dicGender As Dictionary(Of Decimal, String)
    Dim CoStates As String()
    Dim DicAvailableStates As Dictionary(Of String, String)
    Dim DicCoLocals As Dictionary(Of Decimal, String)
    Dim DicAvailableLocals As Dictionary(Of Decimal, String)

    Dim CoNewHire As String

    Dim OtherPays As List(Of OTHER_PAY)
    Dim DeductionCodes As List(Of DEDUCTION)

    Friend DB As dbEPDataDataContext
    Dim _IsLoading As Boolean

    Dim StateTaxOptions As List(Of state_tax)
    Dim StateStatusOptions As Dictionary(Of String, String())
    Dim StateInstructions As Dictionary(Of String, String)

    Dim CoLocalCodes As List(Of LOCAL_ID)
    Dim Locals As List(Of LOCAL)
    Dim LocalTaxOptions As List(Of local_tax)

    Private CoOptionsSecondCheck As List(Of CoOptions_SecondCheckPayCode)

    Private Sub LoadEmployeeSecreen(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            dicGender = New Dictionary(Of Decimal, String) From {{0, "Male"}, {1, "Female"}, {3, "UnSpecified"}}
            Me.GENDERLookUpEdit.Properties.DataSource = New BindingSource(dicGender, Nothing)

            LoadData()

            CoStates = (From A In DB.STATE_IDs Where A.CONUM = CoNum Select A.ST_ABBRV Order By ST_ABBRV).Distinct.ToArray
            CoNewHire = (From A In DB.COOPTIONs Where A.CONUM = CoNum Select A.CONEWHIRE).SingleOrDefault
            Me.UCI_STATETextEdit.Properties.Items.AddRange(CoStates)
            Me.ResStateComboBoxEdit.Properties.Items.AddRange(CoStates)
            Me.WrkStateComboBoxEdit.Properties.Items.AddRange(CoStates)
            Me.UIStateComboBoxEdit.Properties.Items.AddRange(CoStates)

            Dim Div = (From A In DB.DIVISIONs Where A.CONUM = CoNum Select A.DDIVNUM, A.DDIVNAME, Descr = A.DDIVNUM & "-" & A.DDIVNAME).ToList
            Me.DIVISIONBindingSource.DataSource = Div


            Dim AllEmployees = DB.EMPLOYEEs _
                    .Where(Function(em) em.CONUM = CoNum) _
                    .Select(Function(em) New EmployeeList With {.EmpNum = em.EMPNUM, .FirstName = em.F_NAME, .LastName = em.L_NAME, .MiddleName = em.M_NAME, .DepNum = em.DEPTNUM, .StartDate = em.START_DATE, .TermDate = em.TERM_DATE, .Ssn = em.SSN, .Tin = em.TIN}).ToList
            Me.EMPLOYEEBindingSource1.DataSource = AllEmployees

            OtherPays = (From A In DB.OTHER_PAYS Where A.CONUM = CoNum Order By A.OTH_PAY_NUM).ToList
            DeductionCodes = (From A In DB.DEDUCTIONs Where A.CONUM = CoNum Order By A.DED_NUM).ToList
            Me.DEDUCTIONBindingSource.DataSource = DeductionCodes

            Me.riPayCodes.DataSource = New BindingSource(OtherPays, Nothing)
            Me.riLookupEarning.DataSource = New BindingSource(OtherPays, Nothing)

            Me.StateTaxOptions = (From A In DB.state_taxes Where CoStates.Contains(A.state_code)).ToList

            Me.LocalTaxOptions = (From A In DB.local_taxes).ToList
            Me.CoLocalCodes = (From A In DB.LOCAL_IDS Where A.CONUM = Me.CoNum).ToList
            Me.Locals = (From A In DB.LOCALs Where (From B In CoLocalCodes Select B.LOC_ID).ToList.Contains(A.CODE)).ToList

            DicAvailableLocals = (From A In Me.CoLocalCodes
                                  Join B In Me.Locals On A.LOC_ID Equals B.CODE
                                  Where A.CONUM = CoNum Select A.LOC_ID, B.STATE, B.DESCRIPTION).Distinct.ToDictionary(
                                        Function(p) p.LOC_ID, Function(p) String.Format("{0} - ({1}) {2}", p.LOC_ID, p.STATE, p.DESCRIPTION)
                                    )
            DicCoLocals = New Dictionary(Of Decimal, String)(DicAvailableLocals)

            Me.StateStatusOptions = New Dictionary(Of String, String())
            For Each s In CoStates
                Dim State = s
                'Dim MaxDate As Date? = If((From A In DB.STATE_Bs Where A.ST_ABBR = State Select CType(A.STARTDATE, Nullable(Of Date))).Max, Nothing)
                Dim MaxDate As Date? = If((From A In DB.STATE_Bs Where A.ST_ABBR = State Select CType(A.STARTDATE, Nullable(Of Date))).ToList().Max, Nothing)
                Dim q = From A In DB.STATE_Bs Where A.ST_ABBR = State AndAlso A.STARTDATE >= MaxDate
                If State = "CT" Then
                    q = From A In q Where A.CODE.StartsWith("File")
                End If
                Dim Options = (From A In q Order By A.CODE Select A.CODE).Distinct.ToArray
                StateStatusOptions.Add(s, Options)
            Next
            Me.StateInstructions = (From A In DB.STATE_INFOs Where CoStates.Contains(A.ST_ABBR)).ToDictionary(Function(p) p.ST_ABBR, Function(p) p.ee_wh_notes)

            Dim CoInfo = (From A In DB.COMPANies Where A.CONUM = Me.CoNum Select A.FED_WH_EXE_FGC, A.OASDI_EXE_FGC, A.MEDICARE_EXE_FGC, A.FUTA_EXE_FGC).Single
            Me.FED_WH_EXE_FGETextEdit.Properties.ReadOnly = CoInfo.FED_WH_EXE_FGC.GetValueOrDefault = 1
            Me.OASDI_EXE_FGETextEdit.Properties.ReadOnly = CoInfo.OASDI_EXE_FGC.GetValueOrDefault = 1
            Me.FUTA_EXE_FGETextEdit.Properties.ReadOnly = CoInfo.FUTA_EXE_FGC.GetValueOrDefault = 1

            AddHandler DivNum2ndCheck.KeyDown, AddressOf ClearEditor
            AddHandler DeptNum2ndCheck.KeyDown, AddressOf ClearEditor
            AddHandler ResStateComboBoxEdit.KeyDown, AddressOf ClearEditor
            AddHandler WrkStateComboBoxEdit.KeyDown, AddressOf ClearEditor
            AddHandler UIStateComboBoxEdit.KeyDown, AddressOf ClearEditor

            Me.TabbedControlGroup1.SelectedTabPageIndex = 0
            'LoadEmployee()
        Catch ex As Exception
            DisplayErrorMessage("Error loading Employee form", ex)
        End Try
    End Sub

    Sub LoadData()
        DB = New dbEPDataDataContext(GetConnectionString)
        Me.Changes = Nothing
    End Sub

    Sub enableDDGroup(ByVal DDEnable As Boolean, ByVal DD_AccountNo As String)
        'Me.DD_BANK_RT_1TextEdit.Enabled = DDEnable

        'Me.DD_BANK_RT_1TextEdit.Parent.Name

        Me.gcDdSetup.Controls("DD_BANK_RT_" & DD_AccountNo & "TextEdit").Enabled = DDEnable
        Me.gcDdSetup.Controls("DD_ACC_NO_" & DD_AccountNo & "TextEdit").Enabled = DDEnable
        Me.gcDdSetup.Controls("DD_SPLIT_MET_" & DD_AccountNo & "ComboBoxEdit").Enabled = DDEnable
        Me.gcDdSetup.Controls("DD_SPLIT_AMT_" & DD_AccountNo & "TextEdit").Enabled = DDEnable
        Me.gcDdSetup.Controls("DD_STATUS_" & DD_AccountNo & "ComboBoxEdit").Enabled = DDEnable
    End Sub

    Sub formatPercentGroups(ByVal DD_AccountNo As String, ByVal cmbSplitMethod As ComboBoxEdit, ByVal splitMethod As String, ByVal txtSplitAmount As TextEdit, Optional ByVal splitAmount As Decimal = 0)
        Dim IsPercent As Boolean = (cmbSplitMethod.SelectedItem & "").Contains("Percent")
        If splitAmount > 0 Then
            If IsPercent Then
                txtSplitAmount.EditValue = FormatPercent(splitAmount)
            Else
                txtSplitAmount.EditValue = FormatCurrency(splitAmount)
            End If
        End If
    End Sub

    Public Sub NewEmployee()
        btnNewEmployee.PerformClick()
    End Sub

    Public Sub LoadEmployee(empnum As Decimal)
        Dim emp = DB.EMPLOYEEs.Where(Function(em) em.CONUM = CoNum AndAlso em.EMPNUM = empnum) _
                    .Select(Function(em) New EmployeeList With {.EmpNum = em.EMPNUM, .FirstName = em.F_NAME, .LastName = em.L_NAME, .MiddleName = em.M_NAME, .DepNum = em.DEPTNUM, .StartDate = em.START_DATE, .TermDate = em.TERM_DATE, .Ssn = em.SSN, .Tin = em.TIN, .DivNum = em.DIVNUM}).Single
        LoadEmployee(emp)
    End Sub

    Public Sub LoadEmployee(_employee As EmployeeList)
        Try
            Logger.Debug("Loding Employee. Co#: {Conum} Emp#: {Empnum}", CoNum, _employee.EmpNum)
            Me.EmployeeID = _employee.EmpNum
            SelectedEmployee = _employee
            lcEmpName.Text = $"{SelectedEmployee.LastName}, {SelectedEmployee.FirstName} ({SelectedEmployee.EmpNum})"
            LoadPayHistory()

            If Not _IsLoading Then
                LoadEmployee()
            End If
        Finally
            _IsLoading = False
        End Try
    End Sub

    Dim lastEmpNum As Decimal?
    Private Sub LoadPayHistory()
        If TabbedControlGroup1.SelectedTabPage Is lcgPayHistory Then
            If lastEmpNum.HasValue AndAlso SelectedEmployee.EmpNum = lastEmpNum.Value Then Exit Sub
            UcEmployeePayHistory2.LoadPayHistory(CoNum, EmployeeID)
        End If
    End Sub

    Sub LoadEmployee(Optional ByVal NewEmployeeEnt As EMPLOYEE = Nothing,
                     Optional ByVal NewStateInfoList As List(Of STATE_EE_INFO) = Nothing,
                     Optional ByVal NewLocalInfoList As List(Of LOCAL_EE_INFO) = Nothing,
                     Optional ByVal NewCheckOverridesList As List(Of pr_batch_overrides_setup) = Nothing,
                     Optional ByVal NewSecondChecksList As List(Of CoOptions_SecondCheckPayCode) = Nothing)
        _IsLoading = True
        ToBeValidated = NewEmployeeEnt IsNot Nothing
        If EmployeeID <> 0 Then
            EmployeeEnt = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.EMPNUM = EmployeeID).SingleOrDefault
            StateInfoEnt = (From A In DB.STATE_EE_INFOs Where A.CONUM = CoNum AndAlso A.EMPNUM = EmployeeID).ToList
            LocalInfoEntList = (From A In DB.local_ee_infos Where A.conum = CoNum AndAlso A.empnum = EmployeeID).ToList

            For Each item In LocalInfoEntList
                Dim local = DB.LOCALs.Where(Function(l) l.CODE = item.local_id).FirstOrDefault()
                item.LocalDescription = String.Format("{0} - ({1}) {2}", local.CODE, local.STATE, local.DESCRIPTION)
            Next

            CheckOverrides = (From A In DB.pr_batch_overrides_setups Where A.CoNum = CoNum AndAlso A.EmpNum = EmployeeID).ToList
            CoOptionsSecondCheck = (From A In DB.CoOptions_SecondCheckPayCodes Where A.CoNum = CoNum AndAlso A.EmpNum = Me.EmployeeID Order By A.PayCode).ToList
            'Me.lblEmployeeTitle.Text = EmployeeEnt.F_NAME & " " & EmployeeEnt.L_NAME
            'Me.Text = "Edit Employee"
            UcNotes1.LoadNotes(DB, CoNum, EmployeeID)
            Dim IsReguler = EmployeeEnt.EMP_TYPE = "REGULAR"
            Me.TINTextEdit.Visible = Not IsReguler
        Else
            lcEmpName.Text = "**Add New Employee**"
            Dim NextEmployeeID = Me.GetNextEmployeeID
            If NewEmployeeEnt Is Nothing Then
                EmployeeEnt = New EMPLOYEE With {.CONUM = CoNum}
            Else
                EmployeeEnt = NewEmployeeEnt
            End If

            If Not chkUseSameEmpID.Checked Then
                EmployeeEnt.EMPNUM = NextEmployeeID
            End If

            EmployeeEnt.START_DATE = Today
            EmployeeEnt.rowguid = Guid.NewGuid
            SetEmployeeDefaults(If(NewEmployeeEnt IsNot Nothing, Nothing, EmployeeEnt))
            If NewStateInfoList Is Nothing Then
                StateInfoEnt = New List(Of STATE_EE_INFO)
                StateInfoEnt.Add(New STATE_EE_INFO With {.CONUM = CoNum, .EMPNUM = EmployeeEnt.EMPNUM})
                SetStateDefaults(StateInfoEnt(0))
            Else
                StateInfoEnt = NewStateInfoList
            End If
            If NewLocalInfoList Is Nothing Then
                Me.LocalInfoEntList = New List(Of LOCAL_EE_INFO)
                LocalInfoEntList.Add(New LOCAL_EE_INFO With {.conum = CoNum, .empnum = EmployeeEnt.EMPNUM})
            Else
                Me.LocalInfoEntList = NewLocalInfoList
            End If
            If NewCheckOverridesList Is Nothing Then
                CheckOverrides = New List(Of pr_batch_overrides_setup)
            Else
                CheckOverrides = NewCheckOverridesList
            End If
            If NewSecondChecksList Is Nothing Then
                CoOptionsSecondCheck = New List(Of CoOptions_SecondCheckPayCode)
            Else
                CoOptionsSecondCheck = NewSecondChecksList
            End If
            'SetEmployeeDefaults(If(NewEmployeeEnt IsNot Nothing, Nothing, EmployeeEnt), If(NewStateInfoList IsNot Nothing, Nothing, StateInfoEnt(0)))

            If EmployeeEnt.UCI_STATE Is Nothing Then
                If CoStates.Length = 1 Then
                    EmployeeEnt.UCI_STATE = CoStates(0)
                Else
                    Dim DefaultState = (From A In DB.CO_UDFs Where A.CONUM = CoNum AndAlso A.UDF_DESCR = "%WEBSTDEF" Select A.UDF_STRING).FirstOrDefault
                    If DefaultState IsNot Nothing AndAlso CoStates.Contains(DefaultState) Then
                        EmployeeEnt.UCI_STATE = DefaultState
                    End If
                End If
            End If
            If EmployeeEnt.UCI_STATE IsNot Nothing Then
                If NewStateInfoList Is Nothing Then
                    StateInfoEnt(0).STATE = EmployeeEnt.UCI_STATE
                    StateInfoEnt(0).ST_STATUS = GetStateMerriedStatus(StateInfoEnt(0).STATE)
                End If
            End If


            If EmployeeEnt.PAY_FREQ Is Nothing Then
                Dim Freq = (From A In DB.CALENDAR_RULES Where A.conum = CoNum AndAlso A.status = "Active" Order By A.period_id Select A.frequency).FirstOrDefault
                If Freq IsNot Nothing Then
                    EmployeeEnt.PAY_FREQ = Freq
                Else
                    EmployeeEnt.PAY_FREQ = "Weekly"
                End If
                EmployeeEnt.default_hours = CalculateDefaultHours(EmployeeEnt.PAY_FREQ)
            End If

            'Me.Text = "Add New Employee"
        End If

        Dim AvailStates As String()
        If Me.StateInfoEnt Is Nothing Then
            AvailStates = (From A In CoStates Order By A).ToArray
        Else
            Dim SelectedStates = (From A In Me.StateInfoEnt Select A.STATE).ToList
            AvailStates = (From A In CoStates Where Not SelectedStates.Contains(A) Order By A).ToArray
        End If
        Me.lstAvailableStates.Items.Clear()
        DicAvailableStates = (From A In USStates.GetList Where AvailStates.Contains(A.Key)).ToDictionary(Function(p) p.Key, Function(p) p.Value)
        Me.lstAvailableStates.DataSource = New BindingSource(DicAvailableStates, Nothing)
        'Me.lstAvailableStates.Items.AddRange(AvailStates)

        Dim SelectedLocalCodes = (From A In Me.LocalInfoEntList Select A.local_id).ToList
        If Not SelectedLocalCodes Is Nothing Then
            DicAvailableLocals = (From A In DicAvailableLocals Where Not SelectedLocalCodes.Contains(A.Key)).ToDictionary(Function(p) p.Key, Function(p) p.Value)
        End If
        Me.lstAvailLocals.DataSource = New BindingSource(DicAvailableLocals, Nothing)

        'Bindings
        Me.EMPLOYEEBindingSource.DataSource = EmployeeEnt
        Me.STATE_EE_INFOBindingSource.DataSource = StateInfoEnt
        Me.LOCAL_EE_INFOBindingSource.DataSource = LocalInfoEntList

        Dim W4Stylle = EmployeeEnt.w4_style.GetValueOrDefault(0)
        Me.rgrp_w4Style.EditValue = W4Stylle

        Me.Pr_batch_overrideBindingSource.DataSource = CheckOverrides
        If CheckOverrides.Count = 0 Then
            Me.Pr_batch_overrideBindingSource_CurrentChanged(Me, New EventArgs)
        End If

        Me.CoOptionsSecondCheckPayCodeBindingSource.DataSource = CoOptionsSecondCheck

        RefreshAutoPays()

        Me.DIVNUMLookUpEdit_Validated(Me, New EventArgs)

        enableDDGroup(EmployeeEnt.DD_ACC_TYPE_1 & "" <> "None", "1")
        enableDDGroup(EmployeeEnt.DD_ACC_TYPE_2 & "" <> "None", "2")

        formatPercentGroups("1", Me.DD_SPLIT_MET_1ComboBoxEdit, EmployeeEnt.DD_SPLIT_AMT_1 & "", Me.DD_SPLIT_AMT_1TextEdit, If(IsNothing(EmployeeEnt.DD_SPLIT_AMT_1), 0, EmployeeEnt.DD_SPLIT_AMT_1))
        formatPercentGroups("2", Me.DD_SPLIT_MET_2ComboBoxEdit, EmployeeEnt.DD_SPLIT_AMT_2 & "", Me.DD_SPLIT_AMT_2TextEdit, If(IsNothing(EmployeeEnt.DD_SPLIT_AMT_2), 0, EmployeeEnt.DD_SPLIT_AMT_2))

        If Not EmployeeEnt.DIVNUM.HasValue Then
            If DIVISIONBindingSource.DataSource.Count = 1 Then
                EmployeeEnt.DIVNUM = DIVISIONBindingSource.DataSource(0).DDIVNUM
            End If
        End If

        If EmployeeEnt.DIVNUM.HasValue AndAlso Not EmployeeEnt.DEPTNUM.HasValue Then
            DIVNUMLookUpEdit_Validated(Nothing, Nothing)
            If DEPARTMENTBindingSource.DataSource.Count = 1 Then
                EmployeeEnt.DEPTNUM = DEPARTMENTBindingSource.DataSource(0).DEPTNUM
            End If
        End If


        Me.EMPNUMTextEdit.Properties.ReadOnly = Me.EmployeeID <> 0
        Me.TERM_DATEDateEdit.Enabled = Me.EmployeeID <> 0

        Me.tabAutoPaysAndDeds.PageEnabled = Me.EmployeeID <> 0
        'SetStateOptions()
        If Me.TabbedControlGroup1.SelectedTabPageIndex = 0 Then
            'Me.F_NAMETextEdit.Select()
        End If
        Me.PanelControlPayrollTax.Refresh()
        SetTerminateDisplay()
        SetParsonageDisplay()

        Me.btnSaveAndCopyPaysLI.ContentVisible = False
        Me.btnCopy.Enabled = True


        Try
            Dim hasDd = Query(Of String)("Select IIF(TRAN_BANK_DD NOT IN ('*********','NATPAYMNT'), 'No', 'Yes')  FROM Company Where CONUM = @conum", New With {CoNum}).Single
            'gcDdSetup.ShowCaption = hasDd = "No"

            gcDdSetup.Text = IIf(hasDd = "No", "THIS COMPANY IS NOT SET UP FOR DIRECT DEPOSIT", "DIRECT DEPOSIT SETUP")
            gcDdSetup.AppearanceCaption.ForeColor = IIf(hasDd = "No", Color.Red, Color.Blue)

            DD_ACC_TYPE_1ComboBoxEdit.Enabled = hasDd = "Yes"
            DD_ACC_TYPE_2ComboBoxEdit.Enabled = hasDd = "Yes"
            DD_ACC_TYPE_3ComboBoxEdit.Enabled = hasDd = "Yes"
            DD_ACC_TYPE_4ComboBoxEdit.Enabled = hasDd = "Yes"

            sbRequestDDSetup.Visible = hasDd = "No"
        Catch ex As Exception
            DisplayErrorMessage("Error getting Company DD status.", ex)
        End Try

        Dim PrintW2Menu = utilsMenu.Items().FirstOrDefault(Function(f) f.Caption = "Print W2")
        PrintW2Menu.Enabled = EmployeeEnt.EMP_TYPE = "REGULAR"

        _IsLoading = False
    End Sub

    Private Sub DIVNUMLookUpEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DIVNUMLookUpEdit.Validated
        Dim DivNum As Decimal?
        DivNum = nz(DIVNUMLookUpEdit.EditValue, Nothing)
        If _IsLoading Then DivNum = EmployeeEnt.DIVNUM
        Dim Depts = (From A In DB.DEPARTMENTs Where A.CONUM = CoNum AndAlso A.DIVNUMD = DivNum AndAlso (A.DPACTIVE = "YES" OrElse A.DIVNUMD = EmployeeEnt.DIVNUM) Select A.DEPTNUM, A.DEPT_DESC, A.DIVNUMD, Descr = A.DEPTNUM & "-" & A.DEPT_DESC).ToList
        Me.DEPARTMENTBindingSource.DataSource = Depts
        If EmployeeEnt.DEPTNUM.HasValue Then
            Dim Dept = (From A In Depts Where A.DIVNUMD = DivNum AndAlso A.DEPTNUM = EmployeeEnt.DEPTNUM).FirstOrDefault
            If Dept Is Nothing Then
                EmployeeEnt.DEPTNUM = Nothing
            End If
        Else
            If Depts.Count = 1 Then
                EmployeeEnt.DEPTNUM = Depts(0).DEPTNUM
            End If
        End If
    End Sub

    Private Sub DIVNUMLookUpEdit_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles DIVNUMLookUpEdit.EditValueChanging
        Dim DivNum As Decimal?
        DivNum = e.NewValue
        'DivNum = nz(DIVNUMLookUpEdit.EditValue, Nothing)
        Dim Depts = (From A In DB.DEPARTMENTs Where A.CONUM = CoNum AndAlso A.DIVNUMD = DivNum AndAlso (A.DPACTIVE = "YES" OrElse A.DIVNUMD = EmployeeEnt.DIVNUM) Select A.DEPTNUM, A.DEPT_DESC, A.DIVNUMD, Descr = A.DEPTNUM & "-" & A.DEPT_DESC).ToList
        Me.DEPARTMENTBindingSource.DataSource = Depts
        If Depts.Count = 1 Then
            EmployeeEnt.DEPTNUM = Depts(0).DEPTNUM
        End If
    End Sub

    Private Sub DivNum2ndCheck_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DivNum2ndCheck.Validated
        Dim DivNum As Decimal?
        DivNum = nz(DivNum2ndCheck.EditValue, Nothing)
        Dim Depts = (From A In DB.DEPARTMENTs Where A.CONUM = CoNum AndAlso A.DIVNUMD = DivNum AndAlso (A.DPACTIVE = "YES" OrElse A.DIVNUMD = EmployeeEnt.DIVNUM) Select A.DEPTNUM, A.DEPT_DESC, A.DIVNUMD, Descr = A.DEPTNUM & "-" & A.DEPT_DESC).ToList
        Me.DEPARTMENTBindingSource2.DataSource = Depts
    End Sub

    Private Sub ZIPTextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ZIPTextEdit.Validated
        If Not Me.ZIPTextEdit.HasValue Then Return
        If Me.ZIPTextEdit.EditValue = Me.ZIPTextEdit.OldEditValue & "" Then Return
        Dim Zip = (From A In DB.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.ZIPTextEdit.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            Me.EmployeeEnt.ADDR_STATE = Zip.STATE
            Me.EmployeeEnt.CITY = Zip.CITY
        End If
    End Sub

    Private Sub SetEmployeeDefaults(ByRef EmployeeEnt As EMPLOYEE)
        Try
            If EmployeeEnt IsNot Nothing Then
                Dim Defaults = (From A In DB.EP_IMP_LISTs Where A.table_name = "EMPLOYEE" AndAlso A.table_column <> "PAY_FREQ" AndAlso A.default_value IsNot Nothing).ToList
                For Each defaultValue In Defaults
                    Dim Prop = EmployeeEnt.GetType.GetProperty(defaultValue.table_column, Reflection.BindingFlags.Public OrElse Reflection.BindingFlags.Instance OrElse Reflection.BindingFlags.IgnoreCase)
                    Dim t = If(Nullable.GetUnderlyingType(Prop.PropertyType), Prop.PropertyType)
                    Dim Value = If((defaultValue.default_value Is Nothing), Nothing, Convert.ChangeType(defaultValue.default_value, t))
                    Prop.SetValue(EmployeeEnt, Value)
                    'If defaultValue.data_type = "N" Then
                    '    Prop.SetValue(EmployeeEnt, Decimal.Parse(defaultValue.default_value), Nothing)
                    'Else
                    '    Prop.SetValue(EmployeeEnt, defaultValue.default_value, Nothing)
                    'End If
                Next
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error setting defaults on Employee.", ex)
        End Try
    End Sub

    Private Sub SetStateDefaults(ByRef StateInfoEnt As STATE_EE_INFO)
        If StateInfoEnt IsNot Nothing Then
            Dim Defaults = (From A In DB.EP_IMP_LISTs Where A.table_name = "STATE_EE_INFO" AndAlso A.default_value IsNot Nothing).ToList
            For Each defaultValue In Defaults
                Dim Prop = StateInfoEnt.GetType.GetProperty(defaultValue.table_column, Reflection.BindingFlags.Public OrElse Reflection.BindingFlags.Instance OrElse Reflection.BindingFlags.IgnoreCase)
                If defaultValue.data_type = "N" Then
                    Prop.SetValue(StateInfoEnt, Decimal.Parse(defaultValue.default_value), Nothing)
                Else
                    Prop.SetValue(StateInfoEnt, defaultValue.default_value, Nothing)
                End If
            Next
        End If
    End Sub

    Private Sub SetLocalDefaults(ByRef LocalInfoEnt As LOCAL_EE_INFO)
        If LocalInfoEnt IsNot Nothing Then
            Dim Defaults = (From A In DB.EP_IMP_LISTs Where A.table_name = "LOCAL_EE_INFO" AndAlso A.default_value IsNot Nothing).ToList
            For Each defaultValue In Defaults
                SetValue(LocalInfoEnt, defaultValue.table_column, defaultValue.default_value)
                'Dim Prop = LocalInfoEnt.GetType.GetProperty(defaultValue.table_column, Reflection.BindingFlags.Public OrElse Reflection.BindingFlags.Instance OrElse Reflection.BindingFlags.IgnoreCase)
                'If defaultValue.data_type = "N" Then
                '    Prop.SetValue(LocalInfoEnt, Decimal.Parse(defaultValue.default_value), Nothing)
                'Else
                '    Prop.SetValue(LocalInfoEnt, defaultValue.default_value, Nothing)
                'End If
            Next
        End If
    End Sub

    Private Function GetNextEmployeeID() As Decimal
        Dim maxEmpNum = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum Select A.EMPNUM)
        If maxEmpNum.Count = 0 Then
            Return 1
        Else
            Return maxEmpNum.Max + 1
        End If
    End Function

    Private Sub FED_STATUSTextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles FED_STATUSTextEdit.Validated, FED_DEPSTextEdit.Validated
        UpdateStateAndLocals()
    End Sub

    Private Sub FED_STATUSTextEdit_EditValueChanging(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles FED_STATUSTextEdit.EditValueChanging, FED_DEPSTextEdit.EditValueChanging
        If Me.StateInfoEnt.Count = 0 OrElse String.IsNullOrEmpty(Me.StateInfoEnt(0).STATE) Then
            DisplayMessageBox("Please enter a state first")
            e.Cancel = True
        End If
    End Sub

    Private Function UpdateStateAndLocals() As Boolean
        If _IsLoading Then Return True
        If Me.StateInfoEnt.Count = 0 OrElse String.IsNullOrEmpty(Me.StateInfoEnt(0).STATE) Then
            DisplayMessageBox("Please enter a state first")
            Return False
        End If
        For Each StateRec In Me.StateInfoEnt
            If StateRec.ALTW4 = "NO" Then
                StateRec.ST_STATUS = GetStateMerriedStatus(StateRec.STATE)
                StateRec.ST_DEPS = Me.FED_DEPSTextEdit.EditValue

                Dim State = StateRec.STATE
                Dim StateLocalCodes = (From A In Me.Locals Join B In Me.CoLocalCodes On A.CODE Equals B.LOC_ID Where A.STATE = State Select A.CODE).Distinct.ToList
                Dim Locals = (From A In Me.LocalInfoEntList Where StateLocalCodes.Contains(A.local_id)).ToList
                For Each LocalRec In Locals
                    If LocalRec.alternate_w4 = "NO" Then
                        LocalRec.local_status = StateRec.ST_STATUS
                        LocalRec.dependents = StateRec.ST_DEPS
                    End If
                Next
            End If
        Next
        Return True
    End Function

    Sub AccountFieldsRequired()
        For i = 1 To 4
            Dim cbe As ComboBoxEdit = Me.gcDdSetup.Controls("DD_ACC_TYPE_" & i & "ComboBoxEdit")
            If cbe.Text <> "None" AndAlso cbe.Text <> "" Then

                'Require all fields for account that is not None
                Dim ctl As Control
                For Each ctl In Me.gcDdSetup.Controls
                    If InStr(ctl.Name, "Edit") > 0 AndAlso InStr(ctl.Name, i) > 0 Then
                        Dim ReqField As DevExpress.XtraEditors.BaseEdit = ctl
                        If Not ReqField.HasValue Then
                            Me.DxErrorProvider1.SetError(ReqField, String.Format("Required because account type {0}'st account is not 'None'", i))
                        End If
                    End If
                Next ctl

            End If
        Next i
    End Sub

    'Sub SsnTinValidate(ByVal te As TextEdit)
    '    Dim strTE As String = Replace(te.Text, "-", "")
    '    If te.HasValue And Len(strTE) <> 9 Then
    '        Me.DxErrorProvider1.SetError(te, String.Format("{0} must be 9 digits", te.Name.Substring(0, 3)))
    '    End If
    'End Sub


    Private Function Save() As Boolean
        Dim results As Boolean = False

        If HasAlerts() Then
            HandleAlerts()
            Return False
        End If

        Try
            Me.DxErrorProvider1.ClearErrors()

            EmployeeEnt.w4_style = Me.rgrp_w4Style.EditValue
            Dim ReqFields As New List(Of DevExpress.XtraEditors.BaseEdit)({Me.DIVNUMLookUpEdit, Me.DEPTNUMLookUpEdit, Me.EMP_TYPETextEdit, Me.F_NAMETextEdit, Me.L_NAMETextEdit, Me.STREETTextEdit, Me.CITYTextEdit, Me.ADDR_STATETextEdit, Me.ZIPTextEdit, Me.SSNTextEdit, Me.PAY_FREQTextEdit, Me.UCI_STATETextEdit, Me.FED_STATUSTextEdit, Me.FED_DEPSTextEdit, Me.ST_STATUSTextEdit, Me.START_DATEDateEdit})
            If EmployeeEnt.EMP_TYPE = "CONTRACT" Then
                ReqFields.Remove(Me.L_NAMETextEdit)
                ReqFields.Remove(Me.SSNTextEdit)
                ReqFields.Add(Me.TINTextEdit)
            End If

            For Each fld In ReqFields
                If fld.Visible AndAlso (Not fld.HasValue) Then
                    Me.DxErrorProvider1.SetError(fld, "Required")
                End If
            Next
            If Me.lstSelectedStates.SelectedValue Is Nothing Then
                Me.DxErrorProvider1.SetError(Me.lstSelectedStates, "Required")
            End If
            AccountFieldsRequired()

            Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
            If CurrentRow IsNot Nothing AndAlso Not ValidateCheckOverride(CurrentRow) Then
                Return False
            End If

            If DxErrorProvider1.HasErrors Then
                DisplayMessageBox("Required fields missing")
            ElseIf Me.StateInfoEnt Is Nothing OrElse Me.StateInfoEnt.Count = 0 OrElse String.IsNullOrEmpty(Me.StateInfoEnt(0).STATE) Then
                DisplayMessageBox("State entry is missing. Select a state from the list")
            Else
                Dim DefaultStateRes = (From A In Me.StateInfoEnt Where A.DEFAULT_RES = "YES").Count
                Dim DefaultStateWrk = (From A In Me.StateInfoEnt Where A.DEFAULT_WORK = "YES").Count
                If DefaultStateRes <> 1 OrElse DefaultStateWrk <> 1 Then
                    DisplayMessageBox("Default state not properly set")
                    Return False
                End If

                If ToBeValidated Then
                    If Not IsValidSSN(EmployeeEnt.EMPNUM, EmployeeEnt.SSN) Then
                        'Me.SSNTextEdit.Select()
                        Return False
                    End If
                End If

                EmployeeEnt.MEDICARE_EXE_FGE = EmployeeEnt.OASDI_EXE_FGE
                If EmployeeID = 0 Then 'New Record
                    DB.EMPLOYEEs.InsertOnSubmit(EmployeeEnt)
                    If CoNewHire = "N/A" OrElse EmployeeEnt.EMP_TYPE = "CONTRACT" Then
                        EmployeeEnt.EMPNEWHIRE = "N/A"
                    Else
                        If EmployeeEnt.SSN <> "***********" Then
                            EmployeeEnt.EMPNEWHIRE = "READY"
                        Else
                            EmployeeEnt.EMPNEWHIRE = "WAITING"
                        End If
                    End If
                End If
                For Each rec In Me.StateInfoEnt
                    rec.EMPNUM = EmployeeEnt.EMPNUM
                    rec.CONUM = EmployeeEnt.CONUM
                    If rec.rowguid = Guid.Empty Then
                        rec.rowguid = Guid.NewGuid
                        DB.STATE_EE_INFOs.InsertOnSubmit(rec)
                    End If
                Next
                For Each rec In Me.LocalInfoEntList
                    If Not rec.local_id = 0 Then
                        rec.empnum = EmployeeEnt.EMPNUM
                        rec.conum = EmployeeEnt.CONUM
                        If rec.rowguid = Guid.Empty Then
                            rec.rowguid = Guid.NewGuid
                            DB.local_ee_infos.InsertOnSubmit(rec)
                        End If
                    End If
                Next
                For Each rec In CheckOverrides
                    If rec.rowguid = Guid.Empty Then
                        rec.EmpNum = Me.EmployeeEnt.EMPNUM
                        rec.CoNum = Me.EmployeeEnt.CONUM
                        rec.rowguid = Guid.NewGuid
                        DB.pr_batch_overrides_setups.InsertOnSubmit(rec)
                    End If
                Next

                For Each n In CoOptionsSecondCheck
                    If n.ID = 0 Then
                        n.CoNum = Me.CoNum
                        n.EmpNum = Me.EmployeeEnt.EMPNUM
                        DB.CoOptions_SecondCheckPayCodes.InsertOnSubmit(n)
                    End If
                Next
S:
                Me.Changes = Me.GetChanges
                If Not DB.SaveChanges() Then
                    Return False ' Exit if save failed due to concurrency conflict
                End If
                Dim isNewEmp = EmployeeID = 0
                EmployeeID = EmployeeEnt.EMPNUM
                results = True

                If Not isNewEmp AndAlso SelectedEmployee IsNot Nothing Then
                    SelectedEmployee.FirstName = EmployeeEnt.F_NAME
                    SelectedEmployee.LastName = EmployeeEnt.L_NAME
                    SelectedEmployee.MiddleName = EmployeeEnt.M_NAME
                    SelectedEmployee.DivNum = EmployeeEnt.DIVNUM
                    SelectedEmployee.DepNum = EmployeeEnt.DEPTNUM
                    SelectedEmployee.Ssn = EmployeeEnt.SSN
                    SelectedEmployee.Tin = EmployeeEnt.TIN
                    SelectedEmployee.StartDate = EmployeeEnt.START_DATE
                    SelectedEmployee.TermDate = EmployeeEnt.TERM_DATE
                    LoadEmployee()
                    lcEmpName.Text = $"{SelectedEmployee.LastName}, {SelectedEmployee.FirstName} ({SelectedEmployee.EmpNum})"
                End If
                RaiseEvent EndEditResults(New EmployeeEndEditResults(DialogResult.OK, EmployeeEnt, Me.Changes, isNewEmp))
            End If
            'Catch ex As Linq.ChangeConflictException
            '    Dim msg = "Cannot save Employee. The record has been modified by someone else" & vbCrLf
            '    Dim Records = DB.ChangeConflicts
            '    For Each rec In Records
            '        If rec.memberconflicts?.Count > 0 Then
            '            msg &= Environment.NewLine
            '            msg &= "--" & rec.object.GetType.ToString.Split(".").Last & "--" & Environment.NewLine
            '            For Each mem In rec.memberconflicts
            '                If mem.member.name <> "timestamp" Then
            '                    msg &= $"{mem.member.name}: Original = {mem.originalvalue}, Current DB Value = {mem.databasevalue}" & vbCrLf
            '                End If
            '            Next
            '        End If
            '    Next
            '    'msg &= vbCrLf & "Would you like to still save your changes?" & vbCrLf & "Click 'Yes' to save your changes, or 'No' to refresh the screen with the current values."
            '    msg &= "The employee will be reloaded, and YOUR CHANGES WILL BE LOST."
            '    For Each rec In Records
            '        'rec.Resolve(Linq.RefreshMode.KeepChanges)
            '        rec.Resolve(dbEPDataDataContext.ChangeConflict.Linq.RefreshMode.OverwriteCurrentValues)
            '    Next
            '    'If XtraMessageBox.Show(msg, ex.Message, MessageBoxButtons.YesNo, MessageBoxIcon.Error) = DialogResult.Yes Then
            '    '    GoTo S
            '    'Else
            '    '    Me.EMPLOYEEBindingSource.ResetBindings(False)
            '    '    Me.LOCAL_EE_INFOBindingSource.ResetBindings(False)
            '    '    Me.STATE_EE_INFOBindingSource.ResetBindings(False)
            '    'End If
            '    DisplayErrorMessage(msg, ex)
            '    Me.btnCancel.PerformClick()
        Catch ex As DbUpdateConcurrencyException
            Dim msg = "Cannot save Employee. The record has been modified by someone else" & vbCrLf

            ' Get the conflicting entity entries
            For Each entry In ex.Entries
                ' Check if the entity is your Employee type (adjust as needed)
                If TypeOf entry.Entity Is Employee Then
                    Dim employeeEntry = DirectCast(entry, Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry(Of Employee))

                    Dim currentValues = employeeEntry.CurrentValues
                    Dim databaseValues = employeeEntry.GetDatabaseValues() ' Get the current values from the database
                    Dim originalValues = employeeEntry.OriginalValues

                    If databaseValues IsNot Nothing Then
                        msg &= Environment.NewLine
                        msg &= "--" & employeeEntry.Metadata.DisplayName & "--" & Environment.NewLine ' Use entity name

                        For Each prop In currentValues.Properties
                            ' Avoid showing the timestamp/rowversion property if you've configured one
                            If prop.Name.ToLower() <> "timestamp" AndAlso prop.Name.ToLower() <> "rowversion" Then
                                Dim originalValue = originalValues(prop)
                                Dim databaseValue = databaseValues(prop)

                                msg &= $"{prop.Name}: Original = {originalValue}, Current DB Value = {databaseValue}" & vbCrLf
                            End If
                        Next

                        ' Resolve the conflict by overwriting with database values (Database wins)
                        employeeEntry.OriginalValues.SetValues(databaseValues)
                    Else
                        ' Handle the case where the entity was deleted in the database
                        msg &= "The entity was deleted by another user." & vbCrLf
                        ' Optionally mark the entity as deleted or detach it
                        ' employeeEntry.State = EntityState.Deleted
                    End If
                End If
            Next

            'msg &= vbCrLf & "Would you like to still save your changes?" & vbCrLf & "Click 'Yes' to save your changes, or 'No' to refresh the screen with the current values."
            msg &= "The employee will be reloaded, and YOUR CHANGES WILL BE LOST."

            ' Display the conflict information to the user
            DisplayErrorMessage(msg, ex)

            ' Refresh the UI with the latest data from the database
            ' You might need to reload the entity from the database or re-query the data
            Me.btnCancel.PerformClick() ' This might trigger a reload of the form/data
        Catch ex As Exception
            DisplayErrorMessage("Error saving employee changes", ex)
        End Try
        Return results
    End Function

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        DxErrorProvider1.ClearErrors()
        LoadData()
        RaiseEvent EndEditResults(New EmployeeEndEditResults(DialogResult.Cancel, Nothing, Nothing, False))
        LoadEmployee()
    End Sub

    Public ReadOnly Property GetChanges() As String
        Get
            'Dim Modifications = DB.EMPLOYEEs.GetModifiedMembers(EmployeeEnt)
            'Dim sb As New System.Text.StringBuilder
            'For Each mv In Modifications
            '    sb.AppendLine(String.Format("Employee{0}{1}{0}{2}{0}{3}", vbTab, mv.Member.Name, mv.OriginalValue.ToString, mv.CurrentValue.ToString))
            'Next

            'Dim tableChanges As New List(Of TableChange)
            'For Each change In DB.GetChangeSet.Updates
            '    Dim TableName = change.GetType.Name
            '    Dim Modifications = DB.GetTable(change.GetType).GetModifiedMembers(change)
            '    For Each mv In Modifications
            '        Dim tc As New TableChange With {.TableName = TableName,
            '                                        .FieldName = mv.Member.Name,
            '                                        .OldValue = If(Not mv.OriginalValue Is Nothing, mv.OriginalValue.ToString, Nothing),
            '                                        .NewValue = If(Not mv.CurrentValue Is Nothing, mv.CurrentValue.ToString, Nothing)}
            '        If TableName = "STATE_EE_INFO" Then
            '            tc.Key = change.GetType.GetProperty("STATE").GetValue(change, Nothing)
            '        ElseIf TableName = "LOCAL_EE_INFO" Then
            '            tc.Key = change.GetType.GetProperty("local_id").GetValue(change, Nothing)
            '        End If
            '        tableChanges.Add(tc)
            '    Next
            'Next

            Dim tableChanges As New List(Of TableChange)

            ' Assuming context is your EF DbContext object
            For Each entityEntry In DB.ChangeTracker.Entries().Where(Function(e) e.State = Microsoft.EntityFrameworkCore.EntityState.Modified)
                Dim tableName As String = entityEntry.Entity.GetType().Name
                Dim modifications = entityEntry.Properties.Where(Function(p) p.IsModified)

                For Each mv In modifications
                    Dim tc As New TableChange With {
            .TableName = tableName,
            .FieldName = mv.Metadata.Name,
            .OldValue = If(mv.OriginalValue IsNot Nothing, mv.OriginalValue.ToString(), Nothing),
            .NewValue = If(mv.CurrentValue IsNot Nothing, mv.CurrentValue.ToString(), Nothing)
        }

                    If tableName = "STATE_EE_INFO" Then
                        ' Assuming entityEntry.Entity implements a property called STATE
                        tc.Key = entityEntry.OriginalValues("STATE")
                    ElseIf tableName = "LOCAL_EE_INFO" Then
                        ' Assuming entityEntry.Entity implements a property called local_id
                        tc.Key = entityEntry.OriginalValues("local_id")
                    End If

                    tableChanges.Add(tc)
                Next
            Next

            For Each change In DB.GetChangeSet.Inserts
                Dim TableName = change.GetType.Name
                Dim tc As New TableChange With {.TableName = TableName,
                                                .FieldName = "New Record"}
                If TableName = "STATE_EE_INFO" Then
                    tc.Key = change.GetType.GetProperty("STATE").GetValue(change, Nothing)
                ElseIf TableName = "LOCAL_EE_INFO" Then
                    tc.Key = change.GetType.GetProperty("local_id").GetValue(change, Nothing)
                End If
                tableChanges.Add(tc)
            Next
            For Each change In DB.GetChangeSet.Deletes
                Dim TableName = change.GetType.Name
                Dim tc As New TableChange With {.TableName = TableName,
                                                .FieldName = "Deleted"}
                If TableName = "STATE_EE_INFO" Then
                    tc.Key = change.GetType.GetProperty("STATE").GetValue(change, Nothing)
                ElseIf TableName = "LOCAL_EE_INFO" Then
                    tc.Key = change.GetType.GetProperty("local_id").GetValue(change, Nothing)
                End If
                tableChanges.Add(tc)
            Next

            Return String.Join(vbNewLine, tableChanges)
        End Get
    End Property

    Sub SetStateOptions()
        If StateInfoEnt IsNot Nothing Then
            For Each StateRec In Me.StateInfoEnt
                If StateRec.ALTW4 & "" <> "YES" Then
                    StateRec.ST_WH_EXE_FGE = EmployeeEnt.FED_WH_EXE_FGE
                End If
                If StateRec.ST_WH_EXE_FGE = 1 Then
                    'StateRec.ST_WH_EXTRA = Nothing
                    'StateRec.ST_WH_FIXED = Nothing
                End If
                If StateRec.ST_WH_EXTRA.HasValue Then
                    StateRec.ST_WH_FIXED = Nothing
                ElseIf StateRec.ST_WH_FIXED.HasValue Then
                    StateRec.ST_WH_EXTRA = Nothing
                End If
            Next
        End If
    End Sub

    Private Sub StateValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ALTW4TextEdit.CheckedChanged
        ',ST_WH_EXE_FGETextEdit.CheckedChanged, ST_WH_EXTRATextEdit.EditValueChanged, ST_WH_FIXEDTextEdit.EditValueChanged
        If sender IsNot Nothing Then
            Dim Cntrl As DevExpress.XtraEditors.BaseEdit = sender
            Cntrl.DoValidate()
            UpdateStateAndLocals()
        End If

        Dim StateRec As STATE_EE_INFO = Me.STATE_EE_INFOBindingSource.Current
        Dim NotFieldSeperate = StateRec.ALTW4 & "" <> "YES" AndAlso Me.ALTW4TextEdit.Visible

        Me.ST_WH_EXE_FGETextEdit.Properties.ReadOnly = NotFieldSeperate
        Me.ST_STATUSTextEdit.Properties.ReadOnly = NotFieldSeperate
        Me.ST_DEPSTextEdit.Properties.ReadOnly = NotFieldSeperate
        Me.ST_WH_EXTRATextEdit.Properties.ReadOnly = StateRec.ST_WH_EXE_FGE = 1 OrElse StateRec.ST_WH_FIXED.HasValue
        Me.ST_WH_FIXEDTextEdit.Properties.ReadOnly = StateRec.ST_WH_EXE_FGE = 1 OrElse StateRec.ST_WH_EXTRA.HasValue
    End Sub

    Private Sub PAY_FREQTextEdit_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PAY_FREQTextEdit.SelectedIndexChanged
        If _IsLoading Then Return
        PAY_FREQTextEdit.DoValidate()
        EmployeeEnt.default_hours = CalculateDefaultHours(EmployeeEnt.PAY_FREQ)
    End Sub

    'Private Sub UCI_STATETextEdit_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles UCI_STATETextEdit.Validating
    '    If Not {"NY", "NJ"}.Contains(EmployeeEnt.UCI_STATE) Then
    '        DisplayErrorMessage("For this State setup in EP")
    '        e.Cancel = True
    '    End If
    'End Sub

    Private Sub btnTerminate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTerminate.Click
        Dim btn As Control = sender
        If btn.Text = "Terminate" Then
            EmployeeEnt.TERM_DATE = Today
            Me.TERM_DATEDateEdit.Select()
        Else
            Dim ResultEmpDDPriorTermination = Query("SELECT TOP 1 * FROM custom.EmpDDPriorTermination WHERE @CONUM = CONUM AND EMPNUM = @EMPNUM ORDER BY DateUpdated DESC",
                  New SqlParameter("@CONUM", EmployeeEnt.CONUM),
                  New SqlParameter("@EMPNUM", EmployeeEnt.EMPNUM)
            )

            If (ResultEmpDDPriorTermination.Rows.Count = 1) Then
                Dim EmpDDPriorTermination = ResultEmpDDPriorTermination.Rows(0)

                Dim emp As EMPLOYEE = EMPLOYEEBindingSource(0)

                If (nz(EmpDDPriorTermination("DD_ACC_NO_1"), "") = nz(emp.DD_ACC_NO_1, "") AndAlso nz(EmpDDPriorTermination("DD_ACC_TYPE_1"), "") <> nz(emp.DD_ACC_TYPE_1, "")) _
                        OrElse (nz(EmpDDPriorTermination("DD_ACC_NO_2"), "") = nz(emp.DD_ACC_NO_2, "") AndAlso nz(EmpDDPriorTermination("DD_ACC_TYPE_2"), "") <> nz(emp.DD_ACC_TYPE_2, "")) _
                        OrElse (nz(EmpDDPriorTermination("DD_ACC_NO_3"), "") = nz(emp.DD_ACC_NO_3, "") AndAlso nz(EmpDDPriorTermination("DD_ACC_TYPE_3"), "") <> nz(emp.DD_ACC_TYPE_3, "")) _
                        OrElse (nz(EmpDDPriorTermination("DD_ACC_NO_4"), "") = nz(emp.DD_ACC_NO_4, "") AndAlso nz(EmpDDPriorTermination("DD_ACC_TYPE_4"), "") <> nz(emp.DD_ACC_TYPE_4, "")) Then

                    Alerts.hadPriorDD = True
                    gcEmpPriorDD.Visible = True

                    gcEmpPriorDD.Controls("lblPriorDDAcctType1").Text = IIf(nz(EmpDDPriorTermination("DD_ACC_NO_1"), "") <> nz(emp.DD_ACC_NO_1, "") OrElse nz(EmpDDPriorTermination("DD_ACC_TYPE_1"), "") = nz(emp.DD_ACC_TYPE_1, ""), "", nz(EmpDDPriorTermination("DD_ACC_TYPE_1"), "None"))
                    gcEmpPriorDD.Controls("lblPriorDDAcctType2").Text = IIf(nz(EmpDDPriorTermination("DD_ACC_NO_2"), "") <> nz(emp.DD_ACC_NO_2, "") OrElse nz(EmpDDPriorTermination("DD_ACC_TYPE_2"), "") = nz(emp.DD_ACC_TYPE_2, ""), "", nz(EmpDDPriorTermination("DD_ACC_TYPE_2"), "None"))
                    gcEmpPriorDD.Controls("lblPriorDDAcctType3").Text = IIf(nz(EmpDDPriorTermination("DD_ACC_NO_3"), "") <> nz(emp.DD_ACC_NO_3, "") OrElse nz(EmpDDPriorTermination("DD_ACC_TYPE_3"), "") = nz(emp.DD_ACC_TYPE_3, ""), "", nz(EmpDDPriorTermination("DD_ACC_TYPE_3"), "None"))
                    gcEmpPriorDD.Controls("lblPriorDDAcctType4").Text = IIf(nz(EmpDDPriorTermination("DD_ACC_NO_4"), "") <> nz(emp.DD_ACC_NO_4, "") OrElse nz(EmpDDPriorTermination("DD_ACC_TYPE_4"), "") = nz(emp.DD_ACC_TYPE_4, ""), "", nz(EmpDDPriorTermination("DD_ACC_TYPE_4"), "None"))
                End If
            End If

            If DirectCast(GridDeds.DataSource, BindingSource).Count <> 0 OrElse DirectCast(GridPays.DataSource, BindingSource).Count <> 0 Then
                Alerts.hadPriorPaysDeds = True
                lciPriorPaysDeds.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            End If

            If HasAlerts() Then
                HandleAlerts()
            End If

            EmployeeEnt.TERM_DATE = Nothing
            EmployeeEnt.START_DATE = DateTime.Now
        End If
        SetTerminateDisplay()
        SetParsonageDisplay()
    End Sub

    Sub SetTerminateDisplay()
        Me.pnlTerminate.Visible = EmployeeID <> 0
        If EmployeeEnt.TERM_DATE.HasValue Then
            Me.btnTerminate.Text = "Activate"
            Me.lblEmployeeStatus.Text = "Terminated"
            Me.lblEmployeeStatus.ForeColor = Color.Red
        Else
            Me.btnTerminate.Text = "Terminate"
            Me.lblEmployeeStatus.Text = "Active"
            Me.lblEmployeeStatus.ForeColor = Color.Black
        End If
    End Sub

    Sub SetParsonageDisplay()
        btnParsonage.Visible = EmployeeID <> 0
        If EmployeeEnt.USERDEF21 = "P30" Then
            btnParsonage.Checked = True
            btnParsonage.BackColor = Color.Pink
            btnParsonage.Text = "UnParsonage"
        Else
            btnParsonage.Checked = False
            btnParsonage.Text = "Mark Emp as Parsonage"
            btnParsonage.BackColor = Color.FromArgb(235, 236, 239)
        End If
    End Sub

    Private Sub TERM_DATEDateEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TERM_DATEDateEdit.Validated
        SetTerminateDisplay()
    End Sub


    Private Sub frmEmployeeInfo_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.Enter AndAlso Me.ActiveControl IsNot Nothing Then
            If TypeOf Me.ActiveControl Is DevExpress.XtraEditors.BaseControl Then
                Dim nextControl = Me.GetNextControl(Me.ActiveControl, Not e.Shift)
                If nextControl Is Nothing Then
                    nextControl = Me.GetNextControl(Nothing, True)
                End If
                Me.ActiveControl = nextControl
                nextControl.Focus()
                e.SuppressKeyPress = True
            End If
        End If
    End Sub

    Private Sub Pr_batch_overrideBindingSource_CurrentChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Pr_batch_overrideBindingSource.CurrentChanged
        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        Me.grpSecondCheckTaxes.Visible = CurrentRow IsNot Nothing AndAlso CurrentRow.RecordType = "Second Check"
        Me.grpScheduling.Visible = CurrentRow IsNot Nothing AndAlso {"Second Check", "Pay Only On"}.Contains(CurrentRow.RecordType)
        Me.pnlNetOverride.Visible = CurrentRow IsNot Nothing AndAlso {"Second Check", "Net Override"}.Contains(CurrentRow.RecordType)
        Me.pnlOTHours.Visible = CurrentRow IsNot Nothing AndAlso CurrentRow.RecordType = "Auto OT Hours"
        Me.pnlSecondCheck.Visible = CurrentRow IsNot Nothing AndAlso CurrentRow.RecordType = "Second Check"
        Me.pnlRecordType.Visible = CurrentRow IsNot Nothing
        Me.AllPrdsCheckEdit.Enabled = CurrentRow IsNot Nothing AndAlso CurrentRow.RecordType <> "Pay Only On"
        Me.pnlGeneralOptions.Visible = CurrentRow IsNot Nothing AndAlso CurrentRow.RecordType = "General Options"

        Me.NetOverrideDedNumTextEdit.Enabled = CurrentRow IsNot Nothing AndAlso CurrentRow.NetOverrideAdjustType IsNot Nothing AndAlso CurrentRow.NetOverrideAdjustType.StartsWith("Deduction")

        If Me.grpSecondCheckTaxes.Visible Then
            Me.OASDIOverrideAmountTextEdit.Enabled = CurrentRow.CheckType & "" = "MANUAL"
            Me.MedicareOverrideAmountTextEdit.Enabled = CurrentRow.CheckType & "" = "MANUAL"
            Me.tbManualCheckNumber.Enabled = CurrentRow.CheckType & "" = "MANUAL"
        End If

        If CurrentRow IsNot Nothing AndAlso CurrentRow.RecordType = "Second Check" Then
            SetPayDedOptions(CurrentRow.PayDedType & "", CurrentRow.PayDedCode.GetValueOrDefault)
            Me.ResStateComboBoxEdit.Enabled = Me.WrkStateComboBoxEdit.HasValue
            If Me.DivNum2ndCheck.HasValue Then
                Me.DivNum2ndCheck_Validated(sender, e)
            End If
        End If
    End Sub

    Sub SetPayDedOptions(ByVal PayDedType As String, ByVal CurrentValue As Decimal)
        If PayDedType = "P" Then
            Me.OTHERPAYBindingSource.DataSource = (From A In OtherPays Where A.OACTIVE.ToUpper = "YES" OrElse A.OTH_PAY_NUM = CurrentValue).ToList
        ElseIf PayDedType = "D" Then
            Me.OTHERPAYBindingSource.DataSource = (From A In DeductionCodes
                                                   Where A.DACTIVE.ToUpper = "YES" OrElse A.DED_NUM = CurrentValue
                                                   Select New With {
                                                       .OTH_PAY_DESC = A.DED_DESC,
                                                       .OTH_PAY_NUM = A.DED_NUM
                                                   }).ToList
        End If
    End Sub

    Private Sub PayDedTypeTextEdit_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PayDedTypeTextEdit.SelectedIndexChanged
        Me.PayDedTypeTextEdit.DoValidate()
        Dim CurrentValue As Decimal
        Decimal.TryParse(nz(Me.PayDedCodeLookUpEdit.Text, ""), CurrentValue)
        SetPayDedOptions(Me.PayDedTypeTextEdit.EditValue, CurrentValue)
    End Sub

    Private Sub btnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdd.Click
        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        If CurrentRow IsNot Nothing Then
            If Not ValidateCheckOverride(CurrentRow) Then
                Exit Sub
            End If
        End If

        Dim NewRec As New pr_batch_overrides_setup With {.CoNum = CoNum,
                                                  .EmpNum = EmployeeID,
                                                  .RecordType = "Second Check",
                                                  .AllPrds = True,
                                                  .Prd1 = False,
                                                  .Prd2 = False,
                                                  .Prd3 = False,
                                                  .Prd4 = False,
                                                  .Prd5 = False,
                                                  .LastOfMonth = False,
                                                  .NetOverrideAdjustType = "Gross"}
        Dim Position = Me.Pr_batch_overrideBindingSource.Add(NewRec)
        Me.Pr_batch_overrideBindingSource.Position = Position
        Me.Pr_batch_overrideBindingSource_CurrentChanged(sender, e)
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If MessageBox.Show("Delete - Are you sure?", "Check Override", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.Yes Then
            Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
            If CurrentRow.ID > 0 Then
                DB.pr_batch_overrides_setups.DeleteOnSubmit(CurrentRow)
            End If
            Me.CheckOverrides.Remove(CurrentRow)
            Me.gridViewCheckOverrides.RefreshData()
            If Me.CheckOverrides.Count = 0 Then
                Me.Pr_batch_overrideBindingSource_CurrentChanged(sender, e)
            End If
        End If
    End Sub

    Function ValidateCheckOverride(ByVal Row As pr_batch_overrides_setup) As Boolean
        Dim IsValid As Boolean
        If Not Row.CheckType & "" <> "MANUAL" Then
            Row.OASDIOverrideAmount = Nothing
            Row.MedicareOverrideAmount = Nothing
            Row.ManualCheckNumber = Nothing
        End If
        Select Case Row.RecordType
            Case "Second Check"
                IsValid = Row.PayDedType IsNot Nothing AndAlso Row.PayDedCode IsNot Nothing AndAlso Row.PayDedAmount.HasValue
            Case "Net Override"
                IsValid = Row.NetOverrideAdjustType IsNot Nothing AndAlso Row.NetOverrideAmount.HasValue AndAlso (Row.NetOverrideDedNum.HasValue OrElse (Not (Row.NetOverrideAdjustType & "").StartsWith("Deduction")))
            Case "Auto OT Hours"
                IsValid = Row.OTHours.HasValue
            Case Else
                IsValid = True
        End Select
        If Not IsValid Then
            DisplayMessageBox("Finish current Check-Override entry, or delete.")
        End If
        Return IsValid
    End Function

    Private Sub PrdCheckEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Prd1CheckEdit.CheckedChanged, Prd2CheckEdit.CheckedChanged, Prd3CheckEdit.CheckedChanged, Prd4CheckEdit.CheckedChanged, Prd5CheckEdit.CheckedChanged, LastOfMonthCheckEdit.CheckedChanged, LastOfQtrCheckEdit.CheckedChanged, FirstOfQtrCheckEdit.CheckedChanged
        Dim Chk As DevExpress.XtraEditors.CheckEdit = sender
        Chk.DoValidate()
        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        If CurrentRow Is Nothing Then Exit Sub
        If Chk.EditValue AndAlso CurrentRow.AllPrds = True Then
            CurrentRow.AllPrds = False
        End If
    End Sub

    Private Sub AllPrdsCheckEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles AllPrdsCheckEdit.CheckedChanged
        Me.AllPrdsCheckEdit.DoValidate()
        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        If CurrentRow Is Nothing Then Exit Sub
        If CurrentRow.AllPrds.GetValueOrDefault Then
            CurrentRow.Prd1 = False
            CurrentRow.Prd2 = False
            CurrentRow.Prd3 = False
            CurrentRow.Prd4 = False
            CurrentRow.Prd5 = False
            CurrentRow.LastOfMonth = False
        End If
    End Sub

    Private Sub gridViewCheckOverrides_FocusedRowChanged(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles gridViewCheckOverrides.FocusedRowChanged
        If e.PrevFocusedRowHandle >= 0 Then
            Dim PrevRow As pr_batch_overrides_setup = Me.gridViewCheckOverrides.GetRow(e.PrevFocusedRowHandle)
            If PrevRow IsNot Nothing Then
                If Not ValidateCheckOverride(PrevRow) Then
                    Me.gridViewCheckOverrides.FocusedRowHandle = e.PrevFocusedRowHandle
                End If
            End If
        End If
    End Sub

    Private Sub RecordTypeTextEdit_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RecordTypeTextEdit.SelectedIndexChanged
        Me.RecordTypeTextEdit.DoValidate()
        Me.Pr_batch_overrideBindingSource_CurrentChanged(sender, e)

        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        If CurrentRow Is Nothing Then Exit Sub
        If CurrentRow.RecordType = "Pay Only On" Then
            If CurrentRow.AllPrds.GetValueOrDefault Then
                CurrentRow.Prd1 = True
                AllPrdsCheckEdit.Enabled = False
            End If
        ElseIf CurrentRow.RecordType = "Second Check" Then
            If CurrentRow.PayDedType Is Nothing Then CurrentRow.PayDedType = "P"
            If Not CurrentRow.PayDedCode.HasValue Then CurrentRow.PayDedCode = 0
        End If
    End Sub

    Private Sub NetOverrideAmountTextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles NetOverrideAmountTextEdit.Validated
        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        If CurrentRow.NetOverrideAmount.HasValue AndAlso Not CurrentRow.PayDedAmount.HasValue Then
            CurrentRow.PayDedAmount = CalculateFicaAdd(CurrentRow.NetOverrideAmount.Value)
        ElseIf CurrentRow.NetOverrideAmount.HasValue Then
            If Me.NetOverrideAmountTextEdit.OldEditValue.ToString & "" <> Me.NetOverrideAmountTextEdit.EditValue.ToString & "" AndAlso EmployeeEnt.SALARY_AMT.GetValueOrDefault <> 0 Then
                MessageBox.Show("Please make sure to update employee salary amount, use Cntrl+U in EP to figure out the gross for the new net amount", "Net Amount Changed", MessageBoxButtons.OK)
            End If
        End If
        XtraMessageBox.Show("Please enter a note when overiding the net amount")
        UcNotes1.SearchNote("Net")
        NetOverrideAmountTextEdit.BeginInvoke(Sub() TabbedControlGroup1.SelectedTabPageIndex = 4)
    End Sub

    Private Sub CheckTypeTextEdit_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckTypeTextEdit.SelectedIndexChanged
        Me.CheckCounterTextEdit.DoValidate()
        Dim ChkType = Me.CheckTypeTextEdit.EditValue
        Me.OASDIOverrideAmountTextEdit.Enabled = ChkType & "" = "MANUAL"
        Me.MedicareOverrideAmountTextEdit.Enabled = ChkType & "" = "MANUAL"
        Me.tbManualCheckNumber.Enabled = ChkType & "" = "MANUAL"
    End Sub

    Private Sub DD_ACC_TYPE_1ComboBoxEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_ACC_TYPE_1ComboBoxEdit.SelectedValueChanged
        enableDDGroup(Me.DD_ACC_TYPE_1ComboBoxEdit.SelectedItem & "" <> "None", "1")

        'Dim DDEnable1 As Boolean = Me.DD_ACC_TYPE_1ComboBoxEdit.SelectedItem & "" <> "None"
        'Me.DD_BANK_RT_1TextEdit.Enabled = DDEnable1
        'Me.DD_BANK_RT_1TextEdit.Enabled = DDEnable1
        'Me.DD_ACC_NO_1TextEdit.Enabled = DDEnable1
        'Me.DD_SPLIT_MET_1ComboBoxEdit.Enabled = DDEnable1
        'Me.DD_SPLIT_AMT_1TextEdit.Enabled = DDEnable1
        'Me.DD_STATUS_1ComboBoxEdit.Enabled = DDEnable1
    End Sub

    Private Sub DD_SPLIT_MET_1ComboBoxEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_SPLIT_MET_1ComboBoxEdit.SelectedValueChanged
        Me.DD_SPLIT_MET_1ComboBoxEdit.DoValidate()
        ''Dim IsPercent As Boolean = Me.DD_SPLIT_MET_1ComboBoxEdit.SelectedItem.ToString.Contains("Percent")
        'Dim IsPercent As Boolean = (Me.DD_SPLIT_MET_1ComboBoxEdit.SelectedItem & "").Contains("Percent")

        'If Me.DD_SPLIT_AMT_1TextEdit.EditValue <> "" Then
        '    Dim Amount As String = Me.DD_SPLIT_AMT_1TextEdit.EditValue.ToString.Replace("%", "")
        '    Dim Amount2 As String = Amount.Replace("$", "")
        '    Dim Amount3 As Decimal = CDec(Amount2)
        '    If IsPercent Then
        '        'Me.DD_SPLIT_AMT_1TextEdit.EditValue = Me.DD_SPLIT_AMT_1TextEdit.EditValue.ToString("p")
        '        Me.DD_SPLIT_AMT_1TextEdit.EditValue = FormatPercent(Amount3 / 100)
        '        DDAmount1 = (Amount3 / 1000)
        '    Else
        '        'Me.DD_SPLIT_AMT_1TextEdit.EditValue = Me.DD_SPLIT_AMT_1TextEdit.EditValue.ToString("c")
        '        Me.DD_SPLIT_AMT_1TextEdit.EditValue = FormatCurrency(Amount3)
        '        DDAmount1 = Amount3
        '    End If
        '    'Dim valueD As Decimal
        '    'If Decimal.TryParse(Nz(Me.DD_SPLIT_AMT_1TextEdit.EditValue, ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, valueD) Then
        '    '    If (Me.DD_SPLIT_MET_1ComboBoxEdit.SelectedItem & "" & "").StartsWith("Percent") Then
        '    '        valueD = valueD / 100
        '    '    End If
        '    '    EmployeeEnt.DD_SPLIT_AMT_1 = valueD
        '    'End If

        'End If

    End Sub

    Private Sub DD_SPLIT_AMT_1TextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_SPLIT_AMT_1TextEdit.Validated
        Me.DD_SPLIT_AMT_1TextEdit.EditValue = Me.EmployeeEnt.FormattedDDAmount1
    End Sub


    Private Sub DD_ACC_TYPE_2ComboBoxEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_ACC_TYPE_2ComboBoxEdit.SelectedValueChanged
        enableDDGroup(Me.DD_ACC_TYPE_2ComboBoxEdit.SelectedItem & "" <> "None", "2")
    End Sub

    Private Sub DD_SPLIT_MET_2ComboBoxEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_SPLIT_MET_2ComboBoxEdit.SelectedValueChanged
        Me.DD_SPLIT_MET_2ComboBoxEdit.DoValidate()
    End Sub

    Private Sub DD_SPLIT_AMT_2TextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_SPLIT_AMT_2TextEdit.Validated
        Me.DD_SPLIT_AMT_2TextEdit.EditValue = Me.EmployeeEnt.FormattedDDAmount2
    End Sub

    Private Sub GridViewSecondCheckPay_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs)
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete Note", AddressOf OnDeleteRowClick)
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As CoOptions_SecondCheckPayCode = Me.GridViewSecondCheckPay.GetRow(rowHandle)
        Me.GridViewSecondCheckPay.DeleteRow(rowHandle)
        If Row IsNot Nothing AndAlso Row.ID > 0 Then
            DB.CoOptions_SecondCheckPayCodes.DeleteOnSubmit(Row)
        End If
    End Sub

    Private Sub OTSeperateCheckCheckEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OTSeperateCheckCheckEdit.CheckedChanged
        Me.OTSeperateCheckHoursMoreThanTextEdit.Enabled = Me.OTSeperateCheckCheckEdit.Checked
    End Sub

    'Sub SSNorTIN()
    '    If _IsLoading = True Then Exit Sub

    '    Dim payrolls = (From A In DB.CHK_MASTs Where A.CONUM = CoNum And A.EMPNUM = EmployeeID).Count
    '    Dim strOldValue = Me.EMP_TYPETextEdit.OldEditValue
    '    Dim strEMP_TYPE As String = Me.EMP_TYPETextEdit.SelectedItem & ""
    '    Dim strTextEdit As String
    '    If strEMP_TYPE = strOldValue Then Exit Sub
    '    If payrolls > 0 And strEMP_TYPE <> strOldValue Then
    '        iResponse = MessageBox.Show("this Employee already has payrolls do you wish to create a new " & strEMP_TYPE & " employee based on the current employee", "", MessageBoxButtons.OKCancel)
    '        If iResponse = System.Windows.Forms.DialogResult.Yes Then
    '            'create new employee based on old
    '        Else
    '            Me.EMP_TYPETextEdit.Text = strOldValue
    '            Me.EMP_TYPETextEdit.DoValidate()
    '            Exit Sub
    '        End If
    '    End If


    '    Me.TINTextEdit.Visible = False
    '    Me.SSNTextEdit.Visible = False



    '    If strEMP_TYPE = "REGULAR" Then
    '        strTextEdit = TINTextEdit.Text.ToString()
    '        'SSNTextEdit.EditValue Me.EmployeeEnt.TIN
    '        If strTextEdit <> "" Then
    '            strTextEdit = Replace(strTextEdit, "-", "")
    '            strTextEdit = strTextEdit.Insert(5, "-").Insert(3, "-")
    '        End If
    '        Me.SSNTextEdit.Text = strTextEdit
    '        Me.SSNTextEdit.EditValue = strTextEdit
    '        Me.SSNTextEdit.DoValidate()
    '        Me.TINTextEdit.Text = ""
    '        Me.SSNTextEdit.Visible = True
    '        Me.SSNTINLabelControl.Text = "SSN: "
    '    ElseIf strEMP_TYPE = "CONTRACT" Then
    '        strTextEdit = SSNTextEdit.Text.ToString()
    '        If strTextEdit <> "" Then
    '            strTextEdit = Replace(strTextEdit, "-", "")
    '            strTextEdit = strTextEdit.Insert(2, "-")

    '        End If
    '        Me.TINTextEdit.Text = strTextEdit
    '        Me.TINTextEdit.EditValue = strTextEdit
    '        Me.TINTextEdit.DoValidate()
    '        Me.SSNTextEdit.Text = ""
    '        Me.TINTextEdit.Visible = True
    '        Me.SSNTINLabelControl.Text = "TIN: "
    '    End If
    '    'Me.TINTextEdit.DoValidate()
    '    'Me.SSNTextEdit.DoValidate()
    'End Sub


    Private Sub EMP_TYPETextEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EMP_TYPETextEdit.SelectedValueChanged
        Me.EMP_TYPETextEdit.DoValidate()
        Dim IsReguler = Me.EMP_TYPETextEdit.SelectedItem = "REGULAR"
        Me.SSNTextEdit.Visible = IsReguler
        Me.SSNLabelControl.Visible = IsReguler
        Me.TINTextEdit.Visible = Not IsReguler
        Me.TINLabel.Visible = Not IsReguler
        If IsReguler AndAlso String.IsNullOrEmpty(EmployeeEnt.SSN) AndAlso Not String.IsNullOrEmpty(EmployeeEnt.TIN) Then
            EmployeeEnt.SSN = EmployeeEnt.TIN
        ElseIf Not IsReguler AndAlso Not String.IsNullOrEmpty(EmployeeEnt.SSN) AndAlso String.IsNullOrEmpty(EmployeeEnt.TIN) Then
            EmployeeEnt.TIN = EmployeeEnt.SSN
        End If
    End Sub

    Private Sub DD_ACC_TYPE_3ComboBoxEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_ACC_TYPE_3ComboBoxEdit.SelectedValueChanged
        enableDDGroup(Me.DD_ACC_TYPE_3ComboBoxEdit.SelectedItem & "" <> "None", "3")
    End Sub

    Private Sub DD_ACC_TYPE_4ComboBoxEdit_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DD_ACC_TYPE_4ComboBoxEdit.SelectedValueChanged
        enableDDGroup(Me.DD_ACC_TYPE_4ComboBoxEdit.SelectedItem & "" <> "None", "4")
    End Sub

    Private Function CopyEmployeeOption(ByVal ControlSrc As String, ByVal Msg As String, ByVal NewValue As Object) As Boolean
        Dim MsgTitle As String = Msg.Substring(0, If(Msg.IndexOf(",") > 0, Msg.IndexOf(","), 20))
        If ControlSrc = "EMP_TYPE" Then
            MsgTitle = "Cannot change employee type"
        End If
        If DevExpress.XtraEditors.XtraMessageBox.Show(Msg & vbCrLf & vbCrLf & "Do you want to create a new employee record?", MsgTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
            Return False
        Else
            Me.EmployeeID = 0
            Dim NewEmployeeEnt = EmployeeEnt.CloneEntity
            Dim NewStateInfoList As New List(Of STATE_EE_INFO)
            For Each rec In Me.StateInfoEnt
                Dim NewStateInfoEnt = rec.CloneEntity
                NewStateInfoEnt.rowguid = Guid.Empty
                NewStateInfoList.Add(NewStateInfoEnt)
            Next
            If ControlSrc = "EMP_TYPE" Then
                NewEmployeeEnt.EMP_TYPE = NewValue
                If NewValue = "REGULAR" Then
                    If NewEmployeeEnt.SSN Is Nothing Then NewEmployeeEnt.SSN = NewEmployeeEnt.TIN
                ElseIf NewValue = "CONTRACT" Then
                    If NewEmployeeEnt.TIN Is Nothing Then NewEmployeeEnt.TIN = NewEmployeeEnt.SSN
                End If
            ElseIf ControlSrc = "OASDI Exempt" Then
                NewEmployeeEnt.OASDI_EXE_FGE = NewValue
                NewEmployeeEnt.MEDICARE_EXE_FGE = NewValue
            ElseIf ControlSrc = "FUTA Exempt" Then
                NewEmployeeEnt.FUTA_EXE_FGE = NewValue
            ElseIf ControlSrc = "SUTA Exempt" Then
                NewEmployeeEnt.SUTA_EXE_FG = NewValue
            End If
            Dim NotCopiedMsg As New List(Of String)
            If CheckOverrides.Count > 0 Then NotCopiedMsg.Add("'Check overrides'")
            If CoOptionsSecondCheck.Count > 0 Then NotCopiedMsg.Add("'Second checks'")
            Dim OPCount = (From A In DB.EMP_OPS Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID).Count
            If OPCount > 0 Then NotCopiedMsg.Add("'Auto pays")
            Dim ODCount = (From A In DB.EMP_DEDS Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID).Count
            If ODCount > 0 Then NotCopiedMsg.Add("'Auto deds")
            If NotCopiedMsg.Count > 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show(String.Join(" / ", NotCopiedMsg) & " are not copied over. Please make sure setup is correct.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            LoadEmployee(NewEmployeeEnt, NewStateInfoList)
        End If
        Return True
    End Function

    Private Sub EMP_TYPETextEdit_EditValueChanging(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles EMP_TYPETextEdit.EditValueChanging
        If _IsLoading Then Return
        If Me.EmployeeID = 0 Then Exit Sub
        If String.IsNullOrEmpty(e.OldValue & "") Then Exit Sub
        If e.OldValue = e.NewValue Then Exit Sub

        Dim pCount As Integer
        If e.NewValue = "REGULAR" Then
            pCount = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID AndAlso A.MEDTAXABLE.GetValueOrDefault = 0 AndAlso A.FEDWH_TAXABLE.GetValueOrDefault = 0).Count
        ElseIf e.NewValue = "CONTRACT" Then
            pCount = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID AndAlso (A.MEDTAXABLE.GetValueOrDefault <> 0 OrElse A.FEDWH_TAXABLE.GetValueOrDefault <> 0 AndAlso A.GROSS <> 0)).Count
        End If
        If pCount > 0 Then
            e.Cancel = True
            Dim msg = "Cannot change employee type, because employee already has " & IIf(e.NewValue = "REGULAR", "non-", "") & "taxable payroll."
            CopyEmployeeOption("EMP_TYPE", msg, e.NewValue)
        End If
    End Sub

    Private Sub OASDI_EXE_FGETextEdit_EditValueChanging(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles OASDI_EXE_FGETextEdit.EditValueChanging,
                                                                                                                                                        FUTA_EXE_FGETextEdit.EditValueChanging,
                                                                                                                                                        SUTA_EXE_FGTextEdit.EditValueChanging
        If _IsLoading Then Exit Sub
        If Me.EmployeeID = 0 Then Exit Sub
        If String.IsNullOrEmpty(e.OldValue & "") Then Exit Sub
        If e.OldValue = e.NewValue Then Exit Sub

        Dim cntrlName As String = CType(sender, Control).Name
        Dim taxType As String = cntrlName.Substring(0, cntrlName.IndexOf("_"))

        Dim pCount As Integer
        If e.NewValue = 1 Then
            Dim q = From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID AndAlso A.GROSS <> 0
            If taxType = "OASDI" Then
                q = From A In q Where A.MEDTAXABLE.GetValueOrDefault <> 0
            ElseIf taxType = "FUTA" Then
                q = From A In q Where A.FUTA_TAXABLE.GetValueOrDefault <> 0
            ElseIf taxType = "SUTA" Then
                q = From A In q Where A.SUTA_TAXABLE.GetValueOrDefault <> 0
            End If
            pCount = q.Count
        End If
        If pCount > 0 Then
            e.Cancel = True
            Dim msg = "Cannot exempt " & taxType & ", because employee already has taxable payroll."
            CopyEmployeeOption(taxType & " Exempt", msg, e.NewValue)
        End If
    End Sub

    Private Sub RATE_1TextEdit_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles SALARY_AMTTextEdit.KeyDown, RATE_3TextEdit.KeyDown, RATE_2TextEdit.KeyDown, RATE_1TextEdit.KeyDown, Default_hoursTextEdit.KeyDown
        If e.KeyCode = Keys.Back OrElse e.KeyCode = Keys.Delete Then
            CType(sender, DevExpress.XtraEditors.BaseEdit).EditValue = Nothing
            e.Handled = True
        End If
    End Sub

    Private Sub SALARY_AMTTextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SALARY_AMTTextEdit.Validated
        If Me.SALARY_AMTTextEdit.HasValue AndAlso Me.SALARY_AMTTextEdit.OldEditValue.ToString & "" <> Me.SALARY_AMTTextEdit.EditValue.ToString & "" Then
            Dim CheckOverrideRec = (From A In Me.CheckOverrides Where A.RecordType = "Net Override" AndAlso A.NetOverrideAmount.GetValueOrDefault <> 0).FirstOrDefault
            If CheckOverrideRec IsNot Nothing Then
                MessageBox.Show("Employee has a net setup, Please adjust Net override amount accordingly", "Salary Amount Changed", MessageBoxButtons.OK)
            End If
        End If
        Dim employee As EMPLOYEE = EMPLOYEEBindingSource.DataSource
        If employee.SALARY_AMT.HasValue Then employee.LAST_SALARY_RAISE = DateTime.Now
    End Sub

    Function GetStateMerriedStatus(ByVal StateCode As String) As String
        Dim FedStatus = EmployeeEnt.FED_STATUS
        If String.IsNullOrEmpty(FedStatus) Then Return Nothing
        Dim AvailStatus = Me.StateStatusOptions(StateCode)
        If AvailStatus.Contains(FedStatus) Then
            Return FedStatus
        Else
            For Each status In AvailStatus
                If status.StartsWith(FedStatus, True, Globalization.CultureInfo.CurrentCulture) Then
                    Return status
                End If
            Next
        End If
        If AvailStatus.Length > 0 Then
            If StateCode = "CT" Then
                Return "File Status D"
            End If
            Return AvailStatus(0)
        Else
            Return Nothing
        End If
    End Function

    Private Sub lstAvailableStates_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lstAvailableStates.DoubleClick, btnAddState.Click
        Try
            If Me.DicAvailableStates.Count = 0 Then
                Exit Sub
            ElseIf Me.lstAvailableStates.SelectedIndex < 0 Then
                Exit Sub
            End If
            If StateInfoEnt(0).STATE Is Nothing Then
                StateInfoEnt(0).STATE = Me.lstAvailableStates.SelectedValue
            Else
                Dim NewRec = New STATE_EE_INFO With {.EMPNUM = Me.EmployeeID,
                                                     .STATE = Me.lstAvailableStates.SelectedValue,
                                                     .CONUM = Me.CoNum
                                                    }
                NewRec.ST_DEPS = EmployeeEnt.FED_DEPS
                NewRec.ST_STATUS = GetStateMerriedStatus(Me.lstAvailableStates.SelectedValue)
                SetStateDefaults(NewRec)
                'If Not Me.StateInfoEnt Is Nothing AndAlso Me.StateInfoEnt.Count > 0 Then
                NewRec.DEFAULT_RES = "NO"
                NewRec.DEFAULT_WORK = "NO"
                'End If
                Me.StateInfoEnt.Add(NewRec)
            End If
            _IsLoading = True
            Me.DicAvailableStates.Remove(Me.lstAvailableStates.SelectedValue)
            Me.lstAvailableStates.DataSource = New BindingSource(DicAvailableStates, Nothing)
            'Me.STATE_EE_INFOBindingSource.DataSource = StateInfoEnt
            Me.STATE_EE_INFOBindingSource.Position = Me.StateInfoEnt.Count - 1
            'SetStateOptions()
            Me.lstAvailableStates.SelectedIndex = -1
            _IsLoading = False
            Me.STATE_EE_INFOBindingSource.ResetBindings(False)
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        End Try
    End Sub

    Private Sub btnAddLocal_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddLocal.Click, lstAvailLocals.DoubleClick
        If Me.DicAvailableLocals.Count = 0 Then
            Exit Sub
        ElseIf Me.lstAvailLocals.SelectedIndex < 0 Then
            Exit Sub
        ElseIf Me.lstAvailLocals.SelectedValue = 1200 OrElse Me.lstAvailLocals.SelectedValue = 1201 OrElse Me.lstAvailLocals.SelectedValue = 1205 Then
            DisplayMessageBox("Do Not Setup this local code for NY, local will be setup automatically by trigger")
            Exit Sub
        End If
        Dim LocalRec As LOCAL_EE_INFO
        If LocalInfoEntList.Count > 0 AndAlso LocalInfoEntList(0).local_id = 0 Then
            LocalRec = LocalInfoEntList(0)
        Else
            LocalRec = New LOCAL_EE_INFO With {.empnum = Me.EmployeeID,
                                                 .conum = Me.CoNum,
                                                 .local_id = Me.lstAvailLocals.SelectedValue,
                                                 .LocalDescription = Me.lstAvailLocals.Text
                                                }
            SetLocalDefaults(LocalRec)
            Me.LocalInfoEntList.Add(LocalRec)
        End If
        LocalRec.default_position = 0
        LocalRec.local_id = Me.lstAvailLocals.SelectedValue
        Dim LocalState = (From A In Me.Locals Where A.CODE = LocalRec.local_id Order By A.STARTDATE Select A.STATE).FirstOrDefault
        Dim EmpStateRec = (From A In Me.StateInfoEnt Where A.STATE = LocalState).FirstOrDefault
        If EmpStateRec IsNot Nothing Then
            LocalRec.dependents = EmpStateRec.ST_DEPS
            LocalRec.local_status = EmpStateRec.ST_STATUS
        Else
            LocalRec.dependents = EmployeeEnt.FED_DEPS
            LocalRec.local_status = GetStateMerriedStatus(LocalState)
        End If

        Me.DicAvailableLocals.Remove(Me.lstAvailLocals.SelectedValue)
        Me.lstAvailLocals.DataSource = New BindingSource(DicAvailableLocals, Nothing)
        Me.LOCAL_EE_INFOBindingSource.Position = Me.LocalInfoEntList.Count - 1
        Me.LOCAL_EE_INFOBindingSource.ResetBindings(False)
        Me.LOCAL_EE_INFOBindingSource_CurrentChanged(sender, e)
        Me.lstAvailLocals.SelectedIndex = -1
    End Sub

    Private Sub btnRemoveState_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRemoveState.Click
        Dim CurrentRec As STATE_EE_INFO = Me.STATE_EE_INFOBindingSource.Current
        If CurrentRec Is Nothing Then Exit Sub
        Dim pCount = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID _
                      AndAlso (A.STATE_UCI = CurrentRec.STATE OrElse A.STATE_WH = CurrentRec.STATE OrElse A.STATE_WH_RES = CurrentRec.STATE)).Count
        If pCount > 0 Then
            DisplayMessageBox("This state is in use within an employee's check. You may not delete it!")
            Exit Sub
        End If
        If Me.StateInfoEnt.Count = 1 Then
            DisplayMessageBox("You must first add another state, before deleting this state")
            Exit Sub
        End If
        If Not CurrentRec.rowguid = Guid.Empty Then
            DB.STATE_EE_INFOs.DeleteOnSubmit(CurrentRec)
        End If
        Me.STATE_EE_INFOBindingSource.Remove(CurrentRec)
        Me.DicAvailableStates.Add(CurrentRec.STATE, CurrentRec.StateName)
        Me.lstAvailableStates.DataSource = New BindingSource(DicAvailableStates, Nothing)
        'CType(Me.lstAvailableStates.DataSource, BindingSource).DataSource = DicAvailableStates
        'Me.lstAvailableStates.Items.Add(CurrentRec.STATE)
        Me.STATE_EE_INFOBindingSource.ResetBindings(False)
    End Sub

    Private Sub btnRemoveLocal_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRemoveLocal.Click
        Dim CurrentRec As LOCAL_EE_INFO = Me.LOCAL_EE_INFOBindingSource.Current
        If CurrentRec Is Nothing Then Exit Sub
        If CurrentRec.local_id = 0 Then Exit Sub
        Dim pCount = (From A In DB.CHK_DET_PAYs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = Me.EmployeeID _
                      AndAlso (A.CL_LOC_CD1 = CurrentRec.local_id OrElse A.CL_LOC_CD2 = CurrentRec.local_id OrElse A.CL_LOC_CD3 = CurrentRec.local_id OrElse A.CL_LOC_CD4 = CurrentRec.local_id OrElse A.CL_LOC_CD5 = CurrentRec.local_id)).Count
        If pCount > 0 Then
            DisplayMessageBox("This Local is in use within an employee's check. You may not delete it!")
            Exit Sub
        End If
        If CurrentRec.local_id = 1200 OrElse CurrentRec.local_id = 1201 OrElse CurrentRec.local_id = 1205 Then
            DisplayMessageBox("Do Not Edit this local code for NY, local will be setup automatically by trigger")
            Exit Sub
        End If
        If Not CurrentRec.rowguid = Guid.Empty Then
            DB.local_ee_infos.DeleteOnSubmit(CurrentRec)
        End If
        If Not CurrentRec.rowguid = Guid.Empty Then
            DB.local_ee_infos.DeleteOnSubmit(CurrentRec)
        End If
        Me.LocalInfoEntList.Remove(CurrentRec)
        Me.DicAvailableLocals.Add(CurrentRec.local_id, CurrentRec.LocalDescription)
        Me.lstAvailLocals.DataSource = New BindingSource(DicAvailableLocals, Nothing)
        If Me.LocalInfoEntList.Count = 0 Then
            Dim LocalRec = New LOCAL_EE_INFO With {.empnum = Me.EmployeeID,
                                                   .conum = Me.CoNum,
                                                   .default_position = 0,
                                                   .alternate_w4 = "NO"
                                                }
            Me.LocalInfoEntList.Add(LocalRec)
            Me.LOCAL_EE_INFOBindingSource.DataSource = Me.LocalInfoEntList
        End If
        Me.LOCAL_EE_INFOBindingSource.ResetBindings(False)
        Me.LOCAL_EE_INFOBindingSource.Position = 0
        Me.LOCAL_EE_INFOBindingSource_CurrentChanged(sender, e)
    End Sub

    Private Sub DEFAULT_WORKTextEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DEFAULT_WORKTextEdit.Validated, DEFAULT_RESTextEdit.Validated
        If _IsLoading Then Exit Sub
        Dim Chk As DevExpress.XtraEditors.CheckEdit = sender
        If Not Chk.Checked Then
            Chk.Checked = True
            Chk.DoValidate()
            Exit Sub
        End If
        Dim CurrentRec As STATE_EE_INFO = Me.STATE_EE_INFOBindingSource.Current
        Dim Res = Chk.Name = "DEFAULT_RESTextEdit"
        If Chk.Checked Then
            For Each rec In Me.StateInfoEnt
                If rec.STATE <> CurrentRec.STATE Then
                    If Res Then
                        rec.DEFAULT_RES = "NO"
                    Else
                        rec.DEFAULT_WORK = "NO"
                    End If
                End If
            Next
        End If
    End Sub

    Private Sub STATE_EE_INFOBindingSource_CurrentChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles STATE_EE_INFOBindingSource.CurrentChanged
        Dim CurrentRec As STATE_EE_INFO = Me.STATE_EE_INFOBindingSource.Current
        If CurrentRec Is Nothing OrElse CurrentRec.STATE Is Nothing Then Exit Sub
        Dim State = CurrentRec.STATE

        Dim StateFields = (From A In Me.StateTaxOptions Where A.state_code = State).ToDictionary(Function(p) p.field_code, Function(p) p.field_label)
        Me.DEFAULT_WORKTextEdit.Visible = StateFields.ContainsKey("DEFAULT_WORK")
        Me.DEFAULT_RESTextEdit.Visible = StateFields.ContainsKey("DEFAULT_RES")
        Me.ST_WH_EXE_FGETextEdit.Visible = StateFields.ContainsKey("ST_WH_EXE_FGE")
        Me.ALTW4TextEdit.Visible = StateFields.ContainsKey("ALTW4")
        Me.ST_STATUSTextEdit.Visible = StateFields.ContainsKey("ST_STATUS")
        Me.ST_DEPSTextEdit.Visible = StateFields.ContainsKey("ST_DEPS")
        Me.ST_WH_EXTRATextEdit.Visible = StateFields.ContainsKey("ST_WH_EXTRA")
        Me.ST_WH_FIXEDTextEdit.Visible = StateFields.ContainsKey("ST_WH_FIXED")
        Me.SDIEXETextEdit.Visible = StateFields.ContainsKey("SDIEXE")
        Me.FLIEXECheckEdit.Visible = StateFields.ContainsKey("FLIEXE")

        Me.DEFAULT_WORKLabel.Visible = StateFields.ContainsKey("DEFAULT_WORK")
        Me.DEFAULT_RESLabel.Visible = StateFields.ContainsKey("DEFAULT_RES")
        Me.ST_WH_EXE_FGELabel.Visible = StateFields.ContainsKey("ST_WH_EXE_FGE")
        Me.ALTW4Label.Visible = StateFields.ContainsKey("ALTW4")
        Me.ST_STATUSLabel.Visible = StateFields.ContainsKey("ST_STATUS")
        Me.ST_DEPSLabel.Visible = StateFields.ContainsKey("ST_DEPS")
        Me.ST_WH_EXTRALabel.Visible = StateFields.ContainsKey("ST_WH_EXTRA")
        Me.ST_WH_FIXEDLabel.Visible = StateFields.ContainsKey("ST_WH_FIXED")
        Me.SDIEXELabel.Visible = StateFields.ContainsKey("SDIEXE")
        Me.FLIEXELabel.Visible = StateFields.ContainsKey("FLIEXE")

        If StateFields.ContainsKey("ALTW4") Then Me.ALTW4Label.Text = StateFields.Item("ALTW4")
        If StateFields.ContainsKey("DEFAULT_WORKLabel") Then Me.DEFAULT_WORKLabel.Text = StateFields.Item("DEFAULT_WORK")
        If StateFields.ContainsKey("DEFAULT_RES") Then Me.DEFAULT_RESLabel.Text = StateFields.Item("DEFAULT_RES")
        If StateFields.ContainsKey("ST_WH_EXE_FGE") Then Me.ST_WH_EXE_FGELabel.Text = StateFields.Item("ST_WH_EXE_FGE")
        If StateFields.ContainsKey("ST_STATUS") Then Me.ST_STATUSLabel.Text = StateFields.Item("ST_STATUS")
        If StateFields.ContainsKey("ST_DEPS") Then Me.ST_DEPSLabel.Text = StateFields.Item("ST_DEPS")
        If StateFields.ContainsKey("ST_WH_EXTRA") Then Me.ST_WH_EXTRALabel.Text = StateFields.Item("ST_WH_EXTRA")
        If StateFields.ContainsKey("ST_WH_FIXED") Then Me.ST_WH_FIXEDLabel.Text = StateFields.Item("ST_WH_FIXED")
        If StateFields.ContainsKey("SDIEXE") Then Me.SDIEXELabel.Text = StateFields.Item("SDIEXE")
        If StateFields.ContainsKey("FLIEXE") Then Me.FLIEXELabel.Text = StateFields.Item("FLIEXE")

        'If Not StateFields.ContainsKey("ALTW4") Then
        '    CurrentRec.ALTW4 = "YES"
        'End If

        Me.grpStateNotes.Text = State & " Instructions"
        Me.txtStateNotes.EditValue = StateInstructions(State)

        Me.ST_STATUSTextEdit.Properties.Items.Clear()
        Me.ST_STATUSTextEdit.Properties.Items.AddRange(Me.StateStatusOptions(State))

        Me.StateValueChanged(Nothing, e)
    End Sub

    Private Sub DEFAULT_WORKTextEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DEFAULT_WORKTextEdit.Click, DEFAULT_RESTextEdit.Click
        CType(sender, DevExpress.XtraEditors.CheckEdit).DoValidate()
    End Sub

    Private Sub FED_WH_EXTRATextEdit_ParseEditValue(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.ConvertEditValueEventArgs) Handles FED_WH_EXTRATextEdit.ParseEditValue,
                FED_WH_FIXEDTextEdit.ParseEditValue, ST_WH_FIXEDTextEdit.ParseEditValue, ST_WH_EXTRATextEdit.ParseEditValue, Extra_whTextEdit.ParseEditValue, Fixed_whTextEdit.ParseEditValue
        If Not IsDBNull(e.Value) AndAlso e.Value.ToString = "" Then
            e.Value = Nothing
        End If
    End Sub

    Private Sub XtraTabControl1_SelectedPageChanged(ByVal sender As System.Object, ByVal e As LayoutTabPageChangedEventArgs) Handles TabbedControlGroup1.SelectedPageChanged
        If IsInDesignMode Then Exit Sub
        If e.Page Is Me.LayoutControlGroupPayrollTax Then
            Me.lstAvailableStates.SelectedIndex = -1

            Me.lstAvailLocals.SelectedIndex = -1

            'not showing second item workaround. need to find proper way
            Dim pos = Me.LOCAL_EE_INFOBindingSource.Position
            Me.LOCAL_EE_INFOBindingSource.Position = Me.LocalInfoEntList.Count - 1
            Me.LOCAL_EE_INFOBindingSource_CurrentChanged(sender, e)
            Me.LOCAL_EE_INFOBindingSource.Position = pos
            Me.LOCAL_EE_INFOBindingSource_CurrentChanged(sender, e)
        ElseIf e.Page Is lcgPayHistory Then
            LoadPayHistory()
        End If
    End Sub


    Private Sub LOCAL_EE_INFOBindingSource_CurrentChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LOCAL_EE_INFOBindingSource.CurrentChanged
        If Me.LOCAL_EE_INFOBindingSource.Position < 0 Then Exit Sub
        Dim CurrentRec As LOCAL_EE_INFO = Me.LOCAL_EE_INFOBindingSource.Current
        If CurrentRec Is Nothing OrElse CurrentRec.local_id = 0 Then Exit Sub

        'CurrentRec.LocalDescription = Me.DicCoLocals(CurrentRec.local_id)

        Dim Local = (From A In Me.Locals Where A.CODE = CurrentRec.local_id Order By A.STARTDATE).FirstOrDefault

        Dim LocalFields = (From A In Me.LocalTaxOptions
                           Where (A.state_code = Local.STATE AndAlso A.local_type = Local.TYPE) OrElse A.state_code = "ALL"
                           ).ToDictionary(Function(p) p.field_code, Function(p) p.field_label)
        Me.Alternate_w4TextEdit.Visible = LocalFields.ContainsKey("ALTERNATE_W4")
        Me.Local_statusTextEdit.Visible = LocalFields.ContainsKey("LOCAL_STATUS")
        Me.DependentsTextEdit.Visible = LocalFields.ContainsKey("DEPENDENTS")

        Me.Alternate_w4Label.Visible = LocalFields.ContainsKey("ALTERNATE_W4")
        Me.Local_statusLabel.Visible = LocalFields.ContainsKey("LOCAL_STATUS")
        Me.DependentsLabel.Visible = LocalFields.ContainsKey("DEPENDENTS")

        If LocalFields.ContainsKey("ALTERNATE_W4") Then Alternate_w4Label.Text = LocalFields.Item("ALTERNATE_W4")
        If LocalFields.ContainsKey("LOCAL_STATUS") Then Local_statusLabel.Text = LocalFields.Item("LOCAL_STATUS")
        If LocalFields.ContainsKey("DEPENDENTS") Then DependentsLabel.Text = LocalFields.Item("DEPENDENTS")

        'If Not LocalFields.ContainsKey("ALTERNATE_W4") Then
        '    CurrentRec.alternate_w4 = "YES"
        'End If

        Me.Local_statusTextEdit.Properties.Items.Clear()
        Me.Local_statusTextEdit.Properties.Items.AddRange(Me.StateStatusOptions(Local.STATE))
    End Sub

    Private Sub Alternate_w4TextEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Alternate_w4TextEdit.CheckedChanged
        If _IsLoading Then Return
        Me.Alternate_w4TextEdit.DoValidate()

        Dim LocalRec As LOCAL_EE_INFO = Me.LOCAL_EE_INFOBindingSource.Current
        Dim FieldSeperate = LocalRec.alternate_w4.FromYesNoString

        Me.Local_statusTextEdit.Properties.ReadOnly = Not FieldSeperate
        Me.DependentsTextEdit.Properties.ReadOnly = Not FieldSeperate
        ST_STATUSTextEdit_Validated(ST_STATUSTextEdit, e)
    End Sub

    Private Sub ST_STATUSTextEdit_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ST_STATUSTextEdit.Validated, ST_DEPSTextEdit.Validated
        Dim StateRec As STATE_EE_INFO = Me.STATE_EE_INFOBindingSource.Current
        If StateRec Is Nothing Then Return
        Dim State = StateRec.STATE
        Dim StateLocalCodes = (From A In Me.Locals Join B In Me.CoLocalCodes On A.CODE Equals B.LOC_ID Where A.STATE = State Select A.CODE).Distinct.ToList
        Dim Locals = (From A In Me.LocalInfoEntList Where StateLocalCodes.Contains(A.local_id)).ToList
        For Each LocalRec In Locals
            If LocalRec.alternate_w4 = "NO" Then
                LocalRec.local_status = StateRec.ST_STATUS
                LocalRec.dependents = StateRec.ST_DEPS
            End If
        Next
    End Sub

    Private Sub EMPNUMTextEdit_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles EMPNUMTextEdit.Validating
        If (Not Me.EMPNUMTextEdit.Properties.ReadOnly) AndAlso Me.EMPNUMTextEdit.HasValue Then
            Dim SelectedNum As Decimal = Me.EMPNUMTextEdit.EditValue
            If (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.EMPNUM = SelectedNum Select A.EMPNUM, A.CONUM).FirstOrDefault IsNot Nothing Then
                DisplayMessageBox("Employee Number " & SelectedNum & " is not available")
                Me.EMPNUMTextEdit.EditValue = GetNextEmployeeID()
            Else
                _IsLoading = True
            End If
        End If
    End Sub

    Private Sub EMPNUMTextEdit_Validated(sender As Object, e As EventArgs) Handles EMPNUMTextEdit.Validated
        _IsLoading = False
    End Sub

    Private Sub SSNTextEdit_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles SSNTextEdit.Validating
        If SSNTextEdit.HasValue Then
            Dim Num As Decimal = Me.EMPNUMTextEdit.EditValue
            Dim SSN As String = SSNTextEdit.EditValue
            e.Cancel = Not IsValidSSN(Num, SSN)
        End If
    End Sub

    Private Function IsValidSSN(CurrentEmpNum As Decimal, SSN As String) As Boolean
        Dim Emp = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.EMPNUM <> CurrentEmpNum AndAlso A.SSN = SSN Select A.EMPNUM, A.F_NAME, A.L_NAME).FirstOrDefault
        If Emp IsNot Nothing Then
            Dim Msg = String.Format("SSN '{0}' is already in use on Emp #: {1} - {2}, {3}{4}Continue?", SSN, Emp.EMPNUM, Emp.L_NAME, Emp.F_NAME, Environment.NewLine)
            If DevExpress.XtraEditors.XtraMessageBox.Show(Msg, "Duplicate SSN", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                Return False
            End If
        End If
        Return True
    End Function

    Private Sub btnParsonage_CheckedChanged(sender As Object, e As EventArgs) Handles btnParsonage.CheckedChanged
        If _IsLoading Then Return
        EmployeeEnt.USERDEF21 = IIf(btnParsonage.Checked, "P30", String.Empty)
        SetParsonageDisplay()
    End Sub

    Private Sub btnParsonage_Click(sender As Object, e As EventArgs) Handles btnParsonage.Click
        Dim frm = New frmNotesPopUp
        frm.LoadNotes(CoNum, "Pars", (btnParsonage.Text = "UnParsonage"), EmployeeID)
        frm.ShowDialog()
        UcNotes1.SaveChanges()
        UcNotes1.RefreshNotes()
        UcNotes1.SearchNote("Parsonage", False)
        TabbedControlGroup1.SelectedTabPageIndex = 4
    End Sub

    Private Sub btnCancelCopyToNewCompany_Click(sender As Object, e As EventArgs) Handles btnCancelCopyToNewCompany.Click
        Me.popupCopyFrom.Hide()
    End Sub

    Private Sub btnCopy_Click(sender As Object, e As EventArgs) Handles btnCopy.Click
        slueCompanyCopyFrom.Properties.DataSource = (
            From A In DB.COMPANies
            Order By A.CONUM
            Select New CompanySummary With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                                                .CO_DBA = A.CO_DBA, .CO_PHONE = A.CO_PHONE, .CO_FAX = A.CO_FAX,
                                                .CO_EMAIL = A.CO_EMAIL, .CO_STATUS = A.CO_STATUS}
                                            ).ToList
        slueCompanyCopyFrom.Properties.DisplayMember = "CONUM"
        slueCompanyCopyFrom.Properties.ValueMember = "CONUM"
        Me.popupCopyFrom.Left = (Me.Width - popupCopyFrom.Width) / 2
        Me.popupCopyFrom.Top = 200
        Me.chkUseSameEmpID.Checked = False
        Me.chkUseSameEmpID.Enabled = False

        slueCompanyCopyFrom.EditValue = Nothing
        slueEmployeeCopyFrom.EditValue = Nothing

        Me.popupCopyFrom.Show()
        Me.slueCompanyCopyFrom.Select()
        Me.popupCopyFrom.BringToFront()
    End Sub

    Private Sub slueCompanyCopyFrom_EditValueChanged(sender As Object, e As EventArgs) Handles slueCompanyCopyFrom.EditValueChanged
        Me.slueEmployeeCopyFrom.Properties.DataSource = Nothing
        Me.txtCoName.EditValue = Nothing
        Me.txtEmpNum.EditValue = Nothing
        If Me.slueCompanyCopyFrom.HasValue Then
            Dim CoNum As Decimal = Me.slueCompanyCopyFrom.EditValue
            Me.txtCoName.EditValue = (From A In CType(Me.slueCompanyCopyFrom.Properties.DataSource, IList) Where A.CONUM = CoNum Select A.CO_NAME).First

            Dim EmpList = (
                From A In DB.EMPLOYEEs
                Where A.CONUM = CoNum
                Order By A.EMPNUM
                Select A.EMPNUM, A.L_NAME, A.F_NAME, A.M_NAME).ToList

            Me.slueEmployeeCopyFrom.Properties.DataSource = (From A In EmpList Select A.EMPNUM, EmpName = A.L_NAME & ", " & A.F_NAME & " " & A.M_NAME).ToList
            Me.slueEmployeeCopyFrom.Properties.DisplayMember = "EMPNUM"
            Me.slueEmployeeCopyFrom.Properties.ValueMember = "EMPNUM"
            Me.chkUseSameEmpID.Checked = False
        End If
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        If Not (Me.slueCompanyCopyFrom.HasValue AndAlso Me.slueEmployeeCopyFrom.HasValue) Then
            DisplayMessageBox("Please select Company and Employee")
            Exit Sub
        End If

        Dim SourceCoNum As Decimal = Me.slueCompanyCopyFrom.EditValue
        Dim SourceEmpNum As Decimal = Me.slueEmployeeCopyFrom.EditValue

        Dim SourceEmployee = (From A In DB.EMPLOYEEs Where A.CONUM = SourceCoNum AndAlso A.EMPNUM = SourceEmpNum).First
        Dim NewEmployeeEnt = SourceEmployee.CloneEntity
        NewEmployeeEnt.CONUM = Me.CoNum

        If chkUseSameEmpID.Checked Then
            Dim EmpNum As Decimal = Me.slueEmployeeCopyFrom.EditValue
            NewEmployeeEnt.EMPNUM = EmpNum
            txtEmpNum.Text = EmpNum
            EMPNUMTextEdit.EditValue = EmpNum
        End If

        NewEmployeeEnt.DD_SPLIT_AMT_1 = SourceEmployee.DD_SPLIT_AMT_1
        NewEmployeeEnt.DD_SPLIT_AMT_2 = SourceEmployee.DD_SPLIT_AMT_2
        NewEmployeeEnt.DD_SPLIT_AMT_3 = SourceEmployee.DD_SPLIT_AMT_3
        NewEmployeeEnt.DD_SPLIT_AMT_4 = SourceEmployee.DD_SPLIT_AMT_4
        Dim NewStateInfoList As New List(Of STATE_EE_INFO)

        Dim SourceStateInfo = (From A In DB.STATE_EE_INFOs Where A.CONUM = SourceCoNum AndAlso A.EMPNUM = SourceEmpNum).ToList
        For Each rec In SourceStateInfo
            If Me.StateStatusOptions.ContainsKey(rec.STATE) Then
                Dim NewStateInfoEnt = rec.CloneEntity
                NewStateInfoEnt.rowguid = Guid.Empty
                NewStateInfoEnt.CONUM = Me.CoNum
                NewStateInfoList.Add(NewStateInfoEnt)
            Else
                DisplayMessageBox(String.Format("Emp State {0} is not setup on selected company. Setup state in company, or select another state.", rec.STATE))
            End If
        Next

        Dim NewCheckOverrdiesList As New List(Of pr_batch_overrides_setup)
        Dim SourceCheckOverrides = (From A In DB.pr_batch_overrides_setups Where A.CoNum = SourceCoNum AndAlso A.EmpNum = SourceEmpNum).ToList
        For Each rec In SourceCheckOverrides
            Dim NewEnt = rec.CloneEntity
            NewEnt.ID = 0
            NewEnt.rowguid = Guid.Empty
            NewCheckOverrdiesList.Add(NewEnt)
        Next

        Dim NewCoOptionsSecondCheckList As New List(Of CoOptions_SecondCheckPayCode)
        Dim SourceCoOptionsSecondCheck = (From A In DB.CoOptions_SecondCheckPayCodes Where A.CoNum = SourceCoNum AndAlso A.EmpNum = SourceEmpNum Order By A.PayCode).ToList
        For Each rec In SourceCoOptionsSecondCheck
            Dim NewEnt = rec.CloneEntity
            NewEnt.ID = 0
            NewCoOptionsSecondCheckList.Add(NewEnt)
        Next

        Dim NotCopiedMsg As New List(Of String)
        Dim OPCount = (From A In DB.EMP_OPS Where A.CONUM = SourceCoNum AndAlso A.EMPNUM = SourceEmpNum).Count
        If OPCount > 0 Then NotCopiedMsg.Add(OPCount & " Auto pay" & If(OPCount = 1, "", "s"))
        Dim ODCount = (From A In DB.EMP_DEDS Where A.CONUM = SourceCoNum AndAlso A.EMPNUM = SourceEmpNum).Count
        If ODCount > 0 Then NotCopiedMsg.Add(ODCount & " Auto deds" & If(ODCount = 1, "", "s"))
        If NotCopiedMsg.Count > 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show(String.Join(" / ", NotCopiedMsg) & " are not copied over." & vbCrLf & vbCrLf & "Please make sure setup is correct, and then click 'Save & Copy Pays/Deductions' button.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
        LoadEmployee(NewEmployeeEnt, NewStateInfoList, Nothing, NewCheckOverrdiesList, NewCoOptionsSecondCheckList)
        Me.popupCopyFrom.Hide()
        If NotCopiedMsg.Count > 0 Then
            Me.btnCopy.Enabled = False
            Me.btnSaveAndCopyPaysLI.ContentVisible = True
        End If
    End Sub

    Private Sub slueEmployeeCopyFrom_EditValueChanged(sender As Object, e As EventArgs) Handles slueEmployeeCopyFrom.EditValueChanged
        Me.txtEmpNum.Text = Nothing
        If Me.slueEmployeeCopyFrom.HasValue Then
            Dim EmpNum As Decimal = Me.slueEmployeeCopyFrom.EditValue
            Me.txtEmpNum.EditValue = (From A In CType(Me.slueEmployeeCopyFrom.Properties.DataSource, IList) Where A.EMPNUM = EmpNum Select A.EmpName).First

            Dim EmpNumExist = DB.EMPLOYEEs.Where(Function(emp) emp.CONUM = Me.CoNum AndAlso emp.EMPNUM = EmpNum).Count > 0
            If EmpNumExist AndAlso chkUseSameEmpID.Enabled = True Then
                chkUseSameEmpID.Checked = False
                chkUseSameEmpID.Enabled = False
            ElseIf EmpNumExist = False AndAlso chkUseSameEmpID.Enabled = False Then
                chkUseSameEmpID.Enabled = True
            End If
        ElseIf chkUseSameEmpID.Checked Then
            chkUseSameEmpID.Checked = False
        End If
    End Sub

    Private Sub popupCopyFrom_Popup(sender As Object, e As EventArgs) Handles popupCopyFrom.VisibleChanged
        Me.LayoutControlGroupTabs.Enabled = Not Me.popupCopyFrom.Visible
    End Sub

    Private Sub DD_ACC_TYPE__EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles DD_ACC_TYPE_1ComboBoxEdit.EditValueChanging, DD_ACC_TYPE_4ComboBoxEdit.EditValueChanging, DD_ACC_TYPE_3ComboBoxEdit.EditValueChanging, DD_ACC_TYPE_2ComboBoxEdit.EditValueChanging
        If e.NewValue <> "None" Then
            If Not _IsLoading Then
                Dim employee As EMPLOYEE = EMPLOYEEBindingSource.DataSource
                employee.DD_STATUS_1 = "Pre-Note"
                employee.DD_STATUS_2 = "Pre-Note"
                employee.DD_STATUS_3 = "Pre-Note"
                employee.DD_STATUS_4 = "Pre-Note"
            End If
        End If
    End Sub

    Private Sub RATE_2TextEdit_Validated(sender As Object, e As EventArgs) Handles RATE_2TextEdit.Validated
        Dim employee As EMPLOYEE = EMPLOYEEBindingSource.DataSource
        If employee.RATE_2.HasValue Then employee.LAST_RAISE_2 = DateTime.Now
    End Sub

    Private Sub RATE_1TextEdit_Validated(sender As Object, e As EventArgs) Handles RATE_1TextEdit.Validated
        Dim employee As EMPLOYEE = EMPLOYEEBindingSource.DataSource
        If employee.RATE_1.HasValue Then employee.LAST_RAISE_1 = DateTime.Now
    End Sub

    Private Sub RATE_3TextEdit_Validated(sender As Object, e As EventArgs) Handles RATE_3TextEdit.Validated
        Dim employee As EMPLOYEE = EMPLOYEEBindingSource.DataSource
        If employee.RATE_3.HasValue Then employee.LAST_RAISE_3 = DateTime.Now
    End Sub

    Private Sub btnSave_Click_1(sender As Object, e As EventArgs) Handles btnSave.Click
        Save()
    End Sub

    Private Sub btnNewEmployee_Click(sender As Object, e As EventArgs) Handles btnNewEmployee.Click
        If HasChanges() Then
            Dim Changes = New pr_batch_employee_change With {.change_log = Me.GetChanges}
            DisplayMessageBox("You must save or cancel changes before adding a new employee." & vbCrLf & Changes.GetFormattedString)
            Exit Sub
        End If
        Me.EmployeeID = 0
        Me.EmployeeEnt = Nothing
        LoadEmployee()
        Me.TabbedControlGroup1.SelectedTabPageIndex = 0
        Me.F_NAMETextEdit.Select()
    End Sub

    Private Sub RefreshAutoPays()
        Me.AutoPaysAndDeds = New List(Of EmpAutoPay)
        If Me.EmployeeID <> 0 Then
            Dim PaysAndDedData = DB.prc_GetAutoPaysForPayroll(Me.CoNum, 0, Me.EmployeeID, True, False).ToList
            Me.AutoPaysAndDeds.AddRange(From A In PaysAndDedData Select EmpAutoPay.From_SP_Results(A))
        End If
        Me.EmpAutoPaysBindingSource.DataSource = (From A In AutoPaysAndDeds Where A.Type = "P").ToList
        Me.EmpAutoDedsBindingSource.DataSource = (From A In AutoPaysAndDeds Where A.Type = "D").ToList
    End Sub

    Private Sub GridPays_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles GridPays.MouseDoubleClick, GridDeds.MouseDoubleClick
        Dim grid As DevExpress.XtraGrid.GridControl = sender
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = grid.MainView
        Dim hi = view.CalcHitInfo(e.Location)
        If hi.InRow AndAlso hi.RowHandle >= 0 Then
            Dim row As EmpAutoPay = view.GetRow(hi.RowHandle)
            EditAutoPay(row, row.Type)
        End If
    End Sub

    Sub EditAutoPay(Rec As EmpAutoPay, RecType As Char)
        If Rec IsNot Nothing AndAlso RecType = "D" AndAlso Rec.IsNewRec = False Then
            Rec.SourceEntity = (From A In DB.EMP_DEDS_SETUPs Where A.CONUM = CoNum AndAlso A.EMPNUM = Me.EmployeeEnt.EMPNUM AndAlso A.deduction_key = Rec.RecordKey).SingleOrDefault
        End If
        Dim frm As New frmEmployeeAutoPay With {.AutoPayEnt = Rec, .EmployeForm = Me, .RecType = RecType}
        Dim results = frm.ShowDialog
        frm.Dispose()
        If results = DialogResult.OK Then
            Me.RefreshAutoPays()
        End If
    End Sub

    Private Sub btnAddAutoPay_Click(sender As Object, e As EventArgs) Handles btnAddAutoPay.Click
        EditAutoPay(Nothing, "P"c)
    End Sub

    Private Sub btnAddAutoDeduction_Click(sender As Object, e As EventArgs) Handles btnAddAutoDeduction.Click
        EditAutoPay(Nothing, "D"c)
    End Sub

    Private Sub btnEditAutoPay_Click(sender As Object, e As EventArgs) Handles btnEditAutoPay.Click
        Dim row As EmpAutoPay = GridViewPays.GetFocusedRow
        If row IsNot Nothing Then
            EditAutoPay(row, row.Type)
        End If
    End Sub

    Private Sub btnEditAutoDeduction_Click(sender As Object, e As EventArgs) Handles btnEditAutoDeduction.Click
        Dim row As EmpAutoPay = GridViewDeds.GetFocusedRow
        If row IsNot Nothing Then
            EditAutoPay(row, row.Type)
        End If
    End Sub

    Private Sub ACC_NOTextEdit_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles DD_ACC_NO_1TextEdit.Validating, DD_ACC_NO_4TextEdit.Validating, DD_ACC_NO_3TextEdit.Validating, DD_ACC_NO_2TextEdit.Validating
        Dim te As TextEdit = sender
        If te.Text.IsNullOrWhiteSpace OrElse (te.OldEditValue Is DBNull.Value AndAlso te.EditValue Is DBNull.Value) OrElse ((te.OldEditValue IsNot DBNull.Value) AndAlso te.OldEditValue = te.EditValue) Then Exit Sub
        Try
            te.Properties.PasswordChar = "*"
            Dim ssnOrTin = IIf(EmployeeEnt.EMP_TYPE = "CONTRACT", EmployeeEnt.TIN, EmployeeEnt.SSN)
            Dim frm = New frmEmployeeDDConfirmation(te.Text, EmployeeEnt.F_NAME, EmployeeEnt.L_NAME, ssnOrTin) With {.StartPosition = FormStartPosition.CenterParent}
            If frm.ShowDialog(Me) <> DialogResult.OK Then
                e.Cancel = True
                te.SelectAll()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in validate DD info", ex)
            e.Cancel = True
        Finally
            te.Properties.PasswordChar = ""
        End Try
    End Sub

    Private Sub ZIPTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles ZIPTextEdit.EditValueChanged
        Dim newValue As String = DirectCast(ZIPTextEdit.Text, String)
        If Not String.IsNullOrWhiteSpace(newValue) Then
            If newValue.Length = 6 AndAlso newValue(5) <> "-" AndAlso newValue(4) <> "-" Then
                ZIPTextEdit.EditValue = newValue.Substring(0, 5) & "-" & newValue.Substring(5, 1)
                ZIPTextEdit.Refresh()
                ZIPTextEdit.SelectionStart = 7
                'ElseIf newValue.Length = 6 AndAlso newValue(5) = "-" Then
                '    ZIPTextEdit.EditValue = newValue.Substring(0, 5)
                '    ZIPTextEdit.Refresh()
                '    ZIPTextEdit.SelectionStart = 6
            End If
        End If
    End Sub

    Private Sub ZIPTextEdit_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles ZIPTextEdit.EditValueChanging
        Dim newValue As String = DirectCast(e.NewValue, String)
        If Not String.IsNullOrWhiteSpace(newValue) Then
            If Not IsNumeric(newValue.Substring(0, Math.Min(newValue.Length, 5))) _
                OrElse (newValue.Length >= 7 AndAlso (Not IsNumeric(newValue.Substring(6, newValue.Length - 6)) OrElse newValue(6) = "-")) _
                OrElse newValue.Length > 10 Then
                e.Cancel = True
            End If
        End If
    End Sub


    Private Sub NetOverrideAdjustTypeTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles NetOverrideAdjustTypeTextEdit.EditValueChanged
        Dim CurrentRow As pr_batch_overrides_setup = Me.Pr_batch_overrideBindingSource.Current
        If CurrentRow Is Nothing Then Exit Sub
        Me.NetOverrideAdjustTypeTextEdit.DoValidate()

        If CurrentRow.NetOverrideAdjustType IsNot Nothing AndAlso CurrentRow.NetOverrideAdjustType.StartsWith("Deduction") Then
            Me.NetOverrideDedNumTextEdit.Enabled = True
        Else
            Me.NetOverrideDedNumTextEdit.Enabled = False
            CurrentRow.NetOverrideDedNum = Nothing
        End If
    End Sub

    Private Sub btnSaveAndCopyPays_Click(sender As Object, e As EventArgs) Handles btnSaveAndCopyPays.Click
        Dim EmployeeID = EmployeeEnt.EMPNUM
        If Save() Then
            Me.EmployeeID = EmployeeID
            LoadEmployee()
        Else
            Exit Sub
        End If

        Dim SourceCoNum As Decimal = Me.slueCompanyCopyFrom.EditValue
        Dim SourceEmpNum As Decimal = Me.slueEmployeeCopyFrom.EditValue

        Dim NotCopiedMsg As New List(Of String)

        Dim Departments = (From A In DB.DEPARTMENTs Where A.CONUM = Me.CoNum).ToList
        Dim SourceOPs = (From A In DB.EMP_OPS Where A.CONUM = SourceCoNum AndAlso A.EMPNUM = SourceEmpNum).ToList
        For Each op In SourceOPs
            Dim ent = op.CloneEntity()
            If Not (From A In Me.OtherPays Where A.OTH_PAY_NUM = ent.OPS_NUM).Any Then
                NotCopiedMsg.Add("Pay # " & ent.OPS_NUM)
            Else
                ent.CONUM = Me.CoNum : ent.EMPNUM = Me.EmployeeID : ent.otherpay_key = Guid.NewGuid : ent.rowguid = Guid.NewGuid
                Dim Dept = (From A In Departments Where A.DIVNUMD = ent.OPS_DIVNUM AndAlso A.DEPTNUM = ent.OPS_DEPTNUM).FirstOrDefault
                If Dept Is Nothing Then
                    ent.OPS_DIVNUM = Me.EmployeeEnt.DIVNUM : ent.OPS_DEPTNUM = Me.EmployeeEnt.DEPTNUM
                End If
                Me.DB.EMP_OPS.InsertOnSubmit(ent)
            End If
        Next

        Dim SourceDeds = (From A In DB.EMP_DEDS_SETUPs Where A.CONUM = SourceCoNum AndAlso A.EMPNUM = SourceEmpNum).ToList
        For Each ded In SourceDeds
            Dim ent = ded.CloneEntity()
            If Not (From A In Me.DeductionCodes Where A.DED_NUM = ent.DED_NUM_E).Any Then
                NotCopiedMsg.Add("Ded # " & ent.DED_NUM_E)
            Else
                ent.CONUM = Me.CoNum : ent.EMPNUM = Me.EmployeeID : ent.deduction_key = Guid.NewGuid : ent.rowguid = Guid.NewGuid
                Dim Dept = (From A In Departments Where A.DIVNUMD = ent.DED_DIVNUM AndAlso A.DEPTNUM = ent.DED_DEPTNUM).FirstOrDefault
                If Dept Is Nothing Then
                    ent.DED_DIVNUM = Me.EmployeeEnt.DIVNUM : ent.DED_DEPTNUM = Me.EmployeeEnt.DEPTNUM
                End If
                If ded.DED_LIMIT_E.GetValueOrDefault <> 0 Then
                    'Get Balance
                    Dim Paid As Decimal? = (From A In DB.CHK_DET_DEDs
                                            Join P In DB.PAYROLLs On A.CONUM Equals P.CONUM And A.PAYROLL_NUM Equals P.PRNUM
                                            Where A.CONUM = ded.CONUM AndAlso A.EMPNUM = ded.EMPNUM AndAlso A.CL_CODE = ded.DED_NUM_E AndAlso
                                                 (P.CHECK_DATE >= ded.DED_STARTDATE OrElse P.CHECK_DATE Is Nothing)
                                            Select A.CL_AMOUNT).Sum
                    If Paid > 0 Then
                        ent.DED_LIMIT_E = ded.DED_LIMIT_E - Paid
                    End If
                End If
                Me.DB.EMP_DEDS_SETUPs.InsertOnSubmit(ent)

                Dim Scdls = (From A In DB.emp_deds_amount_schedules Where A.deduction_key = ded.deduction_key AndAlso (A.end_date Is Nothing OrElse A.end_date >= Date.Today)).ToList
                For Each itm In Scdls
                    Dim nScdl As New emp_deds_amount_schedule With {.conum = Me.CoNum, .empnum = Me.EmployeeID, .deduction_key = ent.deduction_key, .amount_key = Guid.NewGuid,
                        .amount = itm.amount, .end_date = itm.end_date, .rowguid = Guid.NewGuid, .start_date = itm.start_date}
                    Me.DB.emp_deds_amount_schedules.InsertOnSubmit(nScdl)
                Next
            End If
        Next

        If NotCopiedMsg.Count > 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The following has not been copied, because they are not setup on this company." & Environment.NewLine & String.Join(Environment.NewLine, NotCopiedMsg), "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If

        If Me.Save() Then
            LoadEmployee()
        End If
    End Sub

    Private Sub WrkStateComboBoxEdit_EditValueChanged(sender As Object, e As EventArgs) Handles WrkStateComboBoxEdit.EditValueChanged
        Me.ResStateComboBoxEdit.Enabled = Me.WrkStateComboBoxEdit.HasValue
    End Sub

    Private Sub WrkStateComboBoxEdit_Validated(sender As Object, e As EventArgs) Handles WrkStateComboBoxEdit.Validated
        If Not Me.WrkStateComboBoxEdit.HasValue Then Me.ResStateComboBoxEdit.EditValue = Nothing
    End Sub

    Public Event EndEditResults As EmployeeEndEditHandler
    Public Delegate Sub EmployeeEndEditHandler(result As EmployeeEndEditResults)

#End Region


    Public Class EmployeeList
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property EmpNum As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property FirstName As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property LastName As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property MiddleName As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property DivNum As Decimal?
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property DepNum As Decimal?
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property StartDate As DateTime?
        ReadOnly Property DisplayName As String
            Get
                Return $"{LastName}, {FirstName} {MiddleName} - {EmpNum} - {DepNum}, {IIf(StartDate.HasValue, StartDate.Value.ToString("d"), "")}".ToUpper
            End Get
        End Property
        Public TermDate As DateTime?

        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property HasChanges As Boolean
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Ssn As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Tin As String
    End Class

    Public Class Taxes
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Category As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Description As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Taxable As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Capped As Decimal
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property Amount As Decimal
    End Class

    Private Sub btnUtilities_Click(sender As Object, e As EventArgs) Handles btnUtilities.Click
        Me.btnUtilities.ShowDropDown()
    End Sub

    Private Sub PrintW2Report()
        Using frm = New frmPrintW2(Me.CoNum, Me.EmployeeID)
            frm.ShowDialog()
        End Using
    End Sub

    Private Sub rgrp_w4Style_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgrp_w4Style.SelectedIndexChanged
        Me.grpTY2020Options.Enabled = rgrp_w4Style.SelectedIndex = 1
        Me.FED_DEPSTextEdit.Enabled = rgrp_w4Style.SelectedIndex = 0
    End Sub

    Private Sub FED_WH_EXE_FGETextEdit_CheckedChanged(sender As Object, e As EventArgs) Handles FED_WH_EXE_FGETextEdit.CheckedChanged
        Me.grpFedOptions.Enabled = Not FED_WH_EXE_FGETextEdit.Checked
        Me.grpTY2020Options.Enabled = Not FED_WH_EXE_FGETextEdit.Checked
    End Sub

    Private Sub sbRequestDDSetup_Click(sender As Object, e As EventArgs) Handles sbRequestDDSetup.Click
        Try
            Dim SqlStr = GetUdfValue("DD_Setup_Email_Notification_Zendesk_Script")
            Brands_FrontDesk.modGlobals.UpdateSql(SqlStr, New SqlParameter("CoNum", CoNum))
            DevExpress.XtraEditors.XtraMessageBox.Show("Setup team will reach out to client with DD enrollment kit", "Email Sent", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DisplayErrorMessage("Error Request DD Setup", ex)
        End Try
    End Sub

    Private Sub EMPLOYEEBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles EMPLOYEEBindingSource.CurrentChanged
        Alerts = New EmpInfoAlerts()
        gcEmpPriorDD.Visible = False
        lciPriorPaysDeds.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
    End Sub

    Private Sub sbDismiss_Click(sender As Object, e As EventArgs) Handles sbDismiss.Click
        gcEmpPriorDD.Visible = False
        Alerts.hadPriorDD = False
    End Sub

    Public Function HasAlerts() As Boolean
        Return Alerts.hadPriorDD OrElse Alerts.hadPriorPaysDeds
    End Function

    Public Sub HandleAlerts()
        Alerts.ShowMessage()
        If Alerts.hadPriorDD Then
            LayoutControl1.SuspendLayout()
            LayoutControl1.FocusHelper.FocusElement(lcgDirectDeposit, False)
            LayoutControl1.ResumeLayout()
        ElseIf Alerts.hadPriorPaysDeds Then
            LayoutControl1.SuspendLayout()
            LayoutControl1.FocusHelper.FocusElement(tabAutoPaysAndDeds, False)
            LayoutControl1.ResumeLayout()
        End If
    End Sub

    Private Class EmpInfoAlerts
        Public hadPriorDD As Boolean
        Public hadPriorPaysDeds As Boolean

        Private Function HasAlerts() As Boolean
            Return hadPriorDD OrElse hadPriorPaysDeds
        End Function

        Sub ShowMessage()
            Dim msg As String = ""

            Dim msgPriorDd As String = "This employee had direct deposit active prior to terminating, confirm with client if to activate again and review all dd setup"
            Dim msgPriorPaysDeds As String = "This employee has Auto Pays\Deds, make sure with client that this is correct or make the appropriate changes" _
            + vbCrLf + vbCrLf + "When done with the edits, click the ""Dismiss"" button on the Auto Pays / Deds page to be able to save."

            If hadPriorDD AndAlso hadPriorPaysDeds Then
                msg = msgPriorDd + vbCrLf + vbCrLf + msgPriorPaysDeds
            ElseIf hadPriorDD Then
                msg = msgPriorDd
            ElseIf hadPriorPaysDeds Then
                msg = msgPriorPaysDeds
            End If

            If msg <> "" Then
                Dim result = XtraMessageBox.Show(msg, "Prior Termination Alert", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            End If
        End Sub
    End Class

    Private Sub sbDismissAutoPaysDedsPanel_Click(sender As Object, e As EventArgs) Handles sbDismissAutoPaysDedsPanel.Click
        lciPriorPaysDeds.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        Alerts.hadPriorPaysDeds = False
    End Sub

    Private Sub B_DAYDateEdit_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles B_DAYDateEdit.EditValueChanging
        If IsDate(e.NewValue) AndAlso (CDate(e.NewValue).Year < 1900 OrElse CDate(e.NewValue).Year > 2079) Then
            MessageBox.Show("Invalid Date", "Reseting back Date")
            e.Cancel = True
        End If
    End Sub
End Class