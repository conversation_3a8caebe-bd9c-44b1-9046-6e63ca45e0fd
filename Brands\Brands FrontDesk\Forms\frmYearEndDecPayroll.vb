﻿Imports System.ComponentModel
Imports DevExpress.XtraSplashScreen

Public Class frmYearEndDecPayroll

    Dim DB As dbEPDataDataContext
    Dim _IsLoaded As Boolean

    Dim data As List(Of prc_YearEnd_WithNoDecPayrollResult)
    Dim frmLogger As Serilog.ILogger

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property RunType As RunTypeEnum
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoRun As Boolean = False

    Public Sub New()
        frmLogger = Logger.ForContext(Of frmYearEndDecPayroll)()
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Private Sub frmYearEndDecPayroll_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        gcRecentInvoiceLog.Visible = Me.RunType = RunTypeEnum.OpenInvoice

        DB = New dbEPDataDataContext(GetConnectionString) With {.CommandTimeout = 1200}

        Dim Y = If(Today.Month = 12, Today, New Date(Today.Year - 1, 12, 1))
        Y = New Date(Y.Year, 12, 1)
        Me.YearEnd.Properties.Items.Add(Y.Year)
        Me.YearEnd.Properties.Items.Add(Y.AddYears(-1).Year)

        Me.YearEnd.EditValue = Y.Year
        Me.DateEdit.EditValue = GetLastQuarterEndDate()

        Me.pnlSelectYear.Visible = (RunType <> RunTypeEnum.ZeroReurns AndAlso RunType <> RunTypeEnum.UnlimitedPlanInv)
        Me.pnlSelectDate.Visible = (RunType = RunTypeEnum.ZeroReurns OrElse RunType = RunTypeEnum.UnlimitedPlanInv)

        Select Case Me.RunType
            Case RunTypeEnum.YearEnd
                Me.txtEntryType.EditValue = "BrandsXpressW2"
                Me.txtPRDesc.EditValue = "W2 Bill"
            Case RunTypeEnum.OpenInvoice
                Me.txtEntryType.EditValue = "BrandsXpressBill"
                Me.txtPRDesc.EditValue = "Bill"
            Case RunTypeEnum.ZeroReurns
                Me.txtEntryType.EditValue = "BrandsXpressZeroReturns"
                Me.txtPRDesc.EditValue = "Zero Returns"
            Case RunTypeEnum.RefundBilling
                Me.txtEntryType.EditValue = "BrandsXpressRefundBilling"
                Me.txtPRDesc.EditValue = "Refund Billing"
            Case RunTypeEnum.UnlimitedPlanInv
                Me.txtEntryType.EditValue = "BrandsXpressUnlPlanInv"
                Me.txtPRDesc.EditValue = "Unlimited Plan Inv"
        End Select
        Me.GridControlInvoiceToRefund.Visible = Me.RunType = RunTypeEnum.RefundBilling
    End Sub

    Private Async Sub frmYearEndDecPayroll_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        If AutoRun AndAlso RunType = RunTypeEnum.UnlimitedPlanInv Then
            Me.DateEdit.EditValue = DateTime.Today
            Me.DateEdit.EditValue = DateTime.Today.AddDays(-99)
        ElseIf RunType = RunTypeEnum.UnlimitedPlanInv Then
            Me.DateEdit.EditValue = DateTime.Today
        ElseIf RunType = RunTypeEnum.RefundBilling Then
            Me.DateEdit.EditValue = DateTime.Today
        End If

        Await LoadData()

        Me.gridData.ForceInitialize()

        'Make all read only
        For Each col As DevExpress.XtraGrid.Columns.GridColumn In Me.gridViewData.Columns
            If col IsNot colSelectAuto Then _
                col.OptionsColumn.ReadOnly = True
        Next

        _IsLoaded = True

        Me.gridViewData.FocusedRowHandle = -1
        Me.gridViewData.Focus()
        Me.gridData.Select()
        If Me.gridViewData.RowCount > 0 Then
            Me.ViewBindingSource_CurrentChanged(sender, e)
        End If
        Select Case RunType
            Case RunTypeEnum.OpenInvoice
                Text = "Inactive Co With open Invoice"
            Case RunTypeEnum.YearEnd
                Text = "Year end no Dec payroll"
            Case RunTypeEnum.ZeroReurns
                Text = "Zero Returns"
            Case RunTypeEnum.RefundBilling
                Text = "Refund Billing"
                Me.colJanRunDate.Visible = False
                Me.colDecRunDate.Visible = False
                Me.colDecPrForUI.Visible = False
                Me.colW2Bill.Visible = False
                Me.colQtrJob.Visible = False
                Me.colFrequency.Visible = False
                Me.colOpenPayrollStatus.Visible = False
                Me.colNextProcessDate.Visible = False
                Me.colNotes.Visible = True
            Case RunTypeEnum.UnlimitedPlanInv
                Text = "Ulimited Plan Inv"
        End Select

        'If RunType = RunTypeEnum.UnlimitedPlanInv AndAlso AutoRun Then
        '    Dim frm = New EmailService("<EMAIL>")
        '    'frm.ToEmail.AddRange(GetUdfValue("AutoTasksEmailNotification").Split(";"))
        '    frm.ToEmail.Add("<EMAIL>")
        '    Try
        '        frmLogger.Information("AutoRun is True")
        '        gridViewData.SelectAll()
        '        btnProcessAutos.PerformClick()
        '        frm.Subject = $"Auto YearEndDecPayroll {RunType} - Success"
        '        frm.Body = $"Auto YearEndDecPayroll {RunType} - completed successfully"
        '        frm.SendEmail()
        '        frmLogger.Information($"Finished sending YearEndDecPayroll {RunType}")
        '    Catch ex As Exception
        '        frmLogger.Error(ex, $"Error in YearEndDecPayroll {RunType} AutoRun")
        '        frm.Subject = $"Error in YearEndDecPayroll {RunType} AutoRun"
        '        frm.Body = ex.ToString()
        '        frm.SendEmail()
        '        Application.Exit()
        '    End Try
        '    Application.Exit()
        'End If
    End Sub

    Public Async Function LoadData() As Threading.Tasks.Task
        SplashScreenManager.ShowForm(Me, GetType(WaitForm1), True, True, False)
        Try
            Me.ToolStripStatusLabel1.Text = "Loading Data"
            Me.gridData.Enabled = False
            Application.DoEvents()
            Await System.Threading.Tasks.Task.Run(Sub()

                                                      DB = New dbEPDataDataContext(GetConnectionString) With {.CommandTimeout = 1200}

                                                      Dim d As Date
                                                      If RunType = RunTypeEnum.ZeroReurns OrElse RunType = RunTypeEnum.UnlimitedPlanInv OrElse RunType = RunTypeEnum.RefundBilling Then
                                                          d = Me.DateEdit.EditValue
                                                      Else
                                                          d = New Date(Me.YearEnd.EditValue, 12, 31)
                                                      End If

                                                      data = DB.prc_YearEnd_WithNoDecPayroll(d, Me.RunType).ToList
                                                  End Sub)

            If Not _IsLoaded Then
                btnSelectAll_Click(btnSelectAll, New EventArgs)
            End If
            RefreshSelectedCount()

            Me.ViewBindingSource.DataSource = data
            Me.ToolStripStatusLabel1.Text = String.Format("Ready - {0} record{1} loaded", data.Count, If(data.Count = 1, "", "s"))

            Me.gridData.Enabled = True
            gridViewData.BestFitColumns()
            colUDF_STRING.Width = 125
            colCoName.Width = 250
            SplashScreenManager.CloseForm(False)
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
            SplashScreenManager.CloseForm(False)
        End Try
    End Function

    Private Async Sub FromDateDE_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles _
                                                                                                                      _
                    YearEnd.EditValueChanged
        If Not _IsLoaded Then Exit Sub
        Dim ctrl As DevExpress.XtraEditors.BaseEdit = sender
        If ctrl.IsModified Then
            ctrl.IsModified = False
            Await LoadData()
        End If
    End Sub

    Sub GetNotes()
        Dim row As prc_YearEnd_WithNoDecPayrollResult = Me.gridViewData.GetFocusedRow
        If row Is Nothing Then
            Me.Notes_tsBindingSource.Clear()
            Me.InvoiceBindingSource.Clear()
            Me.UcCompInfo1.Clean()
            Exit Sub
        End If

        Dim comp = DB.COMPANies.SingleOrDefault(Function(c) c.CONUM = row.CONUM)
        Me.UcCompInfo1.LoadDate(comp, Nothing, Nothing)

        Me.Notes_tsBindingSource.DataSource = (From A In DB.NOTEs
                                               Where A.conum = row.CONUM _
                                               AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) _
                                               AndAlso A.category <> "Employee"
                                               Order By If(A.category <> "Delivery", 1, 2)).ToList

        If Me.RunType = RunTypeEnum.RefundBilling Then
            'Me.InvoiceBindingSource.DataSource = (From a In DB.INVOICE_xLOGs Where a.CONUM = row.CONUM AndAlso a.PRICE < 0 AndAlso a.INVOICE_NUM <= 0).ToList
            Me.InvoiceBindingSource.DataSource = (From d In DB.invoice_item_details Join m In DB.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                  Where d.conum = row.CONUM AndAlso m.invoice_number <= 0 AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing)
                                                  Select ITEM_NAME = d.item_name, ITEM_DATE = d.item_date, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number).ToList()

        End If
    End Sub

    Private Async Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        Await LoadData()
    End Sub

    Private Sub riSelectRunAuto_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riSelectRunAuto.EditValueChanged
        Dim value = CType(Me.gridViewData.ActiveEditor, DevExpress.XtraEditors.CheckEdit).Checked
        Dim row As prc_YearEnd_WithNoDecPayrollResult = Me.gridViewData.GetRow(Me.gridViewData.FocusedRowHandle)
        If row Is Nothing Then Exit Sub
        If value Then
            If Not String.IsNullOrEmpty(row.OpenPayrollStatus) Then
                DisplayMessageBox("There is an open payroll for this company")
                value = False
            ElseIf (Not String.IsNullOrEmpty(row.PR_PASSWORD)) Then
                If Not ConfirmPassword(row.PR_PASSWORD) Then
                    value = False
                End If
            End If
            If value = False Then
                Me.gridViewData.SetFocusedValue(False)
            End If
        End If
        Me.gridViewData.PostEditor()
        Me.gridViewData.UpdateCurrentRow()
        RefreshSelectedCount()
    End Sub

    Sub RefreshSelectedCount()
        Dim Count = (From A In data Where A.IsSelect = True).Count
        Me.lblSelectedCount.Text = String.Format("{0} compan{1} selected to process.", Count, If(Count = 1, "y", "ies"))
        Me.btnProcessAutos.Enabled = Count > 0
    End Sub

    Private Async Sub btnProcessAutos_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnProcessAutos.Click
        Dim Count = (From A In data Where A.IsSelect = True).Count

        If Not AutoRun Then
            If DevExpress.XtraEditors.XtraMessageBox.Show(String.Format("You are about to process {0} payroll{1}{2}Continue", Count, If(Count = 1, "", "s"), vbCrLf), "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.No Then Exit Sub
        End If

        Dim YearEnd As Date = IIf(RunType = RunTypeEnum.UnlimitedPlanInv OrElse RunType = RunTypeEnum.RefundBilling, DateEdit.EditValue, New Date(Me.YearEnd.EditValue, 12, 31))

        Dim SelectedCals = (From A In data Where A.IsSelect = True
                            Order By A.CONUM
                            Select New RunAutoStatus With {.CalID = -1,
                                                           .CoNum = A.CONUM,
                                                           .CoName = A.CO_NAME,
                                                           .Status = "Waiting",
                                                           .CheckDate = A.ChkDateToBe,
                                                           .IsYearEnd = True,
                                                           .RunDate = GetRunDate(A.CO_BILL_FREQ, YearEnd),
                                                           .PrDesc = A.PR_DESCR,
                                                           .VoidOnly = A.VoidOnly,
                                                           .SUPP_PR = If(RunType = RunTypeEnum.UnlimitedPlanInv, "YES", "NO")
                                                          }
                                                       ).ToList()

        Dim frm = New frmRunAutos With {.RunAutoList = SelectedCals, .YearEndBilling = Me, .CloseWhenDone = AutoRun}
        Dim results = frm.ShowDialog()
        Await Me.LoadData()
    End Sub

    Private Function GetRunDate(CO_BILL_FREQ As String, yearEnd As DateTime) As DateTime
        If RunType = RunTypeEnum.YearEnd Then
            Return If(CO_BILL_FREQ.ToUpper = "PER PAYROLL", yearEnd.AddDays(1), yearEnd)
        ElseIf RunType = RunTypeEnum.ZeroReurns Then
            Return DateEdit.DateTime
        Else
            Return Today
        End If
    End Function

    Private Sub btnSelectNone_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectNone.Click
        data.ForEach(Sub(p) p.IsSelect = False)
        Me.gridViewData.RefreshData()
        RefreshSelectedCount()
    End Sub

    Private Sub ViewBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles ViewBindingSource.CurrentChanged
        If Not _IsLoaded Then Exit Sub
        GetNotes()
        Dim Row As prc_YearEnd_WithNoDecPayrollResult = Me.ViewBindingSource.Current
        Me.btnUpdateRunDate.Enabled = Row IsNot Nothing AndAlso (Row.CO_BILL_FREQ = "Per Month" AndAlso Row.DecRunDate = "" AndAlso Row.JanRunDate = "Yes")
    End Sub

    Private Sub btnSelectAll_Click(sender As Object, e As EventArgs) Handles btnSelectAll.Click
        For Each row In data
            If (Not (row.UDF_STRING & "").ToUpper.StartsWith("SW")) AndAlso String.IsNullOrEmpty(row.PR_PASSWORD) AndAlso String.IsNullOrEmpty(row.OpenPayrollStatus) Then
                row.IsSelect = True
            End If
        Next
        Me.gridViewData.RefreshData()
        Me.RefreshSelectedCount()
    End Sub

    Private Async Sub btnUpdateRunDate_Click(sender As Object, e As EventArgs) Handles btnUpdateRunDate.Click
        Dim Row As prc_YearEnd_WithNoDecPayrollResult = Me.ViewBindingSource.Current
        Dim JanDate = New Date(Me.YearEnd.EditValue, 12, 31).AddDays(1)
        Dim JanEndDate = JanDate.AddMonths(1)
        Dim PayrollRec = (From A In DB.PAYROLLs Where A.CONUM = Row.CONUM AndAlso A.RUN_DATE >= JanDate AndAlso A.RUN_DATE < JanEndDate Order By A.PRNUM).FirstOrDefault
        Dim PayrollExtRec = (From A In DB.payroll_exts Where A.conum = Row.CONUM AndAlso A.prnum = PayrollRec.PRNUM).FirstOrDefault

        If PayrollRec IsNot Nothing Then
            Dim Msg = String.Format("You are about to update 'RUN_DATE & Billing Period Date' for Co # '{3}', Pr # '{0}' from '{1}' to '12/31/{2}'", PayrollRec.PRNUM, PayrollRec.RUN_DATE, Me.YearEnd.EditValue, PayrollRec.CONUM)
            If DevExpress.XtraEditors.XtraMessageBox.Show(Msg & vbCrLf & vbCrLf & "Continue?", "Confirm Update", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes Then
                PayrollRec.RUN_DATE = New Date(Me.YearEnd.EditValue, 12, 31)
                PayrollExtRec.billing_period_date = New Date(Me.YearEnd.EditValue, 12, 31)
                DB.SubmitChanges()
                Await LoadData()
            End If
        End If
    End Sub

    Public Enum RunTypeEnum
        YearEnd = 1
        OpenInvoice = 2
        ZeroReurns = 3
        RefundBilling
        UnlimitedPlanInv = 5
    End Enum

    Private Sub gridViewData_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gridViewData.FocusedRowObjectChanged
        Try
            Dim row = CType(gridViewData.GetRow(e.FocusedRowHandle), prc_YearEnd_WithNoDecPayrollResult)
            If row Is Nothing Then
                gvRecentInvoiceLog.Columns.Clear()
            Else
                gcRecentInvoiceLog.DataSource = (From d In DB.invoice_item_details Join m In DB.invoice_masters On m.conum Equals d.conum And m.invoice_key Equals d.invoice_key
                                                 Where d.conum = row.CONUM AndAlso (d.item_deleted = 0 OrElse d.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing) AndAlso m.invoice_gen_freq = "Per Payroll" AndAlso m.invoice_number <= 0 AndAlso d.item_final_price > 0
                                                 Select ITEM_NAME = d.item_name, ITEM_DATE = d.item_date, ITEM_NUM = d.item_num, PRICE = d.item_final_price, CHK_ENTRIES = d.item_count, ITEM_FREQ_TYPE = m.invoice_gen_freq, INVOICE_NUM = m.invoice_number, ItemPrNum = d.item_prnum).ToList()
                gvRecentInvoiceLog.ExpandAllGroups()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading invoice history", ex)
        End Try
    End Sub
End Class
