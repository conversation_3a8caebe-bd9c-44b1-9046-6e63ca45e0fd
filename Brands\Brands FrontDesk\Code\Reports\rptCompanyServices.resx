﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="SqlDataSource1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="SqlDataSource1.ResultSchemaSerializable" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="IndustryOptions.SerializableRtfString" xml:space="preserve">
    <value>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</value>
  </data>
  <metadata name="BindingSourceServices.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 56</value>
  </metadata>
  <metadata name="BindingSourceDivisions.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 95</value>
  </metadata>
  <metadata name="BindingSourceRelatedCompanies.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 134</value>
  </metadata>
  <metadata name="BindingSourceCalendar.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 173</value>
  </metadata>
  <metadata name="BindingSourceAutos.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 212</value>
  </metadata>
  <metadata name="BindingSourceNotes.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 251</value>
  </metadata>
  <metadata name="BindingSourceContacts.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 290</value>
  </metadata>
</root>