﻿Imports System.ComponentModel
Imports Microsoft.EntityFrameworkCore

Public Class frmCompanyOpenOrders
    Public ReadOnly Property _Conum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property SelectedOrderNumber As Integer


    Sub New(_conum As Decimal)
        InitializeComponent()
        Me._Conum = _conum
    End Sub

    Private Sub frmCompanyOpenOrders_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                suppliesOrdersBindingSource.DataSource = ctxDB.SuppliesOrders.Where(Function(o) o.CoNum = _Conum AndAlso {"Pending", "Open"}.Contains(o.Status)).ToList
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GridView1.FocusedRowObjectChanged
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim row As SuppliesOrder = e.Row
            SelectedOrderNumber = row.ID
            'Dim result As ISingleResult(Of prc_RptPrCoverSheetResult) = ctx.prc_RptPrCoverSheet(_Conum, row.ID)
            'Dim address As prc_RptPrCoverSheetResult = result.Single

            ' Call the stored procedure using FromSqlInterpolated
            Dim results = ctxDB.Set(Of prc_RptPrCoverSheetResult)().FromSqlInterpolated($"EXEC prc_RptPrCoverSheet {_Conum}, {row.ID}").ToList()

            ' Assuming the stored procedure returns a single row
            Dim address As prc_RptPrCoverSheetResult = results.SingleOrDefault()

            meShippingAddress.Text = $"{address.ShipCo}{vbCrLf}{address.ShipAddress}{vbCrLf}{address.ShipCity}{vbTab}{address.ShipState}{vbTab}{address.ShipZip}"
        End Using
    End Sub

    Private Sub btnSelectOrder_Click(sender As Object, e As EventArgs) Handles btnSelectOrder.Click
        DialogResult = DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub
End Class