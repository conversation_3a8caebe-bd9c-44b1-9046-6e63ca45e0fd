﻿Imports System.ComponentModel

Public Class ucCompanyBankAccounts

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Conum As Decimal


    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub

    Private Sub ucCompanyBankAccounts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub


    Private Sub LoadData()
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                GridControl1.DataSource = db.prc_GetBankAccounts(Conum).ToList
            End Using
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error loading compnay bank accounts", ex)
        End Try
    End Sub
End Class
