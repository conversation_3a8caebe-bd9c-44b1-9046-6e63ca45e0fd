﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmUnassignedPhoneNumbers
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.btnSearch = New DevExpress.XtraEditors.SimpleButton()
        Me.deToDate = New DevExpress.XtraEditors.DateEdit()
        Me.deFromDate = New DevExpress.XtraEditors.DateEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colPhoneLine = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colext = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAgents = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colExternalNumber = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colcname = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colContactType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPhoneInfo = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsActive = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNumAll = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTalkTIme_sec = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTalkTime_hms = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRingTime_sec = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCalls = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRN = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReportCat = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTopDur = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTopCall = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.deToDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deToDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deFromDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.btnSearch)
        Me.LayoutControl1.Controls.Add(Me.deToDate)
        Me.LayoutControl1.Controls.Add(Me.deFromDate)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(845, 553)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'btnSearch
        '
        Me.btnSearch.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.search_16x16
        Me.btnSearch.Location = New System.Drawing.Point(359, 12)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Size = New System.Drawing.Size(118, 22)
        Me.btnSearch.StyleController = Me.LayoutControl1
        Me.btnSearch.TabIndex = 7
        Me.btnSearch.Text = "Search"
        '
        'deToDate
        '
        Me.deToDate.EditValue = Nothing
        Me.deToDate.Location = New System.Drawing.Point(246, 12)
        Me.deToDate.Name = "deToDate"
        Me.deToDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deToDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deToDate.Size = New System.Drawing.Size(109, 20)
        Me.deToDate.StyleController = Me.LayoutControl1
        Me.deToDate.TabIndex = 6
        '
        'deFromDate
        '
        Me.deFromDate.EditValue = Nothing
        Me.deFromDate.Location = New System.Drawing.Point(74, 12)
        Me.deFromDate.Name = "deFromDate"
        Me.deFromDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deFromDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deFromDate.Size = New System.Drawing.Size(118, 20)
        Me.deFromDate.StyleController = Me.LayoutControl1
        Me.deFromDate.TabIndex = 5
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(12, 38)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(821, 503)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colPhoneLine, Me.colext, Me.colAgents, Me.colExternalNumber, Me.colcname, Me.colCoNum, Me.colCoName, Me.colContactType, Me.colPhoneInfo, Me.colIsActive, Me.colCoNumAll, Me.colTalkTIme_sec, Me.colTalkTime_hms, Me.colRingTime_sec, Me.colCalls, Me.colRN, Me.colReportCat, Me.colTopDur, Me.colTopCall})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsBehavior.ReadOnly = True
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.ColumnAutoWidth = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colPhoneLine
        '
        Me.colPhoneLine.Caption = "Phone Line"
        Me.colPhoneLine.FieldName = "PhoneLine"
        Me.colPhoneLine.Name = "colPhoneLine"
        Me.colPhoneLine.Visible = True
        Me.colPhoneLine.VisibleIndex = 0
        '
        'colext
        '
        Me.colext.Caption = "Ext"
        Me.colext.FieldName = "ext"
        Me.colext.Name = "colext"
        Me.colext.Visible = True
        Me.colext.VisibleIndex = 1
        '
        'colAgents
        '
        Me.colAgents.Caption = "Agents"
        Me.colAgents.FieldName = "Agents"
        Me.colAgents.Name = "colAgents"
        Me.colAgents.Visible = True
        Me.colAgents.VisibleIndex = 2
        '
        'colExternalNumber
        '
        Me.colExternalNumber.Caption = "External Number"
        Me.colExternalNumber.FieldName = "ExternalNumber"
        Me.colExternalNumber.Name = "colExternalNumber"
        Me.colExternalNumber.Visible = True
        Me.colExternalNumber.VisibleIndex = 3
        '
        'colcname
        '
        Me.colcname.Caption = "cname"
        Me.colcname.FieldName = "cname"
        Me.colcname.Name = "colcname"
        Me.colcname.Visible = True
        Me.colcname.VisibleIndex = 4
        '
        'colCoNum
        '
        Me.colCoNum.Caption = "Co Num"
        Me.colCoNum.FieldName = "CoNum"
        Me.colCoNum.Name = "colCoNum"
        '
        'colCoName
        '
        Me.colCoName.Caption = "Co Name"
        Me.colCoName.FieldName = "CoName"
        Me.colCoName.Name = "colCoName"
        '
        'colContactType
        '
        Me.colContactType.Caption = "Contact Type"
        Me.colContactType.FieldName = "ContactType"
        Me.colContactType.Name = "colContactType"
        '
        'colPhoneInfo
        '
        Me.colPhoneInfo.Caption = "Phone Info"
        Me.colPhoneInfo.FieldName = "PhoneInfo"
        Me.colPhoneInfo.Name = "colPhoneInfo"
        '
        'colIsActive
        '
        Me.colIsActive.Caption = "IsActive"
        Me.colIsActive.FieldName = "IsActive"
        Me.colIsActive.Name = "colIsActive"
        '
        'colCoNumAll
        '
        Me.colCoNumAll.Caption = "Co Num All"
        Me.colCoNumAll.FieldName = "CoNumAll"
        Me.colCoNumAll.Name = "colCoNumAll"
        '
        'colTalkTIme_sec
        '
        Me.colTalkTIme_sec.Caption = "Talk TIme Sec"
        Me.colTalkTIme_sec.FieldName = "TalkTIme_sec"
        Me.colTalkTIme_sec.Name = "colTalkTIme_sec"
        Me.colTalkTIme_sec.Visible = True
        Me.colTalkTIme_sec.VisibleIndex = 5
        '
        'colTalkTime_hms
        '
        Me.colTalkTime_hms.Caption = "Talk Time HMS"
        Me.colTalkTime_hms.FieldName = "TalkTime_hms"
        Me.colTalkTime_hms.Name = "colTalkTime_hms"
        Me.colTalkTime_hms.Visible = True
        Me.colTalkTime_hms.VisibleIndex = 6
        '
        'colRingTime_sec
        '
        Me.colRingTime_sec.Caption = "Ring Time Sec"
        Me.colRingTime_sec.FieldName = "RingTime_sec"
        Me.colRingTime_sec.Name = "colRingTime_sec"
        Me.colRingTime_sec.Visible = True
        Me.colRingTime_sec.VisibleIndex = 7
        '
        'colCalls
        '
        Me.colCalls.Caption = "Calls"
        Me.colCalls.FieldName = "Calls"
        Me.colCalls.Name = "colCalls"
        Me.colCalls.Visible = True
        Me.colCalls.VisibleIndex = 8
        '
        'colRN
        '
        Me.colRN.Caption = "RN"
        Me.colRN.FieldName = "RN"
        Me.colRN.Name = "colRN"
        Me.colRN.Visible = True
        Me.colRN.VisibleIndex = 9
        '
        'colReportCat
        '
        Me.colReportCat.Caption = "Report Cat"
        Me.colReportCat.FieldName = "ReportCat"
        Me.colReportCat.Name = "colReportCat"
        Me.colReportCat.Visible = True
        Me.colReportCat.VisibleIndex = 10
        '
        'colTopDur
        '
        Me.colTopDur.Caption = "Top Dur"
        Me.colTopDur.FieldName = "TopDur"
        Me.colTopDur.Name = "colTopDur"
        Me.colTopDur.Visible = True
        Me.colTopDur.VisibleIndex = 11
        '
        'colTopCall
        '
        Me.colTopCall.Caption = "Top Call"
        Me.colTopCall.FieldName = "TopCall"
        Me.colTopCall.Name = "colTopCall"
        Me.colTopCall.Visible = True
        Me.colTopCall.VisibleIndex = 12
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.EmptySpaceItem1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(845, 553)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 26)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(825, 507)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.deFromDate
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.MaxSize = New System.Drawing.Size(184, 24)
        Me.LayoutControlItem2.MinSize = New System.Drawing.Size(184, 24)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(184, 26)
        Me.LayoutControlItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem2.Text = "From Date: "
        Me.LayoutControlItem2.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(57, 13)
        Me.LayoutControlItem2.TextToControlDistance = 5
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.deToDate
        Me.LayoutControlItem3.Location = New System.Drawing.Point(184, 0)
        Me.LayoutControlItem3.MaxSize = New System.Drawing.Size(163, 24)
        Me.LayoutControlItem3.MinSize = New System.Drawing.Size(163, 24)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(163, 26)
        Me.LayoutControlItem3.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem3.Text = "To Date: "
        Me.LayoutControlItem3.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(45, 13)
        Me.LayoutControlItem3.TextToControlDistance = 5
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.btnSearch
        Me.LayoutControlItem4.Location = New System.Drawing.Point(347, 0)
        Me.LayoutControlItem4.MaxSize = New System.Drawing.Size(122, 26)
        Me.LayoutControlItem4.MinSize = New System.Drawing.Size(122, 26)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(122, 26)
        Me.LayoutControlItem4.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(469, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(356, 26)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'frmUnassignedPhoneNumbers
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(845, 553)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "frmUnassignedPhoneNumbers"
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Unassigned Phone Numbers"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.deToDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deToDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deFromDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents btnSearch As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents deToDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents deFromDate As DevExpress.XtraEditors.DateEdit
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colPhoneLine As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colext As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colAgents As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colExternalNumber As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colcname As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colContactType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPhoneInfo As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsActive As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNumAll As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTalkTIme_sec As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTalkTime_hms As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRingTime_sec As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCalls As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRN As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReportCat As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTopDur As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTopCall As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
End Class
