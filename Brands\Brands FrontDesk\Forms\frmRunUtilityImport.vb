﻿Imports System.ComponentModel
Public Class frmRunUtilityImport
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Conum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property LastPayroll As PAYROLL

    Private Property Logger As Serilog.ILogger = modGlobals.Logger.ForContext(Of frmRunUtilityImport)

    Public Sub New()
        InitializeComponent()
    End Sub


    Public Sub New(_conum As Decimal)
        InitializeComponent()
        Me.Conum = _conum
    End Sub


    Private Sub frmRunUtilityImport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        lcErrorText.Text = String.Empty
        If Conum <= 0 Then
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                slueCompany.Properties.DataSource = ctxDB.view_CompanySumarries.ToList
            End Using
        Else
            lciCoNumSearch.Visibility = False.ToBarItemVisibility
            LoadLastPayroll()
        End If
    End Sub

    Private Sub slueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles slueCompany.EditValueChanged
        Decimal.TryParse(nz(slueCompany.EditValue, ""), Conum)
        If Conum <= 0 Then Exit Sub
        LoadLastPayroll()
    End Sub

    Private Sub LoadLastPayroll()
        Try
            LayoutControl1.ShowProgessPanel

            btnProcess.Enabled = False
            txtPrNum.Text = String.Empty
            txtPrStatus.Text = String.Empty
            gcChecks.DataSource = Nothing
            lcErrorText.Text = String.Empty

            If Conum <= 0 Then Exit Sub
            Using db As New dbEPDataDataContext(GetConnectionString)
                LastPayroll = db.PAYROLLs.Where(Function(c) c.CONUM = Conum).OrderByDescending(Function(p) p.PRNUM).FirstOrDefault
                If LastPayroll Is Nothing Then
                    lcErrorText.Text = "Company has no payrolls yet."
                    Exit Sub
                End If
                txtPrNum.Text = LastPayroll?.PRNUM
                txtPrStatus.Text = LastPayroll?.PAYROLL_STATUS

                gcChecks.DataSource = db.CHK_MASTs.Where(Function(c) c.CONUM = Conum AndAlso c.PAYROLL_NUM = LastPayroll.PRNUM).ToList
                gvChecks.BestFitColumns()

                Dim _coOptionsPayroll = (From _cop In db.CoOptions_Payrolls Where _cop.CoNum = _Conum).FirstOrDefault
                If _coOptionsPayroll Is Nothing OrElse _coOptionsPayroll.Run_prc_PrUtilityImport.IsNullOrWhiteSpace Then
                    lcErrorText.Text = "Company isn't setup with a utility."
                    Exit Sub
                End If
            End Using


            If LastPayroll.PAYROLL_STATUS <> "Entering Checks" Then
                lcErrorText.Text = "Pr#: {0} status is: {1} {2}the RunUtilityImpot can only be run when Payroll Status is Entering Checks".FormatWith(LastPayroll.PRNUM, LastPayroll.PAYROLL_STATUS, vbCrLf)
                Exit Sub
            End If
            btnProcess.Enabled = True
        Catch ex As Exception
            DisplayErrorMessage("Error loading Payroll.", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Sub btnProcess_Click(sender As Object, e As EventArgs) Handles btnProcess.Click
        If AddPrInProcessIfNotExist() Then
            Try
                LayoutControl1.ShowProgessPanel
                Application.DoEvents()
                Dim obj As New PayrollUtilityImport(_Conum)
                obj.ProcessImport(LastPayroll.PRNUM, True)
            Catch ex As Exception
                DisplayErrorMessage("Error while processing", ex)
            Finally
                LayoutControl1.HideProgressPanel
            End Try
        End If
    End Sub

    Public Function AddPrInProcessIfNotExist() As Boolean
        Try
            Logger.Debug("Entering AddPrInProcessIfNotExist")
            Dim db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
            Dim PayRec As view_pr_batch_in_process = Nothing
            Try
                PayRec = (From A In db.view_pr_batch_in_processes Where A.CoNum = Conum AndAlso A.PrNum = LastPayroll.PRNUM AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
            Catch ex As InvalidCastException
                Logger.Error(ex, "Type mismatch in view_pr_batch_in_processes query")
                Return False
            End Try

            If PayRec Is Nothing Then
                PayRec = New view_pr_batch_in_process With {.CoNum = _Conum,
                                                   .PrNum = LastPayroll.PRNUM,
                                                   .rowguid = Guid.NewGuid,
                                                   .ProcessedDate = LastPayroll.RUN_DATE,
                                                   .ProcessedBy = LastPayroll.OPNAME,
                                                   .ProcessStatus = "Ready"}
                db.view_pr_batch_in_processes.InsertOnSubmit(PayRec)
                Return db.SaveChanges()
            End If
            Return True
        Catch ex As Exception
            Logger.Error(ex, "Error in AddPrInProcessIfNotExist")
            DisplayErrorMessage("Error in AddPrInProcessIfNotExist", ex)
            Return False
        End Try
    End Function
End Class