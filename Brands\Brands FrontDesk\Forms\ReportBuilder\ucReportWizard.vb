﻿Imports System.ComponentModel
Imports Brands_FrontDesk
Imports DevExpress.Skins
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports Microsoft.EntityFrameworkCore
Public Class ucReportWizard
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property DefaultSelectedConum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property ConnectionString As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property UserName As String
    Private Property userLookAndFeel As DevExpress.LookAndFeel.UserLookAndFeel
    Private Property IsRunningFromPPImports As Boolean
    Public Delegate Sub OpenForm(frm As XtraForm)
    Private _OpenForm As Action(Of XtraForm)

    Private Property SavedReports As List(Of CustomReportLayout)
    Private Property SharedReports As List(Of CustomReportLayout)
    Private Property AdminReports As List(Of CustomReportLayout)

    Sub New()
        InitializeComponent()
    End Sub

    Sub Initialize(_userName As String, conString As String, action As Action(Of XtraForm), _userLookAndFeel As DevExpress.LookAndFeel.UserLookAndFeel, _IsRunningFromPPImports As Boolean)
        UserName = _userName
        IsRunningFromPPImports = _IsRunningFromPPImports
        ConnectionString = conString
        userLookAndFeel = _userLookAndFeel
        _OpenForm = action
    End Sub

    Private Function AllowReporting() As Boolean
        Using db = New dbEPDataDataContext(ConnectionString)
            db.ObjectTrackingEnabled = False
            If Not db.FrontDeskOptions.Single.AllowReporting Then
                Logger.Fatal("Reporting has bean diabled in settings table")
                Return False
            End If
        End Using
        Return True
    End Function

    'Private Sub frmReportWizard_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
    '    If Not AllowReporting() Then
    '        DisplayMessageBox("Reporting is currently unavailable.")
    '        'Close()
    '        Return
    '    End If
    '    Dim currentSkin As DevExpress.Skins.Skin = DevExpress.Skins.EditorsSkins.GetSkin(Me.LookAndFeel)
    '    AddHandler Me.LookAndFeel.StyleChanged, AddressOf frmReportWizard_Shown
    '    Dim element = currentSkin(DevExpress.Skins.EditorsSkins.SkinEditorButton)
    '    element.Image.Image = Nothing
    '    Me.LookAndFeel.UpdateStyleSettings()
    'End Sub

    Public SelectedReportLayout As CustomReportLayout

    Private Sub frmReportWizard_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If IsInDesignMode Then Exit Sub
        colCustomReport.BestFit()
        LoadDate()
        If SavedReports?.Any Then
            LoadReports(False)
        ElseIf SharedReports?.Any Then
            TileBar1.SelectedItem = tbiSharedReports
        Else
            TileBar1.SelectedItem = tbiPayrollReports
        End If
    End Sub

    Private Sub LoadDate()
        Try
            If Not AllowReporting() Then
                Return
            End If
            Using db = New dbEPDataDataContext(ConnectionString)
                db.ObjectTrackingEnabled = False
                If Not db.FrontDeskOptions.Single.AllowReporting Then
                    DisplayMessageBox("Reporting is currently unavailable.")
                    Logger.Fatal("Reporting has bean diabled in settings table")
                    'Close()
                    Throw New UnauthorizedAccessException()
                End If
                SavedReports = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Where(Function(r) r.UserName = UserName).ToList
                Dim sharedReportIds = db.CustomReportLayoutShares.Where(Function(u) u.username = UserName).Select(Function(r) r.ReportLayoutId).ToList
                SharedReports = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Where(Function(r) sharedReportIds.Contains(r.ID)).ToList
                AdminReports = db.CustomReportLayouts.Include(Function(x) x.CustomReport).Where(Function(r) r.IsAdminLayout).ToList

                tbiMySavedReports.Enabled = SavedReports.Any
                tbiSharedReports.Enabled = SharedReports.Any
            End Using
        Catch ex As Exception
            Logger.Fatal(ex, "Error loading reports data")
            DisplayMessageBox("Error loading reports")
        End Try
    End Sub

    Private Sub TileBar1_SelectedItemChanged(sender As Object, e As DevExpress.XtraEditors.TileItemEventArgs) Handles TileBar1.SelectedItemChanged
        LoadReports(False)
    End Sub

    Private Sub LoadReports(refreshData As Boolean)
        Try
            If refreshData Then
                LoadDate()
            End If

            TabPane1.SelectedPageIndex = 3
            TabPane1.SelectedPageIndex = 0
            colChgDate.Visible = False

            If TileBar1.SelectedItem Is tbiMySavedReports Then
                colEditRemove.Visible = True
                riEditRemove.Buttons(0).Visible = True
                riEditRemove.Buttons(1).Visible = True
                colChgDate.Visible = True
                GridControl1.DataSource = SavedReports
                GridControl1.Parent.Visible = True 'solomon added this line to ensure the grid is visible
                colCustomReport.BestFit()
            ElseIf TileBar1.SelectedItem Is tbiSharedReports Then
                colEditRemove.Visible = True
                riEditRemove.Buttons(0).Visible = False
                riEditRemove.Buttons(1).Visible = False
                GridControl1.DataSource = SharedReports
                colCustomReport.BestFit()
            ElseIf TileBar1.SelectedItem Is tbiPayrollReports Then
                LoadReports("Payroll")
            ElseIf TileBar1.SelectedItem Is tbiTimeAndLabor Then
                LoadReports("Time & Labor")
            ElseIf TileBar1.SelectedItem Is tbiEmployeeAndHR Then
                LoadReports("Employee & HR")
            End If
        Catch ex As Exception
            Logger.Fatal(ex, "Error loading reports")
            DisplayMessageBox("Error loading reports")
        End Try
    End Sub

    Private Sub LoadReports(cat As String)
        colEditRemove.Visible = False
        GridControl1.DataSource = AdminReports?.Where(Function(r) r.Category = cat AndAlso r.IsAdminLayout).ToList
        colCustomReport.BestFit()
    End Sub

    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles GridView1.DoubleClick
        Dim view As GridView = CType(sender, GridView)
        Try
            MainForm.ShowWaitForm
            Dim pt As Point = view.GridControl.PointToClient(Control.MousePosition)
            Dim info As GridHitInfo = view.CalcHitInfo(pt)
            If (info.InRow OrElse info.InRowCell) AndAlso info.RowHandle >= 0 Then
                SelectedReportLayout = view.GetRow(info.RowHandle)
                Dim frm1 = New frmReportBuilder(ConnectionString, SelectedReportLayout.CustomReportId, userLookAndFeel, IsRunningFromPPImports, SelectedReportLayout.ID) With {.DefaultSelectedConum = DefaultSelectedConum}
                _OpenForm(frm1)
                'MainForm.ShowForm(frm1)
                'Close()
            End If
        Catch ex As Exception
            MainForm.CloseWaitForm
        Finally
            MainForm.CloseWaitForm
        End Try
    End Sub

    Private Sub RepositoryItemHyperLinkEdit1_Click(sender As Object, e As EventArgs) Handles riHleReportName.Click
        GridView1_DoubleClick(GridView1, Nothing)
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If TileBar1.SelectedItem Is tbiMySavedReports Then
            Dim row As CustomReportLayout = GridView1.GetRow(e.HitInfo.RowHandle)
            If row IsNot Nothing Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteSavedReport(row), My.Resources.delete_16x16))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit", Sub() EditSavedReport(row), My.Resources.edit_16x16))
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Refresh", Sub()
                                                                                     LoadReports(True)
                                                                                 End Sub, My.Resources.refresh2_16x16))
            End If
        End If
    End Sub

    Private Sub EditSavedReport(row As CustomReportLayout)
        Dim frm = New frmReportBuilder(ConnectionString(), row.CustomReportId, userLookAndFeel, IsRunningFromPPImports, row.ID)
        frm.InitializeForm()
        frm.SaveUserLayout(Me.ParentForm, row.ID, False)
        LoadReports(True)
    End Sub

    Private Sub DeleteSavedReport(row As CustomReportLayout)
        Try
            Using db = New dbEPDataDataContext(ConnectionString)
                If TileBar1.SelectedItem Is tbiMySavedReports Then
                    Dim message = "Are you sure you would like to delete this saved report?"
                    Dim shares = db.CustomReportLayoutShares.Where(Function(s) s.ReportLayoutId = row.ID).ToList
                    If shares.Any Then
                        message &= $"{vbCrLf}Please Note, You have shared this report with {shares.Count} user(s).{vbCrLf}{String.Join(vbCrLf, shares.Select(Function(u) u.username))}{vbCrLf}"
                        message &= "If you delete this report, it will also be deleted from all other users."
                    End If
                    If XtraMessageBox.Show(message, "Delete?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                        db.CustomReportLayouts.Attach(row)
                        db.CustomReportLayouts.DeleteOnSubmit(row)
                        db.CustomReportLayoutShares.DeleteAllOnSubmit(shares)
                        db.SaveChanges()
                        LoadReports(True)
                    End If
                ElseIf TileBar1.SelectedItem Is tbiSharedReports Then
                    If XtraMessageBox.Show("Are you sure you would like to delete this shared report?", "Delete?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                        Dim share = db.CustomReportLayoutShares.Single(Function(s) s.username = UserName AndAlso s.ReportLayoutId = row.ID)
                        db.CustomReportLayoutShares.DeleteOnSubmit(share)
                        db.SaveChanges()
                        LoadReports(True)
                    End If
                Else
                    Throw New Exception("You do not have permission to delete me!!!!")
                End If
            End Using
        Catch ex As Exception
            Logger.Error(ex, "Error deleting report")
            DisplayMessageBox("Error deleting report")
        End Try
    End Sub

    Private Sub riEditRemove_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles riEditRemove.ButtonClick
        Dim row As CustomReportLayout = GridView1.GetFocusedRow()
        If row Is Nothing Then Exit Sub
        If e.Button.ToolTip = "Delete" Then
            DeleteSavedReport(row)
        ElseIf e.Button.ToolTip = "Edit" Then
            EditSavedReport(row)
        ElseIf e.Button.ToolTip = "Share" Then
            Dim frm = New frmShareReport(row.ID)
            frm.ShowDialog()
        End If
    End Sub
End Class