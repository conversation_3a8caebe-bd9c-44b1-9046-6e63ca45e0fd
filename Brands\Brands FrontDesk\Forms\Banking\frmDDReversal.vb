﻿Imports System.Data
Imports Brands_FrontDesk
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraBars.Navigation
Imports DevExpress.XtraEditors

Public Class frmDDReversal

    Private Property db As dbEPDataDataContext
    Private Property SelectedQueue As DD_Reversal_Log_Queue

    Sub New()
        InitializeComponent()
        GridView1.SetGridLayoutAndAddMenues("DD Reversal")
    End Sub

    Private Sub frmDDReversal_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
        bbiProcessTransactions.Visibility = (Permissions.DDReversalLevel = "SuperAdmin").ToBarItemVisibility
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            aceQueus.Elements.Clear()
            For Each queue In db.DD_Reversal_Log_Queues.OrderBy(Function(q) q.SortValue)
                If queue.VisibleToLevel.IsNotNullOrWhiteSpace AndAlso queue.VisibleToLevel.Split(",").Contains(Permissions.DDReversalLevel) Then
                    aceQueus.Elements.Add(New AccordionControlElement(ElementStyle.Item) With {.Text = queue.QueueName, .Tag = queue})
                End If
            Next
            AccordionControl1.SelectedElement = aceQueus.Elements(0)
            LoadCount()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub LoadCount()
        Try
            For Each item In aceQueus.Elements
                Dim queue As DD_Reversal_Log_Queue = item.Tag
                Dim filter = queue.StatusList.Split(",")
                item.Text = $"{queue.QueueName} ({db.DD_Reversal_Logs.Count(Function(d) filter.Contains(d.Reversal_Status))})"
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error loading counts", ex)
        End Try
    End Sub

    Private Async Sub bbiProcessTransactions_ItemClickAsync(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiProcessTransactions.ItemClick
        Try
            Dim a = New SqlClient.SqlParameter() {New SqlClient.SqlParameter("@ExecType", "Draft From EEs")}
            Await ExecuteStoredProcedureAsync("[custom].[DD_REVERSAL_TRANSACTIONS]", a)

            Dim b = New SqlClient.SqlParameter() {New SqlClient.SqlParameter("@ExecType", "Refund To Client")}
            Await ExecuteStoredProcedureAsync("[custom].[DD_REVERSAL_TRANSACTIONS]", b)

            Dim c = New SqlClient.SqlParameter() {New SqlClient.SqlParameter("@ExecType", "Process Manual Billing")}
            Await ExecuteStoredProcedureAsync("[custom].[DD_REVERSAL_TRANSACTIONS]", c)

            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                For Each row In ctxDB.DD_Reversal_Emails.Where(Function(eml) Not eml.IsDone)
                    If SendEmail(row.CONUM, row.DD_Reversal_Id, row.EmailTemplate, False) Then
                        row.IsDone = True
                        row.SentDate = Now
                        ctxDB.SaveChanges()
                    End If
                Next
            End Using

            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error executing sp DD_REVERSAL_TRANSACTIONS", ex)
        End Try
    End Sub

    Private Sub AccordionControl1_SelectedElementChanged(sender As Object, e As SelectedElementChangedEventArgs) Handles AccordionControl1.SelectedElementChanged
        SelectedQueue = e.Element.Tag
        LoadSelectedQueueData()
    End Sub

    Private Sub LoadSelectedQueueData()
        Try
            btnChangeStatus.Enabled = Permissions.DDReversalLevel = "SuperAdmin" OrElse ({"CS", "Admin"}.Contains(Permissions.DDReversalLevel) AndAlso SelectedQueue.CS_Changes_Allowed.GetValueOrDefault)
            Dim filter = SelectedQueue.StatusList.Split(",")
            db = New dbEPDataDataContext(GetConnectionString)
            dD_Reversal_LogsBindingSource.DataSource = db.DD_Reversal_Logs.Where(Function(d) filter.Contains(d.Reversal_Status)).ToList
        Catch ex As Exception
            DisplayErrorMessage("Error loading data.", ex)
        End Try
    End Sub

    Private Sub dD_Reversal_LogsBindingSource_CurrentChanged(sender As Object, e As EventArgs) Handles dD_Reversal_LogsBindingSource.CurrentChanged
        Dim row As DD_Reversal_Log = dD_Reversal_LogsBindingSource.Current
        If row Is Nothing Then
            teConum.Text = String.Empty
            teEmpNum.Text = String.Empty
            lciPrNum.Text = String.Empty
            lcgDetails.Text = "Transaction Details"
            Exit Sub
        End If
        teConum.Text = db.view_CompanySumarries.Single(Function(c) c.CONUM = row.CONUM).CoNumAndName
        teEmpNum.Text = $"{row.EMPNUM} - {db.EMPLOYEEs.Single(Function(emp) emp.CONUM = row.CONUM AndAlso emp.EMPNUM = row.EMPNUM).FullName}"
        lciPrNum.Text = $"{row.Payroll_num} - {db.PAYROLLs.Single(Function(p) p.CONUM = row.CONUM AndAlso p.PRNUM = row.Payroll_num).CHECK_DATE}"
        lcgDetails.Text = $"Transaction Details - {row.ID}"
    End Sub

    Private Sub btnChangeStatus_Click(sender As Object, e As EventArgs) Handles btnChangeStatus.Click
        Try
            Dim row As DD_Reversal_Log = dD_Reversal_LogsBindingSource.Current
            Using frm = New frmDDReversalStatusUpdate(row)
                If SelectedQueue.ChangeToValues.IsNotNullOrWhiteSpace Then
                    For Each item In SelectedQueue.ChangeToValues?.Split(",")
                        frm.cbeStatus.Properties.Items.Add(item)
                    Next
                End If

                frm.cbeStatus.EditValue = row.Reversal_Status
                frm.meNotes.EditValue = row.Banking_Dept_Notes
                If frm.ShowDialog = DialogResult.OK Then
                    db.SaveChanges
                    AlertToHandleMultipleTrans(row)
                    LoadSelectedQueueData()
                    LoadCount()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error changing status", ex)
        End Try
    End Sub

    Private Sub AlertToHandleMultipleTrans(row As DD_Reversal_Log)
        Try
            Dim checks = db.DD_Reversal_Logs.Where(Function(d) d.CONUM = row.CONUM AndAlso d.CHK_COUNTER = row.CHK_COUNTER AndAlso d.Payroll_num = row.Payroll_num AndAlso d.EMPNUM = row.EMPNUM AndAlso d.ID <> row.ID)
            checks = checks.Where(Function(d) GetUdfValueSplitted("DD Reversal Status Change Save Alert").Contains(d.Reversal_Status))
            If checks.Any() Then
                XtraMessageBox.Show($"This check exist {checks.Count} more time(s) in the reversal log table.{vbCrLf}Please ensure to update the status of the other transaction if necessary")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error getting alerting for splitted transactions", ex)
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        Try
            Dim row As DD_Reversal_Log = GridView1.GetRow(e.HitInfo.RowHandle)
            If row IsNot Nothing Then
                Dim menu = New DXSubMenuItem("Email Client")
                For Each item In db.ReportEmailTeplates.Where(Function(t) t.Tag = "DD Reversal")
                    menu.Items.Add(New DXMenuItem(item.Name, Sub() SendEmail(row.CONUM, row.ID, item.Name, True)))
                Next
                e.Menu.Items.Add(menu)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in show popup", ex)
        End Try
    End Sub

    Public Function SendEmail(conum As Decimal, DD_Reversal_LogId As Integer, name As String, showEmailForm As Boolean) As Boolean
        Try
            Logger.Debug("Entering SendEmail for DD_Reversal_Log ID: {ID} {EmailTemplate}", DD_Reversal_LogId, name)
            Dim processor = New ReportProcessor(conum, name, FileType.Pdf) With {.showInRecentReports = False, .showParametersForm = False, .DefaultParamValues = New List(Of KeyValuePair)()}
            processor.DefaultParamValues.Add(New KeyValuePair("@ID", DD_Reversal_LogId))

            Dim result = processor.ProcessReport()
            Dim reportSender = New ReportSender(result) With {.showEmailList = False, .showWebPost = False}
            If reportSender.EmailReport(showEmailForm) Then
                If showEmailForm Then XtraMessageBox.Show($"Email [{name}] sent to client.")
                Return True
            End If
        Catch ex As Exception
            Logger.Error(ex, "Entering Sending email for DD_Reversal_Log ID: {ID}", DD_Reversal_LogId)
            DisplayErrorMessage($"Entering Sending email for DD_Reversal_Log ID: {DD_Reversal_LogId}", ex)
        End Try
        Return False
    End Function
End Class