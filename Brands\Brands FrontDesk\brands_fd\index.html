<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link name="favicon" rel="icon" href="./favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title loc-text="defaultReportTitle">Upgrade Analysis Report</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/style.css">
</head>

<body>

    <nav class="navbar navbar-dark bg-dark p-3" aria-label="Top title panel" loc-aria-label="TopTitlePanel">
        <nav class="d-flex col-12 col-md-3 col-lg-2 mb-2 mb-lg-0 flex-wrap flex-md-nowrap">
            <a id="route-home" class="navbar-brand" href="#">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="36px" height="36px"><linearGradient id="k8yl7~hDat~FaoWq8WjN6a" x1="-1254.397" x2="-1261.911" y1="877.268" y2="899.466" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#114a8b" /><stop offset="1" stop-color="#0669bc" /></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6a)" d="M17.634,6h11.305L17.203,40.773c-0.247,0.733-0.934,1.226-1.708,1.226H6.697 c-0.994,0-1.8-0.806-1.8-1.8c0-0.196,0.032-0.39,0.094-0.576L15.926,7.227C16.173,6.494,16.86,6,17.634,6L17.634,6z" /><path fill="#0078d4" d="M34.062,29.324H16.135c-0.458-0.001-0.83,0.371-0.831,0.829c0,0.231,0.095,0.451,0.264,0.608 l11.52,10.752C27.423,41.826,27.865,42,28.324,42h10.151L34.062,29.324z" /><linearGradient id="k8yl7~hDat~FaoWq8WjN6b" x1="-1252.05" x2="-1253.788" y1="887.612" y2="888.2" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-opacity=".3" /><stop offset=".071" stop-opacity=".2" /><stop offset=".321" stop-opacity=".1" /><stop offset=".623" stop-opacity=".05" /><stop offset="1" stop-opacity="0" /></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6b)" d="M17.634,6c-0.783-0.003-1.476,0.504-1.712,1.25L5.005,39.595 c-0.335,0.934,0.151,1.964,1.085,2.299C6.286,41.964,6.493,42,6.702,42h9.026c0.684-0.122,1.25-0.603,1.481-1.259l2.177-6.416 l7.776,7.253c0.326,0.27,0.735,0.419,1.158,0.422h10.114l-4.436-12.676l-12.931,0.003L28.98,6H17.634z" /><linearGradient id="k8yl7~hDat~FaoWq8WjN6c" x1="-1252.952" x2="-1244.704" y1="876.6" y2="898.575" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#3ccbf4" /><stop offset="1" stop-color="#2892df" /></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6c)" d="M32.074,7.225C31.827,6.493,31.141,6,30.368,6h-12.6c0.772,0,1.459,0.493,1.705,1.224 l10.935,32.399c0.318,0.942-0.188,1.963-1.13,2.281C29.093,41.968,28.899,42,28.703,42h12.6c0.994,0,1.8-0.806,1.8-1.801 c0-0.196-0.032-0.39-0.095-0.575L32.074,7.225z" /></svg>
                <span loc-text="defaultReportTitle">Upgrade Analysis Report</span>
            </a>
        </nav>

        <nav class="navbar-right" aria-label="Top additional information panel" loc-aria-label="TopAdditionalInfoPanel">
            <small class="text-light">
                <span loc-text="generatedBy">Generated by </span>
                <a id="tool-name" class="text-light" style="font-style: italic;" href="https://go.microsoft.com/fwlink/?LinkID=2207921">.NET Upgrade Assistant</a>
            </small>
        </nav>
    </nav>

    <nav id="sidebar" class="bg-dark sidebar" aria-label="Left navigation panel" loc-aria-label="LeftNavigationPanel">
        <div class="pt-md-5">
            <button id="sidebar-toggle" class="sidebar-toggle sidebar-aligned-right text-white" type="button" loc-title="ShowLessInformation" title="Show less information">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevrons-left">
                    <polyline points="11 17 6 12 11 7"></polyline>
                    <polyline points="18 17 13 12 18 7"></polyline>
                </svg>

                <svg class="hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevrons-right">
                    <polyline points="13 17 18 12 13 7"></polyline>
                    <polyline points="6 17 11 12 6 7"></polyline>
                </svg>
            </button>

            <ul class="nav flex-column sidebar-menu" role="tablist">
                <li class="nav-item">
                    <a id="route-dashboard" class="nav-link nav-link-aligned text-light" href="#" loc-title="Dashboard" title="Dashboard" role="tab" aria-selected="true" global-tab="dashboard">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather feather-pie-chart">
                            <path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path>
                            <path d="M22 12A10 10 0 0 0 12 2v10z"></path>
                        </svg>
                        <span class="ml-2 hidden-in-small-sidebar" loc-text="Dashboard">Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a id="route-projects" class="nav-link nav-link-aligned text-light" href="#" loc-title="Projects" title="Projects" role="tab" aria-selected="false" global-tab="projects">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span id="ReportEntries" class="ml-2 hidden-in-small-sidebar" loc-text="Projects">Projects</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a id="route-issues" class="nav-link nav-link-aligned text-light" href="#" loc-title="AggregateIssues" title="Aggregate Issues" role="tab" aria-selected="false" global-tab="issues">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather feather-alert-triangle">
                            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                            <line x1="12" y1="9" x2="12" y2="13"></line>
                            <line x1="12" y1="17" x2="12.01" y2="17"></line>
                        </svg>
                        <span class="ml-2 hidden-in-small-sidebar" loc-text="AggregateIssues">Aggregate Issues</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a id="route-help" class="nav-link nav-link-aligned text-light" href="#" loc-title="help" title="Help" role="tab" aria-selected="false" global-tab="help">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather feather-help-circle">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                            <line x1="12" y1="17" x2="12.01" y2="17"></line>
                        </svg>
                        <span class="ml-2 hidden-in-small-sidebar" loc-text="Help">Help</span>
                    </a>
                    <ul class="nav flex-column text-small hidden-in-small-sidebar" role="tablist">
                        <li class="nav-item">
                            <a id="route-help-issues" class="nav-link nav-link-aligned sidebar-submenu-item text-light" href="#" loc-title="Issues" title="Issues" role="tab" aria-selected="false" global-tab="help-issues">
                                <span class="ml-2" loc-text="Issues">Issues</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a id="route-help-incidents" class="nav-link nav-link-aligned sidebar-submenu-item text-light" href="#" loc-title="Incidents" title="Incidents" role="tab" aria-selected="false" global-tab="help-incidents">
                                <span class="ml-2" loc-text="Incidents">Incidents</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a id="route-help-severity" class="nav-link nav-link-aligned sidebar-submenu-item text-light" href="#" loc-title="IssueSeverity" title="Issue Severity" role="tab" aria-selected="false" global-tab="help-severity">
                                <span class="ml-2" loc-text="IssueSeverity">Issue Severity</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a id="route-help-storyPoints" class="nav-link nav-link-aligned sidebar-submenu-item text-light" href="#" loc-title="WhatAreStoryPoints" title="What are Story Points?" role="tab" aria-selected="false" global-tab="help-storyPoints">
                                <span class="ml-2" loc-text="WhatAreStoryPoints">What are Story Points?</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link nav-link-aligned text-light" href="https://go.microsoft.com/fwlink/?linkid=2251644" loc-title="feedback" title="Feedback">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather feather-smile">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <span class="ml-2 hidden-in-small-sidebar" loc-text="feedback">Feedback</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <div id="main" class="container-fluid">
        <div class="row">
            <main class="col-md-12 ml-sm-auto col-lg-12 px-md-4 py-4">

                <!-- Breadcrumbs -->
                <nav aria-label="breadcrumb" class="text-small">
                    <div style="display: inline-block">
                        <ol id="breadcrumbs-container" class="breadcrumb purple lighten-4">
                        </ol>
                    </div>
                    <div id="privacymode-container" style="float: right;">
                    </div>
                </nav>

                <div class="title-panel mb-4">
                    <h1 id="page-title" style="display: inline; margin-right: 5px;"></h1>
                    <span id="page-subtitle" class="text-muted align-center"></span>
                </div>

                <div id="content-container">
                </div>

            </main>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.5.1/chart.min.js"></script>
    <script src="data/results.json"></script>
    <script src="data/strings.json"></script>
    <script src="static/js/shared.js" defer></script>
    <script src="static/js/help.incidents.js" defer></script>
    <script src="static/js/help.issues.js" defer></script>
    <script src="static/js/help.severity.js" defer></script>
    <script src="static/js/help.storyPoints.js" defer></script>
    <script src="static/js/help.js" defer></script>
    <script src="static/js/dashboard.js" defer></script>
    <script src="static/js/project.dashboard.js" defer></script>
    <script src="static/js/project.components.js" defer></script>
    <script src="static/js/project.issues.js" defer></script>
    <script src="static/js/project.js" defer></script>
    <script src="static/js/projects.js" defer></script>
    <script src="static/js/issues.js" defer></script>
    <script src="static/js/pages.js" defer></script>
    <script src="static/js/index.js" defer></script>
</body>

</html>
