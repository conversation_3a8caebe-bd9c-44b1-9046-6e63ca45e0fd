﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucCompInfo
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ucCompInfo))
        Dim EditorButtonImageOptions3 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject21 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject22 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject23 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject24 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions4 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject25 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject26 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject27 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject28 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions1 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject2 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject3 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject4 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject5 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject6 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject7 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject8 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject9 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject10 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject11 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject12 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject13 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject14 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject15 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject16 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Me.bsCompInfo = New System.Windows.Forms.BindingSource(Me.components)
        Me.RibbonControl2 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.BarButtonItem6 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem7 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem8 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem9 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem10 = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage2 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup2 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.BarButtonItem1 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem2 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem3 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem4 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem5 = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.lcRoot = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.hllcCancel = New DevExpress.XtraEditors.HyperlinkLabelControl()
        Me.hllcUpdate = New DevExpress.XtraEditors.HyperlinkLabelControl()
        Me.hllcEdit = New DevExpress.XtraEditors.HyperlinkLabelControl()
        Me.cbeShStats = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.teShExt = New DevExpress.XtraEditors.TextEdit()
        Me.teShZip = New DevExpress.XtraEditors.TextEdit()
        Me.teShCity = New DevExpress.XtraEditors.TextEdit()
        Me.teShStreet = New DevExpress.XtraEditors.TextEdit()
        Me.teShName = New DevExpress.XtraEditors.TextEdit()
        Me.ceShSameAsCompany = New DevExpress.XtraEditors.CheckEdit()
        Me.teCoCounty = New DevExpress.XtraEditors.TextEdit()
        Me.teCoZip = New DevExpress.XtraEditors.TextEdit()
        Me.cbeStateGeneral = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.teCoCity = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.teTaxService = New DevExpress.XtraEditors.TextEdit()
        Me.teShipAddress3 = New DevExpress.XtraEditors.TextEdit()
        Me.teShipAddress1 = New DevExpress.XtraEditors.TextEdit()
        Me.teShipAddress2 = New DevExpress.XtraEditors.TextEdit()
        Me.teEmpsCount = New DevExpress.XtraEditors.TextEdit()
        Me.CO_DBATextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CO_NAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CO_STATUSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CONUMSpinEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PR_CONTACTTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CO_EXTENSIONTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PR_PASSWORDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CO_EMAILTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DELVDESCTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CO_PHONETextEdit = New DevExpress.XtraEditors.ButtonEdit()
        Me.CO_MODEMTextEdit = New DevExpress.XtraEditors.ButtonEdit()
        Me.OP_OWNERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CO_FAXTextEdit = New DevExpress.XtraEditors.ButtonEdit()
        Me.teShPhone = New DevExpress.XtraEditors.ButtonEdit()
        Me.teShFax = New DevExpress.XtraEditors.ButtonEdit()
        Me.teShModem = New DevExpress.XtraEditors.ButtonEdit()
        Me.teClosedStatus = New DevExpress.XtraEditors.TextEdit()
        Me.teDelvDesc1 = New DevExpress.XtraEditors.TextEdit()
        Me.teDelvDesc2 = New DevExpress.XtraEditors.TextEdit()
        Me.teGroup = New DevExpress.XtraEditors.TextEdit()
        Me.teNsfClearedDate = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.lcgCompanyInfo = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForCONUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_NAME = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_DBA = New DevExpress.XtraLayout.LayoutControlItem()
        Me.label1099Company = New DevExpress.XtraLayout.SimpleLabelItem()
        Me.ItemForOP_OWNER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciInactiveStatus = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_STATUS = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lblAchRisk = New DevExpress.XtraLayout.SimpleLabelItem()
        Me.lciGroup = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForPR_CONTACT = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_PHONE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_FAX = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_EMAIL = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForPR_PASSWORD = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_EXTENSION = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCO_MODEM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
        Me.lcgShippingGeneral = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgGeneralShipping = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForDELVDESC = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lcgShipping = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.EmptySpaceItem4 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem24 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem5 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.ItemForSh_Ph = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForSh_Fax = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForSh_Modem = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciHyperLinkUpdate = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciHyperLinkEdit = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lciHyperLinkCancel = New DevExpress.XtraLayout.LayoutControlItem()
        Me.AccountManagerTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.bsCompInfo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.lcRoot.SuspendLayout()
        CType(Me.cbeShStats.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShExt.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShZip.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShCity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShStreet.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ceShSameAsCompany.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCoCounty.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCoZip.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStateGeneral.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCoCity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teTaxService.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShipAddress3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShipAddress1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShipAddress2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teEmpsCount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_DBATextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CONUMSpinEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PR_CONTACTTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_EXTENSIONTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PR_PASSWORDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_EMAILTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DELVDESCTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_PHONETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_MODEMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OP_OWNERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CO_FAXTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShPhone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShFax.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShModem.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teClosedStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teDelvDesc1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teDelvDesc2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teGroup.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teNsfClearedDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgCompanyInfo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCONUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_NAME, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_DBA, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.label1099Company, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForOP_OWNER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciInactiveStatus, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_STATUS, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblAchRisk, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciGroup, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPR_CONTACT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_PHONE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_FAX, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_EMAIL, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPR_PASSWORD, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_EXTENSION, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCO_MODEM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgShippingGeneral, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgGeneralShipping, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDELVDESC, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgShipping, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForSh_Ph, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForSh_Fax, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForSh_Modem, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciHyperLinkUpdate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciHyperLinkEdit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciHyperLinkCancel, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AccountManagerTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'bsCompInfo
        '
        Me.bsCompInfo.DataSource = GetType(Brands_FrontDesk.COMPANY)
        '
        'RibbonControl2
        '
        Me.RibbonControl2.ExpandCollapseItem.Id = 0
        Me.RibbonControl2.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl2.ExpandCollapseItem, Me.RibbonControl2.SearchEditItem, Me.BarButtonItem6, Me.BarButtonItem7, Me.BarButtonItem8, Me.BarButtonItem9, Me.BarButtonItem10})
        Me.RibbonControl2.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl2.MaxItemId = 7
        Me.RibbonControl2.Name = "RibbonControl2"
        Me.RibbonControl2.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage2})
        Me.RibbonControl2.Size = New System.Drawing.Size(1256, 0)
        '
        'BarButtonItem6
        '
        Me.BarButtonItem6.Caption = "New Payroll"
        Me.BarButtonItem6.Id = 1
        Me.BarButtonItem6.Name = "BarButtonItem6"
        '
        'BarButtonItem7
        '
        Me.BarButtonItem7.Caption = "Co Options"
        Me.BarButtonItem7.Id = 2
        Me.BarButtonItem7.ImageOptions.Image = CType(resources.GetObject("BarButtonItem7.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem7.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem7.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem7.Name = "BarButtonItem7"
        '
        'BarButtonItem8
        '
        Me.BarButtonItem8.Caption = "Employee"
        Me.BarButtonItem8.Id = 3
        Me.BarButtonItem8.ImageOptions.Image = CType(resources.GetObject("BarButtonItem8.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem8.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem8.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem8.Name = "BarButtonItem8"
        '
        'BarButtonItem9
        '
        Me.BarButtonItem9.Caption = "Calendar"
        Me.BarButtonItem9.Id = 4
        Me.BarButtonItem9.ImageOptions.Image = CType(resources.GetObject("BarButtonItem9.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem9.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem9.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem9.Name = "BarButtonItem9"
        '
        'BarButtonItem10
        '
        Me.BarButtonItem10.Caption = "Minimum Wage"
        Me.BarButtonItem10.Id = 5
        Me.BarButtonItem10.ImageOptions.Image = CType(resources.GetObject("BarButtonItem10.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem10.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem10.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem10.Name = "BarButtonItem10"
        '
        'RibbonPage2
        '
        Me.RibbonPage2.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup2})
        Me.RibbonPage2.Name = "RibbonPage2"
        Me.RibbonPage2.Text = "Home"
        '
        'RibbonPageGroup2
        '
        Me.RibbonPageGroup2.ItemLinks.Add(Me.BarButtonItem6)
        Me.RibbonPageGroup2.ItemLinks.Add(Me.BarButtonItem7)
        Me.RibbonPageGroup2.ItemLinks.Add(Me.BarButtonItem8)
        Me.RibbonPageGroup2.ItemLinks.Add(Me.BarButtonItem9)
        Me.RibbonPageGroup2.ItemLinks.Add(Me.BarButtonItem10)
        Me.RibbonPageGroup2.Name = "RibbonPageGroup2"
        Me.RibbonPageGroup2.Text = "Company"
        '
        'RibbonControl1
        '
        Me.RibbonControl1.ExpandCollapseItem.Id = 0
        Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.RibbonControl1.SearchEditItem, Me.BarButtonItem1, Me.BarButtonItem2, Me.BarButtonItem3, Me.BarButtonItem4, Me.BarButtonItem5})
        Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl1.MaxItemId = 7
        Me.RibbonControl1.Name = "RibbonControl1"
        Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
        Me.RibbonControl1.Size = New System.Drawing.Size(1256, 0)
        '
        'BarButtonItem1
        '
        Me.BarButtonItem1.Caption = "New Payroll"
        Me.BarButtonItem1.Id = 1
        Me.BarButtonItem1.Name = "BarButtonItem1"
        '
        'BarButtonItem2
        '
        Me.BarButtonItem2.Caption = "Co Options"
        Me.BarButtonItem2.Id = 2
        Me.BarButtonItem2.ImageOptions.Image = CType(resources.GetObject("BarButtonItem2.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem2.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem2.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem2.Name = "BarButtonItem2"
        '
        'BarButtonItem3
        '
        Me.BarButtonItem3.Caption = "Employee"
        Me.BarButtonItem3.Id = 3
        Me.BarButtonItem3.ImageOptions.Image = CType(resources.GetObject("BarButtonItem3.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem3.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem3.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem3.Name = "BarButtonItem3"
        '
        'BarButtonItem4
        '
        Me.BarButtonItem4.Caption = "Calendar"
        Me.BarButtonItem4.Id = 4
        Me.BarButtonItem4.ImageOptions.Image = CType(resources.GetObject("BarButtonItem4.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem4.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem4.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem4.Name = "BarButtonItem4"
        '
        'BarButtonItem5
        '
        Me.BarButtonItem5.Caption = "Minimum Wage"
        Me.BarButtonItem5.Id = 5
        Me.BarButtonItem5.ImageOptions.Image = CType(resources.GetObject("BarButtonItem5.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem5.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem5.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem5.Name = "BarButtonItem5"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.ItemLinks.Add(Me.BarButtonItem1)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.BarButtonItem2)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.BarButtonItem3)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.BarButtonItem4)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.BarButtonItem5)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.Text = "Company"
        '
        'lcRoot
        '
        Me.lcRoot.Controls.Add(Me.AccountManagerTextEdit)
        Me.lcRoot.Controls.Add(Me.hllcCancel)
        Me.lcRoot.Controls.Add(Me.hllcUpdate)
        Me.lcRoot.Controls.Add(Me.hllcEdit)
        Me.lcRoot.Controls.Add(Me.cbeShStats)
        Me.lcRoot.Controls.Add(Me.teShExt)
        Me.lcRoot.Controls.Add(Me.teShZip)
        Me.lcRoot.Controls.Add(Me.teShCity)
        Me.lcRoot.Controls.Add(Me.teShStreet)
        Me.lcRoot.Controls.Add(Me.teShName)
        Me.lcRoot.Controls.Add(Me.ceShSameAsCompany)
        Me.lcRoot.Controls.Add(Me.teCoCounty)
        Me.lcRoot.Controls.Add(Me.teCoZip)
        Me.lcRoot.Controls.Add(Me.cbeStateGeneral)
        Me.lcRoot.Controls.Add(Me.teCoCity)
        Me.lcRoot.Controls.Add(Me.TextEdit2)
        Me.lcRoot.Controls.Add(Me.TextEdit1)
        Me.lcRoot.Controls.Add(Me.teTaxService)
        Me.lcRoot.Controls.Add(Me.teShipAddress3)
        Me.lcRoot.Controls.Add(Me.teShipAddress1)
        Me.lcRoot.Controls.Add(Me.teShipAddress2)
        Me.lcRoot.Controls.Add(Me.teEmpsCount)
        Me.lcRoot.Controls.Add(Me.CO_DBATextEdit)
        Me.lcRoot.Controls.Add(Me.CO_NAMETextEdit)
        Me.lcRoot.Controls.Add(Me.CO_STATUSTextEdit)
        Me.lcRoot.Controls.Add(Me.CONUMSpinEdit)
        Me.lcRoot.Controls.Add(Me.PR_CONTACTTextEdit)
        Me.lcRoot.Controls.Add(Me.CO_EXTENSIONTextEdit)
        Me.lcRoot.Controls.Add(Me.PR_PASSWORDTextEdit)
        Me.lcRoot.Controls.Add(Me.CO_EMAILTextEdit)
        Me.lcRoot.Controls.Add(Me.DELVDESCTextEdit)
        Me.lcRoot.Controls.Add(Me.CO_PHONETextEdit)
        Me.lcRoot.Controls.Add(Me.CO_MODEMTextEdit)
        Me.lcRoot.Controls.Add(Me.OP_OWNERTextEdit)
        Me.lcRoot.Controls.Add(Me.CO_FAXTextEdit)
        Me.lcRoot.Controls.Add(Me.teShPhone)
        Me.lcRoot.Controls.Add(Me.teShFax)
        Me.lcRoot.Controls.Add(Me.teShModem)
        Me.lcRoot.Controls.Add(Me.teClosedStatus)
        Me.lcRoot.Controls.Add(Me.teDelvDesc1)
        Me.lcRoot.Controls.Add(Me.teDelvDesc2)
        Me.lcRoot.Controls.Add(Me.teGroup)
        Me.lcRoot.Controls.Add(Me.teNsfClearedDate)
        Me.lcRoot.DataSource = Me.bsCompInfo
        Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lcRoot.Location = New System.Drawing.Point(0, 0)
        Me.lcRoot.Name = "lcRoot"
        Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(0, 323, 789, 507)
        Me.lcRoot.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.[True]
        Me.lcRoot.Root = Me.LayoutControlGroup1
        Me.lcRoot.Size = New System.Drawing.Size(701, 522)
        Me.lcRoot.TabIndex = 0
        Me.lcRoot.Text = "DataLayoutControl1"
        '
        'hllcCancel
        '
        Me.hllcCancel.Cursor = System.Windows.Forms.Cursors.Hand
        Me.hllcCancel.Location = New System.Drawing.Point(606, 507)
        Me.hllcCancel.Name = "hllcCancel"
        Me.hllcCancel.Size = New System.Drawing.Size(32, 13)
        Me.hllcCancel.StyleController = Me.lcRoot
        Me.hllcCancel.TabIndex = 31
        Me.hllcCancel.Text = "Cancel"
        '
        'hllcUpdate
        '
        Me.hllcUpdate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.hllcUpdate.Location = New System.Drawing.Point(642, 507)
        Me.hllcUpdate.Name = "hllcUpdate"
        Me.hllcUpdate.Size = New System.Drawing.Size(35, 13)
        Me.hllcUpdate.StyleController = Me.lcRoot
        Me.hllcUpdate.TabIndex = 24
        Me.hllcUpdate.Text = "Update"
        '
        'hllcEdit
        '
        Me.hllcEdit.Cursor = System.Windows.Forms.Cursors.Hand
        Me.hllcEdit.Location = New System.Drawing.Point(681, 507)
        Me.hllcEdit.Name = "hllcEdit"
        Me.hllcEdit.Size = New System.Drawing.Size(18, 13)
        Me.hllcEdit.StyleController = Me.lcRoot
        Me.hllcEdit.TabIndex = 23
        Me.hllcEdit.Text = "Edit"
        '
        'cbeShStats
        '
        Me.cbeShStats.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_STATE", True))
        Me.cbeShStats.Location = New System.Drawing.Point(442, 381)
        Me.cbeShStats.MenuManager = Me.RibbonControl2
        Me.cbeShStats.Name = "cbeShStats"
        Me.cbeShStats.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeShStats.Properties.ReadOnly = True
        Me.cbeShStats.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeShStats.Size = New System.Drawing.Size(50, 20)
        Me.cbeShStats.StyleController = Me.lcRoot
        Me.cbeShStats.TabIndex = 41
        Me.cbeShStats.TabStop = False
        '
        'teShExt
        '
        Me.teShExt.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_EXTENSION", True))
        Me.teShExt.Location = New System.Drawing.Point(442, 405)
        Me.teShExt.MenuManager = Me.RibbonControl2
        Me.teShExt.Name = "teShExt"
        Me.teShExt.Properties.ReadOnly = True
        Me.teShExt.Size = New System.Drawing.Size(251, 20)
        Me.teShExt.StyleController = Me.lcRoot
        Me.teShExt.TabIndex = 38
        '
        'teShZip
        '
        Me.teShZip.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_ZIP", True))
        Me.teShZip.Location = New System.Drawing.Point(596, 381)
        Me.teShZip.MenuManager = Me.RibbonControl2
        Me.teShZip.Name = "teShZip"
        Me.teShZip.Properties.ReadOnly = True
        Me.teShZip.Size = New System.Drawing.Size(97, 20)
        Me.teShZip.StyleController = Me.lcRoot
        Me.teShZip.TabIndex = 36
        '
        'teShCity
        '
        Me.teShCity.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_CITY", True))
        Me.teShCity.Enabled = False
        Me.teShCity.Location = New System.Drawing.Point(108, 381)
        Me.teShCity.MenuManager = Me.RibbonControl2
        Me.teShCity.Name = "teShCity"
        Me.teShCity.Properties.ReadOnly = True
        Me.teShCity.Size = New System.Drawing.Size(230, 20)
        Me.teShCity.StyleController = Me.lcRoot
        Me.teShCity.TabIndex = 35
        Me.teShCity.TabStop = False
        '
        'teShStreet
        '
        Me.teShStreet.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_STREET", True))
        Me.teShStreet.Location = New System.Drawing.Point(108, 357)
        Me.teShStreet.MenuManager = Me.RibbonControl2
        Me.teShStreet.Name = "teShStreet"
        Me.teShStreet.Properties.ReadOnly = True
        Me.teShStreet.Size = New System.Drawing.Size(585, 20)
        Me.teShStreet.StyleController = Me.lcRoot
        Me.teShStreet.TabIndex = 34
        '
        'teShName
        '
        Me.teShName.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_NAME", True))
        Me.teShName.Location = New System.Drawing.Point(108, 333)
        Me.teShName.MenuManager = Me.RibbonControl2
        Me.teShName.Name = "teShName"
        Me.teShName.Properties.ReadOnly = True
        Me.teShName.Size = New System.Drawing.Size(585, 20)
        Me.teShName.StyleController = Me.lcRoot
        Me.teShName.TabIndex = 33
        '
        'ceShSameAsCompany
        '
        Me.ceShSameAsCompany.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "ShipSameBool", True))
        Me.ceShSameAsCompany.Location = New System.Drawing.Point(528, 309)
        Me.ceShSameAsCompany.MenuManager = Me.RibbonControl2
        Me.ceShSameAsCompany.Name = "ceShSameAsCompany"
        Me.ceShSameAsCompany.Properties.Caption = "Same As Company"
        Me.ceShSameAsCompany.Properties.ReadOnly = True
        Me.ceShSameAsCompany.Size = New System.Drawing.Size(165, 19)
        Me.ceShSameAsCompany.StyleController = Me.lcRoot
        Me.ceShSameAsCompany.TabIndex = 32
        '
        'teCoCounty
        '
        Me.teCoCounty.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_COUNTY", True))
        Me.teCoCounty.Location = New System.Drawing.Point(108, 381)
        Me.teCoCounty.MenuManager = Me.RibbonControl2
        Me.teCoCounty.Name = "teCoCounty"
        Me.teCoCounty.Properties.ReadOnly = True
        Me.teCoCounty.Size = New System.Drawing.Size(208, 20)
        Me.teCoCounty.StyleController = Me.lcRoot
        Me.teCoCounty.TabIndex = 30
        '
        'teCoZip
        '
        Me.teCoZip.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_ZIP", True))
        Me.teCoZip.Location = New System.Drawing.Point(575, 357)
        Me.teCoZip.MenuManager = Me.RibbonControl2
        Me.teCoZip.Name = "teCoZip"
        Me.teCoZip.Properties.ReadOnly = True
        Me.teCoZip.Size = New System.Drawing.Size(118, 20)
        Me.teCoZip.StyleController = Me.lcRoot
        Me.teCoZip.TabIndex = 29
        '
        'cbeStateGeneral
        '
        Me.cbeStateGeneral.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_STATE", True))
        Me.cbeStateGeneral.Location = New System.Drawing.Point(420, 357)
        Me.cbeStateGeneral.MenuManager = Me.RibbonControl2
        Me.cbeStateGeneral.Name = "cbeStateGeneral"
        Me.cbeStateGeneral.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeStateGeneral.Properties.ReadOnly = True
        Me.cbeStateGeneral.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeStateGeneral.Size = New System.Drawing.Size(51, 20)
        Me.cbeStateGeneral.StyleController = Me.lcRoot
        Me.cbeStateGeneral.TabIndex = 28
        '
        'teCoCity
        '
        Me.teCoCity.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_CITY", True))
        Me.teCoCity.Location = New System.Drawing.Point(108, 357)
        Me.teCoCity.MenuManager = Me.RibbonControl2
        Me.teCoCity.Name = "teCoCity"
        Me.teCoCity.Properties.ReadOnly = True
        Me.teCoCity.Size = New System.Drawing.Size(208, 20)
        Me.teCoCity.StyleController = Me.lcRoot
        Me.teCoCity.TabIndex = 27
        '
        'TextEdit2
        '
        Me.TextEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_STREET", True))
        Me.TextEdit2.Location = New System.Drawing.Point(108, 333)
        Me.TextEdit2.MenuManager = Me.RibbonControl2
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.ReadOnly = True
        Me.TextEdit2.Size = New System.Drawing.Size(585, 20)
        Me.TextEdit2.StyleController = Me.lcRoot
        Me.TextEdit2.TabIndex = 26
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_NAME", True))
        Me.TextEdit1.Location = New System.Drawing.Point(108, 309)
        Me.TextEdit1.MenuManager = Me.RibbonControl2
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.ReadOnly = True
        Me.TextEdit1.Size = New System.Drawing.Size(585, 20)
        Me.TextEdit1.StyleController = Me.lcRoot
        Me.TextEdit1.TabIndex = 25
        '
        'teTaxService
        '
        Me.teTaxService.Location = New System.Drawing.Point(412, 27)
        Me.teTaxService.MenuManager = Me.RibbonControl2
        Me.teTaxService.Name = "teTaxService"
        Me.teTaxService.Properties.ReadOnly = True
        Me.teTaxService.Size = New System.Drawing.Size(124, 20)
        Me.teTaxService.StyleController = Me.lcRoot
        Me.teTaxService.TabIndex = 22
        '
        'teShipAddress3
        '
        Me.teShipAddress3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "ShipAddress3", True))
        Me.teShipAddress3.Location = New System.Drawing.Point(8, 357)
        Me.teShipAddress3.MenuManager = Me.RibbonControl2
        Me.teShipAddress3.Name = "teShipAddress3"
        Me.teShipAddress3.Properties.ReadOnly = True
        Me.teShipAddress3.Size = New System.Drawing.Size(685, 20)
        Me.teShipAddress3.StyleController = Me.lcRoot
        Me.teShipAddress3.TabIndex = 20
        '
        'teShipAddress1
        '
        Me.teShipAddress1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "ShipAddress1", True))
        Me.teShipAddress1.Location = New System.Drawing.Point(8, 309)
        Me.teShipAddress1.MenuManager = Me.RibbonControl2
        Me.teShipAddress1.Name = "teShipAddress1"
        Me.teShipAddress1.Properties.ReadOnly = True
        Me.teShipAddress1.Size = New System.Drawing.Size(685, 20)
        Me.teShipAddress1.StyleController = Me.lcRoot
        Me.teShipAddress1.TabIndex = 18
        '
        'teShipAddress2
        '
        Me.teShipAddress2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "ShipAddress2", True))
        Me.teShipAddress2.Location = New System.Drawing.Point(8, 333)
        Me.teShipAddress2.MenuManager = Me.RibbonControl2
        Me.teShipAddress2.Name = "teShipAddress2"
        Me.teShipAddress2.Properties.ReadOnly = True
        Me.teShipAddress2.Size = New System.Drawing.Size(685, 20)
        Me.teShipAddress2.StyleController = Me.lcRoot
        Me.teShipAddress2.TabIndex = 17
        '
        'teEmpsCount
        '
        Me.teEmpsCount.Location = New System.Drawing.Point(179, 27)
        Me.teEmpsCount.MenuManager = Me.RibbonControl2
        Me.teEmpsCount.Name = "teEmpsCount"
        Me.teEmpsCount.Properties.ReadOnly = True
        Me.teEmpsCount.Size = New System.Drawing.Size(57, 20)
        Me.teEmpsCount.StyleController = Me.lcRoot
        Me.teEmpsCount.TabIndex = 8
        '
        'CO_DBATextEdit
        '
        Me.CO_DBATextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_DBA", True))
        Me.CO_DBATextEdit.Location = New System.Drawing.Point(72, 75)
        Me.CO_DBATextEdit.MenuManager = Me.RibbonControl2
        Me.CO_DBATextEdit.Name = "CO_DBATextEdit"
        Me.CO_DBATextEdit.Properties.ReadOnly = True
        Me.CO_DBATextEdit.Size = New System.Drawing.Size(301, 20)
        Me.CO_DBATextEdit.StyleController = Me.lcRoot
        Me.CO_DBATextEdit.TabIndex = 5
        '
        'CO_NAMETextEdit
        '
        Me.CO_NAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_NAME", True))
        Me.CO_NAMETextEdit.Location = New System.Drawing.Point(72, 51)
        Me.CO_NAMETextEdit.MenuManager = Me.RibbonControl2
        Me.CO_NAMETextEdit.Name = "CO_NAMETextEdit"
        Me.CO_NAMETextEdit.Properties.ReadOnly = True
        Me.CO_NAMETextEdit.Size = New System.Drawing.Size(622, 20)
        Me.CO_NAMETextEdit.StyleController = Me.lcRoot
        Me.CO_NAMETextEdit.TabIndex = 6
        '
        'CO_STATUSTextEdit
        '
        Me.CO_STATUSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_STATUS", True))
        Me.CO_STATUSTextEdit.Location = New System.Drawing.Point(72, 99)
        Me.CO_STATUSTextEdit.MenuManager = Me.RibbonControl2
        Me.CO_STATUSTextEdit.Name = "CO_STATUSTextEdit"
        Me.CO_STATUSTextEdit.Properties.ReadOnly = True
        Me.CO_STATUSTextEdit.Size = New System.Drawing.Size(141, 20)
        Me.CO_STATUSTextEdit.StyleController = Me.lcRoot
        Me.CO_STATUSTextEdit.TabIndex = 7
        '
        'CONUMSpinEdit
        '
        Me.CONUMSpinEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CONUM", True))
        Me.CONUMSpinEdit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.CONUMSpinEdit.Location = New System.Drawing.Point(72, 27)
        Me.CONUMSpinEdit.MenuManager = Me.RibbonControl2
        Me.CONUMSpinEdit.Name = "CONUMSpinEdit"
        Me.CONUMSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.CONUMSpinEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CONUMSpinEdit.Properties.ReadOnly = True
        Me.CONUMSpinEdit.Size = New System.Drawing.Size(70, 20)
        Me.CONUMSpinEdit.StyleController = Me.lcRoot
        Me.CONUMSpinEdit.TabIndex = 4
        '
        'PR_CONTACTTextEdit
        '
        Me.PR_CONTACTTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "PR_CONTACT", True))
        Me.PR_CONTACTTextEdit.Location = New System.Drawing.Point(67, 153)
        Me.PR_CONTACTTextEdit.MenuManager = Me.RibbonControl2
        Me.PR_CONTACTTextEdit.Name = "PR_CONTACTTextEdit"
        Me.PR_CONTACTTextEdit.Properties.ReadOnly = True
        Me.PR_CONTACTTextEdit.Size = New System.Drawing.Size(627, 20)
        Me.PR_CONTACTTextEdit.StyleController = Me.lcRoot
        Me.PR_CONTACTTextEdit.TabIndex = 9
        '
        'CO_EXTENSIONTextEdit
        '
        Me.CO_EXTENSIONTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_EXTENSION", True))
        Me.CO_EXTENSIONTextEdit.Location = New System.Drawing.Point(414, 177)
        Me.CO_EXTENSIONTextEdit.MenuManager = Me.RibbonControl2
        Me.CO_EXTENSIONTextEdit.Name = "CO_EXTENSIONTextEdit"
        Me.CO_EXTENSIONTextEdit.Properties.ReadOnly = True
        Me.CO_EXTENSIONTextEdit.Size = New System.Drawing.Size(50, 20)
        Me.CO_EXTENSIONTextEdit.StyleController = Me.lcRoot
        Me.CO_EXTENSIONTextEdit.TabIndex = 11
        '
        'PR_PASSWORDTextEdit
        '
        Me.PR_PASSWORDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "PR_PASSWORD", True))
        Me.PR_PASSWORDTextEdit.Location = New System.Drawing.Point(67, 253)
        Me.PR_PASSWORDTextEdit.MenuManager = Me.RibbonControl2
        Me.PR_PASSWORDTextEdit.Name = "PR_PASSWORDTextEdit"
        Me.PR_PASSWORDTextEdit.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.PR_PASSWORDTextEdit.Properties.Appearance.Options.UseForeColor = True
        Me.PR_PASSWORDTextEdit.Properties.ReadOnly = True
        Me.PR_PASSWORDTextEdit.Size = New System.Drawing.Size(249, 20)
        Me.PR_PASSWORDTextEdit.StyleController = Me.lcRoot
        Me.PR_PASSWORDTextEdit.TabIndex = 12
        '
        'CO_EMAILTextEdit
        '
        Me.CO_EMAILTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_EMAIL", True))
        Me.CO_EMAILTextEdit.Location = New System.Drawing.Point(67, 229)
        Me.CO_EMAILTextEdit.MenuManager = Me.RibbonControl2
        Me.CO_EMAILTextEdit.Name = "CO_EMAILTextEdit"
        Me.CO_EMAILTextEdit.Properties.ReadOnly = True
        Me.CO_EMAILTextEdit.Size = New System.Drawing.Size(627, 20)
        Me.CO_EMAILTextEdit.StyleController = Me.lcRoot
        Me.CO_EMAILTextEdit.TabIndex = 13
        '
        'DELVDESCTextEdit
        '
        Me.DELVDESCTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "DELVDESC", True))
        Me.DELVDESCTextEdit.Location = New System.Drawing.Point(108, 381)
        Me.DELVDESCTextEdit.MenuManager = Me.RibbonControl2
        Me.DELVDESCTextEdit.Name = "DELVDESCTextEdit"
        Me.DELVDESCTextEdit.Properties.ReadOnly = True
        Me.DELVDESCTextEdit.Size = New System.Drawing.Size(585, 20)
        Me.DELVDESCTextEdit.StyleController = Me.lcRoot
        Me.DELVDESCTextEdit.TabIndex = 16
        '
        'CO_PHONETextEdit
        '
        Me.CO_PHONETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_PHONE", True))
        Me.CO_PHONETextEdit.Location = New System.Drawing.Point(67, 177)
        Me.CO_PHONETextEdit.MenuManager = Me.RibbonControl2
        Me.CO_PHONETextEdit.Name = "CO_PHONETextEdit"
        EditorButtonImageOptions3.Image = Global.Brands_FrontDesk.My.Resources.Resources.Phone_Small
        Me.CO_PHONETextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions3, New DevExpress.Utils.KeyShortcut((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.P)), SerializableAppearanceObject21, SerializableAppearanceObject22, SerializableAppearanceObject23, SerializableAppearanceObject24, "Call", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.CO_PHONETextEdit.Properties.ReadOnly = True
        Me.CO_PHONETextEdit.Size = New System.Drawing.Size(324, 22)
        Me.CO_PHONETextEdit.StyleController = Me.lcRoot
        Me.CO_PHONETextEdit.TabIndex = 10
        '
        'CO_MODEMTextEdit
        '
        Me.CO_MODEMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_MODEM", True))
        Me.CO_MODEMTextEdit.Location = New System.Drawing.Point(67, 203)
        Me.CO_MODEMTextEdit.MenuManager = Me.RibbonControl2
        Me.CO_MODEMTextEdit.Name = "CO_MODEMTextEdit"
        EditorButtonImageOptions4.Image = Global.Brands_FrontDesk.My.Resources.Resources.Phone_Small
        Me.CO_MODEMTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions4, New DevExpress.Utils.KeyShortcut((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.P)), SerializableAppearanceObject25, SerializableAppearanceObject26, SerializableAppearanceObject27, SerializableAppearanceObject28, "Call", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.CO_MODEMTextEdit.Properties.ReadOnly = True
        Me.CO_MODEMTextEdit.Size = New System.Drawing.Size(281, 22)
        Me.CO_MODEMTextEdit.StyleController = Me.lcRoot
        Me.CO_MODEMTextEdit.TabIndex = 15
        '
        'OP_OWNERTextEdit
        '
        Me.OP_OWNERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "OP_OWNER", True))
        Me.OP_OWNERTextEdit.Location = New System.Drawing.Point(597, 75)
        Me.OP_OWNERTextEdit.MenuManager = Me.RibbonControl2
        Me.OP_OWNERTextEdit.Name = "OP_OWNERTextEdit"
        Me.OP_OWNERTextEdit.Properties.ReadOnly = True
        Me.OP_OWNERTextEdit.Size = New System.Drawing.Size(97, 20)
        Me.OP_OWNERTextEdit.StyleController = Me.lcRoot
        Me.OP_OWNERTextEdit.TabIndex = 21
        '
        'CO_FAXTextEdit
        '
        Me.CO_FAXTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "CO_FAX", True))
        Me.CO_FAXTextEdit.Location = New System.Drawing.Point(528, 177)
        Me.CO_FAXTextEdit.MenuManager = Me.RibbonControl2
        Me.CO_FAXTextEdit.Name = "CO_FAXTextEdit"
        EditorButtonImageOptions1.Image = Global.Brands_FrontDesk.My.Resources.Resources.Phone_Small
        Me.CO_FAXTextEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions1, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject1, SerializableAppearanceObject2, SerializableAppearanceObject3, SerializableAppearanceObject4, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.CO_FAXTextEdit.Properties.ReadOnly = True
        Me.CO_FAXTextEdit.Size = New System.Drawing.Size(166, 22)
        Me.CO_FAXTextEdit.StyleController = Me.lcRoot
        Me.CO_FAXTextEdit.TabIndex = 14
        '
        'teShPhone
        '
        Me.teShPhone.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_PHONE", True))
        Me.teShPhone.Location = New System.Drawing.Point(108, 405)
        Me.teShPhone.Name = "teShPhone"
        Me.teShPhone.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions4, New DevExpress.Utils.KeyShortcut((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.P)), SerializableAppearanceObject5, SerializableAppearanceObject6, SerializableAppearanceObject7, SerializableAppearanceObject8, "Call", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.teShPhone.Properties.ReadOnly = True
        Me.teShPhone.Size = New System.Drawing.Size(230, 22)
        Me.teShPhone.StyleController = Me.lcRoot
        Me.teShPhone.TabIndex = 14
        '
        'teShFax
        '
        Me.teShFax.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_FAX", True))
        Me.teShFax.Location = New System.Drawing.Point(108, 431)
        Me.teShFax.Name = "teShFax"
        Me.teShFax.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions4, New DevExpress.Utils.KeyShortcut((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.P)), SerializableAppearanceObject9, SerializableAppearanceObject10, SerializableAppearanceObject11, SerializableAppearanceObject12, "Call", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.teShFax.Properties.ReadOnly = True
        Me.teShFax.Size = New System.Drawing.Size(230, 22)
        Me.teShFax.StyleController = Me.lcRoot
        Me.teShFax.TabIndex = 14
        '
        'teShModem
        '
        Me.teShModem.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "SH_MODEM", True))
        Me.teShModem.Location = New System.Drawing.Point(442, 431)
        Me.teShModem.Name = "teShModem"
        Me.teShModem.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions4, New DevExpress.Utils.KeyShortcut((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.P)), SerializableAppearanceObject13, SerializableAppearanceObject14, SerializableAppearanceObject15, SerializableAppearanceObject16, "Call", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.teShModem.Properties.ReadOnly = True
        Me.teShModem.Size = New System.Drawing.Size(251, 22)
        Me.teShModem.StyleController = Me.lcRoot
        Me.teShModem.TabIndex = 14
        '
        'teClosedStatus
        '
        Me.teClosedStatus.Location = New System.Drawing.Point(288, 99)
        Me.teClosedStatus.MenuManager = Me.RibbonControl2
        Me.teClosedStatus.Name = "teClosedStatus"
        Me.teClosedStatus.Properties.ReadOnly = True
        Me.teClosedStatus.Size = New System.Drawing.Size(406, 20)
        Me.teClosedStatus.StyleController = Me.lcRoot
        Me.teClosedStatus.TabIndex = 42
        '
        'teDelvDesc1
        '
        Me.teDelvDesc1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "DELVDESC", True))
        Me.teDelvDesc1.Location = New System.Drawing.Point(420, 381)
        Me.teDelvDesc1.MenuManager = Me.RibbonControl2
        Me.teDelvDesc1.Name = "teDelvDesc1"
        Me.teDelvDesc1.Properties.ReadOnly = True
        Me.teDelvDesc1.Size = New System.Drawing.Size(273, 20)
        Me.teDelvDesc1.StyleController = Me.lcRoot
        Me.teDelvDesc1.TabIndex = 44
        '
        'teDelvDesc2
        '
        Me.teDelvDesc2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsCompInfo, "DELVDESC", True))
        Me.teDelvDesc2.Location = New System.Drawing.Point(108, 309)
        Me.teDelvDesc2.MenuManager = Me.RibbonControl2
        Me.teDelvDesc2.Name = "teDelvDesc2"
        Me.teDelvDesc2.Properties.ReadOnly = True
        Me.teDelvDesc2.Size = New System.Drawing.Size(156, 20)
        Me.teDelvDesc2.StyleController = Me.lcRoot
        Me.teDelvDesc2.TabIndex = 45
        '
        'teGroup
        '
        Me.teGroup.Location = New System.Drawing.Point(411, 75)
        Me.teGroup.MenuManager = Me.RibbonControl2
        Me.teGroup.Name = "teGroup"
        Me.teGroup.Properties.ReadOnly = True
        Me.teGroup.Size = New System.Drawing.Size(133, 20)
        Me.teGroup.StyleController = Me.lcRoot
        Me.teGroup.TabIndex = 46
        '
        'teNsfClearedDate
        '
        Me.teNsfClearedDate.Location = New System.Drawing.Point(380, 253)
        Me.teNsfClearedDate.MenuManager = Me.RibbonControl2
        Me.teNsfClearedDate.Name = "teNsfClearedDate"
        Me.teNsfClearedDate.Properties.ReadOnly = True
        Me.teNsfClearedDate.Size = New System.Drawing.Size(314, 20)
        Me.teNsfClearedDate.StyleController = Me.lcRoot
        Me.teNsfClearedDate.TabIndex = 47
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(701, 522)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.AllowDrawBackground = False
        Me.LayoutControlGroup2.AppearanceItemCaption.Font = New System.Drawing.Font("Arial Narrow", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LayoutControlGroup2.AppearanceItemCaption.Options.UseFont = True
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lcgCompanyInfo, Me.LayoutControlGroup4, Me.TabbedControlGroup1, Me.lciHyperLinkUpdate, Me.lciHyperLinkEdit, Me.EmptySpaceItem2, Me.lciHyperLinkCancel})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup2.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup2.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(701, 522)
        '
        'lcgCompanyInfo
        '
        Me.lcgCompanyInfo.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.lcgCompanyInfo.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForCONUM, Me.LayoutControlItem1, Me.ItemForCO_NAME, Me.ItemForCO_DBA, Me.label1099Company, Me.ItemForOP_OWNER, Me.lciInactiveStatus, Me.LayoutControlItem4, Me.ItemForCO_STATUS, Me.lblAchRisk, Me.lciGroup})
        Me.lcgCompanyInfo.Location = New System.Drawing.Point(0, 0)
        Me.lcgCompanyInfo.Name = "lcgCompanyInfo"
        Me.lcgCompanyInfo.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.lcgCompanyInfo.Size = New System.Drawing.Size(701, 126)
        Me.lcgCompanyInfo.Text = "Company Info"
        '
        'ItemForCONUM
        '
        Me.ItemForCONUM.Control = Me.CONUMSpinEdit
        Me.ItemForCONUM.Location = New System.Drawing.Point(0, 0)
        Me.ItemForCONUM.MinSize = New System.Drawing.Size(119, 24)
        Me.ItemForCONUM.Name = "ItemForCONUM"
        Me.ItemForCONUM.Size = New System.Drawing.Size(139, 24)
        Me.ItemForCONUM.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.ItemForCONUM.Text = "Co #"
        Me.ItemForCONUM.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.ItemForCONUM.TextSize = New System.Drawing.Size(60, 15)
        Me.ItemForCONUM.TextToControlDistance = 5
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.teEmpsCount
        Me.LayoutControlItem1.Location = New System.Drawing.Point(139, 0)
        Me.LayoutControlItem1.MinSize = New System.Drawing.Size(87, 24)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(94, 24)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem1.Text = "Emp's"
        Me.LayoutControlItem1.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(28, 15)
        Me.LayoutControlItem1.TextToControlDistance = 5
        '
        'ItemForCO_NAME
        '
        Me.ItemForCO_NAME.Control = Me.CO_NAMETextEdit
        Me.ItemForCO_NAME.Location = New System.Drawing.Point(0, 24)
        Me.ItemForCO_NAME.Name = "ItemForCO_NAME"
        Me.ItemForCO_NAME.Size = New System.Drawing.Size(691, 24)
        Me.ItemForCO_NAME.Text = "Company"
        Me.ItemForCO_NAME.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.ItemForCO_NAME.TextSize = New System.Drawing.Size(60, 15)
        Me.ItemForCO_NAME.TextToControlDistance = 5
        '
        'ItemForCO_DBA
        '
        Me.ItemForCO_DBA.Control = Me.CO_DBATextEdit
        Me.ItemForCO_DBA.Location = New System.Drawing.Point(0, 48)
        Me.ItemForCO_DBA.Name = "ItemForCO_DBA"
        Me.ItemForCO_DBA.Size = New System.Drawing.Size(370, 24)
        Me.ItemForCO_DBA.Text = "DBA"
        Me.ItemForCO_DBA.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.ItemForCO_DBA.TextSize = New System.Drawing.Size(60, 15)
        Me.ItemForCO_DBA.TextToControlDistance = 5
        '
        'label1099Company
        '
        Me.label1099Company.AllowHotTrack = False
        Me.label1099Company.AppearanceItemCaption.ForeColor = System.Drawing.Color.Red
        Me.label1099Company.AppearanceItemCaption.Options.UseForeColor = True
        Me.label1099Company.Location = New System.Drawing.Point(233, 0)
        Me.label1099Company.MinSize = New System.Drawing.Size(101, 19)
        Me.label1099Company.Name = "label1099Company"
        Me.label1099Company.Size = New System.Drawing.Size(116, 24)
        Me.label1099Company.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.label1099Company.Text = "1099 only company   "
        Me.label1099Company.TextSize = New System.Drawing.Size(97, 15)
        Me.label1099Company.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'ItemForOP_OWNER
        '
        Me.ItemForOP_OWNER.Control = Me.OP_OWNERTextEdit
        Me.ItemForOP_OWNER.Location = New System.Drawing.Point(541, 48)
        Me.ItemForOP_OWNER.MaxSize = New System.Drawing.Size(150, 24)
        Me.ItemForOP_OWNER.MinSize = New System.Drawing.Size(150, 24)
        Me.ItemForOP_OWNER.Name = "ItemForOP_OWNER"
        Me.ItemForOP_OWNER.Size = New System.Drawing.Size(150, 24)
        Me.ItemForOP_OWNER.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.ItemForOP_OWNER.Text = "Specialist"
        Me.ItemForOP_OWNER.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.ItemForOP_OWNER.TextSize = New System.Drawing.Size(44, 15)
        Me.ItemForOP_OWNER.TextToControlDistance = 5
        '
        'lciInactiveStatus
        '
        Me.lciInactiveStatus.Control = Me.teClosedStatus
        Me.lciInactiveStatus.Location = New System.Drawing.Point(210, 72)
        Me.lciInactiveStatus.Name = "lciInactiveStatus"
        Me.lciInactiveStatus.Size = New System.Drawing.Size(481, 24)
        Me.lciInactiveStatus.Text = "Inactive Status"
        Me.lciInactiveStatus.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.lciInactiveStatus.TextSize = New System.Drawing.Size(66, 15)
        Me.lciInactiveStatus.TextToControlDistance = 5
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.teTaxService
        Me.LayoutControlItem4.Location = New System.Drawing.Point(349, 0)
        Me.LayoutControlItem4.MinSize = New System.Drawing.Size(110, 24)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(184, 24)
        Me.LayoutControlItem4.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem4.Text = "Tax Service"
        Me.LayoutControlItem4.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(51, 15)
        Me.LayoutControlItem4.TextToControlDistance = 5
        '
        'ItemForCO_STATUS
        '
        Me.ItemForCO_STATUS.Control = Me.CO_STATUSTextEdit
        Me.ItemForCO_STATUS.Location = New System.Drawing.Point(0, 72)
        Me.ItemForCO_STATUS.Name = "ItemForCO_STATUS"
        Me.ItemForCO_STATUS.Size = New System.Drawing.Size(210, 24)
        Me.ItemForCO_STATUS.Text = "Status"
        Me.ItemForCO_STATUS.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize
        Me.ItemForCO_STATUS.TextSize = New System.Drawing.Size(60, 15)
        Me.ItemForCO_STATUS.TextToControlDistance = 5
        '
        'lblAchRisk
        '
        Me.lblAchRisk.AllowHotTrack = False
        Me.lblAchRisk.AppearanceItemCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.lblAchRisk.AppearanceItemCaption.ForeColor = System.Drawing.Color.Red
        Me.lblAchRisk.AppearanceItemCaption.Options.UseFont = True
        Me.lblAchRisk.AppearanceItemCaption.Options.UseForeColor = True
        Me.lblAchRisk.AppearanceItemCaption.Options.UseTextOptions = True
        Me.lblAchRisk.AppearanceItemCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.lblAchRisk.Location = New System.Drawing.Point(533, 0)
        Me.lblAchRisk.MinSize = New System.Drawing.Size(101, 17)
        Me.lblAchRisk.Name = "lblAchRisk"
        Me.lblAchRisk.Size = New System.Drawing.Size(158, 24)
        Me.lblAchRisk.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.lblAchRisk.Text = "ACH Risk"
        Me.lblAchRisk.TextSize = New System.Drawing.Size(97, 13)
        '
        'lciGroup
        '
        Me.lciGroup.Control = Me.teGroup
        Me.lciGroup.Location = New System.Drawing.Point(370, 48)
        Me.lciGroup.MaxSize = New System.Drawing.Size(171, 24)
        Me.lciGroup.MinSize = New System.Drawing.Size(171, 24)
        Me.lciGroup.Name = "lciGroup"
        Me.lciGroup.Size = New System.Drawing.Size(171, 24)
        Me.lciGroup.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.lciGroup.Text = "Group"
        Me.lciGroup.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.lciGroup.TextSize = New System.Drawing.Size(29, 15)
        Me.lciGroup.TextToControlDistance = 5
        '
        'LayoutControlGroup4
        '
        Me.LayoutControlGroup4.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForPR_CONTACT, Me.ItemForCO_PHONE, Me.ItemForCO_EMAIL, Me.ItemForPR_PASSWORD, Me.ItemForCO_EXTENSION, Me.ItemForCO_MODEM, Me.LayoutControlItem6, Me.ItemForCO_FAX, Me.LayoutControlItem20})
        Me.LayoutControlGroup4.Location = New System.Drawing.Point(0, 126)
        Me.LayoutControlGroup4.Name = "LayoutControlGroup4"
        Me.LayoutControlGroup4.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.LayoutControlGroup4.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.LayoutControlGroup4.Size = New System.Drawing.Size(701, 154)
        Me.LayoutControlGroup4.Text = "Payroll Contact"
        '
        'ItemForPR_CONTACT
        '
        Me.ItemForPR_CONTACT.Control = Me.PR_CONTACTTextEdit
        Me.ItemForPR_CONTACT.Location = New System.Drawing.Point(0, 0)
        Me.ItemForPR_CONTACT.Name = "ItemForPR_CONTACT"
        Me.ItemForPR_CONTACT.Size = New System.Drawing.Size(691, 24)
        Me.ItemForPR_CONTACT.Text = "Name"
        Me.ItemForPR_CONTACT.TextSize = New System.Drawing.Size(57, 15)
        '
        'ItemForCO_PHONE
        '
        Me.ItemForCO_PHONE.Control = Me.CO_PHONETextEdit
        Me.ItemForCO_PHONE.Location = New System.Drawing.Point(0, 24)
        Me.ItemForCO_PHONE.Name = "ItemForCO_PHONE"
        Me.ItemForCO_PHONE.Size = New System.Drawing.Size(388, 26)
        Me.ItemForCO_PHONE.Text = "Phone"
        Me.ItemForCO_PHONE.TextSize = New System.Drawing.Size(57, 15)
        '
        'ItemForCO_FAX
        '
        Me.ItemForCO_FAX.Control = Me.CO_FAXTextEdit
        Me.ItemForCO_FAX.Location = New System.Drawing.Point(461, 24)
        Me.ItemForCO_FAX.Name = "ItemForCO_FAX"
        Me.ItemForCO_FAX.Size = New System.Drawing.Size(230, 26)
        Me.ItemForCO_FAX.Text = "Fax"
        Me.ItemForCO_FAX.TextSize = New System.Drawing.Size(57, 15)
        '
        'ItemForCO_EMAIL
        '
        Me.ItemForCO_EMAIL.Control = Me.CO_EMAILTextEdit
        Me.ItemForCO_EMAIL.Location = New System.Drawing.Point(0, 76)
        Me.ItemForCO_EMAIL.Name = "ItemForCO_EMAIL"
        Me.ItemForCO_EMAIL.Size = New System.Drawing.Size(691, 24)
        Me.ItemForCO_EMAIL.Text = "Email"
        Me.ItemForCO_EMAIL.TextSize = New System.Drawing.Size(57, 15)
        '
        'ItemForPR_PASSWORD
        '
        Me.ItemForPR_PASSWORD.Control = Me.PR_PASSWORDTextEdit
        Me.ItemForPR_PASSWORD.Location = New System.Drawing.Point(0, 100)
        Me.ItemForPR_PASSWORD.Name = "ItemForPR_PASSWORD"
        Me.ItemForPR_PASSWORD.Size = New System.Drawing.Size(313, 24)
        Me.ItemForPR_PASSWORD.Text = "Pr Password"
        Me.ItemForPR_PASSWORD.TextSize = New System.Drawing.Size(57, 15)
        '
        'ItemForCO_EXTENSION
        '
        Me.ItemForCO_EXTENSION.Control = Me.CO_EXTENSIONTextEdit
        Me.ItemForCO_EXTENSION.Location = New System.Drawing.Point(388, 24)
        Me.ItemForCO_EXTENSION.Name = "ItemForCO_EXTENSION"
        Me.ItemForCO_EXTENSION.Size = New System.Drawing.Size(73, 26)
        Me.ItemForCO_EXTENSION.Text = "Ext"
        Me.ItemForCO_EXTENSION.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.ItemForCO_EXTENSION.TextSize = New System.Drawing.Size(14, 15)
        Me.ItemForCO_EXTENSION.TextToControlDistance = 5
        '
        'ItemForCO_MODEM
        '
        Me.ItemForCO_MODEM.Control = Me.CO_MODEMTextEdit
        Me.ItemForCO_MODEM.Location = New System.Drawing.Point(0, 50)
        Me.ItemForCO_MODEM.Name = "ItemForCO_MODEM"
        Me.ItemForCO_MODEM.Size = New System.Drawing.Size(345, 26)
        Me.ItemForCO_MODEM.Text = "Cell"
        Me.ItemForCO_MODEM.TextSize = New System.Drawing.Size(57, 15)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.teNsfClearedDate
        Me.LayoutControlItem6.Location = New System.Drawing.Point(313, 100)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(378, 24)
        Me.LayoutControlItem6.Text = "Clear Date"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(57, 15)
        '
        'TabbedControlGroup1
        '
        Me.TabbedControlGroup1.Location = New System.Drawing.Point(0, 280)
        Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
        Me.TabbedControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.TabbedControlGroup1.SelectedTabPage = Me.lcgShippingGeneral
        Me.TabbedControlGroup1.Size = New System.Drawing.Size(701, 225)
        Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lcgGeneralShipping, Me.lcgShippingGeneral, Me.lcgShipping})
        '
        'lcgShippingGeneral
        '
        Me.lcgShippingGeneral.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem8, Me.LayoutControlItem9, Me.LayoutControlItem10, Me.LayoutControlItem11, Me.LayoutControlItem12, Me.LayoutControlItem13, Me.EmptySpaceItem3, Me.LayoutControlItem7})
        Me.lcgShippingGeneral.Location = New System.Drawing.Point(0, 0)
        Me.lcgShippingGeneral.Name = "lcgShippingGeneral"
        Me.lcgShippingGeneral.Size = New System.Drawing.Size(689, 192)
        Me.lcgShippingGeneral.Text = "General / Filing"
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.TextEdit1
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem8.Text = "Name: "
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.TextEdit2
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem9.Text = "Street: "
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.teCoCity
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(312, 24)
        Me.LayoutControlItem10.Text = "City: "
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.cbeStateGeneral
        Me.LayoutControlItem11.Location = New System.Drawing.Point(312, 48)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(155, 24)
        Me.LayoutControlItem11.Text = "St.: "
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.teCoZip
        Me.LayoutControlItem12.Location = New System.Drawing.Point(467, 48)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(222, 24)
        Me.LayoutControlItem12.Text = "Zip: "
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.teCoCounty
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(312, 24)
        Me.LayoutControlItem13.Text = "Country: "
        Me.LayoutControlItem13.TextSize = New System.Drawing.Size(97, 15)
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(0, 96)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(689, 96)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.teDelvDesc1
        Me.LayoutControlItem7.Location = New System.Drawing.Point(312, 72)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(377, 24)
        Me.LayoutControlItem7.Text = "Delv Desc:"
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(97, 15)
        '
        'lcgGeneralShipping
        '
        Me.lcgGeneralShipping.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForDELVDESC, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem5, Me.EmptySpaceItem1})
        Me.lcgGeneralShipping.Location = New System.Drawing.Point(0, 0)
        Me.lcgGeneralShipping.Name = "lcgGeneralShipping"
        Me.lcgGeneralShipping.Size = New System.Drawing.Size(689, 192)
        Me.lcgGeneralShipping.Text = "Address"
        Me.lcgGeneralShipping.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'ItemForDELVDESC
        '
        Me.ItemForDELVDESC.Control = Me.DELVDESCTextEdit
        Me.ItemForDELVDESC.Location = New System.Drawing.Point(0, 72)
        Me.ItemForDELVDESC.Name = "ItemForDELVDESC"
        Me.ItemForDELVDESC.Size = New System.Drawing.Size(689, 24)
        Me.ItemForDELVDESC.Text = "Delv Desc"
        Me.ItemForDELVDESC.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.teShipAddress2
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.teShipAddress1
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.teShipAddress3
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 96)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(689, 96)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'lcgShipping
        '
        Me.lcgShipping.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.EmptySpaceItem4, Me.LayoutControlItem16, Me.LayoutControlItem17, Me.LayoutControlItem18, Me.LayoutControlItem19, Me.LayoutControlItem21, Me.LayoutControlItem24, Me.LayoutControlItem15, Me.EmptySpaceItem5, Me.ItemForSh_Ph, Me.ItemForSh_Fax, Me.ItemForSh_Modem, Me.LayoutControlItem14})
        Me.lcgShipping.Location = New System.Drawing.Point(0, 0)
        Me.lcgShipping.Name = "lcgShipping"
        Me.lcgShipping.Size = New System.Drawing.Size(689, 192)
        Me.lcgShipping.Text = "Shipping"
        '
        'EmptySpaceItem4
        '
        Me.EmptySpaceItem4.AllowHotTrack = False
        Me.EmptySpaceItem4.Location = New System.Drawing.Point(0, 148)
        Me.EmptySpaceItem4.Name = "EmptySpaceItem4"
        Me.EmptySpaceItem4.Size = New System.Drawing.Size(689, 44)
        Me.EmptySpaceItem4.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.teShName
        Me.LayoutControlItem16.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem16.Text = "Name: "
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.teShStreet
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(689, 24)
        Me.LayoutControlItem17.Text = "Street: "
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.teShCity
        Me.LayoutControlItem18.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem18.Name = "LayoutControlItem18"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(334, 24)
        Me.LayoutControlItem18.Text = "City: "
        Me.LayoutControlItem18.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.teShZip
        Me.LayoutControlItem19.Location = New System.Drawing.Point(488, 72)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(201, 24)
        Me.LayoutControlItem19.Text = "Zip: "
        Me.LayoutControlItem19.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.teShExt
        Me.LayoutControlItem21.Location = New System.Drawing.Point(334, 96)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(355, 26)
        Me.LayoutControlItem21.Text = "Ext.: "
        Me.LayoutControlItem21.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem24
        '
        Me.LayoutControlItem24.Control = Me.cbeShStats
        Me.LayoutControlItem24.Location = New System.Drawing.Point(334, 72)
        Me.LayoutControlItem24.Name = "LayoutControlItem24"
        Me.LayoutControlItem24.Size = New System.Drawing.Size(154, 24)
        Me.LayoutControlItem24.Text = "St.: "
        Me.LayoutControlItem24.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.ceShSameAsCompany
        Me.LayoutControlItem15.Location = New System.Drawing.Point(520, 0)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(169, 24)
        Me.LayoutControlItem15.TextLocation = DevExpress.Utils.Locations.Left
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem15.TextVisible = False
        '
        'EmptySpaceItem5
        '
        Me.EmptySpaceItem5.AllowHotTrack = False
        Me.EmptySpaceItem5.Location = New System.Drawing.Point(260, 0)
        Me.EmptySpaceItem5.Name = "EmptySpaceItem5"
        Me.EmptySpaceItem5.Size = New System.Drawing.Size(260, 24)
        Me.EmptySpaceItem5.TextSize = New System.Drawing.Size(0, 0)
        '
        'ItemForSh_Ph
        '
        Me.ItemForSh_Ph.Control = Me.teShPhone
        Me.ItemForSh_Ph.ControlAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.ItemForSh_Ph.CustomizationFormText = "Fax"
        Me.ItemForSh_Ph.Location = New System.Drawing.Point(0, 96)
        Me.ItemForSh_Ph.Name = "ItemForSh_Ph"
        Me.ItemForSh_Ph.Size = New System.Drawing.Size(334, 26)
        Me.ItemForSh_Ph.Text = "Phone:"
        Me.ItemForSh_Ph.TextSize = New System.Drawing.Size(97, 15)
        '
        'ItemForSh_Fax
        '
        Me.ItemForSh_Fax.Control = Me.teShFax
        Me.ItemForSh_Fax.ControlAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.ItemForSh_Fax.CustomizationFormText = "Fax"
        Me.ItemForSh_Fax.Location = New System.Drawing.Point(0, 122)
        Me.ItemForSh_Fax.Name = "ItemForSh_Fax"
        Me.ItemForSh_Fax.Size = New System.Drawing.Size(334, 26)
        Me.ItemForSh_Fax.Text = "Fax:"
        Me.ItemForSh_Fax.TextSize = New System.Drawing.Size(97, 15)
        '
        'ItemForSh_Modem
        '
        Me.ItemForSh_Modem.Control = Me.teShModem
        Me.ItemForSh_Modem.ControlAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.ItemForSh_Modem.CustomizationFormText = "Fax"
        Me.ItemForSh_Modem.Location = New System.Drawing.Point(334, 122)
        Me.ItemForSh_Modem.Name = "ItemForSh_Modem"
        Me.ItemForSh_Modem.Size = New System.Drawing.Size(355, 26)
        Me.ItemForSh_Modem.Text = "Modem:"
        Me.ItemForSh_Modem.TextSize = New System.Drawing.Size(97, 15)
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.teDelvDesc2
        Me.LayoutControlItem14.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(260, 24)
        Me.LayoutControlItem14.Text = "Delv Desc:"
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(97, 15)
        '
        'lciHyperLinkUpdate
        '
        Me.lciHyperLinkUpdate.Control = Me.hllcUpdate
        Me.lciHyperLinkUpdate.Location = New System.Drawing.Point(640, 505)
        Me.lciHyperLinkUpdate.Name = "lciHyperLinkUpdate"
        Me.lciHyperLinkUpdate.Size = New System.Drawing.Size(39, 17)
        Me.lciHyperLinkUpdate.TextSize = New System.Drawing.Size(0, 0)
        Me.lciHyperLinkUpdate.TextVisible = False
        Me.lciHyperLinkUpdate.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'lciHyperLinkEdit
        '
        Me.lciHyperLinkEdit.Control = Me.hllcEdit
        Me.lciHyperLinkEdit.Location = New System.Drawing.Point(679, 505)
        Me.lciHyperLinkEdit.Name = "lciHyperLinkEdit"
        Me.lciHyperLinkEdit.Size = New System.Drawing.Size(22, 17)
        Me.lciHyperLinkEdit.TextSize = New System.Drawing.Size(0, 0)
        Me.lciHyperLinkEdit.TextVisible = False
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 505)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(604, 17)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'lciHyperLinkCancel
        '
        Me.lciHyperLinkCancel.Control = Me.hllcCancel
        Me.lciHyperLinkCancel.Location = New System.Drawing.Point(604, 505)
        Me.lciHyperLinkCancel.Name = "lciHyperLinkCancel"
        Me.lciHyperLinkCancel.Size = New System.Drawing.Size(36, 17)
        Me.lciHyperLinkCancel.TextSize = New System.Drawing.Size(0, 0)
        Me.lciHyperLinkCancel.TextVisible = False
        Me.lciHyperLinkCancel.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'AccountManagerTextEdit
        '
        Me.AccountManagerTextEdit.Location = New System.Drawing.Point(412, 203)
        Me.AccountManagerTextEdit.MenuManager = Me.RibbonControl2
        Me.AccountManagerTextEdit.Name = "AccountManagerTextEdit"
        Me.AccountManagerTextEdit.Properties.ReadOnly = True
        Me.AccountManagerTextEdit.Size = New System.Drawing.Size(282, 20)
        Me.AccountManagerTextEdit.StyleController = Me.lcRoot
        Me.AccountManagerTextEdit.TabIndex = 48
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.AccountManagerTextEdit
        Me.LayoutControlItem20.Location = New System.Drawing.Point(345, 50)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(346, 26)
        Me.LayoutControlItem20.Text = "Acct Mgr."
        Me.LayoutControlItem20.TextSize = New System.Drawing.Size(57, 15)
        '
        'ucCompInfo
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.lcRoot)
        Me.Name = "ucCompInfo"
        Me.Size = New System.Drawing.Size(701, 522)
        CType(Me.bsCompInfo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.lcRoot.ResumeLayout(False)
        CType(Me.cbeShStats.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShExt.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShZip.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShCity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShStreet.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ceShSameAsCompany.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCoCounty.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCoZip.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStateGeneral.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCoCity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teTaxService.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShipAddress3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShipAddress1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShipAddress2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teEmpsCount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_DBATextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_NAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CONUMSpinEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PR_CONTACTTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_EXTENSIONTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PR_PASSWORDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_EMAILTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DELVDESCTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_PHONETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_MODEMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OP_OWNERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CO_FAXTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShPhone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShFax.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShModem.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teClosedStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teDelvDesc1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teDelvDesc2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teGroup.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teNsfClearedDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgCompanyInfo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCONUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_NAME, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_DBA, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.label1099Company, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForOP_OWNER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciInactiveStatus, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_STATUS, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblAchRisk, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciGroup, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPR_CONTACT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_PHONE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_FAX, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_EMAIL, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPR_PASSWORD, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_EXTENSION, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCO_MODEM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgShippingGeneral, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgGeneralShipping, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDELVDESC, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgShipping, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForSh_Ph, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForSh_Fax, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForSh_Modem, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciHyperLinkUpdate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciHyperLinkEdit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciHyperLinkCancel, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AccountManagerTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents BarButtonItem1 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem2 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem3 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem4 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem5 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bsCompInfo As System.Windows.Forms.BindingSource
    Friend WithEvents RibbonControl2 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents BarButtonItem6 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem7 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem8 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem9 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem10 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage2 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup2 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents lcRoot As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents teEmpsCount As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CO_DBATextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CO_NAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CO_STATUSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CONUMSpinEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents lcgCompanyInfo As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForCO_DBA As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCONUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCO_STATUS As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCO_NAME As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents PR_CONTACTTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ItemForPR_CONTACT As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents CO_EXTENSIONTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PR_PASSWORDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CO_EMAILTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForCO_PHONE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForPR_PASSWORD As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCO_FAX As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCO_MODEM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCO_EXTENSION As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCO_EMAIL As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teShipAddress3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShipAddress1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShipAddress2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DELVDESCTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CO_PHONETextEdit As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents CO_MODEMTextEdit As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents OP_OWNERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ItemForOP_OWNER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents CO_FAXTextEdit As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents teTaxService As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents hllcUpdate As DevExpress.XtraEditors.HyperlinkLabelControl
    Friend WithEvents hllcEdit As DevExpress.XtraEditors.HyperlinkLabelControl
    Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
    Friend WithEvents lcgShippingGeneral As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents lcgShipping As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents lciHyperLinkUpdate As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lciHyperLinkEdit As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDELVDESC As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lcgGeneralShipping As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents teCoCounty As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teCoZip As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cbeStateGeneral As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents teCoCity As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents hllcCancel As DevExpress.XtraEditors.HyperlinkLabelControl
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents lciHyperLinkCancel As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents ceShSameAsCompany As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents EmptySpaceItem4 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents teShExt As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShZip As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShCity As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShStreet As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents cbeShStats As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem24 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem5 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents label1099Company As DevExpress.XtraLayout.SimpleLabelItem
    Friend WithEvents teShPhone As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents ItemForSh_Ph As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teShFax As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents ItemForSh_Fax As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teShModem As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents ItemForSh_Modem As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teClosedStatus As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lciInactiveStatus As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teDelvDesc1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teDelvDesc2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lblAchRisk As DevExpress.XtraLayout.SimpleLabelItem
    Friend WithEvents teGroup As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lciGroup As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teNsfClearedDate As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents AccountManagerTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
End Class
