﻿Imports DevExpress.XtraEditors

Public Class frmUpdateUdf20

    Private _coNum As Decimal?
    Dim udf As COUSERDEF
    Dim db As dbEPDataDataContext

    Public Sub New(CoNum As Decimal)
        InitializeComponent()
        _coNum = CoNum
    End Sub

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub UpdateUdf20_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        BindingSource2.DataSource = db.view_CompanySumarries.ToList()
        lueCompany.EditValue = _coNum
        lueCompany.Focus()
        lueCompany.Select()
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnSave.Click, btnSaveAndClose.Click
        Try
            If lueCompany.EditValue Is Nothing Then
                XtraMessageBox.Show("No Co# selected.")
                Exit Sub
            End If

            If cbeCode.EditValue Is Nothing OrElse cbeCode.EditValue.ToString().IsNullOrWhiteSpace Then
                If XtraMessageBox.Show("No Value entered for Code, Would you like to continue?", "Missing Code", MessageBoxButtons.YesNo) = DialogResult.No Then
                    Exit Sub
                End If
            End If
            SetUdfValue("Udf20DescDefYear", cbeYear.EditValue)
            SetUdfValue("Udf20DescDefList", cbeCode.EditValue)
            udf.udf20_desc = (cbeCode.EditValue & cbeYear.EditValue & teExistingCode.Text & New String(" ", 50)).ToString().Substring(0, 50).Trim()
            If db.SaveChanges Then
                If sender Is btnSaveAndClose Then
                    DialogResult = DialogResult.OK
                Else
                    lueCompany.EditValue = Nothing
                    lueCompany.Focus()
                    lueCompany.Select()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error updating UDF 20", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Private Sub lueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles lueCompany.EditValueChanged
        db = New dbEPDataDataContext(GetConnectionString)
        _coNum = lueCompany.EditValue
        If _coNum.HasValue Then
            PanelControl1.Enabled = True
            btnSave.Enabled = True
            btnSaveAndClose.Enabled = True
            udf = db.COUSERDEFs.Single(Function(c) c.conum = _coNum)
            teExistingCode.Text = udf.udf20_desc
            cbeCode.Properties.Items.AddRange(GetUdfValue("Udf20DescList").Split(","))
            cbeCode.EditValue = GetUdfValue("Udf20DescDefList")
            cbeYear.EditValue = GetUdfValue("Udf20DescDefYear")
        Else
            PanelControl1.Enabled = False
            btnSave.Enabled = False
            btnSaveAndClose.Enabled = False
        End If
    End Sub
End Class