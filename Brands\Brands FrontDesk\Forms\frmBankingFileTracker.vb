﻿Imports DevExpress.Utils.Menu

Public Class frmBankingFileTracker
    Private db As dbEPDataDataContext
    Dim popupMenu As DXPopupMenu
    Dim menuPrintPreview As New DXMenuItem() With {.Caption = "Print Preview"}

    Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
    End Sub

    Private Sub frmBankingFileTracker_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            popupMenu = New DXPopupMenu()
            menuPrintPreview = New DXMenuItem() With {.Caption = "Print Preview"}
            menuPrintPreview.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.preview_16x16
            popupMenu.Items.Add(menuPrintPreview)
            AddHandler menuPrintPreview.Click, AddressOf btnPrint_Click
            DropDownButtonPrint.DropDownControl = popupMenu

            'Dim DB = New dbEPDataDataContext(GetConnectionString)
            GridControl1.DataSource = Query("EXEC custom.prc_RptFDMultiReport @Report = 'DelayedWireReport'")

            db = New dbEPDataDataContext(GetConnectionString())
            rilueProcessor.DataSource = (From p In db.BankingFileUploads Order By p.Processor Select New With {.Processor = p.Processor}).Distinct().ToList()
            rilueProcessor2.DataSource = (From p In db.BankingFileUploads Order By p.Processor Select New With {.Processor = p.Processor}).Distinct().ToList()
            LoadData()
            GVFileHolds.ActiveFilterString = "[Closed] = False"
        Catch ex As Exception
            DisplayErrorMessage("Error in frmBankingFileTracker_Load", ex)
        End Try
    End Sub

    Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString())
            'GCFileUpload.DataSource = db.BankingFileUploads.OrderByDescending(Function(f) f.ID).ToList()
            GCFileUpload.DataSource = db.BankingFileUploads.OrderByDescending(Function(f) f.ID).ToList()
            GVFileUpload.BestFitColumns()
            'GCFileHolds.DataSource = db.BankingFileHolds.OrderByDescending(Function(f) f.ID)
            GCFileHolds.DataSource = db.BankingFileHolds.OrderByDescending(Function(f) f.ID).ToList()
            GVFileHolds.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Close()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            db.SaveChanges()
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in Save", ex)
        End Try
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles DropDownButtonPrint.Click
        Try
            Dim Report = Query(Of ReportEmailTeplate)("SELECT * FROM custom.ReportEmailTeplate ret WHERE ret.ID = 462").First()
            Dim Co As COMPANY = Query(Of COMPANY)("SELECT top 1 * FROM Company").First()
            Dim processor = New ReportProcessor(Co, Report, FileType.Pdf) With {.showParametersForm = False}
            Dim result = processor.ProcessReport()

            If result.AllFileExist AndAlso result.Paths.Count <> 0 Then
                If sender Is menuPrintPreview Then
                    'System.Diagnostics.Process.Start(result.Paths(0))
                    Dim psi As New System.Diagnostics.ProcessStartInfo()
                    psi.FileName = result.Paths(0)
                    psi.UseShellExecute = True
                    System.Diagnostics.Process.Start(result.Paths(0))
                    Return
                End If

                Dim startInfo = New ProcessStartInfo With {
                    .Verb = "PrintTo",
                    .FileName = result.Paths(0),
                    .CreateNoWindow = True,
                    .WindowStyle = ProcessWindowStyle.Minimized,
                    .UseShellExecute = True,
                    .Arguments = Nothing 'New Printing.PrinterSettings().PrinterName
                }
                Process.Start(startInfo)

            End If
        Catch ex As Exception
            DisplayErrorMessage("error printing report", ex)
        End Try
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Async Sub bbiFixBankingStatus_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiFixBankingStatus.ItemClick
        Try
            Dim FixBankingScriptID = 1344
            Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
            If sqlFrm IsNot Nothing Then
                frmMain.CloseForm(sqlFrm)
            End If
            frmMain.AddOrActivateForm(Of frmSqlScripts)()
            sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
            sqlFrm.FindAndFocusRecord(FixBankingScriptID)
            Await sqlFrm.ExecuteClick(FixBankingScriptID)
        Catch ex As Exception
            DisplayErrorMessage("bbiFixBankingStatus_ItemClick", ex)
        End Try
    End Sub
End Class