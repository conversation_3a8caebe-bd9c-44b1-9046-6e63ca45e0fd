﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors
Public Class frmLinkExistingEmployee
    Private ReadOnly conum As Decimal

    Public Sub New(conum As Decimal)
        InitializeComponent()
        Me.conum = conum
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property contact As co_contact

    Private Sub frmLinkExistingEmployee_Load(sender As Object, e As EventArgs) Handles Me.Load
        Try
            Using DB As New dbEPDataDataContext(GetConnectionString)
                Dim company = DB.COMPANies.Single(Function(c) c.CONUM = conum)
                slueCompany.Properties.DataSource = DB.view_CompanySumarries.ToList()
                slueCompany.EditValue = conum
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error in frmInviteUser_Load", ex)
        End Try
    End Sub

    Private Sub slueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles slueCompany.EditValueChanged
        Try
            Dim selectedConum As Integer = slueCompany.EditValue
            Using db As New dbEPDataDataContext(GetConnectionString)
                Dim AllEmployees = db.EMPLOYEEs.Where(Function(c) c.CONUM = selectedConum).
                    Select(Function(a) New With {
                        .EmpNum = a.EMPNUM,
                        .FullName = "{0} - {1} {2}".FormatWith(a.EMPNUM, a.F_NAME, a.L_NAME),
                        .TermDate = a.TERM_DATE,
                        .Email = a.user_email
                       }).ToList
                slueEmployee.Properties.DataSource = AllEmployees
                slueEmployee.Properties.DisplayMember = "FullName"
                slueEmployee.Properties.ValueMember = "EmpNum"
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error loading employee", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Dim employee As Integer
            If slueEmployee.EditValue Is Nothing OrElse Not Integer.TryParse(slueEmployee.EditValue?.ToString(), employee) Then
                XtraMessageBox.Show("Please select an employee")
                Exit Sub
            ElseIf Not (ceCpaBookeeper.Checked OrElse cePrincipal.Checked OrElse ceHr.Checked OrElse cePayroll.Checked OrElse ceEndOfPeriod.Checked) Then
                XtraMessageBox.Show("Please select at least 1 role")
                Exit Sub
            End If

            Using db As New dbEPDataDataContext(GetConnectionString)
                Dim selectedConum As Integer = slueCompany.EditValue
                Dim selectedEmp As Integer = slueEmployee.EditValue
                Dim hasExistingContact = db.co_contacts.Any(Function(c) c.conum = conum AndAlso c.empconum = selectedConum AndAlso c.empnum = selectedEmp)
                If hasExistingContact Then
                    XtraMessageBox.Show("This employee is already linked")
                    Exit Sub
                End If

                contact = New co_contact With {
                                              .conum = conum,
                                              .empconum = selectedConum,
                                              .empnum = selectedEmp,
                                              .co_cpa_Bool = ceCpaBookeeper.Checked,
                                              .co_principal_Bool = cePrincipal.Checked,
                                              .hr_contact_Bool = ceHr.Checked,
                                              .pr_contact_Bool = cePayroll.Checked,
                                              .qyend_contact_Bool = ceEndOfPeriod.Checked}
                db.co_contacts.InsertOnSubmit(contact)
                db.SubmitChanges()
            End Using

            DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving company contact", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

End Class