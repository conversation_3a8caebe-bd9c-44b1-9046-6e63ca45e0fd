﻿Imports System.Text.RegularExpressions
Imports Brands.Core.ColumbusApi
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports Serilog

Public Class frmDeliveries

    Dim DB As dbEPDataDataContext
    Dim ScannedList As List(Of Delivery)
    Dim TicketEnt As DeliveryTicket
    Dim api As ColumbusAPI
    Dim ColumbusLabelPrinter As String
    Dim logger As ILogger = modGlobals.Logger.ForContext(Of frmDeliveries)
    Dim menuForceNewTicket As DXMenuItem
    Dim menuPrintPreview As DXMenuItem

    Private Sub frmDeliveries_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler IDTextBox.DataBindings("Text").Parse, AddressOf ConvertToNothing

        DB = New dbEPDataDataContext(GetConnectionString)

        TicketEnt = (From A In DB.DeliveryTickets Where A.DeliverBy = UserName Order By A.ID Descending).FirstOrDefault
        If TicketEnt IsNot Nothing Then
            BindData()
        Else
            SwitchEnterNewTicket(True)
        End If

        Dim AllUsers = (From A In DB.DBUSERs Order By A.name).ToList
        Me.DBUSERBindingSource.DataSource = AllUsers

        Me.DeliverByComboBox.Text = UserName
        Me.DeliverByComboBox.Enabled = nz(Permissions.AllowDeliveryScanFor, False).GetValueOrDefault
        Me.ScannedByTextBox.Text = UserName
        Me.BarCodeTextBox.Focus()

        Dim apiLogin = GetUdfValue("ColumbusApiLogin").Split(New Char() {" "}).ToArray()
        api = New ColumbusAPI(apiLogin(0), apiLogin(1))
        ColumbusLabelPrinter = GetUdfValue("ColumbusLabelPrinter")

        Dim popupMenu As DXPopupMenu = New DXPopupMenu()
        menuForceNewTicket = New DXMenuItem() With {.Caption = "Force"}
        popupMenu.Items.Add(menuForceNewTicket)
        AddHandler menuForceNewTicket.Click, AddressOf ddbNewTicket_Click
        ddbNewTicket.DropDownControl = popupMenu

        popupMenu = New DXPopupMenu()
        menuPrintPreview = New DXMenuItem() With {.Caption = "Print Preview"}
        menuPrintPreview.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.preview_16x16
        popupMenu.Items.Add(menuPrintPreview)
        AddHandler menuPrintPreview.Click, AddressOf btnPrint_Click
        DropDownButtonPrint.DropDownControl = popupMenu
    End Sub

    Private Sub BindData()
        Me.DeliveryTicketBindingSource.DataSource = TicketEnt

        ScannedList = TicketEnt.Deliveries.ToList
        Me.DeliveryBindingSource.DataSource = New SortableBindingList(Of Delivery)(ScannedList)
        SwitchEnterNewTicket(False)
        Me.lblClosed.Visible = TicketEnt.IsClosed
        Me.BarCodeTextBox.ReadOnly = TicketEnt.IsClosed
        Me.NoteTextBox.ReadOnly = TicketEnt.IsClosed

        DeliveryDataGridView.Columns(5).Visible = TicketEnt.DeliverBy = "Columbus.O"
        DeliveryDataGridView.Columns(6).Visible = TicketEnt.DeliverBy = "Columbus.O"
    End Sub

    Private Sub SwitchEnterNewTicket(ByVal IsNew As Boolean, Optional OverrideAllowPrint As Boolean = False)
        If IsNew Then Me.IDTextBox.Focus()
        ddbNewTicket.Visible = Not IsNew
        btnSaveTicket.Visible = IsNew
        btnCancelTicket.Visible = IsNew
        BarCodeTextBox.Enabled = Not IsNew
        NoteTextBox.Enabled = Not IsNew
        DeliveryDataGridView.Enabled = Not IsNew OrElse OverrideAllowPrint
        lblNewTicket.Visible = IsNew
        btnPrevTicket.Enabled = Not IsNew
        btnNextTicket.Enabled = Not IsNew
        btnPrint.Enabled = Not IsNew
        DropDownButtonPrint.Enabled = Not IsNew
        lblClosed.Visible = Not IsNew
        If IsNew Then
            IDTextBox.Text = "New Ticket"
        End If
        If Not IsNew Then Me.BarCodeTextBox.Focus()
    End Sub

    Private Sub BarCodeTextBox_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BarCodeTextBox.Validated, NoteTextBox.Validated
        If BarCodeTextBox.Text = "" Then Exit Sub
        If Regex.IsMatch(Me.BarCodeTextBox.Text, "^([0-9]+)-([0-9]+)-([0-9]+)$") Or Regex.IsMatch(Me.BarCodeTextBox.Text, "^([0-9]+)-([0-9]+)$") Then
            Dim notes As String
            Dim IsScanned = (From A In DB.view_Deliveries Where A.BarCode = BarCodeTextBox.Text Order By A.ID Descending).FirstOrDefault
            Dim ColumbusRePrint As Boolean = False

            If IsScanned IsNot Nothing AndAlso Me.NoteTextBox.Text = "" Then
                Dim msg = String.Format("{0} is already scanned by {1} on {2}.{3}Please enter a reason for rescanning.", BarCodeTextBox.Text, IsScanned.ScannedBy, IsScanned.ScannedDate.ToString("g"), vbCrLf)
                Dim frm As New frmDuplicateScan With {.Message = msg}
                My.Computer.Audio.Play(My.Resources._Error, AudioPlayMode.Background)
                Dim results = frm.ShowDialog()
                notes = frm.tbNote.Text
                frm.Close()
                frm.Dispose()
                If results = System.Windows.Forms.DialogResult.OK Then
                    'Me.NoteTextBox.Text = Notes
                    If DeliverByComboBox.Text = "Columbus.O" Then
                        Dim DelivSplit = BarCodeTextBox.Text.Split("-")
                        Dim shipping = DB.prc_GetShippingAddress(DelivSplit(0)).Where(Function(f) f.DDIVNUM = IIf(DelivSplit.Length = 3, DelivSplit(2), 0)).FirstOrDefault()

                        If Query(Of String)($"SELECT ZipCode FROM custom.CDL_ExcludedZipCodes WHERE ZipCode = '" + shipping.ShipZip.ToString() + "'").Count > 0 Then
                            My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Exclamation)
                            MessageBox.Show("CDL not shipping to zip code " + shipping.ShipZip.ToString(), "Cannot ship", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                            Me.BarCodeTextBox.Focus()
                            Me.BarCodeTextBox.SelectionStart = 0
                            Me.BarCodeTextBox.SelectionLength = 100
                            Return
                        End If

                        ColumbusRePrint = True
                    End If
                Else
                    My.Computer.Audio.Play(My.Resources._Error, AudioPlayMode.Background)
                    Me.BarCodeTextBox.Text = ""
                    Me.BarCodeTextBox.Focus()
                    Return
                End If

            ElseIf DeliverByComboBox.Text = "Columbus.O" Then
                'check if columbus sends to this zip code
                Dim DelivSplit = BarCodeTextBox.Text.Split("-")
                Dim shipping = DB.prc_GetShippingAddress(DelivSplit(0)).Where(Function(f) f.DDIVNUM = IIf(DelivSplit.Length = 3, DelivSplit(2), 0)).FirstOrDefault()

                If Query(Of String)($"SELECT ZipCode FROM custom.CDL_ExcludedZipCodes WHERE ZipCode = '" + shipping.ShipZip.ToString() + "'").Count > 0 Then
                    My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Exclamation)
                    MessageBox.Show("CDL not shipping to zip code " + shipping.ShipZip.ToString(), "Cannot ship", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                    Me.BarCodeTextBox.Focus()
                    Me.BarCodeTextBox.SelectionStart = 0
                    Me.BarCodeTextBox.SelectionLength = 100
                    Return
                End If
            End If

            Dim D As New Delivery With {.ScannedBy = UserName,
                                        .ScannedDate = Date.Now,
                                        .BarCode = BarCodeTextBox.Text,
                                        .Note = notes}

            If ColumbusRePrint Then
                Dim barCodeVersion = DB.Deliveries.Where(Function(f) f.BarCode = BarCodeTextBox.Text).Count
                D.BarCodeVersion = barCodeVersion + 1
            End If

            If Not NoteTextBox.Text = "" Then D.Note = NoteTextBox.Text
            If Not frmReceiveInventory.CheckForClocks(BarCodeTextBox.Text) Then
                Me.BarCodeTextBox.Text = ""
                Me.NoteTextBox.Text = ""
                Me.BarCodeTextBox.Focus()
                Exit Sub
            End If
            frmOrderSupplies.ChargeShippingForPaperlessClients(DB, D.BarCode)
            TicketEnt.Deliveries.Add(D)
            DB.SubmitChanges()
            My.Computer.Audio.Play(My.Resources.Record, AudioPlayMode.Background)

            Me.DeliveryBindingSource.Insert(0, D)
            My.Computer.Audio.Play(My.Resources.Record, AudioPlayMode.WaitToComplete)
            '
            Me.BarCodeTextBox.Text = ""
            Me.NoteTextBox.Text = ""
            Me.BarCodeTextBox.Focus()
        Else
E:
            My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Exclamation)
            MessageBox.Show("Invalid Company-Payroll Number")
            Me.BarCodeTextBox.Focus()
            Me.BarCodeTextBox.SelectionStart = 0
            Me.BarCodeTextBox.SelectionLength = 100
        End If
    End Sub

    Private Sub BarCodeTextBox_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BarCodeTextBox.Enter
        Me.BarCodeTextBox.BackColor = Color.LightGreen
    End Sub

    Private Sub BarCodeTextBox_Leave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BarCodeTextBox.Leave
        Me.BarCodeTextBox.BackColor = Color.FromName("Window")
    End Sub

    Private Sub BarCodeTextBox_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles BarCodeTextBox.KeyDown, NoteTextBox.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.Validate()
        ElseIf e.KeyCode = Keys.Escape Then
            Me.BarCodeTextBox.Text = ""
        End If
    End Sub

    Private Sub DeliveryDataGridView_UserDeletingRow(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewRowCancelEventArgs) Handles DeliveryDataGridView.UserDeletingRow
        If MessageBox.Show("Are you sure you want to delete this record?", "Confirm delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.No Then
            e.Cancel = True
        Else
            Dim D As Delivery = e.Row.DataBoundItem
            DB.Deliveries.DeleteOnSubmit(D)
            DB.SubmitChanges()
        End If
    End Sub

    Private Sub btNewTicket_LinkClicked(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles xbtnNewTicket.Click
        If TicketEnt.Deliveries.Count = 0 AndAlso Not TicketEnt.IsClosed Then
            Dim ticketInfo = $"ID: {TicketEnt.ID}, DeliverBy: {TicketEnt.DeliverBy}"

            Dim email As New EmailService("<EMAIL>") With {.Subject = "Cannot create delivery ticket", .Body = $"There is already an open ticket for this user {vbCrLf}[{ticketInfo}]{vbCrLf}{vbCrLf}By continuing the above open ticket will be closed" + vbCrLf + "User: " + UserName}
            email.ToEmail.Add("<EMAIL>")
            email.SendEmail()

            logger.Debug($"Cannot create delivery ticket with with existing open ticket.  Existing Ticket [{ticketInfo}, DeliverBy: {TicketEnt.DeliverBy}]", "Delivery ticket issue")
            If XtraMessageBox.Show($"There is already an open ticket for this user {vbCrLf}[{ticketInfo}]{vbCrLf}{vbCrLf}By continuing the above open ticket will be closed{vbCrLf}Continue?", "Cannot create new ticket when there is already an open ticket", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button3) = DialogResult.Yes Then
                SwitchEnterNewTicket(True)
            End If
        Else
            SwitchEnterNewTicket(True)
        End If
    End Sub

    Private Sub btnSaveTicket_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles btnSaveTicket.LinkClicked
        DB = New dbEPDataDataContext(GetConnectionString)
        TicketEnt = New DeliveryTicket With {.DeliverBy = DeliverByComboBox.Text}
        DB.DeliveryTickets.InsertOnSubmit(TicketEnt)
        DB.SubmitChanges()
        BindData()
    End Sub

    Private Sub btnCancelTicket_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles btnCancelTicket.LinkClicked
        If TicketEnt IsNot Nothing Then SwitchEnterNewTicket(False)
    End Sub


    Private Async Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click, DropDownButtonPrint.Click
        If sender Is menuPrintPreview AndAlso MessageBox.Show("Please confirm you want to preview only, by doing so this order would not be uploaded to CDL until printed", "Print Preview Action", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2) = vbNo Then
            Return
        End If

        Try
            Me.Enabled = False
            Dim rpt As New frmReport
            rpt.TicketNumber = TicketEnt.ID
            logger.Debug("In btnPrint for {ticketId}", TicketEnt.ID)

            If sender Is DropDownButtonPrint Then
                rpt.PrintAndClose()
            Else
                Dim results = rpt.ShowDialog()
            End If
            If rpt.IsPrinted OrElse sender Is DropDownButtonPrint Then
                Me.Cursor = Cursors.WaitCursor
                logger.Debug("Print {ticketId}", TicketEnt.ID)
                TicketEnt.IsClosed = True
                DB.SubmitChanges()
                logger.Debug("closed ticket {ticketId}", TicketEnt.ID)

                If TicketEnt.DeliverBy = "Columbus.O" Then
                    logger.Debug("columbus ticket {ticketId}", TicketEnt.ID)
                    Dim hasReprints As Boolean = False
                    For Each delv As Delivery In TicketEnt.Deliveries
                        Try
                            logger.Debug("columbus ticket {ticketId} looping delivery {DeliveryId}", TicketEnt.ID, delv.ID)
                            Dim orderNum As String = delv.OrderNum

                            If delv.BarCodeVersion > 0 Then
                                hasReprints = True
                            End If

                            'should be null
                            If orderNum.IsNullOrWhiteSpace() Then
                                logger.Debug("columbus ticket {ticketId} delivery {DeliveryId} beore getting order object", TicketEnt.ID, delv.ID)
                                Dim ord = ScanToOrder(delv)
                                logger.Debug("columbus ticket {ticketId} delivery {DeliveryId} after getting order object", TicketEnt.ID, delv.ID)
                                If Not ord Is Nothing Then
                                    logger.Debug("columbus ticket {ticketId} delivery {DeliveryId} before posting", TicketEnt.ID, delv.ID)
                                    orderNum = Await PostOrder(api, ord)
                                    logger.Debug("columbus ticket {ticketId} delivery {DeliveryId} after posting got orderNum {orderNum}", TicketEnt.ID, delv.ID, orderNum)
                                    delv.OrderNum = orderNum
                                    DB.SubmitChanges()
                                    logger.Debug("columbus ticket {ticketId} delivery {DeliveryId} after saving orderNum {orderNum}", TicketEnt.ID, delv.ID, orderNum)
                                Else
                                    logger.Debug("ord is nothing")
                                End If
                            End If
                        Catch ex As Exception
                            logger.Error(ex, "Error creating shipment loop")
                            Throw
                        End Try
                    Next
                    logger.Debug("Finished loop")
                    Me.Cursor = Cursors.Default
                    If hasReprints Then
                        Dim msg = "Some barcode(s) were already used in the past." + vbCrLf + vbCrLf + "Must print special labels for the indicated rows and affix on top of envelope shipping window so the new appended barcode is used by shipping company."
                        Dim caption = "Duplicate Barcode(s) Alert"
                        MessageBox.Show(msg, caption, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                    End If
                End If
                Me.Enabled = True
                TicketEnt = Nothing
                logger.Debug("set TicketEnt = Nothing")
                SwitchEnterNewTicket(True, True)
            End If
            rpt.Close()
            rpt.Dispose()
            Me.Enabled = True
            Me.Activate()
        Catch ex As Exception
            logger.Error(ex, "Error creating shipment")
            DisplayErrorMessage("Error creating shipment", ex)
        Finally
            Me.Enabled = True
            Me.Cursor = Cursors.Default
        End Try
    End Sub

    Private Sub btnPrevTicket_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrevTicket.Click
        Dim Q = From A In DB.DeliveryTickets Where A.ID < TicketEnt.ID Order By A.ID Descending
        If Not Permissions.AllowDeliveryHistory Then
            Q = From A In Q Where A.DeliverBy = UserName
        End If
        Dim PTicketEnt = Q.FirstOrDefault
        If PTicketEnt IsNot Nothing Then
            TicketEnt = PTicketEnt
            BindData()
        Else
            btnPrevTicket.Enabled = False
        End If
    End Sub

    Private Sub btnNextTicket_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNextTicket.Click
        Dim Q = From A In DB.DeliveryTickets Where A.ID > TicketEnt.ID Order By A.ID Ascending
        If Not Permissions.AllowDeliveryHistory Then
            Q = From A In Q Where A.DeliverBy = UserName
        End If
        Dim NTicketEnt = Q.FirstOrDefault
        If NTicketEnt IsNot Nothing Then
            TicketEnt = NTicketEnt
            BindData()
        Else
            btnNextTicket.Enabled = False
        End If
    End Sub

    Private Sub ConvertToNothing(ByVal sender As Object, ByVal e As ConvertEventArgs)
        If e.Value = "New Ticket" Then
            e.Value = 0
        End If
    End Sub

    Private Async Sub DeliveryDataGridView_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DeliveryDataGridView.CellClick
        If e.ColumnIndex = 6 Then
            Try
                DeliveryDataGridView.CurrentCell = DeliveryDataGridView.Rows(e.RowIndex).Cells(0)
                Dim delv As Delivery = DeliveryBindingSource.Current
                Dim orderNum As String = delv.OrderNum

                If orderNum Is Nothing Then
                    MessageBox.Show("Print delivery sheet before attempting to print the label", "Label not ready")
                    Return
                End If

                Dim fileName = api.GetLabel(orderNum)
                System.IO.File.Copy(fileName, ColumbusLabelPrinter)
                MessageBox.Show("Label Printed", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("Error Occured", ex.Message, MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Async Function PostOrder(columbus As Brands.Core.ColumbusApi.ColumbusAPI, order As Order) As Threading.Tasks.Task(Of Decimal)
        Dim result As Order
        result = columbus.PostOrderAsync(order)

        Return result.OrderTrackingID
    End Function

    Function ScanToOrder(deliv As Delivery) As Order
        Dim shipping = DB.prc_GetShippingAddress(deliv.CoNum).Where(Function(f) f.DDIVNUM = deliv.DivNum).FirstOrDefault()

        If shipping Is Nothing Then
            MessageBox.Show($"Did not found shipping info for this scan {deliv.BarCode}", "Incorrect Scan")
            Return Nothing
        End If

        Dim PackageBarCode = deliv.BarCode

        If Not deliv.BarCodeVersion Is Nothing Then
            PackageBarCode += "-" + deliv.BarCodeVersion.ToString()
        End If

        Dim specialInstructions As String = ""
        'only need delivery notes
        'Dim orderSupply = (From s In DB.SuppliesOrders Where s.CoNum = deliv.CoNum AndAlso s.ID = deliv.PayrollNum Select s).FirstOrDefault()
        'If Not orderSupply Is Nothing AndAlso Not orderSupply.Notes Is Nothing Then
        '    specialInstructions = orderSupply.Notes
        'End If

        Dim notes = (From n In DB.NOTEs Where n.conum = deliv.CoNum AndAlso n.category = "Delivery" AndAlso n.priority <> "3-Low" AndAlso n.note IsNot Nothing AndAlso (n.expiration_date > Date.Today OrElse n.expiration_date Is Nothing) AndAlso (n.divnum = 0 OrElse n.divnum = deliv.DivNum OrElse n.divnum Is Nothing) Select n).OrderBy(Function(n) n.priority).ThenByDescending(Function(n) n.timestamp).ToList()

        For Each n As NOTE In notes
            Dim enc As New System.Text.UTF8Encoding()
            Dim RTFConvert As RichTextBox = New RichTextBox
            RTFConvert.Rtf = enc.GetString(System.Text.Encoding.Default.GetBytes(n.note))
            If RTFConvert.Text.Trim = "" Then
                Continue For
            ElseIf specialInstructions = "" Then
                specialInstructions = RTFConvert.Text
            ElseIf specialInstructions.EndsWith(".") Then
                specialInstructions += "  " + RTFConvert.Text
            Else
                specialInstructions += ".  " + RTFConvert.Text
            End If
        Next

        Dim Order = New Order With {
        .Service = "Distribution",
        .Vehicle = "Truck",
        .PickupAddress = New PickupAddress With {
            .Name = "Brands",
            .City = "Brooklyn",
            .State = "NY",
            .Zip = "11205",
            .Street = "Brooklyn Navy Yard Bldg. 27, 63 Flushing Avenue #106"
        },
        .DeliveryAddress = New DeliveryAddress With {
            .Name = shipping.ShipCo,
            .City = shipping.ShipCity,
            .State = shipping.ShipState,
            .Zip = shipping.ShipZip,
            .Street = shipping.ShipAddress,
            .Street2 = specialInstructions
        },
        .PickupTargetFrom = DateTime.Now.AddMinutes(10),
        .PickupTargetTo = DateTime.Now.AddHours(12),
        .DeliveryTargetFrom = DateTime.Now.AddMinutes(30),
        .DeliveryTargetTo = DateTime.Now.AddHours(15),
        .OrderPackageItems = New List(Of OrderPackageItem) From {
            New OrderPackageItem With {
                .RefNo = PackageBarCode,
                .PackageName = "Package"
                }
            }
        }
        Return Order
    End Function

    Private Sub DeliverByComboBox_SelectedIndexChanged(sender As Object, e As EventArgs) Handles DeliverByComboBox.SelectedIndexChanged
        DeliveryDataGridView.Columns(5).Visible = Not TicketEnt Is Nothing AndAlso TicketEnt.DeliverBy = "Columbus.O"
        DeliveryDataGridView.Columns(6).Visible = Not TicketEnt Is Nothing AndAlso TicketEnt.DeliverBy = "Columbus.O"
    End Sub

    Private Sub DeliveryDataGridView_DataBindingComplete(sender As Object, e As DataGridViewBindingCompleteEventArgs) Handles DeliveryDataGridView.DataBindingComplete
        If DeliverByComboBox.Text = "Columbus.O" Then
            For Each r As DataGridViewRow In DeliveryDataGridView.Rows
                If Not CType(r.DataBoundItem, Delivery).BarCodeVersion Is Nothing Then
                    r.DefaultCellStyle = New DataGridViewCellStyle With {.BackColor = Color.Yellow}
                End If
            Next
        End If
    End Sub

    Private Sub ddbNewTicket_Click(sender As Object, e As EventArgs) Handles ddbNewTicket.Click
        If TicketEnt.Deliveries.Count = 0 AndAlso Not TicketEnt.IsClosed Then
            If sender.Equals(menuForceNewTicket) Then
                Dim ticketInfo = $"ID: {TicketEnt.ID}, DeliverBy: {TicketEnt.DeliverBy}"

                If XtraMessageBox.Show($"There is already an open ticket for this user {vbCrLf}[{ticketInfo}]{vbCrLf}{vbCrLf}By continuing the above open ticket will be closed{vbCrLf}Continue?", "Cannot create new ticket when there is already an open ticket", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button3) = DialogResult.Yes Then
                    Dim email As New EmailService("<EMAIL>") With {.Subject = "Cannot create delivery ticket", .Body = $"There is already an open ticket for this user {vbCrLf}[{ticketInfo}]{vbCrLf}{vbCrLf}By continuing the above open ticket will be closed" + vbCrLf + "User: " + UserName}
                    email.ToEmail.Add("<EMAIL>")
                    email.SendEmail()

                    logger.Debug($"Cannot create delivery ticket with with existing open ticket.  Existing Ticket [{ticketInfo}, DeliverBy: {TicketEnt.DeliverBy}]", "Delivery ticket issue")

                    SwitchEnterNewTicket(True)
                Else
                    BarCodeTextBox.Select()
                End If
            Else
                BarCodeTextBox.Select()
            End If
        Else
            SwitchEnterNewTicket(True)
        End If
    End Sub
End Class