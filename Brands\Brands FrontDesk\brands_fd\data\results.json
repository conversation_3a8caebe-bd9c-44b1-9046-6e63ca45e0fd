results = 
{
  "settings": {
    "components": {
      "code": true,
      "binaries": false
    },
    "targetId": "",
    "targetDisplayName": ".NET 9.0"
  },
  "analysisStartTime": "2025-06-15T19:23:36.6074926Z",
  "analysisEndTime": "2025-06-15T19:24:02.0000575Z",
  "privacyMode": "Restricted",
  "privacyModeHelpUrl": "https://go.microsoft.com/fwlink/?linkid=2270980",
  "stats": {
    "summary": {
      "projects": 6,
      "issues": 8,
      "incidents": 497,
      "effort": 497
    },
    "charts": {
      "severity": {
        "Mandatory": 41,
        "Optional": 21,
        "Potential": 435,
        "Information": 0
      },
      "category": {
        "NuGet": 101,
        "Api": 390,
        "Project": 6
      }
    }
  },
  "projects": [
    {
      "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
      "startingProject": true,
      "issues": 7,
      "storyPoints": 95,
      "ruleInstances": [
        {
          "incidentId": "a53f11bc-7db7-4902-a4e2-f410b9222e0f",
          "ruleId": "NuGet.0001",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "DeltaCompressionDotNet",
              "PackageVersion": "2.0.0.0",
              "PackageNewVersion": "2.0.1",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "94d97ab5-57b4-4be6-bed9-8c82a7878c50",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.Connections.Abstractions",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "1a671dfa-b02b-47c3-a607-d85a8df0eaf1",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.Http.Connections.Client",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "4b99c1b1-d8e2-475c-be8d-000b38023b18",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.Http.Connections.Common",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "87a14db7-f8b1-4285-ac55-c1e5990f7488",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.Http.Features",
              "PackageVersion": "5.0.17",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f9dab12b-e06b-45d8-ba04-c1a436befc59",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.SignalR.Client",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "b3f66e2e-ee00-4992-a6e6-f9542fc335d9",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.SignalR.Client.Core",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "aedaebbf-f334-4de4-9c0f-f9ec6b38245a",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.SignalR.Common",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "3a9e3a43-f4a2-41c4-ae39-ae166ced8338",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.SignalR.Protocols.Json",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "2237a10a-df80-4a3e-8891-4f82001e5d40",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "c2bc83c3-3cb7-417d-8079-3696df98c13b",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Bcl.AsyncInterfaces",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "fba0005f-7616-4558-ab9c-7b27da2f7985",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Bcl.HashCode",
              "PackageVersion": "1.1.1",
              "PackageNewVersion": "6.0.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "769644fe-049d-48e5-b951-a7d86b7b0dbd",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Bcl.TimeProvider",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "008a1a62-159a-446b-814c-72eae87de290",
          "ruleId": "NuGet.0001",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Data.SqlClient.SNI",
              "PackageVersion": "2.1.1",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "7de68820-4459-490d-a650-c701c7f4177b",
          "ruleId": "NuGet.0001",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Diagnostics.Tracing.EventSource",
              "PackageVersion": "1.1.28",
              "PackageNewVersion": "1.0.24",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "aaa00723-3411-44cd-9bd0-3ab97a9e67bf",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Diagnostics.Tracing.EventSource.Redist",
              "PackageVersion": "2.2.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "9037c421-372f-476f-b1c3-3b74779439d0",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "274fde3a-371e-4d18-aa6a-49ed07dcbd9c",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.Abstractions",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "6fb26c4d-19d2-4f44-a27a-49995f1a57db",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.Analyzers",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "635e5389-786e-410b-84d8-9261d72f7784",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.Proxies",
              "PackageVersion": "3.1.16",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "24f87f4b-cba2-4c65-9961-d2f3d2283da9",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.Relational",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "0a4fa5c9-a639-4154-af85-9f55f3663d32",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.SqlServer",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "defe3a3f-43b2-43b2-bb0b-98a5f85e2fc7",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Caching.Abstractions",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "47636b43-94a2-4bac-9853-ccba9b7f589d",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Caching.Memory",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "a7a8eb0a-9924-4673-81f0-d96e180b5ada",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Configuration",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "5eecefb8-19f5-4147-a55d-89ab45770b4a",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Configuration.Abstractions",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "7a8f60d8-abdc-4de7-a58f-a248c7a44021",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Configuration.Binder",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "6880c79b-3ec2-439a-a363-aeade6b9ae51",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.DependencyInjection",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "4eaadb06-11ca-494f-a490-fe72c0244fff",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.DependencyInjection.Abstractions",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "0942358f-6879-46e2-bf84-4baf4344c98d",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Features",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "48697347-12a8-4e36-a16c-f5c64242bde7",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Http",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "c51a23b2-34bb-488d-96fb-a19fc3d3fe9c",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Logging",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "39157c47-ae68-4820-a7b0-63be6774750c",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Logging.Abstractions",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "16872b23-1220-404e-b783-9f54b3728df3",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Options",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "129b8480-56e7-48c0-81a6-8e34d97070b1",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Options.ConfigurationExtensions",
              "PackageVersion": "3.1.31",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "cb1609a1-05f9-440a-ae25-6b781098b2c0",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.Primitives",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "71dd07a6-3563-43b8-99e6-7fb03bf3b169",
          "ruleId": "NuGet.0001",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "PDFsharp",
              "PackageVersion": "1.50.5147",
              "PackageNewVersion": "6.2.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "06020821-6b70-42e7-8aad-c6e9d1142cbf",
          "ruleId": "NuGet.0001",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Pubnub",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": "7.3.13",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "e33f186c-2231-4c03-afd2-c6f8131fb277",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Buffers",
              "PackageVersion": "4.6.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "fabc03a0-df19-4826-8377-e6c0adb6b967",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Collections",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "df29e1a9-1319-4b80-a8ed-909ffa27e87e",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Collections.Immutable",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "c50b2937-60f4-4a34-aeb3-f2f02d247725",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.ComponentModel.Annotations",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "82ca6517-dab8-41c7-b471-201c1024b063",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Data.Common",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f64e9a5c-785a-47b1-9b83-7c432e65587b",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Diagnostics.Debug",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "088afcd6-33b6-4493-bbd1-47fe7427f92e",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Diagnostics.DiagnosticSource",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "d0ec9f8f-4389-43a8-8c7f-f58969a9e935",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Drawing.Primitives",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "d4568110-1b85-417d-99b9-644abbd706f0",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Globalization",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "55716f32-3984-4b91-8864-820d00f63711",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.IO",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "7b100dec-fc81-4240-a849-e042a5844971",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.IO.Pipelines",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "484b5325-ccac-469f-b911-5bd6c73c47c5",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Linq",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "b564a752-9261-41da-8fa1-034b675d6019",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Memory",
              "PackageVersion": "4.6.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "883c9237-2859-43f5-bfb0-49d7da39c596",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Memory.Data",
              "PackageVersion": "6.0.1",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "d0446a73-b397-44b7-ac5d-8d6e759273e4",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Net.Http",
              "PackageVersion": "4.3.4",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "fd6b1053-2377-4565-8257-ffa4d8c09831",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Net.Http.Json",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "beb9933d-fbb9-40a8-8567-cd97093796e9",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Net.Sockets",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "22e5e60f-4997-49ff-8b6a-f55c0e2be441",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Numerics.Vectors",
              "PackageVersion": "4.6.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f052cd92-7d1c-4267-8980-f117629da467",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Reflection",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "8f17fef0-deed-4448-8af0-61ca6012c4c9",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Reflection.Emit.Lightweight",
              "PackageVersion": "4.7.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "212a8a2a-c382-4224-9233-e7d0d5ec70e3",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Reflection.Extensions",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "4c569e7a-83c6-4c9b-8fa6-347fd87822fd",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Runtime",
              "PackageVersion": "4.3.1",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f251b23e-e153-4d4b-9256-061ac5ef5cc0",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Runtime.CompilerServices.Unsafe",
              "PackageVersion": "6.1.0",
              "PackageNewVersion": "6.1.2",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "49b1efea-8206-4b6d-9b42-36da89e75e02",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Runtime.Extensions",
              "PackageVersion": "4.3.1",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "5b10816d-f229-44be-a646-e18e373bf545",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Runtime.InteropServices.RuntimeInformation",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "9c0276bf-def0-4e02-8ec5-0405b7186b1f",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Security.Cryptography.Algorithms",
              "PackageVersion": "4.3.1",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "383258ec-ab18-4e69-9ca4-641ef0c3cb29",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Security.Cryptography.Encoding",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "3cdf8a92-7c1a-4f82-b69b-cbac52fd244b",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Security.Cryptography.Primitives",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "aa0a6300-fe1c-4c12-b288-27bbd08bb7dd",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Security.Cryptography.X509Certificates",
              "PackageVersion": "4.3.2",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "491f00cb-8cf2-46bb-afa8-6668396523a9",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Text.Encoding",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "337d1508-3e8a-4f2c-b332-700256725f3a",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Text.Encodings.Web",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "283f1d68-a9a7-4367-b85e-384e6ccaba6a",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Text.Json",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "07977935-fe94-4035-8bb3-2622e01e6b26",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Threading",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "c2687dc5-b8b7-4f46-9823-897e96d4926f",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Threading.Channels",
              "PackageVersion": "9.0.5",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "4ebf798e-30ba-41f0-8328-45e79dd447f7",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Threading.Tasks",
              "PackageVersion": "4.3.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "eda364de-4a52-404f-b39e-734fe6992a8c",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Threading.Tasks.Extensions",
              "PackageVersion": "4.5.4",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f34e120b-fb0c-403a-9a0b-ce106b86d33d",
          "ruleId": "NuGet.0003",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.ValueTuple",
              "PackageVersion": "4.5.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "a1cc2918-0015-4148-886f-2fb74e98a986",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Dynamsoft.DotNet.TWAIN",
              "PackageVersion": "8.3.3",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "2f525339-5ab5-47f1-af4c-f92800062f65",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.AspNetCore.Http.Features",
              "PackageVersion": "5.0.17",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f4592438-d7df-4157-9f5b-d5b73bc41d5c",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Data.SqlClient",
              "PackageVersion": "2.1.2",
              "PackageNewVersion": "6.0.2",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "1763e72d-1559-4cc9-99f5-37913995fee5",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Data.SqlClient",
              "PackageVersion": "2.1.2",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "3caeabda-d270-4d84-a5ad-e8b9e9a47b6c",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.Identity.Client",
              "PackageVersion": "4.34.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "c64fb8c9-893b-4b91-8e32-9b24644f4dc1",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.IdentityModel.JsonWebTokens",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": "8.12.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "a0ce7d95-3f01-436a-9bc2-19cbde4bc238",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.IdentityModel.JsonWebTokens",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "663d69c5-260f-4036-a6ec-fa56cad9c556",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.IdentityModel.Logging",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "682d7c7d-f788-448d-918f-fa215b6d6f88",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.IdentityModel.Protocols",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f070f256-02b0-4bbb-a4c9-7b0d7db85034",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.IdentityModel.Protocols.OpenIdConnect",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "164772ed-a701-4dc8-a714-a340d6140f8b",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Microsoft.IdentityModel.Tokens",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "6f4d13a8-8b0c-426b-82b5-5d9e89e9f3b9",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "Pubnub",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": "7.3.13",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "742de9c7-3a61-47ab-b82d-05f78222882f",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "RestSharp",
              "PackageVersion": "106.11.7",
              "PackageNewVersion": "112.1.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "5bce5cfd-4d06-4cb3-a158-b6bf90b43002",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Collections.Immutable",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "254a3287-f210-46f4-a12c-c27c99508c23",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Data.SqlClient",
              "PackageVersion": "4.8.2",
              "PackageNewVersion": "4.9.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "ae49b74b-c4fc-4458-8267-5c65ec1063ee",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.IdentityModel.Tokens.Jwt",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": "8.12.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "8a02bfbd-3831-4243-ad60-3404d980d4cb",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.IdentityModel.Tokens.Jwt",
              "PackageVersion": "6.12.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "2bf98fb2-b536-4e6e-9ad5-4ad12c2ad441",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj",
            "properties": {
              "PackageId": "System.Net.Http.Json",
              "PackageVersion": "5.0.0",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "308a21df-4367-4ead-8915-c88ff212efba",
          "ruleId": "Project.0001",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj"
          }
        },
        {
          "incidentId": "89811cce-b26f-40c6-95c1-75d8a8ca71d8",
          "ruleId": "Project.0002",
          "projectPath": "Brands FrontDesk\\Brands FrontDesk.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands FrontDesk\\Brands FrontDesk.vbproj"
          }
        }
      ]
    },
    {
      "path": "Brands.Core\\Brands.Core.csproj",
      "startingProject": false,
      "issues": 3,
      "storyPoints": 140,
      "ruleInstances": [
        {
          "incidentId": "d9c1f2fd-6cff-4743-916e-52bf00265fc8",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\Brands.Core.csproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "a91cbf25-84a1-45fa-8667-6ea6194115f4",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\Brands.Core.csproj",
            "properties": {
              "PackageId": "System.Data.SqlClient",
              "PackageVersion": "4.8.2",
              "PackageNewVersion": "4.9.0",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "bc442531-5bca-4d13-9fac-e206fafa5018",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 24,
            "column": 9
          }
        },
        {
          "incidentId": "daecc3a3-ab0c-45de-808f-ff432c02c4cf",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 24,
            "column": 9
          }
        },
        {
          "incidentId": "94028485-397b-4e78-b277-5c27758d291b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 21,
            "column": 9
          }
        },
        {
          "incidentId": "80e30f1f-96c9-4987-90c3-50c5b0c8ef65",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 21,
            "column": 9
          }
        },
        {
          "incidentId": "481c51e2-83b5-4b81-a585-298153cf537d",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 18,
            "column": 9
          }
        },
        {
          "incidentId": "113197ff-fc6f-46cd-bb5c-ad6b0aa6ba7b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 18,
            "column": 9
          }
        },
        {
          "incidentId": "ddbc7dbc-1fc6-4b9d-a914-9dc0917c088b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 15,
            "column": 9
          }
        },
        {
          "incidentId": "ae39e8be-6f12-4f49-98f6-6fd7385337f1",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 15,
            "column": 9
          }
        },
        {
          "incidentId": "7847561e-6a9f-48eb-b463-63545bae95df",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 12,
            "column": 9
          }
        },
        {
          "incidentId": "44b5db01-4369-4337-9e00-7904d213be2e",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 12,
            "column": 9
          }
        },
        {
          "incidentId": "e9f4a709-a9ba-4238-b371-c10b0a6b15ff",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "2b43fac3-814b-43e3-a24e-b1d1bb2aeed4",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "26247281-a564-4d89-b684-b0d73f9661fd",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 9
          }
        },
        {
          "incidentId": "c4603624-7e49-4e0e-b2fb-09c52177cb91",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 9
          }
        },
        {
          "incidentId": "ec81428b-eb46-44e0-84fe-3032d80e5dc9",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 64,
            "column": 9
          }
        },
        {
          "incidentId": "4b538830-8289-423b-95d2-f39fcd3131f2",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 64,
            "column": 9
          }
        },
        {
          "incidentId": "8eff0fd7-da81-4d7e-9283-7c21255cd528",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 61,
            "column": 9
          }
        },
        {
          "incidentId": "70e6b571-1383-4ef2-9651-c8225f757cb3",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 61,
            "column": 9
          }
        },
        {
          "incidentId": "ba7f24e3-b156-4004-bb39-3fceb5e483cd",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 58,
            "column": 9
          }
        },
        {
          "incidentId": "3f8b609b-d684-4b45-9764-1b4f8d4e9067",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 58,
            "column": 9
          }
        },
        {
          "incidentId": "383b02e6-d121-4415-8796-15cfeee7194b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 55,
            "column": 9
          }
        },
        {
          "incidentId": "4d6ee50c-e887-4420-84aa-77d6e439f1f9",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 55,
            "column": 9
          }
        },
        {
          "incidentId": "aeff947c-3331-4879-aa5a-6a7a760df4e1",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 52,
            "column": 9
          }
        },
        {
          "incidentId": "2207f34c-9f26-43c8-9664-131c2bde6828",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 52,
            "column": 9
          }
        },
        {
          "incidentId": "517d4aed-8c98-4c05-bd85-ca091d59ba46",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 49,
            "column": 9
          }
        },
        {
          "incidentId": "b5e795d1-02be-4eac-b734-5e355c5e3750",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 49,
            "column": 9
          }
        },
        {
          "incidentId": "c21c955e-c135-4646-8dc2-18d0de824eb5",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 46,
            "column": 9
          }
        },
        {
          "incidentId": "4be05c56-7444-411a-8596-cbd4db1bd082",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 46,
            "column": 9
          }
        },
        {
          "incidentId": "6ede7c71-c1e6-4cf8-afbe-5e5987901099",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 43,
            "column": 9
          }
        },
        {
          "incidentId": "315f8b2e-bd35-4940-b5b7-8363620aa7ce",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 43,
            "column": 9
          }
        },
        {
          "incidentId": "d932ef5b-afea-42df-b057-8ba8879237f9",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 40,
            "column": 9
          }
        },
        {
          "incidentId": "b74b218c-1c8f-4803-83ae-182a16446064",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 40,
            "column": 9
          }
        },
        {
          "incidentId": "560d14f3-a72b-414a-81d8-260b1d9493fa",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 9
          }
        },
        {
          "incidentId": "14720b58-91ea-450f-8d1e-d82134cdf8dc",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 9
          }
        },
        {
          "incidentId": "076f9561-7589-4eca-a7c9-b13d339e49c4",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 34,
            "column": 9
          }
        },
        {
          "incidentId": "b888fd08-6bdb-46ca-8264-5454c7e320df",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 34,
            "column": 9
          }
        },
        {
          "incidentId": "41046119-5b5c-48e6-9e04-08ae25f387a1",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 31,
            "column": 9
          }
        },
        {
          "incidentId": "0c91162d-10fa-41a8-b351-a97ab1ccb71c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 31,
            "column": 9
          }
        },
        {
          "incidentId": "49a30fe9-d415-4dce-bd3c-5f0dcb6045fd",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 28,
            "column": 9
          }
        },
        {
          "incidentId": "da741087-32e6-4315-ac2f-396096e7a105",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 28,
            "column": 9
          }
        },
        {
          "incidentId": "8b8d565a-36f9-4eec-9a95-b9ab3fd90a9a",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 25,
            "column": 9
          }
        },
        {
          "incidentId": "6814ac16-5594-41bc-9289-d7679873ff53",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 25,
            "column": 9
          }
        },
        {
          "incidentId": "719d4168-421f-4921-838f-5c91223bd5bc",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 22,
            "column": 9
          }
        },
        {
          "incidentId": "9c2f8f7d-0baf-428d-9cf7-a66bd9e7250b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 22,
            "column": 9
          }
        },
        {
          "incidentId": "44e73194-fbd3-4f78-a1a6-b1f7a4337c7f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 9
          }
        },
        {
          "incidentId": "3c269167-b9e1-401b-b4bf-46891ab10e6f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 9
          }
        },
        {
          "incidentId": "bd82f1a6-3727-4af0-9f32-bd8ce97383bd",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 16,
            "column": 9
          }
        },
        {
          "incidentId": "0db58cd8-280f-4f01-85fe-e157d3bc20cf",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 16,
            "column": 9
          }
        },
        {
          "incidentId": "ddbb24c5-9b32-4994-ba57-226f4ec095ab",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 9
          }
        },
        {
          "incidentId": "48f5b169-a4ef-430d-8008-be4540a0445c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 9
          }
        },
        {
          "incidentId": "bb90399d-f548-4fc1-9d96-0f8fa09176f8",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "0b0f708b-12b7-4c39-ad61-e9e6ad6e2c0b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\TicketField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "098ab2b6-f778-4600-8769-45ead048af5c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\IndividualTicketFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "c145da0d-60a8-4110-8a04-62b25d65ab50",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\IndividualTicketFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "3570c4ed-d1ab-4ba8-b188-9a488617e4e5",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\IndividualTicketFieldOptionResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "d25ee404-c809-48d2-bb30-c6c91ad4b946",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\IndividualTicketFieldOptionResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "d7d03125-b477-4644-8cdc-04bbb953c7ef",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\GroupTicketFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "ee0b4ec0-e59c-47cb-a8aa-1422d5257491",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\GroupTicketFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "1a4fb147-b748-407c-b8e2-11448745dbd8",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\GroupTicketFieldOptionResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "c1822c9d-1ad8-4339-8263-40a1c9fd1409",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\TicketFields\\GroupTicketFieldOptionResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "0716acc3-e27c-407c-bab9-f8bcf47f8b92",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 17,
            "column": 9
          }
        },
        {
          "incidentId": "b9ffe785-96e1-4617-9e34-6d8704fcc722",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 17,
            "column": 9
          }
        },
        {
          "incidentId": "d18ee215-b6f4-43c4-8011-7b42b98fe710",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 14,
            "column": 9
          }
        },
        {
          "incidentId": "ad9053c2-d6d6-491f-a4dd-c5c34201706c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 14,
            "column": 9
          }
        },
        {
          "incidentId": "bb5d3ca1-1ba9-4022-95e2-82d5ede90cdb",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 11,
            "column": 9
          }
        },
        {
          "incidentId": "9b9680ed-5503-4cf2-bd49-faa8c85b3e31",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationFieldOption.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 11,
            "column": 9
          }
        },
        {
          "incidentId": "f2b39cf7-b464-4d22-b11c-ba46c99c8fca",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 43,
            "column": 9
          }
        },
        {
          "incidentId": "8f63b764-67cd-444c-b913-73037c01a444",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 43,
            "column": 9
          }
        },
        {
          "incidentId": "8b040774-2a72-4425-9f66-a3120be7cce0",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 40,
            "column": 9
          }
        },
        {
          "incidentId": "172f2fb8-4906-4d84-b847-fddfa8a6d904",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 40,
            "column": 9
          }
        },
        {
          "incidentId": "e3b9e44b-fe6e-4b36-ab9c-6f2906e83e2c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 9
          }
        },
        {
          "incidentId": "b104ad5d-1164-4274-be6a-5bd925c806a3",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 9
          }
        },
        {
          "incidentId": "73aff75f-0db3-4634-9979-029b5d597580",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 34,
            "column": 9
          }
        },
        {
          "incidentId": "d04f25f8-d1ab-4987-968a-cab91623180f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 34,
            "column": 9
          }
        },
        {
          "incidentId": "b31ed4b2-32e2-4697-8c93-f8d9cf0b2eb7",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 31,
            "column": 9
          }
        },
        {
          "incidentId": "a6a796d8-1046-4d44-bf6f-26ffa98b6ce0",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 31,
            "column": 9
          }
        },
        {
          "incidentId": "f9625154-cbbe-4de5-874f-4bde94fc21c8",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 28,
            "column": 9
          }
        },
        {
          "incidentId": "583b96cc-cb46-4f58-aabb-0b1a33588d9c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 28,
            "column": 9
          }
        },
        {
          "incidentId": "fd7eae67-aa0a-4249-a7df-ae7c20029025",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 25,
            "column": 9
          }
        },
        {
          "incidentId": "f36a9115-deeb-4453-8560-e5421f53396f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 25,
            "column": 9
          }
        },
        {
          "incidentId": "f48f02cf-79e4-43e4-8ca0-e3edfa4d0fed",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 22,
            "column": 9
          }
        },
        {
          "incidentId": "4197b2b9-82f3-4d2a-8b67-a692e6c7586f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 22,
            "column": 9
          }
        },
        {
          "incidentId": "0ef783fa-c1ac-4e01-94bd-0e9c733ec8b9",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 9
          }
        },
        {
          "incidentId": "b258ab56-1b17-4cf0-ab3f-75f62041bf2a",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 9
          }
        },
        {
          "incidentId": "441c67a5-9f03-40a8-9365-f95b5f515cd8",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 16,
            "column": 9
          }
        },
        {
          "incidentId": "5eed30c7-7877-470d-b0e0-deee181ded5e",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 16,
            "column": 9
          }
        },
        {
          "incidentId": "0545eeef-4738-4474-af21-763bf6bf84c4",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 9
          }
        },
        {
          "incidentId": "5adfaac3-baac-4a4b-a662-0a43d6e1f99c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 9
          }
        },
        {
          "incidentId": "5e567ac1-de2c-40d3-89ce-ab3d9abeb44c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "003b4b46-8906-4cb9-8761-b45148fcd7ab",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\OrganizationField.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "907126c3-88d4-4ad4-adf7-818cd118141b",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\IndividualOrganizationFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "acdc4056-96d5-4bc7-9dfc-5122ce0540d2",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\IndividualOrganizationFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 9,
            "column": 9
          }
        },
        {
          "incidentId": "cd7cf6ff-47da-4344-9bdb-faff8b93898e",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\GroupOrganizationFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "412c3750-906b-4372-8856-fcac0748e9d6",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\Models\\OrganizationFields\\GroupOrganizationFieldResponse.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 10,
            "column": 9
          }
        },
        {
          "incidentId": "5302cc1b-9da9-4436-aa0f-15ed63cf1400",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\UpdateTicketService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.entityframeworkqueryableextensions",
                "isCustom": false
              }
            ],
            "line": 135,
            "column": 12
          }
        },
        {
          "incidentId": "c28dcf7a-af28-4e7f-969a-0bbeffb61c95",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ZendeskServices\\UpdateCustomFields.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.entityframeworkqueryableextensions",
                "isCustom": false
              }
            ],
            "line": 86,
            "column": 12
          }
        },
        {
          "incidentId": "7c098479-4313-4855-b680-05f0220e4fa0",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 140,
            "column": 16
          }
        },
        {
          "incidentId": "9753d823-8d34-4b6e-96ce-1a250606b3c0",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.deserializeobject",
                "isCustom": false
              }
            ],
            "line": 140,
            "column": 16
          }
        },
        {
          "incidentId": "a56bb936-cfca-465d-acd4-742b1564a58d",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 106,
            "column": 12
          }
        },
        {
          "incidentId": "e4e76fec-1fea-4147-9046-6ce8ec41cd24",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.deserializeobject",
                "isCustom": false
              }
            ],
            "line": 106,
            "column": 12
          }
        },
        {
          "incidentId": "b1ca880f-d1f4-40c8-bbe8-0df3d1fd2e61",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.AspNet.WebApi.Client, 6.0.0.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.net.http.httpclientextensions",
                "isCustom": false
              }
            ],
            "line": 92,
            "column": 20
          }
        },
        {
          "incidentId": "6893a74f-e52e-4eb9-9f36-dd74d4f8ce5d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.AspNet.WebApi.Client, 6.0.0.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.net.http.httpclientextensions",
                "isCustom": false
              }
            ],
            "line": 79,
            "column": 12
          }
        },
        {
          "incidentId": "13ddfc92-a59f-4e5b-9d17-a9944076c2da",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.AspNet.WebApi.Client, 6.0.0.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.net.http.httpcontentextensions",
                "isCustom": false
              }
            ],
            "line": 42,
            "column": 12
          }
        },
        {
          "incidentId": "0468b48a-3ce2-49af-92e7-73a605567154",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.AspNet.WebApi.Client, 6.0.0.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\ColumbusApi\\ColumbusAPI.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.net.http.httpcontentextensions.readasasync",
                "isCustom": false
              }
            ],
            "line": 42,
            "column": 12
          }
        },
        {
          "incidentId": "7e93fe52-4823-4528-a600-7d3b730e967e",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\Models\\FormSubmissions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.linq.jtoken.toobject",
                "isCustom": false
              }
            ],
            "line": 45,
            "column": 20
          }
        },
        {
          "incidentId": "3a59cfa4-6bf2-4ff8-85a0-6645726a1bb2",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\Models\\FormSubmissions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 9
          }
        },
        {
          "incidentId": "13f64f4a-d1fc-4e81-ad38-0c5f6eb901dd",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\Models\\FormSubmissions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonpropertyattribute",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 9
          }
        },
        {
          "incidentId": "435c6d5c-4df2-4ba2-a9c6-31703130215a",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 113,
            "column": 8
          }
        },
        {
          "incidentId": "359141a5-cc68-4aca-b2bc-7fbcb8491a8d",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 145,
            "column": 24
          }
        },
        {
          "incidentId": "50fbc5bb-5927-4c7c-bdc4-fe46ecb40e59",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 145,
            "column": 24
          }
        },
        {
          "incidentId": "785f5aa9-d6e4-4844-8dfb-26a45fb537eb",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 140,
            "column": 24
          }
        },
        {
          "incidentId": "cd145aa8-d728-437f-b595-1ebdca86e739",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 140,
            "column": 24
          }
        },
        {
          "incidentId": "3cc82443-c79d-4982-b26a-29370eb40dda",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 134,
            "column": 20
          }
        },
        {
          "incidentId": "17f27ff6-cb1d-45be-8a0e-08e11d9ed222",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 134,
            "column": 20
          }
        },
        {
          "incidentId": "e253f879-ff71-42eb-be93-bc83528f4d2e",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 117,
            "column": 16
          }
        },
        {
          "incidentId": "481dc184-191e-44f9-807d-7f224ea93154",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 117,
            "column": 16
          }
        },
        {
          "incidentId": "********-74db-4a34-a542-a6d456aa0361",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlcommand.executenonquery",
                "isCustom": false
              }
            ],
            "line": 97,
            "column": 16
          }
        },
        {
          "incidentId": "cbf3c961-cd0f-4164-bffa-6e90f5eba3d3",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlconnection.open",
                "isCustom": false
              }
            ],
            "line": 96,
            "column": 16
          }
        },
        {
          "incidentId": "e454963e-ef10-4da7-8e75-a54775b3009a",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparametercollection",
                "isCustom": false
              }
            ],
            "line": 94,
            "column": 20
          }
        },
        {
          "incidentId": "d0947a71-d7f6-436c-8db3-4a2072fedb84",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlcommand.parameters",
                "isCustom": false
              }
            ],
            "line": 94,
            "column": 20
          }
        },
        {
          "incidentId": "8cd307e7-7fb0-4d4d-9848-9d4ed0c91ca7",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparametercollection.addrange",
                "isCustom": false
              }
            ],
            "line": 94,
            "column": 20
          }
        },
        {
          "incidentId": "cb5eedaf-177d-4d85-9044-a7a7ffc33a07",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlcommand.commandtimeout",
                "isCustom": false
              }
            ],
            "line": 91,
            "column": 16
          }
        },
        {
          "incidentId": "2f233bac-b4aa-46fe-b08a-6f0608ee0e33",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 90,
            "column": 16
          }
        },
        {
          "incidentId": "7dbed005-b9b6-4340-8997-7a71b790c068",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlcommand.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 90,
            "column": 16
          }
        },
        {
          "incidentId": "db5edff5-3dcb-4d71-b774-e64775ccaa1e",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlconnection",
                "isCustom": false
              }
            ],
            "line": 87,
            "column": 12
          }
        },
        {
          "incidentId": "6a822b1e-4714-4029-a8d6-7178f935d9a9",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlconnection.sqlconnection",
                "isCustom": false
              }
            ],
            "line": 87,
            "column": 12
          }
        },
        {
          "incidentId": "ba65996b-afbe-42fc-8c44-1912149242ce",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 16
          }
        },
        {
          "incidentId": "e18cdfcb-6b1d-4577-a890-2f6f47338527",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 64,
            "column": 12
          }
        },
        {
          "incidentId": "c5326d01-0fee-4c77-9f78-b7fffc426ed0",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 56,
            "column": 12
          }
        },
        {
          "incidentId": "b849bbfb-9a9d-4585-aa12-2eab66c8c7a6",
          "ruleId": "Api.0002",
          "description": "API is available in package System.Data.SqlClient, 4.8.6.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/system.data.sqlclient.sqlparameter.sqlparameter",
                "isCustom": false
              }
            ],
            "line": 56,
            "column": 12
          }
        },
        {
          "incidentId": "c0039f00-b7a3-40bc-b785-2653399ceb09",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\AccountLeads\\AccountLeadsApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.linq.jtoken.toobject",
                "isCustom": false
              }
            ],
            "line": 28,
            "column": 12
          }
        },
        {
          "incidentId": "3df54fb5-2958-4ac2-8352-96e280bb5fd8",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 16
          }
        },
        {
          "incidentId": "cfaf3f92-e990-41fe-92d9-f34102bb7ee3",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 16
          }
        },
        {
          "incidentId": "06dd3913-46dc-4a67-9898-829ca9270171",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting.indented",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 16
          }
        },
        {
          "incidentId": "05715fac-84fe-4fbe-8b4d-289d9da62a1f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 16
          }
        },
        {
          "incidentId": "6e0afb49-3fb4-41f1-a256-025ac70f3615",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.serializeobject",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 16
          }
        },
        {
          "incidentId": "e018420c-1738-4fd3-97d3-823fc6964713",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 212,
            "column": 16
          }
        },
        {
          "incidentId": "d13d9aa2-e75c-4841-a824-449b4f2da760",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "Brands.Core\\Brands.Core.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.Core\\TelebroadApi.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.deserializeobject",
                "isCustom": false
              }
            ],
            "line": 212,
            "column": 16
          }
        }
      ]
    },
    {
      "path": "Brands.DAL\\Brands.DAL.csproj",
      "startingProject": false,
      "issues": 4,
      "storyPoints": 226,
      "ruleInstances": [
        {
          "incidentId": "7737f9fa-0ea3-449b-af40-9db459bebde5",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Brands.DAL.csproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.Abstractions",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "2391b075-9940-4ed9-95b1-e1ad2aa4f9d4",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Brands.DAL.csproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore.SqlServer",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "774553ea-b626-4bbe-afd4-266fe7c14250",
          "ruleId": "NuGet.0002",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Brands.DAL.csproj",
            "properties": {
              "PackageId": "Microsoft.Extensions.DependencyInjection.Abstractions",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "4828776c-f1d9-48f6-b874-b0dd37c18723",
          "ruleId": "NuGet.0004",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Brands.DAL.csproj",
            "properties": {
              "PackageId": "Microsoft.Data.SqlClient",
              "PackageVersion": "2.0.1",
              "PackageNewVersion": "6.0.2",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "7c3b2e3e-0657-46ce-9fba-ab3852932e56",
          "ruleId": "NuGet.0005",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Brands.DAL.csproj",
            "properties": {
              "PackageId": "Microsoft.Data.SqlClient",
              "PackageVersion": "2.0.1",
              "PackageNewVersion": null,
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "002bd725-bed7-40a4-a748-8cc8b8cb7cc5",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Mapping\\UdfMapping.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 16,
            "column": 12
          }
        },
        {
          "incidentId": "bdabb4a9-2e51-471d-981a-1d498a5fb4f0",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Mapping\\UdfMapping.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.relationalentitytypebuilderextensions",
                "isCustom": false
              }
            ],
            "line": 14,
            "column": 12
          }
        },
        {
          "incidentId": "5cc05d2c-3e61-4cd7-babc-3dded96d9515",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 75,
            "column": 20
          }
        },
        {
          "incidentId": "04028c68-ddc7-42d2-95e9-e0e215910a7b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection",
                "isCustom": false
              }
            ],
            "line": 75,
            "column": 20
          }
        },
        {
          "incidentId": "7d8066c0-91e9-4128-bb91-fe338f3e3dda",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.parameters",
                "isCustom": false
              }
            ],
            "line": 75,
            "column": 20
          }
        },
        {
          "incidentId": "b6f9abca-ecb6-484b-b7ff-85d990fe513f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection.clear",
                "isCustom": false
              }
            ],
            "line": 75,
            "column": 20
          }
        },
        {
          "incidentId": "05da0b2d-3987-4379-bb6a-b2b45ff42e61",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 69,
            "column": 20
          }
        },
        {
          "incidentId": "4fd42737-6a24-4cbe-a825-e5cd2e2784cd",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.executereaderasync",
                "isCustom": false
              }
            ],
            "line": 69,
            "column": 20
          }
        },
        {
          "incidentId": "c86ab5ae-c4ef-494f-9e7e-34559644e084",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlconnection.openasync",
                "isCustom": false
              }
            ],
            "line": 68,
            "column": 20
          }
        },
        {
          "incidentId": "09caf1b8-6fc3-4f76-8f6e-524b7a3c4cdf",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 24
          }
        },
        {
          "incidentId": "9eb1397f-946a-404e-928d-f4bd0957b2de",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 24
          }
        },
        {
          "incidentId": "26e60d9f-6e23-44ae-a326-e292b03a09e8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.parameters",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 24
          }
        },
        {
          "incidentId": "a981480a-ff50-40ee-ab67-9dd3eb55617a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection.addrange",
                "isCustom": false
              }
            ],
            "line": 67,
            "column": 24
          }
        },
        {
          "incidentId": "ab3aac8c-0ec0-4195-acaf-eade1e43aea8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.commandtimeout",
                "isCustom": false
              }
            ],
            "line": 65,
            "column": 20
          }
        },
        {
          "incidentId": "00f48691-85ea-48b9-a4e4-44a6e444a433",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 65,
            "column": 20
          }
        },
        {
          "incidentId": "a94347df-0426-4844-9d91-ccfdfadf6aab",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 65,
            "column": 20
          }
        },
        {
          "incidentId": "a25c2c23-b8de-40b8-b52e-72f3bb6f73f2",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 65,
            "column": 20
          }
        },
        {
          "incidentId": "8eb837dd-1ff1-4f06-b1a6-4eed4bf03ea8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlconnection",
                "isCustom": false
              }
            ],
            "line": 61,
            "column": 12
          }
        },
        {
          "incidentId": "59b64fae-b0b8-42d1-9244-e5678c84dd7b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlconnection.sqlconnection",
                "isCustom": false
              }
            ],
            "line": 61,
            "column": 12
          }
        },
        {
          "incidentId": "ebe7650a-8bf3-4010-8cf2-6fbe28682227",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparameter.value",
                "isCustom": false
              }
            ],
            "line": 58,
            "column": 20
          }
        },
        {
          "incidentId": "ad3ee76f-9cfc-46c3-8843-5946c4b57314",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparameter.value",
                "isCustom": false
              }
            ],
            "line": 57,
            "column": 16
          }
        },
        {
          "incidentId": "f73efd99-169a-422d-a23b-2a7357ce2ffa",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 42,
            "column": 20
          }
        },
        {
          "incidentId": "8196463d-291e-485b-a76d-76044edf00b0",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection",
                "isCustom": false
              }
            ],
            "line": 42,
            "column": 20
          }
        },
        {
          "incidentId": "db81e336-c8ec-4aac-83f8-cbd54a050993",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.parameters",
                "isCustom": false
              }
            ],
            "line": 42,
            "column": 20
          }
        },
        {
          "incidentId": "ab0e44a7-fcb6-40e4-bced-90389924e484",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection.clear",
                "isCustom": false
              }
            ],
            "line": 42,
            "column": 20
          }
        },
        {
          "incidentId": "d3399925-b30a-4f13-a601-2e51930bc11e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 20
          }
        },
        {
          "incidentId": "5b6a6af1-bb90-4353-96c1-b55f7b4d5904",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqldatareader",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 20
          }
        },
        {
          "incidentId": "fbe009a7-58a9-413c-b571-e1dcf1f64dd9",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.executereader",
                "isCustom": false
              }
            ],
            "line": 37,
            "column": 20
          }
        },
        {
          "incidentId": "146613b1-cdc2-4c69-bd88-da97175aa6b9",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlconnection.open",
                "isCustom": false
              }
            ],
            "line": 36,
            "column": 20
          }
        },
        {
          "incidentId": "ee2729e4-e2e7-4ec2-a9c9-ef0a95bd560e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 35,
            "column": 24
          }
        },
        {
          "incidentId": "16716cae-c52b-4726-9d66-391a398961e0",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection",
                "isCustom": false
              }
            ],
            "line": 35,
            "column": 24
          }
        },
        {
          "incidentId": "2f35de07-8225-4723-b71b-72ca43262b6a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.parameters",
                "isCustom": false
              }
            ],
            "line": 35,
            "column": 24
          }
        },
        {
          "incidentId": "9e37ad19-9b30-42ef-926d-ba0de7134ac5",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparametercollection.addrange",
                "isCustom": false
              }
            ],
            "line": 35,
            "column": 24
          }
        },
        {
          "incidentId": "9768c0f2-dac7-42e1-9b04-b8fb4240324d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.commandtimeout",
                "isCustom": false
              }
            ],
            "line": 33,
            "column": 20
          }
        },
        {
          "incidentId": "30743da8-d04a-40f6-9832-94d41fc4c4eb",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 33,
            "column": 20
          }
        },
        {
          "incidentId": "05000a0b-d445-4d49-9f7a-10afa9cf8a70",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 33,
            "column": 20
          }
        },
        {
          "incidentId": "cd8ad963-d45e-41df-bd7f-2faf0486350d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 33,
            "column": 20
          }
        },
        {
          "incidentId": "562d7500-6d08-449b-abb9-7b53b3fe1cad",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlconnection",
                "isCustom": false
              }
            ],
            "line": 29,
            "column": 12
          }
        },
        {
          "incidentId": "cda2a084-8bc1-4afa-9067-0e1b7dcb501f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlconnection.sqlconnection",
                "isCustom": false
              }
            ],
            "line": 29,
            "column": 12
          }
        },
        {
          "incidentId": "c14c452e-3d55-4a8a-938b-61498b3287b7",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparameter.value",
                "isCustom": false
              }
            ],
            "line": 26,
            "column": 20
          }
        },
        {
          "incidentId": "82997db3-88da-47b1-aa32-be445bf2f13d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlparameter.value",
                "isCustom": false
              }
            ],
            "line": 25,
            "column": 16
          }
        },
        {
          "incidentId": "c6c5acc5-dd65-4361-aa05-57733f968f8d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 14,
            "column": 44
          }
        },
        {
          "incidentId": "c666fee9-7aaa-4945-a682-74570c898118",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 14,
            "column": 39
          }
        },
        {
          "incidentId": "d11f42c0-d907-47e8-ad67-b5c2e955fa31",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.Data.SqlClient, 5.2.1.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\SqlService.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.data.sqlclient.sqlcommand",
                "isCustom": false
              }
            ],
            "line": 14,
            "column": 8
          }
        },
        {
          "incidentId": "708fd81f-9588-49d0-99d6-bca48ca347c2",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 83,
            "column": 19
          }
        },
        {
          "incidentId": "197ebaf6-5c41-423e-ad49-a16e7a41678a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.lazyloaderextensions",
                "isCustom": false
              }
            ],
            "line": 83,
            "column": 19
          }
        },
        {
          "incidentId": "bc58ca37-f0a1-49ce-915d-66976ae20651",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 76,
            "column": 19
          }
        },
        {
          "incidentId": "3dbaa13d-f5bc-4564-99a9-3760b2d28f56",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.lazyloaderextensions",
                "isCustom": false
              }
            ],
            "line": 76,
            "column": 19
          }
        },
        {
          "incidentId": "a3cd85d9-ac47-4a5b-9f8f-b9fef3737f33",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 69,
            "column": 19
          }
        },
        {
          "incidentId": "77b7b448-d118-4ac5-a61a-1520935b1519",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.lazyloaderextensions",
                "isCustom": false
              }
            ],
            "line": 69,
            "column": 19
          }
        },
        {
          "incidentId": "a4a7e764-fab5-4b8a-b715-7a7ceba6f466",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 62,
            "column": 19
          }
        },
        {
          "incidentId": "670e278d-8d39-4ce9-b951-1781d36a4d5c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.lazyloaderextensions",
                "isCustom": false
              }
            ],
            "line": 62,
            "column": 19
          }
        },
        {
          "incidentId": "eb0c995b-c5e0-48f8-8152-a9fc7d10cd63",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 53,
            "column": 19
          }
        },
        {
          "incidentId": "4f2dea91-0678-47f6-a6a9-fe4d1b135646",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.lazyloaderextensions",
                "isCustom": false
              }
            ],
            "line": 53,
            "column": 19
          }
        },
        {
          "incidentId": "00868af1-3f36-4056-b152-9f292ffe56f3",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 21,
            "column": 8
          }
        },
        {
          "incidentId": "10be56cd-6edc-4ed5-a2bb-785c4e0519f1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 21,
            "column": 55
          }
        },
        {
          "incidentId": "145666c8-dce4-4c02-8fb5-c15c1ca5d0ba",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 37
          }
        },
        {
          "incidentId": "5f6012d6-c0a3-448e-8e70-078efacd4e66",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 21,
            "column": 8
          }
        },
        {
          "incidentId": "d952af21-eda5-4378-873e-1ff32ac42e90",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\QueueDetail.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 21,
            "column": 55
          }
        },
        {
          "incidentId": "c0346e6f-1c32-4d90-adf6-bdf8b739223a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 46,
            "column": 18
          }
        },
        {
          "incidentId": "10cf9b9d-60f6-48ca-9ca0-7db72b2c38b3",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.lazyloaderextensions",
                "isCustom": false
              }
            ],
            "line": 46,
            "column": 18
          }
        },
        {
          "incidentId": "58e5f030-2419-49d7-9b1c-cbf568972f15",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 8
          }
        },
        {
          "incidentId": "68689195-b026-44f5-a353-de813e733d6f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 49
          }
        },
        {
          "incidentId": "bca6a14b-5d81-4e36-a175-7f8b852d1c26",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 17,
            "column": 37
          }
        },
        {
          "incidentId": "7eb8c4f5-8f4a-4997-9010-f5f7818ccb0c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 8
          }
        },
        {
          "incidentId": "c6fdae51-bdb4-4f62-ae12-84aa5df74fc1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Abstractions, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\Queue.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.infrastructure.ilazyloader",
                "isCustom": false
              }
            ],
            "line": 19,
            "column": 49
          }
        },
        {
          "incidentId": "91428bd6-154c-4f97-b2d3-124cff7d7698",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessor.process",
                "isCustom": false
              }
            ],
            "line": 319,
            "column": 12
          }
        },
        {
          "incidentId": "bb302dea-80f2-482d-b7fa-834ed07fca3e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querycompilationcontext",
                "isCustom": false
              }
            ],
            "line": 308,
            "column": 8
          }
        },
        {
          "incidentId": "7e953cc1-e313-4f37-bc10-183c880d341a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 308,
            "column": 8
          }
        },
        {
          "incidentId": "3a073d92-5e25-43a5-875b-9ede7e8c8dc1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 308,
            "column": 8
          }
        },
        {
          "incidentId": "8c36b751-82a3-4159-bac8-f9e3ec05aab2",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessor.relationalquerytranslationpreprocessor",
                "isCustom": false
              }
            ],
            "line": 312,
            "column": 12
          }
        },
        {
          "incidentId": "83c5b7ba-8c50-40e5-89e7-0fa9b13ea834",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querycompilationcontext",
                "isCustom": false
              }
            ],
            "line": 308,
            "column": 8
          }
        },
        {
          "incidentId": "ce347426-245b-4bc5-a380-19c6172fa0c0",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 308,
            "column": 8
          }
        },
        {
          "incidentId": "434372e6-bb42-4b8f-bcbc-8d2f1a54a5c8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 308,
            "column": 8
          }
        },
        {
          "incidentId": "23663d87-54fa-4d95-a64f-48fb1e37f038",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessor.relationalquerytranslationpreprocessor",
                "isCustom": false
              }
            ],
            "line": 312,
            "column": 12
          }
        },
        {
          "incidentId": "22de83b9-cd0e-4d8f-b5d6-6325d3d401e5",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessor",
                "isCustom": false
              }
            ],
            "line": 306,
            "column": 69
          }
        },
        {
          "incidentId": "151179a9-f988-40e9-a0f8-c90bb29664a5",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querycompilationcontext",
                "isCustom": false
              }
            ],
            "line": 302,
            "column": 8
          }
        },
        {
          "incidentId": "280db2d6-218e-446b-ac5f-426bf55b6720",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessor",
                "isCustom": false
              }
            ],
            "line": 302,
            "column": 8
          }
        },
        {
          "incidentId": "d47e04f9-cb87-4a6e-b476-68d4a7f83627",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 303,
            "column": 84
          }
        },
        {
          "incidentId": "73ae3bc3-d043-44bc-be74-802555152e1d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 303,
            "column": 69
          }
        },
        {
          "incidentId": "0b981547-c2ce-415c-ae59-8e1d40d8229e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 294,
            "column": 8
          }
        },
        {
          "incidentId": "60eb6dd3-f78e-40e9-9b3c-b6f8779eca17",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 294,
            "column": 8
          }
        },
        {
          "incidentId": "0a75eebd-1cd7-430a-90bb-7d824cc50921",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 299,
            "column": 12
          }
        },
        {
          "incidentId": "b684fa23-38c3-472f-952e-16db81ecf580",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 298,
            "column": 12
          }
        },
        {
          "incidentId": "48cfea0b-6377-4ddd-80e2-e0e0d59e8b59",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 292,
            "column": 76
          }
        },
        {
          "incidentId": "a84b49e6-29cb-4f8d-9cd1-1f07e110729b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 291,
            "column": 66
          }
        },
        {
          "incidentId": "ab48fad8-6a83-4d16-ba8a-dc5042871463",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 294,
            "column": 8
          }
        },
        {
          "incidentId": "9035fa1c-f0bd-4d3e-8a1a-09d091cea15d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 294,
            "column": 8
          }
        },
        {
          "incidentId": "bfddb2f1-4efa-460c-bfd0-b21eadfab1df",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.relationalquerytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 299,
            "column": 12
          }
        },
        {
          "incidentId": "dc4acaff-d242-4103-8ea5-dfe941fbe82c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.querytranslationpreprocessordependencies",
                "isCustom": false
              }
            ],
            "line": 298,
            "column": 12
          }
        },
        {
          "incidentId": "a970411f-e3d7-4039-bf56-824e70b826bd",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.query.iquerytranslationpreprocessorfactory",
                "isCustom": false
              }
            ],
            "line": 289,
            "column": 76
          }
        },
        {
          "incidentId": "cb6f4e44-d0c5-4d4d-8f80-c24863355844",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.dispose",
                "isCustom": false
              }
            ],
            "line": 285,
            "column": 12
          }
        },
        {
          "incidentId": "43f233d2-7c4d-4c46-97fe-e3a4f71a2e7f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder",
                "isCustom": false
              }
            ],
            "line": 150,
            "column": 8
          }
        },
        {
          "incidentId": "89beb9eb-73f4-461a-a1a1-55c923d68cd5",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 247,
            "column": 12
          }
        },
        {
          "incidentId": "18dc8ce9-78f5-4178-9260-bd79a930f424",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 247,
            "column": 12
          }
        },
        {
          "incidentId": "d0e6dcef-aca9-4f1b-b9dc-0143de858d5b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 246,
            "column": 12
          }
        },
        {
          "incidentId": "5ad3ead7-cc8f-4556-bacf-f90e7d8358ce",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 246,
            "column": 12
          }
        },
        {
          "incidentId": "490cf06f-3dee-4f1a-a0d5-d59edbf06af4",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 245,
            "column": 12
          }
        },
        {
          "incidentId": "836b7bb5-cb60-409a-89c1-034125134d1a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 245,
            "column": 12
          }
        },
        {
          "incidentId": "73c5488c-6310-445f-bd48-f3976022429a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 244,
            "column": 12
          }
        },
        {
          "incidentId": "452e0b5e-aa10-4ec0-8d02-563d7598e1ce",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 243,
            "column": 12
          }
        },
        {
          "incidentId": "c1ff514a-db23-4ba4-907e-dcb6a2491bcd",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 242,
            "column": 12
          }
        },
        {
          "incidentId": "cff60271-0690-43c1-a8c6-a6d25d044f44",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 241,
            "column": 12
          }
        },
        {
          "incidentId": "2290e969-4a2e-459a-a759-5a7a0c22f055",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 240,
            "column": 12
          }
        },
        {
          "incidentId": "ef97fc6d-05b1-4936-8864-1a208d2e61e6",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 239,
            "column": 12
          }
        },
        {
          "incidentId": "e2055a4c-40df-48da-bdc5-5044a5bcd3d9",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 238,
            "column": 12
          }
        },
        {
          "incidentId": "c14b123b-44bd-41ad-9b7c-48523a414ae4",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 237,
            "column": 12
          }
        },
        {
          "incidentId": "47d3455d-2ea5-4e1d-bfe9-4255af3bc308",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 237,
            "column": 12
          }
        },
        {
          "incidentId": "01a798b2-f836-4d9c-91b9-384ed0938be8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 236,
            "column": 12
          }
        },
        {
          "incidentId": "a943cd13-4f31-47e9-badf-a19f4bcadd7c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 236,
            "column": 12
          }
        },
        {
          "incidentId": "57cc00df-d774-4d39-b74d-b4cf7ad2ff0a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 235,
            "column": 12
          }
        },
        {
          "incidentId": "60aafc42-c61b-4893-a4c4-78fe77a79c93",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 235,
            "column": 12
          }
        },
        {
          "incidentId": "197f59b6-a7fd-4092-872d-7da1ce892060",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 234,
            "column": 12
          }
        },
        {
          "incidentId": "c345c25d-03aa-4852-8d23-215bf3552527",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 234,
            "column": 12
          }
        },
        {
          "incidentId": "7d3e35a5-7adf-4cfa-9ba0-78f0af17ddc1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 232,
            "column": 12
          }
        },
        {
          "incidentId": "95ba4c61-e020-47bc-b0f0-db53977c3e3a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 232,
            "column": 12
          }
        },
        {
          "incidentId": "b47c6bff-847b-4fcd-9619-fdfff2363b08",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 231,
            "column": 12
          }
        },
        {
          "incidentId": "17776fbc-66e0-44bd-9b53-2ea9613e8f60",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 231,
            "column": 12
          }
        },
        {
          "incidentId": "5ab449c3-fb3a-4844-9348-c1c9af2e1a02",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 230,
            "column": 12
          }
        },
        {
          "incidentId": "0385415f-44b8-46b4-a995-f0c104dc8480",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 230,
            "column": 12
          }
        },
        {
          "incidentId": "15fda2d5-49c6-4833-ab76-b17ae2617895",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 229,
            "column": 12
          }
        },
        {
          "incidentId": "3c0c809c-e6d4-4c36-9711-900367a9a6df",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 229,
            "column": 12
          }
        },
        {
          "incidentId": "4754c612-86e7-4819-a01a-804793cc2e2c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 228,
            "column": 12
          }
        },
        {
          "incidentId": "9286d039-dfcc-4164-be0e-76d853953daa",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 228,
            "column": 12
          }
        },
        {
          "incidentId": "ff870cca-1d01-402b-989b-19fee783e6cd",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 227,
            "column": 12
          }
        },
        {
          "incidentId": "9cca3da2-be38-47ba-ae4f-421fafaabcb7",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 227,
            "column": 12
          }
        },
        {
          "incidentId": "a8b079f8-ea1c-415b-962e-f070132d70a3",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 226,
            "column": 12
          }
        },
        {
          "incidentId": "76769ee4-9f08-4fb1-9bc6-b170d037e19d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 226,
            "column": 12
          }
        },
        {
          "incidentId": "a4540081-057d-4463-aac4-c59fb1239cbe",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 225,
            "column": 12
          }
        },
        {
          "incidentId": "16598c37-5026-4caf-8e2b-ae9677de0ae3",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 225,
            "column": 12
          }
        },
        {
          "incidentId": "10b4b2c0-5ae9-4526-a0b7-3d776ac11b04",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 224,
            "column": 12
          }
        },
        {
          "incidentId": "5a12c3fd-1e53-49f0-a37e-02950c0cac76",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 224,
            "column": 12
          }
        },
        {
          "incidentId": "4ce0d3db-4ab2-49e9-92e6-0fe203090bf3",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 223,
            "column": 12
          }
        },
        {
          "incidentId": "2319d1a5-43bb-4f19-87bd-ff4127b8f37b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 223,
            "column": 12
          }
        },
        {
          "incidentId": "d40e85d5-7b0c-4c33-bf9b-ce65123c1768",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 221,
            "column": 12
          }
        },
        {
          "incidentId": "5ec86885-f39f-4bbc-8e3b-79b7af64cb92",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 221,
            "column": 12
          }
        },
        {
          "incidentId": "c80e2b77-dd37-4c57-8b91-8fa380d954b2",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 220,
            "column": 12
          }
        },
        {
          "incidentId": "7fa84313-a780-44bb-8817-de1122dc69bb",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 220,
            "column": 12
          }
        },
        {
          "incidentId": "a54bca92-1332-4387-a4ad-3dbdcc585757",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 218,
            "column": 12
          }
        },
        {
          "incidentId": "86729d51-a872-481a-ae9d-778905354029",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 218,
            "column": 12
          }
        },
        {
          "incidentId": "a8b55b12-9117-4766-966b-d91edd791184",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 217,
            "column": 12
          }
        },
        {
          "incidentId": "a3c8ad9b-7349-43df-a1bb-73148ee46d7f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 217,
            "column": 12
          }
        },
        {
          "incidentId": "7bfe8024-f5f0-402d-966a-aef0f5fbfd39",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 215,
            "column": 12
          }
        },
        {
          "incidentId": "66de1a0a-5611-4482-bfe9-ede2e1cf3127",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 215,
            "column": 12
          }
        },
        {
          "incidentId": "01e72161-3aac-4960-887c-e5ca6e720f00",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 214,
            "column": 12
          }
        },
        {
          "incidentId": "3a387601-5219-4a7f-a892-b2383ed2244b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 214,
            "column": 12
          }
        },
        {
          "incidentId": "86c4b57d-6456-4009-af8f-5699bcdd5929",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 12
          }
        },
        {
          "incidentId": "57f59bba-d31a-49af-913a-51adc141be53",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 213,
            "column": 12
          }
        },
        {
          "incidentId": "e9292419-7d0f-48f7-963e-12bd1a9b8ddb",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 212,
            "column": 12
          }
        },
        {
          "incidentId": "cb87b026-0c56-486a-895e-515cc0129156",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 212,
            "column": 12
          }
        },
        {
          "incidentId": "31bc60a1-e652-4957-aa36-b7a447b85faa",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 211,
            "column": 12
          }
        },
        {
          "incidentId": "2cb5cc70-878f-4975-b086-e2fdded8f235",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 211,
            "column": 12
          }
        },
        {
          "incidentId": "68a5bb51-2d8c-498c-8425-d70758576319",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 210,
            "column": 12
          }
        },
        {
          "incidentId": "5b67f130-f65b-4da6-a322-e8856c77c4d2",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 210,
            "column": 12
          }
        },
        {
          "incidentId": "4e374cb3-330b-46ba-93fc-2098a58f1617",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 209,
            "column": 12
          }
        },
        {
          "incidentId": "8d82e838-2234-40ec-a46b-5902c5891eed",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 209,
            "column": 12
          }
        },
        {
          "incidentId": "6971a464-455b-41fd-954e-7b78b3199ac0",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 208,
            "column": 12
          }
        },
        {
          "incidentId": "29684190-c282-4dd0-a746-3f476fa0c6ce",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 208,
            "column": 12
          }
        },
        {
          "incidentId": "edcd672d-610c-4402-8c95-67d3d0c4e7e0",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 207,
            "column": 12
          }
        },
        {
          "incidentId": "73e4cb5c-9dbd-4707-a44d-09e8080e4071",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 207,
            "column": 12
          }
        },
        {
          "incidentId": "29929e33-2390-4e4d-b57b-0829955fd75e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 206,
            "column": 12
          }
        },
        {
          "incidentId": "d4d249b1-3693-4f3d-ad5a-e072b12cd3d9",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 206,
            "column": 12
          }
        },
        {
          "incidentId": "36f087f3-b8a5-420c-baed-8b7163b40cec",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 205,
            "column": 12
          }
        },
        {
          "incidentId": "4156f88c-31d6-4160-9ca3-59e8171ed88f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 205,
            "column": 12
          }
        },
        {
          "incidentId": "4aea6747-4405-44f5-b2b5-efc4f4fa115f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 204,
            "column": 12
          }
        },
        {
          "incidentId": "544b39d4-e0ff-4caa-ae75-63ba24170968",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 204,
            "column": 12
          }
        },
        {
          "incidentId": "6f2a9970-990f-4d0c-bfcd-39007a814fed",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 203,
            "column": 12
          }
        },
        {
          "incidentId": "58db8ccf-92f4-4cb9-a731-b9b769001f7f",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 203,
            "column": 12
          }
        },
        {
          "incidentId": "9d502893-8476-4f5e-b6ae-e34a7ea457c4",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 202,
            "column": 12
          }
        },
        {
          "incidentId": "91d8fb8f-3d03-436a-99ee-6c85057af269",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 198,
            "column": 12
          }
        },
        {
          "incidentId": "d9d194e4-c68b-4d6a-93b9-74c7b082e982",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 194,
            "column": 12
          }
        },
        {
          "incidentId": "c22ce155-553e-4ace-97c3-accb4aca39bb",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 193,
            "column": 12
          }
        },
        {
          "incidentId": "994ae608-3396-44b5-9ead-cab9123b5f24",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 193,
            "column": 12
          }
        },
        {
          "incidentId": "0fc3cf48-88fa-46a7-97c3-2d39bd69f74e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 191,
            "column": 12
          }
        },
        {
          "incidentId": "4c0784cf-bfab-4e35-b55c-7a4686fea1db",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 191,
            "column": 12
          }
        },
        {
          "incidentId": "9b8ec39b-733a-4eb0-a4d6-5086b4acb90a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 190,
            "column": 12
          }
        },
        {
          "incidentId": "d9271b63-b7ea-4efb-87e4-71f3823dca60",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 190,
            "column": 12
          }
        },
        {
          "incidentId": "52ae7c06-f7fa-4cec-bb6e-a28411135ee8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 188,
            "column": 12
          }
        },
        {
          "incidentId": "ca582dcb-faab-4759-a87a-dc75a2adb719",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 188,
            "column": 12
          }
        },
        {
          "incidentId": "29cbdeaf-182e-41d2-a123-02241ae6fa13",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 186,
            "column": 12
          }
        },
        {
          "incidentId": "c20f1d1e-e971-4b67-af96-7cd36cc7d43b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 186,
            "column": 12
          }
        },
        {
          "incidentId": "c3573a90-ba63-4197-b7d2-18df993c4bab",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 182,
            "column": 12
          }
        },
        {
          "incidentId": "643b8252-32ea-4bd7-a986-ba846f040c1b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 181,
            "column": 12
          }
        },
        {
          "incidentId": "bd1ceabc-d070-421f-a318-6c85e9d9eb74",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 181,
            "column": 12
          }
        },
        {
          "incidentId": "77958cd8-64f9-4e19-8b39-6d9cf4628bcf",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 180,
            "column": 12
          }
        },
        {
          "incidentId": "aa75397a-5687-41a2-9600-33be92270a12",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 180,
            "column": 12
          }
        },
        {
          "incidentId": "9ca42db7-0f62-4897-bcd5-baf07a0a80ee",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 178,
            "column": 12
          }
        },
        {
          "incidentId": "24c4b50f-ef3b-45f0-82a8-a4fb0289c7a9",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 178,
            "column": 12
          }
        },
        {
          "incidentId": "376ab18b-1741-47f6-88dc-6cc36a86b7b4",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 174,
            "column": 12
          }
        },
        {
          "incidentId": "a3a8c9d4-463a-4bcb-91df-a3ff5f6317a1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 171,
            "column": 12
          }
        },
        {
          "incidentId": "5e0b7227-a690-4d8f-b8cf-5b54e001f12c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 170,
            "column": 12
          }
        },
        {
          "incidentId": "82fb910e-2bd9-4e82-8ea9-9aa9566da83b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 170,
            "column": 12
          }
        },
        {
          "incidentId": "d4c15463-9c8d-4902-bdef-058210c1c7a7",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 168,
            "column": 12
          }
        },
        {
          "incidentId": "3e736e3c-0af2-4ee4-b770-df271d2b8387",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 168,
            "column": 12
          }
        },
        {
          "incidentId": "fd49c210-491e-4487-be2d-345d90bbe2f8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 166,
            "column": 12
          }
        },
        {
          "incidentId": "acf17d26-06ff-4052-b93e-aa2b0dc48f5d",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 166,
            "column": 12
          }
        },
        {
          "incidentId": "699b64ca-eafb-41b8-affd-f749a4205354",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.relationalentitytypebuilderextensions",
                "isCustom": false
              }
            ],
            "line": 164,
            "column": 12
          }
        },
        {
          "incidentId": "16fa69d2-34e9-4bb0-ad92-bc4e7524b3a8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder",
                "isCustom": false
              }
            ],
            "line": 164,
            "column": 12
          }
        },
        {
          "incidentId": "7a1fd9ea-32e8-45cd-abda-e759899d6864",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.relationalentitytypebuilderextensions",
                "isCustom": false
              }
            ],
            "line": 163,
            "column": 12
          }
        },
        {
          "incidentId": "0908e65e-333a-4cd4-8762-aac5b7f35663",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder",
                "isCustom": false
              }
            ],
            "line": 163,
            "column": 12
          }
        },
        {
          "incidentId": "ef68c987-c340-4514-8749-d4e76613d8c1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder",
                "isCustom": false
              }
            ],
            "line": 162,
            "column": 12
          }
        },
        {
          "incidentId": "0c8d1f32-0116-45e9-89b5-d3dcce4b298a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.Relational, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.relationalentitytypebuilderextensions",
                "isCustom": false
              }
            ],
            "line": 161,
            "column": 12
          }
        },
        {
          "incidentId": "52c448c5-6d5b-4f5d-b83d-1e62d5b2ab73",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder",
                "isCustom": false
              }
            ],
            "line": 161,
            "column": 12
          }
        },
        {
          "incidentId": "230549a0-0357-4420-aaf9-a12850c58362",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 156,
            "column": 12
          }
        },
        {
          "incidentId": "dbd18541-6203-41b8-86a5-7e488159431a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 155,
            "column": 12
          }
        },
        {
          "incidentId": "dbac0cf4-0d57-48d3-93c3-7208f61d9fc4",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 155,
            "column": 12
          }
        },
        {
          "incidentId": "ed47da9e-fc59-43c3-820c-6756ef8e14cb",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder.entity",
                "isCustom": false
              }
            ],
            "line": 154,
            "column": 12
          }
        },
        {
          "incidentId": "0601c8ba-100b-413f-8447-273b05596f8b",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.metadata.builders.keybuilder",
                "isCustom": false
              }
            ],
            "line": 154,
            "column": 12
          }
        },
        {
          "incidentId": "8f6c3b03-04f3-4bcd-bb3a-b407fe503154",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.modelbuilder",
                "isCustom": false
              }
            ],
            "line": 152,
            "column": 12
          }
        },
        {
          "incidentId": "7e3bb615-fbb4-4fe8-803f-81f33722a7ff",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontextoptionsbuilder",
                "isCustom": false
              }
            ],
            "line": 140,
            "column": 8
          }
        },
        {
          "incidentId": "35d3a252-e651-4985-8849-c3128ed40f55",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontextoptionsbuilder",
                "isCustom": false
              }
            ],
            "line": 145,
            "column": 16
          }
        },
        {
          "incidentId": "0f56e20d-7618-49cc-b490-4be3e8a55e9a",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontextoptionsbuilder.replaceservice",
                "isCustom": false
              }
            ],
            "line": 145,
            "column": 16
          }
        },
        {
          "incidentId": "c04b627a-8f82-49ae-9744-a50948be6639",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.SqlServer, 8.0.6.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.sqlserverdbcontextoptionsextensions",
                "isCustom": false
              }
            ],
            "line": 144,
            "column": 16
          }
        },
        {
          "incidentId": "c83dbf9b-2877-44d3-a154-994787c6013e",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontextoptionsbuilder",
                "isCustom": false
              }
            ],
            "line": 144,
            "column": 16
          }
        },
        {
          "incidentId": "1b1f387d-3c92-4f77-a67e-578e031fe7d5",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore.SqlServer, 8.0.6.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.sqlserverdbcontextoptionsextensions.usesqlserver",
                "isCustom": false
              }
            ],
            "line": 144,
            "column": 16
          }
        },
        {
          "incidentId": "6e9eb0ad-9310-4c3f-8df7-a604b8e3fe80",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontextoptionsbuilder.isconfigured",
                "isCustom": false
              }
            ],
            "line": 142,
            "column": 12
          }
        },
        {
          "incidentId": "cec7bbf3-5eda-48ba-a6fe-205bcdde3f52",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.dbcontext",
                "isCustom": false
              }
            ],
            "line": 24,
            "column": 12
          }
        },
        {
          "incidentId": "2984982c-29fd-4ae3-b6a2-ef9c640c1b88",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.dbcontext",
                "isCustom": false
              }
            ],
            "line": 18,
            "column": 8
          }
        },
        {
          "incidentId": "0c958786-59f2-4af7-beef-98d15f66743c",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.dbcontext",
                "isCustom": false
              }
            ],
            "line": 24,
            "column": 12
          }
        },
        {
          "incidentId": "6bad9157-142b-48b3-9c27-903c65a41db6",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.dbcontext",
                "isCustom": false
              }
            ],
            "line": 18,
            "column": 8
          }
        },
        {
          "incidentId": "d0e57f03-6f17-4592-8354-1b8328c14bc1",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "Brands.DAL\\Brands.DAL.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "Brands.DAL\\EPDataContext.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext",
                "isCustom": false
              }
            ],
            "line": 15,
            "column": 41
          }
        }
      ]
    },
    {
      "path": "DXCustomControls\\DXCustomControls.vbproj",
      "startingProject": false,
      "issues": 2,
      "storyPoints": 2,
      "ruleInstances": [
        {
          "incidentId": "fef94121-da03-4592-bfb4-e65e49b28c67",
          "ruleId": "Project.0001",
          "projectPath": "DXCustomControls\\DXCustomControls.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "DXCustomControls\\DXCustomControls.vbproj"
          }
        },
        {
          "incidentId": "f7d63d45-e8dc-41d3-8328-c1803cfe40a1",
          "ruleId": "Project.0002",
          "projectPath": "DXCustomControls\\DXCustomControls.vbproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "DXCustomControls\\DXCustomControls.vbproj"
          }
        }
      ]
    },
    {
      "path": "PwdEnc\\PwdEnc.csproj",
      "startingProject": false,
      "issues": 2,
      "storyPoints": 2,
      "ruleInstances": [
        {
          "incidentId": "097a5c8e-e52d-411e-bfc3-2681eebb8f2a",
          "ruleId": "Project.0001",
          "projectPath": "PwdEnc\\PwdEnc.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "PwdEnc\\PwdEnc.csproj"
          }
        },
        {
          "incidentId": "aa005b5e-42ea-45d6-af44-daae5c0b8854",
          "ruleId": "Project.0002",
          "projectPath": "PwdEnc\\PwdEnc.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "PwdEnc\\PwdEnc.csproj"
          }
        }
      ]
    },
    {
      "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
      "startingProject": false,
      "issues": 2,
      "storyPoints": 32,
      "ruleInstances": [
        {
          "incidentId": "a5c86685-0f4c-4b21-accd-56775ae27040",
          "ruleId": "NuGet.0002",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
            "properties": {
              "PackageId": "Microsoft.EntityFrameworkCore",
              "PackageVersion": "3.1.32",
              "PackageNewVersion": "9.0.6",
              "PackageReplacements": null
            }
          }
        },
        {
          "incidentId": "f2701c67-5755-4038-917d-3ac808354756",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueReportExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 12
          }
        },
        {
          "incidentId": "866e9eed-d059-4dbc-ad25-e750c843024a",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueReportExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 12
          }
        },
        {
          "incidentId": "c000085f-15e8-4beb-a148-8ff12c446ab3",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueReportExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting.indented",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 12
          }
        },
        {
          "incidentId": "e53cb7f5-d913-4811-be50-34a1a66a4be8",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueReportExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 12
          }
        },
        {
          "incidentId": "5c62dbd4-e90d-4996-b015-e67d3cf5d40c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueReportExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.serializeobject",
                "isCustom": false
              }
            ],
            "line": 13,
            "column": 12
          }
        },
        {
          "incidentId": "b89c386a-3774-4414-844b-93cae322586c",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 130,
            "column": 16
          }
        },
        {
          "incidentId": "c546ed5b-5f7c-4650-af10-74aa01d008f1",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 130,
            "column": 16
          }
        },
        {
          "incidentId": "0ded36c0-2bec-4463-819f-a4e75d9395b0",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting.none",
                "isCustom": false
              }
            ],
            "line": 130,
            "column": 16
          }
        },
        {
          "incidentId": "6ac9cec3-ae40-4fa1-ae36-a37d96360a7e",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 130,
            "column": 16
          }
        },
        {
          "incidentId": "81ee1781-b9f0-4ce9-a137-54f1c70b7475",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.serializeobject",
                "isCustom": false
              }
            ],
            "line": 130,
            "column": 16
          }
        },
        {
          "incidentId": "1a299316-a83b-445e-a077-81386c0ddccd",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 59,
            "column": 12
          }
        },
        {
          "incidentId": "0e120c98-7094-4160-8203-bf676411130e",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 59,
            "column": 12
          }
        },
        {
          "incidentId": "fecee0af-8b18-4166-b43b-bf88ec97ed6f",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting.none",
                "isCustom": false
              }
            ],
            "line": 59,
            "column": 12
          }
        },
        {
          "incidentId": "9bd1653a-fb6c-4356-875f-949fb803bd59",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 59,
            "column": 12
          }
        },
        {
          "incidentId": "31a1042c-e5fe-4229-b358-d6c68335ef52",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueEmailExtensions.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.serializeobject",
                "isCustom": false
              }
            ],
            "line": 59,
            "column": 12
          }
        },
        {
          "incidentId": "7c53cd1e-e2f3-4830-b488-d944dcf27616",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.referenceloophandling",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "16eb4449-4d3c-4ea8-a9ed-6a8567cf95ce",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.referenceloophandling",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "264245f7-df0a-47d6-92aa-1e9860e87677",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.referenceloophandling.ignore",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "3e4bd61b-de27-45ae-8cde-fb5975b320f7",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.referenceloophandling",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "1b45960c-1b37-4a53-9244-38840879a15d",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonserializersettings.referenceloophandling",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "c3bcda3b-6ad5-45f5-8320-4436d23cc9c2",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "3eac834c-3fec-4287-bad9-0ff9a57b8d7d",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "56f073b3-cf21-4eb5-b935-dbea5ec7ec69",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting.indented",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "89a7b1e2-9078-4f53-a761-075dcddc1bdb",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.formatting",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "ca19756f-beb5-4c13-bbe8-e59f55545f73",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonserializersettings.formatting",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "d4e5f760-57cb-401b-8e44-52e8585c61dc",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonserializersettings",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "d7150be9-6504-4151-a15b-49185f6eebc4",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonserializersettings.jsonserializersettings",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "59b98497-5b97-4899-a91a-9205d7be36d6",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "cfb95c45-b39b-499f-89bd-cbd83474a698",
          "ruleId": "Api.0002",
          "description": "API is available in package Newtonsoft.Json, 13.0.3.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/newtonsoft.json.jsonconvert.defaultsettings",
                "isCustom": false
              }
            ],
            "line": 125,
            "column": 12
          }
        },
        {
          "incidentId": "1ef66279-3f5c-4d78-b23a-9579ca16bee8",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.savechangesasync",
                "isCustom": false
              }
            ],
            "line": 105,
            "column": 16
          }
        },
        {
          "incidentId": "ba808879-282e-4682-9ef5-909ee0b83a67",
          "ruleId": "Api.0002",
          "description": "API is available in package Microsoft.EntityFrameworkCore, 9.0.0-preview.6.24302.4.",
          "projectPath": "QueueProcessors\\QueueBuilder\\QueueBuilder.csproj",
          "state": "Active",
          "location": {
            "kind": "File",
            "path": "QueueProcessors\\QueueBuilder\\QueueBuilder.cs",
            "links": [
              {
                "title": "API documentation",
                "url": "https://learn.microsoft.com/dotnet/api/microsoft.entityframeworkcore.dbcontext.savechanges",
                "isCustom": false
              }
            ],
            "line": 96,
            "column": 12
          }
        }
      ]
    }
  ],
  "rules": {
    "NuGet.0002": {
      "id": "NuGet.0002",
      "description": "NuGet package upgrade is recommended for selected target framework.\n\nStandard .NET packages are recommended to have versions matching version of .NET that project targets.\n\nSome other packages also are known to work better for selected target frameworks.",
      "label": "NuGet package upgrade is recommended",
      "severity": "Potential",
      "effort": 1,
      "links": [
        {
          "url": "https://go.microsoft.com/fwlink/?linkid=2262530",
          "isCustom": false
        }
      ]
    },
    "NuGet.0004": {
      "id": "NuGet.0004",
      "description": "NuGet package contains security vulnerabilities.\n\nPackage needs to be upgraded to a newer version that addresses known security vulnerabilities.",
      "label": "NuGet package contains security vulnerability",
      "severity": "Optional",
      "effort": 1
    },
    "NuGet.0005": {
      "id": "NuGet.0005",
      "description": "NuGet package is deprecated.\n\nGo to its documentation and if there is a guidance for replacement of functionality provided by this package.",
      "label": "NuGet package is deprecated",
      "severity": "Optional",
      "effort": 1,
      "links": [
        {
          "url": "https://go.microsoft.com/fwlink/?linkid=2262531",
          "isCustom": false
        }
      ]
    },
    "Api.0002": {
      "id": "Api.0002",
      "description": "Some API that lived in older versions of .NET frameworks were moved to standalone NuGet packages that could be added to projects explicitly.",
      "label": "API available in NuGet package",
      "severity": "Potential",
      "effort": 1,
      "links": [
        {
          "title": "Breaking changes in .NET",
          "url": "https://go.microsoft.com/fwlink/?linkid=2262679",
          "isCustom": false
        }
      ]
    },
    "Project.0001": {
      "id": "Project.0001",
      "description": "Project file needs to be converted to SDK-style. Modern .NET framework projects require a change in the project file format and use SDK corresponding to project flavor and functionality.",
      "label": "Project file needs to be converted to SDK-style",
      "severity": "Mandatory",
      "effort": 1,
      "links": [
        {
          "title": "Overview of porting from .NET Framework to .NET",
          "url": "https://go.microsoft.com/fwlink/?linkid=2265227",
          "isCustom": false
        },
        {
          "title": ".NET project SDKs",
          "url": "https://go.microsoft.com/fwlink/?linkid=2265226",
          "isCustom": false
        }
      ]
    },
    "Project.0002": {
      "id": "Project.0002",
      "description": "Project\u0027s target framework(s) needs to be changed to the new target framework that you selected for this upgrade.\n\nDuring upgrade target framework will be adjusted to corresponding platform when applicable. In some cases project would result in multiple target frameworks after the upgrade if it was using features that now have their own platforms in modern .NET frameworks (windows, iOS, Android etc).",
      "label": "Project\u0027s target framework(s) needs to be changed",
      "severity": "Mandatory",
      "effort": 1,
      "links": [
        {
          "title": "Overview of porting from .NET Framework to .NET",
          "url": "https://go.microsoft.com/fwlink/?linkid=2265227",
          "isCustom": false
        },
        {
          "title": ".NET project SDKs",
          "url": "https://go.microsoft.com/fwlink/?linkid=2265226",
          "isCustom": false
        }
      ]
    },
    "NuGet.0001": {
      "id": "NuGet.0001",
      "description": "NuGet package is incompatible with selected target framework.\n\nPackage needs to be upgraded to a version supporting selected project target framework. If there no new package versions supporting new target framework, different package needs to be used and all code needs to be upgraded to new API.",
      "label": "NuGet package is incompatible",
      "severity": "Mandatory",
      "effort": 1,
      "links": [
        {
          "url": "https://go.microsoft.com/fwlink/?linkid=2262529",
          "isCustom": false
        }
      ]
    },
    "NuGet.0003": {
      "id": "NuGet.0003",
      "description": "NuGet package functionality is included with framework reference.\n\nPackage needs to be removed.",
      "label": "NuGet package functionality is included with framework reference",
      "severity": "Mandatory",
      "effort": 1,
      "links": [
        {
          "url": "https://go.microsoft.com/fwlink/?linkid=2262609",
          "isCustom": false
        }
      ]
    }
  }
}