﻿Imports System.ComponentModel
Imports System.Data
Imports System.IO
Imports System.Net.Mail
Imports DevExpress.Pdf
Imports DevExpress.XtraEditors
Public Class frmQuarterEndEmailReports
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property IsTesting = False
    Dim db As dbEPDataDataContext
    Dim emailList As List(Of prc_QtrEndEmailResult)
    'changed by Solomon.  found dup id
    'Dim reportNames As Dictionary(Of Integer, REPORT_LIST)
    Dim reportNames As List(Of REPORT_LIST)
    Dim IsLoaded As Boolean = False
    Dim frmLogger As Serilog.ILogger
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property AutoRun As Boolean = False
    Private mRunType As RunTypeEnum = RunTypeEnum.NonAca

    Public ReadOnly Property RunType As RunTypeEnum
        Get
            Return mRunType
        End Get
    End Property

    Public Sub New()
        frmLogger = Logger.ForContext(Of frmQuarterEndEmailReports)()
        InitializeComponent()
        mRunType = RunTypeEnum.NonAca
    End Sub

    Public Sub New(Optional RunType As RunTypeEnum = RunTypeEnum.NonAca)
        frmLogger = Logger.ForContext(Of frmQuarterEndEmailReports)()
        InitializeComponent()
        mRunType = RunType
        If RunType = RunTypeEnum.Aca Then
            Text = "ACA Email Reports"
        End If
    End Sub

    Public Sub New(coNum As Decimal)
        frmLogger = Logger.ForContext(Of frmQuarterEndEmailReports)()
        InitializeComponent()
        lueCoNum.EditValue = coNum
    End Sub

    Private Async Sub frmQuarterEndEmailReports_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString())
        db.CommandTimeout = 1000

        db.DeferredLoadingEnabled = False
        IsLoaded = True
        If lueCoNum.Text.IsNullOrWhiteSpace Then
            deQuarterEndDate.DateTime = GetLastQuarterEndDate()
        Else
            deQuarterEndDate.EditValue = Nothing
            deQuarterEndDate.Text = String.Empty
        End If
        Dim frm = New EmailService("<EMAIL>")
        Try
            Await LoadData()
            If AutoRun Then
                frm.ToEmail.AddRange(GetUdfValue("AutoTasksEmailNotification").Split(";"))
                frmLogger.Information("AutoRun is True")
                gridView.SelectAll()
                Await Process(True)
                frm.Subject = $"Auto Quarter Email Reports Results{If(Me.meErrorLog.Text.IsNullOrWhiteSpace(), " - Success", "")}"
                frm.Body = String.Format("The Auto system completed sending the quarterly emails{0}{0}Please review the results of the Auto system{0}{0}{1}{0}{0}{2}", vbCrLf, If(meErrorLog.Text.IsNullOrWhiteSpace(), "-No Errors-", "Errors: " + vbCrLf + vbCrLf + meErrorLog.Text), If(meLog.Text.IsNullOrWhiteSpace(), "-no emails sent-", meLog.Text))
                frm.SendEmail()
                frmLogger.Information("Finished sending QuarterEndEmailReports")
                Application.Exit()
            End If
        Catch ex As Exception
            If AutoRun Then
                frmLogger.Error(ex, "Error in AutoRun")
                frm.Subject = "Error in Auto Quarter Email Reports"
                frm.Body = ex.ToString()
                frm.SendEmail()
                Application.Exit()
            End If
        End Try
    End Sub

    Private Async Function LoadData() As Task
        If Not IsLoaded Then Exit Function
        Try
            lcRoot.ShowProgessPanel()

            'modified by Solomon
            'reportNames = db.REPORT_LISTs.ToDictionary(Function(key) key.REPORT_ID)
            reportNames = db.REPORT_LISTs.ToList

            emailList = New List(Of prc_QtrEndEmailResult)
            Dim emailList1 = Await Task.Run(Function() db.prc_QtrEndEmail(SelectedDate, SelectedCompany, If(mRunType = RunTypeEnum.NonAca, Nothing, "ACA")))
            'Dim emailList1 = (Await QueryAsync(Of prc_QtrEndEmailResult)($"exec custom.prc_QtrEndEmailTest '{SelectedDate()}'")).ToList()
            ddbSendEmails.DropDownArrowStyle = If(lueCoNum.EditValue, DropDownArrowStyle.SplitButton, DropDownArrowStyle.Hide)
            Dim include = If(lueCoNum.EditValue, False, True)
            For Each item In emailList1
                If item.SsaPath1.IsNotNullOrWhiteSpace AndAlso item.SsaPath2.IsNotNullOrWhiteSpace AndAlso Not item.QYEND_HOLD_TYPE.ToUpper().StartsWith("LINKED") Then
                    If Not System.IO.File.Exists(item.SsaPath1) OrElse Not System.IO.File.Exists(item.SsaPath2) Then
                        item.NeedApproval &= "Pending SSA"
                    End If
                End If
                emailList.Add(item)
            Next
            gridControl.DataSource = emailList
            gridView.BestFitColumns()
            'riCeInclude_CheckedChanged(Nothing, Nothing)
            lueCoNum.Properties.DataSource = db.COMPANies.ToList()

            If lueCoNum.EditValue IsNot Nothing Then
                teQuarterEndHold.Text = db.COOPTIONs.Single(Function(c) c.CONUM.Equals(lueCoNum.EditValue)).QYEND_HOLD_TYPE
                If teQuarterEndHold.Text.ToLower.Contains("billing") Then
                    lciQuarterEndHold.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                Else
                    lciQuarterEndHold.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                End If
            ElseIf lueCoNum.EditValue Is Nothing Then
                lciQuarterEndHold.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            End If
            SetCount()
        Catch ex As Exception
            If AutoRun Then Throw
            DisplayErrorMessage("Error loading data", ex)
        Finally
            deQuarterEndDate.Enabled = True
            lcRoot.HideProgressPanel()
        End Try
    End Function

    Private Async Function Process(_SendEmail As Boolean) As Task
        Try
            ddbSendEmails.Enabled = False

            For Each item In gridView.GetSelectedRows(Of prc_QtrEndEmailResult)()
                Await Task.Run(Sub() ProcessRow(item, _SendEmail))
            Next

            Log("Finished..............")
            If Not _SendEmail Then
                MainForm.OpenCompForm(SelectedCompany, 1)
            End If
        Catch ex As Exception
            Log(ex.ToString())
            DisplayErrorMessage("Error processing Quarter End Emails", ex)
        Finally
            ddbSendEmails.Enabled = True
        End Try
        Await LoadData()
        If meErrorLog.Text.IsNotNullOrWhiteSpace Then
            TabbedControlGroup1.SelectedTabPageIndex = 1
        End If
    End Function

    Public Sub ProcessRow(qtrEndRow As prc_QtrEndEmailResult, _SendEmail As Boolean)
        Try
            Log("Processing Co#: {0}".FormatWith(qtrEndRow.CONUM))
            Dim isWebPost = qtrEndRow.EmailBodyTemplate = "QtrEndWebPost"
            Dim isPP = qtrEndRow.EmailBodyTemplate = "QtrEndWebPostPP"
            'Solomon added on Apr 14, 21.  Per Mendy only attach if not Paydeck/Webpost/PPP
            Dim isOnlineClient As Boolean = qtrEndRow.EmailBodyTemplate <> "QtrEnd"
            Dim fileName As String = $"{qtrEndRow.CONUM}_{qtrEndRow.Qtr}_{qtrEndRow.Year}_QYEND_REPORTS.pdf"
            Dim attachments As List(Of System.Net.Mail.Attachment) = New List(Of Attachment)
            Dim reportIdsToSendSeparateAttachment = GetUdfValue("QtrEndReportIdsToSendseparateAttachment")?.Split(",")
            Dim coPassword = GetComPassword(qtrEndRow.CONUM)


            QuarterEndUtils.ResetReportsDisplayDate(qtrEndRow.CONUM, 0)

            Dim ssaAttachment As ReportResults = Nothing
            If qtrEndRow.SsaCoverName.IsNotNullOrWhiteSpace Then
                ssaAttachment = GetSsaReport(qtrEndRow, coPassword, Not isOnlineClient)
                If ssaAttachment IsNot Nothing Then
                    If isOnlineClient Then
                        SaveReportToSchfedForms(qtrEndRow, 297, ssaAttachment)
                    Else
                        attachments.Add(ssaAttachment.ToMailAttachment)
                    End If
                End If
            End If

            Dim uiChecksAttachemtn = GetUiChecks(qtrEndRow, Not isOnlineClient)
            If uiChecksAttachemtn IsNot Nothing Then
                If isOnlineClient Then
                    SaveReportToSchfedForms(qtrEndRow, 298, uiChecksAttachemtn)
                Else
                    attachments.Add(uiChecksAttachemtn.ToMailAttachment)
                End If
            End If

            If qtrEndRow.CalendarPath.IsNotNullOrWhiteSpace Then
                attachments.Add(NewAttachment(qtrEndRow.CalendarPath, $"Brands Calendar-{DateTime.Now.Year}.pdf"))
            End If

            If Not isOnlineClient Then
                Dim combinedPdf = New PdfDocumentProcessor()
                combinedPdf.CreateEmptyDocument()

                Dim forms = db.view_REPORTS_UNIONs.Where(Function(s) s.CONUM = qtrEndRow.CONUM AndAlso s.RPT_COUNTER = qtrEndRow.QtrJobNum AndAlso Not (s.RPT_ID = 80 AndAlso s.EMPNUM.HasValue)).OrderBy(Function(s) s.RPT_ORDER).ToList
                Log($"{forms.Count} documents")
                For index = 0 To forms.Count - 1
                    Dim rpt = forms(index)
                    Dim dt = modGlobals.Query("SELECT ru.REPORT_FILE FROM dbo.REPORTS_UNION ru WHERE ru.id = @P0 AND ru.CONUM = @P1", New SqlClient.SqlParameter("@P0", rpt.ID), New SqlClient.SqlParameter("@P1", rpt.CONUM))
                    Dim byteArray As Byte() = dt.Rows(0)(0)
                    Dim pdf = New PdfDocumentProcessor()
                    pdf.LoadDocument(New MemoryStream(byteArray))
                    Try
                        If pdf.Document.Pages.Count > 0 Then
                            CopyPages(pdf, combinedPdf, rpt.REPORT_NAME)
                            If reportIdsToSendSeparateAttachment.Contains(rpt.RPT_ID) AndAlso Not rpt.EMPNUM.HasValue Then
                                Log($"Report: {rpt.REPORT_NAME} - will be added as separate attachment.")
                                attachments.Add(GetSeparateAttachment(qtrEndRow, coPassword, rpt, byteArray))
                            End If
                        Else
                            Log("form {0} is empty and is not sent".FormatWith(rpt.REPORT_NAME))
                        End If
                    Catch ex As Exception
                        frmLogger.Error(ex, "Error attaching form: {ReportName} Co#: {CoNum}", rpt.REPORT_NAME, qtrEndRow.CONUM)
                        Log(String.Format("Error attaching form: {3} to co#: {2}.{0}{0}{1}", vbCrLf, ex, qtrEndRow.CONUM, rpt.REPORT_NAME), ex, True)
                    End Try
                Next

                If lueCoNum.Text.IsNotNullOrWhiteSpace() Then
                    Dim path As String = System.IO.Path.Combine(ReportsExtensions.GetCrystalReportsFolder, fileName)
                    combinedPdf.SaveDocument(path)
                    Log("File Saved to: {0}".FormatWith(path))
                End If

                Dim savedPath As String = System.IO.Path.Combine(ReportsExtensions.GetSecureCrystalReportsFolder, fileName)
                combinedPdf.SaveDocument(savedPath, GetPdfEncryption(GetComPassword(qtrEndRow.CONUM)), True)
                attachments.Insert(0, NewAttachment(savedPath))
                Log("File Saved to: {0}".FormatWith(savedPath))
                combinedPdf.Dispose()
                combinedPdf = Nothing
            End If


            Try
                If _SendEmail Then
                    SendEmail(qtrEndRow, attachments)

                    If Not isOnlineClient Then
                        Try
                            EmailPassword(qtrEndRow.CONUM, False, qtrEndRow.user_email)
                        Catch ex As Exception
                            Log(String.Format("Error sending document(s) password to co#: {2}.{0}{0}{1}", vbCrLf, ex, qtrEndRow.CONUM), ex, True)
                        End Try
                    End If

                    Dim message = "Qtr-"
                    If qtrEndRow.HasW2_80 Then message &= "W2EE - "
                    If uiChecksAttachemtn IsNot Nothing Then message &= "UiCks - "
                    If ssaAttachment IsNot Nothing Then message &= "SSA - "
                    If qtrEndRow.HasNY_DBPFL Then message &= "NyDbPFL - "

                    If isWebPost Then
                        message &= "WebPost - "
                    ElseIf isPP Then
                        message &= "PP - "
                    End If

                    MarkEmailDone(qtrEndRow, ssaAttachment IsNot Nothing, message)
                End If

            Catch ex As System.IO.IOException
                Log(String.Format("Error sending document(s) to co#: {2}.{0}{0}{1}", vbCrLf, ex, qtrEndRow.CONUM), ex, True)
            End Try
        Catch ex As Exception
            Log(String.Format("Error sending document(s) to co#: {2}.{0}{0}{1}", vbCrLf, ex, qtrEndRow.CONUM), ex, True)
        End Try
    End Sub

    Private Sub SaveReportToSchfedForms(qtrEndRow As prc_QtrEndEmailResult, rpt_id As Integer, report As ReportResults)
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim schfedForm = New SCHFED_FORM With {
                .RPT_ID = rpt_id,
                .RPT_ORDER = 200, 'verify
                .RPT_FILE = System.IO.File.ReadAllBytes(report.Path),
                .RPT_SIZE = New FileInfo(report.Path).Length,
                .PRT_DATE = DateTime.Now,
                .PRT_QTR = qtrEndRow.Qtr,
                .PRT_YEAR = qtrEndRow.Year,
                .RPT_STATUS = "Completed",
                .RPT_COUNTER = qtrEndRow.QtrJobNum,
                .JOB_TYPE = "Client",
                .PRT_COMPLETEDDATE = Today,
                .CONUM = qtrEndRow.CONUM,
                .file_arcflag = Nothing,
                .RPT_PRTTYPE = 2,
                .PRT_MONTH = modGlobals.GetMonthFromQuarter(qtrEndRow.Qtr),
                .RPT_SCOUNTER = $"{qtrEndRow.Year}-{qtrEndRow.Qtr}-{qtrEndRow.EmailedRPT_COUNTER}",
                .rpt_posted = String.Empty
            }
            ctxDB.SCHFED_FORMS.InsertOnSubmit(schfedForm)
            ctxDB.SubmitChanges()
        End Using
    End Sub

    Private Function GetUiChecks(qtrEndRow As prc_QtrEndEmailResult, passwordProtect As Boolean) As ReportResults
        If qtrEndRow.UiCksName.IsNotNullOrWhiteSpace Then
            Log($"Processing Report: {qtrEndRow.UiCksName}")
            Dim report = New ReportProcessor(qtrEndRow.CONUM, qtrEndRow.UiCksName, FileType.Pdf) With {.showParametersForm = False, .showInRecentReports = True}
            report.DefaultParamValues = New List(Of KeyValuePair) From {
                New KeyValuePair("Conum", qtrEndRow.CONUM),
                New KeyValuePair("Qtr", qtrEndRow.Qtr),
                New KeyValuePair("Year", qtrEndRow.Year)
            }
            Dim reportResults = report.ProcessReport()
            If reportResults.AllFileExist AndAlso Not reportResults.Cancalled Then
                If reportResults.Paths.Count > 1 Then Throw New Exception("Report UiCksName should only return one file.")
                If passwordProtect Then
                    Dim reportSender = New ReportSender(reportResults)
                    Dim documents = reportSender.ProcessDocuments()
                    Return New ReportResults(documents.Single, "UI Checks.pdf")
                Else
                    Return New ReportResults(reportResults.Paths.Single, "UI Checks.pdf")
                End If
            Else
                Throw New Exception($"Report not processed sucsessfully. ReportName: {qtrEndRow.UiCksName}")
            End If
        Else
            Return Nothing
        End If
    End Function

    Private Function GetSsaReport(qtrEndRow As prc_QtrEndEmailResult, coPassword As String, passwordProtect As Boolean) As ReportResults
        Log("Processing Ssa report.")
        Dim pdfs = New List(Of String)
        If qtrEndRow.SsaPath1.IsNotNullOrWhiteSpace AndAlso System.IO.File.Exists(qtrEndRow.SsaPath1) Then
            pdfs.Add(qtrEndRow.SsaPath1)
        End If
        If qtrEndRow.SsaPath2.IsNotNullOrWhiteSpace AndAlso System.IO.File.Exists(qtrEndRow.SsaPath2) Then
            pdfs.Add(qtrEndRow.SsaPath2)
        End If
        If pdfs.Any Then
            Log($"Processing Report: {qtrEndRow.SsaCoverName}")
            Dim report = New ReportProcessor(qtrEndRow.CONUM, qtrEndRow.SsaCoverName, FileType.Pdf) With {.showParametersForm = False, .showInRecentReports = True}
            report.DefaultParamValues = New List(Of KeyValuePair)
            report.DefaultParamValues.Add(New KeyValuePair("Conum", qtrEndRow.CONUM))
            report.DefaultParamValues.Add(New KeyValuePair("QtrEndDate", GetQuarterEndDate(qtrEndRow.Year, qtrEndRow.Qtr)))
            report.DefaultParamValues.Add(New KeyValuePair("FormType", "SSA W2"))
            Dim reportResults = report.ProcessReport()
            If reportResults.AllFileExist AndAlso Not reportResults.Cancalled Then
                pdfs.Insert(0, reportResults.Paths.Single)
                Dim securePath = System.IO.Path.Combine(modReports.GetSecureCrystalReportsFolder, System.IO.Path.GetFileName(reportResults.Paths.Single))
                Dim newPdf = PdfUtilities.CombinePdfsNew(securePath, pdfs.ToArray, If(passwordProtect, coPassword, Nothing))
                Log($"Report: {qtrEndRow.SsaCoverName} Saved to {securePath}")
                Return New ReportResults(securePath, "SSA W2.pdf")
            Else
                Throw New Exception($"Report not processed sucsessfully. ReportName: {qtrEndRow.SsaCoverName}")
            End If
        Else
            Log($"No Ssa report found. Co#: {qtrEndRow.CONUM} Year: {qtrEndRow.Year} Qtr: {qtrEndRow.Qtr}", Nothing, True)
            Return Nothing
        End If
    End Function

    Private Function GetSeparateAttachment(qtrEndRow As prc_QtrEndEmailResult, coPassword As String, rpt As view_REPORTS_UNION, byteArray() As Byte) As Attachment
        Dim separateFileName = $"{qtrEndRow.CONUM}_{qtrEndRow.Qtr}_{qtrEndRow.Year}_{rpt.RPT_ORDER}_{rpt.REPORT_NAME}.pdf"
        Dim securepath As String = System.IO.Path.Combine(ReportsExtensions.GetSecureCrystalReportsFolder, separateFileName)

        Using doc = New DevExpress.Pdf.PdfDocumentProcessor()
            doc.LoadDocument(New MemoryStream(byteArray), True)
            doc.SaveDocument(securepath, PdfUtilities.GetPdfEncryption(coPassword))
            Log($"Report: {rpt.REPORT_NAME} - Saved to {securepath}")

            If lueCoNum.Text.IsNotNullOrWhiteSpace() Then
                Dim path = System.IO.Path.Combine(ReportsExtensions.GetCrystalReportsFolder, separateFileName)
                doc.SaveDocument(path)
                Log($"Report: {rpt.REPORT_NAME} - Saved to {path} (Recent Reports)")
            End If
        End Using

        Return NewAttachment(securepath, $"{rpt.REPORT_NAME}.pdf")
    End Function

    Private Shared Function NewAttachment(path As String, Optional name As String = Nothing) As System.Net.Mail.Attachment
        Dim attach = New Attachment(path)
        If name.IsNotNullOrWhiteSpace Then attach.Name = name
        Return attach
    End Function

    Private Async Sub deQuarterEndDate_EditValueChanged(sender As Object, e As EventArgs)
        Await LoadData()
    End Sub

    Private Function SelectedCompany() As Decimal?
        Dim dec As Decimal
        If Decimal.TryParse(nz(lueCoNum.EditValue, ""), dec) Then
            Return dec
        Else
            Return Nothing
        End If
    End Function

    Private Function SelectedDate() As DateTime?
        Dim dat As DateTime
        If DateTime.TryParse(deQuarterEndDate.EditValue, dat) Then
            Return dat
        Else
            Return Nothing
        End If
    End Function

    Private Sub SendEmail(qtrEndRow As prc_QtrEndEmailResult, attachments As List(Of System.Net.Mail.Attachment))
        Try
            If qtrEndRow.user_email.IsNullOrWhiteSpace Then
                Throw New Exception("Blank email address")
            End If

            Using MM As New MailMessage
                Using MC As New SmtpClient
                    MM.From = New MailAddress("<EMAIL>", "Brands Paperless")

                    For Each i In qtrEndRow.user_email.Split(";")
                        If i.IsNotNullOrWhiteSpace Then MM.To.Add(i)
                    Next

                    Dim strCat As String = If(mRunType = RunTypeEnum.NonAca, "Quarter End", "ACA")

                    MM.Bcc.Add("<EMAIL>")
                    If qtrEndRow.EmailSubject.IsNullOrWhiteSpace Then
                        MM.Subject = "{0} Reports for company # {1} {2}".FormatWith(strCat, qtrEndRow.CONUM, qtrEndRow.CO_NAME)
                    Else
                        MM.Subject = qtrEndRow.EmailSubject
                    End If
                    For Each attachment In attachments
                        MM.Attachments.Add(attachment)
                    Next

                    If qtrEndRow.EmailBodyTemplate.IsNotNullOrWhiteSpace() Then
                        Dim emailTemplate = (From et In db.ReportEmailTeplates Where et.Name = qtrEndRow.EmailBodyTemplate).FirstOrDefault
                        If emailTemplate Is Nothing Then
                            Throw New InvalidDataException($"Email Template {qtrEndRow.EmailBodyTemplate} does not exist. Please fix.")
                        End If
                        'Dim t = HandlebarsDotNet.Handlebars.Compile(emailTemplate.EmailBody)
                        'Dim rr = t(qtrEndRow)
                        'MM.Body = rr
                        Dim rows = New DataRow() {ObjectToDataRow(qtrEndRow)}
                        rows.First().Table.Columns.Add("IsYearEnd", GetType(Boolean))
                        rows.First.SetField(Of Boolean)("IsYearEnd", qtrEndRow.Qtr = 4)
                        Dim reportSender = New ReportSender(Nothing, emailTemplate, Nothing, rows)
                        Dim body = reportSender.GetReportEmailBody
                        Using rec As New DevExpress.XtraRichEdit.RichEditControl()
                            rec.HtmlText = body
                            Dim exporter As New frmComposeEmail.RichEditMailMessageExporter(rec, MM)
                            exporter.Export()
                        End Using
                        MM.IsBodyHtml = True
                    Else
                        MM.Body = "{4}, {1} {0}{0}The attached file(s) contains your {5} Reports for year {2} and quarter {3}.  Save the file(s) to your system and a destination of your choosing.{0}{0}".FormatWith(vbCrLf, qtrEndRow.Name, qtrEndRow.Year, qtrEndRow.Qtr, modGlobals.Greeting(), strCat)
                        MM.Body &= "If you have any questions, please call us at (718) 625-1800 or email to this address (mailto: <EMAIL> ){0}{0}Thank you for using our Payroll Service,{0}{0}Brands Paycheck & HR Services{0}{0}".FormatWith(vbCrLf)
                        MM.Body &= "This message and any attachments may contain confidential or privileged information and are intended only for the use of the intended recipients of this message. If you are not the intended recipient of this message, please notify the sender by return email, and delete this and all copies of this message and any attachments from your system. Any unauthorized disclosure, use, distribution, or reproduction of this message or any attachments is prohibited and may be unlawful,"
                    End If

                    For index = 1 To 3
                        Try
                            MC.Send(MM)
                            Log("Email sent to {0}".FormatWith(qtrEndRow.user_email))
                            If index > 1 Then
                                Log($"Email To: {qtrEndRow.user_email.Split(";")} was sent successfully after {index} retries.")
                            End If
                            Exit For
                        Catch ex As Exception
                            Log($"---------------------------------------------------------------{vbCrLf}Error sending email. Retry Count: {index}, to: {qtrEndRow.user_email}{vbCrLf} Exception: {ex.ToString()}")
                            If index = 3 Then
                                Throw
                            End If
                        End Try
                    Next
                    MC.Dispose()
                    MM.Dispose()
                End Using
            End Using
        Catch ex As Exception
            frmLogger.Error(ex, "Error sending email")
            Throw
        End Try
    End Sub

    Private Sub MarkEmailDone(email As prc_QtrEndEmailResult, hasSsaReport As Boolean, message As String)
        Dim print = New PrintingScan With {.BarCode = email.BarCode, .Note = $"{message}{email.Name} - {email.user_email}", .ScannedBy = UserName, .ScannedDate = DateTime.Now, .ShipOption = 99}
        db.PrintingScans.InsertOnSubmit(print)
        'If email.HasW2_80.GetValueOrDefault Then
        '    print = New PrintingScan With {.BarCode = email.BarCodeEeW2, .Note = "{0} - {1}".FormatWith(email.Name, email.user_email), .ScannedBy = UserName, .ScannedDate = DateTime.Now, .ShipOption = 99}
        '    db.PrintingScans.InsertOnSubmit(print)
        'End If
        'If hasSsaReport Then
        '    print = New PrintingScan With {.BarCode = email.BarCodeSsa, .Note = "{0} - {1}".FormatWith(email.Name, email.user_email), .ScannedBy = UserName, .ScannedDate = DateTime.Now, .ShipOption = 99}
        '    db.PrintingScans.InsertOnSubmit(print)
        'End If
        db.SubmitChanges()
        Log("Executed successfully")
    End Sub

    Private Sub CopyPages(fromPdf As PdfDocumentProcessor, toPdf As PdfDocumentProcessor, rptName As String)
        For index = 0 To fromPdf.Document.Pages.Count - 1
            toPdf.Document.Pages.Add(fromPdf.Document.Pages(index))
        Next

        If rptName.IsNotNullOrWhiteSpace Then
            Dim outline = New PdfBookmark With {.Title = rptName, .Destination = toPdf.CreateDestination((toPdf.Document.Pages.Count - fromPdf.Document.Pages.Count) + 1), .IsInitiallyClosed = True}

            toPdf.Document.Bookmarks.Add(outline)
            If fromPdf.Document.Pages.Count > 1 Then
                For pageIndex = 1 To fromPdf.Document.Pages.Count - 1
                    outline.Children.Add(New PdfBookmark With {.Title = "Page {0}".FormatWith(pageIndex + 1), .Destination = toPdf.CreateDestination((toPdf.Document.Pages.Count - fromPdf.Document.Pages.Count) + pageIndex + 1), .IsInitiallyClosed = True})
                Next
            End If
        End If
    End Sub

    Private Sub Log(val As String, Optional exception As Exception = Nothing, Optional isError As Boolean = False)
        Try
            If exception IsNot Nothing OrElse isError Then
                frmLogger.Error(exception, val)
                If Not IsTesting Then
                    meLog.Invoke(New Action(Sub()
                                                meErrorLog.Text &= String.Format("{0}{1}   {2}", vbCrLf, DateTime.Now, val)
                                                meErrorLog.SelectionStart = Int32.MaxValue
                                                meErrorLog.ScrollToCaret()
                                            End Sub))
                End If
            Else
                frmLogger.Information(val)
                If Not IsTesting Then
                    meLog.Invoke(New Action(Sub()
                                                meLog.Text &= String.Format("{0}{1}   {2}", vbCrLf, DateTime.Now, val)
                                                meLog.SelectionStart = Int32.MaxValue
                                                meLog.ScrollToCaret()
                                            End Sub))
                End If
            End If
        Catch ex As Exception
            frmLogger.Error(ex, "Error in Log")
        End Try
    End Sub

    Private Sub glueCoNum_Properties_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles lueCoNum.Properties.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Delete Then
            lueCoNum.EditValue = Nothing
            Dim t = LoadData()
        End If
    End Sub

    Private Sub ddbSendEmail_Click(sender As Object, e As EventArgs) Handles ddbSendEmails.Click
        Dim t = Process(True)
    End Sub

    Private Sub lueCoNum_QueryCloseUp(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles lueCoNum.QueryCloseUp
        Dim t = LoadData()
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Dim t = LoadData()
    End Sub

    Private Sub bbiProcess_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiProcess.ItemClick
        Dim t = Process(False)
    End Sub

    Private isAllRowsSelected As Boolean = False
    Private Sub gridView_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles gridView.SelectionChanged
        If e Is Nothing Then Exit Sub
        If Not ceAllowClientOnHold.Checked Then ' Permissions.AllowSendQtrEndEmailOnBillindHold Then

            If e.ControllerRow = -2147483648 Then
                Try
                    gridView.BeginUpdate()
                    If isAllRowsSelected Then
                        For Each i In gridView.GetSelectedRows
                            gridView.UnselectRow(i)
                        Next
                    Else
                        For Each i In gridView.GetSelectedRows
                            Dim row As prc_QtrEndEmailResult = gridView.GetRow(i)
                            If row.NeedApproval.IsNotNullOrWhiteSpace() Then
                                gridView.UnselectRow(i)
                            End If
                        Next
                    End If
                Finally
                    gridView.EndUpdate()
                    isAllRowsSelected = Not isAllRowsSelected
                End Try
            Else
                Dim prc As prc_QtrEndEmailResult = gridView.GetRow(e.ControllerRow)
                If prc.NeedApproval.IsNotNullOrWhiteSpace() Then
                    gridView.UnselectRow(e.ControllerRow)
                End If
            End If
        End If

        SetCount()
    End Sub

    Private Sub gridView_DataSourceChanged(sender As Object, e As EventArgs) Handles gridView.DataSourceChanged
        SetCount()
    End Sub

    Private Sub ceAllowClientOnHold_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles ceAllowClientOnHold.EditValueChanging
        If Not Permissions.AllowSendQtrEndEmailOnBillindHold Then
            DisplayMessageBox("You do not have permission to send to clients with billing hold status")
            e.Cancel = True
        ElseIf e.OldValue = False Then
            e.Cancel = Not (XtraMessageBox.Show("You are about to send clients on hold, Continue?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Send?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK)
        End If
    End Sub

    Private Sub SetCount()
        ddbSendEmails.Text = "Send ({0}) Email's".FormatWith(gridView.GetSelectedRows().Count())
        bbiProcess.Caption = "Process ({0}) Email's".FormatWith(gridView.GetSelectedRows().Count())
    End Sub

    Public Enum RunTypeEnum
        NonAca
        Aca
    End Enum

    Private Class ReportResults

        Sub New(path As String, name As String)
            Me.Name = name
            Me.Path = path
        End Sub

        Public ReadOnly Property Name As String
        Public ReadOnly Property Path As String

        Public Function ToMailAttachment() As Attachment
            Return NewAttachment(Path, Name)
        End Function
    End Class

    Public Sub SetDbContextForTesting()
        db = New dbEPDataDataContext(GetConnectionString())
    End Sub
End Class
