﻿Imports DevExpress.XtraEditors.Controls

<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucCompanyRelatedContacts
    Inherits System.Windows.Forms.UserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ucCompanyRelatedContacts))
        Dim GridFormatRule1 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleValue1 As DevExpress.XtraEditors.FormatConditionRuleValue = New DevExpress.XtraEditors.FormatConditionRuleValue()
        Dim GridFormatRule2 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleValue2 As DevExpress.XtraEditors.FormatConditionRuleValue = New DevExpress.XtraEditors.FormatConditionRuleValue()
        Dim GridFormatRule3 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleValue3 As DevExpress.XtraEditors.FormatConditionRuleValue = New DevExpress.XtraEditors.FormatConditionRuleValue()
        Dim GridFormatRule4 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleValue4 As DevExpress.XtraEditors.FormatConditionRuleValue = New DevExpress.XtraEditors.FormatConditionRuleValue()
        Dim EditorButtonImageOptions1 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject2 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject3 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject4 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Me.colMatchedBy = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.btnLinkExistingEmployee = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.btnLinkedCompanies = New DevExpress.XtraEditors.SimpleButton()
        Me.cbSearchByUdf22 = New DevExpress.XtraEditors.CheckEdit()
        Me.btnRefresh = New DevExpress.XtraEditors.SimpleButton()
        Me.cboActiveStatus = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.chkSearchByCPA = New DevExpress.XtraEditors.CheckEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.prc_GetAllRelatedContactsResultBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCoNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEmpConum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEmpNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNotes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.coluser_email = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsPayroll = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsHr = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsEndOfPeriod = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsCpa = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsPrincipal = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsClient = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsWebPost = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPhone = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colExt = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCell = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ribeRCPhoneDial = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.colContactType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRoles = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPermission = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.chkSearchByPhone = New DevExpress.XtraEditors.CheckEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.cbSearchByUdf22.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cboActiveStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkSearchByCPA.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.prc_GetAllRelatedContactsResultBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ribeRCPhoneDial, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chkSearchByPhone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'colMatchedBy
        '
        Me.colMatchedBy.FieldName = "MatchedBy"
        Me.colMatchedBy.Name = "colMatchedBy"
        Me.colMatchedBy.OptionsColumn.AllowEdit = False
        Me.colMatchedBy.Visible = True
        Me.colMatchedBy.VisibleIndex = 9
        Me.colMatchedBy.Width = 92
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.btnLinkExistingEmployee)
        Me.LayoutControl1.Controls.Add(Me.LabelControl1)
        Me.LayoutControl1.Controls.Add(Me.btnLinkedCompanies)
        Me.LayoutControl1.Controls.Add(Me.cbSearchByUdf22)
        Me.LayoutControl1.Controls.Add(Me.btnRefresh)
        Me.LayoutControl1.Controls.Add(Me.cboActiveStatus)
        Me.LayoutControl1.Controls.Add(Me.chkSearchByCPA)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Controls.Add(Me.chkSearchByPhone)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(822, 228, 650, 400)
        Me.LayoutControl1.OptionsView.UseDefaultDragAndDropRendering = False
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1301, 507)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'btnLinkExistingEmployee
        '
        Me.btnLinkExistingEmployee.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.employee_16x16
        Me.btnLinkExistingEmployee.Location = New System.Drawing.Point(678, 28)
        Me.btnLinkExistingEmployee.Name = "btnLinkExistingEmployee"
        Me.btnLinkExistingEmployee.Size = New System.Drawing.Size(135, 22)
        Me.btnLinkExistingEmployee.StyleController = Me.LayoutControl1
        Me.btnLinkExistingEmployee.TabIndex = 9
        Me.btnLinkExistingEmployee.Text = "Link Existing Employee"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Georgia", 8.25!, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseTextOptions = True
        Me.LabelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl1.Location = New System.Drawing.Point(2, 60)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(1297, 13)
        Me.LabelControl1.StyleController = Me.LayoutControl1
        Me.LabelControl1.TabIndex = 8
        Me.LabelControl1.Text = "Right Click To Add \ Edit \ or to Remove Contact"
        '
        'btnLinkedCompanies
        '
        Me.btnLinkedCompanies.ImageOptions.Image = CType(resources.GetObject("btnLinkedCompanies.ImageOptions.Image"), System.Drawing.Image)
        Me.btnLinkedCompanies.Location = New System.Drawing.Point(1063, 28)
        Me.btnLinkedCompanies.Name = "btnLinkedCompanies"
        Me.btnLinkedCompanies.Size = New System.Drawing.Size(230, 22)
        Me.btnLinkedCompanies.StyleController = Me.LayoutControl1
        Me.btnLinkedCompanies.TabIndex = 6
        Me.btnLinkedCompanies.Text = "Add \ Edit Linked Companies (UDF 22)"
        '
        'cbSearchByUdf22
        '
        Me.cbSearchByUdf22.Location = New System.Drawing.Point(216, 28)
        Me.cbSearchByUdf22.Name = "cbSearchByUdf22"
        Me.cbSearchByUdf22.Properties.Caption = "Match By Udf 22"
        Me.cbSearchByUdf22.Size = New System.Drawing.Size(109, 19)
        Me.cbSearchByUdf22.StyleController = Me.LayoutControl1
        Me.cbSearchByUdf22.TabIndex = 7
        '
        'btnRefresh
        '
        Me.btnRefresh.ImageOptions.Image = CType(resources.GetObject("btnRefresh.ImageOptions.Image"), System.Drawing.Image)
        Me.btnRefresh.Location = New System.Drawing.Point(581, 28)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Size = New System.Drawing.Size(93, 22)
        Me.btnRefresh.StyleController = Me.LayoutControl1
        Me.btnRefresh.TabIndex = 4
        Me.btnRefresh.Text = "Refresh"
        '
        'cboActiveStatus
        '
        Me.cboActiveStatus.EditValue = "Active"
        Me.cboActiveStatus.Location = New System.Drawing.Point(429, 28)
        Me.cboActiveStatus.Name = "cboActiveStatus"
        Me.cboActiveStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cboActiveStatus.Properties.Items.AddRange(New Object() {"Active", "Inactive", "All"})
        Me.cboActiveStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cboActiveStatus.Size = New System.Drawing.Size(148, 20)
        Me.cboActiveStatus.StyleController = Me.LayoutControl1
        Me.cboActiveStatus.TabIndex = 3
        '
        'chkSearchByCPA
        '
        Me.chkSearchByCPA.Location = New System.Drawing.Point(117, 28)
        Me.chkSearchByCPA.Name = "chkSearchByCPA"
        Me.chkSearchByCPA.Properties.Caption = "Match By CPA"
        Me.chkSearchByCPA.Size = New System.Drawing.Size(95, 19)
        Me.chkSearchByCPA.StyleController = Me.LayoutControl1
        Me.chkSearchByCPA.TabIndex = 1
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.prc_GetAllRelatedContactsResultBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(2, 77)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.ribeRCPhoneDial})
        Me.GridControl1.Size = New System.Drawing.Size(1297, 428)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'prc_GetAllRelatedContactsResultBindingSource
        '
        Me.prc_GetAllRelatedContactsResultBindingSource.DataSource = GetType(Brands_FrontDesk.prc_GetAllRelatedContactsResult)
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNum, Me.colCO_NAME, Me.colCO_STATUS, Me.colEmpConum, Me.colEmpNum, Me.colName, Me.colNotes, Me.coluser_email, Me.colIsPayroll, Me.colIsHr, Me.colIsEndOfPeriod, Me.colIsCpa, Me.colIsPrincipal, Me.colIsClient, Me.colIsWebPost, Me.colMatchedBy, Me.colPhone, Me.colExt, Me.colCell, Me.colContactType, Me.GridColumn1, Me.colRoles, Me.colPermission})
        GridFormatRule1.Column = Me.colMatchedBy
        GridFormatRule1.ColumnApplyTo = Me.colMatchedBy
        GridFormatRule1.Name = "Format0"
        FormatConditionRuleValue1.Appearance.BackColor = System.Drawing.Color.Moccasin
        FormatConditionRuleValue1.Appearance.Options.UseBackColor = True
        FormatConditionRuleValue1.Condition = DevExpress.XtraEditors.FormatCondition.Equal
        FormatConditionRuleValue1.Value1 = "UDF22"
        GridFormatRule1.Rule = FormatConditionRuleValue1
        GridFormatRule2.Column = Me.colMatchedBy
        GridFormatRule2.ColumnApplyTo = Me.colMatchedBy
        GridFormatRule2.Name = "Format1"
        FormatConditionRuleValue2.Appearance.BackColor = System.Drawing.Color.LightPink
        FormatConditionRuleValue2.Appearance.Options.UseBackColor = True
        FormatConditionRuleValue2.Condition = DevExpress.XtraEditors.FormatCondition.Equal
        FormatConditionRuleValue2.Value1 = "UDF22 2nd Level"
        GridFormatRule2.Rule = FormatConditionRuleValue2
        GridFormatRule3.Column = Me.colMatchedBy
        GridFormatRule3.ColumnApplyTo = Me.colMatchedBy
        GridFormatRule3.Name = "Format2"
        FormatConditionRuleValue3.Appearance.BackColor = System.Drawing.Color.LavenderBlush
        FormatConditionRuleValue3.Appearance.Options.UseBackColor = True
        FormatConditionRuleValue3.Condition = DevExpress.XtraEditors.FormatCondition.Equal
        FormatConditionRuleValue3.Value1 = "Phone"
        GridFormatRule3.Rule = FormatConditionRuleValue3
        GridFormatRule4.Column = Me.colMatchedBy
        GridFormatRule4.ColumnApplyTo = Me.colMatchedBy
        GridFormatRule4.Name = "Format3"
        FormatConditionRuleValue4.Appearance.BackColor = System.Drawing.Color.GhostWhite
        FormatConditionRuleValue4.Appearance.Options.UseBackColor = True
        FormatConditionRuleValue4.Condition = DevExpress.XtraEditors.FormatCondition.Equal
        FormatConditionRuleValue4.Value1 = "CPA"
        GridFormatRule4.Rule = FormatConditionRuleValue4
        Me.GridView1.FormatRules.Add(GridFormatRule1)
        Me.GridView1.FormatRules.Add(GridFormatRule2)
        Me.GridView1.FormatRules.Add(GridFormatRule3)
        Me.GridView1.FormatRules.Add(GridFormatRule4)
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.ReadOnly = True
        Me.GridView1.OptionsView.ShowDetailButtons = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'colCoNum
        '
        Me.colCoNum.FieldName = "CoNum"
        Me.colCoNum.Name = "colCoNum"
        Me.colCoNum.OptionsColumn.AllowEdit = False
        Me.colCoNum.Visible = True
        Me.colCoNum.VisibleIndex = 0
        Me.colCoNum.Width = 106
        '
        'colCO_NAME
        '
        Me.colCO_NAME.FieldName = "CO_NAME"
        Me.colCO_NAME.Name = "colCO_NAME"
        Me.colCO_NAME.OptionsColumn.AllowEdit = False
        Me.colCO_NAME.Visible = True
        Me.colCO_NAME.VisibleIndex = 1
        Me.colCO_NAME.Width = 106
        '
        'colCO_STATUS
        '
        Me.colCO_STATUS.FieldName = "CO_STATUS"
        Me.colCO_STATUS.Name = "colCO_STATUS"
        Me.colCO_STATUS.OptionsColumn.AllowEdit = False
        '
        'colEmpConum
        '
        Me.colEmpConum.FieldName = "EmpConum"
        Me.colEmpConum.Name = "colEmpConum"
        Me.colEmpConum.OptionsColumn.AllowEdit = False
        Me.colEmpConum.Visible = True
        Me.colEmpConum.VisibleIndex = 2
        Me.colEmpConum.Width = 106
        '
        'colEmpNum
        '
        Me.colEmpNum.FieldName = "EmpNum"
        Me.colEmpNum.Name = "colEmpNum"
        Me.colEmpNum.OptionsColumn.AllowEdit = False
        Me.colEmpNum.Visible = True
        Me.colEmpNum.VisibleIndex = 3
        Me.colEmpNum.Width = 106
        '
        'colName
        '
        Me.colName.FieldName = "Name"
        Me.colName.Name = "colName"
        Me.colName.OptionsColumn.AllowEdit = False
        Me.colName.Visible = True
        Me.colName.VisibleIndex = 4
        Me.colName.Width = 106
        '
        'colNotes
        '
        Me.colNotes.Caption = "Notes"
        Me.colNotes.FieldName = "Notes"
        Me.colNotes.MinWidth = 100
        Me.colNotes.Name = "colNotes"
        Me.colNotes.OptionsColumn.AllowEdit = False
        Me.colNotes.Visible = True
        Me.colNotes.VisibleIndex = 5
        Me.colNotes.Width = 181
        '
        'coluser_email
        '
        Me.coluser_email.Caption = "Email"
        Me.coluser_email.FieldName = "user_email"
        Me.coluser_email.Name = "coluser_email"
        Me.coluser_email.OptionsColumn.AllowEdit = False
        Me.coluser_email.Visible = True
        Me.coluser_email.VisibleIndex = 8
        Me.coluser_email.Width = 92
        '
        'colIsPayroll
        '
        Me.colIsPayroll.FieldName = "IsPayroll"
        Me.colIsPayroll.Name = "colIsPayroll"
        Me.colIsPayroll.OptionsColumn.AllowEdit = False
        '
        'colIsHr
        '
        Me.colIsHr.FieldName = "IsHr"
        Me.colIsHr.Name = "colIsHr"
        Me.colIsHr.OptionsColumn.AllowEdit = False
        '
        'colIsEndOfPeriod
        '
        Me.colIsEndOfPeriod.FieldName = "IsEndOfPeriod"
        Me.colIsEndOfPeriod.Name = "colIsEndOfPeriod"
        Me.colIsEndOfPeriod.OptionsColumn.AllowEdit = False
        '
        'colIsCpa
        '
        Me.colIsCpa.FieldName = "IsCpa"
        Me.colIsCpa.Name = "colIsCpa"
        Me.colIsCpa.OptionsColumn.AllowEdit = False
        '
        'colIsPrincipal
        '
        Me.colIsPrincipal.FieldName = "IsPrincipal"
        Me.colIsPrincipal.Name = "colIsPrincipal"
        Me.colIsPrincipal.OptionsColumn.AllowEdit = False
        '
        'colIsClient
        '
        Me.colIsClient.FieldName = "IsClient"
        Me.colIsClient.Name = "colIsClient"
        Me.colIsClient.OptionsColumn.AllowEdit = False
        '
        'colIsWebPost
        '
        Me.colIsWebPost.FieldName = "IsWebPost"
        Me.colIsWebPost.Name = "colIsWebPost"
        Me.colIsWebPost.OptionsColumn.AllowEdit = False
        '
        'colPhone
        '
        Me.colPhone.FieldName = "Phone"
        Me.colPhone.Name = "colPhone"
        Me.colPhone.OptionsColumn.AllowEdit = False
        '
        'colExt
        '
        Me.colExt.FieldName = "Ext"
        Me.colExt.Name = "colExt"
        Me.colExt.OptionsColumn.AllowEdit = False
        '
        'colCell
        '
        Me.colCell.ColumnEdit = Me.ribeRCPhoneDial
        Me.colCell.FieldName = "Cell"
        Me.colCell.Name = "colCell"
        Me.colCell.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways
        Me.colCell.Visible = True
        Me.colCell.VisibleIndex = 7
        Me.colCell.Width = 92
        '
        'ribeRCPhoneDial
        '
        Me.ribeRCPhoneDial.AutoHeight = False
        EditorButtonImageOptions1.Image = Global.Brands_FrontDesk.My.Resources.Resources.Phone_Small
        Me.ribeRCPhoneDial.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions1, New DevExpress.Utils.KeyShortcut((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.P)), SerializableAppearanceObject1, SerializableAppearanceObject2, SerializableAppearanceObject3, SerializableAppearanceObject4, "Call", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.ribeRCPhoneDial.Name = "ribeRCPhoneDial"
        '
        'colContactType
        '
        Me.colContactType.FieldName = "ContactType"
        Me.colContactType.Name = "colContactType"
        Me.colContactType.OptionsColumn.AllowEdit = False
        Me.colContactType.Visible = True
        Me.colContactType.VisibleIndex = 12
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "Phone"
        Me.GridColumn1.ColumnEdit = Me.ribeRCPhoneDial
        Me.GridColumn1.FieldName = "PhoneExt"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways
        Me.GridColumn1.UnboundExpression = "[Phone] + Iif([Ext] Is Not Null And [Ext] <> '', ' x ' + [Ext], '')"
        Me.GridColumn1.UnboundType = DevExpress.Data.UnboundColumnType.[String]
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 6
        Me.GridColumn1.Width = 92
        '
        'colRoles
        '
        Me.colRoles.Caption = "Roles"
        Me.colRoles.FieldName = "Roles"
        Me.colRoles.Name = "colRoles"
        Me.colRoles.OptionsColumn.AllowEdit = False
        Me.colRoles.Visible = True
        Me.colRoles.VisibleIndex = 10
        Me.colRoles.Width = 67
        '
        'colPermission
        '
        Me.colPermission.FieldName = "Permission"
        Me.colPermission.Name = "colPermission"
        Me.colPermission.OptionsColumn.AllowEdit = False
        Me.colPermission.Visible = True
        Me.colPermission.VisibleIndex = 11
        Me.colPermission.Width = 132
        '
        'chkSearchByPhone
        '
        Me.chkSearchByPhone.Location = New System.Drawing.Point(8, 28)
        Me.chkSearchByPhone.Name = "chkSearchByPhone"
        Me.chkSearchByPhone.Properties.Caption = "Match By Phone"
        Me.chkSearchByPhone.Size = New System.Drawing.Size(105, 19)
        Me.chkSearchByPhone.StyleController = Me.LayoutControl1
        Me.chkSearchByPhone.TabIndex = 0
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlGroup2, Me.LayoutControlItem2})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1301, 507)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 75)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(1301, 432)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem6, Me.LayoutControlItem5, Me.LayoutControlItem4, Me.LayoutControlItem3, Me.LayoutControlItem7, Me.LayoutControlItem8, Me.EmptySpaceItem1, Me.LayoutControlItem9})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Padding = New DevExpress.XtraLayout.Utils.Padding(3, 3, 3, 3)
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(1301, 58)
        Me.LayoutControlGroup2.Text = "Load Options"
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.cboActiveStatus
        Me.LayoutControlItem6.Location = New System.Drawing.Point(321, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(252, 26)
        Me.LayoutControlItem6.Text = "Company Statuses: "
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(97, 13)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.cbSearchByUdf22
        Me.LayoutControlItem5.Location = New System.Drawing.Point(208, 0)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(113, 26)
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.chkSearchByCPA
        Me.LayoutControlItem4.Location = New System.Drawing.Point(109, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(99, 26)
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.chkSearchByPhone
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(109, 26)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.btnRefresh
        Me.LayoutControlItem7.Location = New System.Drawing.Point(573, 0)
        Me.LayoutControlItem7.MaxSize = New System.Drawing.Size(97, 26)
        Me.LayoutControlItem7.MinSize = New System.Drawing.Size(97, 26)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(97, 26)
        Me.LayoutControlItem7.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.btnLinkedCompanies
        Me.LayoutControlItem8.Location = New System.Drawing.Point(1055, 0)
        Me.LayoutControlItem8.MaxSize = New System.Drawing.Size(234, 0)
        Me.LayoutControlItem8.MinSize = New System.Drawing.Size(234, 26)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(234, 26)
        Me.LayoutControlItem8.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(809, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(246, 26)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.btnLinkExistingEmployee
        Me.LayoutControlItem9.Location = New System.Drawing.Point(670, 0)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(139, 26)
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.LabelControl1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 58)
        Me.LayoutControlItem2.MinSize = New System.Drawing.Size(70, 17)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(1301, 17)
        Me.LayoutControlItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'ucCompanyRelatedContacts
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "ucCompanyRelatedContacts"
        Me.Size = New System.Drawing.Size(1301, 507)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.cbSearchByUdf22.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cboActiveStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkSearchByCPA.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.prc_GetAllRelatedContactsResultBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ribeRCPhoneDial, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chkSearchByPhone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents prc_GetAllRelatedContactsResultBindingSource As BindingSource
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colCoNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEmpConum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEmpNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents coluser_email As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsPayroll As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsHr As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsEndOfPeriod As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsCpa As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsPrincipal As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsClient As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsWebPost As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colMatchedBy As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnRefresh As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents cboActiveStatus As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents chkSearchByCPA As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents chkSearchByPhone As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colPhone As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colExt As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCell As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colContactType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnLinkedCompanies As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents colCO_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents cbSearchByUdf22 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPermission As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnLinkExistingEmployee As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colRoles As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNotes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ribeRCPhoneDial As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
End Class
