﻿Namespace Ach
    Public Class AchSettings
        Public Property AutoEmailsSql As String
        Public Property StatusListString As String
        Public ReadOnly Property StatusList As Array
            Get
                Return StatusListString?.Replace(vbCrLf, "~").Split("~")
            End Get
        End Property
        Public Property StatusDefaultFilter As String

        Public Property InitialDirectoryForFiles As String
    End Class
End Namespace