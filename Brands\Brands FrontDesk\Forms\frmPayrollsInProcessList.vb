﻿Imports System.Data
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports Microsoft.AspNetCore.SignalR.Client
Imports QueueBuilder

Public Class frmPayrollsInProcessList


    Dim DB As dbEPDataDataContext
    Dim _isLoaded As Boolean
    Dim CurrentRec As view_PayrollInProcess
    Dim _EnableProcess As Boolean
    Dim whoIsCalling As String = ""
    Dim _logger As Serilog.ILogger

    Dim NoteTypeWithOption As Dictionary(Of String, String)

    Public Sub New()
        InitializeComponent()
        _logger = Logger.ForContext(Of frmPayrollsInProcessList)
        QueuePayrollProcessor.Initialize(GetConnectionString, Logger, Nothing, UserName)
    End Sub

    Private Sub SetToCheckDate()
        Dim DtmToCheckDate As Date = DateTime.Today.AddDays(1)

        If DB Is Nothing Then
            DB = New dbEPDataDataContext(GetConnectionString)
        End If

        Do While (DtmToCheckDate.DayOfWeek = DayOfWeek.Saturday OrElse DtmToCheckDate.DayOfWeek = DayOfWeek.Sunday OrElse DB.Bank_Holidays.Where(Function(d) d.holiday_date = DtmToCheckDate).Count <> 0)
            DtmToCheckDate = DtmToCheckDate.AddDays(1)
        Loop
        Me.deToCheckDate.DateTime = DtmToCheckDate
    End Sub

    Private Sub frmPayrollsInProcessList_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        UcCompInfo1.TabbedControlGroup1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        NoteTypeWithOption = New Dictionary(Of String, String)
        NoteTypeWithOption.Add("MANCHKTOADD", "Add Manual Checks")
        NoteTypeWithOption.Add("MANCHKTOADDYR", "Add Manuals Not From Current Year")
        NoteTypeWithOption.Add("VOIDSTOADD", "Add Voids")
        NoteTypeWithOption.Add("VOIDSTOADDYR", "Add Voids Not From Current Year")
        NoteTypeWithOption.Add("AUTO2NDCHK", "Add Second checks")
        NoteTypeWithOption.Add("GRSTONET", "Calculate Check")
        NoteTypeWithOption.Add("RES_ST_OVRIDE", "Process Res State Overrides")
        NoteTypeWithOption.Add("NETOVR", "Process Net Overrides")
        NoteTypeWithOption.Add("EMLREG", "Send Register")
        NoteTypeWithOption.Add("FICA_PENY_OVRIDE", "Fix Fica/Fed Penny")
        NoteTypeWithOption.Add("FED_PENY_OVRIDE", "Fix Fica/Fed Penny")
        NoteTypeWithOption.Add("PASTCHKDATE", "Fix Check Date")
        NoteTypeWithOption.Add("NONET", "Delete Zero Checks")
        NoteTypeWithOption.Add("Review_Excessive_Hours", "Mark Excessive Hours Reviewed")
        NoteTypeWithOption.Add("CLIENTS_MSG", "Fix Check Date")
        'NoteTypeWithOption.Add("DUP_CHK_NUMS", "Change payroll starting check number")

        'Me.SplitContainerControl2.PanelVisibility = DevExpress.XtraEditors.SplitPanelVisibility.Panel1
        'Solomon modified on Feb 23, '21.  Get next business day
        'Me.deToCheckDate.DateTime = DateTime.Today

        SetToCheckDate()

        Try
            Dim isInUserList = GetUdfValueSplitted("PrInProc_ShowQueueOption_Users").Contains(UserName.ToLower)
            Dim udfName As Object = IIf(isInUserList, "PrInProc_bciProcessInBackground_ConfigValue_Users", "PrInProc_bciProcessInBackground_ConfigValue_All")
            Dim configValue As Integer = GetUdfValue(udfName).Trim()
            _logger.Debug("bciProcessInBackground: isInUserList: {isInUserList} udfName: {udfName} configValue: {configValue}", isInUserList, udfName, configValue)

            bciProcessInBackground.Enabled = (configValue And 1) > 0
            bciProcessInBackground.Visibility = ((configValue And 2) > 0).ToBarItemVisibility
            bciProcessInBackground.Checked = (configValue And 4) > 0

            _logger.Debug("bciProcessInBackground Enabled: {Enabled} Visibility: {Visibility} Checked: {Checked}", bciProcessInBackground.Enabled, bciProcessInBackground.Visibility, bciProcessInBackground.Checked)
        Catch ex As Exception
            _logger.Error(ex, "Error in ShowQueueOption")
        End Try

        Try
            AddHandler modSignalRCoreClient.OnPrInProcessUpdate, AddressOf PrInProcessUpdated
        Catch ex As Exception
            _logger.Error(ex, "Error connecting to SignalR")
        End Try

        Try
            rpgBackgroundAutoFixPayrolls.Visible = modGlobals.UserInRole("AllowManageBackgroundAutoFixPayroll", UserName)
            bciAutoFixStatus.Checked = GetUdfValue("BackgroundAutofixPayrolls Status") = "On"
            AddHandler bciAutoFixStatus.CheckedChanged, AddressOf bciAutoFixStatus_CheckedChanged
        Catch ex As Exception
            _logger.Error(ex, "Error in bciAutoFixStatus")
        End Try

        LoadData()

        For Each PrinterName In Printing.PrinterSettings.InstalledPrinters
            Me.cbPrinters.Properties.Items.Add(PrinterName)
        Next
        Dim defaultPrinting = New Printing.PrinterSettings
        Me.cbPrinters.EditValue = defaultPrinting.PrinterName

        Try
            Dim comList = DB.COMPANies.Select(Function(c) New With {Key .CONUM = c.CONUM}).ToList

            riSlueCoNumName1.DataSource = DB.view_CompanySumarries.ToList()
            riSlueCoNumName1.DisplayMember = "CONUM"
            riSlueCoNumName1.ValueMember = "CONUM"

            riSlueCoNumName2.DataSource = DB.view_CompanySumarries.ToList()
            riSlueCoNumName2.DisplayMember = "CONUM"
            riSlueCoNumName2.ValueMember = "CONUM"

            riSlueCoNumName3.DataSource = DB.view_CompanySumarries.ToList()
            riSlueCoNumName3.DisplayMember = "CONUM"
            riSlueCoNumName3.ValueMember = "CONUM"
        Catch ex As Exception
            _logger.Error(ex, "Error in loading riSlueCoNumName")
        End Try

        SetupGridView(GridViewIncompletePayroll, colIncompletePayrollCoNum)
        SetupGridView(GridViewNSFHold, colNsfHoldCoNum)
        SetupGridView(GridViewPayrollsCurrentlyProcessing, colPowerGridPayrollCurProcCoNum)
    End Sub

    Private Sub PrInProcessUpdated(sender As Object, e As EventArgs)
        Try
            If InvokeRequired Then
                MainForm.TryInvoke(Sub() PrInProcessUpdated(sender, e))
                Exit Sub
            End If

            Dim pr As Brands.DAL.DomainModels.App.PayrollInProcessUpdate = sender
            If pr IsNot Nothing Then
                Dim list As List(Of view_PayrollInProcess) = Me.ViewPayrollInProcessBindingSource.DataSource
                Dim match = list.SingleOrDefault(Function(p) p.ID = pr.Id)
                If match IsNot Nothing Then
                    _logger.Debug("ID: {ID} Remove: {Remove} IsQueued: {IsQueued}", pr.Id, pr.IsQueued, pr.Remove)
                    If pr.Remove Then
                        list.Remove(match)
                        GridView1.RefreshData()
                    Else
                        match.IsQueued = pr.IsQueued
                        match.AutoFixPayrollCompletedOn = pr.AutoFixPayrollCompletedOn
                        GridView1.RefreshData()
                    End If
                Else
                    _logger.Debug("ID: {ID} is not in the list", pr.Id)
                End If
            End If
        Catch ex As Exception
            _logger.Error(ex, "Error in PrInProcessUpdated. {@PayrollInProcessUpdate}", sender)
        End Try
    End Sub

    Sub LoadData()
        Try
            _isLoaded = False

            Me.PrbatchmsgBindingSource.Clear()
            Me.ViewPayrollInProcessBindingSource.Clear()

            DB = New dbEPDataDataContext(GetConnectionString)
            DB.CommandTimeout = 1000
            Dim Data = (From A In DB.view_PayrollInProcesses Where A.ProcessStatus = "Ready" OrElse A.ProcessStatus = "NSF Hold").ToList
            Me.ViewPayrollInProcessBindingSource.DataSource = (From A In Data Where A.ProcessStatus = "Ready").ToList

            Dim NsfDataSource = (From A In Data Where A.ProcessStatus = "NSF Hold").ToList
            Me.ViewPayrollInProcessBindingSourceNSFHold.DataSource = NsfDataSource

            xtpNsfHold.Text = String.Format("NSF Hold ({0})", NsfDataSource.Count)

            'Solomon modified on DEec 31, 20.  Show always
            'If Me.chkShowProcessing.Checked Then
            '    Dim Data1 As IEnumerable(Of view_PayrollInProcess) = DB.view_PayrollInProcesses.Where(Function(A) A.ProcessStatus <> "Ready" AndAlso A.ProcessStatus <> "NSF Hold")
            '    If Not Permissions.AllowProcessingPayroll.GetValueOrDefault Then
            '        Data1 = Data1.Where(Function(a) a.ProcessedBy = UserName)
            '    End If
            '    Me.ViewPayrollInProcessStuckBindingSource.DataSource = Data1.ToList
            'End If

            Dim Data1 As IEnumerable(Of view_PayrollInProcess) = DB.view_PayrollInProcesses.Where(Function(A) A.ProcessStatus <> "Ready" AndAlso A.ProcessStatus <> "NSF Hold")
            If Not nz(Permissions.AllowProcessingPayroll, False).GetValueOrDefault Then
                Data1 = Data1.Where(Function(a) a.ProcessedBy = UserName OrElse a.LastOpenedBy = UserName)
            End If
            Me.ViewPayrollInProcessStuckBindingSource.DataSource = Data1.ToList
            xtpPowerGridProc.Text = String.Format("PowerGrid Proc. ({0})", Data1.Count)

            LoadPayrollsIncomplete()
            GridViewPayrollsCurrentlyProcessing.BestFitColumns()
            colPowerGridPayrollCurProcCoNum.Width = 65
            GridViewNSFHold.BestFitColumns()
            colNsfHoldCoNum.Width = 65
            _isLoaded = True
            ViewPayrollInProcessBindingSource_CurrentChanged(Me, New EventArgs)
            MainForm.RefreshBadgeCounter()
            ToggleSwitchIgnoreIssues.Enabled = UserInRole("AllowEnforce")
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Sub LoadPayrollsIncomplete()
        Try
            If DB Is Nothing Then Return
            Dim DataPayrollsIncomplete = DB.prc_PayrollsIncomplete(deToCheckDate.DateTime, IIf(ceShowAll.Checked, "Yes", "No")).ToList()

            Dim line As prc_PayrollsIncompleteResult
            For Each line In DataPayrollsIncomplete
                Dim notesList = getPayrollNotes(line.CONUM, line.PRNUM)
                If notesList.Count <> 0 Then
                    Dim note = notesList.OrderBy(Function(f) f.Priority).FirstOrDefault()
                    line.Note = note.Note
                    line.NotePriority = note.Priority.Substring(0, 1)
                End If
            Next

            If Not nz(Permissions.AllowProcessingPayroll, False).GetValueOrDefault Then
                DataPayrollsIncomplete = DataPayrollsIncomplete.Where(Function(a) a.OPNAME = UserName OrElse a.LastOpenedBy = UserName).ToList()
            End If

            GridControlIncompletePayrolls.DataSource = DataPayrollsIncomplete
            GridViewIncompletePayroll.BestFitColumns()
            colIncompletePayrollCoNum.Width = 65
            xtpIncompletePayroll.Text = String.Format("Incomplete Payroll ({0})", DataPayrollsIncomplete.Count)
        Catch ex As Exception
            DisplayErrorMessage("Error loading data payrolls incomplete", ex)
        End Try
    End Sub

    Function getPayrollNotes(CoNum As Decimal, PrNum As Decimal) As List(Of pr_batch_note)
        Dim BatchNotes = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = PrNum AndAlso A.EmployeeID = -995).ToList
        Return BatchNotes
    End Function

    Function getNotes(CoNum As Decimal, PrNum As Decimal, CheckDate As Date) As List(Of pr_batch_msg)
        Dim NotesList As New List(Of pr_batch_msg)

        Dim coNotes = (From A In DB.NOTEs
                       Where A.conum = CoNum _
                       AndAlso {"Payroll", "Company", "Employee"}.Contains(A.category)).ToList

        Dim CompanyAndPayrollNotes = (From A In coNotes
                                      Where {"Payroll", "Company"}.Contains(A.category) _
                                      AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date > Today)
                                      Select New pr_batch_msg With {.msg_type = A.category,
                                                                    .msg_body = A.title & ": " & A.ParsedNote,
                                                                    .Priority = A.priority,
                                                                    .id = A.note_id}
                                      ).ToList
        NotesList.AddRange(CompanyAndPayrollNotes)

        Dim TaxDue = DB.prc_NyTaxOverdue(CoNum, CheckDate).ToList

        Dim TaxDueNotes = (From A In TaxDue Select New pr_batch_msg With {
                                                          .msg_type = "TAXDUE",
                                                          .msg_body = "Fed Tax was not closed out, resubmit from EP",
                                                          .Priority = "1-High"}
                                                   ).ToList
        NotesList.AddRange(TaxDueNotes)

        'pr_batch_note
        Dim BatchNotes = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = PrNum AndAlso (A.EmployeeID <> -995 OrElse A.EmployeeID Is Nothing)).ToList
        NotesList.AddRange(From A In BatchNotes Select New pr_batch_msg With {.empnum = A.EmployeeID,
                                                                              .msg_type = "SpcMsg",
                                                                              .msg_body = String.Format("{0} {1}{2}<b>{3}</b>",
                                                                                    A.EnteredBy,
                                                                                    IIf(A.DateEntered.HasValue, "[on " + A.DateEntered.GetValueOrDefault + "]", ""),
                                                                                    IIf(A.DateEntered.HasValue OrElse A.EnteredBy <> Nothing, ": ", ""),
                                                                                    A.Note
                                                                               ),
                                                                              .chk_counter = A.CheckNum,
                                                                              .Priority = A.Priority})

        'replaced .msg_body = $"{A.EnteredBy} [on {A.DateEntered.GetValueOrDefault}]: <b>{A.Note}</b>",

        'CR03
        If (From A In DB.COUSERDEFs Where A.conum = CoNum AndAlso A.udf8_data = "CR03").Count > 0 Then
            NotesList.Add(New pr_batch_msg With {.msg_body = "Give to Mordy to review", .msg_type = "Audit"})
        End If

        'prc_CheckPayrollIssues
        Dim IssuecCount As Integer
        Dim CheckPayrollIssues = DB.prc_CheckPayrollIssues(CoNum, PrNum, True, IssuecCount).ToList
        If CheckPayrollIssues.Count > 0 Then
            Dim NoteID As Guid, Note As NOTE
            For Each pNote In CheckPayrollIssues
                Dim msg = New pr_batch_msg With {.empnum = pNote.EmpNum,
                                                 .chk_counter = pNote.ChkCounter,
                                                 .msg_type = pNote.MsgType,
                                                 .msg_body = pNote.MsgBody,
                                                 .Priority = pNote.Priority,
                                                 .AgentRequired = pNote.AgentRequired
                                                }
                If Guid.TryParse(pNote.MsgBody, NoteID) Then
                    Note = (From A In coNotes Where A.note_id = NoteID).FirstOrDefault
                    If Note IsNot Nothing Then
                        msg.msg_body = Note.title & ": " & Note.ParsedNote
                        msg.Priority = Note.priority
                        msg.id = Note.note_id
                    End If
                End If
                NotesList.Add(msg)
            Next
        End If

        Return NotesList
    End Function

    Sub LoadNotes()
        If CurrentRec Is Nothing Then Exit Sub

        If Not CurrentRec.DoNotRunUtilityImport.HasValue OrElse Not CurrentRec.DoNotRunUtilityImport.Value Then
            CurrentRec.DoNotRunUtilityImport = CurrentRec.DoNotAutoRunUtilitySecondTime
        End If

        Dim NotesList = getNotes(CurrentRec.CoNum, CurrentRec.PrNum, CurrentRec.CHECK_DATE)

        Dim MsgCount = (From A In NotesList Where A.Priority Is Nothing OrElse A.Priority <> "3-Low").Count
        Me.GridViewNotes.ViewCaption = String.Format("Notes for Co #: {0} - Pr #: {1}     (Count: {2})", CurrentRec.CoNum, CurrentRec.PrNum, MsgCount)
        NotesList = (From A In NotesList Order By A.empnum, A.chk_counter, A.msg_type).ToList

        'auto check email reg and client notes 
        If CurrentRec.ClientApprovedRegisterOn.HasValue Then
            Dim note = NotesList.SingleOrDefault(Function(n) n.msg_type = "EMLREG")
            If note IsNot Nothing Then
                note.IsChecked = True
            End If
        End If
        If CurrentRec.HandledClientMessageOn.HasValue Then
            Dim note = NotesList.SingleOrDefault(Function(n) n.msg_type = "CLIENTS_MSG")
            If note IsNot Nothing Then
                note.IsChecked = True
            End If
        End If

        Me.PrbatchmsgBindingSource.DataSource = NotesList
        CheckNotes()
    End Sub

    Private Sub ViewPayrollInProcessBindingSource_CurrentChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ViewPayrollInProcessBindingSource.CurrentChanged
        If Not _isLoaded Then Exit Sub
        CurrentRec = Me.ViewPayrollInProcessBindingSource.Current
        If CurrentRec Is Nothing Then
            Exit Sub
        End If

        pnlUtilityImport.Visible = CurrentRec IsNot Nothing AndAlso Not String.IsNullOrEmpty(CurrentRec.Run_prc_PrUtilityImport)

        LoadNotes()

        If CurrentRec Is Nothing Then
            Me.btnTake.Enabled = False
            Me.btnReleasePayroll.Enabled = False
            _EnableProcess = False
            Me.btnHandleAllIssues.Enabled = False
        ElseIf CurrentRec.Grp = 7 Then
            Me.btnTake.Enabled = False
            Me.btnReleasePayroll.Enabled = False
            _EnableProcess = False
        ElseIf CurrentRec.CompleteBy = UserName Then
            Me.btnTake.Enabled = False
            Me.btnReleasePayroll.Enabled = True
            _EnableProcess = True
        ElseIf CurrentRec.CompleteBy IsNot Nothing AndAlso CurrentRec.CompleteBy.Contains(".") Then
            Me.btnTake.Enabled = False
            Me.btnReleasePayroll.Enabled = False
            _EnableProcess = False
        Else
            Me.btnTake.Enabled = True
            Me.btnReleasePayroll.Enabled = False
            _EnableProcess = True
        End If
        CheckNotes()

        ToggleSwitchIgnoreIssues.IsOn = CurrentRec.IgnoreIssues.HasValue AndAlso CurrentRec.IgnoreIssues = 100
        'ToggleSwitchIgnoreIssues.Enabled = Not CurrentRec.IgnoreIssues.HasValue OrElse CurrentRec.IgnoreIssues <> 100
        ToggleSwitchSubmitInEP.IsOn = CurrentRec.SubmitInEP.HasValue AndAlso CurrentRec.SubmitInEP = True

        Dim comp = DB.COMPANies.SingleOrDefault(Function(c) c.CONUM = CurrentRec.CoNum)
        If comp Is Nothing Then
            UcCompInfo1.Clean()
            Exit Sub
        End If
        Dim _empsCount = DB.EMPLOYEEs.Where(Function(em) em.CONUM = CurrentRec.CoNum AndAlso Not em.TERM_DATE.HasValue).Count
        Dim _coOptions = DB.COOPTIONs.FirstOrDefault(Function(c) c.CONUM = CurrentRec.CoNum)
        Me.UcCompInfo1.LoadDate(comp, _empsCount, _coOptions)

        Dim allowSubmitPayroll = CurrentRec.Grp <> 7
        Dim allowToggleSwitchIgnoreIssues = UserInRole("AllowEnforce") AndAlso CurrentRec.Grp <> 7

        If GridControlNotes.Enabled <> allowSubmitPayroll Then
            GridControlNotes.Enabled = allowSubmitPayroll
        End If

        If ToggleSwitchIgnoreIssues.Enabled <> allowToggleSwitchIgnoreIssues Then
            ToggleSwitchIgnoreIssues.Enabled = allowToggleSwitchIgnoreIssues
        End If

        If ToggleSwitchSubmitInEP.Enabled <> allowSubmitPayroll Then
            ToggleSwitchSubmitInEP.Enabled = allowSubmitPayroll
        End If

        If btnRunUtilityImpot.Enabled <> allowSubmitPayroll Then
            btnRunUtilityImpot.Enabled = allowSubmitPayroll
        End If
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        SetToCheckDate()
        LoadData()
    End Sub

    Private Sub btnTake_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTake.Click
        TakePayroll(CurrentRec.ID)
        Me.btnReleasePayroll.Enabled = True
        Me.btnTake.Enabled = False
    End Sub

    Private Sub btnReleasePayroll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReleasePayroll.Click
        Dim PayrollStatus = (From A In DB.PAYROLLs Where A.CONUM = CurrentRec.CoNum AndAlso A.PRNUM = CurrentRec.PrNum Select A.PAYROLL_STATUS).Single
        If PayrollStatus = "Entering Checks" OrElse PayrollStatus = "Submitted" Then
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
            Rec.LastOpenedBy = UserName
            Rec.LastOpenedDate = Now
            Rec.CompleteBy = Nothing
            Rec.CompleteDate = Nothing
            DB.SaveChanges()
        End If
        Me.btnReleasePayroll.Enabled = False
        LoadData()
    End Sub

    Private Sub GridView1_BeforeLeaveRow(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Base.RowAllowEventArgs) Handles GridView1.BeforeLeaveRow
        If CurrentRec.CompleteBy = UserName Then
            Dim PayrollStatus = (From A In DB.PAYROLLs Where A.CONUM = CurrentRec.CoNum AndAlso A.PRNUM = CurrentRec.PrNum Select A.PAYROLL_STATUS).Single
            If PayrollStatus = "Entering Checks" Then
                If MessageBox.Show("Payroll is still open. Do you want to release this payroll?", "Release Payroll?", MessageBoxButtons.YesNo, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.Yes Then
                    btnReleasePayroll.PerformClick()
                End If
            End If
        End If
    End Sub

    Private Sub riNoteIsChecked_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riNoteIsChecked.CheckedChanged
        Me.GridViewNotes.PostEditor()
        Me.GridViewNotes.UpdateCurrentRow()
        CheckNotes()
    End Sub

    Sub CheckNotes()
        Dim UncheckedCount = (From A As pr_batch_msg In Me.PrbatchmsgBindingSource.List
                              Where Not A.IsChecked AndAlso A.Priority <> "3-Low").Count
        Me.btnSubmitPayroll.Enabled = _EnableProcess AndAlso UncheckedCount = 0 AndAlso CurrentRec.Grp <> 7
        Me.cbPrinters.Properties.Buttons(1).Enabled = Me.PrbatchmsgBindingSource.Count > 0

        Me.btnHandleAllIssues.Enabled = CurrentRec.Grp <> 7 AndAlso (From A As pr_batch_msg In Me.PrbatchmsgBindingSource.List Where NoteTypeWithOption.ContainsKey(A.msg_type)).Any
    End Sub

    Private Async Sub btnSubmitPayroll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSubmitPayroll.Click
        Try
            SplashScreenManager1.TryShowWaitForm(True)
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
            If Rec.IsQueued Then
                DisplayMessageBox("This payroll was already sent to be processed in the Background")
                Exit Sub
            ElseIf Rec.CompleteBy IsNot Nothing AndAlso Rec.CompleteBy <> UserName Then
                DisplayMessageBox(String.Format("Payroll taken by '{0}' on '{1}'", Rec.CompleteBy, Rec.CompleteDate?.ToString("g")))
                Exit Sub
            End If

            Dim SubmittedInQueue As Boolean? = Query(Of Boolean?)(String.Format("
                SELECT	1
                FROM	custom.Queue q 
		                INNER JOIN custom.QueueDetail qd ON q.Id = qd.QueueId AND qd.Prnum = {1}
                WHERE	q.Description = 'Submit Payroll' 
		                AND q.Status IN ('Pending', 'Processing')
		                AND q.Conum = {0}
            ", Rec.CoNum, Rec.PrNum)).FirstOrDefault()

            If SubmittedInQueue Then
                DisplayMessageBox("The payroll was already submitted and is pending in Queue")
                Exit Sub
            End If

            Dim Password = (From A In DB.COMPANies Where A.CONUM = CurrentRec.CoNum Select A.PR_PASSWORD).SingleOrDefault
            If Not String.IsNullOrEmpty(Password) Then
                If Not ConfirmPassword(Password) Then Exit Sub
            End If

            'Solomon added on Dec 27, '22.  If issue found increase immediately ignore issues and allow to submit
            Dim IssueCount = (From A As pr_batch_msg In getNotes(Rec.CoNum, Rec.PrNum, CurrentRec.CHECK_DATE)
                              Where A.Priority <> "3-Low").Count

            If IssueCount > 0 Then
                Rec.IgnoreIssues = nz(Rec.IgnoreIssues, 0) + 1
                CurrentRec.IgnoreIssues = Rec.IgnoreIssues
                DB.SubmitChanges()
            End If

            If Not DoNotRunUtilityImportCheckEdit.Checked Then ' {"xpress", "platinumpay"}.Contains(CurrentRec.ENTRY_TYPE.ToLower) Then
                Try
                    ProgressPanel1.Visible = True
                    Application.DoEvents()
                    Dim obj As New PayrollUtilityImport(CurrentRec.CoNum)
                    obj.ProcessImport(CurrentRec.PrNum)
                Finally
                    ProgressPanel1.Visible = False
                End Try
            End If

            If bciProcessInBackground.Checked Then
                Await SetIsQueuedAsync(True)
                Dim builder = modGlobals.GetQueueBuilder().WithConum(Rec.CoNum).WithDescription("Submit Payroll") _
                    .EnqueueSubmitPayroll(Rec.PrNum, Rec.ID) _
                    .EnqueuePayrollStatusNotification(Rec.PrNum)
                Await builder.SaveAndPublishAsync()
                Exit Sub
            End If

            Dim payroll = (From A In DB.PAYROLLs Where A.CONUM = CurrentRec.CoNum AndAlso A.PRNUM = CurrentRec.PrNum).Single

            Logger.Debug("Processing Payroll for Co#: {CoNum} Pr#: {PrNum} {PrStatus}", Rec.CoNum, Rec.PrNum, payroll.PAYROLL_STATUS)

            Dim sp = New SubmitPayroll()
            Dim results = Await sp.SubmitAsync(Rec.ID, Rec.CoNum, Rec.PrNum, UserName)
            If Not results.Item1 AndAlso results.Item2.IsNotNullOrWhiteSpace() Then
                XtraMessageBox.Show(results.Item2)
            End If
            XtraMessageBox.Show("Finished.")
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error while submitting payroll", ex)
        Finally
            SplashScreenManager1.TryShowWaitForm(False)
        End Try

    End Sub

    Private Sub chkShowProcessing_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkShowProcessing.CheckedChanged
        If Me.chkShowProcessing.Checked Then
            Me.xtpNsfHold.PageVisible = True
            LoadData()
        Else
            Me.xtpNsfHold.PageVisible = False
        End If
    End Sub

    Private Sub GridControlPayrollsCurrentlyProcessing_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles GridControlPayrollsCurrentlyProcessing.MouseDoubleClick
        Try
            Dim hi = Me.GridViewPayrollsCurrentlyProcessing.CalcHitInfo(e.Location)
            If hi.RowHandle >= 0 Then
                Dim row As view_PayrollInProcess = Me.GridViewPayrollsCurrentlyProcessing.GetRow(hi.RowHandle)
                'refresh from database
                Dim db2 As New dbEPDataDataContext(GetConnectionString)
                row = (From A In db2.view_PayrollInProcesses Where A.ID = row.ID).SingleOrDefault
                If row Is Nothing Then
                    Me.LoadData()
                    Exit Sub
                End If
                Dim BatchList = (From A In db2.pr_batch_lists Where A.id = row.PowerGridID).SingleOrDefault
                Dim frmPowerGrid = New frmBrandsPowerGrid With {
                    .CoNum = row.CoNum,
                    .PrNum = row.PrNum,
                    .BatchList = BatchList,
                    .IsAuto = False,
                    .IsReadOnly = True}
                If row.CalendarID.HasValue Then frmPowerGrid.CalanderID = row.CalendarID
                frmPowerGrid.MdiParent = MainForm
                Dim API = EP_API()
                API.CompanyID = row.CoNum
                API.PayrollID = row.PrNum
                API.CalendarID = row.CalendarID
                frmPowerGrid.LoadData()
                frmPowerGrid.Show()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error processing payroll.", ex)
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            Dim viewPayInProc As view_PayrollInProcess = GridView1.GetRow(e.HitInfo.RowHandle)
            If viewPayInProc.ZendeskTicketId.HasValue Then
                e.Menu.Items.Add(New DXMenuItem("View Payroll Ticket", Sub()
                                                                           Dim filePath = $"{GetUdfValue("ZendeskUrl")}/agent/tickets/{viewPayInProc.ZendeskTicketId}"
                                                                           'System.Diagnostics.Process.Start(filePath)
                                                                           Dim psi As New System.Diagnostics.ProcessStartInfo()
                                                                           psi.FileName = filePath
                                                                           psi.UseShellExecute = True
                                                                           System.Diagnostics.Process.Start(psi)

                                                                       End Sub))
            End If
            If viewPayInProc.EmailRegisterZendeskTicketId.HasValue Then
                e.Menu.Items.Add(New DXMenuItem("View Register Approval Ticket", Sub()
                                                                                     Dim filePath = $"{GetUdfValue("ZendeskUrl")}/agent/tickets/{viewPayInProc.EmailRegisterZendeskTicketId}"
                                                                                     System.Diagnostics.Process.Start(filePath)
                                                                                     Dim psi As New System.Diagnostics.ProcessStartInfo()
                                                                                     psi.FileName = filePath
                                                                                     psi.UseShellExecute = True
                                                                                     System.Diagnostics.Process.Start(psi)

                                                                                 End Sub))
            End If

            Dim payroll = (From p In DB.PAYROLLs Where p.CONUM = viewPayInProc.CoNum AndAlso p.PRNUM = viewPayInProc.PrNum).SingleOrDefault
            If payroll IsNot Nothing AndAlso (payroll.PAYROLL_STATUS = "Submitted" OrElse payroll.PAYROLL_STATUS = "Ach Risk") Then
                e.Menu.Items.Add(New DXMenuItem($"Payroll status is {payroll.PAYROLL_STATUS}. Change Payroll status to 'Entering Checks' ?",
                                                        Sub()
                                                            If Not IsLastPayroll(CurrentRec.CoNum, CurrentRec.PrNum) Then
                                                                XtraMessageBox.Show("A new payroll was opened on top of this payroll. You may only submit")
                                                                Exit Sub
                                                            End If

                                                            If XtraMessageBox.Show("Are you sure you would like to change Payroll #{0} from: {1} to: {2}" _
                                                                .FormatWith(payroll.PRNUM, payroll.PAYROLL_STATUS, "Entering Checks"), "Update ?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes Then
                                                                payroll.PAYROLL_STATUS = "Entering Checks"
                                                                DB.SaveChanges()
                                                                LoadData()
                                                            End If
                                                        End Sub))
            End If
            e.Menu.Items.Add(New DXMenuItem("View Register", Sub() ViewAndProcessReport(GetCheckRegister(viewPayInProc)), My.Resources.open_16x16))
            e.Menu.Items.Add(New DXMenuItem("View Check Checkers", Sub() ViewAndProcessReport(GetCheckersCheckRegister(viewPayInProc)), My.Resources.show_16x16))
            e.Menu.Items.Add(New DXMenuItem($"Calculate Checks{If(viewPayInProc.ENTRY_TYPE <> "Xpress", " (Only available for Xpress payrolls)", "")}", Sub() CalculateAllChecks(viewPayInProc), My.Resources.calculatesheet_16x16) With {.Enabled = viewPayInProc.ENTRY_TYPE = "Xpress"})
            e.Menu.Items.Add(New DXMenuItem("NSF Hold", click:=AddressOf ChangeProcessStatus) With {.Tag = e.HitInfo.RowInfo, .BeginGroup = True})
            If Permissions.Manager AndAlso viewPayInProc.IsQueued Then
                e.Menu.Items.Add(New DXMenuItem("Remove From Queue", Async Sub()
                                                                         Await SetIsQueuedAsync(False, viewPayInProc)
                                                                     End Sub))
            End If
            e.Menu.Items.Add(New DXMenuItem("Open Company", Sub() MainForm.OpenCompForm(viewPayInProc.CoNum), My.Resources.project_16x16))
            e.Menu.Items.Add(New DXMenuItem("View Register (Legacy)", Sub() ViewAndProcessReport(GetPayrollRegister(viewPayInProc)), My.Resources.open_16x16) With {.BeginGroup = True})
            If modGlobals.UserInRole("AllowManageBackgroundAutoFixPayroll") AndAlso viewPayInProc.AutoFixPayrollCompletedOn.HasValue Then
                e.Menu.Items.Add(New DXMenuItem("Clear AutoFixPayrollCompletedOn", Sub()
                                                                                       Dim a = DB.pr_batch_in_processes.Single(Function(p) p.ID = viewPayInProc.ID)
                                                                                       a.AutoFixPayrollCompletedOn = Nothing
                                                                                       viewPayInProc.AutoFixPayrollCompletedOn = Nothing
                                                                                       DB.SaveChanges()
                                                                                       GridView1.RefreshRow(e.HitInfo.RowHandle)
                                                                                   End Sub, My.Resources.clear_16x16) With {.BeginGroup = True})
            End If
            If modGlobals.UserInRole("AllowReleaseOtherUsersPayroll") Then
                Dim menuText = $"Release Payroll From {viewPayInProc.CompleteBy}"
                e.Menu.Items.Add(New DXMenuItem(menuText, Sub()
                                                              Dim a = DB.pr_batch_in_processes.Single(Function(p) p.ID = viewPayInProc.ID)
                                                              a.LastOpenedBy = a.CompleteBy
                                                              a.CompleteBy = Nothing
                                                              a.CompleteDate = Nothing
                                                              DB.SaveChanges()
                                                              viewPayInProc.CompleteBy = Nothing
                                                              GridView1.RefreshRow(e.HitInfo.RowHandle)
                                                          End Sub))
            End If
        End If
    End Sub

    Private Sub ChangeProcessStatus(sender As DXMenuItem, e As EventArgs)
        Dim rowInfo As DevExpress.XtraGrid.Views.Grid.ViewInfo.GridRowInfo = sender.Tag
        Dim row As view_PayrollInProcess = rowInfo.View.GetRow(rowInfo.RowHandle)
        Dim rd = (From A In DB.pr_batch_in_processes Where A.ID = row.ID).First
        If sender.Caption = "NSF Hold" Then
            rd.ProcessStatus = sender.Caption
        Else
            rd.ProcessStatus = "Ready"
        End If
        DB.SaveChanges
        LoadData()
    End Sub

    Private Sub CalculateAllChecks(ByVal view_PayrollInProcess As view_PayrollInProcess)
        Try
            Dim frm = New frmCalculateChecks(view_PayrollInProcess.CoNum, view_PayrollInProcess.PrNum)
            If frm.ShowDialog = DialogResult.OK Then
                LoadNotes()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error calculating checks", ex)
        End Try
    End Sub

    Function GetPayrollRegister(viewPayInProc As view_PayrollInProcess) As ReportProcessor
        Dim reportProcessor = New ReportProcessor(viewPayInProc.CoNum, "Payroll Register", FileType.Pdf) With {.DefaultParamValues = New List(Of KeyValuePair), .showParametersForm = False, .showInRecentReports = True}
        reportProcessor.DefaultParamValues.Add(New KeyValuePair("cf", viewPayInProc.CoNum))
        reportProcessor.DefaultParamValues.Add(New KeyValuePair("pr", viewPayInProc.PrNum))
        reportProcessor.DefaultParamValues.Add(New KeyValuePair("y", viewPayInProc.CHECK_DATE.GetValueOrDefault().Year))
        Return reportProcessor
    End Function

    Function GetCheckRegister(viewPayInProc As view_PayrollInProcess) As ReportProcessor
        Dim reportProcessor = New ReportProcessor(viewPayInProc.CoNum, "Check Register", FileType.Pdf) With {
            .DefaultParamValues = New List(Of KeyValuePair),
            .showParametersForm = False,
            .showInRecentReports = True,
            .LastPrNum = viewPayInProc.PrNum,
            .LastCheckDate = viewPayInProc.CHECK_DATE}
        'reportProcessor.DefaultParamValues.Add(New KeyValuePair("cf", viewPayInProc.CoNum))
        'reportProcessor.DefaultParamValues.Add(New KeyValuePair("pr", viewPayInProc.PrNum))
        'reportProcessor.DefaultParamValues.Add(New KeyValuePair("y", viewPayInProc.CHECK_DATE.GetValueOrDefault().Year))
        Return reportProcessor
    End Function

    Function GetCheckersCheckRegister(viewPayInProc As view_PayrollInProcess) As ReportProcessor
        Dim reportProcessor = New ReportProcessor(viewPayInProc.CoNum, "Checkers Check Register", FileType.Pdf) With {.DefaultParamValues = New List(Of KeyValuePair), .showParametersForm = False, .showInRecentReports = True}
        reportProcessor.DefaultParamValues.Add(New KeyValuePair("CoNo", viewPayInProc.CoNum))
        reportProcessor.DefaultParamValues.Add(New KeyValuePair("PrNo", viewPayInProc.PrNum))
        Return reportProcessor
    End Function

    Private Sub ViewAndProcessReport(reportProcessor As ReportProcessor)
        Try
            SplashScreenManager1.TryShowWaitForm(True)
            Dim reportResults = reportProcessor.ProcessReport()
            If reportResults.Cancalled OrElse Not reportResults.AllFileExist Then
                DisplayMessageBox("No records returned for the parameters values entered")
                Exit Sub
            End If
            For Each file In reportResults.Paths
                'System.Diagnostics.Process.Start(file)
                Dim psi As New System.Diagnostics.ProcessStartInfo()
                psi.FileName = file
                psi.UseShellExecute = True
                System.Diagnostics.Process.Start(psi)
            Next
        Catch ex As Exception
            SplashScreenManager1.CloseWaitForm()
            DisplayErrorMessage("Error viewing payroll register.", ex)
        Finally
            SplashScreenManager1.CloseWaitForm()
        End Try
    End Sub

    Sub ReOpenPayroll()
        Dim payroll = (From p In DB.PAYROLLs Where p.CONUM = CurrentRec.CoNum AndAlso p.PRNUM = CurrentRec.PrNum).SingleOrDefault
        If payroll IsNot Nothing AndAlso payroll.PAYROLL_STATUS = "Submitted" Then
            payroll.PAYROLL_STATUS = "Entering Checks"
            DB.SaveChanges()
        End If
    End Sub

    Private Sub GridView1_RowStyle(sender As Object, e As RowStyleEventArgs) Handles GridView1.RowStyle
        Dim viewInfo = CType(GridView1.GetViewInfo(), ViewInfo.GridViewInfo)
        If e.RowHandle = GridView1.FocusedRowHandle Then
            e.Appearance.BackColor = viewInfo.PaintAppearance.FocusedRow.BackColor
            e.Appearance.ForeColor = viewInfo.PaintAppearance.FocusedRow.ForeColor
        End If
        Dim row = CType(GridView1.GetRow(e.RowHandle), view_PayrollInProcess)
        If row IsNot Nothing Then
            If row.FaxId IsNot Nothing OrElse row.EmailNum IsNot Nothing Then
                e.Appearance.ForeColor = Color.Red
                e.Appearance.BackColor = Color.AliceBlue
                e.Appearance.BackColor2 = Color.Azure
            End If
        End If
    End Sub

    Private Sub GridViewNotes_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewNotes.PopupMenuShowing
        If CurrentRec Is Nothing Then Exit Sub
        Dim row = CType(GridViewNotes.GetRow(e.HitInfo.RowHandle), pr_batch_msg)
        If row Is Nothing Then Exit Sub

        If NoteTypeWithOption.ContainsKey(row.msg_type) Then
            e.Menu.Items.Add(New DXMenuItem(NoteTypeWithOption(row.msg_type), click:=Sub()
                                                                                         Cursor = Cursors.WaitCursor
                                                                                         Dim t = RunFixAsync(row.msg_type, True)
                                                                                         Cursor = Cursors.Default
                                                                                     End Sub))
        End If

        If row.msg_type = "EMLREG" Then
            e.Menu.Items.Add(New DXMenuItem("Send Register. (Legacy - will be processed locally)", Sub()
                                                                                                       Try
                                                                                                           GridViewNotes.ShowLoadingPanel()
                                                                                                           Dim result = frmBrandsPowerGrid.PayrollApproval(CurrentRec.CoNum, CurrentRec.PrNum, True, CurrentRec.CHECK_DATE)
                                                                                                           XtraMessageBox.Show(result)
                                                                                                       Catch ex As Exception
                                                                                                           DisplayErrorMessage("Error sending payroll register", ex)
                                                                                                       Finally
                                                                                                           GridViewNotes.HideLoadingPanel()
                                                                                                       End Try
                                                                                                   End Sub))
            e.Menu.Items.Add(New DXMenuItem("Mark Approved", Sub() MarkApproved(), My.Resources.checkbox_16x16))
        End If
        If {"Payroll", "Company", "EMPSPCMSG"}.Contains(row.msg_type) AndAlso row.id <> Guid.Empty Then
            e.Menu.Items.Add(New DXMenuItem("Edit Note", click:=Sub()
                                                                    Dim status = DB.PAYROLLs.Where(Function(f) f.CONUM = CurrentRec.CoNum AndAlso f.PRNUM = CurrentRec.PrNum).First()?.PAYROLL_STATUS
                                                                    If status <> "Entering Checks" AndAlso status <> "Submitted" Then
                                                                        XtraMessageBox.Show("Payroll not in Entering Checks or Submitted")
                                                                        Return
                                                                    End If

                                                                    Dim frm = New frmNotes(CurrentRec.CoNum)
                                                                    AddHandler frm.Shown, Sub()
                                                                                              frm.UcNotes1.SetFocuseNoteID(row.id)
                                                                                          End Sub
                                                                    Dim results = frm.ShowDialog()
                                                                    If frm.HasChanges Then LoadNotes()
                                                                    frm.Dispose()
                                                                End Sub))
        End If

        Dim list = (From A As pr_batch_msg In Me.PrbatchmsgBindingSource.List Where A.msg_type = row.msg_type).ToList
        If list.Count > 1 Then

            Dim caption = $"Check ({list.Count}) {row.msg_type}"
            Dim menu = New DXMenuItem(caption, Sub()
                                                   If XtraMessageBox.Show($"Warning make sure you have reviewed each message.{vbCrLf}Continue?", "Warning", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                                                       For Each item In list
                                                           item.IsChecked = True
                                                       Next
                                                       GridViewNotes.RefreshData()
                                                       CheckNotes()
                                                   End If
                                               End Sub)
            menu.ImageOptions.SvgImage = My.Resources.DoneAll
            e.Menu.Items.Add(menu)
        End If

        If row.msg_type = "CLIENTS_MSG" Then
            e.Menu.Items.Add(New DXMenuItem("Mark Handled", Sub() MarkHandledClientMessage(), My.Resources.checkbox_16x16))
        End If
    End Sub

    Private Sub MarkApproved()
        Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single

        Dim status = DB.PAYROLLs.Where(Function(f) f.CONUM = Rec.CoNum AndAlso f.PRNUM = Rec.PrNum).First()?.PAYROLL_STATUS
        If status <> "Entering Checks" AndAlso status <> "Submitted" Then
            XtraMessageBox.Show("Payroll not in Entering Checks or Submitted")
            Return
        End If

        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
        Rec.ClientApprovedRegisterOn = DateTime.Now
        Rec.ClientApprovedRegisterBy = UserName
        Rec.ClientApprovedRegisterVia = "Agent"
        DB.SubmitChanges()
        AddBatchNote($"Payroll Approved Via Agent By {UserName} On {DateTime.Now}")
        CurrentRec.ClientApprovedRegisterOn = DateTime.Now
        GridView1.RefreshData()
        LoadNotes()
    End Sub

    Private Sub MarkHandledClientMessage()
        Dim status = DB.PAYROLLs.Where(Function(f) f.CONUM = CurrentRec.CoNum AndAlso f.PRNUM = CurrentRec.PrNum).First()?.PAYROLL_STATUS
        If status <> "Entering Checks" AndAlso status <> "Submitted" Then
            XtraMessageBox.Show("Payroll not in Entering Checks or Submitted")
            Return
        End If

        Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
        Rec.HandledClientMessageOn = DateTime.Now
        Rec.HandledClientMessageBy = UserName
        AddBatchNote($"Handled Client Messages On {DateTime.Now}")
        CurrentRec.HandledClientMessageOn = DateTime.Now
        CurrentRec.HandledClientMessageBy = UserName
        GridView1.RefreshData()
        LoadNotes()
    End Sub

    Private Sub MarkExcessiveHoursReviewed()
        Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
        Rec.ExcessiveHoursReviewed = $"By: {UserName} On: {DateTime.Now}"
        AddBatchNote($"Excessive Hours Reviewed On {DateTime.Now}")
        GridView1.RefreshData()
        LoadNotes()
    End Sub

    Private Sub AddBatchNote(note As String, Optional priority As String = "3-Low")
        Dim msg As New pr_batch_note With {.rowguid = Guid.NewGuid,
                                            .Conum = CurrentRec.CoNum,
                                            .PrNum = CurrentRec.PrNum,
                                            .DateEntered = DateTime.Now,
                                            .EnteredBy = UserName,
                                            .Note = note,
                                            .Priority = priority,
                                            .ListID = Guid.NewGuid,
                                            .EmployeeID = -997}
        DB.pr_batch_notes.InsertOnSubmit(msg)
        DB.SaveChanges
    End Sub


    Private Async Function RunFixAsync(MsgType As String, Optional ShowOptions As Boolean = False) As Threading.Tasks.Task
        _logger.Debug("Entering RunFixAsync. MsgType: {MsgType} ShowOptions: {ShowOptions}", MsgType, ShowOptions)

        Dim status = DB.PAYROLLs.Where(Function(f) f.CONUM = CurrentRec.CoNum AndAlso f.PRNUM = CurrentRec.PrNum).First()?.PAYROLL_STATUS
        If status <> "Entering Checks" AndAlso status <> "Submitted" Then
            XtraMessageBox.Show("Payroll not in Entering Checks or Submitted")
            Return
        End If

        Select Case MsgType
            Case "MANCHKTOADD"
                ProcessManuals(True)
            Case "MANCHKTOADDYR"
                ProcessManuals(False)
            Case "VOIDSTOADD"
                ProcessVoids(True)
            Case "VOIDSTOADDYR"
                ProcessVoids(False)
            Case "AUTO2NDCHK"
                ProcessSecondCheck()
            Case "GRSTONET"
                CalculateCheck()
            Case "RES_ST_OVRIDE"
                ProcessOverride("RES_ST_OVRIDE")
            Case "NETOVR"
                ProcessOverride("NETOVR")
            Case "EMLREG"
                Try
                    If Not VerifyIfDoubleSend_ContinueSending(CurrentRec.CoNum, CurrentRec.PrNum) Then Exit Function
                    GridViewNotes.ShowLoadingPanel()
                    Dim queueBuilder = GetQueueBuilder()
                    queueBuilder.WithConum(CurrentRec.CoNum).WithDescription("Payroll Approval")
                    QueuePayrollProcessor.Initialize(GetConnectionString, Logger, Nothing, UserName)
                    QueuePayrollProcessor.modPayrollUtilities.QueuePayrollReports(queueBuilder, CurrentRec.CoNum, CurrentRec.PrNum, CurrentRec.CHECK_DATE, Nothing)
                    Await queueBuilder.SaveAndPublishAsync()
                    XtraMessageBox.Show("The Payroll approvals has bean Queued")
                Catch ex As Exception
                    DisplayErrorMessage("Error sending payroll register", ex)
                Finally
                    GridViewNotes.HideLoadingPanel()
                End Try
            Case "FICA_PENY_OVRIDE", "FED_PENY_OVRIDE"
                FicaPennyOvveride()
            Case "PASTCHKDATE", "CLIENTS_MSG"
                Dim PayrollRec = (From A In DB.PAYROLLs Where A.CONUM = CurrentRec.CoNum AndAlso A.PRNUM = CurrentRec.PrNum).Single
                Dim PayrollRecPending = (From A In DB.PAYROLLs Where A.CONUM = CurrentRec.CoNum AndAlso A.PRNUM <> CurrentRec.PrNum AndAlso A.PAYROLL_STATUS = "Entering Checks").FirstOrDefault()
                If PayrollRecPending IsNot Nothing Then
                    If ShowOptions Then
                        DisplayErrorMessage("Cannot change date.  Delete later payroll and retry.", New Exception("Another payroll in Entering Checks"))
                        Return
                    Else
                        Throw New Exception("There is another payroll in Entering Checks.  Delete later payroll and retry.")
                        Return
                    End If
                End If
                Dim NewDate = Query(Of Date)($"SELECT custom.fn_GetNextSubmissionDate({CurrentRec.CoNum})").FirstOrDefault()
                If ShowOptions Then
                    Dim editor = New DateEdit
                    editor.Properties.Mask.EditMask = "d"
                    editor.Properties.MaxValue = NewDate.AddDays(6)
                    editor.Properties.MinValue = NewDate
                    editor.Properties.ShowClear = False

                    Dim args = New XtraInputBoxArgs() With {
                    .Caption = "Fix Past Check Date Issue",
                    .Prompt = "Set new Check Date",
                    .DefaultResponse = NewDate,
                    .Editor = editor}

                    Dim results = XtraInputBox.Show(args)
                    If results Is Nothing Then
                        Return
                    Else
                        NewDate = results
                    End If
                End If

                PayrollRec.CHECK_DATE = NewDate
                If PayrollRec.PAYROLL_STATUS.ToLower() <> "entering checks" Then
                    PayrollRec.PAYROLL_STATUS = "Entering Checks"
                End If
                DB.SaveChanges()
            Case "NONET"
                Dim cnt = DeleteZeroChecks()
                If ShowOptions Then
                    DisplayMessageBox($"{cnt} check{If(cnt = 1, "", "s")} deleted", "Completed")
                End If
            Case "Review_Excessive_Hours"
                MarkExcessiveHoursReviewed()
                'Case "DUP_CHK_NUMS"
                '    FixDuplicateCheckNumbers(ShowOptions)
        End Select
        LoadNotes()
    End Function

    Private Function VerifyIfDoubleSend_ContinueSending(coNum As Decimal, prNum As Decimal) As Object
        _logger.Debug("Entering VerifyIfDoubleSend_ContinueSending. Co#: {Conum} Pr#: {Prnum}", coNum, prNum)
        Using db As New Brands.DAL.EPDATAContext(GetConnectionString())
            Dim lastQueued = (From q In db.Queues
                              Join qd In db.QueueDetails On q.Id Equals qd.QueueId
                              Join qr In db.QueueReports On qd.Id Equals qr.QueueDetailId
                              Where q.Description = "Payroll Approval" AndAlso q.Conum = coNum AndAlso qr.LastPrNum = prNum
                              Order By q.RequestedOn Descending
                              Select q).ToList()
            If Not lastQueued.Any Then
                _logger.Debug("Nothing found in queue for Payroll Approval.  Co#: {Conum} Pr#: {Prnum}", coNum, prNum)
                Return True
            End If

            Dim last = lastQueued.First()
            Dim message = $"Are you sure you would like to Resend Payroll Aprroval Email?{vbCrLf}{vbCrLf}Last Request Details:{vbCrLf}By: {last.RequestedBy}{vbCrLf}on: {last.RequestedOn}{vbCrLf}Status: {last.Status}"
            If XtraMessageBox.Show(message, "Resend  Email?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                _logger.Debug("user selected not to resend Payroll Approval. Co#: {Conum} Pr#: {Prnum} {Message}", coNum, prNum, message)
                Return False
            End If
        End Using
        _logger.Warning("user selected to resend Payroll Approval. Co#: {Conum} Pr#: {Prnum}", coNum, prNum)
        Return True
    End Function

    Private Sub ProcessManuals(curYearOnly As Boolean)
        Logger.Debug("User clicked on menu 'Add Manual Checks'")
        If Not IsLastPayroll(CurrentRec.CoNum, CurrentRec.PrNum) Then
            XtraMessageBox.Show("A new payroll was opened on top of this payroll. You may only submit")
            Exit Sub
        End If
        If Not curYearOnly AndAlso XtraMessageBox.Show("Warning, You are about to add manual check(s) NOT from current year." & vbCrLf & "Continue?", "Previos year manual check", MessageBoxButtons.YesNo) = DialogResult.No Then
            Exit Sub
        End If

        Dim ManCheckCount As Integer = 0
        DB.prc_IncludeManualChecksToPayroll(CurrentRec.CoNum, CurrentRec.PrNum, curYearOnly, ManCheckCount)
        Logger.Debug("Calling sp prc_IncludeManualChecksToPayroll. Added Checks count is: {ManCheckCount}", ManCheckCount)
        If ManCheckCount > 0 Then
            Dim msg As New pr_batch_note With {.rowguid = Guid.NewGuid,
                                                                                .Conum = CurrentRec.CoNum,
                                                                                .PrNum = CurrentRec.PrNum,
                                                                                .DateEntered = DateTime.Now,
                                                                                .EnteredBy = UserName,
                                                                                .Note = ManCheckCount & " manual check(s) added to payroll",
                                                                                .Priority = "3-Low",
                                                                               .ListID = Guid.NewGuid,
                                                                               .EmployeeID = -997}
            DB.pr_batch_notes.InsertOnSubmit(msg)
            Logger.Debug("Saving new pr_batch_note to DB {@msg} pending added records is: {PendingInsertsCount}", msg, DB.GetChangeSet.Inserts.Count)
            DB.SaveChanges()
        End If
    End Sub

    Private Sub FicaPennyOvveride()
        Try
            Dim dt = Query("SELECT CONUM, PAYROLL_NUM, EMPNUM, CHK_COUNTER FROM dbo.CHK_MAST
                        WHERE CONUM = @CoNum AND PAYROLL_NUM = @PrNum
                        AND (
                            (ABS(OASDI) < .05 AND OASDI <> 0 AND ABS(MEDICARE) < .05 ) 
                            OR 
                            (ABS(MEDICARE) < .05 AND MEDICARE <> 0 AND ABS(OASDI) < .05)
                            OR
                            (FED_WH <> 0 AND ABS(FED_WH) < .05)
                            ) 
                        AND CHK_TYPE IN('NORMAL', 'DD')", New SqlClient.SqlParameter("@CoNum", CurrentRec.CoNum), New SqlClient.SqlParameter("@PrNum", CurrentRec.PrNum))

            For Each row As System.Data.DataRow In dt.Rows
                Dim empNum As Decimal = row("EMPNUM")
                Dim chkCounter As Decimal = row("CHK_COUNTER")
                Dim chkMst = DB.CHK_MASTs.Single(Function(c) c.CONUM = CurrentRec.CoNum AndAlso c.PAYROLL_NUM = CurrentRec.PrNum AndAlso c.EMPNUM = empNum AndAlso c.CHK_COUNTER = chkCounter)
                If Math.Abs(chkMst.OASDI.GetValueOrDefault) < 0.05 Then chkMst.OR_OASDI = 0
                If Math.Abs(chkMst.MEDICARE.GetValueOrDefault) < 0.05 Then chkMst.OR_MEDICARE = 0
                If Math.Abs(chkMst.FED_WH.GetValueOrDefault) < 0.05 Then chkMst.OR_FED_WH = 0
                DB.SaveChanges
                Dim API = EP_API()
                API.CompanyID = CurrentRec.CoNum
                API.PayrollID = CurrentRec.PrNum

                'If Not UsePPxLibrary Then
                '    Dim ppx = CType(API, PPxPayroll)
                '    If Not ppx.PayrollAPI.CalculateSingleCheck(API.ProviderID, API.CompanyID, API.PayrollID, empNum, chkCounter, whoIsCalling, -1) Then
                '        DisplayMessageBox($"Error calculating single check Co#: {CurrentRec.CoNum} Pr#: {CurrentRec.PrNum} Emp#: {empNum} ChkCnt: {chkCounter}")
                '    End If
                'Else
                Try
                    If Not API.CalculateSingleCheck(CurrentRec.CoNum, CurrentRec.PrNum, empNum, chkCounter) Then
                        DisplayMessageBox($"Error calculating single check Co#: {CurrentRec.CoNum} Pr#: {CurrentRec.PrNum} Emp#: {empNum} ChkCnt: {chkCounter}")
                    End If
                Catch ex As Exception
                    DisplayMessageBox($"Error calculating single check Co#: {CurrentRec.CoNum} Pr#: {CurrentRec.PrNum} Emp#: {empNum} ChkCnt: {chkCounter} Error: {ex.Message}")
                End Try
                'End If
            Next
        Catch ex As Exception
            DisplayErrorMessage($"Error in FicaPennyOvveride Co#: {CurrentRec.CoNum} Pr#: {CurrentRec.PrNum}{vbCrLf}{ex.Message}", ex)
        End Try
    End Sub

    Private Sub ProcessVoids(curYearOnly As Boolean)
        Try
            Logger.Debug("User clicked on menu 'Add Voids'")
            If Not IsLastPayroll(CurrentRec.CoNum, CurrentRec.PrNum) Then
                XtraMessageBox.Show("A new payroll was opened on top of this payroll. You may only submit")
                Exit Sub
            End If
            Dim ToVoidCount = (From A In DB.Voids Where A.CoNum = CurrentRec.CoNum AndAlso A.VoidPrNum Is Nothing).Count
            If ToVoidCount > 0 Then
                'check for duplicate void checks 
                Dim ToVoid = (From A In DB.Voids Where A.CoNum = CurrentRec.CoNum AndAlso A.VoidPrNum Is Nothing AndAlso
                             ((curYearOnly AndAlso A.CheckDate.HasValue AndAlso A.CheckDate.Value.Year = CurrentRec.CHECK_DATE.Value.Year) OrElse
                             (Not curYearOnly AndAlso A.CheckDate.HasValue AndAlso A.CheckDate.Value.Year <> CurrentRec.CHECK_DATE.Value.Year))).ToList()
                For Each void_chk In ToVoid
                    'check if void check date not match to payroll date
                    If void_chk.CheckDate.Value.Year <> CurrentRec.CHECK_DATE.Value.Year AndAlso
                        XtraMessageBox.Show("Warning:" &
                                      vbCrLf & "You are about to void a check from year {0} and it does not match to the year {1} of the current payroll.".FormatWith(void_chk.CheckDate.Value, CurrentRec.CHECK_DATE) &
                                      vbCrLf & "its recommended to cancel the void and do a negative manual check matching the gross and net of the void." &
                                      vbCrLf & vbCrLf & "For any questions consult the tax department." &
                                      vbCrLf & vbCrLf & "Continue?", "Previous year Payroll", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                        Exit Sub
                    End If

                    Dim chk_mast = (From c In DB.CHK_MASTs Where c.CONUM = void_chk.CoNum _
                                 AndAlso c.EMPNUM = void_chk.EmpNum _
                                 AndAlso c.CHK_NUM = void_chk.ChkNum _
                                 AndAlso (void_chk.Net.HasValue AndAlso (c.NET.Value = (-void_chk.Net.Value))) _
                                 AndAlso (void_chk.Gross.HasValue AndAlso (c.GROSS.Value = (-void_chk.Gross.Value))) _
                                 AndAlso c.CHK_TYPE.StartsWith("REV")).FirstOrDefault
                    If chk_mast IsNot Nothing Then
                        If XtraMessageBox.Show("Chk#: {0} was already voided in Pr#: {1}.{2}Are you sure you wish to void again?".FormatWith(void_chk.ChkNum, chk_mast.PAYROLL_NUM, vbCrLf), "Duplicate Void", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                            void_chk.VoidPrNum = CurrentRec.PrNum
                            void_chk.Note = "DuplicateVoidRemoved Orig Pr#: {0}".FormatWith(chk_mast.PAYROLL_NUM)
                        End If
                    End If
                Next
                DB.SaveChanges()


                Dim ProcessVoids = DB.prc_AddVoidsToPayroll(CurrentRec.CoNum, CurrentRec.PrNum, curYearOnly)
                DB = New dbEPDataDataContext(GetConnectionString)
                Dim CurrentVoidCount = (From A In DB.Voids Where A.CoNum = CurrentRec.CoNum AndAlso A.VoidPrNum Is Nothing AndAlso
                             ((curYearOnly AndAlso A.CheckDate.HasValue AndAlso A.CheckDate.Value.Year = CurrentRec.CHECK_DATE.Value.Year) OrElse
                             (Not curYearOnly AndAlso A.CheckDate.HasValue AndAlso A.CheckDate.Value.Year <> CurrentRec.CHECK_DATE.Value.Year))).Count
                If CurrentVoidCount > 0 Then
                    DisplayMessageBox("Error adding void checks. Please Try again.")
                Else
                    Dim msg As New pr_batch_note With {.rowguid = Guid.NewGuid,
                                                                                      .Conum = CurrentRec.CoNum,
                                                                                      .PrNum = CurrentRec.PrNum,
                                                                                      .DateEntered = DateTime.Now,
                                                                                      .EnteredBy = UserName,
                                                                                      .Note = ToVoidCount & " void check(s) added to payroll",
                                                                                      .Priority = "3-Low",
                                                                                     .ListID = Guid.NewGuid,
                                                                                     .EmployeeID = -997}
                    DB.pr_batch_notes.InsertOnSubmit(msg)
                    DB.SaveChanges()
                End If
            Else
                DisplayMessageBox("No Voids to be added.")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error while processing voids", ex)
        End Try
    End Sub

    Private Async Sub ProcessSecondCheck()
        Try
            Logger.Debug("User clicked on menu 'Add Second checks'")
            If Not IsLastPayroll(CurrentRec.CoNum, CurrentRec.PrNum) Then
                DisplayMessageBox("A new payroll was opened on top of this payroll. You may only submit")
                Exit Sub
            End If

            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
            If Rec.PowerGridID.HasValue Then
                DisplayMessageBox("This functions can only be used when payroll not done in silverpay")
                Exit Sub
            End If

            If Rec.SecondCheckProcessed.HasValue AndAlso Rec.SecondCheckProcessed.Value Then
                If XtraMessageBox.Show("Second check was already added to this payroll, Would you like to run it again ?", "Process Again", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) <> System.Windows.Forms.DialogResult.OK Then
                    Exit Sub
                End If
            End If

            Dim SecondChecks = New PayrollSecondChecks(CurrentRec.CoNum, CurrentRec.PrNum) 'Company and Payroll num
            If SecondChecks.SecondChecks.Count > 0 Then 'if second checks setup exists for employees paid in current payroll
                ReOpenPayroll()
                Dim ProcessResults = Await SecondChecks.ProcessSecondChecks(True)
                If ProcessResults = PayrollSecondChecks.ProcessResults.Processed OrElse ProcessResults = PayrollSecondChecks.ProcessResults.Submitted Then
                    Dim msg As New pr_batch_note With {.rowguid = Guid.NewGuid,
                                                                              .Conum = CurrentRec.CoNum,
                                                                              .PrNum = CurrentRec.PrNum,
                                                                              .DateEntered = DateTime.Now,
                                                                              .EnteredBy = UserName,
                                                                              .Note = "Auto second check(s) was added successfully",
                                                                              .Priority = "3-Low",
                                                                             .ListID = Guid.NewGuid,
                                                                             .EmployeeID = -997}
                    Rec.SecondCheckProcessed = True
                    DB.pr_batch_notes.InsertOnSubmit(msg)
                    XtraMessageBox.Show("Second check was added successfully.")
                Else
                    XtraMessageBox.Show("unsuccessful. Please try again, if the issue persist contact administrator.")
                End If
            Else
                DisplayMessageBox("No auto second check(s) has to be added to this payroll. (possible because it's not set for current schedule)")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in process second check", ex)
        End Try
    End Sub

    Private Sub CalculateCheck()
        Try
            Dim API = EP_API()
            API.CompanyID = CurrentRec.CoNum
            API.PayrollID = CurrentRec.PrNum
            'API.CheckPayrollStatus()
            Dim Msgs = (From A As pr_batch_msg In PrbatchmsgBindingSource.List Where A.msg_type = "GRSTONET").ToList
            For Each Line In Msgs
                'Dim Line As pr_batch_msg = Me.GridViewNotes.GetFocusedRow
                'If Not UsePPxLibrary Then
                '    Dim ppx = CType(API, PPxPayroll)
                '    Dim results = ppx.PayrollAPI.CalculateSingleCheck(API.ProviderID, API.CompanyID, API.PayrollID, Line.empnum, Line.chk_counter, whoIsCalling, -1)
                '    Logger.Debug("CalculateSingleCheck Result: {results} Co#: {CoNum} Pr#: {PrNum}", results, CurrentRec.CoNum, CurrentRec.PrNum)
                'Else
                'Dim results = API.CalculateSingleCheck(CurrentRec.CoNum, CurrentRec.PrNum, Line.empnum, Line.chk_counter)
                'Logger.Debug("CalculateSingleCheck Result: {results} Co#: {CoNum} Pr#: {PrNum}", results, CurrentRec.CoNum, CurrentRec.PrNum)
                Try
                    Dim results = API.CalculateSingleCheck(CurrentRec.CoNum, CurrentRec.PrNum, Line.empnum, Line.chk_counter)
                    Logger.Debug("CalculateSingleCheck Result: {results} Co#: {CoNum} Pr#: {PrNum}", results, CurrentRec.CoNum, CurrentRec.PrNum)
                Catch ex As Exception
                    DisplayMessageBox($"Error calculating single check Co#: {CurrentRec.CoNum} Pr#: {CurrentRec.PrNum} Emp#: {Line.empnum} ChkCnt: {Line.chk_counter} Error: {ex.Message}")
                End Try
                'End If
            Next
            'DisplayErrorMessage("Calculate failed")
            XtraMessageBox.Show("Calculate Done.")
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        End Try
    End Sub


    Private Function IsLastPayroll(coNum As Decimal, prNum As Decimal) As Boolean
        Dim maxPayrollNum = DB.PAYROLLs.Where(Function(p) p.CONUM = coNum).Select(Function(p) p.PRNUM).Max()
        Logger.Debug("Current Payroll is {PrNum} Last Payroll for Co#: {CoNum} Is Pr#: {PrNum}", prNum, coNum, maxPayrollNum)
        Return maxPayrollNum = prNum
    End Function

    Private Sub GridView1_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        If e.Column Is colDelDesc Then
            Dim grid As DevExpress.XtraGrid.Views.Grid.GridView = sender
            Dim row = grid.GetRowCellDisplayText(e.RowHandle, colDelDesc)
            If (row = "L" OrElse row = "S") AndAlso (DateTime.Now.TimeOfDay.Hours = 12 AndAlso DateTime.Now.TimeOfDay.Minutes < 30) Then
                e.Appearance.BackColor = Color.LightCoral
            ElseIf row <> "No Delivery" Then
                e.Appearance.BackColor = Color.FromArgb(&HFF, &HFF, &H99)
                e.Appearance.ForeColor = Color.Black
            End If
        End If
    End Sub

    Private Sub btnRunUtilityImpot_Click(sender As Object, e As EventArgs) Handles btnRunUtilityImpot.Click
        Try
            ProgressPanel1.Show()
            ProgressPanel1.Location = New Point((Me.Width - ProgressPanel1.Width) / 2, (Me.Height - ProgressPanel1.Height) / 2)
            ProgressPanel1.BringToFront()
            Application.DoEvents()
            Dim obj As New PayrollUtilityImport(CurrentRec.CoNum, DB)
            obj.ProcessImport(CurrentRec.PrNum, True)
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        Finally
            ProgressPanel1.Visible = False
        End Try

    End Sub

    Private Sub ProgressPanel1_VisibleChanged(sender As Object, e As EventArgs) Handles ProgressPanel1.VisibleChanged
        ProgressPanel1.BringToFront()
        ProgressPanel1.Location = New Point((Me.Width - ProgressPanel1.Width) / 2, (Me.Height - ProgressPanel1.Height) / 2)
        Application.DoEvents()
    End Sub

    Private Sub btnRefreshNotes_Click(sender As Object, e As EventArgs) Handles btnRefreshNotes.Click
        LoadNotes()
    End Sub

    Private Sub ToggleSwitchIgnoreIssues_Toggled(sender As Object, e As EventArgs) Handles ToggleSwitchIgnoreIssues.Toggled
        If CurrentRec IsNot Nothing Then
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)

            Dim shouldBe = CurrentRec.IgnoreIssues.HasValue AndAlso CurrentRec.IgnoreIssues = 100

            If shouldBe = ToggleSwitchIgnoreIssues.IsOn Then
                Return
            End If

            If ToggleSwitchIgnoreIssues.IsOn Then
                _logger.Debug("Marking Enforce Conum: {Conum} Prnum: {Prnum} Id: {Id}", Rec.CoNum, Rec.PrNum, Rec.ID)
                Rec.ProcessNote += If(Rec.ProcessNote = Nothing, "", vbCrLf) + "Ignore issued by " + UserName + " on " + Date.Now()
                Rec.IgnoreIssues = 100
                DB.SaveChanges()
                CurrentRec.IgnoreIssues = 100
                Dim email = EmailHelpers.SendEmailToUdf("EnforcePayrollNotificationEmail", $"Payroll Enforced on Co# {Rec.CoNum} Pr# {Rec.PrNum}", $"<p>Payroll was enforced by user {UserName}</p>")
                email.CcEmail.Add(UserInfo.email)

                Dim currentIssues = Query(Of htmlTableResult)($"DECLARE @html VARCHAR(MAX)
DECLARE @rowCount int

EXEC custom.prc_QueryToHtmlTable @query = N'SELECT cpil.* FROM custom.CheckPayrollIssuesLog cpil 
		CROSS APPLY (
			SELECT	ID = MAX(ccl.ID) FROM custom.CPI_CallLog ccl
			WHERE	ccl.CoNum = cpil.CoNum AND ccl.PrNum = cpil.PrNum
		) ccl
WHERE	cpil.CoNum = {Rec.CoNum} AND cpil.PrNum = {Rec.PrNum} AND cpil.CPI_CallLogID = ccl.ID'
								,@orderBy = N''
								,@html = @html OUT
								,@RowCount = @rowCount OUT
SELECT RowCnt = @rowCount, Html = @html").FirstOrDefault()

                If currentIssues IsNot Nothing AndAlso currentIssues.RowCnt > 0 Then
                    email.Body += "<p></p>" + currentIssues.Html
                End If
                email.IsBodyHtml = True
                email.SendEmail()

            Else
                _logger.Debug("Removing Enforce Conum: {Conum} Prnum: {Prnum} Id: {Id}", Rec.CoNum, Rec.PrNum, Rec.ID)
                Rec.ProcessNote += If(Rec.ProcessNote = Nothing, "", vbCrLf) + "Ignore removed by " + UserName + " on " + Date.Now()
                Rec.IgnoreIssues = 50
                DB.SaveChanges()
                CurrentRec.IgnoreIssues = 50
            End If
        End If
    End Sub

    Private Sub ToggleSwitchSubmitInEP_Toggled(sender As Object, e As EventArgs) Handles ToggleSwitchSubmitInEP.Toggled
        If CurrentRec IsNot Nothing Then
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)

            Dim shouldBe = CurrentRec.SubmitInEP.HasValue AndAlso CurrentRec.SubmitInEP = True

            If shouldBe = ToggleSwitchSubmitInEP.IsOn Then
                Return
            End If

            If ToggleSwitchSubmitInEP.IsOn Then
                _logger.Debug("Marking Submit in EP Conum: {Conum} Prnum: {Prnum} Id: {Id}", Rec.CoNum, Rec.PrNum, Rec.ID)
                Rec.ProcessNote += If(Rec.ProcessNote = Nothing, "", vbCrLf) + "Submit in EP issued by " + UserName + " on " + Date.Now()
                Rec.SubmitInEP = True
                DB.SaveChanges()
                CurrentRec.SubmitInEP = True
                Dim email = EmailHelpers.SendEmailToUdf("EnforcePayrollNotificationEmail", $"Payroll Submit in EP set on Co# {Rec.CoNum} Pr# {Rec.PrNum}", $"<p>Payroll was set to Submit in EP by user {UserName}</p>")
                email.CcEmail.Add(UserInfo.email)

                Dim currentIssues = Query(Of htmlTableResult)($"DECLARE @html VARCHAR(MAX)
DECLARE @rowCount int

EXEC custom.prc_QueryToHtmlTable @query = N'SELECT cpil.* FROM custom.CheckPayrollIssuesLog cpil 
		CROSS APPLY (
			SELECT	ID = MAX(ccl.ID) FROM custom.CPI_CallLog ccl
			WHERE	ccl.CoNum = cpil.CoNum AND ccl.PrNum = cpil.PrNum
		) ccl
WHERE	cpil.CoNum = {Rec.CoNum} AND cpil.PrNum = {Rec.PrNum} AND cpil.CPI_CallLogID = ccl.ID'
								,@orderBy = N''
								,@html = @html OUT
								,@RowCount = @rowCount OUT
SELECT RowCnt = @rowCount, Html = @html").FirstOrDefault()

                If currentIssues IsNot Nothing AndAlso currentIssues.RowCnt > 0 Then
                    email.Body += "<p></p>" + currentIssues.Html
                End If
                email.IsBodyHtml = True
                email.SendEmail()

            Else
                _logger.Debug("Removing Submit in EP Conum: {Conum} Prnum: {Prnum} Id: {Id}", Rec.CoNum, Rec.PrNum, Rec.ID)
                Rec.ProcessNote += If(Rec.ProcessNote = Nothing, "", vbCrLf) + "Submit in EP removed by " + UserName + " on " + Date.Now()
                Rec.SubmitInEP = False
                DB.SaveChanges()
                CurrentRec.SubmitInEP = False
            End If
        End If
    End Sub

    'Private Sub sbSubmitInEP_Click(sender As Object, e As EventArgs) Handles sbSubmitInEP.Click
    '    Try
    '        'should be on since its visible
    '        If ToggleSwitchIgnoreIssues.IsOn Then
    '            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
    '            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
    '            If Not Rec.IgnoreIssues = 100 Then
    '                Rec.ProcessNote += If(Rec.ProcessNote = Nothing, "", vbCrLf) + "Ignore issued by " + UserName + " on " + Date.Now()
    '                Rec.IgnoreIssues = 100
    '                DB.SaveChanges()
    '            End If
    '            MessageBox.Show($"Payroll can now be submitted from EP{vbCrLf}although payroll contains messages", "Payroll Enforce Done", MessageBoxButtons.OK)
    '        End If
    '    Catch ex As Exception
    '        DisplayMessageBox("Error setting allow to submit in EP")
    '    End Try
    'End Sub

    Private Sub ProcessOverride(OverrideType As String)
        GridViewNotes.ShowLoadingPanel()
        Try
            Dim OverrideUtility As New PayrollCheckOverrides(CurrentRec.CoNum, CurrentRec.PrNum, Nothing)
            Select Case OverrideType
                Case "RES_ST_OVRIDE"
                    OverrideUtility.ProcessResStateWHOverride("Reduce Res WH Amt by Work St WH Amt")
                    MessageBox.Show("Completed")
                Case "NETOVR"
                    'Was removed on Jul 15, '20 by Marty and reinstated by Solomon per HershyW on Dec 12, '21.
                    Dim netSetupOverrides = (From A In DB.pr_batch_overrides_setups Where A.CoNum = CurrentRec.CoNum AndAlso A.RecordType = "Net Override" AndAlso A.NetOverrideAmount.GetValueOrDefault <> 0).ToList
                    Dim checks = (From A In DB.CHK_MASTs
                                  Where A.CONUM = CurrentRec.CoNum AndAlso A.PAYROLL_NUM = CurrentRec.PrNum AndAlso A.CHK_COUNTER = 1 AndAlso (A.CHK_TYPE = "NORMAL" OrElse A.CHK_TYPE = "DD") And
                                      (From B In netSetupOverrides Select B.EmpNum).Contains(A.EMPNUM)
                                  Select A.EMPNUM, A.NET).ToList
                    Dim netOverrides = (From A In netSetupOverrides
                                        Join B In checks On A.EmpNum Equals B.EMPNUM
                                        Where A.NetOverrideAmount <> B.NET
                                        Select New pr_batch_override With {.CONUM = CurrentRec.CoNum, .PRNUM = CurrentRec.PrNum, .EMPNUM = A.EmpNum, .CHK_COUNTER = 1, .NetOverrideAmount = A.NetOverrideAmount, .NetOverrideAdjustType = A.NetOverrideAdjustType, .NetOverrideDedNum = A.NetOverrideDedNum}
                                        ).ToList
                    OverrideUtility.ProcessNetOverrides(netOverrides)
                    MessageBox.Show("Completed")
            End Select
        Catch ex As Exception
            DisplayErrorMessage($"Error in ProcessOverride. OverrideType: {OverrideType} Co#: {CurrentRec.CoNum} Pr#: {CurrentRec.PrNum}", ex)
        Finally
            GridViewNotes.HideLoadingPanel()
        End Try
    End Sub

    Private Sub GridViewNSFHold_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridViewNSFHold.PopupMenuShowing
        If e.MenuType = GridMenuType.Row Then
            e.Menu.Items.Add(New DXMenuItem("Remove NSF Hold", click:=AddressOf ChangeProcessStatus) With {.Tag = e.HitInfo.RowInfo})
        End If
    End Sub

    Private Sub GridViewNotes_CustomDrawRowIndicator(sender As Object, e As RowIndicatorCustomDrawEventArgs) Handles GridViewNotes.CustomDrawRowIndicator
        If e.Info.IsRowIndicator AndAlso e.RowHandle >= 0 Then
            Dim Value As String = Me.GridViewNotes.GetRowCellValue(e.RowHandle, "msg_type")
            If Value IsNot Nothing AndAlso NoteTypeWithOption.ContainsKey(Value) Then
                'e.Graphics.DrawImage(Me.riemCheckEditActionIcon.PictureChecked, e.Bounds.Location)
                e.Info.ImageIndex = -1
                e.Painter.DrawObject(e.Info)
                e.Graphics.DrawImageUnscaled(Me.ImageCollection1.Images(0), e.Bounds.X, e.Bounds.Y)
                e.Handled = True
            End If
        End If
    End Sub

    Private Sub GridViewNotes_CustomColumnDisplayText(sender As Object, e As CustomColumnDisplayTextEventArgs) Handles GridViewNotes.CustomColumnDisplayText
        If e.DisplayText = Nothing Then
            e.DisplayText = String.Empty
        End If
    End Sub

    Private Async Sub btnHandleAllIssues_Click(sender As Object, e As EventArgs) Handles btnHandleAllIssues.Click
        Dim batch = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, batch)
        If batch.IsQueued Then
            DisplayMessageBox("This payroll was already sent to be processed in the Background")
            Exit Sub
        End If

        Dim status = DB.PAYROLLs.Where(Function(f) f.CONUM = CurrentRec.CoNum AndAlso f.PRNUM = CurrentRec.PrNum).First()?.PAYROLL_STATUS
        If status <> "Entering Checks" AndAlso status <> "Submitted" Then
            XtraMessageBox.Show("Payroll not in Entering Checks or Submitted")
            Return
        End If

        If (XtraMessageBox.Show("Are you sure?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = DialogResult.No) Then
            Return
        End If


        'If bciProcessInBackground.Checked Then
        '    Await SetIsQueuedAsync()
        '    'Also queue to fix them all.
        '    Exit Sub
        'End If

        Dim Processed = New List(Of pr_batch_msg)
        'always run the utility before sending the register. 
Start:
        Try
            For ix = 0 To Me.GridViewNotes.DataRowCount - 1
                Dim rec As pr_batch_msg = Me.GridViewNotes.GetRow(ix)
                If NoteTypeWithOption.ContainsKey(rec.msg_type) AndAlso (Not Processed.Any(Function(p) p.msg_type = rec.msg_type)) Then
                    If rec.msg_type = "EMLREG" Then
                        'Check if already emailed
                        Dim HasLog = (From A As pr_batch_msg In PrbatchmsgBindingSource.List Where A.msg_type = "EMLREG_LOG").Any
                        If HasLog Then Continue For
                    ElseIf rec.msg_type = "MANCHKTOADDYR" OrElse rec.msg_type = "VOIDSTOADDYR" Then
                        Continue For
                    End If
                    SplashScreenManager1.TryShowWaitForm(True)
                    SplashScreenManager1.SetWaitFormDescription($"Processing {rec.msg_type}...")
                    Me.GridViewNotes.FocusedRowHandle = ix
                    Cursor = Cursors.WaitCursor
                    Await RunFixAsync(rec.msg_type)
                    Cursor = Cursors.Default
                    Processed.Add(rec)
                    GoTo Start
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage(ex.Message, ex)
        Finally
            If SplashScreenManager1.IsSplashFormVisible Then
                SplashScreenManager1.CloseWaitForm()
            End If
            Cursor = Cursors.Default
        End Try

        DisplayMessageBox($"{Processed.Count} item{If(Processed.Count = 1, "", "s")} processed", "Completed")
    End Sub



    Function DeleteZeroChecks() As Integer
        Dim Checks = (From A In DB.CHK_MASTs
                      Where A.CONUM = CurrentRec.CoNum AndAlso A.PAYROLL_NUM = CurrentRec.PrNum And
                          Math.Abs(A.NET.GetValueOrDefault) <= 0.05 And
                          A.GROSS = 0 And
                          Math.Abs(A.MEDICARE.GetValueOrDefault + A.OASDI.GetValueOrDefault) <= 0.05 And
                          Math.Abs(A.ER_MEDICARE.GetValueOrDefault + A.ER_OASDI.GetValueOrDefault) <= 0.05 And
                          A.EMP_UCI.GetValueOrDefault = 0 And
                          A.STATE_AMOUNT.GetValueOrDefault = 0 And
                          A.EIC.GetValueOrDefault = 0 And
                          A.EMP_DIS.GetValueOrDefault = 0 And
                          A.FED_WH.GetValueOrDefault = 0 And
                          A.STATE_RES_AMT.GetValueOrDefault = 0 And
                          A.WC_TAX.GetValueOrDefault = 0 And
                          A.NJ_WF_TAX.GetValueOrDefault = 0 And
                          {"NORMAL", "DD"}.Contains(A.CHK_TYPE)
                          ).ToList

        Dim deleted As Integer
        For Each chk In Checks
            Dim Pays = chk.CHK_DET_PAYs.ToList
            Dim Deds = chk.CHK_DET_DEDs.ToList
            Dim Memos = chk.CHK_DET_MEMOs.ToList
            Dim NoPays = Pays.TrueForAll(Function(p) p.CL_REG_PAY.GetValueOrDefault = 0 AndAlso
                                             p.CL_OT_PAY.GetValueOrDefault = 0 AndAlso
                                             p.CL_OT_PAY.GetValueOrDefault = 0 AndAlso
                                             p.CL_LOC_AMT1.GetValueOrDefault = 0 AndAlso
                                             p.CL_LOC_AMT2.GetValueOrDefault = 0 AndAlso
                                             p.CL_LOC_AMT3.GetValueOrDefault = 0 AndAlso
                                             p.CL_LOC_AMT4.GetValueOrDefault = 0 AndAlso
                                             p.CL_LOC_AMT5.GetValueOrDefault = 0)
            If NoPays AndAlso
                Deds.TrueForAll(Function(p) p.CL_AMOUNT.GetValueOrDefault = 0) AndAlso
                Memos.TrueForAll(Function(p) p.CL_AMOUNT.GetValueOrDefault = 0) Then
                DB.CHK_DET_MEMOs.DeleteAllOnSubmit(Memos)
                DB.CHK_DET_DEDs.DeleteAllOnSubmit(Deds)
                DB.CHK_DET_PAYs.DeleteAllOnSubmit(Pays)
                DB.CHK_MASTs.DeleteOnSubmit(chk)
                deleted += 1
            End If
        Next
        DB.SubmitChanges()
        Return deleted
    End Function

    Private Sub GridView1_CustomUnboundColumnData(sender As Object, e As CustomColumnDataEventArgs) Handles GridView1.CustomUnboundColumnData
        If e.IsGetData AndAlso e.Column.FieldName = "Group" Then
            Dim row As view_PayrollInProcess = e.Row
            If row.IsQueued Then
                e.Value = 6
            ElseIf row.IsPendingApproval AndAlso Not row.ClientApprovedRegisterOn.HasValue Then
                e.Value = 4
            ElseIf String.Equals(row.ProcessedBy, Globals.UserName, StringComparison.InvariantCultureIgnoreCase) Then
                e.Value = 0
            ElseIf (row.OnlineAuto & "" = "Online" OrElse row.OnlineAuto = "Auto") AndAlso Not row.AutoFixPayrollCompletedOn.HasValue Then
                e.Value = 5
            ElseIf Not row.AutoFixPayrollCompletedOn.HasValue AndAlso row.ClientApprovedRegisterVia = "Paydeck" AndAlso row.ClientApprovedRegisterOn.HasValue Then
                e.Value = 5
            ElseIf row.OnlineAuto & "" = "Online" Then
                e.Value = 1
            ElseIf row.OnlineAuto & "" = "Auto" Then
                e.Value = 2
            Else
                e.Value = 3
            End If
        End If
    End Sub

    Private Sub btnAddNote_Click(sender As Object, e As EventArgs) Handles btnAddNote.Click
        If CurrentRec Is Nothing Then Exit Sub
        Dim editor = New MemoEdit
        editor.Properties.LinesCount = 5

        Dim args = New XtraInputBoxArgs() With {
            .Caption = "Enter Note",
            .Prompt = "Notes",
            .Editor = editor}

        Dim results = XtraInputBox.Show(args)
        If results Is Nothing Then
            Exit Sub
        Else
            Dim msg As New pr_batch_note With {.rowguid = Guid.NewGuid,
                                            .Conum = CurrentRec.CoNum,
                                            .PrNum = CurrentRec.PrNum,
                                            .DateEntered = DateTime.Now,
                                            .EnteredBy = UserName,
                                            .Note = results,
                                            .Priority = "1-High",
                                            .ListID = Guid.NewGuid}
            DB.pr_batch_notes.InsertOnSubmit(msg)
            DB.SaveChanges()
            LoadNotes()
        End If
    End Sub

    Private Async Function SetIsQueuedAsync(isQueued As Boolean, Optional row As view_PayrollInProcess = Nothing) As Threading.Tasks.Task
        If row Is Nothing Then row = CurrentRec
        row.IsQueued = isQueued
        GridView1.RefreshRow(GridView1.FocusedRowHandle)
        GridView1.RefreshData()
        Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = row.ID).Single
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
        Rec.IsQueued = isQueued
        DB.SaveChanges()
        Await modSignalRCoreClient.connection.InvokeAsync("PrInProcessUpdate", New Brands.DAL.DomainModels.App.PayrollInProcessUpdate With {.Id = row.ID, .IsQueued = isQueued, .Remove = False, .AutoFixPayrollCompletedOn = row.AutoFixPayrollCompletedOn})
        DB.SaveChanges()
    End Function

    Private Sub frmPayrollsInProcessList_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        RemoveHandler modSignalRCoreClient.OnPrInProcessUpdate, AddressOf PrInProcessUpdated
    End Sub

    Private Sub cbPrinters_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles cbPrinters.ButtonClick
        If e.Button.Index = 1 Then
            Dim SelectedPrinter = Me.cbPrinters.EditValue
            PrintableComponentLink1.CreateDocument()
            Me.PrintableComponentLink1.Print(SelectedPrinter)
        End If
    End Sub

    Private Sub ToolTipController1_GetActiveObjectInfo(sender As Object, e As DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs) Handles ToolTipController1.GetActiveObjectInfo
        Try
            If e.SelectedControl IsNot GridControlNotes Then Exit Sub
            Dim view As GridView = GridControlNotes.GetViewAt(e.ControlMousePosition)
            If view Is Nothing Then Return
            Dim hi As GridHitInfo = view.CalcHitInfo(e.ControlMousePosition)
            If hi.HitTest = GridHitTest.RowCell Then
                Dim msgType = GridViewNotes.GetRowCellValue(hi.RowHandle, colmsg_type)
                If {"Payroll", "Company", "EMPSPCMSG"}.Contains(msgType) Then
                    e.Info = New ToolTipControlInfo(hi.Column, "Right click to edit note.", ToolTipIconType.Information) With {.HideHintOnMouseMove = True, .ImmediateToolTip = True}
                Else
                    e.Info = Nothing
                End If
            End If
        Catch ex As Exception
            _logger.Error(ex, "Error in setting the tool tip item")
        End Try
    End Sub

    Private Sub bbiAutoFixPayrollsLogs_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiAutoFixPayrollsLogs.ItemClick
        MainForm.AddOrActivateForm(Of frmPayrollsInProcessAutoFixLogs)()
    End Sub

    Private Sub bciAutoFixStatus_CheckedChanged(sender As Object, e As ItemClickEventArgs)
        SetUdfValue("BackgroundAutofixPayrolls Status", If(bciAutoFixStatus.Checked, "On", "Off"))
    End Sub

    Private Sub GridViewNotes_CustomColumnSort(sender As Object, e As CustomColumnSortEventArgs) Handles GridViewNotes.CustomColumnSort
        If e.Column.FieldName = "msg_type" Then
            Dim val1 = GridViewNotes.GetListSourceRowCellValue(e.ListSourceRowIndex1, "msg_type")
            Dim val2 = GridViewNotes.GetListSourceRowCellValue(e.ListSourceRowIndex2, "msg_type")
            e.Handled = True
            If val1 = "SpcMsg" AndAlso val2 <> "SpcMsg" Then
                e.Result = -1
            ElseIf val1 <> "SpcMsg" AndAlso val2 = "SpcMsg" Then
                e.Result = 1
            Else
                e.Result = 0
            End If
        End If
    End Sub

    Private Sub deToCheckDate_EditValueChanged(sender As Object, e As EventArgs) Handles deToCheckDate.EditValueChanged
        If deToCheckDate.EditValue = Nothing Then Return
        LoadPayrollsIncomplete()
    End Sub

    Private Sub ceShowAll_CheckedChanged(sender As Object, e As EventArgs) Handles ceShowAll.CheckedChanged
        LoadPayrollsIncomplete()
    End Sub

    'Private Sub FixDuplicateCheckNumbers(showOptions As Boolean)
    '    Dim DupChecks = DB.fn_GetDuplicateCheckNumbers(CurrentRec.CoNum, CurrentRec.PrNum).ToList
    '    Dim msgs As New List(Of String)
    '    If DupChecks.Any(Function(p) p.DBANKSAME = "YES") Then
    '        Dim NextNum = (From A In DupChecks Where A.DBANKSAME = "YES" Select A.ChkNum).Max + 1
    '        Dim PayrollRec = (From A In DB.PAYROLLs Where A.CONUM = CurrentRec.CoNum And A.PRNUM = CurrentRec.PrNum).Single
    '        msgs.Add($"Starting check number for payroll was updated from {PayrollRec.BEG_CKNUM} to {NextNum}")
    '        PayrollRec.BEG_CKNUM = NextNum
    '    End If
    '    If DupChecks.Any(Function(p) p.DBANKSAME = "NO") Then
    '        Dim DivisionChkNumbers = (From A In DupChecks Where A.DBANKSAME = "NO" Group By A.DivNum Into MaxNum = Max(A.ChkNum)).ToDictionary(Function(p) p.DivNum, Function(p) p.MaxNum)
    '        Dim Divisions = (From A In DB.DIVISIONs Where A.CONUM = CurrentRec.CoNum And DivisionChkNumbers.Keys.Contains(A.DDIVNUM)).ToList
    '        For Each divison In Divisions
    '            msgs.Add($"Starting check number for Division # {divison.DDIVNUM} was updated from {divison.PR_CKNUM} to {DivisionChkNumbers(divison.DDIVNUM) + 1}")
    '            divison.PR_CKNUM = DivisionChkNumbers(divison.DDIVNUM) + 1
    '        Next
    '    End If
    '    DB.SubmitChanges()
    '    If showOptions Then
    '        DisplayMessageBox(String.Join(vbCrLf, msgs), "Updated")
    '    End If
    'End Sub

    Private Sub riSlueCoNumName_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riSlueCoNumName1.ButtonClick, riSlueCoNumName2.ButtonClick, riSlueCoNumName3.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Search Then
            Dim editor As SearchLookUpEdit = sender
            If editor.EditValue IsNot Nothing AndAlso editor.EditValue <> 0 Then MainForm.OpenCompForm(editor.EditValue)
        End If
    End Sub

    Private Sub SetupGridView(GV As GridView, CoNumSearchCol As GridColumn)
        GV.OptionsBehavior.Editable = True

        If GV.Equals(GridViewIncompletePayroll) Then
            Dim BCol As New DevExpress.XtraGrid.Columns.GridColumn With
                                                            {.Name = "colNotes",
                                                                .Caption = "Notes",
                                                                .Visible = True,
                                                                .Width = 40,
                                                                .FieldName = "HasNotes",
                                                                .UnboundType = DevExpress.Data.UnboundColumnType.Integer,
                                                                .UnboundExpression = "[NotePriority]"
                                                            }
            BCol.OptionsColumn.ReadOnly = True
            BCol.OptionsColumn.AllowEdit = False
            BCol.VisibleIndex = 1
            BCol.ColumnEdit = riNotesImageComboBox
            GridViewIncompletePayroll.Columns.Add(BCol)
        End If

        Dim gridCol As GridColumn
        For Each gridCol In GV.Columns
            If gridCol.ReadOnly = True AndAlso gridCol.Equals(CoNumSearchCol) Then
                gridCol.OptionsColumn.ReadOnly = False
            ElseIf gridCol.ReadOnly = False AndAlso Not gridCol.Equals(CoNumSearchCol) Then
                gridCol.OptionsColumn.ReadOnly = True
            End If
        Next
    End Sub

    Private Sub GridControlIncompletePayrolls_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles GridControlIncompletePayrolls.MouseDoubleClick
        Dim hi = Me.GridViewIncompletePayroll.CalcHitInfo(e.Location)
        If hi.RowHandle < 0 Then Return
        If hi.Column Is Nothing Then Return

        Dim Row As prc_PayrollsIncompleteResult = Me.GridViewIncompletePayroll.GetRow(Me.GridViewIncompletePayroll.FocusedRowHandle)
        If hi.Column.Name = "colNotes" Then
            Dim notesList = getPayrollNotes(Row.CONUM, CType(Row.PRNUM, Decimal))
            Dim frmNotes = New frmPayrollNotes(Row.CONUM, CType(Row.PRNUM, Decimal), notesList)
            frmNotes.ShowDialog()

            notesList = getPayrollNotes(Row.CONUM, CType(Row.PRNUM, Decimal))
            If notesList.Count <> 0 Then
                Dim note As pr_batch_note = notesList.OrderBy(Function(f) f.Priority).FirstOrDefault()
                Row.Note = note.Note
                Row.NotePriority = note.Priority.Substring(0, 1)
            End If
        End If
    End Sub

    Private Sub ToggleSwitchIgnoreIssues_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles ToggleSwitchIgnoreIssues.EditValueChanging
        If CurrentRec IsNot Nothing Then
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
            Dim shouldBe = CurrentRec.IgnoreIssues.HasValue AndAlso CurrentRec.IgnoreIssues = 100

            If shouldBe = e.NewValue Then
                Return
            End If

            If e.NewValue = True Then
                If MessageBox.Show("Please be very carefull with this action

If confirmed, an email will also be sent to your manager.", "Are you sure you want to ignore all issues", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button3) <> DialogResult.Yes Then
                    e.Cancel = True
                End If
            Else
                If MessageBox.Show("Please confirm removing ignore", "Are you sure you want to remove ignore all issues", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button3) <> DialogResult.Yes Then
                    e.Cancel = True
                End If
            End If
        End If
    End Sub

    Class htmlTableResult
        Property RowCnt As Int32
        Property Html As String
    End Class

    Private Sub ToggleSwitchSubmitInEP_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles ToggleSwitchSubmitInEP.EditValueChanging
        If CurrentRec IsNot Nothing Then
            Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = CurrentRec.ID).Single
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Rec)
            Dim shouldBe = CurrentRec.SubmitInEP.HasValue AndAlso CurrentRec.SubmitInEP = True

            If shouldBe = e.NewValue Then
                Return
            End If

            If e.NewValue = True Then
                If MessageBox.Show("Please confirm setting Submit In EP.", "Confirm", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button3) <> DialogResult.Yes Then
                    e.Cancel = True
                End If
            Else
                If MessageBox.Show("Please confirm removing Submit In EP", "Confirm", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button3) <> DialogResult.Yes Then
                    e.Cancel = True
                End If
            End If
        End If

    End Sub
End Class