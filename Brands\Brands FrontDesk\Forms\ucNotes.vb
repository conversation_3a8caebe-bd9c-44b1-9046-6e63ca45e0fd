﻿Imports System.ComponentModel
Imports System.ComponentModel.DataAnnotations.Schema
Imports DevExpress.XtraEditors

Public Class ucNotes
    Public Sub New()
        InitializeComponent()
    End Sub

    Public Sub New(coNum As Decimal)
        InitializeComponent()
        Me.CoNum = coNum
    End Sub

    Dim _notes As List(Of NOTE)
    Dim DB As dbEPDataDataContext
    Dim _empnum As Decimal?
    Dim _conum As Decimal


    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property CoNum() As Decimal
        Get
            Return _conum
        End Get
        Set(ByVal value As Decimal)
            _conum = value
            If Not IsNothing(DB) Then
                _notes = DB.NOTEs.Where(Function(n) n.conum.Equals(value)).OrderByDescending(Function(n) n.modify_date).ToList
                gcNotes.DataSource = _notes
            End If
        End Set
    End Property

    Public Sub LoadNotes(ByVal DB As dbEPDataDataContext, ByVal CoNum As Decimal, Optional ByVal EmpNum As Decimal? = Nothing, Optional ExcludCat As String = Nothing)
        Me.DB = DB
        Me._conum = CoNum
        _empnum = EmpNum
        Dim notesQuery = DB.NOTEs.Where(Function(n) n.conum.Equals(_conum) AndAlso (n.expiration_date Is Nothing OrElse n.expiration_date.Value > Date.Today)).OrderByDescending(Function(n) n.modify_date)
        If Not IsNothing(_empnum) Then
            notesQuery = notesQuery.Where(Function(n) n.empnum.HasValue AndAlso n.empnum.Value = _empnum.Value)
        End If
        If ExcludCat IsNot Nothing Then
            notesQuery = notesQuery.Where(Function(n) n.category <> ExcludCat)
        End If
        _notes = notesQuery.ToList
        gcNotes.DataSource = _notes
        If Not _notes.Any Then
            Me.NOTEBindingSource.Clear()
            lcgNoteDetails.Enabled = False
            btnDeleteNote.Enabled = False
        End If
    End Sub

    Private Sub gvNotes_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvNotes.FocusedRowObjectChanged
        Dim _note As NOTE = e.Row
        If _note IsNot Nothing Then
            Me.NOTEBindingSource.DataSource = _note
            lcgNoteDetails.Enabled = True
            btnDeleteNote.Enabled = True
        Else
            Me.NOTEBindingSource.Clear()
            lcgNoteDetails.Enabled = False
            btnDeleteNote.Enabled = False
        End If
    End Sub

    Public Function SaveChanges() As Boolean
        Return DB.SaveChanges()
    End Function

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles btnDeleteNote.Click
        If IsNothing(gvNotes.GetFocusedRow) Then Exit Sub
        If XtraMessageBox.Show("Delete - Are you sure?", "Delete?", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = DialogResult.OK Then
            DB.NOTEs.DeleteOnSubmit(gvNotes.GetFocusedRow)
            gvNotes.DeleteSelectedRows()
            NOTEBindingSource.Clear()
        End If
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        If _conum = 0 Or IsNothing(DB) Then Exit Sub
        If Not Valid() Then
            Exit Sub
        End If
        Dim _note = New NOTE With {.note_id = Guid.NewGuid, .conum = _conum, .create_date = DateTime.Now, .created_by = UserName, .modify_date = DateTime.Now, .empnum = _empnum,
                                   .category = IIf(IsNothing(_empnum), "Company", "Employee"), .priority = "2-Medium", .show2client = "NO", .show2emp = "NO"}
        InsertNote(_note)
    End Sub

    Private Sub gvNotes_BeforeLeaveRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowAllowEventArgs) Handles gvNotes.BeforeLeaveRow
        e.Allow = Valid()
    End Sub

    Private Function Valid() As Boolean
        Dim _note As NOTE = gvNotes.GetRow(gvNotes.FocusedRowHandle)
        If _note Is Nothing Then
            Return True
        End If
        If String.IsNullOrWhiteSpace(_note.title) Then
            XtraMessageBox.Show("Please enter a title")
            Return False
        Else
            Return True
        End If
    End Function

    Sub SearchNote(p1 As String, Optional showMsg As Boolean = True)
        If IsNothing(_notes) Then Return
        Dim no As NOTE = _notes.FirstOrDefault(Function(n) n.title.ToLower.Contains(p1.ToLower) Or n.ParsedNote.ToLower.Contains(p1.ToLower))
        If Not IsNothing(no) Then
            gvNotes.FocusedRowHandle = FindRowHandleByRowObject(gvNotes, no)
        ElseIf Valid() AndAlso showMsg AndAlso XtraMessageBox.Show("No note found with the term '{0}' inside, would you like  the system should create one?".FormatWith(p1), "Create New Note?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Dim _note = New NOTE With {.note_id = Guid.NewGuid, .conum = _conum, .create_date = DateTime.Now, .created_by = UserName, .modify_date = DateTime.Now, .empnum = _empnum, .category = "Employee", .priority = "3-Low", .show2client = "NO", .show2emp = "NO"}
            InsertNote(_note)
        End If
    End Sub

    Private Function FindRowHandleByRowObject(ByVal view As DevExpress.XtraGrid.Views.Grid.GridView, ByVal row As Object) As Integer
        Dim I As Integer
        If Not row Is Nothing Then
            For I = 0 To view.DataRowCount - 1
                If row.Equals(view.GetRow(I)) Then
                    Return I
                End If
            Next
        End If
        Return DevExpress.XtraGrid.GridControl.InvalidRowHandle
    End Function

    Public Sub SetFocuseNoteID(ID As Guid)
        Dim row = _notes.FirstOrDefault(Function(n) n.note_id = ID)
        If row IsNot Nothing Then
            Dim ix = Me.gvNotes.FindRow(row)
            If ix >= 0 Then
                Me.gvNotes.FocusedRowHandle = ix
            End If
        End If
    End Sub

    Private Sub InsertNote(ByVal _note As NOTE)
        DB.NOTEs.InsertOnSubmit(_note)
        _notes.Add(_note)
        NOTEBindingSource.DataSource = _note
        gvNotes.FocusedRowHandle = FindRowHandleByRowObject(gvNotes, _note)
        gcNotes.RefreshDataSource()
    End Sub

    Public Sub RefreshNotes()
        LoadNotes(DB, CoNum, _empnum)
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, _notes)
    End Sub

End Class


Partial Public Class NOTE
    <NotMapped>
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property ShowToClient() As Boolean
        Get
            Return IIf(show2client = "YES", True, False)
        End Get
        Set(ByVal value As Boolean)
            show2client = IIf(value, "YES", "NO")
        End Set
    End Property
    <NotMapped>
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property ShowToEmp() As Boolean
        Get
            Return IIf(show2emp = "YES", True, False)
        End Get
        Set(ByVal value As Boolean)
            show2emp = IIf(value, "YES", "NO")
        End Set
    End Property
End Class
