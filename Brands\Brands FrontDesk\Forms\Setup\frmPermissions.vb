﻿Imports DevExpress.XtraEditors

Public Class frmPermissions
    Sub New()
        InitializeComponent()
    End Sub

    Private db As dbEPDataDataContext
    Dim list As List(Of myDateTime) = New List(Of myDateTime)

    Private Sub Permissions_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub LoadData()
        Dim list = New List(Of DoorPermission)
        list.Add(New DoorPermission With {.Id = 0, .Display = "Hidden"})
        list.Add(New DoorPermission With {.Id = 1, .Display = "With Confirmation"})
        list.Add(New DoorPermission With {.Id = 2, .Display = "Allowed"})
        lueDoorPermission.Properties.DataSource = list
        lueDoorPermission.Properties.ValueMember = "Id"
        lueDoorPermission.Properties.DisplayMember = "Display"

        db = New dbEPDataDataContext(GetConnectionString)
        If Not (UserName = "moti.e" OrElse UserName = "hershy.w" OrElse UserName = "joel.f" OrElse UserName = "solomon.w") Then
            Close()
        End If
        bsAllUsers.DataSource = db.FrontDeskPermissions.ToList()
        cbeAsignedFaxCategory.Properties.Items.Add("")
        cbeAsignedFaxCategory.Properties.Items.AddRange(db.FaxCategories.Select(Function(c) c.Category).ToArray())
    End Sub

    Private Sub SetUser(username As String)
        Try
            Dim user = db.FrontDeskPermissions.Single(Function(f) f.UserName = username)
            FrontDeskPermissionBindingSource.DataSource = user
            Dim dt As DateTime
            If user.CsEmailNotificationInterval.IsNotNullOrWhiteSpace Then
                Dim list1 = user.CsEmailNotificationInterval.Split(",").Where(Function(t) DateTime.TryParse(t, dt))
                If list1.Count > 0 Then
                    list = list1.Select(Function(t) New myDateTime With {.Time = DateTime.Parse(t)}).ToList
                Else
                    list = New List(Of myDateTime)
                End If
            Else
                list = New List(Of myDateTime)
            End If

            BindingSource1.DataSource = list
            GridControl1.DataSource = BindingSource1

            bsFrontDeskRoles.DataSource = db.fn_FrontDeskUserRoles(username).OrderBy(Function(f) f.Category).ThenBy(Function(f) f.RoleName).ToList()
            gcUserRoles.DataSource = bsFrontDeskRoles
        Catch ex As Exception
            DisplayErrorMessage("Error in SetUser", ex)
        End Try
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim curRow = DirectCast(bsAllUsers.Current, FrontDeskPermission)
        If curRow.DDReversalLevel = "None" Then
            curRow.DDReversalLevel = Nothing
        End If
        Dim currentUser = curRow.UserName

        Dim exUser = db.FrontDeskPermissions.FirstOrDefault(Function(f) f.UserName = currentUser)

        'if username changed then skip this section
        If Not exUser Is Nothing Then
            Dim RolesCur As List(Of fn_FrontDeskUserRolesResult) = bsFrontDeskRoles.DataSource
            Dim RolesExist = db.fn_FrontDeskUserRoles(currentUser).ToList()

            Dim RolesMod = (From rc In RolesCur Join re In RolesExist On rc.RoleID Equals re.RoleID Where rc.IsMember <> re.IsMember Select rc.RoleID, rc.IsMember).ToList()

            For Each r In RolesMod
                If r.IsMember.Value = True Then
                    Dim NewMemberRole = New FrontDeskRoleUser With {.RoleID = r.RoleID, .UserName = currentUser}
                    db.FrontDeskRoleUsers.InsertOnSubmit(NewMemberRole)
                ElseIf r.IsMember.Value = False Then
                    Dim ExMemberRole = (From ex In db.FrontDeskRoleUsers Where ex.UserName = currentUser AndAlso ex.RoleID = r.RoleID Select ex).First
                    db.FrontDeskRoleUsers.DeleteOnSubmit(ExMemberRole)
                End If
            Next
        End If

        db.SaveChanges()
    End Sub

    Private Class myDateTime
        Public Property Time As DateTime
    End Class

    Private Sub BindingSource1_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs)
        Dim m = New myDateTime
        list.Add(m)
        e.NewObject = m
    End Sub

    Private Sub UpdateList()
        Dim user As FrontDeskPermission = FrontDeskPermissionBindingSource.DataSource
        user.CsEmailNotificationInterval = String.Join(",", list.Select(Function(f) "{0}:{1}".FormatWith(f.Time.Hour, f.Time.Minute)))
    End Sub

    Private Sub BindingSource1_ListChanged(sender As Object, e As System.ComponentModel.ListChangedEventArgs)
        UpdateList()
    End Sub

    Private Sub RepositoryItemTimeEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles RepositoryItemTimeEdit1.EditValueChanged
        UpdateList()
    End Sub

    Private Sub gvAllUsers_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvAllUsers.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
            Dim row As FrontDeskPermission = bsAllUsers.Current
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Copy User", Sub()
                                                                                   DuplicateRow(row)
                                                                               End Sub))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub()
                                                                                If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you would like to delete user: {row.UserName}", "Delete User?", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                                                                                    db.FrontDeskPermissions.DeleteOnSubmit(row)
                                                                                    db.SaveChanges
                                                                                    LoadData()
                                                                                End If
                                                                            End Sub))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Impersonate User Permission", Sub()
                                                                                                     ReloadPermissions(row.UserName)
                                                                                                     MainForm.SetPermissions()
                                                                                                 End Sub, My.Resources.switch_16x16) With {.BeginGroup = -True})
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Impersonate User Name", Sub()
                                                                                               UserName = row.UserName
                                                                                           End Sub, My.Resources.switch_16x16))

            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Refresh Permission", Sub()
                                                                                            modSignalRClient.PushRefreshPermission(row.UserName)
                                                                                        End Sub, My.Resources.refresh2_16x16))
        End If
    End Sub


    Private Sub DuplicateRow(row As FrontDeskPermission)
        Try
            If row Is Nothing Then Exit Sub
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim newRow = row.CloneEntity
            Dim s = InputBox("Please enter new User Name")
            newRow.UserName = s
            newRow.rowguid = Guid.NewGuid
            newRow.ZendeskEmailAddress = Nothing
            newRow.ZendeskFullName = Nothing
            newRow.ZendeskRole = Nothing
            newRow.ZendeskExternalLoginAllowed = False
            newRow.PhoneUserName = Nothing
            newRow.PhonePassword = Nothing
            newRow.LookAndFeel = Nothing
            newRow.SvgSkinPalette = Nothing
            newRow.CsEmailNotificationInterval = Nothing
            newRow.AsignedFaxCategory = Nothing
            newRow.WrikeToken = Nothing
            newRow.WrikeRefreshToken = Nothing
            newRow.WrikeRefreshToken = Nothing
            newRow.ComputerName = Nothing
            db.FrontDeskPermissions.InsertOnSubmit(newRow)

            'Solomon added on Oct 31, 21.  Copy user roles for new user
            Dim rolesToAdd = (From r In db.FrontDeskRoleUsers Where r.UserName = row.UserName Select r).ToList()
            For Each r In rolesToAdd
                db.FrontDeskRoleUsers.InsertOnSubmit(New FrontDeskRoleUser With {.RoleID = r.RoleID, .UserName = s})
            Next

            db.SaveChanges
            XtraMessageBox.Show("Reminder to setup new user with zendesk info")
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error copying row", ex)
        End Try
    End Sub

    Private Sub bsAllUsers_CurrentChanged(sender As Object, e As EventArgs) Handles bsAllUsers.CurrentChanged
        If bsAllUsers.Current Is Nothing Then
            FrontDeskPermissionBindingSource.Clear()
            BindingSource1.Clear()
        Else
            Dim row As FrontDeskPermission = bsAllUsers.Current
            SetUser(row.UserName)
        End If
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Class DoorPermission
        Public Property Id As Integer
        Public Property Display As String
    End Class

    Private Sub btnCreateOrUpdateUserInZendesk_Click(sender As Object, e As EventArgs) Handles btnCreateOrUpdateUserInZendesk.Click
        Try
            Dim user As FrontDeskPermission = bsAllUsers.Current
            If user Is Nothing Then
                XtraMessageBox.Show("No user is selected")
            Else
                GetZendeskEmailAddress()

                Dim email = user.ZendeskEmailAddress
                If email.IsNullOrWhiteSpace Then email = db.DBUSERs.SingleOrDefault(Function(u) u.name.ToLower = user.UserName.ToLower)?.email
                If email.IsNullOrWhiteSpace Then
                    XtraMessageBox.Show($"User: {user.UserName} does not have an email address setup in the system")
                End If

                Using frm = New frmCreateOrUpdateUser(user, email)
                    frm.ShowDialog()
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error creating/updating user", ex)
        End Try
    End Sub

    Private Sub sbAddNewRole_Click(sender As Object, e As EventArgs) Handles sbAddNewRole.Click
        Dim currentUser = DirectCast(bsAllUsers.Current, FrontDeskPermission).UserName

        Dim frmNewRole = New frmAddNewRole()
        frmNewRole.ShowDialog()

        SetUser(currentUser)
    End Sub
End Class