﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAccountLeadAddOrEdit
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.XtraScrollableControl2 = New DevExpress.XtraEditors.XtraScrollableControl()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.SearchLookUpEdit1 = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.BindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
        Me.bsCompany = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.btnAddConnectedCompany = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEdit11 = New DevExpress.XtraEditors.TextEdit()
        Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.bbiSave = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.DateEdit6 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit31 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit30 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit29 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit28 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit27 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit25 = New DevExpress.XtraEditors.TextEdit()
        Me.teShipZip = New DevExpress.XtraEditors.TextEdit()
        Me.teShipStreetAddress = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit22 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit21 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit20 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit19 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit18 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.cbePayFreuency = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TextEdit15 = New DevExpress.XtraEditors.TextEdit()
        Me.teCoStreetAddress = New DevExpress.XtraEditors.TextEdit()
        Me.teRoutingNumber = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit9 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit13 = New DevExpress.XtraEditors.TextEdit()
        Me.NumericUpDown1 = New System.Windows.Forms.NumericUpDown()
        Me.TextEdit12 = New DevExpress.XtraEditors.TextEdit()
        Me.teCoZip = New DevExpress.XtraEditors.TextEdit()
        Me.teFedId = New DevExpress.XtraEditors.TextEdit()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit4 = New DevExpress.XtraEditors.TextEdit()
        Me.cbeCoState = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TextEdit16 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.CalcEdit4 = New DevExpress.XtraEditors.TextEdit()
        Me.CalcEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.CalcEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.CalcEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.cbeShipState = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.teListEmployeeWorkStates = New DevExpress.XtraEditors.TokenEdit()
        Me.bePhone = New DevExpress.XtraEditors.ButtonEdit()
        Me.beCell = New DevExpress.XtraEditors.ButtonEdit()
        Me.beEmail = New DevExpress.XtraEditors.ButtonEdit()
        Me.beFax = New DevExpress.XtraEditors.ButtonEdit()
        Me.teConnectecCompanies = New DevExpress.XtraEditors.TokenEdit()
        Me.LayoutControlGroup7 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.SimpleSeparator1 = New DevExpress.XtraLayout.SimpleSeparator()
        Me.SimpleSeparator2 = New DevExpress.XtraLayout.SimpleSeparator()
        Me.item0 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem30 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem22 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem23 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem33 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem34 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem58 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.item1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem35 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup5 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem31 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem37 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem38 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem39 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem36 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem40 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup6 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem41 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem42 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem43 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem44 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem45 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem46 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup8 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem50 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem49 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem48 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup9 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem56 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem54 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem57 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem53 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem52 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem51 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem59 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem60 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.cbeAignedTo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.DateEdit5 = New DevExpress.XtraEditors.DateEdit()
        Me.DateEdit4 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit7 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit6 = New DevExpress.XtraEditors.TextEdit()
        Me.slueCoNum = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.SearchLookUpEdit2View = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCoNumAndName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPR_CONTACT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_PHONE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_FAX = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_MODEM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFED_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_EMAIL = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.cbeCategory = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.slueReferedByCoNum = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.SearchLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCoNumAndName1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPR_CONTACT1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_PHONE1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_MODEM1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_EMAIL1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.DateEdit3 = New DevExpress.XtraEditors.DateEdit()
        Me.DateEdit2 = New DevExpress.XtraEditors.DateEdit()
        Me.meNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.DateEdit1 = New DevExpress.XtraEditors.DateEdit()
        Me.cbeStatus = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.lcgHighImportance = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem32 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem25 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem24 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem47 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem26 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem27 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem28 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem29 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SimpleSeparator3 = New DevExpress.XtraLayout.SimpleSeparator()
        Me.SimpleSeparator4 = New DevExpress.XtraLayout.SimpleSeparator()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.RibbonPage2 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        Me.XtraScrollableControl2.SuspendLayout()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.SearchLookUpEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsCompany, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit11.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit6.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit31.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit30.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit29.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit28.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit27.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit25.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShipZip.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teShipStreetAddress.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit22.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit21.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit20.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit19.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit18.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbePayFreuency.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit15.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCoStreetAddress.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teRoutingNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit9.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit13.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NumericUpDown1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit12.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teCoZip.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teFedId.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeCoState.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit16.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CalcEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CalcEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CalcEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CalcEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeShipState.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teListEmployeeWorkStates.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bePhone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beCell.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beEmail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.beFax.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teConnectecCompanies.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleSeparator1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleSeparator2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item0, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem30, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem33, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem34, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem58, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem35, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem31, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem37, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem38, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem39, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem36, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem40, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem41, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem42, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem43, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem44, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem45, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem46, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem50, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem49, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem48, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem56, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem54, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem57, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem53, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem52, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem51, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem59, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem60, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeAignedTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit5.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit4.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit7.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueCoNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit2View, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.slueReferedByCoNum.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.meNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgHighImportance, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem32, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem47, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem28, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem29, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleSeparator3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SimpleSeparator4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.XtraScrollableControl2)
        Me.LayoutControl1.Controls.Add(Me.cbeAignedTo)
        Me.LayoutControl1.Controls.Add(Me.DateEdit5)
        Me.LayoutControl1.Controls.Add(Me.DateEdit4)
        Me.LayoutControl1.Controls.Add(Me.TextEdit7)
        Me.LayoutControl1.Controls.Add(Me.TextEdit6)
        Me.LayoutControl1.Controls.Add(Me.slueCoNum)
        Me.LayoutControl1.Controls.Add(Me.cbeCategory)
        Me.LayoutControl1.Controls.Add(Me.slueReferedByCoNum)
        Me.LayoutControl1.Controls.Add(Me.TextEdit2)
        Me.LayoutControl1.Controls.Add(Me.DateEdit3)
        Me.LayoutControl1.Controls.Add(Me.DateEdit2)
        Me.LayoutControl1.Controls.Add(Me.meNotes)
        Me.LayoutControl1.Controls.Add(Me.DateEdit1)
        Me.LayoutControl1.Controls.Add(Me.cbeStatus)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.HiddenItems.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lcgHighImportance})
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 144)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(2026, 271, 800, 579)
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1079, 533)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'XtraScrollableControl2
        '
        Me.XtraScrollableControl2.Controls.Add(Me.LayoutControl2)
        Me.XtraScrollableControl2.Location = New System.Drawing.Point(12, 177)
        Me.XtraScrollableControl2.Name = "XtraScrollableControl2"
        Me.XtraScrollableControl2.Size = New System.Drawing.Size(468, 318)
        Me.XtraScrollableControl2.TabIndex = 45
        '
        'LayoutControl2
        '
        Me.LayoutControl2.Controls.Add(Me.SearchLookUpEdit1)
        Me.LayoutControl2.Controls.Add(Me.btnAddConnectedCompany)
        Me.LayoutControl2.Controls.Add(Me.TextEdit11)
        Me.LayoutControl2.Controls.Add(Me.DateEdit6)
        Me.LayoutControl2.Controls.Add(Me.TextEdit31)
        Me.LayoutControl2.Controls.Add(Me.TextEdit30)
        Me.LayoutControl2.Controls.Add(Me.TextEdit29)
        Me.LayoutControl2.Controls.Add(Me.TextEdit28)
        Me.LayoutControl2.Controls.Add(Me.TextEdit27)
        Me.LayoutControl2.Controls.Add(Me.TextEdit25)
        Me.LayoutControl2.Controls.Add(Me.teShipZip)
        Me.LayoutControl2.Controls.Add(Me.teShipStreetAddress)
        Me.LayoutControl2.Controls.Add(Me.TextEdit22)
        Me.LayoutControl2.Controls.Add(Me.TextEdit21)
        Me.LayoutControl2.Controls.Add(Me.TextEdit20)
        Me.LayoutControl2.Controls.Add(Me.TextEdit19)
        Me.LayoutControl2.Controls.Add(Me.TextEdit18)
        Me.LayoutControl2.Controls.Add(Me.TextEdit1)
        Me.LayoutControl2.Controls.Add(Me.cbePayFreuency)
        Me.LayoutControl2.Controls.Add(Me.TextEdit15)
        Me.LayoutControl2.Controls.Add(Me.teCoStreetAddress)
        Me.LayoutControl2.Controls.Add(Me.teRoutingNumber)
        Me.LayoutControl2.Controls.Add(Me.TextEdit9)
        Me.LayoutControl2.Controls.Add(Me.TextEdit13)
        Me.LayoutControl2.Controls.Add(Me.NumericUpDown1)
        Me.LayoutControl2.Controls.Add(Me.TextEdit12)
        Me.LayoutControl2.Controls.Add(Me.teCoZip)
        Me.LayoutControl2.Controls.Add(Me.teFedId)
        Me.LayoutControl2.Controls.Add(Me.CheckEdit1)
        Me.LayoutControl2.Controls.Add(Me.TextEdit3)
        Me.LayoutControl2.Controls.Add(Me.TextEdit4)
        Me.LayoutControl2.Controls.Add(Me.cbeCoState)
        Me.LayoutControl2.Controls.Add(Me.TextEdit16)
        Me.LayoutControl2.Controls.Add(Me.CalcEdit4)
        Me.LayoutControl2.Controls.Add(Me.CalcEdit3)
        Me.LayoutControl2.Controls.Add(Me.CalcEdit2)
        Me.LayoutControl2.Controls.Add(Me.CalcEdit1)
        Me.LayoutControl2.Controls.Add(Me.cbeShipState)
        Me.LayoutControl2.Controls.Add(Me.teListEmployeeWorkStates)
        Me.LayoutControl2.Controls.Add(Me.bePhone)
        Me.LayoutControl2.Controls.Add(Me.beCell)
        Me.LayoutControl2.Controls.Add(Me.beEmail)
        Me.LayoutControl2.Controls.Add(Me.beFax)
        Me.LayoutControl2.Controls.Add(Me.teConnectecCompanies)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(1162, 506, 650, 400)
        Me.LayoutControl2.OptionsCustomizationForm.ShowPropertyGrid = True
        Me.LayoutControl2.Root = Me.LayoutControlGroup7
        Me.LayoutControl2.Size = New System.Drawing.Size(468, 318)
        Me.LayoutControl2.TabIndex = 0
        Me.LayoutControl2.Text = "LayoutControl2"
        '
        'SearchLookUpEdit1
        '
        Me.SearchLookUpEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ConnectedConumToCopyFrom", True))
        Me.SearchLookUpEdit1.Location = New System.Drawing.Point(180, 1017)
        Me.SearchLookUpEdit1.Name = "SearchLookUpEdit1"
        Me.SearchLookUpEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.SearchLookUpEdit1.Properties.DataSource = Me.bsCompany
        Me.SearchLookUpEdit1.Properties.DisplayMember = "CoNumAndName"
        Me.SearchLookUpEdit1.Properties.NullText = ""
        Me.SearchLookUpEdit1.Properties.PopupFormSize = New System.Drawing.Size(800, 0)
        Me.SearchLookUpEdit1.Properties.PopupView = Me.GridView2
        Me.SearchLookUpEdit1.Properties.ValueMember = "CONUM"
        Me.SearchLookUpEdit1.Size = New System.Drawing.Size(257, 20)
        Me.SearchLookUpEdit1.StyleController = Me.LayoutControl2
        Me.SearchLookUpEdit1.TabIndex = 68
        '
        'BindingSource1
        '
        Me.BindingSource1.DataSource = GetType(Brands_FrontDesk.AccountLead)
        '
        'bsCompany
        '
        Me.bsCompany.DataSource = GetType(Brands_FrontDesk.view_CompanySumarry)
        '
        'GridView2
        '
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6})
        Me.GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView2.OptionsView.ShowGroupPanel = False
        '
        'GridColumn1
        '
        Me.GridColumn1.FieldName = "CoNumAndName"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 150
        '
        'GridColumn2
        '
        Me.GridColumn2.FieldName = "PR_CONTACT"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 46
        '
        'GridColumn3
        '
        Me.GridColumn3.FieldName = "CO_PHONE"
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 2
        Me.GridColumn3.Width = 46
        '
        'GridColumn4
        '
        Me.GridColumn4.FieldName = "CO_MODEM"
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 3
        Me.GridColumn4.Width = 46
        '
        'GridColumn5
        '
        Me.GridColumn5.FieldName = "CO_EMAIL"
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 4
        Me.GridColumn5.Width = 46
        '
        'GridColumn6
        '
        Me.GridColumn6.FieldName = "CO_STATUS"
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 5
        Me.GridColumn6.Width = 50
        '
        'btnAddConnectedCompany
        '
        Me.btnAddConnectedCompany.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.add_16x16
        Me.btnAddConnectedCompany.Location = New System.Drawing.Point(415, 991)
        Me.btnAddConnectedCompany.Name = "btnAddConnectedCompany"
        Me.btnAddConnectedCompany.Size = New System.Drawing.Size(22, 22)
        Me.btnAddConnectedCompany.StyleController = Me.LayoutControl2
        Me.btnAddConnectedCompany.TabIndex = 67
        '
        'TextEdit11
        '
        Me.TextEdit11.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoCounty", True))
        Me.TextEdit11.Location = New System.Drawing.Point(91, 105)
        Me.TextEdit11.MenuManager = Me.RibbonControl1
        Me.TextEdit11.Name = "TextEdit11"
        Me.TextEdit11.Size = New System.Drawing.Size(132, 20)
        Me.TextEdit11.StyleController = Me.LayoutControl2
        Me.TextEdit11.TabIndex = 38
        '
        'RibbonControl1
        '
        Me.RibbonControl1.ExpandCollapseItem.Id = 0
        Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.bbiSave})
        Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl1.MaxItemId = 3
        Me.RibbonControl1.Name = "RibbonControl1"
        Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
        Me.RibbonControl1.Size = New System.Drawing.Size(1079, 144)
        '
        'bbiSave
        '
        Me.bbiSave.Caption = "Save"
        Me.bbiSave.Id = 1
        Me.bbiSave.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.save
        Me.bbiSave.Name = "bbiSave"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiSave)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.ShowCaptionButton = False
        Me.RibbonPageGroup1.Text = "Actions"
        '
        'DateEdit6
        '
        Me.DateEdit6.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "DateEstablished", True))
        Me.DateEdit6.EditValue = Nothing
        Me.DateEdit6.Location = New System.Drawing.Point(180, 1065)
        Me.DateEdit6.MenuManager = Me.RibbonControl1
        Me.DateEdit6.Name = "DateEdit6"
        Me.DateEdit6.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit6.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit6.Size = New System.Drawing.Size(257, 20)
        Me.DateEdit6.StyleController = Me.LayoutControl2
        Me.DateEdit6.TabIndex = 66
        '
        'TextEdit31
        '
        Me.TextEdit31.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "NumberOfLocation", True))
        Me.TextEdit31.Location = New System.Drawing.Point(180, 1041)
        Me.TextEdit31.MenuManager = Me.RibbonControl1
        Me.TextEdit31.Name = "TextEdit31"
        Me.TextEdit31.Size = New System.Drawing.Size(257, 20)
        Me.TextEdit31.StyleController = Me.LayoutControl2
        Me.TextEdit31.TabIndex = 63
        '
        'TextEdit30
        '
        Me.TextEdit30.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "TypeOfEntity", True))
        Me.TextEdit30.Location = New System.Drawing.Point(180, 1089)
        Me.TextEdit30.MenuManager = Me.RibbonControl1
        Me.TextEdit30.Name = "TextEdit30"
        Me.TextEdit30.Size = New System.Drawing.Size(257, 20)
        Me.TextEdit30.StyleController = Me.LayoutControl2
        Me.TextEdit30.TabIndex = 62
        '
        'TextEdit29
        '
        Me.TextEdit29.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "Industry", True))
        Me.TextEdit29.Location = New System.Drawing.Point(180, 1113)
        Me.TextEdit29.MenuManager = Me.RibbonControl1
        Me.TextEdit29.Name = "TextEdit29"
        Me.TextEdit29.Size = New System.Drawing.Size(257, 20)
        Me.TextEdit29.StyleController = Me.LayoutControl2
        Me.TextEdit29.TabIndex = 61
        '
        'TextEdit28
        '
        Me.TextEdit28.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "PromptTaxFilerOrCode", True))
        Me.TextEdit28.Location = New System.Drawing.Point(180, 1137)
        Me.TextEdit28.MenuManager = Me.RibbonControl1
        Me.TextEdit28.Name = "TextEdit28"
        Me.TextEdit28.Size = New System.Drawing.Size(257, 20)
        Me.TextEdit28.StyleController = Me.LayoutControl2
        Me.TextEdit28.TabIndex = 60
        '
        'TextEdit27
        '
        Me.TextEdit27.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ShippingContactName", True))
        Me.TextEdit27.Location = New System.Drawing.Point(91, 785)
        Me.TextEdit27.MenuManager = Me.RibbonControl1
        Me.TextEdit27.Name = "TextEdit27"
        Me.TextEdit27.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit27.StyleController = Me.LayoutControl2
        Me.TextEdit27.TabIndex = 55
        '
        'TextEdit25
        '
        Me.TextEdit25.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ShippingCity", True))
        Me.TextEdit25.Location = New System.Drawing.Point(91, 761)
        Me.TextEdit25.MenuManager = Me.RibbonControl1
        Me.TextEdit25.Name = "TextEdit25"
        Me.TextEdit25.Size = New System.Drawing.Size(116, 20)
        Me.TextEdit25.StyleController = Me.LayoutControl2
        Me.TextEdit25.TabIndex = 53
        '
        'teShipZip
        '
        Me.teShipZip.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ShippingZip", True))
        Me.teShipZip.Location = New System.Drawing.Point(343, 761)
        Me.teShipZip.MenuManager = Me.RibbonControl1
        Me.teShipZip.Name = "teShipZip"
        Me.teShipZip.Size = New System.Drawing.Size(94, 20)
        Me.teShipZip.StyleController = Me.LayoutControl2
        Me.teShipZip.TabIndex = 52
        '
        'teShipStreetAddress
        '
        Me.teShipStreetAddress.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ShippingStreetAddress", True))
        Me.teShipStreetAddress.Location = New System.Drawing.Point(91, 737)
        Me.teShipStreetAddress.MenuManager = Me.RibbonControl1
        Me.teShipStreetAddress.Name = "teShipStreetAddress"
        Me.teShipStreetAddress.Size = New System.Drawing.Size(346, 20)
        Me.teShipStreetAddress.StyleController = Me.LayoutControl2
        Me.teShipStreetAddress.TabIndex = 51
        '
        'TextEdit22
        '
        Me.TextEdit22.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ShippingName", True))
        Me.TextEdit22.Location = New System.Drawing.Point(91, 713)
        Me.TextEdit22.MenuManager = Me.RibbonControl1
        Me.TextEdit22.Name = "TextEdit22"
        Me.TextEdit22.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit22.StyleController = Me.LayoutControl2
        Me.TextEdit22.TabIndex = 50
        '
        'TextEdit21
        '
        Me.TextEdit21.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "UciIdNumber", True))
        Me.TextEdit21.Location = New System.Drawing.Point(186, 646)
        Me.TextEdit21.MenuManager = Me.RibbonControl1
        Me.TextEdit21.Name = "TextEdit21"
        Me.TextEdit21.Size = New System.Drawing.Size(251, 20)
        Me.TextEdit21.StyleController = Me.LayoutControl2
        Me.TextEdit21.TabIndex = 49
        '
        'TextEdit20
        '
        Me.TextEdit20.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "EndOfWeekDatPeriod", True))
        Me.TextEdit20.Location = New System.Drawing.Point(186, 526)
        Me.TextEdit20.MenuManager = Me.RibbonControl1
        Me.TextEdit20.Name = "TextEdit20"
        Me.TextEdit20.Size = New System.Drawing.Size(251, 20)
        Me.TextEdit20.StyleController = Me.LayoutControl2
        Me.TextEdit20.TabIndex = 48
        '
        'TextEdit19
        '
        Me.TextEdit19.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ProcessDayOfWeekPeriod", True))
        Me.TextEdit19.Location = New System.Drawing.Point(186, 574)
        Me.TextEdit19.MenuManager = Me.RibbonControl1
        Me.TextEdit19.Name = "TextEdit19"
        Me.TextEdit19.Size = New System.Drawing.Size(251, 20)
        Me.TextEdit19.StyleController = Me.LayoutControl2
        Me.TextEdit19.TabIndex = 47
        '
        'TextEdit18
        '
        Me.TextEdit18.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CheckDateDayOfWeekPerion", True))
        Me.TextEdit18.Location = New System.Drawing.Point(186, 550)
        Me.TextEdit18.MenuManager = Me.RibbonControl1
        Me.TextEdit18.Name = "TextEdit18"
        Me.TextEdit18.Size = New System.Drawing.Size(251, 20)
        Me.TextEdit18.StyleController = Me.LayoutControl2
        Me.TextEdit18.TabIndex = 46
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoName", True))
        Me.TextEdit1.Location = New System.Drawing.Point(91, 33)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Size = New System.Drawing.Size(127, 20)
        Me.TextEdit1.StyleController = Me.LayoutControl2
        Me.TextEdit1.TabIndex = 7
        '
        'cbePayFreuency
        '
        Me.cbePayFreuency.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "PayFrequency", True))
        Me.cbePayFreuency.Location = New System.Drawing.Point(186, 502)
        Me.cbePayFreuency.Name = "cbePayFreuency"
        Me.cbePayFreuency.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbePayFreuency.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbePayFreuency.Size = New System.Drawing.Size(251, 20)
        Me.cbePayFreuency.StyleController = Me.LayoutControl2
        Me.cbePayFreuency.TabIndex = 34
        '
        'TextEdit15
        '
        Me.TextEdit15.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "AccountNumber", True))
        Me.TextEdit15.Location = New System.Drawing.Point(91, 411)
        Me.TextEdit15.MenuManager = Me.RibbonControl1
        Me.TextEdit15.Name = "TextEdit15"
        Me.TextEdit15.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit15.StyleController = Me.LayoutControl2
        Me.TextEdit15.TabIndex = 43
        '
        'teCoStreetAddress
        '
        Me.teCoStreetAddress.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoStreetAddress", True))
        Me.teCoStreetAddress.Location = New System.Drawing.Point(91, 57)
        Me.teCoStreetAddress.MenuManager = Me.RibbonControl1
        Me.teCoStreetAddress.Name = "teCoStreetAddress"
        Me.teCoStreetAddress.Size = New System.Drawing.Size(346, 20)
        Me.teCoStreetAddress.StyleController = Me.LayoutControl2
        Me.teCoStreetAddress.TabIndex = 36
        '
        'teRoutingNumber
        '
        Me.teRoutingNumber.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "RoutingNumber", True))
        Me.teRoutingNumber.Location = New System.Drawing.Point(91, 387)
        Me.teRoutingNumber.MenuManager = Me.RibbonControl1
        Me.teRoutingNumber.Name = "teRoutingNumber"
        Me.teRoutingNumber.Size = New System.Drawing.Size(346, 20)
        Me.teRoutingNumber.StyleController = Me.LayoutControl2
        Me.teRoutingNumber.TabIndex = 42
        '
        'TextEdit9
        '
        Me.TextEdit9.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoCity", True))
        Me.TextEdit9.Location = New System.Drawing.Point(91, 81)
        Me.TextEdit9.MenuManager = Me.RibbonControl1
        Me.TextEdit9.Name = "TextEdit9"
        Me.TextEdit9.Size = New System.Drawing.Size(77, 20)
        Me.TextEdit9.StyleController = Me.LayoutControl2
        Me.TextEdit9.TabIndex = 39
        '
        'TextEdit13
        '
        Me.TextEdit13.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "BankName", True))
        Me.TextEdit13.Location = New System.Drawing.Point(91, 363)
        Me.TextEdit13.MenuManager = Me.RibbonControl1
        Me.TextEdit13.Name = "TextEdit13"
        Me.TextEdit13.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit13.StyleController = Me.LayoutControl2
        Me.TextEdit13.TabIndex = 41
        '
        'NumericUpDown1
        '
        Me.NumericUpDown1.DataBindings.Add(New System.Windows.Forms.Binding("Value", Me.BindingSource1, "NumberOfEmployees", True))
        Me.NumericUpDown1.Location = New System.Drawing.Point(186, 598)
        Me.NumericUpDown1.Name = "NumericUpDown1"
        Me.NumericUpDown1.Size = New System.Drawing.Size(251, 21)
        Me.NumericUpDown1.TabIndex = 8
        Me.NumericUpDown1.Maximum = 100000
        '
        'TextEdit12
        '
        Me.TextEdit12.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "SignersTitle", True))
        Me.TextEdit12.Location = New System.Drawing.Point(91, 129)
        Me.TextEdit12.MenuManager = Me.RibbonControl1
        Me.TextEdit12.Name = "TextEdit12"
        Me.TextEdit12.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit12.StyleController = Me.LayoutControl2
        Me.TextEdit12.TabIndex = 40
        '
        'teCoZip
        '
        Me.teCoZip.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoZip", True))
        Me.teCoZip.Location = New System.Drawing.Point(332, 81)
        Me.teCoZip.MenuManager = Me.RibbonControl1
        Me.teCoZip.Name = "teCoZip"
        Me.teCoZip.Size = New System.Drawing.Size(105, 20)
        Me.teCoZip.StyleController = Me.LayoutControl2
        Me.teCoZip.TabIndex = 37
        '
        'teFedId
        '
        Me.teFedId.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "FedId", True))
        Me.teFedId.Location = New System.Drawing.Point(299, 33)
        Me.teFedId.Name = "teFedId"
        Me.teFedId.Properties.Mask.BeepOnError = True
        Me.teFedId.Properties.Mask.EditMask = "00-0000000"
        Me.teFedId.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Simple
        Me.teFedId.Size = New System.Drawing.Size(138, 20)
        Me.teFedId.StyleController = Me.LayoutControl2
        Me.teFedId.TabIndex = 33
        '
        'CheckEdit1
        '
        Me.CheckEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "IsNewAccount", True))
        Me.CheckEdit1.Location = New System.Drawing.Point(227, 105)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Caption = "Is New Account"
        Me.CheckEdit1.Size = New System.Drawing.Size(210, 19)
        Me.CheckEdit1.StyleController = Me.LayoutControl2
        Me.CheckEdit1.TabIndex = 39
        '
        'TextEdit3
        '
        Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ContactName", True))
        Me.TextEdit3.Location = New System.Drawing.Point(91, 198)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit3.StyleController = Me.LayoutControl2
        Me.TextEdit3.TabIndex = 14
        '
        'TextEdit4
        '
        Me.TextEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoPhoneNubmerExt", True))
        Me.TextEdit4.Location = New System.Drawing.Point(382, 222)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Size = New System.Drawing.Size(55, 20)
        Me.TextEdit4.StyleController = Me.LayoutControl2
        Me.TextEdit4.TabIndex = 16
        '
        'cbeCoState
        '
        Me.cbeCoState.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoState", True))
        Me.cbeCoState.Location = New System.Drawing.Point(206, 81)
        Me.cbeCoState.MenuManager = Me.RibbonControl1
        Me.cbeCoState.Name = "cbeCoState"
        Me.cbeCoState.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeCoState.Size = New System.Drawing.Size(96, 20)
        Me.cbeCoState.StyleController = Me.LayoutControl2
        Me.cbeCoState.TabIndex = 38
        '
        'TextEdit16
        '
        Me.TextEdit16.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "AccountType", True))
        Me.TextEdit16.Location = New System.Drawing.Point(91, 435)
        Me.TextEdit16.MenuManager = Me.RibbonControl1
        Me.TextEdit16.Name = "TextEdit16"
        Me.TextEdit16.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TextEdit16.Properties.Items.AddRange(New Object() {"C", "S"})
        Me.TextEdit16.Size = New System.Drawing.Size(346, 20)
        Me.TextEdit16.StyleController = Me.LayoutControl2
        Me.TextEdit16.TabIndex = 44
        '
        'CalcEdit4
        '
        Me.CalcEdit4.Location = New System.Drawing.Point(91, 852)
        Me.CalcEdit4.MenuManager = Me.RibbonControl1
        Me.CalcEdit4.Name = "CalcEdit4"
        Me.CalcEdit4.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CalcEdit4.Size = New System.Drawing.Size(346, 20)
        Me.CalcEdit4.StyleController = Me.LayoutControl2
        Me.CalcEdit4.TabIndex = 59
        '
        'CalcEdit3
        '
        Me.CalcEdit3.Location = New System.Drawing.Point(91, 876)
        Me.CalcEdit3.MenuManager = Me.RibbonControl1
        Me.CalcEdit3.Name = "CalcEdit3"
        Me.CalcEdit3.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CalcEdit3.Size = New System.Drawing.Size(346, 20)
        Me.CalcEdit3.StyleController = Me.LayoutControl2
        Me.CalcEdit3.TabIndex = 58
        '
        'CalcEdit2
        '
        Me.CalcEdit2.Location = New System.Drawing.Point(91, 900)
        Me.CalcEdit2.MenuManager = Me.RibbonControl1
        Me.CalcEdit2.Name = "CalcEdit2"
        Me.CalcEdit2.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CalcEdit2.Size = New System.Drawing.Size(346, 20)
        Me.CalcEdit2.StyleController = Me.LayoutControl2
        Me.CalcEdit2.TabIndex = 57
        '
        'CalcEdit1
        '
        Me.CalcEdit1.Location = New System.Drawing.Point(91, 924)
        Me.CalcEdit1.MenuManager = Me.RibbonControl1
        Me.CalcEdit1.Name = "CalcEdit1"
        Me.CalcEdit1.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CalcEdit1.Size = New System.Drawing.Size(346, 20)
        Me.CalcEdit1.StyleController = Me.LayoutControl2
        Me.CalcEdit1.TabIndex = 56
        '
        'cbeShipState
        '
        Me.cbeShipState.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ShippingState", True))
        Me.cbeShipState.Location = New System.Drawing.Point(245, 761)
        Me.cbeShipState.MenuManager = Me.RibbonControl1
        Me.cbeShipState.Name = "cbeShipState"
        Me.cbeShipState.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeShipState.Size = New System.Drawing.Size(68, 20)
        Me.cbeShipState.StyleController = Me.LayoutControl2
        Me.cbeShipState.TabIndex = 54
        '
        'teListEmployeeWorkStates
        '
        Me.teListEmployeeWorkStates.Location = New System.Drawing.Point(186, 622)
        Me.teListEmployeeWorkStates.MenuManager = Me.RibbonControl1
        Me.teListEmployeeWorkStates.Name = "teListEmployeeWorkStates"
        Me.teListEmployeeWorkStates.Properties.DropDownShowMode = DevExpress.XtraEditors.TokenEditDropDownShowMode.Outlook
        Me.teListEmployeeWorkStates.Properties.Separators.AddRange(New String() {","})
        Me.teListEmployeeWorkStates.Size = New System.Drawing.Size(251, 20)
        Me.teListEmployeeWorkStates.StyleController = Me.LayoutControl2
        Me.teListEmployeeWorkStates.TabIndex = 45
        '
        'bePhone
        '
        Me.bePhone.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoPhoneNumber", True))
        Me.bePhone.Location = New System.Drawing.Point(91, 222)
        Me.bePhone.Name = "bePhone"
        Me.bePhone.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.bePhone.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.bePhone.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.bePhone.Size = New System.Drawing.Size(210, 20)
        Me.bePhone.StyleController = Me.LayoutControl2
        Me.bePhone.TabIndex = 15
        '
        'beCell
        '
        Me.beCell.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoCellNumber", True))
        Me.beCell.Location = New System.Drawing.Point(91, 246)
        Me.beCell.Name = "beCell"
        Me.beCell.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beCell.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.beCell.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.beCell.Size = New System.Drawing.Size(346, 20)
        Me.beCell.StyleController = Me.LayoutControl2
        Me.beCell.TabIndex = 18
        '
        'beEmail
        '
        Me.beEmail.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoEmail", True))
        Me.beEmail.Location = New System.Drawing.Point(91, 270)
        Me.beEmail.Name = "beEmail"
        Me.beEmail.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beEmail.Size = New System.Drawing.Size(346, 20)
        Me.beEmail.StyleController = Me.LayoutControl2
        Me.beEmail.TabIndex = 17
        '
        'beFax
        '
        Me.beFax.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoFax", True))
        Me.beFax.Location = New System.Drawing.Point(91, 294)
        Me.beFax.Name = "beFax"
        Me.beFax.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.beFax.Properties.Mask.EditMask = "(\d?\d?\d?) \d\d\d-\d\d\d\d"
        Me.beFax.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.beFax.Size = New System.Drawing.Size(346, 20)
        Me.beFax.StyleController = Me.LayoutControl2
        Me.beFax.TabIndex = 19
        '
        'teConnectecCompanies
        '
        Me.teConnectecCompanies.Location = New System.Drawing.Point(180, 991)
        Me.teConnectecCompanies.MenuManager = Me.RibbonControl1
        Me.teConnectecCompanies.Name = "teConnectecCompanies"
        Me.teConnectecCompanies.Properties.Separators.AddRange(New String() {","})
        Me.teConnectecCompanies.Size = New System.Drawing.Size(231, 20)
        Me.teConnectecCompanies.StyleController = Me.LayoutControl2
        Me.teConnectecCompanies.TabIndex = 65
        '
        'LayoutControlGroup7
        '
        Me.LayoutControlGroup7.AppearanceItemCaption.Font = New System.Drawing.Font("Arial Narrow", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LayoutControlGroup7.AppearanceItemCaption.Options.UseFont = True
        Me.LayoutControlGroup7.CustomizationFormText = "Root"
        Me.LayoutControlGroup7.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup7.GroupBordersVisible = False
        Me.LayoutControlGroup7.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.SimpleSeparator1, Me.SimpleSeparator2, Me.item0, Me.item1, Me.LayoutControlGroup2, Me.LayoutControlGroup5, Me.LayoutControlGroup6, Me.LayoutControlGroup8, Me.LayoutControlGroup9})
        Me.LayoutControlGroup7.Name = "Root"
        Me.LayoutControlGroup7.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup7.Size = New System.Drawing.Size(451, 1171)
        Me.LayoutControlGroup7.TextVisible = False
        '
        'SimpleSeparator1
        '
        Me.SimpleSeparator1.AllowHotTrack = False
        Me.SimpleSeparator1.CustomizationFormText = "SimpleSeparator1"
        Me.SimpleSeparator1.Location = New System.Drawing.Point(0, 163)
        Me.SimpleSeparator1.Name = "SimpleSeparator1"
        Me.SimpleSeparator1.Size = New System.Drawing.Size(451, 2)
        '
        'SimpleSeparator2
        '
        Me.SimpleSeparator2.AllowHotTrack = False
        Me.SimpleSeparator2.CustomizationFormText = "SimpleSeparator2"
        Me.SimpleSeparator2.Location = New System.Drawing.Point(0, 328)
        Me.SimpleSeparator2.Name = "SimpleSeparator2"
        Me.SimpleSeparator2.Size = New System.Drawing.Size(451, 2)
        '
        'item0
        '
        Me.item0.CustomizationFormText = "Company Legal Info"
        Me.item0.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.item0.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.LayoutControlItem30, Me.LayoutControlItem21, Me.LayoutControlItem22, Me.LayoutControlItem23, Me.LayoutControlItem33, Me.LayoutControlItem6, Me.LayoutControlItem34, Me.LayoutControlItem58})
        Me.item0.Location = New System.Drawing.Point(0, 0)
        Me.item0.Name = "item0"
        Me.item0.Size = New System.Drawing.Size(451, 163)
        Me.item0.Text = "Company Legal Info"
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.TextEdit1
        Me.LayoutControlItem4.CustomizationFormText = "Entity Name: "
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(208, 24)
        Me.LayoutControlItem4.Text = "Entity Name: "
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem30
        '
        Me.LayoutControlItem30.Control = Me.teFedId
        Me.LayoutControlItem30.CustomizationFormText = "EIN: "
        Me.LayoutControlItem30.Location = New System.Drawing.Point(208, 0)
        Me.LayoutControlItem30.Name = "LayoutControlItem30"
        Me.LayoutControlItem30.Size = New System.Drawing.Size(219, 24)
        Me.LayoutControlItem30.Text = "EIN: "
        Me.LayoutControlItem30.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.teCoStreetAddress
        Me.LayoutControlItem21.CustomizationFormText = "Street Address: "
        Me.LayoutControlItem21.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem21.Text = "Street Address: "
        Me.LayoutControlItem21.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem22
        '
        Me.LayoutControlItem22.Control = Me.TextEdit9
        Me.LayoutControlItem22.CustomizationFormText = "City: "
        Me.LayoutControlItem22.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem22.Name = "LayoutControlItem22"
        Me.LayoutControlItem22.Size = New System.Drawing.Size(158, 24)
        Me.LayoutControlItem22.Text = "City: "
        Me.LayoutControlItem22.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem23
        '
        Me.LayoutControlItem23.Control = Me.cbeCoState
        Me.LayoutControlItem23.CustomizationFormText = "State: "
        Me.LayoutControlItem23.Location = New System.Drawing.Point(158, 48)
        Me.LayoutControlItem23.Name = "LayoutControlItem23"
        Me.LayoutControlItem23.Size = New System.Drawing.Size(134, 24)
        Me.LayoutControlItem23.Text = "State: "
        Me.LayoutControlItem23.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem23.TextSize = New System.Drawing.Size(29, 16)
        Me.LayoutControlItem23.TextToControlDistance = 5
        '
        'LayoutControlItem33
        '
        Me.LayoutControlItem33.Control = Me.teCoZip
        Me.LayoutControlItem33.CustomizationFormText = "Zip: "
        Me.LayoutControlItem33.Location = New System.Drawing.Point(292, 48)
        Me.LayoutControlItem33.Name = "LayoutControlItem33"
        Me.LayoutControlItem33.Size = New System.Drawing.Size(135, 24)
        Me.LayoutControlItem33.Text = "Zip: "
        Me.LayoutControlItem33.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem33.TextSize = New System.Drawing.Size(21, 16)
        Me.LayoutControlItem33.TextToControlDistance = 5
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.CheckEdit1
        Me.LayoutControlItem6.CustomizationFormText = "LayoutControlItem6"
        Me.LayoutControlItem6.Location = New System.Drawing.Point(213, 72)
        Me.LayoutControlItem6.MinSize = New System.Drawing.Size(107, 24)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(214, 24)
        Me.LayoutControlItem6.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem6.Text = "Is New Account"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem34
        '
        Me.LayoutControlItem34.Control = Me.TextEdit12
        Me.LayoutControlItem34.CustomizationFormText = "Signers Title: "
        Me.LayoutControlItem34.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem34.Name = "LayoutControlItem34"
        Me.LayoutControlItem34.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem34.Text = "Signers Title: "
        Me.LayoutControlItem34.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem58
        '
        Me.LayoutControlItem58.Control = Me.TextEdit11
        Me.LayoutControlItem58.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem58.Name = "LayoutControlItem58"
        Me.LayoutControlItem58.Size = New System.Drawing.Size(213, 24)
        Me.LayoutControlItem58.Text = "County: "
        Me.LayoutControlItem58.TextSize = New System.Drawing.Size(74, 16)
        '
        'item1
        '
        Me.item1.CustomizationFormText = "Contact Info"
        Me.item1.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.item1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem11, Me.LayoutControlItem12, Me.LayoutControlItem13, Me.LayoutControlItem15, Me.LayoutControlItem14, Me.LayoutControlItem16})
        Me.item1.Location = New System.Drawing.Point(0, 165)
        Me.item1.Name = "item1"
        Me.item1.Size = New System.Drawing.Size(451, 163)
        Me.item1.Text = "Contact Info"
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.TextEdit3
        Me.LayoutControlItem11.CustomizationFormText = "Name: "
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem11.Text = "Name: "
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.bePhone
        Me.LayoutControlItem12.CustomizationFormText = "Phone #: "
        Me.LayoutControlItem12.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(291, 24)
        Me.LayoutControlItem12.Text = "Phone #: "
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.TextEdit4
        Me.LayoutControlItem13.CustomizationFormText = "Ext: "
        Me.LayoutControlItem13.Location = New System.Drawing.Point(291, 24)
        Me.LayoutControlItem13.MinSize = New System.Drawing.Size(136, 24)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(136, 24)
        Me.LayoutControlItem13.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem13.Text = "Ext: "
        Me.LayoutControlItem13.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.beCell
        Me.LayoutControlItem15.CustomizationFormText = "Cell #: "
        Me.LayoutControlItem15.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem15.Text = "Cell #: "
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.beEmail
        Me.LayoutControlItem14.CustomizationFormText = "LayoutControlItem14"
        Me.LayoutControlItem14.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem14.Text = "Email Address: "
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.beFax
        Me.LayoutControlItem16.CustomizationFormText = "LayoutControlItem16"
        Me.LayoutControlItem16.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem16.Text = "Fax #: "
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.CustomizationFormText = "Bank Info"
        Me.LayoutControlGroup2.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem17, Me.LayoutControlItem18, Me.LayoutControlItem19, Me.LayoutControlItem35})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 330)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(451, 139)
        Me.LayoutControlGroup2.Text = "Bank Info"
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.TextEdit13
        Me.LayoutControlItem17.CustomizationFormText = "LayoutControlItem17"
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem17.Text = "Bank Name: "
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.teRoutingNumber
        Me.LayoutControlItem18.CustomizationFormText = "LayoutControlItem18"
        Me.LayoutControlItem18.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem18.Name = "LayoutControlItem18"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem18.Text = "Routing #: "
        Me.LayoutControlItem18.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.TextEdit15
        Me.LayoutControlItem19.CustomizationFormText = "LayoutControlItem19"
        Me.LayoutControlItem19.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem19.Text = "Account #: "
        Me.LayoutControlItem19.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem35
        '
        Me.LayoutControlItem35.Control = Me.TextEdit16
        Me.LayoutControlItem35.CustomizationFormText = "Account Type: "
        Me.LayoutControlItem35.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem35.Name = "LayoutControlItem35"
        Me.LayoutControlItem35.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem35.Text = "Account Type: "
        Me.LayoutControlItem35.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlGroup5
        '
        Me.LayoutControlGroup5.CustomizationFormText = "Payroll Info"
        Me.LayoutControlGroup5.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup5.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem31, Me.LayoutControlItem37, Me.LayoutControlItem38, Me.LayoutControlItem39, Me.LayoutControlItem5, Me.LayoutControlItem36, Me.LayoutControlItem40})
        Me.LayoutControlGroup5.Location = New System.Drawing.Point(0, 469)
        Me.LayoutControlGroup5.Name = "LayoutControlGroup5"
        Me.LayoutControlGroup5.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.LayoutControlGroup5.Size = New System.Drawing.Size(451, 211)
        Me.LayoutControlGroup5.Text = "Payroll Info"
        '
        'LayoutControlItem31
        '
        Me.LayoutControlItem31.Control = Me.cbePayFreuency
        Me.LayoutControlItem31.CustomizationFormText = "Pay Frequency: "
        Me.LayoutControlItem31.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem31.Name = "LayoutControlItem31"
        Me.LayoutControlItem31.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem31.Text = "Pay Frequency: "
        Me.LayoutControlItem31.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlItem37
        '
        Me.LayoutControlItem37.Control = Me.TextEdit18
        Me.LayoutControlItem37.CustomizationFormText = "Check Date Day Of Week \ Period: "
        Me.LayoutControlItem37.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem37.Name = "LayoutControlItem37"
        Me.LayoutControlItem37.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem37.Text = "Check Date Day Of Week \ Period: "
        Me.LayoutControlItem37.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlItem38
        '
        Me.LayoutControlItem38.Control = Me.TextEdit19
        Me.LayoutControlItem38.CustomizationFormText = "Process Day Of Week \ Period: "
        Me.LayoutControlItem38.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem38.Name = "LayoutControlItem38"
        Me.LayoutControlItem38.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem38.Text = "Process Day Of Week \ Period: "
        Me.LayoutControlItem38.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlItem39
        '
        Me.LayoutControlItem39.Control = Me.TextEdit20
        Me.LayoutControlItem39.CustomizationFormText = "End Of Week Day \ Period: "
        Me.LayoutControlItem39.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem39.Name = "LayoutControlItem39"
        Me.LayoutControlItem39.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem39.Text = "End Of Week Day \ Period: "
        Me.LayoutControlItem39.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.NumericUpDown1
        Me.LayoutControlItem5.CustomizationFormText = "Num Of Emps: "
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem5.Text = "Num Of Emps: "
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlItem36
        '
        Me.LayoutControlItem36.Control = Me.teListEmployeeWorkStates
        Me.LayoutControlItem36.CustomizationFormText = "List States in which employees work"
        Me.LayoutControlItem36.Location = New System.Drawing.Point(0, 120)
        Me.LayoutControlItem36.Name = "LayoutControlItem36"
        Me.LayoutControlItem36.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem36.Text = "List States in which employees work"
        Me.LayoutControlItem36.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlItem40
        '
        Me.LayoutControlItem40.Control = Me.TextEdit21
        Me.LayoutControlItem40.CustomizationFormText = "UCI ID Number: "
        Me.LayoutControlItem40.Location = New System.Drawing.Point(0, 144)
        Me.LayoutControlItem40.Name = "LayoutControlItem40"
        Me.LayoutControlItem40.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem40.Text = "UCI ID Number: "
        Me.LayoutControlItem40.TextSize = New System.Drawing.Size(169, 16)
        '
        'LayoutControlGroup6
        '
        Me.LayoutControlGroup6.CustomizationFormText = "Shipping Info"
        Me.LayoutControlGroup6.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup6.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem41, Me.LayoutControlItem42, Me.LayoutControlItem43, Me.LayoutControlItem44, Me.LayoutControlItem45, Me.LayoutControlItem46})
        Me.LayoutControlGroup6.Location = New System.Drawing.Point(0, 680)
        Me.LayoutControlGroup6.Name = "LayoutControlGroup6"
        Me.LayoutControlGroup6.Size = New System.Drawing.Size(451, 139)
        Me.LayoutControlGroup6.Text = "Shipping Info"
        '
        'LayoutControlItem41
        '
        Me.LayoutControlItem41.Control = Me.TextEdit22
        Me.LayoutControlItem41.CustomizationFormText = "Name: "
        Me.LayoutControlItem41.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem41.Name = "LayoutControlItem41"
        Me.LayoutControlItem41.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem41.Text = "Name: "
        Me.LayoutControlItem41.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem42
        '
        Me.LayoutControlItem42.Control = Me.teShipStreetAddress
        Me.LayoutControlItem42.CustomizationFormText = "Street Address: "
        Me.LayoutControlItem42.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem42.Name = "LayoutControlItem42"
        Me.LayoutControlItem42.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem42.Text = "Street Address: "
        Me.LayoutControlItem42.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem43
        '
        Me.LayoutControlItem43.Control = Me.teShipZip
        Me.LayoutControlItem43.CustomizationFormText = "Zip: "
        Me.LayoutControlItem43.Location = New System.Drawing.Point(303, 48)
        Me.LayoutControlItem43.Name = "LayoutControlItem43"
        Me.LayoutControlItem43.Size = New System.Drawing.Size(124, 24)
        Me.LayoutControlItem43.Text = "Zip: "
        Me.LayoutControlItem43.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem43.TextSize = New System.Drawing.Size(21, 16)
        Me.LayoutControlItem43.TextToControlDistance = 5
        '
        'LayoutControlItem44
        '
        Me.LayoutControlItem44.Control = Me.TextEdit25
        Me.LayoutControlItem44.CustomizationFormText = "City: "
        Me.LayoutControlItem44.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem44.Name = "LayoutControlItem44"
        Me.LayoutControlItem44.Size = New System.Drawing.Size(197, 24)
        Me.LayoutControlItem44.Text = "City: "
        Me.LayoutControlItem44.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem45
        '
        Me.LayoutControlItem45.Control = Me.cbeShipState
        Me.LayoutControlItem45.CustomizationFormText = "State: "
        Me.LayoutControlItem45.Location = New System.Drawing.Point(197, 48)
        Me.LayoutControlItem45.Name = "LayoutControlItem45"
        Me.LayoutControlItem45.Size = New System.Drawing.Size(106, 24)
        Me.LayoutControlItem45.Text = "State: "
        Me.LayoutControlItem45.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem45.TextSize = New System.Drawing.Size(29, 16)
        Me.LayoutControlItem45.TextToControlDistance = 5
        '
        'LayoutControlItem46
        '
        Me.LayoutControlItem46.Control = Me.TextEdit27
        Me.LayoutControlItem46.CustomizationFormText = "Contact Name: "
        Me.LayoutControlItem46.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem46.Name = "LayoutControlItem46"
        Me.LayoutControlItem46.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem46.Text = "Contact Name: "
        Me.LayoutControlItem46.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlGroup8
        '
        Me.LayoutControlGroup8.CustomizationFormText = "Pricing"
        Me.LayoutControlGroup8.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup8.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem50, Me.LayoutControlItem49, Me.LayoutControlItem48, Me.LayoutControlItem20})
        Me.LayoutControlGroup8.Location = New System.Drawing.Point(0, 819)
        Me.LayoutControlGroup8.Name = "LayoutControlGroup8"
        Me.LayoutControlGroup8.Size = New System.Drawing.Size(451, 139)
        Me.LayoutControlGroup8.Text = "Pricing"
        '
        'LayoutControlItem50
        '
        Me.LayoutControlItem50.Control = Me.CalcEdit4
        Me.LayoutControlItem50.CustomizationFormText = "Setup Fee: "
        Me.LayoutControlItem50.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem50.Name = "LayoutControlItem50"
        Me.LayoutControlItem50.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem50.Text = "Setup Fee: "
        Me.LayoutControlItem50.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem49
        '
        Me.LayoutControlItem49.Control = Me.CalcEdit3
        Me.LayoutControlItem49.CustomizationFormText = "Payroll Fee: "
        Me.LayoutControlItem49.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem49.Name = "LayoutControlItem49"
        Me.LayoutControlItem49.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem49.Text = "Payroll Fee: "
        Me.LayoutControlItem49.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem48
        '
        Me.LayoutControlItem48.Control = Me.CalcEdit2
        Me.LayoutControlItem48.CustomizationFormText = "Tax Fee: "
        Me.LayoutControlItem48.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem48.Name = "LayoutControlItem48"
        Me.LayoutControlItem48.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem48.Text = "Tax Fee: "
        Me.LayoutControlItem48.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.CalcEdit1
        Me.LayoutControlItem20.CustomizationFormText = "Check Fee: "
        Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem20.Text = "Check Fee: "
        Me.LayoutControlItem20.TextSize = New System.Drawing.Size(74, 16)
        '
        'LayoutControlGroup9
        '
        Me.LayoutControlGroup9.CustomizationFormText = "Misc Info"
        Me.LayoutControlGroup9.GroupStyle = DevExpress.Utils.GroupStyle.Light
        Me.LayoutControlGroup9.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem56, Me.LayoutControlItem54, Me.LayoutControlItem57, Me.LayoutControlItem53, Me.LayoutControlItem52, Me.LayoutControlItem51, Me.LayoutControlItem59, Me.LayoutControlItem60})
        Me.LayoutControlGroup9.Location = New System.Drawing.Point(0, 958)
        Me.LayoutControlGroup9.Name = "LayoutControlGroup9"
        Me.LayoutControlGroup9.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.LayoutControlGroup9.Size = New System.Drawing.Size(451, 213)
        Me.LayoutControlGroup9.Text = "Misc Info"
        '
        'LayoutControlItem56
        '
        Me.LayoutControlItem56.Control = Me.teConnectecCompanies
        Me.LayoutControlItem56.CustomizationFormText = "Connected Companies \ Conums: "
        Me.LayoutControlItem56.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem56.Name = "LayoutControlItem56"
        Me.LayoutControlItem56.Size = New System.Drawing.Size(401, 26)
        Me.LayoutControlItem56.Text = "Connected Companies \ Conums: "
        Me.LayoutControlItem56.TextSize = New System.Drawing.Size(163, 16)
        '
        'LayoutControlItem54
        '
        Me.LayoutControlItem54.Control = Me.TextEdit31
        Me.LayoutControlItem54.CustomizationFormText = "Number Of Locations: "
        Me.LayoutControlItem54.Location = New System.Drawing.Point(0, 50)
        Me.LayoutControlItem54.Name = "LayoutControlItem54"
        Me.LayoutControlItem54.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem54.Text = "Number Of Locations: "
        Me.LayoutControlItem54.TextSize = New System.Drawing.Size(163, 16)
        '
        'LayoutControlItem57
        '
        Me.LayoutControlItem57.Control = Me.DateEdit6
        Me.LayoutControlItem57.CustomizationFormText = "Date Etablished: "
        Me.LayoutControlItem57.Location = New System.Drawing.Point(0, 74)
        Me.LayoutControlItem57.Name = "LayoutControlItem57"
        Me.LayoutControlItem57.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem57.Text = "Date Etablished: "
        Me.LayoutControlItem57.TextSize = New System.Drawing.Size(163, 16)
        '
        'LayoutControlItem53
        '
        Me.LayoutControlItem53.Control = Me.TextEdit30
        Me.LayoutControlItem53.CustomizationFormText = "Type Of Entity: "
        Me.LayoutControlItem53.Location = New System.Drawing.Point(0, 98)
        Me.LayoutControlItem53.Name = "LayoutControlItem53"
        Me.LayoutControlItem53.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem53.Text = "Type Of Entity: "
        Me.LayoutControlItem53.TextSize = New System.Drawing.Size(163, 16)
        '
        'LayoutControlItem52
        '
        Me.LayoutControlItem52.Control = Me.TextEdit29
        Me.LayoutControlItem52.CustomizationFormText = "Industry: "
        Me.LayoutControlItem52.Location = New System.Drawing.Point(0, 122)
        Me.LayoutControlItem52.Name = "LayoutControlItem52"
        Me.LayoutControlItem52.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem52.Text = "Industry: "
        Me.LayoutControlItem52.TextSize = New System.Drawing.Size(163, 16)
        '
        'LayoutControlItem51
        '
        Me.LayoutControlItem51.Control = Me.TextEdit28
        Me.LayoutControlItem51.CustomizationFormText = "Prompt Tax Filter \ Code: "
        Me.LayoutControlItem51.Location = New System.Drawing.Point(0, 146)
        Me.LayoutControlItem51.Name = "LayoutControlItem51"
        Me.LayoutControlItem51.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem51.Text = "Prompt Tax Filter \ Code: "
        Me.LayoutControlItem51.TextSize = New System.Drawing.Size(163, 16)
        '
        'LayoutControlItem59
        '
        Me.LayoutControlItem59.Control = Me.btnAddConnectedCompany
        Me.LayoutControlItem59.Location = New System.Drawing.Point(401, 0)
        Me.LayoutControlItem59.MaxSize = New System.Drawing.Size(26, 26)
        Me.LayoutControlItem59.MinSize = New System.Drawing.Size(26, 26)
        Me.LayoutControlItem59.Name = "LayoutControlItem59"
        Me.LayoutControlItem59.Size = New System.Drawing.Size(26, 26)
        Me.LayoutControlItem59.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem59.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem59.TextVisible = False
        '
        'LayoutControlItem60
        '
        Me.LayoutControlItem60.Control = Me.SearchLookUpEdit1
        Me.LayoutControlItem60.Location = New System.Drawing.Point(0, 26)
        Me.LayoutControlItem60.Name = "LayoutControlItem60"
        Me.LayoutControlItem60.Size = New System.Drawing.Size(427, 24)
        Me.LayoutControlItem60.Text = "Connected Conum To Copy From: "
        Me.LayoutControlItem60.TextSize = New System.Drawing.Size(163, 16)
        '
        'cbeAignedTo
        '
        Me.cbeAignedTo.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "AssignedTo", True))
        Me.cbeAignedTo.Location = New System.Drawing.Point(112, 67)
        Me.cbeAignedTo.Name = "cbeAignedTo"
        Me.cbeAignedTo.Properties.AllowMouseWheel = False
        Me.cbeAignedTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeAignedTo.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeAignedTo.Size = New System.Drawing.Size(132, 20)
        Me.cbeAignedTo.StyleController = Me.LayoutControl1
        Me.cbeAignedTo.TabIndex = 35
        '
        'DateEdit5
        '
        Me.DateEdit5.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ChgDate", True))
        Me.DateEdit5.EditValue = Nothing
        Me.DateEdit5.Location = New System.Drawing.Point(430, 501)
        Me.DateEdit5.Name = "DateEdit5"
        Me.DateEdit5.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit5.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit5.Properties.DisplayFormat.FormatString = "g"
        Me.DateEdit5.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.DateEdit5.Properties.ReadOnly = True
        Me.DateEdit5.Size = New System.Drawing.Size(50, 20)
        Me.DateEdit5.StyleController = Me.LayoutControl1
        Me.DateEdit5.TabIndex = 32
        '
        'DateEdit4
        '
        Me.DateEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "AddDate", True))
        Me.DateEdit4.EditValue = Nothing
        Me.DateEdit4.Location = New System.Drawing.Point(173, 501)
        Me.DateEdit4.Name = "DateEdit4"
        Me.DateEdit4.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit4.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit4.Properties.DisplayFormat.FormatString = "g"
        Me.DateEdit4.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.DateEdit4.Properties.ReadOnly = True
        Me.DateEdit4.Size = New System.Drawing.Size(50, 20)
        Me.DateEdit4.StyleController = Me.LayoutControl1
        Me.DateEdit4.TabIndex = 31
        '
        'TextEdit7
        '
        Me.TextEdit7.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ChgUser", True))
        Me.TextEdit7.Location = New System.Drawing.Point(301, 501)
        Me.TextEdit7.Name = "TextEdit7"
        Me.TextEdit7.Properties.ReadOnly = True
        Me.TextEdit7.Size = New System.Drawing.Size(50, 20)
        Me.TextEdit7.StyleController = Me.LayoutControl1
        Me.TextEdit7.TabIndex = 30
        '
        'TextEdit6
        '
        Me.TextEdit6.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "AddUser", True))
        Me.TextEdit6.Location = New System.Drawing.Point(65, 501)
        Me.TextEdit6.Name = "TextEdit6"
        Me.TextEdit6.Properties.ReadOnly = True
        Me.TextEdit6.Size = New System.Drawing.Size(50, 20)
        Me.TextEdit6.StyleController = Me.LayoutControl1
        Me.TextEdit6.TabIndex = 29
        '
        'slueCoNum
        '
        Me.slueCoNum.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "CoNum", True))
        Me.slueCoNum.EditValue = ""
        Me.slueCoNum.Location = New System.Drawing.Point(112, 139)
        Me.slueCoNum.Name = "slueCoNum"
        Me.slueCoNum.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueCoNum.Properties.DataSource = Me.bsCompany
        Me.slueCoNum.Properties.DisplayMember = "CoNumAndName"
        Me.slueCoNum.Properties.NullText = ""
        Me.slueCoNum.Properties.PopupFormSize = New System.Drawing.Size(800, 0)
        Me.slueCoNum.Properties.PopupView = Me.SearchLookUpEdit2View
        Me.slueCoNum.Properties.ValueMember = "CONUM"
        Me.slueCoNum.Size = New System.Drawing.Size(132, 20)
        Me.slueCoNum.StyleController = Me.LayoutControl1
        Me.slueCoNum.TabIndex = 28
        '
        'SearchLookUpEdit2View
        '
        Me.SearchLookUpEdit2View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNumAndName, Me.colPR_CONTACT, Me.colCO_PHONE, Me.colCO_FAX, Me.colCO_MODEM, Me.colFED_ID, Me.colCO_EMAIL})
        Me.SearchLookUpEdit2View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.SearchLookUpEdit2View.Name = "SearchLookUpEdit2View"
        Me.SearchLookUpEdit2View.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.SearchLookUpEdit2View.OptionsView.ShowGroupPanel = False
        '
        'colCoNumAndName
        '
        Me.colCoNumAndName.FieldName = "CoNumAndName"
        Me.colCoNumAndName.Name = "colCoNumAndName"
        Me.colCoNumAndName.Visible = True
        Me.colCoNumAndName.VisibleIndex = 0
        Me.colCoNumAndName.Width = 150
        '
        'colPR_CONTACT
        '
        Me.colPR_CONTACT.FieldName = "PR_CONTACT"
        Me.colPR_CONTACT.Name = "colPR_CONTACT"
        Me.colPR_CONTACT.Visible = True
        Me.colPR_CONTACT.VisibleIndex = 1
        Me.colPR_CONTACT.Width = 38
        '
        'colCO_PHONE
        '
        Me.colCO_PHONE.FieldName = "CO_PHONE"
        Me.colCO_PHONE.Name = "colCO_PHONE"
        Me.colCO_PHONE.Visible = True
        Me.colCO_PHONE.VisibleIndex = 2
        Me.colCO_PHONE.Width = 38
        '
        'colCO_FAX
        '
        Me.colCO_FAX.FieldName = "CO_FAX"
        Me.colCO_FAX.Name = "colCO_FAX"
        Me.colCO_FAX.Visible = True
        Me.colCO_FAX.VisibleIndex = 3
        Me.colCO_FAX.Width = 38
        '
        'colCO_MODEM
        '
        Me.colCO_MODEM.FieldName = "CO_MODEM"
        Me.colCO_MODEM.Name = "colCO_MODEM"
        Me.colCO_MODEM.Visible = True
        Me.colCO_MODEM.VisibleIndex = 4
        Me.colCO_MODEM.Width = 38
        '
        'colFED_ID
        '
        Me.colFED_ID.FieldName = "FED_ID"
        Me.colFED_ID.Name = "colFED_ID"
        Me.colFED_ID.Visible = True
        Me.colFED_ID.VisibleIndex = 5
        Me.colFED_ID.Width = 38
        '
        'colCO_EMAIL
        '
        Me.colCO_EMAIL.FieldName = "CO_EMAIL"
        Me.colCO_EMAIL.Name = "colCO_EMAIL"
        Me.colCO_EMAIL.Visible = True
        Me.colCO_EMAIL.VisibleIndex = 6
        Me.colCO_EMAIL.Width = 44
        '
        'cbeCategory
        '
        Me.cbeCategory.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "Category", True))
        Me.cbeCategory.Location = New System.Drawing.Point(336, 43)
        Me.cbeCategory.Name = "cbeCategory"
        Me.cbeCategory.Properties.AllowMouseWheel = False
        Me.cbeCategory.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeCategory.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeCategory.Size = New System.Drawing.Size(132, 20)
        Me.cbeCategory.StyleController = Me.LayoutControl1
        Me.cbeCategory.TabIndex = 27
        '
        'slueReferedByCoNum
        '
        Me.slueReferedByCoNum.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ReferedByCoNum", True))
        Me.slueReferedByCoNum.Location = New System.Drawing.Point(336, 115)
        Me.slueReferedByCoNum.Name = "slueReferedByCoNum"
        Me.slueReferedByCoNum.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueReferedByCoNum.Properties.DataSource = Me.bsCompany
        Me.slueReferedByCoNum.Properties.DisplayMember = "CoNumAndName"
        Me.slueReferedByCoNum.Properties.NullText = ""
        Me.slueReferedByCoNum.Properties.PopupFormSize = New System.Drawing.Size(800, 0)
        Me.slueReferedByCoNum.Properties.PopupView = Me.SearchLookUpEdit1View
        Me.slueReferedByCoNum.Properties.ValueMember = "CONUM"
        Me.slueReferedByCoNum.Size = New System.Drawing.Size(132, 20)
        Me.slueReferedByCoNum.StyleController = Me.LayoutControl1
        Me.slueReferedByCoNum.TabIndex = 13
        '
        'SearchLookUpEdit1View
        '
        Me.SearchLookUpEdit1View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNumAndName1, Me.colPR_CONTACT1, Me.colCO_PHONE1, Me.colCO_MODEM1, Me.colCO_EMAIL1, Me.colCO_STATUS})
        Me.SearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.SearchLookUpEdit1View.Name = "SearchLookUpEdit1View"
        Me.SearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.SearchLookUpEdit1View.OptionsView.ShowGroupPanel = False
        '
        'colCoNumAndName1
        '
        Me.colCoNumAndName1.FieldName = "CoNumAndName"
        Me.colCoNumAndName1.Name = "colCoNumAndName1"
        Me.colCoNumAndName1.Visible = True
        Me.colCoNumAndName1.VisibleIndex = 0
        Me.colCoNumAndName1.Width = 150
        '
        'colPR_CONTACT1
        '
        Me.colPR_CONTACT1.FieldName = "PR_CONTACT"
        Me.colPR_CONTACT1.Name = "colPR_CONTACT1"
        Me.colPR_CONTACT1.Visible = True
        Me.colPR_CONTACT1.VisibleIndex = 1
        Me.colPR_CONTACT1.Width = 46
        '
        'colCO_PHONE1
        '
        Me.colCO_PHONE1.FieldName = "CO_PHONE"
        Me.colCO_PHONE1.Name = "colCO_PHONE1"
        Me.colCO_PHONE1.Visible = True
        Me.colCO_PHONE1.VisibleIndex = 2
        Me.colCO_PHONE1.Width = 46
        '
        'colCO_MODEM1
        '
        Me.colCO_MODEM1.FieldName = "CO_MODEM"
        Me.colCO_MODEM1.Name = "colCO_MODEM1"
        Me.colCO_MODEM1.Visible = True
        Me.colCO_MODEM1.VisibleIndex = 3
        Me.colCO_MODEM1.Width = 46
        '
        'colCO_EMAIL1
        '
        Me.colCO_EMAIL1.FieldName = "CO_EMAIL"
        Me.colCO_EMAIL1.Name = "colCO_EMAIL1"
        Me.colCO_EMAIL1.Visible = True
        Me.colCO_EMAIL1.VisibleIndex = 4
        Me.colCO_EMAIL1.Width = 46
        '
        'colCO_STATUS
        '
        Me.colCO_STATUS.FieldName = "CO_STATUS"
        Me.colCO_STATUS.Name = "colCO_STATUS"
        Me.colCO_STATUS.Visible = True
        Me.colCO_STATUS.VisibleIndex = 5
        Me.colCO_STATUS.Width = 50
        '
        'TextEdit2
        '
        Me.TextEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "ReferedBy", True))
        Me.TextEdit2.Location = New System.Drawing.Point(112, 115)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Size = New System.Drawing.Size(132, 20)
        Me.TextEdit2.StyleController = Me.LayoutControl1
        Me.TextEdit2.TabIndex = 12
        '
        'DateEdit3
        '
        Me.DateEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "StartingDate", True))
        Me.DateEdit3.EditValue = Nothing
        Me.DateEdit3.Location = New System.Drawing.Point(336, 67)
        Me.DateEdit3.Name = "DateEdit3"
        Me.DateEdit3.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit3.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit3.Size = New System.Drawing.Size(132, 20)
        Me.DateEdit3.StyleController = Me.LayoutControl1
        Me.DateEdit3.TabIndex = 11
        '
        'DateEdit2
        '
        Me.DateEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "LastFollowedUpDate", True))
        Me.DateEdit2.EditValue = Nothing
        Me.DateEdit2.Location = New System.Drawing.Point(336, 91)
        Me.DateEdit2.Name = "DateEdit2"
        Me.DateEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit2.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit2.Size = New System.Drawing.Size(132, 20)
        Me.DateEdit2.StyleController = Me.LayoutControl1
        Me.DateEdit2.TabIndex = 10
        '
        'meNotes
        '
        Me.meNotes.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "Notes", True))
        Me.meNotes.Location = New System.Drawing.Point(484, 28)
        Me.meNotes.Name = "meNotes"
        Me.meNotes.Properties.Appearance.Font = New System.Drawing.Font("Arial Narrow", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.meNotes.Properties.Appearance.Options.UseFont = True
        Me.meNotes.Properties.ReadOnly = True
        Me.meNotes.Size = New System.Drawing.Size(583, 493)
        Me.meNotes.StyleController = Me.LayoutControl1
        Me.meNotes.TabIndex = 6
        '
        'DateEdit1
        '
        Me.DateEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "NextFollowUpDate", True))
        Me.DateEdit1.EditValue = Nothing
        Me.DateEdit1.Location = New System.Drawing.Point(112, 91)
        Me.DateEdit1.Name = "DateEdit1"
        Me.DateEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit1.Size = New System.Drawing.Size(132, 20)
        Me.DateEdit1.StyleController = Me.LayoutControl1
        Me.DateEdit1.TabIndex = 5
        '
        'cbeStatus
        '
        Me.cbeStatus.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "Status", True))
        Me.cbeStatus.Location = New System.Drawing.Point(112, 43)
        Me.cbeStatus.Name = "cbeStatus"
        Me.cbeStatus.Properties.AllowMouseWheel = False
        Me.cbeStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeStatus.Properties.UseCtrlScroll = True
        Me.cbeStatus.Size = New System.Drawing.Size(132, 20)
        Me.cbeStatus.StyleController = Me.LayoutControl1
        Me.cbeStatus.TabIndex = 4
        '
        'lcgHighImportance
        '
        Me.lcgHighImportance.AppearanceGroup.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Bold)
        Me.lcgHighImportance.AppearanceGroup.Options.UseFont = True
        Me.lcgHighImportance.AppearanceGroup.Options.UseTextOptions = True
        Me.lcgHighImportance.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.lcgHighImportance.CustomizationFormText = "High Importance"
        Me.lcgHighImportance.Location = New System.Drawing.Point(0, 169)
        Me.lcgHighImportance.Name = "lcgHighImportance"
        Me.lcgHighImportance.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.lcgHighImportance.Size = New System.Drawing.Size(1320, 70)
        Me.lcgHighImportance.Text = "High Importance"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.CustomizationFormText = "Root"
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup4, Me.LayoutControlItem3, Me.LayoutControlItem47, Me.LayoutControlItem26, Me.LayoutControlItem27, Me.LayoutControlItem28, Me.LayoutControlItem29, Me.SimpleSeparator3, Me.SimpleSeparator4})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1079, 533)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlGroup4
        '
        Me.LayoutControlGroup4.AppearanceGroup.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Bold)
        Me.LayoutControlGroup4.AppearanceGroup.Options.UseFont = True
        Me.LayoutControlGroup4.AppearanceGroup.Options.UseTextOptions = True
        Me.LayoutControlGroup4.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlGroup4.CustomizationFormText = "Lead Status"
        Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem32, Me.LayoutControlItem9, Me.LayoutControlItem2, Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem10, Me.LayoutControlItem25, Me.EmptySpaceItem1, Me.LayoutControlItem24})
        Me.LayoutControlGroup4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup4.Name = "LayoutControlGroup4"
        Me.LayoutControlGroup4.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.LayoutControlGroup4.Size = New System.Drawing.Size(472, 163)
        Me.LayoutControlGroup4.Text = "Lead Status"
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.cbeStatus
        Me.LayoutControlItem1.CustomizationFormText = "Status: "
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem1.Text = "Status: "
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem32
        '
        Me.LayoutControlItem32.Control = Me.cbeAignedTo
        Me.LayoutControlItem32.CustomizationFormText = "Asigned To: "
        Me.LayoutControlItem32.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem32.Name = "LayoutControlItem32"
        Me.LayoutControlItem32.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem32.Text = "Asigned To: "
        Me.LayoutControlItem32.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.TextEdit2
        Me.LayoutControlItem9.CustomizationFormText = "Refered By: "
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem9.Text = "Refered By: "
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.DateEdit1
        Me.LayoutControlItem2.CustomizationFormText = "Follow Up:  "
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem2.Text = "Follow Up:  "
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.DateEdit2
        Me.LayoutControlItem7.CustomizationFormText = "Last Follow Up: "
        Me.LayoutControlItem7.Location = New System.Drawing.Point(224, 48)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem7.Text = "Last Follow Up: "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.DateEdit3
        Me.LayoutControlItem8.CustomizationFormText = "Starting Date: "
        Me.LayoutControlItem8.Location = New System.Drawing.Point(224, 24)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem8.Text = "Starting Date: "
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.slueReferedByCoNum
        Me.LayoutControlItem10.CustomizationFormText = "Refered By Co#: "
        Me.LayoutControlItem10.Location = New System.Drawing.Point(224, 72)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem10.Text = "Refered By Co#: "
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem25
        '
        Me.LayoutControlItem25.Control = Me.slueCoNum
        Me.LayoutControlItem25.CustomizationFormText = "Assigned Co#: "
        Me.LayoutControlItem25.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem25.Name = "LayoutControlItem25"
        Me.LayoutControlItem25.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem25.Text = "Assigned Co#: "
        Me.LayoutControlItem25.TextSize = New System.Drawing.Size(85, 13)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.CustomizationFormText = "item0"
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(224, 96)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(224, 24)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem24
        '
        Me.LayoutControlItem24.Control = Me.cbeCategory
        Me.LayoutControlItem24.CustomizationFormText = "Category: "
        Me.LayoutControlItem24.Location = New System.Drawing.Point(224, 0)
        Me.LayoutControlItem24.Name = "LayoutControlItem24"
        Me.LayoutControlItem24.Size = New System.Drawing.Size(224, 24)
        Me.LayoutControlItem24.Text = "Category: "
        Me.LayoutControlItem24.TextSize = New System.Drawing.Size(85, 13)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.meNotes
        Me.LayoutControlItem3.CustomizationFormText = "Notes: "
        Me.LayoutControlItem3.Location = New System.Drawing.Point(472, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(587, 513)
        Me.LayoutControlItem3.Text = "Notes: "
        Me.LayoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(35, 13)
        '
        'LayoutControlItem47
        '
        Me.LayoutControlItem47.Control = Me.XtraScrollableControl2
        Me.LayoutControlItem47.CustomizationFormText = "LayoutControlItem47"
        Me.LayoutControlItem47.Location = New System.Drawing.Point(0, 165)
        Me.LayoutControlItem47.MinSize = New System.Drawing.Size(5, 5)
        Me.LayoutControlItem47.Name = "LayoutControlItem47"
        Me.LayoutControlItem47.Size = New System.Drawing.Size(472, 322)
        Me.LayoutControlItem47.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem47.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem47.TextVisible = False
        '
        'LayoutControlItem26
        '
        Me.LayoutControlItem26.Control = Me.TextEdit6
        Me.LayoutControlItem26.CustomizationFormText = "Add User:"
        Me.LayoutControlItem26.Location = New System.Drawing.Point(0, 489)
        Me.LayoutControlItem26.Name = "LayoutControlItem26"
        Me.LayoutControlItem26.Size = New System.Drawing.Size(107, 24)
        Me.LayoutControlItem26.Text = "Add User:"
        Me.LayoutControlItem26.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem26.TextSize = New System.Drawing.Size(48, 13)
        Me.LayoutControlItem26.TextToControlDistance = 5
        '
        'LayoutControlItem27
        '
        Me.LayoutControlItem27.Control = Me.TextEdit7
        Me.LayoutControlItem27.CustomizationFormText = "Change User: "
        Me.LayoutControlItem27.Location = New System.Drawing.Point(215, 489)
        Me.LayoutControlItem27.Name = "LayoutControlItem27"
        Me.LayoutControlItem27.Size = New System.Drawing.Size(128, 24)
        Me.LayoutControlItem27.Text = "Change User: "
        Me.LayoutControlItem27.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem27.TextSize = New System.Drawing.Size(69, 13)
        Me.LayoutControlItem27.TextToControlDistance = 5
        '
        'LayoutControlItem28
        '
        Me.LayoutControlItem28.Control = Me.DateEdit4
        Me.LayoutControlItem28.CustomizationFormText = "Add Date:"
        Me.LayoutControlItem28.Location = New System.Drawing.Point(107, 489)
        Me.LayoutControlItem28.Name = "LayoutControlItem28"
        Me.LayoutControlItem28.Size = New System.Drawing.Size(108, 24)
        Me.LayoutControlItem28.Text = "Add Date:"
        Me.LayoutControlItem28.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem28.TextSize = New System.Drawing.Size(49, 13)
        Me.LayoutControlItem28.TextToControlDistance = 5
        '
        'LayoutControlItem29
        '
        Me.LayoutControlItem29.Control = Me.DateEdit5
        Me.LayoutControlItem29.CustomizationFormText = "Change Date: "
        Me.LayoutControlItem29.Location = New System.Drawing.Point(343, 489)
        Me.LayoutControlItem29.Name = "LayoutControlItem29"
        Me.LayoutControlItem29.Size = New System.Drawing.Size(129, 24)
        Me.LayoutControlItem29.Text = "Change Date: "
        Me.LayoutControlItem29.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem29.TextSize = New System.Drawing.Size(70, 13)
        Me.LayoutControlItem29.TextToControlDistance = 5
        '
        'SimpleSeparator3
        '
        Me.SimpleSeparator3.AllowHotTrack = False
        Me.SimpleSeparator3.Location = New System.Drawing.Point(0, 163)
        Me.SimpleSeparator3.Name = "SimpleSeparator3"
        Me.SimpleSeparator3.Size = New System.Drawing.Size(472, 2)
        '
        'SimpleSeparator4
        '
        Me.SimpleSeparator4.AllowHotTrack = False
        Me.SimpleSeparator4.Location = New System.Drawing.Point(0, 487)
        Me.SimpleSeparator4.Name = "SimpleSeparator4"
        Me.SimpleSeparator4.Size = New System.Drawing.Size(472, 2)
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.Location = New System.Drawing.Point(0, 368)
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(345, 66)
        '
        'RibbonPage2
        '
        Me.RibbonPage2.Name = "RibbonPage2"
        Me.RibbonPage2.Text = "RibbonPage2"
        '
        'frmAccountLeadAddOrEdit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1079, 677)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.RibbonControl1)
        Me.Name = "frmAccountLeadAddOrEdit"
        Me.Ribbon = Me.RibbonControl1
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Account Lead"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        Me.XtraScrollableControl2.ResumeLayout(False)
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.SearchLookUpEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsCompany, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit11.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit6.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit31.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit30.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit29.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit28.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit27.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit25.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShipZip.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teShipStreetAddress.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit22.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit21.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit20.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit19.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit18.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbePayFreuency.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit15.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCoStreetAddress.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teRoutingNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit9.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit13.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NumericUpDown1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit12.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teCoZip.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teFedId.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeCoState.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit16.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CalcEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CalcEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CalcEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CalcEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeShipState.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teListEmployeeWorkStates.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bePhone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beCell.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beEmail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.beFax.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teConnectecCompanies.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleSeparator1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleSeparator2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item0, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem30, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem33, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem34, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem58, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem35, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem31, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem37, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem38, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem39, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem36, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem40, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem41, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem42, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem43, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem44, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem45, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem46, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem50, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem49, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem48, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem56, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem54, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem57, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem53, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem52, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem51, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem59, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem60, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeAignedTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit5.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit4.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit7.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueCoNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit2View, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.slueReferedByCoNum.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.meNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgHighImportance, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem32, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem47, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem28, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem29, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleSeparator3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SimpleSeparator4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents DateEdit2 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents NumericUpDown1 As NumericUpDown
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents meNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents DateEdit1 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents cbeStatus As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents BindingSource1 As BindingSource
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents slueReferedByCoNum As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents SearchLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents DateEdit3 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents slueCoNum As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents SearchLookUpEdit2View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents cbeCategory As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents bsCompany As BindingSource
    Friend WithEvents colCoNumAndName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPR_CONTACT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_PHONE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_FAX As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_MODEM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFED_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_EMAIL As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents DateEdit5 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents DateEdit4 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit7 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit6 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents colCoNumAndName1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPR_CONTACT1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_PHONE1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_MODEM1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_EMAIL1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents cbePayFreuency As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents teFedId As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cbeAignedTo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPage2 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents bbiSave As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents teCoStreetAddress As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit15 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teRoutingNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit13 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit12 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teCoZip As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit9 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup7 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents TextEdit20 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit19 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit18 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit27 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit25 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShipZip As DevExpress.XtraEditors.TextEdit
    Friend WithEvents teShipStreetAddress As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit22 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit21 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents XtraScrollableControl2 As DevExpress.XtraEditors.XtraScrollableControl
    Friend WithEvents DateEdit6 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit31 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit30 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit29 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit28 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SimpleSeparator1 As DevExpress.XtraLayout.SimpleSeparator
    Friend WithEvents SimpleSeparator2 As DevExpress.XtraLayout.SimpleSeparator
    Friend WithEvents item0 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem30 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem22 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem23 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem33 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem34 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents item1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem35 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup5 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem31 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem37 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem38 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem39 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem36 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem40 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup6 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem41 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem42 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem43 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem44 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem45 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem46 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup8 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem50 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem49 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem48 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup9 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem56 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem54 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem57 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem53 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem52 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem51 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lcgHighImportance As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem32 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem25 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem24 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem47 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem26 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem27 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem28 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem29 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SimpleSeparator3 As DevExpress.XtraLayout.SimpleSeparator
    Friend WithEvents SimpleSeparator4 As DevExpress.XtraLayout.SimpleSeparator
    Friend WithEvents TextEdit11 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem58 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents cbeCoState As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TextEdit16 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents CalcEdit4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CalcEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CalcEdit2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CalcEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cbeShipState As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents teListEmployeeWorkStates As DevExpress.XtraEditors.TokenEdit
    Friend WithEvents bePhone As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents beCell As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents beEmail As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents beFax As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents teConnectecCompanies As DevExpress.XtraEditors.TokenEdit
    Friend WithEvents btnAddConnectedCompany As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem59 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SearchLookUpEdit1 As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlItem60 As DevExpress.XtraLayout.LayoutControlItem
End Class
