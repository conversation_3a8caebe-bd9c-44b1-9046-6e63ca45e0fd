Imports System.Configuration
Imports System.Net
Imports System.Net.Mail
Imports System.ServiceModel
Imports Microsoft.Extensions.Logging

Module Program
    Sub Main()
        ' Logging setup
        Dim loggerFactorys = LoggerFactory.Create(Sub(builder)
                                                      builder.AddConsole()
                                                      ' To add file logging, use Serilog or NLog here
                                                  End Sub)
        Dim logger = loggerFactorys.CreateLogger(Of frmLogin)()
        logger.LogInformation("Application started")

        ' WCF setup
        Dim endpointAddress = ConfigurationManager.AppSettings("ServiceAgent_EndpointAddress")
        Dim bindingType = ConfigurationManager.AppSettings("ServiceAgent_Binding")
        Dim endpoint = New EndpointAddress(endpointAddress)
        If bindingType = "netTcpBinding" Then
            Dim binding = New NetTcpBinding()
            ' Dim client As New ServiceAgentLib.ServiceAgentClient(binding, endpoint)
            ' client.Open()
            ' client.YourMethod()
            ' client.Close()
        Else
            Throw New NotSupportedException("Only netTcpBinding is supported in this example.")
        End If

        ' SMTP setup
        Dim smtpHost = ConfigurationManager.AppSettings("SmtpHost")
        Dim smtpPort = Integer.Parse(ConfigurationManager.AppSettings("SmtpPort"))
        Dim smtpEnableSsl = Boolean.Parse(ConfigurationManager.AppSettings("SmtpEnableSsl"))
        Dim smtpUser = ConfigurationManager.AppSettings("SmtpUser")
        Dim smtpPassword = ConfigurationManager.AppSettings("SmtpPassword")
        Dim smtp As New SmtpClient(smtpHost, smtpPort)
        smtp.EnableSsl = smtpEnableSsl
        smtp.Credentials = New NetworkCredential(smtpUser, smtpPassword)

        ' Add authentication/role logic here if needed
    End Sub
End Module