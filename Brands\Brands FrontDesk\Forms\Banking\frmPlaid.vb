﻿Imports Going.Plaid

Public Class frmPlaid
    Private _CoNum As Decimal,
            _CoName As String,
            _Env As String,
            _PlaidClient As PlaidClient
    Public Sub New(CoNum As Decimal, CoName As String)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        _CoNum = CoNum
        _CoName = CoName
    End Sub

    Private Sub btnAuth_Click(sender As Object, e As EventArgs) Handles btnAuth.Click
        If lueAccount.EditValue Is Nothing Then Return
        Dim Response As Auth.AuthGetResponse
        LayoutControl1.ShowProgessPanel
        Try

            Dim request = New Auth.AuthGetRequest()
            request.Options = New PlaidQuickstartBlazor.Shared.AuthGetRequestOptions2 With {.min_last_updated_datetime = DateTime.Now.AddMinutes(-1000).ToString("yyyy-MM-ddTHH:mm:ssZ")}
            Response = _PlaidClient.AuthGetAsync(request).GetAwaiter().GetResult()
            If Response.IsSuccessStatusCode Then
                Dim sql As String = ""
                For Each r In Response.Accounts
                    Dim numberAch = Response.Numbers.Ach.Where(Function(f) f.AccountId = r.AccountId).First()
                    Dim Name = r.Name
                    Dim balance = nz(r.Balances.Available, r.Balances.Current)
                    Dim Account = numberAch.Account
                    Dim routing = numberAch.Routing
                    Dim acctID = r.AccountId
                    Dim acctMask = IIf(r.Mask > "", $"'{r.Mask}'", "NULL")
                    Dim subType = r.Subtype
                    sql += $"EXEC custom.prc_UpdatePlaid @Item = 'auth', @CoNum = {_CoNum}, @Environment = '{_Env}', @PlaidID = '{lueAccount.EditValue}', @AcctName = '{Name}', @Balance = {balance}, @AcctNumber = '{Account}', @Routing = '{routing}', @AcctID = '{acctID}', @AcctMask = {acctMask}, @SubType = '{subType}';"
                Next
                UpdateSql(sql)
                LoadData()
            Else
                Throw New Exception("incorrect api call")
            End If
        Catch ex As Exception
            DisplayErrorMessage("errror in getting auth", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Sub btnBalance_Click(sender As Object, e As EventArgs) Handles btnBalance.Click
        If lueAccount.EditValue Is Nothing Then Return
        Dim Response As Accounts.AccountsGetResponse
        LayoutControl1.ShowProgessPanel
        Try

            Dim request = New Accounts.AccountsBalanceGetRequest()
            request.Options = New PlaidQuickstartBlazor.Shared.AccountsBalanceGetRequestOptions2 With {.min_last_updated_datetime = DateTime.Now.AddMinutes(-1000).ToString("yyyy-MM-ddTHH:mm:ssZ")}
            request.Options.MinLastUpdatedDatetime = DateTime.Parse(DateTime.Now.AddMinutes(-1000).ToString("yyyy-MM-ddTHH:mm:ssZ"))
            Response = _PlaidClient.AccountsBalanceGetAsync(request).GetAwaiter().GetResult()

            If Response.IsSuccessStatusCode Then
                Dim sql As String = ""
                For Each r In Response.Accounts
                    Dim Name = r.Name
                    Dim AcctID = r.AccountId
                    Dim balance = If(r.Balances.Available Is Nothing, r.Balances.Current, r.Balances.Available)
                    sql += $"EXEC custom.prc_UpdatePlaid @Item = 'balance', @CoNum = {_CoNum}, @Environment = '{_Env}', @PlaidID = '{lueAccount.EditValue}', @AcctName = '{Name}', @Balance = {balance}, @AcctNumber = null, @Routing = null, @AcctID = '{AcctID}';"
                Next
                UpdateSql(sql)
                LoadData()
            Else
                Throw New Exception("incorrect api call." + "Response:" + vbCrLf + Join({Newtonsoft.Json.JsonConvert.SerializeObject(Response)}.ToArray(), vbCrLf))
            End If
        Catch ex As Exception
            DisplayErrorMessage("errror in getting auth", ex)
        Finally
            LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Public Sub LoadData()
        Try
            GCAuth.DataSource = Query($"SELECT * FROM custom.PlaidAuth WHERE PlaidID = {lueAccount.EditValue}")
            GCBalance.DataSource = Query($"SELECT * FROM custom.PlaidBalance WHERE PlaidID = {lueAccount.EditValue}")

            If Me.GVAauth.Columns("LastUpdate") IsNot Nothing Then
                Me.GVAauth.Columns("LastUpdate").DisplayFormat.FormatString = "g"
            End If

            If Me.GVBalance.Columns("LastUpdate") IsNot Nothing Then
                Me.GVBalance.Columns("LastUpdate").DisplayFormat.FormatString = "g"
            End If

            GVAauth.BestFitColumns()
            GVBalance.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error in load data", ex)
        End Try
    End Sub

    Private Sub lueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles lueCompany.EditValueChanged
        Try
            _CoNum = lueCompany.EditValue
            _CoName = lueCompany.Text
            Me.SimpleLabelItem1.Text = $"{_CoName}"

            Dim Accts = Query(Of PlaidAccts)($"SELECT PlaidID, TokenDesc FROM custom.view_PlaidTokens WHERE CoNum = {_CoNum} AND Environment = '{_Env}'").ToList()

            lueAccount.Properties.DisplayMember = "TokenDesc"
            lueAccount.Properties.ValueMember = "PlaidID"
            lueAccount.Properties.DataSource = Accts

            Me.GCAuth.DataSource = Nothing
            Me.GCBalance.DataSource = Nothing

            If Accts.Count <> 1 Then
                lueAccount.Properties.NullText = "Choose"
            Else
                lueAccount.Properties.NullText = ""
                lueAccount.ItemIndex = 0
            End If

            GCClientAccounts.DataSource = Query($"EXEC custom.prc_PlaidGetAccountsForCo @CoNum = {_CoNum}")
            GVClientAccounts.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error company changed", ex)
        End Try
    End Sub

    Private Sub lueAccount_EditValueChanged(sender As Object, e As EventArgs) Handles lueAccount.EditValueChanged
        Try
            LoadData()
            If lueAccount.EditValue = Nothing Then
                _PlaidClient.AccessToken = ""
            Else
                _PlaidClient.AccessToken = Query(Of String)($"SELECT AccessToken FROM custom.PlaidTokens WHERE PlaidID = {lueAccount.EditValue}").First()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in acct change", ex)
        End Try
    End Sub

    Private Sub frmPlaid_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Dim db As New dbEPDataDataContext(GetConnectionString)
            _Env = GetUdfValue("PlaidEnvironment")
            Dim PlaidEnv = IIf(_Env = "S", 2, IIf(_Env = "D", 1, 0))
            _PlaidClient = New PlaidClient(PlaidEnv, GetUdfValue("PlaidClientID"), GetUdfValue("PlaidSecret"), "", Nothing, Nothing, ApiVersion.v20200914)

            lueCompany.Properties.DataSource = (From c In db.COMPANies Join p In db.view_PlaidTokens On p.CoNum Equals c.CONUM Select New With {.CoNum = c.CONUM, .CoName = c.CONUM.ToString() + " - " + c.CO_NAME}).OrderBy(Function(s) s.CoNum).Distinct.ToList()
            lueCompany.Properties.ValueMember = "CoNum"
            lueCompany.Properties.DisplayMember = "CoName"
            lueCompany.EditValue = _CoNum
        Catch ex As Exception
            DisplayErrorMessage("Error in frmPlaid Load", ex)
        End Try
    End Sub
    Class PlaidAccts
        Public Property PlaidID As Int32
        Public Property TokenDesc As String
    End Class
End Class