﻿Imports System.ComponentModel
Imports System.Data.SqlClient

Public Class ucReportsQueue
    Dim _conum As Decimal?
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Conum As Decimal?
        Get
            Return _conum
        End Get
        Set
            _conum = Value
            LoadReportsQueue()
        End Set
    End Property

    Dim _queueEmail As QueueEmail

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadReportsQueue()
    End Sub

    Private Sub ucReportsQueue_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        deFromDate.DateTime = DateTime.Today.AddDays(-3)
        LoadReportsQueue()
    End Sub

    Public Sub LoadReportsQueue()
        If modGlobals.IsInDesignMode Then Exit Sub
        colConumAndName.Visible = Not Conum.HasValue
        colRequestedBy.Visible = Conum.HasValue
        If deFromDate.DateTime = Nothing Then
            deFromDate.DateTime = DateTime.Today.AddDays(-3)
        End If

        Try
            gcQueue.DataSource = Query("--DECLARE @Conum decimal(6,0) = 812, @UserName varchar(75) = 'joel.f', @FromDate datetime = getdate() -3
                SELECT 
                    q.Id,
					c.CONUM,
					c.CO_NAME,
					ConumAndName = CONVERT(varchar(6), q.Conum) + ' ' + c.CO_NAME,
		            q.Status,
                    q.RequestedBy,
		            q.RequestedOn,
		            qe.EmailTo,
		            qe.EmailSubject,
		            qe.ZendeskTicketId,
		            qr.ReportsCount
	            FROM custom.Queue q (NOLOCK)
				INNER JOIN COMPANY c (NOLOCK) ON q.Conum = c.CONUM
	            LEFT OUTER JOIN custom.QueueDetail qd (NOLOCK) ON q.Id = qd.QueueId AND qd.QueueTypeName = 'Outbound Emails'
	            LEFT OUTER JOIN custom.QueueEmail qe (NOLOCK) ON qe.QueueDetailId = qd.Id
	            LEFT OUTER JOIN (
		            SELECT qd2.QueueId, COUNT(*) ReportsCount FROM EPDATA.custom.QueueDetail qd2 (NOLOCK)
		            WHERE qd2.QueueTypeName = 'EP Report' OR qd2.QueueTypeName = 'CR Report'
		            GROUP BY qd2.QueueId
	            ) qr ON qr.QueueId = q.Id
	            WHERE q.Description = 'FD Process Report' 
				AND ((@Conum IS NOT NULL AND q.Conum = @Conum) OR (@Conum IS NULL AND lower(q.RequestedBy) = lower(@UserName)))
				AND q.RequestedOn > @FromDate
	            ORDER BY q.RequestedOn DESC", New SqlParameter("conum", Conum), New SqlParameter("UserName", UserName), New SqlParameter("FromDate", deFromDate.DateTime))
        Catch ex As Exception
            DisplayErrorMessage("Error loading Reports", ex)
        End Try
    End Sub

    Private Sub gvQueue_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvQueue.FocusedRowObjectChanged
        Try
            Dim queueId As Integer = gvQueue.GetRowCellValue(e.RowHandle, "Id")

            Using ctx = New Brands.DAL.EPDATAContext(GetConnectionString)
                Dim qd = ctx.QueueDetails.Where(Function(f) f.QueueId = queueId AndAlso (f.QueueTypeName = "EP Report" OrElse f.QueueTypeName = "CR Report"))
                GridControl2.DataSource = ctx.QueueReports.Where(Function(r) qd.Select(Function(q) q.Id).Contains(r.QueueDetailId)).ToList
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error loading Reports", ex)
        End Try
    End Sub


    Private Sub gvQueue_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvQueue.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim zendeskTicketId = gvQueue.GetRowCellValue(e.HitInfo.RowHandle, "ZendeskTicketId")
            If zendeskTicketId IsNot DBNull.Value Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Open Zendesk Ticket", Sub()
                                                                                                 'System.Diagnostics.Process.Start($"{GetUdfValue("ZendeskUrl")}/agent/tickets/{zendeskTicketId}")
                                                                                                 Dim psi As New System.Diagnostics.ProcessStartInfo()
                                                                                                 psi.FileName = $"{GetUdfValue("ZendeskUrl")}/agent/tickets/{zendeskTicketId}"
                                                                                                 psi.UseShellExecute = True
                                                                                                 System.Diagnostics.Process.Start(psi)
                                                                                             End Sub))
            End If
        End If
    End Sub

    Private Sub riZendeskTicket_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riZendeskTicket.ButtonClick
        Dim zendeskTicketId = CType(sender, DevExpress.XtraEditors.ButtonEdit).EditValue
        If zendeskTicketId IsNot DBNull.Value Then
            'System.Diagnostics.Process.Start($"{GetUdfValue("ZendeskUrl")}/agent/tickets/{zendeskTicketId}")
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = $"{GetUdfValue("ZendeskUrl")}/agent/tickets/{zendeskTicketId}"
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        End If
    End Sub
End Class
