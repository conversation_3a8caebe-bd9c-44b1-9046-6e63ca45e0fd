﻿Imports DevExpress.Utils
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo

Public Class frmUnassignedPhoneNumbers
    Private Property Data As List(Of prc_1059AgentTopCallsResult)

    Private Sub frmUnassignedPhoneNumbers_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        deFromDate.DateTime = DateTime.Today.AddDays(-7)
        deToDate.DateTime = DateTime.Today
    End Sub

    Private Async Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Try
            Dim sql = $"EXEC custom.prc_1059AgentTopCalls '{deFromDate.DateTime:d}', '{deToDate.DateTime:d}', 'Agent', 1"
            Me.LayoutControl1.ShowProgessPanel
            Dim results = Await Task.Run(Function() Query(Of prc_1059AgentTopCallsResult)(sql))
            Data = results.ToList
            GridControl1.DataSource = Data
            GridView1.BestFitColumns()
        Catch ex As Exception
            Me.LayoutControl1.HideProgressPanel
            DisplayErrorMessage("Error loading data", ex)
        Finally
            Me.LayoutControl1.HideProgressPanel
        End Try
    End Sub

    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles GridView1.DoubleClick
        Dim ea As DXMouseEventArgs = TryCast(e, DXMouseEventArgs)
        Dim view As GridView = TryCast(sender, GridView)
        Dim info As GridHitInfo = view.CalcHitInfo(ea.Location)
        If info.InRow OrElse info.InRowCell Then
            Dim row As prc_1059AgentTopCallsResult = GridView1.GetRow(info.RowHandle)
            AddPhoneComments(row)
        End If
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As prc_1059AgentTopCallsResult = GridView1.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add Comments", Sub()
                                                                                      AddPhoneComments(row)
                                                                                  End Sub))
        End If
    End Sub

    Private Sub AddPhoneComments(row As prc_1059AgentTopCallsResult)
        Try
            Using frm = New frmPhoneDataComments(row.ExternalNumber)
                If frm.ShowDialog() = DialogResult.OK Then
                    'remove all calls matching number
                    Data.RemoveAll(Function(p) p.ExternalNumber = row.ExternalNumber)
                    GridControl1.RefreshDataSource()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error adding comments", ex)
        End Try
    End Sub

    Private Class prc_1059AgentTopCallsResult
        Public Property PhoneLine As String
        Public Property ext As Integer
        Public Property Agents As String
        Public Property ExternalNumber As String
        Public Property cname As String
        Public Property Conum As Decimal?
        Public Property CoName As String
        Public Property ContactType As String
        Public Property PhoneInfo As String
        Public Property IsActive As String
        Public Property CoNUmAll As String
        Public Property TaskTime_sec As Integer
        Public Property TalkTime_hms As String
        Public Property RingTime_hms As String
        Public Property Calls As Integer
        Public Property RN As Integer
        Public Property ReportCat As String
        Public Property TopDur As String
        Public Property TopCall As Integer?
    End Class


End Class