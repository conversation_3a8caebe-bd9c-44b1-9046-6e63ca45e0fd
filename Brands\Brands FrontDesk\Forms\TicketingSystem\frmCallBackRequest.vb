Imports System.ComponentModel
Imports System.Net.Mail
Imports Brands.Core.ZendeskServices
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls

Public Class frmCallBackRequest
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property _SelectedMacro As ZendeskApi_v2.Models.Macros.Macro

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Company As COMPANY
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property TicketID As Integer

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property FaxAttachemnt As view_FaxAndEmail
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property ShowMarkCompleteButton As Boolean = False

    Protected FrontDeskTicketEnt As FrontDeskTicket
    Protected TicketEnt As Ticket
    Protected Attachments As List(Of FrontDeskTicketAttachment)
    Protected DB As New dbEPDataDataContext(GetConnectionString)
    Protected FaxCategories As String()
    Protected AllUsers As List(Of DBUSER)

    Public Sub New()
        InitializeComponent()
        bePhone.AddPhoneButton
        btnSend.Enabled = Permissions.AllowSendEmailInFD
    End Sub

    Private Async Sub frmTicket_LoadAsync(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            NotesTextBox.ToTitleCase
            'AllUsers = (From A In DB.DBUSERs
            '            Group Join u In DB.FrontDeskPermissions On A.name Equals u.UserName Into Group
            '            From u In Group.DefaultIfEmpty()
            '            Where (u Is Nothing OrElse Not u.HideInUsersList) AndAlso A.email IsNot Nothing AndAlso A.email <> ""
            '            Select A
            '            Order By A.name).ToList
            AllUsers = (From A In DB.DBUSERs Join fdp In DB.FrontDeskPermissions On fdp.UserName Equals A.name Where fdp.HideInUsersList = 0 AndAlso A.email <> "" Select A).ToList()
            Me.DBUSERBindingSource.DataSource = AllUsers

            Dim NewList = (From A In AllUsers Order By A.name).ToList
            NewList.Insert(0, New DBUSER)
            'Me.DBUSERBindingSource1.DataSource = NewList

            If TicketID = 0 Then
                TicketEnt = New Ticket With {.TicketOpenedOn = Now, .CoNum = Company.CONUM, .AssignToUser = Company.OP_OWNER}
                FrontDeskTicketEnt = New FrontDeskTicket With {.Date = Date.Now, .ByUser = UserName}
                TicketEnt.FrontDeskTickets.Add(FrontDeskTicketEnt)
                DB.Tickets.InsertOnSubmit(TicketEnt)
                If Company IsNot Nothing Then
                    FrontDeskTicketEnt.CoNum = Company.CONUM
                    FrontDeskTicketEnt.CoContactName = Company.PR_CONTACT
                    FrontDeskTicketEnt.CoPhone = Company.CO_PHONE
                    FrontDeskTicketEnt.CoExt = Company.CO_EXTENSION
                    FrontDeskTicketEnt.CoEmail = Company.CO_EMAIL
                End If

                If FaxAttachemnt IsNot Nothing Then
                    FrontDeskTicketEnt.Category = "Fax Received"
                    FrontDeskTicketEnt.Notes = "Fax Received." & vbCrLf & vbCrLf
                    FrontDeskTicketEnt.FrontDeskTicketAttachments.Add(New FrontDeskTicketAttachment With {.FileName = FaxAttachemnt.FileName})
                End If

                Me.Text = "Enter new Ticket"
            Else
                FrontDeskTicketEnt = (From A In DB.FrontDeskTickets Where A.ID = TicketID).Single
                TicketEnt = FrontDeskTicketEnt.Ticket
                Me.Text = "Ticket # " & Me.TicketID
                lcRoot.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.True
            End If

            Me.TicketBindingSource.DataSource = TicketEnt
            Me.FrontDeskTicketBindingSource.DataSource = FrontDeskTicketEnt
            If TicketID = 0 Then
                SpellChecker.Check(NotesTextBox)
            End If

            Attachments = FrontDeskTicketEnt.FrontDeskTicketAttachments.ToList
            Me.lstAttachements.Items.Clear()
            For Each Att In Attachments
                Me.lstAttachements.Items.Add(Att.FileName)
            Next

            Me.btnSend.Enabled = TicketID = 0
            Me.btnAttach.Enabled = TicketID = 0

            lciBtnSend.Visibility = (Not ShowMarkCompleteButton).ToBarItemVisibility

            Dim macroHelper = New ZendeskMacrosHelper
            Await macroHelper.PopulatMacrosAsync(PopupMenu1, AddressOf macroItemClicked)

        Catch ex As Exception
            Logger.Error(ex, "Error initilizing frmTicket")
            DisplayErrorMessage("Error while loading data.", ex)
        End Try
    End Sub

    Private Sub macroItemClicked(sender As Object, e As ItemClickEventArgs)
        _SelectedMacro = DirectCast(e.Item, BarButtonItem).Tag
    End Sub

    Private Sub CategoryComboBox_SelectedIndexChanged(sender As Object, e As ChangingEventArgs)
        'cbeCategory.RefreshEditValue()
        If FaxCategories IsNot Nothing AndAlso FaxCategories.Contains(e.NewValue) Then
            TicketEnt.AssignToUser = "CS.O"
        End If

        If e.NewValue IsNot DBNull.Value AndAlso (FrontDeskTicketEnt.Subject.IsNullOrWhiteSpace OrElse e.OldValue Is DBNull.Value OrElse FrontDeskTicketEnt.Subject = GetSubject(e.OldValue)) Then
            FrontDeskTicketEnt.Subject = GetSubject(e.NewValue)
        End If
    End Sub

    Private Function GetSubject(cat As String) As String
        Return $"Brands FrontDesk Ticket - Co #: {FrontDeskTicketEnt.CoNum}; {Company?.CO_NAME}; Cat: {cat};"
    End Function

    Private Sub AssignToUserComboBox_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lueAssignedToUser.EditValueChanged
        Dim u As DBUSER = lueAssignedToUser.Properties.GetDataSourceRowByKeyValue(lueAssignedToUser.EditValue)
        If u IsNot Nothing Then
            Me.tbEmail.Text = u.email
        End If
        teSubject.Enabled = u?.name <> "CS.O"
    End Sub

    Private Sub CCUserComboBox_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lueCCUser.EditValueChanged
        Dim u As DBUSER = lueCCUser.Properties.GetDataSourceRowByKeyValue(lueCCUser.EditValue)
        If u IsNot Nothing Then
            Me.tbEmailCC.Text = u.email
        End If
    End Sub

    Private Sub btnsend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSend.Click
        If Not ValidateForm() Then Exit Sub

        Try
            DB.SubmitChanges()
            SaveAttachments()
            Dim user = DB.FrontDeskPermissions.SingleOrDefault(Function(u) u.UserName = TicketEnt.AssignToUser)
            If GetUdfValue("Call Back Request - Send Emails") = "Yes" OrElse (user?.SendEmailForCallBackRequests.HasValue AndAlso user?.SendEmailForCallBackRequests.Value) Then
                SendEmail()
            End If
            FrontDeskTicketEnt.IsCCSelf = True
            FrontDeskTicketEnt.IsSent = True
            DB.SubmitChanges()
            modSignalRClient.PushFaxOrEmailUpdate("FrontDeskTicket", FrontDeskTicketEnt.ID, "New Ticket")
            modSignalRClient.PushTicketAssignedToUpdate(TicketEnt)
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error send call back request email", ex)
        End Try
    End Sub

    Private Function ValidateForm() As Boolean
        Dim Errors As New List(Of String)
        If FrontDeskTicketEnt.CoNum = 0 AndAlso FaxAttachemnt Is Nothing Then
            Errors.Add("Company is not selected")
        End If
        If Me.tbEmail.Text = "" Then
            Errors.Add("Email address is blank")
        End If
        If Errors.Count > 0 Then
            MessageBox.Show(Join(Errors.ToArray, vbCrLf), "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End If

        If FrontDeskTicketEnt.Subject.IsNullOrWhiteSpace Then
            XtraMessageBox.Show("Please enter a subject.")
            Return False
        End If

        Return True
    End Function

    Private Sub SaveAttachments()
        While True
            Try
                If Not Attachments.Any Then Exit Sub
                Dim attachmentsDirectory = System.IO.Directory.CreateDirectory($"\\Brands.local\DFS\Execupay\FrontDesk\FrontDeskTickets\{FrontDeskTicketEnt.ID}").FullName
                For Each file In Attachments
                    While True
                        Try
                            Dim newFileName = System.IO.Path.Combine(attachmentsDirectory, System.IO.Path.GetFileName(file.FileName))
                            Logger.Debug("Copying file {From} {To}", file.FileName, newFileName)
                            System.IO.File.Copy(file.FileName, newFileName)
                            file.FileName = newFileName
                            Exit While
                        Catch ex As Exception
                            Logger.Error(ex, "Error while uploading file. {FileName}", file.FileName)
                            If XtraMessageBox.Show($"There was an error while uploading file: {file.FileName}.{vbCrLf}{ex.ToString()}{vbCrLf}Would you like to try again?", "Error uploading File", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                                Exit While
                            End If
                        End Try
                    End While
                Next
                FrontDeskTicketEnt.FrontDeskTicketAttachments.AddRange(Attachments)
                Exit While
            Catch ex As Exception
                Logger.Error(ex, "Error while uploading files")
                If XtraMessageBox.Show($"There was an error while uploading files.{vbCrLf}{ex.ToString()}{vbCrLf}Would you like to try again?", "Error uploading File", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                    Exit While
                End If
            End Try
        End While
    End Sub


    Sub SendEmail()
        Dim html As String = GetEmailBodyHtml()

        Dim From = (From A As DBUSER In DBUSERBindingSource.List Where A.name.ToLower = UserName.ToLower).SingleOrDefault
        Dim MM As New MailMessage
        MM.From = New MailAddress(From.email, From.name)
        MM.To.Add(New MailAddress(Me.tbEmail.Text, lueAssignedToUser.Text))
        MM.Subject = FrontDeskTicketEnt.Subject
        If FaxAttachemnt IsNot Nothing Then
            MM.Subject &= " - Fax Received"
        End If
        'MM.Body = SB.ToString
        MM.Body = html
        MM.IsBodyHtml = True
        Dim u As DBUSER = lueCCUser.Properties.GetDataSourceRowByKeyValue(lueCCUser.EditValue)
        If u IsNot Nothing AndAlso u.email IsNot Nothing Then
            MM.CC.Add(New MailAddress(u.email, u.name))
        End If
        MM.CC.Add(MM.From)

        For Each FN In Attachments
            MM.Attachments.Add(New Attachment(FN.FileName))
        Next

        Dim MC As New SmtpClient
        MC.Send(MM)
    End Sub

    Private Function GetEmailBodyHtml() As String
        Dim html = IO.File.ReadAllText(IO.Path.Combine(My.Application.Info.DirectoryPath, "email.htm"))
        html = html.Replace("<--From-->", FrontDeskTicketEnt.ByUser)
        html = html.Replace("<--CoNum-->", If(Company IsNot Nothing, Company.CONUM, "N/A"))
        html = html.Replace("<--CompanyName-->", If(Company IsNot Nothing, Company.CO_NAME, "N/A"))
        html = html.Replace("<--Notes-->", (FrontDeskTicketEnt.Notes & "").Replace(vbCrLf, "<br/>"))
        html = html.Replace("<--Contact-->", FrontDeskTicketEnt.CoContactName)
        html = html.Replace("<--Phone-->", FrontDeskTicketEnt.CoPhone)
        html = html.Replace("<--Ext-->", IIf(Not String.IsNullOrEmpty(FrontDeskTicketEnt.CoExt), " Ext. " & FrontDeskTicketEnt.CoExt, ""))
        html = html.Replace("<--Email-->", IIf(Not String.IsNullOrEmpty(FrontDeskTicketEnt.CoEmail), String.Format("<a href='mailto:{0}'>{0}</a>", FrontDeskTicketEnt.CoEmail), ""))
        html = html.Replace("<--DBA-->", If(Company IsNot Nothing, Company.CO_DBA, "N/A"))
        html = html.Replace("<--PRContact-->", If(Company IsNot Nothing, Company.PR_CONTACT, "N/A"))
        html = html.Replace("<--PRPassword-->", If(Company IsNot Nothing, Company.PR_PASSWORD, "N/A"))
        html = html.Replace("<--EntryType-->", If(Company IsNot Nothing, Company.ENTRY_TYPE, "N/A"))

        If Company IsNot Nothing AndAlso (FrontDeskTicketEnt.CoContactName & "" <> Company.PR_CONTACT OrElse FrontDeskTicketEnt.CoPhone & "" <> Company.CO_PHONE) Then
            html = html.Replace("<--DefContact-->", Company.PR_CONTACT)
            html = html.Replace("<--DefPhone-->", Company.CO_PHONE)
            html = html.Replace("<--DefExt-->", IIf(Not String.IsNullOrEmpty(Company.CO_EXTENSION), " Ext. " & Company.CO_EXTENSION, ""))
            html = html.Replace("<--DefEmail-->", IIf(Not String.IsNullOrEmpty(Company.CO_EMAIL), String.Format("<a href='mailto:{0}'>{0}</a>", Company.CO_EMAIL), ""))
        Else
            Dim StartIX = html.IndexOf("<!--~~Default-->")
            Dim EndIX = html.LastIndexOf("<!--~~Default-->") + ("<!--~~Default-->").Length
            html = html.Remove(StartIX, EndIX - StartIX)
        End If

        Return html
    End Function

    Private Sub btnAttach_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAttach.Click
        Dim FD As New OpenFileDialog
        FD.Multiselect = True
        If FD.ShowDialog = System.Windows.Forms.DialogResult.OK Then
            For Each FN In FD.FileNames
                Dim FileName = FN
                If Attachments.Find(Function(p) p.FileName = FileName) Is Nothing Then
                    Me.Attachments.Add(New FrontDeskTicketAttachment With {.FileName = FileName})
                    Me.lstAttachements.Items.Add(FileName)
                End If
            Next
        End If
    End Sub

    Private Sub lstAttachements_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles lstAttachements.KeyDown
        If e.KeyData = Keys.Delete Then
            For I = 0 To Me.lstAttachements.SelectedItems.Count - 1
                Dim li = Me.lstAttachements.SelectedItems(I)
                Me.lstAttachements.Items.Remove(li)
                Dim Att = Attachments.Find(Function(p) p.FileName = li)
                If Att IsNot Nothing Then Attachments.Remove(Att)
            Next
        End If
    End Sub


    Private Sub lstAttachements_MouseDown(sender As Object, e As MouseEventArgs) Handles lstAttachements.MouseDown
        If e.Button = System.Windows.Forms.MouseButtons.Right Then
            DisplayMessageBox("To delete a attachment, Please use the 'Del' key.")
        End If
    End Sub

    Private Sub btnMarkComplete_Click(sender As Object, e As EventArgs)
        Try
            FrontDeskTicketEnt.ClosedDate = Now
            DB.SubmitChanges()
            DialogResult = DialogResult.OK
            Close()
        Catch ex As Exception
            Logger.Error(ex, "Error marking ticket as completed {ID}", TicketID)
            DisplayErrorMessage("Error marking ticket as completed", ex)
        End Try
    End Sub

    Private Async Sub btnStartTicketInZendesk_ClickAsync(sender As Object, e As EventArgs) Handles btnStartTicketInZendesk.Click
        Logger.Information("User click on Start Ticket in zendesk")
        If Not ValidateForm() Then Exit Sub
        Try
            lcRoot.ShowProgessPanel
            Await CanCreateTicketAsync()
            Dim createTicketService = GetCreateTicketDraftService()
            Dim user = modGlobals.Users.SingleOrDefault(Function(u) u.UserName.ToLower = TicketEnt.AssignToUser.ToLower())
            Dim ticketOptions = New CreateTicketDraft.CreateTicketOptions With
            {
                .Conum = Company.CONUM,
                .CollaboratorEmails = New List(Of String),
                .AssignedTo = user?.ZendeskUserId,
                .EmailBodyHtml = GetEmailBodyHtml(),
                .Subject = FrontDeskTicketEnt.Subject
            }
            If Attachments.Any() Then
                ticketOptions.Attachments.AddRange(Attachments.Select(Function(a) a.FileName))
            End If
            '.Comment = "Created In Front Desk",
            If tbEmailCC.Text.IsNotNullOrWhiteSpace Then ticketOptions.CollaboratorEmails.Add(tbEmailCC.Text)

            Dim newTicket = Await createTicketService.CreateTicketForDefaultUserAsync(ticketOptions, _SelectedMacro?.Id)
            Dim macroId = GetUdfValue("Zendesk_CallBackRequest_MacroId")
            Await createTicketService.NavigateToTicket(newTicket.Ticket.Id.Value, UserName, IIf(macroId.IsNotNullOrWhiteSpace(), macroId, Nothing))
            Close()
        Catch ex As Exception
            lcRoot.HideProgressPanel
            DisplayErrorMessage("Error creating ticket in zendesk", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub
End Class