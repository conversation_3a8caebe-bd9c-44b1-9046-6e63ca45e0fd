﻿Imports System.Configuration
Imports System.Data
Imports System.Data.SqlClient
Imports System.Globalization
Imports System.Runtime.CompilerServices
Imports System.Text
Imports System.Text.RegularExpressions
Imports Dapper
Imports DevExpress.XtraEditors
Imports PwdEnc
Imports QueueBuilder
Imports Serilog

Public Class Globals
    Public Shared Property UserName() As String
        Get
            Return modGlobals.UserName
        End Get
        Set(ByVal value As String)
            Logger?.Debug("Setting Globals.UserName To: {UserName}", value)
            modGlobals.UserName = value
        End Set
    End Property

    Public Shared Property Password() As String
        Get
            Return modGlobals.Password
        End Get
        Set(ByVal value As String)
            modGlobals.Password = value
        End Set
    End Property

    Public Shared Property IsBackgroundProcess() As String
        Get
            Return modGlobals.IsBackgroundProcess
        End Get
        Set(value As String)
            modGlobals.IsBackgroundProcess = value
        End Set
    End Property

    Public Shared Property Logger As Serilog.ILogger
        Get
            Return modGlobals.Logger
        End Get
        Set(value As Serilog.ILogger)
            modGlobals.Logger = value
        End Set
    End Property

End Class

Public Module modGlobals
    Sub New()
        SetUpLogger()
    End Sub

    Public Property UserName As String
    Public Password As String
    Public AppInstanceId As String = Guid.NewGuid.ToString
    Public Property Version As String
    Public CommandLineArgs As String()

    Private _Permissions As FrontDeskPermission
    Private _PermissionsZD As FrontDeskPermission
    Private _UsePPxLibrary As Boolean = False
    Public Property Users As List(Of FrontDeskPermission)
    Private Property _LastUpdateRecordCount As Int32

    Public ReadOnly Property LastUpdateRecordCount As Int32
        Get
            Return _LastUpdateRecordCount
        End Get
    End Property

    Public Property Permissions As FrontDeskPermission
        Get
            Return _Permissions
        End Get
        Private Set(value As FrontDeskPermission)
            _Permissions = value
        End Set
    End Property

    Public Property PermissionsZD As FrontDeskPermission
        Get
            Return _PermissionsZD
        End Get
        Set(value As FrontDeskPermission)
            _PermissionsZD = value
        End Set
    End Property

    Public Property UsePPxLibrary As Boolean
        Get
            Return _UsePPxLibrary
        End Get
        Set(value As Boolean)
            _UsePPxLibrary = value

            Try
                Using DB = New dbEPDataDataContext(GetConnectionString)
                    DB.ExecuteCommand($"insert custom.UsePPxLibraryLog(UsePPxLibrary, UserName) select '{value}', '{UserName}'")
                    Logger.Information($"UsePPxLibrary = {value}")
                    MainForm.toggleUsePPxLibrary.Checked = value
                End Using
            Catch ex As Exception
                Logger.Error($"error when logging UsePPxLibrary value = {value}", ex)
            End Try
        End Set
    End Property

    Public Property CallActionAfterStatementComplete As Action(Of String)

    Private Sub StatementCompleted(sender As Object, e As StatementCompletedEventArgs)
        Try
            _LastUpdateRecordCount = e.RecordCount

            If CallActionAfterStatementComplete IsNot Nothing Then
                CallActionAfterStatementComplete.Invoke(e.RecordCount)
            End If
        Catch ex As Exception

        End Try
    End Sub

    Public Sub ReloadPermissions(Optional _username As String = Nothing)
        Using DB = New dbEPDataDataContext(GetConnectionString)
            Permissions = DB.FrontDeskPermissions.Single(Function(p) p.UserName = If(_username, UserName))
            Users = DB.FrontDeskPermissions.ToList

            If Permissions.UsePPxLibrary Then
                UsePPxLibrary = True
            End If
        End Using
    End Sub

    Public Function UserInRole(roleName As String, Optional username As String = Nothing) As Boolean
        If username Is Nothing Then username = modGlobals.UserName
        Using db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
            Dim role = db.FrontDeskRoles.Single(Function(f) f.RoleName = roleName)
            Dim userRole = role.FrontDeskRoleUsers.SingleOrDefault(Function(u) u.UserName = username)
            Return userRole IsNot Nothing
        End Using
    End Function

    Public Property UserInfo As DBUSER

    Public MainForm As frmMain
    Public Property IsBackgroundProcess As Boolean = False

    Public Property Logger As ILogger
    Public Sub SetUpLogger(Optional enricher As KeyValuePair = Nothing)
        Dim config = New LoggerConfiguration() _
        .Destructure.ToMaximumDepth(3) _
        .Enrich.WithMachineName() _
        .Enrich.WithThreadId() _
        .Enrich.WithProcessId() _
        .Enrich.WithProperty("User", UserName) _
        .Enrich.WithProperty("Version", Version) _
        .Enrich.WithProperty("AppID", Application.ProductName) _
        .Enrich.WithProperty("AppInstanceId", AppInstanceId) _
        .WriteTo.Seq("http://appserver:5341", Serilog.Events.LogEventLevel.Verbose, apiKey:="LQ3w6zXadJDeJMkHv2Kq") _
        .MinimumLevel.Verbose() _
        .Enrich.FromLogContext()

        If enricher IsNot Nothing Then config.Enrich.WithProperty(enricher.Name, enricher.Value)
        Logger = config.CreateLogger()
        Logger.Debug("Logger Initialized.")
    End Sub

    Public ReadOnly Property IsInDesignMode As Boolean
        Get
            Return System.Diagnostics.Process.GetCurrentProcess().ProcessName = "devenv"
        End Get
    End Property

    Public Sub ShowAlert(ByVal caption As String, ByVal text As String, Optional loc As DevExpress.XtraBars.Alerter.AlertFormLocation = DevExpress.XtraBars.Alerter.AlertFormLocation.BottomRight)
        MainForm.AlertControl.FormLocation = loc
        MainForm.AlertControl.Show(MainForm, caption, text, text)
    End Sub

    Function GetConnectionString() As String
        Dim NewConnBuilder As New SqlClient.SqlConnectionStringBuilder(My.Settings.EPDATAConnectionString)
        ' Use UserName directly if Permissions is not available (during login) or if Permissions.UserName is empty
        NewConnBuilder.UserID = If(Permissions?.UserName?.IsNotNullOrWhiteSpace = True, Permissions.UserName, UserName)
        NewConnBuilder.Password = Password
        NewConnBuilder.ApplicationName = "Brands FrontDesk"
        NewConnBuilder.IntegratedSecurity = False
        NewConnBuilder.TrustServerCertificate = True
        'NewConnBuilder.AsynchronousProcessing = True
        NewConnBuilder.ConnectTimeout = 2000
        Return NewConnBuilder.ConnectionString & ";MultipleActiveResultSets=true"
    End Function

    Function GetQueueBuilder() As QueueBuilder.QueueBuilder
        Dim q = New QueueBuilder.QueueBuilder(New Brands.DAL.EPDATAContext(GetConnectionString), Logger, New RabbitMqConnection())
        q.WithRequesterInfo(UserName, "FD")
        Return q
    End Function

    Private Class RabbitMqConnection
        Implements QueueBuilder.IRabbitMQConnectionString

        Public ReadOnly Property RabbitMQConnectionString As String Implements IRabbitMQConnectionString.RabbitMQConnectionString
            Get
                Return My.Settings.RabbitMQConnectionString
            End Get
        End Property
    End Class

    Function GetAdminConnectionString() As String
        Dim NewConnBuilder As New SqlClient.SqlConnectionStringBuilder(My.Settings.EPDATAConnectionString)
        Dim cs = PwdEncr.DecUI(ConfigurationManager.AppSettings("uidp")).Split(New Char() {"=", ";"}, 4)
        NewConnBuilder.UserID = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(1), "")
        NewConnBuilder.Password = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(3), "")
        NewConnBuilder.ApplicationName = "Brands FrontDesk"
        NewConnBuilder.IntegratedSecurity = False
        NewConnBuilder.TrustServerCertificate = True
        'NewConnBuilder.AsynchronousProcessing = True
        Return NewConnBuilder.ConnectionString & ";MultipleActiveResultSets=true"
    End Function

    Function EP_API() As PPxPayrollInterface
        'If Not UsePPxLibrary Then
        '    If PPxPayroll.Instance Is Nothing Then
        '        PPxPayroll.Instance = New PPxPayroll(My.Settings.APIProviderID, 0, 0, UserName)
        '        PPxPayroll.Instance.PayrollAPI.ConnectionProfile.ConnectionString = GetAdminConnectionString()
        '    End If
        '    Return PPxPayroll.Instance
        'Else
        '    'If PPxPayroll2.Instance Is Nothing Then
        '    '    PPxPayroll2.Instance = New PPxPayroll2(My.Settings.APIProviderID, 0, 0, UserName)
        '    '    PPxPayroll2.Instance.setConnectionString(GetAdminConnectionString())
        '    'End If
        '    Return PPxPayrollRabbit.Instance(UserName)
        'End If
        Return PPxPayrollRabbit.Instance(UserName)
    End Function

    Function nz(ByVal Input As Object, ByVal ValueIfNull As Object) As Object
        Return If(IsDBNull(Input) OrElse Input Is Nothing, ValueIfNull, Input)
    End Function

    Function nz(ByVal Input? As Boolean, ByVal ValueIfNull As Object) As Boolean
        Return If(IsDBNull(Input) OrElse Input Is Nothing, ValueIfNull, Input)
    End Function


    Sub DisplayMessageBox(ByVal Msg As String, Optional Caption As String = "Error")
        Logger.ForContext("MsgBoxVisible", True).Error(Msg)
        If Not IsBackgroundProcess Then XtraMessageBox.Show(Msg, Caption, MessageBoxButtons.OK)
    End Sub

    Sub DisplayErrorMessage(ByVal msg As String, ex As Exception, Optional Caption As String = "Error")
        If IsBackgroundProcess Then
            Logger.Error(ex, msg)
        Else
            Logger.Error(ex, "Showing Error Message. Message: {msg}", msg)
            Using frm = New frmErrorWindow(msg, ex) With {.TopMost = True}
                If frm.ShowDialog() = DialogResult.OK Then

                End If
            End Using
        End If
        'msg = "{0}{1}{2}".FormatWith(msg, vbCrLf, ex?.Message)
        'If Not IsBackgroundProcess Then XtraMessageBox.Show(msg, Caption, MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Sub

    'Sub DisplayErrorMessage(ByVal ex As Exception, ByVal Msg As String, Optional ShowMsgBox As Boolean = True, Optional Caption As String = "Error")
    '    Logger.ForContext("MsgBoxVisible", ShowMsgBox).Error(ex, Msg)
    '    If ShowMsgBox Then
    '        If Not IsBackgroundProcess Then XtraMessageBox.Show($"{Msg}{vbCrLf}{ex.Message}", Caption, MessageBoxButtons.OK, MessageBoxIcon.Error)
    '    End If
    'End Sub

    Public Function GetDictionary(val As String) As Dictionary(Of String, String)
        Dim dic = New Dictionary(Of String, String)
        dic.Add("Message", val)
        Return dic
    End Function

    Function ConvertHoursToDecimal(ByVal Input As String) As Decimal?
        If String.IsNullOrEmpty(Input) Then
            Return Nothing
        End If
        Dim d As Decimal
        If Decimal.TryParse(nz(Input.Replace(":", ""), "."), d) Then
            Input = d.ToString("F2")
        End If
        Dim Parts = Input.Split({":"c, "."c})
        Dim Value As Decimal = Parts(0)
        If Parts.Length > 1 Then
            Value += Decimal.Round(Decimal.Parse(Parts(1)) / 60, 4)
        End If
        Return Value
    End Function

    Function ConvertDecimalToHours(ByVal Input As Decimal?) As String
        If Not Input.HasValue Then
            Return Nothing
        End If
        Dim Parts = Input.ToString.Split(".")
        Dim Value As String = Parts(0)
        If Parts.Length > 1 Then
            Dim Minutes = Decimal.Parse("." & Parts(1))
            Minutes = Math.Round(60 / 100 * Minutes, 2)
            Value &= "." & Minutes.ToString.Split(".")(1) ' - Decimal.Floor(Minutes)
        End If
        Return Value
    End Function

    Function CalculateDefaultHours(ByVal Frequency As String) As Decimal
        Select Case Frequency
            Case "Weekly"
                Return 40
            Case "Bi-Weekly"
                Return 80
            Case "Semi-Monthly"
                Return 86.67
            Case "Monthly"
                Return 173.33
            Case "Quarterly"
                Return 520
            Case "Annually"
                Return 2080
            Case "Daily"
                Return 8
            Case Else
                Return 40
        End Select
    End Function
    Public Structure validateOverrideDB
        Public DBOverrideAmount As Decimal
        Public message As String
    End Structure
    Sub TakePayroll(ByVal ID As Integer)
        Dim DB As dbEPDataDataContext
        DB = New dbEPDataDataContext(GetConnectionString)
        Dim Rec = (From A In DB.pr_batch_in_processes Where A.ID = ID).Single
        If Rec.CompleteBy IsNot Nothing AndAlso Rec.CompleteBy <> UserName Then
            DisplayMessageBox("Payroll taken by '" & Rec.CompleteBy & "' on '" & "{0:g}".FormatWith(Rec.CompleteDate) & "'")
            Exit Sub
        End If
        Rec.LastOpenedBy = UserName
        Rec.LastOpenedDate = Now
        Rec.CompleteBy = UserName
        Rec.CompleteDate = Now

        DB.SubmitChanges()

    End Sub
    Function overrideDisability(ByVal DBOverrideAmount As Decimal, ByVal gross As Decimal, ByVal payFrequency As String, ByVal state As String) As validateOverrideDB
        Dim vodb As New validateOverrideDB

        Dim maxDB As Decimal = NYMaxDB(payFrequency)
        vodb.DBOverrideAmount = 0
        vodb.message = ""
        Const maxPercent As Decimal = 0.005
        Dim calculatedDB As Decimal = (gross * maxPercent)

        If state <> "NY" Then
            vodb.DBOverrideAmount = DBOverrideAmount
        ElseIf DBOverrideAmount > maxDB Then
            vodb.message = String.Format("DB Override for NY " & FormatCurrency(DBOverrideAmount) & " exceeds the maximum of " & FormatCurrency(maxDB) & " per " & payFrequency & " Override was skipped")
        ElseIf DBOverrideAmount > calculatedDB Then
            vodb.message = String.Format("DB Override for NY " & FormatCurrency(DBOverrideAmount) & " exceeds the maximum of " & FormatPercent(maxPercent) & " of gross amount Override was skipped")
        Else
            vodb.DBOverrideAmount = DBOverrideAmount
        End If
        Return vodb

    End Function

    Public Function NYMaxDB(ByVal Frequency As String) As Decimal
        Select Case Frequency
            Case "Weekly"
                Return 0.6
            Case "Bi-Weekly"
                Return 1.2
            Case "Semi-Monthly"
                Return 1.3
            Case "Monthly"
                Return 2.6
            Case "Quarterly"
                Return 5.94
            Case "Annually"
                Return 23.76
            Case "Daily"
                Return 0.12
            Case Else
                Return 0
        End Select
    End Function

    Function CalculateFicaAdd(ByVal Amount As Decimal) As Decimal
        Dim TaxRate As Decimal = 7.65 / 100.0
        Dim LeftOver As Decimal = 1.0 - TaxRate
        Dim Results As Decimal = Amount / LeftOver
        Return Decimal.Round(Results, 2)
    End Function

    Function ConfirmPassword(ByVal InputPassword As String) As Boolean
        Dim frm As New frmConfirmPassword With {.Password = InputPassword}
        Dim results = frm.ShowDialog
        frm.Dispose()
        Return results = DialogResult.OK
    End Function

    Sub ClearEditor(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = Keys.Back OrElse e.KeyCode = Keys.Delete Then
            Dim editor As DevExpress.XtraEditors.BaseEdit = CType(sender, DevExpress.XtraEditors.BaseEdit)
            editor.EditValue = DBNull.Value
            e.Handled = True
            'editor.DoValidate()
        End If
    End Sub

    Function GetLinkedCoNum(conum As Decimal) As Decimal
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim company = db.COMPANies.Single(Function(c) c.CONUM = conum)
            Dim linkedCo = db.COMPANies.SingleOrDefault(Function(c) c.CONUM > 900 And c.CONUM < 999 AndAlso c.FED_ID = company.FED_ID AndAlso c.CO_STATUS = "Active Status")
            If linkedCo IsNot Nothing Then
                Return linkedCo.CONUM
            Else
                Return conum
            End If
        End Using
    End Function

    <Extension()>
    Function ConvertToLower(ByVal input As String) As String
        Return (input & "").ToLower
    End Function

    <Extension()>
    Function ConvertToNothing(ByVal input As String)
        If String.IsNullOrEmpty(input) Then
            Return Nothing
        Else
            Return input
        End If
    End Function

    <Extension()>
    Function ToYesNoString(ByVal input As Boolean) As String
        Return If(input, "YES", "NO")
    End Function

    <Extension()>
    Function FromYesNoString(ByVal input As String) As Boolean
        Return If(String.IsNullOrWhiteSpace(input), False, input.ToUpper() = "YES")
    End Function

    <Extension()>
    Function HasValue(ByVal Control As DevExpress.XtraEditors.BaseEdit)
        Return Not IsDBNull(Control.EditValue) AndAlso Not String.IsNullOrEmpty(Control.EditValue)
    End Function

    <Extension()>
    Function GetValue(ByVal Row As DataRow, ByVal FieldName As String) As Object
        If IsDBNull(Row(FieldName)) Then
            Return Nothing
        Else
            Return Row(FieldName)
        End If
    End Function

    <Extension()>
    Function IsNullOrWhiteSpace(ByVal value As String) As Boolean
        Return String.IsNullOrWhiteSpace(value)
    End Function

    <Extension()>
    Function IfNotNullThenAdd(ByVal value As String, appendValue As String) As String
        Return value & IIf(value.IsNullOrWhiteSpace(), "", appendValue)
    End Function

    <Extension()>
    Function IsNotNullOrWhiteSpace(ByVal value As String) As Boolean
        Return Not String.IsNullOrWhiteSpace(value)
    End Function

    <Extension()>
    Function FormatWith(ByVal value As String, ParamArray params() As Object) As String
        Return String.Format(value, params)
    End Function

    <Extension>
    Function IsMinDataThenNothing(ByVal value As DateTime) As DateTime?
        If value = DateTime.MinValue Then
            Return Nothing
        Else
            Return value
        End If
    End Function

    <Extension()>
    Function QuoteSQLString(ByVal value As String) As String
        If value Is Nothing Then
            Return "NULL"
        Else
            Return "'" + value.Replace("'", "''") + "'"
        End If
    End Function

    <Extension()>
    Function QuoteSQL(ByVal value As Decimal?) As String
        If value Is Nothing Then
            Return "NULL"
        Else
            Return value
        End If
    End Function

    <Extension()>
    Function QuoteSQL(ByVal value As DateTime?) As String
        If value Is Nothing Then
            Return "NULL"
        Else
            Return value
        End If
    End Function

    ''' <summary>
    ''' Clones any object and returns the new cloned object.
    ''' </summary>
    ''' <typeparam name="T">The type of object.</typeparam>
    ''' <param name="source">The original object.</param>
    ''' <returns>The clone of the object.</returns>
    <Extension()>
    Public Function CloneEntity(Of T As {Class, New})(ByVal source As T) As T
        Dim Props = source.GetType.GetProperties(Reflection.BindingFlags.Instance Or Reflection.BindingFlags.Public)
        Dim copy = New T()
        For Each p In Props
            Dim pt As Type = p.PropertyType

            If (pt.IsValueType OrElse pt = GetType(String)) AndAlso (p.CanRead) AndAlso (p.CanWrite) Then
                p.SetValue(copy, p.GetValue(source, Nothing), Nothing)
            End If
        Next
        Return copy
    End Function

    '<Extension()>
    'Public Function SaveChanges(ByVal entity As dbEPDataDataContext) As Boolean
    '    Try
    '        entity.SaveChanges()
    '        Return True
    '    Catch ex As Linq.ChangeConflictException
    '        Dim msg = "Cannot save data. The record has been modified by someone else" & vbCrLf
    '        Dim Records = entity.ChangeConflicts
    '        For Each rec In Records
    '            If rec.memberconflicts?.Count > 0 Then
    '                msg &= Environment.NewLine
    '                msg &= "--" & rec.object.GetType.ToString.Split(".").Last & "--" & Environment.NewLine
    '                For Each mem In rec.memberconflicts
    '                    If mem.member.name <> "timestamp" Then
    '                        msg &= $"{mem.member.name}: Original = {mem.originalvalue}, Current DB Value = {mem.databasevalue}" & vbCrLf
    '                    End If
    '                Next
    '            End If
    '        Next
    '        msg &= "The record will be reloaded, and YOUR CHANGES WILL BE LOST."
    '        For Each rec In Records
    '            'rec.Resolve(Linq.RefreshMode.KeepChanges)
    '            rec.Resolve(Data.Linq.RefreshMode.OverwriteCurrentValues)
    '        Next
    '        DisplayErrorMessage(msg, ex)
    '        Return False
    '    Catch ex As Exception
    '        Logger.Error(ex, "Error in SaveChanges")
    '        DisplayErrorMessage("Error Saving Changed", ex)
    '        Return False
    '    End Try
    'End Function

    ' Extension method removed - retry logic is now in the overridden SaveChanges method in dbEPDataDataContext









    <Extension()>
    Public Sub LogToConsole(ByVal entity As dbEPDataDataContext)
        entity.Log = New DebugTextWriter
    End Sub

    <Extension()>
    Public Function GetValueOrDefault(value As Boolean, Optional defaultValue As Boolean = False) As Boolean
        Return value
    End Function


    Class DebugTextWriter
        Inherits System.IO.TextWriter
        Public Overrides Sub Write(buffer As Char(), index As Integer, count As Integer)
            System.Diagnostics.Debug.Write(New [String](buffer, index, count))
            Logger.Debug(New String(buffer, index, count))
        End Sub

        Public Overrides Sub Write(value As String)
            System.Diagnostics.Debug.Write(value)
            Logger.Debug(value)
        End Sub

        Public Overrides ReadOnly Property Encoding() As Encoding
            Get
                Return System.Text.Encoding.[Default]
            End Get
        End Property
    End Class

    Public Sub SetValue(ByVal inputObject As Object, ByVal propertyName As String, ByVal propertyVal As Object)
        'http://technico.qnownow.com/how-to-set-property-value-using-reflection-in-c/
        'find out the type 
        Dim type As Type = inputObject.GetType()

        'get the property information based on the type
        Dim propertyInfo As System.Reflection.PropertyInfo = type.GetProperty(propertyName, Reflection.BindingFlags.Public Or Reflection.BindingFlags.Instance Or Reflection.BindingFlags.IgnoreCase)

        'find the property type
        Dim propertyType As Type = propertyInfo.PropertyType

        'Convert.ChangeType does not handle conversion to nullable types
        'if the property type is nullable, we need to get the underlying type of the property
        Dim targetType = If(IsNullableType(propertyInfo.PropertyType), Nullable.GetUnderlyingType(propertyInfo.PropertyType), propertyInfo.PropertyType)

        'Returns an System.Object with the specified System.Type and whose value is
        'equivalent to the specified object.
        propertyVal = Convert.ChangeType(propertyVal, targetType)

        'Set the value of the property
        propertyInfo.SetValue(inputObject, propertyVal, Nothing)
    End Sub

    Private Function IsNullableType(ByVal type As Type) As Boolean
        Return type.IsGenericType AndAlso type.GetGenericTypeDefinition().Equals(GetType(Nullable(Of )))
    End Function


    Public Function ParseEmail(ByVal str As String) As List(Of String)
        Dim emailList = New List(Of String)
        For Each i As String In str.Split(";")
            If i.Contains("<") Then
                emailList.Add(i.Split("<")(1).Replace(">", ""))
            Else
                emailList.Add(i)
            End If
        Next
        Return emailList
    End Function

    Public Function ImageToByteArray(ByVal image As System.Drawing.Image) As Byte()
        Dim ms = New IO.MemoryStream()
        image.Save(ms, System.Drawing.Imaging.ImageFormat.Gif)
        Return ms.ToArray()
    End Function

    <Extension>
    Public Function RemoveFromEnd(value As String, suffix As String) As String
        Return If(value.EndsWith(suffix), value.Substring(0, value.Length - suffix.Length), value)
    End Function

    <Extension>
    Public Function RemoveFromStart(value As String, length As Integer) As String
        Return If(value IsNot Nothing AndAlso value.Length > length AndAlso length <> -1, value.Substring(length), value)
    End Function

    <Extension>
    Public Function RemoveFromStart(value As String, splitChar As String) As String
        If value.Length = 1 AndAlso value = splitChar Then
            Return String.Empty
        End If
        Return value.RemoveFromStart(value.IndexOf(splitChar) + 1)
    End Function

    Public Function Query(ByVal qry As String, Optional ConnectionTimeout As Integer = 30) As DataTable
        Dim dt As New DataTable
        Dim con = New SqlConnection(GetConnectionString)
        con.Open()
        Dim cmd = New SqlCommand(qry, con)
        cmd.CommandTimeout = ConnectionTimeout
        Dim adapter As New SqlDataAdapter(cmd)
        adapter.Fill(dt)
        con.Close()
        Return dt
    End Function

    Public Function Query(ByVal qry As String, ParamArray paramaters As SqlParameter()) As DataTable
        Return Query(qry, 1200, Nothing, paramaters)
    End Function

    Public Function Query(ByVal qry As String, CommandTimeout As Integer, UseCon As SqlConnection, ParamArray paramaters As SqlParameter()) As DataTable
        For Each p In paramaters
            If p.Value Is Nothing Then
                p.Value = DBNull.Value
            End If
        Next
        Dim dt As New DataTable
        Dim con As SqlConnection

        If UseCon IsNot Nothing Then
            con = UseCon
        Else
            con = New SqlConnection(GetConnectionString)
        End If

        Dim command As SqlCommand = New SqlCommand(qry, con)
        command.CommandTimeout = CommandTimeout
        If paramaters IsNot Nothing Then command.Parameters.AddRange(paramaters)
        Try
            If con.State <> ConnectionState.Open Then
                con.Open()
            End If
            Dim adapter As New SqlDataAdapter(command)
            adapter.Fill(dt)
            If UseCon Is Nothing Then
                con.Close()
            End If
            Return dt
        Finally
            command.Parameters.Clear()
        End Try
    End Function

    Public Async Function QueryAsync(ByVal qry As String, CommandTimeout As Integer, ParamArray paramaters As SqlParameter()) As Task(Of DataTable)
        For Each p In paramaters
            If p.Value Is Nothing Then
                p.Value = DBNull.Value
            End If
        Next
        Dim dt As New DataTable
        Dim con = New SqlConnection(GetConnectionString)
        Dim command As SqlCommand = New SqlCommand(qry, con)
        command.CommandTimeout = CommandTimeout
        If paramaters IsNot Nothing Then command.Parameters.AddRange(paramaters)
        Try
            con.Open()
            Dim adapter As New SqlDataAdapter(command)
            adapter.Fill(dt)
            con.Close()
            Return dt
        Finally
            command.Parameters.Clear()
        End Try
    End Function

    Private Function GetParamDataType(p As SqlParameter) As String
        Dim val = p.SqlValue.ToString.Replace("'", "''")
        Dim listNumWholeType = New List(Of Int32)(New Int32() {CInt(SqlDbType.Int), CInt(SqlDbType.BigInt), CInt(SqlDbType.SmallInt), CInt(SqlDbType.TinyInt), CInt(SqlDbType.Bit)})
        Dim listDateType = New List(Of Int32)(New Int32() {CInt(SqlDbType.Date), CInt(SqlDbType.DateTime), CInt(SqlDbType.DateTime2), CInt(SqlDbType.SmallDateTime)})
        If listNumWholeType.IndexOf(CInt(p.SqlDbType)) > -1 Then
            Return p.SqlDbType.ToString
        ElseIf listDateType.IndexOf(CInt(p.SqlDbType)) > -1 Then
            Return p.SqlDbType.ToString
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.Money) Then
            Return p.SqlDbType.ToString
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.UniqueIdentifier) Then
            Return p.SqlDbType.ToString
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.Decimal) Then
            Return p.SqlDbType.ToString + "(" + Math.Max(Math.Max(p.Precision, 1), If(p.Value IsNot Nothing, p.Value.ToString().Length, 1)).ToString + ", " + p.Scale.ToString + ")"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.Float) Then
            Return p.SqlDbType.ToString + "(" + Math.Max(Math.Max(p.Precision, 1), If(p.Value IsNot Nothing, p.Value.ToString().Length, 1)).ToString + ", " + p.Scale.ToString + ")"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.NVarChar) AndAlso (p.Value Is Nothing OrElse p.Value.GetType() = DBNull.Value.GetType() OrElse (p.Value IsNot Nothing AndAlso (p.Value Like "[0-9]*" OrElse p.Value.ToString().Length <= 4000))) AndAlso p.Size <= 4000 Then
            Return p.SqlDbType.ToString + "(" + If(p.Value Is Nothing OrElse p.Value.GetType() = DBNull.Value.GetType() OrElse p.Value.ToString() = "", 4000, If(p.Size > p.Value.ToString.Length, p.Size, p.Value.ToString.Length)).ToString + ")"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.NVarChar) Then
            Return p.SqlDbType.ToString + "(MAX)"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.VarChar) AndAlso (p.Value Is Nothing OrElse p.Value.GetType() = DBNull.Value.GetType() OrElse (p.Value IsNot Nothing AndAlso (p.Value Like "[0-9]*" OrElse p.Value.ToString().Length <= 8000))) AndAlso p.Size <= 8000 Then
            Return p.SqlDbType.ToString + "(" + If(p.Value Is Nothing OrElse p.Value.GetType() = DBNull.Value.GetType() OrElse p.Value.ToString() = "", 8000, If(p.Size > p.Value.ToString.Length, p.Size, p.Value.ToString.Length)).ToString + ")"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.VarChar) Then
            Return p.SqlDbType.ToString + "(MAX)"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.Char) Then
            Return p.SqlDbType.ToString + "(" + If(val = "NULL", 1, If(Len(val) < 1, "1", If(Len(val) > 8000, "MAX", Len(val).ToString()))) + ")"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.NChar) Then
            Return p.SqlDbType.ToString + "(" + If(val = "NULL", 1, If(Len(val) < 1, "1", If(Len(val) > 4000, "MAX", Len(val).ToString()))) + ")"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.Text) Then
            Return "VARCHAR (MAX)"
        ElseIf CInt(p.SqlDbType) = CInt(SqlDbType.NText) Then
            Return "NVARCHAR (MAX)"
        Else
            Return ""
        End If
    End Function

    Public Async Function QueryDSAsync(SqlScriptID As Int32, ByVal qry As String, CommandTimeout As Integer, UseCon As SqlConnection, ParamArray paramaters As SqlParameter()) As Task(Of DataSet)
        Dim ds As New DataSet
        ds = QueryDS(SqlScriptID, qry, CommandTimeout, UseCon, paramaters)
        Return ds
    End Function

    Public Function QueryDS(SqlScriptID As Int32, ByVal qry As String, CommandTimeout As Integer, UseCon As SqlConnection, ParamArray paramaters As SqlParameter()) As DataSet
        For Each p In paramaters
            If p.Value Is Nothing Then
                p.Value = DBNull.Value
            End If
        Next
        Dim ds As New DataSet
        Dim con As SqlConnection
        If UseCon IsNot Nothing Then
            con = UseCon
        Else
            con = New SqlConnection(GetConnectionString)
        End If
        Dim command As SqlCommand = New SqlCommand(qry, con)
        command.CommandTimeout = CommandTimeout
        If paramaters IsNot Nothing Then
            'command.Parameters.AddRange(paramaters)

            If Query(Of Boolean)($"SELECT custom.fn_FD_DeclareParamVariables({SqlScriptID})").First = True Then
                Dim paramsMatched As Boolean = True

                Dim paramSql As String = ""
                For Each p In paramaters
                    Dim val = p.SqlValue.ToString.Replace("'", "''")
                    If p.SqlDbType = SqlDbType.Int OrElse p.SqlDbType = SqlDbType.BigInt OrElse p.SqlDbType = SqlDbType.SmallInt OrElse p.SqlDbType = SqlDbType.TinyInt OrElse p.SqlDbType = SqlDbType.Bit OrElse p.SqlDbType = SqlDbType.Decimal OrElse p.SqlDbType = SqlDbType.Float OrElse p.SqlDbType = SqlDbType.Money Then
                        paramSql += If(paramSql = "", "DECLARE ", ", ") + p.ParameterName + " " + GetParamDataType(p) + " = " + If(p.SqlValue Is DBNull.Value, "NULL", If(p.SqlValue.IsNull = True, "NULL", p.SqlValue.ToString))
                    ElseIf p.SqlDbType = SqlDbType.UniqueIdentifier OrElse p.SqlDbType = SqlDbType.NVarChar OrElse p.SqlDbType = SqlDbType.VarChar OrElse p.SqlDbType = SqlDbType.Char OrElse p.SqlDbType = SqlDbType.NChar OrElse p.SqlDbType = SqlDbType.DateTime OrElse p.SqlDbType = SqlDbType.DateTime2 OrElse p.SqlDbType = SqlDbType.SmallDateTime OrElse p.SqlDbType = SqlDbType.Text OrElse p.SqlDbType = SqlDbType.NText Then
                        paramSql += If(paramSql = "", "DECLARE ", ", ") + p.ParameterName + " " + GetParamDataType(p) + " = " + If(p.SqlValue Is DBNull.Value, "NULL", If(p.SqlValue.IsNull = True, "NULL", "'" + p.SqlValue.ToString + "'"))
                    ElseIf p.SqlDbType = SqlDbType.Date Then
                        paramSql += If(paramSql = "", "DECLARE ", ", ") + p.ParameterName + " " + GetParamDataType(p) + " = " + If(p.SqlValue Is DBNull.Value, "NULL", "'" + p.SqlValue.ToString + "'")
                    Else
                        paramsMatched = False
                    End If
                Next

                If paramsMatched Then
                    command.CommandText = paramSql + vbCrLf + command.CommandText
                Else
                    command.Parameters.AddRange(paramaters)
                End If
            Else
                command.Parameters.AddRange(paramaters)
            End If
        End If

        Try
            If con.State <> ConnectionState.Open Then
                con.Open()
            End If
            Dim adapter As New SqlDataAdapter(command)
            adapter.Fill(ds)

            If UseCon Is Nothing Then
                con.Close()
            End If

            Return ds
        Finally
            command.Parameters.Clear()
        End Try
    End Function

    Public Function Query(Of T)(qry As String, Optional parameter As Object = Nothing) As IEnumerable(Of T)
        Dim con = New SqlConnection(GetConnectionString)
        Return con.Query(Of T)(qry, parameter, commandTimeout:=1200)
    End Function

    Public Function QueryAsync(Of T)(qry As String, Optional parameter As Object = Nothing) As Task(Of IEnumerable(Of T))
        Dim con = New SqlConnection(GetConnectionString)
        Return con.QueryAsync(Of T)(qry, parameter, commandTimeout:=1200)
    End Function

    Public Function QuerySingleOrDefault(Of T)(qry As String, Optional parameter As Object = Nothing) As T
        Dim con = New SqlConnection(GetConnectionString)
        Return con.QuerySingleOrDefault(Of T)(qry, parameter)
    End Function

    Public Function UpdateSql(qry As String, ParamArray paramaters As SqlParameter()) As Integer
        Logger.Information("Entering UpdateSqlAsync with qry {Sql} Params: {Parameters}", qry, paramaters)
        Dim con = New SqlConnection(GetConnectionString)
        Dim command As SqlCommand = New SqlCommand(qry, con)
        command.CommandTimeout = 1000
        If paramaters IsNot Nothing Then command.Parameters.AddRange(paramaters)
        Try
            con.Open()
            Return command.ExecuteNonQuery()
        Catch ex As Exception
            Logger.Error(ex, "Error in UpdateSqlAsync Qry: {Sql} Params: {Parameters}", qry, paramaters)
            Throw
        Finally
            con.Close()
            command.Parameters.Clear()
        End Try
    End Function

    Public Async Function UpdateSqlAsync(qry As String, UseCon As SqlConnection, ParamArray paramaters As System.Data.SqlClient.SqlParameter()) As Task(Of Integer)
        Logger.Information("Entering UpdateSqlAsync with qry {Sql} Params: {Parameters}", qry, paramaters)
        Dim con As SqlConnection
        If UseCon IsNot Nothing Then
            con = UseCon
        Else
            con = New SqlConnection(GetConnectionString)
        End If

        Dim command As SqlCommand = New SqlCommand(qry, con)
        command.CommandTimeout = 1000

        'call always event, so we can tell what was last result
        'If CallActionAfterStatementComplete IsNot Nothing Then
        _LastUpdateRecordCount = 0
        AddHandler command.StatementCompleted, AddressOf StatementCompleted
        'End If

        If paramaters IsNot Nothing Then command.Parameters.AddRange(paramaters)
        Try
            If con.State <> ConnectionState.Open Then
                con.Open()
            End If
            Return Await command.ExecuteNonQueryAsync()
        Catch ex As Exception
            Logger.Error(ex, "Error in UpdateSqlAsync Qry: {Sql} Params: {Parameters}", qry, paramaters)
            Throw
        Finally

            If UseCon Is Nothing Then
                con.Close()
            End If
            command.Parameters.Clear()
        End Try
    End Function

    Public Async Function ExecuteStoredProcedureAsync(name As String, ParamArray paramaters() As System.Data.SqlClient.SqlParameter) As Task(Of Integer)
        Logger.Information("Entering UpdateSqlAsync with qry {Sql} Params: {Parameters}", name, paramaters)
        Dim con = New SqlConnection(GetConnectionString)
        Dim command As SqlCommand = New SqlCommand(name, con)
        command.CommandType = CommandType.StoredProcedure
        command.CommandTimeout = 1000
        If paramaters IsNot Nothing Then command.Parameters.AddRange(paramaters)
        Try
            con.Open()
            Dim t = Await command.ExecuteNonQueryAsync()
            Logger.Debug("ExecuteStoredProcedureAsync {Name} {Params} {Result}", name, paramaters, t)
            Return t
        Catch ex As Exception
            Logger.Error(ex, "Error in UpdateSqlAsync Qry: {Sql} Params: {Parameters}", name, paramaters)
            Throw
        Finally
            con.Close()
            command.Parameters.Clear()
        End Try
    End Function

    Public Function GetLastQuarterEndDate(Optional _date As DateTime? = Nothing) As Date
        Dim curDate As DateTime = If(_date, DateTime.Now)

        If curDate.Month >= 1 AndAlso curDate.Month <= 3 Then
            ' January - March
            Return New DateTime(curDate.Year - 1, 12, 31)
        ElseIf curDate.Month >= 4 AndAlso curDate.Month <= 6 Then
            ' April - June
            Return New DateTime(curDate.Year, 3, 31)
        ElseIf curDate.Month >= 7 AndAlso curDate.Month <= 9 Then
            ' July - September
            Return New DateTime(curDate.Year, 6, 30)
        ElseIf curDate.Month >= 10 AndAlso curDate.Month <= 12 Then
            ' October - December
            Return New DateTime(curDate.Year, 9, 30)
        Else
            Throw New Exception("please enter a valid date")
        End If
    End Function

    Public Function GetQuarterStartDate(Optional _date As DateTime? = Nothing) As DateTime
        Dim curDate As DateTime = If(_date, DateTime.Now)

        If curDate.Month >= 1 AndAlso curDate.Month <= 3 Then
            ' January - March
            Return New DateTime(curDate.Year, 1, 1)
        ElseIf curDate.Month >= 4 AndAlso curDate.Month <= 6 Then
            ' April - June
            Return New DateTime(curDate.Year, 4, 1)
        ElseIf curDate.Month >= 7 AndAlso curDate.Month <= 9 Then
            ' July - September
            Return New DateTime(curDate.Year, 7, 1)
        ElseIf curDate.Month >= 10 AndAlso curDate.Month <= 12 Then
            ' October - December
            Return New DateTime(curDate.Year, 10, 1)
        Else
            Throw New Exception("please enter a valid date")
        End If
    End Function

    Public Function GetQuarterEndDate(Optional _date As DateTime? = Nothing) As DateTime
        Dim curDate As DateTime = If(_date, DateTime.Now)

        If curDate.Month >= 1 AndAlso curDate.Month <= 3 Then
            ' January - March
            Return New DateTime(curDate.Year, 3, DateTime.DaysInMonth(curDate.Year, 3))
        ElseIf curDate.Month >= 4 AndAlso curDate.Month <= 6 Then
            ' April - June
            Return New DateTime(curDate.Year, 6, DateTime.DaysInMonth(curDate.Year, 6))
        ElseIf curDate.Month >= 7 AndAlso curDate.Month <= 9 Then
            ' July - September
            Return New DateTime(curDate.Year, 9, DateTime.DaysInMonth(curDate.Year, 9))
        ElseIf curDate.Month >= 10 AndAlso curDate.Month <= 12 Then
            ' October - December
            Return New DateTime(curDate.Year, 12, DateTime.DaysInMonth(curDate.Year, 12))
        Else
            Throw New Exception("please enter a valid date")
        End If
    End Function

    Public Function GetQuarter(Optional _date As DateTime? = Nothing) As Integer
        Dim curDate As DateTime = If(_date, DateTime.Now)

        If curDate.Month >= 1 AndAlso curDate.Month <= 3 Then
            ' January - March
            Return 1
        ElseIf curDate.Month >= 4 AndAlso curDate.Month <= 6 Then
            ' April - June
            Return 2
        ElseIf curDate.Month >= 7 AndAlso curDate.Month <= 9 Then
            ' July - September
            Return 3
        ElseIf curDate.Month >= 10 AndAlso curDate.Month <= 12 Then
            ' October - December
            Return 4
        Else
            Throw New Exception("please enter a valid date")
        End If
    End Function

    Public Function GetQuarterStartDate(year As Integer, qtr As Integer) As Date
        Dim month As Integer = 0
        Select Case qtr
            Case 1
                month = 1
            Case 2
                month = 4
            Case 3
                month = 7
            Case 4
                month = 10
        End Select

        Return New DateTime(year, month, 1)
    End Function

    Public Function GetQuarterEndDate(year As Integer, qtr As Integer) As Date
        Dim month As Integer = 0
        Select Case qtr
            Case 1
                month = 3
            Case 2
                month = 6
            Case 3
                month = 9
            Case 4
                month = 12
        End Select

        Return New DateTime(year, month, DateTime.DaysInMonth(year, month))
    End Function

    Public Function Greeting() As String
        If DateTime.Now.Hour < 12 Then
            Return "Good Morning"
        ElseIf DateTime.Now.Hour < 17 Then
            Return "Good Afternoon"
        Else
            Return "Good Evening"
        End If
    End Function



    'Public Async Sub CheckForUpdate(confirmUpdate As Boolean, displayAlreadyUptoDateMessage As Boolean)
    '    Try
    '        Logger.Information("Entering CheckForUpdate")
    '        Dim info As UpdateCheckInfo = Nothing

    '        If (Not ApplicationDeployment.IsNetworkDeployed) Then
    '            XtraMessageBox.Show("Application is not network deployed")
    '            Exit Sub
    '        End If
    '        Dim AD As ApplicationDeployment = ApplicationDeployment.CurrentDeployment

    '        Try
    '            info = AD.CheckForDetailedUpdate()
    '        Catch dde As DeploymentDownloadException
    '            DisplayMessageBox(String.Format("The new version of the application cannot be downloaded at this time. {0}{0}Please check your network connection, or try again later. Error: {1}", ControlChars.Lf, dde.Message))
    '            Return
    '        Catch ioe As InvalidOperationException
    '            DisplayMessageBox("This application cannot be updated. It is likely not a ClickOnce application. Error: " & ioe.Message)
    '            Return
    '        End Try

    '        If (info.UpdateAvailable) Then
    '            If confirmUpdate Then
    '                Beep()
    '                MainForm.WindowState = FormWindowState.Maximized
    '                MainForm.TopMost = True
    '                MainForm.fpApplicationUpdateNotification.Options.VertIndent = MainForm.RibbonControl1.Height
    '                MainForm.fpApplicationUpdateNotification.ShowPopup()
    '                Await Task.Delay(TimeSpan.FromSeconds(3))
    '                MainForm.TopMost = False
    '                Exit Sub
    '            End If

    '            Try
    '                MainForm.ShowWaitForm()
    '                Application.DoEvents()
    '                Await Task.Delay(500)
    '                Application.DoEvents()
    '                AD.Update()
    '                MainForm.CloseWaitForm
    '                MainForm.WindowState = FormWindowState.Maximized
    '                MainForm.TopMost = True
    '                Await Task.Delay(TimeSpan.FromSeconds(1))
    '                MainForm.TopMost = False
    '                XtraMessageBox.Show("The application has been upgraded, and will now restart.")
    '                Logger.Information("Application has been updated.")
    '                Try
    '                    Dim pi = New ProcessStartInfo(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) & "\Microsoft\Windows\Start Menu\Programs\Brands Paycheck\Brands Front Desk.appref-ms", """{0},{1}""".FormatWith(UserName, Password))
    '                    Dim p As Process = New Process With {.EnableRaisingEvents = True, .StartInfo = pi}
    '                    p.Start()
    '                    Application.Exit()
    '                Catch ex As Exception
    '                    Logger.Error(ex, "Error starting application from start menu & passing in username and password. will just restart.")
    '                    Application.Restart()
    '                End Try
    '            Catch dde As DeploymentDownloadException
    '                MainForm.CloseWaitForm
    '                DisplayMessageBox("Cannot install the latest version of the application. " & ControlChars.Lf & ControlChars.Lf & "Please check your network connection, or try again later.")
    '                Return
    '            End Try
    '        Else
    '            If displayAlreadyUptoDateMessage Then XtraMessageBox.Show("Application is up to date.")
    '        End If
    '    Catch ex As Exception
    '        DisplayErrorMessage("Error checking for update", ex, "Error")
    '        MainForm.fpApplicationUpdateNotification.HidePopup()
    '    End Try
    'End Sub

    Public Async Sub CheckForUpdate(confirmUpdate As Boolean, displayAlreadyUptoDateMessage As Boolean)
        Try
            Logger.Information("Entering CheckForUpdate")

            ' Access ClickOnce deployment properties via environment variables
            Dim isNetworkDeployed As Boolean = CBool(Environment.GetEnvironmentVariable("ClickOnce_IsNetworkDeployed"))
            Dim updateAvailable As Boolean = CBool(Environment.GetEnvironmentVariable("ClickOnce_UpdateAvailable")) ' Assuming this variable exists and is set by the launcher

            If Not isNetworkDeployed Then
                XtraMessageBox.Show("Application is not network deployed.")
                Exit Sub
            End If


            ' In .NET 9, you can't directly check for detailed update or trigger the update programmatically.
            ' ClickOnce handles the update process automatically based on your update strategy.
            ' However, you can check if an update is available based on environment variables.







            If updateAvailable Then
                If confirmUpdate Then
                    Beep()
                    MainForm.WindowState = FormWindowState.Maximized
                    MainForm.TopMost = True
                    MainForm.fpApplicationUpdateNotification.Options.VertIndent = MainForm.RibbonControl1.Height
                    MainForm.fpApplicationUpdateNotification.ShowPopup()
                    Await Task.Delay(TimeSpan.FromSeconds(3))
                    MainForm.TopMost = False
                    Exit Sub
                End If

                ' ClickOnce will handle the update on the next launch if configured for automatic updates
                ' based on your deployment manifest.
                ' You can provide a notification to the user that an update is available.
                XtraMessageBox.Show("An update is available. The application will update on the next launch.")

            End If

        Catch ex As Exception
            Logger.Error(ex, "Error checking for update.")
            ' Handle other potential errors
        End Try
    End Sub


    Public Sub SetUdfValue(name As String, value As String)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim _udf = db.UDFs.SingleOrDefault(Function(u) u.name = name)
        If _udf Is Nothing Then Throw New Exception("UDF name [{0}] does not exist.".FormatWith(name))
        Logger.Information("Setting {UdfName} to {Value}", name, value)
        _udf.value = value
        _udf.ChgUser = UserName
        _udf.ChgDate = DateTime.Now
        db.SaveChanges()
    End Sub

    Public Function GetUdfValue(name As String) As String
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim _udf = db.UDFs.SingleOrDefault(Function(u) u.name = name)
            If _udf Is Nothing Then Throw New Exception("UDF name [{0}] does not exist in UDF table.".FormatWith(name))
            Return _udf.value
        End Using
    End Function

    Public Function GetUdfValue_AsLong(name As String) As Long?
        Dim udf = GetUdfValue(name)
        Dim results As Long
        If udf.IsNullOrWhiteSpace Then
            Return Nothing
        ElseIf Long.TryParse(udf, results) Then
            Return results
        Else
            Throw New Exception($"Invalid UDF. Name: {name} Value: {udf} isn't a valid long value.")
        End If
    End Function

    Public Function GetUdfValue_AsDecimal(name As String) As Decimal?
        Dim udf = GetUdfValue(name)
        Dim results As Decimal
        If udf.IsNullOrWhiteSpace Then
            Return Nothing
        ElseIf Decimal.TryParse(Nz(udf, ""), results) Then
            Return results
        Else
            Throw New Exception($"Invalid UDF. Name: {name} Value: {udf} isn't a valid Decimal value.")
        End If
    End Function

    Public Function GetUdfValue_AsInteger(name As String) As Integer?
        Dim udf = GetUdfValue(name)
        Dim results As Integer
        If udf.IsNullOrWhiteSpace Then
            Return Nothing
        ElseIf Integer.TryParse(udf, results) Then
            Return results
        Else
            Throw New Exception($"Invalid UDF. Name: {name} Value: {udf} isn't a valid Integer value.")
        End If
    End Function

    Public Function GetUdfValueSplitted(name As String) As String()
        Dim value = GetUdfValue(name).Replace(vbCrLf, "~").Split("~").ToArray
        Return value
    End Function

    Public Function CleanMassEmail(Emails As String) As String
        Return Strings.Join(CleanMassEmail(Emails.Replace(":", ";").Replace(";", ",").Split(",")).Where(Function(e) e.IsNotNullOrWhiteSpace).ToArray(), ",").Trim()
    End Function

    Public Function CleanMassEmail(Email() As String) As String()
        Dim lstEmail As New List(Of String)
        For Each e In Email.Where(Function(em) em.IsNotNullOrWhiteSpace)
            lstEmail.Add(CleanEmail(e))
        Next

        Return lstEmail.ToArray()
    End Function

    Public Function CleanEmail(Email As String) As String
        Return Regex.Replace(Email, "[^A-z0-9@,._]", "", RegularExpressions.RegexOptions.Compiled)
    End Function

    Public Function GetDataTimePropertiesWithMinValue(obj As Object) As List(Of Reflection.PropertyInfo)
        Dim minValueProperties As List(Of Reflection.PropertyInfo) = New List(Of Reflection.PropertyInfo)
        Dim properties = obj.GetType().GetProperties()
        For Each prop In properties
            If prop.PropertyType Is GetType(DateTime) AndAlso prop.GetValue(obj) = DateTime.MinValue Then
                minValueProperties.Add(prop)
            End If
        Next
        Return minValueProperties
    End Function

    Public Function GetDataTimePropertiesWithMinValue(obj As Object, properties As List(Of Reflection.PropertyInfo)) As String
        Dim message = "Error. The following properties has invalid DateTime values."
        For Each prop In properties
            message &= $"\n{prop.Name}: {prop.GetValue(obj)}"
        Next
        message &= "\nPlease fix the above properties and try again"
        Return message
    End Function

    Public Function GetMonthFromQuarter(qtr As Integer) As Integer
        Select Case qtr
            Case 1
                Return 3
            Case 2
                Return 6
            Case 3
                Return 9
            Case 4
                Return 12
            Case Else
                Throw New ArgumentException($"Invalid Qtr: {qtr}")
        End Select
    End Function
End Module

'class found in https://msdn.microsoft.com/en-us/library/01escwtf(v=vs.110).aspx?cs-save-lang=1&cs-lang=vb#code-snippet-1
Public Class EmailUtilities
    Shared invalid As Boolean = False

    Public Shared Function IsValidEmail(strIn As String) As Boolean
        invalid = False
        If String.IsNullOrEmpty(strIn) Then Return False

        ' Use IdnMapping class to convert Unicode domain names.
        Try
            strIn = Regex.Replace(strIn, "(@)(.+)$", AddressOf DomainMapper,
                                RegexOptions.None, TimeSpan.FromMilliseconds(200))
        Catch e As RegexMatchTimeoutException
            Return False
        End Try

        If invalid Then Return False

        ' Return true if strIn is in valid e-mail format.
        Try
            Return Regex.IsMatch(strIn,
                 "^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                 "(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-\w]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                 RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250))
        Catch e As RegexMatchTimeoutException
            Return False
        End Try
    End Function

    Private Shared Function DomainMapper(match As Match) As String
        ' IdnMapping class with default property values.
        Dim idn As New IdnMapping()

        Dim domainName As String = match.Groups(2).Value
        Try
            domainName = idn.GetAscii(domainName)
        Catch e As ArgumentException
            invalid = True
        End Try
        Return match.Groups(1).Value + domainName
    End Function
End Class

Partial Public Class Email
    Public ReadOnly Property BodyReadOnly As String
        Get
            Return Body
        End Get
    End Property
End Class

Public Enum ShipOption
    None = 0
    FeDex = 1
    UPS = 2
    Mail = 3
    Pickup = 4
    ShipWith = 5
    CarService = 6
    DirectEEMailing = 7
    Other = 99
End Enum