﻿Imports System.ComponentModel
Imports Brands_FrontDesk
Imports DevExpress.XtraEditors
Public Class frmEditCoContact
    Sub New()
        InitializeComponent()
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Contact As prc_GetAllRelatedContactsResult

    Private db As dbEPDataDataContext
    Private companiesDataSource As List(Of view_CompanySumarry)
    Private co_contacts As List(Of co_contact)
    Private emp As EMPLOYEE
    Private empOptions As EmployeeOption
    Private comp As COMPANY


    Public Sub InitializeForm()
        db = New dbEPDataDataContext(GetConnectionString)
        Me.cboSpecialType.Properties.Items.Add(String.Empty)
        Me.cboSpecialType.Properties.Items.AddRange(GetUdfValueSplitted("RelatedContactTitle"))

        If Contact IsNot Nothing Then
            Me.Text = "Edit Contact"
            If Contact.EmpNum.GetValueOrDefault <> 0 Then
                Me.emp = (From A In db.EMPLOYEEs Where A.CONUM = Contact.EmpConum AndAlso A.EMPNUM = Contact.EmpNum).First
                Me.empOptions = db.EmployeeOptions.SingleOrDefault(Function(e) e.CoNum = Contact.EmpConum AndAlso e.EmpNum = Contact.EmpNum)
                If empOptions Is Nothing Then
                    empOptions = New EmployeeOption With {.CoNum = Contact.EmpConum, .EmpNum = Contact.EmpNum}
                    db.EmployeeOptions.InsertOnSubmit(empOptions)
                End If
                Me.L_NAMETextEdit.EditValue = emp.L_NAME
                Me.M_NAMETextEdit.EditValue = emp.M_NAME
                Me.F_NAMETextEdit.EditValue = emp.F_NAME
                Me.User_emailTextEdit.EditValue = emp.user_email
                Me.Contact_homephoneTextEdit.EditValue = emp.PH_NO
                Me.Contact_pagerTextEdit.EditValue = emp.contact_cellphone
                Me.txtExt.EditValue = empOptions.Extension
                Me.txtNotes.EditValue = empOptions.Notes
                Me.ccbePermission.EditValue = empOptions.Permission
            Else
                Me.comp = (From A In db.COMPANies Where A.CONUM = Contact.CoNum).First
                Me.Text = "Edit Company Conatct"
                Me.lblName.Text = "PR Conatct"
                Me.L_NAMETextEdit.EditValue = comp.PR_CONTACT
                Me.Contact_homephoneTextEdit.EditValue = comp.CO_PHONE
                Me.txtExt.EditValue = comp.CO_EXTENSION
                Me.Contact_pagerTextEdit.EditValue = comp.CO_MODEM
                Me.User_emailTextEdit.EditValue = comp.CO_EMAIL

                Me.F_NAMETextEdit.Enabled = False
                Me.M_NAMETextEdit.Enabled = False
                Me.txtNotes.Enabled = False
                Me.ccbePermission.Enabled = False
                Me.GroupControl1.Enabled = False
                gcLinkedCompanies.Enabled = False
            End If

            Me.cboSpecialType.EditValue = Contact.USERDEF23


            co_contacts = db.co_contacts.Where(Function(c) c.empnum = Contact.EmpNum AndAlso c.empconum = Contact.EmpConum).ToList()
            companiesDataSource = db.view_CompanySumarries.Where(Function(c) co_contacts.Select(Function(co) co.conum).Contains(c.CONUM)).ToList

            ccbePermission.Properties.DataSource = GetUdfValueSplitted("EmployeePermission")
        Else
            If co_contacts Is Nothing Then co_contacts = New List(Of co_contact)
            If companiesDataSource Is Nothing Then companiesDataSource = New List(Of view_CompanySumarry)
            Me.Text = "Add New Contact"
        End If
        riCompanies.DataSource = companiesDataSource
        gcLinkedCompanies.DataSource = co_contacts
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If comp IsNot Nothing Then
            comp.PR_CONTACT = Me.L_NAMETextEdit.EditValue
            comp.CO_EMAIL = Me.User_emailTextEdit.EditValue
            comp.CO_PHONE = Me.Contact_homephoneTextEdit.EditValue
            comp.CO_EXTENSION = Me.txtExt.EditValue
            comp.CO_MODEM = Me.Contact_pagerTextEdit.EditValue
        Else
            Dim IsNewRecord = emp Is Nothing
            If IsNewRecord Then
                Using sDB As New dbEPDataDataContext(GetConnectionString)
                    emp = (From A In db.EMPLOYEEs Where A.CONUM = 720 AndAlso A.EMPNUM = 275).SingleOrDefault
                    If emp Is Nothing Then
                        DisplayMessageBox("Source employee not found (720,275)")
                        Return
                    End If
                    emp = emp.CloneEntity
                End Using
                Dim NextNum = (From A In db.EMPLOYEEs Where A.CONUM = 720 Select A.EMPNUM).Max() + 1
                emp.CONUM = 720 ' Me.CoNum
                emp.EMPNUM = NextNum
                emp.F_NAME = Me.F_NAMETextEdit.EditValue
                emp.M_NAME = Me.M_NAMETextEdit.EditValue
                emp.L_NAME = Me.L_NAMETextEdit.EditValue
                emp.user_email = Me.User_emailTextEdit.EditValue
                emp.PH_NO = Me.Contact_homephoneTextEdit.EditValue
                emp.contact_cellphone = Me.Contact_pagerTextEdit.EditValue
                emp.USERDEF23 = Me.cboSpecialType.EditValue
                db.EMPLOYEEs.InsertOnSubmit(emp)
                db.SubmitChanges()

                For Each co In co_contacts
                    If co.empnum = 0 Then
                        db.co_contacts.InsertOnSubmit(co)
                    End If
                    co.empconum = 720
                    co.empnum = NextNum
                Next
                empOptions = New EmployeeOption With {.CoNum = emp.CONUM, .EmpNum = emp.EMPNUM}
                db.EmployeeOptions.InsertOnSubmit(empOptions)
            Else
                emp.F_NAME = Me.F_NAMETextEdit.EditValue
                emp.M_NAME = Me.M_NAMETextEdit.EditValue
                emp.L_NAME = Me.L_NAMETextEdit.EditValue
                emp.user_email = Me.User_emailTextEdit.EditValue
                emp.PH_NO = Me.Contact_homephoneTextEdit.EditValue
                emp.contact_cellphone = Me.Contact_pagerTextEdit.EditValue
                emp.USERDEF23 = Me.cboSpecialType.EditValue
            End If
            'emp.USERDEF24 = Me.txtPermissions.EditValue
            empOptions.Notes = Me.txtNotes.Text
            empOptions.Extension = Me.txtExt.Text
            empOptions.Permission = Me.ccbePermission.EditValue
        End If
        If db.SaveChanges Then Me.DialogResult = DialogResult.OK
        db.Dispose()
    End Sub

    Friend Sub LinkContactToAllCompanies(CoNums As List(Of Decimal))
        If Contact IsNot Nothing AndAlso Not Contact.EmpNum.HasValue Then Throw New InvalidOperationException("Can't add company contacts, Only Employee contacts can be added")
        For Each co In CoNums
            Dim newContact = co_contacts.SingleOrDefault(Function(c) c.conum = co)
            If newContact Is Nothing Then
                newContact = New co_contact With {.conum = co}
                If Contact IsNot Nothing Then
                    newContact.empconum = Contact.EmpConum
                    newContact.empnum = Contact.EmpNum
                    db.co_contacts.InsertOnSubmit(newContact)
                End If
                co_contacts.Add(newContact)
            End If
            If Contact IsNot Nothing Then
                If Contact.IsPayroll Then newContact.pr_contact_Bool = True
                If Contact.IsHr Then newContact.hr_contact_Bool = True
                If Contact.IsEndOfPeriod Then newContact.qyend_contact_Bool = True
                If Contact.IsCpa Then newContact.co_cpa_Bool = True
                If Contact.IsPrincipal Then newContact.co_principal_Bool = True
            End If
        Next
        companiesDataSource.AddRange(db.view_CompanySumarries.Where(Function(c) CoNums.Contains(c.CONUM)).ToList)
        gvLinkedCompanies.RefreshData()
    End Sub

    Friend Sub RemoveContact(CoNums As List(Of Decimal))
        If Contact Is Nothing Then Throw New ArgumentNullException("Contact is null")
        If Not Contact.EmpNum.HasValue Then Throw New InvalidOperationException("Can't delete company contacts, Only Employee contacts can be removed")
        For Each co In CoNums
            Dim contactToRemove = db.co_contacts.SingleOrDefault(Function(c) c.conum = co AndAlso c.empnum = Contact.EmpNum AndAlso c.empconum = Contact.EmpConum)
            If contactToRemove IsNot Nothing Then
                db.co_contacts.DeleteOnSubmit(contactToRemove)
            End If
        Next
        db.SubmitChanges()
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub User_emailTextEdit_Validated(sender As Object, e As EventArgs) Handles User_emailTextEdit.Validated
        Try
            If User_emailTextEdit.Text.IsNullOrWhiteSpace OrElse Contact IsNot Nothing Then Exit Sub
            Dim existingEmp = db.EMPLOYEEs.FirstOrDefault(Function(em) {700, 720}.Contains(em.CONUM) AndAlso em.user_email = User_emailTextEdit.Text)
            If existingEmp IsNot Nothing Then
                If XtraMessageBox.Show("An existing contact with the same email address was found, would you like to view the contact now?", "Edit Existing Contact", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    Dim newContact = New prc_GetAllRelatedContactsResult With {.IsPayroll = False, .IsPrincipal = False, .IsCpa = False, .IsEndOfPeriod = False, .IsHr = False}
                    newContact.EmpNum = existingEmp.EMPNUM
                    newContact.EmpConum = existingEmp.CONUM
                    Dim p = Me.Location
                    Dim half = Me.Size.Width / 2
                    Me.Location = New Point(Me.Location.X - half, Me.Location.Y)
                    Me.gcExistingContactMessage.Visible = True
                    Using frm = New frmEditCoContact() With {.Contact = newContact, .StartPosition = FormStartPosition.Manual, .Location = New Point(p.X + half, p.Y)}
                        frm.InitializeForm()
                        frm.LinkContactToAllCompanies(co_contacts.Select(Function(c) c.conum).ToList)
                        If frm.ShowDialog = DialogResult.OK Then
                            DialogResult = DialogResult.Cancel
                        Else
                            Me.gcExistingContactMessage.Visible = False
                            Me.Location = p
                        End If
                    End Using
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error matching email to any existing contact", ex)
        End Try
    End Sub

    Private Sub Contact_homephoneTextEdit_Validated(sender As Object, e As EventArgs) Handles Contact_homephoneTextEdit.Validated
        Try
            If Contact_homephoneTextEdit.Text.IsNullOrWhiteSpace OrElse Contact IsNot Nothing Then Exit Sub
            Dim existingEmp = db.EMPLOYEEs.FirstOrDefault(Function(em) {700, 720}.Contains(em.CONUM) AndAlso em.PH_NO = Contact_homephoneTextEdit.Text)
            If existingEmp IsNot Nothing Then
                If XtraMessageBox.Show("An existing contact with the same email address was found, would you like to view the contact now?", "Edit Existing Contact", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    Dim newContact = New prc_GetAllRelatedContactsResult With {.IsPayroll = False, .IsPrincipal = False, .IsCpa = False, .IsEndOfPeriod = False, .IsHr = False}
                    newContact.EmpNum = existingEmp.EMPNUM
                    newContact.EmpConum = existingEmp.CONUM
                    Dim p = Me.Location
                    Dim half = Me.Size.Width / 2
                    Me.gcExistingContactMessage.Visible = True
                    Me.Location = New Point(Me.Location.X - half, Me.Location.Y)
                    Using frm = New frmEditCoContact() With {.Contact = newContact, .StartPosition = FormStartPosition.Manual, .Location = New Point(p.X + half, p.Y)}
                        frm.InitializeForm()
                        frm.LinkContactToAllCompanies(co_contacts.Select(Function(c) c.conum).ToList)
                        If frm.ShowDialog = DialogResult.OK Then
                            DialogResult = DialogResult.Cancel
                        Else
                            Me.gcExistingContactMessage.Visible = False
                            Me.Location = p
                        End If
                    End Using
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error matching email to any existing contact", ex)
        End Try
    End Sub

    Private Sub gvLinkedCompanies_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvLinkedCompanies.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.InRow Then
            Dim row As co_contact = gvLinkedCompanies.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add This Contact to New Co#", Sub() AddLinkedCompany(), My.Resources.add_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem($"Remove This Contact (From Co#: {row.conum})", Sub() RemoveLinkedCompany(row, e.HitInfo.RowHandle), My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub AddLinkedCompany()
        Using frm = New frmSelectCompany
            If frm.ShowDialog() = DialogResult.OK Then
                If co_contacts.Select(Function(c) c.conum).Contains(frm.SelectedCompany.CONUM) Then
                    XtraMessageBox.Show($"Company {frm.SelectedCompany.CONUM} is already linked")
                    Return
                Else
                    LinkContactToAllCompanies(New List(Of Decimal)({frm.SelectedCompany.CONUM}))
                End If
            End If
        End Using
    End Sub

    Private Sub RemoveLinkedCompany(row As co_contact, rowHandle As Integer)
        If XtraMessageBox.Show($"Are you sure you would like to remove Co#: {row.conum}?", "Remove Linked Company", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
            gvLinkedCompanies.DeleteRow(rowHandle)
            db.co_contacts.DeleteOnSubmit(row)
        End If
    End Sub

    Private Sub cePayroll_CheckedChanged(sender As Object, e As EventArgs) Handles cePrincipal.CheckedChanged, cePayroll.CheckedChanged, ceHr.CheckedChanged, ceEndOfPeriod.CheckedChanged, ceCpaBookeeper.CheckedChanged
        Dim isChecked = DirectCast(sender, CheckEdit).Checked
        If sender Is cePayroll Then
            co_contacts.ForEach(Sub(c) c.pr_contact_Bool = isChecked)
        ElseIf sender Is ceHr Then
            co_contacts.ForEach(Sub(c) c.hr_contact_Bool = isChecked)
        ElseIf sender Is ceEndOfPeriod Then
            co_contacts.ForEach(Sub(c) c.qyend_contact_Bool = isChecked)
        ElseIf sender Is ceCpaBookeeper Then
            co_contacts.ForEach(Sub(c) c.co_cpa_Bool = isChecked)
        ElseIf sender Is cePrincipal Then
            co_contacts.ForEach(Sub(c) c.co_principal_Bool = isChecked)
        End If
        gcLinkedCompanies.RefreshDataSource()
    End Sub

    Private Sub frmEditCoContact_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
End Class