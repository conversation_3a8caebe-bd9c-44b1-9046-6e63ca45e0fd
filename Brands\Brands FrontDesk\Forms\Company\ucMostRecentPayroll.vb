﻿Public Class ucMostRecentPayroll

    Private DB As dbEPDataDataContext
    Private ReadOnly _coNum As Decimal
    Private IsPaperless As String
    Private ReadOnly _payrolls As List(Of PAYROLL)
    Sub New(coNum As Decimal, payrolls As List(Of PAYROLL), _IsPaperless As String, _Transit As String)
        InitializeComponent()
        _payrolls = payrolls
        _coNum = coNum
        IsPaperless = _IsPaperless
        teTransit.Text = _Transit
    End Sub

    Private Sub ucMostRecentPayroll_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DB = New dbEPDataDataContext(GetConnectionString)
        PAYROLLBindingSource.DataSource = _payrolls
        PAYROLLBindingSource.MoveLast()
        'Dim lastOnlinePayroll = _payrolls.OrderByDescending(Function(p) p.PRNUM).Take(3).LastOrDefault(Function(p) p.OPNAME.StartsWith("952") OrElse p.ENTRY_TYPE = "Xpress")
        'If lastOnlinePayroll IsNot Nothing Then
        '    If lastOnlinePayroll.OPNAME.StartsWith("952") Then
        '        teLastEntryType.Text = "Platinum Pay"
        '    ElseIf lastOnlinePayroll.ENTRY_TYPE = "Xpress" Then
        '        teLastEntryType.Text = "PPX"
        '    Else
        '        teLastEntryType.Text = "N/A"
        '    End If

        'End If
        teLastEntryType.Text = Query(Of String)($"SELECT custom.fn_GetCoOnline({_coNum})").FirstOrDefault()
    End Sub

    Public Function GetCurrentPayroll() As PAYROLL
        Return PAYROLLBindingSource.Current
    End Function

    Private Sub DataNavigator1_PositionChanged(sender As Object, e As EventArgs) Handles DataNavigator1.PositionChanged
        Try
            If PAYROLLBindingSource.Count > 0 Then
                Dim payroll = CType(PAYROLLBindingSource.Current, PAYROLL)
                Dim LastScans = (From A In DB.view_Deliveries Where A.CoNum = _coNum AndAlso A.PayrollNum = payroll.PRNUM Order By A.ID Descending).ToList
                If LastScans.Count > 1 Then
                    tePickup.Text = ""
                    tePickupBy.Text = "Multiple (Check Shipping History)"
                    Return
                End If

                Dim LastScan = LastScans.FirstOrDefault
                If LastScan IsNot Nothing Then
                    tePickup.Text = LastScan.ScannedDate.ToString("g")
                    tePickupBy.Text = LastScan.DeliverBy
                Else
                    tePickupBy.Text = IsPaperless
                    tePickup.Text = ""
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error viewing payroll", ex)
        End Try
    End Sub
End Class
