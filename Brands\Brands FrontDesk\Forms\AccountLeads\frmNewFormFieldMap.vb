﻿Imports Brands.Core.AccountLeads.Models
Imports DevExpress.XtraEditors

Public Class frmNewFormFieldMap
    Private Property ParameterMappings As List(Of FieldMap)


    Private _Questions As List(Of FormQuestions)
    Public ReadOnly Property MapToParameterMode As Boolean
    Public ReadOnly Property AddSub As Action(Of FieldMap)

    Public Sub New(mapToParameterMode As Boolean, addSub As Action(Of FieldMap))
        InitializeComponent()
        TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        TabbedControlGroup2.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        Me.MapToParameterMode = mapToParameterMode
        Me.AddSub = addSub
        If mapToParameterMode Then TabbedControlGroup2.SelectedTabPageIndex = 1
        TabbedControlGroup1.SelectedTabPageIndex = 0
    End Sub

    Private Sub frmNewFormFieldMap_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Dim list = modGlobals.Query(Of String)("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'AccountLeads' AND TABLE_SCHEMA = 'custom'")
            list.ToList().ForEach(Function(c) cbeColumn.Properties.Items.Add(c))

            If Not MapToParameterMode Then
                'load parameters
                Dim json = GetUdfValue("JotForms Parameter Mapping")
                ParameterMappings = Newtonsoft.Json.JsonConvert.DeserializeObject(Of List(Of FieldMap))(json)
                GridControl2.DataSource = ParameterMappings
            End If

        Catch ex As Exception
            DisplayErrorMessage("Error loading column names", ex)
            btnAddAndClose.Enabled = False
        End Try
    End Sub

    Friend Sub SetQuestions(questions As List(Of FormQuestions))
        _Questions = questions
        GridControl1.DataSource = questions
        GridView1.BestFitColumns()
    End Sub

    Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GridView1.FocusedRowObjectChanged
        Dim row As FormQuestions = e.Row
        cbeSubField.Properties.Items.Clear()
        If row Is Nothing Then Exit Sub
        If row.Sublabels IsNot Nothing AndAlso row.Sublabels.Any Then
            row.Sublabels.ToList.ForEach(Function(l) cbeSubField.Properties.Items.Add(l.Key))
        End If
    End Sub

    Private Sub rgFieldMapType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgFieldMapType.SelectedIndexChanged
        TabbedControlGroup1.SelectedTabPageIndex = rgFieldMapType.SelectedIndex
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click, btnAddAndClose.Click
        Try
            Dim row As FormQuestions = GridView1.GetFocusedRow()
            If Not ValidateInput(row) Then
                Return
            End If

            Dim field = New FieldMap With {.FieldId = row.Id, .SubLabel = cbeSubField.EditValue}
            field.FieldMapType = If(rgFieldMapType.SelectedIndex = 0, FieldMapType.Value, FieldMapType.Expression)
            field.ColumnName = If(MapToParameterMode, teParameterName.Text, cbeColumn.Text)
            If rgFieldMapType.SelectedIndex = 1 Then field.Expression = meSqlExpression.Text
            AddSub.Invoke(field)

            If sender Is btnAddAndClose Then DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error adding new map", ex)
        End Try
    End Sub

    Private Function ValidateInput(row As FormQuestions) As Boolean
        If MapToParameterMode Then
            If teParameterName.Text.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("Please enter a Parameter name")
                Return False
            End If
            If row.Sublabels IsNot Nothing AndAlso row.Sublabels.Any AndAlso cbeSubField.Text.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("Please select a sub lable")
                Return False
            End If
        Else
            If cbeColumn.Text.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("Please select a column name")
                Return False
            End If

            If rgFieldMapType.SelectedIndex = 0 Then
                If row.Sublabels IsNot Nothing AndAlso row.Sublabels.Any AndAlso cbeSubField.Text.IsNullOrWhiteSpace Then
                    XtraMessageBox.Show("Please select a sub lable")
                    Return False
                End If
            Else
                If meSqlExpression.Text.IsNullOrWhiteSpace() Then
                    XtraMessageBox.Show("Please enter a SQL Expression")
                    Return False
                End If
            End If
        End If
        Return True
    End Function

    Private Sub btnDeclareNewParameter_Click(sender As Object, e As EventArgs) Handles btnDeclareNewParameter.Click
        Try
            Dim addFieldMap = Sub(field As FieldMap)
                                  field.FieldMapType = FieldMapType.DeclareParameter
                                  ParameterMappings.Add(field)
                                  GridView2.RefreshData()
                                  SetUdfValue("JotForms Parameter Mapping", Newtonsoft.Json.JsonConvert.SerializeObject(ParameterMappings))
                              End Sub
            Using frm = New frmNewFormFieldMap(True, addFieldMap)
                frm.SetQuestions(_Questions)
                frm.ShowDialog()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error adding new map", ex)
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView2.PopupMenuShowing
        If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete Parameter", Sub()
                                                                                          Dim row As FieldMap = GridView2.GetRow(e.HitInfo.RowHandle)
                                                                                          ParameterMappings.Remove(row)
                                                                                          GridView2.RefreshData()
                                                                                          SetUdfValue("JotForms Parameter Mapping", Newtonsoft.Json.JsonConvert.SerializeObject(ParameterMappings))
                                                                                      End Sub))
        End If
    End Sub
End Class