﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucCompanyBankAccounts
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.btnRefresh = New DevExpress.XtraEditors.SimpleButton()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivisionNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDivisionName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSource = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBA_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBA_ROUTING_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPR_ACCT_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_ACCT_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSTART_CHK_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.btnRefresh)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.LayoutControlGroup1
        Me.LayoutControl1.Size = New System.Drawing.Size(1093, 674)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.EmptySpaceItem1})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1093, 674)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(12, 38)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(1069, 624)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM, Me.colDivisionNum, Me.colDivisionName, Me.colSource, Me.colBA_NAME, Me.colBA_ROUTING_NUM, Me.colPR_ACCT_NUM, Me.colCO_ACCT_TYPE, Me.colSTART_CHK_NUM})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GridControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 26)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(1073, 628)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'btnRefresh
        '
        Me.btnRefresh.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.recurrence_16x16
        Me.btnRefresh.Location = New System.Drawing.Point(952, 12)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Size = New System.Drawing.Size(129, 22)
        Me.btnRefresh.StyleController = Me.LayoutControl1
        Me.btnRefresh.TabIndex = 5
        Me.btnRefresh.Text = "Refresh"
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.btnRefresh
        Me.LayoutControlItem2.Location = New System.Drawing.Point(940, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(133, 26)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(940, 26)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        Me.colCONUM.Visible = True
        Me.colCONUM.VisibleIndex = 0
        '
        'colDivisionNum
        '
        Me.colDivisionNum.FieldName = "DivisionNum"
        Me.colDivisionNum.Name = "colDivisionNum"
        Me.colDivisionNum.Visible = True
        Me.colDivisionNum.VisibleIndex = 1
        '
        'colDivisionName
        '
        Me.colDivisionName.FieldName = "DivisionName"
        Me.colDivisionName.Name = "colDivisionName"
        Me.colDivisionName.Visible = True
        Me.colDivisionName.VisibleIndex = 2
        '
        'colSource
        '
        Me.colSource.FieldName = "Source"
        Me.colSource.Name = "colSource"
        Me.colSource.Visible = True
        Me.colSource.VisibleIndex = 3
        '
        'colBA_NAME
        '
        Me.colBA_NAME.FieldName = "BA_NAME"
        Me.colBA_NAME.Name = "colBA_NAME"
        Me.colBA_NAME.Visible = True
        Me.colBA_NAME.VisibleIndex = 4
        '
        'colBA_ROUTING_NUM
        '
        Me.colBA_ROUTING_NUM.FieldName = "BA_ROUTING_NUM"
        Me.colBA_ROUTING_NUM.Name = "colBA_ROUTING_NUM"
        Me.colBA_ROUTING_NUM.Visible = True
        Me.colBA_ROUTING_NUM.VisibleIndex = 5
        '
        'colPR_ACCT_NUM
        '
        Me.colPR_ACCT_NUM.FieldName = "PR_ACCT_NUM"
        Me.colPR_ACCT_NUM.Name = "colPR_ACCT_NUM"
        Me.colPR_ACCT_NUM.Visible = True
        Me.colPR_ACCT_NUM.VisibleIndex = 6
        '
        'colCO_ACCT_TYPE
        '
        Me.colCO_ACCT_TYPE.FieldName = "CO_ACCT_TYPE"
        Me.colCO_ACCT_TYPE.Name = "colCO_ACCT_TYPE"
        Me.colCO_ACCT_TYPE.Visible = True
        Me.colCO_ACCT_TYPE.VisibleIndex = 7
        '
        'colSTART_CHK_NUM
        '
        Me.colSTART_CHK_NUM.FieldName = "START_CHK_NUM"
        Me.colSTART_CHK_NUM.Name = "colSTART_CHK_NUM"
        Me.colSTART_CHK_NUM.Visible = True
        Me.colSTART_CHK_NUM.VisibleIndex = 8
        '
        'ucCompanyBankAccounts
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.LayoutControl1)
        Me.Name = "ucCompanyBankAccounts"
        Me.Size = New System.Drawing.Size(1093, 674)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents btnRefresh As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDivisionNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDivisionName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSource As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBA_NAME As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBA_ROUTING_NUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPR_ACCT_NUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_ACCT_TYPE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colSTART_CHK_NUM As DevExpress.XtraGrid.Columns.GridColumn
End Class
