﻿'Imports CrystalDecisions.CrystalReports.Engine
'Imports CrystalDecisions.Shared
Imports System.Data
Imports System.IO

Public Class ReportProcessor

    Private Property Co As COMPANY
    Private Property report As ReportEmailTeplate
    Public Property showInRecentReports As Boolean = False
    Private Property fileType As modReports.FileType
    Public Property LastPrNum As Decimal? = Nothing
    Public Property LastCheckDate As DateTime? = Nothing
    Public Property useEPTestPath As Boolean = False
    Public Property showParametersForm As Boolean = True
    Public Property DefaultParamValues As List(Of KeyValuePair)
    Private Property Logger As Serilog.ILogger = modGlobals.Logger.ForContext(Of ReportProcessor)

    Public Sub New(_co As COMPANY, _report As ReportEmailTeplate, _fileType As modReports.FileType)
        Co = _co
        report = _report
        fileType = _fileType
    End Sub

    Public Sub New(_co As Decimal, _report As ReportEmailTeplate, _fileType As modReports.FileType)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Co = db.COMPANies.Single(Function(c) c.CONUM = _co)
        report = _report
        fileType = _fileType
    End Sub

    Public Sub New(coNum As Decimal, name As String, _fileType As modReports.FileType)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        report = db.ReportEmailTeplates.SingleOrDefault(Function(r) r.Name = name)
        Co = db.COMPANies.Single(Function(c) c.CONUM = coNum)
        fileType = _fileType
    End Sub


    Public Function ProcessReport() As ReportResults
        If report Is Nothing Then Throw New Exception("Report was not set")
        Dim result = New ReportResults With {.Comp = Co, .EmailTemplate = report}

        Try
            Dim sw = New Stopwatch()
            sw.Start()
            Dim fileName As String = GetReportFilePath(report.Name, fileType.ToString)

            If report.ReportType = "CR" Then
                Try
                    report.CheckIfReportExist
                    'Using cryRpt = report.ToCrystalReport
                    'cryRpt.SetReportLogOn()
                    Dim ParamFields As List(Of ParameterField) = Nothing
                    'If PopulateReportParameters(cryRpt, ParamFields) <> DialogResult.OK Then
                    If PopulateReportParameters(report.ReportPath, ParamFields) <> DialogResult.OK Then
                        result.Cancalled = True
                        Return result
                    End If

                    Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
                    Dim Errors As String = Nothing
                    'Dim params As ParameterField() = Nothing
                    'Dim status = rpcCls.GetCrystalReportParams($"c:\temp\brandslogo.rpt", params, Errors)

                    Dim EncrPwd = PwdEnc.PwdEncr.Encrypt(UserName) + PwdEnc.PwdEncr.SplitString + PwdEnc.PwdEncr.Encrypt(Password)
                    Dim decr = PwdEnc.PwdEncr.DecUI(EncrPwd)
                    'Dim status = rpcCls.ProcessCrReportOnRabbit(report.ReportPath, If(ParamFields Is Nothing, Nothing, ParamFields.ToArray()), Errors, UserName, EncrPwd, fileName)
                    Dim retrn = rpcCls.ProcessCrReportOnRabbit(report.ReportPath, If(ParamFields Is Nothing, Nothing, ParamFields.ToArray()), Errors, UserName, EncrPwd, fileName)
                    Dim status = retrn.Status
                    Dim params As Object
                    If retrn.Result.Item2 IsNot Nothing Then
                        params = retrn.Result.Item2.ToList()
                    End If
                    'cryRpt.SetReportLogOn()
                    'ProcessCrReport(cryRpt, fileName)
                    result.Paths.Add(fileName)
                    'Logger.Information("Processed {ReportType} [{ReportName}] Co#: {CoNum} with params: {Params} {@Report}", report.ReportType, report.Name, Co.CONUM, cryRpt.GetParameters().ToKeyValuePairs(), report)
                    'Logger.Information("Processed {ReportType} [{ReportName}] Co#: {CoNum} with params: {Params} {@Report}", report.ReportType, report.Name, Co.CONUM, ParamFields.ToKeyValuePairs(), report)
                    Logger.Information("Processed {ReportType} [{ReportName}] Co#: {CoNum} with params: {Params} {@Report}", report.ReportType, report.Name, Co.CONUM, params?.ToKeyValuePairs(), report)
                    'End Using
                Catch ex As Exception
                    Logger.Error(ex, "Error processing CR report {@Report} for {CoNum}", report, Co.CONUM)
                    Throw
                End Try
            ElseIf report.ReportType = "PDF" Then
                report.CheckIfReportExist
                If report.Sql.IsNotNullOrWhiteSpace Then
                    Dim dt As DataTable = Nothing
                    Dim params As List(Of SqlClient.SqlParameter) = Nothing
                    If TryGetDataTable(report.Sql, dt, params, PopulateSqlParameters(), showParametersForm) Then
                        If dt.Rows.Count <> 1 Then
                            Throw New Exception("Please make sure that the sql result should return 1 & only 1 row.")
                        End If
                        result.Rows = dt.Select()
                        result.Paths.Add(PdfUtilities.SetPdfFields(report.ReportPath, report.FieldsMap, dt.Rows(0), fileName))
                    Else
                        result.Cancalled = True
                        Return result
                    End If
                Else
                    File.Copy(report.ReportPath, fileName)
                    File.SetCreationTime(fileName, DateTime.Now)
                    File.SetLastWriteTime(fileName, DateTime.Now)
                    File.SetLastAccessTime(fileName, DateTime.Now)
                    result.Paths.Add(fileName)
                End If
            ElseIf report.ReportType = "EP" Then
                Try
                    Dim params As List(Of ParameterField) = report.GetEPParameter()
                    If (PopulateEPReportParameters(params)) <> DialogResult.OK Then
                        result.Cancalled = True
                        Return result
                    End If
                    processEPReport(params, fileName)
                    result.Paths.Add(fileName)
                    Logger.Information("Processed [{ReportName}] Co#: {CoNum} with params: {Params} {@Report}", report.Name, Co.CONUM, params.ToKeyValuePairs(), report)
                Catch ex As Exception
                    Logger.Error(ex, "Error processing EP report {@Report} for {CoNum}", report, Co.CONUM)
                    Throw
                End Try
            ElseIf report.ReportType = "Eml" Then
                If report.Sql.IsNotNullOrWhiteSpace Then
                    Dim dt As DataTable = Nothing
                    Dim params As List(Of SqlClient.SqlParameter) = Nothing
                    If TryGetDataTable(report.Sql, dt, params, PopulateSqlParameters(), showParametersForm) Then
                        If dt.Rows.Count < 1 Then
                            Throw New Exception("No records found")
                        End If
                        result.Rows = dt.Select
                    Else
                        result.Cancalled = True
                        Return result
                    End If
                End If
                Return result
            ElseIf report.ReportType = "Multi" Then
                If report.Sql.IsNotNullOrWhiteSpace Then
                    Dim dt As DataTable = Nothing
                    Dim params As List(Of SqlClient.SqlParameter) = Nothing
                    If TryGetDataTable(report.Sql, dt, params, PopulateSqlParameters(), showParametersForm) Then
                        If dt.Rows.Count <> 1 Then
                            Throw New Exception("Please make sure that the sql result should return 1 & only 1 row.")
                        End If
                        result.Rows = dt.Select
                    Else
                        result.Cancalled = True
                        Return result
                    End If
                End If

                For Each file In report.ReportEmailTemplateFiles
                    result.Paths.Add(ProcessFile(file.Name, file.ReportPath, file.FieldsMap, result.Rows(0)))
                Next
            ElseIf report.ReportType = "Zendesk" Then
                result.Cancalled = False
            Else
                Throw New Exception("ReportType {0} is invalid".FormatWith(report.ReportType))
            End If
            sw.Stop()
            SaveReportRequestInQueue(sw.Elapsed)
        Catch ex As Exception
            Logger.Error(ex, "Error in processing report {ReportName} FileType: {FileType} CoNum: {CoNum}", report.Name, fileType, Co.CONUM)
            Throw
        End Try
        If result.Paths.All(Function(p) p.IsNotNullOrWhiteSpace) AndAlso result.Paths.All(Function(p) IO.File.Exists(p)) Then
            result.AllFileExist = True
        End If
        Return result
    End Function

    'Private Function PopulateReportParameters(report As ReportDocument, ByRef ParamFields As List(Of ParameterField)) As Windows.Forms.DialogResult
    '    report.SetInitialParameterValues()
    '    report.TrySetParameterValue("CoNum", Co.CONUM)
    '    report.TrySetParameterValue("CoNo", Co.CONUM)
    '    report.TrySetParameterValue("Conum", Co.CONUM)
    '    If LastPrNum.HasValue Then
    '        report.TrySetParameterValue("PrNum", LastPrNum)
    '        report.TrySetParameterValue("Prnum", LastPrNum)
    '        report.TrySetParameterValue("PrNo", LastPrNum)
    '        report.TrySetParameterValue("PrNumSp", LastPrNum) 'added by solomon
    '    End If
    '    report.TrySetParameterValue("Year", DateTime.Now.Year)
    '    report.TrySetParameterValue("Qtr", GetQuarter)
    '    report.TrySetParameterValue("Quarter", GetQuarter)
    '    If LastCheckDate.HasValue Then
    '        report.TrySetParameterValue(ParameterValueKind.DateTimeParameter, LastCheckDate)
    '        report.TrySetParameterValue(ParameterValueKind.DateParameter, LastCheckDate)
    '    End If
    '    If DefaultParamValues IsNot Nothing Then
    '        For Each p In DefaultParamValues
    '            report.TrySetParameterValue(p.Name, p.Value)
    '        Next
    '    End If
    '    If showParametersForm Then
    '        Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
    '        Dim Errors As String = Nothing
    '        Dim params As ParameterField() = Nothing
    '        'Dim frm1 = New frmReportsParameters(report) With {.Co = Co, .LastPrNum = LastPrNum, .LastCheckDate = LastCheckDate}
    '        Dim status = rpcCls.GetCrystalReportParamsFull(report.FileName, params, Errors)
    '        Dim frm1 = New frmReportsParameters(params.ToList(), True) With {.Co = Co, .LastPrNum = LastPrNum, .LastCheckDate = LastCheckDate}
    '        Dim dg As DialogResult
    '        dg = frm1.ShowDialog()
    '        ParamFields = frm1.ParamFields
    '        Return dg
    '    Else
    '        Return DialogResult.OK
    '    End If
    'End Function

    Private Function PopulateReportParameters(ReportFileName As String, ByRef ParamFields As List(Of ParameterField)) As System.Windows.Forms.DialogResult
        Dim Errors As String = Nothing
        Dim paramsInput As ParameterField() = Nothing
        Dim params As ParameterField() = Nothing
        Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
        'Dim status = rpcCls.GetCrystalReportParamsFull(ReportFileName, params, Errors)
        Dim retrn = rpcCls.GetCrystalReportParamsFull(ReportFileName, paramsInput, Errors)
        params = retrn.Result.Item2
        Dim status = retrn.Status

        Dim param As ParameterField = params.FirstOrDefault(Function(f) f IsNot Nothing AndAlso (f.Name.ToLower() = "conum" OrElse f.Name.ToLower() = "cono"))
        If param IsNot Nothing Then
            param.Value = Co.CONUM
        End If

        If LastPrNum.HasValue Then
            param = params.FirstOrDefault(Function(f) f IsNot Nothing AndAlso (f.Name.ToLower() = "prnum" OrElse f.Name.ToLower() = "prno" OrElse f.Name.ToLower() = "prnumsp"))
            If param IsNot Nothing Then
                param.Value = LastPrNum
            End If
        End If

        param = params.FirstOrDefault(Function(f) f?.Name.ToLower() = "year")
        If param IsNot Nothing Then
            param.Value = DateTime.Now.Year
        End If

        param = params.FirstOrDefault(Function(f) f IsNot Nothing AndAlso (f.Name.ToLower() = "qtr" OrElse f.Name.ToLower() = "quarter"))
        If param IsNot Nothing Then
            param.Value = GetQuarter()
        End If

        If LastCheckDate.HasValue Then
            For Each param In params.Where(Function(p) p.ParameterValueType = ParameterValueKind.DateParameter OrElse p.ParameterValueType = ParameterValueKind.DateTimeParameter)
                param.Value = LastCheckDate
            Next
        End If

        If DefaultParamValues IsNot Nothing Then
            For Each p In DefaultParamValues
                param = params.FirstOrDefault(Function(f) f?.Name.ToLower() = p.Name.ToLower())

                If param IsNot Nothing Then
                    param.Value = p.Value
                End If
            Next
        End If

        If showParametersForm Then
            'Dim frm1 = New frmReportsParameters(report) With {.Co = Co, .LastPrNum = LastPrNum, .LastCheckDate = LastCheckDate}
            Dim frm1 = New frmReportsParameters(params.ToList(), True) With {.Co = Co, .LastPrNum = LastPrNum, .LastCheckDate = LastCheckDate}
            Dim dg As DialogResult
            dg = frm1.ShowDialog()
            ParamFields = frm1.ParamFields
            Return dg
        Else
            ParamFields = params.ToList()
            Return DialogResult.OK
        End If
    End Function

    Private Function PopulateSqlParameters() As List(Of SqlClient.SqlParameter)
        Dim list = New List(Of SqlClient.SqlParameter)
        list.Add(New SqlClient.SqlParameter("@CoNum", Co.CONUM))
        list.Add(New SqlClient.SqlParameter("@CoNo", Co.CONUM))
        list.Add(New SqlClient.SqlParameter("@PrNum", LastPrNum))
        list.Add(New SqlClient.SqlParameter("@Date", LastCheckDate))
        If DefaultParamValues IsNot Nothing Then
            For Each p In DefaultParamValues
                list.Add(New SqlClient.SqlParameter(p.Name, p.Value))
            Next
        End If
        Return list
    End Function

    Private Function PopulateEPReportParameters(ByRef Params As List(Of ParameterField)) As System.Windows.Forms.DialogResult
        Params = New List(Of ParameterField)
        Dim ParamList = (From A In report.ReportParameters.Split({","c, ";"c, ":"c}) Select A.Trim).ToArray
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim ReportEPParamMaps = db.ReportEPParamMaps.ToList


        If DefaultParamValues IsNot Nothing Then
            For Each p In DefaultParamValues
                Dim para = New ParameterField With {.Name = p.Name, .ParameterValueType = ParameterValueKind.StringParameter}
                'para.CurrentValues.Add(New ParameterDiscreteValue With {.Value = p.Value})
                para.Value = p.Value
                para.CurrentValues.Add(p.Value)
                Params.Add(para)
            Next
            Dim param = New ParameterField With {.Name = "cf", .PromptText = "Company #", .ParameterValueType = ParameterValueKind.NumberParameter}
            'param.CurrentValues.Add(New ParameterDiscreteValue With {.Value = Co.CONUM})
            param.CurrentValues.Add(Co.CONUM)
            param.Value = Co.CONUM
            Params.Add(param)
        Else

            For Each p In ParamList
                Dim param As ParameterField = Nothing
                Select Case p
                    Case "y"
                        param = New ParameterField With {.Name = "y", .PromptText = "Year", .ParameterValueType = ParameterValueKind.NumberParameter}
                        If LastCheckDate.HasValue Then
                            param.CurrentValues.AddValue(LastCheckDate.Value.Year)
                            Dim asdf = param.CurrentValues.Count
                            param.Value = LastCheckDate.Value.Year
                        End If
                    Case "cf"
                        param = New ParameterField With {.Name = "cf", .PromptText = "Company #", .ParameterValueType = ParameterValueKind.NumberParameter}
                        'param.CurrentValues.Add(New ParameterDiscreteValue With {.Value = Co.CONUM})
                        param.CurrentValues.Add(Co.CONUM)
                        param.Value = Co.CONUM
                    Case "pr"
                        param = New ParameterField With {.Name = "pr", .PromptText = "Payroll #", .ParameterValueType = ParameterValueKind.NumberParameter}
                        'param.CurrentValues.Add(New ParameterDiscreteValue With {.Value = LastPrNum})
                        param.CurrentValues.Add(LastPrNum)
                        param.Value = LastPrNum
                    'Case "dv"
                    '    param = New ParameterField With {.Name = "dv", .PromptText = "Division", .ParameterValueType = ParameterValueKind.StringParameter}
                    '    param.CurrentValues.Add(New ParameterDiscreteValue With {.Value = "-1"})
                    'Case "dp"
                    '    param = New ParameterField With {.Name = "dp", .PromptText = "Department", .ParameterValueType = ParameterValueKind.StringParameter}
                    '    param.CurrentValues.AddValue("All Departments")
                    Case "d"
                        param = New ParameterField With {.Name = "d", .PromptText = "Deduction", .ParameterValueType = ParameterValueKind.StringParameter}
                        param.CurrentValues.AddValue("All Deductions")
                        param.Value = "All Deductions"
                    Case "j"
                        param = New ParameterField With {.Name = "j", .PromptText = "Job", .ParameterValueType = ParameterValueKind.StringParameter}
                        param.CurrentValues.AddValue("All Jobs")
                        param.Value = "All Jobs"
                    Case "pw"
                        param = New ParameterField With {.Name = "pw", .PromptText = "Password", .ParameterValueType = ParameterValueKind.StringParameter}
                    Case "df"
                        param = New ParameterField With {.Name = "df", .PromptText = "Date From", .ParameterValueType = ParameterValueKind.DateParameter}
                        param.CurrentValues.AddValue(LastCheckDate)
                        param.Value = LastCheckDate
                    Case "dt"
                        param = New ParameterField With {.Name = "dt", .PromptText = "Date To", .ParameterValueType = ParameterValueKind.DateParameter}
                        param.CurrentValues.AddValue(LastCheckDate)
                        param.Value = LastCheckDate
                    Case "ef"
                        param = New ParameterField With {.Name = "ef", .PromptText = "Employee From", .ParameterValueType = ParameterValueKind.NumberParameter}
                        param.CurrentValues.AddValue(-1)
                        param.Value = -1
                    Case "et"
                        param = New ParameterField With {.Name = "et", .PromptText = "Employee To", .ParameterValueType = ParameterValueKind.NumberParameter}
                        param.CurrentValues.AddValue(-1)
                        param.Value = -1
                    Case "m"
                        param = New ParameterField With {.Name = "m", .PromptText = "Month", .ParameterValueType = ParameterValueKind.NumberParameter}
                        If LastCheckDate.HasValue Then
                            param.CurrentValues.AddValue(LastCheckDate.Value.Month)
                            param.Value = LastCheckDate.Value.Month
                        End If
                    Case "q"
                        param = New ParameterField With {.Name = "q", .PromptText = "Quarter", .ParameterValueType = ParameterValueKind.NumberParameter}
                        param.CurrentValues.AddValue(GetQuarter())
                        param.Value = GetQuarter()
                    Case "s"
                        param = New ParameterField With {.Name = "s", .PromptText = "Employee Status", .ParameterValueType = ParameterValueKind.NumberParameter}
                        param.CurrentValues.AddValue(-1)
                        param.Value = -1
                    Case "t"
                        param = New ParameterField With {.Name = "t", .PromptText = "Tip", .ParameterValueType = ParameterValueKind.StringParameter}
                    Case "tr"
                        param = New ParameterField With {.Name = "tr", .PromptText = "Tip Rate", .ParameterValueType = ParameterValueKind.StringParameter}
                    Case Else
                        Dim epP = ReportEPParamMaps.FirstOrDefault(Function(ep) ep.Param = p)
                        If epP IsNot Nothing Then
                            param = New ParameterField With {.Name = p, .PromptText = epP.Description, .ParameterValueType = ParameterValueKind.StringParameter}
                            If epP.AvailibleValues.IsNotNullOrWhiteSpace Then
                                For Each v In epP.AvailibleValues.Split(",")
                                    'param.CurrentValues.Add(New ParameterDiscreteValue With {.Value = v})
                                    param.CurrentValues.Add(v)
                                    param.Value = v
                                Next
                            End If
                        End If
                End Select
                If param IsNot Nothing Then
                    Params.Add(param)
                End If
            Next
        End If

        If showParametersForm Then
            Dim frm1 = New frmReportsParameters(Params)
            Dim dg As DialogResult
            dg = frm1.ShowDialog()
            Return dg
        Else
            Return DialogResult.OK
        End If
    End Function

    Private Sub processEPReport(params As List(Of ParameterField), FileName As String)
        'Dim aParamValues = (From A In params
        '                    Where A.HasCurrentValue
        '                    Select v = String.Format("{0}:{1}", A.Name, CType(A.CurrentValues(0), ParameterDiscreteValue).Value.ToString)
        '                    ).ToArray

        Dim aParamValues = (From A In params
                            Where A.HasCurrentValue OrElse A.Value IsNot Nothing
                            Select v = String.Format("{0}:{1}", A.Name, A.Value.ToString)
                            ).ToArray
        Dim ParamValues = String.Join(" ", (From A In aParamValues Select v = String.Format("""{0}""", A)))

        Dim cmdArgs = String.Format("""{0}"" ""{1}"" {2}", report.ReportPath, FileName, ParamValues)
        Logger.Information("Starting EP Reports Process, {ReportName} {CmdArgs}", report.Name, cmdArgs)
        Dim pi As ProcessStartInfo
        If useEPTestPath Then
            Dim db = New dbEPDataDataContext(GetConnectionString)
            pi = New ProcessStartInfo(db.FrontDeskOptions.Single.ReportTestPath + "ReportExport.exe", cmdArgs)
            pi.WorkingDirectory = db.FrontDeskOptions.Single.ReportTestPath
        Else
            pi = New ProcessStartInfo("\\brands.local\dfs\Reports\EP Reports\ReportExport.exe", cmdArgs)
            pi.WorkingDirectory = "\\brands.local\dfs\Reports\EP Reports\"
        End If

        Dim p As Process = Nothing
        p = New Process With {.EnableRaisingEvents = True, .StartInfo = pi}
        p.Start()
        AddHandler p.Exited, Sub()
                                 Logger.Information("EP Reports Process exited.")
                             End Sub
        'Dim b = p.WaitForExit(60 * 1000)
        While Not p.HasExited
            Threading.Thread.Sleep(100)
        End While
        p.Dispose()
    End Sub

    'Private Sub ProcessCrReport(report As ReportDocument, reportPath As String)
    '    Select Case fileType
    '        Case modReports.FileType.Pdf
    '            report.ExportToDisk(ExportFormatType.PortableDocFormat, reportPath)
    '        Case modReports.FileType.Xls
    '            report.ExportToDisk(ExportFormatType.Excel, reportPath)
    '        Case modReports.FileType.Txt
    '            report.ExportToDisk(ExportFormatType.Text, reportPath)
    '            If System.IO.File.Exists(reportPath) Then
    '                Dim fileContent = System.IO.File.ReadAllText(reportPath)
    '                System.IO.File.Delete(reportPath)
    '                System.IO.File.WriteAllText(reportPath, fileContent.TrimEnd())
    '            End If
    '    End Select
    'End Sub

    Private Function ProcessFile(reportName As String, reportPath As String, fieldsMap As String, row As DataRow) As String
        Dim saveLocation As String = GetReportFilePath(reportName, Path.GetExtension(reportPath))

        If row IsNot Nothing AndAlso Path.GetExtension(reportPath).ToLower() = ".pdf" Then
            Return PdfUtilities.SetPdfFields(reportPath, fieldsMap, row, saveLocation)
        Else
            File.Copy(reportPath, saveLocation)
            File.SetCreationTime(saveLocation, DateTime.Now)
            File.SetLastWriteTime(saveLocation, DateTime.Now)
            File.SetLastAccessTime(saveLocation, DateTime.Now)
            Return saveLocation
        End If
    End Function

    Private Function GetReportFilePath(reportName As String, extension As String) As String
        If Not extension.StartsWith(".") Then extension = $".{extension}"
        extension = extension.ToLower()
        Dim basePath As String = If(showInRecentReports, modReports.GetCrystalReportsFolder(), modReports.GetSecureCrystalReportsFolder())
        Dim filePath As String
        If showInRecentReports Then
            filePath = Path.Combine(basePath, modReports.GetFileName(Co, reportName, extension))
        Else
            filePath = Path.Combine(basePath, reportName) & extension
        End If

        Try
            If File.Exists(filePath) Then
                File.Delete(filePath)
            End If
        Catch ex As Exception
            Throw New Exception($"Error while trying to delete existing file {filePath}", ex)
        End Try

        Return filePath
    End Function

    Private Sub SaveReportRequestInQueue(elapsed As TimeSpan)
        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                Dim reportQueue = New ReportQueue With {.ReportId = report.ID,
                    .ReportName = report.Name,
                    .Conum = Me.Co.CONUM,
                    .RequestedBy = UserName,
                    .RequestedOn = DateTime.Now,
                    .Status = "Completed",
                    .ProcessedBy = "FD"'',
                        }
                '.ProcessingDuration = elapsed 'readonly
                ctxDB.ReportQueues.InsertOnSubmit(reportQueue)
                If Not ctxDB.SaveChanges() Then
                    Logger.Warning("Unable to save report queue entry due to concurrency conflict")
                End If
            End Using
        Catch ex As Exception
            Logger.Error(ex, "Error saving ReportQueue")
        End Try
    End Sub

    Public Function GetReportParameters() As List(Of Brands.DAL.ReportEmailTemplateParameter)
        Dim result = New List(Of Brands.DAL.ReportEmailTemplateParameter)
        If report.ReportType = "CR" Then
            GetCrystalReportParameters(result)
        ElseIf report.ReportType = "EP" Then
            Dim ParamList = New List(Of String)

            If report.ReportParameters.IsNotNullOrWhiteSpace() Then
                ParamList = (From A In report.ReportParameters.Split({","c, ";"c, ":"c}) Select A.Trim).ToList()
            End If

            Dim db = New Brands.DAL.EPDATAContext(GetConnectionString)
            For Each p In ParamList
                Dim paramMap = (From pm In db.ReportEPParamMaps Where pm.Param = p).SingleOrDefault
                If paramMap Is Nothing Then Throw New Exception("Error resolving parameter {0} from EP report")
                Dim paramater = New Brands.DAL.ReportEmailTemplateParameter With {
                        .Name = paramMap.Param,
                        .Description = paramMap.Description,
                        .DataType = paramMap.DataType,
                        .EditorType = GetParameterEditorType(paramMap.Param),
                        .IsPublic = True}
                result.Add(paramater)
            Next
        End If
        Return result
    End Function

    Private Sub GetCrystalReportParameters(result As List(Of Brands.DAL.ReportEmailTemplateParameter))
        Try
            report.CheckIfReportExist
            'Using cryRpt = report.ToCrystalReport
            '    cryRpt.SetReportLogOn()
            '    For Each item In cryRpt.GetParameters
            '        '.DataType = GetParameterDataType(item),
            '        Dim paramater = New Brands.DAL.ReportEmailTemplateParameter With {
            '            .Name = item.Name,
            '            .Description = item.PromptText,
            '            .DataType = GetParameterDataType(New ParameterField(item.Name, item.GetFirstValue())),
            '            .EditorType = GetParameterEditorType(item.Name),
            '            .IsPublic = True}
            '        result.Add(paramater)
            '    Next

            '    Dim initialParams = cryRpt.ReportClientDocument.DataDefController.DataDefinition.ParameterFields
            '    For i As Integer = 0 To initialParams.Count - 1
            '        Dim ip As CrystalDecisions.ReportAppServer.DataDefModel.ISCRParameterField = initialParams(i)
            '        If ip.InitialValues.Count > 0 Then
            '            Dim v As CrystalDecisions.ReportAppServer.DataDefModel.ParameterFieldDiscreteValue = ip.InitialValues.Item(0)
            '            Dim value As Object
            '            If v.Value = "DateTime(1900,01,01,00,00,00)" Then
            '                value = New DateTime(1900, 1, 1)
            '            Else
            '                value = v.Value
            '            End If
            '            Dim paramater = result.Single(Function(p) p.Name = ip.Name)
            '            If paramater IsNot Nothing Then
            '                paramater.DefaultValue = value.ToString()
            '            End If
            '        End If
            '    Next
            'End Using
            GetCrystalReportParametersNew(result)
        Catch ex As Exception
            Logger.Error(ex, "Error processing CR report {@Report} for {CoNum}", report, Co.CONUM)
            Throw
        End Try
    End Sub

    Private Sub GetCrystalReportParametersNew(result As List(Of Brands.DAL.ReportEmailTemplateParameter))
        Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
        Dim Errors As String = Nothing
        Dim params As ParameterField() = Nothing
        Dim paramsInput As ParameterField() = Nothing
        'Dim status = rpcCls.GetCrystalReportParamsFull(report.ReportPath, params, Errors)
        Dim retrn = rpcCls.GetCrystalReportParamsFull(report.ReportPath, paramsInput, Errors)
        params = retrn.Result.Item2
        Dim status = retrn.Status

        For Each item In params
            Dim value = If(item.Value?.ToString() = "DateTime(1900,01,01,00,00,00)", New DateTime(1900, 1, 1), item.Value)
            Dim pf = New ParameterField With {.Name = item.Name, .Value = item.Value, .ParameterValueType = item.ParameterValueType}
            Dim paramater = New Brands.DAL.ReportEmailTemplateParameter With {
                .Name = item.Name,
                .Description = item.PromptText,
                .DataType = GetParameterDataType(pf),
                .EditorType = GetParameterEditorType(item.Name),
                .IsPublic = True,
                .DefaultValue = value
            }
            result.Add(paramater)
        Next
    End Sub

    Private Function GetParameterDataType(ByVal item As ParameterField) As String
        Select Case item.ParameterValueType
            Case ParameterValueKind.BooleanParameter
                Return "bool"
            Case ParameterValueKind.CurrencyParameter
                Return "currency"
            Case ParameterValueKind.DateParameter
                Return "date"
            Case ParameterValueKind.DateTimeParameter
                Return "datetime"
            Case ParameterValueKind.NumberParameter
                Return "int"
            Case ParameterValueKind.StringParameter
                Return "string"
            Case ParameterValueKind.TimeParameter
                Return "time"
            Case Else
                Throw New Exception("Unknown parameter type")
        End Select
    End Function

    Private Function GetParameterEditorType(ByVal item As String) As String
        Return Nothing
    End Function
End Class
