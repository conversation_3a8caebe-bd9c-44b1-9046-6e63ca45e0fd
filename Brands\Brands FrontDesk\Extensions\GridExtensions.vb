﻿Imports System.Data
Imports System.IO
Imports System.Runtime.CompilerServices
Imports DevExpress.Utils
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid

Public Module GridExtensions
    Private RepItemButtonClick_def As Action(Of Object, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
    Private RepItemHLEOpenLink_def As Action(Of Object, DevExpress.XtraEditors.Controls.OpenLinkEventArgs)

    Sub RepItemButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
        RepItemButtonClick_def.Invoke(sender, e)
    End Sub

    Sub RepItemHLEOpenLink(sender As Object, e As DevExpress.XtraEditors.Controls.OpenLinkEventArgs)
        RepItemHLEOpenLink_def.Invoke(sender, e)
    End Sub

    <Extension>
    Public Function GetSelectedRows(Of T)(gv As GridView) As List(Of T)
        Dim list = New List(Of T)
        If Not gv.OptionsSelection.MultiSelect Then
            Return list
        End If
        For Each i In gv.GetSelectedRows
            If Not gv.IsGroupRow(i) Then
                Dim row As T = gv.GetRow(i)
                list.Add(row)
            End If
        Next
        Return list
    End Function

    <Extension>
    Public Sub SetGridLayoutAndAddMenues(gv As GridView, gridId As String)
        gv.SetGridLayout(gridId)
        AddHandler gv.PopupMenuShowing, New PopupMenuShowingEventHandler(Sub(sender As Object, e As PopupMenuShowingEventArgs)
                                                                             gvPopUpMenuShowing(e, gridId, sender)
                                                                         End Sub)
    End Sub

    Private Sub gvPopUpMenuShowing(e As PopupMenuShowingEventArgs, gridId As String, gv As GridView)
        If e.MenuType = GridMenuType.Column Then
            e.Menu.Items.Add(New Menu.DXMenuItem("Save Layout", Sub()
                                                                    SaveGridLayout(gridId, gv)
                                                                End Sub, My.Resources.saveas_16x16) With {.BeginGroup = True})
            e.Menu.Items.Add(New Menu.DXMenuItem("Reload Layout", Sub()
                                                                      gv.SetGridLayout(gridId)
                                                                  End Sub, My.Resources.refresh_16x16))
            e.Menu.Items.Add(New Menu.DXMenuItem("Reset Layout", Sub()
                                                                     SaveGridLayout(gridId, Nothing)
                                                                     DisplayMessageBox("Please restart the form for this to take effect.")
                                                                 End Sub, My.Resources.delete_16x16))
            e.Menu.Items.Add(New Menu.DXMenuItem("Export Layout", Sub() SaveLayoutToXml(gv), My.Resources.exporttotxt_16x16))
        End If
    End Sub

    Private Sub SaveLayoutToXml(gv As GridView)
        Try
            Using fd = New XtraSaveFileDialog()
                fd.DefaultExt = "xml"
                fd.RestoreDirectory = True
                fd.FileName = $"{gv.Name}.xml"
                If fd.ShowDialog = DialogResult.OK Then
                    gv.OptionsLayout.Columns.StoreAllOptions = True
                    gv.SaveLayoutToXml(fd.FileName)
                    'System.Diagnostics.Process.Start(New FileInfo(fd.FileName).Directory.FullName)
                    Dim psi As New System.Diagnostics.ProcessStartInfo()
                    psi.FileName = New FileInfo(fd.FileName).Directory.FullName
                    psi.UseShellExecute = True
                    System.Diagnostics.Process.Start(psi)
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error saving grid layout", ex)
        End Try
    End Sub

    <Extension>
    Public Sub SetGridLayout(gv As GridView, gridId As String)
        Using db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
            Dim layout = (From l In db.GridLayouts Where l.UserId = UserName AndAlso l.GridId = gridId AndAlso l.IsDefault).SingleOrDefault
            If layout IsNot Nothing AndAlso layout.Layout.IsNotNullOrWhiteSpace Then
                Dim byteArray = System.Text.Encoding.ASCII.GetBytes(layout.Layout)
                Dim ms = New MemoryStream(byteArray)
                gv.RestoreLayoutFromStream(ms)
            End If
        End Using
    End Sub

    Private Sub SaveGridLayout(gridId As String, gv As GridView)
        Using db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
            Dim layout = (From l In db.GridLayouts Where l.UserId = UserName AndAlso l.GridId = gridId AndAlso l.IsDefault).SingleOrDefault
            If layout Is Nothing Then
                layout = New GridLayout With {.GridId = gridId, .IsDefault = True, .UserId = UserName}
                db.GridLayouts.InsertOnSubmit(layout)
            End If
            If gv IsNot Nothing Then
                layout.Layout = gv.GetGridLayout()
            Else
                layout.Layout = String.Empty
            End If
            db.SaveChanges
        End Using
    End Sub

    <Extension>
    Public Function GetGridLayout(gv As GridView, Optional fullLayout As Boolean = True) As String
        Dim stream = New System.IO.MemoryStream()
        gv.SaveLayoutToStream(stream, If(fullLayout, OptionsLayoutBase.FullLayout, Nothing))
        stream.Seek(0, SeekOrigin.Begin)
        Return New StreamReader(stream).ReadToEnd()
    End Function

    <Extension>
    Public Sub ChangeCopyBehavior(gv As GridView)
        AddHandler gv.PopupMenuShowing, Sub(obj, e)
                                            If e.Allow AndAlso e.HitInfo.RowHandle >= 0 AndAlso e.HitInfo.InRowCell Then
                                                e.Menu.Items.Add(New Menu.DXMenuItem("Copy Cell", Sub()
                                                                                                      Dim value = gv.GetFocusedRowCellDisplayText(gv.FocusedColumn)
                                                                                                      If value.IsNotNullOrWhiteSpace Then Clipboard.SetText(value)
                                                                                                  End Sub, My.Resources.copy_16x16))
                                            End If
                                        End Sub

        AddHandler gv.GridControl.KeyDown, Sub(obj As Object, e As KeyEventArgs)
                                               If e.Control AndAlso e.KeyCode = Keys.C Then
                                                   If gv.GetRowCellValue(gv.FocusedRowHandle, gv.FocusedColumn) IsNot Nothing AndAlso gv.GetRowCellValue(gv.FocusedRowHandle, gv.FocusedColumn).ToString() <> [String].Empty Then
                                                       Clipboard.SetText(gv.GetRowCellValue(gv.FocusedRowHandle, gv.FocusedColumn).ToString())
                                                       e.Handled = True
                                                   End If
                                               End If
                                           End Sub
    End Sub
    <Extension>
    Public Function IsAllRowsSelected(ByVal view As GridView) As Boolean
        Dim allRowCount As Integer = view.DataController.VisibleListSourceRowCount + view.DataController.GroupRowCount
        Return view.GetSelectedRows().Length = allRowCount
    End Function

    <Extension>
    Sub SetCols(ByVal GV As GridView, dtCols As DataTable, Optional FieldToShowCnt As String = Nothing _
            , Optional ButtonClickHandler As Action(Of Object, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) = Nothing _
            , Optional HLEOpenLinkHandler As Action(Of Object, DevExpress.XtraEditors.Controls.OpenLinkEventArgs) = Nothing
        )

        Dim Col As DevExpress.XtraGrid.Columns.GridColumn

        Dim row As DataRow
        For Each row In dtCols.Rows
            Col = New DevExpress.XtraGrid.Columns.GridColumn With
            {.FieldName = row("FieldName"), .Caption = row("Caption"), .Width = row("Width"), .VisibleIndex = row("VisibleIndex")}
            Col.OptionsColumn.ReadOnly = row("ReadOnly")

            If Not IsDBNull(row("RepositoryType")) Then
                Dim colRep As DevExpress.XtraEditors.Repository.RepositoryItem = Nothing

                If row("RepositoryType") = "LookUpEdit" Then
                    Dim colRepRICB = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
                    colRep = colRepRICB
                    If Not IsDBNull(row("Options")) Then
                        colRepRICB.Items.AddRange(Split(row("Options"), "~").ToList())
                    End If
                ElseIf row("RepositoryType") = "DateEdit" Then
                    Dim colRepDE = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
                    colRep = colRepDE
                ElseIf row("RepositoryType") = "MemoEdit" Then
                    Dim colRepDE = New DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit()
                    colRep = colRepDE
                ElseIf row("RepositoryType") = "RepositoryItemHyperLinkEdit" Then
                    Dim colRepDE = New DevExpress.XtraEditors.Repository.RepositoryItemHyperLinkEdit
                    colRepDE.SingleClick = True
                    RepItemHLEOpenLink_def = HLEOpenLinkHandler
                    AddHandler colRepDE.OpenLink, AddressOf RepItemHLEOpenLink
                    colRep = colRepDE
                ElseIf row("RepositoryType") = "RepositoryItemOpenState" Then
                    Dim colRepDE = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
                    colRepDE.Tag = row("FieldName")
                    colRepDE.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor

                    Dim EditorButtonImageOptions1 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
                    EditorButtonImageOptions1.Image = Global.Brands_FrontDesk.My.Resources.Resources.open_16x16

                    colRepDE.Buttons.Clear()
                    colRepDE.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, True, EditorButtonImageOptions1)})
                    colRepDE.Buttons(0).Appearance.BackColor = Color.Transparent

                    RepItemButtonClick_def = ButtonClickHandler
                    AddHandler colRepDE.ButtonClick, AddressOf RepItemButtonClick
                    colRep = colRepDE
                End If

                If colRep IsNot Nothing Then
                    If Not IsDBNull(row("colFormat")) Then
                        If row("colFormat") = "Currency" Then
                            colRep.EditFormat.FormatString = "C2"
                            colRep.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                        ElseIf row("colFormat") = "Date" Then
                            colRep.EditFormat.FormatString = "MM/dd/yyyy"
                            colRep.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                        ElseIf row("colFormat") = "DateTime" Then
                            Dim colRepDE As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit = colRep
                            colRep.EditFormat.FormatString = "g"
                            colRep.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                            colRep.DisplayFormat.FormatString = "g"
                            colRep.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                            colRepDE.EditMask = "g"

                            colRepDE.CalendarTimeEditing = DevExpress.Utils.DefaultBoolean.[True]
                            colRepDE.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
                            colRepDE.CalendarTimeProperties.DisplayFormat.FormatString = "g"
                            colRepDE.CalendarTimeProperties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                            colRepDE.CalendarTimeProperties.EditFormat.FormatString = "g"
                            colRepDE.CalendarTimeProperties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                            colRepDE.CalendarTimeProperties.Mask.EditMask = ""
                            colRepDE.DisplayFormat.FormatString = "g"
                            colRepDE.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                            colRepDE.EditFormat.FormatString = "g"
                            colRepDE.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                            colRepDE.Mask.EditMask = "g"

                        End If
                    End If
                    Col.ColumnEdit = colRep
                End If
            End If

            If Not IsDBNull(row("colFormat")) Then
                If row("colFormat") = "Currency" Then
                    Col.DisplayFormat.FormatString = "C2"
                    Col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                ElseIf row("colFormat") = "Date" Then
                    Col.DisplayFormat.FormatString = "d"
                    Col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                ElseIf row("colFormat") = "DateTime" Then
                    Col.DisplayFormat.FormatString = "g"
                    Col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                End If
            End If

            If FieldToShowCnt IsNot Nothing AndAlso row("FieldName").ToString().ToUpper() = FieldToShowCnt.ToUpper() Then
                Col.SummaryItem.FieldName = row("FieldName")
                Col.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Count
                Col.SummaryItem.DisplayFormat = "{0:n0} Rows"
            End If

            If row("SummaryItemType").ToString() <> "" Then
                Col.SummaryItem.FieldName = row("FieldName")
                Col.SummaryItem.SummaryType = If(row("SummaryItemType").ToString() = "Sum", DevExpress.Data.SummaryItemType.Sum, If(row("SummaryItemType").ToString() = "Count", DevExpress.Data.SummaryItemType.Count, DevExpress.Data.SummaryItemType.Average))
                Col.SummaryItem.Mode = If(row("SummaryMode").ToString() = "Selection", DevExpress.Data.SummaryMode.Selection, DevExpress.Data.SummaryMode.AllRows)
                Col.SummaryItem.DisplayFormat = row("DisplayFormat").ToString()
            End If
            GV.Columns.Add(Col)
        Next
    End Sub

    <Extension> Sub SortCols(ByVal GV As GridView, dtCols As DataTable)
        Dim Col As DevExpress.XtraGrid.Columns.GridColumn
        Dim sc = (From c In dtCols Where Not IsDBNull(c("SortOrder")) Order By c("SortOrder")).ToList()

        For Each c In sc
            Col = GV.Columns.Where(Function(cl) cl.FieldName.ToUpper() = c("FieldName").ToUpper()).FirstOrDefault()
            If Col IsNot Nothing Then
                If Not IsDBNull(c("SortOrder")) AndAlso c("SortOrder") > 0 Then
                    Col.SortIndex = c("SortOrder")
                    Col.SortOrder = If(c("SortDesc") = 0, SortOrder.Ascending, SortOrder.Descending)
                Else
                    Col.SortOrder = DevExpress.Data.ColumnSortOrder.None
                End If
            End If
        Next
    End Sub

End Module
