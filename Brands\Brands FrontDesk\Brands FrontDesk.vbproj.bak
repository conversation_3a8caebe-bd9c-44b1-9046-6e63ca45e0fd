﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <OutputType>WinExe</OutputType>
    <StartupObject>Brands_FrontDesk.My.MyApplication</StartupObject>
    <RootNamespace>Brands_FrontDesk</RootNamespace>
    <MyType>WindowsForms</MyType>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <PublishUrl>I:\FrontDesk\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Unc</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>false</MapFileExtensions>
    <InstallUrl>\\brands.local\DFS\execupay\FrontDesk\</InstallUrl>
    <ProductName>Brands Front Desk</ProductName>
    <PublisherName>Brands Paycheck</PublisherName>
    <TrustUrlParameters>true</TrustUrlParameters>
    <ApplicationRevision>9</ApplicationRevision>
    <ApplicationVersion>2.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>true</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>true</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <Configurations>API;debug design;Debug;Development;Release</Configurations>
    <TargetFramework>net9.0-windows</TargetFramework>
    <AssemblyTitle>Brands FrontDesk</AssemblyTitle>
    <Product>Brands FrontDesk</Product>
    <Copyright>Copyright ©  2011</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x64</PlatformTarget>
    <DocumentationFile>Brands FrontDesk.xml</DocumentationFile>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <WarningsAsErrors>
    </WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>Brands FrontDesk.xml</DocumentationFile>
    <WarningsAsErrors>
    </WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DocumentationFile>Brands FrontDesk.xml</DocumentationFile>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <WarningsAsErrors>
    </WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DefineDebug>false</DefineDebug>
    <DocumentationFile>Brands FrontDesk.xml</DocumentationFile>
    <WarningsAsErrors>
    </WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>23AF957E33650BD9CA0D87B173087CC1A0DBFEC7</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>Brands FrontDesk_1_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'debug design|x86'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x86\debug design\</OutputPath>
    <DocumentationFile>Brands FrontDesk.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'debug design|x64'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x64\debug design\</OutputPath>
    <DocumentationFile>Brands FrontDesk.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Update="System.Drawing">
      <HintPath>..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Drawing.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Code\dsMain.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>dsMain.xsd</DependentUpon>
    </Compile>
    <Compile Update="Code\dsMain.vb">
      <DependentUpon>dsMain.xsd</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\rptCompanyServices.Designer.vb">
      <DependentUpon>rptCompanyServices.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\rptCompanyServices.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\Reports\srptCompanyNotes.Designer.vb">
      <DependentUpon>srptCompanyNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\srptCompanyNotes.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\Reports\srptCompanyAutos.Designer.vb">
      <DependentUpon>srptCompanyAutos.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\srptCompanyAutos.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\Reports\srptPayrollCalendar.Designer.vb">
      <DependentUpon>srptPayrollCalendar.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\srptPayrollCalendar.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\Reports\srptRelatedContacts.Designer.vb">
      <DependentUpon>srptRelatedContacts.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\srptRelatedContacts.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\Reports\srptRelatedCompanies.Designer.vb">
      <DependentUpon>srptRelatedCompanies.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\srptRelatedCompanies.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\Reports\srptCompanyDivisions.Designer.vb">
      <DependentUpon>srptCompanyDivisions.vb</DependentUpon>
    </Compile>
    <Compile Update="Code\Reports\srptCompanyDivisions.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Code\test.designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>test.dbml</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\frmDownloadSubmittions.Designer.vb">
      <DependentUpon>frmDownloadSubmittions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\frmFormFieldMap.Designer.vb">
      <DependentUpon>frmFormFieldMap.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\frmNewFormFieldMap.Designer.vb">
      <DependentUpon>frmNewFormFieldMap.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\ucAccountLeadDetails.Designer.vb">
      <DependentUpon>ucAccountLeadDetails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\ucAccountLeadDetails.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Admin\frmAgents.Designer.vb">
      <DependentUpon>frmAgents.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Admin\frmPhoneDataComments.Designer.vb">
      <DependentUpon>frmPhoneDataComments.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Admin\frmPhoneSetup.Designer.vb">
      <DependentUpon>frmPhoneSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Admin\frmScorecard.Designer.vb">
      <DependentUpon>frmScorecard.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Admin\frmUnassignedPhoneNumbers.Designer.vb">
      <DependentUpon>frmUnassignedPhoneNumbers.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Audit\frmAudit.Designer.vb">
      <DependentUpon>frmAudit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Banking\frmPlaid.Designer.vb">
      <DependentUpon>frmPlaid.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Banking\frmRiskManagement.Designer.vb">
      <DependentUpon>frmRiskManagement.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Banking\ucEmailAlerts.Designer.vb">
      <DependentUpon>ucEmailAlerts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Banking\ucEmailAlerts.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Billing\frmBillingCredits.Designer.vb">
      <DependentUpon>frmBillingCredits.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\frmManualBillingDraftDetails.Designer.vb">
      <DependentUpon>frmManualBillingDraftDetails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\frmManualBillingDraftEdit.Designer.vb">
      <DependentUpon>frmManualBillingDraftEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmEditCoContact.Designer.vb">
      <DependentUpon>frmEditCoContact.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmLinkedCompanies.Designer.vb">
      <DependentUpon>frmLinkedCompanies.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmLinkExistingEmployee.Designer.vb">
      <DependentUpon>frmLinkExistingEmployee.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmPrintW2.Designer.vb">
      <DependentUpon>frmPrintW2.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyBankAccounts.Designer.vb">
      <DependentUpon>ucCompanyBankAccounts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyBankAccounts.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyEmails.Designer.vb">
      <DependentUpon>ucCompanyEmails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyEmails.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucReportOptions.Designer.vb">
      <DependentUpon>ucReportOptions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucReportOptions.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucSelectEmployeeRange.Designer.vb">
      <DependentUpon>ucSelectEmployeeRange.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucSelectEmployeeRange.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\EssSetup\frmEmployeeDeckCompanySetup.Designer.vb">
      <DependentUpon>frmEmployeeDeckCompanySetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frm1059UserRelationships.Designer.vb">
      <DependentUpon>frm1059UserRelationships.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmAcctOnHoldPopup.Designer.vb">
      <DependentUpon>frmAcctOnHoldPopup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmClientLimits.Designer.vb">
      <DependentUpon>frmClientLimits.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmClientNotSetupDDDialog.designer.vb">
      <DependentUpon>frmClientNotSetupDDDialog.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCovidEmailNotify.Designer.vb">
      <DependentUpon>frmCovidEmailNotify.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmDelayDD.Designer.vb">
      <DependentUpon>frmDelayDD.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmGlobalRiskSettings.Designer.vb">
      <DependentUpon>frmGlobalRiskSettings.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmObcVoidChecks.Designer.vb">
      <DependentUpon>frmObcVoidChecks.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmOnboardingDashboard.Designer.vb">
      <DependentUpon>frmOnboardingDashboard.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollNotes.Designer.vb">
      <DependentUpon>frmPayrollNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollsInProcessAutoFixLogs.Designer.vb">
      <DependentUpon>frmPayrollsInProcessAutoFixLogs.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmDashboard.Designer.vb">
      <DependentUpon>frmDashboard.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmRelatedContacts.Designer.vb">
      <DependentUpon>frmRelatedContacts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmReleaseNotes.Designer.vb">
      <DependentUpon>frmReleaseNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmSqlScriptsExpr.Designer.vb">
      <DependentUpon>frmSqlScriptsExpr.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmSqlScriptsParamAutoRun.Designer.vb">
      <DependentUpon>frmSqlScriptsParamAutoRun.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmFiling.Designer.vb">
      <DependentUpon>frmFiling.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmTaxNoticeAiJson.Designer.vb">
      <DependentUpon>frmTaxNoticeAiJson.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmTaxNotices.Designer.vb">
      <DependentUpon>frmTaxNotices.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmTaxSheet.Designer.vb">
      <DependentUpon>frmTaxSheet.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frm_OT_ST_FilingCtrl.Designer.vb">
      <DependentUpon>frm_OT_ST_FilingCtrl.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmLoginAudit.Designer.vb">
      <DependentUpon>frmLoginAudit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmPaydeckAuthUserEvents.Designer.vb">
      <DependentUpon>frmPaydeckAuthUserEvents.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmPaydeckBannerAddOrEdit.Designer.vb">
      <DependentUpon>frmPaydeckBannerAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmPayDeckBanners.Designer.vb">
      <DependentUpon>frmPayDeckBanners.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmPaydeckInviteUser.Designer.vb">
      <DependentUpon>frmPaydeckInviteUser.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmPaydeckAllUsers.Designer.vb">
      <DependentUpon>frmPaydeckAllUsers.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\ucPaydeckUsers.Designer.vb">
      <DependentUpon>ucPaydeckUsers.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\ucPaydeckUsers.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\QuarterEnd\frmCollectAndSaveReports.Designer.vb">
      <DependentUpon>frmCollectAndSaveReports.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmEmailReport.Designer.vb">
      <DependentUpon>frmEmailReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmDraggableAttachments.Designer.vb">
      <DependentUpon>frmDraggableAttachments.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmParamDropDownEditor.Designer.vb">
      <DependentUpon>frmParamDropDownEditor.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmReportsSavedParameters.Designer.vb">
      <DependentUpon>frmReportsSavedParameters.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmSqlScriptAddOrEditAdvancedOptions.Designer.vb">
      <DependentUpon>frmSqlScriptAddOrEditAdvancedOptions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmSqlScriptLogCompletion.Designer.vb">
      <DependentUpon>frmSqlScriptLogCompletion.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReportQueuePending.Designer.vb">
      <DependentUpon>ucReportQueuePending.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReportQueuePending.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Reports\ucReportsQueue.Designer.vb">
      <DependentUpon>ucReportsQueue.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReportsQueue.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Setup\frmAddNewRole.Designer.vb">
      <DependentUpon>frmAddNewRole.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Shared\frmExportSetup.Designer.vb">
      <DependentUpon>frmExportSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmACACost.Designer.vb">
      <DependentUpon>frmACACost.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmEncrypt.Designer.vb">
      <DependentUpon>frmEncrypt.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmFix941Negatives.Designer.vb">
      <DependentUpon>frmFix941Negatives.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmImportPbxReport.Designer.vb">
      <DependentUpon>frmImportPbxReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmImportPunchReport.Designer.vb">
      <DependentUpon>frmImportPunchReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmQbExportSetup.Designer.vb">
      <DependentUpon>frmQbExportSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmResendDDVoucher.Designer.vb">
      <DependentUpon>frmResendDDVoucher.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmUpdatePayrollPeriod.Designer.vb">
      <DependentUpon>frmUpdatePayrollPeriod.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\ucEmailsSearch.Designer.vb">
      <DependentUpon>ucEmailsSearch.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\ucEmailsSearch.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyRelatedContacts.Designer.vb">
      <DependentUpon>ucCompanyRelatedContacts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyRelatedContacts.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyServices.Designer.vb">
      <DependentUpon>ucCompanyServices.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyServices.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Controls\frmAuditLog.Designer.vb">
      <DependentUpon>frmAuditLog.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\FilePreviewUserControl.Designer.vb">
      <DependentUpon>FilePreviewUserControl.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\FilePreviewUserControl.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Banking\frmDDReversalStatusUpdate.Designer.vb">
      <DependentUpon>frmDDReversalStatusUpdate.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\FrontDeskFeedback\frmErrorWindow.Designer.vb">
      <DependentUpon>frmErrorWindow.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\frmManualBillingDraft.Designer.vb">
      <DependentUpon>frmManualBillingDraft.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\frmManualBillingDraftAdd.Designer.vb">
      <DependentUpon>frmManualBillingDraftAdd.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\FrontDeskFeedback\frmFeedbacks.Designer.vb">
      <DependentUpon>frmFeedbacks.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\FrontDeskFeedback\frmReviewFeedback.Designer.vb">
      <DependentUpon>frmReviewFeedback.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\FrontDeskFeedback\frmSubmitFeedback.Designer.vb">
      <DependentUpon>frmSubmitFeedback.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmReportWizard.Designer.vb">
      <DependentUpon>frmReportWizard.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReports.Designer.vb">
      <DependentUpon>ucReports.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReports.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Ach\frmAchAutoEmails.Designer.vb">
      <DependentUpon>frmAchAutoEmails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Ach\frmAchSettings.Designer.vb">
      <DependentUpon>frmAchSettings.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\ucShugoFileGrid.Designer.vb">
      <DependentUpon>ucShugoFileGrid.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\ucShugoFileGrid.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Billing\ucSwipeClockGrid.Designer.vb">
      <DependentUpon>ucSwipeClockGrid.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\ucSwipeClockGrid.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\frmCompanyActivityLog.Designer.vb">
      <DependentUpon>frmCompanyActivityLog.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmCompanyDocumentsAddOrEdit.Designer.vb">
      <DependentUpon>frmCompanyDocumentsAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmEmployeeAddOrEdit.Designer.vb">
      <DependentUpon>frmEmployeeAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmQuarterEndHoldLog.Designer.vb">
      <DependentUpon>frmQuarterEndHoldLog.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmRenumberEmployees.Designer.vb">
      <DependentUpon>frmRenumberEmployees.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmUpdateNYDependentHealth.Designer.vb">
      <DependentUpon>frmUpdateNYDependentHealth.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\QuarterEnd\frmUpdateQtrEndHold.Designer.vb">
      <DependentUpon>frmUpdateQtrEndHold.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmCompanyBillingOverrides.Designer.vb">
      <DependentUpon>frmCompanyBillingOverrides.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyActivityHistoryWidget.Designer.vb">
      <DependentUpon>ucCompanyActivityHistoryWidget.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyActivityHistoryWidget.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyDocuments.Designer.vb">
      <DependentUpon>ucCompanyDocuments.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanyDocuments.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\frmEmailComp.Designer.vb">
      <DependentUpon>frmEmailComp.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmUpdateUdf20.Designer.vb">
      <DependentUpon>frmUpdateUdf20.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucEmployeeList.Designer.vb">
      <DependentUpon>ucEmployeeList.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucEmployeeList.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucEmployeePayHistory.Designer.vb">
      <DependentUpon>ucEmployeePayHistory.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucEmployeePayHistory.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucShippingAddress.Designer.vb">
      <DependentUpon>ucShippingAddress.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucShippingAddress.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucEmployeeInfo.Designer.vb">
      <DependentUpon>ucEmployeeInfo.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucEmployeeInfo.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmReportScheduler.Designer.vb">
      <DependentUpon>frmReportScheduler.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ReportBuilder\ucAvaillableColumns.Designer.vb">
      <DependentUpon>ucAvaillableColumns.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ReportBuilder\ucAvaillableColumns.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\frmColumnTemplateSetup.Designer.vb">
      <DependentUpon>frmColumnTemplateSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\frmColumnTemplatOptions.Designer.vb">
      <DependentUpon>frmColumnTemplatOptions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\frmCustomReportAddOrEdit.Designer.vb">
      <DependentUpon>frmCustomReportAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmReportBuilder.Designer.vb">
      <DependentUpon>frmReportBuilder.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmReportDesignerClosing.Designer.vb">
      <DependentUpon>frmReportDesignerClosing.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmReportLayoutAddOrEdit.Designer.vb">
      <DependentUpon>frmReportLayoutAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\frmReportLinkTables.Designer.vb">
      <DependentUpon>frmReportLinkTables.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ucReportWizard.Designer.vb">
      <DependentUpon>ucReportWizard.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ucReportWizard.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmShareReport.Designer.vb">
      <DependentUpon>frmShareReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\frmTableAddOrEdit.Designer.vb">
      <DependentUpon>frmTableAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ucColumnMouseMove.Designer.vb">
      <DependentUpon>ucColumnMouseMove.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ucColumnMouseMove.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\ucColumnTemplateOptions.Designer.vb">
      <DependentUpon>ucColumnTemplateOptions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\ucColumnTemplateOptions.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ReportBuilder\ucReportOrderGroup.Designer.vb">
      <DependentUpon>ucReportOrderGroup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ReportBuilder\ucReportOrderGroup.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Documantation\frmDocumentationEditWord.Designer.vb">
      <DependentUpon>frmDocumentationEditWord.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmAcaServiceOptOut.Designer.vb">
      <DependentUpon>frmAcaServiceOptOut.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmAcaServiceOptOutAddOrEdit.Designer.vb">
      <DependentUpon>frmAcaServiceOptOutAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\frmAccountLeadAddOrEdit.Designer.vb">
      <DependentUpon>frmAccountLeadAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\AccountLeads\frmAccountLeads.Designer.vb">
      <DependentUpon>frmAccountLeads.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Ach\frmAchTransactions.Designer.vb">
      <DependentUpon>frmAchTransactions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Banking\frmDDReversalDetails.Designer.vb">
      <DependentUpon>frmDDReversalDetails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmBrowser.Designer.vb">
      <DependentUpon>frmBrowser.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmCalculateChecks.Designer.vb">
      <DependentUpon>frmCalculateChecks.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalendarNoMorePayroll.Designer.vb">
      <DependentUpon>frmCalendarNoMorePayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCheckFigures.Designer.vb">
      <DependentUpon>frmCheckFigures.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCompanyUnionUpdateWageRate.Designer.vb">
      <DependentUpon>frmCompanyUnionUpdateWageRate.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Documantation\frmDocumentation.Designer.vb">
      <DependentUpon>frmDocumentation.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Documantation\frmDocumentationAddOrEdit.Designer.vb">
      <DependentUpon>frmDocumentationAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Banking\frmDDReversal.Designer.vb">
      <DependentUpon>frmDDReversal.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmAssignTicket.Designer.vb">
      <DependentUpon>frmAssignTicket.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmComposeEmail.Designer.vb">
      <DependentUpon>frmComposeEmail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmpIncentive.Designer.vb">
      <DependentUpon>frmEmpIncentive.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmployeeDDConfirmation.Designer.vb">
      <DependentUpon>frmEmployeeDDConfirmation.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmEmployeeRateChange.Designer.vb">
      <DependentUpon>frmEmployeeRateChange.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmExecupayApiManagement.Designer.vb">
      <DependentUpon>frmExecupayApiManagement.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmExistingProcessList.Designer.vb">
      <DependentUpon>frmExistingProcessList.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmFaxAndEmail.Designer.vb">
      <DependentUpon>frmFaxAndEmail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmFaxesAdvancedSearch.Designer.vb">
      <DependentUpon>frmFaxesAdvancedSearch.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmFaxesCategoryFavorites.Designer.vb">
      <DependentUpon>frmFaxesCategoryFavorites.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmImportW2HealthCare.Designer.vb">
      <DependentUpon>frmImportW2HealthCare.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNewTaxRecon.Designer.vb">
      <DependentUpon>frmNewTaxRecon.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmOpenDoor.Designer.vb">
      <DependentUpon>frmOpenDoor.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Payroll\frmVoidCheck.Designer.vb">
      <DependentUpon>frmVoidCheck.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmCompanyOpenOrders.Designer.vb">
      <DependentUpon>frmCompanyOpenOrders.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmOrderReports.Designer.vb">
      <DependentUpon>frmOrderReports.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmOrdersBilling.Designer.vb">
      <DependentUpon>frmOrdersBilling.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmOrderShippingAddressSelection.Designer.vb">
      <DependentUpon>frmOrderShippingAddressSelection.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollOnlineClientCalledIn.Designer.vb">
      <DependentUpon>frmPayrollOnlineClientCalledIn.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPdfCombine.Designer.vb">
      <DependentUpon>frmPdfCombine.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmCloseCompanyTabs.Designer.vb">
      <DependentUpon>frmCloseCompanyTabs.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCallBackTickets.Designer.vb">
      <DependentUpon>ucCallBackTickets.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCallBackTickets.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompanySummary.Designer.vb">
      <DependentUpon>ucCompanySummary.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompanySummary.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompInfo.Designer.vb">
      <DependentUpon>ucCompInfo.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompInfo.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucCompNotes.Designer.vb">
      <DependentUpon>ucCompNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucCompNotes.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucMostRecentPayroll.Designer.vb">
      <DependentUpon>ucMostRecentPayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucMostRecentPayroll.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\frmCompanySumarry.Designer.vb">
      <DependentUpon>frmCompanySumarry.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucNextScheduledPayroll.Designer.vb">
      <DependentUpon>ucNextScheduledPayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucNextScheduledPayroll.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Company\ucSearchCompany.Designer.vb">
      <DependentUpon>ucSearchCompany.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucSearchCompany.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Payroll\frmAddVoid.Designer.vb">
      <DependentUpon>frmAddVoid.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmGetDDDrafts.Designer.vb">
      <DependentUpon>frmGetDDDrafts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPDFViewer.Designer.vb">
      <DependentUpon>frmPDFViewer.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Setup\frmPermissions.Designer.vb">
      <DependentUpon>frmPermissions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPhone.Designer.vb">
      <DependentUpon>frmPhone.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPhoneFax.Designer.vb">
      <DependentUpon>frmPhoneFax.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNewVarianceDraftReport.designer.vb">
      <DependentUpon>frmNewVarianceDraftReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPrintSchedular.Designer.vb">
      <DependentUpon>frmPrintSchedular.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmReceiveInventory.Designer.vb">
      <DependentUpon>frmReceiveInventory.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmSelectCompany.Designer.vb">
      <DependentUpon>frmSelectCompany.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmRunUtilityImport.Designer.vb">
      <DependentUpon>frmRunUtilityImport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Utilities\frmSearchEmployeeOrEmail.Designer.vb">
      <DependentUpon>frmSearchEmployeeOrEmail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmSignalrPopup.Designer.vb">
      <DependentUpon>frmSignalrPopup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Billing\frmBillingImports.Designer.vb">
      <DependentUpon>frmBillingImports.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmUDF.Designer.vb">
      <DependentUpon>frmUDF.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmUDFAddOrEdit.Designer.vb">
      <DependentUpon>frmUDFAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmOrderHistoryDetails.Designer.vb">
      <DependentUpon>frmOrderHistoryDetails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmReprintCoverSheet.Designer.vb">
      <DependentUpon>frmReprintCoverSheet.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmSetupProducts.Designer.vb">
      <DependentUpon>frmSetupProducts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmWaiveChargeReason.Designer.vb">
      <DependentUpon>frmWaiveChargeReason.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ReportBuilder\ucColumnCustomize.Designer.vb">
      <DependentUpon>ucColumnCustomize.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ReportBuilder\ucColumnCustomize.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\frmErrorDetails.Designer.vb">
      <DependentUpon>frmErrorDetails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\Setup\frmCustomReportSetup.Designer.vb">
      <DependentUpon>frmCustomReportSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmDeclareSqlParameters.Designer.vb">
      <DependentUpon>frmDeclareSqlParameters.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmEmailDesigner.Designer.vb">
      <DependentUpon>frmEmailDesigner.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmParameters.Designer.vb">
      <DependentUpon>frmParameters.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmPivoteGridReport.Designer.vb">
      <DependentUpon>frmPivoteGridReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmSqlScriptsCategoriesSetup.Designer.vb">
      <DependentUpon>frmSqlScriptsCategoriesSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Setup\frmFaxCategories.Designer.vb">
      <DependentUpon>frmFaxCategories.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Setup\frmFaxCategoriesAddOrEdit.Designer.vb">
      <DependentUpon>frmFaxCategoriesAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmNewNote.Designer.vb">
      <DependentUpon>frmNewNote.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmSystemEmailSent.Designer.vb">
      <DependentUpon>frmSystemEmailSent.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\UcEmail.Designer.vb">
      <DependentUpon>UcEmail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\UcEmail.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxMoveMouse.Designer.vb">
      <DependentUpon>ucFaxMoveMouse.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxMoveMouse.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ucInputBox.Designer.vb">
      <DependentUpon>ucInputBox.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ReportBuilder\ucInputBox.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Orders\ucOrdersQueue.Designer.vb">
      <DependentUpon>ucOrdersQueue.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\ucOrdersQueue.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucTicket.Designer.vb">
      <DependentUpon>ucTicket.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucTicket.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucTicketNotes.Designer.vb">
      <DependentUpon>ucTicketNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucTicketNotes.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmConnectedSessions.Designer.vb">
      <DependentUpon>frmConnectedSessions.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmEmployeeLogins.Designer.vb">
      <DependentUpon>frmEmployeeLogins.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmHelpCenterSetup.Designer.vb">
      <DependentUpon>frmHelpCenterSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmReportrCategorySetup.Designer.vb">
      <DependentUpon>frmReportrCategorySetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmReportsSetup.Designer.vb">
      <DependentUpon>frmReportsSetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\PaydeckSetup\frmReportsSubTabs.designer.vb">
      <DependentUpon>frmReportsSubTabs.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\frmDescriptionRichEdit.Designer.vb">
      <DependentUpon>frmDescriptionRichEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\frmSelectFolder.Designer.vb">
      <DependentUpon>frmSelectFolder.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\frmSetUdfFields.Designer.vb">
      <DependentUpon>frmSetUdfFields.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\frmWrike.Designer.vb">
      <DependentUpon>frmWrike.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmSqlScriptAddOrEdit.Designer.vb">
      <DependentUpon>frmSqlScriptAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmployeeAutoPay.Designer.vb">
      <DependentUpon>frmEmployeeAutoPay.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Payroll\frmManualChecks.Designer.vb">
      <DependentUpon>frmManualChecks.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmSqlScripts.Designer.vb">
      <DependentUpon>frmSqlScripts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmReportEmailTemplateAddOrEdit.Designer.vb">
      <DependentUpon>frmReportEmailTemplateAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollStatusNotes.Designer.vb">
      <DependentUpon>frmPayrollStatusNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmChangePassword.Designer.vb">
      <DependentUpon>frmChangePassword.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmReportMassEmailProcess.Designer.vb">
      <DependentUpon>frmReportMassEmailProcess.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmReportsMassEmail.Designer.vb">
      <DependentUpon>frmReportsMassEmail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmReportsMassEmailAddOrEdit.Designer.vb">
      <DependentUpon>frmReportsMassEmailAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmRunAutosLog.Designer.vb">
      <DependentUpon>frmRunAutosLog.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEditNotes.Designer.vb">
      <DependentUpon>frmEditNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxAndEmail.Designer.vb">
      <DependentUpon>ucFaxAndEmail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxAndEmail.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\QuarterEnd\frmQuarterEndEmailReports.Designer.vb">
      <DependentUpon>frmQuarterEndEmailReports.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\frmReportsParameters.Designer.vb">
      <DependentUpon>frmReportsParameters.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmYearEndDecPayroll.Designer.vb">
      <DependentUpon>frmYearEndDecPayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucRecentReports.Designer.vb">
      <DependentUpon>ucRecentReports.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucRecentReports.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Reports\ucReportLibrary.Designer.vb">
      <DependentUpon>ucReportLibrary.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReportLibrary.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Reports\ucReportEmailTemplate.Designer.vb">
      <DependentUpon>ucReportEmailTemplate.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Reports\ucReportEmailTemplate.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\SplashScreen1.Designer.vb">
      <DependentUpon>SplashScreen1.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmBrandsPowerGrid.Designer.vb">
      <DependentUpon>frmBrandsPowerGrid.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmBrandsPowerGridList.Designer.vb">
      <DependentUpon>frmBrandsPowerGridList.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmChangeRateAutoPay.Designer.vb">
      <DependentUpon>frmChangeRateAutoPay.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCompanyUnion.Designer.vb">
      <DependentUpon>frmCompanyUnion.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\UcFax.Designer.vb">
      <DependentUpon>UcFax.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\UcFax.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmFaxes.Designer.vb">
      <DependentUpon>frmFaxes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmMain.Designer.vb">
      <DependentUpon>frmMain.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNotes.Designer.vb">
      <DependentUpon>frmNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNotesPopUp.Designer.vb">
      <DependentUpon>frmNotesPopUp.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollStatus.Designer.vb">
      <DependentUpon>frmPayrollStatus.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmStartNewPayrollMultiSet.Designer.vb">
      <DependentUpon>frmStartNewPayrollMultiSet.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmBrandsPowerGridProcess.Designer.vb">
      <DependentUpon>frmBrandsPowerGridProcess.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmActivatePayDeds.Designer.vb">
      <DependentUpon>frmActivatePayDeds.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmChangeAutoDedStartDate.Designer.vb">
      <DependentUpon>frmChangeAutoDedStartDate.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCommCompanysetup.Designer.vb">
      <DependentUpon>frmCommCompanysetup.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCommPlans.Designer.vb">
      <DependentUpon>frmCommPlans.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmConfirmPassword.Designer.vb">
      <DependentUpon>frmConfirmPassword.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\frmCoOptionsPayroll.Designer.vb">
      <DependentUpon>frmCoOptionsPayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmDDMonitor.designer.vb">
      <DependentUpon>frmDDMonitor.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmployeeAutoPaysAndDeds.Designer.vb">
      <DependentUpon>frmEmployeeAutoPaysAndDeds.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmployeeBatchNotesAll.Designer.vb">
      <DependentUpon>frmEmployeeBatchNotesAll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmAddDeliveryTicket.Designer.vb">
      <DependentUpon>frmAddDeliveryTicket.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmAddNewEmployee.Designer.vb">
      <DependentUpon>frmAddNewEmployee.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalendar.Designer.vb">
      <DependentUpon>frmCalendar.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalendarDetail.Designer.vb">
      <DependentUpon>frmCalendarDetail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalendarLinkToPayroll.Designer.vb">
      <DependentUpon>frmCalendarLinkToPayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalendarNotesAll.Designer.vb">
      <DependentUpon>frmCalendarNotesAll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmChangeRate.Designer.vb">
      <DependentUpon>frmChangeRate.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCompanyList.Designer.vb">
      <DependentUpon>frmCompanyList.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmDeliveries.Designer.vb">
      <DependentUpon>frmDeliveries.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucDeliveryHistory.Designer.vb">
      <DependentUpon>ucDeliveryHistory.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Company\ucDeliveryHistory.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\frmDuplicateScan.Designer.vb">
      <DependentUpon>frmDuplicateScan.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmployeeInfoOld.Designer.vb">
      <DependentUpon>frmEmployeeInfoOld.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmEmployeeNotes.Designer.vb">
      <DependentUpon>frmEmployeeNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmErrorLogEntry.Designer.vb">
      <DependentUpon>frmErrorLogEntry.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmMinimumWageList.Designer.vb">
      <DependentUpon>frmMinimumWageList.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNachaTaxDrafts.Designer.vb">
      <DependentUpon>frmNachaTaxDrafts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNachaTaxDraftsDetail.Designer.vb">
      <DependentUpon>frmNachaTaxDraftsDetail.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmOrderHistory.Designer.vb">
      <DependentUpon>frmOrderHistory.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Orders\frmOrderSupplies.Designer.vb">
      <DependentUpon>frmOrderSupplies.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollAlertDetails.Designer.vb">
      <DependentUpon>frmPayrollAlertDetails.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollAlerts.Designer.vb">
      <DependentUpon>frmPayrollAlerts.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPayrollsInProcessList.Designer.vb">
      <DependentUpon>frmPayrollsInProcessList.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPrintingScan.Designer.vb">
      <DependentUpon>frmPrintingScan.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmPrintingScans.Designer.vb">
      <DependentUpon>frmPrintingScans.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmQuarterlyCentral.Designer.vb">
      <DependentUpon>frmQuarterlyCentral.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmReport.Designer.vb">
      <DependentUpon>frmReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmRunAutos.Designer.vb">
      <DependentUpon>frmRunAutos.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmStartNewPayroll.Designer.vb">
      <DependentUpon>frmStartNewPayroll.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmTaxRecon.designer.vb">
      <DependentUpon>frmTaxRecon.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\frmCallBackRequest.Designer.vb">
      <DependentUpon>frmCallBackRequest.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmVarianceDraftReport.designer.vb">
      <DependentUpon>frmVarianceDraftReport.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxAndEmailDockPanel.Designer.vb">
      <DependentUpon>ucFaxAndEmailDockPanel.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxAndEmailDockPanel.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxAndEmailGrid.Designer.vb">
      <DependentUpon>ucFaxAndEmailGrid.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\TicketingSystem\ucFaxAndEmailGrid.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\ucNotes.Designer.vb">
      <DependentUpon>ucNotes.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\ucNotes.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Wrike\frmWrikeAddOrEdit.Designer.vb">
      <DependentUpon>frmWrikeAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\ucWrikeAddOrEdit.Designer.vb">
      <DependentUpon>ucWrikeAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\ucWrikeAddOrEdit.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Wrike\ucWrikeFolderProjectAddOrEdit.Designer.vb">
      <DependentUpon>ucWrikeFolderProjectAddOrEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Wrike\ucWrikeFolderProjectAddOrEdit.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Zendesk\frmCreateOrUpdateUser.Designer.vb">
      <DependentUpon>frmCreateOrUpdateUser.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Zendesk\frmSelectNewOrExistingTicket.Designer.vb">
      <DependentUpon>frmSelectNewOrExistingTicket.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Zendesk\frmTest.Designer.vb">
      <DependentUpon>frmTest.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\Zendesk\frmZendeskTicket.Designer.vb">
      <DependentUpon>frmZendeskTicket.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmAcaFileBatch.Designer.vb">
      <DependentUpon>frmAcaFileBatch.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmBankingFileTracker.Designer.vb">
      <DependentUpon>frmBankingFileTracker.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalanderRulesTimeEdit.Designer.vb">
      <DependentUpon>frmCalanderRulesTimeEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCalenderRulesTimeEdit.Designer.vb">
      <DependentUpon>frmCalenderRulesTimeEdit.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmHoursOnly.Designer.vb">
      <DependentUpon>frmHoursOnly.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmMatchWire.Designer.vb">
      <DependentUpon>frmMatchWire.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmNacha.Designer.vb">
      <DependentUpon>frmNacha.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmRemovePwd.Designer.vb">
      <DependentUpon>frmRemovePwd.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmCompTaxDashboard.Designer.vb">
      <DependentUpon>frmCompTaxDashboard.vb</DependentUpon>
    </Compile>
    <Compile Update="Forms\frmTemp.Designer.vb">
      <DependentUpon>frmTemp.vb</DependentUpon>
    </Compile>
    <Compile Update="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Remove="Resources\resources.resx" />
    <Compile Update="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Update="ucUserLogs.Designer.vb">
      <DependentUpon>ucUserLogs.vb</DependentUpon>
    </Compile>
    <Compile Update="ucUserLogs.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="WaitForm1.Designer.vb">
      <DependentUpon>WaitForm1.vb</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="BrandsLogo.ico" />
    <None Include="..\.editorconfig">
      <Link>.editorconfig</Link>
    </None>
    <None Update="Code\test.dbml">
      <Generator>MSLinqToSQLGenerator</Generator>
      <LastGenOutput>test.designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Update="Code\test.dbml.layout">
      <DependentUpon>test.dbml</DependentUpon>
    </None>
    <None Include="My Project\DataSources\TaxNotice1.datasource" />
    <None Include="Resources\DirectEEMail.png" />
    <None Include="Resources\bounced check1.png" />
    <Content Include="x86\SNI.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="log4net.xml" />
    <None Include="My Project\DataSources\ACA_ServiceOptOutLog.datasource" />
    <None Include="My Project\DataSources\AchTransactionsLog.datasource" />
    <None Include="My Project\DataSources\Brands_FrontDesk.FingerCheckAPI.Employee.datasource" />
    <None Include="My Project\DataSources\Brands_FrontDesk.WrikeApi.CustomField.datasource" />
    <None Include="My Project\DataSources\COUSERDEF.datasource" />
    <None Include="My Project\DataSources\CO_UDF.datasource" />
    <None Include="My Project\DataSources\CustomReportColumn1.datasource" />
    <None Include="My Project\DataSources\DOCUMENT.datasource" />
    <None Include="My Project\DataSources\frmFaxes.FaxAdvancedSearch.datasource" />
    <None Include="My Project\DataSources\System.Data.SqlClient.SqlParameter.datasource" />
    <None Include="My Project\DataSources\System.Reflection.ICustomTypeProvider.datasource" />
    <None Include="My Project\DataSources\UDF.datasource" />
    <None Include="My Project\DataSources\view_CompanySumarry1.datasource" />
    <None Include="My Project\DataSources\Brands_FrontDesk.TelebroadApi.line_item.datasource" />
    <None Include="My Project\DataSources\Brands_FrontDesk.WrikeApi.Tasks.datasource" />
    <None Include="My Project\DataSources\ConnectedChannels.datasource" />
    <None Include="My Project\DataSources\FaxCoverSheet.datasource" />
    <None Include="My Project\DataSources\fn_GetPayrollReportsResult.datasource" />
    <None Include="My Project\DataSources\frmPhone.FaxCoverSheet.datasource" />
    <None Include="My Project\DataSources\MAN_CHK_MAST.datasource" />
    <None Include="My Project\DataSources\PhoneCall.datasource" />
    <None Include="My Project\DataSources\prc_GetDD_DraftsResult.datasource" />
    <None Include="My Project\DataSources\prc_GetVoidsToVoidResult.datasource" />
    <None Include="My Project\DataSources\REPORTS_UNION.datasource" />
    <None Include="My Project\DataSources\SCHFED_FORM.datasource" />
    <None Include="My Project\DataSources\fn_NextScheduledPayrollResult.datasource" />
    <None Include="My Project\DataSources\UnionRate.datasource" />
    <Content Include="sql.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <EmbeddedResource Include="Code\Reports\rptVarianceDreft - Copy.rdlc" />
    <EmbeddedResource Include="Code\Reports\rptVarianceDreft.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Code\Reports\rptOrderSuppliesInvoice.rdlc" />
    <EmbeddedResource Include="Code\Reports\rptDeliveryReport.rdlc" />
    <EmbeddedResource Include="Code\Reports\rptDeliverySummery.rdlc" />
    <EmbeddedResource Include="Code\Reports\rptDeliveryTicket.rdlc" />
    <EmbeddedResource Include="Code\Reports\rptOrderSuppliesLabel.rdlc" />
    <EmbeddedResource Include="Code\Aspose.Cells.lic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <Content Include="EmployeeTimeSheet.rpt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="My Project\DataSources\ActionLogTypeEventArgs.datasource" />
    <None Include="My Project\DataSources\ACT_ITEM.datasource" />
    <None Include="My Project\DataSources\AutoPayrollsLog.datasource" />
    <None Include="My Project\DataSources\CalendarInfo.datasource" />
    <None Include="My Project\DataSources\CommCompanySetup.datasource" />
    <None Include="My Project\DataSources\CommPlan.datasource" />
    <None Include="My Project\DataSources\CommPlanDetail.datasource" />
    <None Include="My Project\DataSources\CompanySummary.datasource" />
    <None Include="My Project\DataSources\COOPTION.datasource" />
    <None Include="My Project\DataSources\CoOptions_Payroll.datasource" />
    <None Include="My Project\DataSources\CoOptions_SecondCheckPayCode.datasource" />
    <None Include="My Project\DataSources\CO_JOB.datasource" />
    <None Include="My Project\DataSources\DEDUCTION.datasource" />
    <None Include="My Project\DataSources\DEPARTMENT.datasource" />
    <None Include="My Project\DataSources\DIVISION.datasource" />
    <None Include="My Project\DataSources\Email.datasource" />
    <None Include="My Project\DataSources\EMPLOYEE.datasource" />
    <None Include="My Project\DataSources\EMP_OP.datasource" />
    <None Include="My Project\DataSources\Fax.datasource" />
    <None Include="My Project\DataSources\FaxCategory.datasource" />
    <None Include="My Project\DataSources\FrontDeskPermission.datasource" />
    <None Include="My Project\DataSources\LOCAL_EE_INFO.datasource" />
    <None Include="My Project\DataSources\MEMO.datasource" />
    <None Include="My Project\DataSources\NotesForm.datasource" />
    <None Include="My Project\DataSources\OTHER_PAY.datasource" />
    <None Include="My Project\DataSources\PayrollReopenedNote.datasource" />
    <None Include="My Project\DataSources\prc_GetManualChecksResult.datasource" />
    <None Include="My Project\DataSources\prc_GetPayrollsReadyToPrintResult.datasource" />
    <None Include="My Project\DataSources\prc_QtrEndEmailResult.datasource" />
    <None Include="My Project\DataSources\prc_TaxReconDetailsResult.datasource" />
    <None Include="My Project\DataSources\prc_YearEnd_WithNoDecPayrollResult.datasource" />
    <None Include="My Project\DataSources\ProcessPowergridStatus.datasource" />
    <None Include="My Project\DataSources\pr_batch_hours_only.datasource" />
    <None Include="My Project\DataSources\pr_batch_msg.datasource" />
    <None Include="My Project\DataSources\pr_batch_override.datasource" />
    <None Include="My Project\DataSources\pr_batch_overrides_setup.datasource" />
    <None Include="My Project\DataSources\ReportEmailTeplate.datasource" />
    <None Include="My Project\DataSources\ReportMassEmailFile.datasource" />
    <None Include="My Project\DataSources\ReportMassEmailPdf.datasource" />
    <None Include="My Project\DataSources\ReportMassEmailTemplate.datasource" />
    <None Include="My Project\DataSources\SALESPEOPLE.datasource" />
    <None Include="My Project\DataSources\SqlScript.datasource" />
    <None Include="My Project\DataSources\STATE_EE_INFO.datasource" />
    <None Include="My Project\DataSources\UnionDepartment.datasource" />
    <None Include="My Project\DataSources\view_FaxAndEmail.datasource" />
    <None Include="My Project\DataSources\view_MinimumWageReq.datasource" />
    <None Include="My Project\DataSources\view_PayrollInProcess.datasource" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Code\dsMain.xsc">
      <DependentUpon>dsMain.xsd</DependentUpon>
    </None>
    <None Update="Code\dsMain.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>dsMain.Designer.vb</LastGenOutput>
    </None>
    <None Update="Code\dsMain.xss">
      <DependentUpon>dsMain.xsd</DependentUpon>
    </None>
    <Content Include="connectionStrings.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Update="Resources\en_US.aff">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\en_US.dic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="My Project\DataSources\CALENDAR.datasource" />
    <None Include="My Project\DataSources\CalendarNote.datasource" />
    <None Include="My Project\DataSources\COMPANY.datasource" />
    <None Include="My Project\DataSources\CompanySummery.datasource" />
    <None Include="My Project\DataSources\DBUSER.datasource" />
    <None Include="My Project\DataSources\Delivery.datasource" />
    <None Include="My Project\DataSources\DeliveryTicket.datasource" />
    <None Include="My Project\DataSources\ErrorLog.datasource" />
    <None Include="My Project\DataSources\NachaTaxDraft.datasource" />
    <None Include="My Project\DataSources\NOTE.datasource" />
    <None Include="My Project\DataSources\PAYROLL.datasource" />
    <None Include="My Project\DataSources\PayrollAlert.datasource" />
    <None Include="My Project\DataSources\prc_BrandsPowerGridColumnsResult.datasource" />
    <None Include="My Project\DataSources\prc_GetPayrollsNotAttachedToCalendersResult.datasource" />
    <None Include="My Project\DataSources\PrintingScan.datasource" />
    <None Include="My Project\DataSources\pr_batch_grid_column.datasource" />
    <None Include="My Project\DataSources\pr_batch_list.datasource" />
    <None Include="My Project\DataSources\pr_batch_note.datasource" />
    <None Include="My Project\DataSources\pr_batch_total.datasource" />
    <None Include="My Project\DataSources\SuppliesOrder.datasource" />
    <None Include="My Project\DataSources\SuppliesOrderItem.datasource" />
    <None Include="My Project\DataSources\SuppliesOrderItemDisplay.datasource" />
    <None Include="My Project\DataSources\view_Calendar.datasource" />
    <None Include="My Project\DataSources\view_Delivery.datasource" />
    <None Include="My Project\DataSources\view_PayrollAletsGrouped.datasource" />
    <None Include="My Project\DataSources\view_QuarterlyAllProcessAndDelivery.datasource" />
    <None Include="My Project\DataSources\view_QuarterlyShipAndEmailControl.datasource" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{3259AA49-8AA1-44D3-9025-A0B520596A8C}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.ReportViewer.12.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Report Viewer 2014 Runtime</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="email.htm">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="favicon.ico" />
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="Brands FrontDesk.pdb">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="C1.Win.C1FlexGrid.4">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="CDIntfNet">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Auto</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="CrystalDecisions.ReportAppServer.CommLayer">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="CrystalDecisions.ReportAppServer.CommonControls">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="CrystalDecisions.ReportAppServer.CubeDefModel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="CrystalDecisions.ReportAppServer.Prompting">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="EasyNetQ">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="EnvDTE">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Auto</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Execupay.Foundation.Client">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Execupay.License">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Execupay.WebPortal.Data">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="FlashControlV71">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Auto</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.Diagnostics.Tracing.EventSource">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.MSXML">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.DataVisualization">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.ProcessingObjectModel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.ConnectionInfo">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Diagnostics.STrace">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Dmf">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Management.Sdk.Sfc">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Management.SmoMetadataProvider">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Management.SqlParser">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.ServiceBrokerEnum">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Smo">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.SqlClrProvider">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.SqlEnum">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Designer.Interfaces">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.OLE.Interop">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.ProjectAggregator">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Shell.10.0">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Shell.Design">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Shell.Immutable.10.0">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>False</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Shell.Interop">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Shell.Interop.8.0">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.Shell.Interop.9.0">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.TextManager.Interop">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.TextManager.Interop.8.0">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.VisualStudio.VSHelp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.Win32.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="netfx.force.conflicts">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="netstandard">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="O2S.Components.PDFView4NET">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="PPJ.Runtime.4">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="PPJ.Runtime.Vis.4">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="PPJ.Runtime.XSal.4">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ShockwaveFlashObjects">
      <Visible>False</Visible>
      <Group>Execupay</Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Auto</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.AppContext">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Collections">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Collections.Concurrent">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Collections.NonGeneric">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Collections.Specialized">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.ComponentModel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.ComponentModel.EventBasedAsync">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.ComponentModel.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.ComponentModel.TypeConverter">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Console">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Data.Common">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.Contracts">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.Debug">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.FileVersionInfo">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.Process">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.StackTrace">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.TextWriterTraceListener">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.Tools">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.TraceSource">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Diagnostics.Tracing">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Drawing.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Dynamic.Runtime">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Globalization">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Globalization.Calendars">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Globalization.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.Compression">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.Compression.ZipFile">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.FileSystem">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.FileSystem.DriveInfo">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.FileSystem.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.FileSystem.Watcher">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.IsolatedStorage">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.MemoryMappedFiles">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.Pipes">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.IO.UnmanagedMemoryStream">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Linq">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Linq.Expressions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Linq.Parallel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Linq.Queryable">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.Http.Formatting">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.NameResolution">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.NetworkInformation">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.Ping">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.Requests">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.Security">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.Sockets">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.WebHeaderCollection">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.WebSockets">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Net.WebSockets.Client">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Numerics.Vectors">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.ObjectModel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Reflection">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Reflection.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Reflection.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Resources.Reader">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Resources.ResourceManager">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Resources.Writer">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.CompilerServices.VisualC">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Handles">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.InteropServices">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.InteropServices.RuntimeInformation">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Numerics">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Serialization.Formatters">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Serialization.Json">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Serialization.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Runtime.Serialization.Xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Claims">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Cryptography.Algorithms">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Cryptography.Csp">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Cryptography.Encoding">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Cryptography.Primitives">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Cryptography.X509Certificates">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.Principal">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Security.SecureString">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Text.Encoding">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Text.Encoding.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Text.RegularExpressions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading.Overlapped">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading.Tasks">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading.Tasks.Parallel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading.Thread">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading.ThreadPool">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Threading.Timer">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.ValueTuple">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Xml.ReaderWriter">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Xml.XDocument">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Xml.XmlDocument">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Xml.XmlSerializer">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Xml.XPath">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="System.Xml.XPath.XDocument">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="x86\SNI.dll">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="Microsoft.Office.Interop.Excel">
      <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>7</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Interop.Outlook">
      <Guid>{00062FFF-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>9</VersionMajor>
      <VersionMinor>4</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Brands.Core\Brands.Core.csproj" />
    <ProjectReference Include="..\Brands.DAL\Brands.DAL.csproj" />
    <ProjectReference Include="..\DXCustomControls\DXCustomControls.vbproj" />
    <ProjectReference Include="..\PwdEnc\PwdEnc.csproj" />
    <ProjectReference Include="..\QueueProcessors\QueueBuilder\QueueBuilder.csproj" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\Microsoft.DependencyValidation.Analyzers.0.11.0\analyzers\dotnet\Microsoft.DependencyValidation.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspose.Cells" Version="25.5.0" />
    <PackageReference Include="Azure.Core" Version="1.46.1" />
    <PackageReference Include="CommunityToolkit.Diagnostics" Version="8.4.0" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="DeltaCompressionDotNet" Version="2.0.1" />
    <PackageReference Include="Dynamsoft.DotNet.TWAIN" Version="8.3.3" />
    <PackageReference Include="EasyNetQ" Version="7.8.0" />
    <PackageReference Include="Flurl" Version="4.0.0" />
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="Going.Plaid" Version="6.43.0" />
    <PackageReference Include="Handlebars.Net" Version="2.1.6" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
    <PackageReference Include="Humanizer.Core" Version="2.14.1" />
    <PackageReference Include="log4net" Version="3.1.0" />
    <PackageReference Include="Microsoft.AspNet.SignalR.Client" Version="2.4.3" />
    <PackageReference Include="Microsoft.AspNetCore.Connections.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Connections.Client" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Connections.Common" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Common" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="9.0.5" />
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.5" />
    <PackageReference Include="Microsoft.Bcl.HashCode" Version="6.0.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.DependencyValidation.Analyzers" Version="0.11.0" />
    <PackageReference Include="Microsoft.Diagnostics.Tracing.EventRegister" Version="1.1.28" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Analyzers" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Primitives" Version="9.0.5" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.72.1" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.11.0" />
    <PackageReference Include="Microsoft.IdentityModel.Logging" Version="8.11.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="8.11.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.11.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.11.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PDFsharp" Version="6.2.0" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="Pubnub" Version="7.3.11" />
    <PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
    <PackageReference Include="Remotion.Linq" Version="2.2.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.PeriodicBatching" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageReference Include="Sigil" Version="5.0.0" />
    <PackageReference Include="System.Buffers" Version="4.6.1" />
    <PackageReference Include="System.Collections" Version="4.3.0" />
    <PackageReference Include="System.Collections.Immutable" Version="9.0.5" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.ComponentModel.Composition" Version="9.0.5" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.Diagnostics.Debug" Version="4.3.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.5" />
    <PackageReference Include="System.DirectoryServices" Version="9.0.5" />
    <PackageReference Include="System.Drawing.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Globalization" Version="4.3.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
    <PackageReference Include="System.IO" Version="4.3.0" />
    <PackageReference Include="System.IO.Pipelines" Version="9.0.5" />
    <PackageReference Include="System.Linq" Version="4.3.0" />
    <PackageReference Include="System.Memory" Version="4.6.3" />
    <PackageReference Include="System.Memory.Data" Version="9.0.5" />
    <PackageReference Include="System.Net.Http.Json" Version="9.0.5" />
    <PackageReference Include="System.Net.Sockets" Version="4.3.0" />
    <PackageReference Include="System.Numerics.Vectors" Version="4.6.1" />
    <PackageReference Include="System.Reflection" Version="4.3.0" />
    <PackageReference Include="System.Reflection.Emit.Lightweight" Version="4.7.0" />
    <PackageReference Include="System.Reflection.Extensions" Version="4.3.0" />
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.2" />
    <PackageReference Include="System.Runtime.Extensions" Version="4.3.1" />
    <PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" Version="4.3.0" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="System.Security.Cryptography.Encoding" Version="4.3.0" />
    <PackageReference Include="System.Security.Cryptography.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    <PackageReference Include="System.Text.Encoding" Version="4.3.0" />
    <PackageReference Include="System.Text.Encodings.Web" Version="9.0.5" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
    <PackageReference Include="System.Threading" Version="4.3.0" />
    <PackageReference Include="System.Threading.Channels" Version="9.0.5" />
    <PackageReference Include="System.Threading.Tasks" Version="4.3.0" />
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.6.3" />
    <PackageReference Include="System.ValueTuple" Version="4.6.1" />
    <PackageReference Include="ZendeskApi_v2" Version="3.12.4" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.v24.2">
      <HintPath>..\..\..\..\Program Files\DevExpress 24.2\Components\Bin\NetCore\DevExpress.Data.v24.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v24.2">
      <HintPath>..\..\..\..\Program Files\DevExpress 24.2\Components\Bin\NetCore\DevExpress.Utils.v24.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v24.2">
      <HintPath>..\..\..\..\Program Files\DevExpress 24.2\Components\Bin\NetCore\DevExpress.XtraEditors.v24.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGauges.v24.2.Design.Win">
      <HintPath>..\..\..\..\Program Files\DevExpress 24.2\Components\Bin\NetCore\Design\DevExpress.XtraGauges.v24.2.Design.Win.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v24.2">
      <HintPath>..\..\..\..\Program Files\DevExpress 24.2\Components\Bin\NetCore\DevExpress.XtraGrid.v24.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v24.2.Design">
      <HintPath>..\..\..\..\Program Files\DevExpress 24.2\Components\Bin\NetCore\Design\DevExpress.XtraGrid.v24.2.Design.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\save_16x16.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resources\save_16x161.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resources\save_16x162.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resources\save_32x32.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
</Project>