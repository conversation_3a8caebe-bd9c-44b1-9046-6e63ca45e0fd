﻿Imports System.ComponentModel
Imports System.Data
Imports DevExpress.Spreadsheet
Imports DevExpress.Spreadsheet.Export
Imports DevExpress.XtraEditors
Namespace Billing
    Public Class ucShugoFileGrid
        Implements IBillingImportService

        Private Logger As Serilog.ILogger

        Private Property db As dbEPDataDataContext
        Private Property billingUtilities As BillingUtilities

        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property ShugoFileData As List(Of ShugoFile)

        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property PriceCodesList As List(Of Integer) Implements IBillingImportService.PriceCodesList
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property globalBilling As List(Of ACT_ITEM) Implements IBillingImportService.globalBilling
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property InvoiceDate As DateTime Implements IBillingImportService.InvoiceDate

        Public ReadOnly Property Records As List(Of IRecord) Implements IBillingImportService.Records
            Get
                Return ShugoFileData.OfType(Of IRecord).ToList
            End Get
        End Property


        Public Event SelectedRowChanged(row As IRecord) Implements IBillingImportService.SelectedRowChanged
        Public Event SelectionChanged() Implements IBillingImportService.SelectionChanged


        Public Sub New()
            InitializeComponent()
            Logger = modGlobals.Logger.ForContext(Of ucShugoFileGrid)
            Initialize()
            GridView1.SetGridLayoutAndAddMenues("ShugoFileImport")
        End Sub

        Public Sub Initialize() Implements IBillingImportService.Initialize
            db = New dbEPDataDataContext(GetConnectionString)
            PriceCodesList = New List(Of Integer)([Enum].GetValues(GetType(PriceCodes)))
            globalBilling = db.ACT_ITEMS.Where(Function(b) PriceCodesList.Contains(b.ITEM_NUM)).ToList
            billingUtilities = New BillingUtilities(Me)
        End Sub

        Public Sub ImportFile() Implements IBillingImportService.ImportFile
            Dim fd = New OpenFileDialog()
            If fd.ShowDialog() = DialogResult.OK Then
                ShugoFileData = ImportFile(fd.FileName)
                Dim args = New XtraInputBoxArgs With {.Caption = "Invoice Date", .Prompt = "Please enter the invoice date"}
                args.Editor = New DateEdit
                Dim result = XtraInputBox.Show(args)
                If result IsNot Nothing Then
                    InvoiceDate = result
                End If
            End If
        End Sub

        Public Function ImportFile(fileName As String) As List(Of ShugoFile)
            Logger.Debug("Importing file {FileName}", fileName)
            Dim book = New Workbook()
            book.Options.Import.ThrowExceptionOnInvalidDocument = True
            book.LoadDocument(fileName)
            Dim sheet = book.Worksheets.ActiveWorksheet
            Dim table = sheet.CreateDataTable(sheet.GetUsedRange(), True)
            Dim exporter As DataTableExporter = sheet.CreateDataTableExporter(sheet.GetUsedRange(), table, True)
            exporter.Export()
            Dim list = New List(Of ShugoFile)

            For Each row As DataRow In table.Rows
                Dim sc = New ShugoFile()
                sc.Conum = row.Field(Of Double)("CompanyCode")
                sc.Coname = row.Field(Of String)("ClientName")
                sc.HubFullEmps = row.Field(Of Double)("HubFullSuiteCount")
                sc.HubPlusEmps = row.Field(Of Double)("HubPlusSuiteCount")
                sc.HubBasicEmps = row.Field(Of Double)("HubBasicSuiteCount")
                sc.HubHREmps = row.Field(Of Double)("HubHRCount")
                sc.HubFlightFull = row.Field(Of Double)("FlightFullEnabled")
                sc.HubFlightBasic = row.Field(Of Double)("FlightBasicEnabled")
                list.Add(sc)
            Next
            Return list
        End Function


        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property GetItemPrice As Func(Of Decimal, Decimal, Decimal, Decimal) = Function(conum, itemNumber, qty) billingUtilities.GetPrice(db, conum, itemNumber, qty)
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property GetOverrideItemPrice As Func(Of Decimal, Decimal, ACT_AUTO_PriceOverride) = Function(conum, itemNumber) db.ACT_AUTO_PriceOverrides.SingleOrDefault(Function(a) a.CONUM = conum AndAlso a.ITEM_NUM = itemNumber)

        Public Sub CalculatePrices() Implements IBillingImportService.CalculatePrices
            If ShugoFileData Is Nothing Then Exit Sub
            db = New dbEPDataDataContext(GetConnectionString)

            'Solomon modified on Aug 1, '21.  New invoice system
            'Dim invoiceLogs = (From i In db.INVOICE_xLOGs Where PriceCodesList.Contains(i.ITEM_NUM) AndAlso i.ITEM_DATE.Year = InvoiceDate.Year).ToList
            Dim invoiceLogs = (From i In db.invoice_item_details Join m In db.invoice_masters On m.conum Equals i.conum And m.invoice_key Equals i.invoice_key Where PriceCodesList.Contains(i.item_num) AndAlso Convert.ToDateTime(i.item_date).Year = InvoiceDate.Year AndAlso (i.item_deleted = 0 OrElse i.item_deleted Is Nothing) AndAlso (m.invoice_deleted = 0 OrElse m.invoice_deleted Is Nothing) Select i).ToList

            For Each item In ShugoFileData
                Try
                    If item.Conum > 0 Then
                        item.BaseFee = (GetOverrideItemPrice(item.Conum, PriceCodes.ShugoBaseFee)?.ACTUAL_PRICE).GetValueOrDefault
                        item.IsBaseFeeInvoiced = GetIsInvoiced(item.Conum, item.BaseFee, PriceCodes.ShugoBaseFee, invoiceLogs)

                        item.HubBasicPrice = GetItemPrice(item.Conum, PriceCodes.ShugoHubBasic, item.HubBasicEmps)
                        Dim stdMinBasicPrice = db.ACT_ITEMS.Single(Function(i) i.ITEM_NUM = PriceCodes.ShugoMinimumFee).STD_PRICE
                        Dim overrideMinBasicPrice = GetOverrideItemPrice(item.Conum, PriceCodes.ShugoMinimumFee)?.ACTUAL_PRICE
                        Dim minBasicPrice = If(overrideMinBasicPrice, stdMinBasicPrice)
                        If item.HubBasicPrice < minBasicPrice Then
                            item.HubBasicPrice = minBasicPrice
                        End If
                        item.IsHubBasicInvoiced = GetIsInvoiced(item.Conum, item.HubBasicPrice, PriceCodes.ShugoHubBasic, invoiceLogs)

                        item.HubFullPrice = GetItemPrice(item.Conum, PriceCodes.ShugoHubFull, item.HubFullEmps)
                        item.IsHubFullInvoiced = GetIsInvoiced(item.Conum, item.HubFullPrice, PriceCodes.ShugoHubFull, invoiceLogs)

                        item.HubPlusPrice = GetItemPrice(item.Conum, PriceCodes.ShugoHubPlus, item.HubPlusEmps)
                        item.IsHubPlusInvoiced = GetIsInvoiced(item.Conum, item.HubPlusPrice, PriceCodes.ShugoHubPlus, invoiceLogs)

                        item.HubHRPrice = GetItemPrice(item.Conum, PriceCodes.ShugoHubHR, item.HubHREmps)
                        item.IsHubHRInvoiced = GetIsInvoiced(item.Conum, item.HubHRPrice, PriceCodes.ShugoHubHR, invoiceLogs)

                        item.HubFlightFullPrice = GetItemPrice(item.Conum, PriceCodes.ShugoFlightFull, item.HubFlightFull)
                        item.IsHubFlightFullInvoiced = GetIsInvoiced(item.Conum, item.HubFlightFullPrice, PriceCodes.ShugoFlightFull, invoiceLogs)

                        item.HubFlightBasicPrice = GetItemPrice(item.Conum, PriceCodes.ShugoFlightBasic, item.HubFlightBasic)
                        item.IsHubFlightFullInvoiced = GetIsInvoiced(item.Conum, item.HubFlightBasicPrice, PriceCodes.ShugoFlightBasic, invoiceLogs)

                        Dim isAllInvoiced =
                            (item.BaseFee = 0 OrElse item.IsBaseFeeInvoiced) AndAlso
                            (item.HubBasicPrice = 0 OrElse item.IsHubBasicInvoiced) AndAlso
                            (item.HubFullPrice = 0 OrElse item.IsHubFullInvoiced) AndAlso
                            (item.HubPlusPrice = 0 OrElse item.IsHubPlusInvoiced) AndAlso
                            (item.HubHRPrice = 0 OrElse item.IsHubHRInvoiced) AndAlso
                            (item.HubFlightFullPrice = 0 OrElse item.IsHubFlightFullInvoiced) AndAlso
                            (item.HubFlightBasicPrice = 0 OrElse item.IsHubFlightBasicInvoiced)
                        item.IsAllInvoiced = isAllInvoiced.HasValue AndAlso isAllInvoiced.Value = True
                    End If
                Catch ex As Exception
                    Logger.Error(ex, "Error calculating price for {Conum} {@Item}", item?.Conum)
                    Throw
                End Try
            Next
        End Sub

        'Solomon modified on Aug 1, '21.  New invoice system
        'Public Function GetIsInvoiced(conum As Decimal, price As Decimal, itemNum As Decimal, invoiceLogs As List(Of INVOICE_xLOG)) As Boolean?
        Public Function GetIsInvoiced(conum As Decimal, price As Decimal, itemNum As Decimal, invoiceLogs As List(Of invoice_item_detail)) As Boolean?
            If price > 0 Then
                Return (From i In invoiceLogs Where i.conum = conum AndAlso i.item_num = itemNum AndAlso i.item_name = billingUtilities.GetItemName(itemNum)).Any
            Else
                Return Nothing
            End If
        End Function


        Public Sub InvoiceRow(record As IRecord) Implements IBillingImportService.InvoiceRow
            Dim row As ShugoFile = record
            If row.HubFullPrice > 0 AndAlso Not row.IsHubFullInvoiced.GetValueOrDefault() Then
                InvoiceRow(record, PriceCodes.ShugoHubFull, row.HubFullPrice, row.HubFullEmps)
            End If

            If row.HubPlusPrice > 0 AndAlso Not row.IsHubPlusInvoiced.GetValueOrDefault() Then
                InvoiceRow(row, PriceCodes.ShugoHubPlus, row.HubPlusPrice, row.HubPlusEmps)
            End If

            If row.HubBasicPrice > 0 AndAlso Not row.IsHubBasicInvoiced.GetValueOrDefault() Then
                InvoiceRow(row, PriceCodes.ShugoHubBasic, row.HubBasicPrice, row.HubBasicEmps)
            End If

            If row.HubHRPrice > 0 AndAlso Not row.IsHubHRInvoiced.GetValueOrDefault() Then
                InvoiceRow(row, PriceCodes.ShugoHubHR, row.HubHRPrice, row.HubHREmps)
            End If

            If row.BaseFee > 0 AndAlso Not row.IsBaseFeeInvoiced.GetValueOrDefault() Then
                InvoiceRow(row, PriceCodes.ShugoBaseFee, row.BaseFee, 1)
            End If

            If row.HubFlightFullPrice > 0 AndAlso Not row.IsHubFlightFullInvoiced.GetValueOrDefault() Then
                InvoiceRow(row, PriceCodes.ShugoFlightFull, row.HubFlightFullPrice, row.HubFlightFull)
            End If

            If row.HubFlightBasicPrice > 0 AndAlso Not row.IsHubFlightBasicInvoiced.GetValueOrDefault() Then
                InvoiceRow(row, PriceCodes.ShugoFlightFull, row.HubFlightBasicPrice, row.HubFlightBasic)
            End If
        End Sub

        Public Sub InvoiceRow(row As ShugoFile, itemNum As Decimal, price As Decimal, count As Decimal)
            billingUtilities.InsertInvoiceLog(row.Conum, itemNum, price, count)
        End Sub

        Private Sub GridView1_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles GridView1.FocusedRowObjectChanged
            Try
                RaiseEvent SelectedRowChanged(e.Row)
            Catch ex As Exception
                DisplayErrorMessage("Error loading company details", ex)
            End Try
        End Sub

        Public Function GetSelectedRow() As IRecord Implements IBillingImportService.GetSelectedRow
            Return GridView1.GetFocusedRow()
        End Function

        Public Function GetSelectedRows() As List(Of IRecord) Implements IBillingImportService.GetSelectedRows
            Dim selectedlist As List(Of IRecord) = GridView1.GetSelectedRows(Of IRecord)()
            Return selectedlist
        End Function

        Sub Gridview_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles GridView1.SelectionChanged
            RaiseEvent SelectionChanged()
        End Sub

        Sub RefreshData(ShowBilled As Boolean, ShowNonMatched As Boolean) Implements IBillingImportService.RefreshData
            GridControl1.DataSource = (From s In ShugoFileData Where ShowBilled OrElse Not s.IsAllInvoiced).ToList()
        End Sub


        Public Enum PriceCodes
            ShugoHubBasic = 467
            ShugoHubPlus = 468
            ShugoHubFull = 469
            ShugoHubHR = 470
            ShugoFlightFull = 476
            ShugoFlightBasic = 477
            ShugoMinimumFee = 478
            ShugoBaseFee = 479
        End Enum

        Public Class ShugoFile
            Implements IRecord
            Public Property Conum As Decimal Implements IRecord.Conum
            Public Property Coname As String

            Public Property BaseFee As Decimal
            Public Property IsBaseFeeInvoiced As Boolean?

            Property HubFullEmps As Decimal
            Property HubFullPrice As Decimal
            Property IsHubFullInvoiced As Boolean?

            Property HubPlusEmps As Decimal
            Property HubPlusPrice As Decimal
            Property IsHubPlusInvoiced As Boolean?

            Property HubBasicEmps As Decimal
            Public Property HubBasicPrice As Decimal
            Public Property IsHubBasicInvoiced As Boolean?

            Property HubHREmps As Decimal
            Property HubHRPrice As Decimal
            Property IsHubHRInvoiced As Boolean?

            Property HubFlightFull As Decimal
            Property HubFlightFullPrice As Decimal
            Property IsHubFlightFullInvoiced As Boolean?

            Property HubFlightBasic As Decimal
            Property HubFlightBasicPrice As Decimal
            Property IsHubFlightBasicInvoiced As Boolean?

            Public Property IsAllInvoiced As Boolean Implements IRecord.IsAllInvoiced
        End Class

    End Class
End Namespace