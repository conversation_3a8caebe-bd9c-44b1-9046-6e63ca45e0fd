﻿Imports System.ComponentModel

Public Class frmSelectCompany
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property SelectedCompany As view_CompanySumarry

    Sub New()
        InitializeComponent()
    End Sub


    Private Sub frmChooseCompany_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'Try
        '    Using db As New dbEPDataDataContext(GetConnectionString)
        '        SearchLookUpEdit1.Properties.DataSource = db.view_CompanySumarries.ToList()
        '    End Using
        'Catch ex As Exception
        '    DisplayErrorMessage("Error loading data", ex)
        'End Try
    End Sub

    Friend Function GetSelectedConum() As Decimal
        Return SelectedCompany.CONUM
    End Function

    Private Sub UcSearchCompany1_SelectedCompanyChangedEvent(sender As Object, e As ucSearchCompany.SelectedCompanyChangedEventArgs) Handles UcSearchCompany1.SelectedCompanyChangedEvent
        If e.Company Is Nothing Then
            DialogResult = DialogResult.Cancel
        Else
            SelectedCompany = e.Company
            DialogResult = DialogResult.OK
        End If
    End Sub
End Class