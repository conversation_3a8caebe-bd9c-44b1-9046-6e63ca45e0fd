﻿Imports System.Data
Imports System.Data.SqlClient

Public Class frmCompanyUnionUpdateWageRate

    Private db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
    End Sub

    Private Sub frmCompanyUnionUpdateWageRate_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        Dim coNumList = db.UnionDepartments.Select(Function(c) c.Conum).Distinct
        slueCoNum.Properties.DataSource = db.view_CompanySumarries.Where(Function(c) coNumList.Contains(c.CONUM)).ToList()
    End Sub

    Private Sub slueCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles slueCoNum.EditValueChanged
        Dim coNum As Decimal
        If Decimal.TryParse(nz(slueCoNum.EditValue, ""), coNum) Then
            GridControl1.DataSource = Query("  SELECT LEFT(ur.UnionNamePositionFund, CHARINDEX('_', ur.UnionNamePositionFund)) UnionName, ur.EffectiveDate, ud.Conum
  FROM custom.UnionDepartments ud
  INNER JOIN 
  (
  SELECT UnionNamePositionFund, LEFT(UnionNamePositionFund, CHARINDEX('_', UnionNamePositionFund)) UnionName, 
  MAX(EffectiveDate) EffectiveDate
  FROM  [EPDATA].[custom].[UnionRates]
  WHERE IsActive = 1
  GROUP BY UnionNamePositionFund, LEFT(UnionNamePositionFund, CHARINDEX('_', UnionNamePositionFund))
  ) ur ON ur.UnionNamePositionFund = ud.UnionNamePositionFund
  WHERE ur.UnionNamePositionFund LIKE '%Wage%' AND Conum = @Conum
  GROUP BY LEFT(ur.UnionNamePositionFund, CHARINDEX('_', ur.UnionNamePositionFund)), ur.EffectiveDate, ud.Conum
  ORDER BY ud.Conum", New SqlParameter("CoNum", coNum))
            GridView1.BestFitColumns()
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnUpdateSelected_Click(sender As Object, e As EventArgs) Handles btnUpdateSelected.Click
        Dim list = GridView1.GetSelectedRows(Of DataRowView)
        For Each item In list
            Dim row = item.Row
            updateRow(row)
        Next
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As System.Data.DataRowView = GridView1.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Update", Sub() updateRow(row.Row)))
        End If
    End Sub

    Private Async Sub updateRow(row As DataRow)
        Try
            lcRoot.ShowProgessPanel
            Dim coNum = row.GetValue("CoNum")
            Dim effDate = row.GetValue("EffectiveDate")
            Dim name = row.GetValue("UnionName")
            Dim sql = $"[custom].[prc_PrUtilWageUpdate] @Conum = {coNum} , @EffectiveDate = '{effDate}' , @UnionNames = '{name}'"
            Await UpdateSqlAsync(sql, Nothing)
        Catch ex As Exception
            DisplayErrorMessage("Error update Wage", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub
End Class