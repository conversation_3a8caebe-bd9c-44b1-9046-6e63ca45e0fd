﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmAccountLeads
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAccountLeads))
        Dim GridFormatRule1 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleValue1 As DevExpress.XtraEditors.FormatConditionRuleValue = New DevExpress.XtraEditors.FormatConditionRuleValue()
        Dim GridFormatRule2 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleExpression1 As DevExpress.XtraEditors.FormatConditionRuleExpression = New DevExpress.XtraEditors.FormatConditionRuleExpression()
        Me.colNextFollowUpDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
        Me.UcAccountLeadDetails1 = New Brands_FrontDesk.ucAccountLeadDetails()
        Me.cbeCategory = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.bbiRefresh = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiAddLead = New DevExpress.XtraBars.BarButtonItem()
        Me.PopupControlContainer1 = New DevExpress.XtraBars.PopupControlContainer(Me.components)
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.slueAddFromExistingCo = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.bsCompanies = New System.Windows.Forms.BindingSource(Me.components)
        Me.SearchLookUpEdit3View = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colCoNumAndName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPR_CONTACT = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_PHONE = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_FAX = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_MODEM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFED_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_EMAIL = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.btnAddFromExistingCoOk = New DevExpress.XtraEditors.SimpleButton()
        Me.bbiEmail = New DevExpress.XtraBars.BarButtonItem()
        Me.pmEmails = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.bbiMergeWithRecord = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiMoveToNextStatus = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiFieldsMapping = New DevExpress.XtraBars.BarButtonItem()
        Me.bbiDownloadNewSubmittions = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup2 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.gcAccountLeads = New DevExpress.XtraGrid.GridControl()
        Me.bsAccountLead = New System.Windows.Forms.BindingSource(Me.components)
        Me.gvAccountLeads = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colStatus = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riCbeStatus = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.colCategory = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAssignedTo = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colContactName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoPhoneNumber = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoPhoneNubmerExt = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoCellNumber = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoFax = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoEmail = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNumberOfEmployees = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riSeNumOfEmps = New DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit()
        Me.colIsNewAccount = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNotes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riNotes = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.colStartingDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReferedBy = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReferedByCoNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colLastFollowedUpDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsPayroll = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsHr = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsEndOfPeriod = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsCpa = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsPrincipal = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNum = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAddDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colAddUser = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChgDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colChgUser = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFedId = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPayFrequency = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEnrollment_Workflow = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.SplitterItem2 = New DevExpress.XtraLayout.SplitterItem()
        Me.bsRecentFiles = New System.Windows.Forms.BindingSource(Me.components)
        Me.AccordionControl1 = New DevExpress.XtraBars.Navigation.AccordionControl()
        Me.aceEnrollmentWeb = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceAnonymousSignup = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.acePendingMatchVerification = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceSignupInProcess = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceSignupCompleted = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceAccountLeads = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceAll = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceFollowUp = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceQualifiedLead = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceProspect = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceReadyToProcess = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceSetupInProcess = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.aceSetupDone = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.AceLeadsByStatus = New DevExpress.XtraBars.Navigation.AccordionControlElement()
        Me.colJotFormsSubmissionId = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.lcRoot.SuspendLayout()
        CType(Me.cbeCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupControlContainer1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PopupControlContainer1.SuspendLayout()
        CType(Me.slueAddFromExistingCo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit3View, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pmEmails, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gcAccountLeads, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsAccountLead, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvAccountLeads, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riCbeStatus, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riSeNumOfEmps, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsRecentFiles, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AccordionControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'colNextFollowUpDate
        '
        Me.colNextFollowUpDate.Caption = "Follow Up"
        Me.colNextFollowUpDate.FieldName = "NextFollowUpDate"
        Me.colNextFollowUpDate.Name = "colNextFollowUpDate"
        Me.colNextFollowUpDate.Visible = True
        Me.colNextFollowUpDate.VisibleIndex = 1
        '
        'lcRoot
        '
        Me.lcRoot.Controls.Add(Me.UcAccountLeadDetails1)
        Me.lcRoot.Controls.Add(Me.cbeCategory)
        Me.lcRoot.Controls.Add(Me.PopupControlContainer1)
        Me.lcRoot.Controls.Add(Me.gcAccountLeads)
        Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lcRoot.Location = New System.Drawing.Point(211, 162)
        Me.lcRoot.Margin = New System.Windows.Forms.Padding(0)
        Me.lcRoot.Name = "lcRoot"
        Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(1560, 198, 1015, 681)
        Me.lcRoot.Root = Me.LayoutControlGroup1
        Me.lcRoot.Size = New System.Drawing.Size(1041, 578)
        Me.lcRoot.TabIndex = 0
        Me.lcRoot.Text = "LayoutControl1"
        '
        'UcAccountLeadDetails1
        '
        Me.UcAccountLeadDetails1.Location = New System.Drawing.Point(652, 24)
        Me.UcAccountLeadDetails1.Margin = New System.Windows.Forms.Padding(0)
        Me.UcAccountLeadDetails1.Name = "UcAccountLeadDetails1"
        Me.UcAccountLeadDetails1.Size = New System.Drawing.Size(389, 554)
        Me.UcAccountLeadDetails1.TabIndex = 17
        '
        'cbeCategory
        '
        Me.cbeCategory.Location = New System.Drawing.Point(59, 2)
        Me.cbeCategory.MenuManager = Me.RibbonControl1
        Me.cbeCategory.Name = "cbeCategory"
        Me.cbeCategory.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeCategory.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeCategory.Size = New System.Drawing.Size(159, 20)
        Me.cbeCategory.StyleController = Me.lcRoot
        Me.cbeCategory.TabIndex = 16
        '
        'RibbonControl1
        '
        Me.RibbonControl1.ExpandCollapseItem.Id = 0
        Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.RibbonControl1.SearchEditItem, Me.bbiRefresh, Me.bbiAddLead, Me.bbiEmail, Me.bbiMergeWithRecord, Me.bbiMoveToNextStatus, Me.bbiFieldsMapping, Me.bbiDownloadNewSubmittions})
        Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl1.MaxItemId = 9
        Me.RibbonControl1.Name = "RibbonControl1"
        Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
        Me.RibbonControl1.Size = New System.Drawing.Size(1252, 162)
        '
        'bbiRefresh
        '
        Me.bbiRefresh.Caption = "Refresh"
        Me.bbiRefresh.Id = 1
        Me.bbiRefresh.ImageOptions.Image = CType(resources.GetObject("bbiRefresh.ImageOptions.Image"), System.Drawing.Image)
        Me.bbiRefresh.ImageOptions.LargeImage = CType(resources.GetObject("bbiRefresh.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.bbiRefresh.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.refresh
        Me.bbiRefresh.Name = "bbiRefresh"
        '
        'bbiAddLead
        '
        Me.bbiAddLead.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown
        Me.bbiAddLead.Caption = "Add Lead"
        Me.bbiAddLead.DropDownControl = Me.PopupControlContainer1
        Me.bbiAddLead.Id = 2
        Me.bbiAddLead.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.add_16x16
        Me.bbiAddLead.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.add_32x32
        Me.bbiAddLead.Name = "bbiAddLead"
        '
        'PopupControlContainer1
        '
        Me.PopupControlContainer1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PopupControlContainer1.Controls.Add(Me.LabelControl2)
        Me.PopupControlContainer1.Controls.Add(Me.slueAddFromExistingCo)
        Me.PopupControlContainer1.Controls.Add(Me.LabelControl1)
        Me.PopupControlContainer1.Controls.Add(Me.btnAddFromExistingCoOk)
        Me.PopupControlContainer1.Location = New System.Drawing.Point(239, 223)
        Me.PopupControlContainer1.Name = "PopupControlContainer1"
        Me.PopupControlContainer1.Ribbon = Me.RibbonControl1
        Me.PopupControlContainer1.Size = New System.Drawing.Size(418, 77)
        Me.PopupControlContainer1.TabIndex = 2
        Me.PopupControlContainer1.Visible = False
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(15, 44)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(28, 13)
        Me.LabelControl2.TabIndex = 3
        Me.LabelControl2.Text = "Co#: "
        '
        'slueAddFromExistingCo
        '
        Me.slueAddFromExistingCo.Location = New System.Drawing.Point(46, 41)
        Me.slueAddFromExistingCo.MenuManager = Me.RibbonControl1
        Me.slueAddFromExistingCo.Name = "slueAddFromExistingCo"
        Me.slueAddFromExistingCo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.slueAddFromExistingCo.Properties.DataSource = Me.bsCompanies
        Me.slueAddFromExistingCo.Properties.DisplayMember = "CoNumAndName"
        Me.slueAddFromExistingCo.Properties.NullText = ""
        Me.slueAddFromExistingCo.Properties.PopupFormSize = New System.Drawing.Size(1000, 0)
        Me.slueAddFromExistingCo.Properties.PopupView = Me.SearchLookUpEdit3View
        Me.slueAddFromExistingCo.Properties.ValueMember = "CONUM"
        Me.slueAddFromExistingCo.Size = New System.Drawing.Size(264, 20)
        Me.slueAddFromExistingCo.TabIndex = 2
        '
        'bsCompanies
        '
        Me.bsCompanies.DataSource = GetType(Brands_FrontDesk.view_CompanySumarry)
        '
        'SearchLookUpEdit3View
        '
        Me.SearchLookUpEdit3View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNumAndName, Me.colPR_CONTACT, Me.colCO_PHONE, Me.colCO_FAX, Me.colCO_MODEM, Me.colFED_ID, Me.colCO_EMAIL, Me.colCO_STATUS})
        Me.SearchLookUpEdit3View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.SearchLookUpEdit3View.Name = "SearchLookUpEdit3View"
        Me.SearchLookUpEdit3View.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.SearchLookUpEdit3View.OptionsView.ShowGroupPanel = False
        '
        'colCoNumAndName
        '
        Me.colCoNumAndName.FieldName = "CoNumAndName"
        Me.colCoNumAndName.Name = "colCoNumAndName"
        Me.colCoNumAndName.Visible = True
        Me.colCoNumAndName.VisibleIndex = 0
        Me.colCoNumAndName.Width = 150
        '
        'colPR_CONTACT
        '
        Me.colPR_CONTACT.FieldName = "PR_CONTACT"
        Me.colPR_CONTACT.Name = "colPR_CONTACT"
        Me.colPR_CONTACT.Visible = True
        Me.colPR_CONTACT.VisibleIndex = 1
        Me.colPR_CONTACT.Width = 33
        '
        'colCO_PHONE
        '
        Me.colCO_PHONE.FieldName = "CO_PHONE"
        Me.colCO_PHONE.Name = "colCO_PHONE"
        Me.colCO_PHONE.Visible = True
        Me.colCO_PHONE.VisibleIndex = 2
        Me.colCO_PHONE.Width = 33
        '
        'colCO_FAX
        '
        Me.colCO_FAX.FieldName = "CO_FAX"
        Me.colCO_FAX.Name = "colCO_FAX"
        Me.colCO_FAX.Visible = True
        Me.colCO_FAX.VisibleIndex = 3
        Me.colCO_FAX.Width = 33
        '
        'colCO_MODEM
        '
        Me.colCO_MODEM.FieldName = "CO_MODEM"
        Me.colCO_MODEM.Name = "colCO_MODEM"
        Me.colCO_MODEM.Visible = True
        Me.colCO_MODEM.VisibleIndex = 4
        Me.colCO_MODEM.Width = 33
        '
        'colFED_ID
        '
        Me.colFED_ID.FieldName = "FED_ID"
        Me.colFED_ID.Name = "colFED_ID"
        Me.colFED_ID.Visible = True
        Me.colFED_ID.VisibleIndex = 5
        Me.colFED_ID.Width = 33
        '
        'colCO_EMAIL
        '
        Me.colCO_EMAIL.FieldName = "CO_EMAIL"
        Me.colCO_EMAIL.Name = "colCO_EMAIL"
        Me.colCO_EMAIL.Visible = True
        Me.colCO_EMAIL.VisibleIndex = 6
        Me.colCO_EMAIL.Width = 33
        '
        'colCO_STATUS
        '
        Me.colCO_STATUS.FieldName = "CO_STATUS"
        Me.colCO_STATUS.Name = "colCO_STATUS"
        Me.colCO_STATUS.Visible = True
        Me.colCO_STATUS.VisibleIndex = 7
        Me.colCO_STATUS.Width = 36
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Tahoma", 9.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.Blue
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseForeColor = True
        Me.LabelControl1.Location = New System.Drawing.Point(16, 13)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(205, 14)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "Add Lead From Existing Company"
        '
        'btnAddFromExistingCoOk
        '
        Me.btnAddFromExistingCoOk.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.add_16x16
        Me.btnAddFromExistingCoOk.Location = New System.Drawing.Point(329, 41)
        Me.btnAddFromExistingCoOk.Name = "btnAddFromExistingCoOk"
        Me.btnAddFromExistingCoOk.Size = New System.Drawing.Size(75, 23)
        Me.btnAddFromExistingCoOk.TabIndex = 0
        Me.btnAddFromExistingCoOk.Text = "Ok"
        '
        'bbiEmail
        '
        Me.bbiEmail.ActAsDropDown = True
        Me.bbiEmail.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown
        Me.bbiEmail.Caption = "Email "
        Me.bbiEmail.DropDownControl = Me.pmEmails
        Me.bbiEmail.Id = 3
        Me.bbiEmail.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.mail_16x16
        Me.bbiEmail.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.mail_32x32
        Me.bbiEmail.Name = "bbiEmail"
        '
        'pmEmails
        '
        Me.pmEmails.Name = "pmEmails"
        Me.pmEmails.Ribbon = Me.RibbonControl1
        '
        'bbiMergeWithRecord
        '
        Me.bbiMergeWithRecord.Caption = "Merge With Record"
        Me.bbiMergeWithRecord.Id = 5
        Me.bbiMergeWithRecord.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.Merge
        Me.bbiMergeWithRecord.Name = "bbiMergeWithRecord"
        '
        'bbiMoveToNextStatus
        '
        Me.bbiMoveToNextStatus.Caption = "Move To Next Status"
        Me.bbiMoveToNextStatus.Id = 6
        Me.bbiMoveToNextStatus.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.right2
        Me.bbiMoveToNextStatus.Name = "bbiMoveToNextStatus"
        '
        'bbiFieldsMapping
        '
        Me.bbiFieldsMapping.Caption = "Fields Mapping"
        Me.bbiFieldsMapping.Id = 7
        Me.bbiFieldsMapping.ImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.addcalculatedfield
        Me.bbiFieldsMapping.Name = "bbiFieldsMapping"
        '
        'bbiDownloadNewSubmittions
        '
        Me.bbiDownloadNewSubmittions.Caption = "Download New Submittions"
        Me.bbiDownloadNewSubmittions.Id = 8
        Me.bbiDownloadNewSubmittions.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.download_16x16
        Me.bbiDownloadNewSubmittions.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.download_32x32
        Me.bbiDownloadNewSubmittions.Name = "bbiDownloadNewSubmittions"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1, Me.RibbonPageGroup2})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRefresh)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiAddLead)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiEmail)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiMergeWithRecord)
        Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiMoveToNextStatus)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.Text = "Leads"
        '
        'RibbonPageGroup2
        '
        Me.RibbonPageGroup2.ItemLinks.Add(Me.bbiFieldsMapping)
        Me.RibbonPageGroup2.ItemLinks.Add(Me.bbiDownloadNewSubmittions)
        Me.RibbonPageGroup2.Name = "RibbonPageGroup2"
        Me.RibbonPageGroup2.Text = "Jot Forms API"
        '
        'gcAccountLeads
        '
        Me.gcAccountLeads.DataSource = Me.bsAccountLead
        Me.gcAccountLeads.Location = New System.Drawing.Point(2, 26)
        Me.gcAccountLeads.MainView = Me.gvAccountLeads
        Me.gcAccountLeads.Name = "gcAccountLeads"
        Me.gcAccountLeads.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riSeNumOfEmps, Me.riCbeStatus, Me.riNotes})
        Me.gcAccountLeads.Size = New System.Drawing.Size(638, 550)
        Me.gcAccountLeads.TabIndex = 4
        Me.gcAccountLeads.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvAccountLeads})
        '
        'bsAccountLead
        '
        Me.bsAccountLead.DataSource = GetType(Brands_FrontDesk.view_AccountLead)
        '
        'gvAccountLeads
        '
        Me.gvAccountLeads.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colID, Me.colStatus, Me.colCategory, Me.colNextFollowUpDate, Me.colAssignedTo, Me.colCoName, Me.colContactName, Me.colCoPhoneNumber, Me.colCoPhoneNubmerExt, Me.colCoCellNumber, Me.colCoFax, Me.colCoEmail, Me.colNumberOfEmployees, Me.colIsNewAccount, Me.colNotes, Me.colStartingDate, Me.colReferedBy, Me.colReferedByCoNum, Me.colLastFollowedUpDate, Me.colIsPayroll, Me.colIsHr, Me.colIsEndOfPeriod, Me.colIsCpa, Me.colIsPrincipal, Me.colCoNum, Me.colAddDate, Me.colAddUser, Me.colChgDate, Me.colChgUser, Me.colFedId, Me.colPayFrequency, Me.colEnrollment_Workflow, Me.colJotFormsSubmissionId})
        GridFormatRule1.Column = Me.colNextFollowUpDate
        GridFormatRule1.ColumnApplyTo = Me.colNextFollowUpDate
        GridFormatRule1.Name = "Format0"
        FormatConditionRuleValue1.Appearance.BackColor = System.Drawing.Color.HotPink
        FormatConditionRuleValue1.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        FormatConditionRuleValue1.Appearance.Options.UseBackColor = True
        FormatConditionRuleValue1.Appearance.Options.UseFont = True
        FormatConditionRuleValue1.Condition = DevExpress.XtraEditors.FormatCondition.Expression
        FormatConditionRuleValue1.Expression = "[NextFollowUpDate] = Today()"
        GridFormatRule1.Rule = FormatConditionRuleValue1
        GridFormatRule2.Column = Me.colNextFollowUpDate
        GridFormatRule2.ColumnApplyTo = Me.colNextFollowUpDate
        GridFormatRule2.Name = "Format1"
        FormatConditionRuleExpression1.Appearance.BackColor = System.Drawing.Color.Red
        FormatConditionRuleExpression1.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        FormatConditionRuleExpression1.Appearance.Options.UseBackColor = True
        FormatConditionRuleExpression1.Appearance.Options.UseFont = True
        FormatConditionRuleExpression1.Expression = "[NextFollowUpDate] > '1/1/2010' And [NextFollowUpDate] < Today()"
        GridFormatRule2.Rule = FormatConditionRuleExpression1
        Me.gvAccountLeads.FormatRules.Add(GridFormatRule1)
        Me.gvAccountLeads.FormatRules.Add(GridFormatRule2)
        Me.gvAccountLeads.GridControl = Me.gcAccountLeads
        Me.gvAccountLeads.Name = "gvAccountLeads"
        Me.gvAccountLeads.OptionsBehavior.Editable = False
        Me.gvAccountLeads.OptionsEditForm.ShowOnDoubleClick = DevExpress.Utils.DefaultBoolean.[True]
        Me.gvAccountLeads.OptionsEditForm.ShowUpdateCancelPanel = DevExpress.Utils.DefaultBoolean.[True]
        Me.gvAccountLeads.OptionsView.ColumnAutoWidth = False
        Me.gvAccountLeads.OptionsView.ShowGroupPanel = False
        '
        'colID
        '
        Me.colID.FieldName = "ID"
        Me.colID.Name = "colID"
        '
        'colStatus
        '
        Me.colStatus.ColumnEdit = Me.riCbeStatus
        Me.colStatus.FieldName = "Status"
        Me.colStatus.Name = "colStatus"
        '
        'riCbeStatus
        '
        Me.riCbeStatus.AutoHeight = False
        Me.riCbeStatus.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riCbeStatus.Name = "riCbeStatus"
        Me.riCbeStatus.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        '
        'colCategory
        '
        Me.colCategory.FieldName = "Category"
        Me.colCategory.Name = "colCategory"
        Me.colCategory.Visible = True
        Me.colCategory.VisibleIndex = 0
        '
        'colAssignedTo
        '
        Me.colAssignedTo.FieldName = "AssignedTo"
        Me.colAssignedTo.Name = "colAssignedTo"
        Me.colAssignedTo.Visible = True
        Me.colAssignedTo.VisibleIndex = 2
        '
        'colCoName
        '
        Me.colCoName.FieldName = "CoName"
        Me.colCoName.Name = "colCoName"
        Me.colCoName.Visible = True
        Me.colCoName.VisibleIndex = 3
        '
        'colContactName
        '
        Me.colContactName.FieldName = "ContactName"
        Me.colContactName.Name = "colContactName"
        Me.colContactName.Visible = True
        Me.colContactName.VisibleIndex = 4
        '
        'colCoPhoneNumber
        '
        Me.colCoPhoneNumber.Caption = "Phone #"
        Me.colCoPhoneNumber.FieldName = "CoPhoneNumber"
        Me.colCoPhoneNumber.Name = "colCoPhoneNumber"
        Me.colCoPhoneNumber.Visible = True
        Me.colCoPhoneNumber.VisibleIndex = 5
        '
        'colCoPhoneNubmerExt
        '
        Me.colCoPhoneNubmerExt.Caption = "Ext"
        Me.colCoPhoneNubmerExt.FieldName = "CoPhoneNubmerExt"
        Me.colCoPhoneNubmerExt.Name = "colCoPhoneNubmerExt"
        Me.colCoPhoneNubmerExt.Visible = True
        Me.colCoPhoneNubmerExt.VisibleIndex = 6
        '
        'colCoCellNumber
        '
        Me.colCoCellNumber.FieldName = "CoCellNumber"
        Me.colCoCellNumber.Name = "colCoCellNumber"
        Me.colCoCellNumber.Visible = True
        Me.colCoCellNumber.VisibleIndex = 7
        '
        'colCoFax
        '
        Me.colCoFax.FieldName = "CoFax"
        Me.colCoFax.Name = "colCoFax"
        Me.colCoFax.Visible = True
        Me.colCoFax.VisibleIndex = 8
        '
        'colCoEmail
        '
        Me.colCoEmail.FieldName = "CoEmail"
        Me.colCoEmail.Name = "colCoEmail"
        Me.colCoEmail.Visible = True
        Me.colCoEmail.VisibleIndex = 9
        '
        'colNumberOfEmployees
        '
        Me.colNumberOfEmployees.Caption = "Number EEs"
        Me.colNumberOfEmployees.ColumnEdit = Me.riSeNumOfEmps
        Me.colNumberOfEmployees.FieldName = "NumberOfEmployees"
        Me.colNumberOfEmployees.Name = "colNumberOfEmployees"
        Me.colNumberOfEmployees.Visible = True
        Me.colNumberOfEmployees.VisibleIndex = 10
        '
        'riSeNumOfEmps
        '
        Me.riSeNumOfEmps.AutoHeight = False
        Me.riSeNumOfEmps.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riSeNumOfEmps.Name = "riSeNumOfEmps"
        '
        'colIsNewAccount
        '
        Me.colIsNewAccount.FieldName = "IsNewAccount"
        Me.colIsNewAccount.Name = "colIsNewAccount"
        Me.colIsNewAccount.Visible = True
        Me.colIsNewAccount.VisibleIndex = 11
        '
        'colNotes
        '
        Me.colNotes.ColumnEdit = Me.riNotes
        Me.colNotes.FieldName = "Notes"
        Me.colNotes.Name = "colNotes"
        '
        'riNotes
        '
        Me.riNotes.AutoHeight = False
        Me.riNotes.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.riNotes.Name = "riNotes"
        Me.riNotes.ReadOnly = True
        '
        'colStartingDate
        '
        Me.colStartingDate.FieldName = "StartingDate"
        Me.colStartingDate.Name = "colStartingDate"
        Me.colStartingDate.Visible = True
        Me.colStartingDate.VisibleIndex = 12
        '
        'colReferedBy
        '
        Me.colReferedBy.FieldName = "ReferedBy"
        Me.colReferedBy.Name = "colReferedBy"
        Me.colReferedBy.Visible = True
        Me.colReferedBy.VisibleIndex = 14
        '
        'colReferedByCoNum
        '
        Me.colReferedByCoNum.Caption = "Refered By Co#"
        Me.colReferedByCoNum.FieldName = "ReferedByCoNum"
        Me.colReferedByCoNum.Name = "colReferedByCoNum"
        Me.colReferedByCoNum.Visible = True
        Me.colReferedByCoNum.VisibleIndex = 15
        '
        'colLastFollowedUpDate
        '
        Me.colLastFollowedUpDate.Caption = "Last Followed"
        Me.colLastFollowedUpDate.FieldName = "LastFollowedUpDate"
        Me.colLastFollowedUpDate.Name = "colLastFollowedUpDate"
        Me.colLastFollowedUpDate.Visible = True
        Me.colLastFollowedUpDate.VisibleIndex = 13
        '
        'colIsPayroll
        '
        Me.colIsPayroll.FieldName = "IsPayroll"
        Me.colIsPayroll.Name = "colIsPayroll"
        Me.colIsPayroll.Visible = True
        Me.colIsPayroll.VisibleIndex = 16
        '
        'colIsHr
        '
        Me.colIsHr.FieldName = "IsHr"
        Me.colIsHr.Name = "colIsHr"
        Me.colIsHr.Visible = True
        Me.colIsHr.VisibleIndex = 17
        '
        'colIsEndOfPeriod
        '
        Me.colIsEndOfPeriod.FieldName = "IsEndOfPeriod"
        Me.colIsEndOfPeriod.Name = "colIsEndOfPeriod"
        Me.colIsEndOfPeriod.Visible = True
        Me.colIsEndOfPeriod.VisibleIndex = 18
        '
        'colIsCpa
        '
        Me.colIsCpa.FieldName = "IsCpa"
        Me.colIsCpa.Name = "colIsCpa"
        Me.colIsCpa.Visible = True
        Me.colIsCpa.VisibleIndex = 19
        '
        'colIsPrincipal
        '
        Me.colIsPrincipal.FieldName = "IsPrincipal"
        Me.colIsPrincipal.Name = "colIsPrincipal"
        Me.colIsPrincipal.Visible = True
        Me.colIsPrincipal.VisibleIndex = 20
        '
        'colCoNum
        '
        Me.colCoNum.FieldName = "CoNum"
        Me.colCoNum.Name = "colCoNum"
        Me.colCoNum.Visible = True
        Me.colCoNum.VisibleIndex = 21
        '
        'colAddDate
        '
        Me.colAddDate.FieldName = "AddDate"
        Me.colAddDate.Name = "colAddDate"
        Me.colAddDate.Visible = True
        Me.colAddDate.VisibleIndex = 22
        '
        'colAddUser
        '
        Me.colAddUser.FieldName = "AddUser"
        Me.colAddUser.Name = "colAddUser"
        Me.colAddUser.Visible = True
        Me.colAddUser.VisibleIndex = 23
        '
        'colChgDate
        '
        Me.colChgDate.FieldName = "ChgDate"
        Me.colChgDate.Name = "colChgDate"
        Me.colChgDate.Visible = True
        Me.colChgDate.VisibleIndex = 24
        '
        'colChgUser
        '
        Me.colChgUser.FieldName = "ChgUser"
        Me.colChgUser.Name = "colChgUser"
        Me.colChgUser.Visible = True
        Me.colChgUser.VisibleIndex = 25
        '
        'colFedId
        '
        Me.colFedId.FieldName = "FedId"
        Me.colFedId.Name = "colFedId"
        Me.colFedId.Visible = True
        Me.colFedId.VisibleIndex = 26
        '
        'colPayFrequency
        '
        Me.colPayFrequency.FieldName = "PayFrequency"
        Me.colPayFrequency.Name = "colPayFrequency"
        Me.colPayFrequency.Visible = True
        Me.colPayFrequency.VisibleIndex = 27
        '
        'colEnrollment_Workflow
        '
        Me.colEnrollment_Workflow.FieldName = "Enrollment_Workflow"
        Me.colEnrollment_Workflow.Name = "colEnrollment_Workflow"
        Me.colEnrollment_Workflow.Visible = True
        Me.colEnrollment_Workflow.VisibleIndex = 28
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.EmptySpaceItem1, Me.LayoutControlItem13, Me.LayoutControlItem3, Me.SplitterItem2})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1041, 578)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.gcAccountLeads
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(642, 554)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.AllowHotTrack = False
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(220, 0)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(821, 24)
        Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.cbeCategory
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(220, 24)
        Me.LayoutControlItem13.Text = "Category: "
        Me.LayoutControlItem13.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem13.TextSize = New System.Drawing.Size(52, 13)
        Me.LayoutControlItem13.TextToControlDistance = 5
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.UcAccountLeadDetails1
        Me.LayoutControlItem3.Location = New System.Drawing.Point(652, 24)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlItem3.Size = New System.Drawing.Size(389, 554)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'SplitterItem2
        '
        Me.SplitterItem2.AllowHotTrack = True
        Me.SplitterItem2.Location = New System.Drawing.Point(642, 24)
        Me.SplitterItem2.Name = "SplitterItem2"
        Me.SplitterItem2.Size = New System.Drawing.Size(10, 554)
        '
        'bsRecentFiles
        '
        Me.bsRecentFiles.DataSource = GetType(Brands_FrontDesk.ucRecentReports.RecentFiles)
        '
        'AccordionControl1
        '
        Me.AccordionControl1.AllowItemSelection = True
        Me.AccordionControl1.Dock = System.Windows.Forms.DockStyle.Left
        Me.AccordionControl1.Elements.AddRange(New DevExpress.XtraBars.Navigation.AccordionControlElement() {Me.aceEnrollmentWeb, Me.aceAccountLeads, Me.AceLeadsByStatus})
        Me.AccordionControl1.Location = New System.Drawing.Point(0, 162)
        Me.AccordionControl1.Margin = New System.Windows.Forms.Padding(0)
        Me.AccordionControl1.Name = "AccordionControl1"
        Me.AccordionControl1.OptionsMinimizing.NormalWidth = 211
        Me.AccordionControl1.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.[Auto]
        Me.AccordionControl1.ShowFilterControl = DevExpress.XtraBars.Navigation.ShowFilterControl.[Auto]
        Me.AccordionControl1.Size = New System.Drawing.Size(211, 578)
        Me.AccordionControl1.TabIndex = 5
        Me.AccordionControl1.Text = "AccordionControl1"
        Me.AccordionControl1.ViewType = DevExpress.XtraBars.Navigation.AccordionControlViewType.HamburgerMenu
        '
        'aceEnrollmentWeb
        '
        Me.aceEnrollmentWeb.Elements.AddRange(New DevExpress.XtraBars.Navigation.AccordionControlElement() {Me.aceAnonymousSignup, Me.acePendingMatchVerification, Me.aceSignupInProcess, Me.aceSignupCompleted})
        Me.aceEnrollmentWeb.Expanded = True
        Me.aceEnrollmentWeb.Name = "aceEnrollmentWeb"
        Me.aceEnrollmentWeb.Text = "Enrollment - Web"
        '
        'aceAnonymousSignup
        '
        Me.aceAnonymousSignup.Name = "aceAnonymousSignup"
        Me.aceAnonymousSignup.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceAnonymousSignup.Text = "Anonymous Signup"
        '
        'acePendingMatchVerification
        '
        Me.acePendingMatchVerification.Name = "acePendingMatchVerification"
        Me.acePendingMatchVerification.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.acePendingMatchVerification.Text = "Pending Match Verification"
        '
        'aceSignupInProcess
        '
        Me.aceSignupInProcess.Name = "aceSignupInProcess"
        Me.aceSignupInProcess.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceSignupInProcess.Text = "Signup in Process"
        '
        'aceSignupCompleted
        '
        Me.aceSignupCompleted.Name = "aceSignupCompleted"
        Me.aceSignupCompleted.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceSignupCompleted.Text = "Signup Completed"
        '
        'aceAccountLeads
        '
        Me.aceAccountLeads.Elements.AddRange(New DevExpress.XtraBars.Navigation.AccordionControlElement() {Me.aceAll, Me.aceFollowUp, Me.aceQualifiedLead, Me.aceProspect, Me.aceReadyToProcess, Me.aceSetupInProcess, Me.aceSetupDone})
        Me.aceAccountLeads.Expanded = True
        Me.aceAccountLeads.Name = "aceAccountLeads"
        Me.aceAccountLeads.Text = "Account Leads"
        '
        'aceAll
        '
        Me.aceAll.Name = "aceAll"
        Me.aceAll.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceAll.Text = "All"
        '
        'aceFollowUp
        '
        Me.aceFollowUp.Name = "aceFollowUp"
        Me.aceFollowUp.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceFollowUp.Text = "Follow Up Due"
        '
        'aceQualifiedLead
        '
        Me.aceQualifiedLead.Name = "aceQualifiedLead"
        Me.aceQualifiedLead.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceQualifiedLead.Text = "Qualified Lead"
        '
        'aceProspect
        '
        Me.aceProspect.Name = "aceProspect"
        Me.aceProspect.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceProspect.Text = "Prospect"
        '
        'aceReadyToProcess
        '
        Me.aceReadyToProcess.Name = "aceReadyToProcess"
        Me.aceReadyToProcess.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceReadyToProcess.Text = "Ready to Process"
        '
        'aceSetupInProcess
        '
        Me.aceSetupInProcess.Name = "aceSetupInProcess"
        Me.aceSetupInProcess.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceSetupInProcess.Text = "Setup in Process"
        '
        'aceSetupDone
        '
        Me.aceSetupDone.Name = "aceSetupDone"
        Me.aceSetupDone.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
        Me.aceSetupDone.Text = "Setup Done"
        '
        'AceLeadsByStatus
        '
        Me.AceLeadsByStatus.Name = "AceLeadsByStatus"
        Me.AceLeadsByStatus.Text = "Leads (By Status)"
        '
        'colJotFormsSubmissionId
        '
        Me.colJotFormsSubmissionId.FieldName = "JotFormsSubmissionId"
        Me.colJotFormsSubmissionId.Name = "colJotFormsSubmissionId"
        Me.colJotFormsSubmissionId.Visible = True
        Me.colJotFormsSubmissionId.VisibleIndex = 29
        '
        'frmAccountLeads
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1252, 740)
        Me.Controls.Add(Me.lcRoot)
        Me.Controls.Add(Me.AccordionControl1)
        Me.Controls.Add(Me.RibbonControl1)
        Me.Name = "frmAccountLeads"
        Me.Ribbon = Me.RibbonControl1
        Me.Text = "Account Leads"
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.lcRoot.ResumeLayout(False)
        CType(Me.cbeCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupControlContainer1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PopupControlContainer1.ResumeLayout(False)
        Me.PopupControlContainer1.PerformLayout()
        CType(Me.slueAddFromExistingCo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsCompanies, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit3View, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pmEmails, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gcAccountLeads, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsAccountLead, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvAccountLeads, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riCbeStatus, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riSeNumOfEmps, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riNotes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsRecentFiles, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AccordionControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents gcAccountLeads As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvAccountLeads As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents bsAccountLead As BindingSource
    Friend WithEvents colID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoPhoneNumber As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colContactName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoEmail As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoCellNumber As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colStatus As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNumberOfEmployees As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsNewAccount As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNotes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoPhoneNubmerExt As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoFax As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colStartingDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReferedBy As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReferedByCoNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colLastFollowedUpDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNextFollowUpDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsPayroll As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsHr As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsEndOfPeriod As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsCpa As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsPrincipal As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents bbiRefresh As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents riSeNumOfEmps As DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit
    Friend WithEvents riCbeStatus As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents riNotes As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents bbiAddLead As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiEmail As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents pmEmails As DevExpress.XtraBars.PopupMenu
    Friend WithEvents colCategory As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNum As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents PopupControlContainer1 As DevExpress.XtraBars.PopupControlContainer
    Friend WithEvents btnAddFromExistingCoOk As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents slueAddFromExistingCo As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents SearchLookUpEdit3View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents bsCompanies As BindingSource
    Friend WithEvents colCoNumAndName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPR_CONTACT As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_PHONE As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_FAX As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_MODEM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFED_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_EMAIL As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents cbeCategory As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents colAddDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colAddUser As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChgDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colChgUser As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colAssignedTo As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFedId As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPayFrequency As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents bsRecentFiles As BindingSource
    Friend WithEvents AccordionControl1 As DevExpress.XtraBars.Navigation.AccordionControl
    Friend WithEvents aceEnrollmentWeb As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceAnonymousSignup As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents acePendingMatchVerification As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceSignupInProcess As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceSignupCompleted As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceAccountLeads As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceAll As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceFollowUp As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceQualifiedLead As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceProspect As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceReadyToProcess As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceSetupInProcess As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents aceSetupDone As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents UcAccountLeadDetails1 As ucAccountLeadDetails
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents SplitterItem2 As DevExpress.XtraLayout.SplitterItem
    Friend WithEvents colEnrollment_Workflow As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents bbiMergeWithRecord As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bbiMoveToNextStatus As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents AceLeadsByStatus As DevExpress.XtraBars.Navigation.AccordionControlElement
    Friend WithEvents bbiFieldsMapping As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup2 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bbiDownloadNewSubmittions As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents colJotFormsSubmissionId As DevExpress.XtraGrid.Columns.GridColumn
End Class
