﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucReportOptions
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.gcReportOptions = New DevExpress.XtraGrid.GridControl()
        Me.prc_ReportOptionsResultBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.gvReportOptions = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colrptOption = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colRptFunction = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDefaultOption = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCategory = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colIsSet = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colSRPT_ID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colPath = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colComments = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReportsApplied = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNumApplied = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTimesReportSelected = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colUsed = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCategory1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colUsage = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.gcReportOptions, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.prc_ReportOptionsResultBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvReportOptions, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'gcReportOptions
        '
        Me.gcReportOptions.DataSource = Me.prc_ReportOptionsResultBindingSource
        Me.gcReportOptions.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gcReportOptions.Location = New System.Drawing.Point(0, 0)
        Me.gcReportOptions.MainView = Me.gvReportOptions
        Me.gcReportOptions.Name = "gcReportOptions"
        Me.gcReportOptions.Size = New System.Drawing.Size(484, 362)
        Me.gcReportOptions.TabIndex = 28
        Me.gcReportOptions.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvReportOptions})
        '
        'prc_ReportOptionsResultBindingSource
        '
        Me.prc_ReportOptionsResultBindingSource.DataSource = GetType(Brands_FrontDesk.prc_ReportOptionsResult)
        '
        'gvReportOptions
        '
        Me.gvReportOptions.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colrptOption, Me.colRptFunction, Me.colDefaultOption, Me.colCategory1, Me.colIsSet, Me.colSRPT_ID, Me.colName, Me.colCategory, Me.colDescription, Me.colPath, Me.colCONUM, Me.colComments, Me.colReportsApplied, Me.colCoNumApplied, Me.colTimesReportSelected, Me.colUsed, Me.colUsage})
        Me.gvReportOptions.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.gvReportOptions.GridControl = Me.gcReportOptions
        Me.gvReportOptions.Name = "gvReportOptions"
        Me.gvReportOptions.OptionsBehavior.Editable = False
        Me.gvReportOptions.OptionsView.ShowGroupPanel = False
        Me.gvReportOptions.OptionsView.ShowIndicator = False
        Me.gvReportOptions.OptionsView.ShowViewCaption = True
        Me.gvReportOptions.ViewCaption = "Report Options"
        '
        'colrptOption
        '
        Me.colrptOption.FieldName = "rptOption"
        Me.colrptOption.Name = "colrptOption"
        Me.colrptOption.Visible = True
        Me.colrptOption.VisibleIndex = 0
        '
        'colRptFunction
        '
        Me.colRptFunction.FieldName = "RptFunction"
        Me.colRptFunction.Name = "colRptFunction"
        Me.colRptFunction.Visible = True
        Me.colRptFunction.VisibleIndex = 1
        '
        'colDefaultOption
        '
        Me.colDefaultOption.FieldName = "DefaultOption"
        Me.colDefaultOption.Name = "colDefaultOption"
        Me.colDefaultOption.Visible = True
        Me.colDefaultOption.VisibleIndex = 2
        '
        'colCategory
        '
        Me.colCategory.FieldName = "Category"
        Me.colCategory.Name = "colCategory"
        '
        'colIsSet
        '
        Me.colIsSet.FieldName = "IsSet"
        Me.colIsSet.Name = "colIsSet"
        Me.colIsSet.Visible = True
        Me.colIsSet.VisibleIndex = 4
        '
        'colSRPT_ID
        '
        Me.colSRPT_ID.FieldName = "SRPT_ID"
        Me.colSRPT_ID.Name = "colSRPT_ID"
        '
        'colName
        '
        Me.colName.FieldName = "Name"
        Me.colName.Name = "colName"
        '
        'colDescription
        '
        Me.colDescription.FieldName = "Description"
        Me.colDescription.Name = "colDescription"
        '
        'colPath
        '
        Me.colPath.FieldName = "Path"
        Me.colPath.Name = "colPath"
        '
        'colCONUM
        '
        Me.colCONUM.FieldName = "CONUM"
        Me.colCONUM.Name = "colCONUM"
        '
        'colComments
        '
        Me.colComments.FieldName = "Comments"
        Me.colComments.Name = "colComments"
        '
        'colReportsApplied
        '
        Me.colReportsApplied.FieldName = "ReportsApplied"
        Me.colReportsApplied.Name = "colReportsApplied"
        '
        'colCoNumApplied
        '
        Me.colCoNumApplied.FieldName = "CoNumApplied"
        Me.colCoNumApplied.Name = "colCoNumApplied"
        '
        'colTimesReportSelected
        '
        Me.colTimesReportSelected.FieldName = "TimesReportSelected"
        Me.colTimesReportSelected.Name = "colTimesReportSelected"
        '
        'colUsed
        '
        Me.colUsed.FieldName = "Used"
        Me.colUsed.Name = "colUsed"
        '
        'colCategory1
        '
        Me.colCategory1.FieldName = "Category1"
        Me.colCategory1.Name = "colCategory1"
        Me.colCategory1.Visible = True
        Me.colCategory1.VisibleIndex = 3
        '
        'colUsage
        '
        Me.colUsage.FieldName = "Usage"
        Me.colUsage.Name = "colUsage"
        '
        'ucReportOptions
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.gcReportOptions)
        Me.Name = "ucReportOptions"
        Me.Size = New System.Drawing.Size(484, 362)
        CType(Me.gcReportOptions, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.prc_ReportOptionsResultBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvReportOptions, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents gcReportOptions As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvReportOptions As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents prc_ReportOptionsResultBindingSource As BindingSource
    Friend WithEvents colSRPT_ID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDescription As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colPath As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colrptOption As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colRptFunction As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCategory As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colDefaultOption As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colComments As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReportsApplied As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNumApplied As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTimesReportSelected As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUsed As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colIsSet As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCategory1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colUsage As DevExpress.XtraGrid.Columns.GridColumn
End Class
