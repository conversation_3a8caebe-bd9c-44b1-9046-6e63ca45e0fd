﻿Imports System.Data
Imports System.Text.RegularExpressions
Imports DevExpress.Utils
Imports DevExpress.XtraGrid.Columns

Public Class frmPrintingScans

    Dim DB As dbEPDataDataContext
    'Dim ClosingForm As Boolean = False

    Private Sub frmPrintingScans_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        DB = New dbEPDataDataContext(GetConnectionString)
        Dim data = (From A In DB.PrintingScans Order By A.ID Descending).Take(200).ToList
        Me.PrintingScanBindingSource.DataSource = New SortableBindingList(Of PrintingScan)(data)
        BarCodeTextBox.Focus()
        btnRefresh_Click(Nothing, Nothing)

        Timer1.Interval = 10000 'once in 10 secs get changes
        Timer1.Start()
        UpdateBadges()
    End Sub

    Private Sub BarCodeTextBox_Enter(ByVal sender As Object, ByVal e As EventArgs) Handles BarCodeTextBox.Enter
        Me.BarCodeTextBox.BackColor = Color.LightGreen
    End Sub

    Private Sub BarCodeTextBox_Leave(ByVal sender As Object, ByVal e As EventArgs) Handles BarCodeTextBox.Leave
        Me.BarCodeTextBox.BackColor = Color.FromName("Window")
    End Sub

    Private Sub BarCodeTextBox_KeyDown(ByVal sender As Object, ByVal e As KeyEventArgs) Handles BarCodeTextBox.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.Validate()
        ElseIf e.KeyCode = Keys.Escape Then
            Me.BarCodeTextBox.Text = ""
        End If
    End Sub

    Private Sub BarCodeTextBox_Validated(ByVal sender As Object, ByVal e As EventArgs) Handles BarCodeTextBox.Validated
        'processing will cancel the close
        'If ClosingForm Then Exit Sub

        If BarCodeTextBox.Text = "" Then Exit Sub
        If Regex.IsMatch(Me.BarCodeTextBox.Text, "^([0-9]+)-([0-9]+)-([0-9]+)$") Or Regex.IsMatch(Me.BarCodeTextBox.Text, "^([0-9]+)-([0-9]+)$") Then  '"^([0-9]+)-([0-9]+)$"
            Dim IsScanned = (From A In DB.view_Deliveries Where A.BarCode = BarCodeTextBox.Text Order By A.ID Descending).FirstOrDefault
            If IsScanned IsNot Nothing Then
                Dim msg = String.Format("{0} is already scanned by {1} on {2}.{3}Are you sure you want to rescan?.", BarCodeTextBox.Text, IsScanned.ScannedBy, IsScanned.ScannedDate.ToString("g"), vbCrLf)
                If MessageBox.Show(msg, "Duplicate Scan", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                    BarCodeTextBox.Text = ""
                    BarCodeTextBox.Focus()
                    Return
                End If
            End If
            Dim ent As New PrintingScan With {.BarCode = BarCodeTextBox.Text}
            Dim frm As New frmPrintingScan With {.PrintingScanEnt = ent}
            If IsScanned IsNot Nothing Then frm.IsDuplicate = True
            Dim results = frm.ShowDialog
            If results = System.Windows.Forms.DialogResult.OK Then
                ent.ShipOption = frm.SelectedOption
                ent.ShipWith = frm.ShipWithTextBox.Text.ConvertToNothing
                ent.OtherOption = frm.OtherOptionTextBox.Text.ConvertToNothing
                ent.Note = frm.NoteTextBox.Text.ConvertToNothing
                ent.ScannedBy = UserName
                ent.ScannedDate = Date.Now
                If Not frmReceiveInventory.CheckForClocks(ent.BarCode) Then
                    ent = Nothing
                    BarCodeTextBox.Text = ""
                    BarCodeTextBox.Focus()
                    Exit Sub
                End If
                If frm.SelectedOption <> ShipOption.Pickup Then frmOrderSupplies.ChargeShippingForPaperlessClients(DB, ent.BarCode)
                DB.PrintingScans.InsertOnSubmit(ent)
                DB.SubmitChanges()
                PrintingScanBindingSource.Insert(0, ent)
            Else
                ent = Nothing
            End If
            frm.Close()
            frm.Dispose()

            BarCodeTextBox.Text = ""
            BarCodeTextBox.Focus()

            ''for option Direct EE Mailing close when success
            'If frm.Tag = "CloseParent" Then
            '    ClosingForm = True      'dont process some events
            '    Timer1.Enabled = True   'delay for second till ui gets avail
            '    Return
            'End If
        Else
E:
            My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Exclamation)
            MessageBox.Show("Invalid Company-Payroll Number")
            Me.BarCodeTextBox.Focus()
            Me.BarCodeTextBox.SelectionStart = 0
            Me.BarCodeTextBox.SelectionLength = 100
        End If
    End Sub

    Private Sub PrintingScanDataGridView_CellFormatting(ByVal sender As Object, ByVal e As DataGridViewCellFormattingEventArgs) Handles PrintingScanDataGridView.CellFormatting
        If e.RowIndex < 0 Then Exit Sub
        If e.ColumnIndex = colShipOption.Index Then
            e.Value = [Enum].GetName(GetType(ShipOption), e.Value)
        End If
    End Sub

    Private Sub PrintingScanDataGridView_UserDeletingRow(ByVal sender As Object, ByVal e As DataGridViewRowCancelEventArgs) Handles PrintingScanDataGridView.UserDeletingRow
        If MessageBox.Show("Are you sure you want to delete this record?", "Confirm delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.No Then
            e.Cancel = True
        Else
            Dim PS As PrintingScan = e.Row.DataBoundItem
            DB.PrintingScans.DeleteOnSubmit(PS)
            DB.SubmitChanges()
        End If
    End Sub

    Private Sub PrintingScanDataGridView_CellDoubleClick(ByVal sender As Object, ByVal e As DataGridViewCellEventArgs) Handles PrintingScanDataGridView.CellDoubleClick
        If e.RowIndex < 0 Then Exit Sub
        Dim CurrentEnt As PrintingScan = Me.PrintingScanDataGridView.Rows(e.RowIndex).DataBoundItem
        Dim ent As New PrintingScan With {.ID = CurrentEnt.ID,
                                          .BarCode = CurrentEnt.BarCode,
                                          .Note = CurrentEnt.Note,
                                          .OtherOption = CurrentEnt.OtherOption,
                                          .ShipOption = CurrentEnt.ShipOption,
                                          .ShipWith = CurrentEnt.ShipWith}
        Dim frm As New frmPrintingScan With {.PrintingScanEnt = ent}
        Dim results = frm.ShowDialog
        If results = System.Windows.Forms.DialogResult.OK Then
            CurrentEnt.ShipOption = frm.SelectedOption
            CurrentEnt.ShipWith = frm.ShipWithTextBox.Text.ConvertToNothing
            CurrentEnt.OtherOption = frm.OtherOptionTextBox.Text.ConvertToNothing
            CurrentEnt.Note = frm.NoteTextBox.Text.ConvertToNothing
            DB.SubmitChanges()
        End If
        frm.Close()
        frm.Dispose()
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        GridControl1.DataSource = Query("exec custom.prc_GetPayrollsReadyToPrint")
        For Each col As GridColumn In GridView1.Columns
            If col.FieldName.ToLower().Contains("rpt") Then
                col.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList
            End If
        Next

        For Each col As DataColumn In DirectCast(GridControl1.DataSource, DataTable).Columns
            If col.DataType = GetType(DateTime) Then
                Dim c = GridView1.Columns.ColumnByFieldName(col.ColumnName)
                c.DisplayFormat.FormatType = FormatType.DateTime
                c.DisplayFormat.FormatString = "G"
            End If
        Next

        GridView1.BestFitColumns()
    End Sub

    Private Sub bbiReprintCoverSheet_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiReprintCoverSheet.ItemClick
        Dim frm = New frmReprintCoverSheet
        frm.ShowDialog()
    End Sub

    Private Sub bbiPrintW2CoverSheet_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiPrintW2CoverSheet.ItemClick
        Dim frm = New frmReprintCoverSheet(True)
        frm.ShowDialog()
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        Try
            If e.Allow AndAlso e.HitInfo.RowHandle >= 0 Then
                Dim printerType = GridView1.GetRowCellValue(e.HitInfo.RowHandle, "PrinterType")
                Logger.Debug("User right clicked on grid, PrinterType: {PrinterType}", printerType)
                If printerType.ToString().StartsWith("Stuck") OrElse printerType.ToString.Contains("Auto Print") Then
                    Dim conum = Convert.ToInt32(GridView1.GetRowCellValue(e.HitInfo.RowHandle, "CONUM"))
                    Dim prnum = Convert.ToInt32(GridView1.GetRowCellValue(e.HitInfo.RowHandle, "PRNUM"))

                    If printerType.ToString().StartsWith("Stuck") Then
                        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Fix Payroll", Sub() UpdatePayrollSubmitType(conum, prnum)))
                    ElseIf printerType.ToString().Contains("Auto Print") AndAlso (nz(Permissions.AllowProcessingPayroll, False).GetValueOrDefault OrElse nz(Permissions.Manager, False).GetValueOrDefault) Then
                        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete From Queue Print Row (use when not loading in auto print)", Sub() DeletePrintQueue(conum, prnum)))
                    End If
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in fix payroll menu", ex)
        End Try
    End Sub

    Private Sub UpdatePayrollSubmitType(conum As Integer, prnum As Integer)
        Try
            Logger.Debug("Entering UpdatePayrollSubmitType Conum: {Conum} Prnum: {Prnum}", conum, prnum)
            Using DB As New dbEPDataDataContext(GetConnectionString)
                Dim payroll_ext = DB.payroll_exts.Single(Function(p) p.conum = conum AndAlso p.prnum = prnum)
                payroll_ext.submit_type = 3
                DB.SaveChanges()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error updating Payroll submit type", ex)
        End Try
    End Sub

    Private Sub DeletePrintQueue(conum As Integer, prnum As Integer)
        Try
            Logger.Debug("Entering DeletePrintQueue Conum: {Conum} Prnum: {Prnum}", conum, prnum)

            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you would like to delete the print queue row?", "Delete Print Queue Row?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = DialogResult.No Then
                Return
            End If

            Using DB As New dbEPDataDataContext(GetConnectionString)
                DB.ExecuteCommand(String.Format("DELETE	pqt
FROM	print_queue pqt 
		INNER JOIN PAYROLL P ON P.CONUM = pqt.conum AND P.PRNUM = pqt.prnum
		INNER JOIN payroll_ext pet ON pet.conum = P.CONUM AND pet.prnum = P.PRNUM
WHERE	pqt.conum = {0} AND pqt.prnum = {1}
		AND P.PAYROLL_STATUS = 'Submitted'
		AND pet.submit_type = 3", conum, prnum))
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error DeletePrintQueue", ex)
        End Try
    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        'Close()
        'Timer1.Enabled = False

        If IsActive Then
            UpdateBadges()
        End If
    End Sub

    Private Sub UpdateBadges()
        Dim OrderQueueCount = DB.SuppliesOrders.Where(Function(o) o.Status = "Open" OrElse o.Status = $"In Progress - {Environment.MachineName}").Count
        BadgeOrdersQueue.Properties.Text = OrderQueueCount

        Dim PayrollReadyToPrintCount = Query("exec custom.prc_GetPayrollsReadyToPrint").Rows.Count
        BadgePayrollsReadyToPrint.Properties.Text = PayrollReadyToPrintCount
    End Sub
End Class