﻿Imports System.Configuration
Imports System.Data
Imports System.IO
Imports System.Net.Mail
Imports PwdEnc
Public Class modLibrary
    Public Shared Sub WriteMsgToConsole(Msg As String)
        Console.WriteLine(String.Format("{0} {1}", DateTime.Now, Msg))
    End Sub

    Public Shared Function ReportWasProcessed(db As dbEPDataDataContext, CoNum As Integer, reportId As Int16, lastQtrDate As Date) As Boolean
        Return db.QuarterlyPreProcessedFiles.Where(Function(f) f.CONUM = CoNum AndAlso f.RPT_ID = reportId AndAlso f.PRT_YEAR = lastQtrDate.Year AndAlso f.PRT_QTR = lastQtrDate.Quarter).Count > 0
    End Function

    Public Shared Sub SaveQuarterlyPreProcessedFile(db As dbEPDataDataContext, CoNum As Integer, report As ReportResults, lastQtrDate As Date)
        Dim processedFile = New QuarterlyPreProcessedFile With {
            .ID = Guid.NewGuid,
            .RPT_ID = report.EmailTemplate.WebPostReportId,
            .RPT_ORDER = 2,
            .RPT_FILE = System.IO.File.ReadAllBytes(report.Paths.First),
            .RPT_SIZE = New FileInfo(report.Paths.First).Length,
            .PRT_DATE = DateTime.Now,
            .PRT_QTR = lastQtrDate.Quarter,
            .PRT_YEAR = lastQtrDate.Year,
            .RPT_STATUS = "Completed",
            .JOB_TYPE = "Client",
            .PRT_COMPLETEDDATE = Today,
            .CONUM = CoNum,
            .RPT_STATE = "NY",
            .file_arcflag = Nothing,
            .RPT_PRTTYPE = 2,
            .rpt_posted = String.Empty
        }
        db.QuarterlyPreProcessedFiles.InsertOnSubmit(processedFile)
        db.SubmitChanges()
    End Sub

    Public Shared Sub ProcessReport(TemplateId As Int32, row As DataRow, db As dbEPDataDataContext, lastQtrDate As Date)
        Dim report = db.ReportEmailTeplates.SingleOrDefault(Function(rp) rp.ID = TemplateId)
        'report.Path = report.Path.Replace("R:", "\\file1\reports")
        Dim params = db.QtrReportsToProcessAndSaveMappings.Where(Function(f) f.ReportID = TemplateId).ToList()

        Dim processor = New ReportProcessor(Convert.ToInt32(row("CoNum")), report, FileType.Pdf) _
                With {.showInRecentReports = False, .showParametersForm = False, .useEPTestPath = False}

        processor.DefaultParamValues = New List(Of KeyValuePair)

        Dim param As QtrReportsToProcessAndSaveMapping
        For Each param In params
            If param.IsField Then
                processor.DefaultParamValues.Add(New KeyValuePair(param.Param, row(param.Value)))
            ElseIf param.Value = "[QtrEndDate]" Then
                processor.DefaultParamValues.Add(New KeyValuePair(param.Param, lastQtrDate))
            Else
                processor.DefaultParamValues.Add(New KeyValuePair(param.Param, param.Value))
            End If
        Next

        Dim result = processor.ProcessReport()
        If Not result.Cancalled AndAlso result.AllFileExist Then
            SaveQuarterlyPreProcessedFile(db, row("CoNum"), result, lastQtrDate)
        Else
            Throw New Exception("Report was cancelled or file not exists")
        End If
    End Sub

    Public Shared Sub CollectAndSaveQuarterlyReports(Optional Udf As String = "")
        Dim cs = PwdEncr.DecUI(ConfigurationManager.AppSettings("uidp")).Split(New Char() {"=", ";"}, 4)
        UserName = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(1), "")
        Password = IIf(cs IsNot Nothing AndAlso cs.Length() = 4, cs(3), "")

        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim sb As New System.Text.StringBuilder(10000)
        Dim lastQtrDate As Date = GetLastQuarterEndDate()
        Dim TotalErrorsFound As Integer = 0

        Try
            Dim ReportsToProcess As List(Of QtrReportsToProcessAndSave) = Nothing

            If Not Udf = "" Then
                Dim udfValue = db.UDFs.Where(Function(u) u.name = Udf).FirstOrDefault().value
                ReportsToProcess = db.ExecuteQuery(Of QtrReportsToProcessAndSave)(udfValue, New String() {}).ToList()
            Else
                ReportsToProcess = db.QtrReportsToProcessAndSaves.Where(Function(f) f.Active).ToList()
            End If

            Dim Rep As QtrReportsToProcessAndSave
            For Each Rep In ReportsToProcess
                Dim EmailTemplate = db.ReportEmailTeplates.Where(Function(f) f.ID = Rep.ID.ToString()).FirstOrDefault()
                Dim taskName As String = EmailTemplate.Name
                WriteMsgToConsole("Starting task " + taskName)
                Try
                    Dim data = Query(Rep.SqlString)

                    If data.Rows.Count = 0 Then Continue For

                    If data.Columns.IndexOf("conum") = -1 Then
                        Throw New Exception(String.Format("Error processing report {0}({1}) in frmCollectAndSaveReports (Quarterly pre-processing reports).  Missing CoNum field", taskName, Rep.ID))
                    End If

                    For Each r In data.Rows
                        Dim conum As String = r("Conum")
                        Try
                            WriteMsgToConsole("Processing Report for CoNum " + conum.ToString())
                            If Not ReportWasProcessed(db, conum, EmailTemplate.WebPostReportId, lastQtrDate) Then
                                ProcessReport(Rep.ID, r, db, lastQtrDate)
                            End If
                        Catch ex As Exception
                            TotalErrorsFound += 1

                            Dim msg As String
                            msg = String.Format("Error processing report {0}({1}) for CoNum {2} in frmCollectAndSaveReports (Quarterly pre-processing reports)", taskName, Rep.ID, conum)
                            Console.WriteLine(msg + vbCrLf)
                            Console.WriteLine(ex.ToString() + vbCrLf)
                            Logger.Error(ex, msg)

                            sb.Append("Error:")
                            sb.Append(msg)
                            sb.Append(ex)
                            sb.Append("------------------------")
                            sb.Append(vbCrLf + vbCrLf)
                        End Try
                    Next
                Catch ex As Exception
                    TotalErrorsFound += 1

                    Dim msg As String
                    msg = String.Format("Error processing report {0}({1}) in frmCollectAndSaveReports (Quarterly pre-processing reports)", taskName, Rep.ID)
                    Console.WriteLine(msg + vbCrLf)
                    Console.WriteLine(ex.ToString() + vbCrLf)
                    Logger.Error(ex, msg)

                    sb.Append("Error:")
                    sb.Append(msg)
                    sb.Append(ex)
                    sb.Append("------------------------")
                    sb.Append(vbCrLf + vbCrLf)
                End Try
            Next
        Catch ex As Exception
            TotalErrorsFound += 1

            Dim msg As String
            msg = "Error processing reports in frmCollectAndSaveReports (Quarterly pre-processing reports)"
            Console.WriteLine(msg + vbCrLf)
            Console.WriteLine(ex.ToString() + vbCrLf)
            Logger.Error(ex, msg)

            sb.Append("Error:")
            sb.Append(msg)
            sb.Append(ex)
            sb.Append("------------------------")
            sb.Append(vbCrLf + vbCrLf)
        End Try

        If TotalErrorsFound > 0 Then
            Console.WriteLine("Error Summary")
            Console.WriteLine(sb.ToString() + vbCrLf)

            Dim SmtpServer As New SmtpClient()
            Dim m As New MailMessage()
            m.From = New MailAddress("<EMAIL>")
            m.To.Add(New MailAddress("<EMAIL>"))
            m.Subject = "Pre-process Qtr Reports Error"
            m.Body = sb.ToString()
            SmtpServer.Send(m)
        End If
    End Sub
End Class
