﻿Imports System.ComponentModel
Namespace Ach
    Public Class frmAchSettings
        Dim frmLogger As Serilog.ILogger
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property _AchSettings As AchSettings
        Public Sub New(setting As AchSettings)
            InitializeComponent()
            frmLogger = Logger.ForContext(Of frmAchSettings)
            _AchSettings = setting
            BindingSource1.DataSource = setting
            PopulateDropDowns()
        End Sub


        Private Sub PopulateDropDowns()
            ccbeDefaultStatusFIlter.Properties.DataSource = _AchSettings.StatusList
        End Sub

        Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
            DialogResult = DialogResult.OK
        End Sub

        Private Sub meStatusDropDown_Validated(sender As Object, e As EventArgs) Handles meStatusDropDown.Validated
            PopulateDropDowns()
        End Sub
    End Class
End Namespace