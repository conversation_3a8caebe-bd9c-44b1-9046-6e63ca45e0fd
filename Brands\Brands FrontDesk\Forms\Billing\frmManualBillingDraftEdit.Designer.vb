﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmManualBillingDraftEdit
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim ColumnDefinition1 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim ColumnDefinition2 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim RowDefinition1 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim ColumnDefinition3 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim ColumnDefinition4 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim RowDefinition2 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim RowDefinition3 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim RowDefinition4 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim RowDefinition5 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim RowDefinition6 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim ColumnDefinition5 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim ColumnDefinition6 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim RowDefinition7 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Me.NachaTaxDraftsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ROUTING_NUMBERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ACCOUNT_NUMBERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ACCT_TYPEComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Trans_NoteTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.AMOUNTTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Transaction_StatusComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Credit_RoutingTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Credit_AccountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Credit_ACCT_TYPEComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ChangeLogMemoEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.TransTypeTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Date_RequestedDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForROUTING_NUMBER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForACCOUNT_NUMBER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForACCT_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForCredit_Routing = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCredit_Account = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCredit_ACCT_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup5 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForTransType = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForTrans_Note = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForDate_Requested = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForAMOUNT = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForTransaction_Status = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup6 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForChangeLog = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup7 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.NachaTaxDraftsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.ROUTING_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCOUNT_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCT_TYPEComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Trans_NoteTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AMOUNTTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Transaction_StatusComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_RoutingTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_AccountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Credit_ACCT_TYPEComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChangeLogMemoEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TransTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_RequestedDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForROUTING_NUMBER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCOUNT_NUMBER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCT_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_Routing, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_Account, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCredit_ACCT_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTransType, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTrans_Note, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDate_Requested, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForAMOUNT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTransaction_Status, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForChangeLog, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'NachaTaxDraftsBindingSource
        '
        Me.NachaTaxDraftsBindingSource.DataSource = GetType(Brands_FrontDesk.DD_Manual_Trans_Log)
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.Controls.Add(Me.btnCancel)
        Me.DataLayoutControl1.Controls.Add(Me.btnOK)
        Me.DataLayoutControl1.Controls.Add(Me.ROUTING_NUMBERTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCOUNT_NUMBERTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCT_TYPEComboBoxEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Trans_NoteTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.AMOUNTTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Transaction_StatusComboBoxEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_RoutingTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_AccountTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Credit_ACCT_TYPEComboBoxEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ChangeLogMemoEdit)
        Me.DataLayoutControl1.Controls.Add(Me.TransTypeTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Date_RequestedDateEdit)
        Me.DataLayoutControl1.DataSource = Me.NachaTaxDraftsBindingSource
        Me.DataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.DataLayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(562, 205, 650, 400)
        Me.DataLayoutControl1.Root = Me.Root
        Me.DataLayoutControl1.Size = New System.Drawing.Size(507, 466)
        Me.DataLayoutControl1.TabIndex = 22
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(325, 419)
        Me.btnCancel.MaximumSize = New System.Drawing.Size(100, 0)
        Me.btnCancel.MinimumSize = New System.Drawing.Size(0, 35)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(100, 35)
        Me.btnCancel.StyleController = Me.DataLayoutControl1
        Me.btnCancel.TabIndex = 25
        Me.btnCancel.Text = "Cancel"
        '
        'btnOK
        '
        Me.btnOK.Location = New System.Drawing.Point(81, 419)
        Me.btnOK.MaximumSize = New System.Drawing.Size(100, 0)
        Me.btnOK.MinimumSize = New System.Drawing.Size(0, 35)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(100, 35)
        Me.btnOK.StyleController = Me.DataLayoutControl1
        Me.btnOK.TabIndex = 24
        Me.btnOK.Text = "OK"
        '
        'ROUTING_NUMBERTextEdit
        '
        Me.ROUTING_NUMBERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "ROUTING_NUMBER", True))
        Me.ROUTING_NUMBERTextEdit.Location = New System.Drawing.Point(77, 208)
        Me.ROUTING_NUMBERTextEdit.Name = "ROUTING_NUMBERTextEdit"
        Me.ROUTING_NUMBERTextEdit.Properties.MaxLength = 9
        Me.ROUTING_NUMBERTextEdit.Size = New System.Drawing.Size(162, 20)
        Me.ROUTING_NUMBERTextEdit.StyleController = Me.DataLayoutControl1
        Me.ROUTING_NUMBERTextEdit.TabIndex = 4
        '
        'ACCOUNT_NUMBERTextEdit
        '
        Me.ACCOUNT_NUMBERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "ACCOUNT_NUMBER", True))
        Me.ACCOUNT_NUMBERTextEdit.Location = New System.Drawing.Point(77, 232)
        Me.ACCOUNT_NUMBERTextEdit.Name = "ACCOUNT_NUMBERTextEdit"
        Me.ACCOUNT_NUMBERTextEdit.Properties.MaxLength = 30
        Me.ACCOUNT_NUMBERTextEdit.Size = New System.Drawing.Size(162, 20)
        Me.ACCOUNT_NUMBERTextEdit.StyleController = Me.DataLayoutControl1
        Me.ACCOUNT_NUMBERTextEdit.TabIndex = 5
        '
        'ACCT_TYPEComboBoxEdit
        '
        Me.ACCT_TYPEComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "ACCT_TYPE", True))
        Me.ACCT_TYPEComboBoxEdit.Location = New System.Drawing.Point(77, 256)
        Me.ACCT_TYPEComboBoxEdit.Name = "ACCT_TYPEComboBoxEdit"
        Me.ACCT_TYPEComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ACCT_TYPEComboBoxEdit.Properties.Items.AddRange(New Object() {"C", "S"})
        Me.ACCT_TYPEComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ACCT_TYPEComboBoxEdit.Size = New System.Drawing.Size(162, 20)
        Me.ACCT_TYPEComboBoxEdit.StyleController = Me.DataLayoutControl1
        Me.ACCT_TYPEComboBoxEdit.TabIndex = 6
        '
        'Trans_NoteTextEdit
        '
        Me.Trans_NoteTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "Trans_Note", True))
        Me.Trans_NoteTextEdit.Location = New System.Drawing.Point(117, 68)
        Me.Trans_NoteTextEdit.Name = "Trans_NoteTextEdit"
        Me.Trans_NoteTextEdit.Properties.ReadOnly = True
        Me.Trans_NoteTextEdit.Size = New System.Drawing.Size(134, 20)
        Me.Trans_NoteTextEdit.StyleController = Me.DataLayoutControl1
        Me.Trans_NoteTextEdit.TabIndex = 7
        '
        'AMOUNTTextEdit
        '
        Me.AMOUNTTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "AMOUNT", True))
        Me.AMOUNTTextEdit.Location = New System.Drawing.Point(117, 116)
        Me.AMOUNTTextEdit.Name = "AMOUNTTextEdit"
        Me.AMOUNTTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.AMOUNTTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.AMOUNTTextEdit.Properties.Mask.EditMask = "G"
        Me.AMOUNTTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.AMOUNTTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.AMOUNTTextEdit.Properties.ReadOnly = True
        Me.AMOUNTTextEdit.Size = New System.Drawing.Size(134, 20)
        Me.AMOUNTTextEdit.StyleController = Me.DataLayoutControl1
        Me.AMOUNTTextEdit.TabIndex = 8
        '
        'Transaction_StatusComboBoxEdit
        '
        Me.Transaction_StatusComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "Transaction_Status", True))
        Me.Transaction_StatusComboBoxEdit.Location = New System.Drawing.Point(117, 140)
        Me.Transaction_StatusComboBoxEdit.Name = "Transaction_StatusComboBoxEdit"
        Me.Transaction_StatusComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Transaction_StatusComboBoxEdit.Properties.Items.AddRange(New Object() {"New", "For Review"})
        Me.Transaction_StatusComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Transaction_StatusComboBoxEdit.Size = New System.Drawing.Size(134, 20)
        Me.Transaction_StatusComboBoxEdit.StyleController = Me.DataLayoutControl1
        Me.Transaction_StatusComboBoxEdit.TabIndex = 9
        '
        'Credit_RoutingTextEdit
        '
        Me.Credit_RoutingTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "Credit_Routing", True))
        Me.Credit_RoutingTextEdit.Location = New System.Drawing.Point(320, 208)
        Me.Credit_RoutingTextEdit.Name = "Credit_RoutingTextEdit"
        Me.Credit_RoutingTextEdit.Properties.MaxLength = 9
        Me.Credit_RoutingTextEdit.Size = New System.Drawing.Size(163, 20)
        Me.Credit_RoutingTextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_RoutingTextEdit.TabIndex = 10
        '
        'Credit_AccountTextEdit
        '
        Me.Credit_AccountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "Credit_Account", True))
        Me.Credit_AccountTextEdit.Location = New System.Drawing.Point(320, 232)
        Me.Credit_AccountTextEdit.Name = "Credit_AccountTextEdit"
        Me.Credit_AccountTextEdit.Properties.MaxLength = 30
        Me.Credit_AccountTextEdit.Size = New System.Drawing.Size(163, 20)
        Me.Credit_AccountTextEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_AccountTextEdit.TabIndex = 11
        '
        'Credit_ACCT_TYPEComboBoxEdit
        '
        Me.Credit_ACCT_TYPEComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "Credit_ACCT_TYPE", True))
        Me.Credit_ACCT_TYPEComboBoxEdit.Location = New System.Drawing.Point(320, 256)
        Me.Credit_ACCT_TYPEComboBoxEdit.Name = "Credit_ACCT_TYPEComboBoxEdit"
        Me.Credit_ACCT_TYPEComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Credit_ACCT_TYPEComboBoxEdit.Properties.Items.AddRange(New Object() {"C", "S"})
        Me.Credit_ACCT_TYPEComboBoxEdit.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Credit_ACCT_TYPEComboBoxEdit.Size = New System.Drawing.Size(163, 20)
        Me.Credit_ACCT_TYPEComboBoxEdit.StyleController = Me.DataLayoutControl1
        Me.Credit_ACCT_TYPEComboBoxEdit.TabIndex = 12
        '
        'ChangeLogMemoEdit
        '
        Me.ChangeLogMemoEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "ChangeLog", True))
        Me.ChangeLogMemoEdit.Location = New System.Drawing.Point(24, 324)
        Me.ChangeLogMemoEdit.Name = "ChangeLogMemoEdit"
        Me.ChangeLogMemoEdit.Properties.ReadOnly = True
        Me.ChangeLogMemoEdit.Size = New System.Drawing.Size(459, 79)
        Me.ChangeLogMemoEdit.StyleController = Me.DataLayoutControl1
        Me.ChangeLogMemoEdit.TabIndex = 13
        '
        'TransTypeTextEdit
        '
        Me.TransTypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "TransType", True))
        Me.TransTypeTextEdit.Location = New System.Drawing.Point(117, 44)
        Me.TransTypeTextEdit.Name = "TransTypeTextEdit"
        Me.TransTypeTextEdit.Properties.ReadOnly = True
        Me.TransTypeTextEdit.Size = New System.Drawing.Size(134, 20)
        Me.TransTypeTextEdit.StyleController = Me.DataLayoutControl1
        Me.TransTypeTextEdit.TabIndex = 22
        '
        'Date_RequestedDateEdit
        '
        Me.Date_RequestedDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.NachaTaxDraftsBindingSource, "Date_Requested", True))
        Me.Date_RequestedDateEdit.EditValue = Nothing
        Me.Date_RequestedDateEdit.Location = New System.Drawing.Point(117, 92)
        Me.Date_RequestedDateEdit.Name = "Date_RequestedDateEdit"
        Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_RequestedDateEdit.Properties.ReadOnly = True
        Me.Date_RequestedDateEdit.Size = New System.Drawing.Size(134, 20)
        Me.Date_RequestedDateEdit.StyleController = Me.DataLayoutControl1
        Me.Date_RequestedDateEdit.TabIndex = 23
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(507, 466)
        Me.Root.TextVisible = False
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AllowDrawBackground = False
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup4, Me.LayoutControlGroup5, Me.LayoutControlGroup6, Me.LayoutControlGroup7})
        Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup1.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(487, 446)
        '
        'LayoutControlGroup4
        '
        Me.LayoutControlGroup4.GroupBordersVisible = False
        Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2, Me.LayoutControlGroup3})
        Me.LayoutControlGroup4.LayoutMode = DevExpress.XtraLayout.Utils.LayoutMode.Table
        Me.LayoutControlGroup4.Location = New System.Drawing.Point(0, 164)
        Me.LayoutControlGroup4.Name = "LayoutControlGroup4"
        ColumnDefinition1.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition1.Width = 50.0R
        ColumnDefinition2.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition2.Width = 50.0R
        Me.LayoutControlGroup4.OptionsTableLayoutGroup.ColumnDefinitions.AddRange(New DevExpress.XtraLayout.ColumnDefinition() {ColumnDefinition1, ColumnDefinition2})
        RowDefinition1.Height = 100.0R
        RowDefinition1.SizeType = System.Windows.Forms.SizeType.Percent
        Me.LayoutControlGroup4.OptionsTableLayoutGroup.RowDefinitions.AddRange(New DevExpress.XtraLayout.RowDefinition() {RowDefinition1})
        Me.LayoutControlGroup4.Size = New System.Drawing.Size(487, 116)
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForROUTING_NUMBER, Me.ItemForACCOUNT_NUMBER, Me.ItemForACCT_TYPE})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignWithChildren
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(243, 116)
        Me.LayoutControlGroup2.Text = "From Account"
        '
        'ItemForROUTING_NUMBER
        '
        Me.ItemForROUTING_NUMBER.Control = Me.ROUTING_NUMBERTextEdit
        Me.ItemForROUTING_NUMBER.Location = New System.Drawing.Point(0, 0)
        Me.ItemForROUTING_NUMBER.Name = "ItemForROUTING_NUMBER"
        Me.ItemForROUTING_NUMBER.Size = New System.Drawing.Size(219, 24)
        Me.ItemForROUTING_NUMBER.Text = "Routing #"
        Me.ItemForROUTING_NUMBER.TextSize = New System.Drawing.Size(50, 13)
        '
        'ItemForACCOUNT_NUMBER
        '
        Me.ItemForACCOUNT_NUMBER.Control = Me.ACCOUNT_NUMBERTextEdit
        Me.ItemForACCOUNT_NUMBER.Location = New System.Drawing.Point(0, 24)
        Me.ItemForACCOUNT_NUMBER.Name = "ItemForACCOUNT_NUMBER"
        Me.ItemForACCOUNT_NUMBER.Size = New System.Drawing.Size(219, 24)
        Me.ItemForACCOUNT_NUMBER.Text = "Account #"
        Me.ItemForACCOUNT_NUMBER.TextSize = New System.Drawing.Size(50, 13)
        '
        'ItemForACCT_TYPE
        '
        Me.ItemForACCT_TYPE.Control = Me.ACCT_TYPEComboBoxEdit
        Me.ItemForACCT_TYPE.Location = New System.Drawing.Point(0, 48)
        Me.ItemForACCT_TYPE.Name = "ItemForACCT_TYPE"
        Me.ItemForACCT_TYPE.Size = New System.Drawing.Size(219, 24)
        Me.ItemForACCT_TYPE.Text = "Acct Type"
        Me.ItemForACCT_TYPE.TextSize = New System.Drawing.Size(50, 13)
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForCredit_Routing, Me.ItemForCredit_Account, Me.ItemForCredit_ACCT_TYPE})
        Me.LayoutControlGroup3.Location = New System.Drawing.Point(243, 0)
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignWithChildren
        Me.LayoutControlGroup3.OptionsTableLayoutItem.ColumnIndex = 1
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(244, 116)
        Me.LayoutControlGroup3.Text = "To Account"
        '
        'ItemForCredit_Routing
        '
        Me.ItemForCredit_Routing.Control = Me.Credit_RoutingTextEdit
        Me.ItemForCredit_Routing.Location = New System.Drawing.Point(0, 0)
        Me.ItemForCredit_Routing.Name = "ItemForCredit_Routing"
        Me.ItemForCredit_Routing.Size = New System.Drawing.Size(220, 24)
        Me.ItemForCredit_Routing.Text = "Routing #"
        Me.ItemForCredit_Routing.TextSize = New System.Drawing.Size(50, 13)
        '
        'ItemForCredit_Account
        '
        Me.ItemForCredit_Account.Control = Me.Credit_AccountTextEdit
        Me.ItemForCredit_Account.Location = New System.Drawing.Point(0, 24)
        Me.ItemForCredit_Account.Name = "ItemForCredit_Account"
        Me.ItemForCredit_Account.Size = New System.Drawing.Size(220, 24)
        Me.ItemForCredit_Account.Text = "Account #"
        Me.ItemForCredit_Account.TextSize = New System.Drawing.Size(50, 13)
        '
        'ItemForCredit_ACCT_TYPE
        '
        Me.ItemForCredit_ACCT_TYPE.Control = Me.Credit_ACCT_TYPEComboBoxEdit
        Me.ItemForCredit_ACCT_TYPE.Location = New System.Drawing.Point(0, 48)
        Me.ItemForCredit_ACCT_TYPE.Name = "ItemForCredit_ACCT_TYPE"
        Me.ItemForCredit_ACCT_TYPE.Size = New System.Drawing.Size(220, 24)
        Me.ItemForCredit_ACCT_TYPE.Text = "Acct Type"
        Me.ItemForCredit_ACCT_TYPE.TextSize = New System.Drawing.Size(50, 13)
        '
        'LayoutControlGroup5
        '
        Me.LayoutControlGroup5.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForTransType, Me.ItemForTrans_Note, Me.ItemForDate_Requested, Me.ItemForAMOUNT, Me.ItemForTransaction_Status})
        Me.LayoutControlGroup5.LayoutMode = DevExpress.XtraLayout.Utils.LayoutMode.Table
        Me.LayoutControlGroup5.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup5.Name = "LayoutControlGroup5"
        ColumnDefinition3.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition3.Width = 50.0R
        ColumnDefinition4.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition4.Width = 50.0R
        Me.LayoutControlGroup5.OptionsTableLayoutGroup.ColumnDefinitions.AddRange(New DevExpress.XtraLayout.ColumnDefinition() {ColumnDefinition3, ColumnDefinition4})
        RowDefinition2.Height = 24.0R
        RowDefinition2.SizeType = System.Windows.Forms.SizeType.AutoSize
        RowDefinition3.Height = 24.0R
        RowDefinition3.SizeType = System.Windows.Forms.SizeType.AutoSize
        RowDefinition4.Height = 24.0R
        RowDefinition4.SizeType = System.Windows.Forms.SizeType.AutoSize
        RowDefinition5.Height = 24.0R
        RowDefinition5.SizeType = System.Windows.Forms.SizeType.AutoSize
        RowDefinition6.Height = 24.0R
        RowDefinition6.SizeType = System.Windows.Forms.SizeType.AutoSize
        Me.LayoutControlGroup5.OptionsTableLayoutGroup.RowDefinitions.AddRange(New DevExpress.XtraLayout.RowDefinition() {RowDefinition2, RowDefinition3, RowDefinition4, RowDefinition5, RowDefinition6})
        Me.LayoutControlGroup5.Size = New System.Drawing.Size(487, 164)
        Me.LayoutControlGroup5.Text = "Transaction Details"
        '
        'ItemForTransType
        '
        Me.ItemForTransType.Control = Me.TransTypeTextEdit
        Me.ItemForTransType.Location = New System.Drawing.Point(0, 0)
        Me.ItemForTransType.Name = "ItemForTransType"
        Me.ItemForTransType.Size = New System.Drawing.Size(231, 24)
        Me.ItemForTransType.Text = "Trans Type"
        Me.ItemForTransType.TextSize = New System.Drawing.Size(90, 13)
        '
        'ItemForTrans_Note
        '
        Me.ItemForTrans_Note.Control = Me.Trans_NoteTextEdit
        Me.ItemForTrans_Note.Location = New System.Drawing.Point(0, 24)
        Me.ItemForTrans_Note.Name = "ItemForTrans_Note"
        Me.ItemForTrans_Note.OptionsTableLayoutItem.RowIndex = 1
        Me.ItemForTrans_Note.Size = New System.Drawing.Size(231, 24)
        Me.ItemForTrans_Note.Text = "Trans Note"
        Me.ItemForTrans_Note.TextSize = New System.Drawing.Size(90, 13)
        '
        'ItemForDate_Requested
        '
        Me.ItemForDate_Requested.Control = Me.Date_RequestedDateEdit
        Me.ItemForDate_Requested.Location = New System.Drawing.Point(0, 48)
        Me.ItemForDate_Requested.Name = "ItemForDate_Requested"
        Me.ItemForDate_Requested.OptionsTableLayoutItem.RowIndex = 2
        Me.ItemForDate_Requested.Size = New System.Drawing.Size(231, 24)
        Me.ItemForDate_Requested.Text = "Date Requested"
        Me.ItemForDate_Requested.TextSize = New System.Drawing.Size(90, 13)
        '
        'ItemForAMOUNT
        '
        Me.ItemForAMOUNT.Control = Me.AMOUNTTextEdit
        Me.ItemForAMOUNT.Location = New System.Drawing.Point(0, 72)
        Me.ItemForAMOUNT.Name = "ItemForAMOUNT"
        Me.ItemForAMOUNT.OptionsTableLayoutItem.RowIndex = 3
        Me.ItemForAMOUNT.Size = New System.Drawing.Size(231, 24)
        Me.ItemForAMOUNT.Text = "Amount"
        Me.ItemForAMOUNT.TextSize = New System.Drawing.Size(90, 13)
        '
        'ItemForTransaction_Status
        '
        Me.ItemForTransaction_Status.Control = Me.Transaction_StatusComboBoxEdit
        Me.ItemForTransaction_Status.Location = New System.Drawing.Point(0, 96)
        Me.ItemForTransaction_Status.Name = "ItemForTransaction_Status"
        Me.ItemForTransaction_Status.OptionsTableLayoutItem.RowIndex = 4
        Me.ItemForTransaction_Status.Size = New System.Drawing.Size(231, 24)
        Me.ItemForTransaction_Status.Text = "Transaction Status"
        Me.ItemForTransaction_Status.TextSize = New System.Drawing.Size(90, 13)
        '
        'LayoutControlGroup6
        '
        Me.LayoutControlGroup6.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForChangeLog})
        Me.LayoutControlGroup6.Location = New System.Drawing.Point(0, 280)
        Me.LayoutControlGroup6.Name = "LayoutControlGroup6"
        Me.LayoutControlGroup6.Size = New System.Drawing.Size(487, 127)
        Me.LayoutControlGroup6.Text = "Change Log"
        '
        'ItemForChangeLog
        '
        Me.ItemForChangeLog.Control = Me.ChangeLogMemoEdit
        Me.ItemForChangeLog.Location = New System.Drawing.Point(0, 0)
        Me.ItemForChangeLog.Name = "ItemForChangeLog"
        Me.ItemForChangeLog.Size = New System.Drawing.Size(463, 83)
        Me.ItemForChangeLog.StartNewLine = True
        Me.ItemForChangeLog.Text = "Change Log"
        Me.ItemForChangeLog.TextLocation = DevExpress.Utils.Locations.Top
        Me.ItemForChangeLog.TextSize = New System.Drawing.Size(0, 0)
        Me.ItemForChangeLog.TextVisible = False
        '
        'LayoutControlGroup7
        '
        Me.LayoutControlGroup7.GroupBordersVisible = False
        Me.LayoutControlGroup7.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2})
        Me.LayoutControlGroup7.LayoutMode = DevExpress.XtraLayout.Utils.LayoutMode.Table
        Me.LayoutControlGroup7.Location = New System.Drawing.Point(0, 407)
        Me.LayoutControlGroup7.Name = "LayoutControlGroup7"
        ColumnDefinition5.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition5.Width = 50.0R
        ColumnDefinition6.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition6.Width = 50.0R
        Me.LayoutControlGroup7.OptionsTableLayoutGroup.ColumnDefinitions.AddRange(New DevExpress.XtraLayout.ColumnDefinition() {ColumnDefinition5, ColumnDefinition6})
        RowDefinition7.Height = 39.0R
        RowDefinition7.SizeType = System.Windows.Forms.SizeType.AutoSize
        Me.LayoutControlGroup7.OptionsTableLayoutGroup.RowDefinitions.AddRange(New DevExpress.XtraLayout.RowDefinition() {RowDefinition7})
        Me.LayoutControlGroup7.Size = New System.Drawing.Size(487, 39)
        Me.LayoutControlGroup7.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlItem1.Control = Me.btnOK
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(243, 39)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.SupportHorzAlignment
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LayoutControlItem2.Control = Me.btnCancel
        Me.LayoutControlItem2.Location = New System.Drawing.Point(243, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.OptionsTableLayoutItem.ColumnIndex = 1
        Me.LayoutControlItem2.Size = New System.Drawing.Size(244, 39)
        Me.LayoutControlItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.SupportHorzAlignment
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'frmManualBillingDraftEdit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(507, 466)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmManualBillingDraftEdit"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Edit Record"
        CType(Me.NachaTaxDraftsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.ROUTING_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCOUNT_NUMBERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCT_TYPEComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Trans_NoteTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AMOUNTTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Transaction_StatusComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_RoutingTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_AccountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Credit_ACCT_TYPEComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChangeLogMemoEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TransTypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_RequestedDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForROUTING_NUMBER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCOUNT_NUMBER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCT_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_Routing, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_Account, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCredit_ACCT_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTransType, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTrans_Note, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDate_Requested, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForAMOUNT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTransaction_Status, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForChangeLog, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents NachaTaxDraftsBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents ROUTING_NUMBERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCOUNT_NUMBERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCT_TYPEComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Trans_NoteTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents AMOUNTTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Transaction_StatusComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Credit_RoutingTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Credit_AccountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Credit_ACCT_TYPEComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ChangeLogMemoEdit As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents TransTypeTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForTrans_Note As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTransType As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForAMOUNT As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTransaction_Status As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForChangeLog As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForROUTING_NUMBER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCOUNT_NUMBER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCT_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForCredit_Routing As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_Account As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCredit_ACCT_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Date_RequestedDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup5 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForDate_Requested As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup6 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup7 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
End Class
