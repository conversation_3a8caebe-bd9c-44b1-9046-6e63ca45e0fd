﻿Imports System.ComponentModel
Imports System.Data

Namespace Ach
    Public Class frmAchAutoEmails
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property _Settings As AchSettings
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property frmLogger As Serilog.ILogger
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Property _AchEmailService As AchEmailService

        Public Sub New(settings As AchSettings)
            InitializeComponent()
            frmLogger = Logger.ForContext(Of frmAchAutoEmails)
            _Settings = settings
        End Sub

        Private Sub frmAchAutoEmails_Load(sender As Object, e As EventArgs) Handles MyBase.Load
            LoadDate()
        End Sub

        Private Sub LoadDate()
            Try
                lcRoot.ShowProgessPanel
                _AchEmailService = New AchEmailService(_Settings, True)
                GridControl1.DataSource = Query(_Settings.AutoEmailsSql)
                btnSendEmail.Enabled = False
            Catch ex As Exception
                lcRoot.HideProgressPanel
                DisplayErrorMessage("Error laoding data.", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try
        End Sub

        Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
            LoadDate()
        End Sub

        Private Sub GridView1_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles GridView1.SelectionChanged
            btnSendEmail.Enabled = GridView1.SelectedRowsCount >= 1
            btnSendEmail.Text = $"Send ({GridView1.SelectedRowsCount}) Emails"
        End Sub

        Private Async Sub btnSendEmail_Click(sender As Object, e As EventArgs) Handles btnSendEmail.Click
            Try
                lcRoot.ShowProgessPanel
                For Each row In GridView1.GetSelectedRows(Of DataRowView)
                    Try
                        Dim coNum As Decimal = row.Row("CoNum")
                        Dim emailTemplate As String = row.Row("emailTemplate")
                        Dim shouldSetCoPass As Boolean = row.Row("shouldSetCoPass")
                        Dim EntryDescType As String = row.Row("EntryDescType")
                        Await _AchEmailService.EmailClient(coNum, emailTemplate, shouldSetCoPass, EntryDescType, False)
                    Catch ex As Exception
                        DisplayErrorMessage("Error setting email", ex)
                    End Try
                Next
                lcRoot.HideProgressPanel
            Catch ex As Exception
                DisplayErrorMessage("Error sending emails.", ex)
            Finally
                lcRoot.HideProgressPanel
            End Try

        End Sub
    End Class
End Namespace