﻿Public Class PayrollUtilityImport

    Dim DB As dbEPDataDataContext
    Dim Run_prc_PrUtilityImport As String
    Dim CoNum As Decimal

    Dim whoIsCalling As String = ""

    Dim UtilitiImportGuid As Guid

    Property UnionPays As List(Of prc_PrUtilityImportResult)

    Public Sub New(CoNum As Decimal, Optional DB As dbEPDataDataContext = Nothing)
        If DB Is Nothing Then
            Me.DB = New dbEPDataDataContext(GetConnectionString)
        Else
            Me.DB = DB
        End If

        Me.CoNum = CoNum
        UtilitiImportGuid = Guid.Parse("00000000-0000-0000-0000-000000000001")
        Run_prc_PrUtilityImport = (From A In Me.DB.CoOptions_Payrolls Where A.CoNum = CoNum Select A.Run_prc_PrUtilityImport).FirstOrDefault
    End Sub

    Public Function LoadData(PrNum As Decimal) As Boolean
        Dim CoPayrollOptions = (From A In DB.CoOptions_Payrolls Where A.CoNum = CoNum).FirstOrDefault
        If CoPayrollOptions IsNot Nothing AndAlso Not String.IsNullOrEmpty(CoPayrollOptions.Run_prc_PrUtilityImport) Then
            UnionPays = DB.prc_PrUtilityImport(CoNum, PrNum, CoPayrollOptions.Run_prc_PrUtilityImport, Nothing, Nothing)
            Return UnionPays.Count > 0
        End If
        Return False
    End Function

    Public Function ProcessImport(PrNum As Decimal, Optional confirm As Boolean = False) As Boolean
        If confirm AndAlso DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you would like to run the Utility Import?", "Run Utility Import?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
            Return False
        End If
        FixReimbursableDeductions(CoNum, PrNum)

        Dim API = EP_API()
        API.CompanyID = CoNum

        'If Not UsePPxLibrary Then
        '    CType(API, PPxPayroll).CheckPayrollStatus()
        'Else
        '    API.CheckPayrollStatus(CoNum)
        'End If

        API.CheckPayrollStatus(CoNum)

        If UnionPays Is Nothing Then
            If Not LoadData(PrNum) Then
                If confirm Then DevExpress.XtraEditors.XtraMessageBox.Show("No UnionPays to Import")
                MarkUtilityDone(PrNum)
                Return False
            End If
        End If

        Dim Checks = (From A In UnionPays Group By A.EMPNUM, A.CHK_COUNTER Into Group).ToList
        For Each chk In Checks
            For Each itm In chk.Group
                Select Case itm.PDM
                    Case "P"
                        Dim drec = New CHK_DET_PAY With {.CONUM = CoNum, .EMPNUM = itm.EMPNUM, .PAYROLL_NUM = PrNum, .CHK_COUNTER = itm.CHK_COUNTER, .CL_NUM = itm.CL_NUM,
                                                        .CL_CODE = itm.CL_CODE, .CL_DEPT = itm.DEPT, .CL_RATE = itm.Rate, .CL_REG_HRS = itm.RegHours, .CL_OT_HRS = 0, .CL_REG_PAY = itm.Amount, .CL_OT_PAY = 0,
                                                        .CL_LOC_CD1 = itm.CL_LOC_CD1, .CL_LOC_CD2 = itm.CL_LOC_CD2, .CL_LOC_CD3 = itm.CL_LOC_CD3, .CL_LOC_CD4 = itm.CL_LOC_CD4, .CL_LOC_CD5 = itm.CL_LOC_CD5,
                                                        .CL_LOC_AMT1 = 0, .CL_LOC_AMT2 = 0, .CL_LOC_AMT3 = 0, .CL_LOC_AMT4 = 0, .CL_LOC_AMT5 = 0, .OR_CALC = "NO",
                                                        .LI_HRS = 0, .rowguid = Guid.NewGuid, .BATCH_LINEID = UtilitiImportGuid,
                                                        .job_id = itm.job_id, .Job2 = itm.Job2, .Job3 = itm.Job3, .Job4 = itm.Job4, .Job5 = itm.Job5,
                                                        .WRKMNS_COMP_CD = itm.WRKMNS_COMP_CD, .work_local = itm.work_local, .res_local = itm.res_local}
                        If itm.Rate.HasValue Then
                            itm.RegHours = itm.RegHours
                        End If
                        If nz(itm.DeleteExisting, False).GetValueOrDefault Then
                            Dim Existing = (From A As CHK_DET_PAY In DB.CHK_DET_PAYs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_CODE = itm.CL_CODE AndAlso A.BATCH_LINEID = UtilitiImportGuid).ToList
                            DB.CHK_DET_PAYs.DeleteAllOnSubmit(Existing)
                        End If
                        DB.CHK_DET_PAYs.InsertOnSubmit(drec)
                    Case "D"
                        Dim drec = New CHK_DET_DED With {.CONUM = CoNum, .EMPNUM = itm.EMPNUM, .PAYROLL_NUM = PrNum, .CHK_COUNTER = itm.CHK_COUNTER, .CL_NUM = itm.CL_NUM,
                                                        .CL_CODE = itm.CL_CODE, .CL_DEPT = itm.DEPT, .CL_AMOUNT = itm.Amount, .CL_OR_FLAT = itm.Amount, .CL_TYPE = "MANUAL",
                                                         .rowguid = Guid.NewGuid, .BATCH_LINEID = UtilitiImportGuid,
                                                         .job_id = itm.job_id, .job2 = itm.Job2, .job3 = itm.Job3, .job4 = itm.Job4, .job5 = itm.Job5}
                        If nz(itm.DeleteExisting, False).GetValueOrDefault Then
                            Dim Existing = (From A In DB.CHK_DET_DEDs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_CODE = itm.CL_CODE AndAlso A.BATCH_LINEID = UtilitiImportGuid).ToList
                            DB.CHK_DET_DEDs.DeleteAllOnSubmit(Existing)
                        End If
                        DB.CHK_DET_DEDs.InsertOnSubmit(drec)
                    Case "M"
                        Dim drec = New CHK_DET_MEMO With {.CONUM = CoNum, .EMPNUM = itm.EMPNUM, .PAYROLL_NUM = PrNum, .CHK_COUNTER = itm.CHK_COUNTER, .CL_NUM = itm.CL_NUM,
                                                          .CL_CODE = itm.CL_CODE, .CL_DEPT = itm.DEPT, .CL_AMOUNT = itm.Amount, .CL_OR_FLAT = itm.Amount, .CL_TYPE = "MANUAL",
                                                          .rowguid = Guid.NewGuid, .BATCH_LINEID = UtilitiImportGuid,
                                                          .job_id = itm.job_id, .job2 = itm.Job2, .job3 = itm.Job3, .job4 = itm.Job4, .job5 = itm.Job5}
                        If nz(itm.DeleteExisting, False).GetValueOrDefault Then
                            Dim Existing = (From A In DB.CHK_DET_MEMOs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_CODE = itm.CL_CODE AndAlso A.BATCH_LINEID = UtilitiImportGuid).ToList
                            DB.CHK_DET_MEMOs.DeleteAllOnSubmit(Existing)
                        End If
                        DB.CHK_DET_MEMOs.InsertOnSubmit(drec)
                End Select
            Next
            DB.SaveChanges()

            'If Not UsePPxLibrary Then
            '    Dim ppx = CType(API, PPxPayroll)
            '    ppx.PayrollAPI.CalculateSingleCheck(API.ProviderID, API.CompanyID, API.PayrollID, chk.EMPNUM, chk.CHK_COUNTER, whoIsCalling, -1)
            'Else
            '    API.CalculateSingleCheck(CoNum, PrNum, chk.EMPNUM, chk.CHK_COUNTER)
            'End If

            API.CalculateSingleCheck(CoNum, PrNum, chk.EMPNUM, chk.CHK_COUNTER)

            Dim ChkRecord = (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = chk.EMPNUM AndAlso A.CHK_COUNTER = chk.CHK_COUNTER).FirstOrDefault
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, ChkRecord)
        Next

        MarkUtilityDone(PrNum)
        If confirm Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Utility Import Completed.")
        End If

        Return True
    End Function

    Private Sub MarkUtilityDone(PrNum As Decimal)
        Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = PrNum AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
        PayRec.UtilityImportProcessed = True
        DB.SaveChanges()
    End Sub

    Public Function ProcessImportManualCheck(ManCheck As MAN_CHK_MAST, Optional confirm As Boolean = False) As Boolean
        If confirm AndAlso DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you would like to run the Utility Import?", "Run Utility Import?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
            Return False
        End If

        Dim API = EP_API()
        API.CompanyID = CoNum

        'If Not UsePPxLibrary Then
        '    CType(API, PPxPayroll).CheckPayrollStatus()
        'Else
        '    API.CheckPayrollStatus(CoNum)
        'End If

        API.CheckPayrollStatus(CoNum)

        Dim Checks = DB.prc_PrUtilityImport(CoNum, -1, Run_prc_PrUtilityImport, ManCheck.EMPNUM, ManCheck.CHK_COUNTER).ToList
        If Checks.Count = 0 Then Return True

        For Each itm As prc_PrUtilityImportResult In Checks
            Select Case itm.PDM
                Case "P"
                    Dim drec = New MAN_CHK_DET_PAY With {.CONUM = CoNum, .EMPNUM = itm.EMPNUM, .PAYROLL_NUM = -1, .CHK_COUNTER = itm.CHK_COUNTER, .CL_NUM = itm.CL_NUM,
                                                    .CL_CODE = itm.CL_CODE, .CL_DEPT = itm.DEPT, .CL_RATE = itm.Rate, .CL_REG_HRS = itm.RegHours, .CL_OT_HRS = 0, .CL_REG_PAY = itm.Amount, .CL_OT_PAY = 0,
                                                    .CL_LOC_CD1 = itm.CL_LOC_CD1, .CL_LOC_CD2 = itm.CL_LOC_CD2, .CL_LOC_CD3 = itm.CL_LOC_CD3, .CL_LOC_CD4 = itm.CL_LOC_CD4, .CL_LOC_CD5 = itm.CL_LOC_CD5,
                                                    .CL_LOC_AMT1 = 0, .CL_LOC_AMT2 = 0, .CL_LOC_AMT3 = 0, .CL_LOC_AMT4 = 0, .CL_LOC_AMT5 = 0, .OR_CALC = "NO",
                                                    .LI_HRS = 0, .rowguid = Guid.NewGuid,
                                                    .job_id = itm.job_id, .job2 = itm.Job2, .job3 = itm.Job3, .job4 = itm.Job4, .job5 = itm.Job5,
                                                    .WRKMNS_COMP_CD = itm.WRKMNS_COMP_CD, .work_local = itm.work_local, .res_local = itm.res_local}
                    If itm.Rate.HasValue Then
                        itm.RegHours = itm.RegHours
                    End If
                    If nz(itm.DeleteExisting, False).GetValueOrDefault Then
                        Dim Existing = (From A In DB.MAN_CHK_DET_PAYs Where A.CONUM = CoNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_CODE = itm.CL_CODE).ToList
                        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Existing)
                        DB.MAN_CHK_DET_PAYs.DeleteAllOnSubmit(Existing)
                    End If
                    DB.MAN_CHK_DET_PAYs.InsertOnSubmit(drec)
                Case "D"
                    Dim drec = New MAN_CHK_DET_DED With {.CONUM = CoNum, .EMPNUM = itm.EMPNUM, .PAYROLL_NUM = -1, .CHK_COUNTER = itm.CHK_COUNTER, .CL_NUM = itm.CL_NUM,
                                                    .CL_CODE = itm.CL_CODE, .CL_DEPT = itm.DEPT, .CL_AMOUNT = itm.Amount, .CL_OR_FLAT = itm.Amount, .CL_TYPE = "MANUAL", .rowguid = Guid.NewGuid}
                    If nz(itm.DeleteExisting, False).GetValueOrDefault Then
                        Dim Existing = (From A In DB.MAN_CHK_DET_DEDs Where A.CONUM = CoNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_CODE = itm.CL_CODE).ToList
                        DB.MAN_CHK_DET_DEDs.DeleteAllOnSubmit(Existing)
                        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Existing)
                    End If
                    DB.MAN_CHK_DET_DEDs.InsertOnSubmit(drec)
                Case "M"
                    Dim drec = New MAN_CHK_DET_MEMO With {.CONUM = CoNum, .EMPNUM = itm.EMPNUM, .PAYROLL_NUM = -1, .CHK_COUNTER = itm.CHK_COUNTER, .CL_NUM = itm.CL_NUM,
                                                    .CL_CODE = itm.CL_CODE, .CL_DEPT = itm.DEPT, .CL_AMOUNT = itm.Amount, .CL_OR_FLAT = itm.Amount, .CL_TYPE = "MANUAL", .rowguid = Guid.NewGuid}
                    If nz(itm.DeleteExisting, False).GetValueOrDefault Then
                        Dim Existing = (From A In DB.MAN_CHK_DET_MEMOs Where A.CONUM = CoNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_CODE = itm.CL_CODE).ToList
                        DB.MAN_CHK_DET_MEMOs.DeleteAllOnSubmit(Existing)
                        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Existing)
                    End If
                    DB.MAN_CHK_DET_MEMOs.InsertOnSubmit(drec)
            End Select
        Next
        DB.SubmitChanges()

        'Dim ChkRecord = (From A In DB.MAN_CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = EmpNum AndAlso A.CHK_COUNTER = ChkCounter Select A.check_date, A.PAY_FREQ).Single
        Dim errors As String = Nothing

        Dim results As Boolean
        'If Not UsePPxLibrary Then
        '    Dim ppx = CType(API, PPxPayroll)
        '    results = ppx.PayrollAPI.CalculateManualCheck(API.ProviderID, API.CompanyID, ManCheck.EMPNUM, ManCheck.CHK_COUNTER, ManCheck.PAY_FREQ, ManCheck.check_date.Value, -1, UserName, errors)
        'Else
        '    results = API.CalculateManualCheck(ManCheck.CONUM, ManCheck.EMPNUM, ManCheck.CHK_COUNTER, ManCheck.PAY_FREQ, ManCheck.check_date.Value, errors)
        'End If

        results = API.CalculateManualCheck(ManCheck.CONUM, ManCheck.EMPNUM, ManCheck.CHK_COUNTER, ManCheck.PAY_FREQ, ManCheck.check_date.Value, errors)

        If Not results Then
            If String.IsNullOrEmpty(errors) Then errors = "Unkonown results"
            Throw New Exception("Calculate manual check failed: " & errors)
        End If
        'Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = PrNum AndAlso A.IsDeleted.GetValueOrDefault = False).FirstOrDefault
        'PayRec.UtilityImportProcessed = True
        'DB.SubmitChanges()
        Return True
    End Function

    Public Sub FixReimbursableDeductions(coNum As Decimal, PrNum As Decimal)
        DB.ExecuteCommand(String.Format("UPDATE dbo.CHK_DET_DED SET CL_OR_FLAT = CL_AMOUNT WHERE coNum = {0} And PAYROLL_NUM = {1} AND CL_AMOUNT <> CL_OR_FLAT AND ABS(CL_AMOUNT) = CL_OR_FLAT ", coNum, PrNum))
    End Sub

End Class
