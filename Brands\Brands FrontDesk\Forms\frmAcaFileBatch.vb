﻿Imports DevExpress.Utils
Imports DevExpress.XtraEditors

Public Class frmAcaFileBatch
    Private DB As dbEPDataDataContext
    Private DateEditCreated As New Repository.RepositoryItemDateEdit
    Private DateEditTransmitted As New Repository.RepositoryItemDateEdit

    Private Sub frmAcaFileBatch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            teYear.EditValue = If(Today.Month = 12, Today.Year, Today.Year - 1)
            LoadData(teYear.EditValue)
        Catch ex As Exception
            DisplayErrorMessage("Error in load", ex)
        End Try
    End Sub

    Private Sub LoadData(Year As Int16)
        Try
            DB = New dbEPDataDataContext(GetConnectionString())
            GridControlFileBatch.DataSource = DB.ACA_File_Batch_Ts.Where(Function(b) b.File_Year = Year).ToList()
            GridViewFileBatch.BestFitColumns()

            Dim col As DevExpress.XtraGrid.Columns.GridColumn
            For Each col In GridViewFileBatch.Columns
                If col.Name = "colDate_Created" Then
                    col.DisplayFormat.FormatType = FormatType.DateTime
                    col.DisplayFormat.FormatString = "g"
                    col.ColumnEdit = DateEditCreated
                    DateEditCreated.Mask.EditMask = "g"
                ElseIf col.Name = "colDate_Transmitted" Then
                    col.DisplayFormat.FormatType = FormatType.DateTime
                    col.DisplayFormat.FormatString = "g"
                    col.ColumnEdit = DateEditTransmitted
                    DateEditTransmitted.Mask.EditMask = "g"
                End If
            Next
            ';GridViewFileBatch.Columns().Where()
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        End Try
    End Sub

    Private Sub LoadDetailData(BatchID As Int16)
        Try
            GridControlFileBatchCompany.DataSource = DB.ACA_File_Batch_Company_Ts.Where(Function(b) b.Batch_ID = BatchID).ToList()
            Dim column As DevExpress.XtraGrid.Columns.GridColumn
            For Each column In GridViewFileBatchCompany.Columns
                If "ACA_File_Batch_T,".ToLower().Split(New Char() {","}).ToList().IndexOf(column.FieldName.ToLower()) >= 0 Then
                    column.Visible = False
                End If
            Next

            GridViewFileBatchCompany.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        End Try
    End Sub

    Private Sub teYear_Validated(sender As Object, e As EventArgs) Handles teYear.Validated
        Try
            If teYear.EditValue IsNot Nothing Then
                LoadData(teYear.EditValue)
            Else
                GridViewFileBatch.Columns.Clear()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in year validated", ex)
        End Try
    End Sub

    Private Sub teYear_KeyUp(sender As Object, e As KeyEventArgs) Handles teYear.KeyUp
        Try
            If e.KeyCode = Keys.Enter Then
                If teYear.EditValue IsNot Nothing Then
                    LoadData(teYear.EditValue)
                Else
                    GridViewFileBatch.Columns.Clear()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in year keyup", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            DB.SaveChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error button save", ex)
        End Try
    End Sub

    Private Sub GridViewFileBatch_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewFileBatch.PopupMenuShowing
        Dim row As ACA_File_Batch_T = GridViewFileBatch.GetFocusedRow
        If row Is Nothing Then Return

        If e.Allow AndAlso e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
            Dim mnuSubItemWhileUploading As New Menu.DXSubMenuItem("(While Uploading to IRS)")
            mnuSubItemWhileUploading.Items.Add(New Menu.DXMenuItem("Add File Upload Confirmation to log", Sub() OpenScript(row.Batch_ID, "ACA File Batch File Transmitted Status Update")))
            e.Menu.Items.Add(mnuSubItemWhileUploading)

            Dim mnuSubItemWhileChecking As New Menu.DXSubMenuItem("(While checking IRS File Status)")
            mnuSubItemWhileChecking.Items.Add(New Menu.DXMenuItem("Add File Status to log", Sub() OpenScript(row.Batch_ID, "ACA File Batch Table File Status Update")))
            e.Menu.Items.Add(mnuSubItemWhileChecking)

            Dim mnuSubItemCompanyLog As New Menu.DXSubMenuItem("Company Log")
            mnuSubItemCompanyLog.Items.Add(New Menu.DXMenuItem("Insert Companies to log (By Range)", Sub() OpenScript(row.Batch_ID, "ACA File Company Control")))
            mnuSubItemCompanyLog.Items.Add(New Menu.DXMenuItem("Insert Companies to log (By List)", Sub() OpenScript(row.Batch_ID, "Special ACA File Company Control")))
            mnuSubItemCompanyLog.Items.Add(New Menu.DXMenuItem("Update Company With Actual Submission ID", Sub() OpenScript(row.Batch_ID, "Update Company log with Actual Submission ID")))
            mnuSubItemCompanyLog.Items.Add(New Menu.DXMenuItem("After saving ACK data to database, Update Company with IRS status", Sub() OpenScript(row.Batch_ID, "Update Company log with IRS status")))
            e.Menu.Items.Add(mnuSubItemCompanyLog)

            Dim mnuSubItemCompanyExceptions As New Menu.DXSubMenuItem("Company Exceptions")
            mnuSubItemCompanyExceptions.Items.Add(New Menu.DXMenuItem("ACA Clients Still To Be filed With The IRS", Sub() OpenScript(row.Batch_ID, "ACA Clients Still To Be filed With The IRS")))
            mnuSubItemCompanyExceptions.Items.Add(New Menu.DXMenuItem("Update Failed Company Status", Sub() OpenScript(row.Batch_ID, "Update Client Status")))
            e.Menu.Items.Add(mnuSubItemCompanyExceptions)
        End If
    End Sub

    Private Async Sub OpenScript(BatchID As Int16, Action As String, Optional BatchCompanyID As Int16 = Nothing)
        Try
            Dim row As ACA_File_Batch_T = GridViewFileBatch.GetFocusedRow
            If row Is Nothing Then Return

            Dim rpt = DB.ExecuteQuery(Of AcaReportActionResult)($"EXEC custom.prc_FDCoNumMultiReport @Action='{Action}', @CoNum = NULL, @Option1 = {BatchID}, @Option2 = {If(BatchCompanyID = Nothing, Nothing, BatchCompanyID)}").FirstOrDefault()
            Dim script As SqlScript = Query(Of SqlScript)($"SELECT TOP 1 * FROM custom.SqlScripts WHERE ID = {rpt.SqlScriptID}").FirstOrDefault()

            If Action = "ACA File Company Control" OrElse Action = "AnalyzeSubmissionResultForCompany" Then
                script.Parameters = rpt.Parm
            ElseIf Action = "Update Company with IRS status" OrElse Action = "ACA Clients Still To Be filed With The IRS" Then
                script.Parameters = "@Year=" + teYear.EditValue.ToString()
            Else
                script.ParametersJson = rpt.Parm
            End If

            modGlobals.UpdateSql($"Update custom.SqlScripts SET Parameters = '{script.Parameters}', ParametersJson = '{script.ParametersJson}' WHERE ID = {rpt.SqlScriptID}")

            Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
            If sqlFrm IsNot Nothing Then
                frmMain.CloseForm(sqlFrm)
            End If
            frmMain.AddOrActivateForm(Of frmSqlScripts)()
            sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
            sqlFrm.FindAndFocusRecord(rpt.SqlScriptID)
            Await sqlFrm.ExecuteClick(rpt.SqlScriptID)
        Catch ex As Exception
            DisplayErrorMessage("Error in OpenScript", ex)
        End Try
    End Sub

    Private Sub GridViewFileBatch_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles GridViewFileBatch.FocusedRowChanged
        Try
            Dim row As ACA_File_Batch_T = GridViewFileBatch.GetFocusedRow
            If row Is Nothing Then
                GridControlFileBatchCompany.DataSource = Nothing
                Return
            End If

            LoadDetailData(row.Batch_ID)
        Catch ex As Exception
            DisplayErrorMessage("Error in GridViewFileBatch row changed", ex)
        End Try
    End Sub

    Private Sub GridViewFileBatch_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridViewFileBatch.ValidateRow
        Try
            Dim row As ACA_File_Batch_T = GridViewFileBatch.GetFocusedRow
            If row.File_Year = Nothing OrElse row.File_Year < 2000 Then
                e.Valid = False
                e.ErrorText = "Please enter File Year"
            ElseIf row.File_Name Is Nothing OrElse row.File_Name = "" Then
                e.Valid = False
                e.ErrorText = "Please enter File_Name"
            ElseIf row.Manifest_Name Is Nothing OrElse row.Manifest_Name = "" Then
                e.Valid = False
                e.ErrorText = "Please enter Manifest_Name"
            ElseIf row.Range_Start Is Nothing OrElse row.Range_Start < 1 Then
                e.Valid = False
                e.ErrorText = "Please enter Range Start"
            ElseIf row.Range_End Is Nothing OrElse row.Range_End < 1 Then
                e.Valid = False
                e.ErrorText = "Please enter Range End"
            ElseIf row.Date_Created = Nothing OrElse row.Date_Created.Year < 2000 Then
                row.Date_Created = DateTime.Now()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in validated row", ex)
        End Try
    End Sub

    Private Sub GridControlFileBatchCompany_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles GridControlFileBatchCompany.Validating
        'Dim row As ACA_File_Batch_T = GridViewFileBatch.GetFocusedRow
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Try
            LoadData(teYear.EditValue)
            Dim row As ACA_File_Batch_T = GridViewFileBatch.GetFocusedRow
            If Not row Is Nothing AndAlso row.Batch_ID > 0 Then
                LoadDetailData(row.Batch_ID)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in refresh button", ex)
        End Try
    End Sub

    Private Class AcaReportActionResult
        Public Property Parm As String
        Public Property SqlScriptID As Int32
    End Class

    Private Async Sub hlcInsertFilesCreatedToLog_Click(sender As Object, e As EventArgs) Handles hlcInsertFilesCreatedToLog.Click
        Dim SqlScriptID As Int16 = 563
        Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
        If sqlFrm IsNot Nothing Then
            frmMain.CloseForm(sqlFrm)
        End If
        frmMain.AddOrActivateForm(Of frmSqlScripts)()
        sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
        sqlFrm.FindAndFocusRecord(SqlScriptID)
        Await sqlFrm.ExecuteClick(SqlScriptID)
    End Sub

    Private Sub GridViewFileBatchCompany_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewFileBatchCompany.PopupMenuShowing
        Dim row As ACA_File_Batch_Company_T = GridViewFileBatchCompany.GetFocusedRow
        If row Is Nothing Then Return

        If e.Allow AndAlso e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
            e.Menu.Items.Add(New Menu.DXMenuItem("Analyze Submission Result", Sub() OpenScript(row.Batch_ID, "AnalyzeSubmissionResultForCompany", row.ID)))
        End If
    End Sub
End Class