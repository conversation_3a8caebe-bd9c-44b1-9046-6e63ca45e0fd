﻿Imports System.Data.SqlClient
Imports Brands_FrontDesk.RabbitRpc
'Imports ClassLibraryEP
'Imports Execupay

Public Interface PPxPayrollInterface
    Property CalendarID As Int32
    Property CompanyID As Decimal
    Property PayrollCalendar As PPxPayrollCalendar
    Property PayrollException As Exception
    Property PayrollID As Decimal
    Property PayrollStatus As String
    Property PowerGridBatchID As Guid
    Property ppxPayrollEntryType As String
    Property PPxPayrollStep As PPxPayrollSteps
    Property ProviderID As Decimal

    Sub CheckPayrollStatus()
    Sub DeleteManualCheck(CoNum As Decimal, PrNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal)
    Function CalculateManualCheck(CoNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal, pay_freq As String, check_date As DateTime, errors As String) As Boolean
    Function CalculateSingleCheck(ByVal CoNum As Decimal, ByVal PrNum As Decimal, ByVal EmpNum As Decimal?, ByVal ChkCntr As Decimal?) As Boolean
    Function CalculateTotals() As Boolean
    Function CalculateTotals(CoNum As Decimal, PrNum As Decimal) As Boolean
    Function CheckPayrollStatus(ByVal CoNum As Decimal) As PayrollStatusResult
    Function CreateNewPayrollFromCalendar() As Boolean
    Function CreateNewPayrollFromCalendar(CoNum As Decimal) As Boolean
    Function GetSinglePayrollInfo(CoNum As Decimal, PrNum As Decimal) As PayrollInfo
    Function ProcessPowerGrid(CoNum As Decimal, PrNum As Decimal, PowerGridID As String) As Boolean
    Function SubmitPayroll(CoNum As Decimal, PrNum As Decimal, ByVal submitType As Short, PayrollNotes As String) As Boolean
    Function SubmitPayroll(ByVal submitType As Short) As Boolean
    Function UndoPayroll(CoNum As Decimal, PrNum As Decimal, deletePayrollGrid As Boolean)
    Function UndoPayroll(CoNum As Decimal, PrNum As Decimal, deletePayrollGrid As Boolean, PowerGridBatchID As Guid)
End Interface

Public Class PPxPayrollRabbit
    Implements PPxPayrollInterface
    Private _CurrentUserName As String
    Public Property CalendarID As Int32 Implements PPxPayrollInterface.CalendarID
    Public Property CompanyID As Decimal Implements PPxPayrollInterface.CompanyID
    Public Property PayrollCalendar As PPxPayrollCalendar Implements PPxPayrollInterface.PayrollCalendar
    Public Property PayrollID As Decimal Implements PPxPayrollInterface.PayrollID
    Public Property PayrollStatus As String Implements PPxPayrollInterface.PayrollStatus
    Public Property PowerGridBatchID As Guid Implements PPxPayrollInterface.PowerGridBatchID
    Public Property ppxPayrollEntryType As String = "BrandsXpress" Implements PPxPayrollInterface.ppxPayrollEntryType
    Public Property PPxPayrollStatus As String
    Public Property PPxPayrollStep As PPxPayrollSteps Implements PPxPayrollInterface.PPxPayrollStep
    Public Property pr_batch_list As List(Of pr_batch_msg_classs)
    Public Property ProviderID As Decimal Implements PPxPayrollInterface.ProviderID
    'Public Property EmployeeID As Decimal
    'Public Property LastModified As DateTime

    Public Property PayrollException() As Exception Implements PPxPayrollInterface.PayrollException
        Get
            If _PayrollException Is Nothing Then
                _PayrollException = New Exception()
            End If
            Return _PayrollException
        End Get
        Set(value As Exception)
            _PayrollException = value
        End Set
    End Property

    Private Shared _Instance As PPxPayrollRabbit
    Private _PayrollException As Exception

    Public Sub New(UserName As String)
        _CurrentUserName = UserName
        _PPxPayrollStatus = String.Empty
        PPxPayrollStep = PPxPayrollSteps.None
        PayrollStatus = String.Empty
    End Sub

    Public Overloads Shared Property Instance(UserName As String) As PPxPayrollRabbit
        Get
            If _Instance Is Nothing Then
                _Instance = New PPxPayrollRabbit(UserName)
            End If

            If Not _Instance._CurrentUserName = UserName Then
                _Instance._CurrentUserName = UserName
            End If

            Return _Instance

        End Get
        Set(value As PPxPayrollRabbit)
            _Instance = value
            _Instance._CurrentUserName = UserName
        End Set
    End Property

    Function CalculateManualCheck(CoNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal, pay_freq As String, check_date As DateTime, errors As String) As Boolean Implements PPxPayrollInterface.CalculateManualCheck
        Dim rpcCls As New RpcClasses()
        Return rpcCls.CalculateManualCheck(CoNum, EmpNum, ChkCounter, pay_freq, check_date, errors)
    End Function

    Function CalculateSingleCheck(ByVal CoNum As Decimal, ByVal PrNum As Decimal, ByVal EmpNum As Decimal?, ByVal ChkCntr As Decimal?) As Boolean Implements PPxPayrollInterface.CalculateSingleCheck
        Dim rpcCls As New RpcClasses()
        Return rpcCls.CalculateSingleCheck(CoNum, PrNum, EmpNum, ChkCntr)
    End Function

    Public Function CalculateTotals(ByVal CoNum As Decimal, ByVal PrNum As Decimal) As Boolean Implements PPxPayrollInterface.CalculateTotals
        Dim rpcCls As New RpcClasses()

        Dim RetryCount As Integer = 1
        Try
            WebLogger.PayrollProcessStep = 3
            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.CalculatingTotals + " :: PPxPayroll.CalculatePayrollTotals(" + PowerGridBatchID.ToString + ")", _CurrentUserName)


            If True Then
                Me.PPxPayrollStatus = PPxPayrollStatusType.CalculatingTotals
            End If
            ' Powergrid Processing was Complete Successfully. display return results in a popup and provide a linq to view Totals in next page
            ' show how many checks are created and other information in that popup.
A:
            Dim result = rpcCls.CalculateTotals(CoNum, PrNum)
            If result.Success Then
                'Me.PayrollTotal = payrollTotal
                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, result.LogInfo1, _CurrentUserName)
                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, result.LogInfo2)
                'lock (this.PPxPayrollStatus) 
                If True Then
                    Me.PPxPayrollStatus = PPxPayrollStatusType.TotalsCalculated
                End If
            Else
                WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], result.exception.Message, _CurrentUserName)
                Throw New Exception("Error while calculating payroll totals")
            End If
            Return True
        Catch ex As Exception
            Logger.Error(ex, "Error in CalculateTotals API {CoNum} {Prnum}", CoNum, PrNum)
            If ex.Message.ToLower.Contains("object reference not set to an instance of an object") Then
                If RetryCount < 3 Then
                    RetryCount += 1
                    GoTo A
                End If
            End If
            'GC.Collect(); // force cleanup memory
            GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced)
            ' this will cleanup any Open connections/cursors by PPJ.Runtime
            Me.PayrollException = ex
            WebLogger.GetInstance.Log(WebLogger.LogLevel.[Error], ex.Message, ex)
            'Throw ex
            Return False
        End Try
    End Function

    Sub CheckPayrollStatus() Implements PPxPayrollInterface.CheckPayrollStatus
        CheckPayrollStatus(CompanyID)
    End Sub

    Public Function CheckPayrollStatus(ByVal CoNum As Decimal) As PayrollStatusResult Implements PPxPayrollInterface.CheckPayrollStatus
        Dim rpcCls As New RpcClasses()

        Dim ret = rpcCls.CheckPayrollStatus(CoNum)

        Me.PowerGridBatchID = ret.PowerGridBatchID
        Me.PayrollID = ret.PayrollID
        Me.PayrollStatus = ret.PayrollStatus
        Me.PPxPayrollStatus = ret.PPxPayrollStatus
        Me.PPxPayrollStep = ret.PPxPayrollStep
        Return ret
    End Function

    Public Function CreateNewPayrollFromCalendar(CoNum As Decimal) As Boolean Implements PPxPayrollInterface.CreateNewPayrollFromCalendar
        Dim currentStatus As String = String.Empty
        Dim canCreate As Boolean = False
        Dim canEdit As Boolean = False
        Dim errorID As Double = 0
        Dim errorText As String = Nothing
        Dim payrollPassword As String = Nothing
        ' in case if payroll password is setup at company level
        Try
            Dim payrollStatus = CheckPayrollStatus(CoNum)
            Dim payrollNumber As Double = payrollStatus.PayrollID

            If payrollStatus.CanCreate Then
                ' status of last payroll
                Me.PayrollStatus = currentStatus
                Me.PayrollID = CDec(payrollNumber)

                Me.PPxPayrollStep = PPxPayrollSteps.Start
                ' PPx Payroll Status == Start
                Me.PPxPayrollStatus = PPxPayrollStatusType.StartingPayroll
                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.StartingPayroll & " :: PPxPayroll.CreateNewPayrollFromCalendar()", _CurrentUserName)

                errorText = Nothing
                Dim rpcCls As New RpcClasses()
                payrollNumber = rpcCls.CreatePayrollWithCalendar(CoNum, CalendarID, payrollPassword)

                If CDec(payrollNumber) > Me.PayrollID Then
                    ' only if new payroll number is greater than the previous
                    Me.PayrollID = CDec(payrollNumber)
                    ' assign the new payroll number
                    Me.PayrollStatus = currentStatus
                    ' actual status on Payroll Record in database (shld be [Entering Checks])
                    Me.PPxPayrollStep = PPxPayrollSteps.Edit

                    Me.PPxPayrollStatus = PPxPayrollStatusType.PayrollCreated
                    WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.PayrollCreated & " :: PPxPayroll.CreateNewPayrollFromCalendar(Payroll#" & payrollNumber.ToString & ")", _CurrentUserName)

                    'CreateNewPowerGridForUser()
                Else
                    Me.PayrollID = CDec(payrollNumber)
                    ' assign the new payroll number
                    Me.PayrollStatus = currentStatus
                    ' actual status on Payroll Record in database (shld be [Entering Checks])
                    ' in case error Occurred or payroll was not create for some reason (payroll API returns same PayrollID)
                    Throw New PayrollException("Error Occurred while Creating Payroll(# " & payrollNumber.ToString & "). " & errorText)

                End If
            ElseIf payrollStatus.CanEdit AndAlso payrollNumber > 0 Then
                ' Logically in PPx this else condition should not be triggered. may be someone else created Payroll at the same time current user did.
                Throw New PayrollException("Unhandled Exception Occurred while creating Poyroll(# " & payrollNumber.ToString & ", User:" & Me._CurrentUserName & "). ErrorText: " & errorText & "{" & errorID & "}")
            End If
            Return True
        Catch ex As Exception
            Logger.Error(ex, "Error Occurred in CreateNewPayrollFromCalendar while Creating Payroll for Co# {CoNum} ", Me.CompanyID.ToString)
            Me.PayrollException = ex
            'throw ex; //new Exception("Error Occurred while Creating Payroll!", ex);
            WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "Error Occurred while Creating Payroll for Company # " & Me.CompanyID.ToString, ex)
            Return False
        End Try
    End Function

    Sub DeleteManualCheck(CoNum As Decimal, PrNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal) Implements PPxPayrollInterface.DeleteManualCheck
        Dim rpcCls As New RpcClasses()
        rpcCls.DeleteManualCheck(CoNum, PrNum, EmpNum, ChkCounter)
    End Sub


    Function GetSinglePayrollInfo(ByVal CoNum As Decimal, ByVal PrNum As Decimal) As Brands_FrontDesk.RabbitRpc.PayrollInfo Implements PPxPayrollInterface.GetSinglePayrollInfo
        Dim rpcCls As New RpcClasses()
        Dim ret = rpcCls.GetSinglePayrollInfo(CoNum, PrNum)

        Return ret
    End Function

    Public Function ProcessPowerGrid(ByVal CoNum As Decimal, ByVal PrNum As Decimal, PowerGridBatchID As String) As Boolean Implements PPxPayrollInterface.ProcessPowerGrid
        Try
            WebLogger.PayrollProcessStep = 1
            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, PPxPayrollStatusType.ProcessingPowerGrid & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)

            'lock (this.PPxPayrollStatus) 
            If True Then
                Me.PPxPayrollStatus = PPxPayrollStatusType.ProcessingPowerGrid
            End If

            Dim rpcCls As New RpcClasses()
            Dim res = rpcCls.ProcessPowerGrid(CoNum, PrNum, PowerGridBatchID)
            Me.pr_batch_list = res.pr_batches
            ' Processing PowerGrid and Calculating Checks
            Dim resultCollection As ProcessPowerGridResult = rpcCls.ProcessPowerGrid(CoNum, PrNum, PowerGridBatchID)
            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PowerGrid Processing Done :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)

            WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "PowerGrid Processing Results " & resultCollection.Status.ToString & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)

            If resultCollection.Success Then
                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "Checks Completed " & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)
                'lock (this.PPxPayrollStatus) 
                If True Then
                    Me.PPxPayrollStatus = PPxPayrollStatusType.ChecksCompleted
                End If
            Else
                WebLogger.GetInstance.Log(WebLogger.LogLevel.Info, "Error :: " & PPxPayrollStatusType.ProcessingPowerGrid & " :: PPxPayroll.ProcessPowerGrid(" & PowerGridBatchID.ToString & ")", _CurrentUserName)
                ' Powergrid processing caused errors. display all errors in a grid
                If resultCollection.exception IsNot Nothing Then
                    Throw resultCollection.exception
                End If
            End If
            Return True
        Catch ex As Exception
            Me.PayrollException = ex
            WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, ex.Message, ex)

            If ex.InnerException IsNot Nothing And TypeOf (ex.InnerException) Is DivideByZeroException Then
                DisplayErrorMessage(ex.InnerException.Message, ex)
                'you can add here code how to handle if it was a divide by zero exception 
            End If

            If ex.InnerException IsNot Nothing And TypeOf (ex.InnerException) Is SqlException Then
                Dim sqlEx As SqlException = CType(ex, SqlException)

                If sqlEx.ErrorCode = 1205 Then

                    Dim msg = "There was a dead lock error," + vbCrLf + " Error is:" + ex.Message + vbCrLf + "Before retrying report to Hershy, Would you like to retry ?"
                    If MessageBox.Show(msg, "Dead Lock Error", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.Yes Then
                        ProcessPowerGrid(CoNum, PrNum, PowerGridBatchID)
                    End If

                End If

                MessageBox.Show(ex.InnerException.Message)
                'you can add here code how to handle if it was a divide by ziro exception 
            End If

            Return False
        Finally
            'LastModified = DateTime.Now
        End Try
    End Function

    Public Function SubmitPayroll(ByVal CoNum As Decimal, ByVal PrNum As Decimal, ByVal submitType As Short, payrollNotes As String) As Boolean Implements PPxPayrollInterface.SubmitPayroll
        Dim rpcCls As New RpcClasses()

        ' Submit Type
        '  0 - Phone Call
        '  1 - Instructions
        '  2 - Submit Now (to Print Run)
        If Me.PPxPayrollStep = PPxPayrollSteps.Complete Then
            Return rpcCls.SubmitPayroll(CoNum, PrNum, payrollNotes, submitType)
        Else
            WebLogger.GetInstance.Log(WebLogger.LogLevel.Fatal, "Error :: PayrollAPI.SubmitPayrollForReview(Pr: " + Me.PayrollID.ToString + ", Status: " + Me.PayrollStatus + ", PPxStatus:" + Me.PPxPayrollStatus + ")", _CurrentUserName)
            Throw New PayrollException("Payroll cannot be submitted. Check Payroll status or contact a Payroll specialist")
        End If
    End Function

    Public Function UndoPayroll(ByVal CoNum As Decimal, ByVal PrNum As Decimal, deletePowerGrid As Boolean, PowerGridBatchID As Guid) Implements PPxPayrollInterface.UndoPayroll
        Dim rpcCls As New RpcClasses()
        Return rpcCls.UndoPayroll(CoNum, PrNum, deletePowerGrid, PowerGridBatchID)
    End Function

#Region "methods back reference"
    Public Function CalculateTotals() As Boolean Implements PPxPayrollInterface.CalculateTotals
        Return CalculateTotals(CompanyID, PayrollID)
    End Function

    Public Function CreateNewPayrollFromCalendar() As Boolean Implements PPxPayrollInterface.CreateNewPayrollFromCalendar
        Return CreateNewPayrollFromCalendar(CompanyID)
    End Function

    Public Function SubmitPayroll(submitType As Short) As Boolean Implements PPxPayrollInterface.SubmitPayroll
        Return SubmitPayroll(CompanyID, PayrollID, submitType, "")
    End Function

    Public Function UndoPayroll(ByVal CoNum As Decimal, ByVal PrNum As Decimal, deletePowerGrid As Boolean) Implements PPxPayrollInterface.UndoPayroll
        Throw New NotImplementedException
    End Function
#End Region

End Class
