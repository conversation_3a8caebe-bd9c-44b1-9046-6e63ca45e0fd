﻿Public Class frmTaxNoticeAiJson
    Private db As dbEPDataDataContext

    Private Async Function LoadData() As Threading.Tasks.Task
        Try
            SplashScreenManager1.ShowWaitForm()

            If db Is Nothing Then
                db = New dbEPDataDataContext(GetConnectionString())
            End If

            FilePreviewUserControl1.Clear()
            GVData.Columns.Clear()
            GCData.DataSource = Nothing

            Dim ds = Await QueryAsync("SELECT * FROM custom.AI_FormRecognitionJson afrj", 30)
            GCData.DataSource = ds
            GVData.BestFitColumns()

            GC.Collect()
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        Finally
            SplashScreenManager1.CloseWaitForm()
        End Try
    End Function

    Public Sub FindAndFocusRecord(ID As Integer, Optional Filter As Boolean = True)
        If Filter Then
            GVData.ActiveFilterString = "[ID] = " + ID.ToString()
        Else
            GVData.ActiveFilterString = ""
        End If

        Dim rowHandle = GVData.LocateByValue(1, GVData.Columns("ID"), ID)
        If rowHandle <> DevExpress.XtraGrid.GridControl.InvalidRowHandle Then
            GVData.FocusedRowHandle = rowHandle
        End If
    End Sub

    Private Sub HyperlinkLabelControlAnalyzeJson_HyperlinkClick(sender As Object, e As DevExpress.Utils.HyperlinkClickEventArgs) Handles HyperlinkLabelControlAnalyzeJson.HyperlinkClick
        Try
            'System.Diagnostics.Process.Start(e.Text)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = e.Text
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error in HyperlinkLabelControl1_HyperlinkClick", ex)
        End Try
    End Sub

    Private Sub HyperlinkLabelControlCopyJson_Click(sender As Object, e As EventArgs) Handles HyperlinkLabelControlCopyJson.Click
        Try
            Clipboard.SetText(MemoEdit1.EditValue.ToString())
        Catch ex As Exception
            DisplayErrorMessage("Error in HyperlinkLabelControlCopyJson_Click", ex)
        End Try
    End Sub

    Private Sub GVData_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles GVData.FocusedRowChanged
        Try
            GCKeyValue.DataSource = Query($"EXEC custom.prc_RptAIFormRecognitionJsonKeyValue {GVData.GetFocusedRow.Row("id")}, NULL")
            MemoEdit1.EditValue = GVData.GetFocusedRow.Row("content")
            Dim row = Query(Of TaxNotice)($"SELECT * FROM custom.TaxNotice WHERE ID = {GVData.GetFocusedRow.Row("RefID")}").FirstOrDefault()

            If row.Notice IsNot Nothing Then
                Dim byteArray As Byte() = row.Notice.ToArray()
                FilePreviewUserControl1.LoadPdfMemoryStream(byteArray)
            End If
            GC.Collect()
        Catch ex As Exception
            DisplayErrorMessage("Error in GVData_FocusedRowChanged", ex)
        End Try
    End Sub

    Private Async Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        Timer1.Enabled = False
        Await LoadData()
    End Sub
End Class