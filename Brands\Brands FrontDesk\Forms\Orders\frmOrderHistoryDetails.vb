﻿Public Class frmOrderHistoryDetails
    Private Property OrderId As Integer

    Sub New(_orderId As Integer)
        InitializeComponent()
        OrderId = _orderId
    End Sub

    Private Sub frmOrderHistoryDetails_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Using ctxDB = New dbEPDataDataContext(GetConnectionString)
                Dim SuppliesOrder = ctxDB.SuppliesOrders.Single(Function(o) o.ID = OrderId)
                BindingSource1.DataSource = SuppliesOrder
                riProduct.DataSource = ctxDB.Supplies.ToList
                suppliesOrderItemsBindingSource.DataSource = ctxDB.SuppliesOrderItems.Where(Function(o) o.OrderID = OrderId).ToList
                Dim shippingInfo = Query($"SELECT * FROM custom.fn_ShippingAddress({SuppliesOrder.CoNum}, NULL, {SuppliesOrder.ID}, 0) fsa")

                If shippingInfo IsNot Nothing AndAlso shippingInfo.Rows.Count <> 0 Then
                    TextEditShipCo.EditValue = nz(shippingInfo.Rows(0)("ShipCo"), "")
                    TextEditShipAddress.EditValue = nz(shippingInfo.Rows(0)("ShipAddress"), "")
                    TextEditShipCSZ.EditValue = nz(shippingInfo.Rows(0)("ShipCity"), "") + ", " + nz(shippingInfo.Rows(0)("ShipState"), "") + " " + nz(shippingInfo.Rows(0)("ShipZip"), "")
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error loading order details.", ex)
        End Try
    End Sub
End Class