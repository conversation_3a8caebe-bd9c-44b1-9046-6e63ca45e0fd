﻿Imports Microsoft.EntityFrameworkCore

Public Class frmPaydeckBannerAddOrEdit
    Private PaydeckBannerId As Integer?
    Private PaydeckBanner As PaydeckBanner
    Private DbContextDB As dbEPDataDataContext

    Public Sub New(bannerId As Integer?)
        InitializeComponent()
        Me.PaydeckBannerId = bannerId
    End Sub

    Private Async Sub frmPaydeckBannerAddOrEdit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            lcRoot.ShowProgessPanel
            Me.DbContextDB = New dbEPDataDataContext(GetConnectionString)
            If PaydeckBannerId.HasValue Then
                PaydeckBanner = Await DbContextDB.PaydeckBanners.SingleAsync(Function(b) b.Id = PaydeckBannerId)
            Else
                Me.PaydeckBanner = New PaydeckBanner
                DbContextDB.PaydeckBanners.Add(PaydeckBanner)
            End If
            BindingSource1.DataSource = PaydeckBanner
        Catch ex As Exception
            DisplayErrorMessage("Error loading Paydeck Banner", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            lcRoot.ShowProgessPanel
            DbContextDB.SaveChangesAsync()
            DialogResult = DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error saving banner", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub
End Class