﻿Imports DevExpress.XtraEditors

Public Class ucSearchCompany
    Public Delegate Sub SelectedCompanyChangedHandler(sender As Object, e As SelectedCompanyChangedEventArgs)
    Public Event SelectedCompanyChangedEvent As SelectedCompanyChangedHandler

    Dim db As dbEPDataDataContext
    Private Property _Comp As view_CompanySumarry

    Private Property _CompList As List(Of view_CompanySumarry)
    Public CoNumList As List(Of Decimal)
    Public CoNumListPrev As List(Of Decimal)
    Public RefreshFilter As Boolean

    Private Property _UserSelectedValue As Boolean = False
    Private Logger As Serilog.ILogger

    Public Sub New()
        Logger = modGlobals.Logger.ForContext(Of ucSearchCompany)()
        InitializeComponent()
    End Sub

    Private Async Sub ucSearchCompany_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Await LoadDate()
        Await Threading.Tasks.Task.Delay(100)
        Me.Focus()
        Me.Select()
        TextEdit1.Focus()
        TextEdit1.SelectionStart = TextEdit1.Text.Length
    End Sub

    Private Async Function LoadDate(Optional ByVal refresh As Boolean = False) As System.Threading.Tasks.Task
        Try
            GridView1.ShowLoadingPanel()
            db = New dbEPDataDataContext(GetConnectionString)
            'Solomon added on Jan 10, '21.  Do not lock when trying to get list of companies
            db.ExecuteCommand("SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;")

            'temp test
            Dim dfd = RefreshFilter

            If CoNumList Is Nothing Then
                _CompList = Await System.Threading.Tasks.Task.Run(Function() db.view_CompanySumarries.ToList)
            Else
                _CompList = Await System.Threading.Tasks.Task.Run(Function() (From cs In db.view_CompanySumarries Where CoNumList.Contains(cs.CONUM) Select cs).ToList())
            End If

            BindingSource1.DataSource = _CompList
            GridControl1.ForceInitialize()
            GridView1.BestFitColumns()
            ccbeColumns.Properties.Items.AddRange(GridView1.Columns.Select(Function(c) c.FieldName).ToArray())
            ccbeColumns.RefreshEditValue()
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        Finally
            GridView1.HideLoadingPanel()
        End Try
    End Function

    Private Sub TextEdit1_TextChanged(sender As Object, e As EventArgs) Handles TextEdit1.TextChanged
        Try
            Logger.Verbose("Filter text changed to {Filter} - ceSearchFullWord: {ceSearchFullWord}", TextEdit1.Text, ceSearchFullWord.Checked)
            If ceSearchFullWord.Checked Then
                Dim sb = TextEdit1.EditValue?.ToString()
                Dim IsNum = IsNumeric(sb)
                If ccbeColumns.Properties.Items.Count > 0 Then
                    ccbeColumns.Properties.Items("CONUM").CheckState = IIf(IsNum AndAlso (sb.Length >= 3 AndAlso sb.Length <= 5), CheckState.Checked, CheckState.Unchecked)
                    ccbeColumns.Properties.Items("CO_PHONE").CheckState = IIf(IsNum AndAlso sb.Length >= 4, CheckState.Checked, CheckState.Unchecked)
                    ccbeColumns.Properties.Items("CO_FAX").CheckState = IIf(IsNum AndAlso sb.Length >= 4, CheckState.Checked, CheckState.Unchecked)
                    ccbeColumns.Properties.Items("CO_MODEM").CheckState = IIf(IsNum AndAlso sb.Length >= 4, CheckState.Checked, CheckState.Unchecked)
                    ccbeColumns.Properties.Items("CO_NAME").CheckState = IIf(IsNum AndAlso sb.Length >= 3, CheckState.Checked, CheckState.Unchecked)
                End If

                If Not TextEdit1.Text.StartsWith("""") AndAlso TextEdit1.Text.Contains(" ") Then
                    TextEdit1.Text = String.Format("""{0}""", TextEdit1.Text)
                    TextEdit1.SelectionStart = TextEdit1.Text.Length - 1
                    TextEdit1.SelectionLength = 0
                    TextEdit1.Refresh()
                End If

                If sb IsNot Nothing AndAlso Not sb.Contains(" ") AndAlso sb.StartsWith("""") AndAlso sb.EndsWith("""") Then
                    Dim s = TextEdit1.Text.RemoveFromStart(1).RemoveFromEnd("""")
                    TextEdit1.EditValue = s
                    TextEdit1.Refresh()
                    TextEdit1.SelectionStart = TextEdit1.Text.Length
                End If
            End If

            GridView1.ApplyFindFilter(TextEdit1.Text)

            If {3, 4, 5}.Contains(TextEdit1.Text.Length) AndAlso IsNumeric(TextEdit1.EditValue) Then
                Dim coNum As Decimal = TextEdit1.EditValue
                For index = 1 To GridView1.RowCount
                    Dim handle = GridView1.GetVisibleRowHandle(index)
                    Dim row As view_CompanySumarry = GridView1.GetRow(handle)
                    If row IsNot Nothing AndAlso row.CONUM = TextEdit1.EditValue Then
                        GridView1.FocusedRowHandle = handle
                        GridView1.MakeRowVisible(handle)
                        Exit For
                    End If
                Next
            End If

        Catch ex As Exception
            DisplayErrorMessage("Error while searching for company", ex)
        End Try
    End Sub

    Private Sub ucSearchCompany_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown, TextEdit1.KeyDown, GridControl1.KeyDown
        Try
            If e.KeyCode = Keys.Down Then
                GridView1.Focus()
            ElseIf e.KeyCode = Keys.Up Then
                If GridControl1.Focused Then
                    If GridView1.FocusedRowHandle = 0 Then
                        TextEdit1.Focus()
                    End If
                End If
            ElseIf e.KeyCode = Keys.Enter Then
                If GridView1.IsFocusedView Then
                    _Comp = CType(BindingSource1.Current, view_CompanySumarry)
                Else
                    _Comp = GridView1.GetFocusedRow()
                End If
                _UserSelectedValue = True
                Application.DoEvents()
                RaiseSelectedCompanyChanged(_Comp)
                '_editor.EditValue = _Comp?.CONUM
            ElseIf e.KeyCode = Keys.Escape Then
                RaiseSelectedCompanyCanceled()
                'editor.CancelPopup()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in search company key down", ex)
        End Try
    End Sub

    Public Function GetComp() As view_CompanySumarry
        If Not _UserSelectedValue Then Return Nothing
        Return _Comp
    End Function

    Private Async Sub SetCoNum(CoNum As Decimal, Optional DontWait As Boolean = False)
        If _CompList Is Nothing Then
            If DontWait Then
                db = New dbEPDataDataContext(GetConnectionString)
                db.ExecuteCommand("SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;")
                _Comp = db.view_CompanySumarries.SingleOrDefault(Function(c) c.CONUM = CoNum)
                _UserSelectedValue = True
            End If

            Await LoadDate()
        End If
        _Comp = _CompList.SingleOrDefault(Function(c) c.CONUM = CoNum)
        _UserSelectedValue = True
    End Sub

    Private Async Sub SetSearch(value As String)
        If _CompList Is Nothing Then
            Await LoadDate()
        End If
        TextEdit1.Text = value
        TextEdit1.SelectionStart = TextEdit1.Text.Length
    End Sub

    Private Sub Clear(obj As Object)
        Try
            If obj IsNot Nothing Then
                TextEdit1.Text = obj.ToString()
            Else
                TextEdit1.Text = String.Empty
            End If
            GridView1.FocusedRowHandle = 0
            GridView1.MakeRowVisible(0)
            ceActiveOnly.Checked = True
            TextEdit1.Focus()
            _UserSelectedValue = False
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        End Try
    End Sub

    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles ceActiveOnly.CheckedChanged
        Logger.Information("Changed ceActiveOnly to {Checked}", ceActiveOnly.Checked)
        If ceActiveOnly.Checked Then
            GridView1.Columns(colCO_STATUS.FieldName).FilterInfo = New DevExpress.XtraGrid.Columns.ColumnFilterInfo("[CO_STATUS] <> 'Inactive - Ready to Delete'")
        Else
            GridView1.ClearColumnsFilter()
        End If
    End Sub

    Private Sub GridView1_RowClick(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowClickEventArgs) Handles GridView1.RowClick
        _Comp = GridView1.GetRow(e.RowHandle)
        _UserSelectedValue = True
        '        _editor.EditValue = _Comp.CONUM
        RaiseSelectedCompanyChanged(_Comp)
    End Sub

    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        _Comp = Nothing
        '_editor?.ClosePopup()
        RaiseSelectedCompanyCanceled()
    End Sub

    Private Sub ccbeColumns_EditValueChanged(sender As Object, e As EventArgs) Handles ccbeColumns.EditValueChanged
        Try
            ccbeColumns.RefreshEditValue()
            Logger.Information("FindFilterColumns changed to {Columns} - filter string is {Filter}", ccbeColumns.Properties.GetCheckedItems(), TextEdit1.Text)
            If ccbeColumns.Text.IsNullOrWhiteSpace() Then
                GridView1.OptionsFind.FindFilterColumns = "*"
            Else
                Dim items = String.Join(";", ccbeColumns.Properties.GetCheckedItems().ToString().Split(",").Select(Function(v) v.Trim()))
                GridView1.OptionsFind.FindFilterColumns = items
            End If
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        End Try
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Logger.Information("User clicked on refresh")

        If RefreshFilter Then
            SetRefreshFilter(RefreshFilter)
        End If

        Dim t = LoadDate(True)
    End Sub

    Private Sub RaiseSelectedCompanyChanged(company As view_CompanySumarry)
        RaiseEvent SelectedCompanyChangedEvent(Me, New SelectedCompanyChangedEventArgs(company))
    End Sub

    Private Sub RaiseSelectedCompanyCanceled()
        RaiseEvent SelectedCompanyChangedEvent(Me, New SelectedCompanyChangedEventArgs(Nothing))
    End Sub

    Public Class SelectedCompanyChangedEventArgs
        Inherits EventArgs
        Public Sub New(company As view_CompanySumarry)
            Me.Company = company
        End Sub

        Public ReadOnly Property Company As view_CompanySumarry
    End Class

    Public Shared Sub BindPopupContainerEdit(editor As PopupContainerEdit, Optional clearSearchOnOpen As Boolean = True, Optional CoNumFilter As Func(Of List(Of Decimal)) = Nothing, Optional RefreshFilter As Func(Of Boolean) = Nothing)
        Dim pcc = New PopupContainerControl With {.Width = 1050, .Height = 450}
        Dim uc As ucSearchCompany
        uc = New ucSearchCompany() With {.Dock = DockStyle.Fill}
        pcc.Controls.Add(uc)
        editor.Properties.PopupControl = pcc

        AddHandler uc.SelectedCompanyChangedEvent, Sub(sender As Object, e As ucSearchCompany.SelectedCompanyChangedEventArgs)
                                                       If e.Company Is Nothing Then
                                                           editor.CancelPopup()
                                                       Else
                                                           editor.EditValue = e.Company.CONUM
                                                       End If
                                                   End Sub

        AddHandler editor.QueryDisplayText, Sub(s As Object, ee As DevExpress.XtraEditors.Controls.QueryDisplayTextEventArgs)
                                                Dim co As view_CompanySumarry = uc.GetComp
                                                If co IsNot Nothing Then
                                                    ee.DisplayText = co.CoNumAndName
                                                Else
                                                    ee.DisplayText = String.Empty
                                                End If
                                            End Sub

        AddHandler editor.QueryResultValue, Sub(s As Object, ee As DevExpress.XtraEditors.Controls.QueryResultValueEventArgs)
                                                uc.Logger.Debug("Entering: QueryResultValue")
                                                Dim co As view_CompanySumarry = uc.GetComp
                                                If co IsNot Nothing Then
                                                    ee.Value = co.CONUM
                                                Else
                                                    ee.Value = Nothing
                                                End If
                                                editor.Tag = Nothing
                                            End Sub

        AddHandler editor.QueryPopUp, Sub()
                                          uc.Logger.Debug("Entering: QueryPopUp")
                                          If CoNumFilter IsNot Nothing Then
                                              uc.SetCoNumFilter(CoNumFilter())
                                          End If
                                          uc.Focus()
                                          uc.Select()
                                          If clearSearchOnOpen Then uc.Clear(Nothing)
                                      End Sub

        AddHandler editor.EditValueChanging, Sub(sender As Object, e As Controls.ChangingEventArgs)
                                                 uc.Logger.Debug("Entering: EditValueChanging")
                                                 If uc._Comp Is Nothing Then
                                                     uc.TextEdit1.Focus()
                                                     uc.TextEdit1.Text = e.NewValue
                                                     uc.TextEdit1.SelectionStart = uc.TextEdit1.Text.Length
                                                     e.Cancel = True
                                                 End If
                                             End Sub

        AddHandler editor.EditValueChanged, Sub()
                                                uc.Logger.Debug("Entering: EditValueChanged")
                                                editor.ClosePopup()
                                            End Sub

        AddHandler editor.PreviewKeyDown, Sub(sender As Object, e As PreviewKeyDownEventArgs)
                                              uc.Logger.Debug("Entering: PreviewKeyDown")
                                              If Not editor.IsPopupOpen Then
                                                  If e.KeyCode <> Keys.Enter AndAlso e.KeyCode <> Keys.Tab AndAlso e.KeyCode <> Keys.Escape Then
                                                      uc._Comp = Nothing
                                                      editor.SelectAll()
                                                      editor.ShowPopup()
                                                  End If
                                              End If
                                          End Sub

        AddHandler editor.MouseUp, Sub(sender As Object, e As MouseEventArgs)
                                       uc.Logger.Debug("Entering: MouseDown")
                                       editor.ShowPopup()
                                   End Sub

        AddHandler uc.btnRefresh.Click, Sub(sender As Object, e As EventArgs)
                                            uc.Logger.Information("User clicked on refresh")

                                            If RefreshFilter IsNot Nothing Then
                                                uc.SetRefreshFilter(RefreshFilter())
                                            End If

                                            Dim t = uc.LoadDate(True)
                                        End Sub
    End Sub

    Private Sub SetCoNumFilter(coNumFilter As List(Of Decimal))
        Dim changed = CoNumListPrev Is Nothing OrElse String.Join(",", CoNumList) <> String.Join(",", coNumFilter)

        If changed Then
            CoNumList = coNumFilter
            CoNumListPrev = CoNumList.ToList()
            Dim t = LoadDate()
        End If
    End Sub

    Private Sub SetRefreshFilter(RefreshFilter As Boolean)
        Me.RefreshFilter = RefreshFilter
    End Sub

    Public Shared Sub SetCompany(editor As PopupContainerEdit, CoNum As Decimal, Optional DontWait As Boolean = False)
        Dim uc As PopupContainerControl = editor.Properties.PopupControl
        Dim ucComp As ucSearchCompany = uc.Controls.OfType(Of ucSearchCompany).Single
        ucComp.SetCoNum(CoNum, DontWait)
        editor.RefreshEditValue()
    End Sub

    Public Shared Sub SetSearchText(editor As PopupContainerEdit, value As String)
        Dim uc As PopupContainerControl = editor.Properties.PopupControl
        Dim ucComp As ucSearchCompany = uc.Controls.OfType(Of ucSearchCompany).Single
        ucComp.SetSearch(value)
    End Sub

    Public Shared Sub SetSearchText(editor As Repository.RepositoryItemPopupContainerEdit, value As String)
        Dim uc As PopupContainerControl = editor.PopupControl
        Dim ucComp As ucSearchCompany = uc.Controls.OfType(Of ucSearchCompany).Single
        ucComp.SetSearch(value)
    End Sub

    Shared Sub BindPopupContainerEdit(editor As Repository.RepositoryItemPopupContainerEdit)
        Dim pcc = New PopupContainerControl With {.Width = 1050, .Height = 450}
        Dim uc = New ucSearchCompany() With {.Dock = DockStyle.Fill}
        pcc.Controls.Add(uc)
        editor.PopupControl = pcc
        Dim pce As PopupContainerEdit = Nothing
        AddHandler uc.SelectedCompanyChangedEvent, Sub(sender As Object, e As ucSearchCompany.SelectedCompanyChangedEventArgs)
                                                       If e.Company Is Nothing Then
                                                           pce?.CancelPopup()
                                                       Else
                                                           pce.EditValue = e.Company.CONUM
                                                       End If
                                                   End Sub
        AddHandler editor.Popup, Sub(s As Object, e As System.EventArgs)
                                     pce = TryCast(s, PopupContainerEdit)
                                     If pce Is Nothing Then Throw New Exception("Error in BindPopupContainerEdit editor.Popup is not of type PopupContainerEdit")
                                     'uc.SetEditor(pce)
                                 End Sub
        AddHandler editor.QueryDisplayText, Sub(s As Object, ee As DevExpress.XtraEditors.Controls.QueryDisplayTextEventArgs)
                                                Dim co As view_CompanySumarry = uc.GetComp
                                                If co IsNot Nothing Then
                                                    ee.DisplayText = co.CoNumAndName
                                                Else
                                                    ee.DisplayText = String.Empty
                                                End If
                                            End Sub
        AddHandler editor.QueryResultValue, Sub(s As Object, ee As DevExpress.XtraEditors.Controls.QueryResultValueEventArgs)
                                                Dim co As view_CompanySumarry = uc.GetComp
                                                If co IsNot Nothing Then
                                                    'ee.Value = co.CONUM
                                                Else
                                                    ee.Value = Nothing
                                                End If
                                                editor.Tag = Nothing
                                            End Sub
        AddHandler editor.QueryPopUp, Sub()
                                          uc.Clear(editor.Tag)
                                          uc.TextEdit1.Select()
                                      End Sub
    End Sub
    Public Shared Sub FocusToSearch(editor As PopupContainerEdit)
        Try
            Dim ucComp As ucSearchCompany = CType(editor, PopupContainerEdit).GetPopupEditForm().Controls(2).Controls.OfType(Of ucSearchCompany).Single
            If ucComp IsNot Nothing Then
                ucComp.TextEdit1.Focus()
            End If
        Catch ex As Exception
            DisplayErrorMessage("error in FocusToSearch", ex)
        End Try
    End Sub
End Class
