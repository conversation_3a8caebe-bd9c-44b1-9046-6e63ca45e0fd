Imports System.Collections.Concurrent
Imports System.Data
Imports System.Data.Common
'Imports System.Data.SqlClient
Imports System.Reflection
Imports System.Runtime.CompilerServices
Imports Brands_FrontDesk.Data
Imports Dapper
Imports Dapper.SqlMapper ' Needed for IEnumerable
Imports Microsoft.EntityFrameworkCore
Imports Microsoft.EntityFrameworkCore.ChangeTracking
Imports Microsoft.EntityFrameworkCore.Diagnostics
Imports Microsoft.EntityFrameworkCore.Infrastructure
Imports Microsoft.EntityFrameworkCore.Metadata.Builders
Imports Microsoft.EntityFrameworkCore.Metadata.Conventions
'Imports Microsoft.EntityFrameworkCore.Proxies
Imports Microsoft.EntityFrameworkCore.Query
Imports Microsoft.EntityFrameworkCore.Query.SqlExpressions
Imports Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal
Imports Microsoft.Extensions.DependencyInjection

Public Class dbEPDataDataContext
    Inherits DbContext
    Private Shared ReadOnly _optionsCache As New ConcurrentDictionary(Of String, DbContextOptions(Of dbEPDataDataContext))()

    Public Sub New(ConnectionString As String)
        MyBase.New(GetOrCreateOptions(ConnectionString))
        Me._connectionString = ConnectionString
    End Sub

    Public Sub New(options As DbContextOptions(Of dbEPDataDataContext))
        MyBase.New(options)
        ' Extract connection string from options if available
        Dim sqlServerExtension = options.Extensions.OfType(Of SqlServerOptionsExtension)().FirstOrDefault()
        If sqlServerExtension IsNot Nothing Then
            Me._connectionString = sqlServerExtension.ConnectionString
        End If
    End Sub

    Private Shared Function GetOrCreateOptions(connectionString As String) As DbContextOptions(Of dbEPDataDataContext)
        ' Check if connection string is valid before caching
        If String.IsNullOrWhiteSpace(connectionString) Then
            Throw New ArgumentNullException(NameOf(connectionString), "Connection string cannot be null or empty")
        End If

        Return _optionsCache.GetOrAdd(connectionString, Function(cs)
                                                            Try
                                                                Dim builder = New DbContextOptionsBuilder(Of dbEPDataDataContext)()
                                                                builder.UseSqlServer(cs, Sub(options)
                                                                                             ' Enable transient error resiliency
                                                                                             options.EnableRetryOnFailure(
                                                                                                 maxRetryCount:=3,
                                                                                                 maxRetryDelay:=TimeSpan.FromSeconds(5),
                                                                                                 errorNumbersToAdd:=Nothing)
                                                                                         End Sub).UseLazyLoadingProxies()
                                                                ' Temporarily disable VB support to fix provider issue
                                                                ' builder.AddVbSupport
                                                                Return builder.Options
                                                            Catch ex As Exception
                                                                ' Remove from cache if there was an error creating options
                                                                _optionsCache.TryRemove(cs, Nothing)
                                                                Throw New InvalidOperationException($"Failed to create DbContext options for connection string. Error: {ex.Message}", ex)
                                                            End Try
                                                        End Function)
    End Function

    ''' <summary>
    ''' Clears the options cache. Useful for troubleshooting connection issues.
    ''' </summary>
    Public Shared Sub ClearOptionsCache()
        _optionsCache.Clear()
    End Sub



    Private Property _ChangeSet As New ChangeSet(Me)

    Public ReadOnly Property GetChangeSet As ChangeSet
        Get
            Return _ChangeSet
        End Get
    End Property

    Public Property Log As IO.TextWriter
    Private ReadOnly _connectionString As String

    '#Region "dbo with _t"
    '    Public Property positions As DbSet(Of positions)
    '    Public Property company_groups As DbSet(Of company_groups)
    '    Public Property gl_report As DbSet(Of gl_report)
    '    Public Property safety_documents As DbSet(Of safety_document)
    '    Public Property Machine_Fingerpris As DbSet(Of Machine_Fingerprint)
    Public Property MEMOS As DbSet(Of MEMO)
    '    Public Property LCR_NOTICES As DbSet(Of LCR_NOTICES)
    '    Public Property company_group_lists As DbSet(Of company_group_list)
    '    Public Property employee_positions As DbSet(Of employee_position)
    '    Public Property QYEND_HOLDs As DbSet(Of QYEND_HOLDS)
    '    Public Property rpt_link_conditions As DbSet(Of rpt_link_conditions)
    '    Public Property CODE_GROUPS As DbSet(Of CODE_GROUPS)
    '    Public Property crm_email_checkpois As DbSet(Of crm_email_checkpoint)
    '    Public Property web_app_users As DbSet(Of web_app_users)
    '    Public Property cobra_notifications As DbSet(Of cobra_notification)
    '    Public Property crm_change_logs As DbSet(Of crm_change_log)
    Public Property CODE_GROUP_LISTs As DbSet(Of CODE_GROUP_LIST)
    '    Public Property rpt_query_links As DbSet(Of rpt_query_links)
    '    Public Property ats_onboarding_company_doc_storages As DbSet(Of ats_onboarding_company_doc_storage)
    Public Property CHK_DET_MEMOs As DbSet(Of CHK_DET_MEMO)
    '    Public Property pr_batch_rows_dels As DbSet(Of pr_batch_rows_del)
    Public Property co_contacts As DbSet(Of co_contact)
    Public Property COMPANYs As DbSet(Of COMPANY)
    '    Public Property ETAX_CHKS As DbSet(Of ETAX_CHK)
    Public Property CO_JOBS As DbSet(Of CO_JOB)
    '    Public Property revlogin_expos As DbSet(Of revlogin_export)
    '    Public Property ats_onboarding_em_contacts As DbSet(Of ats_onboarding_em_contact)
    Public Property MAN_CHK_DET_MEMOs As DbSet(Of MAN_CHK_DET_MEMO)
    '    Public Property cobra_payments As DbSet(Of cobra_payment)
    '    Public Property ats_onboarding_documents As DbSet(Of ats_onboarding_document)
    '    'Public Property integration_tlm_transactions_dels As DbSet(Of integration_tlm_transactions_del)
    Public Property EMPLOYEEs As DbSet(Of EMPLOYEE)
    '    'Public Property eula_users As DbSet(Of eula_user)
    '    Public Property grid_groups As DbSet(Of grid_groups)
    '    Public Property mpwEmpAutoLoads As DbSet(Of mpwEmpAutoLoad)
    '    Public Property aca_classifications As DbSet(Of aca_classification)
    '    Public Property ats_onboarding_states As DbSet(Of ats_onboarding_state)
    '    Public Property PDS_RESUs As DbSet(Of PDS_RESULT)
    Public Property CO_UDFs As DbSet(Of CO_UDF)
    '    Public Property ats_onboardings As DbSet(Of ats_onboarding)
    '    Public Property mpwClientErrors As DbSet(Of mpwClientErrors)
    '    Public Property aca_company_datas As DbSet(Of aca_company_data)
    Public Property local_ee_infos As DbSet(Of LOCAL_EE_INFO)
    '    Public Property pm_reviews As DbSet(Of pm_reviews)
    '    Public Property EMP_MEMOS As DbSet(Of EMP_MEMOS)
    '    Public Property Security_epusers As DbSet(Of Security_epuser)
    Public Property PR_DELIVERies As DbSet(Of PR_DELIVERY)
    '    Public Property aca_coverage_details As DbSet(Of aca_coverage_detail)
    Public Property UDF_DEF_VALUES As DbSet(Of UDF_DEF_VALUES)
    '    Public Property cobra_documents As DbSet(Of cobra_document)
    Public Property CALENDAR_RULES As DbSet(Of CALENDAR_RULES)
    '    Public Property RESPONSE_MASTERs As DbSet(Of RESPONSE_MASTER)
    '    Public Property Job2s As DbSet(Of Job2)
    Public Property CO_CALCS As DbSet(Of CO_CALCS)
    Public Property CALENDARs As DbSet(Of CALENDAR)
    '    Public Property SALES As DbSet(Of SALES)
    '    Public Property grid_totals As DbSet(Of grid_totals)
    '    Public Property live_report_favorites As DbSet(Of live_report_favorite)
    '    Public Property Job3s As DbSet(Of Job3)
    '    Public Property aca_coverage_masters As DbSet(Of aca_coverage_master)
    Public Property documents As DbSet(Of DOCUMENT)
    '    Public Property Job4s As DbSet(Of Job4)
    '    Public Property file_types As DbSet(Of file_types)
    '    Public Property live_repos As DbSet(Of live_report)
    '    Public Property Job5s As DbSet(Of Job5)
    Public Property CHK_DET_TAXs As DbSet(Of CHK_DET_TAX)
    Public Property global_lists As DbSet(Of global_lists)
    '    Public Property employee_pictures As DbSet(Of employee_picture)
    '    Public Property pphr_outbar_items As DbSet(Of pphr_outbar_items)
    '    Public Property SQLERRORLOGs As DbSet(Of SQLERRORLOG)
    '    Public Property crm_instant_messages As DbSet(Of crm_instant_message)
    '    Public Property live_report_scripts As DbSet(Of live_report_script)
    Public Property enrollments As DbSet(Of enrollment)
    Public Property NOTEs As DbSet(Of NOTE)
    '    Public Property pm_review_tasks As DbSet(Of pm_review_task)
    '    Public Property CHK_DET_EMP_SNAPSHOTs As DbSet(Of CHK_DET_EMP_SNAPSHOT)
    '    Public Property notifications As DbSet(Of notifications)
    '    Public Property aca_large_er_counts As DbSet(Of aca_large_er_count)
    '    Public Property MAN_CHK_DET_TAXs As DbSet(Of MAN_CHK_DET_TAX)
    '    Public Property tasks As DbSet(Of tasks)
    '    Public Property cobra_notes As DbSet(Of cobra_note)
    Public Property EE_UDFS As DbSet(Of EE_UDF)
    '    Public Property crm_conversations As DbSet(Of crm_conversation)
    '    Public Property ACCRUAL_MASTERs As DbSet(Of ACCRUAL_MASTER)
    Public Property employee_rates As DbSet(Of employee_rate)
    '    Public Property rpt_querys As DbSet(Of rpt_query)
    Public Property ACT_AUTOs As DbSet(Of ACT_AUTO)
    '    Public Property pphr_outbar_items_panels As DbSet(Of pphr_outbar_items_panels)
    '    Public Property issues As DbSet(Of issues)
    '    Public Property aca_large_er_form_counts As DbSet(Of aca_large_er_form_count)
    '    Public Property integration_company_transactions As DbSet(Of integration_company_transactions)
    '    Public Property COWA_LITAXs As DbSet(Of COWA_LITAX)
    '    Public Property pp_web_links As DbSet(Of pp_web_links)
    Public Property DEDUCTIONs As DbSet(Of DEDUCTION)
    '    Public Property HR_ATTENDANCEs As DbSet(Of HR_ATTENDANCE)
    Public Property DIVISIONs As DbSet(Of DIVISION)
    '    Public Property pphr_outbar_panels As DbSet(Of pphr_outbar_panels)
    Public Property EMP_DEDS As DbSet(Of EMP_DED)
    '    Public Property pr_batch_grid_filters As DbSet(Of pr_batch_grid_filters)
    '    Public Property EMP_LEAVE_TOTs As DbSet(Of EMP_LEAVE_TOT)
    '    Public Property HR_EDUCATIONs As DbSet(Of HR_EDUCATION)
    '    Public Property pm_review_notes As DbSet(Of pm_review_note)
    Public Property EMP_OPS As DbSet(Of EMP_OPS)
    '    Public Property employee_ncs As DbSet(Of employee_ncs)
    '    Public Property integration_shugo_logs As DbSet(Of integration_shugo_log)
    '    Public Property GENLEDGEs As DbSet(Of GENLEDGE)
    '    Public Property cobra_tasks As DbSet(Of cobra_task)
    '    Public Property ats_candidates As DbSet(Of ats_candidates)
    '    Public Property GL_DESCs As DbSet(Of GL_DESC)
    '    Public Property HR_EMCONTACTS As DbSet(Of HR_EMCONTACTS)
    '    Public Property rpt_storage_forms As DbSet(Of rpt_storage_forms)
    '    Public Property INVOICE_LOGs As DbSet(Of INVOICE_LOG)
    '    Public Property labor_distributions As DbSet(Of labor_distribution)
    '    Public Property pphr_outbar_icons As DbSet(Of pphr_outbar_icons)
    Public Property LOCAL_IDS As DbSet(Of LOCAL_ID)
    '    Public Property integration_deductions_logs As DbSet(Of integration_deductions_log)
    '    Public Property NPCs As DbSet(Of NPC)
    '    Public Property HR_FAMILYs As DbSet(Of HR_FAMILY)
    Public Property OTHER_PAYS As DbSet(Of OTHER_PAY)
    Public Property pr_batch_lists As DbSet(Of pr_batch_list)
    '    Public Property HR_FORMER_EMPLOYERs As DbSet(Of HR_FORMER_EMPLOYER)
    '    Public Property pm_review_documents As DbSet(Of pm_review_document)
    '    Public Property ats_applicant_notes As DbSet(Of ats_applicant_note)
    '    Public Property pp_employee_documents As DbSet(Of pp_employee_document)
    Public Property STATE_EE_INFOs As DbSet(Of STATE_EE_INFO)
    '    Public Property cobra_insurances As DbSet(Of cobra_insurance)
    '    Public Property crm_bases As DbSet(Of crm_base)
    Public Property COUSERDEFs As DbSet(Of COUSERDEF)
    Public Property STATE_IDs As DbSet(Of STATE_IDS)
    '    Public Property UCI_CREDITs As DbSet(Of UCI_CREDIT)
    '    Public Property HR_REMINDERS As DbSet(Of HR_REMINDERS)
    '    Public Property PRTSETUPs As DbSet(Of PRTSETUP)
    '    Public Property crm_quick_notes As DbSet(Of crm_quick_notes)
    '    Public Property tax_credits As DbSet(Of tax_credits)
    '    Public Property WKMAN_COMPs As DbSet(Of WKMAN_COMP)
    '    Public Property NACHA_DDs As DbSet(Of NACHA_DD)
    '    Public Property HR_REVIEWS As DbSet(Of HR_REVIEWS)
    '    Public Property ACCRUAL_DETAILs As DbSet(Of ACCRUAL_DETAIL)
    '    Public Property co_401k_components As DbSet(Of co_401k_components)
    '    Public Property crm_document_storages As DbSet(Of crm_document_storage)
    '    Public Property integration_tlm_transactions As DbSet(Of integration_tlm_transactions)
    '    Public Property ats_applicant_documents As DbSet(Of ats_applicant_document)
    '    Public Property pp_employee_notes As DbSet(Of pp_employee_note)
    Public Property CHK_MASTs As DbSet(Of CHK_MAST)
    '    Public Property pm_review_goals As DbSet(Of pm_review_goal)
    '    Public Property state_registration_details As DbSet(Of state_registration_details)
    '    Public Property HR_SKILLS As DbSet(Of HR_SKILLS)
    '    Public Property crm_tickets As DbSet(Of crm_tickets)
    '    Public Property employee_change_details As DbSet(Of employee_change_detail)
    Public Property DEPARTMENTs As DbSet(Of DEPARTMENT)
    '    Public Property DD_INTERCEPTs As DbSet(Of DD_INTERCEPT)
    '    Public Property GL_TOTALS As DbSet(Of GL_TOTALS)
    '    Public Property crm_documents As DbSet(Of crm_documents)
    '    Public Property web_app_sso_preauths As DbSet(Of web_app_sso_preauth)
    '    Public Property employee_change_requests As DbSet(Of employee_change_request)
    '    Public Property HR_TRAININGs As DbSet(Of HR_TRAINING)
    '    Public Property NPC_DDs As DbSet(Of NPC_DD)
    Public Property payroll_exts As DbSet(Of payroll_ext)
    '    Public Property SP_CHKs As DbSet(Of SP_CHK)
    Public Property emp_deds_amount_schedules As DbSet(Of emp_deds_amount_schedule)
    '    Public Property ats_former_employers As DbSet(Of ats_former_employer)
    '    Public Property mpwWebReturnMsgs As DbSet(Of mpwWebReturnMsgs)
    '    Public Property UCI_TOTALS As DbSet(Of UCI_TOTALS)
    '    Public Property pp_global_lists As DbSet(Of pp_global_lists)
    Public Property CHK_DET_DEDs As DbSet(Of CHK_DET_DED)
    Public Property pr_batch_grid_schemas As DbSet(Of pr_batch_grid_schema)
    '    Public Property ats_educations As DbSet(Of ats_education)
    Public Property CHK_DET_PAYs As DbSet(Of CHK_DET_PAY)
    '    Public Property background_check_requests As DbSet(Of background_check_requests)
    '    Public Property ats_skills As DbSet(Of ats_skills)
    '    Public Property pp_company_documents As DbSet(Of pp_company_document)
    '    Public Property tes As DbSet(Of Test)
    Public Property PAYMENT_ADDRESSes As DbSet(Of PAYMENT_ADDRESS)
    '    Public Property integration_accrual_transactions As DbSet(Of integration_accrual_transactions)
    '    Public Property ats_certifications As DbSet(Of ats_certifications)
    '    Public Property STRCTR_MAs As DbSet(Of STRCTR_MAST)
    '    'Public Property report_data_requests As DbSet(Of report_data_request)
    '    Public Property ats_applicants As DbSet(Of ats_applicants)
    Public Property PAYROLLs As DbSet(Of PAYROLL)
    Public Property REPORT_LISTs As DbSet(Of REPORT_LIST)
    '    Public Property INVOICE_LOG_T_INSEs As DbSet(Of INVOICE_LOG_T_INSERT)
    '    Public Property ep_index_frag_his As DbSet(Of ep_index_frag_hist)
    '    Public Property aca_affordabilitys As DbSet(Of aca_affordability)
    '    Public Property erm_tickets As DbSet(Of erm_tickets)
    '    Public Property report_data_responses As DbSet(Of report_data_response)
    '    Public Property integration_employee_transactions As DbSet(Of integration_employee_transactions)
    '    Public Property ats_open_positions As DbSet(Of ats_open_positions)
    '    Public Property CHK_DET_MSGS As DbSet(Of CHK_DET_MSGS)
    '    Public Property safety_incidents As DbSet(Of safety_incidents)
    '    Public Property aca_initial_assessments As DbSet(Of aca_initial_assessment)
    Public Property COOPTIONs As DbSet(Of COOPTION)
    '    Public Property ach_verification_logs As DbSet(Of ach_verification_log)
    '    Public Property TaxRecons As DbSet(Of TaxRecon)
    '    Public Property ats_candidate_documents As DbSet(Of ats_candidate_document)
    '    Public Property pr_batch_grid_columns As DbSet(Of pr_batch_grid_column)
    '    Public Property payroll_progress_bars As DbSet(Of payroll_progress_bar)
    '    Public Property print_queues As DbSet(Of print_queue)
    '    Public Property company_state_tax_payment_methods As DbSet(Of company_state_tax_payment_method)
    '    Public Property hr_certifications As DbSet(Of hr_certifications)
    '    Public Property emp_ops_amount_schedules As DbSet(Of emp_ops_amount_schedule)
    '    Public Property HR_CONTACTS As DbSet(Of HR_CONTACTS)
    '    Public Property hr_assets As DbSet(Of hr_assets)
    '    Public Property schreport_authviewers As DbSet(Of schreport_authviewers)
    '    Public Property integration_parameters As DbSet(Of integration_parameters)
    '    Public Property state_tax_report_tables As DbSet(Of state_tax_report_table)
    '    Public Property ats_candidate_notes As DbSet(Of ats_candidate_note)
    '    Public Property hr_events As DbSet(Of hr_events)
    '    Public Property integration_rates_transactions As DbSet(Of integration_rates_transactions)
    '    Public Property safety_tasks As DbSet(Of safety_task)
    '    Public Property gl_enhanceds As DbSet(Of gl_enhanced)
    '    Public Property tracking_services_datas As DbSet(Of tracking_services_data)
    '    Public Property erm_ticket_tasks As DbSet(Of erm_ticket_task)
    '    Public Property ESSNEWUSERS As DbSet(Of ESSNEWUSERS)
    '    Public Property pr_batch_grid_totals As DbSet(Of pr_batch_grid_totals)
    '    Public Property dependent_coverages As DbSet(Of dependent_coverage)
    '    Public Property integration_id_mappings As DbSet(Of integration_id_mapping)
    '    Public Property sb_register_spls As DbSet(Of sb_register_split)
    '    Public Property crm_call_logs As DbSet(Of crm_call_log)
    Public Property pr_batch_rows As DbSet(Of pr_batch_row)
    '    Public Property SCHRPT_LIs As DbSet(Of SCHRPT_LIST)
    '    Public Property service_requests As DbSet(Of service_request)
    '    Public Property eftps_report_table_masters As DbSet(Of eftps_report_table_master)
    '    Public Property gl_enhanced_reports As DbSet(Of gl_enhanced_report)
    '    Public Property integration_users As DbSet(Of integration_users)
    '    Public Property ats_candidate_scores As DbSet(Of ats_candidate_score)
    '    Public Property accrual_requests As DbSet(Of accrual_request)
    '    Public Property AUDIT_TEMPLATES As DbSet(Of AUDIT_TEMPLATES)
    Public Property services As DbSet(Of Service)
    Public Property invoice_masters As DbSet(Of invoice_master)
    '    Public Property SCHFED_FORMSEXCEPTIONS As DbSet(Of SCHFED_FORMSEXCEPTIONS)
    '    Public Property safety_correctives As DbSet(Of safety_correctives)
    Public Property SCHCORPT_LISTs As DbSet(Of SCHCORPT_LIST)
    '    Public Property co_sites As DbSet(Of co_sites)
    '    Public Property state_registrations As DbSet(Of state_registration)
    '    Public Property integration_accounts As DbSet(Of integration_accounts)
    '    Public Property notification_executions As DbSet(Of notification_execution)
    '    Public Property erm_ticket_notes As DbSet(Of erm_ticket_note)
    '    Public Property SCHCORPT_PARAMS As DbSet(Of SCHCORPT_PARAMS)
    '    Public Property eftps_report_table_details As DbSet(Of eftps_report_table_detail)
    '    Public Property MSpeer_reques As DbSet(Of MSpeer_request)
    '    Public Property SCHCO_RPTUSERs As DbSet(Of SCHCO_RPTUSER)
    Public Property EP_IMP_LISTs As DbSet(Of EP_IMP_LIST)
    '    Public Property SCHCO_USERRPTs As DbSet(Of SCHCO_USERRPT)
    '    Public Property event_alerts As DbSet(Of event_alert)
    '    Public Property co_site_details As DbSet(Of co_site_detail)
    '    Public Property Payroll_Services As DbSet(Of Payroll_Services)
    '    Public Property SCHCO_NCUEMAILs As DbSet(Of SCHCO_NCUEMAIL)
    '    Public Property otp_securitys As DbSet(Of otp_security)
    '    Public Property security_hists As DbSet(Of security_hist)
    '    Public Property ep_statistics As DbSet(Of ep_statistics)
    '    Public Property MSpeer_topologyreques As DbSet(Of MSpeer_topologyrequest)
    '    Public Property SCHPRT_JOBS As DbSet(Of SCHPRT_JOBS)
    '    Public Property taxable_fixes As DbSet(Of taxable_fixes)
    Public Property MAN_CHK_MASTs As DbSet(Of MAN_CHK_MAST)
    Public Property invoice_item_details As DbSet(Of invoice_item_detail)
    Public Property MAN_CHK_DET_PAYs As DbSet(Of MAN_CHK_DET_PAY)
    '    Public Property accrual_request_details As DbSet(Of accrual_request_detail)
    Public Property pr_batch_msgs As DbSet(Of pr_batch_msg)
    Public Property MAN_CHK_DET_DEDs As DbSet(Of MAN_CHK_DET_DED)
    '    Public Property ats_applicant_ncs As DbSet(Of ats_applicant_ncs)
    '    Public Property safety_notes As DbSet(Of safety_note)
    '    Public Property emp_memos_amount_schedules As DbSet(Of emp_memos_amount_schedule)
    '    Public Property company_local_tax_filings As DbSet(Of company_local_tax_filing)
    '    Public Property ats_candidate_tasks As DbSet(Of ats_candidate_task)
    '    Public Property MSpeer_conflictdetectionconfigreques As DbSet(Of MSpeer_conflictdetectionconfigrequest)
    '    Public Property grid_filters As DbSet(Of grid_filters)
    '    Public Property crm_propertys As DbSet(Of crm_property)
    '    Public Property erm_ticket_documents As DbSet(Of erm_ticket_document)
    '    Public Property company_tax_filings As DbSet(Of company_tax_filing)
    '    Public Property pr_batch_grid_groups As DbSet(Of pr_batch_grid_group)
    '    Public Property invoice_salestax_details As DbSet(Of invoice_salestax_detail)
    '    Public Property company_taxes As DbSet(Of company_taxes)
    '    Public Property SMART_RPTs As DbSet(Of SMART_RPT)
    '    Public Property grid_schemas As DbSet(Of grid_schemas)
    '    Public Property ACHBatchs As DbSet(Of ACHBatch)
    '    Public Property safety_treatments As DbSet(Of safety_treatments)
    '    Public Property pr_batch_grid_access As DbSet(Of pr_batch_grid_access)
    '    Public Property pr_batch_buffers As DbSet(Of pr_batch_buffer)
    '    Public Property smart_rpt_favorites As DbSet(Of smart_rpt_favorite)
    '    Public Property ACHBatch_companys As DbSet(Of ACHBatch_company)
    '    Public Property pay_grades As DbSet(Of pay_grades)
    '    Public Property cobra_events As DbSet(Of cobra_events)
    '    Public Property pr_batch_buffer_rows As DbSet(Of pr_batch_buffer_row)
    '    Public Property invoice_creddebs As DbSet(Of invoice_creddebs)
    '    Public Property grid_columns As DbSet(Of grid_columns)
    '#End Region
    '#Region "dbo not ending with _t"
    '    Public Property [SecurityQuestionss] As DbSet(Of SecurityQuestions)
    '    Public Property [web_app_user_tokenss] As DbSet(Of web_app_user_tokens)
    '    Public Property [local_tax_triggerss] As DbSet(Of local_tax_triggers)
    Public Property [Bank_Holidays] As DbSet(Of Bank_Holidays)
    '    Public Property [web_app_user_loginss] As DbSet(Of web_app_user_logins)
    '    Public Property [mpwWebUserDivss] As DbSet(Of mpwWebUserDivs)
    '    Public Property [billing_template_masters] As DbSet(Of billing_template_master)
    '    Public Property [ach_verification_exceptionss] As DbSet(Of ach_verification_exceptions)
    '    Public Property [Advertisementss] As DbSet(Of Advertisements)
    '    Public Property [web_app_user_claimss] As DbSet(Of web_app_user_claims)
    '    Public Property [qyend_templatess] As DbSet(Of qyend_templates)
    '    Public Property [Login_Activitys] As DbSet(Of Login_Activity)
    '    Public Property [mpwWebUserCoss] As DbSet(Of mpwWebUserCos)
    '    Public Property [syspublicationss] As DbSet(Of syspublications)
    '    Public Property [billing_template_itemss] As DbSet(Of billing_template_items)
    '    Public Property [IHpublishercolumnss] As DbSet(Of IHpublishercolumns)
    '    Public Property [eula_detail_types] As DbSet(Of eula_detail_type)
    '    Public Property [IHcolumnss] As DbSet(Of IHcolumns)
    '    Public Property [Multi_Company_Accesss] As DbSet(Of Multi_Company_Access)
    '    Public Property [QYEND_SELECTIONSs] As DbSet(Of QYEND_SELECTIONS)
    '    Public Property [qyend_templ_settingss] As DbSet(Of qyend_templ_settings)
    Public Property [ACT_ITEMS] As DbSet(Of ACT_ITEM)
    '    Public Property [mpwLinkss] As DbSet(Of mpwLinks)
    '    Public Property [BANK_INFOs] As DbSet(Of BANK_INFO)
    Public Property [LEASE_GROUPS] As DbSet(Of LEASE_GROUPS)
    '    Public Property [CATEGORYs] As DbSet(Of CATEGORY)
    '    Public Property [eula_locations] As DbSet(Of eula_location)
    '    Public Property [billing_template_categoriess] As DbSet(Of billing_template_categories)
    '    Public Property [CREPORTSs] As DbSet(Of CREPORTS)
    '    Public Property [DELIVERYs] As DbSet(Of Delivery)
    '    Public Property [UDF_DEFAULTSs] As DbSet(Of UDF_DEFAULTS)
    '    Public Property [EVENT_LOGs] As DbSet(Of EVENT_LOG)
    '    Public Property [epsusers_rightss] As DbSet(Of epsusers_rights)
    Public Property [LOCALs] As DbSet(Of LOCAL)
    '    Public Property [NUMCHECKs] As DbSet(Of NUMCHECK)
    '    Public Property [PDSs] As DbSet(Of PDS)
    '    Public Property [eula_details] As DbSet(Of eula_detail)
    '    Public Property [local_ee_info_temps] As DbSet(Of local_ee_info_temp)
    '    Public Property [PDS_QUOTEs] As DbSet(Of PDS_QUOTE)
    '    Public Property [live_report_columnss] As DbSet(Of live_report_columns)
    '    Public Property [tax_periodss] As DbSet(Of tax_periods)
    '    Public Property [office_settingss] As DbSet(Of office_settings)
    '    Public Property [sch_reportfolio_books] As DbSet(Of sch_reportfolio_book)
    Public Property [SALESPEOPLEs] As DbSet(Of SALESPEOPLE)
    '    Public Property [PDS_LOGs] As DbSet(Of PDS_LOG)
    '    Public Property [execulogs] As DbSet(Of execulog)
    '    Public Property [crm_message_activitys] As DbSet(Of crm_message_activity)
    '    Public Property [execulog_detailss] As DbSet(Of execulog_details)
    '    Public Property [CATLOCALORs] As DbSet(Of CATLOCALOR)
    '    Public Property [STACKs] As DbSet(Of STACK)
    '    Public Property [execulog_keyss] As DbSet(Of execulog_keys)
    Public Property [STATE_INFOs] As DbSet(Of STATE_INFO)
    '    Public Property [execulog_logins] As DbSet(Of execulog_login)
    '    Public Property [WA_LITAXs] As DbSet(Of WA_LITAX)
    '    Public Property [eula_masters] As DbSet(Of eula_master)
    '    Public Property [crm_conversation_users] As DbSet(Of crm_conversation_user)
    '    Public Property [WY_WCTAXs] As DbSet(Of WY_WCTAX)
    '    Public Property [live_report_accesss] As DbSet(Of live_report_access)
    Public Property [ZIPS] As DbSet(Of ZIP)
    '    Public Property [live_parameter_definitions] As DbSet(Of live_parameter_definition)
    '    Public Property [enrollment_detailss] As DbSet(Of enrollment_details)
    '    Public Property [category_catchup_agess] As DbSet(Of category_catchup_ages)
    '    Public Property [FEEDBACKs] As DbSet(Of FEEDBACK)
    '    Public Property [live_report_parameters] As DbSet(Of live_report_parameter)
    '    Public Property [live_report_displays] As DbSet(Of live_report_display)
    '    Public Property [epsusers_managerss] As DbSet(Of epsusers_managers)
    '    Public Property [applicationss] As DbSet(Of applications)
    '    Public Property [web_app_user_roless] As DbSet(Of web_app_user_roles)
    '    Public Property [applications_databases] As DbSet(Of applications_database)
    '    Public Property [epsuserss] As DbSet(Of epsusers)
    '    Public Property [web_app_user_userroless] As DbSet(Of web_app_user_userroles)
    '    Public Property [epsusers_detailss] As DbSet(Of epsusers_details)
    '    Public Property [applications_servers] As DbSet(Of applications_server)
    '    Public Property [epsusers_divs] As DbSet(Of epsusers_divs)
    '    Public Property [applications_versions] As DbSet(Of applications_version)
    '    Public Property [GTL] As DbSet(Of GTL)
    '    Public Property [RESPONSE_LOG] As DbSet(Of RESPONSE_LOG)
    '    Public Property [STATE_A] As DbSet(Of STATE_A)
    '    Public Property [HR_LISTS] As DbSet(Of HR_LISTS)
    '    Public Property [NACHAENTRY] As DbSet(Of NACHAENTRY)
    '    Public Property [salestax_info] As DbSet(Of salestax_info)
    Public Property [settings] As DbSet(Of setting)
    '    Public Property [STATE_DEFAULTS] As DbSet(Of STATE_DEFAULTS)
    '    Public Property [SCHCORPT_PARAMS_T2s] As DbSet(Of SCHCORPT_PARAMS_T2)
    '    Public Property [special_chars] As DbSet(Of special_char)
    '    Public Property [progress_bar_data] As DbSet(Of progress_bar_data)
    '    Public Property [salestax_itemratess] As DbSet(Of salestax_itemrates)
    '    Public Property [LOG_TYPEs] As DbSet(Of LOG_TYPE)
    '    Public Property [CALL_TYPEs] As DbSet(Of CALL_TYPE)
    '    Public Property [employee_change_templates] As DbSet(Of employee_change_template)
    '    Public Property [PR_PROVIDERs] As DbSet(Of PR_PROVIDER)
    '    Public Property [epsusers_ticketss] As DbSet(Of epsusers_tickets)
    '    Public Property [salestax_groupdetailss] As DbSet(Of salestax_groupdetails)
    Public Property [STATE_Bs] As DbSet(Of STATE_B)
    '    Public Property [epsusers_accesss] As DbSet(Of epsusers_access)
    '    Public Property [ACCT_TYPEs] As DbSet(Of ACCT_TYPE)
    '    Public Property [esign_documentss] As DbSet(Of esign_documents)
    '    Public Property [dtpropertiess] As DbSet(Of dtproperties)
    '    Public Property [PDS_NOTESs] As DbSet(Of PDS_NOTES)
    '    Public Property [PDS_TEMPLATESs] As DbSet(Of PDS_TEMPLATES)
    '    Public Property [sb_categoriess] As DbSet(Of sb_categories)
    '    Public Property [esign_document_fieldss] As DbSet(Of esign_document_fields)
    '    Public Property [act_items_priceinfos] As DbSet(Of act_items_priceinfo)
    '    Public Property [SECURITYs] As DbSet(Of SECURITY)
    '    Public Property [tracking_event_statuss] As DbSet(Of tracking_event_status)
    '    Public Property [sysarticless] As DbSet(Of sysarticles)
    '    Public Property [act_items_pricedetailss] As DbSet(Of act_items_pricedetails)
    '    Public Property [sb_accountss] As DbSet(Of sb_accounts)
    '    Public Property [Priors_Shma_Koleynus] As DbSet(Of Priors_Shma_Koleynu)
    '    Public Property [print_queue_syncs] As DbSet(Of print_queue_sync)
    '    Public Property [state_tax_payment_methods] As DbSet(Of state_tax_payment_method)
    '    Public Property [report_request_logs] As DbSet(Of report_request_log)
    '    Public Property [integration_providerss] As DbSet(Of integration_providers)
    '    Public Property [esign_groupss] As DbSet(Of esign_groups)
    '    Public Property [sb_acct_balss] As DbSet(Of sb_acct_bals)
    '    Public Property [xxAUDIT_LOG_TRANSACTIONSs] As DbSet(Of xxAUDIT_LOG_TRANSACTIONS)
    '    Public Property [report_request_services] As DbSet(Of report_request_service)
    '    Public Property [TAXCODESs] As DbSet(Of TAXCODES)
    '    Public Property [tracking_eventss] As DbSet(Of tracking_events)
    Public Property [EPSYS1s] As DbSet(Of EPSYS1)
    '    Public Property [integration_fset_masters] As DbSet(Of integration_fset_master)
    '    Public Property [xxAUDIT_LOG_DATAs] As DbSet(Of xxAUDIT_LOG_DATA)
    '    Public Property [act_items_count_calcss] As DbSet(Of act_items_count_calcs)
    '    Public Property [sysarticlecolumnss] As DbSet(Of sysarticlecolumns)
    '    Public Property [sb_acct_srvcss] As DbSet(Of sb_acct_srvcs)
    '    Public Property [report_request_service_usages] As DbSet(Of report_request_service_usage)
    '    Public Property [AUDIT_LOG_TABLESs] As DbSet(Of AUDIT_LOG_TABLES)
    '    Public Property [sysschemaarticless] As DbSet(Of sysschemaarticles)
    '    Public Property [sb_registers] As DbSet(Of sb_register)
    '    Public Property [integration_fset_clientdetails] As DbSet(Of integration_fset_clientdetail)
    '    Public Property [ActiveMasters] As DbSet(Of ActiveMaster)
    '    Public Property [eftps_report_table_batchs] As DbSet(Of eftps_report_table_batch)
    '    Public Property [syssubscriptionss] As DbSet(Of syssubscriptions)
    '    Public Property [SCHFED_FORMS_T_TABLEs] As DbSet(Of SCHFED_FORMS_T_TABLE)
    '    Public Property [AUDIT_LOG_TABLE_TEMPLATESs] As DbSet(Of AUDIT_LOG_TABLE_TEMPLATES)
    '    Public Property [print_autologs] As DbSet(Of print_autolog)
    '    Public Property [AUDIT_LOG_TEMPLATESs] As DbSet(Of AUDIT_LOG_TEMPLATES)
    '    Public Property [notification_systems] As DbSet(Of notification_system)
    '    Public Property [REPORTS_T_TABLEs] As DbSet(Of REPORTS_T_TABLE)
    '    Public Property [definition_401k_servicess] As DbSet(Of definition_401k_services)
    '    Public Property [OFF_ACCTSs] As DbSet(Of OFF_ACCTS)
    '    Public Property [sysarticleupdatess] As DbSet(Of sysarticleupdates)
    '    Public Property [SCHRPT_PARAMSs] As DbSet(Of SCHRPT_PARAMS)
    '    Public Property [state_minwage_codess] As DbSet(Of state_minwage_codes)
    '    Public Property [OFFICE_INFOs] As DbSet(Of OFFICE_INFO)
    '    Public Property [sysdiagramss] As DbSet(Of sysdiagrams)
    '    Public Property [Import_Fieldss] As DbSet(Of Import_Fields)
    '    Public Property [SCHRPT_TEMPLATESs] As DbSet(Of SCHRPT_TEMPLATES)
    '    Public Property [MSpub_identity_ranges] As DbSet(Of MSpub_identity_range)
    '    Public Property [SCHTEMPLATE_RPTSs] As DbSet(Of SCHTEMPLATE_RPTS)
    '    Public Property [MSpeer_lsnss] As DbSet(Of MSpeer_lsns)
    '    Public Property [systranschemass] As DbSet(Of systranschemas)
    '    Public Property [SCHTEMPRPT_PARAMSs] As DbSet(Of SCHTEMPRPT_PARAMS)
    '    Public Property [state_minwage_coderatess] As DbSet(Of state_minwage_coderates)
    '    Public Property [import_transs] As DbSet(Of import_trans)
    '    Public Property [sb_banktranss] As DbSet(Of sb_banktrans)
    '    Public Property [FedACHdirs] As DbSet(Of FedACHdir)
    '    Public Property [PAYROLL_FILEARCHIVE_T_TABLEs] As DbSet(Of PAYROLL_FILEARCHIVE_T_TABLE)
    '    Public Property [tracking_detailss] As DbSet(Of tracking_details)
    '    Public Property [crm_property_definitions] As DbSet(Of crm_property_definition)
    '    Public Property [sb_savedsearchess] As DbSet(Of sb_savedsearches)
    '    Public Property [MSpeer_responses] As DbSet(Of MSpeer_response)
    '    Public Property [Import_Masters] As DbSet(Of Import_Master)
    '    Public Property [ats_applicant_ncs_package_paramss] As DbSet(Of ats_applicant_ncs_package_params)
    '    Public Property [tracking_conditionss] As DbSet(Of tracking_conditions)
    '    Public Property [EMP_RateChangesLogs] As DbSet(Of EMP_RateChangesLog)
    '    Public Property [OFF_MAILTMPLs] As DbSet(Of OFF_MAILTMPL)
    '    Public Property [MSpeer_topologyresponses] As DbSet(Of MSpeer_topologyresponse)
    '    Public Property [SCH_EXPPARAMSs] As DbSet(Of SCH_EXPPARAMS)
    '    Public Property [MSpeer_originatorid_historys] As DbSet(Of MSpeer_originatorid_history)
    '    Public Property [SCHRPT_NAMEREPLs] As DbSet(Of SCHRPT_NAMEREPL)
    '    Public Property [tracking_detail_eventss] As DbSet(Of tracking_detail_events)
    '    Public Property [document_storage_TABLEs] As DbSet(Of document_storage_TABLE)
    '    Public Property [tracking_systems] As DbSet(Of tracking_system)
    '    Public Property [SCHCORPT_PARAMS_T1s] As DbSet(Of SCHCORPT_PARAMS_T1)
    Public Property [state_taxes] As DbSet(Of state_tax)
    '    Public Property [definition_401k_componentss] As DbSet(Of definition_401k_components)
    '    Public Property [MSpeer_conflictdetectionconfigresponses] As DbSet(Of MSpeer_conflictdetectionconfigresponse)
    '    Public Property [tracking_event_executionss] As DbSet(Of tracking_event_executions)
    '    Public Property [SecurityAnswerss] As DbSet(Of SecurityAnswers)
    '    Public Property [state_tax_triggerss] As DbSet(Of state_tax_triggers)
    '    Public Property [ats_applicant_ncs_paramss] As DbSet(Of ats_applicant_ncs_params)
    '    Public Property [crm_property_types] As DbSet(Of crm_property_type)
    '    Public Property [tracking_execution_detailss] As DbSet(Of tracking_execution_details)
    '    Public Property [service_eulas] As DbSet(Of service_eula)
    '    Public Property [SecurityImageSetups] As DbSet(Of SecurityImageSetup)
    '    Public Property [payroll_packarchive_TABLEs] As DbSet(Of payroll_packarchive_TABLE)
    '    Public Property [SYS_IMAGESs] As DbSet(Of SYS_IMAGES)
    '    Public Property [epsusers_configs] As DbSet(Of epsusers_config)
    '    Public Property [crm_property_values] As DbSet(Of crm_property_value)
    '    Public Property [email_profiles] As DbSet(Of email_profile)
    '    Public Property [SecurityImagess] As DbSet(Of SecurityImages)
    Public Property [local_taxes] As DbSet(Of local_tax)
    '    Public Property [mpwWebUserDeptss] As DbSet(Of mpwWebUserDepts)
    '    Public Property [integration_account_parameterss] As DbSet(Of integration_account_parameters)
    '    Public Property [cdc_transactionss] As DbSet(Of cdc_transactions)
    '    Public Property [service_definitions] As DbSet(Of service_definition)
    '#End Region

    '#Region "custom"
    '    Public Property [PaydeckServiceSvcButton_discontinueds] As DbSet(Of PaydeckServiceSvcButton_discontinued)
    '    Public Property [ObjectVersionControls] As DbSet(Of ObjectVersionControl)
    Public Property [CustomReportTableLinks] As DbSet(Of CustomReportTableLink)
    '    Public Property [ACA_Annual_Affordabilitys] As DbSet(Of ACA_Annual_Affordability)
    '    Public Property [OnboardingStateEmployeeInfos] As DbSet(Of OnboardingStateEmployeeInfo)
    '    Public Property [PriceIncreaseSuggestions] As DbSet(Of PriceIncreaseSuggestion)
    '    Public Property [Emails_Tables] As DbSet(Of Emails_Table)
    '    Public Property [Logs] As DbSet(Of Log)
    '    Public Property [NYS45Combined_As] As DbSet(Of NYS45Combined_A)
    '    Public Property [PayrollFundings] As DbSet(Of PayrollFunding)
    '    Public Property [OnboardingEvents] As DbSet(Of OnboardingEvent)
    '    Public Property [PP_ImportSessions] As DbSet(Of PP_ImportSession)
    '    Public Property [TelebroadPhoneUrsJsonLogss] As DbSet(Of TelebroadPhoneUrsJsonLogs)
    '    Public Property [Audit_Logins] As DbSet(Of Audit_Logins)
    '    Public Property [SecurityAlertIncidents] As DbSet(Of SecurityAlertIncident)
    '    Public Property [FilingControl1099s] As DbSet(Of FilingControl1099)
    Public Property [ShipAddressOverrides] As DbSet(Of ShipAddressOverride)
    Public Property [AutoPayrollsLogs] As DbSet(Of AutoPayrollsLog)
    '    Public Property [BrandsLogos] As DbSet(Of BrandsLogo)
    '    Public Property [BrandsAuthUserEvents] As DbSet(Of BrandsAuthUserEvent)
    '    Public Property [NJW2FilingControls] As DbSet(Of NJW2FilingControl)
    '    Public Property [AuditLoginInfos] As DbSet(Of AuditLoginInfo)
    '    Public Property [OtherStatesFilingControlDeleteds] As DbSet(Of OtherStatesFilingControlDeleted)
    '    Public Property [PaydeckPosterEliteTranss] As DbSet(Of PaydeckPosterEliteTrans)
    '    Public Property [NYS45Combined_Ss] As DbSet(Of NYS45Combined_S)
    '    Public Property [QueEventImports1059] As DbSet(Of QueEventImport1059)
    '    Public Property [OnboardingSelectedServicess] As DbSet(Of OnboardingSelectedServices)
    '    Public Property [ACA2016_Controls] As DbSet(Of ACA2016_Control)
    '    Public Property [HRUserTypess] As DbSet(Of HRUserTypes)
    '    Public Property [PayrollFundingRequests] As DbSet(Of PayrollFundingRequest)
    '    Public Property [Weeks1059] As DbSet(Of Week1059)
    '    Public Property [QueConnection1059Logs] As DbSet(Of QueConnectionLogs1059)
    '    Public Property [CovidReEligEmailNotifys] As DbSet(Of CovidReEligEmailNotify)
    Public Property [Voids] As DbSet(Of Void)
    '    Public Property [NYS45Combined_Zs] As DbSet(Of NYS45Combined_Z)
    '    Public Property [LogProcedureNotes] As DbSet(Of LogProcedureNote)
    '    Public Property [BrandsAuthUserLiveReportss] As DbSet(Of BrandsAuthUserLiveReports)
    '    Public Property [CompanyPayrollStatss] As DbSet(Of CompanyPayrollStats)
    Public Property [_1059Measures] As DbSet(Of _1059Measure)
    '    Public Property [NYS45Combined_Es] As DbSet(Of NYS45Combined_E)
    Public Property [CustomReportLayouts] As DbSet(Of CustomReportLayout)
    Public Property [SqlScripts] As DbSet(Of SqlScript)
    '    Public Property [SqlScriptsOptions] As DbSet(Of SqlScriptsOption)
    '    Public Property [CompletedQbReportss] As DbSet(Of CompletedQbReports)
    Public Property [pr_batch_in_processes] As DbSet(Of pr_batch_in_process)
    '    Public Property [QueConnectionLogs1059s] As DbSet(Of QueConnectionLogs1059)
    '    Public Property [Queues] As DbSet(Of Queue)
    '    Public Property [MeasureCounter1059s] As DbSet(Of MeasureCounter1059)
    Public Property [ReportMassEmailPdfs] As DbSet(Of ReportMassEmailPdf)
    '    Public Property [ReportEmailTemplatesCompanys] As DbSet(Of ReportEmailTemplatesCompany)
    '    Public Property [CPAFirmss] As DbSet(Of CPAFirms)
    Public Property [Faxes] As DbSet(Of Fax)
    Public Property [FrontDeskFeedbacks] As DbSet(Of FrontDeskFeedback)
    '    Public Property [BrandsAuthClaimOverridesByCompanys] As DbSet(Of BrandsAuthClaimOverridesByCompany)
    '    Public Property [NYS45Combined_Vs] As DbSet(Of NYS45Combined_V)
    '    Public Property [ReportEmailTeplate_bkup20241105s] As DbSet(Of ReportEmailTeplate_bkup20241105)
    '    Public Property [CPAFirmsCompaniess] As DbSet(Of CPAFirmsCompanies)
    '    Public Property [HRDocumentss] As DbSet(Of HRDocuments)
    '    Public Property [PhoneData1059s] As DbSet(Of PhoneData1059)
    '    Public Property [PlaidTokenss] As DbSet(Of PlaidTokens)
    Public Property [ReportMassEmailTemplates] As DbSet(Of ReportMassEmailTemplate)
    Public Property [CoOptions_Payrolls] As DbSet(Of CoOptions_Payroll)
    'Public Property [pr_batch_overrides_dels] As DbSet(Of pr_batch_overrides_del)
    '    Public Property [NYS45_As] As DbSet(Of NYS45_A)
    '    Public Property [Meetings1059s] As DbSet(Of Meetings1059)
    '    Public Property [BrandsAuthUserCPAFirmss] As DbSet(Of BrandsAuthUserCPAFirms)
    Public Property [pr_batch_overrides] As DbSet(Of pr_batch_override)
    '    Public Property [EmployeeLogins] As DbSet(Of EmployeeLogin)
    '    Public Property [TrCh_Employees] As DbSet(Of TrCh_Employee)
    '    Public Property [NYS45_Bs] As DbSet(Of NYS45_B)
    '    Public Property [SSAW2_REs] As DbSet(Of SSAW2_RE)
    Public Property [CommPlanDetails] As DbSet(Of CommPlanDetail)
    '    Public Property [FD_GridControlColumnss] As DbSet(Of FD_GridControlColumns)
    '    Public Property [CoOptions_Exports_Ts] As DbSet(Of CoOptions_Export)
    '    Public Property [CoMonitors] As DbSet(Of CoMonitor)
    '    Public Property [PP_ImportErrors] As DbSet(Of PP_ImportError)
    '    Public Property [OnboardingEmployeeDocumentss] As DbSet(Of OnboardingEmployeeDocuments)
    Public Property [pr_batch_override_autos] As DbSet(Of pr_batch_override_auto)
    '    Public Property [Analytics_GlobalLists] As DbSet(Of Analytics_GlobalList)
    '    Public Property [NYS45_Cs] As DbSet(Of NYS45_C)
    Public Property [pr_batch_overrides_setups] As DbSet(Of pr_batch_overrides_setup)
    '    Public Property [SavedCheckDatess] As DbSet(Of SavedCheckDates)
    '    Public Property [ZendeskGroupss] As DbSet(Of ZendeskGroups)
    Public Property [pr_batch_notes] As DbSet(Of pr_batch_note)
    '    Public Property [Tax1099s] As DbSet(Of Tax1099)
    '    Public Property [BrandsAuthUserCPAFirmsCompaniess] As DbSet(Of BrandsAuthUserCPAFirmsCompanies)
    '    Public Property [SCHFED_FORMS_T_deleted_since_Dec_20_20s] As DbSet(Of SCHFED_FORMS_T_deleted_since_Dec_20_20)
    '    Public Property [PP_ImportErrorLines] As DbSet(Of PP_ImportErrorLine)
    '    Public Property [NYS45_Es] As DbSet(Of NYS45_E)
    Public Property _1059punches As DbSet(Of _1059punch)
    '    Public Property [OtherStatesFilingControl] As DbSet(Of OtherStatesFilingControl)
    '    Public Property [pr_batch_rows2s] As DbSet(Of pr_batch_rows2)
    '    Public Property [Analytics_User_Settingss] As DbSet(Of Analytics_User_Settings)
    '    Public Property [SSAW2FilingControls] As DbSet(Of SSAW2FilingControl)
    '    Public Property [Messagings] As DbSet(Of Messaging)
    '    Public Property [Zendesk_OrganizationsZDs] As DbSet(Of Zendesk_OrganizationsZD)
    '    Public Property [EP_Form941RecalculateEligibleWagesByCheckHistorys] As DbSet(Of EP_Form941RecalculateEligibleWagesByCheckHistory)
    Public Property [PayrollAlerts] As DbSet(Of PayrollAlert)
    '    Public Property [NYS45_Ds] As DbSet(Of NYS45_D)
    '    Public Property [ImportEmployeesScripts] As DbSet(Of ImportEmployeesScripts)
    '    Public Property [Tickets_Lockeds] As DbSet(Of Tickets_Locked)
    '    Public Property [ApiLogItemss] As DbSet(Of ApiLogItems)
    '    Public Property [TaxReconTransition_sb_register_MidFeb2018s] As DbSet(Of TaxReconTransition_sb_register_MidFeb2018)
    '    Public Property [co_contacts900_ts] As DbSet(Of co_contacts900_t)
    Public Property _1059MeasureEmpSetups As DbSet(Of _1059MeasureEmpSetup)
    Public Property [CommCompanySetups] As DbSet(Of CommCompanySetup)
    '    Public Property [QboConnectionss] As DbSet(Of QboConnections)
    '    Public Property [Zendesk_Organizationss] As DbSet(Of Zendesk_Organizations)
    '    Public Property [pr_batch_rows_inserts] As DbSet(Of pr_batch_rows_insert)
    '    Public Property [ManualCheckMasterExtensions] As DbSet(Of ManualCheckMasterExtension)
    Public Property [ExportFormats] As DbSet(Of ExportFormat)
    '    Public Property [NJWR30_Ms] As DbSet(Of NJWR30_M)
    Public Property [DD_Reversal_Logs] As DbSet(Of DD_Reversal_Log)
    '    Public Property [testLocks] As DbSet(Of testLock)
    Public Property [SuppliesOrders] As DbSet(Of SuppliesOrder)
    '    Public Property [TaxReconTransition_NACHA_TABLE_Update_MidFeb2018s] As DbSet(Of TaxReconTransition_NACHA_TABLE_Update_MidFeb2018)
    '    Public Property [Zendesk_OrganizationsHists] As DbSet(Of Zendesk_OrganizationsHist)
    '    Public Property [EssEmailNotifys] As DbSet(Of EssEmailNotify)
    '    Public Property [OtherStatesFilingConfigs] As DbSet(Of OtherStatesFilingConfig)
    '    Public Property [EssAuthClaimss] As DbSet(Of EssAuthClaims)
    '    Public Property [NJWR30_Bs] As DbSet(Of NJWR30_B)
    Public Property [FaxCategories] As DbSet(Of FaxCategoryStruct)
    Public Property [ReportEmailedPasswords] As DbSet(Of ReportEmailedPassword)
    '    Public Property [OnboardingEssEmployeess] As DbSet(Of OnboardingEssEmployees)
    '    Public Property [QueueDetails] As DbSet(Of QueueDetail)
    '    Public Property [TaxReconTransition_sb_register_MidFeb2018_W_Running_Balances] As DbSet(Of TaxReconTransition_sb_register_MidFeb2018_W_Running_Balance)
    Public Property [CommPlans] As DbSet(Of CommPlan)
    Public Property [ImportEmployeesLoginTemplates] As DbSet(Of ImportEmployeesLoginTemplates)
    '    Public Property [ReportEmailTemplateParameters] As DbSet(Of ReportEmailTemplateParameter)
    '    Public Property [CustomReportColumns] As DbSet(Of CustomReportColumn)
    '    Public Property [EssAuthRoless] As DbSet(Of EssAuthRoles)
    '    Public Property [NYS45ATT_Ws] As DbSet(Of NYS45ATT_W)
    '    Public Property [CR_TempDataSessions] As DbSet(Of CR_TempDataSession)
    '    Public Property [QeControls] As DbSet(Of QeControl)
    '    Public Property [SqlScripts_Bkup_Oct282021s] As DbSet(Of SqlScripts_Bkup_Oct282021)
    '    Public Property [ImportEmployeesLoginTemplatesBkups] As DbSet(Of ImportEmployeesLoginTemplatesBkup)
    '    Public Property [OnboardingLocalEmployeeInfos] As DbSet(Of OnboardingLocalEmployeeInfo)
    '    'Public Property [_PrUtilityImportResultss] As DbSet(Of _PrUtilityImportResults)
    '    Public Property [Onboardings] As DbSet(Of Onboarding)
    '    Public Property [EssAuthUserss] As DbSet(Of EssAuthUsers)
    '    Public Property [NYS45ATT_Ts] As DbSet(Of NYS45ATT_T)
    Public Property [AccountLeads] As DbSet(Of AccountLead)
    '    Public Property [QueuePayrolls] As DbSet(Of QueuePayroll)
    '    Public Property [TechProjectss] As DbSet(Of TechProjects)
    '    Public Property [NYS45FilingControls] As DbSet(Of NYS45FilingControl)
    '    Public Property [InvoiceIncreaseToApproveV11059] As DbSet(Of InvoiceIncreaseToApproveV11059)
    Public Property [BankingFileUploads] As DbSet(Of BankingFileUpload)
    '    Public Property [PaydeckServices] As DbSet(Of PaydeckService)
    '    Public Property [EssAuthRoleClaimss] As DbSet(Of EssAuthRoleClaims)
    '    Public Property [NYS45ATT_Fs] As DbSet(Of NYS45ATT_F)
    Public Property [SqlScripts_Logs] As DbSet(Of SqlScripts_Log)
    Public Property [ACA_ServiceOptOutLogs] As DbSet(Of ACA_ServiceOptOutLog)
    '    Public Property [TempQueueCancels] As DbSet(Of TempQueueCancel)
    '    Public Property [QueueReports] As DbSet(Of QueueReport)
    Public Property [PayrollReopenedNotes] As DbSet(Of PayrollReopenedNote)
    '    Public Property [PPI_Emp_Templates] As DbSet(Of PPI_Emp_Template)
    '    Public Property [RiskManagementAlerts] As DbSet(Of RiskManagementAlert)
    Public Property [ReportEPParamMaps] As DbSet(Of ReportEPParamMap)
    '    Public Property [PAYROLL_FILEARCHIVE_T_deleted_since_Dec_20_20s] As DbSet(Of PAYROLL_FILEARCHIVE_T_deleted_since_Dec_20_20)
    '    Public Property [NYS45ATT_Es] As DbSet(Of NYS45ATT_E)
    '    Public Property [CHECK_NUMBERS_BACKUPs] As DbSet(Of CHECK_NUMBERS_BACKUP)
    Public Property [Act_Items_Details] As DbSet(Of Act_Items_Detail)
    '    Public Property [CHECK_NUMBERS_BACKUP_RVSDs] As DbSet(Of CHECK_NUMBERS_BACKUP_RVSD)
    '    Public Property [State_Reciprocity_Lists] As DbSet(Of State_Reciprocity_List)
    Public Property [CustomReportLayoutShares] As DbSet(Of CustomReportLayoutShare)
    '    Public Property [EssAuthUserClaimss] As DbSet(Of EssAuthUserClaims)
    '    Public Property [tb_profile_output_copy_companys] As DbSet(Of tb_profile_output_copy_company)
    '    Public Property [NYS45ATT_As] As DbSet(Of NYS45ATT_A)
    '    Public Property [NJ927SuiControls] As DbSet(Of NJ927SuiControl)
    Public Property [BankTaxPmtCtrls] As DbSet(Of BankTaxPmtCtrl)
    Public Property [Supplies] As DbSet(Of Supply)
    '    Public Property [ObcVoidedCheckss] As DbSet(Of ObcVoidedChecks)
    '    Public Property [OnboardingDropDowns] As DbSet(Of OnboardingDropDown)
    '    'Public Property [NJ927_Ps] As DbSet(Of NJ927_P)
    '    Public Property [CR_TempDataPayrollDeds1s] As DbSet(Of CR_TempDataPayrollDeds1)
    '    Public Property [AODBalanceImports] As DbSet(Of AODBalanceImport)
    '    Public Property [LocalsW2FilingControls] As DbSet(Of LocalsW2FilingControl)
    '    Public Property [OnboardingStagess] As DbSet(Of OnboardingStages)
    '    Public Property [PlaidWebHooks] As DbSet(Of PlaidWebHook)
    '    Public Property [ApiReqRespJsons] As DbSet(Of ApiReqRespJson)
    Public Property [GridLayouts] As DbSet(Of GridLayout)
    Public Property [UnionDepartments] As DbSet(Of UnionDepartment)
    '    Public Property [ServiceStates] As DbSet(Of ServiceState)
    '    Public Property [AutoPrintJobs] As DbSet(Of AutoPrintJob)
    Public Property [AuditLogs] As DbSet(Of AuditLog)
    Public Property [SuppliesOrderItems] As DbSet(Of SuppliesOrderItem)
    '    Public Property [ACA2016_Control_BackupDefs] As DbSet(Of ACA2016_Control_BackupDef)
    Public Property [FrontDeskPermissions] As DbSet(Of FrontDeskPermission)
    '    Public Property [Tax940s] As DbSet(Of Tax940)
    '    Public Property [AuditTabless] As DbSet(Of AuditTables)
    '    Public Property [ReportEmailTemplateCategorys] As DbSet(Of ReportEmailTemplateCategory)
    '    Public Property [EssAuthUserLoginss] As DbSet(Of EssAuthUserLogins)
    '    Public Property [NJ927_Zs] As DbSet(Of NJ927_Z)
    Public Property [ReportQueues] As DbSet(Of ReportQueue)
    '    Public Property [CDL_ExcludedZipCodess] As DbSet(Of CDL_ExcludedZipCodes)
    '    Public Property [EssAuthUserEventss] As DbSet(Of EssAuthUserEvents)
    '    Public Property [CheckPayrollIssuesLogs] As DbSet(Of CheckPayrollIssuesLog)
    '    Public Property [EnrolledTPALists] As DbSet(Of EnrolledTPAList)
    '    Public Property [SwipeClockAccrBalanceImports] As DbSet(Of SwipeClockAccrBalanceImport)
    '    Public Property [Tax941s] As DbSet(Of Tax941)
    '    Public Property [PaydeckServiceClientSetups] As DbSet(Of PaydeckServiceClientSetup)
    '    Public Property [NACHAs] As DbSet(Of NACHA)
    '    Public Property [InvoiceCreditsPendingLogs] As DbSet(Of InvoiceCreditsPendingLog)
    '    Public Property [CR_TempDataEmpPaysChkYtds] As DbSet(Of CR_TempDataEmpPaysChkYtd)
    '    Public Property [ImportPowerGridsMappingss] As DbSet(Of ImportPowerGridsMappings)
    '    Public Property [EssAuthUserRoless] As DbSet(Of EssAuthUserRoles)
    '    Public Property [BrandsAuthUserEssEmployeess] As DbSet(Of BrandsAuthUserEssEmployees)
    Public Property [RenumberEmployees] As DbSet(Of RenumberEmployee)
    Public Property [CoOptions_SecondCheckPayCodes] As DbSet(Of CoOptions_SecondCheckPayCode)
    '    Public Property [CR_TempDataPayrollTaxes1s] As DbSet(Of CR_TempDataPayrollTaxes1)
    '    Public Property [BrandsAuthClaims] As DbSet(Of BrandsAuthClaim)
    '    Public Property [EmailOutboundZDs] As DbSet(Of EmailOutboundZD)
    '    Public Property [CR_TempDataEmpPaysChkRegs] As DbSet(Of CR_TempDataEmpPaysChkReg)
    '    Public Property [EssAuthUserTokenss] As DbSet(Of EssAuthUserTokens)
    '    Public Property [ACA_FORMS_Ts] As DbSet(Of ACA_FORMS)
    '    Public Property [FDUserLogss] As DbSet(Of FDUserLogs)
    '    Public Property [QueueEmails] As DbSet(Of QueueEmail)
    Public Property [BankingFileHolds] As DbSet(Of BankingFileHold)
    '    Public Property [NewSqlObjectsWhPermissions] As DbSet(Of NewSqlObjectsWhPermission)
    '    Public Property [EssEmployeeDDAccountss] As DbSet(Of EssEmployeeDDAccounts)
    Public Property [CustomReportColumnTemplates] As DbSet(Of CustomReportColumnTemplate)
    Public Property [FrontDeskOptions] As DbSet(Of FrontDeskOption)
    Public Property [QtrReportsToProcessAndSaves] As DbSet(Of QtrReportsToProcessAndSave)
    Public Property [ACA_File_Batch_Company_Ts] As DbSet(Of ACA_File_Batch_Company_T)
    '    Public Property [LocalsW2FilingControlNews] As DbSet(Of LocalsW2FilingControlNew)
    '    Public Property [AuditStatss] As DbSet(Of AuditStats)
    '    Public Property [CheckMasterVirtualLiness] As DbSet(Of CheckMasterVirtualLines)
    '    Public Property [QueueProcessors] As DbSet(Of QueueProcessor)
    '    Public Property [AODBalances] As DbSet(Of AODBalance)
    '    Public Property [EssEmployeess] As DbSet(Of EssEmployees)
    '    Public Property [PaydeckImpersonates] As DbSet(Of PaydeckImpersonate)
    '    Public Property [ResolvedPayrollIssuess] As DbSet(Of ResolvedPayrollIssues)
    Public Property [TaxCarryFwdSheets] As DbSet(Of TaxCarryFwdSheet)
    '    Public Property [PayrollsInProcessMsgTypess] As DbSet(Of PayrollsInProcessMsgTypes)
    '    Public Property [Tooltipss] As DbSet(Of Tooltips)
    '    Public Property [PayrollEmployeeFilterss] As DbSet(Of PayrollEmployeeFilters)
    '    Public Property [test2s] As DbSet(Of test2)
    '    Public Property [CheckLineExtensions] As DbSet(Of CheckLineExtension)
    '    Public Property [CR_TempDataRegisterDedsAs] As DbSet(Of CR_TempDataRegisterDedsA)
    '    Public Property [TaxServiceSwitchs] As DbSet(Of TaxServiceSwitch)
    '    Public Property [OnboardingEmployeeI9Documentss] As DbSet(Of OnboardingEmployeeI9Documents)
    Public Property [QtrReportsToProcessAndSaveMappings] As DbSet(Of QtrReportsToProcessAndSaveMapping)
    '    Public Property [EmployeePayScheduless] As DbSet(Of EmployeePaySchedules)
    '    Public Property [PaydeckPayrollProcessStepss] As DbSet(Of PaydeckPayrollProcessSteps)
    Public Property [pr_batch_employee_changes] As DbSet(Of pr_batch_employee_change)
    '    Public Property [DdMessup02062024s] As DbSet(Of DdMessup02062024)
    '    Public Property [EssEmailTemplatess] As DbSet(Of EssEmailTemplates)
    Public Property [W2ErSponsHlths] As DbSet(Of W2ErSponsHlth)
    '    Public Property [CR_TempDataEmpPaysChks] As DbSet(Of CR_TempDataEmpPaysChk)
    '    Public Property [test3s] As DbSet(Of test3)
    '    Public Property [CustomReportSchedules] As DbSet(Of CustomReportSchedule)
    Public Property [DivDepGroups] As DbSet(Of DivDepGroup)
    '    Public Property [CR_TempDataYtdRegTaxess] As DbSet(Of CR_TempDataYtdRegTaxes)
    '    Public Property [ReportOptionss] As DbSet(Of ReportOptions)
    '    Public Property [CompanyTerminations] As DbSet(Of CompanyTermination)
    '    Public Property [SwipeClockDatas] As DbSet(Of SwipeClockData)
    Public Property [pr_batch_hours_only] As DbSet(Of pr_batch_hours_only)
    '    Public Property [ReportScriptss] As DbSet(Of ReportScripts)
    '    Public Property [BW2_AccessOverrides] As DbSet(Of BW2_AccessOverride)
    Public Property [QuarterlyPreProcessedFiles] As DbSet(Of QuarterlyPreProcessedFile)
    Public Property [SqlScriptsExpressions] As DbSet(Of SqlScriptsExpression)
    '    Public Property [CR_TempDataEmpDDInfos] As DbSet(Of CR_TempDataEmpDDInfo)
    '    Public Property [EssHiddenPayrollss] As DbSet(Of EssHiddenPayrolls)
    Public Property [ServicesOffereds] As DbSet(Of ServicesOffered)
    '    Public Property [AccntMappingOrigs] As DbSet(Of AccntMappingOrig)
    '    Public Property [ProgramReleaseNotess] As DbSet(Of ProgramReleaseNotes)
    Public Property [ErrorLogs] As DbSet(Of ErrorLog)
    '    Public Property [OnboardingEmployeeI9DocumentTypess] As DbSet(Of OnboardingEmployeeI9DocumentTypes)
    '    Public Property [PayrollSubmmiteds] As DbSet(Of PayrollSubmmited)
    Public Property [CustomReports] As DbSet(Of CustomReport)
    '    Public Property [EssAuthUserEmployeess] As DbSet(Of EssAuthUserEmployees)
    Public Property [SqlScriptsParamAutoRuns] As DbSet(Of SqlScriptsParamAutoRun)
    '    Public Property [ScheduledSqlMaints] As DbSet(Of ScheduledSqlMaint)
    '    Public Property [CR_TempDataLast4QtrGrosss] As DbSet(Of CR_TempDataLast4QtrGross)
    '    Public Property [FD_ST_PortalLoginsCoNums] As DbSet(Of FD_ST_PortalLoginsCoNum)
    '    Public Property [PaydeckPosterElites] As DbSet(Of PaydeckPosterElite)
    Public Property [BankingManualTaxAdjCtrlDetails] As DbSet(Of BankingManualTaxAdjCtrlDetail)
    '    Public Property [UsePPxLibraryLogs] As DbSet(Of UsePPxLibraryLog)
    '    'Public Property [ACT_AUTO_PriceChangesLogs] As DbSet(Of ACT_AUTO_PriceChangesLog)
    '    Public Property [PlaidBalances] As DbSet(Of PlaidBalance)
    '    Public Property [CR_TempDataLastVacSicks] As DbSet(Of CR_TempDataLastVacSick)
    Public Property [ACA_File_Batch_Ts] As DbSet(Of ACA_File_Batch_T)
    Public Property [Accounts] As DbSet(Of Account)
    '    Public Property [OldXXX1059ZenDeskTicketDatas] As DbSet(Of OldXXX1059ZenDeskTicketData)
    '    Public Property [PaydeckServiceButtons] As DbSet(Of PaydeckServiceButton)
    '    Public Property [CheckMasterBatchs] As DbSet(Of CheckMasterBatch)
    '    Public Property [BankingManualTaxAdjCtrls] As DbSet(Of BankingManualTaxAdjCtrl)
    '    Public Property [PayrolStatusChangeds] As DbSet(Of PayrolStatusChanged)
    '    Public Property [CheckMasterBatchItemss] As DbSet(Of CheckMasterBatchItems)
    '    Public Property [FilingControlss] As DbSet(Of FilingControls)
    '    Public Property [HRLayoutss] As DbSet(Of HRLayouts)
    Public Property [NachaTaxDrafts] As DbSet(Of NachaTaxDraft)
    Public Property [CalendarNotes] As DbSet(Of CalendarNote)
    '    Public Property [ST_W2_Fillings] As DbSet(Of ST_W2_Filling)
    '    Public Property [OtherStatesFilingConfigYtds] As DbSet(Of OtherStatesFilingConfigYtd)
    '    Public Property [ReportFavoritess] As DbSet(Of ReportFavorites)
    '    Public Property [FD_GridControlColumnsSortOvers] As DbSet(Of FD_GridControlColumnsSortOver)
    '    Public Property [PaydeckServiceButtonSetting_Xs] As DbSet(Of PaydeckServiceButtonSetting_X)
    '    Public Property [BrandsAuthUserRememberedLoginss] As DbSet(Of BrandsAuthUserRememberedLogins)
    Public Property [ReportEmailTemplateFiles] As DbSet(Of ReportEmailTemplateFile)
    '    Public Property [UnionMatchRetros] As DbSet(Of UnionMatchRetro)
    '    Public Property [CPI_CallLogs] As DbSet(Of CPI_CallLog)
    '    Public Property [tmp_EP_Form941RecalculateEligibleWagesByChecks] As DbSet(Of tmp_EP_Form941RecalculateEligibleWagesByCheck)
    '    Public Property [HRDocumentTypess] As DbSet(Of HRDocumentTypes)
    '    Public Property [PriorPayrollDocumentss] As DbSet(Of PriorPayrollDocuments)
    Public Property [BrandsAuthLoginRequests] As DbSet(Of BrandsAuthLoginRequest)
    '    Public Property [OtherStatesFilingControlYtds] As DbSet(Of OtherStatesFilingControlYtd)
    Public Property [PaydeckBanners] As DbSet(Of PaydeckBanner)
    '    Public Property [junk_tables] As DbSet(Of junk_table)
    Public Property [AccntMappings] As DbSet(Of AccntMapping)
    '    Public Property [ReconFillingControls] As DbSet(Of ReconFillingControl)
    '    Public Property [ReportParameterHolderss] As DbSet(Of ReportParameterHolders)
    Public Property [DD_Manual_Trans_Logs] As DbSet(Of DD_Manual_Trans_Log)
    '    Public Property [tmp2s] As DbSet(Of tmp2)
    Public Property [DD_Reversal_Emails] As DbSet(Of DD_Reversal_Email)
    '    Public Property [ProcedureUsageLogs] As DbSet(Of ProcedureUsageLog)
    '    Public Property [SwipeClockAccrBalances] As DbSet(Of SwipeClockAccrBalance)
    '    Public Property [pr_batch_parent_note_ts] As DbSet(Of pr_batch_parent_note)
    '    Public Property [ReportParameterValuess] As DbSet(Of ReportParameterValues)
    '    Public Property [W2ErSponsHlth1514s] As DbSet(Of W2ErSponsHlth1514)
    '    Public Property [junk_table2s] As DbSet(Of junk_table2)
    Public Property [BrandsAuthUsers] As DbSet(Of BrandsAuthUser)
    Public Property [CustomReportTables] As DbSet(Of CustomReportTable)
    '    Public Property [DD_Reversal_Log1514s] As DbSet(Of DD_Reversal_Log1514)
    '    Public Property [EmailAttachments_Tables] As DbSet(Of EmailAttachments_Table)
    '    Public Property [FinalReturnss] As DbSet(Of FinalReturns)
    '    Public Property [ReportEmailTemplateOverrides] As DbSet(Of ReportEmailTemplateOverride)
    Public Property [TicketNotes] As DbSet(Of TicketNote)
    '    Public Property [AI_FormRecognitionJsons] As DbSet(Of AI_FormRecognitionJson)
    '    Public Property [CustomReportLayoutSharePaydecks] As DbSet(Of CustomReportLayoutSharePaydeck)
    '    Public Property [EmpDDPriorTerminations] As DbSet(Of EmpDDPriorTermination)
    '    Public Property [CoContactsExtensions] As DbSet(Of CoContactsExtension)
    '    Public Property [PayrollReopeneds] As DbSet(Of PayrollReopened)
    Public Property [DD_Reversal_Log_Queues] As DbSet(Of DD_Reversal_Log_Queue)
    '    Public Property [ACA_Evens_InsertedBy_BRANDSs] As DbSet(Of ACA_Evens_InsertedBy_BRANDS)
    '    Public Property [BankingManualTaxAdjCtrlPayments] As DbSet(Of BankingManualTaxAdjCtrlPayment)
    Public Property [UDFs] As DbSet(Of UDF)
    '    Public Property [StateAbbrevs] As DbSet(Of StateAbbrev)
    '    Public Property [CompanyCreateds] As DbSet(Of CompanyCreated)
    '    Public Property [BrandsAuthUserEmployeeSqlUserss] As DbSet(Of BrandsAuthUserEmployeeSqlUsers)
    '    Public Property [CaptureDebugs] As DbSet(Of CaptureDebug)
    '    Public Property [HRRestrictedEmpss] As DbSet(Of HRRestrictedEmps)
    '    Public Property [ACH_Capital_One_Returnss] As DbSet(Of ACH_Capital_One_Returns)
    '    Public Property [RowNums] As DbSet(Of RowNum)
    Public Property [FrontDeskRoles] As DbSet(Of FrontDeskRole)
    Public Property [Tickets] As DbSet(Of Ticket)
    '    Public Property [OnboardingAdditionalServicess] As DbSet(Of OnboardingAdditionalServices)
    '    Public Property [PaydeckServiceGroups] As DbSet(Of PaydeckServiceGroup)
    Public Property [QeHoldLogs] As DbSet(Of QeHoldLog)
    '    Public Property [FD_ST_PortalLoginss] As DbSet(Of FD_ST_PortalLogins)
    '    Public Property [CoOptions_Exports_Fee_Ts] As DbSet(Of CoOptions_Exports_Fee_T)
    Public Property [FrontDeskRoleUsers] As DbSet(Of FrontDeskRoleUser)
    Public Property [AchTransactionDailyLogs] As DbSet(Of AchTransactionDailyLog)
    '    Public Property [OutageEmailDec_2020s] As DbSet(Of OutageEmailDec_2020)
    Public Property [EmailOutbounds] As DbSet(Of EmailOutbound)
    Public Property [EmpIncentives] As DbSet(Of EmpIncentive)
    '    Public Property [RiskManageCaptures] As DbSet(Of RiskManageCapture)
    '    Public Property [PaydeckPayrollExtensions] As DbSet(Of PaydeckPayrollExtension)
    Public Property [Deliveries] As DbSet(Of Delivery)
    '    Public Property [PaydeckServiceFeaturess] As DbSet(Of PaydeckServiceFeatures)
    '    Public Property [HRDatas] As DbSet(Of HRData)
    '    Public Property [ImportFieldSettingss] As DbSet(Of ImportFieldSettings)
    '    Public Property [FilingControl941s] As DbSet(Of FilingControl941)
    '    Public Property [ImportPowerGridsMappingDetailss] As DbSet(Of ImportPowerGridsMappingDetails)
    '    Public Property [CaptureLoginss] As DbSet(Of CaptureLogins)
    '    Public Property [Emails_Copys] As DbSet(Of Emails_Copy)
    '    Public Property [MtaFilingControls] As DbSet(Of MtaFilingControl)
    '    Public Property [PaydeckServicePricings] As DbSet(Of PaydeckServicePricing)
    Public Property [FrontDeskTickets] As DbSet(Of FrontDeskTicket)
    '    Public Property [CompanyPayrollSummaryReportss] As DbSet(Of CompanyPayrollSummaryReports)
    Public Property [PrintingScans] As DbSet(Of PrintingScan)
    '    Public Property [BrandsAuthRoleClaimss] As DbSet(Of BrandsAuthRoleClaims)
    Public Property [EmployeeOptions] As DbSet(Of EmployeeOption)
    Public Property [AchTransactionsLogs] As DbSet(Of AchTransactionsLog)
    Public Property [ReportMassEmailFiles] As DbSet(Of ReportMassEmailFile)
    Public Property [ReportEmailTeplates] As DbSet(Of ReportEmailTeplate)
    Public Property [ACT_AUTO_PriceOverrides] As DbSet(Of ACT_AUTO_PriceOverride)
    '    Public Property [BrandsAuthRoless] As DbSet(Of BrandsAuthRoles)
    '    Public Property [OnboardingEmployeess] As DbSet(Of OnboardingEmployees)
    '    Public Property [QueueToActivates] As DbSet(Of QueueToActivate)
    '    Public Property [TaxReconTransition_NACHA_TABLE_Update_Mid_Aug2018s] As DbSet(Of TaxReconTransition_NACHA_TABLE_Update_Mid_Aug2018)
    '    Public Property [MinimumWages] As DbSet(Of MinimumWage)
    '    Public Property [TaxReconTransition_sb_register_Mid_Aug2018s] As DbSet(Of TaxReconTransition_sb_register_Mid_Aug2018)
    '    Public Property [SpecialCharTreatments] As DbSet(Of SpecialCharTreatment)
    '    Public Property [FutaFilingControls] As DbSet(Of FutaFilingControl)
    '    Public Property [CheckMasterExtensions] As DbSet(Of CheckMasterExtension)
    '    Public Property [ServiceActivations] As DbSet(Of ServiceActivation)
    '    Public Property [TaxReconTransition_sb_register_Mid_Aug2018_W_Running_Balances] As DbSet(Of TaxReconTransition_sb_register_Mid_Aug2018_W_Running_Balance)
    '    Public Property [BrandsAuthUserClaimss] As DbSet(Of BrandsAuthUserClaims)
    '    Public Property [CalendarReuseds] As DbSet(Of CalendarReused)
    '    Public Property [BrandsAuthUserLoginss] As DbSet(Of BrandsAuthUserLogins)
    '    Public Property [InvoicesDownloadeds] As DbSet(Of InvoicesDownloaded)
    Public Property [_1059UsersRealtionships] As DbSet(Of _1059UsersRealtionship)
    '    Public Property [FrontDeskTicketAttachments] As DbSet(Of FrontDeskTicketAttachment)
    Public Property [SqlScriptsLogUses] As DbSet(Of SqlScriptsLogUse)
    '    Public Property [CoOptionsExportsSQLs] As DbSet(Of CoOptionsExportsSQL)
    '    Public Property [BrandsAuthUserRoless] As DbSet(Of BrandsAuthUserRoles)
    '    Public Property [ACA2016_ControlBkup20241222s] As DbSet(Of ACA2016_ControlBkup20241222)
    Public Property [SuppliesInventories] As DbSet(Of SuppliesInventory)
    '    Public Property [SecurityAlertss] As DbSet(Of SecurityAlerts)
    '    Public Property [StateFilingInfos] As DbSet(Of StateFilingInfo)
    '    Public Property [TempMatchs] As DbSet(Of TempMatch)
    '    Public Property [BrandsAuthUserTokenss] As DbSet(Of BrandsAuthUserTokens)
    '    Public Property [Fix941CheckDateLogs] As DbSet(Of Fix941CheckDateLog)
    Public Property [CovidEmailNotifies] As DbSet(Of CovidEmailNotify)
    '    Public Property [MultiUses1059] As DbSet(Of MultiUse1059)
    Public Property [CompanyActivityLogs] As DbSet(Of CompanyActivityLog)
    Public Property [InvoiceCreditsPendings] As DbSet(Of InvoiceCreditsPending)
    Public Property [BrandsAuthUserEmployees] As DbSet(Of BrandsAuthUserEmployee)
    '    Public Property [PayrollPaydeckLogs] As DbSet(Of PayrollPaydeckLog)
    '    Public Property [PaydeckServiceChangeRequesteds] As DbSet(Of PaydeckServiceChangeRequested)
    Public Property [CovidEmailNotifyFollowUps] As DbSet(Of CovidEmailNotifyFollowUp)
    Public Property [DeliveryTickets] As DbSet(Of DeliveryTicket)
    Public Property [Documentations] As DbSet(Of Documentation)
    Public Property [UnionRates] As DbSet(Of UnionRate)
    '    Public Property [SqlScriptsLogUseDetails] As DbSet(Of SqlScriptsLogUseDetail)
    '    Public Property [PlaidAuths] As DbSet(Of PlaidAuth)
    '    Public Property [FORMS_T1099] As DbSet(Of FORMS_T1099)
    '    Public Property [EmailOutbound_SecurityInserts] As DbSet(Of EmailOutbound_SecurityInsert)
    '    Public Property [ApiActivityLogs] As DbSet(Of ApiActivityLog)
    '    Public Property [OnboardingProgresss] As DbSet(Of OnboardingProgress)
    '    Public Property [HRColumnCaptions] As DbSet(Of HRColumnCaption)
    Public Property [ResourcesLinkss] As DbSet(Of ResourcesLinks)
    '    'Public Property [ACAUpdateLowCostLogs] As DbSet(Of ACAUpdateLowCostLog)
    Public Property [_1059pbxreport2s] As DbSet(Of _1059pbxreport2)

    '#End Region

    '#Region "DoubleDbo"
    Public Property COMPANies As DbSet(Of COMPANY)
    Public Property CoOptions_Exports As DbSet(Of CoOptions_Export)
    '    Public Property EMP_OP As DbSet(Of EMP_OPS)
    Public Property payroll_ext_ts As DbSet(Of payroll_ext)
    Public Property dbo_DELIVERies As DbSet(Of dbo_DELIVERY)
    Public Property view_PayrollInProcesses As DbSet(Of view_PayrollInProcess)
    Public Property Emails As DbSet(Of Email)
    '    Public Property view_QuarterlyShipAndEmailControl As DbSet(Of view_QuarterlyShipAndEmailControl)
    Public Property view_QuarterlyAllProcessAndDeliveries As DbSet(Of view_QuarterlyAllProcessAndDelivery)
    Public Property view_QuarterlyShipAndEmailControls As DbSet(Of view_QuarterlyShipAndEmailControl)
    Public Property view_PayrollAletsGroupeds As DbSet(Of view_PayrollAletsGrouped)
    Public Property SqlScriptsSavedDataXml As DbSet(Of SqlScriptsSavedDataXml)
    Public Property _1059PhoneDataComments As DbSet(Of _1059PhoneDataComment)

    '#End Region

    '#Region "views"
    Public Property DBUSERs As DbSet(Of DBUSER)
    Public Property TaxNotices As DbSet(Of TaxNotice)
    Public Property pr_batch_totals As DbSet(Of pr_batch_total)
    Public Property view_Calendars As DbSet(Of view_Calendar)
    Public Property view_CompanySumarries As DbSet(Of view_CompanySumarry)
    Public Property view_FaxAndEmails As DbSet(Of view_FaxAndEmail)
    Public Property REPORTS As DbSet(Of REPORT)
    Public Property SCHFED_FORMS As DbSet(Of SCHFED_FORM)
    Public Property view_REPORTS_UNIONs As DbSet(Of view_REPORTS_UNION)
    '    Public Property view_MinimumWageReq As DbSet(Of view_MinimumWageReq)
    Public Property view_Deliveries As DbSet(Of view_Delivery)
    Public Property view_CompanyActivityHistories As DbSet(Of view_CompanyActivityHistory)
    Public Property EMP_DEDS_SETUPs As DbSet(Of EMP_DEDS_SETUP)
    Public Property view_OrderItemInvoices As DbSet(Of view_OrderItemInvoice)
    Public Property view_AccountLeads As DbSet(Of view_AccountLead)
    Public Property document_storages As DbSet(Of document_storage)
    Public Property view_Reverse_DD_Transactions As DbSet(Of view_Reverse_DD_Transaction)
    Public Property view_CovidEmailNotifies As DbSet(Of view_CovidEmailNotify)
    Public Property view_pr_batch_in_processes As DbSet(Of view_pr_batch_in_process)
    Public Property view_BillingCredits As DbSet(Of view_BillingCredit)
    Public Property view_BankingManualTaxAdjCtrls As DbSet(Of view_BankingManualTaxAdjCtrl)
    Public Property view_BankingManualTaxAdjCtrlDetails As DbSet(Of view_BankingManualTaxAdjCtrlDetail)
    Public Property view_BankingManualTaxAdjCtrlPayments As DbSet(Of view_BankingManualTaxAdjCtrlPayment)
    Public Property view_ACT_ITEMs As DbSet(Of view_ACT_ITEM)
    Public Property EmailAttachments As DbSet(Of EmailAttachment)
    Public Property view_epsusers As DbSet(Of view_epsusers)
    Public Property view_AccntMappings As DbSet(Of view_AccntMapping)
    Public Property vMaxCkDates As DbSet(Of vMaxCkDate)
    Public Property view_PlaidTokens As DbSet(Of view_PlaidTokens)
    Public Property Manual_Trans_Account_lists As DbSet(Of Manual_Trans_Account_list)
    Public Property ExtractLogs As DbSet(Of ExtractLog)
    Public Property FtpSessions As DbSet(Of FtpSession)
    Public Property FileCategories As DbSet(Of FileCategory)

    '#End Region
    '    Public Property prc_GetAllRelatedContactsResult As DbSet(Of prc_GetAllRelatedContactsResult)
    Public Property prc_SelectPayrollToEditResult As DbSet(Of prc_SelectPayrollToEditResult)
    Public Property prc_PrUtilityImportResult As DbSet(Of prc_PrUtilityImportResult)
    Public Property prc_GetManualChecksResult As DbSet(Of prc_GetManualChecksResult)
    Public Property prc_GetVoidsToVoidResult As DbSet(Of prc_GetVoidsToVoidResult)
    Public Property ep_ReportPayrollTaxesResult As DbSet(Of ep_ReportPayrollTaxesResult)
    Public Property ep_ReportPayrollDedsResult As DbSet(Of ep_ReportPayrollDedsResult)
    '    Public Property prc_TaxReconListResult As DbSet(Of prc_TaxReconListResult)
    '    Public Property fn_FrontDeskUserRolesResult As DbSet(Of fn_FrontDeskUserRolesResult)
    '    Public Property prc_TaxReconDetailsResult As DbSet(Of prc_TaxReconDetailsResult)
    '    Public Property prc_GetAutoPaysForPayrollResult As DbSet(Of prc_GetAutoPaysForPayrollResult)
    '    Public Property prc_NyTaxOverdueResult As DbSet(Of prc_NyTaxOverdueResult)
    '    Public Property prc_QtrEndEmailResult As DbSet(Of prc_QtrEndEmailResult)
    '    Public Property prc_MinimumWageRequirementsResult As DbSet(Of prc_MinimumWageRequirementsResult)
    '    Public Property prc_GetDD_DraftsResult As DbSet(Of prc_GetDD_DraftsResult)
    '    Public Property fn_GetPayrollReportsResult As DbSet(Of fn_GetPayrollReportsResult)
    '    Public Property prc_PayrollsIncompleteResult As DbSet(Of prc_PayrollsIncompleteResult)
    '    Public Property prc_CheckPayrollIssuesResult As DbSet(Of prc_CheckPayrollIssuesResult)
    '    Public Property prc_YearEnd_WithNoDecPayrollResult As DbSet(Of prc_YearEnd_WithNoDecPayrollResult)
    '    Public Property prc_RptPrCoverSheetResult As DbSet(Of prc_RptPrCoverSheetResult)
    '    Public Property prc_RptActiveEmpsNotPaidResult As DbSet(Of prc_RptActiveEmpsNotPaidResult)
    '    Public Property prc_ReopenPayrollResult As DbSet(Of prc_ReopenPayrollResult)
    '    Public Property prc_GetPayrollsNotAttachedToCalendersResult As DbSet(Of prc_GetPayrollsNotAttachedToCalendersResult)
    '    Public Property prc_GetShippingAddressResult As DbSet(Of prc_GetShippingAddressResult)
    '    Public Property prc_ReportOptionsResult As DbSet(Of prc_ReportOptionsResult)
    '    Public Property prc_GetExportFormatResult As DbSet(Of prc_GetExportFormatResult)
    '    Public Property prc_GetCompanyServicesResult As DbSet(Of prc_GetCompanyServicesResult)
    Public Property StringResult As DbSet(Of StringResult)
    Public Property IntegerResult As DbSet(Of IntegerResult)
    Public Property prc_GetCoRankResult As DbSet(Of prc_GetCoRankResult)
    '    Public Property prc_InsTaxReconResult As DbSet(Of prc_InsTaxReconResult)
    '    Public Property prc_GetAllCompanyEmailsResult As DbSet(Of prc_GetAllCompanyEmailsResult)
    '    Public Property prc_GetBankAccountsResult As DbSet(Of prc_GetBankAccountsResult)
    Public Property prc_ExportFormatMarkResendResult As DbSet(Of prc_ExportFormatMarkResendResult)
    Public Property prc_GetCommItemsForComapnyResult As DbSet(Of prc_GetCommItemsForComapnyResult)
    '    Public Property prc_BrandsPowerGridColumnsResult As DbSet(Of prc_BrandsPowerGridColumnsResult)
    '    Public Property fn_NextScheduledPayrollResult As DbSet(Of fn_NextScheduledPayrollResult)
    '    Public Property prc_GetPrOpenBatch_Result As DbSet(Of prc_GetPrOpenBatch_Result)

    Public Sub SubmitChanges()
        ' Use the overridden SaveChanges method that handles concurrency conflicts
        ' Don't throw exception - let the SaveChanges method handle error display
        Me.SaveChanges()
    End Sub

    Private Function SaveChangesWithRetry() As Integer
        Dim maxRetries As Integer = 3
        Dim retryCount As Integer = 0

        ' Get the calling method for debugging
        Dim stackTrace = New System.Diagnostics.StackTrace()
        Dim callingMethod = stackTrace.GetFrame(1)?.GetMethod()?.Name
        modGlobals.Logger.Information($"SaveChangesWithRetry() started - called from: {callingMethod}")
        modGlobals.Logger.Information($"Exception handling is active and ready to catch DbUpdateConcurrencyException")

        ' Log the entities being changed for debugging
        Try
            Dim changeTracker = Me.ChangeTracker
            Dim changedEntries = changeTracker.Entries.Where(Function(e) e.State <> Microsoft.EntityFrameworkCore.EntityState.Unchanged).ToList()
            modGlobals.Logger.Information($"Changes detected: {changedEntries.Count} total changes")

            For Each entityEntry In changedEntries
                modGlobals.Logger.Information($"Entity: {entityEntry.Entity.GetType().Name}, State: {entityEntry.State}")
                If TypeOf entityEntry.Entity Is CALENDAR Then
                    Dim cal = DirectCast(entityEntry.Entity, CALENDAR)
                    modGlobals.Logger.Information($"  CALENDAR: cal_id={cal.cal_id}, conum={cal.conum}, period_id={cal.period_id}")
                    modGlobals.Logger.Information($"  Values: completed={cal.completed}, payroll_num={cal.payroll_num}")
                End If
            Next
        Catch ex As Exception
            modGlobals.Logger.Warning(ex, "Error logging change set details - continuing with save")
        End Try

        While retryCount < maxRetries
            Try
                modGlobals.Logger.Debug($"Attempting Entity Framework SaveChanges() on attempt {retryCount + 1}")
                ' Call the base Entity Framework SaveChanges method
                Dim changesCount As Integer
                Try
                    changesCount = MyBase.SaveChanges()
                Catch saveEx As Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException
                    modGlobals.Logger.Warning($"DbUpdateConcurrencyException caught directly: {saveEx.Message}")
                    Throw ' Re-throw to be handled by outer catch
                Catch saveEx As Exception
                    modGlobals.Logger.Warning($"Other exception caught during SaveChanges: {saveEx.GetType().FullName} - {saveEx.Message}")
                    Throw ' Re-throw to be handled by outer catch
                End Try
                modGlobals.Logger.Debug($"SaveChanges succeeded on attempt {retryCount + 1}, {changesCount} changes saved")
                Return changesCount
            Catch ex As Exception
                ' Log the exact exception type and details for debugging
                modGlobals.Logger.Warning($"Exception caught in SaveChanges (attempt {retryCount + 1}/{maxRetries})")
                modGlobals.Logger.Warning($"Exception Type: {ex.GetType().FullName}")
                modGlobals.Logger.Warning($"Exception Message: {ex.Message}")
                modGlobals.Logger.Warning($"Stack Trace: {ex.StackTrace}")

                ' Check if this is a concurrency exception (by type or message)
                Dim isConcurrencyException As Boolean = False

                ' Check by type first
                If TypeOf ex Is Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException Then
                    isConcurrencyException = True
                    modGlobals.Logger.Information("Detected DbUpdateConcurrencyException by type")
                ElseIf ex.Message.ToLower().Contains("concurrency") OrElse
                       ex.Message.ToLower().Contains("conflict") OrElse
                       ex.Message.ToLower().Contains("optimistic") OrElse
                       ex.Message.ToLower().Contains("modified") OrElse
                       ex.Message.ToLower().Contains("another user") OrElse
                       ex.Message.ToLower().Contains("row not found") OrElse
                       ex.Message.ToLower().Contains("row was modified") OrElse
                       ex.Message.ToLower().Contains("expected to affect 1 row") OrElse
                       ex.Message.ToLower().Contains("actually affected 0 row") OrElse
                       ex.Message.ToLower().Contains("data may have been modified") Then
                    isConcurrencyException = True
                    modGlobals.Logger.Information("Detected concurrency exception by message content")
                End If

                If isConcurrencyException Then
                    retryCount += 1
                    modGlobals.Logger.Warning($"Concurrency conflict detected (attempt {retryCount}/{maxRetries}): {ex.Message}")

                    ' Handle concurrency conflicts
                    Try
                        ' For DbUpdateConcurrencyException, handle the entries
                        If TypeOf ex Is Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException Then
                            Dim concurrencyEx = DirectCast(ex, Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException)
                            For Each conflictEntry In concurrencyEx.Entries
                                modGlobals.Logger.Information($"Resolving conflict for entity: {conflictEntry.Entity.GetType().Name}, State: {conflictEntry.State}")

                                Select Case conflictEntry.State
                                    Case EntityState.Modified
                                        ' For modified entities, use database values but keep current values (user wins strategy)
                                        modGlobals.Logger.Information("Handling modified entity - applying user wins strategy")
                                        Try
                                            ' Get current values before reload
                                            Dim currentValues = conflictEntry.CurrentValues.Clone()
                                            ' Reload to get latest database values and update concurrency tokens
                                            conflictEntry.Reload()
                                            ' Reapply user's changes (user wins)
                                            conflictEntry.CurrentValues.SetValues(currentValues)
                                        Catch reloadEx As Exception
                                            modGlobals.Logger.Warning(reloadEx, "Error during modified entity reload - entity may have been deleted")
                                            ' If entity was deleted, detach it
                                            conflictEntry.State = EntityState.Detached
                                        End Try

                                    Case EntityState.Deleted
                                        ' For deleted entities, check if still exists in database
                                        modGlobals.Logger.Information("Handling deleted entity - checking if still exists")
                                        Try
                                            conflictEntry.Reload()
                                            ' If reload succeeds, entity still exists, so delete it
                                            conflictEntry.State = EntityState.Deleted
                                            modGlobals.Logger.Information("Entity still exists - will retry delete")
                                        Catch reloadEx As Exception
                                            ' If reload fails, entity was already deleted by another user
                                            modGlobals.Logger.Information("Entity was already deleted by another user - detaching")
                                            conflictEntry.State = EntityState.Detached
                                        End Try

                                    Case EntityState.Added
                                        ' For added entities, just retry the add
                                        modGlobals.Logger.Information("Handling added entity - retrying add")
                                        ' No special handling needed, just retry

                                    Case Else
                                        modGlobals.Logger.Information($"Handling entity with state: {conflictEntry.State}")
                                        Try
                                            conflictEntry.Reload()
                                        Catch reloadEx As Exception
                                            modGlobals.Logger.Warning(reloadEx, $"Error reloading entity in state {conflictEntry.State}")
                                            conflictEntry.State = EntityState.Detached
                                        End Try
                                End Select
                            Next
                        Else
                            ' For other concurrency-related exceptions, just log and continue with retry
                            modGlobals.Logger.Information("Non-DbUpdateConcurrencyException detected - will retry without specific entity handling")
                        End If

                        modGlobals.Logger.Information("Concurrency conflicts resolved - retrying save")
                    Catch refreshEx As Exception
                        modGlobals.Logger.Warning(refreshEx, "Error resolving concurrency conflicts - continuing with retry")
                    End Try

                    If retryCount >= maxRetries Then
                        modGlobals.Logger.Error(ex, "Failed to resolve concurrency conflict after maximum retries")
                        modGlobals.Logger.Error($"Final attempt failed with exception: {ex.GetType().Name} - {ex.Message}")
                        modGlobals.DisplayErrorMessage("Unable to save changes due to a conflict with another user's changes. Please try again.", ex)
                        Return 0
                    End If

                    ' Wait a short time before retrying
                    Threading.Thread.Sleep(100 * retryCount)
                Else
                    ' Handle non-concurrency exceptions
                    modGlobals.Logger.Error(ex, $"Non-concurrency exception in SaveChanges (attempt {retryCount + 1}/{maxRetries}): {ex.GetType().Name} - {ex.Message}")
                    modGlobals.Logger.Error($"Stack trace: {ex.StackTrace}")
                    modGlobals.DisplayErrorMessage("An unexpected error occurred while saving changes. Please try again.", ex)
                    Return 0
                End If

            End Try
        End While

        modGlobals.Logger.Warning("SaveChanges failed after all retry attempts")
        modGlobals.Logger.Warning($"Final failure - no more retries available. Called from: {callingMethod}")
        Return 0
    End Function

    ''' <summary>
    ''' Boolean version of SaveChanges for backward compatibility with existing code that expects Boolean return
    ''' Returns True for successful saves (whether changes were made or not), False only for actual errors
    ''' </summary>
    Public Shadows Function SaveChanges() As Boolean
        ' Get the calling method for debugging
        Dim stackTrace = New System.Diagnostics.StackTrace()
        Dim callingMethod = stackTrace.GetFrame(1)?.GetMethod()?.Name
        Dim callingClass = stackTrace.GetFrame(1)?.GetMethod()?.DeclaringType?.Name
        modGlobals.Logger.Information($"Boolean SaveChanges() called from: {callingClass}.{callingMethod}")

        Dim result As Integer = Me.SaveChangesWithRetry()
        modGlobals.Logger.Information($"Boolean SaveChanges() returning: {result >= 0} (result was {result})")

        ' Return True for successful saves (>= 0), False only for actual errors (< 0)
        ' Note: 0 means no changes were needed, which is still a successful operation
        Return result >= 0
    End Function



    Public Class CustomResult
        Public Property Property1 As Integer
        Public Property Property2 As String
        ' Other properties...
    End Class

    Public Function prc_GetAllRelatedContacts(CoNum As Decimal, SearchByPhone As Boolean, SerachByCPA As Boolean, SearchByUdf22 As Boolean, ShowActive As Boolean) As List(Of prc_GetAllRelatedContactsResult)
        'Return prc_GetAllRelatedContactsResult.FromSqlRaw($"EXEC custom.prc_GetAllRelatedContacts {CoNum}, {SearchByPhone}, {SerachByCPA}, {SearchByUdf22}, {ShowActive}").ToList()
        Return ExecuteQuery(Of prc_GetAllRelatedContactsResult)($"EXEC custom.prc_GetAllRelatedContacts", CoNum, SearchByPhone, SerachByCPA, SearchByUdf22, ShowActive).ToList()
    End Function

    Public Function prc_SelectPayrollToEdit(CoNum As Decimal, PrNum As Decimal) As List(Of prc_SelectPayrollToEditResult)
        Return Query(Of prc_SelectPayrollToEditResult)($"EXEC custom.prc_SelectPayrollToEdit {CoNum}, {PrNum}").ToList()
    End Function

    Public Function ep_ReportPayrollTaxes(coNum As Decimal, prNums As String, empNum As Decimal, Optional chkCounter As Decimal = -2) As List(Of ep_ReportPayrollTaxesResult)
        Return Query(Of ep_ReportPayrollTaxesResult)($"EXEC custom.ep_ReportPayrollTaxes {coNum}, '{prNums}', {empNum}, {chkCounter}").ToList()
    End Function

    Public Function ep_ReportPayrollDeds(coNum As Decimal, prNums As String, empNum As Decimal) As List(Of ep_ReportPayrollDedsResult)
        Return Query(Of ep_ReportPayrollDedsResult)($"EXEC custom.ep_ReportPayrollDeds {coNum}, '{prNums}', {empNum}").ToList()
    End Function

    Public Function prc_GetManualChecks(CoNum As Decimal) As List(Of prc_GetManualChecksResult)
        Return Query(Of prc_GetManualChecksResult)($"EXEC custom.prc_GetManualChecks {CoNum}").ToList()
    End Function

    Public Function prc_GetVoidsToVoid(CoNum As Decimal, CheckNum As Decimal?) As List(Of prc_GetVoidsToVoidResult)
        Return Query(Of prc_GetVoidsToVoidResult)($"EXEC custom.prc_GetVoidsToVoid {CoNum}, {nz(CheckNum, "NULL")}").ToList()
    End Function

    Public Function prc_PrUtilityImport(ByVal conum As System.Nullable(Of Decimal), ByVal prNum As System.Nullable(Of Decimal), ByVal unionNames As String, ByVal ManEmpNum As System.Nullable(Of Decimal), ByVal ManChkCounter As System.Nullable(Of Decimal)) As List(Of prc_PrUtilityImportResult)
        Return Query(Of prc_PrUtilityImportResult)($"EXEC custom.prc_PrUtilityImport {conum}, {prNum}, {unionNames.QuoteSQLString()}, {ManEmpNum.QuoteSQL()},{ManChkCounter.QuoteSQL()}").ToList()
    End Function

    Public Function prc_AddVoidsToPayroll(coNum As Nullable(Of Decimal), prNum As Nullable(Of Decimal), curYearOnly As Nullable(Of Boolean)) As Integer
        ' Convert Boolean to an integer as most databases handle bits as integers
        Dim curYearOnlyInt As Integer = If(curYearOnly.HasValue AndAlso curYearOnly.Value, 1, 0)

        ' Execute the stored procedure
        Return Database.ExecuteSqlRaw(
            String.Format("EXEC custom.prc_AddVoidsToPayroll @CoNum = {0}, @PrNum = {1}, @CurYearOnly = {2}",
            coNum, prNum, curYearOnlyInt))
    End Function

    Public Function fn_ValidRoutingNumbers(field As String) As String
        Dim sqlQuery As String = $"SELECT custom.fn_ValidRoutingNumbers({field.QuoteSQLString()})"
        ' Execute the function as part of a query - expects a single result
        Dim result = Me.Database.ExecuteSqlRaw(sqlQuery)

        ' If your function is composable and part of a larger query, you may need to adapt this
        Return result
    End Function

    Public Function prc_IncludeManualChecksToPayroll(coNum As Decimal, prNum As Decimal, curYearOnly As Boolean, ByRef checkCount As Nullable(Of Integer)) As Integer
        ' Manage database connections
        Using connection As New SqlClient.SqlConnection
            connection.ConnectionString = Me.Database.GetConnectionString

            connection.Open()
            Using command As New SqlClient.SqlCommand("custom.prc_IncludeManualChecksToPayroll", connection)
                command.CommandType = CommandType.StoredProcedure

                ' Set up input parameters
                command.Parameters.Add("@CoNum", SqlDbType.Decimal).Value = coNum
                command.Parameters.Add("@PrNum", SqlDbType.Decimal).Value = prNum
                command.Parameters.Add("@EmpNum", SqlDbType.Decimal).Value = CObj(DBNull.Value)
                command.Parameters.Add("@ChkCounter", SqlDbType.Decimal).Value = CObj(DBNull.Value)
                command.Parameters.Add("@CurYearOnly", SqlDbType.Bit).Value = If(curYearOnly, 1, 0)

                ' Define output parameters
                Dim checkCountParameter = New SqlClient.SqlParameter("@CheckCount", SqlDbType.Int) With {.Direction = ParameterDirection.Output}
                command.Parameters.Add(checkCountParameter)

                Dim checkMasterChkCounterParameter = New SqlClient.SqlParameter("@CheckMasterChkCounter", SqlDbType.Decimal) With {.Direction = ParameterDirection.Output}
                command.Parameters.Add(checkMasterChkCounterParameter)

                ' Execute the command
                Dim result As Integer = command.ExecuteNonQuery()

                ' Get output parameter values
                checkCount = If(checkCountParameter.Value IsNot DBNull.Value, CType(checkCountParameter.Value, Integer?), 0)

                Return result
            End Using
        End Using
    End Function

    Public Function prc_TaxReconList(year As Integer, quarterNum As Integer) As List(Of prc_TaxReconListResult)
        Return Query(Of prc_TaxReconListResult)($"EXEC custom.prc_TaxReconList {year}, {quarterNum}").ToList()
    End Function

    Public Function prc_Calendar(fromDate As DateTime, toDate As DateTime, Optional entryTypes As String = Nothing, Optional opOwner As String = Nothing, Optional excludeJ As Nullable(Of Boolean) = Nothing, Optional includeJ As Nullable(Of Boolean) = Nothing, Optional output As String = "default") As List(Of view_Calendar)
        Dim sql As String = $"EXEC custom.prc_Calendar '{fromDate}', '{toDate}', '{entryTypes}', '{opOwner}', {If(excludeJ.HasValue, Convert.ToInt32(excludeJ.Value), "NULL")}, {If(includeJ.HasValue, Convert.ToInt32(includeJ.Value), "NULL")}, '{output}'"
        'Return Me.Set(Of view_Calendar)().FromSqlRaw(sql).ToList()
        Return Query(Of view_Calendar)(sql).ToList()
    End Function

    Public Function fn_FrontDeskUserRoles(userName As String) As List(Of fn_FrontDeskUserRolesResult)
        'Return Me.Set(Of fn_FrontDeskUserRolesResult)().FromSqlRaw($"SELECT RoleID, RoleName, IsMember, Category FROM custom.fn_FrontDeskUserRoles({userName})").ToList()
        Return ExecuteQuery(Of fn_FrontDeskUserRolesResult)($"SELECT RoleID, RoleName, IsMember, Category FROM custom.fn_FrontDeskUserRoles('{userName}')").ToList()
    End Function

    Public Function prc_TaxReconDetails(coNum As Decimal, year As Integer, quarterNum As Integer) As List(Of prc_TaxReconDetailsResult)
        Return Query(Of prc_TaxReconDetailsResult)($"EXEC custom.prc_TaxReconDetails {coNum}, {year}, {quarterNum}").ToList()
    End Function

    Public Function prc_GetAutoPaysForPayroll(coNum As Decimal, prNum As Decimal, Optional employeeID As Nullable(Of Decimal) = Nothing, Optional returnUnscheduled As Boolean = False, Optional allScheduled As Boolean = False) As List(Of prc_GetAutoPaysForPayrollResult)
        Dim sql As String = $"EXEC custom.prc_GetAutoPaysForPayroll {coNum}, {prNum}, {If(employeeID.HasValue, employeeID, "NULL")}, {Convert.ToInt32(returnUnscheduled)}, {Convert.ToInt32(allScheduled)}"
        Return Query(Of prc_GetAutoPaysForPayrollResult)(sql).ToList()
    End Function

    Public Function prc_NyTaxOverdue(coNum As Decimal, checkDate As Date) As List(Of prc_NyTaxOverdueResult)
        Dim sql As String = $"EXEC custom.prc_NyTaxOverdue {coNum}, '{checkDate:yyyy-MM-dd}'"
        'Return Me.Set(Of prc_NyTaxOverdueResult)().FromSqlRaw(sql).ToList()
        Return Query(Of prc_NyTaxOverdueResult)(sql).ToList()
    End Function

    Public Function prc_QtrEndEmail([date] As Nullable(Of Date), coNum As Nullable(Of Decimal), execType As String) As List(Of prc_QtrEndEmailResult)
        Dim sql As String = $"EXEC custom.prc_QtrEndEmail {If([date].HasValue, $"'{[date]:yyyy-MM-dd HH:mm:ss}'", "NULL")}, {If(coNum.HasValue, coNum.ToString(), "NULL")}, {If(String.IsNullOrEmpty(execType), "NULL", $"'{execType}'")}"
        Return Query(Of prc_QtrEndEmailResult)(sql).ToList()
    End Function

    Public Function prc_1059ScoreCardXml(ByVal dateFrom As DateTime, ByVal dateTo As DateTime, ByVal empNum As String, Optional ByVal measureId As Integer? = Nothing) As List(Of prc_1059ScoreCardXmlResult)
        Return Query(Of prc_1059ScoreCardXmlResult)($"EXEC custom.prc_1059ScoreCardXml @DateFrom = {dateFrom.ToString().QuoteSQLString()}, @DateTo = {dateTo.ToString().QuoteSQLString()}, @EmpNum = {empNum}, @MeasureId = {(If(measureId.HasValue, measureId.Value.ToString(), "NULL"))}").ToList()
    End Function

    Public Function prc_MinimumWageRequirements(ByVal coNum As Decimal, ByVal groupBy As String, ByVal noEmailOnly As String, Optional ByVal fromCoNum As Decimal? = 0, Optional ByVal toCoNum As Decimal? = 0) As List(Of prc_MinimumWageRequirementsResult)
        Return Query(Of prc_MinimumWageRequirementsResult)($"EXEC custom.prc_MinimumWageRequirements @CoNum = {coNum}, @GroupBy = '{groupBy}', @NoEmailOnly = '{noEmailOnly}', @__FromCoNum = {(If(fromCoNum.HasValue, fromCoNum.Value.ToString(), "0"))}, @__ToCoNum = {(If(toCoNum.HasValue, toCoNum.Value.ToString(), "0"))}").ToList()
    End Function

    Public Function prc_GetDD_Drafts(ByVal coNum As Nullable(Of Decimal), ByVal amount As Nullable(Of Decimal), ByVal fromDate As DateTime, ByVal toDate As DateTime, Optional ByVal routing As String = Nothing, Optional ByVal accountNumber As String = Nothing) As List(Of prc_GetDD_DraftsResult)
        Return Query(Of prc_GetDD_DraftsResult)($"EXEC custom.prc_GetDD_Drafts @CoNum = {(If(coNum.HasValue, coNum.Value.ToString(), "NULL"))}, @Amount = {(If(amount.HasValue, amount.Value.ToString(), "NULL"))}, @FromDate = '{fromDate}', @ToDate = '{toDate}', @Routing = {(If(routing IsNot Nothing, $"'{routing}'", "NULL"))}, @AccountNumber = {(If(accountNumber IsNot Nothing, $"'{accountNumber}'", "NULL"))}").ToList()
    End Function

    Public Function fn_GetPayrollReports(ByVal coNum As Decimal, ByVal fromDate As Date, ByVal toDate As Date) As List(Of fn_GetPayrollReportsResult)
        'Return Me.Set(Of fn_GetPayrollReportsResult)().FromSqlRaw($"SELECT * FROM custom.fn_GetPayrollReports @Conum = {coNum}, @FromDate = '{fromDate}', @ToDate = '{toDate}'").ToList()
        Return Query(Of fn_GetPayrollReportsResult)($"SELECT * FROM custom.fn_GetPayrollReports ({coNum}, '{fromDate}', '{toDate}')").ToList()
    End Function

    Public Function prc_PayrollsIncomplete(Optional ByVal toCheckDate As DateTime? = Nothing, Optional ByVal showAll As String = "No") As List(Of prc_PayrollsIncompleteResult)
        'Return Me.Set(Of prc_PayrollsIncompleteResult)().FromSqlRaw($"EXEC custom.prc_PayrollsIncomplete @ToCheckDate = {(If(toCheckDate.HasValue, $"'{toCheckDate.Value}'", "NULL"))}, @ShowAll = '{showAll}'").ToList()
        Return ExecuteQuery(Of prc_PayrollsIncompleteResult)($"EXEC custom.prc_PayrollsIncomplete @ToCheckDate = {(If(toCheckDate.HasValue, $"'{toCheckDate.Value}'", "NULL"))}, @ShowAll = '{showAll}'").ToList()
    End Function

    Public Function prc_CheckPayrollIssues(ByVal coNum As Decimal, ByVal prNum As Decimal, ByVal returnDetails As Boolean, ByRef issueCount As Integer, Optional ByVal logIssues As Boolean = True, Optional ByVal showAllIssues As Boolean = True, Optional ByVal calledByTrigger As Boolean = False, Optional ByVal returnPayloads As Boolean = False) As List(Of prc_CheckPayrollIssuesResult)
        'Dim issueCountParam As New SqlClient.SqlParameter("@IssueCount", SqlDbType.Int) With {.Direction = ParameterDirection.Output, .Value = 0}
        'Dim result = Me.Set(Of prc_CheckPayrollIssuesResult)().FromSqlRaw($"EXEC custom.prc_CheckPayrollIssues @CoNum = {coNum}, @PrNum = {prNum}, @ReturnDetails = {(If(returnDetails, 1, 0))}, @IssueCount = {issueCountParam.Value}, @LogIssues = {(If(logIssues, 1, 0))}, @ShowAllIssues = {(If(showAllIssues, 1, 0))}, @CalledByTrigger = {(If(calledByTrigger, 1, 0))}, @ReturnPayloads = {(If(returnPayloads, 1, 0))}").ToList()
        'Dim result = ExecuteQuery(Of prc_CheckPayrollIssuesResult)($"EXEC custom.prc_CheckPayrollIssues @CoNum = {coNum}, @PrNum = {prNum}, @ReturnDetails = {(If(returnDetails, 1, 0))}, @IssueCount = {issueCountParam.Value}, @LogIssues = {(If(logIssues, 1, 0))}, @ShowAllIssues = {(If(showAllIssues, 1, 0))}, @CalledByTrigger = {(If(calledByTrigger, 1, 0))}, @ReturnPayloads = {(If(returnPayloads, 1, 0))}", issueCountParam).ToList()
        'issueCount = CType(issueCountParam.Value, Integer)

        Using connection As New SqlClient.SqlConnection
            connection.ConnectionString = Me.Database.GetConnectionString

            connection.Open()
            'Using command As New SqlClient.SqlCommand("custom.prc_CheckPayrollIssues", connection)
            'Command.CommandType = CommandType.StoredProcedure

            Dim parameters As New DynamicParameters()
            parameters.Add("@CoNum", coNum, DbType.Decimal)
            parameters.Add("@PrNum", prNum, DbType.Decimal)
            parameters.Add("@ReturnDetails", If(returnDetails, 1, 0), DbType.Boolean)
            parameters.Add("@LogIssues", If(logIssues, 1, 0), DbType.Boolean)
            parameters.Add("@ShowAllIssues", If(showAllIssues, 1, 0), DbType.Boolean)
            parameters.Add("@CalledByTrigger", If(calledByTrigger, 1, 0), DbType.Boolean)
            parameters.Add("@ReturnPayloads", If(returnPayloads, 1, 0), DbType.Boolean)
            parameters.Add("@IssueCount", DbType.Int32, direction:=ParameterDirection.Output)


            Using results As GridReader = connection.QueryMultiple("custom.prc_CheckPayrollIssues", parameters, commandType:=CommandType.StoredProcedure)
                Dim resultSet = results.Read(Of prc_CheckPayrollIssuesResult)().ToList()
                Dim outputValue = parameters.Get(Of Integer)("@IssueCount")

                Return resultSet
            End Using

            'End Using
        End Using
    End Function

    Public Function prc_YearEnd_WithNoDecPayroll(Optional ByVal [date] As DateTime = #12/31/2014#, Optional ByVal runType As Integer = 1) As List(Of prc_YearEnd_WithNoDecPayrollResult)
        Return Query(Of prc_YearEnd_WithNoDecPayrollResult)($"EXEC custom.prc_YearEnd_WithNoDecPayroll @Date = '{[date]}', @RunType = {runType}").ToList()
    End Function

    Public Function prc_RptPrCoverSheet(ByVal coNum As Decimal, ByVal prNum As Decimal) As List(Of prc_RptPrCoverSheetResult)
        Return Query(Of prc_RptPrCoverSheetResult)($"EXEC custom.prc_RptPrCoverSheet @Conum = {coNum}, @Prnum = {prNum}").ToList()
    End Function

    Public Function prc_RptActiveEmpsNotPaid(ByVal coNum As Decimal) As List(Of prc_RptActiveEmpsNotPaidResult)
        Return Query(Of prc_RptActiveEmpsNotPaidResult)($"EXEC custom.prc_RptActiveEmpsNotPaid @Conum = {coNum}").ToList()
    End Function

    Public Function prc_ReopenPayroll(ByVal coNum As Decimal, ByVal prNum As Decimal) As List(Of prc_ReopenPayrollResult)
        'Return Me.Set(Of prc_ReopenPayrollResult)().FromSqlRaw($"EXEC custom.prc_ReopenPayroll @Conum = {coNum}, @Prnum = {prNum}").ToList()
        Return Query(Of prc_ReopenPayrollResult)($"EXEC custom.prc_ReopenPayroll @Conum = {coNum}, @Prnum = {prNum}").ToList()
    End Function

    Public Function prc_QtrEnd_ResetReportsDisplayDate(ByVal coNum As Decimal, ByVal hideReports As Boolean) As Integer
        Return Database.ExecuteSqlRaw($"EXEC custom.prc_QtrEnd_ResetReportsDisplayDate @Conum = {coNum}, @HideReports = {(If(hideReports, 1, 0))}")
    End Function

    Public Function prc_InsNachaEntry(ByVal upToDate As DateTime, ByVal status As String) As Integer
        Return Database.ExecuteSqlRaw($"EXEC custom.prc_InsNachaEntry @UpToDate = '{upToDate}', @Status = '{status}'")
    End Function

    Public Function prc_InsBrandsPowerGridTemplate(ByVal coCode As Decimal) As Int32
        Return Database.ExecuteSqlRaw($"EXEC custom.prc_InsBrandsPowerGridTemplate @CoCode = {coCode}")
    End Function

    Public Function prc_GetPayrollsNotAttachedToCalenders(ByVal coNum As Integer) As List(Of prc_GetPayrollsNotAttachedToCalendersResult)
        Return Query(Of prc_GetPayrollsNotAttachedToCalendersResult)($"EXEC custom.prc_GetPayrollsNotAttachedToCalenders @CoNum = {coNum}").ToList()
    End Function

    Public Function prc_GetShippingAddress(ByVal coNum As Decimal, Optional ByVal divNum As Decimal? = Nothing, Optional ByVal prNum As Decimal? = Nothing) As List(Of prc_GetShippingAddressResult)
        Return Query(Of prc_GetShippingAddressResult)($"EXEC custom.prc_GetShippingAddress @CoNum = {coNum}, @DivNum = {(If(divNum.HasValue, divNum.Value.ToString(), "NULL"))}, @PrNum = {(If(prNum.HasValue, prNum.Value.ToString(), "NULL"))}").ToList()
    End Function
    Public Function prc_ReportOptions(Optional ByVal coNum As Decimal = 0, Optional ByVal srptId As Integer? = Nothing, Optional ByVal showAllReports As Byte? = Nothing) As List(Of prc_ReportOptionsResult)
        Return Query(Of prc_ReportOptionsResult)($"EXEC custom.prc_ReportOptions @Conum = {coNum}, @SRPT_ID = {(If(srptId.HasValue, srptId.Value.ToString(), "NULL"))}, @ShowAllReports = {(If(showAllReports.HasValue, showAllReports.Value.ToString(), "NULL"))}").ToList()
    End Function

    Public Function prc_GetExportFormat(Optional ByVal coNum As Decimal? = Nothing, Optional ByVal prNum As Decimal? = Nothing, Optional ByVal exportFormat As Integer? = Nothing) As List(Of prc_GetExportFormatResult)
        Return Query(Of prc_GetExportFormatResult)($"EXEC custom.prc_GetExportFormat @Conum = {(If(coNum.HasValue, coNum.Value.ToString(), "NULL"))}, @Prnum = {(If(prNum.HasValue, prNum.Value.ToString(), "NULL"))}, @ExportFormat = {(If(exportFormat.HasValue, exportFormat.Value.ToString(), "NULL"))}").ToList()
    End Function

    Public Function fn_GetCoTransit(ByVal coNum As Decimal) As String
        'Return StringResult.FromSqlRaw($"SELECT custom.fn_GetCoTransit {coNum}").FirstOrDefault().StringProperty
        Return Query(Of StringResult)($"SELECT custom.fn_GetCoTransit ({coNum}) AS StringProperty").FirstOrDefault().StringProperty
    End Function

    Public Function prc_GetCoRank(ByVal coNum As Decimal) As List(Of prc_GetCoRankResult)
        Return Query(Of prc_GetCoRankResult)($"EXEC custom.prc_GetCoRank {coNum}").ToList()
    End Function

    Public Function prc_GetCompanyServices(ByVal coNum As Decimal) As List(Of prc_GetCompanyServicesResult)
        'Return Me.Set(Of prc_GetCompanyServicesResult)().FromSqlRaw($"EXEC custom.prc_GetCompanyServices @Conum = {coNum}").ToList()
        Return ExecuteQuery(Of prc_GetCompanyServicesResult)($"EXEC custom.prc_GetCompanyServices @CoCode = {coNum}").ToList()
    End Function

    Public Function prc_InsTaxRecon(ByVal coNum As Decimal, ByVal year As Integer, ByVal quarterNum As Integer, ByVal effectiveDate As DateTime) As List(Of prc_InsTaxReconResult)
        Return Query(Of prc_InsTaxReconResult)($"EXEC custom.prc_InsTaxRecon @CoNum = {coNum}, @Year = {year}, @QuarterNum = {quarterNum}, @EffectiveDate = '{effectiveDate}'").ToList()
    End Function
    Public Function prc_InactivateCalendar(ByVal coNum As Decimal, Optional ByVal setValue As Integer? = Nothing, Optional ByVal effectiveDate As DateTime = #1/1/1900#) As Int32
        Return Database.ExecuteSqlRaw($"EXEC custom.prc_InactivateCalendar @CoNum = {coNum}, @Set = {(If(setValue.HasValue, setValue.Value.ToString(), "NULL"))}, @EffectiveDate = '{effectiveDate}'")
    End Function

    Public Function prc_GetPrOpenBatch(ByVal coNum As Decimal) As List(Of prc_GetPrOpenBatch_Result)
        Return Query(Of prc_GetPrOpenBatch_Result)($"EXEC custom.prc_GetPrOpenBatch @Conum = {coNum}").ToList()
    End Function

    Public Function prc_GetAllCompanyEmails(ByVal coNum As Decimal, email As String) As List(Of prc_GetAllCompanyEmailsResult)
        Return Query(Of prc_GetAllCompanyEmailsResult)($"EXEC custom.prc_GetAllCompanyEmails @Conum = {coNum}, @email = {email.QuoteSQLString}").ToList()
    End Function

    Public Function prc_GetBankAccounts(ByVal coNum As Decimal) As List(Of prc_GetBankAccountsResult)
        Return Query(Of prc_GetBankAccountsResult)($"EXEC custom.prc_GetBankAccounts @Conum = {coNum}").ToList()
    End Function

    Public Function prc_ExportFormatMarkResend(Optional ByVal coNum As Decimal? = Nothing, Optional ByVal prNum As Decimal? = Nothing, Optional ByVal exportFormat As Integer? = Nothing, Optional ByVal updateMode As Boolean = False) As List(Of prc_ExportFormatMarkResendResult)
        Return Query(Of prc_ExportFormatMarkResendResult)($"EXEC custom.prc_ExportFormatMarkResend @Conum = {(If(coNum.HasValue, coNum.Value.ToString(), "NULL"))}, @Prnum = {(If(prNum.HasValue, prNum.Value.ToString(), "NULL"))}, @ExportFormat = {(If(exportFormat.HasValue, exportFormat.Value.ToString(), "NULL"))}, @updateMode = {(If(updateMode, 1, 0))}")
    End Function

    Public Function prc_GetCommItemsForComapny(ByVal coNum As Decimal) As List(Of prc_GetCommItemsForComapnyResult)
        Return Query(Of prc_GetCommItemsForComapnyResult)($"EXEC custom.prc_GetCommItemsForComapny @Conum ={coNum}").ToList()
    End Function

    Public Function prc_CreateZeroCheck(coNum As Decimal, prNum As Decimal) As Integer
        Return Me.Database.ExecuteSqlRaw($"EXEC custom.prc_CreateZeroCheck @Conum = {coNum}, @PrNum = {prNum}")
    End Function

    Public Function prc_CreateInvoiceItem(coNum As Decimal, prNum As Decimal, divNum As Decimal, ItemNum As Decimal, Qty As Int32) As Integer
        Return Me.Database.ExecuteSqlRaw($"EXEC custom.prc_CreateInvoiceItem @Conum = {coNum}, @PrNum = {prNum}, @divNum = {divNum}, @ItemNum = {ItemNum}, @Qty = {Qty}")
    End Function


    Public Property fn_1059MeasureUsersByDeptResult As DbSet(Of fn_1059MeasureUsersByDeptResult)
    Public Property fn_1059DeptResult As DbSet(Of fn_1059DeptResult)



    Public Function prc_CreateInvoice(coNum As Decimal, prNum As Decimal, [Type] As String) As Integer
        Return Me.Database.ExecuteSqlRaw($"EXEC custom.prc_CreateInvoiceItem @Conum = {coNum}, @PrNum = {prNum}, @Type = {nz("'" + Type + "'", "NULL")}")
    End Function

    Public Function prc_BrandsPowerGridColumns(ByVal coNum As Decimal) As List(Of prc_BrandsPowerGridColumnsResult)
        Return Query(Of prc_BrandsPowerGridColumnsResult)($"EXEC custom.prc_BrandsPowerGridColumns @Conum = {coNum}").ToList()
    End Function

    Public Function fn_NextScheduledPayroll(ByVal coNum As Decimal) As List(Of fn_NextScheduledPayrollResult)
        'Return Me.Set(Of fn_NextScheduledPayrollResult)().FromSqlRaw($"SELECT * FROM custom.fn_NextScheduledPayroll @Conum = {coNum}").ToList()
        Return ExecuteQuery(Of fn_NextScheduledPayrollResult)($"SELECT * FROM dbo.fn_NextScheduledPayroll ({coNum})").ToList()
    End Function

    Public Function fn_GetIsPaperless(ByVal coNum As Decimal) As String
        'Return StringResult.FromSqlRaw($"SELECT custom.fn_GetIsPaperless {coNum}").FirstOrDefault().StringProperty
        Return Query(Of StringResult)($"SELECT custom.fn_GetIsPaperless ({coNum}) AS StringProperty").FirstOrDefault().StringProperty
    End Function

    Public Function fn_GetCoRank(ByVal coNum As Decimal) As Int32?
        'Return IntegerResult.FromSqlRaw($"SELECT custom.fn_GetCoRank {coNum}").FirstOrDefault().IntegerProperty
        Return Query(Of IntegerResult)($"SELECT custom.fn_GetCoRank ({coNum})").FirstOrDefault().IntegerProperty
    End Function

    Public Function fn_NeedApproval(ByVal coNum As Decimal, year As Decimal, qtr As Byte) As String
        'Return StringResult.FromSqlRaw($"SELECT custom.fn_NeedApproval {coNum}, {year}, {qtr}").FirstOrDefault().StringProperty
        Return Query(Of StringResult)($"SELECT custom.fn_NeedApproval ({coNum}, {year}, {qtr})").FirstOrDefault().StringProperty
    End Function

    Public Function fn_1059MeasureUsersByDept(ByVal DeptString As String) As List(Of fn_1059MeasureUsersByDeptResult)
        Return Query(Of fn_1059MeasureUsersByDeptResult)($"SELECT * FROM custom.fn_1059MeasureUsersByDept ({DeptString.QuoteSQLString()})").ToList()
    End Function

    Public Function fn_1059Dept() As List(Of fn_1059DeptResult)
        Return Query(Of fn_1059DeptResult)($"SELECT * FROM custom.fn_1059Dept()").ToList()
    End Function


#Region "global"
    Public Function ExecuteCommand(sql As String, ParamArray parameters() As Object) As Int32
        Return Database.ExecuteSqlRaw(sql, parameters)
    End Function

    'Public Function ExecuteQuery(Of T As Class)(query As String, ParamArray parameters() As Object) As IEnumerable(Of T)
    '    Dim con = New Data.SqlClient.SqlConnection(GetConnectionString)
    '    Return con.Query(Of T)(query, parameters, commandTimeout:=1200)
    '    Return Me.Set(Of T)().FromSqlRaw(query, parameters).ToList()
    'End Function

    Public Function ExecuteQuery(Of T)(query As String, ParamArray parameters() As Object) As IEnumerable(Of T)
        Using con = New SqlClient.SqlConnection(GetConnectionString())
            Dim dynParams = New DynamicParameters()

            For i = 0 To parameters.Length - 1
                dynParams.Add($"@p{i}", parameters(i))
            Next

            ' Make sure SQL string uses @p0, @p1, etc., to match parameters
            Return con.Query(Of T)(query, dynParams, commandTimeout:=1200)
        End Using
    End Function

    Protected Overrides Sub OnConfiguring(optionsBuilder As DbContextOptionsBuilder)
        ' Only configure if not already configured (to avoid conflicts with constructor-provided options)
        If Not optionsBuilder.IsConfigured AndAlso Not String.IsNullOrWhiteSpace(_connectionString) Then
            optionsBuilder.UseSqlServer(_connectionString, Sub(options)
                                                               ' Enable transient error resiliency
                                                               options.EnableRetryOnFailure(
                                                                  maxRetryCount:=3,
                                                                  maxRetryDelay:=TimeSpan.FromSeconds(5),
                                                                  errorNumbersToAdd:=Nothing)
                                                           End Sub).UseLazyLoadingProxies().EnableSensitiveDataLogging()
            'optionsBuilder.ConfigureWarnings(Function(warnings) warnings.Ignore(CoreEventId.LazyLoadOnDisposedContextWarning))
            ' Temporarily disable VB support to fix provider issue
            ' optionsBuilder.AddVbSupport()
        End If
        MyBase.OnConfiguring(optionsBuilder)
    End Sub

    Protected Overrides Sub ConfigureConventions(configurationBuilder As ModelConfigurationBuilder)
        ' ... other convention configurations if needed

        configurationBuilder.Conventions.Add(Function() New HasTriggerConvention())

        ' ... other convention configurations if needed
    End Sub

    Protected Overrides Sub OnModelCreating(modelBuilder As ModelBuilder)
        ' Custom function translation disabled - use standard nullable boolean handling instead
        ' The GetValueOrDefault function should be replaced with standard LINQ expressions in queries



        modelBuilder.Entity(Of [_1059Measure])().HasKey(Function(t) New With {t.MeasureId})
        modelBuilder.Entity(Of MeasureCounter1059)().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [_1059MeasureEmpSetup])().HasKey(Function(t) New With {t.EmpNum, t.MeasureId})
        modelBuilder.Entity(Of [Meetings1059])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [MultiUse1059])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [_1059pbxreport2])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PhoneData1059])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [_1059PhoneDataComment])().HasKey(Function(t) New With {t.PhoneNumber})
        modelBuilder.Entity(Of [_1059punch])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of QueConnectionLogs1059)().HasKey(Function(t) New With {t.ID_Connect})
        modelBuilder.Entity(Of [QueEventImport1059])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ScoreCardsClientCSE1059])().HasKey(Function(t) New With {t.Measurable, t.MeasurableYear})
        modelBuilder.Entity(Of [ScoreCardsSales1059])().HasKey(Function(t) New With {t.Measurable, t.MeasurableYear})
        modelBuilder.Entity(Of [ScoreCardXml1059])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [_1059UsersRealtionship])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Week1059])().HasKey(Function(t) New With {t.WeekId})
        modelBuilder.Entity(Of [ZenDeskTicketData1059])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FORMS_T1099])().HasKey(Function(t) New With {t.CONUM, t.RPT_COUNTER})
        modelBuilder.Entity(Of [aca_affordability])().HasKey(Function(t) New With {t.conum, t.empnum})
        modelBuilder.Entity(Of [ACA_Annual_Affordability])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [aca_classification])().HasKey(Function(t) New With {t.conum, t.empnum})
        modelBuilder.Entity(Of [aca_company_data])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [aca_coverage_detail])().HasKey(Function(t) New With {t.conum, t.empnum, t.mth, t.yr})
        modelBuilder.Entity(Of [aca_coverage_master])().HasKey(Function(t) New With {t.conum, t.empnum})
        modelBuilder.Entity(Of [ACA_Evens_InsertedBy_BRANDS])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of ACA_File_Batch_Company_T)().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of ACA_File_Batch_T)().HasKey(Function(t) New With {t.Batch_ID})
        modelBuilder.Entity(Of [ACA_FORMS])().HasKey(Function(t) New With {t.CONUM, t.RPT_COUNTER})
        modelBuilder.Entity(Of [aca_initial_assessment])().HasKey(Function(t) New With {t.conum, t.empnum})
        modelBuilder.Entity(Of [aca_large_er_count])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [aca_large_er_form_count])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [ACA_ServiceOptOutLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ACA2016_Control])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [AccntMapping])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [AccountLead])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Account])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ACCRUAL_DETAIL])().HasKey(Function(t) New With {t.CONUM, t.EMP_STATUS, t.LEAVE_TYPE, t.START_DAY})
        modelBuilder.Entity(Of [ACCRUAL_MASTER])().HasKey(Function(t) New With {t.CONUM, t.EMP_STATUS, t.LEAVE_TYPE})
        modelBuilder.Entity(Of [accrual_request_detail])().HasKey(Function(t) New With {t.conum, t.empnum, t.id, t.request_id})
        modelBuilder.Entity(Of [accrual_request])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [ACCT_TYPE])().HasKey(Function(t) New With {t.type_id})
        modelBuilder.Entity(Of [ACH_Capital_One_Returns])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ach_verification_exceptions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [ach_verification_log])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [ACHBatch_company])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [ACHBatch])().HasKey(Function(t) New With {t.batch_id})
        modelBuilder.Entity(Of [AchTransactionDailyLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [AchTransactionsLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ACT_AUTO_PriceOverride])().HasKey(Function(t) New With {t.CONUM, t.DIVNUM, t.ITEM_NAME, t.ITEM_NUM, t.PriceFromQty})
        modelBuilder.Entity(Of [ACT_AUTO])().HasKey(Function(t) New With {t.CONUM, t.line_id})
        modelBuilder.Entity(Of [ACT_ITEM])().HasKey(Function(t) New With {t.ITEM_NUM})
        modelBuilder.Entity(Of [act_items_count_calcs])().HasKey(Function(t) New With {t.calc_id})
        modelBuilder.Entity(Of [Act_Items_Detail])().HasKey(Function(t) New With {t.ITEM_NUM})
        modelBuilder.Entity(Of [act_items_pricedetails])().HasKey(Function(t) New With {t.item_num, t.line_id, t.table_id})
        modelBuilder.Entity(Of [act_items_priceinfo])().HasKey(Function(t) New With {t.item_num, t.table_id})
        modelBuilder.Entity(Of [ActiveMaster])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [Advertisements])().HasKey(Function(t) New With {t.ad_id})
        modelBuilder.Entity(Of [AI_FormRecognitionJson])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Analytics_GlobalList])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Analytics_User_Settings])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [AODBalanceImport])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ApiActivityLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ApiLogItems])().HasKey(Function(t) New With {t.SrcID})
        modelBuilder.Entity(Of [ApiReqRespJson])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [applications])().HasKey(Function(t) New With {t.catalog})
        modelBuilder.Entity(Of [applications_database])().HasKey(Function(t) New With {t.catalog, t.sql_version, t.version})
        modelBuilder.Entity(Of [applications_server])().HasKey(Function(t) New With {t.catalog, t.server, t.version})
        modelBuilder.Entity(Of [applications_version])().HasKey(Function(t) New With {t.catalog, t.version})
        modelBuilder.Entity(Of [ats_applicant_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [ats_applicant_ncs_package_params])().HasKey(Function(t) New With {t.ncs_package_id, t.ncs_parameter_id})
        modelBuilder.Entity(Of [ats_applicant_ncs_params])().HasKey(Function(t) New With {t.ncs_parameter})
        modelBuilder.Entity(Of [ats_applicant_ncs])().HasKey(Function(t) New With {t.ncs_id})
        modelBuilder.Entity(Of [ats_applicant_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [ats_applicants])().HasKey(Function(t) New With {t.appnum, t.conum})
        modelBuilder.Entity(Of [ats_candidate_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [ats_candidate_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [ats_candidate_score])().HasKey(Function(t) New With {t.score_id})
        modelBuilder.Entity(Of [ats_candidate_task])().HasKey(Function(t) New With {t.task_id})
        modelBuilder.Entity(Of [ats_candidates])().HasKey(Function(t) New With {t.candidate_id})
        modelBuilder.Entity(Of [ats_certifications])().HasKey(Function(t) New With {t.appnum, t.conum, t.id})
        modelBuilder.Entity(Of [ats_education])().HasKey(Function(t) New With {t.appnum, t.conum, t.ID})
        modelBuilder.Entity(Of [ats_former_employer])().HasKey(Function(t) New With {t.appnum, t.conum, t.ID})
        modelBuilder.Entity(Of [ats_onboarding_company_doc_storage])().HasKey(Function(t) New With {t.conum, t.document_id})
        modelBuilder.Entity(Of [ats_onboarding_document])().HasKey(Function(t) New With {t.conum, t.id, t.onboarding_id})
        modelBuilder.Entity(Of [ats_onboarding_em_contact])().HasKey(Function(t) New With {t.conum, t.id, t.onboarding_id})
        modelBuilder.Entity(Of [ats_onboarding_state])().HasKey(Function(t) New With {t.conum, t.id, t.onboarding_id, t.state})
        modelBuilder.Entity(Of [ats_onboarding])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [ats_open_positions])().HasKey(Function(t) New With {t.conum, t.opening_id})
        modelBuilder.Entity(Of [ats_skills])().HasKey(Function(t) New With {t.appnum, t.conum, t.ID})
        modelBuilder.Entity(Of [AUDIT_LOG_TABLE_TEMPLATES])().HasKey(Function(t) New With {t.TABLE_NAME, t.TEMPLATE_ID})
        modelBuilder.Entity(Of [AUDIT_LOG_TABLES])().HasKey(Function(t) New With {t.TABLE_NAME})
        modelBuilder.Entity(Of [AUDIT_LOG_TEMPLATES])().HasKey(Function(t) New With {t.TEMPLATE_ID})
        modelBuilder.Entity(Of [AUDIT_TEMPLATES])().HasKey(Function(t) New With {t.AUDIT_TEMPLATE_ID})
        modelBuilder.Entity(Of [AuditLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [AuditStats])().HasKey(Function(t) New With {t.Dtm})
        modelBuilder.Entity(Of [AuditTables])().HasKey(Function(t) New With {t.tblName})
        modelBuilder.Entity(Of [AutoPayrollsLog])().HasKey(Function(t) New With {t.LogID})
        modelBuilder.Entity(Of [AutoPrintJob])().HasKey(Function(t) New With {t.Printer})
        modelBuilder.Entity(Of [background_check_requests])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [Bank_Holidays])().HasKey(Function(t) New With {t.holiday_date})
        modelBuilder.Entity(Of [BANK_INFO])().HasKey(Function(t) New With {t.ROUTING})
        modelBuilder.Entity(Of [BankingFileHold])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [BankingFileUpload])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [BankingManualTaxAdjCtrl])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [BankingManualTaxAdjCtrlDetail])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [BankingManualTaxAdjCtrlPayment])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [BankTaxPmtCtrl])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [billing_template_categories])().HasKey(Function(t) New With {t.category_id, t.template_id})
        modelBuilder.Entity(Of [billing_template_items])().HasKey(Function(t) New With {t.detail_id, t.template_id})
        modelBuilder.Entity(Of [billing_template_master])().HasKey(Function(t) New With {t.template_id})
        modelBuilder.Entity(Of [BrandsAuthClaim])().HasKey(Function(t) New With {t.ClaimValue})
        modelBuilder.Entity(Of [BrandsAuthClaimOverridesByCompany])().HasKey(Function(t) New With {t.ClaimValue, t.Conum})
        modelBuilder.Entity(Of [BrandsAuthLoginRequest])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthRoleClaims])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthRoles])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthUser])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthUserClaims])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthUserCPAFirms])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthUserCPAFirmsCompanies])().HasKey(Function(t) New With {t.BrandsAuthUserCPAFirmId, t.Conum})
        modelBuilder.Entity(Of [BrandsAuthUserEmployee])().HasKey(Function(t) New With {t.Conum, t.UserId})
        modelBuilder.Entity(Of [BrandsAuthUserEmployeeSqlUsers])().HasKey(Function(t) New With {t.Conum, t.UserId})
        modelBuilder.Entity(Of [BrandsAuthUserEssEmployees])().HasKey(Function(t) New With {t.EmployeeEntryId, t.UserId})
        modelBuilder.Entity(Of [BrandsAuthUserEvent])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthUserLiveReports])().HasKey(Function(t) New With {t.CoNum, t.ReportId, t.UserId})
        modelBuilder.Entity(Of [BrandsAuthUserLogins])().HasKey(Function(t) New With {t.LoginProvider, t.ProviderKey})
        modelBuilder.Entity(Of [BrandsAuthUserRememberedLogins])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [BrandsAuthUserRoles])().HasKey(Function(t) New With {t.RoleId, t.UserId})
        modelBuilder.Entity(Of [BrandsAuthUserTokens])().HasKey(Function(t) New With {t.LoginProvider, t.Name, t.UserId})
        modelBuilder.Entity(Of [BrandsLogo])().HasKey(Function(t) New With {t.LogoName, t.VersionNo})
        modelBuilder.Entity(Of [BW2_AccessOverride])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [CALENDAR_RULES])().HasKey(Function(t) New With {t.conum, t.period_id})
        modelBuilder.Entity(Of [CALENDAR])().HasKey(Function(t) New With {t.cal_id, t.conum, t.period_id})
        modelBuilder.Entity(Of [CalendarNote])().HasKey(Function(t) New With {t.NoteID})
        modelBuilder.Entity(Of [CalendarReused])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CALL_TYPE])().HasKey(Function(t) New With {t.type_id})
        modelBuilder.Entity(Of [CaptureDebug])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CaptureLogins])().HasKey(Function(t) New With {t.hostname, t.loginame, t.program_name})
        modelBuilder.Entity(Of [CATEGORY])().HasKey(Function(t) New With {t.NAME})
        modelBuilder.Entity(Of [category_catchup_ages])().HasKey(Function(t) New With {t.catchup_age_from, t.catchup_age_to, t.NAME, t.startdate})
        modelBuilder.Entity(Of [CATLOCALOR])().HasKey(Function(t) New With {t.CODE, t.NAME})
        modelBuilder.Entity(Of [cdc_transactions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [CDL_ExcludedZipCodes])().HasKey(Function(t) New With {t.ZipCode})
        modelBuilder.Entity(Of [CheckLineExtension])().HasKey(Function(t) New With {t.ChkCounter, t.CL_NUM, t.CONUM, t.EMPNUM, t.LineType, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [CheckMasterBatch])().HasKey(Function(t) New With {t.BatchNum, t.Conum, t.Prnum})
        modelBuilder.Entity(Of [CheckMasterBatchItems])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [CheckMasterExtension])().HasKey(Function(t) New With {t.ChkCounter, t.Conum, t.Empnum, t.Prnum})
        modelBuilder.Entity(Of [CheckMasterVirtualLines])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [CheckPayrollIssuesLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CHK_DET_DED])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [CHK_DET_EMP_SNAPSHOT])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [CHK_DET_MEMO])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [CHK_DET_MSGS])().HasKey(Function(t) New With {t.chk_counter, t.conum, t.empnum, t.id, t.payroll_num})
        modelBuilder.Entity(Of [CHK_DET_PAY])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [CHK_DET_TAX])().HasKey(Function(t) New With {t.chk_counter, t.conum, t.empnum, t.payroll_num, t.state, t.tax_type})
        modelBuilder.Entity(Of [CHK_MAST])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [co_401k_components])().HasKey(Function(t) New With {t.component_id, t.component_value, t.conum, t.service_code, t.service_plan_id})
        modelBuilder.Entity(Of [CO_CALCS])().HasKey(Function(t) New With {t.CALC_NAME, t.CONUM})
        modelBuilder.Entity(Of [co_contact])().HasKey(Function(t) New With {t.conum, t.empconum, t.empnum})
        modelBuilder.Entity(Of [CO_JOB])().HasKey(Function(t) New With {t.conum, t.job_id})
        modelBuilder.Entity(Of [co_site_detail])().HasKey(Function(t) New With {t.CONUM, t.DIVNUM, t.SITE_ID})
        modelBuilder.Entity(Of [co_sites])().HasKey(Function(t) New With {t.CONUM, t.SITE_ID})
        modelBuilder.Entity(Of [CO_UDF])().HasKey(Function(t) New With {t.CONUM, t.UDF_DESCR})
        modelBuilder.Entity(Of [cobra_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [cobra_events])().HasKey(Function(t) New With {t.cobra_id})
        modelBuilder.Entity(Of [cobra_insurance])().HasKey(Function(t) New With {t.insurance_id})
        modelBuilder.Entity(Of [cobra_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [cobra_notification])().HasKey(Function(t) New With {t.notification_id})
        modelBuilder.Entity(Of [cobra_payment])().HasKey(Function(t) New With {t.payment_id})
        modelBuilder.Entity(Of [cobra_task])().HasKey(Function(t) New With {t.task_id})
        modelBuilder.Entity(Of [CODE_GROUP_LIST])().HasKey(Function(t) New With {t.CODE, t.CODE_TYPE, t.CONUM, t.GROUP_NUM})
        modelBuilder.Entity(Of [CODE_GROUPS])().HasKey(Function(t) New With {t.CONUM, t.GROUP_NUM})
        modelBuilder.Entity(Of [CommCompanySetup])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CommPlanDetail])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CommPlan])().HasKey(Function(t) New With {t.PlanID})
        modelBuilder.Entity(Of [CoMonitor])().HasKey(Function(t) New With {t.CoCode})
        modelBuilder.Entity(Of [company_group_list])().HasKey(Function(t) New With {t.group_key, t.list_key})
        modelBuilder.Entity(Of [company_groups])().HasKey(Function(t) New With {t.group_key})
        modelBuilder.Entity(Of [company_local_tax_filing])().HasKey(Function(t) New With {t.conum, t.file_pay_type, t.id, t.loc_id})
        modelBuilder.Entity(Of [company_state_tax_payment_method])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [COMPANY])().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of [company_tax_filing])().HasKey(Function(t) New With {t.conum, t.file_pay_type, t.id, t.state, t.tax_type})
        modelBuilder.Entity(Of [company_taxes])().HasKey(Function(t) New With {t.conum, t.state, t.tax_type})
        modelBuilder.Entity(Of [CompanyActivityLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CompanyCreated])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CompanyPayrollStats])().HasKey(Function(t) New With {t.CoNum, t.PrNum})
        modelBuilder.Entity(Of [CompanyPayrollSummaryReports])().HasKey(Function(t) New With {t.Conum, t.ReportId})
        modelBuilder.Entity(Of [CompletedQbReports])().HasKey(Function(t) New With {t.ReportId, t.UserId})
        modelBuilder.Entity(Of CoOptions_Exports_Fee_T)().HasKey(Function(t) New With {t.CooptionsID})
        modelBuilder.Entity(Of [CoOptions_Export])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CoOptions_Payroll])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [CoOptions_SecondCheckPayCode])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [COOPTION])().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of [CoOptionsExportsSQL])().HasKey(Function(t) New With {t.CoOptionsID})
        modelBuilder.Entity(Of [COUSERDEF])().HasKey(Function(t) New With {t.conum})
        modelBuilder.Entity(Of [CovidEmailNotify])().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of [CovidEmailNotifyFollowUp])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [COWA_LITAX])().HasKey(Function(t) New With {t.CONUM, t.RISKCODE, t.STARTDATE})
        modelBuilder.Entity(Of [CPAFirms])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [CPAFirmsCompanies])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [CPI_CallLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CR_TempDataSession])().HasKey(Function(t) New With {t.SessionId})
        modelBuilder.Entity(Of [CREPORTS])().HasKey(Function(t) New With {t.RPT_NAME})
        modelBuilder.Entity(Of [crm_base])().HasKey(Function(t) New With {t.conum, t.crm_id})
        modelBuilder.Entity(Of [crm_call_log])().HasKey(Function(t) New With {t.call_id, t.conum, t.licensee_id})
        modelBuilder.Entity(Of [crm_change_log])().HasKey(Function(t) New With {t.conum, t.crm_id, t.id})
        modelBuilder.Entity(Of [crm_conversation])().HasKey(Function(t) New With {t.conum, t.conversation_id})
        modelBuilder.Entity(Of [crm_conversation_user])().HasKey(Function(t) New With {t.conversation_id, t.user_id})
        modelBuilder.Entity(Of [crm_document_storage])().HasKey(Function(t) New With {t.conum, t.crm_id, t.storage_id})
        modelBuilder.Entity(Of [crm_documents])().HasKey(Function(t) New With {t.conum, t.crm_id})
        modelBuilder.Entity(Of [crm_email_checkpoint])().HasKey(Function(t) New With {t.email_address})
        modelBuilder.Entity(Of [crm_instant_message])().HasKey(Function(t) New With {t.message_id})
        modelBuilder.Entity(Of [crm_message_activity])().HasKey(Function(t) New With {t.message_id, t.type, t.user_id})
        modelBuilder.Entity(Of [crm_property_definition])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [crm_property])().HasKey(Function(t) New With {t.conum, t.crm_id, t.property_id})
        modelBuilder.Entity(Of [crm_property_type])().HasKey(Function(t) New With {t.crm_property, t.crm_type})
        modelBuilder.Entity(Of [crm_property_value])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [crm_quick_notes])().HasKey(Function(t) New With {t.created_by, t.note_id})
        modelBuilder.Entity(Of [crm_tickets])().HasKey(Function(t) New With {t.conum, t.crm_id})
        modelBuilder.Entity(Of [CustomReport])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CustomReportColumn])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CustomReportColumnTemplate])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CustomReportLayout])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CustomReportLayoutShare])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CustomReportLayoutSharePaydeck])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [CustomReportSchedule])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [CustomReportTable])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [CustomReportTableLink])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [DD_INTERCEPT])().HasKey(Function(t) New With {t.ACCT_NUM, t.ACCT_TYPE, t.CHK_NUM, t.CONUM, t.DIVNUM, t.entry_type})
        modelBuilder.Entity(Of [DD_Manual_Trans_Log])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [DD_Reversal_Email])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [DD_Reversal_Log])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [DD_Reversal_Log_Queue])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [DEDUCTION])().HasKey(Function(t) New With {t.CONUM, t.DED_NUM})
        modelBuilder.Entity(Of [definition_401k_components])().HasKey(Function(t) New With {t.component_id})
        modelBuilder.Entity(Of [definition_401k_services])().HasKey(Function(t) New With {t.component_id, t.service_code})
        modelBuilder.Entity(Of [Delivery])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [dbo_DELIVERY])().HasKey(Function(t) New With {t.DELDESC})
        modelBuilder.Entity(Of [DeliveryTicket])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [DEPARTMENT])().HasKey(Function(t) New With {t.CONUM, t.DEPTNUM, t.DIVNUMD})
        modelBuilder.Entity(Of [dependent_coverage])().HasKey(Function(t) New With {t.conum, t.empnum, t.id, t.start_date})
        modelBuilder.Entity(Of [DivDepGroup])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [DIVISION])().HasKey(Function(t) New With {t.CONUM, t.DDIVNUM})
        modelBuilder.Entity(Of [document_storage_TABLE])().HasKey(Function(t) New With {t.document_id, t.storage_id})
        modelBuilder.Entity(Of [Documentation])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [DOCUMENT])().HasKey(Function(t) New With {t.doc_id})
        modelBuilder.Entity(Of [dtproperties])().HasKey(Function(t) New With {t.id, t.property})
        modelBuilder.Entity(Of [EE_UDF])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.UDF_DESCR})
        modelBuilder.Entity(Of [eftps_report_table_batch])().HasKey(Function(t) New With {t.batch_id})
        modelBuilder.Entity(Of [eftps_report_table_detail])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [eftps_report_table_master])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [email_profile])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [EmailAttachments_Table])().HasKey(Function(t) New With {t.AttachmentID, t.EmailID})
        modelBuilder.Entity(Of [EmailOutbound])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [EmailOutboundZD])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [Emails_Table])().HasKey(Function(t) New With {t.EmailNum})
        modelBuilder.Entity(Of [emp_deds_amount_schedule])().HasKey(Function(t) New With {t.amount_key, t.conum, t.deduction_key, t.empnum})
        modelBuilder.Entity(Of [EMP_DED])().HasKey(Function(t) New With {t.CONUM, t.DED_DEPTNUM, t.DED_DIVNUM, t.DED_NUM_E, t.EMPNUM})
        modelBuilder.Entity(Of [EMP_LEAVE_TOT])().HasKey(Function(t) New With {t.CONUM, t.date, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [emp_memos_amount_schedule])().HasKey(Function(t) New With {t.amount_key, t.conum, t.empnum, t.memo_key})
        modelBuilder.Entity(Of [EMP_MEMOS])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.MEM_DEPTNUM, t.MEM_DIVNUM, t.MEM_NUM_E})
        modelBuilder.Entity(Of [emp_ops_amount_schedule])().HasKey(Function(t) New With {t.amount_key, t.conum, t.empnum, t.otherpay_key})
        modelBuilder.Entity(Of [EMP_OPS])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.OPS_DEPTNUM, t.OPS_DIVNUM, t.OPS_NUM})
        modelBuilder.Entity(Of [EMP_RateChangesLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [EmpDDPriorTermination])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [EmpIncentive])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [employee_change_detail])().HasKey(Function(t) New With {t.conum, t.detail_id, t.empnum, t.request_id})
        modelBuilder.Entity(Of [employee_change_request])().HasKey(Function(t) New With {t.conum, t.empnum, t.request_id})
        modelBuilder.Entity(Of [employee_change_template])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [employee_ncs])().HasKey(Function(t) New With {t.ncs_id})
        modelBuilder.Entity(Of [employee_picture])().HasKey(Function(t) New With {t.conum, t.empnum})
        modelBuilder.Entity(Of [employee_position])().HasKey(Function(t) New With {t.conum, t.effective_date, t.empnum})
        modelBuilder.Entity(Of [employee_rate])().HasKey(Function(t) New With {t.conum, t.effective_date, t.empnum, t.rate})
        modelBuilder.Entity(Of [EMPLOYEE])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM})
        modelBuilder.Entity(Of [EmployeeLogin])().HasKey(Function(t) New With {t.Conum, t.Empnum})
        modelBuilder.Entity(Of [EmployeeOption])().HasKey(Function(t) New With {t.CoNum, t.EmpNum})
        modelBuilder.Entity(Of [EmployeePaySchedules])().HasKey(Function(t) New With {t.Conum, t.Empnum})
        modelBuilder.Entity(Of [EnrolledTPAList])().HasKey(Function(t) New With {t.CoNum, t.ST_ABBR, t.TaxType})
        modelBuilder.Entity(Of [enrollment])().HasKey(Function(t) New With {t.EnrollmentID})
        modelBuilder.Entity(Of [enrollment_details])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [EP_Form941RecalculateEligibleWagesByCheckHistory])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [EP_IMP_LIST])().HasKey(Function(t) New With {t.field_name})
        modelBuilder.Entity(Of [ep_statistics])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [epsusers])().HasKey(Function(t) New With {t.username})
        modelBuilder.Entity(Of [epsusers_access])().HasKey(Function(t) New With {t.access, t.value})
        modelBuilder.Entity(Of [epsusers_config])().HasKey(Function(t) New With {t.catalog, t.login})
        modelBuilder.Entity(Of [epsusers_details])().HasKey(Function(t) New With {t.conum, t.username})
        modelBuilder.Entity(Of [epsusers_divs])().HasKey(Function(t) New With {t.conum, t.deptnum, t.divnum, t.username})
        modelBuilder.Entity(Of [epsusers_managers])().HasKey(Function(t) New With {t.conum, t.manager_num, t.username})
        modelBuilder.Entity(Of [epsusers_rights])().HasKey(Function(t) New With {t.username, t.value})
        modelBuilder.Entity(Of [epsusers_tickets])().HasKey(Function(t) New With {t.ticket_id, t.username})
        modelBuilder.Entity(Of [erm_ticket_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [erm_ticket_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [erm_ticket_task])().HasKey(Function(t) New With {t.task_id})
        modelBuilder.Entity(Of [erm_tickets])().HasKey(Function(t) New With {t.ticket_id})
        modelBuilder.Entity(Of [ErrorLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [esign_document_fields])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [esign_documents])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [esign_groups])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [EssAuthClaims])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [EssAuthRoleClaims])().HasKey(Function(t) New With {t.ClaimID, t.RoleID})
        modelBuilder.Entity(Of [EssAuthRoles])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [EssAuthUserClaims])().HasKey(Function(t) New With {t.ClaimID, t.UserID})
        modelBuilder.Entity(Of [EssAuthUserEmployees])().HasKey(Function(t) New With {t.EmployeeEntryID, t.UserID})
        modelBuilder.Entity(Of [EssAuthUserEvents])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [EssAuthUserLogins])().HasKey(Function(t) New With {t.LoginProvider, t.ProviderKey})
        modelBuilder.Entity(Of [EssAuthUserRoles])().HasKey(Function(t) New With {t.RoleId, t.UserId})
        modelBuilder.Entity(Of [EssAuthUsers])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [EssAuthUserTokens])().HasKey(Function(t) New With {t.LoginProvider, t.Name, t.UserId})
        modelBuilder.Entity(Of [EssEmailNotify])().HasKey(Function(t) New With {t.EmailAddress})
        modelBuilder.Entity(Of [EssEmailTemplates])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [EssEmployeeDDAccounts])().HasKey(Function(t) New With {t.CoNum, t.EmpNum, t.Id})
        modelBuilder.Entity(Of [EssEmployees])().HasKey(Function(t) New With {t.CoNum, t.EmpNum})
        modelBuilder.Entity(Of [EssHiddenPayrolls])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ESSNEWUSERS])().HasKey(Function(t) New With {t.ESSNEW_ID})
        modelBuilder.Entity(Of [ETAX_CHK])().HasKey(Function(t) New With {t.batch_num, t.chk_counter, t.conum})
        modelBuilder.Entity(Of [eula_detail])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [eula_detail_type])().HasKey(Function(t) New With {t.detail_type_id})
        modelBuilder.Entity(Of [eula_location])().HasKey(Function(t) New With {t.location_id})
        modelBuilder.Entity(Of [eula_master])().HasKey(Function(t) New With {t.agreement_id})
        modelBuilder.Entity(Of [eula_user])().HasKey(Function(t) New With {t.agreement_id, t.conum, t.empnum})
        modelBuilder.Entity(Of [event_alert])().HasKey(Function(t) New With {t.conum, t.eventId})
        modelBuilder.Entity(Of [EVENT_LOG])().HasKey(Function(t) New With {t.TRAN_TIME, t.TYPE})
        modelBuilder.Entity(Of [ExportFormat])().HasKey(Function(t) New With {t.FormatID})
        modelBuilder.Entity(Of [FaxCategoryStruct])().HasKey(Function(t) New With {t.Category})
        modelBuilder.Entity(Of [Fax])().HasKey(Function(t) New With {t.FaxID})
        modelBuilder.Entity(Of [FD_GridControlColumns])().HasKey(Function(t) New With {t.Caption, t.FieldName, t.Level, t.Src})
        modelBuilder.Entity(Of [FD_GridControlColumnsSortOver])().HasKey(Function(t) New With {t.FieldName, t.Level, t.SortGroup, t.Src})
        modelBuilder.Entity(Of [FD_ST_PortalLogins])().HasKey(Function(t) New With {t.PortalID})
        modelBuilder.Entity(Of [FD_ST_PortalLoginsCoNum])().HasKey(Function(t) New With {t.CoNum, t.PortalID})
        modelBuilder.Entity(Of [FDUserLogs])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FedACHdir])().HasKey(Function(t) New With {t.Routing})
        modelBuilder.Entity(Of [FEEDBACK])().HasKey(Function(t) New With {t.FB_ID})
        modelBuilder.Entity(Of [file_types])().HasKey(Function(t) New With {t.FileTypeName})
        modelBuilder.Entity(Of [FilingControl1099])().HasKey(Function(t) New With {t.CONUM, t.Yr})
        modelBuilder.Entity(Of [FilingControl941])().HasKey(Function(t) New With {t.CONUM, t.Qtr, t.YR})
        modelBuilder.Entity(Of [FilingControls])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FinalReturns])().HasKey(Function(t) New With {t.CoNum, t.FinalQtr, t.FinalYear})
        modelBuilder.Entity(Of [Fix941CheckDateLog])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FrontDeskFeedback])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [FrontDeskOption])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FrontDeskPermission])().HasKey(Function(t) New With {t.UserName})
        modelBuilder.Entity(Of [FrontDeskRole])().HasKey(Function(t) New With {t.RoleID})
        modelBuilder.Entity(Of [FrontDeskRoleUser])().HasKey(Function(t) New With {t.RoleID, t.UserName})
        modelBuilder.Entity(Of [FrontDeskTicketAttachment])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FrontDeskTicket])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [FutaFilingControl])().HasKey(Function(t) New With {t.CONUM, t.Qtr, t.YR})
        modelBuilder.Entity(Of [GENLEDGE])().HasKey(Function(t) New With {t.CODE, t.CONUM, t.DEPTNUM, t.DIVNUM, t.SECTION, t.TYPE})
        modelBuilder.Entity(Of [GL_DESC])().HasKey(Function(t) New With {t.CONUM, t.GL_NUM})
        modelBuilder.Entity(Of [gl_enhanced])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [gl_report])().HasKey(Function(t) New With {t.acctdesc, t.conum, t.deptnum, t.divnum, t.payroll_num})
        modelBuilder.Entity(Of [GL_TOTALS])().HasKey(Function(t) New With {t.CONUM, t.DEPT, t.DIV, t.PAYROLL_NUM, t.ST_ABBR, t.TYPE})
        modelBuilder.Entity(Of [global_lists])().HasKey(Function(t) New With {t.cat_id})
        modelBuilder.Entity(Of [grid_columns])().HasKey(Function(t) New With {t.schema_id, t.sql_column})
        modelBuilder.Entity(Of [grid_filters])().HasKey(Function(t) New With {t.column_name, t.conum, t.schema_id})
        modelBuilder.Entity(Of [grid_groups])().HasKey(Function(t) New With {t.group_id, t.schema_id})
        modelBuilder.Entity(Of [grid_schemas])().HasKey(Function(t) New With {t.schema_id})
        modelBuilder.Entity(Of [grid_totals])().HasKey(Function(t) New With {t.schema_id, t.sql_column})
        modelBuilder.Entity(Of [GridLayout])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [hr_assets])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [HR_ATTENDANCE])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [hr_certifications])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [HR_CONTACTS])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [HR_EDUCATION])().HasKey(Function(t) New With {t.conum, t.empnum, t.ID})
        modelBuilder.Entity(Of [HR_EMCONTACTS])().HasKey(Function(t) New With {t.conum, t.empnum, t.ID})
        modelBuilder.Entity(Of [hr_events])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [HR_FAMILY])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [HR_FORMER_EMPLOYER])().HasKey(Function(t) New With {t.conum, t.empnum, t.ID})
        modelBuilder.Entity(Of [HR_LISTS])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [HR_REMINDERS])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [HR_REVIEWS])().HasKey(Function(t) New With {t.conum, t.empnum, t.ID})
        modelBuilder.Entity(Of [HR_SKILLS])().HasKey(Function(t) New With {t.conum, t.empnum, t.ID})
        modelBuilder.Entity(Of [HR_TRAINING])().HasKey(Function(t) New With {t.conum, t.empnum, t.ID})
        modelBuilder.Entity(Of [HRColumnCaption])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [HRData])().HasKey(Function(t) New With {t.CoNum, t.EmpNum})
        modelBuilder.Entity(Of [HRDocuments])().HasKey(Function(t) New With {t.DocumentID})
        modelBuilder.Entity(Of [HRDocumentTypes])().HasKey(Function(t) New With {t.FieldName})
        modelBuilder.Entity(Of [HRLayouts])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [HRRestrictedEmps])().HasKey(Function(t) New With {t.CoNum, t.EmpNum})
        modelBuilder.Entity(Of [HRUserTypes])().HasKey(Function(t) New With {t.username})
        modelBuilder.Entity(Of [Import_Fields])().HasKey(Function(t) New With {t.field_id})
        modelBuilder.Entity(Of [Import_Master])().HasKey(Function(t) New With {t.IMPORT_NAME})
        modelBuilder.Entity(Of [import_trans])().HasKey(Function(t) New With {t.trans_id})
        modelBuilder.Entity(Of [ImportEmployeesScripts])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ImportFieldSettings])().HasKey(Function(t) New With {t.CoNum, t.Field_name, t.import_name})
        modelBuilder.Entity(Of [ImportPowerGridsMappingDetails])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ImportPowerGridsMappings])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [integration_account_parameters])().HasKey(Function(t) New With {t.account, t.parameter_name})
        modelBuilder.Entity(Of [integration_accounts])().HasKey(Function(t) New With {t.conum, t.id, t.provider_id})
        modelBuilder.Entity(Of [integration_accrual_transactions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [integration_company_transactions])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [integration_deductions_log])().HasKey(Function(t) New With {t.conum, t.tran_id})
        modelBuilder.Entity(Of [integration_employee_transactions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [integration_fset_clientdetail])().HasKey(Function(t) New With {t.conum, t.transmission_key})
        modelBuilder.Entity(Of [integration_fset_master])().HasKey(Function(t) New With {t.transmission_key})
        modelBuilder.Entity(Of [integration_id_mapping])().HasKey(Function(t) New With {t.id, t.payroll_client_id, t.provider_id, t.table_name})
        modelBuilder.Entity(Of [integration_parameters])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [integration_providers])().HasKey(Function(t) New With {t.provider_id})
        modelBuilder.Entity(Of [integration_rates_transactions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [integration_shugo_log])().HasKey(Function(t) New With {t.conum, t.tran_id})
        modelBuilder.Entity(Of [integration_tlm_transactions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [integration_users])().HasKey(Function(t) New With {t.conum, t.ep_user_name, t.id, t.provider_id})
        modelBuilder.Entity(Of [invoice_creddebs])().HasKey(Function(t) New With {t.conum, t.line_id})
        modelBuilder.Entity(Of [invoice_item_detail])().HasKey(Function(t) New With {t.conum, t.detail_key, t.invoice_key})
        modelBuilder.Entity(Of [invoice_master])().HasKey(Function(t) New With {t.conum, t.invoice_key})
        modelBuilder.Entity(Of [invoice_salestax_detail])().HasKey(Function(t) New With {t.conum, t.inv_sales_tax_id, t.invoice_key})
        modelBuilder.Entity(Of [InvoiceCreditsPending])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [InvoicesDownloaded])().HasKey(Function(t) New With {t.CoNum, t.invoice_key})
        modelBuilder.Entity(Of [issues])().HasKey(Function(t) New With {t.issue_id})
        modelBuilder.Entity(Of [Job2])().HasKey(Function(t) New With {t.conum, t.Job_Code})
        modelBuilder.Entity(Of [Job3])().HasKey(Function(t) New With {t.conum, t.Job_Code})
        modelBuilder.Entity(Of [Job4])().HasKey(Function(t) New With {t.conum, t.Job_Code})
        modelBuilder.Entity(Of [Job5])().HasKey(Function(t) New With {t.conum, t.Job_Code})
        modelBuilder.Entity(Of [junk_table])().HasKey(Function(t) New With {t.CONUM, t.PRNUM})
        modelBuilder.Entity(Of [junk_table2])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [labor_distribution])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [LCR_NOTICES])().HasKey(Function(t) New With {t.NOTICE_ID})
        modelBuilder.Entity(Of [LEASE_GROUPS])().HasKey(Function(t) New With {t.lease_id})
        modelBuilder.Entity(Of [live_parameter_definition])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report_access])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report_columns])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report_display])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report_favorite])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report_parameter])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [live_report_script])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [LOCAL])().HasKey(Function(t) New With {t.CODE, t.STARTDATE})
        modelBuilder.Entity(Of [LOCAL_EE_INFO])().HasKey(Function(t) New With {t.conum, t.empnum, t.local_id})
        modelBuilder.Entity(Of [LOCAL_ID])().HasKey(Function(t) New With {t.CONUM, t.LOC_ID})
        modelBuilder.Entity(Of [local_tax])().HasKey(Function(t) New With {t.field_code, t.local_type, t.state_code})
        modelBuilder.Entity(Of [local_tax_triggers])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [LocalsW2FilingControl])().HasKey(Function(t) New With {t.CODE, t.CONUM, t.State, t.YR})
        modelBuilder.Entity(Of [LocalsW2FilingControlNew])().HasKey(Function(t) New With {t.CONUM, t.state, t.tax_type, t.Yr})
        modelBuilder.Entity(Of [Log])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [LOG_TYPE])().HasKey(Function(t) New With {t.type_id})
        modelBuilder.Entity(Of [Login_Activity])().HasKey(Function(t) New With {t.CoNum, t.EmpNum, t.LoginDateTime})
        modelBuilder.Entity(Of [LogProcedureNote])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [Machine_Fingerprint])().HasKey(Function(t) New With {t.CoNum, t.EmpNum, t.MachineProfileID})
        modelBuilder.Entity(Of [MAN_CHK_DET_DED])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [MAN_CHK_DET_MEMO])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [MAN_CHK_DET_PAY])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [MAN_CHK_DET_TAX])().HasKey(Function(t) New With {t.chk_counter, t.conum, t.empnum, t.payroll_num, t.state, t.tax_type})
        modelBuilder.Entity(Of [MAN_CHK_MAST])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CONUM, t.EMPNUM})
        modelBuilder.Entity(Of [ManualCheckMasterExtension])().HasKey(Function(t) New With {t.ChkCounter, t.Conum, t.Empnum})
        modelBuilder.Entity(Of [MEMO])().HasKey(Function(t) New With {t.CONUM, t.MEMO_NUM})
        modelBuilder.Entity(Of [Messaging])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [MinimumWage])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [mpwClientErrors])().HasKey(Function(t) New With {t.conum, t.error_id, t.prnum})
        modelBuilder.Entity(Of [mpwEmpAutoLoad])().HasKey(Function(t) New With {t.conum, t.empnum})
        modelBuilder.Entity(Of [mpwLinks])().HasKey(Function(t) New With {t.link_id})
        modelBuilder.Entity(Of [mpwWebReturnMsgs])().HasKey(Function(t) New With {t.entry_id})
        modelBuilder.Entity(Of [mpwWebUserCos])().HasKey(Function(t) New With {t.conum, t.entry_id})
        modelBuilder.Entity(Of [mpwWebUserDepts])().HasKey(Function(t) New With {t.conum, t.deptnum, t.divnum, t.entry_id})
        modelBuilder.Entity(Of [mpwWebUserDivs])().HasKey(Function(t) New With {t.conum, t.divnum, t.entry_id})
        modelBuilder.Entity(Of [MSpeer_conflictdetectionconfigrequest])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [MSpeer_lsns])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [MtaFilingControl])().HasKey(Function(t) New With {t.CONUM, t.Qtr, t.YR})
        modelBuilder.Entity(Of [Multi_Company_Access])().HasKey(Function(t) New With {t.CoNum, t.DelegatedCoNum, t.EmpNum})
        modelBuilder.Entity(Of [NACHA])().HasKey(Function(t) New With {t.RecordID})
        modelBuilder.Entity(Of [NACHA_DD])().HasKey(Function(t) New With {t.ACCT_TYPE, t.CHK_NUM, t.CONUM, t.DIVNUM, t.EMP_ACCTNUM, t.EMP_ROUTING})
        modelBuilder.Entity(Of [NachaTaxDraft])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [NewSqlObjectsWhPermission])().HasKey(Function(t) New With {t.objName, t.SchemaID})
        modelBuilder.Entity(Of [NJ927SuiControl])().HasKey(Function(t) New With {t.CONUM, t.Qtr, t.YR})
        modelBuilder.Entity(Of [NJW2FilingControl])().HasKey(Function(t) New With {t.CONUM, t.YEAR})
        modelBuilder.Entity(Of [NOTE])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [notification_execution])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [notification_system])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [notifications])().HasKey(Function(t) New With {t.notify_id})
        modelBuilder.Entity(Of [NPC_DD])().HasKey(Function(t) New With {t.ACCT_TYPE, t.BANK_ACCT, t.CHK_NUM, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [NPC])().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of [NUMCHECK])().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of [NYS45FilingControl])().HasKey(Function(t) New With {t.CONUM, t.Qtr, t.YR})
        modelBuilder.Entity(Of [ObcVoidedChecks])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [OFF_ACCTS])().HasKey(Function(t) New With {t.off_acct_id})
        modelBuilder.Entity(Of [OFF_MAILTMPL])().HasKey(Function(t) New With {t.id, t.office_id})
        modelBuilder.Entity(Of [OFFICE_INFO])().HasKey(Function(t) New With {t.office_id})
        modelBuilder.Entity(Of [office_settings])().HasKey(Function(t) New With {t.office_id, t.UDF_DESCR})
        modelBuilder.Entity(Of [Onboarding])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [OnboardingAdditionalServices])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [OnboardingDropDown])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [OnboardingEmployeeDocuments])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [OnboardingEmployeeI9Documents])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [OnboardingEmployeeI9DocumentTypes])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [OnboardingEmployees])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM})
        modelBuilder.Entity(Of [OnboardingEssEmployees])().HasKey(Function(t) New With {t.CoNum, t.EmpNum})
        modelBuilder.Entity(Of [OnboardingEvent])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [OnboardingLocalEmployeeInfo])().HasKey(Function(t) New With {t.conum, t.empnum, t.local_id})
        modelBuilder.Entity(Of [OnboardingProgress])().HasKey(Function(t) New With {t.OnboardingId, t.Stage})
        modelBuilder.Entity(Of [OnboardingSelectedServices])().HasKey(Function(t) New With {t.OnboardingId, t.ServiceId})
        modelBuilder.Entity(Of [OnboardingStages])().HasKey(Function(t) New With {t.Stage})
        modelBuilder.Entity(Of [OnboardingStateEmployeeInfo])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.STATE})
        modelBuilder.Entity(Of [OTHER_PAY])().HasKey(Function(t) New With {t.CONUM, t.OTH_PAY_NUM})
        modelBuilder.Entity(Of [OtherStatesFilingConfig])().HasKey(Function(t) New With {t.STATE, t.TAX_TYPE})
        modelBuilder.Entity(Of [OtherStatesFilingConfigYtd])().HasKey(Function(t) New With {t.State})
        modelBuilder.Entity(Of [OtherStatesFilingControl])().HasKey(Function(t) New With {t.CONUM, t.Qtr, t.state, t.tax_type, t.YR})
        modelBuilder.Entity(Of [OtherStatesFilingControlYtd])().HasKey(Function(t) New With {t.CONUM, t.State, t.YR})
        modelBuilder.Entity(Of [otp_security])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [pay_grades])().HasKey(Function(t) New With {t.conum, t.pay_grade_id})
        modelBuilder.Entity(Of [PaydeckBanner])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PaydeckImpersonate])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PaydeckPayrollExtension])().HasKey(Function(t) New With {t.Conum, t.Prnum})
        modelBuilder.Entity(Of [PaydeckPayrollProcessSteps])().HasKey(Function(t) New With {t.CoNum, t.Name, t.PrNum})
        modelBuilder.Entity(Of [PaydeckPosterElite])().HasKey(Function(t) New With {t.CoNum, t.OrderType})
        modelBuilder.Entity(Of [PaydeckService])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PaydeckServiceButton])().HasKey(Function(t) New With {t.ButtonID})
        modelBuilder.Entity(Of [PaydeckServiceButtonSetting_X])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [PaydeckServiceChangeRequested])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [PaydeckServiceFeatures])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PaydeckServiceGroup])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PaydeckServicePricing])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PAYMENT_ADDRESS])().HasKey(Function(t) New With {t.payee_id})
        modelBuilder.Entity(Of [payroll_ext])().HasKey(Function(t) New With {t.conum, t.prnum})
        modelBuilder.Entity(Of [PAYROLL_FILEARCHIVE_T_TABLE])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [payroll_packarchive_TABLE])().HasKey(Function(t) New With {t.id, t.office_id, t.rpt_id})
        modelBuilder.Entity(Of [payroll_progress_bar])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [Payroll_Services])().HasKey(Function(t) New With {t.conum, t.payroll_num, t.service_key})
        modelBuilder.Entity(Of [PAYROLL])().HasKey(Function(t) New With {t.CONUM, t.PRNUM})
        modelBuilder.Entity(Of [PayrollAlert])().HasKey(Function(t) New With {t.CoNum, t.ID, t.PrNum})
        modelBuilder.Entity(Of [PayrollEmployeeFilters])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PayrollFunding])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [PayrollFundingRequest])().HasKey(Function(t) New With {t.CoNum, t.PayrollNum})
        modelBuilder.Entity(Of [PayrollPaydeckLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [PayrollReopened])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [PayrollReopenedNote])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [PayrollsInProcessMsgTypes])().HasKey(Function(t) New With {t.MsgType})
        modelBuilder.Entity(Of [PayrolStatusChanged])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [PDS])().HasKey(Function(t) New With {t.PDS_NUM})
        modelBuilder.Entity(Of [PDS_LOG])().HasKey(Function(t) New With {t.LOG_NUM, t.PDS_NUM})
        modelBuilder.Entity(Of [PDS_NOTES])().HasKey(Function(t) New With {t.note_id, t.pds_num})
        modelBuilder.Entity(Of [PDS_RESULT])().HasKey(Function(t) New With {t.res_id})
        modelBuilder.Entity(Of [PlaidAuth])().HasKey(Function(t) New With {t.AuthID})
        modelBuilder.Entity(Of [PlaidBalance])().HasKey(Function(t) New With {t.AcctName, t.PlaidID})
        modelBuilder.Entity(Of [PlaidTokens])().HasKey(Function(t) New With {t.PlaidID})
        modelBuilder.Entity(Of [PlaidWebHook])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [pm_review_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [pm_review_goal])().HasKey(Function(t) New With {t.goal_id})
        modelBuilder.Entity(Of [pm_review_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [pm_review_task])().HasKey(Function(t) New With {t.task_id})
        modelBuilder.Entity(Of [pm_reviews])().HasKey(Function(t) New With {t.review_id})
        modelBuilder.Entity(Of [positions])().HasKey(Function(t) New With {t.conum, t.position_id})
        modelBuilder.Entity(Of [pp_company_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [pp_employee_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [pp_employee_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [pp_global_lists])().HasKey(Function(t) New With {t.cat_id})
        modelBuilder.Entity(Of [PP_ImportError])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [PP_ImportErrorLine])().HasKey(Function(t) New With {t.ErrorLineID})
        modelBuilder.Entity(Of [PP_ImportSession])().HasKey(Function(t) New With {t.SessionID})
        modelBuilder.Entity(Of [pp_web_links])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [pphr_outbar_icons])().HasKey(Function(t) New With {t.icon})
        'modelBuilder.Entity(Of [pphr_outbar_items_panels])().HasKey(Function(t) New With {t.id})
        'modelBuilder.Entity(Of [pphr_outbar_items_panels])().HasNoKey()
        modelBuilder.Entity(Of [pphr_outbar_items_panels])().HasKey(Function(poip) New With {poip.conum, poip.panel, poip.item, poip.empnum})
        modelBuilder.Entity(Of [pphr_outbar_items])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [pphr_outbar_panels])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [PPI_Emp_Template])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [pr_batch_buffer_row])().HasKey(Function(t) New With {t.buffer_id, t.conum, t.line_id})
        modelBuilder.Entity(Of [pr_batch_buffer])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [pr_batch_employee_change])().HasKey(Function(t) New With {t.rowguid})
        modelBuilder.Entity(Of [pr_batch_grid_access])().HasKey(Function(t) New With {t.conum, t.empconum, t.empnum, t.schema_id})
        modelBuilder.Entity(Of [pr_batch_grid_column])().HasKey(Function(t) New With {t.code, t.schema_id, t.sql_column, t.type})
        modelBuilder.Entity(Of [pr_batch_grid_filters])().HasKey(Function(t) New With {t.column_name, t.conum, t.schema_id})
        modelBuilder.Entity(Of [pr_batch_grid_group])().HasKey(Function(t) New With {t.code, t.schema_id, t.type})
        modelBuilder.Entity(Of [pr_batch_grid_schema])().HasKey(Function(t) New With {t.schema_id})
        modelBuilder.Entity(Of [pr_batch_grid_totals])().HasKey(Function(t) New With {t.code, t.schema_id, t.sql_column, t.type})
        modelBuilder.Entity(Of [pr_batch_hours_only])().HasKey(Function(t) New With {t.rowguid})
        modelBuilder.Entity(Of [pr_batch_in_process])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [pr_batch_list])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [pr_batch_msg])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [pr_batch_note])().HasKey(Function(t) New With {t.NoteID})
        modelBuilder.Entity(Of [pr_batch_override_auto])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CL_CODE, t.CL_DEPT, t.CONUM, t.EMPNUM, t.PRNUM})
        modelBuilder.Entity(Of [pr_batch_override])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CONUM, t.EMPNUM, t.PRNUM})
        modelBuilder.Entity(Of [pr_batch_overrides_setup])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of pr_batch_parent_note)().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [pr_batch_row])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [pr_batch_rows2])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [PR_DELIVERY])().HasKey(Function(t) New With {t.CONUM, t.DIVNUM, t.PRNUM})
        modelBuilder.Entity(Of [PR_PROVIDER])().HasKey(Function(t) New With {t.prov_id})
        modelBuilder.Entity(Of [PriceIncreaseSuggestion])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [print_autolog])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [print_queue_sync])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [print_queue])().HasKey(Function(t) New With {t.conum, t.id, t.prnum})
        modelBuilder.Entity(Of [PrintingScan])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [PriorPayrollDocuments])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ProcedureUsageLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ProgramReleaseNotes])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [progress_bar_data])().HasKey(Function(t) New With {t.conum, t.payroll_num, t.process_name})
        modelBuilder.Entity(Of [PRTSETUP])().HasKey(Function(t) New With {t.NAME})
        modelBuilder.Entity(Of [QboConnections])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QeHoldLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QtrReportsToProcessAndSave])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [QtrReportsToProcessAndSaveMapping])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Queue])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QueueDetail])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QueueEmail])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QueuePayroll])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QueueProcessor])().HasKey(Function(t) New With {t.ProcessorName})
        modelBuilder.Entity(Of [QueueReport])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [QueueToActivate])().HasKey(Function(t) New With {t.QueueId})
        modelBuilder.Entity(Of [QYEND_HOLDS])().HasKey(Function(t) New With {t.CONUM, t.QYEND_ID})
        modelBuilder.Entity(Of [QYEND_SELECTIONS])().HasKey(Function(t) New With {t.QYEND_ID, t.SELECTION_NAME})
        modelBuilder.Entity(Of [qyend_templ_settings])().HasKey(Function(t) New With {t.templ_setting_id, t.template_id})
        modelBuilder.Entity(Of [qyend_templates])().HasKey(Function(t) New With {t.template_id})
        modelBuilder.Entity(Of [ReconFillingControl])().HasKey(Function(t) New With {t.CoNum, t.Qtr, t.YR})
        modelBuilder.Entity(Of [RenumberEmployee])().HasKey(Function(t) New With {t.ID})
        'modelBuilder.Entity(Of [report_data_request])().HasKey(Function(t) New With {t.request_id})
        modelBuilder.Entity(Of [report_data_response])().HasKey(Function(t) New With {t.response_id})
        modelBuilder.Entity(Of [report_request_log])().HasKey(Function(t) New With {t.log_id})
        modelBuilder.Entity(Of [report_request_service])().HasKey(Function(t) New With {t.service_id})
        modelBuilder.Entity(Of [report_request_service_usage])().HasKey(Function(t) New With {t.usage_id})
        modelBuilder.Entity(Of [ReportEmailedPassword])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ReportEmailTemplateCategory])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ReportEmailTemplateFile])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ReportEmailTemplateOverride])().HasKey(Function(t) New With {t.CoNum, t.TemplateID})
        modelBuilder.Entity(Of [ReportEmailTemplateParameter])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ReportEmailTemplatesCompany])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ReportEmailTeplate])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ReportEPParamMap])().HasKey(Function(t) New With {t.Param})
        modelBuilder.Entity(Of [ReportFavorites])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ReportMassEmailFile])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ReportMassEmailPdf])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ReportMassEmailTemplate])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ReportOptions])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ReportQueue])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [REPORTS_T_TABLE])().HasKey(Function(t) New With {t.REPORT_ID, t.REQUEST_ID})
        modelBuilder.Entity(Of [ReportScripts])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [ResolvedPayrollIssues])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [RESPONSE_LOG])().HasKey(Function(t) New With {t.COUNTER, t.RESPONSENUM})
        modelBuilder.Entity(Of [RESPONSE_MASTER])().HasKey(Function(t) New With {t.RESPONSENUM})
        modelBuilder.Entity(Of [RiskManageCapture])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [RiskManagementAlert])().HasKey(Function(t) New With {t.CoNum, t.PrNum})
        modelBuilder.Entity(Of [RowNum])().HasKey(Function(t) New With {t.RN})
        modelBuilder.Entity(Of [rpt_link_conditions])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [rpt_query_links])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [rpt_query])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [rpt_storage_forms])().HasKey(Function(t) New With {t.category, t.form})
        modelBuilder.Entity(Of [safety_correctives])().HasKey(Function(t) New With {t.corrective_id})
        modelBuilder.Entity(Of [safety_document])().HasKey(Function(t) New With {t.document_id})
        modelBuilder.Entity(Of [safety_incidents])().HasKey(Function(t) New With {t.incident_id})
        modelBuilder.Entity(Of [safety_note])().HasKey(Function(t) New With {t.note_id})
        modelBuilder.Entity(Of [safety_task])().HasKey(Function(t) New With {t.task_id})
        modelBuilder.Entity(Of [safety_treatments])().HasKey(Function(t) New With {t.treatment_id})
        modelBuilder.Entity(Of [SALES])().HasKey(Function(t) New With {t.CHK_CTR, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [SALESPEOPLE])().HasKey(Function(t) New With {t.SALESPERSON})
        modelBuilder.Entity(Of [salestax_groupdetails])().HasKey(Function(t) New With {t.sales_detail_id, t.sales_tax_id})
        modelBuilder.Entity(Of [salestax_info])().HasKey(Function(t) New With {t.sales_tax_id})
        modelBuilder.Entity(Of [salestax_itemrates])().HasKey(Function(t) New With {t.sales_tax_id, t.sales_tax_line_id})
        modelBuilder.Entity(Of [SavedCheckDates])().HasKey(Function(t) New With {t.CONUM, t.PRNUM})
        modelBuilder.Entity(Of [sb_accounts])().HasKey(Function(t) New With {t.account_key})
        modelBuilder.Entity(Of [sb_acct_bals])().HasKey(Function(t) New With {t.account_key, t.balance_key})
        modelBuilder.Entity(Of [sb_acct_srvcs])().HasKey(Function(t) New With {t.account_key, t.account_service})
        modelBuilder.Entity(Of [sb_banktrans])().HasKey(Function(t) New With {t.line_key})
        modelBuilder.Entity(Of [sb_categories])().HasKey(Function(t) New With {t.cat_id})
        modelBuilder.Entity(Of [sb_register])().HasKey(Function(t) New With {t.account_key, t.register_key})
        modelBuilder.Entity(Of [sb_register_split])().HasKey(Function(t) New With {t.account_key, t.register_key, t.split_key})
        modelBuilder.Entity(Of [sb_savedsearches])().HasKey(Function(t) New With {t.search_key})
        modelBuilder.Entity(Of [SCHCO_NCUEMAIL])().HasKey(Function(t) New With {t.conum, t.user_key})
        modelBuilder.Entity(Of [SCHCO_RPTUSER])().HasKey(Function(t) New With {t.user_key})
        modelBuilder.Entity(Of [SCHCO_USERRPT])().HasKey(Function(t) New With {t.conum, t.rpt_id, t.user_key})
        modelBuilder.Entity(Of [SCHCORPT_LIST])().HasKey(Function(t) New With {t.CONUM, t.ID, t.SRPT_ID, t.TRPT_ID, t.TSRPT_ID})
        modelBuilder.Entity(Of [SCHCORPT_PARAMS])().HasKey(Function(t) New With {t.CONUM, t.ID, t.PARAM_ID, t.SRPT_ID, t.TRPT_ID, t.TSRPT_ID})
        modelBuilder.Entity(Of [ScheduledSqlMaint])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SCHFED_FORMS_T_TABLE])().HasKey(Function(t) New With {t.CONUM, t.ID})
        modelBuilder.Entity(Of [SCHFED_FORMSEXCEPTIONS])().HasKey(Function(t) New With {t.CONUM, t.ID})
        modelBuilder.Entity(Of [SCHPRT_JOBS])().HasKey(Function(t) New With {t.CONUM, t.ID})
        modelBuilder.Entity(Of [schreport_authviewers])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [SCHRPT_LIST])().HasKey(Function(t) New With {t.SRPT_ID})
        modelBuilder.Entity(Of [SCHRPT_PARAMS])().HasKey(Function(t) New With {t.ID, t.PARAM_ID, t.SRPT_ID})
        modelBuilder.Entity(Of [SCHRPT_TEMPLATES])().HasKey(Function(t) New With {t.TRPT_ID})
        modelBuilder.Entity(Of [SCHTEMPLATE_RPTS])().HasKey(Function(t) New With {t.ID, t.SRPT_ID, t.TRPT_ID})
        modelBuilder.Entity(Of [SCHTEMPRPT_PARAMS])().HasKey(Function(t) New With {t.ID, t.PARAM_ID, t.SRPT_ID, t.TRPT_ID, t.TSRPT_ID})
        modelBuilder.Entity(Of [SECURITY])().HasKey(Function(t) New With {t.NAME})
        modelBuilder.Entity(Of [Security_epuser])().HasKey(Function(t) New With {t.LoginName, t.Person})
        modelBuilder.Entity(Of [security_hist])().HasKey(Function(t) New With {t.conum, t.empnum, t.id, t.office_id, t.user_key})
        modelBuilder.Entity(Of [SecurityAlertIncident])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SecurityAlerts])().HasKey(Function(t) New With {t.AlertID})
        modelBuilder.Entity(Of [SecurityAnswers])().HasKey(Function(t) New With {t.CoNum, t.EmpNum, t.SecurityAnswerID})
        modelBuilder.Entity(Of [SecurityImages])().HasKey(Function(t) New With {t.CoNum, t.EmpNum, t.SecureImageID})
        modelBuilder.Entity(Of [SecurityImageSetup])().HasKey(Function(t) New With {t.SecureImageID})
        modelBuilder.Entity(Of [SecurityQuestions])().HasKey(Function(t) New With {t.QuestionID})
        modelBuilder.Entity(Of [service_definition])().HasKey(Function(t) New With {t.service_id})
        modelBuilder.Entity(Of [service_eula])().HasKey(Function(t) New With {t.agreement_id})
        modelBuilder.Entity(Of [service_request])().HasKey(Function(t) New With {t.request_id})
        modelBuilder.Entity(Of [ServiceActivation])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Service])().HasKey(Function(t) New With {t.ServiceID})
        modelBuilder.Entity(Of [Service])().HasKey(Function(t) New With {t.ServiceID})
        modelBuilder.Entity(Of [ServicesOffered])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [ServiceState])().HasKey(Function(t) New With {t.CoNum, t.ServiceID})
        modelBuilder.Entity(Of [setting])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [ShipAddressOverride])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [smart_rpt_favorite])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [SMART_RPT])().HasKey(Function(t) New With {t.conum, t.empnum, t.id})
        modelBuilder.Entity(Of [SP_CHK])().HasKey(Function(t) New With {t.CHK_COUNTER, t.CHK_TYPE, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})
        modelBuilder.Entity(Of [special_char])().HasKey(Function(t) New With {t.special_char})
        modelBuilder.Entity(Of [SpecialCharTreatment])().HasKey(Function(t) New With {t.AsciiCode})
        modelBuilder.Entity(Of [SQLERRORLOG])().HasKey(Function(t) New With {t.Error_guid, t.Error_id})
        modelBuilder.Entity(Of [SqlScript])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SqlScripts_Log])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SqlScriptsExpression])().HasKey(Function(t) New With {t.Expr})
        modelBuilder.Entity(Of [SqlScriptsLogUse])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SqlScriptsLogUseDetail])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SqlScriptsOption])().HasKey(Function(t) New With {t.SqlScriptsID})
        modelBuilder.Entity(Of [SqlScriptsParamAutoRun])().HasKey(Function(t) New With {t.ScriptID, t.ScriptParam})
        modelBuilder.Entity(Of [SSAW2FilingControl])().HasKey(Function(t) New With {t.CONUM, t.YEAR})
        modelBuilder.Entity(Of [ST_W2_Filling])().HasKey(Function(t) New With {t.Yr})
        modelBuilder.Entity(Of [STACK])().HasKey(Function(t) New With {t.CONUM, t.DBUSER, t.PRNUM, t.TYPE})
        modelBuilder.Entity(Of [STATE_A])().HasKey(Function(t) New With {t.ST_ABBR, t.STARTDATE})
        modelBuilder.Entity(Of [STATE_B])().HasKey(Function(t) New With {t.CODE, t.LOW_WAGE_LIMIT, t.ST_ABBR, t.STARTDATE})
        modelBuilder.Entity(Of [STATE_DEFAULTS])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [STATE_EE_INFO])().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.STATE})
        modelBuilder.Entity(Of [STATE_IDS])().HasKey(Function(t) New With {t.CONUM, t.ST_ABBRV, t.ST_QTR, t.ST_YEAR})
        modelBuilder.Entity(Of [STATE_INFO])().HasKey(Function(t) New With {t.ST_ABBR})
        modelBuilder.Entity(Of [state_minwage_coderates])().HasKey(Function(t) New With {t.minwage_code, t.st_abbr, t.startdate})
        modelBuilder.Entity(Of [state_minwage_codes])().HasKey(Function(t) New With {t.minwage_code})
        modelBuilder.Entity(Of [State_Reciprocity_List])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [state_registration_details])().HasKey(Function(t) New With {t.conum, t.id, t.modified_date, t.request_id})
        modelBuilder.Entity(Of [state_registration])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [state_tax])().HasKey(Function(t) New With {t.field_code, t.state_code})
        modelBuilder.Entity(Of [state_tax_payment_method])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [state_tax_report_table])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [state_tax_triggers])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [StateAbbrev])().HasKey(Function(t) New With {t.Abbr})
        modelBuilder.Entity(Of [Supply])().HasKey(Function(t) New With {t.ProductID})
        modelBuilder.Entity(Of [SuppliesInventory])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SuppliesOrderItem])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SuppliesOrder])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [SwipeClockAccrBalance])().HasKey(Function(t) New With {t.Category, t.EmployeeCode, t.SiteId})
        modelBuilder.Entity(Of [SwipeClockAccrBalanceImport])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [SYS_IMAGES])().HasKey(Function(t) New With {t.key_id})
        modelBuilder.Entity(Of [sysdiagrams])().HasKey(Function(t) New With {t.diagram_id})
        modelBuilder.Entity(Of [tasks])().HasKey(Function(t) New With {t.task_id})
        modelBuilder.Entity(Of [tax_credits])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tax_periods])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [Tax940])().HasKey(Function(t) New With {t.CreateDate, t.EIN, t.ReturnYear})
        modelBuilder.Entity(Of [taxable_fixes])().HasKey(Function(t) New With {t.change_date, t.chk_counter, t.conum, t.empnum, t.payroll_num, t.taxtype})
        modelBuilder.Entity(Of [TaxCarryFwdSheet])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [TaxRecon])().HasKey(Function(t) New With {t.CONUM, t.DIVNUM, t.PAYROLL_NUM, t.PrCtr, t.STATE, t.TAX_TYPE})
        modelBuilder.Entity(Of [TaxReconTransition_sb_register_MidFeb2018])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [TaxReconTransition_sb_register_MidFeb2018_W_Running_Balance])().HasKey(Function(t) New With {t.TranID})
        modelBuilder.Entity(Of [TaxServiceSwitch])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [tb_profile_output_copy_company])().HasKey(Function(t) New With {t.RowNumber})
        modelBuilder.Entity(Of [TechProjects])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [testLock])().HasKey(Function(t) New With {t.a})
        modelBuilder.Entity(Of [TicketNote])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [Ticket])().HasKey(Function(t) New With {t.TicketNum})
        modelBuilder.Entity(Of [Tickets_Locked])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Tooltips])().HasKey(Function(t) New With {t.TooltipID})
        modelBuilder.Entity(Of [trace_jan_22_2025_1830])().HasKey(Function(t) New With {t.RowNumber})
        modelBuilder.Entity(Of [tracking_conditions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_detail_events])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_details])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_event_executions])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_event_status])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_events])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_execution_details])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [tracking_services_data])().HasKey(Function(t) New With {t.employee_id})
        modelBuilder.Entity(Of [tracking_system])().HasKey(Function(t) New With {t.id})
        modelBuilder.Entity(Of [TrCh_Employee])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [UCI_TOTALS])().HasKey(Function(t) New With {t.CONUM, t.DIVNUM, t.PAYROLL_NUM, t.STATE, t.TAX_TYPE})
        modelBuilder.Entity(Of [UDF])().HasKey(Function(t) New With {t.name})
        modelBuilder.Entity(Of [UDF_DEF_VALUES])().HasKey(Function(t) New With {t.UDF_DESC, t.UDF_VALUE_ID})
        modelBuilder.Entity(Of [UDF_DEFAULTS])().HasKey(Function(t) New With {t.UDF_DESCR})
        modelBuilder.Entity(Of [UnionDepartment])().HasKey(Function(t) New With {t.Conum, t.DivNum, t.UnionNamePositionFund})
        modelBuilder.Entity(Of [UnionRate])().HasKey(Function(t) New With {t.EffectiveDate, t.UnionNamePositionFund})
        modelBuilder.Entity(Of [UsePPxLibraryLog])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [Void])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [W2ErSponsHlth])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [WA_LITAX])().HasKey(Function(t) New With {t.RISKCODE, t.STARTDATE})
        modelBuilder.Entity(Of [web_app_sso_preauth])().HasKey(Function(t) New With {t.conum, t.id})
        modelBuilder.Entity(Of [web_app_user_claims])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [web_app_user_logins])().HasKey(Function(t) New With {t.LoginProvider, t.ProviderKey})
        modelBuilder.Entity(Of [web_app_user_roles])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [web_app_user_tokens])().HasKey(Function(t) New With {t.LoginProvider, t.Name, t.UserId})
        modelBuilder.Entity(Of [web_app_user_userroles])().HasKey(Function(t) New With {t.RoleId, t.UserId})
        modelBuilder.Entity(Of [web_app_users])().HasKey(Function(t) New With {t.Id})
        modelBuilder.Entity(Of [WKMAN_COMP])().HasKey(Function(t) New With {t.CONUM, t.STARTDATE, t.WCODE})
        modelBuilder.Entity(Of [WY_WCTAX])().HasKey(Function(t) New With {t.MAJORCODE, t.SIC, t.STARTDATE})
        modelBuilder.Entity(Of [xxAUDIT_LOG_DATA])().HasKey(Function(t) New With {t.AUDIT_LOG_DATA_ID})
        modelBuilder.Entity(Of [xxAUDIT_LOG_TRANSACTIONS])().HasKey(Function(t) New With {t.AUDIT_LOG_TRANSACTION_ID})
        modelBuilder.Entity(Of [Zendesk_Organizations])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [Zendesk_OrganizationsHist])().HasKey(Function(t) New With {t.ID})
        modelBuilder.Entity(Of [Zendesk_OrganizationsZD])().HasKey(Function(t) New With {t.CoNum})
        modelBuilder.Entity(Of [ZendeskGroups])().HasKey(Function(t) New With {t.ID})

        'modelBuilder.Entity(Of ACAUpdateLowCostLog)().HasNoKey()
        'modelBuilder.Entity(Of ACT_AUTO_PriceChangesLog)().HasNoKey()
        'modelBuilder.Entity(Of _PrUtilityImportResults)().HasNoKey()
        'modelBuilder.Entity(Of ACA2016_Control_BackupDef)().HasNoKey()
        'modelBuilder.Entity(Of ACA2016_ControlBkup20241222)().HasNoKey()
        'modelBuilder.Entity(Of ACAUpdateLowCostLog)().HasNoKey()
        'modelBuilder.Entity(Of AccntMappingOrig)().HasNoKey()
        'modelBuilder.Entity(Of ACT_AUTO_PriceChangesLog)().HasNoKey()
        'modelBuilder.Entity(Of AODBalance)().HasNoKey()
        'modelBuilder.Entity(Of Audit_Logins)().HasNoKey()
        'modelBuilder.Entity(Of AuditLoginInfo)().HasNoKey()
        'modelBuilder.Entity(Of CHECK_NUMBERS_BACKUP)().HasNoKey()
        'modelBuilder.Entity(Of CHECK_NUMBERS_BACKUP_RVSD)().HasNoKey()


        'modelBuilder.Entity(Of CoContactsExtension)().HasNoKey()
        'modelBuilder.Entity(Of CompanyTermination)().HasNoKey()
        'modelBuilder.Entity(Of CovidReEligEmailNotify)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataEmpDDInfo)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataEmpPaysChk)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataEmpPaysChkReg)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataEmpPaysChkYtd)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataLast4QtrGross)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataLastVacSick)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataPayrollDeds1)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataPayrollTaxes1)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataRegisterDedsA)().HasNoKey()
        'modelBuilder.Entity(Of CR_TempDataYtdRegTaxes)().HasNoKey()
        'modelBuilder.Entity(Of DD_Reversal_Log1514)().HasNoKey()
        'modelBuilder.Entity(Of DdMessup02062024)().HasNoKey()
        'modelBuilder.Entity(Of EmailOutbound_SecurityInsert)().HasNoKey()
        'modelBuilder.Entity(Of Emails_Copy)().HasNoKey()
        'modelBuilder.Entity(Of ep_index_frag_hist)().HasNoKey()
        modelBuilder.Entity(Of EPSYS1)().HasKey(Function(t) New With {t.TYPE, t.DESCR})
        modelBuilder.Entity(Of FileCategory)().HasKey(Function(t) New With {t.FileCategory})
        'modelBuilder.Entity(Of execulog)().HasNoKey()
        'modelBuilder.Entity(Of execulog_details)().HasNoKey()
        'modelBuilder.Entity(Of execulog_keys)().HasNoKey()
        'modelBuilder.Entity(Of execulog_login)().HasNoKey()
        'modelBuilder.Entity(Of gl_enhanced_report)().HasNoKey()
        'modelBuilder.Entity(Of IHcolumns)().HasNoKey()
        'modelBuilder.Entity(Of IHpublishercolumns)().HasNoKey()
        modelBuilder.Entity(Of ImportEmployeesLoginTemplates)().HasKey(Function(t) New With {t.ImportID, t.ImportName})
        'modelBuilder.Entity(Of ImportEmployeesLoginTemplatesBkup)().HasNoKey()
        'modelBuilder.Entity(Of integration_tlm_transactions_del)().HasNoKey()
        'modelBuilder.Entity(Of INVOICE_LOG)().HasNoKey()
        'modelBuilder.Entity(Of INVOICE_LOG_T_INSERT)().HasNoKey()
        'modelBuilder.Entity(Of InvoiceCreditsPendingLog)().HasNoKey()
        'modelBuilder.Entity(Of local_ee_info_temp)().HasNoKey()
        'modelBuilder.Entity(Of MSpeer_conflictdetectionconfigresponse)().HasNoKey()
        'modelBuilder.Entity(Of MSpeer_originatorid_history)().HasNoKey()
        'modelBuilder.Entity(Of MSpeer_request)().HasNoKey()
        'modelBuilder.Entity(Of MSpeer_response)().HasNoKey()
        'modelBuilder.Entity(Of MSpeer_topologyrequest)().HasNoKey()
        'modelBuilder.Entity(Of MSpeer_topologyresponse)().HasNoKey()
        'modelBuilder.Entity(Of MSpub_identity_range)().HasNoKey()
        'modelBuilder.Entity(Of NACHAENTRY)().HasNoKey()
        ''modelBuilder.Entity(Of NJ927_P)().HasNoKey()
        'modelBuilder.Entity(Of NJ927_Z)().HasNoKey()
        'modelBuilder.Entity(Of NJWR30_B)().HasNoKey()
        'modelBuilder.Entity(Of NJWR30_M)().HasNoKey()
        'modelBuilder.Entity(Of NYS45_A)().HasNoKey()
        'modelBuilder.Entity(Of NYS45_B)().HasNoKey()
        'modelBuilder.Entity(Of NYS45_C)().HasNoKey()
        'modelBuilder.Entity(Of NYS45_D)().HasNoKey()
        'modelBuilder.Entity(Of NYS45_E)().HasNoKey()
        'modelBuilder.Entity(Of NYS45ATT_A)().HasNoKey()
        'modelBuilder.Entity(Of NYS45ATT_E)().HasNoKey()
        'modelBuilder.Entity(Of NYS45ATT_F)().HasNoKey()
        'modelBuilder.Entity(Of NYS45ATT_W)().HasNoKey()
        'modelBuilder.Entity(Of NYS45Combined_A)().HasNoKey()
        'modelBuilder.Entity(Of NYS45Combined_E)().HasNoKey()
        'modelBuilder.Entity(Of NYS45Combined_S)().HasNoKey()
        'modelBuilder.Entity(Of NYS45Combined_V)().HasNoKey()
        'modelBuilder.Entity(Of NYS45Combined_Z)().HasNoKey()
        'modelBuilder.Entity(Of ObjectVersionControl)().HasNoKey()
        'modelBuilder.Entity(Of OldXXX1059ZenDeskTicketData)().HasNoKey()
        'modelBuilder.Entity(Of OtherStatesFilingControlDeleted)().HasNoKey()
        'modelBuilder.Entity(Of OutageEmailDec_2020)().HasNoKey()
        'modelBuilder.Entity(Of PaydeckPosterEliteTrans)().HasNoKey()
        'modelBuilder.Entity(Of PaydeckServiceClientSetup)().HasNoKey()
        'modelBuilder.Entity(Of PaydeckServiceSvcButton_discontinued)().HasNoKey()
        'modelBuilder.Entity(Of PAYROLL_FILEARCHIVE_T_deleted_since_Dec_20_20)().HasNoKey()
        'modelBuilder.Entity(Of PayrollSubmmited)().HasNoKey()
        'modelBuilder.Entity(Of PDS_QUOTE)().HasNoKey()
        'modelBuilder.Entity(Of PDS_TEMPLATES)().HasNoKey()
        modelBuilder.Entity(Of pr_batch_overrides_del)().HasNoKey()
        'modelBuilder.Entity(Of pr_batch_rows_del)().HasNoKey()
        'modelBuilder.Entity(Of pr_batch_rows_insert)().HasNoKey()
        'modelBuilder.Entity(Of Priors_Shma_Koleynu)().HasNoKey()
        'modelBuilder.Entity(Of QeControl)().HasNoKey()
        modelBuilder.Entity(Of REPORT_LIST)().HasKey(Function(t) New With {t.REPORT_ID, t.rowguid})
        'modelBuilder.Entity(Of ReportEmailTeplate_bkup20241105)().HasNoKey()
        'modelBuilder.Entity(Of ReportParameterHolders)().HasNoKey()
        'modelBuilder.Entity(Of ReportParameterValues)().HasNoKey()
        'modelBuilder.Entity(Of ResourcesLinks)().HasNoKey()
        'modelBuilder.Entity(Of revlogin_export)().HasNoKey()
        'modelBuilder.Entity(Of SCH_EXPPARAMS)().HasNoKey()
        'modelBuilder.Entity(Of sch_reportfolio_book)().HasNoKey()
        'modelBuilder.Entity(Of SCHCORPT_PARAMS_T1)().HasNoKey()
        'modelBuilder.Entity(Of SCHCORPT_PARAMS_T2)().HasNoKey()
        'modelBuilder.Entity(Of SCHFED_FORMS_T_deleted_since_Dec_20_20)().HasNoKey()
        'modelBuilder.Entity(Of SCHRPT_NAMEREPL)().HasNoKey()
        'modelBuilder.Entity(Of SqlScripts_Bkup_Oct282021)().HasNoKey()
        'modelBuilder.Entity(Of SSAW2_RE)().HasNoKey()
        'modelBuilder.Entity(Of StateFilingInfo)().HasNoKey()
        'modelBuilder.Entity(Of STRCTR_MAST)().HasNoKey()
        'modelBuilder.Entity(Of SwipeClockData)().HasNoKey()
        'modelBuilder.Entity(Of sysarticlecolumns)().HasNoKey()
        'modelBuilder.Entity(Of sysarticles)().HasNoKey()
        'modelBuilder.Entity(Of sysarticleupdates)().HasNoKey()
        'modelBuilder.Entity(Of syspublications)().HasNoKey()
        'modelBuilder.Entity(Of sysschemaarticles)().HasNoKey()
        'modelBuilder.Entity(Of syssubscriptions)().HasNoKey()
        'modelBuilder.Entity(Of systranschemas)().HasNoKey()
        'modelBuilder.Entity(Of Tax1099)().HasNoKey()
        'modelBuilder.Entity(Of Tax941)().HasNoKey()
        'modelBuilder.Entity(Of TAXCODES)().HasNoKey()
        'modelBuilder.Entity(Of TaxNoticeBkup)().HasNoKey()
        'modelBuilder.Entity(Of TaxReconTransition_NACHA_TABLE_Update_Mid_Aug2018)().HasNoKey()
        'modelBuilder.Entity(Of TaxReconTransition_NACHA_TABLE_Update_MidFeb2018)().HasNoKey()
        'modelBuilder.Entity(Of TaxReconTransition_sb_register_Mid_Aug2018)().HasNoKey()
        'modelBuilder.Entity(Of TaxReconTransition_sb_register_Mid_Aug2018_W_Running_Balance)().HasNoKey()
        'modelBuilder.Entity(Of TelebroadPhoneUrsJsonLogs)().HasNoKey()
        'modelBuilder.Entity(Of TempMatch)().HasNoKey()
        'modelBuilder.Entity(Of TempQueueCancel)().HasNoKey()
        'modelBuilder.Entity(Of test1)().HasNoKey()
        'modelBuilder.Entity(Of test2)().HasNoKey()
        'modelBuilder.Entity(Of test3)().HasNoKey()
        'modelBuilder.Entity(Of tmp_EP_Form941RecalculateEligibleWagesByCheck)().HasNoKey()
        'modelBuilder.Entity(Of tmp2)().HasNoKey()
        'modelBuilder.Entity(Of UCI_CREDIT)().HasNoKey()
        'modelBuilder.Entity(Of UnionMatchRetro)().HasNoKey()
        'modelBuilder.Entity(Of W2ErSponsHlth1514)().HasNoKey()
        modelBuilder.Entity(Of ZIP)().HasKey(Function(t) New With {t.ZIP})

        modelBuilder.Entity(Of EMP_DEDS_SETUP)().HasKey(Function(t) New With {t.CONUM, t.DED_DEPTNUM, t.DED_DIVNUM, t.DED_NUM_E, t.EMPNUM})
        modelBuilder.Entity(Of [EmailAttachment])().HasKey(Function(t) New With {t.AttachmentID, t.EmailID})
        modelBuilder.Entity(Of SqlScriptsSavedDataXml).HasKey(Function(t) New With {t.ScriptID})
        modelBuilder.Entity(Of view_FaxAndEmail)().HasKey(Function(t) New With {t.Source, t.ID})


        modelBuilder.Entity(Of DBUSER)().HasKey(Function(t) New With {t.name})
        'modelBuilder.Entity(Of FileCategory)().HasNoKey()
        'modelBuilder.Entity(Of GTL)().HasNoKey()
        modelBuilder.Entity(Of IntegerResult)().HasNoKey()
        modelBuilder.Entity(Of StringResult)().HasNoKey()
        'modelBuilder.Entity(Of InvoiceIncreaseToApproveV11059)().HasNoKey()
        modelBuilder.Entity(Of Manual_Trans_Account_list)().HasKey(Function(t) New With {t.CONUM, t.DDIVNUM})
        'modelBuilder.Entity(Of NYS45ATT_T)().HasNoKey()
        modelBuilder.Entity(Of REPORT)().HasKey(Function(t) New With {t.REPORT_ID, t.REQUEST_ID})
        'modelBuilder.Entity(Of Test)().HasNoKey()
        'modelBuilder.Entity(Of co_contacts900_t)().HasNoKey()
        modelBuilder.Entity(Of document_storage)().HasKey(Function(t) New With {t.document_id, t.storage_id})

        modelBuilder.Entity(Of ep_ReportPayrollDedsResult)().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.PAYROLL_NUM, t.CHK_COUNTER, t.Type, t.Sort, t.Deductions, t.Department, t.job_descr, t.Job2Desc, t.Job3Desc, t.Job4Desc, t.Job5Desc, t.Amount})
        modelBuilder.Entity(Of ep_ReportPayrollTaxesResult)().HasKey(Function(t) New With {t.Gross, t.row_num, t.prnum, t.empnum, t.Cat, t.chk_counter, t.current, t.descr, t.taxable, t.ytd})
        modelBuilder.Entity(Of fn_1059DeptResult)().HasKey(Function(t) New With {t.Dept})
        modelBuilder.Entity(Of fn_1059MeasureUsersByDeptResult)().HasKey(Function(t) New With {t.EmpNum, t.Agent, t.Dept})
        'modelBuilder.Entity(Of fn_FrontDeskUserRolesResult)().HasNoKey()
        'modelBuilder.Entity(Of fn_GetPayrollReportsResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_1059ScoreCardXmlResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_BrandsPowerGridColumnsResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_CheckPayrollIssuesResult)().HasNoKey()
        modelBuilder.Entity(Of prc_ExportFormatMarkResendResult)().HasKey(Function(t) New With {t.voidedAlready, t.rowsCount, t.updatedRowCount})
        'modelBuilder.Entity(Of prc_GetAllCompanyEmailsResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_GetAllRelatedContactsResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_GetAutoPaysForPayrollResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_GetBankAccountsResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_GetCommItemsForComapnyResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_GetCompanyServicesResult)().HasNoKey()
        modelBuilder.Entity(Of prc_GetCoRankResult)().HasKey(Function(t) New With {t.Priority})
        'modelBuilder.Entity(Of prc_GetDD_DraftsResult)().HasNoKey()
        'modelBuilder.Entity(Of prc_GetExportFormatResult)().HasNoKey()
        modelBuilder.Entity(Of prc_GetManualChecksResult)().HasKey(Function(t) New With {t.mpwStatus, t.EMPNUM, t.DIVNUM, t.CHK_COUNTER, t.SSN, t.Payee, t.check_date, t.CHK_NUM, t.GROSS, t.NET})
        modelBuilder.Entity(Of prc_GetPayrollsNotAttachedToCalendersResult)().HasKey(Function(t) New With {t.PRNUM, t.PER1_ST_DATE, t.PER1_END_DATE, t.CHECK_DATE, t.PAYROLL_STATUS, t.PR_DESCR, t.CalStartDate, t.CalEndDate, t.CalCheckDate, t.cal_id})
        modelBuilder.Entity(Of prc_GetShippingAddressResult)().HasKey(Function(t) New With {t.CONUM, t.CO_NAME, t.DDIVNUM, t.DDIVNAME, t.DivShip, t.CoShip, t.ShipCo, t.ShipAddress, t.ShipCity, t.ShipState, t.ShipZip, t.ShipContact, t.ShipPhone, t.ShipExtension, t.ShipModem, t.ShipMethod, t.ShipCharge, t.EEcnt, t.DivAddress})
        modelBuilder.Entity(Of prc_GetVoidsToVoidResult)().HasKey(Function(t) New With {t.CoNum, t.PrNum, t.EmpNum, t.ChkCounter, t.ChkType, t.ChkNum, t.IsSPCheck, t.VoidPrNum, t.UserName, t.DateEntered, t.VOID_DATE, t.CHECK_DATE, t.CHK_TYPE, t.Name, t.NET, t.GROSS, t.IsVoided})
        modelBuilder.Entity(Of prc_InsTaxReconResult)().HasKey(Function(t) New With {t.NewRegisterID})

        modelBuilder.Entity(Of prc_MinimumWageRequirementsResult)().HasKey(Function(t) New With {t.CoNum, t.CO_NAME, t.CO_STREET, t.CO_CITY, t.CO_STATE, t.CO_ZIP, t.OP_OWNER, t.CO_EMAIL, t.CO_FAX, t.CO_PHONE, t.PR_CONTACT, t.CO_EXTENSION, t.office_id, t.EMPNUM, t.L_NAME, t.F_NAME, t.M_NAME, t.UCI_STATE, t.LAST_PAID_DT, t.RATE_1, t.Emp_UCI_County, t.CO_COUNTY, t.Industry, t.EEcnt, t.NewRate1, t.AutoHrs, t.AUTO_SAL_HRS, t.SALARY_AMT, t.NewSalary, t.NewMinWageRate, t.NewMinWageDate, t.NewMinTipRate, t.PrevMinTipRate, t.Num, t.HasNY, t.DIVNUM, t.DEPTNUM, t.TIP_DEPT, t.TipEE, t.Tips, t.ID, t.effective_date, t.amount, t.Type, t.UpdateType, t.UpdateToRate, t.Issue})
        modelBuilder.Entity(Of prc_NyTaxOverdueResult)().HasKey(Function(t) New With {t.CONUM, t.TaxService, t.Amount, t.ST_DEP_DATE})
        modelBuilder.Entity(Of prc_PayrollsIncompleteResult)().HasKey(Function(t) New With {t.CONUM, t.CO_NAME, t.PRNUM, t.CHECK_DATE, t.START_TIME, t.MinutesElapsed, t.Issue, t.OPNAME, t.ENTRY_TYPE, t.Subbmitted, t.ProcessStatus, t.PAYROLL_STATUS, t.PR_DESCR, t.CkCnt, t.DdCnt, t.TaxService, t.QYEND_HOLD_TYPE, t.LastOpenedBy, t.CalEntryType, t.Elapsed})
        modelBuilder.Entity(Of prc_PrUtilityImportResult)().HasKey(Function(t) New With {t.EMPNUM, t.CHK_COUNTER, t.DIVNUM, t.DEPT, t.PDM, t.CL_CODE, t.Amount, t.Rate, t.RegHours, t.YTDAmount, t.MTDAmount, t.CurPayrollAmount, t.CL_NUM, t.CL_LOC_CD1, t.CL_LOC_CD2, t.CL_LOC_CD3, t.CL_LOC_CD4, t.CL_LOC_CD5, t.job_id, t.Job2, t.Job3, t.Job4, t.Job5, t.UnionNamePositionfund, t.DeleteExisting, t.WRKMNS_COMP_CD, t.work_local, t.res_local})
        modelBuilder.Entity(Of prc_QtrEndEmailResult)().HasKey(Function(t) New With {t.CONUM, t.Year, t.Qtr})
        modelBuilder.Entity(Of prc_ReopenPayrollResult)().HasKey(Function(t) New With {t.CONUM, t.PRNUM, t.PAYROLL_STATUS, t.BILL_ACH_STAT, t.TOTALTAX_STATUS, t.AchTyp, t.AchAmt, t.AchStatus, t.FilesSent, t.FileName, t.Action, t.ActionDate, t.EmailAddress, t.Row, t.AllowReopen, t.Notes})
        modelBuilder.Entity(Of prc_ReportOptionsResult)().HasKey(Function(t) New With {t.SRPT_ID, t.Name, t.Description, t.Path, t.CONUM, t.rptOption, t.RptFunction, t.Category, t.DefaultOption, t.Comments, t.ReportsApplied, t.CoNumApplied, t.TimesReportSelected, t.Used, t.IsSet, t.Category1, t.Usage})
        modelBuilder.Entity(Of prc_RptActiveEmpsNotPaidResult)().HasKey(Function(t) New With {t.CONUM, t.EMPNUM, t.F_NAME, t.M_NAME, t.L_NAME, t.TERM_DATE, t.LAST_PAID_DT, t.LstCoPr, t.LstPr, t.Note, t.Note2, t.InactiveFor})
        modelBuilder.Entity(Of prc_RptPayrollAlertsResult)().HasKey(Function(t) New With {t.Plaid, t.CONUM, t.CO_NAME, t.Verified, t.PRNUM, t.PAYROLL_STATUS, t.CHECK_DATE, t.DaysToNBD, t.DD, t.FlaggedLimit, t.DdPerPrLimit, t.FlaggedLimitVar, t.Tax, t.FlaggedTaxVar, t.Liab, t.LiabPct, t.GROSS, t.AvgGross, t.IncreasePct, t.NoRouting, t.FirstDDPrNum, t.OverLimit, t.OverLimitVar, t.OverLimitSet, t.OverTaxVar, t.HasOpenTran, t.HasPrPwd, t.IsUnscheduledPrl, t.NSF, t.PrlDaysBefCheckDate, t.Status, t.StatusChangeLog, t.Notes, t.OverMax, t.OverTaxMax, t.CoAlerts, t.EmpAlerts, t.NachaCnt, t.PlaidConnected, t.HasReverseWire, t.RUN_DATE, t.PlaidBalance, t.DDOrTax, t.LowTTF, t.AchRisk, t.RiskStatus, t.DDStatus, t.TaxStatus, t.DDhistory, t.Bill, t.LastNsf, t.ShortWith, t.AmtNeeded, t.StatusRec, t.StatusFinal, t.D7Issue})
        modelBuilder.Entity(Of prc_RptPrCoverSheetResult)().HasKey(Function(t) New With {t.CONUM, t.PRNUM, t.Type, t.CHECK_DATE, t.PER1_ST_DATE, t.PER1_END_DATE, t.LoginName, t.ComputerName, t.period_id, t.ProcessDay, t.status, t.DIVNUM, t.EeCKcnt, t.DDcnt, t.EmailedCnt, t.SPcnt, t.WeekNum, t.FED_ID, t.PR_CONTACT, t.DELVDESC, t.CO_NAME, t.CO_STREET, t.CO_CITY, t.CO_STATE, t.CO_ZIP, t.CO_PHONE, t.CO_EXTENSION, t.CO_MODEM, t.SH_NAME, t.SH_STREET, t.SH_CITY, t.SH_STATE, t.SH_ZIP, t.SH_PHONE, t.SH_EXTENSION, t.SH_MODEM, t.SHIPSAME, t.SH_CONFOR, t.CUR_PRNUM, t.OP_OWNER, t.rpt_pdfchks, t.QYEND_STATUS, t.QYEND_HOLD_TYPE, t.DDIVNUM, t.DDIVNAME, t.DPR_CONTACT, t.DDELVDESC, t.DSHIPSAME, t.DSH_NAME, t.DSH_STREET, t.DSH_CITY, t.DSH_STATE, t.DSH_ZIP, t.DSH_PHONE, t.DSH_EXTENSION, t.DSH_MODEM, t.O_NAME, t.O_STREET, t.O_CITY, t.O_STATE, t.O_ZIP, t.O_PHONE, t.O_FAX, t.O_EMAIL, t.office_id, t.udf21_data, t.PreviousPrintDate, t.AnnalCalendarShip, t.OrderRecvdBy, t.OrderDate, t.OrderWithNxtPr, t.OrderNextPayroll, t.OrderNotes, t.OrdersPending, t.DivShip, t.CoShip, t.DivShipOvr, t.CoShipOvr, t.ShipCo, t.ShipAddress, t.ShipCity, t.ShipState, t.ShipZip, t.ShipContact, t.ShipPhone, t.ShipExt, t.ShipModem})
        modelBuilder.Entity(Of prc_SelectPayrollToEditResult)().HasKey(Function(t) New With {t.CONUM, t.PAYROLL_NUM, t.CHK_COUNTER, t.EMPNUM, t.EmpName, t.CHECK_DATE, t.CHK_TYPE, t.line_date, t.PAY_FREQ, t.MaxClNum, t.CurEndDate, t.NewEndDate, t.CHK_NUM})
        modelBuilder.Entity(Of prc_TaxReconDetailsResult)().HasKey(Function(t) New With {t.Num, t.loc_id, t.catid, t.split_due, t.Debit, t.Credit, t.cat_descr, t.cat_subdescr, t.cat_glnum, t.split_paymentregkey, t.TempDesc, t.TDebits, t.TCredits, t.CurRate, t.PrevRate})
        modelBuilder.Entity(Of prc_TaxReconListResult)().HasKey(Function(t) New With {t.split_conum, t.payroll_num, t.split_debit, t.split_credit, t.split_due, t.tax_route, t.CO_NAME, t.CONUM, t.DIFF, t.cash_flow_sum, t.Co_status, t.UDF_DESCR, t.UDF_STRING, t.PR_PASSWORD, t.Has_NSF, t.NSF_Count, t.Has_Unpaid_Liabilities, t.Unpaid_Liabilities_Count, t.BlockTaxRecone, t.Unpaid_Liabilities, t.AchRisk})
        modelBuilder.Entity(Of prc_YearEnd_WithNoDecPayrollResult).HasKey(Function(t) New With {t.CONUM, t.CO_NAME, t.CO_BILL_FREQ, t.LstChkDtOfYr, t.PR_PASSWORD, t.DecRunDate, t.JanRunDate, t.UDF_STRING, t.CoStatus, t.Notes, t.OpenPayrollStatus, t.PR_CONTACT, t.CO_PHONE, t.CO_EXTENSION, t.CO_FAX, t.CO_MODEM, t.PRICE, t.EmpCnt, t.NextProcessDate, t.entry_type, t.Frequency, t.QtrJob, t.ChkDateToBe, t.DecPrForUI, t.W2Bill, t.OpenInvPrPr, t.InvDate, t.TotalTax, t.QYEND_HOLD_TYPE, t.PR_DESCR, t.VoidOnly})

        'modelBuilder.Entity(Of ReportResults)().HasNoKey()
        'modelBuilder.Entity(Of StringResult)().HasNoKey()
        'modelBuilder.Entity(Of _PrUtilityImportResults)().HasNoKey()
        modelBuilder.Entity(Of fn_NextScheduledPayrollResult)().HasKey(Function(t) New With {t.conum})
        modelBuilder.Entity(Of vMaxCkDate)().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of view_ACT_ITEM)().HasKey(Function(t) New With {t.ITEM_NUM})
        modelBuilder.Entity(Of view_BillingCredit)().HasKey(Function(t) New With {t.VRN})
        modelBuilder.Entity(Of view_Calendar)().HasKey(Function(t) New With {t.CalID})
        modelBuilder.Entity(Of view_CompanySumarry)().HasKey(Function(t) New With {t.CONUM})
        modelBuilder.Entity(Of view_CovidEmailNotify)().HasKey(Function(t) New With {t.CONUM})
        'modelBuilder.Entity(Of view_Delivery)().HasKey(Function(t) New With {t.VD_RN})
        modelBuilder.Entity(Of view_Delivery)().HasNoKey()
        modelBuilder.Entity(Of view_epsusers)().HasKey(Function(t) New With {t.username})
        modelBuilder.Entity(Of view_MinimumWageReq)().HasKey(Function(t) New With {t.CONUM, t.EMPNUM})
        modelBuilder.Entity(Of view_OrderItemInvoice)().HasKey(Function(t) New With {t.CoNum, t.OrderId, t.OrderItemId})
        modelBuilder.Entity(Of view_PayrollAletsGrouped)().HasKey(Function(t) New With {t.CoNum, t.PrNum})
        modelBuilder.Entity(Of view_PayrollInProcess)().HasKey(Function(t) New With {t.CoNum, t.PrNum})
        modelBuilder.Entity(Of view_PlaidTokens)().HasKey(Function(t) New With {t.CoNum, t.PlaidID})
        '' Configure primary key instead of HasNoKey() to fix column ordering
        modelBuilder.Entity(Of view_pr_batch_in_process)().HasKey(Function(e) e.ID)
        modelBuilder.Entity(Of view_QuarterlyAllProcessAndDelivery)().HasKey(Function(t) New With {t.VRN})
        modelBuilder.Entity(Of view_QuarterlyShipAndEmailControl)().HasKey(Function(t) New With {t.CONUM, t.Year, t.Qtr})
        modelBuilder.Entity(Of view_REPORTS_UNION)().HasKey(Function(t) New With {t.ID, t.CONUM})
        modelBuilder.Entity(Of view_Reverse_DD_Transaction)().HasKey(Function(t) New With {t.VRN})


        modelBuilder.Entity(Of prc_GetPrOpenBatch_Result)().HasKey(Function(t) New With {t.id, t.conum, t.name, t.provider_id, t.status, t.prnum, t.obj_ref, t.created_by, t.modified_by, t.created_date, t.modify_date, t.run_date, t.run_add_checks, t.run_auto_hours, t.run_auto_pays, t.run_auto_deds, t.run_auto_memos, t.run_sick, t.run_vacation, t.run_personal, t.run_pay_salary, t.run_override_rate, t.run_dd_flag, t.run_override_freq, t.schema_id, t.total_hours_pr, t.total_imported_hours_pr, t.total_ckeck_number_pr, t.paid, t.hours, t.regular_hours, t.overtime_hours, t.dollars})
        modelBuilder.Entity(Of CHK_MAST)().HasKey(Function(t) New With {t.CHK_COUNTER, t.CONUM, t.EMPNUM, t.PAYROLL_NUM})



        modelBuilder.Entity(Of OtherStatesFilingControl)().
HasOne(Function(c) c.Company).
WithMany(Function(o) o.OtherStatesFilingControl).
HasForeignKey(Function(o) o.CONUM)

        modelBuilder.Entity(Of PAYROLL)().
HasOne(Function(c) c.payroll_ext_ts).
WithOne(Function(o) o.Payroll).
HasForeignKey(Of payroll_ext)(Function(pe) New With {pe.conum, pe.prnum}).
HasPrincipalKey(Of PAYROLL)(Function(c) New With {c.CONUM, c.PRNUM})

        'modelBuilder.Entity(Of payroll_ext)().
        '   Property(Function(pe) New With {pe.conum, pe.prnum}).
        '   ValueGeneratedNever()

        modelBuilder.Entity(Of pr_batch_grid_group)().
HasOne(Function(c) c.pr_batch_grid_schema).
WithMany(Function(o) o.pr_batch_grid_groups).
HasForeignKey(Function(o) o.schema_id)

        modelBuilder.Entity(Of pr_batch_grid_column)().
HasOne(Function(c) c.pr_batch_grid_schema).
WithMany(Function(o) o.pr_batch_grid_columns).
HasForeignKey(Function(o) o.schema_id)

        modelBuilder.Entity(Of pr_batch_row)().
HasOne(Function(c) c.pr_batch_list).
WithMany(Function(o) o.pr_batch_rows).
HasForeignKey(Function(o) o.batch_id)

        modelBuilder.Entity(Of NOTE)().
HasOne(Function(c) c.Company).
WithMany(Function(o) o.NOTEs).
HasForeignKey(Function(o) o.conum)

        modelBuilder.Entity(Of CHK_DET_DED)().
HasOne(Function(c) c.CHK_MAST).
WithMany(Function(o) o.CHK_DET_DEDs).
HasForeignKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER})

        modelBuilder.Entity(Of CHK_DET_PAY)().
HasOne(Function(c) c.CHK_MAST).
WithMany(Function(o) o.CHK_DET_PAYs).
HasForeignKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER})

        modelBuilder.Entity(Of CHK_DET_DED)().
HasOne(Function(c) c.DEDUCTION).
WithMany(Function(o) o.CHK_DET_DEDs).
HasForeignKey(Function(o) New With {o.CONUM, o.CL_CODE})

        modelBuilder.Entity(Of CHK_DET_MEMO)().
HasOne(Function(c) c.CHK_MAST).
WithMany(Function(o) o.CHK_DET_MEMOs).
HasForeignKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER})

        modelBuilder.Entity(Of FrontDeskRoleUser)().
HasOne(Function(c) c.FrontDeskRole).
WithMany(Function(o) o.FrontDeskRoleUsers).
HasForeignKey(Function(o) New With {o.RoleID})

        modelBuilder.Entity(Of MAN_CHK_DET_PAY)().
HasOne(Function(c) c.MAN_CHK_MAST).
WithMany(Function(o) o.MAN_CHK_DET_PAYs).
HasForeignKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER}).
HasPrincipalKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER})

        modelBuilder.Entity(Of MAN_CHK_DET_DED)().
HasOne(Function(c) c.MAN_CHK_MAST).
WithMany(Function(o) o.MAN_CHK_DET_DEDs).
HasForeignKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER}).
HasPrincipalKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER})

        modelBuilder.Entity(Of MAN_CHK_DET_MEMO)().
HasOne(Function(c) c.MAN_CHK_MAST).
WithMany(Function(o) o.MAN_CHK_DET_MEMOs).
HasForeignKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER}).
HasPrincipalKey(Function(o) New With {o.CONUM, o.PAYROLL_NUM, o.EMPNUM, o.CHK_COUNTER})

        modelBuilder.Entity(Of view_BankingManualTaxAdjCtrlDetail)().
HasOne(Function(c) c.view_BankingManualTaxAdjCtrl).
WithMany(Function(o) o.view_BankingManualTaxAdjCtrlDetails).
HasForeignKey(Function(o) New With {o.MasterID})

        modelBuilder.Entity(Of view_BankingManualTaxAdjCtrlPayment)().
HasOne(Function(c) c.view_BankingManualTaxAdjCtrlDetail).
WithMany(Function(o) o.view_BankingManualTaxAdjCtrlPayments).
HasForeignKey(Function(o) New With {o.DetailID})

        modelBuilder.Entity(Of Fax)().
HasOne(Function(c) c.FaxCategory).
WithMany(Function(o) o.Faxs).
HasForeignKey(Function(o) New With {o.Category})

        modelBuilder.Entity(Of Email)().
HasOne(Function(c) c.FaxCategory).
WithMany(Function(o) o.Emails).
HasForeignKey(Function(o) New With {o.Category})

        modelBuilder.Entity(Of view_FaxAndEmail)().
HasOne(Function(c) c.FaxCategory).
WithMany(Function(o) o.view_FaxAndEmails).
HasForeignKey(Function(o) New With {o.EmailID})

        modelBuilder.Entity(Of SqlScript)().
HasOne(Function(c) c.SqlScriptsOption).
WithOne(Function(o) o.SqlScript).
HasForeignKey(Of SqlScriptsOption)(Function(o) o.SqlScriptsID)

        modelBuilder.Entity(Of SuppliesOrderItem)().
HasOne(Function(c) c.SuppliesOrder).
WithMany(Function(o) o.SuppliesOrderItems).
HasForeignKey(Function(o) New With {o.OrderID})

        modelBuilder.Entity(Of SuppliesOrderItem)().
HasOne(Function(c) c.Supply).
WithMany(Function(o) o.SuppliesOrderItems).
HasForeignKey(Function(o) New With {o.ProductID})

        modelBuilder.Entity(Of SuppliesInventory)().
HasOne(Function(c) c.Supply).
WithMany(Function(o) o.SuppliesInventorys).
HasForeignKey(Function(o) New With {o.ProductId})

        modelBuilder.Entity(Of SqlScriptsLogUseDetail)().
HasOne(Function(c) c.SqlScriptsLogUse).
WithMany(Function(o) o.SqlScriptsLogUseDetails).
HasForeignKey(Function(o) New With {o.LogUseID})

        '        modelBuilder.Entity(Of SqlScriptsLogUseDetail)().
        'HasOne(Function(c) c.SqlScript).
        'WithMany(Function(o) o.SqlScriptsLogUseDetails).
        'HasForeignKey(Function(o) New With {o.LogUseID})

        '        modelBuilder.Entity(Of SqlScriptsLogUseDetail)().
        'HasOne(Function(c) c.SqlScriptsLogUse).
        'WithMany(Function(o) o.SqlScriptsLogUseDetails).
        'HasForeignKey(Function(o) New With {o.LogUseID})

        modelBuilder.Entity(Of Fax)().
HasOne(Function(c) c.Ticket).
WithMany(Function(o) o.Faxes).
HasForeignKey(Function(o) New With {o.TicketNum})

        modelBuilder.Entity(Of Email)().
HasOne(Function(c) c.Ticket).
WithMany(Function(o) o.Emails).
HasForeignKey(Function(o) New With {o.TicketNum})

        modelBuilder.Entity(Of FrontDeskTicket)().
HasOne(Function(c) c.Ticket).
WithMany(Function(o) o.FrontDeskTickets).
HasForeignKey(Function(o) New With {o.TicketNum})

        modelBuilder.Entity(Of EmailAttachment)().
HasOne(Function(c) c.Email).
WithMany(Function(o) o.EmailAttachments).
HasForeignKey(Function(o) New With {o.EmailID})

        modelBuilder.Entity(Of CustomReportColumnTemplate)().
HasOne(Function(c) c.CustomReportTable).
WithMany(Function(o) o.CustomReportColumnTemplates).
HasForeignKey(Function(o) New With {o.TableId})

        modelBuilder.Entity(Of Delivery)().
HasOne(Function(c) c.DeliveryTicket).
WithMany(Function(o) o.Deliveries).
HasForeignKey(Function(o) New With {o.TicketID})

        modelBuilder.Entity(Of CustomReportTableLink)().
HasOne(Function(c) c.CustomReportTable).
WithMany(Function(o) o.CustomReportTableLinks).
HasForeignKey(Function(o) New With {o.TableId})

        modelBuilder.Entity(Of CustomReportTableLink)().
HasOne(Function(c) c.CustomReport).
WithMany(Function(o) o.CustomReportTableLinks).
HasForeignKey(Function(o) New With {o.ReportId})

        modelBuilder.Entity(Of FrontDeskTicketAttachment)().
HasOne(Function(c) c.FrontDeskTicket).
WithMany(Function(o) o.FrontDeskTicketAttachments).
HasForeignKey(Function(o) New With {o.TicketID})

        modelBuilder.Entity(Of SqlScriptsParamAutoRun)().
HasOne(Function(c) c.SqlScriptsExpression).
WithMany(Function(o) o.SqlScriptsParamAutoRuns).
HasForeignKey(Function(o) New With {o.Expr})

        modelBuilder.Entity(Of ReportMassEmailFile)().
HasOne(Function(c) c.ReportMassEmailTemplate).
WithMany(Function(o) o.ReportMassEmailFiles).
HasForeignKey(Function(o) New With {o.ReportMassEmailTemplateID})

        modelBuilder.Entity(Of ReportMassEmailPdf)().
HasOne(Function(c) c.ReportMassEmailTemplate).
WithMany(Function(o) o.ReportMassEmailPdfs).
HasForeignKey(Function(o) New With {o.ReportMassEmailTemplateID})

        modelBuilder.Entity(Of ReportEmailTemplateParameter)().
HasOne(Function(c) c.ReportEmailTeplate).
WithMany(Function(o) o.ReportEmailTemplateParameters).
HasForeignKey(Function(o) New With {o.ReportId})

        modelBuilder.Entity(Of ReportMassEmailFile)().
HasOne(Function(c) c.ReportEmailTeplate).
WithMany(Function(o) o.ReportMassEmailFiles).
HasForeignKey(Function(o) New With {o.CrystalReport}).
HasPrincipalKey(Function(o) New With {o.Name})

        modelBuilder.Entity(Of ReportEmailTemplateFile)().
HasOne(Function(c) c.ReportEmailTeplate).
WithMany(Function(o) o.ReportEmailTemplateFiles).
HasForeignKey(Function(o) New With {o.ReportEmailTemplateId})

        modelBuilder.Entity(Of AchTransactionsLog)().
HasOne(Function(c) c.AchTransactionDailyLog).
WithMany(Function(o) o.AchTransactionsLogs).
HasForeignKey(Function(o) New With {o.AchTransactionDailyLogId})

        modelBuilder.Entity(Of CommPlanDetail)().
HasOne(Function(c) c.CommPlan).
WithMany(Function(o) o.CommPlanDetails).
HasForeignKey(Function(o) New With {o.PlanID})

        modelBuilder.Entity(Of CommCompanySetup)().
HasOne(Function(c) c.CommPlan).
WithMany(Function(o) o.CommCompanySetups).
HasForeignKey(Function(o) New With {o.PlanID})

        modelBuilder.Entity(Of CustomReportLayout)().
HasOne(Function(c) c.CustomReport).
WithMany(Function(o) o.CustomReportLayouts).
HasForeignKey(Function(o) New With {o.CustomReportId})

        modelBuilder.Entity(Of BrandsAuthUserEmployee)().
HasOne(Function(c) c.BrandsAuthUser).
WithMany(Function(o) o.BrandsAuthUserEmployees).
HasForeignKey(Function(o) New With {o.UserId})

        modelBuilder.Entity(Of ExportFormat)().
HasOne(Function(c) c.FtpSession).
WithMany(Function(o) o.ExportFormats).
HasForeignKey(Function(o) New With {o.FtpSessionId})

    End Sub

    Class ChangeSet
        Private _context As DbContext
        Sub New(Context As DbContext)
            _context = Context
        End Sub
        Function Updates() As IList(Of Object)
            Return _context.ChangeTracker.Entries.Where(Function(e) e.State = EntityState.Modified).Select(Function(e) e.Entity).ToList()
        End Function

        Function Deletes() As IList(Of Object)
            Return _context.ChangeTracker.Entries.Where(Function(e) e.State = EntityState.Deleted).Select(Function(e) e.Entity).ToList()
        End Function

        Function Inserts() As IList(Of Object)
            Return _context.ChangeTracker.Entries.Where(Function(e) e.State = EntityState.Added).Select(Function(e) e.Entity).ToList()
        End Function
    End Class

    Property CommandTimeout As Integer
        Get
            Return Database.GetCommandTimeout
        End Get
        Set(value As Integer)
            Database.SetCommandTimeout(value)
        End Set
    End Property

    Property Transaction As Storage.IDbContextTransaction
        Get
            Return Database.CurrentTransaction
        End Get
        Set()
            Database.BeginTransaction()
        End Set
    End Property
    <DebuggerBrowsable(DebuggerBrowsableState.Never)>
    ReadOnly Property Connection As DbConnection
        Get
            Return Database.GetDbConnection()
        End Get
    End Property

#End Region
#Region "need fix"
    Property ObjectTrackingEnabled As Boolean
    Property DeferredLoadingEnabled As Boolean

    Public Property ChangeConflicts As List(Of ChangeConflict)
    Class ChangeConflict
        Property Records As List(Of rr)
        Public memberconflicts As List(Of mem)
        Property [object] As Object

        Class mem
            Public member As mem2
            Public databasevalue As String
            Public originalvalue As String

            Class mem2
                Public originalvalue As String
                Public name As String
            End Class
        End Class
        Class rr
        End Class

        Sub Resolve(rt As Linq.RefreshMode)

        End Sub

        Class Linq
            Enum RefreshMode
                OverwriteCurrentValues
            End Enum

        End Class
#End Region
    End Class
End Class
Public Module DbSetExtensions
    <Extension()>
    Public Sub DeleteOnSubmit(Of TEntity As Class)(dbSet As DbSet(Of TEntity), entity As TEntity)
        dbSet.Remove(entity)
    End Sub
    <Extension()>
    Public Sub DeleteAllOnSubmit(Of TEntity As Class)(dbSet As DbSet(Of TEntity), entities As IEnumerable(Of TEntity))
        For Each ent In entities
            dbSet.Remove(ent)
        Next
    End Sub

    <Extension()>
    Public Sub InsertOnSubmit(Of TEntity As Class)(dbSet As DbSet(Of TEntity), entity As TEntity)
        dbSet.Add(entity)
    End Sub
    <Extension()>
    Public Sub InsertAllOnSubmit(Of TEntity As Class)(dbSet As DbSet(Of TEntity), entities As IEnumerable(Of TEntity))
        For Each ent In entities
            dbSet.Add(ent)
        Next
    End Sub

    <Extension()>
    Public Sub AddRange(Of TEntity As Class)(dbSet As DbSet(Of TEntity), entities As IEnumerable(Of TEntity))
        For Each ent In entities
            dbSet.Add(ent)
        Next
    End Sub

End Module

Public Module DbContextExtensions
    'Public Enum RefreshMode
    '    KeepChanges
    '    KeepCurrentValues
    '    OverrideCurrentValues
    'End Enum
    <Extension>
    Public Sub Refresh(Of TEntity As Class)(context As DbContext, mode As Data.Linq.RefreshMode, entity As TEntity)
        Dim entry = context.Entry(entity)

        ' Save the current entity values depending on refresh mode
        Dim originalValues = If(mode = Data.Linq.RefreshMode.KeepChanges OrElse mode = Data.Linq.RefreshMode.KeepCurrentValues, entry.CurrentValues.Clone(), Nothing)

        ' Reload the entity from the database
        entry.Reload()

        ' Apply logic based on refresh mode
        Select Case mode
            Case Data.Linq.RefreshMode.KeepChanges
                ' Reapply only the changes that were made
                For Each propertyEntry In entry.Properties
                    If propertyEntry.IsModified Then
                        ' Restore the modified value
                        propertyEntry.CurrentValue = originalValues(propertyEntry.Metadata.Name)
                    End If
                Next

            Case Data.Linq.RefreshMode.KeepCurrentValues
                ' Restore the current values regardless of modification
                For Each propertyEntry In entry.Properties
                    propertyEntry.CurrentValue = originalValues(propertyEntry.Metadata.Name)
                Next

            Case Data.Linq.RefreshMode.OverwriteCurrentValues
                ' Do nothing more, Reload() already overwrites existing values
        End Select
    End Sub

    <Extension>
    Public Sub Refresh(Of TEntity As Class)(context As DbContext, mode As Data.Linq.RefreshMode, entity As List(Of TEntity))
        If entity Is Nothing OrElse entity.Count = 0 Then Exit Sub
        For Each ent In entity
            Refresh(context, mode, ent)
        Next
    End Sub
End Module

'Public Module QueryableExtensions
'    <System.Runtime.CompilerServices.Extension>
'    Public Function Where(Of TEntity)(
'        source As IQueryable(Of TEntity),
'        predicate As Expression(Of Func(Of TEntity, Boolean))
'    ) As IQueryable(Of TEntity)

'        Dim modifiedPredicate = TransformExpression(predicate)
'        'Return source.Where(modifiedPredicate)
'        Return Queryable.Where(source, modifiedPredicate)
'    End Function

'    <System.Runtime.CompilerServices.Extension>
'    Public Function [Single](Of TEntity)(source As IQueryable(Of TEntity), predicate As Expression(Of Func(Of TEntity, Boolean))) As TEntity
'        Dim modifiedPredicate = TransformExpression(predicate)
'        Return Queryable.Single(source, modifiedPredicate)
'    End Function

'    <System.Runtime.CompilerServices.Extension>
'    Public Function SingleOrDefault(Of TEntity)(source As IQueryable(Of TEntity), predicate As Expression(Of Func(Of TEntity, Boolean))) As TEntity
'        Dim modifiedPredicate = TransformExpression(predicate)
'        Return Queryable.SingleOrDefault(source, modifiedPredicate)
'    End Function

'    <System.Runtime.CompilerServices.Extension>
'    Public Function Count(Of TEntity)(source As IQueryable(Of TEntity), predicate As Expression(Of Func(Of TEntity, Boolean))) As Integer
'        Dim modifiedPredicate = TransformExpression(predicate)
'        Return Queryable.Count(source, modifiedPredicate)
'    End Function

'    Private Function TransformExpression(Of TEntity)(
'        predicate As Expression(Of Func(Of TEntity, Boolean))
'    ) As Expression(Of Func(Of TEntity, Boolean))

'        Dim visitor As New CompareStringVisitor()
'        Dim modifiedExpression = visitor.Visit(predicate)
'        Return CType(modifiedExpression, Expression(Of Func(Of TEntity, Boolean)))
'    End Function
'End Module

'Public Class CompareStringVisitor
'    Inherits ExpressionVisitor

'    Protected Overrides Function VisitBinary(node As BinaryExpression) As Expression
'        ' Handle CompareString(a, b, False) = 0 → a = b
'        If node.NodeType = ExpressionType.Equal OrElse node.NodeType = ExpressionType.GreaterThan OrElse node.NodeType = ExpressionType.LessThan Then
'            Dim left = node.Left
'            Dim right = node.Right

'            If TypeOf left Is MethodCallExpression AndAlso TypeOf right Is ConstantExpression Then
'                Dim methodCall = CType(left, MethodCallExpression)
'                Dim constant = CType(right, ConstantExpression)

'                If methodCall.Method.Name = "CompareString" AndAlso
'                   methodCall.Method.DeclaringType.FullName = "Microsoft.VisualBasic.CompilerServices.Operators" AndAlso
'                   constant.Type = GetType(Integer) AndAlso CInt(constant.Value) = 0 Then

'                    Dim a = Visit(methodCall.Arguments(0))
'                    Dim b = Visit(methodCall.Arguments(1))
'                    Dim textCompare = TryCast(methodCall.Arguments(2), ConstantExpression)

'                    If textCompare IsNot Nothing AndAlso
'                       textCompare.Type = GetType(Boolean) AndAlso
'                       Not CBool(textCompare.Value) Then

'                        If node.NodeType = ExpressionType.Equal Then
'                            Return Expression.Equal(a, b)
'                        ElseIf a.Type.Name = "String" AndAlso node.NodeType = ExpressionType.GreaterThan Then
'                            Dim compareCall = Expression.Call(GetType(String).GetMethod("Compare", {GetType(String), GetType(String)}), a, b)
'                            Return Expression.GreaterThan(compareCall, Expression.Constant(0))
'                        ElseIf a.Type.Name = "String" AndAlso node.NodeType = ExpressionType.LessThan Then
'                            Dim compareCall = Expression.Call(GetType(String).GetMethod("Compare", {GetType(String), GetType(String)}), a, b)
'                            Return Expression.LessThan(compareCall, Expression.Constant(0))
'                        ElseIf node.NodeType = ExpressionType.GreaterThan Then
'                            Return Expression.GreaterThan(a, b)
'                        ElseIf node.NodeType = ExpressionType.LessThan Then
'                            Return Expression.GreaterThan(a, b)
'                        End If
'                    End If
'                End If
'            End If
'        End If


'        ' Handle x <> Nothing → Expression.NotEqual(x, Nothing)
'        If node.NodeType = ExpressionType.NotEqual Then
'            If TypeOf node.Right Is ConstantExpression Then
'                Dim right = CType(node.Right, ConstantExpression)
'                Dim left = Visit(node.Left)

'                ' Handle explicit Nothing
'                If right.Value Is Nothing Then
'                    Return Expression.NotEqual(left, Expression.Constant(Nothing, left.Type))

'                    ' Handle empty string
'                ElseIf TypeOf right.Value Is String AndAlso CStr(right.Value) = "" Then
'                    Return Expression.NotEqual(left, Expression.Constant("", GetType(String)))

'                    ' Handle zero (from VB "" in numeric context)
'                ElseIf TypeOf right.Value Is Integer AndAlso CInt(right.Value) = 0 Then
'                    Return Expression.NotEqual(left, Expression.Constant(0, GetType(Integer)))

'                    ' Fallback
'                Else
'                    Return Expression.NotEqual(left, right)
'                End If
'            End If
'        End If

'        Return MyBase.VisitBinary(node)
'    End Function
'End Class

Public Class VbCompareStringMethodCallTranslator : Implements IMethodCallTranslator

    Private mExpressionFactory As ISqlExpressionFactory

    Public Sub New(expressionFactory As ISqlExpressionFactory)
        Me.mExpressionFactory = expressionFactory
    End Sub

    Public Function Translate(instance As SqlExpression, method As MethodInfo, arguments As IReadOnlyList(Of SqlExpression), logger As IDiagnosticsLogger(Of DbLoggerCategory.Query)) As SqlExpression Implements IMethodCallTranslator.Translate
        If method IsNot Nothing Then
            If method.Name = "CompareString" AndAlso method.DeclaringType?.Name = "Operators" AndAlso
                method.DeclaringType?.Namespace = "Microsoft.VisualBasic.CompilerServices" Then

                Dim left = arguments(0)
                Dim right = arguments(1)

                If method.Name Is NameOf(String.Compare) AndAlso arguments.Count = 2 AndAlso
                        arguments(0).Type.UnwrapNullableType Is arguments(1).Type.UnwrapNullableType Then

                    left = arguments(0)
                    right = arguments(1)

                ElseIf method.Name Is NameOf(String.CompareTo) AndAlso arguments.Count = 1 AndAlso
                        instance IsNot Nothing AndAlso instance.Type.UnwrapNullableType Is arguments(0).Type.UnwrapNullableType Then

                    left = instance
                    right = arguments(0)
                End If

                If left IsNot Nothing AndAlso right IsNot Nothing Then
                    Return Me.mExpressionFactory.[Case]({New CaseWhenClause(Me.mExpressionFactory.Equal(left, right), Me.mExpressionFactory.Constant(0)),
                                                         New CaseWhenClause(Me.mExpressionFactory.GreaterThan(left, right), Me.mExpressionFactory.Constant(1)),
                                                         New CaseWhenClause(Me.mExpressionFactory.LessThan(left, right), Me.mExpressionFactory.Constant(-1))},
                                                         Nothing)
                End If
            End If
        End If

        Return Nothing
    End Function

End Class

Public Module SharedTypeExtensions

    <Extension()>
    Public Function UnwrapNullableType(type As Type) As Type
        Return If(Nullable.GetUnderlyingType(type), type)
    End Function

End Module

Public Class VbMethodCallTranslatorPlugin : Implements IMethodCallTranslatorPlugin

    Public Sub New(expressionFactory As ISqlExpressionFactory)
        Me.Translators = {New VbCompareStringMethodCallTranslator(expressionFactory)}
    End Sub

    Public ReadOnly Property Translators As IEnumerable(Of IMethodCallTranslator) Implements IMethodCallTranslatorPlugin.Translators

End Class

Public Class VbDbContextOptionsExtension : Implements IDbContextOptionsExtension

    Public Sub ApplyServices(services As IServiceCollection) Implements IDbContextOptionsExtension.ApplyServices
        services.AddSingleton(Of IMethodCallTranslatorPlugin, VbMethodCallTranslatorPlugin)
    End Sub

    Public Function ApplyDefaults(options As IDbContextOptions) As IDbContextOptionsExtension Implements IDbContextOptionsExtension.ApplyDefaults
        ' Implement the logic to apply default options here if needed.
        ' In most cases, if the extension doesn't have dynamic defaults,
        ' you can simply return the current instance.
        Return Me
    End Function

    Public Sub Validate(options As IDbContextOptions) Implements IDbContextOptionsExtension.Validate
    End Sub

    Public ReadOnly Property Info As DbContextOptionsExtensionInfo Implements IDbContextOptionsExtension.Info
        Get
            Return New VbDbContextOptionsExtensionInfo(Me)
        End Get
    End Property

End Class

Public Class VbDbContextOptionsExtensionInfo : Inherits DbContextOptionsExtensionInfo

    Public Sub New(extension As IDbContextOptionsExtension)
        MyBase.New(extension)
    End Sub

    Public Overrides Function GetServiceProviderHashCode() As Integer ' Use Integer instead of Long
        ' Return 0 if your extension has no options that require a new IServiceProvider
        ' or a hash code based on relevant options otherwise.
        Return 0
    End Function

    Public Overrides Sub PopulateDebugInfo(debugInfo As IDictionary(Of String, String))
        debugInfo("VB:TranslateMethods") = True.ToString
    End Sub

    Public Overrides ReadOnly Property IsDatabaseProvider As Boolean
        Get
            Return False
        End Get
    End Property

    Public Overrides ReadOnly Property LogFragment As String
        Get
            Return "VbMethodSupport=true"
        End Get
    End Property

    ' Implement the ShouldUseSameServiceProvider method
    Public Overrides Function ShouldUseSameServiceProvider(other As DbContextOptionsExtensionInfo) As Boolean
        ' This method determines if a new IServiceProvider is needed.
        ' Return True if your extension's options are compatible with the other extension's.
        ' If your extension has options that would require a different IServiceProvider,
        ' compare them here and return False if they are different.
        Return True ' Or your specific comparison logic based on your extension's options
    End Function

End Class


Public Module VbDbContextOptionsBuilderExtensions

    <Extension>
    Public Function AddVbSupport(optionsBuilder As DbContextOptionsBuilder) As DbContextOptionsBuilder
        Dim builder = CType(optionsBuilder, IDbContextOptionsBuilderInfrastructure)

        Dim extension = If(optionsBuilder.Options.FindExtension(Of VbDbContextOptionsExtension), New VbDbContextOptionsExtension)
        builder.AddOrUpdateExtension(extension)

        Return optionsBuilder
    End Function

End Module

Public Module CustomEfFunctions
    ' EF Core will need to know how to translate this method to SQL
    '<Extension>
    'Public Function GetValueOrDefaulttmp(b As Boolean) As Boolean
    '    Return False
    'End Function
    '<Extension>
    'Public Function GetValueOrDefaulttmp(b As Nullable(Of Decimal), Optional v As Decimal? = Nothing) As Decimal
    '    Return 0
    'End Function
    '<Extension>
    'Public Function GetValueOrDefaulttmp(b As Nullable(Of Integer)) As Integer
    '    Return 0
    'End Function
    '<Extension>
    'Public Function GetValueOrDefaulttmp(b As Nullable(Of Date)) As Date
    '    Return Nothing
    'End Function
End Module

Namespace Data
    Namespace Linq
        Public Enum RefreshMode
            KeepChanges
            KeepCurrentValues
            OverwriteCurrentValues
        End Enum
    End Namespace
    Public Class VbDbContextOptionsExtension
        Implements IDbContextOptionsExtension

        Public Sub ApplyServices(services As IServiceCollection) Implements IDbContextOptionsExtension.ApplyServices
            If Not services.Any(Function(s) s.ServiceType = GetType(IMethodCallTranslatorPlugin)) Then
                services.AddSingleton(Of IMethodCallTranslatorPlugin, VbMethodCallTranslatorPlugin)
            End If
        End Sub

        Public Function ApplyDefaults(options As IDbContextOptions) As IDbContextOptionsExtension Implements IDbContextOptionsExtension.ApplyDefaults
            Return Me
        End Function

        Public Sub Validate(options As IDbContextOptions) Implements IDbContextOptionsExtension.Validate
        End Sub

        Public ReadOnly Property Info As DbContextOptionsExtensionInfo Implements IDbContextOptionsExtension.Info
            Get
                Return New VbDbContextOptionsExtensionInfo(Me)
            End Get
        End Property
    End Class

    Public Class VbDbContextOptionsExtensionInfo
        Inherits DbContextOptionsExtensionInfo

        Public Sub New(extension As IDbContextOptionsExtension)
            MyBase.New(extension)
        End Sub

        Public Overrides Function GetServiceProviderHashCode() As Integer
            Return 0
        End Function

        Public Overrides Sub PopulateDebugInfo(debugInfo As IDictionary(Of String, String))
            debugInfo("VB:TranslateMethods") = True.ToString
        End Sub

        Public Overrides ReadOnly Property IsDatabaseProvider As Boolean
            Get
                Return False
            End Get
        End Property

        Public Overrides ReadOnly Property LogFragment As String
            Get
                Return "VbMethodSupport=true"
            End Get
        End Property

        Public Overrides Function ShouldUseSameServiceProvider(other As DbContextOptionsExtensionInfo) As Boolean
            Return TypeOf other Is VbDbContextOptionsExtensionInfo
        End Function
    End Class


    Public Class HasTriggerConvention
        Implements IModelFinalizingConvention

        Public Sub ProcessModelFinalizing(modelBuilder As IConventionModelBuilder, context As IConventionContext(Of IConventionModelBuilder)) Implements IModelFinalizingConvention.ProcessModelFinalizing
            For Each entityType In modelBuilder.Metadata.GetEntityTypes()
                Dim tableName As String = entityType.GetTableName()
                entityType.Builder.HasTrigger(tableName)
            Next
        End Sub
    End Class
End Namespace