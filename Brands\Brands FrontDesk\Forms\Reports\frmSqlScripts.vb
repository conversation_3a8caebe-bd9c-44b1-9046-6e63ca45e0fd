﻿Imports System.ComponentModel.DataAnnotations.Schema
Imports System.Data
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports DevExpress.XtraRichEdit
Imports Microsoft.Data.SqlClient
Imports Microsoft.EntityFrameworkCore

Public Class frmSqlScripts
    'Private _db As dbEPDataDataContext
    Private sqlScriptsList As List(Of SqlScript)
    Private frmLogger As Serilog.ILogger
    Private hasFGColor As Boolean = False, hasBGColor As Boolean = False, hasColorColumns As Boolean = False, hasColorSettings As Boolean = False

    Dim popupMenu As DXPopupMenu
    Dim menuExportToIif As New DXMenuItem() With {.Caption = "Export to IIF"}
    Dim DS As DataSet
    Dim MyCon As New SqlClient.SqlConnection(GetConnectionString())
    Dim CancelRunningAllScripts As Boolean

    Dim connection As New SqlConnection(GetConnectionString())

    'Private ReadOnly Property DB As dbEPDataDataContext
    '    Get
    '        If _db Is Nothing Then _db = New dbEPDataDataContext(GetConnectionString)
    '        Return _db
    '    End Get
    'End Property

    Private ReadOnly Property GetDB As dbEPDataDataContext
        Get
            Dim db = New dbEPDataDataContext(GetConnectionString)
            db.Database.SetDbConnection(connection)
            Return db
        End Get
    End Property

    Public Sub New()
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmSqlScripts)
        'DB = New dbEPDataDataContext(GetConnectionString)
        'DB.CommandTimeout = 1000
        gvSqlScripts.SetGridLayoutAndAddMenues("Grid_SqlScripts")
    End Sub

    Private Async Sub frmSqlScripts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        popupMenu = New DXPopupMenu()
        menuExportToIif = New DXMenuItem() With {.Caption = "Export to IIF"}
        popupMenu.Items.Add(menuExportToIif)
        AddHandler menuExportToIif.Click, AddressOf btnExportToExcel_Click
        ddbExportToExcel.DropDownControl = popupMenu

        Await LoadData()
        gvSqlScripts.ShowFindPanel()

        Dim DB = GetDB()
        Dim AllowSortScripts = DB.fn_FrontDeskUserRoles(UserName).Where(Function(r) r.RoleName = "AllowSortScripts").FirstOrDefault?.IsMember

        colID.Visible = True
        If AllowSortScripts Then
            gvSqlScripts.OptionsCustomization.AllowSort = True
            'colID.Visible = True
        End If
    End Sub

    Private Sub gvSqlScriptsCategories_FocusedRowObjectChanged(sender As Object, e As Views.Base.FocusedRowObjectChangedEventArgs) Handles gvSqlScriptsCategories.FocusedRowObjectChanged
        Try
            If e.Row IsNot Nothing Then
                Dim row As Categories = e.Row
                sqlScriptsList = row.Result

                ' Debug: Check if there are any related records that should show plus signs
                Try
                    Dim DB = GetDB()
                    Dim scriptIds = sqlScriptsList.Select(Function(s) s.ID).ToList()

                    ' Check for LogUses - find the correct column name
                    Dim logUsesCount As Integer = 0
                    Try
                        ' First, let's see what properties are available on SqlScriptsLogUse
                        Dim firstLogUse = DB.SqlScriptsLogUses.FirstOrDefault()
                        If firstLogUse IsNot Nothing Then
                            Debug.WriteLine($"Sample SqlScriptsLogUse properties: ID={firstLogUse.ID}")
                            ' Try to find the foreign key property by checking the type
                            Dim logUseType = firstLogUse.GetType()
                            Dim properties = logUseType.GetProperties()
                            For Each prop In properties
                                If prop.PropertyType = GetType(Integer) AndAlso prop.Name.ToLower().Contains("script") Then
                                    Debug.WriteLine($"Found potential foreign key property: {prop.Name}")
                                End If
                            Next
                        End If

                        logUsesCount = DB.SqlScriptsLogUses.Count()
                        Debug.WriteLine($"Total LogUses in database: {logUsesCount}")
                    Catch ex As Exception
                        Debug.WriteLine($"Error checking LogUses: {ex.Message}")
                    End Try

                    ' Check for ParamAutoRuns
                    Dim paramAutoRunsCount = DB.SqlScriptsParamAutoRuns.Where(Function(p) scriptIds.Contains(p.ScriptID)).Count()
                    Debug.WriteLine($"Total ParamAutoRuns for current scripts: {paramAutoRunsCount}")

                Catch ex As Exception
                    Debug.WriteLine($"Error checking related data: {ex.Message}")
                End Try

                ' Use DataSet approach to avoid Entity Framework lazy loading issues
                Try
                    Dim DB = GetDB()
                    Dim scriptIds = sqlScriptsList.Select(Function(s) s.ID).ToList()

                    ' Create DataSet with proper relationships
                    Dim ds As New DataSet()

                    ' Create master table from SqlScript list with ALL properties
                    Dim masterTable As New DataTable("SqlScripts")
                    masterTable.Columns.Add("ID", GetType(Integer))
                    masterTable.Columns.Add("Name", GetType(String))
                    masterTable.Columns.Add("Description", GetType(String))
                    masterTable.Columns.Add("SelectSql", GetType(String))
                    masterTable.Columns.Add("UpdateSql", GetType(String))
                    masterTable.Columns.Add("Category", GetType(String))
                    masterTable.Columns.Add("ParentCategory", GetType(String))
                    masterTable.Columns.Add("SortOrder", GetType(Integer))
                    masterTable.Columns.Add("AutoRun", GetType(Boolean))
                    masterTable.Columns.Add("Status", GetType(String))
                    masterTable.Columns.Add("Users", GetType(String))
                    masterTable.Columns.Add("DefaultExportPath", GetType(String))
                    masterTable.Columns.Add("Parameters", GetType(String))
                    masterTable.Columns.Add("ParametersJson", GetType(String))
                    masterTable.Columns.Add("IsDeleted", GetType(Boolean))
                    masterTable.Columns.Add("Author", GetType(String))
                    masterTable.Columns.Add("AddDateTime", GetType(DateTime))
                    masterTable.Columns.Add("LastRunDate", GetType(DateTime))
                    masterTable.Columns.Add("LastRowCount", GetType(Integer))

                    For Each script In sqlScriptsList
                        Dim row1 = masterTable.NewRow()
                        row1("ID") = script.ID
                        row1("Name") = If(script.Name, "")
                        row1("Description") = If(script.Description, "")
                        row1("SelectSql") = If(script.SelectSql, "")
                        row1("UpdateSql") = If(script.UpdateSql, "")
                        row1("Category") = If(script.Category, "")
                        row1("ParentCategory") = If(script.ParentCategory, "")
                        row1("SortOrder") = script.SortOrder
                        row1("AutoRun") = script.AutoRun
                        row1("Status") = If(script.Status, "")
                        row1("Users") = If(script.Users, "")
                        row1("DefaultExportPath") = If(script.DefaultExportPath, "")
                        row1("Parameters") = If(script.Parameters, "")
                        row1("ParametersJson") = If(script.ParametersJson, "")
                        row1("IsDeleted") = script.IsDeleted
                        row1("Author") = If(script.Author, "")
                        row1("AddDateTime") = If(script.AddDateTime, DateTime.MinValue)
                        row1("LastRunDate") = If(script.LastRunDate, DateTime.MinValue)
                        row1("LastRowCount") = If(script.LastRowCount, 0)
                        masterTable.Rows.Add(row1)
                    Next
                    ds.Tables.Add(masterTable)

                    ' Create detail table for LogUses using raw SQL to avoid EF issues
                    Dim logUsesTable As New DataTable("SqlScriptsLogUses")
                    Try
                        Using connection As New SqlConnection(GetConnectionString())
                            Dim sql = "SELECT ID, SqlScriptID, Action, Dtm, Host, RunBy, Parameters FROM custom.SqlScriptsLogUse WHERE SqlScriptID IN (" + String.Join(",", scriptIds) + ")"
                            Using adapter As New SqlDataAdapter(sql, connection)
                                adapter.Fill(logUsesTable)
                            End Using
                        End Using
                        ds.Tables.Add(logUsesTable)

                        ' Create LogUseDetails table
                        Dim logUseDetailsTable As New DataTable("SqlScriptsLogUseDetails")
                        Try
                            If logUsesTable.Rows.Count > 0 Then
                                Dim logUseIds = logUsesTable.AsEnumerable().Select(Function(r) r.Field(Of Integer)("ID")).ToList()
                                Dim detailsSql = "SELECT ID, LogUseID, ResultSetNum, RowsReturned FROM custom.SqlScriptsLogUseDetail WHERE LogUseID IN (" + String.Join(",", logUseIds) + ")"
                                Using connection As New SqlConnection(GetConnectionString())
                                    Using adapter As New SqlDataAdapter(detailsSql, connection)
                                        adapter.Fill(logUseDetailsTable)
                                    End Using
                                End Using
                            Else
                                ' Add empty columns if no data
                                logUseDetailsTable.Columns.Add("ID", GetType(Integer))
                                logUseDetailsTable.Columns.Add("LogUseID", GetType(Integer))
                                logUseDetailsTable.Columns.Add("ResultSetNum", GetType(Integer))
                                logUseDetailsTable.Columns.Add("RowsReturned", GetType(Integer))
                            End If
                        Catch ex As Exception
                            ' Add empty columns if SQL fails
                            logUseDetailsTable.Columns.Add("ID", GetType(Integer))
                            logUseDetailsTable.Columns.Add("LogUseID", GetType(Integer))
                            logUseDetailsTable.Columns.Add("ResultSetNum", GetType(Integer))
                            logUseDetailsTable.Columns.Add("RowsReturned", GetType(Integer))
                        End Try
                        ds.Tables.Add(logUseDetailsTable)

                        ' Create relationships
                        If masterTable.Rows.Count > 0 AndAlso logUsesTable.Rows.Count > 0 Then
                            Dim relation1 = New DataRelation("SqlScriptsLogUses",
                                                           masterTable.Columns("ID"),
                                                           logUsesTable.Columns("SqlScriptID"))
                            ds.Relations.Add(relation1)
                        End If

                        If logUsesTable.Rows.Count > 0 AndAlso logUseDetailsTable.Rows.Count > 0 Then
                            Dim relation2 = New DataRelation("SqlScriptsLogUseDetails",
                                                           logUsesTable.Columns("ID"),
                                                           logUseDetailsTable.Columns("LogUseID"))
                            ds.Relations.Add(relation2)
                        End If



                    Catch ex As Exception
                        MessageBox.Show($"Error loading LogUses: {ex.Message}", "Error")
                        ' Add empty table if SQL fails
                        logUsesTable.Columns.Add("ID", GetType(Integer))
                        logUsesTable.Columns.Add("SqlScriptID", GetType(Integer))
                        logUsesTable.Columns.Add("Action", GetType(String))
                        ds.Tables.Add(logUsesTable)
                    End Try

                    ' Set DataSource to DataSet
                    gcSqlScripts.DataSource = ds
                    gcSqlScripts.DataMember = "SqlScripts"

                Catch ex As Exception
                    MessageBox.Show($"Error creating DataSet: {ex.Message}", "Error")
                    ' Fall back to original list
                    gcSqlScripts.DataSource = sqlScriptsList
                End Try
                If row.ParentCategory = "(All)" Then
                    colCategory.Visible = True
                    colCategory.VisibleIndex = 0
                Else
                    colCategory.Visible = False
                End If

                If FocusedSqlScript IsNot Nothing AndAlso toggleShowLastResult.IsOn Then
                    ShowLastResult()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Errror in gvSqlScriptsCategories_FocusedRowObjectChanged", ex)
        End Try
    End Sub

    Private Async Function LoadData() As Threading.Tasks.Task
        Try
            lcRoot.ShowProgessPanel()
            'DB = New dbEPDataDataContext(GetConnectionString) With {
            '    .CommandTimeout = 1000
            '}
            Dim i = gvSqlScripts.FocusedRowHandle
            Dim j = gvSqlScriptsCategories.FocusedRowHandle
            ResetSortOrder()

            'Dim categories = From s In db.SqlScripts
            '                 Where Not s.IsDeleted
            '                 Group s By key = New With {.ParentCategory = s.ParentCategory, .Category = s.Category} Into Group
            '                 Select New Categories With {.ParentCategory = key.ParentCategory, .Category = key.Category, .Result = Group.ToList}
            'Dim list = Await Task.Run(Function() categories.ToList)
            Dim DB = GetDB()
            Dim groupedData = DB.SqlScripts.Where(Function(s) Not s.IsDeleted).GroupBy(Function(s) New With {.ParentCategory = s.ParentCategory, .Category = s.Category})
            Dim categories = groupedData.AsEnumerable().Select(Function(groupItem) New Categories With {
                                                                  .ParentCategory = groupItem.Key.ParentCategory,
                                                                  .Category = groupItem.Key.Category,
                                                                  .Result = groupItem.ToList()
                                                                  })

            Dim list = Await Task.Run(Function() categories.ToList())
            For Each item In list
                item.Result = item.Result.Where(Function(s) s.Users.IsNullOrWhiteSpace() OrElse Permissions.UserName.ToLower = "moti.e" OrElse Permissions.UserName.ToLower = "solomon.w" OrElse Permissions.UserName.ToLower = "fd.autouser" OrElse s.Users.ToLower().Split(",").Any(Function(ss) ss.Trim = Permissions.UserName.ToLower())).ToList()
            Next
            list = list.Where(Function(l) l.Result.Any).ToList
            list.Insert(0, New frmSqlScripts.Categories With {.ParentCategory = "(All)", .Category = "(All)", .Result = list.SelectMany(Function(l) l.Result).ToList})
            gcSqlScriptsCategories.DataSource = list
            gvSqlScriptsCategories.FocusedRowHandle = j
            gvSqlScripts.FocusedRowHandle = i
            LoadStatusSettings()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Function

    Private Sub ResetSortOrder()
        Try
            Dim DB = GetDB()
            Dim dic = DB.SqlScripts.GroupBy(Function(s) s.Category).ToDictionary(Function(s) s.Key, Function(s) s)
            For Each category In dic.Keys
                Dim group = DB.SqlScripts.Where(Function(s) s.Category = category).OrderBy(Function(ss) ss.SortOrder).ToList
                For i As Integer = 0 To group.Count() - 1
                    group(i).SortOrder = i
                Next
            Next
            DB.SubmitChanges()
        Catch ex As Exception
            frmLogger.Error(ex, "Error in ResetSortOrder")
            DisplayErrorMessage("Error Saving Changed", ex)
        End Try
    End Sub

    Private Sub ClearDetails()
        Try
            gvDataTable.Columns.Clear()
            gcDataTable.DataSource = Nothing
            ExportFileName = Nothing
            ExportDefaulDirectory = Nothing
            RemoveHandler gvDataTable.CustomColumnDisplayText, AddressOf gv_CustomColumnDisplayText
            SetColorSettings()
            DS = Nothing
            setNavigateBetResults()
        Catch ex As Exception
            DisplayErrorMessage("Error clearing details", ex)
        End Try
    End Sub

    Private ReadOnly Property FocusedSqlScript As SqlScript
        Get
            Dim focusedRow = gvSqlScripts.GetFocusedRow()
            If TypeOf focusedRow Is SqlScriptWithDetails Then
                ' Convert wrapper back to SqlScript for compatibility
                Dim wrapper = CType(focusedRow, SqlScriptWithDetails)
                Dim DB = GetDB()
                Return DB.SqlScripts.Where(Function(s) s.ID = wrapper.ID).FirstOrDefault()
            ElseIf TypeOf focusedRow Is SqlScript Then
                Return CType(focusedRow, SqlScript)
            End If
            Return Nothing
        End Get
    End Property

    Private Function GetSqlScriptFromRow(row As Object) As SqlScript
        If TypeOf row Is SqlScriptWithDetails Then
            Dim wrapper = CType(row, SqlScriptWithDetails)
            Dim DB = GetDB()
            Return DB.SqlScripts.Where(Function(s) s.ID = wrapper.ID).FirstOrDefault()
        ElseIf TypeOf row Is SqlScript Then
            Return CType(row, SqlScript)
        ElseIf TypeOf row Is DataRowView Then
            ' Handle DataRowView from DataSet
            Dim dataRowView = CType(row, DataRowView)
            Dim idValue = dataRowView("ID")
            If idValue IsNot Nothing AndAlso IsNumeric(idValue) Then
                Dim scriptId = Convert.ToInt32(idValue)
                Dim DB = GetDB()
                Return DB.SqlScripts.Where(Function(s) s.ID = scriptId).FirstOrDefault()
            End If
        ElseIf TypeOf row Is DataRow Then
            ' Handle DataRow from DataSet
            Dim dataRow = CType(row, DataRow)
            Dim idValue = dataRow("ID")
            If idValue IsNot Nothing AndAlso IsNumeric(idValue) Then
                Dim scriptId = Convert.ToInt32(idValue)
                Dim DB = GetDB()
                Return DB.SqlScripts.Where(Function(s) s.ID = scriptId).FirstOrDefault()
            End If
        End If
        Return Nothing
    End Function

    Private ReadOnly Property FocusedCategory As Categories
        Get
            Return CType(gvSqlScriptsCategories.GetFocusedRow(), Categories)
        End Get
    End Property

    Private Async Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        If lcRoot.ProgressPanelVisible Then
            Return
        End If
        Await LoadData()
    End Sub

    Public Sub FindAndFocusRecord(ID As Integer)
        gvSqlScripts.ActiveFilterString = "[ID] = " + ID.ToString()
        Dim rowHandle = gvSqlScripts.LocateByValue(1, colID, ID)
        If rowHandle <> DevExpress.XtraGrid.GridControl.InvalidRowHandle Then
            gvSqlScripts.FocusedRowHandle = rowHandle
        End If
    End Sub

    Sub ShowLastResult()
        Try
            Dim DB = GetDB()
            Dim scriptXml = DB.SqlScriptsSavedDataXml.Where(Function(x) x.ScriptID = FocusedSqlScript.ID).FirstOrDefault

            If scriptXml IsNot Nothing Then
                Dim sw As New IO.StringReader(scriptXml.ScriptXml)
                If DS Is Nothing Then DS = New DataSet
                DS.ReadXml(sw)

                If DS.Tables.Count > 0 Then
                    gcDataTable.DataSource = DS.Tables(0)
                    txtCurTable.EditValue = "1 of " + DS.Tables.Count.ToString()
                    txtCurTable.Tag = "1"
                End If
                setNavigateBetResults()
                SetColorSettings()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in ShowLastResult", ex)
        End Try
    End Sub

    Private Sub gvSqlScripts_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles gvSqlScripts.FocusedRowChanged
        Try
            ClearDetails()
            If gvSqlScripts.FocusedRowHandle >= 0 AndAlso toggleShowLastResult.IsOn Then
                ShowLastResult()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in gvSqlScripts_FocusedRowChanged", ex)
        End Try
    End Sub

    Public Async Function ExecuteClick(ID As Int32, Optional UsedSavedParams As Boolean = False) As Task
        Dim DB = GetDB()
        Dim row = DB.SqlScripts.Where(Function(s) s.ID = ID).FirstOrDefault()
        If row Is Nothing Then Return
        Await RunSelect(row, UsedSavedParams)
    End Function

    Private Async Sub btnExecute_Click(sender As Object, e As EventArgs) Handles btnExecute.Click
        If FocusedSqlScript Is Nothing Then Exit Sub
        Dim row = FocusedSqlScript
        Await RunSelect(row)
    End Sub

    Sub CallBack(RecordCount As Int32, LogUse As SqlScriptsLogUse)
        Try
            If LogUse IsNot Nothing Then
                ' Create detail record directly instead of using navigation property
                Dim DB = GetDB()
                Dim detailCount = DB.SqlScriptsLogUseDetails.Where(Function(d) d.LogUseID = LogUse.ID).Count()
                Dim detail = New SqlScriptsLogUseDetail With {
                    .LogUseID = LogUse.ID,
                    .ResultSetNum = detailCount + 1,
                    .RowsReturned = RecordCount
                }
                DB.SqlScriptsLogUseDetails.InsertOnSubmit(detail)
                DB.SubmitChanges()
            End If
        Catch ex As Exception
            ' Log error but don't crash
            Console.WriteLine($"Error in CallBack: {ex.Message}")
        End Try
    End Sub

    Private Async Sub btnRunUpdate_Click(sender As Object, e As EventArgs) Handles btnRunUpdate.Click
        If FocusedSqlScript Is Nothing Then Exit Sub
        Dim row = FocusedSqlScript

        If FocusedSqlScript.SqlScriptsOption IsNot Nothing AndAlso FocusedSqlScript.SqlScriptsOption.PdfCombineDataColumn IsNot Nothing AndAlso FocusedSqlScript.SqlScriptsOption.PdfCombineDataColumn <> "" Then
            If FocusedSqlScript.SqlScriptsOption IsNot Nothing AndAlso FocusedSqlScript.SqlScriptsOption.ConfirmUpdateMsg <> "" Then
                If XtraMessageBox.Show(FocusedSqlScript.SqlScriptsOption.ConfirmUpdateMsg.Replace("{vbcrlf}", $"{vbCrLf}"), "Confirm Update", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) <> DialogResult.Yes Then
                    Return
                End If
            End If

            If FocusedSqlScript.SqlScriptsOption IsNot Nothing AndAlso FocusedSqlScript.SqlScriptsOption.ReqPwdOnUpdate AndAlso Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
                Return
            End If

            Await RunPdfCombiner(FocusedSqlScript)
            Return
        ElseIf row.UpdateSql Is Nothing OrElse row.UpdateSql.Length = 0 Then
            DisplayMessageBox("Update script is not set")
            Return
        End If

        Try
            Dim DB = GetDB()
            DB.Refresh(Data.Linq.RefreshMode.KeepChanges, row)
            lcRoot.ShowProgessPanel()

            Dim recordsAffected As Integer = 0
            frmLogger.Information("Running Update. {Name}", row.Name)
            If row.ParametersJson.IsNullOrWhiteSpace OrElse row.ParametersJson = "[]" Then
                Dim parameters = modReports.GetKeyValueParameters(row.SelectSql, row.Parameters, False, False)
                If parameters Is Nothing Then Exit Sub

                If row.SqlScriptsOption IsNot Nothing AndAlso row.SqlScriptsOption.ConfirmUpdateMsg <> "" Then
                    If XtraMessageBox.Show(row.SqlScriptsOption.ConfirmUpdateMsg.Replace("{vbcrlf}", $"{vbCrLf}"), "Confirm Update", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) <> DialogResult.Yes Then
                        Return
                    End If
                End If

                If row.SqlScriptsOption IsNot Nothing AndAlso row.SqlScriptsOption.ReqPwdOnUpdate AndAlso Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
                    Return
                End If

                Dim sqlScriptLogUsage = New SqlScriptsLogUse With {.Action = "U", .SqlScriptID = row.ID, .Dtm = DateTime.Today.ToEST, .Host = System.Net.Dns.GetHostName(), .RunBy = UserName, .Parameters = parameters.ToStringMap()}
                DB.SqlScriptsLogUses.InsertOnSubmit(sqlScriptLogUsage)
                modGlobals.CallActionAfterStatementComplete = (Sub(RecordCount As Int32) CallBack(RecordCount, sqlScriptLogUsage))

                If parameters.Any() Then
                    recordsAffected = Await UpdateSqlAsync(row.UpdateSql, MyCon, parameters.ToSqlParameters().ToArray())
                Else
                    recordsAffected = Await UpdateSqlAsync(row.UpdateSql, MyCon)
                End If
                If parameters.ToStringMap() <> row.Parameters Then
                    frmLogger.Information("SqlScript: {Name} Updating Parameters From: {OldJson} To: {NewJson}", row.Name, row.Parameters, parameters.ToStringMap())
                    row.Parameters = parameters.ToStringMap()
                    DB.SubmitChanges()
                End If
            Else
                Dim list = GetCustomSqlParameters(row.SelectSql, Nothing, row.ParametersJson, False)
                Dim parameters = New CustomSqlParameterBehavior(list)
                Dim frm = New frmParameters(Of CustomSqlParameter)(parameters, row.AutoFitParams)

                If row.AutoFitParams Then

                    Dim ValueWidth As Int16 = 0
                    Dim ParamWidth As Int16 = 0

                    For Each o In list
                        Dim timesNR As New Font("Tahoma", 8.25F)

                        Dim OptWidth = (
                        From s In nz(o.DropDownOptions, nz(o.Value, "")).ToString().Split(
                            New String() {","}, StringSplitOptions.None).Select(Function(f) TextRenderer.MeasureText(f, timesNR).Width).OrderByDescending(Function(f) f
                        )
                    ).FirstOrDefault()
                        Dim ParWidth = TextRenderer.MeasureText(o.ParameterName, timesNR).Width

                        If OptWidth > ValueWidth Then ValueWidth = OptWidth
                        If ParWidth > ParamWidth Then ParamWidth = ParWidth
                    Next

                    Dim NewWidth = (ParamWidth + ValueWidth) + 50

                    If NewWidth < 410 Then
                        'do nothing
                    ElseIf NewWidth > 1000 Then
                        frm.Width = 1000
                    Else
                        frm.Width = NewWidth
                    End If
                End If

                Dim dialogResult = frm.ShowDialog

                If dialogResult <> DialogResult.OK Then Exit Sub

                Dim paramList = list.Select(Function(p) p.ToSqlParameter())

                If row.SqlScriptsOption IsNot Nothing AndAlso row.SqlScriptsOption.ConfirmUpdateMsg <> "" Then
                    If XtraMessageBox.Show(row.SqlScriptsOption.ConfirmUpdateMsg.Replace("{vbcrlf}", $"{vbCrLf}"), "Confirm Update", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) <> DialogResult.Yes Then
                        Return
                    End If
                End If

                If row.SqlScriptsOption IsNot Nothing AndAlso row.SqlScriptsOption.ReqPwdOnUpdate AndAlso Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
                    Return
                End If

                Dim sqlScriptLogUsage = New SqlScriptsLogUse With {.Action = "U", .SqlScriptID = row.ID, .Dtm = DateTime.Today.ToEST, .Host = System.Net.Dns.GetHostName(), .RunBy = UserName, .Parameters = parameters.ToStringMap()}
                DB.SqlScriptsLogUses.InsertOnSubmit(sqlScriptLogUsage)
                modGlobals.CallActionAfterStatementComplete = (Sub(RecordCount As Int32) CallBack(RecordCount, sqlScriptLogUsage))

                recordsAffected = Await UpdateSqlAsync(row.UpdateSql, MyCon, paramList.ToArray)
                Dim updatedJson = frmDeclareSqlParameters.GetJson(list)
                If row.ParametersJson <> updatedJson Then
                    frmLogger.Information("SqlScript: {Name} Updating ParametersJson From: {OldJson} To: {NewJson}", row.Name, row.ParametersJson, updatedJson)
                    row.ParametersJson = updatedJson
                    DB.SubmitChanges()
                End If
            End If
            XtraMessageBox.Show(Math.Min(modGlobals.LastUpdateRecordCount, recordsAffected) & " Records affected.")
            DB.SubmitChanges()
        Catch ex As Exception
            frmLogger.Error(ex, "Error in run update")
            DisplayErrorMessage("Error in run update", ex)
        Finally
            lcRoot.HideProgressPanel()
            modGlobals.CallActionAfterStatementComplete = Nothing
        End Try
    End Sub

    Private Sub btnNewScript_Click(sender As Object, e As EventArgs) Handles btnNewScript.Click
        Dim cat = FocusedCategory.Category
        If cat = "(All)" Then cat = ""
        Dim parentCat = FocusedCategory.ParentCategory
        If parentCat = "(All)" Then parentCat = ""
        Dim frm = New frmSqlScriptAddOrEdit(cat, parentCat)
        AddHandler frm.FormClosed, New FormClosedEventHandler(Sub()
                                                                  Dim t = LoadData()
                                                              End Sub)
        MainForm.ShowForm(frm)
    End Sub

    Private Sub gvSqlScripts_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvSqlScripts.PopupMenuShowing
        If e.Allow AndAlso e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
            Dim sql = GetSqlScriptFromRow(gvSqlScripts.GetRow(e.HitInfo.RowHandle))
            If sql IsNot Nothing Then
                e.Menu.Items.Add(New DXMenuItem("Edit", Sub()
                                                            Dim frm = New frmSqlScriptAddOrEdit(sql)
                                                            AddHandler frm.FormClosed, New FormClosedEventHandler(Sub()
                                                                                                                      Try
                                                                                                                          If Me.Parent IsNot Nothing Then
                                                                                                                              Dim t = LoadData()
                                                                                                                          End If
                                                                                                                      Catch ex As Exception
                                                                                                                      End Try
                                                                                                                  End Sub)
                                                            MainForm.ShowForm(frm)
                                                        End Sub, My.Resources.edit))
                e.Menu.Items.Add(New DXMenuItem("Open Description in Word", Sub() OpenDescriptionInWord(sql.Description), My.Resources.edit_16x16) With {.BeginGroup = True})
                e.Menu.Items.Add(New DXMenuItem("Open Select Sql In Ssms", Sub() OpenSqlInSsms(sql.SelectSql), My.Resources.editdatasource_16x16) With {.BeginGroup = True})
                e.Menu.Items.Add(New DXMenuItem("Open Update Sql In Ssms", Sub() OpenSqlInSsms(sql.UpdateSql), My.Resources.editdatasource_16x16))
                e.Menu.Items.Add(New DXMenuItem("AutoRun Settings", Sub() AutoRunSettings(sql), My.Resources.play_16x16) With {.BeginGroup = True})
                e.Menu.Items.Add(New DXMenuItem("Delete", Sub()
                                                              If XtraMessageBox.Show("Are you sure you would like to delete this ?", "Delete?", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
                                                                  sql.IsDeleted = True
                                                                  Dim DB = GetDB()
                                                                  DB.SqlScripts.DeleteOnSubmit(DB.SqlScripts.Where(Function(f) f.ID = sql.ID))
                                                                  DB.SubmitChanges()
                                                                  Dim t = LoadData()
                                                              End If
                                                          End Sub, My.Resources.delete_16x16) With {.BeginGroup = True})
            End If
        End If
    End Sub

    Private Function getParamList(row As SqlScript)
        Dim paramList As New List(Of String)

        If row.ParametersJson.IsNullOrWhiteSpace OrElse row.ParametersJson = "[]" Then
            Dim parameters = modReports.GetKeyValueParameters(row.SelectSql, row.Parameters, True, True)
            For Each p In parameters
                paramList.Add(p.Name)
            Next
        Else
            Dim list = GetCustomSqlParameters(row.SelectSql, Nothing, row.ParametersJson, True)
            For Each p In list
                paramList.Add(p.ParameterName)
            Next
        End If
        Return paramList
    End Function

    Private Sub AutoRunSettings(row As SqlScript)
        Dim paramList = getParamList(row)
        Dim SqlScriptsParamAutoRun = New frmSqlScriptsParamAutoRun(row.ID, paramList)
        MainForm.ShowForm(SqlScriptsParamAutoRun)
    End Sub

    Private Sub OpenSqlInSsms(sql As String)
        Try
            Dim newFileName = System.IO.Path.GetTempFileName()
            newFileName = System.IO.Path.ChangeExtension(newFileName, "sql")
            System.IO.File.WriteAllText(newFileName, sql)
            'System.Diagnostics.Process.Start(newFileName)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = newFileName
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error opening file", ex)
        End Try
    End Sub

    Private Sub OpenDescriptionInWord(description As String)
        Try
            Dim edit = New RichEditControl()
            edit.Text = description
            Dim newFileName = System.IO.Path.GetTempFileName()
            newFileName = System.IO.Path.ChangeExtension(newFileName, "doc")
            edit.SaveDocument(newFileName, DevExpress.XtraRichEdit.DocumentFormat.Doc)
            'System.Diagnostics.Process.Start(newFileName)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = newFileName
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            DisplayErrorMessage("Error opening file", ex)
        End Try
    End Sub

    Private Sub gvSqlScripts_DataSourceChanged(sender As Object, e As EventArgs) Handles gvSqlScripts.DataSourceChanged
        ClearDetails()
    End Sub

    Private downHitInfo As GridHitInfo = Nothing

    Private Sub gvSqlScripts_MouseDown(sender As Object, e As MouseEventArgs) Handles gvSqlScripts.MouseDown
        Dim view As GridView = TryCast(sender, GridView)
        downHitInfo = Nothing

        Dim hitInfo As GridHitInfo = view.CalcHitInfo(New Point(e.X, e.Y))
        If Control.ModifierKeys <> Keys.None Then
            Return
        End If
        If e.Button = MouseButtons.Left AndAlso hitInfo.InRow AndAlso hitInfo.RowHandle <> GridControl.NewItemRowHandle Then
            downHitInfo = hitInfo
            'System.Diagnostics.Debug.WriteLine("MouseDown " & hitInfo.RowHandle)
        End If
    End Sub

    Private Sub gvSqlScripts_MouseMove(sender As Object, e As MouseEventArgs) Handles gvSqlScripts.MouseMove
        Dim view As GridView = TryCast(sender, GridView)
        If e.Button = MouseButtons.Left AndAlso downHitInfo IsNot Nothing Then
            Dim dragSize As Size = SystemInformation.DragSize
            Dim dragRect As New Rectangle(New Point(downHitInfo.HitPoint.X - dragSize.Width \ 2, downHitInfo.HitPoint.Y - dragSize.Height \ 2), dragSize)

            If (Not dragRect.Contains(New Point(e.X, e.Y))) Then
                ' System.Diagnostics.Debug.WriteLine(downHitInfo.RowHandle)
                view.GridControl.DoDragDrop(downHitInfo, DragDropEffects.All)
                downHitInfo = Nothing
            End If
        End If
    End Sub

    Private Sub gcSqlScripts_DragOver(sender As Object, e As DragEventArgs) Handles gcSqlScripts.DragOver
        If e.Data.GetDataPresent(GetType(GridHitInfo)) Then
            Dim downHitInfo As GridHitInfo = TryCast(e.Data.GetData(GetType(GridHitInfo)), GridHitInfo)
            If downHitInfo Is Nothing Then
                Return
            End If

            Dim grid As GridControl = TryCast(sender, GridControl)
            Dim view As GridView = TryCast(grid.MainView, GridView)
            Dim hitInfo As GridHitInfo = view.CalcHitInfo(grid.PointToClient(New Point(e.X, e.Y)))
            If hitInfo.InRow AndAlso hitInfo.RowHandle <> downHitInfo.RowHandle AndAlso hitInfo.RowHandle <> GridControl.NewItemRowHandle Then
                Dim sourceScript = GetSqlScriptFromRow(gvSqlScripts.GetRow(downHitInfo.RowHandle))
                Dim targetScript = GetSqlScriptFromRow(gvSqlScripts.GetRow(hitInfo.RowHandle))
                If sourceScript?.Category = targetScript?.Category Then
                    e.Effect = DragDropEffects.Move
                Else
                    e.Effect = DragDropEffects.None
                End If
            Else
                e.Effect = DragDropEffects.None
            End If
        End If
    End Sub

    Private Async Sub gcSqlScripts_DragDrop(sender As Object, e As DragEventArgs) Handles gcSqlScripts.DragDrop
        Dim grid As GridControl = TryCast(sender, GridControl)
        Dim view As GridView = TryCast(grid.MainView, GridView)
        Dim srcHitInfo As GridHitInfo = TryCast(e.Data.GetData(GetType(GridHitInfo)), GridHitInfo)
        Dim hitInfo As GridHitInfo = view.CalcHitInfo(grid.PointToClient(New Point(e.X, e.Y)))
        Dim sourceRow As Integer = srcHitInfo.RowHandle
        Dim targetRow As Integer = hitInfo.RowHandle
        MoveRow(sourceRow, targetRow)

        Await LoadData()
    End Sub

    Private Sub MoveRow(ByVal sourceRow As Integer, ByVal targetRow As Integer)
        Try
            If sourceRow = targetRow OrElse sourceRow = targetRow + 1 Then
                Return
            End If

            Dim DB = GetDB()

            Dim view As GridView = gvSqlScripts
            Dim target As SqlScript = GetSqlScriptFromRow(view.GetRow(targetRow))
            Dim target2 As SqlScript = GetSqlScriptFromRow(view.GetRow(targetRow + 1))
            Dim source As SqlScript = GetSqlScriptFromRow(view.GetRow(sourceRow))

            If target2 Is Nothing Then
                'source.SortOrder = target.SortOrder + 1
                Dim source2 = DB.SqlScripts.Where(Function(f) f.ID = source.ID).First()
                source2.SortOrder = target.SortOrder + 1
            Else
                'For Each s In sqlScriptsList.Where(Function(ss) ss.Category = source.Category AndAlso ss.SortOrder >= target.SortOrder + 1)
                '    s.SortOrder = s.SortOrder + 1
                'Next
                'source.SortOrder = target.SortOrder + 1

                For Each s In DB.SqlScripts.Where(Function(ss) ss.Category = source.Category AndAlso ss.SortOrder >= target.SortOrder + 1)
                    s.SortOrder = s.SortOrder + 1
                Next
                Dim source2 = DB.SqlScripts.Where(Function(f) f.ID = source.ID).First()
                source2.SortOrder = target.SortOrder + 1
            End If
            'gvSqlScripts.RefreshData()
            DB.SubmitChanges()
        Catch ex As Exception
            frmLogger.Error(ex, "Error in MoveRow")
            DisplayErrorMessage("Error in MoveRow", ex)
        End Try
    End Sub


    Private Property ExportFileName As String
    Private Property ExportDefaulDirectory As String
    Private Sub btnExportToExcel_Click(sender As Object, e As EventArgs) Handles ddbExportToExcel.Click
        Try
            gcDataTable.Focus()
            gcDataTable.Select()

            If sender Is ddbExportToExcel Then
                MainForm.ExportToExcel(True, ExportFileName, ExportDefaulDirectory)
            Else
                Dim FD As New SaveFileDialog
                FD.FileName = ExportFileName
                FD.DefaultExt = ".iif"
                FD.AddExtension = True
                FD.Filter = "IIF Files|*.iif"
                If FD.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                    MainForm.ExportToIIF(FD.FileName, "")
                    Dim P As New ProcessStartInfo(FD.FileName)
                    Process.Start(P)
                End If
                FD.Dispose()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in export", ex)
        End Try
    End Sub

    Private Async Sub gvSqlScripts_DoubleClick(sender As Object, e As EventArgs) Handles gvSqlScripts.DoubleClick
        Dim view As GridView = CType(sender, GridView)
        Dim pt As Point = view.GridControl.PointToClient(Control.MousePosition)
        Dim info As GridHitInfo = view.CalcHitInfo(pt)
        If info.InRow Then
            Dim row As SqlScript = GetSqlScriptFromRow(gvSqlScripts.GetRow(info.RowHandle))
            Await RunSelect(row)
        End If
    End Sub

    Private Async Function RunSelect(row As SqlScript, Optional UseSavedParams As Boolean = False) As Task
        Try
            Dim DB = GetDB()
            DB.Refresh(Data.Linq.RefreshMode.KeepChanges, row)
            lcRoot.ShowProgessPanel()
            frmLogger.Information("Running select. {Name}", row.Name)
            ClearDetails()
            ExportDefaulDirectory = row.DefaultExportPath
            Dim udfName = "Remove time in date cols in script if applicable"
            If DB.UDFs.Where(Function(u) u.name = udfName).Count = 1 Then
                If GetUdfValueSplitted(udfName).ToList().IndexOf(row.ID) >= 0 Then
                    AddHandler gvDataTable.CustomColumnDisplayText, AddressOf gv_CustomColumnDisplayText
                End If
            End If

            If row.ParametersJson.IsNullOrWhiteSpace OrElse row.ParametersJson = "[]" Then
                Dim parameters = modReports.GetKeyValueParameters(row.SelectSql, row.Parameters, UseSavedParams, True)
                If parameters Is Nothing Then Exit Function

                If parameters.Any() Then
                    DS = Await Task.Run(Function() QueryDSAsync(row.ID, row.SelectSql, 1000, MyCon, parameters.ToSqlParameters().ToArray()))
                Else
                    DS = Await Task.Run(Function() QueryDSAsync(row.ID, row.SelectSql, 1000, MyCon))
                End If

                Dim sqlScriptLogUsage = New SqlScriptsLogUse With {.Action = "S", .SqlScriptID = row.ID, .Dtm = DateTime.Today.ToEST, .Host = System.Net.Dns.GetHostName(), .RunBy = UserName, .Parameters = parameters.ToStringMap()}
                sqlScriptLogUsage.SqlScriptsLogUseDetails = New List(Of SqlScriptsLogUseDetail)()

                DB.SqlScriptsLogUses.InsertOnSubmit(sqlScriptLogUsage)

                If DS.Tables.Count > 0 Then
                    gcDataTable.DataSource = DS.Tables(0)
                    txtCurTable.EditValue = "1 of " + DS.Tables.Count.ToString()
                    txtCurTable.Tag = "1"

                    ' Create detail records directly instead of using navigation property
                    For x = 0 To DS.Tables.Count - 1
                        Dim detail = New SqlScriptsLogUseDetail With {
                            .LogUseID = sqlScriptLogUsage.ID,
                            .ResultSetNum = x + 1,
                            .RowsReturned = DS.Tables(x).Rows.Count
                        }
                        DB.SqlScriptsLogUseDetails.InsertOnSubmit(detail)
                    Next
                End If

                Dim sw As New IO.StringWriter()
                DS.WriteXml(sw)
                Dim SqlScriptsSavedDataXmlRec = DB.SqlScriptsSavedDataXml.Where(Function(s) s.ScriptID = row.ID).FirstOrDefault()
                If SqlScriptsSavedDataXmlRec IsNot Nothing Then
                    SqlScriptsSavedDataXmlRec.ScriptXml = sw.ToString()
                Else
                    DB.SqlScriptsSavedDataXml.InsertOnSubmit(New SqlScriptsSavedDataXml With {.ScriptID = row.ID, .ScriptXml = sw.ToString()})
                End If

                setNavigateBetResults()

                Dim conumParam = parameters.SingleOrDefault(Function(p) p.Name.ToLower() = "@conum")
                If conumParam IsNot Nothing AndAlso conumParam.Value.IsNotNullOrWhiteSpace Then
                    ExportFileName = $"{conumParam.Value}_{row.Name}"
                Else
                    ExportFileName = row.Name
                End If

                If parameters.ToStringMap() <> row.Parameters Then
                    frmLogger.Information("SqlScript: {Name} Updating Parameters From: {OldJson} To: {NewJson}", row.Name, row.Parameters, parameters.ToStringMap())
                    row.Parameters = parameters.ToStringMap()
                    DB.SubmitChanges()
                End If
            Else
                Dim list = GetCustomSqlParameters(row.SelectSql, Nothing, row.ParametersJson, True)
                Dim parameters = New CustomSqlParameterBehavior(list)

                If Not UseSavedParams Then
                    Dim frm = New frmParameters(Of CustomSqlParameter)(parameters, row.AutoFitParams)

                    If row.AutoFitParams Then

                        Dim ValueWidth As Int16 = 0
                        Dim ParamWidth As Int16 = 0

                        For Each o In list
                            Dim timesNR As New Font("Tahoma", 8.25F)

                            Dim OptWidth = (
                        From s In nz(o.DropDownOptions, nz(o.Value, "")).ToString().Split(
                            New String() {","}, StringSplitOptions.None).Select(Function(f) TextRenderer.MeasureText(f, timesNR).Width).OrderByDescending(Function(f) f
                        )
                    ).FirstOrDefault()
                            Dim ParWidth = TextRenderer.MeasureText(o.ParameterName, timesNR).Width

                            If OptWidth > ValueWidth Then ValueWidth = OptWidth
                            If ParWidth > ParamWidth Then ParamWidth = ParWidth
                        Next

                        Dim NewWidth = (ParamWidth + ValueWidth) + 50

                        If NewWidth < 410 Then
                            'do nothing
                        ElseIf NewWidth > 1000 Then
                            frm.Width = 1000
                        Else
                            frm.Width = NewWidth
                        End If
                    End If

                    Dim dialogResult = frm.ShowDialog

                    If dialogResult <> DialogResult.OK Then Exit Function
                End If

                list = parameters.ParameterList
                Dim paramList = list.Select(Function(p) p.ToSqlParameter())
                DS = Await Task.Run(Function() QueryDSAsync(row.ID, row.SelectSql, 1000, MyCon, paramList.ToArray()))
                Dim sqlScriptLogUsage = New SqlScriptsLogUse With {.Action = "S", .SqlScriptID = row.ID, .Dtm = DateTime.Today.ToEST, .Host = System.Net.Dns.GetHostName(), .RunBy = UserName, .Parameters = parameters.ToStringMap()}
                DB.SqlScriptsLogUses.InsertOnSubmit(sqlScriptLogUsage)
                sqlScriptLogUsage.SqlScriptsLogUseDetails = New List(Of SqlScriptsLogUseDetail)()

                If DS.Tables.Count > 0 Then
                    gcDataTable.DataSource = DS.Tables(0)
                    txtCurTable.EditValue = "1 of " + DS.Tables.Count.ToString()
                    txtCurTable.Tag = "1"

                    ' Create detail records directly instead of using navigation property
                    For x = 0 To DS.Tables.Count - 1
                        Dim detail = New SqlScriptsLogUseDetail With {
                            .LogUseID = sqlScriptLogUsage.ID,
                            .ResultSetNum = x + 1,
                            .RowsReturned = DS.Tables(x).Rows.Count
                        }
                        DB.SqlScriptsLogUseDetails.InsertOnSubmit(detail)
                    Next
                End If

                Dim sw As New IO.StringWriter()
                DS.WriteXml(sw)
                Dim SqlScriptsSavedDataXmlRec = DB.SqlScriptsSavedDataXml.Where(Function(s) s.ScriptID = row.ID).FirstOrDefault()
                If SqlScriptsSavedDataXmlRec IsNot Nothing Then
                    SqlScriptsSavedDataXmlRec.ScriptXml = sw.ToString()
                Else
                    DB.SqlScriptsSavedDataXml.InsertOnSubmit(New SqlScriptsSavedDataXml With {.ScriptID = row.ID, .ScriptXml = sw.ToString()})
                End If

                setNavigateBetResults()

                Dim conumParam = list.SingleOrDefault(Function(p) p.ParameterName.ToLower() = "@conum")
                If conumParam IsNot Nothing AndAlso conumParam.Value?.ToString().IsNotNullOrWhiteSpace Then
                    ExportFileName = $"{conumParam.Value}_{row.Name}"
                Else
                    ExportFileName = row.Name
                End If
                Dim updatedJson = frmDeclareSqlParameters.GetJson(list)
                If row.ParametersJson <> updatedJson Then
                    frmLogger.Information("SqlScript: {Name} Updating ParametersJson From: {OldJson} To: {NewJson}", row.Name, row.ParametersJson, updatedJson)
                    row.ParametersJson = updatedJson
                    DB.SubmitChanges()
                End If
            End If

            Dim tsCol = gvDataTable.Columns("timestamp")
            If tsCol IsNot Nothing AndAlso tsCol.ColumnType.ToString() = "System.Byte[]" Then
                gvDataTable.Columns.Remove(tsCol)
            End If

            gvDataTable.BestFitColumns()
            gcDataTable.Focus()
            gcDataTable.Select()

            If DS.Tables.Count > 0 AndAlso DS.Tables(0).Rows IsNot Nothing AndAlso DS.Tables(0).Rows.Count > 0 AndAlso gvDataTable.Columns.Count > 0 Then
                Dim sumItem = gvDataTable.Columns(0).Summary.Add()
                sumItem.SummaryType = DevExpress.Data.SummaryItemType.Count
                sumItem.DisplayFormat = "{0} rows"
                For Each col As DataColumn In DirectCast(gcDataTable.DataSource, DataTable).Columns
                    If col.DataType = GetType(Integer) OrElse col.DataType = GetType(Decimal) Then
                        sumItem = gvDataTable.Columns(col.ColumnName).Summary.Add()
                        sumItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
                        sumItem.DisplayFormat = "Sum: {0}"
                    ElseIf col.DataType = GetType(DateTime) Then
                        Dim c = gvDataTable.Columns.ColumnByFieldName(col.ColumnName)
                        c.DisplayFormat.FormatType = FormatType.Numeric
                        c.DisplayFormat.FormatString = "G"
                    End If
                Next
                SetColorSettings()
                DB.Refresh(Data.Linq.RefreshMode.KeepChanges, row)
                row.LastRowCount = DS.Tables(0).Rows.Count
                row.LastRunDate = DateTime.Now.ToEST()
            End If
            DB.SubmitChanges()
            gvSqlScripts.RefreshRow(gvSqlScripts.FocusedRowHandle)
        Catch ex As Exception
            Logger.Error(ex, "Error in RunSelect")
            DisplayErrorMessage($"Error running select on {row.Name}", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Function

    Private Async Function RunPdfCombiner(row As SqlScript) As Task
        Try
            lcRoot.ShowProgessPanel()
            frmLogger.Information("Running pdf combiner select. {Name}", row.Name)

            Dim paramList As IEnumerable(Of SqlClient.SqlParameter)

            If row.ParametersJson.IsNullOrWhiteSpace OrElse row.ParametersJson = "[]" Then
                Dim parameters = modReports.GetKeyValueParameters(row.SelectSql, row.Parameters, True)
                If parameters Is Nothing Then Exit Function
                paramList = parameters.ToSqlParameters().ToList()
            Else
                Dim list = GetCustomSqlParameters(row.SelectSql, Nothing, row.ParametersJson, False)
                Dim parameters = New CustomSqlParameterBehavior(list)
                Dim frm = New frmParameters(Of CustomSqlParameter)(parameters, row.AutoFitParams)
                If frm.ShowDialog <> DialogResult.OK Then Exit Function
                list = parameters.ParameterList
                paramList = list.Select(Function(p) p.ToSqlParameter())
            End If

            Dim PdfFile = frmPdfCombine.CombinePdf(row.SelectSql, row.SqlScriptsOption.PdfCombineDataColumn, row.SqlScriptsOption.PdfCombineTitleColumn, paramList.ToList())
            'System.Diagnostics.Process.Start(PdfFile)
            Dim psi As New System.Diagnostics.ProcessStartInfo()
            psi.FileName = PdfFile
            psi.UseShellExecute = True
            System.Diagnostics.Process.Start(psi)
        Catch ex As Exception
            Logger.Error(ex, "Error in RunPdfCombiner")
            DisplayErrorMessage($"Error running RunPdfCombiner on {row.Name}", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Function
    Private Class Categories
        Property Category As String
        Property ParentCategory As String
        Property Result As List(Of SqlScript)
    End Class

    Private Class SqlScriptWithDetails
        ' Copy all SqlScript properties
        Property ID As Integer
        Property Name As String
        Property Description As String
        Property SelectSql As String
        Property UpdateSql As String
        Property Category As String
        Property ParentCategory As String
        Property SortOrder As Integer
        Property AutoRun As Boolean
        Property Status As String
        Property Users As String
        Property DefaultExportPath As String
        Property Parameters As String
        Property ParametersJson As String
        Property IsDeleted As Boolean
        Property Author As String
        Property AddDateTime As DateTime?
        Property LastRunDate As DateTime?
        Property LastRowCount As Integer?

        ' Navigation properties for master-detail
        ' Only include direct relationships - SqlScriptsLogUseDetail is handled at the DataSet level
        Property SqlScriptsLogUses As List(Of SqlScriptsLogUse)
        Property SqlScriptsParamAutoRuns As List(Of SqlScriptsParamAutoRun)

        ' Constructor to copy from SqlScript
        Public Sub New(script As SqlScript)
            If script Is Nothing Then
                Throw New ArgumentNullException(NameOf(script))
            End If

            ID = script.ID
            Name = script.Name
            Description = script.Description
            SelectSql = script.SelectSql
            UpdateSql = script.UpdateSql
            Category = script.Category
            ParentCategory = script.ParentCategory
            SortOrder = script.SortOrder
            AutoRun = script.AutoRun
            Status = script.Status
            Users = script.Users
            DefaultExportPath = script.DefaultExportPath
            Parameters = script.Parameters
            ParametersJson = script.ParametersJson
            IsDeleted = script.IsDeleted
            Author = script.Author
            AddDateTime = script.AddDateTime
            LastRunDate = script.LastRunDate
            LastRowCount = script.LastRowCount

            ' Initialize navigation properties as empty lists
            SqlScriptsLogUses = New List(Of SqlScriptsLogUse)()
            SqlScriptsParamAutoRuns = New List(Of SqlScriptsParamAutoRun)()
        End Sub
    End Class

    Private Sub btnStatusSetup_Click(sender As Object, e As EventArgs) Handles btnStatusSetup.Click
        Try
            Dim allStatus As List(Of String)
            Using _db = New dbEPDataDataContext(GetConnectionString)
                allStatus = _db.SqlScripts.Where(Function(s) s.Status IsNot Nothing).Select(Function(s) s.Status).Distinct.ToList
            End Using

            If StatusSettingsDictionary IsNot Nothing Then
                Dim newStatus = allStatus.Where(Function(s) Not StatusSettingsDictionary.Keys.Contains(s))
                For Each s In newStatus
                    StatusSettingsDictionary.Add(s, Nothing)
                Next

                Dim removedStatus = StatusSettingsDictionary.Where(Function(s) Not allStatus.Contains(s.Key)).ToArray
                For Each s In removedStatus
                    StatusSettingsDictionary.Remove(s.Key)
                Next
            End If

            Using frm = New frmSqlScriptsCategoriesSetup(StatusSettingsDictionary)
                If frm.ShowDialog Then
                    LoadStatusSettings()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error in Setting up status", ex)
        End Try
    End Sub

    Private StatusSettingsDictionary As Dictionary(Of String, Color)

    Private Sub LoadStatusSettings()
        Try
            Dim json = GetUdfValue("SqlScripts_StatusSettings")
            Dim list = Newtonsoft.Json.JsonConvert.DeserializeObject(Of Dictionary(Of String, Color))(json)
            If list Is Nothing Then list = New Dictionary(Of String, Color)
            StatusSettingsDictionary = list
            gvSqlScripts.RefreshData()
        Catch ex As Exception
            DisplayMessageBox("Error loading Status settings")
        End Try
    End Sub

    Private Sub gvSqlScripts_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles gvSqlScripts.RowCellStyle
        If e.Column Is colStatus AndAlso StatusSettingsDictionary IsNot Nothing Then
            Dim row As SqlScript = GetSqlScriptFromRow(gvSqlScripts.GetRow(e.RowHandle))
            If row IsNot Nothing AndAlso row.Status.IsNotNullOrWhiteSpace Then
                Dim c As Color = Nothing
                If StatusSettingsDictionary.TryGetValue(row.Status, c) Then
                    e.Appearance.BackColor = c
                End If
            End If
        End If
    End Sub

    Private Sub ToolTipController1_GetActiveObjectInfo(sender As Object, e As DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs) Handles ToolTipController1.GetActiveObjectInfo
        If Not e.SelectedControl Is gcSqlScripts OrElse (Permissions?.UseEmailBodyToolTip = False) Then Return

        Dim info As ToolTipControlInfo = Nothing
        'Get the view at the current mouse position
        Dim view As GridView = gcSqlScripts.GetViewAt(e.ControlMousePosition)
        If view Is Nothing Then Return
        'Get the view's element information that resides at the current position
        Dim hi As GridHitInfo = view.CalcHitInfo(e.ControlMousePosition)
        'Display a hint for row indicator cells
        If hi.HitTest = GridHitTest.RowPreview Then
            'An object that uniquely identifies a row indicator cell
            Dim o As SqlScript = GetSqlScriptFromRow(gvSqlScripts.GetRow(hi.RowHandle))
            Dim text As String = o.Description
            info = New ToolTipControlInfo(o, text)
        End If
        'Supply tooltip information if applicable, otherwise preserve default tooltip (if any)
        If Not info Is Nothing Then e.Info = info
    End Sub

    Private Sub btnRunCompletion_Click(sender As Object, e As EventArgs) Handles btnRunCompletion.Click
        Using frm = New frmSqlScriptLogCompletion(FocusedSqlScript.ID)
            frm.ShowDialog()
        End Using
    End Sub

    Private Sub btnPrev_Click(sender As Object, e As EventArgs) Handles btnPrev.Click
        Try
            Dim curTable As Int16 = txtCurTable.Tag
            If curTable > 1 Then
                curTable -= 1
                gvDataTable.Columns.Clear()
                gcDataTable.DataSource = DS.Tables(curTable - 1)

                Dim tsCol = gvDataTable.Columns("timestamp")
                If tsCol IsNot Nothing AndAlso tsCol.ColumnType.ToString() = "System.Byte[]" Then
                    gvDataTable.Columns.Remove(tsCol)
                End If

                gvDataTable.BestFitColumns()
                txtCurTable.EditValue = curTable.ToString() + " of " + DS.Tables.Count.ToString()
                txtCurTable.Tag = curTable
                setNavigateBetResults()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in btnPrev_Click", ex)
        End Try
    End Sub

    Private Sub btnNext_Click(sender As Object, e As EventArgs) Handles btnNext.Click
        Try
            Dim curTable As Int16 = txtCurTable.Tag
            If curTable < DS.Tables.Count Then
                curTable += 1
                gvDataTable.Columns.Clear()
                gcDataTable.DataSource = DS.Tables(curTable - 1)

                Dim tsCol = gvDataTable.Columns("timestamp")
                If tsCol IsNot Nothing AndAlso tsCol.ColumnType.ToString() = "System.Byte[]" Then
                    gvDataTable.Columns.Remove(tsCol)
                End If

                gvDataTable.BestFitColumns()
                txtCurTable.EditValue = curTable.ToString() + " of " + DS.Tables.Count.ToString()
                txtCurTable.Tag = curTable
                setNavigateBetResults()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in btnNext_Click", ex)
        End Try
    End Sub

    Private Sub gvDataTable_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles gvDataTable.RowCellStyle
        If Not hasColorSettings Then
            Return
        End If

        Try
            Dim row As DataRow = CType(gvDataTable.GetRow(e.RowHandle), DataRowView).Row
            Dim args() As String

            If hasColorColumns AndAlso Not IsDBNull(row("__ColorColumns")) AndAlso row("__ColorColumns") <> "" AndAlso Not ("," + row("__ColorColumns").ToString().ToLower() + ",").ToString().Contains("," + e.Column.FieldName.ToLower() + ",") Then
                Return
            End If

            If hasFGColor AndAlso Not IsDBNull(row("__ColorFG")) Then
                Dim FGColorValue As String = row("__ColorFG")
                Dim ColorFG As Color
                args = FGColorValue.Split(New Char() {","})

                If args.Count = 4 Then
                    ColorFG = Color.FromArgb(args(0), args(1), args(2), args(3))
                ElseIf args.Count = 3 Then
                    ColorFG = Color.FromArgb(args(0), args(1), args(2))
                ElseIf args.Count = 1 AndAlso FGColorValue Like "[0-9]*" Then
                    ColorFG = Color.FromArgb(FGColorValue)
                Else
                    ColorFG = Color.FromName(FGColorValue)
                End If

                e.Appearance.ForeColor = ColorFG
            End If

            If hasBGColor AndAlso Not IsDBNull(row("__ColorBG")) Then
                Dim BGColorValue As String = row("__ColorBG")
                Dim ColorBG As Color
                args = BGColorValue.Split(New Char() {","})

                If args.Count = 4 Then
                    ColorBG = Color.FromArgb(args(0), args(1), args(2), args(3))
                ElseIf args.Count = 3 Then
                    ColorBG = Color.FromArgb(args(0), args(1), args(2))
                ElseIf args.Count = 1 AndAlso BGColorValue Like "[0-9]*" Then
                    ColorBG = Color.FromArgb(BGColorValue)
                Else
                    ColorBG = Color.FromName(BGColorValue)
                End If

                e.Appearance.BackColor = ColorBG
            End If
        Catch ex As Exception
            Logger.Error(ex, "SqlScripts_RowCellStyle")
        End Try
    End Sub

    Private Sub gvSqlScripts_MasterRowExpandedV2(sender As Object, e As CustomMasterRowEventArgs)
        Try
            Dim colsToShow = New List(Of String)(New String() {"RESULTSETNUM", "ROWSRETURNED"})
            Dim detailGC = TryCast(CType(sender, DevExpress.XtraGrid.Views.Grid.GridView).GetDetailView(e.RowHandle, e.RelationIndex), DevExpress.XtraGrid.Views.Grid.GridView)
            detailGC.GridControl.Width = 100
            detailGC.OptionsBehavior.ReadOnly = True
            Dim x As Int16
            For x = 0 To detailGC.Columns.Count - 1
                If colsToShow.IndexOf(detailGC.Columns(x).FieldName.ToString.ToUpper) > -1 Then
                    detailGC.Columns(x).Visible = True
                Else
                    detailGC.Columns(x).Visible = False
                End If
            Next
            detailGC.OptionsView.ShowGroupPanel = False
            detailGC.OptionsView.ColumnAutoWidth = False
            detailGC.BestFitColumns()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub gvSqlScripts_MasterRowExpanded(sender As Object, e As CustomMasterRowEventArgs) Handles gvSqlScripts.MasterRowExpanded
        Try
            Dim detailGC = TryCast(gvSqlScripts.GetDetailView(e.RowHandle, e.RelationIndex), DevExpress.XtraGrid.Views.Grid.GridView)
            Dim colsToShow = New List(Of String)(New String() {"ID", "DTM", "RUNBY", "HOST", "PARAMETERS", ""})

            If detailGC.LevelName = "SqlScriptsParamAutoRuns" Then
                colsToShow = New List(Of String)(New String() {"SCRIPTID", "SCRIPTPARAM", "STATICVALUE", "EXPR"})
            End If

            AddHandler detailGC.MasterRowExpanded, AddressOf gvSqlScripts_MasterRowExpandedV2
            detailGC.OptionsBehavior.ReadOnly = True
            Dim x As Int16
            For x = 0 To detailGC.Columns.Count - 1
                If colsToShow.IndexOf(detailGC.Columns(x).FieldName.ToString.ToUpper) > -1 Then
                    detailGC.Columns(x).Visible = True

                    If detailGC.Columns(x).FieldName.ToString.ToUpper = "ID" Then
                        detailGC.Columns(x).SortIndex = 1
                        detailGC.Columns(x).SortOrder = DevExpress.Data.ColumnSortOrder.Descending
                    ElseIf detailGC.Columns(x).FieldName.ToString.ToUpper = "DTM" Then
                        detailGC.Columns(x).DisplayFormat.FormatType = FormatType.DateTime
                        detailGC.Columns(x).DisplayFormat.FormatString = "g"
                    End If
                Else
                    detailGC.Columns(x).Visible = False
                End If
            Next

            If detailGC.LevelName = "SqlScriptsLogUses" Then
                Dim ActionUnbExp = "Iif([Action] = 'S', 'Select', 'Update')"
                detailGC.Columns.Add(New Columns.GridColumn With {.VisibleIndex = 4, .UnboundExpression = ActionUnbExp, .Caption = "Action", .UnboundType = DevExpress.Data.UnboundColumnType.String, .Visible = True, .FieldName = "Expr1"})
            End If
            detailGC.MoveFirst()
            detailGC.OptionsView.ShowGroupPanel = False
            detailGC.OptionsView.ColumnAutoWidth = False
            detailGC.BestFitColumns()
        Catch ex As Exception

        End Try
    End Sub

    Private Async Sub btnRunAllScripts_Click(sender As Object, e As EventArgs) Handles btnRunAllScripts.Click
        Try
            Dim DB = GetDB()
            If lcRoot.ProgressPanelVisible Then
                Return
            End If

            'need to refresh list in case we updated auto run in other forms
            'db = New dbEPDataDataContext(GetConnectionString)
            Await LoadData()
            CancelRunningAllScripts = False
            Dim x As Integer
            For x = 0 To gvSqlScripts.RowCount - 1
                If CancelRunningAllScripts OrElse FocusedSqlScript Is Nothing Then
                    Return
                ElseIf GetSqlScriptFromRow(gvSqlScripts.GetRow(x))?.AutoRun Then
                    Dim row = GetSqlScriptFromRow(gvSqlScripts.GetRow(x))
                    Dim paramList = getParamList(row)
                    Dim paramsAutoRunList = DB.SqlScriptsParamAutoRuns.Where(Function(f) f.ScriptID = row.ID).ToList()
                    Dim hasAutoRunConfigured As Boolean = True

                    Dim newParams As String = ""
                    For Each p In paramList
                        Dim autoRun = paramsAutoRunList.Where(Function(f) f.ScriptParam = p).FirstOrDefault()
                        If autoRun IsNot Nothing Then
                            Dim value As String
                            If autoRun.Expr.ToString() <> Nothing Then
                                value = Query(Of String)(autoRun.SqlScriptsExpression.SqlString).First
                            Else
                                value = autoRun.StaticValue
                            End If
                            newParams += If(newParams = "", "", "; ") + p + " = " + value
                        Else
                            hasAutoRunConfigured = False
                        End If
                    Next

                    If hasAutoRunConfigured Then
                        gvSqlScripts.FocusedRowHandle = x
                        row.Parameters = newParams
                        DB.SaveChanges()
                        Await ExecuteClick(FocusedSqlScript.ID, True)
                    End If
                End If
                Application.DoEvents()
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error in run all scripts", ex)
        End Try
    End Sub

    Private Sub btnStopRunningAllScripts_Click(sender As Object, e As EventArgs) Handles btnStopRunningAllScripts.Click
        CancelRunningAllScripts = True
    End Sub

    Private Sub toggleShowLastResult_EditValueChanged(sender As Object, e As EventArgs) Handles toggleShowLastResult.EditValueChanged
        Try
            If toggleShowLastResult.IsOn Then
                ShowLastResult()
            Else
                ClearDetails()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in toggleShowLastResult_EditValueChanged", ex)
        End Try
    End Sub

    Sub SetColorSettings()
        Try
            If gvDataTable.DataSource Is Nothing Then
                hasBGColor = False
                hasFGColor = False
                hasColorColumns = False
                hasColorSettings = False
                Return
            End If

            Dim dt As DataTable = gcDataTable.DataSource

            If dt.Columns.IndexOf("__ColorBG") = -1 AndAlso dt.Columns.IndexOf("__ColorFG") = -1 Then
                hasBGColor = False
                hasFGColor = False
                hasColorColumns = False
                hasColorSettings = False
                Return
            Else
                If dt.Columns.IndexOf("__ColorHideSettings") >= 0 Then
                    gvDataTable.Columns(dt.Columns.IndexOf("__ColorHideSettings")).Visible = False
                End If

                If dt.Columns.IndexOf("__ColorBG") >= 0 Then
                    hasBGColor = True

                    If dt.Columns.IndexOf("__ColorHideSettings") >= 0 Then
                        gvDataTable.Columns(dt.Columns.IndexOf("__ColorBG")).Visible = False
                    End If
                End If

                If dt.Columns.IndexOf("__ColorFG") >= 0 Then
                    hasFGColor = True

                    If dt.Columns.IndexOf("__ColorHideSettings") >= 0 Then
                        gvDataTable.Columns(dt.Columns.IndexOf("__ColorFG")).Visible = False
                    End If
                End If

                If dt.Columns.IndexOf("__ColorColumns") >= 0 Then
                    hasColorColumns = True

                    If dt.Columns.IndexOf("__ColorHideSettings") >= 0 Then
                        gvDataTable.Columns(dt.Columns.IndexOf("__ColorColumns")).Visible = False
                    End If
                End If

                hasColorSettings = True
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in SetColorSettings", ex)
        End Try
    End Sub

    Private Sub gv_CustomColumnDisplayText(sender As Object, e As CustomColumnDisplayTextEventArgs)
        Dim dataType = e.Column.ColumnType.FullName
        Dim dataValue = e.Value
        If e.Column.ColumnType.FullName = "System.DateTime" AndAlso Not IsDBNull(e.Value) AndAlso e.Value <> Nothing AndAlso e.Value.ToString().EndsWith(" 12:00:00 AM") Then
            e.DisplayText = String.Format("{0:d}", e.Value)
        End If
    End Sub

    Sub setNavigateBetResults()
        Try
            If DS Is Nothing OrElse DS.Tables.Count < 2 Then
                lciTableTxt.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                lciTablePrev.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                lciTableNext.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                Return
            Else
                lciTableTxt.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                lciTablePrev.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                lciTableNext.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always

                Dim curTable As Int16 = txtCurTable.Tag
                btnPrev.Enabled = curTable > 1
                btnNext.Enabled = curTable < DS.Tables.Count
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in setNavigateBetResults", ex)
        End Try
    End Sub

    Private Function GetRowValue(rowView As Object, columnName As String) As Object
        If TypeOf rowView Is DataRowView Then
            Dim drv As DataRowView = CType(rowView, DataRowView)
            Return drv(columnName)
        ElseIf rowView IsNot Nothing Then
            ' Try to get property value using reflection
            Dim prop = rowView.GetType().GetProperty(columnName)
            If prop IsNot Nothing Then
                Return prop.GetValue(rowView)
            End If
        End If
        Return Nothing
    End Function




End Class