﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="dsMain" targetNamespace="http://tempuri.org/dsMain.xsd" xmlns:mstns="http://tempuri.org/dsMain.xsd" xmlns="http://tempuri.org/dsMain.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="2" FunctionsComponentName="QueriesTableAdapter" GeneratorFunctionsComponentClassName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" UserFunctionsComponentName="QueriesTableAdapter" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="EPDATAConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="EPDATAConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Brands_FrontDesk.My.MySettings.GlobalReference.Default.EPDATAConnectionString" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="brandsConnectionString" IsAppSettingsProperty="true" Modifier="Assembly" Name="brandsConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Brands_FrontDesk.My.MySettings.GlobalReference.Default.brandsConnectionString" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="BrandsConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="BrandsConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Brands_FrontDesk.My.MySettings.GlobalReference.Default.BrandsConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="prc_RptDeliveryTableAdapter" GeneratorDataComponentClassName="prc_RptDeliveryTableAdapter" Name="prc_RptDelivery" UserDataComponentName="prc_RptDeliveryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="EPDATA.custom.prc_RptDelivery" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>custom.prc_RptDelivery</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@TicketNumber" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TicketID" DataSetColumn="TicketID" />
              <Mapping SourceColumn="DeliverBy" DataSetColumn="DeliverBy" />
              <Mapping SourceColumn="CoNum" DataSetColumn="CoNum" />
              <Mapping SourceColumn="PayrollNum" DataSetColumn="PayrollNum" />
              <Mapping SourceColumn="CO_NAME" DataSetColumn="CO_NAME" />
              <Mapping SourceColumn="CO_STREET" DataSetColumn="CO_STREET" />
              <Mapping SourceColumn="CO_CITY" DataSetColumn="CO_CITY" />
              <Mapping SourceColumn="CO_STATE" DataSetColumn="CO_STATE" />
              <Mapping SourceColumn="CO_ZIP" DataSetColumn="CO_ZIP" />
              <Mapping SourceColumn="CO_PHONE" DataSetColumn="CO_PHONE" />
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="CO_EXTENSION" DataSetColumn="CO_EXTENSION" />
              <Mapping SourceColumn="SHIPSAME" DataSetColumn="SHIPSAME" />
              <Mapping SourceColumn="SH_NAME" DataSetColumn="SH_NAME" />
              <Mapping SourceColumn="SH_STREET" DataSetColumn="SH_STREET" />
              <Mapping SourceColumn="SH_CITY" DataSetColumn="SH_CITY" />
              <Mapping SourceColumn="SH_STATE" DataSetColumn="SH_STATE" />
              <Mapping SourceColumn="SH_ZIP" DataSetColumn="SH_ZIP" />
              <Mapping SourceColumn="SH_PHONE" DataSetColumn="SH_PHONE" />
              <Mapping SourceColumn="SH_EXTENSION" DataSetColumn="SH_EXTENSION" />
              <Mapping SourceColumn="ShipContact" DataSetColumn="ShipContact" />
              <Mapping SourceColumn="ShipModem" DataSetColumn="ShipModem" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DeliveryTicketsSumTableAdapter" GeneratorDataComponentClassName="DeliveryTicketsSumTableAdapter" Name="DeliveryTicketsSum" UserDataComponentName="DeliveryTicketsSumTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        custom.DeliveryTickets.DeliverBy, custom.Deliveries.ID, custom.Deliveries.ScannedDate
FROM            custom.Deliveries INNER JOIN
                         custom.DeliveryTickets ON custom.Deliveries.TicketID = custom.DeliveryTickets.ID
WHERE        (custom.Deliveries.ScannedDate BETWEEN @DateFrom AND @DateTo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="DateFrom" ColumnName="ScannedDate" DataSourceName="EPDATA.custom.Deliveries" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@DateFrom" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ScannedDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="DateTo" ColumnName="ScannedDate" DataSourceName="EPDATA.custom.Deliveries" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@DateTo" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ScannedDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="DeliverBy" DataSetColumn="DeliverBy" />
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="ScannedDate" DataSetColumn="ScannedDate" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DeliveryReportTableAdapter" GeneratorDataComponentClassName="DeliveryReportTableAdapter" Name="DeliveryReport" UserDataComponentName="DeliveryReportTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        custom.DeliveryTickets.DeliverBy, custom.Deliveries.ScannedDate, custom.Deliveries.ScannedBy, custom.Deliveries.BarCode, custom.Deliveries.Note
FROM            custom.Deliveries INNER JOIN
                         custom.DeliveryTickets ON custom.Deliveries.TicketID = custom.DeliveryTickets.ID
WHERE        (custom.DeliveryTickets.DeliverBy = @DeliverBy) AND (custom.Deliveries.ScannedDate BETWEEN @DateFrom AND @DateTo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="DeliverBy" ColumnName="DeliverBy" DataSourceName="EPDATA.custom.DeliveryTickets" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@DeliverBy" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumn="DeliverBy" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="DateFrom" ColumnName="ScannedDate" DataSourceName="EPDATA.custom.Deliveries" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@DateFrom" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ScannedDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="DateTo" ColumnName="ScannedDate" DataSourceName="EPDATA.custom.Deliveries" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@DateTo" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ScannedDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="DeliverBy" DataSetColumn="DeliverBy" />
              <Mapping SourceColumn="ScannedDate" DataSetColumn="ScannedDate" />
              <Mapping SourceColumn="ScannedBy" DataSetColumn="ScannedBy" />
              <Mapping SourceColumn="BarCode" DataSetColumn="BarCode" />
              <Mapping SourceColumn="Note" DataSetColumn="Note" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CALENDAR_RULESTableAdapter" GeneratorDataComponentClassName="CALENDAR_RULESTableAdapter" Name="CALENDAR_RULES" UserDataComponentName="CALENDAR_RULESTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>WITH B AS (
SELECT conum, period_id, process_date, check_date, 
	Num = ROW_NUMBER() OVER (PARTITION BY conum, period_id ORDER BY process_date) 
FROM dbo.CALENDAR
WHERE completed = 'NO' AND process_date &gt;= CAST(GETDATE() AS date)
)

SELECT A.conum, A.period_id, A.frequency, A.entry_type, 
        B.process_date ,
        B.check_date ,
        A.ProcessTime
FROM dbo.CALENDAR_RULES A
LEFT OUTER JOIN (SELECT * FROM B WHERE Num = 1) B ON A.conum = B.conum AND A.period_id = B.period_id
WHERE A.conum = @CoNum</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CoNum" ColumnName="conum" DataSourceName="" DataTypeServer="decimal(6, 0)" DbType="Decimal" Direction="Input" ParameterName="@CoNum" Precision="6" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="conum" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="conum" DataSetColumn="conum" />
              <Mapping SourceColumn="period_id" DataSetColumn="period_id" />
              <Mapping SourceColumn="frequency" DataSetColumn="frequency" />
              <Mapping SourceColumn="entry_type" DataSetColumn="entry_type" />
              <Mapping SourceColumn="process_date" DataSetColumn="process_date" />
              <Mapping SourceColumn="check_date" DataSetColumn="check_date" />
              <Mapping SourceColumn="ProcessTime" DataSetColumn="ProcessTime" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="prc_TaxReconListTableAdapter" GeneratorDataComponentClassName="prc_TaxReconListTableAdapter" Name="prc_TaxReconList" UserDataComponentName="prc_TaxReconListTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="EPDATA.custom.prc_TaxReconList" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>custom.prc_TaxReconList</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Year" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@QuarterNum" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="split_conum" DataSetColumn="split_conum" />
              <Mapping SourceColumn="split_loc_id" DataSetColumn="split_loc_id" />
              <Mapping SourceColumn="payroll_num" DataSetColumn="payroll_num" />
              <Mapping SourceColumn="split_debit" DataSetColumn="split_debit" />
              <Mapping SourceColumn="split_credit" DataSetColumn="split_credit" />
              <Mapping SourceColumn="tax_route" DataSetColumn="tax_route" />
              <Mapping SourceColumn="split_paymentregkey" DataSetColumn="paymentregkey" />
              <Mapping SourceColumn="CO_NAME" DataSetColumn="CO_NAME" />
              <Mapping SourceColumn="split_due" DataSetColumn="split_due" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="prc_TaxReconDetailsTableAdapter" GeneratorDataComponentClassName="prc_TaxReconDetailsTableAdapter" Name="prc_TaxReconDetails" UserDataComponentName="prc_TaxReconDetailsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="EPDATA.custom.prc_TaxReconDetails" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>custom.prc_TaxReconDetails</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="decimal" DbType="Decimal" Direction="Input" ParameterName="@CoNum" Precision="6" ProviderType="Decimal" Scale="0" Size="5" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Year" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@QuarterNum" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Num" DataSetColumn="Num" />
              <Mapping SourceColumn="loc_id" DataSetColumn="loc_id" />
              <Mapping SourceColumn="catid" DataSetColumn="catid" />
              <Mapping SourceColumn="split_due" DataSetColumn="split_due" />
              <Mapping SourceColumn="Debit" DataSetColumn="Debit" />
              <Mapping SourceColumn="Credit" DataSetColumn="Credit" />
              <Mapping SourceColumn="cat_descr" DataSetColumn="cat_descr" />
              <Mapping SourceColumn="cat_subdescr" DataSetColumn="cat_subdescr" />
              <Mapping SourceColumn="cat_glnum" DataSetColumn="cat_glnum" />
              <Mapping SourceColumn="TempDesc" DataSetColumn="TempDesc" />
              <Mapping SourceColumn="TDebits" DataSetColumn="TDebits" />
              <Mapping SourceColumn="TCredits" DataSetColumn="TCredits" />
              <Mapping SourceColumn="split_paymentregkey" DataSetColumn="split_paymentregkey1" />
              <Mapping SourceColumn="CurRate" DataSetColumn="CurRate" />
              <Mapping SourceColumn="PrevRate" DataSetColumn="PrevRate" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="COMPANYTableAdapter" GeneratorDataComponentClassName="COMPANYTableAdapter" Name="COMPANY" UserDataComponentName="COMPANYTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="EPDATA.dbo.COMPANY" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        CONUM, CO_NAME, CO_STREET, CO_CITY, CO_STATE, CO_ZIP, CO_PHONE, CO_EXTENSION, CO_FAX
FROM            COMPANY
WHERE        (CONUM = @CoNum)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CoNum" ColumnName="CONUM" DataSourceName="" DataTypeServer="decimal(6, 0)" DbType="Decimal" Direction="Input" ParameterName="@CoNum" Precision="6" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="CONUM" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CONUM" DataSetColumn="CONUM" />
              <Mapping SourceColumn="CO_NAME" DataSetColumn="CO_NAME" />
              <Mapping SourceColumn="CO_STREET" DataSetColumn="CO_STREET" />
              <Mapping SourceColumn="CO_CITY" DataSetColumn="CO_CITY" />
              <Mapping SourceColumn="CO_STATE" DataSetColumn="CO_STATE" />
              <Mapping SourceColumn="CO_ZIP" DataSetColumn="CO_ZIP" />
              <Mapping SourceColumn="CO_PHONE" DataSetColumn="CO_PHONE" />
              <Mapping SourceColumn="CO_EXTENSION" DataSetColumn="CO_EXTENSION" />
              <Mapping SourceColumn="CO_FAX" DataSetColumn="CO_FAX" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="NachaTaxDraftsTableAdapter" GeneratorDataComponentClassName="NachaTaxDraftsTableAdapter" Name="NachaTaxDrafts" UserDataComponentName="NachaTaxDraftsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT	N.ID, N.DateCreated, N.CoNum, N.RegisterKey, N.Description, N.EffectiveDate
		, N.Status, N.PostedDate, N.EmailSentTo, N.EmailSenton
		, Amount = COALESCE (- R.payment_amt, R.deposit_amt)
		, N.Comments, N.FollowUpOn, RiskStatus = rma.Status
FROM	custom.NachaTaxDrafts AS N 
		INNER JOIN sb_register AS R ON N.RegisterKey = R.register_key
		LEFT JOIN custom.RiskManagementAlerts rma 
			ON rma.CoNum = N.CoNum AND rma.PrNum = R.payroll_num
WHERE	(N.EffectiveDate &lt;= @UpToDate) AND (N.Status LIKE @Status)
ORDER BY N.CoNum</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="UpToDate" ColumnName="EffectiveDate" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="date" DbType="AnsiString" Direction="Input" ParameterName="@UpToDate" Precision="0" ProviderType="Date" Scale="0" Size="3" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="Status" ColumnName="Status" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="DateCreated" DataSetColumn="DateCreated" />
              <Mapping SourceColumn="CoNum" DataSetColumn="CoNum" />
              <Mapping SourceColumn="RegisterKey" DataSetColumn="RegisterKey" />
              <Mapping SourceColumn="Description" DataSetColumn="Description" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="Status" DataSetColumn="Status" />
              <Mapping SourceColumn="PostedDate" DataSetColumn="PostedDate" />
              <Mapping SourceColumn="EmailSentTo" DataSetColumn="EmailSentTo" />
              <Mapping SourceColumn="EmailSenton" DataSetColumn="EmailSenton" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="Comments" DataSetColumn="Comments" />
              <Mapping SourceColumn="FollowUpOn" DataSetColumn="FollowUpOn" />
              <Mapping SourceColumn="RiskStatus" DataSetColumn="RiskStatus" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="UpdateQuery" Modifier="Public" Name="UpdateQuery" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="UpdateQuery">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE       custom.NachaTaxDrafts
SET                EffectiveDate = @EffectiveDate, Status = @Status, Comments = @Comments, FollowUpOn = @FollowUpOn
WHERE        (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="EffectiveDate" ColumnName="EffectiveDate" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="date" DbType="AnsiString" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="Date" Scale="0" Size="3" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="Status" ColumnName="Status" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="Comments" ColumnName="Comments" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="nvarchar(MAX)" DbType="String" Direction="Input" ParameterName="@Comments" Precision="0" ProviderType="NVarChar" Scale="0" Size="**********" SourceColumn="Comments" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="FollowUpOn" ColumnName="FollowUpOn" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FollowUpOn" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="FollowUpOn" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="EPDATA.custom.NachaTaxDrafts" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CoListTableAdapter" GeneratorDataComponentClassName="CoListTableAdapter" Name="CoList" UserDataComponentName="CoListTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        A.CONUM, A.CO_NAME, B.DDLimit, B.TaxLimit, A.CO_STATUS, B.DDAllowed, B.TestTaxPct, B.TestPayrollPct
FROM            COMPANY AS A LEFT OUTER JOIN
                         custom.CoMonitor AS B ON A.CONUM = B.CoCode
WHERE        (A.CO_STATUS LIKE @Status)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="Status" ColumnName="CO_STATUS" DataSourceName="EPDATA.dbo.COMPANY" DataTypeServer="varchar(35)" DbType="AnsiString" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="VarChar" Scale="0" Size="35" SourceColumn="CO_STATUS" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="DDLimit" DataSetColumn="DDLimit" />
              <Mapping SourceColumn="TaxLimit" DataSetColumn="TaxLimit" />
              <Mapping SourceColumn="CONUM" DataSetColumn="CONUM" />
              <Mapping SourceColumn="CO_NAME" DataSetColumn="CO_NAME" />
              <Mapping SourceColumn="CO_STATUS" DataSetColumn="CO_STATUS" />
              <Mapping SourceColumn="DDAllowed" DataSetColumn="DDAllowed" />
              <Mapping SourceColumn="TestTaxPct" DataSetColumn="TestTaxPct" />
              <Mapping SourceColumn="TestPayrollPct" DataSetColumn="TestPayrollPct" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DDPyrollHistoryTableAdapter" GeneratorDataComponentClassName="DDPyrollHistoryTableAdapter" Name="DDPyrollHistory" UserDataComponentName="DDPyrollHistoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="EPDATAConnectionString (MySettings)" DbObjectName="EPDATA.dbo.PAYROLL" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        B.CHECK_DATE, B.PRNUM AS PAYROLL_NUM, SUM(CASE WHEN A.entry_type NOT IN ('B', 'T') THEN A.AMOUNT ELSE 0 END) AS AMOUNT, 
                         SUM(CASE WHEN A.entry_type = 'T' THEN A.AMOUNT ELSE 0 END) AS TAXES, B.TAX941
FROM            PAYROLL AS B LEFT OUTER JOIN
                         NACHA_DD AS A ON A.CONUM = B.CONUM AND A.PAYROLL_NUM = B.PRNUM
WHERE        (A.entry_type &lt;&gt; 'B' OR
                         A.entry_type IS NULL) AND (A.TRAN_TYPE NOT IN ('27', '37') OR
                         A.TRAN_TYPE IS NULL) AND (B.CONUM = @CoNum) AND (DATEDIFF(MONTH, GETDATE(), B.CHECK_DATE) &gt; - 13)
GROUP BY B.PRNUM, B.CHECK_DATE, B.TAX941
ORDER BY B.CHECK_DATE DESC</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CoNum" ColumnName="CONUM" DataSourceName="EPDATA.dbo.PAYROLL" DataTypeServer="decimal(6, 0)" DbType="Decimal" Direction="Input" ParameterName="@CoNum" Precision="6" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="CONUM" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CHECK_DATE" DataSetColumn="CHECK_DATE" />
              <Mapping SourceColumn="PAYROLL_NUM" DataSetColumn="PAYROLL_NUM" />
              <Mapping SourceColumn="AMOUNT" DataSetColumn="AMOUNT" />
              <Mapping SourceColumn="TAXES" DataSetColumn="TAXES" />
              <Mapping SourceColumn="TAX941" DataSetColumn="TAX941" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="prc_UpdateCoMonitorTableAdapter" GeneratorDataComponentClassName="prc_UpdateCoMonitorTableAdapter" Name="prc_UpdateCoMonitor" UserDataComponentName="prc_UpdateCoMonitorTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="BrandsConnectionString (MySettings)" DbObjectName="Brands.dbo.prc_UpdateCoMonitor" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.prc_UpdateCoMonitor</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="decimal" DbType="Decimal" Direction="Input" ParameterName="@CoNum" Precision="6" ProviderType="Decimal" Scale="0" Size="5" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="money" DbType="Currency" Direction="Input" ParameterName="@DDLimit" Precision="19" ProviderType="Money" Scale="4" Size="8" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="money" DbType="Currency" Direction="Input" ParameterName="@TaxLimit" Precision="19" ProviderType="Money" Scale="4" Size="8" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@DDAllowed" Precision="1" ProviderType="Bit" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CONUM" DataSetColumn="CONUM" />
              <Mapping SourceColumn="CO_NAME" DataSetColumn="CO_NAME" />
              <Mapping SourceColumn="DDLimit" DataSetColumn="DDLimit" />
              <Mapping SourceColumn="TaxLimit" DataSetColumn="TaxLimit" />
              <Mapping SourceColumn="CO_STATUS" DataSetColumn="CO_STATUS" />
              <Mapping SourceColumn="DDAllowed" DataSetColumn="DDAllowed" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="dsMain" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="dsMain" msprop:Generator_UserDSName="dsMain">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="prc_RptDelivery" msprop:Generator_TableClassName="prc_RptDeliveryDataTable" msprop:Generator_TableVarName="tableprc_RptDelivery" msprop:Generator_TablePropName="prc_RptDelivery" msprop:Generator_RowDeletingName="prc_RptDeliveryRowDeleting" msprop:Generator_RowChangingName="prc_RptDeliveryRowChanging" msprop:Generator_RowEvHandlerName="prc_RptDeliveryRowChangeEventHandler" msprop:Generator_RowDeletedName="prc_RptDeliveryRowDeleted" msprop:Generator_UserTableName="prc_RptDelivery" msprop:Generator_RowChangedName="prc_RptDeliveryRowChanged" msprop:Generator_RowEvArgName="prc_RptDeliveryRowChangeEvent" msprop:Generator_RowClassName="prc_RptDeliveryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TicketID" msprop:Generator_ColumnVarNameInTable="columnTicketID" msprop:Generator_ColumnPropNameInRow="TicketID" msprop:Generator_ColumnPropNameInTable="TicketIDColumn" msprop:Generator_UserColumnName="TicketID" type="xs:int" />
              <xs:element name="DeliverBy" msprop:Generator_ColumnVarNameInTable="columnDeliverBy" msprop:Generator_ColumnPropNameInRow="DeliverBy" msprop:Generator_ColumnPropNameInTable="DeliverByColumn" msprop:Generator_UserColumnName="DeliverBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CoNum" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnCoNum" msprop:Generator_ColumnPropNameInRow="CoNum" msprop:Generator_ColumnPropNameInTable="CoNumColumn" msprop:Generator_UserColumnName="CoNum" type="xs:int" minOccurs="0" />
              <xs:element name="PayrollNum" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnPayrollNum" msprop:Generator_ColumnPropNameInRow="PayrollNum" msprop:Generator_ColumnPropNameInTable="PayrollNumColumn" msprop:Generator_UserColumnName="PayrollNum" type="xs:int" minOccurs="0" />
              <xs:element name="CO_NAME" msprop:Generator_ColumnVarNameInTable="columnCO_NAME" msprop:Generator_ColumnPropNameInRow="CO_NAME" msprop:Generator_ColumnPropNameInTable="CO_NAMEColumn" msprop:Generator_UserColumnName="CO_NAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_STREET" msprop:Generator_ColumnVarNameInTable="columnCO_STREET" msprop:Generator_ColumnPropNameInRow="CO_STREET" msprop:Generator_ColumnPropNameInTable="CO_STREETColumn" msprop:Generator_UserColumnName="CO_STREET" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_CITY" msprop:Generator_ColumnVarNameInTable="columnCO_CITY" msprop:Generator_ColumnPropNameInRow="CO_CITY" msprop:Generator_ColumnPropNameInTable="CO_CITYColumn" msprop:Generator_UserColumnName="CO_CITY" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_STATE" msprop:Generator_ColumnVarNameInTable="columnCO_STATE" msprop:Generator_ColumnPropNameInRow="CO_STATE" msprop:Generator_ColumnPropNameInTable="CO_STATEColumn" msprop:Generator_UserColumnName="CO_STATE" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_ZIP" msprop:Generator_ColumnVarNameInTable="columnCO_ZIP" msprop:Generator_ColumnPropNameInRow="CO_ZIP" msprop:Generator_ColumnPropNameInTable="CO_ZIPColumn" msprop:Generator_UserColumnName="CO_ZIP" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_PHONE" msprop:Generator_ColumnVarNameInTable="columnCO_PHONE" msprop:Generator_ColumnPropNameInRow="CO_PHONE" msprop:Generator_ColumnPropNameInTable="CO_PHONEColumn" msprop:Generator_UserColumnName="CO_PHONE" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="14" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="CO_EXTENSION" msprop:Generator_ColumnVarNameInTable="columnCO_EXTENSION" msprop:Generator_ColumnPropNameInRow="CO_EXTENSION" msprop:Generator_ColumnPropNameInTable="CO_EXTENSIONColumn" msprop:Generator_UserColumnName="CO_EXTENSION" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SHIPSAME" msprop:Generator_ColumnVarNameInTable="columnSHIPSAME" msprop:Generator_ColumnPropNameInRow="SHIPSAME" msprop:Generator_ColumnPropNameInTable="SHIPSAMEColumn" msprop:Generator_UserColumnName="SHIPSAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_NAME" msprop:Generator_ColumnVarNameInTable="columnSH_NAME" msprop:Generator_ColumnPropNameInRow="SH_NAME" msprop:Generator_ColumnPropNameInTable="SH_NAMEColumn" msprop:Generator_UserColumnName="SH_NAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_STREET" msprop:Generator_ColumnVarNameInTable="columnSH_STREET" msprop:Generator_ColumnPropNameInRow="SH_STREET" msprop:Generator_ColumnPropNameInTable="SH_STREETColumn" msprop:Generator_UserColumnName="SH_STREET" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_CITY" msprop:Generator_ColumnVarNameInTable="columnSH_CITY" msprop:Generator_ColumnPropNameInRow="SH_CITY" msprop:Generator_ColumnPropNameInTable="SH_CITYColumn" msprop:Generator_UserColumnName="SH_CITY" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_STATE" msprop:Generator_ColumnVarNameInTable="columnSH_STATE" msprop:Generator_ColumnPropNameInRow="SH_STATE" msprop:Generator_ColumnPropNameInTable="SH_STATEColumn" msprop:Generator_UserColumnName="SH_STATE" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_ZIP" msprop:Generator_ColumnVarNameInTable="columnSH_ZIP" msprop:Generator_ColumnPropNameInRow="SH_ZIP" msprop:Generator_ColumnPropNameInTable="SH_ZIPColumn" msprop:Generator_UserColumnName="SH_ZIP" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_PHONE" msprop:Generator_ColumnVarNameInTable="columnSH_PHONE" msprop:Generator_ColumnPropNameInRow="SH_PHONE" msprop:Generator_ColumnPropNameInTable="SH_PHONEColumn" msprop:Generator_UserColumnName="SH_PHONE" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="14" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SH_EXTENSION" msprop:Generator_ColumnVarNameInTable="columnSH_EXTENSION" msprop:Generator_ColumnPropNameInRow="SH_EXTENSION" msprop:Generator_ColumnPropNameInTable="SH_EXTENSIONColumn" msprop:Generator_UserColumnName="SH_EXTENSION" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ShipContact" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnShipContact" msprop:Generator_ColumnPropNameInRow="ShipContact" msprop:Generator_ColumnPropNameInTable="ShipContactColumn" msprop:Generator_UserColumnName="ShipContact" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="75" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ShipModem" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnShipModem" msprop:Generator_ColumnPropNameInRow="ShipModem" msprop:Generator_ColumnPropNameInTable="ShipModemColumn" msprop:Generator_UserColumnName="ShipModem" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="14" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DeliveryTicketsSum" msprop:Generator_TableClassName="DeliveryTicketsSumDataTable" msprop:Generator_TableVarName="tableDeliveryTicketsSum" msprop:Generator_TablePropName="DeliveryTicketsSum" msprop:Generator_RowDeletingName="DeliveryTicketsSumRowDeleting" msprop:Generator_RowChangingName="DeliveryTicketsSumRowChanging" msprop:Generator_RowEvHandlerName="DeliveryTicketsSumRowChangeEventHandler" msprop:Generator_RowDeletedName="DeliveryTicketsSumRowDeleted" msprop:Generator_UserTableName="DeliveryTicketsSum" msprop:Generator_RowChangedName="DeliveryTicketsSumRowChanged" msprop:Generator_RowEvArgName="DeliveryTicketsSumRowChangeEvent" msprop:Generator_RowClassName="DeliveryTicketsSumRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="DeliverBy" msprop:Generator_ColumnVarNameInTable="columnDeliverBy" msprop:Generator_ColumnPropNameInRow="DeliverBy" msprop:Generator_ColumnPropNameInTable="DeliverByColumn" msprop:Generator_UserColumnName="DeliverBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="ScannedDate" msprop:Generator_ColumnVarNameInTable="columnScannedDate" msprop:Generator_ColumnPropNameInRow="ScannedDate" msprop:Generator_ColumnPropNameInTable="ScannedDateColumn" msprop:Generator_UserColumnName="ScannedDate" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DeliveryReport" msprop:Generator_TableClassName="DeliveryReportDataTable" msprop:Generator_TableVarName="tableDeliveryReport" msprop:Generator_TablePropName="DeliveryReport" msprop:Generator_RowDeletingName="DeliveryReportRowDeleting" msprop:Generator_RowChangingName="DeliveryReportRowChanging" msprop:Generator_RowEvHandlerName="DeliveryReportRowChangeEventHandler" msprop:Generator_RowDeletedName="DeliveryReportRowDeleted" msprop:Generator_UserTableName="DeliveryReport" msprop:Generator_RowChangedName="DeliveryReportRowChanged" msprop:Generator_RowEvArgName="DeliveryReportRowChangeEvent" msprop:Generator_RowClassName="DeliveryReportRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="DeliverBy" msprop:Generator_ColumnVarNameInTable="columnDeliverBy" msprop:Generator_ColumnPropNameInRow="DeliverBy" msprop:Generator_ColumnPropNameInTable="DeliverByColumn" msprop:Generator_UserColumnName="DeliverBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ScannedDate" msprop:Generator_ColumnVarNameInTable="columnScannedDate" msprop:Generator_ColumnPropNameInRow="ScannedDate" msprop:Generator_ColumnPropNameInTable="ScannedDateColumn" msprop:Generator_UserColumnName="ScannedDate" type="xs:dateTime" />
              <xs:element name="ScannedBy" msprop:Generator_ColumnVarNameInTable="columnScannedBy" msprop:Generator_ColumnPropNameInRow="ScannedBy" msprop:Generator_ColumnPropNameInTable="ScannedByColumn" msprop:Generator_UserColumnName="ScannedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BarCode" msprop:Generator_ColumnVarNameInTable="columnBarCode" msprop:Generator_ColumnPropNameInRow="BarCode" msprop:Generator_ColumnPropNameInTable="BarCodeColumn" msprop:Generator_UserColumnName="BarCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Note" msprop:Generator_ColumnVarNameInTable="columnNote" msprop:Generator_ColumnPropNameInRow="Note" msprop:Generator_ColumnPropNameInTable="NoteColumn" msprop:Generator_UserColumnName="Note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CALENDAR_RULES" msprop:Generator_TableClassName="CALENDAR_RULESDataTable" msprop:Generator_TableVarName="tableCALENDAR_RULES" msprop:Generator_TablePropName="CALENDAR_RULES" msprop:Generator_RowDeletingName="CALENDAR_RULESRowDeleting" msprop:Generator_RowChangingName="CALENDAR_RULESRowChanging" msprop:Generator_RowEvHandlerName="CALENDAR_RULESRowChangeEventHandler" msprop:Generator_RowDeletedName="CALENDAR_RULESRowDeleted" msprop:Generator_UserTableName="CALENDAR_RULES" msprop:Generator_RowChangedName="CALENDAR_RULESRowChanged" msprop:Generator_RowEvArgName="CALENDAR_RULESRowChangeEvent" msprop:Generator_RowClassName="CALENDAR_RULESRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="conum" msprop:Generator_ColumnVarNameInTable="columnconum" msprop:Generator_ColumnPropNameInRow="conum" msprop:Generator_ColumnPropNameInTable="conumColumn" msprop:Generator_UserColumnName="conum" type="xs:decimal" />
              <xs:element name="period_id" msprop:Generator_ColumnVarNameInTable="columnperiod_id" msprop:Generator_ColumnPropNameInRow="period_id" msprop:Generator_ColumnPropNameInTable="period_idColumn" msprop:Generator_UserColumnName="period_id" type="xs:int" />
              <xs:element name="frequency" msprop:Generator_ColumnVarNameInTable="columnfrequency" msprop:Generator_ColumnPropNameInRow="frequency" msprop:Generator_ColumnPropNameInTable="frequencyColumn" msprop:Generator_UserColumnName="frequency">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="entry_type" msprop:Generator_ColumnVarNameInTable="columnentry_type" msprop:Generator_ColumnPropNameInRow="entry_type" msprop:Generator_ColumnPropNameInTable="entry_typeColumn" msprop:Generator_UserColumnName="entry_type" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="75" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="process_date" msprop:Generator_ColumnVarNameInTable="columnprocess_date" msprop:Generator_ColumnPropNameInRow="process_date" msprop:Generator_ColumnPropNameInTable="process_dateColumn" msprop:Generator_UserColumnName="process_date" type="xs:dateTime" minOccurs="0" />
              <xs:element name="check_date" msprop:Generator_ColumnVarNameInTable="columncheck_date" msprop:Generator_ColumnPropNameInRow="check_date" msprop:Generator_ColumnPropNameInTable="check_dateColumn" msprop:Generator_UserColumnName="check_date" type="xs:dateTime" minOccurs="0" />
              <xs:element name="ProcessTime" msprop:Generator_ColumnVarNameInTable="columnProcessTime" msprop:Generator_ColumnPropNameInRow="ProcessTime" msprop:Generator_ColumnPropNameInTable="ProcessTimeColumn" msprop:Generator_UserColumnName="ProcessTime" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="prc_TaxReconList" msprop:Generator_TableClassName="prc_TaxReconListDataTable" msprop:Generator_TableVarName="tableprc_TaxReconList" msprop:Generator_TablePropName="prc_TaxReconList" msprop:Generator_RowDeletingName="prc_TaxReconListRowDeleting" msprop:Generator_RowChangingName="prc_TaxReconListRowChanging" msprop:Generator_RowEvHandlerName="prc_TaxReconListRowChangeEventHandler" msprop:Generator_RowDeletedName="prc_TaxReconListRowDeleted" msprop:Generator_UserTableName="prc_TaxReconList" msprop:Generator_RowChangedName="prc_TaxReconListRowChanged" msprop:Generator_RowEvArgName="prc_TaxReconListRowChangeEvent" msprop:Generator_RowClassName="prc_TaxReconListRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="split_conum" msprop:Generator_ColumnVarNameInTable="columnsplit_conum" msprop:Generator_ColumnPropNameInRow="split_conum" msprop:Generator_ColumnPropNameInTable="split_conumColumn" msprop:Generator_UserColumnName="split_conum" type="xs:int" minOccurs="0" />
              <xs:element name="split_loc_id" msprop:Generator_ColumnVarNameInTable="columnsplit_loc_id" msprop:Generator_ColumnPropNameInRow="split_loc_id" msprop:Generator_ColumnPropNameInTable="split_loc_idColumn" msprop:Generator_UserColumnName="split_loc_id" type="xs:int" minOccurs="0" />
              <xs:element name="payroll_num" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnpayroll_num" msprop:Generator_ColumnPropNameInRow="payroll_num" msprop:Generator_ColumnPropNameInTable="payroll_numColumn" msprop:Generator_UserColumnName="payroll_num" type="xs:int" minOccurs="0" />
              <xs:element name="split_debit" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnsplit_debit" msprop:Generator_ColumnPropNameInRow="split_debit" msprop:Generator_ColumnPropNameInTable="split_debitColumn" msprop:Generator_UserColumnName="split_debit" type="xs:decimal" minOccurs="0" />
              <xs:element name="split_credit" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnsplit_credit" msprop:Generator_ColumnPropNameInRow="split_credit" msprop:Generator_ColumnPropNameInTable="split_creditColumn" msprop:Generator_UserColumnName="split_credit" type="xs:decimal" minOccurs="0" />
              <xs:element name="split_due" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnsplit_due" msprop:Generator_ColumnPropNameInRow="split_due" msprop:Generator_ColumnPropNameInTable="split_dueColumn" msprop:Generator_UserColumnName="split_due" type="xs:string" minOccurs="0" />
              <xs:element name="tax_route" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columntax_route" msprop:Generator_ColumnPropNameInRow="tax_route" msprop:Generator_ColumnPropNameInTable="tax_routeColumn" msprop:Generator_UserColumnName="tax_route" type="xs:string" minOccurs="0" />
              <xs:element name="paymentregkey" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnpaymentregkey" msprop:Generator_ColumnPropNameInRow="paymentregkey" msprop:Generator_ColumnPropNameInTable="paymentregkeyColumn" msprop:Generator_UserColumnName="paymentregkey" type="xs:string" minOccurs="0" />
              <xs:element name="CO_NAME" msprop:Generator_ColumnVarNameInTable="columnCO_NAME" msprop:Generator_ColumnPropNameInRow="CO_NAME" msprop:Generator_ColumnPropNameInTable="CO_NAMEColumn" msprop:Generator_UserColumnName="CO_NAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="prc_TaxReconDetails" msprop:Generator_TableClassName="prc_TaxReconDetailsDataTable" msprop:Generator_TableVarName="tableprc_TaxReconDetails" msprop:Generator_TablePropName="prc_TaxReconDetails" msprop:Generator_RowDeletingName="prc_TaxReconDetailsRowDeleting" msprop:Generator_RowChangingName="prc_TaxReconDetailsRowChanging" msprop:Generator_RowEvHandlerName="prc_TaxReconDetailsRowChangeEventHandler" msprop:Generator_RowDeletedName="prc_TaxReconDetailsRowDeleted" msprop:Generator_UserTableName="prc_TaxReconDetails" msprop:Generator_RowChangedName="prc_TaxReconDetailsRowChanged" msprop:Generator_RowEvArgName="prc_TaxReconDetailsRowChangeEvent" msprop:Generator_RowClassName="prc_TaxReconDetailsRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Num" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnNum" msprop:Generator_ColumnPropNameInRow="Num" msprop:Generator_ColumnPropNameInTable="NumColumn" msprop:Generator_UserColumnName="Num" type="xs:int" />
              <xs:element name="loc_id" msprop:Generator_ColumnVarNameInTable="columnloc_id" msprop:Generator_ColumnPropNameInRow="loc_id" msprop:Generator_ColumnPropNameInTable="loc_idColumn" msprop:Generator_UserColumnName="loc_id" type="xs:int" minOccurs="0" />
              <xs:element name="catid" msprop:Generator_ColumnVarNameInTable="columncatid" msprop:Generator_ColumnPropNameInRow="catid" msprop:Generator_ColumnPropNameInTable="catidColumn" msprop:Generator_UserColumnName="catid" type="xs:int" minOccurs="0" />
              <xs:element name="split_due" msprop:Generator_ColumnVarNameInTable="columnsplit_due" msprop:Generator_ColumnPropNameInRow="split_due" msprop:Generator_ColumnPropNameInTable="split_dueColumn" msprop:Generator_UserColumnName="split_due" type="xs:decimal" minOccurs="0" />
              <xs:element name="Debit" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnDebit" msprop:Generator_ColumnPropNameInRow="Debit" msprop:Generator_ColumnPropNameInTable="DebitColumn" msprop:Generator_UserColumnName="Debit" type="xs:decimal" minOccurs="0" />
              <xs:element name="Credit" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnCredit" msprop:Generator_ColumnPropNameInRow="Credit" msprop:Generator_ColumnPropNameInTable="CreditColumn" msprop:Generator_UserColumnName="Credit" type="xs:decimal" minOccurs="0" />
              <xs:element name="cat_descr" msprop:Generator_ColumnVarNameInTable="columncat_descr" msprop:Generator_ColumnPropNameInRow="cat_descr" msprop:Generator_ColumnPropNameInTable="cat_descrColumn" msprop:Generator_UserColumnName="cat_descr" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="cat_subdescr" msprop:Generator_ColumnVarNameInTable="columncat_subdescr" msprop:Generator_ColumnPropNameInRow="cat_subdescr" msprop:Generator_ColumnPropNameInTable="cat_subdescrColumn" msprop:Generator_UserColumnName="cat_subdescr" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="cat_glnum" msprop:Generator_ColumnVarNameInTable="columncat_glnum" msprop:Generator_ColumnPropNameInRow="cat_glnum" msprop:Generator_ColumnPropNameInTable="cat_glnumColumn" msprop:Generator_UserColumnName="cat_glnum" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="split_paymentregkey" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnsplit_paymentregkey" msprop:Generator_ColumnPropNameInRow="split_paymentregkey" msprop:Generator_ColumnPropNameInTable="split_paymentregkeyColumn" msprop:Generator_UserColumnName="split_paymentregkey" type="xs:string" minOccurs="0" />
              <xs:element name="TempDesc" msprop:Generator_ColumnVarNameInTable="columnTempDesc" msprop:Generator_ColumnPropNameInRow="TempDesc" msprop:Generator_ColumnPropNameInTable="TempDescColumn" msprop:Generator_UserColumnName="TempDesc" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TDebits" msprop:Generator_ColumnVarNameInTable="columnTDebits" msprop:Generator_ColumnPropNameInRow="TDebits" msprop:Generator_ColumnPropNameInTable="TDebitsColumn" msprop:Generator_UserColumnName="TDebits" type="xs:decimal" minOccurs="0" />
              <xs:element name="TCredits" msprop:Generator_ColumnVarNameInTable="columnTCredits" msprop:Generator_ColumnPropNameInRow="TCredits" msprop:Generator_ColumnPropNameInTable="TCreditsColumn" msprop:Generator_UserColumnName="TCredits" type="xs:decimal" minOccurs="0" />
              <xs:element name="split_paymentregkey1" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msdata:Caption="split_paymentregkey" msprop:Generator_ColumnVarNameInTable="columnsplit_paymentregkey1" msprop:Generator_ColumnPropNameInRow="split_paymentregkey1" msprop:Generator_ColumnPropNameInTable="split_paymentregkey1Column" msprop:Generator_UserColumnName="split_paymentregkey1" type="xs:string" minOccurs="0" />
              <xs:element name="CurRate" msprop:Generator_ColumnVarNameInTable="columnCurRate" msprop:Generator_ColumnPropNameInRow="CurRate" msprop:Generator_ColumnPropNameInTable="CurRateColumn" msprop:Generator_UserColumnName="CurRate" type="xs:decimal" minOccurs="0" />
              <xs:element name="PrevRate" msprop:Generator_ColumnVarNameInTable="columnPrevRate" msprop:Generator_ColumnPropNameInRow="PrevRate" msprop:Generator_ColumnPropNameInTable="PrevRateColumn" msprop:Generator_UserColumnName="PrevRate" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="COMPANY" msprop:Generator_TableClassName="COMPANYDataTable" msprop:Generator_TableVarName="tableCOMPANY" msprop:Generator_TablePropName="COMPANY" msprop:Generator_RowDeletingName="COMPANYRowDeleting" msprop:Generator_RowChangingName="COMPANYRowChanging" msprop:Generator_RowEvHandlerName="COMPANYRowChangeEventHandler" msprop:Generator_RowDeletedName="COMPANYRowDeleted" msprop:Generator_UserTableName="COMPANY" msprop:Generator_RowChangedName="COMPANYRowChanged" msprop:Generator_RowEvArgName="COMPANYRowChangeEvent" msprop:Generator_RowClassName="COMPANYRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CONUM" msprop:Generator_ColumnVarNameInTable="columnCONUM" msprop:Generator_ColumnPropNameInRow="CONUM" msprop:Generator_ColumnPropNameInTable="CONUMColumn" msprop:Generator_UserColumnName="CONUM" type="xs:decimal" />
              <xs:element name="CO_NAME" msprop:Generator_ColumnVarNameInTable="columnCO_NAME" msprop:Generator_ColumnPropNameInRow="CO_NAME" msprop:Generator_ColumnPropNameInTable="CO_NAMEColumn" msprop:Generator_UserColumnName="CO_NAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_STREET" msprop:Generator_ColumnVarNameInTable="columnCO_STREET" msprop:Generator_ColumnPropNameInRow="CO_STREET" msprop:Generator_ColumnPropNameInTable="CO_STREETColumn" msprop:Generator_UserColumnName="CO_STREET" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_CITY" msprop:Generator_ColumnVarNameInTable="columnCO_CITY" msprop:Generator_ColumnPropNameInRow="CO_CITY" msprop:Generator_ColumnPropNameInTable="CO_CITYColumn" msprop:Generator_UserColumnName="CO_CITY" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_STATE" msprop:Generator_ColumnVarNameInTable="columnCO_STATE" msprop:Generator_ColumnPropNameInRow="CO_STATE" msprop:Generator_ColumnPropNameInTable="CO_STATEColumn" msprop:Generator_UserColumnName="CO_STATE" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_ZIP" msprop:Generator_ColumnVarNameInTable="columnCO_ZIP" msprop:Generator_ColumnPropNameInRow="CO_ZIP" msprop:Generator_ColumnPropNameInTable="CO_ZIPColumn" msprop:Generator_UserColumnName="CO_ZIP" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_PHONE" msprop:Generator_ColumnVarNameInTable="columnCO_PHONE" msprop:Generator_ColumnPropNameInRow="CO_PHONE" msprop:Generator_ColumnPropNameInTable="CO_PHONEColumn" msprop:Generator_UserColumnName="CO_PHONE" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="14" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_EXTENSION" msprop:Generator_ColumnVarNameInTable="columnCO_EXTENSION" msprop:Generator_ColumnPropNameInRow="CO_EXTENSION" msprop:Generator_ColumnPropNameInTable="CO_EXTENSIONColumn" msprop:Generator_UserColumnName="CO_EXTENSION" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_FAX" msprop:Generator_ColumnVarNameInTable="columnCO_FAX" msprop:Generator_ColumnPropNameInRow="CO_FAX" msprop:Generator_ColumnPropNameInTable="CO_FAXColumn" msprop:Generator_UserColumnName="CO_FAX" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="14" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="NachaTaxDrafts" msprop:Generator_TableClassName="NachaTaxDraftsDataTable" msprop:Generator_TableVarName="tableNachaTaxDrafts" msprop:Generator_TablePropName="NachaTaxDrafts" msprop:Generator_RowDeletingName="NachaTaxDraftsRowDeleting" msprop:Generator_RowChangingName="NachaTaxDraftsRowChanging" msprop:Generator_RowEvHandlerName="NachaTaxDraftsRowChangeEventHandler" msprop:Generator_RowDeletedName="NachaTaxDraftsRowDeleted" msprop:Generator_UserTableName="NachaTaxDrafts" msprop:Generator_RowChangedName="NachaTaxDraftsRowChanged" msprop:Generator_RowEvArgName="NachaTaxDraftsRowChangeEvent" msprop:Generator_RowClassName="NachaTaxDraftsRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="DateCreated" msprop:Generator_ColumnVarNameInTable="columnDateCreated" msprop:Generator_ColumnPropNameInRow="DateCreated" msprop:Generator_ColumnPropNameInTable="DateCreatedColumn" msprop:Generator_UserColumnName="DateCreated" type="xs:dateTime" />
              <xs:element name="CoNum" msprop:Generator_ColumnVarNameInTable="columnCoNum" msprop:Generator_ColumnPropNameInRow="CoNum" msprop:Generator_ColumnPropNameInTable="CoNumColumn" msprop:Generator_UserColumnName="CoNum" type="xs:decimal" />
              <xs:element name="RegisterKey" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnRegisterKey" msprop:Generator_ColumnPropNameInRow="RegisterKey" msprop:Generator_ColumnPropNameInTable="RegisterKeyColumn" msprop:Generator_UserColumnName="RegisterKey" type="xs:string" />
              <xs:element name="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" msprop:Generator_UserColumnName="Description" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Status" msprop:Generator_ColumnVarNameInTable="columnStatus" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_ColumnPropNameInTable="StatusColumn" msprop:Generator_UserColumnName="Status" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PostedDate" msprop:Generator_ColumnVarNameInTable="columnPostedDate" msprop:Generator_ColumnPropNameInRow="PostedDate" msprop:Generator_ColumnPropNameInTable="PostedDateColumn" msprop:Generator_UserColumnName="PostedDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="EmailSentTo" msprop:Generator_ColumnVarNameInTable="columnEmailSentTo" msprop:Generator_ColumnPropNameInRow="EmailSentTo" msprop:Generator_ColumnPropNameInTable="EmailSentToColumn" msprop:Generator_UserColumnName="EmailSentTo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailSenton" msprop:Generator_ColumnVarNameInTable="columnEmailSenton" msprop:Generator_ColumnPropNameInRow="EmailSenton" msprop:Generator_ColumnPropNameInTable="EmailSentonColumn" msprop:Generator_UserColumnName="EmailSenton" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Amount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" minOccurs="0" />
              <xs:element name="Comments" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="Comments" msprop:Generator_ColumnVarNameInTable="columnComments" msprop:Generator_ColumnPropNameInTable="CommentsColumn" msprop:Generator_UserColumnName="Comments" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FollowUpOn" msprop:Generator_ColumnVarNameInTable="columnFollowUpOn" msprop:Generator_ColumnPropNameInRow="FollowUpOn" msprop:Generator_ColumnPropNameInTable="FollowUpOnColumn" msprop:Generator_UserColumnName="FollowUpOn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="RiskStatus" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="RiskStatus" msprop:Generator_ColumnVarNameInTable="columnRiskStatus" msprop:Generator_ColumnPropNameInTable="RiskStatusColumn" msprop:Generator_UserColumnName="RiskStatus" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CoList" msprop:Generator_TableClassName="CoListDataTable" msprop:Generator_TableVarName="tableCoList" msprop:Generator_TablePropName="CoList" msprop:Generator_RowDeletingName="CoListRowDeleting" msprop:Generator_RowChangingName="CoListRowChanging" msprop:Generator_RowEvHandlerName="CoListRowChangeEventHandler" msprop:Generator_RowDeletedName="CoListRowDeleted" msprop:Generator_UserTableName="CoList" msprop:Generator_RowChangedName="CoListRowChanged" msprop:Generator_RowEvArgName="CoListRowChangeEvent" msprop:Generator_RowClassName="CoListRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="DDLimit" msprop:Generator_ColumnVarNameInTable="columnDDLimit" msprop:Generator_ColumnPropNameInRow="DDLimit" msprop:Generator_ColumnPropNameInTable="DDLimitColumn" msprop:Generator_UserColumnName="DDLimit" type="xs:decimal" minOccurs="0" />
              <xs:element name="TaxLimit" msprop:Generator_ColumnVarNameInTable="columnTaxLimit" msprop:Generator_ColumnPropNameInRow="TaxLimit" msprop:Generator_ColumnPropNameInTable="TaxLimitColumn" msprop:Generator_UserColumnName="TaxLimit" type="xs:decimal" minOccurs="0" />
              <xs:element name="CONUM" msprop:Generator_ColumnVarNameInTable="columnCONUM" msprop:Generator_ColumnPropNameInRow="CONUM" msprop:Generator_ColumnPropNameInTable="CONUMColumn" msprop:Generator_UserColumnName="CONUM" type="xs:decimal" />
              <xs:element name="CO_NAME" msprop:Generator_ColumnVarNameInTable="columnCO_NAME" msprop:Generator_ColumnPropNameInRow="CO_NAME" msprop:Generator_ColumnPropNameInTable="CO_NAMEColumn" msprop:Generator_UserColumnName="CO_NAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CO_STATUS" msprop:Generator_ColumnVarNameInTable="columnCO_STATUS" msprop:Generator_ColumnPropNameInRow="CO_STATUS" msprop:Generator_ColumnPropNameInTable="CO_STATUSColumn" msprop:Generator_UserColumnName="CO_STATUS" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DDAllowed" msprop:Generator_ColumnVarNameInTable="columnDDAllowed" msprop:Generator_ColumnPropNameInRow="DDAllowed" msprop:Generator_ColumnPropNameInTable="DDAllowedColumn" msprop:Generator_UserColumnName="DDAllowed" type="xs:boolean" minOccurs="0" />
              <xs:element name="TestTaxPct" msprop:Generator_ColumnVarNameInTable="columnTestTaxPct" msprop:Generator_ColumnPropNameInRow="TestTaxPct" msprop:Generator_ColumnPropNameInTable="TestTaxPctColumn" msprop:Generator_UserColumnName="TestTaxPct" type="xs:int" minOccurs="0" />
              <xs:element name="TestPayrollPct" msprop:Generator_ColumnVarNameInTable="columnTestPayrollPct" msprop:Generator_ColumnPropNameInRow="TestPayrollPct" msprop:Generator_ColumnPropNameInTable="TestPayrollPctColumn" msprop:Generator_UserColumnName="TestPayrollPct" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DDPyrollHistory" msprop:Generator_TableClassName="DDPyrollHistoryDataTable" msprop:Generator_TableVarName="tableDDPyrollHistory" msprop:Generator_TablePropName="DDPyrollHistory" msprop:Generator_RowDeletingName="DDPyrollHistoryRowDeleting" msprop:Generator_RowChangingName="DDPyrollHistoryRowChanging" msprop:Generator_RowEvHandlerName="DDPyrollHistoryRowChangeEventHandler" msprop:Generator_RowDeletedName="DDPyrollHistoryRowDeleted" msprop:Generator_UserTableName="DDPyrollHistory" msprop:Generator_RowChangedName="DDPyrollHistoryRowChanged" msprop:Generator_RowEvArgName="DDPyrollHistoryRowChangeEvent" msprop:Generator_RowClassName="DDPyrollHistoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CHECK_DATE" msprop:Generator_ColumnVarNameInTable="columnCHECK_DATE" msprop:Generator_ColumnPropNameInRow="CHECK_DATE" msprop:Generator_ColumnPropNameInTable="CHECK_DATEColumn" msprop:Generator_UserColumnName="CHECK_DATE" type="xs:dateTime" minOccurs="0" />
              <xs:element name="PAYROLL_NUM" msprop:Generator_ColumnVarNameInTable="columnPAYROLL_NUM" msprop:Generator_ColumnPropNameInRow="PAYROLL_NUM" msprop:Generator_ColumnPropNameInTable="PAYROLL_NUMColumn" msprop:Generator_UserColumnName="PAYROLL_NUM" type="xs:decimal" />
              <xs:element name="AMOUNT" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnAMOUNT" msprop:Generator_ColumnPropNameInRow="AMOUNT" msprop:Generator_ColumnPropNameInTable="AMOUNTColumn" msprop:Generator_UserColumnName="AMOUNT" type="xs:decimal" minOccurs="0" />
              <xs:element name="TAXES" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnTAXES" msprop:Generator_ColumnPropNameInRow="TAXES" msprop:Generator_ColumnPropNameInTable="TAXESColumn" msprop:Generator_UserColumnName="TAXES" type="xs:decimal" minOccurs="0" />
              <xs:element name="TAX941" msprop:Generator_ColumnVarNameInTable="columnTAX941" msprop:Generator_ColumnPropNameInRow="TAX941" msprop:Generator_ColumnPropNameInTable="TAX941Column" msprop:Generator_UserColumnName="TAX941" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="prc_UpdateCoMonitor" msprop:Generator_TableClassName="prc_UpdateCoMonitorDataTable" msprop:Generator_TableVarName="tableprc_UpdateCoMonitor" msprop:Generator_TablePropName="prc_UpdateCoMonitor" msprop:Generator_RowDeletingName="prc_UpdateCoMonitorRowDeleting" msprop:Generator_RowChangingName="prc_UpdateCoMonitorRowChanging" msprop:Generator_RowEvHandlerName="prc_UpdateCoMonitorRowChangeEventHandler" msprop:Generator_RowDeletedName="prc_UpdateCoMonitorRowDeleted" msprop:Generator_UserTableName="prc_UpdateCoMonitor" msprop:Generator_RowChangedName="prc_UpdateCoMonitorRowChanged" msprop:Generator_RowEvArgName="prc_UpdateCoMonitorRowChangeEvent" msprop:Generator_RowClassName="prc_UpdateCoMonitorRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CONUM" msprop:Generator_ColumnVarNameInTable="columnCONUM" msprop:Generator_ColumnPropNameInRow="CONUM" msprop:Generator_ColumnPropNameInTable="CONUMColumn" msprop:Generator_UserColumnName="CONUM" type="xs:decimal" />
              <xs:element name="CO_NAME" msprop:Generator_ColumnVarNameInTable="columnCO_NAME" msprop:Generator_ColumnPropNameInRow="CO_NAME" msprop:Generator_ColumnPropNameInTable="CO_NAMEColumn" msprop:Generator_UserColumnName="CO_NAME" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DDLimit" msprop:Generator_ColumnVarNameInTable="columnDDLimit" msprop:Generator_ColumnPropNameInRow="DDLimit" msprop:Generator_ColumnPropNameInTable="DDLimitColumn" msprop:Generator_UserColumnName="DDLimit" type="xs:decimal" minOccurs="0" />
              <xs:element name="TaxLimit" msprop:Generator_ColumnVarNameInTable="columnTaxLimit" msprop:Generator_ColumnPropNameInRow="TaxLimit" msprop:Generator_ColumnPropNameInTable="TaxLimitColumn" msprop:Generator_UserColumnName="TaxLimit" type="xs:decimal" minOccurs="0" />
              <xs:element name="CO_STATUS" msprop:Generator_ColumnVarNameInTable="columnCO_STATUS" msprop:Generator_ColumnPropNameInRow="CO_STATUS" msprop:Generator_ColumnPropNameInTable="CO_STATUSColumn" msprop:Generator_UserColumnName="CO_STATUS" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DDAllowed" msprop:Generator_ColumnVarNameInTable="columnDDAllowed" msprop:Generator_ColumnPropNameInRow="DDAllowed" msprop:Generator_ColumnPropNameInTable="DDAllowedColumn" msprop:Generator_UserColumnName="DDAllowed" type="xs:boolean" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:prc_RptDelivery" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="DeliveryTicketsSum_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:DeliveryTicketsSum" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="CALENDAR_RULES_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CALENDAR_RULES" />
      <xs:field xpath="mstns:conum" />
      <xs:field xpath="mstns:period_id" />
    </xs:unique>
    <xs:unique name="COMPANY_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:COMPANY" />
      <xs:field xpath="mstns:CONUM" />
    </xs:unique>
    <xs:unique name="NachaTaxDrafts_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:NachaTaxDrafts" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="CoList_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CoList" />
      <xs:field xpath="mstns:CONUM" />
    </xs:unique>
    <xs:unique name="DDPyrollHistory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:DDPyrollHistory" />
      <xs:field xpath="mstns:PAYROLL_NUM" />
    </xs:unique>
    <xs:unique name="prc_UpdateCoMonitor_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:prc_UpdateCoMonitor" />
      <xs:field xpath="mstns:CONUM" />
    </xs:unique>
  </xs:element>
</xs:schema>