﻿Imports System.Runtime.CompilerServices
Imports DevExpress.XtraEditors
Imports DevExpress.XtraLayout
Imports DevExpress.XtraSplashScreen
Imports DevExpress.XtraWaitForm

Public Module FormExtensions

    <Extension>
    Public Sub CenterToScreeen(frm As Form, screen As Screen)
        frm.Location = New Point(((screen.WorkingArea.Width - frm.Width) / 2) + screen.WorkingArea.Left, ((screen.WorkingArea.Height - frm.Height) / 2) + screen.WorkingArea.Top)
    End Sub


    <Extension>
    Public Sub ShowWaitForm(form As XtraForm)
        If SplashScreenManager.Default Is Nothing OrElse Not SplashScreenManager.Default.IsSplashFormVisible Then
            SplashScreenManager.ShowForm(form, GetType(WaitForm1), True, True)
            'SplashScreenManager.SplashFormLocation = New Point((Width - WaitForm1.Width) / 2, (Height - WaitForm1.Height) / 2)
        End If
    End Sub

    <Extension>
    Public Sub ShowDefaultWaitForm(form As XtraForm, Optional caption As String = Nothing, Optional description As String = Nothing)
        If SplashScreenManager.Default Is Nothing OrElse Not SplashScreenManager.Default.IsSplashFormVisible Then
            SplashScreenManager.ShowDefaultWaitForm(form, True, True, caption, description)
        ElseIf SplashScreenManager.Default.IsSplashFormVisible Then
            If Not String.IsNullOrEmpty(caption) Then
                SplashScreenManager.Default.SetWaitFormCaption(caption)
            End If
            If description IsNot Nothing Then
                SplashScreenManager.Default.SetWaitFormDescription(description)
            End If
        End If
    End Sub

    <Extension>
    Public Sub CloseWaitForm(form As XtraForm)
        Try
            SplashScreenManager.CloseForm(False, 100, form)
        Catch ex As Exception
            Logger.Error(ex, "Error in CloseWaitForm")
        End Try
    End Sub

    Public Property ProgressBarName = "PopupContainerControl_MarqueeProgressBarControl"

    <Extension>
    Public Sub ShowProgessPanel(lcRoot As LayoutControl)
        lcRoot.Enabled = False
        Dim controls = lcRoot.Controls.Find(ProgressBarName, True)
        If controls.Any() Then
            For Each _control In controls
                If _control.GetType() = GetType(PopupContainerControl) Then
                    CType(_control, PopupContainerControl).Show()
                End If
            Next
        Else
            Dim popUp = New PopupContainerControl With {.Height = 66, .Width = 200, .Visible = False, .Name = ProgressBarName}
            Dim progressBar = New ProgressPanel With {.Dock = DockStyle.Fill}
            popUp.Controls.Add(progressBar)
            lcRoot.Controls.Add(popUp)
            AddHandler popUp.VisibleChanged, AddressOf VisibleChange
            AddHandler lcRoot.VisibleChanged, AddressOf RootVisibleChanged
            AddHandler lcRoot.SizeChanged, AddressOf RootVisibleChanged
            AddHandler lcRoot.LocationChanged, AddressOf RootVisibleChanged

            AddHandler lcRoot.Disposed, Sub()
                                            RemoveHandler popUp.VisibleChanged, AddressOf VisibleChange
                                            RemoveHandler lcRoot.VisibleChanged, AddressOf RootVisibleChanged
                                            RemoveHandler lcRoot.SizeChanged, AddressOf RootVisibleChanged
                                            RemoveHandler lcRoot.LocationChanged, AddressOf RootVisibleChanged
                                        End Sub
            popUp.Show()
            VisibleChange(popUp, Nothing)
        End If
    End Sub

    <Extension>
    Public Sub AutoCenter(Popup As PopupContainerControl)
        Popup.Name = ProgressBarName
        AddHandler Popup.VisibleChanged, AddressOf VisibleChange
        Dim parent = Popup.Parent
        AddHandler parent.VisibleChanged, AddressOf RootVisibleChanged
        AddHandler parent.SizeChanged, AddressOf RootVisibleChanged
        AddHandler parent.LocationChanged, AddressOf RootVisibleChanged
    End Sub

    <Extension>
    Public Sub HideProgressPanel(lcRoot As LayoutControl)
        Dim controls = lcRoot.Controls.Find(ProgressBarName, True)
        If lcRoot.IsDisposed Then Exit Sub
        lcRoot.Enabled = True
        If controls.Any() Then
            For Each _control In controls
                If _control.GetType() = GetType(PopupContainerControl) Then
                    CType(_control, PopupContainerControl).Hide()
                End If
            Next
        End If
    End Sub

    <Extension>
    Public Function ProgressPanelVisible(lcRoot As LayoutControl) As Boolean
        Dim controls = lcRoot.Controls.Find(ProgressBarName, True)
        If lcRoot.IsDisposed Then Exit Function
        If controls.Any() Then
            For Each _control In controls
                If _control.GetType() = GetType(PopupContainerControl) Then
                    Return CType(_control, PopupContainerControl).Visible
                End If
            Next
        End If
        Return False
    End Function

    <Extension>
    Public Sub CenterTo(control As Control, parentControl As Control)
        control.Location = New Point((parentControl.Width - control.Width) / 2, (parentControl.Height - control.Height) / 2)
    End Sub

    Private Sub VisibleChange(sender As Object, e As EventArgs)
        Dim popUp = CType(sender, PopupContainerControl)
        Dim parentControl = popUp.Parent
        If popUp.Visible Then
            popUp.Location = New Point((parentControl.Width - popUp.Width) / 2, (parentControl.Height - popUp.Height) / 2)
            popUp.BringToFront()
        End If
    End Sub

    Private Sub RootVisibleChanged(sender As Object, e As EventArgs)
        'find popup form and adjust to center
        Dim parentControl As Control = sender
        Dim controls = parentControl.Controls.Find(ProgressBarName, True)
        If controls.Any() Then
            For Each _control In controls
                If _control.GetType() = GetType(PopupContainerControl) Then
                    VisibleChange(_control, Nothing)
                End If
            Next
        End If
    End Sub

End Module
