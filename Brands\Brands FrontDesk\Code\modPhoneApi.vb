﻿Imports System.Collections.ObjectModel
Imports Brands.Core
Imports DevExpress.XtraBars.Alerter
Imports PubnubApi

Module modPhoneApi

    Private Const apiUserName As String = "brandspaycheck"
    Private Const apiPass As String = "7183876401"
    Public ReadOnly pubNubGuid As Guid = Guid.NewGuid()
    Public _ConnectedChannels As ObservableCollection(Of ConnectedChannels) = New ObservableCollection(Of ConnectedChannels)
    Public _PhoneCalls As ObservableCollection(Of PhoneCall) = New ObservableCollection(Of PhoneCall)
    Private LastDailedCallId As String
    Private _MaxDelayForPhonePopUp As Integer = 10
    Private _PhoneLogger As Serilog.ILogger
    Private _CoListCache As List(Of view_CompanySumarry)
    Private _TelebroadApi As TelebroadApi
    Public Property _PushLogIn As TelebroadApi.RootObject(Of TelebroadApi.PushLoginResult)

    Sub New()
        _PhoneLogger = Logger.ForContext("SourceContext", "modPhoneApi")
    End Sub

    Public Async Function InitializePhoneSystem(showError As Boolean) As Task
        Try
            If GetUdfValue("ComputerListNotToShowPhonePopUp").ToUpper().Split(";").Contains(System.Environment.MachineName.ToUpper()) Then
                Exit Function
            End If
            DisconnectAllChannels()
            _MaxDelayForPhonePopUp = Convert.ToInt32(modGlobals.GetUdfValue("MaxDelayForPhonePopUp"))
            _TelebroadApi = New TelebroadApi(GetUserName(), GetUserPassword(), Logger)
            Await PushLogin(showError)

            If _PushLogIn Is Nothing Then Exit Function
            If Not _PushLogIn.Result?.channels.Any Then
                _PhoneLogger.Debug("User: {Username} isn't setup with any lines.", GetUserName())
            Else
                For Each l In _PushLogIn.Result.channels
                    SubscribeToLine(l)
                Next
                Using _db As New dbEPDataDataContext(GetConnectionString)
                    _CoListCache = _db.view_CompanySumarries.ToList()
                End Using
            End If
        Catch ex As Exception
            MainForm.ShowNotification("Error connecting to phone system", "There was error while connecting to the phone system, Please notify tech support", My.Resources.Warning_Small)
            _PhoneLogger.Error(ex, "Error initializing push login for phone.")
        End Try
    End Function

    Public Async Function PushLogin(showError As Boolean) As Task
        Try
            If Not Permissions?.Phonesnumber.HasValue OrElse Permissions?.Phonesnumber = 0 Then Exit Function
            Dim db = New dbEPDataDataContext(GetConnectionString)
            _PushLogIn = Await _TelebroadApi.PushLoginAsync(pubNubGuid)
            Logger.Debug("Push Login {@PushLogin}", _PushLogIn)
        Catch ex As Exception
            MainForm.ShowNotification("Error connecting to phone system", "There was error while connecting to the phone system, Please notify tech support", My.Resources.Warning_Small)
            If showError Then
                DisplayErrorMessage("Error getting pubnub PushLogin info from TelebroadApi.", ex)
            End If
            _PhoneLogger.Error(ex, "Error getting pubnub PushLogin info from TelebroadApi.")
        End Try
    End Function

    Public Async Function SendCall(destPhone As String, Optional showMsgBox As Boolean = False) As Task
        Dim validPhoneNum As String = Nothing
        Try
            If Not Permissions?.Phonesnumber.HasValue OrElse Permissions?.Phonesnumber.ToString.Length <> 7 Then
                Throw New Exception("User Profile {0} is not set up with the phone system, please notify Tech Support.".FormatWith(UserName))
            End If
            validPhoneNum = FormatPhoneNumber(destPhone)
            Dim sendCall1 = New TelebroadApi.SendCall With {.snumber = Permissions.Phonesnumber,
                .dnumber = GetPhoneWithoutExt(validPhoneNum),
                .callerids = "17186251800",
                .calleridd = "17186251800",
                .answer1 = 1}
            Dim r = Await _TelebroadApi.SendCallAsync(sendCall1)
            LastDailedCallId = r.Result.callid
            Dim existingCall = _PhoneCalls.FirstOrDefault(Function(p) p.callid = LastDailedCallId)
            If existingCall IsNot Nothing AndAlso existingCall.alertForm IsNot Nothing Then
                existingCall.alertForm.Close()
            End If
            _PhoneLogger.Debug("successfully called {PhoneNumber} - {@Results}", validPhoneNum, r)
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error placing call {destPhone} ValidPhoneNum: {ValidPhoneNum} snumber: {snumber}", destPhone, validPhoneNum, Permissions?.Phonesnumber)
            If showMsgBox Then
                DisplayMessageBox(ex.Message)
            Else
                Throw
            End If
        End Try
    End Function

    Public Async Function SendFaxAsync(destPhone As String, path As String, Optional showMsgBox As Boolean = False) As Task(Of Boolean)
        Dim validPhoneNum As String = Nothing
        Try
            CheckPermission()
            validPhoneNum = FormatPhoneNumber(destPhone)
            Dim bytes As Byte() = System.IO.File.ReadAllBytes(path)
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim user = db.DBUSERs.SingleOrDefault(Function(u) u.name.ToLower = UserName.ToLower)
            Await _TelebroadApi.SendFaxAsync(New TelebroadApi.SendFax With {.cnumber = validPhoneNum, .snumber = "17186251800", .email = user.email, .data = Convert.ToBase64String(bytes), .file_extension = System.IO.Path.GetExtension(path)})
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error sending fax {destPhone} ValidPhoneNum: {ValidPhoneNum} snumber: {snumber}", destPhone, validPhoneNum, Permissions?.Phonesnumber)
            If showMsgBox Then
                DisplayMessageBox(ex.Message)
                Return False
            Else
                Throw
            End If
        End Try
        Return True
    End Function

    'Public Sub SendFax(destPhone As String, path As String, Optional showMsgBox As Boolean = False)
    '    Dim validPhoneNum As String = Nothing
    '    Try
    '        CheckPermission()
    '        validPhoneNum = FormatPhoneNumber(destPhone)
    '        Dim bytes As Byte() = System.IO.File.ReadAllBytes(path)
    '        Dim db = New dbEPDataDataContext(GetConnectionString)
    '        Dim user = db.DBUSERs.SingleOrDefault(Function(u) u.name.ToLower = UserName.ToLower)
    '        Dim r = telebroadService.do_send_fax(apiUserName, apiPass, "17186251800", validPhoneNum, user.email, "", Convert.ToBase64String(bytes), System.IO.Path.GetExtension(path))
    '    Catch ex As Exception
    '        _PhoneLogger.Error(ex, "Error sending fax {destPhone} ValidPhoneNum: {ValidPhoneNum} snumber: {snumber}", destPhone, validPhoneNum, Permissions.Phonesnumber)
    '        If showMsgBox Then
    '            DisplayMessageBox(ex.Message)
    '        Else
    '            Throw
    '        End If
    '    End Try
    'End Sub

    Private Sub CheckPermission()
        If Not Permissions?.Phonesnumber.HasValue OrElse Permissions?.Phonesnumber.ToString.Length <> 7 Then
            Throw New Exception("User Profile {0} is not set up with the phone system, please notify hershy.".FormatWith(UserName))
        End If
    End Sub

    Public Sub PickUpCall(callId As String)
        Try
            _TelebroadApi.PickupCallAsync(Permissions?.Phonesnumber, callId)
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error hanging up call {callId}", callId)
            Throw
        End Try
    End Sub

    'Public Async Function Getpeople() As Task(Of people_item())
    '    Dim intercapter = New InspectorBehavior()
    '    Try
    '        Dim telebroadService = New TelebroadWebServicesPortTypeClient
    '        telebroadService.Endpoint.EndpointBehaviors.Add(intercapter)
    '        Return Await telebroadService.get_peopleAsync(apiUserName, apiPass)
    '    Catch ex As Exception
    '        _PhoneLogger.Error(ex, "Error gettting Getpeople")
    '        Throw
    '    End Try
    'End Function

    'Public Async Function RedirectCall(activeCall As PhoneCall, people As people_item) As Task
    '    Dim intercapter = New InspectorBehavior()
    '    Try
    '        Dim telebroadService = New TelebroadWebServicesPortTypeClient
    '        telebroadService.Endpoint.EndpointBehaviors.Add(intercapter)
    '        Await telebroadService.redirect_callAsync(apiUserName, apiPass, activeCall.callid, people.extension)
    '    Catch ex As Exception
    '        _PhoneLogger.Error(ex, "Error gettting Getpeople")
    '        Throw
    '    End Try
    'End Function

    Private Function FormatPhoneNumber(number As String) As String
        If number.IsNullOrWhiteSpace Then
            Throw New Exception("Please enter a Phone #.")
        End If
        number = number.Trim
        number = number.Replace("-", "")
        number = number.Replace("(", "").Replace(")", "").Replace(" ", "").Replace(".", "")
        If Not number.StartsWith("1") Then
            number = String.Format("1{0}", number)
        End If
        Return number
    End Function

    Private Function GetPhoneWithoutExt(num As String)
        If num.Contains("W") Then
            num = num.Split("W")(0)
        End If
        If num.Contains("w") Then
            num = num.Split("w")(0)
        End If
        If num.Contains("X") Then
            num = num.Split("X")(0)
        End If
        Return num
    End Function

    Public Sub SubscribeToLine(line As TelebroadApi.Channel)
        Try
            Dim cc = _ConnectedChannels.FirstOrDefault(Function(c) c.Channel = line.channel)
            If cc Is Nothing Then
                cc = New ConnectedChannels With {.Channel = line.channel}
                _ConnectedChannels.Add(cc)
            End If

            'Dim config = New PNConfiguration() With {
            '    .SubscribeKey = _PushLogIn.Result.subcribe_key,
            '    .AuthKey = _PushLogIn.Result.auth,
            '    .Uuid = pubNubGuid.ToString()
            '}

            Dim config As New PNConfiguration(New UserId(pubNubGuid.ToString())) With {
                .SubscribeKey = _PushLogIn.Result.subcribe_key,
                .AuthKey = _PushLogIn.Result.auth
            }

            Dim pubNub = New Pubnub(config)
            Dim listenerSubscribeCallack = New SubscribeCallbackExt(AddressOf userCallback, AddressOf connectCallback, AddressOf errorCallback)
            pubNub.AddListener(listenerSubscribeCallack)
            pubNub.Subscribe(Of String)().Channels(New String() {line.channel}).Execute()
            Logger.Debug("Successfully subscribed to line. Line Channel: {LineChannel}", line.channel)
            'Await Task.Run(Sub() config.Subscribe(line.channel, Sub(obj) userCallback(obj), Sub(obj) connectCallback(obj), Sub(obj) errorCallback(obj))).ConfigureAwait(False)
            cc.PubNub = pubNub
            cc.SubscribeCallback = listenerSubscribeCallack
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error in SuscribeToLine function. line: {@line_item}", line)
            Throw
        End Try
    End Sub

    'Public Async Function GetActiveCalls() As Task(Of active_calls_panel_item())
    '    Dim telebroadService = New TelebroadApi.TelebroadWebServicesPortTypeClient
    '    Dim r As active_calls_panel_item() = Await telebroadService.get_active_calls_panelAsync(GetUserName(), GetUserPassword)
    '    Return r
    'End Function

    Private Property UserEmail As String

    Public Function GetUserName(Optional tempUsername As String = Nothing) As String
        If tempUsername IsNot Nothing Then
            If tempUsername.IsNotNullOrWhiteSpace Then Return tempUsername
        End If

        If Permissions?.PhoneUserName.IsNotNullOrWhiteSpace AndAlso tempUsername Is Nothing Then
            Return Permissions.PhoneUserName
        End If

        If UserEmail.IsNotNullOrWhiteSpace Then
            Return UserEmail
        End If

        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim user As DBUSER
        user = (From u In db.DBUSERs Where u.name.ToLower = UserName).SingleOrDefault
        Return user.email
    End Function

    Public Function GetUserPassword(Optional tempPassword As String = Nothing) As String
        If tempPassword IsNot Nothing Then
            If tempPassword.IsNotNullOrWhiteSpace Then Return tempPassword
            Return "7183876401"
        End If

        If Permissions?.PhonePassword.IsNotNullOrWhiteSpace Then
            Return Permissions.PhonePassword
        Else
            Return "7183876401"
        End If
    End Function

    Private Sub DisconnectAllChannels()
        For Each c In _ConnectedChannels
            DisconnectChannels(c)
        Next
    End Sub

    Public Sub DisconnectChannels(channel As ConnectedChannels)
        _PhoneLogger.Information("Disconnecting {@Channel}", channel)
        channel.PubNub.RemoveListener(channel.SubscribeCallback)
    End Sub

    Private Sub userCallback(pubNub As Pubnub, obj As PNMessageResult(Of Object))
        Try
            Dim phone1 = Newtonsoft.Json.JsonConvert.DeserializeObject(Of PhoneCall)(obj.Message)

            If Not MainForm.TryInvoke(Sub() PushPhoneCall(phone1)) Then
                _PhoneLogger.Debug("not showing incoming call pop up {@PhoneCall}", phone1)
            End If
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error in userCallback {@obj}", obj)
        End Try
    End Sub

    Public Function UnixTimeToDateTime(unixTime As Integer) As DateTime
        Dim dt = New DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
        dt = dt.AddSeconds(unixTime).ToLocalTime()
        Return dt
    End Function

    Public Function DateTimeToUnixTime(dt As DateTime) As Double
        Return (TimeZoneInfo.ConvertTimeToUtc(dt) - New DateTime(1970, 1, 1, 0, 0, 0, System.DateTimeKind.Utc)).TotalSeconds
    End Function

    Private Sub connectCallback(pubnub As Pubnub, obj As Object)
        Try
            Dim list = CType(obj, List(Of Object))
            Dim cc = _ConnectedChannels.FirstOrDefault(Function(c) c.Channel = list(2).ToString())
            If cc IsNot Nothing Then
                cc.Count = list(0)
                cc.Status = list(1)
            Else
                _ConnectedChannels.Add(New ConnectedChannels With {.Channel = list(2), .Count = list(0), .Status = list(1)})
            End If
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error in connectCallback {@obj}", obj)
        End Try
    End Sub

    Private Sub errorCallback(pubnum As Pubnub, obj As PNStatus)
        Try
            If Not obj.Error Then Exit Sub
            For Each channel In obj.AffectedChannels
                Dim cc = _ConnectedChannels.FirstOrDefault(Function(c) c.Channel = channel)
                If cc IsNot Nothing Then
                    cc.Message = obj.ErrorData.Information
                    cc.Status = "Error"
                Else
                    _ConnectedChannels.Add(New ConnectedChannels With {.Channel = channel, .Message = obj.ErrorData.Information})
                    _PhoneLogger.Error("Error in errorCallback {@obj}", obj)
                End If
            Next
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error in errorCallback {@obj}", obj)
        End Try
    End Sub

    Private Sub disconnectCallBack(obj As Object)
        Try
            _PhoneLogger.Information("disconnectCallBack {@obj}", obj)
            Dim list = CType(obj, List(Of Object))
            Dim cc = _ConnectedChannels.FirstOrDefault(Function(c) c.Channel = list(2).ToString())
            If cc IsNot Nothing Then
                cc.Count = list(0)
                cc.Status = list(1)
            Else
                _ConnectedChannels.Add(New ConnectedChannels With {.Channel = list(2), .Count = list(0), .Status = list(1)})
            End If
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error in disconnectCallBack {@Obj}", obj)
        End Try
    End Sub

    Private Sub PushPhoneCall(activeCall As PhoneCall)
        Try
            If activeCall.callid = LastDailedCallId Then activeCall.isOutgoingCallFromProgram = True

            Dim existingCall = _PhoneCalls.FirstOrDefault(Function(p) p.callid = activeCall.callid)
            _PhoneLogger.Debug("Entering PushPhoneCall {@ActiveCall} {@ExistingCall}", activeCall, existingCall)
            If existingCall Is Nothing Then
                _PhoneCalls.Add(activeCall)
            Else
                If activeCall.status = existingCall.status Then
                    Exit Sub
                Else
                    _PhoneCalls.Remove(existingCall)
                    activeCall.isOutgoingCallFromProgram = existingCall.isOutgoingCallFromProgram
                    activeCall.alertForm = existingCall.alertForm
                    activeCall.CoNum = existingCall.CoNum
                    _PhoneCalls.Add(activeCall)
                End If
            End If

            If activeCall.dir = "INCOMING" AndAlso activeCall.snumber = "17186251800" Then Exit Sub
            If activeCall.status = "HISTORY" OrElse activeCall.status = "COMPLETED" Then
                If activeCall.alertForm IsNot Nothing Then
                    activeCall.alertForm.Close()
                End If
                _PhoneLogger.Information("Removing {CallId} from activeCall list", activeCall.callid)
                _PhoneCalls.Remove(activeCall)
            End If

            Dim delay = DateTime.Now.Subtract(activeCall.StartTime).TotalSeconds

            If activeCall.start.HasValue AndAlso delay > _MaxDelayForPhonePopUp Then
                activeCall.alertForm?.Close()
                _PhoneLogger.Debug("PubNub call back push is delayed {delay} seconds, which is more then {MaxDelayForPhonePopUp} seconds. (not showing popup) phone call time is: {Time:G} {@PhoneCall}", delay, _MaxDelayForPhonePopUp, activeCall.StartTime, activeCall)
                Exit Sub
            End If
            If activeCall.status = "RINGING" AndAlso Not activeCall.isOutgoingCallFromProgram AndAlso activeCall.type = "CALL" AndAlso activeCall.dir = "INCOMING" Then
                Dim al = New DevExpress.XtraBars.Alerter.AlertControl With {.AllowHotTrack = True, .FormLocation = DevExpress.XtraBars.Alerter.AlertFormLocation.BottomRight, .FormShowingEffect = AlertFormShowingEffect.MoveVertical,
                                .AutoFormDelay = TimeSpan.FromMinutes(1).TotalMilliseconds, .AutoHeight = True}

                AddHandler al.BeforeFormShow, Sub(s, ev)
                                                  activeCall.alertForm = ev.AlertForm
                                              End Sub

                AddHandler al.AlertClick, Sub()
                                              If activeCall.alertForm IsNot Nothing Then activeCall.alertForm.Close()
                                              Try
                                                  PickUpCall(activeCall.callid)
                                              Catch ex As Exception
                                                  DisplayErrorMessage("Error picking up phone call: " & activeCall.snumber, ex)
                                              End Try
                                          End Sub
                al.AllowHtmlText = True
                Dim text = $"<b>Name:</b> {activeCall.sname}"

                If _CoListCache IsNot Nothing Then
                    Dim r = _CoListCache.Where(Function(c) c.CO_PHONE = activeCall.snumber.RemoveFromStart(1) OrElse c.CO_MODEM = activeCall.snumber.RemoveFromStart(1))
                    If r.Count <> 1 Then
                        r = _CoListCache.Where(Function(c) c.CO_NAME.StartsWith(activeCall.sname.Replace("Operator", "").Replace("Payroll--", "")))
                    End If
                    If r.Count = 1 Then
                        activeCall.CoNum = r.First.CONUM
                        text &= $"<br><br><b>Co: </b>{r.First.CONUM} - {r.First.CO_NAME}"
                        Using _db = New dbEPDataDataContext(GetConnectionString)
                            Dim lastCallHistory = (From l In _db.CompanyActivityLogs Where l.CoNum = r.First.CONUM AndAlso l.CallId IsNot Nothing Order By l.OpenDate Descending).FirstOrDefault
                            If lastCallHistory IsNot Nothing Then
                                text &= $"<br><b>Last Call:</b> {lastCallHistory.OpenDate:g}</br><b>By: </b>{lastCallHistory.UserId}"
                            End If
                        End Using
                    End If
                End If

                al.Show(MainForm, $"Incoming Call - {activeCall.snumber}", text, My.Resources.Phone_Gif)
                Beep()
            ElseIf activeCall.status = "ANSWERED" AndAlso Not activeCall.isOutgoingCallFromProgram Then
                If activeCall.alertForm IsNot Nothing Then
                    activeCall.alertForm.Close()
                End If
                MainForm.SearchComp(activeCall.snumber, activeCall.callid)
            End If

            If activeCall.alertForm IsNot Nothing AndAlso activeCall.isOutgoingCallFromProgram Then
                activeCall.alertForm.Close()
            End If
        Catch ex As Exception
            _PhoneLogger.Error(ex, "Error in PushPhoneCall {@PhoneCall}", activeCall)
        End Try
    End Sub

End Module

Public Class PhoneCall
    Public Property start As Integer?
    Public Property callid As String
    Public Property status As String
    Public Property dir As String
    Public Property type As String
    Public Property sname As String
    Public Property snumber As String
    Public Property dnumber As String
    Public Property CoNum As Decimal?

    Public ReadOnly Property StartTime As DateTime?
        Get
            Return If(start.HasValue, modPhoneApi.UnixTimeToDateTime(start), Nothing)
        End Get
    End Property


    Private _alertForm As Form
    Public Property alertForm() As Form
        Get
            If _alertForm Is Nothing OrElse _alertForm.IsDisposed Then
                Return Nothing
            End If
            Return _alertForm
        End Get
        Set(ByVal value As Form)
            _alertForm = value
        End Set
    End Property

    Public Property isOutgoingCallFromProgram As Boolean


    Public Overrides Function GetHashCode() As Integer
        If callid Is Nothing Then
            Return (New Object).GetHashCode()
        End If
        Return callid.GetHashCode()
    End Function

    Public Overrides Function Equals(obj As Object) As Boolean
        Return CType(obj, PhoneCall).callid = callid
    End Function
End Class

Public Class ConnectedChannels
    Public Property Count As Long
    Public Property Status As String
    Public Property Channel As String
    Public Property Message As String
    Public Property PubNub As Pubnub
    Public Property SubscribeCallback As SubscribeCallbackExt
End Class