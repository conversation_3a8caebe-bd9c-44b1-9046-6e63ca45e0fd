﻿Imports System.Data
Imports DevExpress.XtraEditors

Public Class frmReceiveInventory

    Private _coNum As Decimal?
    Private db As dbEPDataDataContext
    Private _order As SuppliesOrder
    Private isOpenFromPrintingScans As Boolean = False

    Sub New()
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
    End Sub

    Sub New(_db As dbEPDataDataContext, CoNum As Decimal, order As SuppliesOrder)
        InitializeComponent()
        _order = order
        teShipClockOrderNumber.Text = _order.ID
        teShipClockOrderNumber.Properties.ReadOnly = True
        _coNum = CoNum
        db = _db
        slueCoNum.EditValue = CoNum
        slueCoNum.Properties.ReadOnly = True
        TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        TabbedControlGroup1.SelectedTabPageIndex = 1
        isOpenFromPrintingScans = True
    End Sub

    Private Sub LoadInventory()
        Dim sql = GetUdfValue("ClockInventory")
        Dim dv As New DataView(Query(sql))

        If ckHasStock.Checked Then
            dv.RowFilter = "AvailStock > 0"
        End If

        GCInventory.DataSource = dv

        Try
            If _order IsNot Nothing Then
                GCSuggestion.DataSource = Query($"exec custom.rpt_ClockSuggestionForOrder {_order.ID}")
            End If
        Catch ex As Exception
            DisplayErrorMessage("error loading clock suggestion", ex)
        End Try

        Dim col As DevExpress.XtraGrid.Columns.GridColumn
        For Each col In GVInventory.VisibleColumns
            If col.FieldName <> "MinStock" AndAlso col.FieldName <> "OnOrder" Then
                col.OptionsColumn.ReadOnly = True
            End If
        Next
    End Sub

    Private Sub frmReceiveInventory_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            'db = New dbEPDataDataContext(GetConnectionString)
            bsCompanyList.DataSource = db.view_CompanySumarries.ToList()
            Dim list = db.Supplies.Where(Function(s) s.IsClock = True).Select(Function(c) New With {.ProductName = c.ProductName, .ProductID = c.ProductID})
            bsProductList.DataSource = list
            riLueProducts.DataSource = list
            riLueProducts.DisplayMember = "ProductName"
            riLueProducts.ValueMember = "ProductID"
            colProductId1.ColumnEdit = riLueProducts
            LoadShipClock()
            bsReceiveInventory.DataSource = (From a In db.SuppliesInventories Where 1 = 2).ToList()
            LoadInventory()
        Catch ex As Exception
            DisplayErrorMessage("Error loading form", ex)
        End Try
    End Sub

    Private Sub LoadShipClock()
        If _coNum.HasValue Then
            Dim list As IEnumerable(Of SuppliesInventory)
            list = (From s In db.SuppliesInventories Where s.CoNum = _coNum)
            If _order IsNot Nothing Then
                list = (From l In list Where l.OrderId = _order.ID)
            End If
            bsShippingView.DataSource = list.ToList()
        Else
            bsShippingView.DataSource = New List(Of SuppliesInventory)
        End If
    End Sub

    Private Sub bsgcReceiveInventory_AddingNew(sender As Object, e As System.ComponentModel.AddingNewEventArgs) Handles bsReceiveInventory.AddingNew
        Dim newRow = New SuppliesInventory With {.CoNum = Nothing, .ProductId = lueProduct.EditValue}
        e.NewObject = newRow
        'db.SuppliesInventories.InsertOnSubmit(newRow)
    End Sub

    Private Sub gvReceiveInventory_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles gvReceiveInventory.ValidateRow
        Dim clock As SuppliesInventory = e.Row
        Dim list As IEnumerable(Of SuppliesInventory) = db.GetChangeSet.Inserts.Cast(Of SuppliesInventory)
        If clock.SerialNumber.IsNullOrWhiteSpace Then
            e.Valid = False
            e.ErrorText = "Serial number cannot be empty."
        ElseIf db.SuppliesInventories.Any(Function(c) c.SerialNumber = clock.SerialNumber) OrElse list.Count(Function(c) c.SerialNumber = clock.SerialNumber) > 1 Then
            e.Valid = False
            e.ErrorText = "This serial number already exist in the system."
        Else
            e.Valid = True
        End If
    End Sub

    Private Sub lueProduct_EditValueChanged(sender As Object, e As EventArgs) Handles lueProduct.EditValueChanged
        If lueProduct.EditValue IsNot Nothing Then
            gcReceiveInventory.Enabled = True
        End If
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        If db.SaveChanges Then
            db = New dbEPDataDataContext(GetConnectionString)
            bsReceiveInventory.DataSource = (From a In db.SuppliesInventories Where 1 = 2).ToList()
        End If
    End Sub

    Private Sub beScanClock_KeyDown(sender As Object, e As KeyEventArgs) Handles beScanClock.KeyDown
        Try
            If e.KeyCode <> Keys.Enter Then Exit Sub
            If beScanClock.Text.IsNullOrWhiteSpace Then Exit Sub
            If Not _coNum.HasValue Then
                DisplayMessageBox("Please select a Co# first")
                Exit Sub
            End If
            Dim clock As SuppliesInventory = (From c In db.SuppliesInventories Where c.SerialNumber = beScanClock.Text).FirstOrDefault
            If clock Is Nothing Then
                DisplayMessageBox($"This Clock was not found in the system.  Please report to IT if this is an issue ({beScanClock.EditValue})")
                Exit Sub
            Else
                If clock.CoNum.HasValue Then
                    DisplayMessageBox($"This Clock was already assigned to {If(clock.CoNum = _coNum, "this company", "a different company")}.  ({beScanClock.EditValue})")
                    Exit Sub
                Else
                    If _order IsNot Nothing Then
                        Dim orderItem = _order.SuppliesOrderItems.FirstOrDefault(Function(c) c.ProductID = clock.ProductId)
                        If orderItem Is Nothing Then
                            DisplayMessageBox($"The Clock model you scanned does not match any items on the order.  If issue please report to IT ({beScanClock.EditValue})")
                            Exit Sub
                        ElseIf orderItem.Qty = TryCast(bsShippingView.DataSource, List(Of SuppliesInventory)).Where(Function(l) l.ProductId = clock.ProductId).Count Then
                            DisplayMessageBox($"The company only ordered {orderItem.Qty} from this model.")
                            Exit Sub
                        End If
                    End If
                    clock.CoNum = _coNum
                    clock.OrderId = _order?.ID
                    bsShippingView.Insert(0, clock)
                End If
            End If
            beScanClock.EditValue = String.Empty
            If isOpenFromPrintingScans AndAlso ShippingCheckIfAllScanned().IsNullOrWhiteSpace Then
                SendEmail()
                DialogResult = DialogResult.OK
            End If
        Catch ex As Exception
            DisplayErrorMessage($"Error in scan clock", ex)
        End Try
    End Sub

    Public Function ShippingCheckIfAllScanned() As String
        Dim result As String = String.Empty
        If _order Is Nothing Then Return result
        Dim list = (From i In _order.SuppliesOrderItems Where i.Supply.IsClock)
        For Each product In list
            Dim i = product.Qty - TryCast(bsShippingView.DataSource, List(Of SuppliesInventory)).Where(Function(s) s.ProductId = product.ProductID).Count
            If i > 0 Then
                result &= $"{vbCrLf}{product.Supply.ProductName} - Quantity: {i}"
            End If
        Next
        Return result
    End Function

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Dim list As IEnumerable(Of SuppliesInventory) = db.SuppliesInventories
        If slueSearchCoNum.EditValue IsNot Nothing AndAlso slueSearchCoNum.Text.IsNotNullOrWhiteSpace Then
            list = (From l In list Where l.CoNum = DirectCast(slueSearchCoNum.EditValue, Decimal))
        End If
        If lueSearchModel.Text.IsNotNullOrWhiteSpace Then
            list = (From l In list Where l.ProductId = lueSearchModel.EditValue)
        End If
        If teSearchSerial.Text.IsNotNullOrWhiteSpace Then
            list = (From l In list Where l.SerialNumber.Contains(teSearchSerial.Text))
        End If
        If ckInStock.Checked Then
            list = (From l In list Where Not (l.CoNum.HasValue OrElse l.LostDate.HasValue))
        End If
        bsSearchClock.DataSource = list.ToList()
        gvSearchClock.BestFitColumns()
    End Sub

    Private Sub lueSearchModel_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles lueSearchModel.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Delete Then
            lueSearchModel.EditValue = Nothing
        End If
    End Sub

    Private Sub teShipClockOrderNumber_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles teShipClockOrderNumber.Validating
        If teShipClockOrderNumber.Text.IsNotNullOrWhiteSpace Then
            If Not _coNum.HasValue Then
                DisplayMessageBox("Please select a Co# first.")
                e.Cancel = True
            Else
                _order = db.SuppliesOrders.FirstOrDefault(Function(c) c.ID = teShipClockOrderNumber.Text AndAlso c.CoNum = _coNum)
                If _order Is Nothing Then
                    e.Cancel = True
                    DisplayMessageBox($"This Order# was not found in the system. ({teShipClockOrderNumber.EditValue})")
                End If
                Try
                    GCSuggestion.DataSource = Query($"exec custom.rpt_ClockSuggestionForOrder {_order.ID}")
                Catch ex As Exception
                    DisplayErrorMessage("Error getting suggestions", ex)
                End Try
            End If
        Else
            _order = Nothing
        End If
        LoadShipClock()
    End Sub

    Private Sub slueCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles slueCoNum.EditValueChanged
        If slueCoNum.EditValue IsNot Nothing AndAlso slueCoNum.EditValue.ToString().IsNotNullOrWhiteSpace Then
            _coNum = slueCoNum.EditValue
        Else
            _coNum = Nothing
        End If
        LoadShipClock()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Dim msg = ShippingCheckIfAllScanned()
        If isOpenFromPrintingScans Then
            If msg.IsNotNullOrWhiteSpace AndAlso XtraMessageBox.Show($"The following products are still missing, Would you like to continue without it?{vbCrLf}{msg}", "Continue?", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Exit Sub
            End If
            SendEmail()
            DialogResult = DialogResult.OK
        Else
            If _order IsNot Nothing AndAlso msg.IsNotNullOrWhiteSpace Then
                If XtraMessageBox.Show($"The following products are still missing, Would you like to continue without it?{vbCrLf}{msg}", "Continue?", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.No Then
                    Exit Sub
                End If
            End If
            If db.SaveChanges Then
                SendEmail()
                LoadShipClock()
                slueCoNum.EditValue = Nothing
                teShipClockOrderNumber.Text = String.Empty
            End If
        End If
    End Sub

    Private Sub SendEmail()
        Try
            Dim email = New EmailService()
            email.Subject = $"New Swipe Clock for Co#: {_coNum}"
            email.ToEmail.AddRange(GetUdfValue("SwipeClockEmailNotification").Split(","))
            email.Body = "Please see below for the list of serial numbers that was sent to the client."
            For Each si In TryCast(bsShippingView.DataSource, List(Of SuppliesInventory))
                email.Body &= $"{vbCrLf}{vbTab}{si.Supply.ProductName} - {si.SerialNumber}"
            Next
            Dim msg = ShippingCheckIfAllScanned()
            If msg.IsNotNullOrWhiteSpace Then
                email.Body &= $"{vbCrLf}{vbCrLf}The following products are still missing{vbCrLf}{msg}"
            End If
            email.SendEmail()
        Catch ex As Exception
            DisplayErrorMessage("Error while sending email summary", ex)
        End Try
    End Sub

    Public Shared Function CheckForClocks(barCode As String) As Boolean
        Try
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Logger.Debug("Entering CheckForClocks for {BarCode}", barCode)
            Dim split = barCode.Split("-")
            If split.Count > 1 AndAlso IsNumeric(split(1)) AndAlso split(1) > 10000 AndAlso split(1) < 100000 Then
                Dim coNum As Decimal = split(0)
                Dim orderId As Integer = split(1)
                Dim order = (From o In db.SuppliesOrders Where o.ID = orderId).SingleOrDefault
                'Dim orderItems = (From o In DB.SuppliesOrderItems Where o.OrderID = orderId)
                If order IsNot Nothing AndAlso order.SuppliesOrderItems.Any(Function(c) c.Supply.IsClock) Then
                    Dim frmInvt = New frmReceiveInventory(db, coNum, order)
                    If frmInvt.ShippingCheckIfAllScanned.IsNullOrWhiteSpace() Then
                        Logger.Debug("Not showing Manage Inventory window from BarCode: {BarCode} becuase all clocks are already scanned into the system.", barCode)
                    ElseIf frmInvt.ShowDialog() = DialogResult.OK Then
                        db.SaveChanges
                    Else
                        DisplayMessageBox("Cannot save scan. Please make sure you scan each clock.")
                        Return False
                    End If
                End If
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in printing scan while checking for clocks. {BarCode}", barCode)
            DisplayErrorMessage("Error looking up order id.", ex)
        End Try
        Return True
    End Function

    Private Sub gvSearchClock_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvSearchClock.PopupMenuShowing
        Dim row As SuppliesInventory = bsSearchClock.Current
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Return Clock To Inventory", Sub() ReturnClockToInventory(row), My.Resources.backward_16x16) With {.Enabled = row.CoNum.HasValue})
    End Sub

    Private Sub ReturnClockToInventory(row As SuppliesInventory)
        Try
            If row.Log.IsNotNullOrWhiteSpace Then row.Log &= vbCrLf
            row.Log &= $"Clock was returned from Co#: {row.CoNum} On {DateTime.Now} Recived By {UserName}"
            row.ReturnDate = DateTime.Now
            row.LostDate = Nothing
            row.SoldDate = Nothing
            row.CoNum = Nothing
            db.SubmitChanges()
        Catch ex As Exception
            Logger.Error(ex, "Error returning clock to inventory.")
            DisplayErrorMessage("Error returning clock to inventory.", ex)
        End Try
    End Sub

    Private Sub GVInventory_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GVInventory.ValidateRow
        Dim CR = GVInventory.GetRow(GVInventory.FocusedRowHandle)
        Dim SQL As String

        Try
            SQL = String.Format("UPDATE custom.Supplies SET MinStock = {1}, OnOrder = {2} WHERE ProductName = '{0}' ",
                            CR(0),
                            If(Not IsDBNull(CR(5)), CR(5).ToString(), "NULL"),
                            If(Not IsDBNull(CR(6)), CR(6).ToString(), "NULL")
                            )

            db.ExecuteCommand(SQL, New String() {})
            LoadInventory()
        Catch ex As Exception
            DisplayErrorMessage("Error validating row", ex)
        End Try
    End Sub

    Private Sub GVInventory_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GVInventory.RowUpdated
        Try
            db.SubmitChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error updating row", ex)
        End Try
    End Sub

    Private Sub ckHasStock_CheckedChanged(sender As Object, e As EventArgs) Handles ckHasStock.CheckedChanged
        Dim dv As DataView = GCInventory.DataSource

        If ckHasStock.Checked Then
            dv.RowFilter = "AvailStock > 0"
        Else
            dv.RowFilter = ""
        End If
    End Sub
End Class