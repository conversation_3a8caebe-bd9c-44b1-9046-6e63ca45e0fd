﻿Imports DevExpress.XtraEditors

Public Class frmExecupayApiManagement
    Sub New()
        InitializeComponent()
        lcgPublish.Visibility = Permissions.AllowManageExecupayAPIPath.ToBarItemVisibility
        lcgRequiredExecupayApiDllsList.Visibility = Permissions.AllowManageExecupayAPIPath.ToBarItemVisibility
    End Sub

    Private Property RequiredExecupayAPIDllList As List(Of String)
    Private Property Logger As Serilog.ILogger = modGlobals.Logger.ForContext(Of frmExecupayApiManagement)

    Private Sub frmExecupayApiManagement_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            teExecupayApiPath.Text = My.Settings.Override_AssemblyResolverPath
            ceExecupayAPIAutoFallBack.Checked = My.Settings.Override_AssemblyResolverPath_AutoFallbackIfNotFound

            Dim json = GetUdfValue("List of required Execupay API Dll’s")
            If json.IsNullOrWhiteSpace Then
                RequiredExecupayAPIDllList = New List(Of String)
            Else
                RequiredExecupayAPIDllList = Newtonsoft.Json.JsonConvert.DeserializeObject(Of List(Of String))(json)
            End If
            lbcRequiredExecupayAPIDllList.DataSource = RequiredExecupayAPIDllList

            teServerPathToPublish.Text = System.IO.Path.Combine(My.Settings.AssemblyResolverPath, ExecupayAssemblyResolver.Instance.GetServerVersionNumber + 1)
            System.IO.Directory.CreateDirectory(teServerPathToPublish.Text)
            lcServerVersionAutoInc.Text = ExecupayAssemblyResolver.Instance.GetServerVersionNumber

            Dim list = AppDomain.CurrentDomain.GetAssemblies().Where(Function(a) a.IsDynamic = False)
            GridControl1.DataSource = list
            LabelControl2.Text = LabelControl2.Text.Replace("%%doc_link%%", GetUdfValue("ExecupayApiManagement_UpdateQueueDocumentation"))
        Catch ex As Exception
            DisplayErrorMessage("Error loading data.", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        My.Settings.Override_AssemblyResolverPath = teExecupayApiPath.Text
        My.Settings.Override_AssemblyResolverPath_AutoFallbackIfNotFound = ceExecupayAPIAutoFallBack.Checked
        My.Settings.Save()
        Logger.Information("Settings Saved. Override_AssemblyResolverPath: {Override_AssemblyResolverPath} Override_AssemblyResolverPath_AutoFallbackIfNotFound: {Override_AssemblyResolverPath_AutoFallbackIfNotFound}", teExecupayApiPath.Text, ceExecupayAPIAutoFallBack.Checked)
        XtraMessageBox.Show($"Saved successfully.{vbCrLf}Please note, you need to restart Front Desk for this changes to take effect.")
    End Sub

    Private Sub btnUseLatestInstalledExecupay_Click(sender As Object, e As EventArgs) Handles btnUseLatestInstalledExecupay.Click
        Try
            Dim latestInstalledVersion = ExecupayApiManagement.GetLatestInstalledExecupayVersion()
            If latestInstalledVersion Is Nothing Then
                DisplayMessageBox("No Execupay installation was found.")
                Exit Sub
            End If
            Dim serverVersion = ExecupayApiManagement.GetLatestExecupayVersion()
            If serverVersion <> latestInstalledVersion.FormatedVersionNumber Then
                If XtraMessageBox.Show($"It looks like you do not have the latest version of Execupay intalled on your machine.{vbCrLf}Latest Server Version: {serverVersion}{vbCrLf}Latest Installed Version: {latestInstalledVersion.FormatedVersionNumber}{vbCrLf}Would you like to use your latest installed version ({latestInstalledVersion.FormatedVersionNumber})?", "Wrong Version", MessageBoxButtons.YesNo) <> DialogResult.Yes Then
                    Exit Sub
                End If
            End If
            teExecupayApiPath.EditValue = latestInstalledVersion.ExeupayPath
        Catch ex As Exception
            DisplayErrorMessage("Error getting latest execupay version.", ex)
        End Try
    End Sub


    Private Sub btnBrowsPath_Click(sender As Object, e As EventArgs) Handles btnBrowsPath.Click
        Dim folderBrowser = New FolderBrowserDialog()
        folderBrowser.Description = "Brows folder where you have all the Execupay API's"
        folderBrowser.SelectedPath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Execupay")
        folderBrowser.ShowNewFolderButton = False
        If folderBrowser.ShowDialog() = DialogResult.OK Then
            teExecupayApiPath.EditValue = folderBrowser.SelectedPath
        End If
    End Sub

    Private Sub btnClearLocalSettings_Click(sender As Object, e As EventArgs) Handles btnClearLocalSettings.Click
        teExecupayApiPath.EditValue = Nothing
        btnSave_Click(Nothing, Nothing)
    End Sub

    Private Sub btnAddFiles_Click(sender As Object, e As EventArgs) Handles btnAddFiles.Click
        Using ofd = New OpenFileDialog()
            ofd.InitialDirectory = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Execupay")
            ofd.Multiselect = True
            If ofd.ShowDialog() = DialogResult.OK Then
                For Each file In ofd.FileNames
                    RequiredExecupayAPIDllList.Add(System.IO.Path.GetFileName(file))
                Next
                SetUdfValue("List of required Execupay API Dll’s", Newtonsoft.Json.JsonConvert.SerializeObject(RequiredExecupayAPIDllList))
            End If
        End Using
    End Sub

    Private Sub btnRemoveSelectedFiles_Click(sender As Object, e As EventArgs) Handles btnRemoveSelectedFiles.Click
        If lbcRequiredExecupayAPIDllList.SelectedIndices.Count < 1 Then
            XtraMessageBox.Show("No files selected.")
            Exit Sub
        End If

        If XtraMessageBox.Show($"Are you sure you would like to remove {lbcRequiredExecupayAPIDllList.SelectedItems.Count} files from the list?", "Remove selected files?", MessageBoxButtons.YesNo) = DialogResult.Yes Then
            Dim listToRemove = New List(Of String)
            For Each index In lbcRequiredExecupayAPIDllList.SelectedIndices
                listToRemove.Add(lbcRequiredExecupayAPIDllList.GetItemValue(index))
            Next
            listToRemove.ForEach(Function(l) RequiredExecupayAPIDllList.Remove(l))
            SetUdfValue("List of required Execupay API Dll’s", Newtonsoft.Json.JsonConvert.SerializeObject(RequiredExecupayAPIDllList))
        End If
    End Sub

    Private Sub btnUseLatestInstalledExecupay_Publish_Click(sender As Object, e As EventArgs) Handles btnUseLatestInstalledExecupay_Publish.Click
        Dim installedVersions = ExecupayApiManagement.GetInstalledExecupayVersions()
        If installedVersions Is Nothing OrElse Not installedVersions.Any Then
            DisplayMessageBox("No Execupay installation was found.")
            Exit Sub
        End If
        Dim serverVersion = ExecupayApiManagement.GetLatestExecupayVersion()
        Dim latestInstalledVersion = installedVersions.OrderByDescending(Function(v) v.VersionNumber).First()
        If serverVersion <> latestInstalledVersion.FormatedVersionNumber Then
            If XtraMessageBox.Show($"It looks like you do not have the latest version of Execupay intalled on your machine.{vbCrLf}Latest Server Version: {serverVersion}{vbCrLf}Latest Installed Version: {latestInstalledVersion.FormatedVersionNumber}{vbCrLf}Would you like to use your latest installed version ({latestInstalledVersion.FormatedVersionNumber})?", "Wrong Version", MessageBoxButtons.YesNo) <> DialogResult.Yes Then
                Exit Sub
            End If
        End If
        beLocalPathToPublish.EditValue = latestInstalledVersion.ExeupayPath
    End Sub

    Private Sub btnPublishFiles_Click(sender As Object, e As EventArgs) Handles btnPublishFiles.Click
        Try
            If beLocalPathToPublish.Text.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("Please select a local path.")
                Exit Sub
            End If

            If teServerPathToPublish.Text.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("Please select a server path.")
                Exit Sub
            End If

            If XtraMessageBox.Show("Are you sure you would like to publish?", "Publish?", MessageBoxButtons.YesNo) = DialogResult.No Then
                Exit Sub
            End If

            If Not VerifyAllFilesExist() Then Exit Sub

            For Each file In RequiredExecupayAPIDllList
                Try
                    Dim destPath = System.IO.Path.Combine(teServerPathToPublish.Text, file)
                    System.IO.File.Copy(System.IO.Path.Combine(beLocalPathToPublish.Text, file), destPath, True)
                Catch ex As Exception
                    If XtraMessageBox.Show($"Error copying file{file}.{vbCrLf}{ex.Message}{vbCrLf}Abort Publish?", "Abort Publish?", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                        Exit Sub
                    End If
                End Try
            Next

            ExecupayAssemblyResolver.Instance.WriteVersionNumberToFile(System.IO.Path.Combine(My.Settings.AssemblyResolverPath, "CurrentVersion.txt"), ExecupayAssemblyResolver.Instance.GetServerVersionNumber.GetValueOrDefault + 1)
            XtraMessageBox.Show("Done.")
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error publishing.", ex)
        End Try
    End Sub

    Private Function VerifyAllFilesExist() As Boolean
        Dim missingRequiredFiles = New List(Of String)
        For Each file In RequiredExecupayAPIDllList
            If Not System.IO.File.Exists(System.IO.Path.Combine(beLocalPathToPublish.Text, file)) Then
                missingRequiredFiles.Add(file)
            End If
        Next
        If missingRequiredFiles.Any Then
            XtraMessageBox.Show($"Please note the following file(s) was not found in your local path.{vbCrLf}{String.Join(vbCrLf, missingRequiredFiles.ToArray())}")
            Return False
        End If
        Return True
    End Function

    Private Sub btnVerifyAllFilesExist_Click(sender As Object, e As EventArgs) Handles btnVerifyAllFilesExist.Click
        If VerifyAllFilesExist() Then
            XtraMessageBox.Show("Perfect! All file exist!")
        End If
    End Sub

    Private Sub btnRefreshAssemlies_Click(sender As Object, e As EventArgs) Handles btnRefreshAssemlies.Click
        Dim list = AppDomain.CurrentDomain.GetAssemblies().Where(Function(a) a.IsDynamic = False)
        GridControl1.DataSource = list

        Dim refLIst = New List(Of System.Reflection.AssemblyName)
        For Each a In list
            refLIst.AddRange(a.GetReferencedAssemblies)
        Next
        GridControl2.DataSource = refLIst
    End Sub

    Private Sub btnResetLocalExecupayApiDlls_Click(sender As Object, e As EventArgs) Handles btnResetLocalExecupayApiDlls.Click
        ExecupayAssemblyResolver.Instance.SetCurrentVersionNumber(0)
        XtraMessageBox.Show($"Please restart front desk for this change to take effect.")
    End Sub

    Private Sub LabelControl2_HyperlinkClick(sender As Object, e As DevExpress.Utils.HyperlinkClickEventArgs) Handles LabelControl2.HyperlinkClick
        'System.Diagnostics.Process.Start(e.Link)
        Dim psi As New System.Diagnostics.ProcessStartInfo()
        psi.FileName = e.Link
        psi.UseShellExecute = True
        System.Diagnostics.Process.Start(psi)
    End Sub
End Class


Public Class ExecupayApiManagement
    Public Shared Function GetInstalledExecupayVersions() As List(Of ExecupayVersion)
        Dim path = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Execupay")
        Dim versions = New List(Of ExecupayVersion)
        For Each dirInfo As System.IO.DirectoryInfo In New System.IO.DirectoryInfo(path).GetDirectories()
            If dirInfo.Name.StartsWith("EPS") Then
                versions.Add(New ExecupayVersion With {
                             .VersionNumber = dirInfo.Name.GetNumeric(),
                             .FormatedVersionNumber = dirInfo.Name.Replace("EPS", "").Trim(),
                             .ExeupayPath = dirInfo.FullName})
            End If

            'If dirInfo.Name.StartsWith("EPS ") Then
            '    versions.Add(New ExecupayVersion With {
            '                 .VersionNumber = dirInfo.Name.GetNumeric(),
            '                 .FormatedVersionNumber = dirInfo.Name.Replace("EPS ", "").Trim(),
            '                 .ExeupayPath = dirInfo.FullName})
            'End If
        Next
        Dim latestVer = versions.OrderByDescending(Function(v) v.VersionNumber).FirstOrDefault()
        If latestVer IsNot Nothing Then
            latestVer.IsLatest = True
        End If
        Return versions
    End Function

    Public Shared Function GetLatestInstalledExecupayVersion() As ExecupayVersion
        Dim versions = GetInstalledExecupayVersions()
        If versions.Any Then
            Return versions.Single(Function(v) v.IsLatest)
        End If
        Return Nothing
    End Function

    Public Shared Function GetLatestExecupayVersion() As String
        Using ctx As New dbEPDataDataContext(GetConnectionString)
            Return ctx.EPSYS1s.Single(Function(e) e.TYPE = "APP_EPS_VERSION").DESCR
        End Using
    End Function

    Public Shared Function IsLatestExecupayVersionInstalled() As Boolean
        Dim installed = GetLatestInstalledExecupayVersion()
        Dim lastVersion = GetLatestExecupayVersion()
        Logger.Debug("ProgramFilesX86 Installed Version {ProgramFilesX86Version}, DB Version: {DbVersion}", installed, lastVersion)
        If installed?.FormatedVersionNumber = lastVersion Then
            Return True
        End If
        Return False
    End Function

    Public Class ExecupayVersion
        Public VersionNumber As Integer
        Public FormatedVersionNumber As String
        Public ExeupayPath As String
        Public IsLatest As Boolean
    End Class
End Class