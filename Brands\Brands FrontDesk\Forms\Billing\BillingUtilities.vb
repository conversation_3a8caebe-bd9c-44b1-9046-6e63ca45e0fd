﻿Namespace Billing
    Public Class BillingUtilities

        Private ReadOnly importService As IBillingImportService

        Public Sub New(importService As IBillingImportService)
            Me.importService = importService
        End Sub

        Public Function GetPrice(db As dbEPDataDataContext, conum As Decimal, itemNum As Decimal, qty As Decimal) As Decimal
            Dim overides = (From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = conum AndAlso b.ITEM_NUM = itemNum)
            If overides.Any Then
                Dim minQty = overides.Select(Function(a) a.PriceFromQty).Min()
                If minQty > qty Then
                    Throw New Exception($"Error calcualting Price For Item#: {itemNum}, Co#: {conum}.{vbCrLf}This client has a price override, however the client did not reached the minimum quantity ({minQty}),
    Minimum Quantity: {minQty}
    Current Quantity: {qty}
Please add another price override for this client with a quantity of {minQty} or less and try again.")
                Else
                    Return overides.Where(Function(p) p.PriceFromQty <= qty).OrderBy(Function(p) p.PriceFromQty).ToList.Last.ACTUAL_PRICE * qty
                End If
            Else
                Return (From b In db.ACT_ITEMs Where b.ITEM_NUM = itemNum).Single.STD_PRICE * qty
            End If
        End Function

        'Solomon modified on Aug 1, '21.  New invoice system
        'Public Function GetIsInvoiced(conum As Decimal, price As Decimal, itemNum As Decimal, invoiceLogs As List(Of INVOICE_xLOG)) As Boolean?
        Public Function GetIsInvoiced(conum As Decimal, price As Decimal, itemNum As Decimal, invoiceLogs As List(Of invoice_item_detail)) As Boolean?
            If price > 0 Then
                Return (From i In invoiceLogs Where i.conum = conum AndAlso i.item_num = itemNum AndAlso i.item_name = GetItemName(itemNum)).Any
            Else
                Return Nothing
            End If
        End Function

        Public Function GetItemName(ItemNum As Decimal) As String
            Return importService.globalBilling.Single(Function(b) b.ITEM_NUM = ItemNum).ITEM_NAME & "~" & importService.InvoiceDate.ToString("MMMM") & " " & importService.InvoiceDate.ToString("yyyy")
        End Function

        'Public Shared Function xGetInvoiceNumForCo(CoNum As Decimal, InvDate As DateTime, DivNum As Decimal) As invoice_master
        '    Using db = New dbEPDataDataContext(GetConnectionString)
        '        Dim CO = db.COOPTIONs.Single(Function(c) c.CONUM = CoNum)

        '        Return (
        '            From i In db.invoice_masters
        '            Where i.conum = CoNum AndAlso (i.invoice_deleted = 0 OrElse i.invoice_deleted Is Nothing) _
        '                AndAlso (i.invoice_number = 0 OrElse (CO.CO_BILL_FREQ = "Per Month" And i.invoice_date = InvDate.ToShortDateString())) _
        '                AndAlso (i.divnum = DivNum)).
        '                OrderBy(Function(o1) o1.divnum).OrderByDescending(Function(o2) o2.invoice_number).FirstOrDefault()
        '    End Using
        'End Function

        'Solomon modified on Mar 29, '22.  Always match invoice_number 0 only for this division
        Public Shared Function GetInvoiceNumForCo(CoNum As Decimal, DivNum As Decimal, InvDate As Date, InsertIntoExistingInvoice As Boolean, Optional ExactMatch As Boolean = False) As invoice_master
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim CO = db.COOPTIONs.Single(Function(c) c.CONUM = CoNum)

                If CO.CO_BILL_FREQ = "Per Month" AndAlso InsertIntoExistingInvoice = True AndAlso ExactMatch Then
                    Return (
                    From i In db.invoice_masters
                    Where i.conum = CoNum AndAlso (i.invoice_deleted = 0 OrElse i.invoice_deleted Is Nothing) _
                        AndAlso i.invoice_date.Value.Year = InvDate.Year AndAlso i.invoice_date.Value.Month = InvDate.Month _
                        AndAlso i.invoice_date.Value.Day = InvDate.Day AndAlso i.divnum = DivNum AndAlso i.invoice_number > 0).
                        OrderBy(Function(o1) o1.divnum).OrderByDescending(Function(o2) o2.invoice_number).FirstOrDefault()
                ElseIf CO.CO_BILL_FREQ = "Per Month" AndAlso InsertIntoExistingInvoice = True Then
                    Return (
                    From i In db.invoice_masters
                    Where i.conum = CoNum AndAlso (i.invoice_deleted = 0 OrElse i.invoice_deleted Is Nothing) _
                        AndAlso i.invoice_date.Value.Year = InvDate.Year AndAlso i.invoice_date.Value.Month = InvDate.Month AndAlso i.divnum = DivNum).
                        OrderBy(Function(o1) o1.divnum).OrderByDescending(Function(o2) o2.invoice_number).FirstOrDefault()
                Else
                    Return (
                    From i In db.invoice_masters
                    Where i.conum = CoNum AndAlso (i.invoice_deleted = 0 OrElse i.invoice_deleted Is Nothing) _
                        AndAlso i.invoice_number = 0 AndAlso i.divnum = DivNum).
                        OrderBy(Function(o1) o1.divnum).FirstOrDefault()
                End If
            End Using
        End Function

        'Modified Aug 1, '21.  Using new Invoice system
        Public Sub InsertInvoiceLog(conum As Decimal, ItemNum As Decimal, price As Decimal, count As Decimal)
            Try
                Using db = New dbEPDataDataContext(GetConnectionString)
                    'Dim invNum = db.fn_GetInvoiceNumForCo(conum, importService.InvoiceDate)
                    Dim ExInv = GetInvoiceNumForCo(conum, 0, importService.InvoiceDate, 1, 0)
                    Dim CO = db.COOPTIONs.Single(Function(c) c.CONUM = conum)

                    Dim paymentTerms As Nullable(Of Integer) = Query(Of Integer)("SELECT glt.cat_id FROM global_lists glt WHERE glt.list_name = 'Invoice Payment Terms' AND glt.list_value = 'Payment Due Upon Receipt'").FirstOrDefault()

                    If ExInv Is Nothing Then
                        ExInv = New invoice_master() With {
                            .invoice_key = Guid.NewGuid, .conum = conum, .invoice_number = 0, .invoice_date = importService.InvoiceDate, .divnum = 0,
                            .invoice_gen_freq = CO.CO_BILL_FREQ, .prnum = 0, .inv_payment_terms_id = nz(paymentTerms, 0), .inv_payment_method = "Direct Deposit",
                            .inv_salestax_exempt = "NO", .inv_display_type = "Detailed", .inv_summ_desc = "Payroll Processing", .invoice_exported = 2,
                            .created_by = UserName, .created_date = DateTime.Now, .rowguid = Guid.NewGuid
                        }
                        db.invoice_masters.InsertOnSubmit(ExInv)
                    End If

                    Dim lastItem = (From d In db.invoice_item_details.Where(Function(f) f.conum = ExInv.conum AndAlso f.invoice_key = ExInv.invoice_key AndAlso (f.item_deleted = 0 OrElse f.item_deleted Is Nothing)) Select d).OrderByDescending(Function(o) o.item_section_sort_order).FirstOrDefault()
                    Dim sort As Int16 = 1
                    If Not lastItem Is Nothing Then
                        sort = lastItem.item_section_sort_order + 1
                    End If

                    Dim section = db.global_lists.Where(Function(f) f.list_name = "Invoice Group Sections" AndAlso f.list_value = "Payroll").First
                    Dim category = db.global_lists.Where(Function(f) f.list_name = "Invoice Report Categories" AndAlso f.list_value = "Miscellaneous").First
                    Dim sectionId As Integer, categoryId As Integer

                    If section IsNot Nothing Then
                        sectionId = section.cat_id
                    End If

                    If category IsNot Nothing Then
                        categoryId = category.cat_id
                    End If

                    Dim NewDetail = New invoice_item_detail With {
                        .detail_key = Guid.NewGuid,
                        .conum = conum, .invoice_key = ExInv.invoice_key, .item_date = importService.InvoiceDate, .item_category_id = categoryId, .item_num = ItemNum,
                        .item_type = "Manual", .item_sales_taxable = "YES", .item_intial_price = 0, .item_final_price = price, .item_overide_price = price, .item_count = count,
                        .item_prnum = ExInv.prnum, .item_group_section_id = sectionId, .item_discount_amt = 0, .item_name = GetItemName(ItemNum),
                        .item_section_sort_order = sort, .exclude_from_invminmax = "NO", .item_minxmax_applied = 0, .item_deleted = 0, .created_by = UserName,
                        .created_date = Now, .rowguid = Guid.NewGuid
                    }

                    db.invoice_item_details.InsertOnSubmit(NewDetail)

                    db.SaveChanges
                End Using
            Catch ex As Exception
                DisplayErrorMessage($"Error inserting invoice log for Item#: {ItemNum} Co#: {conum} Price: {price} Count: {count}", ex)
            End Try
        End Sub
    End Class
End Namespace