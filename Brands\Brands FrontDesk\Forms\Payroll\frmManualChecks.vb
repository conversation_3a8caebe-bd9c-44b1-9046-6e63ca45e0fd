﻿Imports System.ComponentModel
Imports System.IO
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid

Public Class frmManualChecks

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CheckDate As Date
    Private Property HasUtilitySetup As Boolean

    Private _db As dbEPDataDataContext

    Private IsPPImport As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property UpdateZendeskTicketId As Long?

    Private Sub frmManualChecks_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        IsPPImport = Reflection.Assembly.GetEntryAssembly.GetName.Name = "PP_Import_Emps"
        If IsPPImport Then
            Me.XtraTabControl1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False '.TabPages(1).PageVisible = False
        End If
        Me.txtCoNum.EditValue = Me.CoNum
        _db = New dbEPDataDataContext(GetConnectionString)
        Dim CoName = (From A In _db.COMPANies Where A.CONUM = Me.CoNum Select A.CO_NAME).FirstOrDefault
        Me.txtCoName.EditValue = CoName
        Dim coop = (From co In _db.CoOptions_Payrolls Where co.CoNum = Me.CoNum).SingleOrDefault()
        HasUtilitySetup = (Not IsPPImport) AndAlso coop IsNot Nothing AndAlso coop.Run_prc_PrUtilityImport.IsNotNullOrWhiteSpace()
        If HasUtilitySetup Then
            txtHasUtilImport.Text = "Has Utility Import Setup. Right click a row to run"
        End If

        Dim hasCheckSignature = QuerySingleOrDefault(Of String)("SELECT CHKSIGNATURE
FROM dbo.SCHCORPT_LIST R
INNER JOIN dbo.COOPTIONS CO ON CO.CONUM = R.CONUM
WHERE SRPT_ID IN (59,359,5054)
AND R.CONUM = @CoNum
GROUP BY R.CONUM, CHKSIGNATURE", New With {.CoNum = CoNum})
        teCompanyPSChecks.Visible = (hasCheckSignature = "YES")

        Dim data = _db.prc_GetManualChecks(CoNum).ToList
        For Each rec In data
            If rec.mpwStatus = "New" Then
                If Year(rec.check_date) = Year(CheckDate) Then
                    rec.mpwStatus = "Include In Payroll"
                End If
            End If
        Next
        Me.PrcGetManualChecksResultBindingSource.DataSource = data
        Me.LoadVoids()

        Dim PayrollNumbers = (From A In _db.PAYROLLs Where A.CONUM = Me.CoNum Order By A.PRNUM Descending Select A.PRNUM, CheckDate = A.CHECK_DATE).Take(60).ToList
        Me.BindingSourcePayrollList.DataSource = PayrollNumbers

        Me.riComboBoxNewEndDate.Mask.MaskType = Mask.MaskType.DateTime
        Me.riComboBoxNewEndDate.Mask.EditMask = "d"
    End Sub

    Sub LoadVoids()
        Me.PrcGetVoidsToVoidResultBindingSource.DataSource = _db.prc_GetVoidsToVoid(CoNum, Nothing)
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Dim Recs = (From A As prc_GetManualChecksResult In Me.PrcGetManualChecksResultBindingSource Where A.mpwStatus <> "Include in Payroll").ToList
            If Recs.Count > 0 Then
                Dim data = (From A In _db.MAN_CHK_MASTs Where A.CONUM = Me.CoNum AndAlso {"New", "For Review"}.Contains(A.mpwStatus)).ToList
                For Each rec In Recs
                    Dim source = (From A In data Where A.EMPNUM & A.CHK_COUNTER = rec.EMPNUM & rec.CHK_COUNTER).Single
                    If rec.mpwStatus = "Remove Entry" Then
                        source.mpwStatus = "Deleted"
                    ElseIf rec.mpwStatus = "Include In Payroll" Then
                        source.mpwStatus = "New"
                    ElseIf rec.mpwStatus = "For Review" Then
                        source.mpwStatus = "For Review"
                    End If
                Next
                _db.SubmitChanges()
            End If

            Dim vRecs = (From A As prc_GetVoidsToVoidResult In Me.PrcGetVoidsToVoidResultBindingSource Where A.Status = "Remove Entry").ToList
            For Each rec In vRecs
                Dim vRec = (From A In _db.Voids Where A.CoNum = Me.CoNum AndAlso A.PrNum = rec.PrNum AndAlso A.EmpNum = rec.EmpNum AndAlso A.ChkCounter = rec.ChkCounter AndAlso A.ChkType = rec.ChkType AndAlso A.VoidPrNum Is Nothing).FirstOrDefault
                _db.Voids.DeleteOnSubmit(vRec)
            Next

            If Me.PrcSelectPayrollToEditResultBindingSource.DataSource IsNot Nothing Then
                For Each itm As prc_SelectPayrollToEditResult In Me.PrcSelectPayrollToEditResultBindingSource.List
                    If itm.NewEndDate.HasValue Then
                        Dim clEnt = (From A In _db.CHK_DET_PAYs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = itm.PAYROLL_NUM AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER AndAlso A.CL_NUM = itm.MaxClNum).Single
                        clEnt.line_date = itm.NewEndDate
                        itm.NewEndDate = Nothing
                    End If
                Next
            End If
            _db.SaveChanges()
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
            Me.Close()
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes to Manual Checks.", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub GridView1_ValidatingEditor(sender As Object, e As DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs) Handles GridViewManuals.ValidatingEditor
        Dim rec As prc_GetManualChecksResult = Me.GridViewManuals.GetFocusedRow
        If Not Me.GridViewManuals.FocusedColumn.FieldName = "mpwStatus" Then Exit Sub
        If Year(rec.check_date) <> Year(CheckDate) Then
            If e.Value = "Include In Payroll" Then
                DisplayMessageBox("The check year is not in current payroll year")
                'Dim oldValue As String = Me.GridView1.ActiveEditor.OldEditValue
                e.Value = "New"
            End If
        ElseIf rec.check_date < Today.AddDays(-19) AndAlso e.Value = "For Review" Then
            DisplayMessageBox("Check is already over 20 days. Delete it or include it")
            e.Value = "Include In Payroll"
        End If
    End Sub

    Private Sub GridView1_RowStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs) Handles GridViewManuals.RowCellStyle
        If e.Column.FieldName = "check_date" Then
            Dim chkDate As Date = e.CellValue
            If chkDate.Year <> Me.CheckDate.Year Then
                e.Appearance.BackColor = Color.Red
            End If
        End If
    End Sub

    Private Sub btnAddVoid_Click(sender As Object, e As EventArgs) Handles btnAddVoid.Click
        Dim frm = New frmAddVoid With {.CoNum = Me.CoNum}
        Dim results = frm.ShowDialog
        Me.LoadVoids()
        frm.Dispose()
    End Sub

    Private Sub RunUtilityImport(curRec As prc_GetManualChecksResult)
        Try
            Dim data As List(Of MAN_CHK_MAST)
            If curRec IsNot Nothing Then
                data = (From A In _db.MAN_CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.mpwStatus.ToLower = "new" AndAlso A.EMPNUM = curRec.EMPNUM AndAlso A.CHK_COUNTER = curRec.CHK_COUNTER).ToList
            Else
                data = (From A In _db.MAN_CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.mpwStatus.ToLower = "new").ToList
            End If
            If data.Count > 0 AndAlso XtraMessageBox.Show("Would you like to run the Utility Import now for {0} checks?".FormatWith(data.Count), "Run Utility Import", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.Yes Then
                Dim UtilityImport As New PayrollUtilityImport(Me.CoNum)
                For Each rec In data
                    UtilityImport.ProcessImportManualCheck(rec)
                Next
            End If
            XtraMessageBox.Show("Done")
        Catch ex As Exception
            DisplayErrorMessage("Error running utility import", ex)
        End Try
    End Sub

    Private Sub GridViewManuals_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridViewManuals.PopupMenuShowing
        Dim gv As GridView = sender
        If (Not IsPPImport) AndAlso e.Allow AndAlso e.HitInfo.InRow Then
            Dim rec As prc_GetManualChecksResult = gv.GetRow(e.HitInfo.RowHandle)
            Dim gridHasMultipleRows = gv.RowCount > 1
            If HasUtilitySetup Then
                e.Menu.Items.Add(New DXMenuItem("Run Utility Import for this check", Sub()
                                                                                         RunUtilityImport(rec)
                                                                                     End Sub))
                e.Menu.Items.Add(New DXMenuItem("Run Utility Import for all Checks", Sub()
                                                                                         RunUtilityImport(Nothing)
                                                                                     End Sub))
            End If

            If rec.mpwStatus = "Include In Payroll" OrElse rec.mpwStatus = "New" Then
                e.Menu.Items.Add(New DXMenuItem("Edit Check", click:=Sub()
                                                                         MainForm.ShowForm(New frmCheckFigures(CoNum, rec.EMPNUM, rec.CHK_COUNTER))
                                                                     End Sub))
            End If

            Dim allManualChecks As List(Of prc_GetManualChecksResult) = Me.PrcGetManualChecksResultBindingSource.DataSource
            Dim selectedRowsCount = GridViewManuals.SelectedRowsCount
            Dim selectedText = IIf(selectedRowsCount > 1, $" - Selected Checks ({selectedRowsCount})", "")
            Dim selectedchecks = New List(Of prc_GetManualChecksResult)
            If GridViewManuals.SelectedRowsCount > 1 Then
                selectedchecks.AddRange(GridViewManuals.GetSelectedRows(Of prc_GetManualChecksResult))
            Else
                selectedchecks.Add(rec)
            End If

            Dim printMenu = New DXSubMenuItem("Print Check") With {.Image = My.Resources.print_16x16}
            printMenu.Items.Add(New DXMenuItem($"Manual Check {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "", "Manual Check", Nothing)))
            printMenu.Items.Add(New DXMenuItem("Manual Check - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "", "Manual Check", Nothing)) With {.Visible = gridHasMultipleRows})
            printMenu.Items.Add(New DXMenuItem($"Manual Check - Pressure Seal {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "", "Manual Check Pressure Seal", Nothing)) With {.BeginGroup = True})
            printMenu.Items.Add(New DXMenuItem("Manual Check - Pressure Seal - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "", "Manual Check Pressure Seal", Nothing), My.Resources.print_16x16))
            e.Menu.Items.Add(printMenu)

            Dim zendeskBackgroundMenu = New DXSubMenuItem("Email Check (Via Zendesk - Queued)") With {.Image = My.Resources.Email}
            zendeskBackgroundMenu.Items.Add(New DXMenuItem($"Manual Check {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "Zendesk", "Manual Check", Nothing, True)))
            zendeskBackgroundMenu.Items.Add(New DXMenuItem("Manual Check - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "Zendesk", "Manual Check", Nothing, True)) With {.Visible = gridHasMultipleRows})
            zendeskBackgroundMenu.Items.Add(New DXMenuItem($"Manual Check - Pressure Seal {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "Zendesk", "Manual Check Pressure Seal", Nothing, True)) With {.BeginGroup = True})
            zendeskBackgroundMenu.Items.Add(New DXMenuItem("Manual Check - Pressure Seal - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "Zendesk", "Manual Check Pressure Seal", Nothing, True)) With {.Visible = gridHasMultipleRows})
            e.Menu.Items.Add(zendeskBackgroundMenu)

            Dim shipMenu = New DXSubMenuItem("Ship Check") With {.Image = My.Resources.print_16x16}
            shipMenu.Items.Add(New DXMenuItem($"Manual Check {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "", "Manual Check", "\ChecksPrinter")))
            shipMenu.Items.Add(New DXMenuItem("Manual Check - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "", "Manual Check", "\ChecksPrinter")) With {.Visible = gridHasMultipleRows})
            shipMenu.Items.Add(New DXMenuItem($"Manual Check - Pressure Seal {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "", "Manual Check Pressure Seal", "\ChecksPressPrinter")) With {.BeginGroup = True})
            shipMenu.Items.Add(New DXMenuItem("Manual Check - Pressure Seal - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "", "Manual Check Pressure Seal", "\ChecksPressPrinter"), My.Resources.print_16x16))
            e.Menu.Items.Add(shipMenu)

            Dim moreMenue = New DXSubMenuItem("More")

            Dim zendeskMenu = New DXSubMenuItem("Email Check (Via Zendesk)") With {.Image = My.Resources.Email}
            zendeskMenu.Items.Add(New DXMenuItem($"Manual Check {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "Zendesk", "Manual Check", Nothing)))
            zendeskMenu.Items.Add(New DXMenuItem("Manual Check - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "Zendesk", "Manual Check", Nothing)) With {.Visible = gridHasMultipleRows})
            zendeskMenu.Items.Add(New DXMenuItem($"Manual Check - Pressure Seal {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "Zendesk", "Manual Check Pressure Seal", Nothing)) With {.BeginGroup = True})
            zendeskMenu.Items.Add(New DXMenuItem("Manual Check - Pressure Seal - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "Zendesk", "Manual Check Pressure Seal", Nothing)) With {.Visible = gridHasMultipleRows})
            moreMenue.Items.Add(zendeskMenu)

            Dim emailMenu = New DXSubMenuItem("Email Check") With {.Image = My.Resources.Email}
            emailMenu.Items.Add(New DXMenuItem($"Manual Check {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "Email", "Manual Check", Nothing)))
            emailMenu.Items.Add(New DXMenuItem("Manual Check - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "Email", "Manual Check", Nothing)) With {.Visible = gridHasMultipleRows})
            emailMenu.Items.Add(New DXMenuItem($"Manual Check - Pressure Seal {selectedText}", Sub() ProcessManualCheckReportAsync(selectedchecks, "Email", "Manual Check Pressure Seal", Nothing)) With {.BeginGroup = True})
            emailMenu.Items.Add(New DXMenuItem("Manual Check - Pressure Seal - (All)", Sub() ProcessManualCheckReportAsync(allManualChecks, "Email", "Manual Check Pressure Seal", Nothing)) With {.Visible = gridHasMultipleRows})
            moreMenue.Items.Add(emailMenu)

            e.Menu.Items.Add(moreMenue)
        End If
    End Sub

    Private Async Sub ProcessManualCheckReportAsync(rows As List(Of prc_GetManualChecksResult), deliverVia As String, reportName As String, shipPrinterName As String, Optional useQueue As Boolean = False)
        Try
            If shipPrinterName.IsNotNullOrWhiteSpace AndAlso rows.Select(Function(div) div.DIVNUM).Distinct().Count > 1 Then
                If XtraMessageBox.Show($"Attention! You have selected multiple checks to be shipped, but it can only be delivered to one shipping address, if you need to ship it to multiple addresses, please select that checks separately ", "Multiple Checks Selected", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                    Exit Sub
                End If
            End If

            If useQueue Then
                Dim _report = _db.ReportEmailTeplates.SingleOrDefault(Function(r) r.Name = reportName)
                Dim queue = New ReportProcessorQueue(CoNum, Nothing, Nothing)

                Using frm = New frmSelectNewOrExistingTicket
                    If UpdateZendeskTicketId.HasValue Then
                        frm.cbUpdateTicket.Checked = True
                        frm.seZendeskTicketId.Value = UpdateZendeskTicketId
                    End If
                    frm.ShowDialog()
                    If frm.cbUpdateTicket.Checked Then
                        queue.UpdateZendeskTicketId = frm.seZendeskTicketId.Value
                    End If
                End Using

                For Each check In rows
                    Dim defaultParamValues = New List(Of KeyValuePair)
                    defaultParamValues.Add(New KeyValuePair("ef", check.EMPNUM))
                    defaultParamValues.Add(New KeyValuePair("cc", check.CHK_COUNTER))
                    Await queue.AddReport(_report.ID, False, defaultParamValues)
                Next

                Await queue.SendReports("Manual Check Requested", Nothing, _report)

                XtraMessageBox.Show($"Note, if you're planning to submit this payroll now, please wait for the manual check to finish processing before submitting the payroll, otherwise the manaul check will be sent blank.{vbCrLf}(approx 2 minutes, or check in My Queue status.)")

                Exit Sub
            End If

            Dim result As ReportResults = Nothing
            Try
                Me.ShowDefaultWaitForm(description:="processing manual checks.")
                Application.DoEvents()
                Dim rptProcessor = New ReportProcessor(CoNum, reportName, FileType.Pdf) With {.showInRecentReports = True, .showParametersForm = False}
                For Each check In rows
                    rptProcessor.DefaultParamValues = New List(Of KeyValuePair)
                    rptProcessor.DefaultParamValues.Add(New KeyValuePair("ef", check.EMPNUM))
                    rptProcessor.DefaultParamValues.Add(New KeyValuePair("cc", check.CHK_COUNTER))
                    Dim r = rptProcessor.ProcessReport()
                    If result Is Nothing Then
                        result = r
                    Else
                        result.Paths.AddRange(r.Paths)
                    End If
                Next
                Dim saveLocation = Path.Combine(modReports.GetCrystalReportsFolder(), modReports.GetFileName(result.Comp, reportName, ".pdf"))
                PdfUtilities.CombinePdfsNew(saveLocation, result.Paths.ToArray)
                result.Paths.Clear()
                result.Paths.Add(saveLocation)
            Catch ex As Exception
                Logger.Error(ex, "Error processing manual check report.")
                Throw
            Finally
                Me.CloseWaitForm
            End Try

            If Not result.Cancalled AndAlso Not result.AllFileExist Then
                DisplayMessageBox("No records returned for the parameters values entered")
                Exit Sub
            End If

            If deliverVia = "Email" Then
                Dim sender = New ReportSender(result) With {.sendAttachmentsAsZip = True, .showWebPost = False}
                sender.EmailReport()
            ElseIf deliverVia = "Zendesk" Then
                Dim sender = New ReportSender(result) With {.showWebPost = False}
                Await sender.CreateTicketAsync()
            ElseIf shipPrinterName.IsNotNullOrWhiteSpace Then
                Using frm = New frmOrderReports(CoNum, result.Paths.Single, shipPrinterName, reportName, 11)
                    frm.ShowDialog()
                End Using
            Else
                If Not result.Cancalled AndAlso XtraMessageBox.Show("Do you want to open this file ?", "Export", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                    result.Paths.ForEach(Function(r) Process.Start(r))
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error processing Manual Check", ex)
        Finally
            GridViewManuals.HideLoadingPanel()
        End Try
    End Sub

    Private endDates As Dictionary(Of String, String())
    Private Sub txtPayrollNum_EditValueChanged(sender As Object, e As EventArgs) Handles txtPayrollNum.EditValueChanged
        If Not Me.txtPayrollNum.HasValue Then Exit Sub
        If Me.txtPayrollNum.OldEditValue IsNot Nothing AndAlso Me.txtPayrollNum.OldEditValue.Equals(Me.txtPayrollNum.EditValue) Then Exit Sub


        Dim PrNum As Decimal = Me.txtPayrollNum.EditValue
        Dim pr = (From A In _db.PAYROLLs Where A.CONUM = Me.CoNum AndAlso A.PRNUM = PrNum Select A.PER1_END_DATE, A.CHECK_DATE).SingleOrDefault
        If pr Is Nothing Then
            DisplayMessageBox("Payroll # " & PrNum & " not found")
            Me.txtPayrollNum.EditValue = Nothing
            Exit Sub
        End If

        Me.txtCheckDate.EditValue = pr.CHECK_DATE
        Dim data = _db.prc_SelectPayrollToEdit(Me.CoNum, PrNum).ToList
        Me.PrcSelectPayrollToEditResultBindingSource.DataSource = data

        Dim endDate = (From A In _db.CALENDARs
                       Where A.conum = Me.CoNum AndAlso A.end_date < Today.AddDays(7)
                       Order By A.end_date Descending
                       Select A.end_date, A.frequency).Take(50).ToList()

        endDates = (From a In endDate
                    Group By a.frequency Into Group).
                    ToDictionary(Function(p) p.frequency, Function(p) p.Group.Select(Function(a) a.end_date.Value.ToString("d")).ToArray)


        Dim hasReportSetup = _db.ExecuteQuery(Of String)((String.Format("SELECT RPT_NAME FROM dbo.SCHCORPT_LIST WHERE CONUM = {0} AND SRPT_ID IN (5057, 5054)", Me.CoNum)).ToString()).ToList
        Me.lblCheckDateMsg.Visible = hasReportSetup.Count = 0

    End Sub

    'Private Sub GridViewEditPayroll_ShownEditor(sender As Object, e As EventArgs) Handles GridViewEditPayroll.ShownEditor
    '    Dim edit As ComboBoxEdit = CType(GridViewEditPayroll.ActiveEditor, ComboBoxEdit)

    '    edit.Properties.Items.Clear()
    '    Dim freq As String = GridViewEditPayroll.GetRowCellValue(GridViewEditPayroll.FocusedRowHandle, ("PAY_FREQ"))
    '    edit.Properties.Items.AddRange(endDates(freq))
    'End Sub

    Private Sub riComboBoxNewEndDate_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles riComboBoxNewEndDate.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Combo Then
            'Dim edit As DateEdit = CType(GridViewEditPayroll.ActiveEditor, DateEdit)
            Dim freq As String = GridViewEditPayroll.GetFocusedRowCellValue("PAY_FREQ")
            Dim items = endDates(freq)
            'Dim dxMenue As New DXPopupMenu
            Me.PopupMenu1.ClearLinks()

            For Each itm In items
                Dim mItem = New DevExpress.XtraBars.BarButtonItem With {.Caption = itm}
                AddHandler mItem.ItemClick, AddressOf SelectMenuDate
                Me.PopupMenu1.AddItem(mItem)
                'dxMenue.Items.Add(New DXMenuItem(itm, AddressOf SelectDate))
            Next
            'dxMenue.MenuViewType = MenuViewType.Menu

            'Dim info = TryCast(Me.GridViewEditPayroll.GetViewInfo(), DevExpress.XtraGrid.Views.Grid.ViewInfo.GridViewInfo)
            'Dim cellInfo = info.GetGridCellInfo(Me.GridViewEditPayroll.FocusedRowHandle, Me.GridViewEditPayroll.FocusedColumn)

            Dim editor As ButtonEdit = TryCast(sender, ButtonEdit)
            Dim evi As DevExpress.XtraEditors.ViewInfo.ButtonEditViewInfo = TryCast(editor.GetViewInfo(), DevExpress.XtraEditors.ViewInfo.ButtonEditViewInfo)
            'Dim bvi As DevExpress.XtraEditors.Drawing.EditorButtonObjectInfoArgs = evi.ButtonInfoByButton(e.Button)
            Dim pt As New Point(evi.Bounds.Left, evi.Bounds.Bottom)
            pt = editor.PointToScreen(pt)
            PopupMenu1.ShowPopup(pt)
            'MenuManagerHelper.ShowMenu(dxMenue, riComboBoxNewEndDate.LookAndFeel, Nothing, editor, pt)
        End If
    End Sub

    Private Sub SelectMenuDate(sender As Object, e As ItemClickEventArgs)
        Me.GridViewEditPayroll.HideEditor()
        Me.GridViewEditPayroll.SetFocusedRowCellValue("NewEndDate", e.Item.Caption)
    End Sub

    Sub SelectDate(sender As DXPopupMenu, e As EventArgs)

    End Sub

    Private Sub riVoidStatusCombo_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles riVoidStatusCombo.EditValueChanging
        If GridViewVoids.FocusedColumn Is colStatus AndAlso e.NewValue = "Remove Entry" Then
            Dim row As prc_GetVoidsToVoidResult = GridViewVoids.GetFocusedRow()
            If row.ChkType = "DD" Then
                Dim statuses = GetUdfValueSplitted("DD Reversal Removing Void Allowed To All Users")
                Dim dds = (From d In _db.DD_Reversal_Logs Where (d.CONUM = row.CoNum _
                                                             AndAlso row.EmpNum = d.EMPNUM _
                                                             AndAlso d.Payroll_num = row.PrNum _
                                                             AndAlso d.CHK_NUM = row.ChkNum _
                                                             AndAlso d.CHK_COUNTER = row.ChkCounter) _
                                                             AndAlso Not statuses.Contains(d.Reversal_Status)).ToList()
                If dds.Any() Then
                    If dds.All(Function(d) GetUdfValueSplitted("DD Reversal Delete Pending When Deleting Void").Contains(d.Reversal_Status)) Then
                        Dim result = XtraMessageBox.Show("Direct Deposit Reversal is on pending status, Do you want to cancel the pending direct deposit?", "Cancel DD Reversal", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning)
                        If result = DialogResult.Cancel Then
                            e.Cancel = True
                        ElseIf result = DialogResult.Yes Then
                            For Each check In dds
                                check.Reversal_Status = "Deleted"
                            Next
                            _db.SaveChanges
                        End If
                    Else
                        If Not {"Admin", "SuperAdmin"}.Contains(Permissions.DDReversalLevel) Then
                            XtraMessageBox.Show($"The Direct Deposit of this check was already reversed from Employee, Contact any of the following users to assist in deleting {String.Join(vbCrLf, _db.FrontDeskPermissions.Where(Function(u) u.DDReversalLevel = "Admin" OrElse u.DDReversalLevel = "SuperAdmin").Select(Function(u) u.UserName).ToArray())}")
                            e.Cancel = True
                        Else
                            XtraMessageBox.Show($"The Direct Deposit of this check was already reversed from Employee, Are you sure that you want to delete the void?{vbCrLf}If you're proceeding, enter password in next screen surrounded with '*'")
                            If frmLogin.ShowLogIn <> DialogResult.OK Then
                                e.Cancel = True
                            End If
                        End If
                    End If
                End If
            End If
        End If
    End Sub

End Class
