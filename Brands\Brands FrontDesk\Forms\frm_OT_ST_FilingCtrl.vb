﻿Imports System.Data
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports Microsoft.Data.SqlClient

Public Class frm_OT_ST_FilingCtrl
    Private DB As dbEPDataDataContext
    Private adapterOtherStatesFilingConfig As SqlDataAdapter
    Private adapterOtherStatesFilingControl As SqlDataAdapter
    Private adapterFD_ST_PortalLogins As SqlDataAdapter
    Private adapterFD_ST_PortalLoginsCoNum As SqlDataAdapter
    Private _FilingOption As FilingOptionEnum
    Private _isLoaded As Boolean
    Private _clickedRowHandle As Int32
    Private _FocusedColumn As DevExpress.XtraGrid.Columns.GridColumn
    Private SqlScriptID As Int16
    Private SqlScriptUpdateID As Int16 = 1446
    Private currentViewMode As String = "Regular"

    Public Enum FilingOptionEnum
        Qtr = 1
        Yearly = 2
    End Enum

    Public Sub New(FilingOption As FilingOptionEnum, Text As String)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        _FilingOption = FilingOption
        SqlScriptID = If(FilingOption = FilingOptionEnum.Qtr, 549, 552)
        Me.Text = $"Other States Filing - {Text}"
        GVOtherStatesFilingCtrl.ViewCaption = Me.Text
    End Sub

    Public ReadOnly Property FilingOption As FilingOptionEnum
        Get
            Return _FilingOption
        End Get
    End Property

    Private Sub StateButtonClick(sender As Object, e As ButtonPressedEventArgs)
        Try
            Dim btnEdit = CType(sender, DevExpress.XtraEditors.ButtonEdit)
            Dim col = GVOtherStatesFilingCtrl.FocusedColumn

            Dim state = GVOtherStatesFilingCtrl.GetFocusedRow("State")
            If GVOtherStatesFilingCtrl.OptionsBehavior.Editable = False Then
                state = GVOtherStatesFilingCtrl.GetRowCellValue(_clickedRowHandle, "state")
                col = _FocusedColumn
            End If

            If col.FieldName = "ProcessFolder" Then
                Dim dirInfo = New System.IO.DirectoryInfo($"I:\Downloads\FD & State ~ Setups & Transmission Info\States")
                Dim folder = dirInfo.GetDirectories(state + " - *").FirstOrDefault()
                Process.Start("explorer.exe", folder.FullName)
            ElseIf col.FieldName = "FilesFolder" Then
                Dim dirInfo = New System.IO.DirectoryInfo($"I:\Downloads\QuarterTTF")
                Dim folder = dirInfo.GetDirectories(state + " - *").FirstOrDefault()
                Process.Start("explorer.exe", folder.FullName)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in State Button Click", ex)
        End Try
    End Sub

    Function MadeChanges(GC As DevExpress.XtraGrid.GridControl) As Boolean
        Dim dt As DataTable = GC.DataSource
        If Not dt Is Nothing AndAlso dt.GetChanges() IsNot Nothing Then
            If dt.GetChanges.Rows.Count <> 0 Then
                Return True
            End If
        ElseIf Not dt Is Nothing AndAlso dt.DataSet IsNot Nothing Then
            For Each d In dt.DataSet.Tables
                If d.GetChanges() IsNot Nothing AndAlso d.GetChanges.Rows.Count <> 0 Then
                    Return True
                End If
            Next
        End If
        Return False
    End Function

    Function CanRefreshData(prompt As Boolean) As Boolean
        'this will check the whole data set so we can only set one, but if if this src is empty it will not check ds, so better to do all
        If MadeChanges(GCOtherStatesFilingCnfg) OrElse MadeChanges(GCOtherStatesFilingCtrl) OrElse MadeChanges(GCPortalLogins) Then
            If Not prompt Then
                Return False
            ElseIf XtraMessageBox.Show($"There are uncommited changes{vbCrLf} Are you sure you would like to proceed,{vbCrLf}you may be loosing unsaved changes", "Refresh?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) <> System.Windows.Forms.DialogResult.Yes Then
                Return False
            End If
        End If

        Return True
    End Function

    Function LoadData() As Boolean
        Try
            'this will check the whole data set so we can only set one, but if if this src is empty it will not check ds, so better to do all
            If Not CanRefreshData(True) Then
                Return False
            End If

            Dim Yr As Int16 = nz(lueYear.EditValue, 0)
            Dim Qtr As Int16 = nz(lueQtr.EditValue, 0)

            Dim connection = New SqlConnection(GetConnectionString())
            Dim strQueryOtherStFillCtrl As String

            Dim strCnfgTblSrc As String = ""
            Dim strCnfgOrderBy As String = ""
            Dim strCtrlTblSrc As String = ""
            Dim strCtrlOrderBy As String = ""

            If _FilingOption = FilingOptionEnum.Qtr Then
                strCnfgTblSrc = "OtherStatesFilingConfig"
                strCnfgOrderBy = " ORDER BY state, tax_type"
                strCtrlTblSrc = "OtherStatesFilingControl"
                strCtrlOrderBy = " ORDER BY state, tax_type, CONUM"
            ElseIf _FilingOption = FilingOptionEnum.Yearly Then
                strCnfgTblSrc = "OtherStatesFilingConfigYtd"
                strCnfgOrderBy = " ORDER BY state"
                strCtrlTblSrc = "OtherStatesFilingControlYtd"
                strCtrlOrderBy = " ORDER BY state, CONUM"
            End If

            Dim dtColsOsfc As DataTable
            Dim Col As DevExpress.XtraGrid.Columns.GridColumn
            dtColsOsfc = Query($"EXEC custom.prc_Rpt_FD_GridControlColumns '{strCtrlTblSrc}', 1, {nz("'" + currentViewMode + "'", "NULL")}")
            GVOtherStatesFilingCtrl.Columns.Clear()
            GVOtherStatesFilingCtrl.SetCols(dtColsOsfc, "YR", AddressOf StateButtonClick)

            If Yr = 0 Then
                strQueryOtherStFillCtrl = $"SELECT * FROM custom.{strCtrlTblSrc} {strCtrlOrderBy};"
            ElseIf Qtr = 0 OrElse _FilingOption = FilingOptionEnum.Yearly Then
                strQueryOtherStFillCtrl = $"SELECT * FROM custom.{strCtrlTblSrc} WHERE YR = {Yr} {strCtrlOrderBy};"
            Else
                strQueryOtherStFillCtrl = $"SELECT * FROM custom.{strCtrlTblSrc} WHERE YR = {Yr} AND Qtr = {Qtr} {strCtrlOrderBy};"
            End If

            Dim ds As New DataSet
            adapterOtherStatesFilingConfig = New SqlDataAdapter($"SELECT * FROM custom.{strCnfgTblSrc} osfc {strCnfgOrderBy};", connection)
            adapterOtherStatesFilingConfig.UpdateCommand = New SqlCommandBuilder(adapterOtherStatesFilingConfig).GetUpdateCommand()
            adapterOtherStatesFilingConfig.DeleteCommand = New SqlCommandBuilder(adapterOtherStatesFilingConfig).GetDeleteCommand()
            adapterOtherStatesFilingConfig.InsertCommand = New SqlCommandBuilder(adapterOtherStatesFilingConfig).GetInsertCommand()
            adapterOtherStatesFilingConfig.Fill(ds, strCnfgTblSrc)

            'for Qtr view is different than table
            If _FilingOption = FilingOptionEnum.Yearly Then
                adapterOtherStatesFilingControl = New SqlDataAdapter(strQueryOtherStFillCtrl, connection)
                adapterOtherStatesFilingControl.UpdateCommand = New SqlCommandBuilder(adapterOtherStatesFilingControl).GetUpdateCommand()
                adapterOtherStatesFilingControl.DeleteCommand = New SqlCommandBuilder(adapterOtherStatesFilingControl).GetDeleteCommand()
                adapterOtherStatesFilingControl.InsertCommand = New SqlCommandBuilder(adapterOtherStatesFilingControl).GetInsertCommand()
            Else
                Dim viewName = "view_OtherStatesFilingControl"
                Dim colsToIncludeInUpd = Query(Of String)($"SELECT custom.fn_getTableColumnsFoundInViewForFiling('custom.[{strCtrlTblSrc}]', 'custom.[{viewName}]', '{strCtrlTblSrc}', 1)").FirstOrDefault()

                adapterOtherStatesFilingControl = New SqlDataAdapter(Replace(strQueryOtherStFillCtrl, "*", colsToIncludeInUpd), connection)
                adapterOtherStatesFilingControl.UpdateCommand = New SqlCommandBuilder(adapterOtherStatesFilingControl).GetUpdateCommand()
                adapterOtherStatesFilingControl.DeleteCommand = New SqlCommandBuilder(adapterOtherStatesFilingControl).GetDeleteCommand()
                adapterOtherStatesFilingControl.InsertCommand = New SqlCommandBuilder(adapterOtherStatesFilingControl).GetInsertCommand()

                Dim UpdateCommandText = adapterOtherStatesFilingControl.UpdateCommand.CommandText.Replace(strCtrlTblSrc, viewName)
                Dim DeleteCommandText = adapterOtherStatesFilingControl.DeleteCommand.CommandText.Replace(strCtrlTblSrc, viewName)
                Dim InsertCommandText = adapterOtherStatesFilingControl.InsertCommand.CommandText.Replace(strCtrlTblSrc, viewName)
                Dim UpdateCommandParams = adapterOtherStatesFilingControl.UpdateCommand.Parameters
                Dim DeleteCommandParams = adapterOtherStatesFilingControl.DeleteCommand.Parameters
                Dim InsertCommandParams = adapterOtherStatesFilingControl.InsertCommand.Parameters
                adapterOtherStatesFilingControl = New SqlDataAdapter(Replace(strQueryOtherStFillCtrl, strCtrlTblSrc, viewName), connection)
                adapterOtherStatesFilingControl.UpdateCommand = New SqlCommand(UpdateCommandText, connection)
                Dim p As SqlParameter
                For Each p In UpdateCommandParams
                    adapterOtherStatesFilingControl.UpdateCommand.Parameters.Add(p.CloneEntity)
                Next
                adapterOtherStatesFilingControl.DeleteCommand = New SqlCommand(DeleteCommandText, connection)
                For Each p In DeleteCommandParams
                    adapterOtherStatesFilingControl.DeleteCommand.Parameters.Add(p.CloneEntity)
                Next
                adapterOtherStatesFilingControl.InsertCommand = New SqlCommand(InsertCommandText, connection)
                For Each p In InsertCommandParams
                    adapterOtherStatesFilingControl.InsertCommand.Parameters.Add(p.CloneEntity)
                Next
            End If
            adapterOtherStatesFilingControl.Fill(ds, strCtrlTblSrc)

            Dim adapterViewStateFilingControlPortals As SqlDataAdapter = Nothing
            If _FilingOption = FilingOptionEnum.Qtr Then
                adapterViewStateFilingControlPortals = New SqlDataAdapter($"SELECT * FROM custom.view_StateFilingControlPortals WHERE YR = {Yr} AND Qtr = {Qtr};", connection)
            ElseIf _FilingOption = FilingOptionEnum.Yearly Then
                adapterViewStateFilingControlPortals = New SqlDataAdapter($"SELECT * FROM custom.view_StateFilingControlPortalsYtd WHERE YR = {Yr};", connection)
            End If
            adapterViewStateFilingControlPortals.Fill(ds, "view_StateFilingControlPortals")

            adapterFD_ST_PortalLogins = New SqlDataAdapter("SELECT * FROM custom.FD_ST_PortalLogins ORDER BY State, TaxType;", connection)
            adapterFD_ST_PortalLogins.UpdateCommand = New SqlCommandBuilder(adapterFD_ST_PortalLogins).GetUpdateCommand()
            adapterFD_ST_PortalLogins.DeleteCommand = New SqlCommandBuilder(adapterFD_ST_PortalLogins).GetDeleteCommand()
            adapterFD_ST_PortalLogins.InsertCommand = New SqlCommandBuilder(adapterFD_ST_PortalLogins).GetInsertCommand()
            adapterFD_ST_PortalLogins.Fill(ds, "FD_ST_PortalLogins")

            adapterFD_ST_PortalLoginsCoNum = New SqlDataAdapter("SELECT * FROM custom.FD_ST_PortalLoginsCoNum ORDER BY CoNum;", connection)
            adapterFD_ST_PortalLoginsCoNum.UpdateCommand = New SqlCommandBuilder(adapterFD_ST_PortalLoginsCoNum).GetUpdateCommand()
            adapterFD_ST_PortalLoginsCoNum.DeleteCommand = New SqlCommandBuilder(adapterFD_ST_PortalLoginsCoNum).GetDeleteCommand()
            adapterFD_ST_PortalLoginsCoNum.InsertCommand = New SqlCommandBuilder(adapterFD_ST_PortalLoginsCoNum).GetInsertCommand()
            adapterFD_ST_PortalLoginsCoNum.Fill(ds, "FD_ST_PortalLoginsCoNum")

            If _FilingOption = FilingOptionEnum.Qtr Then
                ds.Relations.Add(New DataRelation("view State Filing Control Portals", New DataColumn() {ds.Tables(1).Columns(0), ds.Tables(1).Columns(1), ds.Tables(1).Columns(2), ds.Tables(1).Columns(4), ds.Tables(1).Columns(5)}, New DataColumn() {ds.Tables(2).Columns(0), ds.Tables(2).Columns(1), ds.Tables(2).Columns(2), ds.Tables(2).Columns(3), ds.Tables(2).Columns(4)}))
            ElseIf _FilingOption = FilingOptionEnum.Yearly Then
                ds.Relations.Add(New DataRelation("view State Filing Control Portals", New DataColumn() {ds.Tables(1).Columns(0), ds.Tables(1).Columns(1), ds.Tables(1).Columns(9)}, New DataColumn() {ds.Tables(2).Columns(0), ds.Tables(2).Columns(1), ds.Tables(2).Columns(2)}))
            End If
            ds.Relations.Add(New DataRelation("FD_ST_PortalLoginsCoNums", ds.Tables(3).Columns(0), ds.Tables(4).Columns(0)))

            GCOtherStatesFilingCtrl.DataSource = ds.Tables(1)
            GVOtherStatesFilingCtrl.BestFitColumns()

            Dim dtColsCnfg As DataTable = Query($"SELECT * FROM custom.FD_GridControlColumns WHERE Src = '{strCnfgTblSrc}' AND Level = 1 Order By VisibleIndex")
            GVOtherStatesFilingCnfg.Columns.Clear()
            GVOtherStatesFilingCnfg.SetCols(dtColsCnfg)

            GCOtherStatesFilingCnfg.DataSource = ds.Tables(0)
            GVOtherStatesFilingCnfg.BestFitColumns()

            Dim dtColsPL As DataTable = Query($"SELECT * FROM custom.FD_GridControlColumns WHERE Src = 'FD_ST_PortalLogins' AND Level = 1 Order By VisibleIndex")
            GVPortalLogins.Columns.Clear()
            GVPortalLogins.SetCols(dtColsPL)
            GCPortalLogins.DataSource = ds.Tables(3)
            GVPortalLogins.BestFitColumns()

            Dim dtColsPL2 As DataTable = Query($"SELECT * FROM custom.FD_GridControlColumns WHERE Src = 'FD_ST_PortalLogins' AND Level = 2 Order By VisibleIndex")
            GridView2.Columns.Clear()
            GridView2.SetCols(dtColsPL2)

            For Each Col In GVOtherStatesFilingCtrl.Columns
                Dim rowFound = dtColsOsfc.Select("FieldName = '" + Col.FieldName + "'")
                GC.Collect()

                If rowFound.Count = 1 Then
                    If rowFound(0)("AutoWidth") = False Then
                        Col.Width = rowFound(0)("Width")
                    End If
                End If
            Next

            For Each Col In GVOtherStatesFilingCnfg.Columns
                Dim rowFound = dtColsCnfg.Select("FieldName = '" + Col.FieldName + "'")
                GC.Collect()

                If rowFound.Count = 1 Then
                    If rowFound(0)("AutoWidth") = False Then
                        Col.Width = rowFound(0)("Width")
                    End If
                End If
            Next

            For Each Col In GVPortalLogins.Columns
                Dim rowFound = dtColsPL.Select("FieldName = '" + Col.FieldName + "'")
                GC.Collect()

                If rowFound.Count = 1 Then
                    If rowFound(0)("AutoWidth") = False Then
                        Col.Width = rowFound(0)("Width")
                    End If
                End If
            Next
            RepositoryItemLookUpEditCoNum.DataSource = (From c In DB.view_CompanySumarries Select New With {.Co = c.CONUM.ToString() + " - " + c.CO_NAME, .CoNum = c.CONUM}).ToList()
            Return True
        Catch ex As Exception
            DisplayErrorMessage("Error in load data", ex)
            Return False
        End Try
    End Function

    Private Sub frm_OT_ST_FilingCtrl_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            DB = New dbEPDataDataContext(GetConnectionString())

            Dim Y = If(Today.Month = 12, Today, New Date(Today.Year - 1, 12, 1))
            Y = New Date(Y.Year, 12, 1)
            lueYear.Properties.ValueMember = "Yr"
            lueYear.Properties.DisplayMember = "Yr"
            lueYear.Properties.DataSource = Query($"SELECT Yr = {Y.Year} UNION SELECT {Y.AddYears(-1).Year} UNION SELECT {Y.AddYears(1).Year}")
            lueQtr.Properties.ValueMember = "Qtr"
            lueQtr.Properties.DisplayMember = "Qtr"
            lueQtr.Properties.DataSource = Query($"SELECT Qtr = {1} UNION SELECT {2} UNION SELECT {3} UNION SELECT {4}")

            Dim QEDtm As DateTime = Now
            If {3, 6, 9, 12}.ToList().IndexOf(QEDtm.Month) = -1 Then
                QEDtm = QEDtm.AddDays(-QEDtm.Day + 1).AddMonths(-QEDtm.Month Mod 3)
            End If

            lueYear.EditValue = QEDtm.Year
            lueQtr.EditValue = QEDtm.Quarter

            Dim viewLayoutSetting As List(Of String) = Nothing
            If _FilingOption = FilingOptionEnum.Qtr Then
                viewLayoutSetting = Query(Of String)("SELECT DISTINCT SortGroup FROM custom.FD_GridControlColumnsSortOver WHERE Src = 'OtherStatesFilingControl'").ToList()
            Else
                viewLayoutSetting = Query(Of String)("SELECT DISTINCT SortGroup FROM custom.FD_GridControlColumnsSortOver WHERE Src = 'OtherStatesFilingControlYtd'").ToList()
            End If

            If viewLayoutSetting.Count <> 0 Then
                viewLayoutSetting.Insert(0, "Regular")

                Dim ric As New Repository.RepositoryItemComboBox()
                ric.NullText = "Choose"
                ric.TextEditStyle = TextEditStyles.DisableTextEditor

                BarEditItemViewEdit.Edit = ric
                BarEditItemViewEdit.EditValue = "Regular"
                AddHandler ric.EditValueChanging, AddressOf RepositoryItemComboBox_EditValueChanging
                AddHandler ric.EditValueChanged, AddressOf RepositoryItemComboBox_EditValueChanged

                For Each v In viewLayoutSetting
                    ric.Items.Add(v)
                Next
            Else
                BarEditItemViewEdit.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
            End If

            _isLoaded = True
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in load form", ex)
        End Try
    End Sub

    Sub RepositoryItemComboBox_EditValueChanged(sender As Object, e As EventArgs)
        Try
            Cursor.Current = Cursors.WaitCursor
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in RepositoryItemComboBox_EditValueChanged", ex)
        Finally
            Cursor.Current = Cursors.Default
        End Try
    End Sub

    Sub RepositoryItemComboBox_EditValueChanging(sender As Object, e As ChangingEventArgs)
        If Not CanRefreshData(False) Then
            XtraMessageBox.Show("Have uncommitted changes. Save or refresh first.", "Cannot switch")
            e.Cancel = True
        Else
            currentViewMode = e.NewValue
        End If
    End Sub


    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click

    End Sub
    Private Sub btnSaveAndExit_Click(sender As Object, e As EventArgs) Handles btnSaveAndExit.Click

    End Sub
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Try
            Close()
        Catch ex As Exception
            DisplayErrorMessage("Error in close", ex)
        End Try
    End Sub

    Private Sub btnRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        Try
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error refreshing", ex)
        End Try
    End Sub

    Private Sub GVPortalLogins_MasterRowGetRelationDisplayCaption(sender As Object, e As DevExpress.XtraGrid.Views.Grid.MasterRowGetRelationNameEventArgs) Handles GVPortalLogins.MasterRowGetRelationDisplayCaption
        'this will just affect the caption
        e.RelationName = "Client Listing"
    End Sub

    Private Sub GVPortalLogins_MasterRowExpanded(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomMasterRowEventArgs) Handles GVPortalLogins.MasterRowExpanded
        Dim detailGC As DevExpress.XtraGrid.Views.Grid.GridView = GVPortalLogins.GetDetailView(e.RowHandle, 0)
        detailGC.BestFitColumns()
        If detailGC.Columns.Count = 1 AndAlso detailGC.Columns(0).Width < 150 Then
            detailGC.Columns(0).Width = 150
        End If
    End Sub

    Private Sub GVPortalLogins_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GVPortalLogins.PopupMenuShowing
        Try
            Dim row = GVPortalLogins.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit", Sub() EditRow(row), My.Resources.edit_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(e.HitInfo.RowHandle, GVPortalLogins), My.Resources.delete_16x16))
        Catch ex As Exception
            DisplayErrorMessage("Error in GVPortalLogins_PopupMenuShowing", ex)
        End Try
    End Sub

    Private Sub GVOtherStatesFilingCtrl_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GVOtherStatesFilingCtrl.PopupMenuShowing
        Try
            Dim row = GVOtherStatesFilingCtrl.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(e.HitInfo.RowHandle, GVOtherStatesFilingCtrl), My.Resources.delete_16x16))
        Catch ex As Exception
            DisplayErrorMessage("Error in GVOtherStatesFilingCtrl_PopupMenuShowing", ex)
        End Try
    End Sub

    Private Sub GridView2_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView2.PopupMenuShowing
        Try
            If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
                Dim detailView As GridView = sender
                Dim row = detailView.GetFocusedRow()

                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(detailView.FocusedRowHandle, GridView2, row), My.Resources.delete_16x16))
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in popup menu showing", ex)
        End Try
    End Sub

    Private Sub DeleteRow(RowHandle As Int32, GV As GridView, Optional row As Object = Nothing)
        If XtraMessageBox.Show($"Are you sure you would like to delete row?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If
        Try
            If GV.Equals(GVPortalLogins) Then
                GVPortalLogins.DeleteRow(RowHandle)
            ElseIf GV.Equals(GridView2) Then
                Dim DataRow As System.Data.DataRowView = row
                'must delete from data table
                Dim OtherStatesFilingCnfgTable As DataTable = GCOtherStatesFilingCnfg.DataSource
                Dim ds = OtherStatesFilingCnfgTable.DataSet
                Dim portalLoginConumTable = ds.Tables(4)
                Dim rowToDelete = portalLoginConumTable.Select("PortalId = " + DataRow("PortalID").ToString() + " AND CoNum = " + DataRow("CoNum").ToString()).FirstOrDefault
                If rowToDelete IsNot Nothing Then
                    rowToDelete.Delete()
                End If
            ElseIf GV.Equals(GVOtherStatesFilingCtrl) Then
                GVOtherStatesFilingCtrl.DeleteRow(RowHandle)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error when deleting row", ex)
        End Try
    End Sub

    Private Sub EditRow(row As Object)
        Try
            GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Portal").FirstOrDefault().OptionsColumn.ReadOnly = False
            GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Portal").FirstOrDefault().ColumnEdit = Nothing
            GVPortalLogins.Columns.Where(Function(c) c.FieldName = "UserName").FirstOrDefault().OptionsColumn.ReadOnly = False
            GVPortalLogins.Columns.Where(Function(c) c.FieldName = "PW").FirstOrDefault().OptionsColumn.ReadOnly = False
            GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Security").FirstOrDefault().OptionsColumn.ReadOnly = False
            GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Answer").FirstOrDefault().OptionsColumn.ReadOnly = False
        Catch ex As Exception
            DisplayErrorMessage("Error when Editing row", ex)
        End Try
    End Sub

    Private Sub lueYear_EditValueChanged(sender As Object, e As EventArgs) Handles lueYear.EditValueChanged
        If _isLoaded Then
            LoadData()
        End If
    End Sub

    Private Sub lueQtr_EditValueChanged(sender As Object, e As EventArgs) Handles lueQtr.EditValueChanged
        If _isLoaded Then
            LoadData()
        End If
    End Sub

    Private Sub GVPortalLogins_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles GVPortalLogins.FocusedRowChanged
        Try
            If e.FocusedRowHandle = DevExpress.XtraGrid.GridControl.NewItemRowHandle Then
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Portal").FirstOrDefault().OptionsColumn.ReadOnly = False
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Portal").FirstOrDefault().ColumnEdit = Nothing
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "UserName").FirstOrDefault().OptionsColumn.ReadOnly = False
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "PW").FirstOrDefault().OptionsColumn.ReadOnly = False
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Security").FirstOrDefault().OptionsColumn.ReadOnly = False
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Answer").FirstOrDefault().OptionsColumn.ReadOnly = False
            Else
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Portal").FirstOrDefault().OptionsColumn.ReadOnly = True
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "UserName").FirstOrDefault().OptionsColumn.ReadOnly = True
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "PW").FirstOrDefault().OptionsColumn.ReadOnly = True
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Security").FirstOrDefault().OptionsColumn.ReadOnly = True
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Answer").FirstOrDefault().OptionsColumn.ReadOnly = True
                Dim colRepDE = New DevExpress.XtraEditors.Repository.RepositoryItemHyperLinkEdit
                colRepDE.SingleClick = True
                GVPortalLogins.Columns.Where(Function(c) c.FieldName = "Portal").FirstOrDefault().ColumnEdit = colRepDE
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in GVPortalLogins_FocusedRowChanged", ex)
        End Try
    End Sub

    Private Sub GVOtherStatesFilingCtrl_MasterRowExpanded(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomMasterRowEventArgs) Handles GVOtherStatesFilingCtrl.MasterRowExpanded
        Try
            Dim colsToShow = New List(Of String)(New String() {"TAXTYPE", "UI", "WH", "FILING", "PAYMENT", "DS", "LC", "W2", "1099", "PORTAL", "TPA", "USERNAME", "PW", "SECURITY", "ANSWER"})
            Dim detailGC As DevExpress.XtraGrid.Views.Grid.GridView = GVOtherStatesFilingCtrl.GetDetailView(e.RowHandle, 0)
            detailGC.OptionsBehavior.ReadOnly = True
            Dim x As Int16
            For x = 0 To detailGC.Columns.Count - 1
                If colsToShow.IndexOf(detailGC.Columns(x).FieldName.ToString.ToUpper) > -1 Then
                    detailGC.Columns(x).Visible = True

                    If detailGC.Columns(x).FieldName.ToUpper = "PORTAL" Then
                        Dim colRepDE = New DevExpress.XtraEditors.Repository.RepositoryItemHyperLinkEdit
                        colRepDE.SingleClick = True
                        detailGC.Columns(x).ColumnEdit = colRepDE
                    End If
                Else
                    detailGC.Columns(x).Visible = False
                End If
            Next
            detailGC.OptionsView.NewItemRowPosition = NewItemRowPosition.None
            detailGC.OptionsView.ShowAutoFilterRow = False
            detailGC.OptionsView.ShowGroupPanel = False
            detailGC.BestFitColumns()
        Catch ex As Exception

        End Try
    End Sub

    Private Async Sub hllcInsertOtherScrFilCtrlScript_Click(sender As Object, e As EventArgs) Handles hllcInsertOtherScrFilCtrlScript.Click
        Dim strParams As String = ""

        Dim script = DB.SqlScripts.Where(Function(s) s.ID = SqlScriptID).FirstOrDefault()
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, script)

        If _FilingOption = FilingOptionEnum.Qtr Then
            Dim QtrEndDate = New Date(lueYear.EditValue, 1, 1).AddMonths(lueQtr.EditValue * 3).AddDays(-1)
            strParams = $"@QtrEndDate = {QtrEndDate},DeleteAndRedoAll=NO"
            script.ParametersJson = Query(Of String)($"DECLARE @json NVARCHAR(MAX)
SELECT @json = ss.ParametersJson FROM custom.SqlScripts ss WHERE ss.ID = {SqlScriptID}
SET @json = JSON_MODIFY(@json, '$[0].Value', '{QtrEndDate}')
SET @json = JSON_MODIFY(@json, '$[1].Value', 'No')
SELECT @json
").First
        ElseIf _FilingOption = FilingOptionEnum.Yearly Then
            strParams = $"@Year = {lueYear.EditValue}, @DeleteAndRedoAll = NO"
            script.ParametersJson = Query(Of String)($"DECLARE @json NVARCHAR(MAX)
SELECT @json = ss.ParametersJson FROM custom.SqlScripts ss WHERE ss.ID = {SqlScriptID}
SET @json = JSON_MODIFY(@json, '$[0].Value', '{lueYear.EditValue}')
SET @json = JSON_MODIFY(@json, '$[1].Value', 'No')
SELECT @json
").First
        End If

        script.Parameters = strParams

        DB.SaveChanges()
        Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
        If sqlFrm IsNot Nothing Then
            frmMain.CloseForm(sqlFrm)
        End If
        frmMain.AddOrActivateForm(Of frmSqlScripts)()
        sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
        sqlFrm.FindAndFocusRecord(SqlScriptID)
        Await sqlFrm.ExecuteClick(SqlScriptID)
    End Sub

    Private Sub GVOtherStatesFilingCtrl_FocusedRowChanged(sender As Object, e As FocusedRowChangedEventArgs) Handles GVPortalLogins.FocusedRowChanged, GVOtherStatesFilingCtrlPortal.FocusedRowChanged, GVOtherStatesFilingCtrl.FocusedRowChanged, GVOtherStatesFilingCnfg.FocusedRowChanged, GridView2.FocusedRowChanged
        Dim view As GridView = sender
        If e.FocusedRowHandle = DevExpress.XtraGrid.GridControl.NewItemRowHandle Then
            view.OptionsBehavior.Editable = True
        Else
            view.OptionsBehavior.Editable = BarToggleSwitchItemEditMode.Checked
        End If
    End Sub

    Private Sub GVOtherStatesFilingCtrl_DoubleClick(sender As Object, e As EventArgs) Handles GVPortalLogins.DoubleClick, GVOtherStatesFilingCtrlPortal.DoubleClick, GVOtherStatesFilingCtrl.DoubleClick, GVOtherStatesFilingCnfg.DoubleClick, GridView2.DoubleClick
        Dim view As GridView = sender
        view.OptionsBehavior.Editable = True
    End Sub

    Private Sub BarToggleSwitchItemEditMode_CheckedChanged(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarToggleSwitchItemEditMode.CheckedChanged
        BarToggleSwitchItemEditMode.Caption = If(BarToggleSwitchItemEditMode.Checked, "Edit Mode", "View Mode")
        GVOtherStatesFilingCtrl.OptionsBehavior.Editable = BarToggleSwitchItemEditMode.Checked
        GVOtherStatesFilingCnfg.OptionsBehavior.Editable = BarToggleSwitchItemEditMode.Checked
        GVOtherStatesFilingCtrlPortal.OptionsBehavior.Editable = BarToggleSwitchItemEditMode.Checked
        GVPortalLogins.OptionsBehavior.Editable = BarToggleSwitchItemEditMode.Checked
        GridView2.OptionsBehavior.Editable = BarToggleSwitchItemEditMode.Checked
    End Sub

    Private Sub GVOtherStatesFilingCtrl_MouseDown(sender As Object, e As MouseEventArgs) Handles GVOtherStatesFilingCtrl.MouseDown
        Dim hitInfo = GVOtherStatesFilingCtrl.CalcHitInfo(e.Location)
        If hitInfo.InRow AndAlso hitInfo.Column IsNot Nothing AndAlso (hitInfo.Column.FieldName = "ProcessFolder" OrElse hitInfo.Column.FieldName = "FilesFolder") Then
            _clickedRowHandle = hitInfo.RowHandle
            _FocusedColumn = hitInfo.Column
            StateButtonClick(Nothing, Nothing)
        End If
    End Sub

    Private Sub bbiCancel_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiCancel.ItemClick
        Try
            Close()
        Catch ex As Exception
            DisplayErrorMessage("Error in bbiCancel_ItemClick", ex)
        End Try
    End Sub

    Private Sub bbiSave_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSave.ItemClick
        Try
            Dim dt As DataTable = GCOtherStatesFilingCtrl.DataSource
            Dim ds As DataSet = dt.DataSet

            For Each t In ds.Tables
                If t.GetChanges() IsNot Nothing AndAlso t.GetChanges.Rows.Count <> 0 Then
                    Dim tblName = CType(t, DataTable).TableName
                    Dim Adapter As SqlDataAdapter = Nothing

                    Select Case tblName
                        Case "OtherStatesFilingConfig", "OtherStatesFilingConfigYtd"
                            Adapter = adapterOtherStatesFilingConfig
                        Case "OtherStatesFilingControl", "OtherStatesFilingControlYtd"
                            Adapter = adapterOtherStatesFilingControl
                        Case "FD_ST_PortalLogins"
                            Adapter = adapterFD_ST_PortalLogins
                        Case "FD_ST_PortalLoginsCoNum"
                            Adapter = adapterFD_ST_PortalLoginsCoNum
                    End Select
                    Adapter.Update(t)
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error in bbiSave_ItemClick", ex)
        End Try
    End Sub

    Private Sub bbiSaveAndExit_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSaveAndExit.ItemClick
        Try
            bbiSave.PerformClick()
            Close()
        Catch ex As Exception
            DisplayErrorMessage("Error saving tax sheet", ex)
        End Try
    End Sub

    Private Async Sub hllcUpdateNsf_Click(sender As Object, e As EventArgs) Handles hllcUpdateNsf.Click
        Dim strParams As String = ""

        Dim script = DB.SqlScripts.Where(Function(s) s.ID = SqlScriptUpdateID).FirstOrDefault()
        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, script)

        Dim QtrEndDate = New Date(lueYear.EditValue, 1, 1).AddMonths(lueQtr.EditValue * 3).AddDays(-1)
        strParams = $"@QtrEndDate = {QtrEndDate}"
        script.ParametersJson = Query(Of String)($"DECLARE @json NVARCHAR(MAX)
SELECT @json = ss.ParametersJson FROM custom.SqlScripts ss WHERE ss.ID = {SqlScriptUpdateID}
SET @json = JSON_MODIFY(@json, '$[0].Value', '{QtrEndDate}')
SELECT @json
").First

        script.Parameters = strParams

        DB.SaveChanges()
        Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
        If sqlFrm IsNot Nothing Then
            frmMain.CloseForm(sqlFrm)
        End If
        frmMain.AddOrActivateForm(Of frmSqlScripts)()
        sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
        sqlFrm.FindAndFocusRecord(SqlScriptUpdateID)
        Await sqlFrm.ExecuteClick(SqlScriptUpdateID)
    End Sub
End Class