﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmScorecard
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.SpreadsheetControl1 = New DevExpress.XtraSpreadsheet.SpreadsheetControl()
        Me.BarManager1 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.CommonBar1 = New DevExpress.XtraSpreadsheet.UI.CommonBar()
        Me.SpreadsheetCommandBarButtonItem3 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem4 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem5 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem6 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem7 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem8 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem9 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SortAndFilterBar1 = New DevExpress.XtraSpreadsheet.UI.SortAndFilterBar()
        Me.SpreadsheetCommandBarButtonItem12 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem13 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarCheckItem1 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarCheckItem()
        Me.SpreadsheetCommandBarButtonItem14 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem15 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.barDockControlTop = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlBottom = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlLeft = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlRight = New DevExpress.XtraBars.BarDockControl()
        Me.SpreadsheetCommandBarButtonItem1 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem2 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem10 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem11 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarSubItem1 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarSubItem()
        Me.SpreadsheetCommandBarButtonItem16 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem17 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem18 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarSubItem2 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarSubItem()
        Me.SpreadsheetCommandBarButtonItem19 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem20 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarSubItem3 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarSubItem()
        Me.SpreadsheetCommandBarButtonItem21 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem22 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem23 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem24 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.SpreadsheetCommandBarButtonItem25 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem()
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl()
        Me.btnAgents = New DevExpress.XtraEditors.SimpleButton()
        Me.lblDateRange = New DevExpress.XtraEditors.LabelControl()
        Me.lueDateRange = New DevExpress.XtraEditors.LookUpEdit()
        Me.lblDept = New DevExpress.XtraEditors.LabelControl()
        Me.ccbeDept = New DevExpress.XtraEditors.CheckedComboBoxEdit()
        Me.lueMatrix = New DevExpress.XtraEditors.LookUpEdit()
        Me.lblMatrix = New DevExpress.XtraEditors.LabelControl()
        Me.lblDateTo = New DevExpress.XtraEditors.LabelControl()
        Me.lblDateFrom = New DevExpress.XtraEditors.LabelControl()
        Me.deTo = New DevExpress.XtraEditors.DateEdit()
        Me.deFrom = New DevExpress.XtraEditors.DateEdit()
        Me.btnSearch = New DevExpress.XtraEditors.SimpleButton()
        Me.lblAgent = New DevExpress.XtraEditors.LabelControl()
        Me.ccbeAgents = New DevExpress.XtraEditors.CheckedComboBoxEdit()
        Me.SpreadsheetBarController1 = New DevExpress.XtraSpreadsheet.UI.SpreadsheetBarController(Me.components)
        Me.btnUsers = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.lueDateRange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ccbeDept.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lueMatrix.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deTo.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deFrom.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.deFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ccbeAgents.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SpreadsheetBarController1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SpreadsheetControl1
        '
        Me.SpreadsheetControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SpreadsheetControl1.Location = New System.Drawing.Point(0, 73)
        Me.SpreadsheetControl1.MenuManager = Me.BarManager1
        Me.SpreadsheetControl1.Name = "SpreadsheetControl1"
        Me.SpreadsheetControl1.Size = New System.Drawing.Size(1653, 423)
        Me.SpreadsheetControl1.TabIndex = 0
        Me.SpreadsheetControl1.Text = "SpreadsheetControl1"
        Me.SpreadsheetControl1.Visible = False
        '
        'BarManager1
        '
        Me.BarManager1.Bars.AddRange(New DevExpress.XtraBars.Bar() {Me.CommonBar1, Me.SortAndFilterBar1})
        Me.BarManager1.DockControls.Add(Me.barDockControlTop)
        Me.BarManager1.DockControls.Add(Me.barDockControlBottom)
        Me.BarManager1.DockControls.Add(Me.barDockControlLeft)
        Me.BarManager1.DockControls.Add(Me.barDockControlRight)
        Me.BarManager1.Form = Me
        Me.BarManager1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.SpreadsheetCommandBarButtonItem1, Me.SpreadsheetCommandBarButtonItem2, Me.SpreadsheetCommandBarButtonItem3, Me.SpreadsheetCommandBarButtonItem4, Me.SpreadsheetCommandBarButtonItem5, Me.SpreadsheetCommandBarButtonItem6, Me.SpreadsheetCommandBarButtonItem7, Me.SpreadsheetCommandBarButtonItem8, Me.SpreadsheetCommandBarButtonItem9, Me.SpreadsheetCommandBarButtonItem10, Me.SpreadsheetCommandBarButtonItem11, Me.SpreadsheetCommandBarButtonItem12, Me.SpreadsheetCommandBarButtonItem13, Me.SpreadsheetCommandBarCheckItem1, Me.SpreadsheetCommandBarButtonItem14, Me.SpreadsheetCommandBarButtonItem15, Me.SpreadsheetCommandBarSubItem1, Me.SpreadsheetCommandBarButtonItem16, Me.SpreadsheetCommandBarButtonItem17, Me.SpreadsheetCommandBarButtonItem18, Me.SpreadsheetCommandBarSubItem2, Me.SpreadsheetCommandBarButtonItem19, Me.SpreadsheetCommandBarButtonItem20, Me.SpreadsheetCommandBarSubItem3, Me.SpreadsheetCommandBarButtonItem21, Me.SpreadsheetCommandBarButtonItem22, Me.SpreadsheetCommandBarButtonItem23, Me.SpreadsheetCommandBarButtonItem24, Me.SpreadsheetCommandBarButtonItem25})
        Me.BarManager1.MaxItemId = 29
        '
        'CommonBar1
        '
        Me.CommonBar1.Control = Me.SpreadsheetControl1
        Me.CommonBar1.DockCol = 0
        Me.CommonBar1.DockRow = 0
        Me.CommonBar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top
        Me.CommonBar1.FloatLocation = New System.Drawing.Point(78, 160)
        Me.CommonBar1.FloatSize = New System.Drawing.Size(268, 48)
        Me.CommonBar1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem3), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem4), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem5), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem6), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem7), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem8), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem9)})
        Me.CommonBar1.Offset = 52
        '
        'SpreadsheetCommandBarButtonItem3
        '
        Me.SpreadsheetCommandBarButtonItem3.CommandName = "FileSave"
        Me.SpreadsheetCommandBarButtonItem3.Id = 2
        Me.SpreadsheetCommandBarButtonItem3.Name = "SpreadsheetCommandBarButtonItem3"
        '
        'SpreadsheetCommandBarButtonItem4
        '
        Me.SpreadsheetCommandBarButtonItem4.CommandName = "FileSaveAs"
        Me.SpreadsheetCommandBarButtonItem4.Id = 3
        Me.SpreadsheetCommandBarButtonItem4.Name = "SpreadsheetCommandBarButtonItem4"
        '
        'SpreadsheetCommandBarButtonItem5
        '
        Me.SpreadsheetCommandBarButtonItem5.CommandName = "FileQuickPrint"
        Me.SpreadsheetCommandBarButtonItem5.Id = 4
        Me.SpreadsheetCommandBarButtonItem5.Name = "SpreadsheetCommandBarButtonItem5"
        '
        'SpreadsheetCommandBarButtonItem6
        '
        Me.SpreadsheetCommandBarButtonItem6.CommandName = "FilePrint"
        Me.SpreadsheetCommandBarButtonItem6.Id = 5
        Me.SpreadsheetCommandBarButtonItem6.Name = "SpreadsheetCommandBarButtonItem6"
        '
        'SpreadsheetCommandBarButtonItem7
        '
        Me.SpreadsheetCommandBarButtonItem7.CommandName = "FilePrintPreview"
        Me.SpreadsheetCommandBarButtonItem7.Id = 6
        Me.SpreadsheetCommandBarButtonItem7.Name = "SpreadsheetCommandBarButtonItem7"
        '
        'SpreadsheetCommandBarButtonItem8
        '
        Me.SpreadsheetCommandBarButtonItem8.CommandName = "FileUndo"
        Me.SpreadsheetCommandBarButtonItem8.Id = 7
        Me.SpreadsheetCommandBarButtonItem8.Name = "SpreadsheetCommandBarButtonItem8"
        '
        'SpreadsheetCommandBarButtonItem9
        '
        Me.SpreadsheetCommandBarButtonItem9.CommandName = "FileRedo"
        Me.SpreadsheetCommandBarButtonItem9.Id = 8
        Me.SpreadsheetCommandBarButtonItem9.Name = "SpreadsheetCommandBarButtonItem9"
        '
        'SortAndFilterBar1
        '
        Me.SortAndFilterBar1.Control = Me.SpreadsheetControl1
        Me.SortAndFilterBar1.DockCol = 1
        Me.SortAndFilterBar1.DockRow = 0
        Me.SortAndFilterBar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top
        Me.SortAndFilterBar1.FloatLocation = New System.Drawing.Point(396, 130)
        Me.SortAndFilterBar1.FloatSize = New System.Drawing.Size(204, 48)
        Me.SortAndFilterBar1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem12), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem13), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarCheckItem1), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem14), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem15)})
        Me.SortAndFilterBar1.Offset = 357
        '
        'SpreadsheetCommandBarButtonItem12
        '
        Me.SpreadsheetCommandBarButtonItem12.CommandName = "DataSortAscending"
        Me.SpreadsheetCommandBarButtonItem12.Id = 11
        Me.SpreadsheetCommandBarButtonItem12.Name = "SpreadsheetCommandBarButtonItem12"
        '
        'SpreadsheetCommandBarButtonItem13
        '
        Me.SpreadsheetCommandBarButtonItem13.CommandName = "DataSortDescending"
        Me.SpreadsheetCommandBarButtonItem13.Id = 12
        Me.SpreadsheetCommandBarButtonItem13.Name = "SpreadsheetCommandBarButtonItem13"
        '
        'SpreadsheetCommandBarCheckItem1
        '
        Me.SpreadsheetCommandBarCheckItem1.CommandName = "DataFilterToggle"
        Me.SpreadsheetCommandBarCheckItem1.Id = 13
        Me.SpreadsheetCommandBarCheckItem1.Name = "SpreadsheetCommandBarCheckItem1"
        '
        'SpreadsheetCommandBarButtonItem14
        '
        Me.SpreadsheetCommandBarButtonItem14.CommandName = "DataFilterClear"
        Me.SpreadsheetCommandBarButtonItem14.Id = 14
        Me.SpreadsheetCommandBarButtonItem14.Name = "SpreadsheetCommandBarButtonItem14"
        '
        'SpreadsheetCommandBarButtonItem15
        '
        Me.SpreadsheetCommandBarButtonItem15.CommandName = "DataFilterReApply"
        Me.SpreadsheetCommandBarButtonItem15.Id = 15
        Me.SpreadsheetCommandBarButtonItem15.Name = "SpreadsheetCommandBarButtonItem15"
        '
        'barDockControlTop
        '
        Me.barDockControlTop.CausesValidation = False
        Me.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.barDockControlTop.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlTop.Manager = Me.BarManager1
        Me.barDockControlTop.Size = New System.Drawing.Size(1653, 28)
        '
        'barDockControlBottom
        '
        Me.barDockControlBottom.CausesValidation = False
        Me.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.barDockControlBottom.Location = New System.Drawing.Point(0, 496)
        Me.barDockControlBottom.Manager = Me.BarManager1
        Me.barDockControlBottom.Size = New System.Drawing.Size(1653, 0)
        '
        'barDockControlLeft
        '
        Me.barDockControlLeft.CausesValidation = False
        Me.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left
        Me.barDockControlLeft.Location = New System.Drawing.Point(0, 28)
        Me.barDockControlLeft.Manager = Me.BarManager1
        Me.barDockControlLeft.Size = New System.Drawing.Size(0, 468)
        '
        'barDockControlRight
        '
        Me.barDockControlRight.CausesValidation = False
        Me.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right
        Me.barDockControlRight.Location = New System.Drawing.Point(1653, 28)
        Me.barDockControlRight.Manager = Me.BarManager1
        Me.barDockControlRight.Size = New System.Drawing.Size(0, 468)
        '
        'SpreadsheetCommandBarButtonItem1
        '
        Me.SpreadsheetCommandBarButtonItem1.CommandName = "FileNew"
        Me.SpreadsheetCommandBarButtonItem1.Id = 0
        Me.SpreadsheetCommandBarButtonItem1.Name = "SpreadsheetCommandBarButtonItem1"
        '
        'SpreadsheetCommandBarButtonItem2
        '
        Me.SpreadsheetCommandBarButtonItem2.CommandName = "FileOpen"
        Me.SpreadsheetCommandBarButtonItem2.Id = 1
        Me.SpreadsheetCommandBarButtonItem2.Name = "SpreadsheetCommandBarButtonItem2"
        '
        'SpreadsheetCommandBarButtonItem10
        '
        Me.SpreadsheetCommandBarButtonItem10.CommandName = "FileEncrypt"
        Me.SpreadsheetCommandBarButtonItem10.Id = 9
        Me.SpreadsheetCommandBarButtonItem10.Name = "SpreadsheetCommandBarButtonItem10"
        '
        'SpreadsheetCommandBarButtonItem11
        '
        Me.SpreadsheetCommandBarButtonItem11.CommandName = "FileShowDocumentProperties"
        Me.SpreadsheetCommandBarButtonItem11.Id = 10
        Me.SpreadsheetCommandBarButtonItem11.Name = "SpreadsheetCommandBarButtonItem11"
        '
        'SpreadsheetCommandBarSubItem1
        '
        Me.SpreadsheetCommandBarSubItem1.CommandName = "DataToolsDataValidationCommandGroup"
        Me.SpreadsheetCommandBarSubItem1.Id = 16
        Me.SpreadsheetCommandBarSubItem1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem16), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem17), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem18)})
        Me.SpreadsheetCommandBarSubItem1.Name = "SpreadsheetCommandBarSubItem1"
        '
        'SpreadsheetCommandBarButtonItem16
        '
        Me.SpreadsheetCommandBarButtonItem16.CommandName = "DataToolsDataValidation"
        Me.SpreadsheetCommandBarButtonItem16.Id = 17
        Me.SpreadsheetCommandBarButtonItem16.Name = "SpreadsheetCommandBarButtonItem16"
        '
        'SpreadsheetCommandBarButtonItem17
        '
        Me.SpreadsheetCommandBarButtonItem17.CommandName = "DataToolsCircleInvalidData"
        Me.SpreadsheetCommandBarButtonItem17.Id = 18
        Me.SpreadsheetCommandBarButtonItem17.Name = "SpreadsheetCommandBarButtonItem17"
        '
        'SpreadsheetCommandBarButtonItem18
        '
        Me.SpreadsheetCommandBarButtonItem18.CommandName = "DataToolsClearValidationCircles"
        Me.SpreadsheetCommandBarButtonItem18.Id = 19
        Me.SpreadsheetCommandBarButtonItem18.Name = "SpreadsheetCommandBarButtonItem18"
        '
        'SpreadsheetCommandBarSubItem2
        '
        Me.SpreadsheetCommandBarSubItem2.CommandName = "OutlineGroupCommandGroup"
        Me.SpreadsheetCommandBarSubItem2.Id = 20
        Me.SpreadsheetCommandBarSubItem2.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem19), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem20)})
        Me.SpreadsheetCommandBarSubItem2.Name = "SpreadsheetCommandBarSubItem2"
        Me.SpreadsheetCommandBarSubItem2.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large
        '
        'SpreadsheetCommandBarButtonItem19
        '
        Me.SpreadsheetCommandBarButtonItem19.CommandName = "GroupOutline"
        Me.SpreadsheetCommandBarButtonItem19.Id = 21
        Me.SpreadsheetCommandBarButtonItem19.Name = "SpreadsheetCommandBarButtonItem19"
        '
        'SpreadsheetCommandBarButtonItem20
        '
        Me.SpreadsheetCommandBarButtonItem20.CommandName = "AutoOutline"
        Me.SpreadsheetCommandBarButtonItem20.Id = 22
        Me.SpreadsheetCommandBarButtonItem20.Name = "SpreadsheetCommandBarButtonItem20"
        '
        'SpreadsheetCommandBarSubItem3
        '
        Me.SpreadsheetCommandBarSubItem3.CommandName = "OutlineUngroupCommandGroup"
        Me.SpreadsheetCommandBarSubItem3.Id = 23
        Me.SpreadsheetCommandBarSubItem3.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem21), New DevExpress.XtraBars.LinkPersistInfo(Me.SpreadsheetCommandBarButtonItem22)})
        Me.SpreadsheetCommandBarSubItem3.Name = "SpreadsheetCommandBarSubItem3"
        Me.SpreadsheetCommandBarSubItem3.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large
        '
        'SpreadsheetCommandBarButtonItem21
        '
        Me.SpreadsheetCommandBarButtonItem21.CommandName = "UngroupOutline"
        Me.SpreadsheetCommandBarButtonItem21.Id = 24
        Me.SpreadsheetCommandBarButtonItem21.Name = "SpreadsheetCommandBarButtonItem21"
        '
        'SpreadsheetCommandBarButtonItem22
        '
        Me.SpreadsheetCommandBarButtonItem22.CommandName = "ClearOutline"
        Me.SpreadsheetCommandBarButtonItem22.Id = 25
        Me.SpreadsheetCommandBarButtonItem22.Name = "SpreadsheetCommandBarButtonItem22"
        '
        'SpreadsheetCommandBarButtonItem23
        '
        Me.SpreadsheetCommandBarButtonItem23.CommandName = "Subtotal"
        Me.SpreadsheetCommandBarButtonItem23.Id = 26
        Me.SpreadsheetCommandBarButtonItem23.Name = "SpreadsheetCommandBarButtonItem23"
        Me.SpreadsheetCommandBarButtonItem23.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large
        '
        'SpreadsheetCommandBarButtonItem24
        '
        Me.SpreadsheetCommandBarButtonItem24.CommandName = "ShowDetail"
        Me.SpreadsheetCommandBarButtonItem24.Id = 27
        Me.SpreadsheetCommandBarButtonItem24.Name = "SpreadsheetCommandBarButtonItem24"
        Me.SpreadsheetCommandBarButtonItem24.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText
        '
        'SpreadsheetCommandBarButtonItem25
        '
        Me.SpreadsheetCommandBarButtonItem25.CommandName = "HideDetail"
        Me.SpreadsheetCommandBarButtonItem25.Id = 28
        Me.SpreadsheetCommandBarButtonItem25.Name = "SpreadsheetCommandBarButtonItem25"
        Me.SpreadsheetCommandBarButtonItem25.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText
        '
        'PanelControl1
        '
        Me.PanelControl1.Controls.Add(Me.btnUsers)
        Me.PanelControl1.Controls.Add(Me.btnAgents)
        Me.PanelControl1.Controls.Add(Me.lblDateRange)
        Me.PanelControl1.Controls.Add(Me.lueDateRange)
        Me.PanelControl1.Controls.Add(Me.lblDept)
        Me.PanelControl1.Controls.Add(Me.ccbeDept)
        Me.PanelControl1.Controls.Add(Me.lueMatrix)
        Me.PanelControl1.Controls.Add(Me.lblMatrix)
        Me.PanelControl1.Controls.Add(Me.lblDateTo)
        Me.PanelControl1.Controls.Add(Me.lblDateFrom)
        Me.PanelControl1.Controls.Add(Me.deTo)
        Me.PanelControl1.Controls.Add(Me.deFrom)
        Me.PanelControl1.Controls.Add(Me.btnSearch)
        Me.PanelControl1.Controls.Add(Me.lblAgent)
        Me.PanelControl1.Controls.Add(Me.ccbeAgents)
        Me.PanelControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelControl1.Location = New System.Drawing.Point(0, 28)
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(1653, 45)
        Me.PanelControl1.TabIndex = 1
        '
        'btnAgents
        '
        Me.btnAgents.Location = New System.Drawing.Point(1393, 10)
        Me.btnAgents.Name = "btnAgents"
        Me.btnAgents.Size = New System.Drawing.Size(75, 23)
        Me.btnAgents.TabIndex = 24
        Me.btnAgents.Text = "Agents"
        '
        'lblDateRange
        '
        Me.lblDateRange.Location = New System.Drawing.Point(372, 16)
        Me.lblDateRange.Name = "lblDateRange"
        Me.lblDateRange.Size = New System.Drawing.Size(31, 13)
        Me.lblDateRange.TabIndex = 23
        Me.lblDateRange.Text = "Range"
        '
        'lueDateRange
        '
        Me.lueDateRange.Location = New System.Drawing.Point(422, 12)
        Me.lueDateRange.Name = "lueDateRange"
        Me.lueDateRange.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueDateRange.Size = New System.Drawing.Size(100, 20)
        Me.lueDateRange.TabIndex = 22
        '
        'lblDept
        '
        Me.lblDept.Location = New System.Drawing.Point(12, 16)
        Me.lblDept.Name = "lblDept"
        Me.lblDept.Size = New System.Drawing.Size(23, 13)
        Me.lblDept.TabIndex = 21
        Me.lblDept.Text = "Dept"
        '
        'ccbeDept
        '
        Me.ccbeDept.EditValue = ""
        Me.ccbeDept.Location = New System.Drawing.Point(54, 12)
        Me.ccbeDept.Name = "ccbeDept"
        Me.ccbeDept.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ccbeDept.Size = New System.Drawing.Size(116, 20)
        Me.ccbeDept.TabIndex = 20
        '
        'lueMatrix
        '
        Me.lueMatrix.Location = New System.Drawing.Point(902, 12)
        Me.lueMatrix.Name = "lueMatrix"
        Me.lueMatrix.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.lueMatrix.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("MeasureName", "Matrix", 49, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.lueMatrix.Properties.DisplayMember = "MeasureName"
        Me.lueMatrix.Properties.DropDownRows = 10
        Me.lueMatrix.Properties.NullText = ""
        Me.lueMatrix.Properties.ValueMember = "MeasureId"
        Me.lueMatrix.Size = New System.Drawing.Size(206, 20)
        Me.lueMatrix.TabIndex = 18
        '
        'lblMatrix
        '
        Me.lblMatrix.Location = New System.Drawing.Point(853, 16)
        Me.lblMatrix.Name = "lblMatrix"
        Me.lblMatrix.Size = New System.Drawing.Size(30, 13)
        Me.lblMatrix.TabIndex = 17
        Me.lblMatrix.Text = "Matrix"
        '
        'lblDateTo
        '
        Me.lblDateTo.Location = New System.Drawing.Point(703, 16)
        Me.lblDateTo.Name = "lblDateTo"
        Me.lblDateTo.Size = New System.Drawing.Size(12, 13)
        Me.lblDateTo.TabIndex = 16
        Me.lblDateTo.Text = "To"
        '
        'lblDateFrom
        '
        Me.lblDateFrom.Location = New System.Drawing.Point(541, 16)
        Me.lblDateFrom.Name = "lblDateFrom"
        Me.lblDateFrom.Size = New System.Drawing.Size(24, 13)
        Me.lblDateFrom.TabIndex = 15
        Me.lblDateFrom.Text = "From"
        '
        'deTo
        '
        Me.deTo.EditValue = Nothing
        Me.deTo.Location = New System.Drawing.Point(734, 12)
        Me.deTo.Name = "deTo"
        Me.deTo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deTo.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deTo.Size = New System.Drawing.Size(100, 20)
        Me.deTo.TabIndex = 14
        '
        'deFrom
        '
        Me.deFrom.EditValue = Nothing
        Me.deFrom.Location = New System.Drawing.Point(584, 12)
        Me.deFrom.Name = "deFrom"
        Me.deFrom.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deFrom.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.deFrom.Size = New System.Drawing.Size(100, 20)
        Me.deFrom.TabIndex = 13
        '
        'btnSearch
        '
        Me.btnSearch.Location = New System.Drawing.Point(1163, 11)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Size = New System.Drawing.Size(75, 23)
        Me.btnSearch.TabIndex = 12
        Me.btnSearch.Text = "&Search"
        '
        'lblAgent
        '
        Me.lblAgent.Location = New System.Drawing.Point(189, 16)
        Me.lblAgent.Name = "lblAgent"
        Me.lblAgent.Size = New System.Drawing.Size(29, 13)
        Me.lblAgent.TabIndex = 0
        Me.lblAgent.Text = "Agent"
        '
        'ccbeAgents
        '
        Me.ccbeAgents.Location = New System.Drawing.Point(237, 12)
        Me.ccbeAgents.Name = "ccbeAgents"
        Me.ccbeAgents.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ccbeAgents.Size = New System.Drawing.Size(116, 20)
        Me.ccbeAgents.TabIndex = 19
        '
        'SpreadsheetBarController1
        '
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem1)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem2)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem3)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem4)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem5)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem6)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem7)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem8)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem9)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem10)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem11)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem12)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem13)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarCheckItem1)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem14)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem15)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem16)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem17)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem18)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarSubItem1)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem19)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem20)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarSubItem2)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem21)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem22)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarSubItem3)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem23)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem24)
        Me.SpreadsheetBarController1.BarItems.Add(Me.SpreadsheetCommandBarButtonItem25)
        Me.SpreadsheetBarController1.Control = Me.SpreadsheetControl1
        '
        'btnUsers
        '
        Me.btnUsers.Location = New System.Drawing.Point(1488, 10)
        Me.btnUsers.Name = "btnUsers"
        Me.btnUsers.Size = New System.Drawing.Size(103, 23)
        Me.btnUsers.TabIndex = 25
        Me.btnUsers.Text = "User Relationships"
        '
        'frmScorecard
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1653, 496)
        Me.Controls.Add(Me.SpreadsheetControl1)
        Me.Controls.Add(Me.PanelControl1)
        Me.Controls.Add(Me.barDockControlLeft)
        Me.Controls.Add(Me.barDockControlRight)
        Me.Controls.Add(Me.barDockControlBottom)
        Me.Controls.Add(Me.barDockControlTop)
        Me.Name = "frmScorecard"
        Me.Text = "Scorecard"
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        Me.PanelControl1.PerformLayout()
        CType(Me.lueDateRange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ccbeDept.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lueMatrix.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deTo.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deFrom.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.deFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ccbeAgents.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SpreadsheetBarController1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents SpreadsheetControl1 As DevExpress.XtraSpreadsheet.SpreadsheetControl
    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents lblAgent As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnSearch As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents deFrom As DevExpress.XtraEditors.DateEdit
    Friend WithEvents lueMatrix As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents lblMatrix As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblDateTo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lblDateFrom As DevExpress.XtraEditors.LabelControl
    Friend WithEvents deTo As DevExpress.XtraEditors.DateEdit
    Friend WithEvents ccbeAgents As DevExpress.XtraEditors.CheckedComboBoxEdit
    Friend WithEvents ccbeDept As DevExpress.XtraEditors.CheckedComboBoxEdit
    Friend WithEvents lblDept As DevExpress.XtraEditors.LabelControl
    Friend WithEvents lueDateRange As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents BarManager1 As DevExpress.XtraBars.BarManager
    Friend WithEvents CommonBar1 As DevExpress.XtraSpreadsheet.UI.CommonBar
    Friend WithEvents SpreadsheetCommandBarButtonItem1 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem2 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem3 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem4 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem5 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem6 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem7 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem8 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem9 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem10 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem11 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents barDockControlTop As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlBottom As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlLeft As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlRight As DevExpress.XtraBars.BarDockControl
    Friend WithEvents SpreadsheetBarController1 As DevExpress.XtraSpreadsheet.UI.SpreadsheetBarController
    Friend WithEvents SortAndFilterBar1 As DevExpress.XtraSpreadsheet.UI.SortAndFilterBar
    Friend WithEvents SpreadsheetCommandBarButtonItem12 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem13 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarCheckItem1 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarCheckItem
    Friend WithEvents SpreadsheetCommandBarButtonItem14 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem15 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarSubItem1 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarSubItem
    Friend WithEvents SpreadsheetCommandBarButtonItem16 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem17 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem18 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarSubItem2 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarSubItem
    Friend WithEvents SpreadsheetCommandBarButtonItem19 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem20 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarSubItem3 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarSubItem
    Friend WithEvents SpreadsheetCommandBarButtonItem21 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem22 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem23 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem24 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents SpreadsheetCommandBarButtonItem25 As DevExpress.XtraSpreadsheet.UI.SpreadsheetCommandBarButtonItem
    Friend WithEvents lblDateRange As DevExpress.XtraEditors.LabelControl
    Friend WithEvents btnAgents As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnUsers As DevExpress.XtraEditors.SimpleButton
End Class
