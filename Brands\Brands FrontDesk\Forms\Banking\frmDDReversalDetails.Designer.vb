﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDDReversalDetails
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.btnCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.btnUpdateStatus = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.IDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CONUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.EMPNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Check_dateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.CHK_NUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.GROSSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.NETTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CHK_COUNTERTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CM_DIVNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Nacha_DIVNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Nacha_CHK_NUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.EMP_ROUTINGTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.EMP_ACCTNUMTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.AMOUNTTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ACCT_TYPETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TRAN_TYPETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Debit_RoutingTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Debit_AccountTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ER_Tran_TypeTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ER_ACCT_TYPETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ReasonTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.FaultTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Date_RequestedDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.Requested_ByTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.BriefDescTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Reversal_StatusTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.EE_Reversal_Effective_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.EE_Reversal_Process_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.ER_Refund_Effective_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.ER_Refund_Process_DateDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.Banking_Dept_NotesTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.EE_Reversal_NACHA_IDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ER_Refund_NACHA_IDTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Payroll_numTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ItemForID = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCHK_COUNTER = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCM_DIVNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNacha_DIVNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNacha_CHK_NUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForACCT_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForTRAN_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForDebit_Routing = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForDebit_Account = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForER_Tran_Type = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForER_ACCT_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForER_Refund_NACHA_ID = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForCONUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCheck_date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForGROSS = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForEMP_ROUTING = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForAMOUNT = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForFault = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForDate_Requested = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForBriefDesc = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForEE_Reversal_Effective_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForEE_Reversal_Process_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForER_Refund_Effective_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForER_Refund_Process_Date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForBanking_Dept_Notes = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForEE_Reversal_NACHA_ID = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForReversal_Status = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForEMPNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCHK_NUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNET = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForEMP_ACCTNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForReason = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForRequested_By = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForPayroll_num = New DevExpress.XtraLayout.LayoutControlItem()
        Me.dD_Reversal_LogsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CONUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Check_dateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Check_dateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CHK_NUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GROSSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NETTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CHK_COUNTERTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CM_DIVNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Nacha_DIVNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Nacha_CHK_NUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMP_ROUTINGTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EMP_ACCTNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AMOUNTTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TRAN_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Debit_RoutingTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Debit_AccountTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_Tran_TypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ReasonTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FaultTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_RequestedDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Requested_ByTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BriefDescTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Reversal_StatusTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EE_Reversal_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EE_Reversal_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EE_Reversal_Process_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EE_Reversal_Process_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_Refund_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_Refund_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_Refund_Process_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_Refund_Process_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Banking_Dept_NotesTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EE_Reversal_NACHA_IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ER_Refund_NACHA_IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Payroll_numTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForID, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCHK_COUNTER, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCM_DIVNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNacha_DIVNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNacha_CHK_NUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForACCT_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTRAN_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDebit_Routing, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDebit_Account, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForER_Tran_Type, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForER_ACCT_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForER_Refund_NACHA_ID, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCONUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCheck_date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForGROSS, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForEMP_ROUTING, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForAMOUNT, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForFault, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForDate_Requested, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForBriefDesc, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForEE_Reversal_Effective_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForEE_Reversal_Process_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForER_Refund_Effective_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForER_Refund_Process_Date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForBanking_Dept_Notes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForEE_Reversal_NACHA_ID, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForReversal_Status, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForEMPNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCHK_NUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNET, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForEMP_ACCTNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForReason, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForRequested_By, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPayroll_num, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dD_Reversal_LogsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.AllowGeneratingNestedGroups = DevExpress.Utils.DefaultBoolean.[True]
        Me.DataLayoutControl1.Controls.Add(Me.btnCancel)
        Me.DataLayoutControl1.Controls.Add(Me.btnUpdateStatus)
        Me.DataLayoutControl1.Controls.Add(Me.LabelControl1)
        Me.DataLayoutControl1.Controls.Add(Me.IDTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.CONUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.EMPNUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Check_dateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.CHK_NUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.GROSSTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.NETTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.CHK_COUNTERTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.CM_DIVNUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Nacha_DIVNUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Nacha_CHK_NUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.EMP_ROUTINGTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.EMP_ACCTNUMTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.AMOUNTTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ACCT_TYPETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.TRAN_TYPETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Debit_RoutingTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Debit_AccountTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ER_Tran_TypeTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ER_ACCT_TYPETextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ReasonTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.FaultTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Date_RequestedDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Requested_ByTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.BriefDescTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Reversal_StatusTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.EE_Reversal_Effective_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.EE_Reversal_Process_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ER_Refund_Effective_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ER_Refund_Process_DateDateEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Banking_Dept_NotesTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.EE_Reversal_NACHA_IDTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.ER_Refund_NACHA_IDTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Payroll_numTextEdit)
        Me.DataLayoutControl1.DataSource = Me.dD_Reversal_LogsBindingSource
        Me.DataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.DataLayoutControl1.HiddenItems.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForID, Me.ItemForCHK_COUNTER, Me.ItemForCM_DIVNUM, Me.ItemForNacha_DIVNUM, Me.ItemForNacha_CHK_NUM, Me.ItemForACCT_TYPE, Me.ItemForTRAN_TYPE, Me.ItemForDebit_Routing, Me.ItemForDebit_Account, Me.ItemForER_Tran_Type, Me.ItemForER_ACCT_TYPE, Me.ItemForER_Refund_NACHA_ID})
        Me.DataLayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.[True]
        Me.DataLayoutControl1.Root = Me.LayoutControlGroup1
        Me.DataLayoutControl1.Size = New System.Drawing.Size(724, 451)
        Me.DataLayoutControl1.TabIndex = 0
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'btnCancel
        '
        Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.btnCancel.Location = New System.Drawing.Point(12, 417)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(347, 22)
        Me.btnCancel.StyleController = Me.DataLayoutControl1
        Me.btnCancel.TabIndex = 41
        Me.btnCancel.Text = "Cancel"
        '
        'btnUpdateStatus
        '
        Me.btnUpdateStatus.Location = New System.Drawing.Point(363, 417)
        Me.btnUpdateStatus.Name = "btnUpdateStatus"
        Me.btnUpdateStatus.Size = New System.Drawing.Size(349, 22)
        Me.btnUpdateStatus.StyleController = Me.DataLayoutControl1
        Me.btnUpdateStatus.TabIndex = 40
        Me.btnUpdateStatus.Text = "Mark Reversal Status as ""Reversal Failed"""
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Segoe UI Semibold", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseTextOptions = True
        Me.LabelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl1.Location = New System.Drawing.Point(12, 12)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(700, 41)
        Me.LabelControl1.StyleController = Me.DataLayoutControl1
        Me.LabelControl1.TabIndex = 39
        Me.LabelControl1.Text = "This Transaction matches the following DD Reversal Record"
        '
        'IDTextEdit
        '
        Me.IDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ID", True))
        Me.IDTextEdit.Location = New System.Drawing.Point(153, 12)
        Me.IDTextEdit.Name = "IDTextEdit"
        Me.IDTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.IDTextEdit.Properties.Mask.EditMask = "N0"
        Me.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.IDTextEdit.Size = New System.Drawing.Size(530, 20)
        Me.IDTextEdit.StyleController = Me.DataLayoutControl1
        Me.IDTextEdit.TabIndex = 4
        '
        'CONUMTextEdit
        '
        Me.CONUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "CONUM", True))
        Me.CONUMTextEdit.Location = New System.Drawing.Point(153, 81)
        Me.CONUMTextEdit.Name = "CONUMTextEdit"
        Me.CONUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.CONUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.CONUMTextEdit.Properties.Mask.EditMask = "G"
        Me.CONUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CONUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.CONUMTextEdit.Properties.ReadOnly = True
        Me.CONUMTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.CONUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.CONUMTextEdit.TabIndex = 5
        '
        'EMPNUMTextEdit
        '
        Me.EMPNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "EMPNUM", True))
        Me.EMPNUMTextEdit.Location = New System.Drawing.Point(505, 81)
        Me.EMPNUMTextEdit.Name = "EMPNUMTextEdit"
        Me.EMPNUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.EMPNUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.EMPNUMTextEdit.Properties.Mask.EditMask = "G"
        Me.EMPNUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.EMPNUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.EMPNUMTextEdit.Properties.ReadOnly = True
        Me.EMPNUMTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.EMPNUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.EMPNUMTextEdit.TabIndex = 6
        '
        'Check_dateDateEdit
        '
        Me.Check_dateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Check_date", True))
        Me.Check_dateDateEdit.EditValue = Nothing
        Me.Check_dateDateEdit.Location = New System.Drawing.Point(153, 105)
        Me.Check_dateDateEdit.Name = "Check_dateDateEdit"
        Me.Check_dateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Check_dateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Check_dateDateEdit.Properties.ReadOnly = True
        Me.Check_dateDateEdit.Size = New System.Drawing.Size(207, 20)
        Me.Check_dateDateEdit.StyleController = Me.DataLayoutControl1
        Me.Check_dateDateEdit.TabIndex = 8
        '
        'CHK_NUMTextEdit
        '
        Me.CHK_NUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "CHK_NUM", True))
        Me.CHK_NUMTextEdit.Location = New System.Drawing.Point(505, 105)
        Me.CHK_NUMTextEdit.Name = "CHK_NUMTextEdit"
        Me.CHK_NUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.CHK_NUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.CHK_NUMTextEdit.Properties.Mask.EditMask = "G"
        Me.CHK_NUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CHK_NUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.CHK_NUMTextEdit.Properties.ReadOnly = True
        Me.CHK_NUMTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.CHK_NUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.CHK_NUMTextEdit.TabIndex = 9
        '
        'GROSSTextEdit
        '
        Me.GROSSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "GROSS", True))
        Me.GROSSTextEdit.Location = New System.Drawing.Point(153, 129)
        Me.GROSSTextEdit.Name = "GROSSTextEdit"
        Me.GROSSTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.GROSSTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.GROSSTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.GROSSTextEdit.Properties.Mask.EditMask = "G"
        Me.GROSSTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.GROSSTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.GROSSTextEdit.Properties.ReadOnly = True
        Me.GROSSTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.GROSSTextEdit.StyleController = Me.DataLayoutControl1
        Me.GROSSTextEdit.TabIndex = 10
        '
        'NETTextEdit
        '
        Me.NETTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "NET", True))
        Me.NETTextEdit.Location = New System.Drawing.Point(505, 129)
        Me.NETTextEdit.Name = "NETTextEdit"
        Me.NETTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.NETTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.NETTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.NETTextEdit.Properties.Mask.EditMask = "G"
        Me.NETTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.NETTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.NETTextEdit.Properties.ReadOnly = True
        Me.NETTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.NETTextEdit.StyleController = Me.DataLayoutControl1
        Me.NETTextEdit.TabIndex = 11
        '
        'CHK_COUNTERTextEdit
        '
        Me.CHK_COUNTERTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "CHK_COUNTER", True))
        Me.CHK_COUNTERTextEdit.Location = New System.Drawing.Point(153, 180)
        Me.CHK_COUNTERTextEdit.Name = "CHK_COUNTERTextEdit"
        Me.CHK_COUNTERTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.CHK_COUNTERTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.CHK_COUNTERTextEdit.Properties.Mask.EditMask = "G"
        Me.CHK_COUNTERTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CHK_COUNTERTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.CHK_COUNTERTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.CHK_COUNTERTextEdit.StyleController = Me.DataLayoutControl1
        Me.CHK_COUNTERTextEdit.TabIndex = 12
        '
        'CM_DIVNUMTextEdit
        '
        Me.CM_DIVNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "CM_DIVNUM", True))
        Me.CM_DIVNUMTextEdit.Location = New System.Drawing.Point(153, 204)
        Me.CM_DIVNUMTextEdit.Name = "CM_DIVNUMTextEdit"
        Me.CM_DIVNUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.CM_DIVNUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.CM_DIVNUMTextEdit.Properties.Mask.EditMask = "G"
        Me.CM_DIVNUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.CM_DIVNUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.CM_DIVNUMTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.CM_DIVNUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.CM_DIVNUMTextEdit.TabIndex = 13
        '
        'Nacha_DIVNUMTextEdit
        '
        Me.Nacha_DIVNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Nacha_DIVNUM", True))
        Me.Nacha_DIVNUMTextEdit.Location = New System.Drawing.Point(153, 228)
        Me.Nacha_DIVNUMTextEdit.Name = "Nacha_DIVNUMTextEdit"
        Me.Nacha_DIVNUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.Nacha_DIVNUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.Nacha_DIVNUMTextEdit.Properties.Mask.EditMask = "G"
        Me.Nacha_DIVNUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Nacha_DIVNUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Nacha_DIVNUMTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.Nacha_DIVNUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.Nacha_DIVNUMTextEdit.TabIndex = 14
        '
        'Nacha_CHK_NUMTextEdit
        '
        Me.Nacha_CHK_NUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Nacha_CHK_NUM", True))
        Me.Nacha_CHK_NUMTextEdit.Location = New System.Drawing.Point(153, 252)
        Me.Nacha_CHK_NUMTextEdit.Name = "Nacha_CHK_NUMTextEdit"
        Me.Nacha_CHK_NUMTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.Nacha_CHK_NUMTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.Nacha_CHK_NUMTextEdit.Properties.Mask.EditMask = "G"
        Me.Nacha_CHK_NUMTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Nacha_CHK_NUMTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Nacha_CHK_NUMTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.Nacha_CHK_NUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.Nacha_CHK_NUMTextEdit.TabIndex = 15
        '
        'EMP_ROUTINGTextEdit
        '
        Me.EMP_ROUTINGTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "EMP_ROUTING", True))
        Me.EMP_ROUTINGTextEdit.Location = New System.Drawing.Point(153, 153)
        Me.EMP_ROUTINGTextEdit.Name = "EMP_ROUTINGTextEdit"
        Me.EMP_ROUTINGTextEdit.Properties.ReadOnly = True
        Me.EMP_ROUTINGTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.EMP_ROUTINGTextEdit.StyleController = Me.DataLayoutControl1
        Me.EMP_ROUTINGTextEdit.TabIndex = 16
        '
        'EMP_ACCTNUMTextEdit
        '
        Me.EMP_ACCTNUMTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "EMP_ACCTNUM", True))
        Me.EMP_ACCTNUMTextEdit.Location = New System.Drawing.Point(505, 153)
        Me.EMP_ACCTNUMTextEdit.Name = "EMP_ACCTNUMTextEdit"
        Me.EMP_ACCTNUMTextEdit.Properties.ReadOnly = True
        Me.EMP_ACCTNUMTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.EMP_ACCTNUMTextEdit.StyleController = Me.DataLayoutControl1
        Me.EMP_ACCTNUMTextEdit.TabIndex = 17
        '
        'AMOUNTTextEdit
        '
        Me.AMOUNTTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "AMOUNT", True))
        Me.AMOUNTTextEdit.Location = New System.Drawing.Point(153, 177)
        Me.AMOUNTTextEdit.Name = "AMOUNTTextEdit"
        Me.AMOUNTTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.AMOUNTTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.AMOUNTTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.AMOUNTTextEdit.Properties.Mask.EditMask = "G"
        Me.AMOUNTTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.AMOUNTTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.AMOUNTTextEdit.Properties.ReadOnly = True
        Me.AMOUNTTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.AMOUNTTextEdit.StyleController = Me.DataLayoutControl1
        Me.AMOUNTTextEdit.TabIndex = 18
        '
        'ACCT_TYPETextEdit
        '
        Me.ACCT_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ACCT_TYPE", True))
        Me.ACCT_TYPETextEdit.Location = New System.Drawing.Point(153, 348)
        Me.ACCT_TYPETextEdit.Name = "ACCT_TYPETextEdit"
        Me.ACCT_TYPETextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.ACCT_TYPETextEdit.StyleController = Me.DataLayoutControl1
        Me.ACCT_TYPETextEdit.TabIndex = 19
        '
        'TRAN_TYPETextEdit
        '
        Me.TRAN_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "TRAN_TYPE", True))
        Me.TRAN_TYPETextEdit.Location = New System.Drawing.Point(153, 372)
        Me.TRAN_TYPETextEdit.Name = "TRAN_TYPETextEdit"
        Me.TRAN_TYPETextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.TRAN_TYPETextEdit.StyleController = Me.DataLayoutControl1
        Me.TRAN_TYPETextEdit.TabIndex = 20
        '
        'Debit_RoutingTextEdit
        '
        Me.Debit_RoutingTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Debit_Routing", True))
        Me.Debit_RoutingTextEdit.Location = New System.Drawing.Point(153, 396)
        Me.Debit_RoutingTextEdit.Name = "Debit_RoutingTextEdit"
        Me.Debit_RoutingTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.Debit_RoutingTextEdit.StyleController = Me.DataLayoutControl1
        Me.Debit_RoutingTextEdit.TabIndex = 21
        '
        'Debit_AccountTextEdit
        '
        Me.Debit_AccountTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Debit_Account", True))
        Me.Debit_AccountTextEdit.Location = New System.Drawing.Point(153, 420)
        Me.Debit_AccountTextEdit.Name = "Debit_AccountTextEdit"
        Me.Debit_AccountTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.Debit_AccountTextEdit.StyleController = Me.DataLayoutControl1
        Me.Debit_AccountTextEdit.TabIndex = 22
        '
        'ER_Tran_TypeTextEdit
        '
        Me.ER_Tran_TypeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ER_Tran_Type", True))
        Me.ER_Tran_TypeTextEdit.Location = New System.Drawing.Point(153, 444)
        Me.ER_Tran_TypeTextEdit.Name = "ER_Tran_TypeTextEdit"
        Me.ER_Tran_TypeTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.ER_Tran_TypeTextEdit.StyleController = Me.DataLayoutControl1
        Me.ER_Tran_TypeTextEdit.TabIndex = 23
        '
        'ER_ACCT_TYPETextEdit
        '
        Me.ER_ACCT_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ER_ACCT_TYPE", True))
        Me.ER_ACCT_TYPETextEdit.Location = New System.Drawing.Point(153, 468)
        Me.ER_ACCT_TYPETextEdit.Name = "ER_ACCT_TYPETextEdit"
        Me.ER_ACCT_TYPETextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.ER_ACCT_TYPETextEdit.StyleController = Me.DataLayoutControl1
        Me.ER_ACCT_TYPETextEdit.TabIndex = 24
        '
        'ReasonTextEdit
        '
        Me.ReasonTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Reason", True))
        Me.ReasonTextEdit.Location = New System.Drawing.Point(505, 201)
        Me.ReasonTextEdit.Name = "ReasonTextEdit"
        Me.ReasonTextEdit.Properties.ReadOnly = True
        Me.ReasonTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.ReasonTextEdit.StyleController = Me.DataLayoutControl1
        Me.ReasonTextEdit.TabIndex = 25
        '
        'FaultTextEdit
        '
        Me.FaultTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Fault", True))
        Me.FaultTextEdit.Location = New System.Drawing.Point(153, 201)
        Me.FaultTextEdit.Name = "FaultTextEdit"
        Me.FaultTextEdit.Properties.ReadOnly = True
        Me.FaultTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.FaultTextEdit.StyleController = Me.DataLayoutControl1
        Me.FaultTextEdit.TabIndex = 26
        '
        'Date_RequestedDateEdit
        '
        Me.Date_RequestedDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Date_Requested", True))
        Me.Date_RequestedDateEdit.EditValue = Nothing
        Me.Date_RequestedDateEdit.Location = New System.Drawing.Point(153, 225)
        Me.Date_RequestedDateEdit.Name = "Date_RequestedDateEdit"
        Me.Date_RequestedDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_RequestedDateEdit.Properties.ReadOnly = True
        Me.Date_RequestedDateEdit.Size = New System.Drawing.Size(207, 20)
        Me.Date_RequestedDateEdit.StyleController = Me.DataLayoutControl1
        Me.Date_RequestedDateEdit.TabIndex = 27
        '
        'Requested_ByTextEdit
        '
        Me.Requested_ByTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Requested_By", True))
        Me.Requested_ByTextEdit.Location = New System.Drawing.Point(505, 225)
        Me.Requested_ByTextEdit.Name = "Requested_ByTextEdit"
        Me.Requested_ByTextEdit.Properties.ReadOnly = True
        Me.Requested_ByTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.Requested_ByTextEdit.StyleController = Me.DataLayoutControl1
        Me.Requested_ByTextEdit.TabIndex = 28
        '
        'BriefDescTextEdit
        '
        Me.BriefDescTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "BriefDesc", True))
        Me.BriefDescTextEdit.Location = New System.Drawing.Point(153, 249)
        Me.BriefDescTextEdit.Name = "BriefDescTextEdit"
        Me.BriefDescTextEdit.Properties.ReadOnly = True
        Me.BriefDescTextEdit.Size = New System.Drawing.Size(559, 20)
        Me.BriefDescTextEdit.StyleController = Me.DataLayoutControl1
        Me.BriefDescTextEdit.TabIndex = 29
        '
        'Reversal_StatusTextEdit
        '
        Me.Reversal_StatusTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Reversal_Status", True))
        Me.Reversal_StatusTextEdit.Location = New System.Drawing.Point(153, 57)
        Me.Reversal_StatusTextEdit.Name = "Reversal_StatusTextEdit"
        Me.Reversal_StatusTextEdit.Properties.ReadOnly = True
        Me.Reversal_StatusTextEdit.Size = New System.Drawing.Size(559, 20)
        Me.Reversal_StatusTextEdit.StyleController = Me.DataLayoutControl1
        Me.Reversal_StatusTextEdit.TabIndex = 30
        '
        'EE_Reversal_Effective_DateDateEdit
        '
        Me.EE_Reversal_Effective_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "EE_Reversal_Effective_Date", True))
        Me.EE_Reversal_Effective_DateDateEdit.EditValue = Nothing
        Me.EE_Reversal_Effective_DateDateEdit.Location = New System.Drawing.Point(153, 273)
        Me.EE_Reversal_Effective_DateDateEdit.Name = "EE_Reversal_Effective_DateDateEdit"
        Me.EE_Reversal_Effective_DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.EE_Reversal_Effective_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EE_Reversal_Effective_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EE_Reversal_Effective_DateDateEdit.Properties.ReadOnly = True
        Me.EE_Reversal_Effective_DateDateEdit.Size = New System.Drawing.Size(559, 20)
        Me.EE_Reversal_Effective_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.EE_Reversal_Effective_DateDateEdit.TabIndex = 31
        '
        'EE_Reversal_Process_DateDateEdit
        '
        Me.EE_Reversal_Process_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "EE_Reversal_Process_Date", True))
        Me.EE_Reversal_Process_DateDateEdit.EditValue = Nothing
        Me.EE_Reversal_Process_DateDateEdit.Location = New System.Drawing.Point(153, 297)
        Me.EE_Reversal_Process_DateDateEdit.Name = "EE_Reversal_Process_DateDateEdit"
        Me.EE_Reversal_Process_DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.EE_Reversal_Process_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EE_Reversal_Process_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EE_Reversal_Process_DateDateEdit.Properties.ReadOnly = True
        Me.EE_Reversal_Process_DateDateEdit.Size = New System.Drawing.Size(559, 20)
        Me.EE_Reversal_Process_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.EE_Reversal_Process_DateDateEdit.TabIndex = 32
        '
        'ER_Refund_Effective_DateDateEdit
        '
        Me.ER_Refund_Effective_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ER_Refund_Effective_Date", True))
        Me.ER_Refund_Effective_DateDateEdit.EditValue = Nothing
        Me.ER_Refund_Effective_DateDateEdit.Location = New System.Drawing.Point(153, 321)
        Me.ER_Refund_Effective_DateDateEdit.Name = "ER_Refund_Effective_DateDateEdit"
        Me.ER_Refund_Effective_DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.ER_Refund_Effective_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ER_Refund_Effective_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ER_Refund_Effective_DateDateEdit.Properties.ReadOnly = True
        Me.ER_Refund_Effective_DateDateEdit.Size = New System.Drawing.Size(559, 20)
        Me.ER_Refund_Effective_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.ER_Refund_Effective_DateDateEdit.TabIndex = 33
        '
        'ER_Refund_Process_DateDateEdit
        '
        Me.ER_Refund_Process_DateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ER_Refund_Process_Date", True))
        Me.ER_Refund_Process_DateDateEdit.EditValue = Nothing
        Me.ER_Refund_Process_DateDateEdit.Location = New System.Drawing.Point(153, 345)
        Me.ER_Refund_Process_DateDateEdit.Name = "ER_Refund_Process_DateDateEdit"
        Me.ER_Refund_Process_DateDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.ER_Refund_Process_DateDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ER_Refund_Process_DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ER_Refund_Process_DateDateEdit.Properties.ReadOnly = True
        Me.ER_Refund_Process_DateDateEdit.Size = New System.Drawing.Size(559, 20)
        Me.ER_Refund_Process_DateDateEdit.StyleController = Me.DataLayoutControl1
        Me.ER_Refund_Process_DateDateEdit.TabIndex = 34
        '
        'Banking_Dept_NotesTextEdit
        '
        Me.Banking_Dept_NotesTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Banking_Dept_Notes", True))
        Me.Banking_Dept_NotesTextEdit.Location = New System.Drawing.Point(153, 369)
        Me.Banking_Dept_NotesTextEdit.Name = "Banking_Dept_NotesTextEdit"
        Me.Banking_Dept_NotesTextEdit.Properties.ReadOnly = True
        Me.Banking_Dept_NotesTextEdit.Size = New System.Drawing.Size(559, 20)
        Me.Banking_Dept_NotesTextEdit.StyleController = Me.DataLayoutControl1
        Me.Banking_Dept_NotesTextEdit.TabIndex = 35
        '
        'EE_Reversal_NACHA_IDTextEdit
        '
        Me.EE_Reversal_NACHA_IDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "EE_Reversal_NACHA_ID", True))
        Me.EE_Reversal_NACHA_IDTextEdit.Location = New System.Drawing.Point(153, 393)
        Me.EE_Reversal_NACHA_IDTextEdit.Name = "EE_Reversal_NACHA_IDTextEdit"
        Me.EE_Reversal_NACHA_IDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.EE_Reversal_NACHA_IDTextEdit.Properties.ReadOnly = True
        Me.EE_Reversal_NACHA_IDTextEdit.Size = New System.Drawing.Size(559, 20)
        Me.EE_Reversal_NACHA_IDTextEdit.StyleController = Me.DataLayoutControl1
        Me.EE_Reversal_NACHA_IDTextEdit.TabIndex = 36
        '
        'ER_Refund_NACHA_IDTextEdit
        '
        Me.ER_Refund_NACHA_IDTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "ER_Refund_NACHA_ID", True))
        Me.ER_Refund_NACHA_IDTextEdit.Location = New System.Drawing.Point(153, 732)
        Me.ER_Refund_NACHA_IDTextEdit.Name = "ER_Refund_NACHA_IDTextEdit"
        Me.ER_Refund_NACHA_IDTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
        Me.ER_Refund_NACHA_IDTextEdit.Size = New System.Drawing.Size(1207, 20)
        Me.ER_Refund_NACHA_IDTextEdit.StyleController = Me.DataLayoutControl1
        Me.ER_Refund_NACHA_IDTextEdit.TabIndex = 37
        '
        'Payroll_numTextEdit
        '
        Me.Payroll_numTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.dD_Reversal_LogsBindingSource, "Payroll_num", True))
        Me.Payroll_numTextEdit.Location = New System.Drawing.Point(505, 177)
        Me.Payroll_numTextEdit.Name = "Payroll_numTextEdit"
        Me.Payroll_numTextEdit.Properties.Appearance.Options.UseTextOptions = True
        Me.Payroll_numTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.Payroll_numTextEdit.Properties.Mask.EditMask = "G"
        Me.Payroll_numTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.Payroll_numTextEdit.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.Payroll_numTextEdit.Properties.ReadOnly = True
        Me.Payroll_numTextEdit.Size = New System.Drawing.Size(207, 20)
        Me.Payroll_numTextEdit.StyleController = Me.DataLayoutControl1
        Me.Payroll_numTextEdit.TabIndex = 38
        '
        'ItemForID
        '
        Me.ItemForID.Control = Me.IDTextEdit
        Me.ItemForID.Location = New System.Drawing.Point(0, 0)
        Me.ItemForID.Name = "ItemForID"
        Me.ItemForID.Size = New System.Drawing.Size(675, 24)
        Me.ItemForID.Text = "ID"
        Me.ItemForID.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForCHK_COUNTER
        '
        Me.ItemForCHK_COUNTER.Control = Me.CHK_COUNTERTextEdit
        Me.ItemForCHK_COUNTER.Location = New System.Drawing.Point(0, 168)
        Me.ItemForCHK_COUNTER.Name = "ItemForCHK_COUNTER"
        Me.ItemForCHK_COUNTER.Size = New System.Drawing.Size(1352, 24)
        Me.ItemForCHK_COUNTER.Text = "CHK_COUNTER"
        Me.ItemForCHK_COUNTER.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForCM_DIVNUM
        '
        Me.ItemForCM_DIVNUM.Control = Me.CM_DIVNUMTextEdit
        Me.ItemForCM_DIVNUM.Location = New System.Drawing.Point(0, 168)
        Me.ItemForCM_DIVNUM.Name = "ItemForCM_DIVNUM"
        Me.ItemForCM_DIVNUM.Size = New System.Drawing.Size(1352, 48)
        Me.ItemForCM_DIVNUM.Text = "CM_DIVNUM"
        Me.ItemForCM_DIVNUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForNacha_DIVNUM
        '
        Me.ItemForNacha_DIVNUM.Control = Me.Nacha_DIVNUMTextEdit
        Me.ItemForNacha_DIVNUM.Location = New System.Drawing.Point(0, 168)
        Me.ItemForNacha_DIVNUM.Name = "ItemForNacha_DIVNUM"
        Me.ItemForNacha_DIVNUM.Size = New System.Drawing.Size(1352, 72)
        Me.ItemForNacha_DIVNUM.Text = "Nacha_DIVNUM"
        Me.ItemForNacha_DIVNUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForNacha_CHK_NUM
        '
        Me.ItemForNacha_CHK_NUM.Control = Me.Nacha_CHK_NUMTextEdit
        Me.ItemForNacha_CHK_NUM.Location = New System.Drawing.Point(0, 168)
        Me.ItemForNacha_CHK_NUM.Name = "ItemForNacha_CHK_NUM"
        Me.ItemForNacha_CHK_NUM.Size = New System.Drawing.Size(1352, 96)
        Me.ItemForNacha_CHK_NUM.Text = "Nacha_CHK_NUM"
        Me.ItemForNacha_CHK_NUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForACCT_TYPE
        '
        Me.ItemForACCT_TYPE.Control = Me.ACCT_TYPETextEdit
        Me.ItemForACCT_TYPE.Location = New System.Drawing.Point(0, 336)
        Me.ItemForACCT_TYPE.Name = "ItemForACCT_TYPE"
        Me.ItemForACCT_TYPE.Size = New System.Drawing.Size(1352, 24)
        Me.ItemForACCT_TYPE.Text = "ACCT_TYPE"
        Me.ItemForACCT_TYPE.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForTRAN_TYPE
        '
        Me.ItemForTRAN_TYPE.Control = Me.TRAN_TYPETextEdit
        Me.ItemForTRAN_TYPE.Location = New System.Drawing.Point(0, 336)
        Me.ItemForTRAN_TYPE.Name = "ItemForTRAN_TYPE"
        Me.ItemForTRAN_TYPE.Size = New System.Drawing.Size(1352, 48)
        Me.ItemForTRAN_TYPE.Text = "TRAN_TYPE"
        Me.ItemForTRAN_TYPE.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForDebit_Routing
        '
        Me.ItemForDebit_Routing.Control = Me.Debit_RoutingTextEdit
        Me.ItemForDebit_Routing.Location = New System.Drawing.Point(0, 336)
        Me.ItemForDebit_Routing.Name = "ItemForDebit_Routing"
        Me.ItemForDebit_Routing.Size = New System.Drawing.Size(1352, 72)
        Me.ItemForDebit_Routing.Text = "Debit_Routing"
        Me.ItemForDebit_Routing.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForDebit_Account
        '
        Me.ItemForDebit_Account.Control = Me.Debit_AccountTextEdit
        Me.ItemForDebit_Account.Location = New System.Drawing.Point(0, 336)
        Me.ItemForDebit_Account.Name = "ItemForDebit_Account"
        Me.ItemForDebit_Account.Size = New System.Drawing.Size(1352, 96)
        Me.ItemForDebit_Account.Text = "Debit_Account"
        Me.ItemForDebit_Account.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForER_Tran_Type
        '
        Me.ItemForER_Tran_Type.Control = Me.ER_Tran_TypeTextEdit
        Me.ItemForER_Tran_Type.Location = New System.Drawing.Point(0, 336)
        Me.ItemForER_Tran_Type.Name = "ItemForER_Tran_Type"
        Me.ItemForER_Tran_Type.Size = New System.Drawing.Size(1352, 120)
        Me.ItemForER_Tran_Type.Text = "ER_Tran_Type"
        Me.ItemForER_Tran_Type.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForER_ACCT_TYPE
        '
        Me.ItemForER_ACCT_TYPE.Control = Me.ER_ACCT_TYPETextEdit
        Me.ItemForER_ACCT_TYPE.Location = New System.Drawing.Point(0, 336)
        Me.ItemForER_ACCT_TYPE.Name = "ItemForER_ACCT_TYPE"
        Me.ItemForER_ACCT_TYPE.Size = New System.Drawing.Size(1352, 144)
        Me.ItemForER_ACCT_TYPE.Text = "ER_ACCT_TYPE"
        Me.ItemForER_ACCT_TYPE.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForER_Refund_NACHA_ID
        '
        Me.ItemForER_Refund_NACHA_ID.Control = Me.ER_Refund_NACHA_IDTextEdit
        Me.ItemForER_Refund_NACHA_ID.Location = New System.Drawing.Point(0, 720)
        Me.ItemForER_Refund_NACHA_ID.Name = "ItemForER_Refund_NACHA_ID"
        Me.ItemForER_Refund_NACHA_ID.Size = New System.Drawing.Size(1352, 24)
        Me.ItemForER_Refund_NACHA_ID.Text = "ER_Refund_NACHA_ID"
        Me.ItemForER_Refund_NACHA_ID.TextSize = New System.Drawing.Size(138, 13)
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(724, 451)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.AllowDrawBackground = False
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForCONUM, Me.ItemForCheck_date, Me.ItemForGROSS, Me.ItemForEMP_ROUTING, Me.ItemForAMOUNT, Me.ItemForFault, Me.ItemForDate_Requested, Me.ItemForBriefDesc, Me.ItemForEE_Reversal_Effective_Date, Me.ItemForEE_Reversal_Process_Date, Me.ItemForER_Refund_Effective_Date, Me.ItemForER_Refund_Process_Date, Me.ItemForBanking_Dept_Notes, Me.ItemForEE_Reversal_NACHA_ID, Me.ItemForReversal_Status, Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.ItemForEMPNUM, Me.ItemForCHK_NUM, Me.ItemForNET, Me.ItemForEMP_ACCTNUM, Me.ItemForReason, Me.ItemForRequested_By, Me.ItemForPayroll_num})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup2.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(704, 431)
        '
        'ItemForCONUM
        '
        Me.ItemForCONUM.Control = Me.CONUMTextEdit
        Me.ItemForCONUM.Location = New System.Drawing.Point(0, 69)
        Me.ItemForCONUM.Name = "ItemForCONUM"
        Me.ItemForCONUM.Size = New System.Drawing.Size(352, 24)
        Me.ItemForCONUM.Text = "CONUM"
        Me.ItemForCONUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForCheck_date
        '
        Me.ItemForCheck_date.Control = Me.Check_dateDateEdit
        Me.ItemForCheck_date.Location = New System.Drawing.Point(0, 93)
        Me.ItemForCheck_date.Name = "ItemForCheck_date"
        Me.ItemForCheck_date.Size = New System.Drawing.Size(352, 24)
        Me.ItemForCheck_date.Text = "Check_date"
        Me.ItemForCheck_date.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForGROSS
        '
        Me.ItemForGROSS.Control = Me.GROSSTextEdit
        Me.ItemForGROSS.Location = New System.Drawing.Point(0, 117)
        Me.ItemForGROSS.Name = "ItemForGROSS"
        Me.ItemForGROSS.Size = New System.Drawing.Size(352, 24)
        Me.ItemForGROSS.Text = "GROSS"
        Me.ItemForGROSS.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForEMP_ROUTING
        '
        Me.ItemForEMP_ROUTING.Control = Me.EMP_ROUTINGTextEdit
        Me.ItemForEMP_ROUTING.Location = New System.Drawing.Point(0, 141)
        Me.ItemForEMP_ROUTING.Name = "ItemForEMP_ROUTING"
        Me.ItemForEMP_ROUTING.Size = New System.Drawing.Size(352, 24)
        Me.ItemForEMP_ROUTING.Text = "EMP_ROUTING"
        Me.ItemForEMP_ROUTING.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForAMOUNT
        '
        Me.ItemForAMOUNT.Control = Me.AMOUNTTextEdit
        Me.ItemForAMOUNT.Location = New System.Drawing.Point(0, 165)
        Me.ItemForAMOUNT.Name = "ItemForAMOUNT"
        Me.ItemForAMOUNT.Size = New System.Drawing.Size(352, 24)
        Me.ItemForAMOUNT.Text = "AMOUNT"
        Me.ItemForAMOUNT.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForFault
        '
        Me.ItemForFault.Control = Me.FaultTextEdit
        Me.ItemForFault.Location = New System.Drawing.Point(0, 189)
        Me.ItemForFault.Name = "ItemForFault"
        Me.ItemForFault.Size = New System.Drawing.Size(352, 24)
        Me.ItemForFault.Text = "Fault"
        Me.ItemForFault.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForDate_Requested
        '
        Me.ItemForDate_Requested.Control = Me.Date_RequestedDateEdit
        Me.ItemForDate_Requested.Location = New System.Drawing.Point(0, 213)
        Me.ItemForDate_Requested.Name = "ItemForDate_Requested"
        Me.ItemForDate_Requested.Size = New System.Drawing.Size(352, 24)
        Me.ItemForDate_Requested.Text = "Date_Requested"
        Me.ItemForDate_Requested.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForBriefDesc
        '
        Me.ItemForBriefDesc.Control = Me.BriefDescTextEdit
        Me.ItemForBriefDesc.Location = New System.Drawing.Point(0, 237)
        Me.ItemForBriefDesc.Name = "ItemForBriefDesc"
        Me.ItemForBriefDesc.Size = New System.Drawing.Size(704, 24)
        Me.ItemForBriefDesc.Text = "Brief Desc"
        Me.ItemForBriefDesc.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForEE_Reversal_Effective_Date
        '
        Me.ItemForEE_Reversal_Effective_Date.Control = Me.EE_Reversal_Effective_DateDateEdit
        Me.ItemForEE_Reversal_Effective_Date.Location = New System.Drawing.Point(0, 261)
        Me.ItemForEE_Reversal_Effective_Date.Name = "ItemForEE_Reversal_Effective_Date"
        Me.ItemForEE_Reversal_Effective_Date.Size = New System.Drawing.Size(704, 24)
        Me.ItemForEE_Reversal_Effective_Date.Text = "EE_Reversal_Effective_Date"
        Me.ItemForEE_Reversal_Effective_Date.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForEE_Reversal_Process_Date
        '
        Me.ItemForEE_Reversal_Process_Date.Control = Me.EE_Reversal_Process_DateDateEdit
        Me.ItemForEE_Reversal_Process_Date.Location = New System.Drawing.Point(0, 285)
        Me.ItemForEE_Reversal_Process_Date.Name = "ItemForEE_Reversal_Process_Date"
        Me.ItemForEE_Reversal_Process_Date.Size = New System.Drawing.Size(704, 24)
        Me.ItemForEE_Reversal_Process_Date.Text = "EE_Reversal_Process_Date"
        Me.ItemForEE_Reversal_Process_Date.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForER_Refund_Effective_Date
        '
        Me.ItemForER_Refund_Effective_Date.Control = Me.ER_Refund_Effective_DateDateEdit
        Me.ItemForER_Refund_Effective_Date.Location = New System.Drawing.Point(0, 309)
        Me.ItemForER_Refund_Effective_Date.Name = "ItemForER_Refund_Effective_Date"
        Me.ItemForER_Refund_Effective_Date.Size = New System.Drawing.Size(704, 24)
        Me.ItemForER_Refund_Effective_Date.Text = "ER_Refund_Effective_Date"
        Me.ItemForER_Refund_Effective_Date.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForER_Refund_Process_Date
        '
        Me.ItemForER_Refund_Process_Date.Control = Me.ER_Refund_Process_DateDateEdit
        Me.ItemForER_Refund_Process_Date.Location = New System.Drawing.Point(0, 333)
        Me.ItemForER_Refund_Process_Date.Name = "ItemForER_Refund_Process_Date"
        Me.ItemForER_Refund_Process_Date.Size = New System.Drawing.Size(704, 24)
        Me.ItemForER_Refund_Process_Date.Text = "ER_Refund_Process_Date"
        Me.ItemForER_Refund_Process_Date.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForBanking_Dept_Notes
        '
        Me.ItemForBanking_Dept_Notes.Control = Me.Banking_Dept_NotesTextEdit
        Me.ItemForBanking_Dept_Notes.Location = New System.Drawing.Point(0, 357)
        Me.ItemForBanking_Dept_Notes.Name = "ItemForBanking_Dept_Notes"
        Me.ItemForBanking_Dept_Notes.Size = New System.Drawing.Size(704, 24)
        Me.ItemForBanking_Dept_Notes.Text = "Banking_Dept_Notes"
        Me.ItemForBanking_Dept_Notes.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForEE_Reversal_NACHA_ID
        '
        Me.ItemForEE_Reversal_NACHA_ID.Control = Me.EE_Reversal_NACHA_IDTextEdit
        Me.ItemForEE_Reversal_NACHA_ID.Location = New System.Drawing.Point(0, 381)
        Me.ItemForEE_Reversal_NACHA_ID.Name = "ItemForEE_Reversal_NACHA_ID"
        Me.ItemForEE_Reversal_NACHA_ID.Size = New System.Drawing.Size(704, 24)
        Me.ItemForEE_Reversal_NACHA_ID.Text = "EE_Reversal_NACHA_ID"
        Me.ItemForEE_Reversal_NACHA_ID.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForReversal_Status
        '
        Me.ItemForReversal_Status.Control = Me.Reversal_StatusTextEdit
        Me.ItemForReversal_Status.Location = New System.Drawing.Point(0, 45)
        Me.ItemForReversal_Status.Name = "ItemForReversal_Status"
        Me.ItemForReversal_Status.Size = New System.Drawing.Size(704, 24)
        Me.ItemForReversal_Status.Text = "Reversal_Status"
        Me.ItemForReversal_Status.TextSize = New System.Drawing.Size(138, 13)
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.LabelControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.MinSize = New System.Drawing.Size(288, 17)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(704, 45)
        Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.btnUpdateStatus
        Me.LayoutControlItem2.Location = New System.Drawing.Point(351, 405)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(353, 26)
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.btnCancel
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 405)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(351, 26)
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem3.TextVisible = False
        '
        'ItemForEMPNUM
        '
        Me.ItemForEMPNUM.Control = Me.EMPNUMTextEdit
        Me.ItemForEMPNUM.Location = New System.Drawing.Point(352, 69)
        Me.ItemForEMPNUM.Name = "ItemForEMPNUM"
        Me.ItemForEMPNUM.Size = New System.Drawing.Size(352, 24)
        Me.ItemForEMPNUM.Text = "EMPNUM"
        Me.ItemForEMPNUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForCHK_NUM
        '
        Me.ItemForCHK_NUM.Control = Me.CHK_NUMTextEdit
        Me.ItemForCHK_NUM.Location = New System.Drawing.Point(352, 93)
        Me.ItemForCHK_NUM.Name = "ItemForCHK_NUM"
        Me.ItemForCHK_NUM.Size = New System.Drawing.Size(352, 24)
        Me.ItemForCHK_NUM.Text = "CHK_NUM"
        Me.ItemForCHK_NUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForNET
        '
        Me.ItemForNET.Control = Me.NETTextEdit
        Me.ItemForNET.Location = New System.Drawing.Point(352, 117)
        Me.ItemForNET.Name = "ItemForNET"
        Me.ItemForNET.Size = New System.Drawing.Size(352, 24)
        Me.ItemForNET.Text = "NET"
        Me.ItemForNET.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForEMP_ACCTNUM
        '
        Me.ItemForEMP_ACCTNUM.Control = Me.EMP_ACCTNUMTextEdit
        Me.ItemForEMP_ACCTNUM.Location = New System.Drawing.Point(352, 141)
        Me.ItemForEMP_ACCTNUM.Name = "ItemForEMP_ACCTNUM"
        Me.ItemForEMP_ACCTNUM.Size = New System.Drawing.Size(352, 24)
        Me.ItemForEMP_ACCTNUM.Text = "EMP_ACCTNUM"
        Me.ItemForEMP_ACCTNUM.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForReason
        '
        Me.ItemForReason.Control = Me.ReasonTextEdit
        Me.ItemForReason.Location = New System.Drawing.Point(352, 189)
        Me.ItemForReason.Name = "ItemForReason"
        Me.ItemForReason.Size = New System.Drawing.Size(352, 24)
        Me.ItemForReason.Text = "Reason"
        Me.ItemForReason.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForRequested_By
        '
        Me.ItemForRequested_By.Control = Me.Requested_ByTextEdit
        Me.ItemForRequested_By.Location = New System.Drawing.Point(352, 213)
        Me.ItemForRequested_By.Name = "ItemForRequested_By"
        Me.ItemForRequested_By.Size = New System.Drawing.Size(352, 24)
        Me.ItemForRequested_By.Text = "Requested_By"
        Me.ItemForRequested_By.TextSize = New System.Drawing.Size(138, 13)
        '
        'ItemForPayroll_num
        '
        Me.ItemForPayroll_num.Control = Me.Payroll_numTextEdit
        Me.ItemForPayroll_num.Location = New System.Drawing.Point(352, 165)
        Me.ItemForPayroll_num.Name = "ItemForPayroll_num"
        Me.ItemForPayroll_num.Size = New System.Drawing.Size(352, 24)
        Me.ItemForPayroll_num.Text = "Payroll_num"
        Me.ItemForPayroll_num.TextSize = New System.Drawing.Size(138, 13)
        '
        'dD_Reversal_LogsBindingSource
        '
        Me.dD_Reversal_LogsBindingSource.DataSource = GetType(Brands_FrontDesk.DD_Reversal_Log)
        '
        'frmDDReversalDetails
        '
        Me.AcceptButton = Me.btnUpdateStatus
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.btnCancel
        Me.ClientSize = New System.Drawing.Size(724, 451)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.Name = "frmDDReversalDetails"
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "DD Reversal Details"
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CONUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMPNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Check_dateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Check_dateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CHK_NUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GROSSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NETTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CHK_COUNTERTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CM_DIVNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Nacha_DIVNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Nacha_CHK_NUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMP_ROUTINGTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EMP_ACCTNUMTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AMOUNTTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TRAN_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Debit_RoutingTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Debit_AccountTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_Tran_TypeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_ACCT_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ReasonTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FaultTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_RequestedDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_RequestedDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Requested_ByTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BriefDescTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Reversal_StatusTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EE_Reversal_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EE_Reversal_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EE_Reversal_Process_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EE_Reversal_Process_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_Refund_Effective_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_Refund_Effective_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_Refund_Process_DateDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_Refund_Process_DateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Banking_Dept_NotesTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EE_Reversal_NACHA_IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ER_Refund_NACHA_IDTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Payroll_numTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForID, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCHK_COUNTER, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCM_DIVNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNacha_DIVNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNacha_CHK_NUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForACCT_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTRAN_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDebit_Routing, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDebit_Account, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForER_Tran_Type, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForER_ACCT_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForER_Refund_NACHA_ID, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCONUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCheck_date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForGROSS, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForEMP_ROUTING, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForAMOUNT, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForFault, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForDate_Requested, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForBriefDesc, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForEE_Reversal_Effective_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForEE_Reversal_Process_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForER_Refund_Effective_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForER_Refund_Process_Date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForBanking_Dept_Notes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForEE_Reversal_NACHA_ID, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForReversal_Status, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForEMPNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCHK_NUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNET, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForEMP_ACCTNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForReason, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForRequested_By, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPayroll_num, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dD_Reversal_LogsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents dD_Reversal_LogsBindingSource As BindingSource
    Friend WithEvents IDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CONUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents EMPNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Check_dateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents CHK_NUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GROSSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents NETTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CHK_COUNTERTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CM_DIVNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Nacha_DIVNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Nacha_CHK_NUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents EMP_ROUTINGTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents EMP_ACCTNUMTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents AMOUNTTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ACCT_TYPETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TRAN_TYPETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Debit_RoutingTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Debit_AccountTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ER_Tran_TypeTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ER_ACCT_TYPETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ReasonTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FaultTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Date_RequestedDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Requested_ByTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents BriefDescTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Reversal_StatusTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents EE_Reversal_Effective_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents EE_Reversal_Process_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents ER_Refund_Effective_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents ER_Refund_Process_DateDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents Banking_Dept_NotesTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents EE_Reversal_NACHA_IDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ER_Refund_NACHA_IDTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForID As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCONUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForEMPNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCheck_date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCHK_NUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForGROSS As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNET As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCHK_COUNTER As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCM_DIVNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNacha_DIVNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNacha_CHK_NUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForEMP_ROUTING As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForEMP_ACCTNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForAMOUNT As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForACCT_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTRAN_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDebit_Routing As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDebit_Account As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForER_Tran_Type As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForER_ACCT_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForReason As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForFault As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForDate_Requested As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForRequested_By As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForBriefDesc As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForReversal_Status As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForEE_Reversal_Effective_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForEE_Reversal_Process_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForER_Refund_Effective_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForER_Refund_Process_Date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForBanking_Dept_Notes As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForEE_Reversal_NACHA_ID As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForER_Refund_NACHA_ID As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Payroll_numTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ItemForPayroll_num As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents btnCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnUpdateStatus As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
End Class
