﻿Imports Brands_FrontDesk
Imports DevExpress.Data
Imports DevExpress.XtraEditors.Repository

Public MustInherit Class BaseParameterBehavior(Of T)
    Public ReadOnly Property _RepositoryItemsCache As Dictionary(Of String, RepositoryItem) = New Dictionary(Of String, RepositoryItem)

    Public Event RefreshDataEvent()
    Public Sub RaiseRefreshDataEvent()
        RaiseEvent RefreshDataEvent()
    End Sub

    Public Event PostEditorEvent()
    Public Sub RaisePostEditorEvent()
        RaiseEvent PostEditorEvent()
    End Sub

    MustOverride Property ParameterList As List(Of T)
    MustOverride Function GetRepositoryItem(param As T) As RepositoryItem

    MustOverride Function GetParameterByName(name As String) As T
    MustOverride Function GetParameterValue(name As String) As Object
    MustOverride Sub ClearParametersValue(param As T)
    MustOverride Sub ClearParametersValue(paramName As String)

    MustOverride Sub CompanyNumberChanged()
    MustOverride Sub YearChanged()

    MustOverride Function ShowDateRange() As Boolean
    Public MustOverride Sub SetDataRange(dateFrom As Date, toDate As Date)


    MustOverride Function GetPromptTextSetting() As ColumnSettings
    MustOverride Function GetColNameSetting() As ColumnSettings
    MustOverride Function GetColValueSetting() As ColumnSettings

    Public Overridable Function GetUnboundValue(row As T, colSettings As ColumnSettings) As Object
        Throw New NotImplementedException()
    End Function

    Public Overridable Sub SetUnboundValue(row As T, colSettings As ColumnSettings, value As Object)
        Throw New NotImplementedException()
    End Sub

    Public Overridable Function GetCoNumParameterValue() As Decimal?
        Return GetDecimalParameter("cf", "conum")
    End Function

    Private Function GetDecimalParameter(ParamArray names As String()) As Decimal?
        Dim number As Decimal
        For Each n In names
            Dim v = GetParameterValue(n)
            If v IsNot Nothing AndAlso Decimal.TryParse(nz(v, ""), number) Then
                Return number
            End If
        Next
        Return Nothing
    End Function


#Region "Repository Items By Name"
    Public Function GetRepositoryItemByName(name As String) As RepositoryItem
        If name = "conum" OrElse name = "cf" Then
            Return GetRepositoryItemForCoNum()
        End If

        If name = "empnum" OrElse name = "et" OrElse name = "ef" Then
            Dim conum = GetCoNumParameterValue()
            If conum.HasValue Then Return GetRepositoryItemForEmpNum(conum.Value)
        ElseIf name = "prnum" OrElse name = "pr" Then
            Dim CoNum = GetCoNumParameterValue()

            If CoNum Is Nothing Then
                Dim paramCoNum = (From p In ParameterList Where CType(CType(p, Object), CustomSqlParameter).ParameterName.ToString().ToLower() = "@conum").FirstOrDefault()
                If paramCoNum IsNot Nothing Then
                    CoNum = CType(CType(CType(paramCoNum, Object), CustomSqlParameter).Value, Double)
                End If
            End If

            Dim Year As Decimal? = GetDecimalParameter("y", "year")

            If Year Is Nothing Then
                Dim paramYear = (From p In ParameterList Where CType(CType(p, Object), CustomSqlParameter).ParameterName.ToString().ToLower() = "@year").FirstOrDefault()
                If paramYear IsNot Nothing Then
                    Year = CType(CType(CType(paramYear, Object), CustomSqlParameter).Value, Double)
                End If
            End If

            If CoNum.HasValue Then Return GetRepositoryItemForPrNum(CoNum.Value, Year)
        ElseIf name = "prnumsp" Then
            Dim CoNum = GetCoNumParameterValue()
            Dim Year As Decimal? = GetDecimalParameter("y", "year")
            If CoNum.HasValue Then Return GetRepositoryItemForPrNumSp(CoNum.Value, Year)
        ElseIf name = "s" Then
            Return GetRepositoryItemForEmployeeStatus()
        ElseIf name = "divnum" OrElse name = "dv" Then
            Dim CoNum = GetCoNumParameterValue()
            If CoNum.HasValue Then Return GetRepositoryItemForDivNum(CoNum.Value)
        ElseIf name = "j" OrElse name = "jobnum" Then
            Dim CoNum = GetCoNumParameterValue()
            If CoNum.HasValue Then Return GetRepositoryItemForJobNum(CoNum)
        ElseIf name = "dp" OrElse name = "depnum" Then
            Dim CoNum = GetCoNumParameterValue()
            If CoNum.HasValue Then GetRepositoryItemForDepNum(CoNum.Value)
        ElseIf name = "y" OrElse name = "year" Then
            Return GetRepositoryItemForYear()
        End If
        Return Nothing
    End Function

    Private Function GetRepositoryItemForCoNum() As RepositoryItem
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim companyListWithActiveStatus = (From A In Db.COMPANies Select New With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                    .CO_FAX = A.CO_FAX, .FED_ID = A.FED_ID, .CO_EMAIL = A.CO_EMAIL, .CoNumAndName = "{0} - {1}".FormatWith(A.CONUM, A.CO_NAME), .Status = A.CO_STATUS}).ToList
            Dim compStatus = (From c In Db.CO_UDFs Where c.UDF_DESCR.Equals("INACTIVE STATUS")).ToDictionary(Function(key) key.CONUM)

            For Each item In companyListWithActiveStatus.Where(Function(c) c.Status = "Active Status")
                Dim value As CO_UDF = Nothing
                If compStatus.TryGetValue(item.CONUM, value) Then
                    item.Status = value.UDF_STRING
                End If
            Next
            Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit() With {.DataSource = companyListWithActiveStatus, .DisplayMember = "CoNumAndName", .ValueMember = "CONUM", .NullText = Nothing}
            AddHandler repo.EditValueChanged, Sub() CompanyNumberChanged()
            Return repo
        End Using
    End Function

    Private Function GetRepositoryItemForEmpNum(ByVal coNum As Decimal) As DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim list As List(Of EMPLOYEE) = (New List(Of EMPLOYEE)(New EMPLOYEE() {New EMPLOYEE With {.EMPNUM = -1, .L_NAME = "", .F_NAME = "(ALL Employees)"}}))
            Dim AllEmployees = (From A In list.Union(Db.EMPLOYEEs.Where(Function(c) c.CONUM = coNum).ToList())
                                Select New With {.EmpNum = A.EMPNUM, .LastName = A.L_NAME, .FistName = A.F_NAME, .TermDate = A.TERM_DATE, .FullName = "{0} - {1} {2}".FormatWith(A.EMPNUM, A.F_NAME, A.L_NAME)}).ToList
            Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit() With {.DataSource = AllEmployees, .DisplayMember = "FullName", .ValueMember = "EmpNum"}
            Return repo
        End Using
    End Function

    Public Function GetRepositoryItemForPrNum(ByVal CoNum As Decimal, ByVal Year As Decimal?) As RepositoryItemLookUpEdit
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim CoPayrolls = (From A In Db.PAYROLLs
                              Where A.CONUM = CoNum AndAlso (Not Year.HasValue OrElse A.CHECK_DATE.Value.Year = Year)
                              Select New With {.PrNum = A.PRNUM, .CheckDate = A.CHECK_DATE, .PrDescr = A.PR_DESCR}).OrderByDescending(Function(o) o.PrNum).ToList
            Dim list = (From A In CoPayrolls Select A.PrNum, A.CheckDate, Value = String.Format("{0}  {1} {2}", A.PrNum, If(A.CheckDate.HasValue, A.CheckDate.Value.ToShortDateString, ""), A.PrDescr)).ToList

            Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "PrNum", .ValueMember = "PrNum"}
            repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
            repo.PopupWidth = 100
            repo.NullText = String.Empty
            repo.ShowHeader = False
            Return repo
        End Using
    End Function

    Private Function GetRepositoryItemForPrNumSp(ByVal CoNum As Decimal, ByVal Year As Decimal?) As RepositoryItemLookUpEdit
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim lstPrl As New List(Of PAYROLL)(New PAYROLL() {New PAYROLL() With {.PRNUM = -1, .CHECK_DATE = Nothing, .PR_DESCR = "(ALL)"}})

            Dim list = (From A In lstPrl.Union(Db.PAYROLLs.Where(Function(c) c.CONUM = CoNum AndAlso (Not Year.HasValue OrElse c.CHECK_DATE.GetValueOrDefault.Year = Year.Value))) Select New With {
            .PrNum = A.PRNUM,
            .CheckDate = A.CHECK_DATE,
            .PrDesc = A.PR_DESCR,
            .Value = [String].Format("{0}  {1} {2}", A.PRNUM, (If(A.CHECK_DATE.HasValue, A.CHECK_DATE.Value.ToShortDateString(), "")), A.PR_DESCR), .AllEmp = If(A.PRNUM = -1, -1, 0)
            }).ToList().OrderBy(Function(s) s.AllEmp).ThenByDescending(Function(s) s.PrNum)

            Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "PrNum", .ValueMember = "PrNum"}
            repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
            repo.PopupWidth = 100
            repo.NullText = String.Empty
            repo.ShowHeader = False
            Return repo
        End Using
    End Function

    Private Function GetRepositoryItemForEmployeeStatus() As RepositoryItemLookUpEdit
        Dim r = New RepositoryItemLookUpEdit With {.DisplayMember = "Name", .ValueMember = "Value"}
        Dim list = New List(Of KeyValuePair) From {
            New KeyValuePair With {.Name = "Active Only", .Value = "-1"},
            New KeyValuePair With {.Name = "Terminated Only", .Value = "1"},
            New KeyValuePair With {.Name = "All Employees", .Value = "2"}
        }
        r.DataSource = list
        r.NullText = String.Empty
        Return r
    End Function

    Private Function GetRepositoryItemForDivNum(ByVal CoNum As Decimal) As RepositoryItem
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim allDivs = (New List(Of DIVISION)(New DIVISION() {New DIVISION With {.DDIVNUM = -1, .DDIVNAME = "All Divisions"}}))
            Dim CoPayrolls = allDivs.Union((From A In Db.DIVISIONs Where A.CONUM = CoNum).OrderByDescending(Function(o) o.DDIVNUM))
            Dim list = (From A In CoPayrolls Select DivNum = A.DDIVNUM, Value = A.DivNumName).ToList
            Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "Value", .ValueMember = "DivNum"}
            repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
            repo.PopupWidth = 100
            repo.NullText = String.Empty
            repo.ShowHeader = False
            Return repo
        End Using
    End Function

    Private Function GetRepositoryItemForJobNum(ByVal CoNum As Decimal) As RepositoryItem
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim allJobs = (New List(Of CO_JOB)(New CO_JOB() {New CO_JOB With {.job_id = -1, .job_descr = "All Jobs"}}))
            Dim CoJobs = allJobs.Union((From A In Db.CO_JOBs Where A.conum = CoNum).OrderByDescending(Function(o) o.job_id))
            Dim list = (From A In CoJobs Select JobId = A.job_id, Value = "{0}: {1}".FormatWith(A.job_id, A.job_descr)).ToList
            Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "Value", .ValueMember = "JobId"}
            repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
            repo.PopupWidth = 100
            repo.NullText = String.Empty
            repo.ShowHeader = False
            Return repo
        End Using
    End Function

    Private Function GetRepositoryItemForDepNum(ByVal CoNum As Decimal) As RepositoryItem
        Using Db As New dbEPDataDataContext(GetConnectionString)
            Dim list As List(Of DEPARTMENT) = (New List(Of DEPARTMENT)(New DEPARTMENT() {New DEPARTMENT With {.DEPTNUM = -1, .DEPT_DESC = "(ALL Departments)"}}))
            Dim AllDepartments = (From A In list.Union(Db.DEPARTMENTs.Where(Function(c) c.CONUM = CoNum).ToList())
                                  Select New With {.DivNum = A.DIVNUMD, .DepNum = A.DEPTNUM, .DeptDesc = A.DEPT_DESC, .Value = "{0}: {1}".FormatWith(A.DEPTNUM, A.DEPT_DESC)}).ToList
            Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
            repo.DataSource = AllDepartments
            repo.DisplayMember = "Value"
            repo.ValueMember = "DepNum"
            Return repo
        End Using
    End Function

    Private Function GetRepositoryItemForYear() As RepositoryItemSpinEdit
        Dim repo = New RepositoryItemSpinEdit
        AddHandler repo.EditValueChanged, Sub() YearChanged()
        Return repo
    End Function
#End Region

    Public Function GetRepositoryItemByParamType(ByVal paramType As Type) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing
        Select Case paramType
            Case GetType(Integer)
                repItem = New RepositoryItemSpinEdit
            Case GetType(Date)
                repItem = New RepositoryItemDateEdit
            Case GetType(DateTime)
                Dim repo = New RepositoryItemDateEdit With {.EditMask = "yyyy/MM/dd HH:mm"}
                repo.Mask.UseMaskAsDisplayFormat = True
                repItem = repo
        End Select
        Return repItem
    End Function
End Class


Public Class ColumnSettings
    Property FieldName As String
    Property UnboundType As UnboundColumnType
    Property Visible As Boolean
End Class

