﻿Public Class frmQuarterlyCentral

    Public ReadOnly Property CoNum As Decimal

    Dim DB As dbEPDataDataContext
    Dim _Loading As Boolean
    Dim Data As List(Of view_QuarterlyShipAndEmailControl)

    Public Sub New(_conum As Decimal)
        InitializeComponent()
        CoNum = _conum
        Text &= $" [{_conum}]"
    End Sub

    Private Sub frmQuarterlyCentral_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If My.Settings.QuerterlyScreenLocation.Y > 0 Then
            Me.Location = My.Settings.QuerterlyScreenLocation
        End If
        DB = New dbEPDataDataContext(GetConnectionString)

        LoadData()

    End Sub

    Private Sub frmQuarterlyCentral_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        My.Settings.QuerterlyScreenLocation = Me.Location
        My.Settings.Save()
    End Sub

    Async Sub LoadData()
        Try
            ProgressPanel1.Location = New Point((Width - ProgressPanel1.Width) / 2, (Height - ProgressPanel1.Height) / 2)
            ProgressPanel1.BringToFront()
            ProgressPanel1.Visible = True
            _Loading = True
            Dim Q = From A In DB.view_QuarterlyShipAndEmailControls
                    Where A.CONUM = Me.CoNum
                    Order By A.Year Descending, A.Qtr Descending, A.QtrJobNum Descending

            'Me.View_QuarterlyShipAndEmailControlBindingSource.Sort = "Year, Qtr, QtrJobNum"
            Data = Await Task.Run(Function() Q.ToList)
            Me.View_QuarterlyShipAndEmailControlBindingSource.DataSource = Data
            _Loading = False
            Me.View_QuarterlyShipAndEmailControlBindingSource_CurrentChanged(Me, Nothing)
            MoveLast()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        Finally
            ProgressPanel1.Visible = False
        End Try
    End Sub

    Sub MoveLast()
        Dim Pos = Data.FindIndex(Function(p) p.QEDate < Date.Now)
        Me.View_QuarterlyShipAndEmailControlBindingSource.Position = Pos
    End Sub

    Private Sub View_QuarterlyShipAndEmailControlBindingSource_CurrentChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles View_QuarterlyShipAndEmailControlBindingSource.CurrentChanged
        If _Loading Then Exit Sub
        Dim CurRec As view_QuarterlyShipAndEmailControl = View_QuarterlyShipAndEmailControlBindingSource.Current
        If CurRec Is Nothing Then
            Me.ViewQuarterlyAllProcessAndDeliveryBindingSource.Clear()
        Else
            Me.YearTsTextBox.Text = CurRec.Year
            Me.QtrComboBox.Text = CurRec.Qtr
            Dim List = (From A In DB.view_QuarterlyAllProcessAndDeliveries
                        Where A.CONUM = Me.CoNum AndAlso A.Year = CurRec.Year AndAlso A.Qtr = CurRec.Qtr
                        Order By A.Sort Descending).ToList
            Me.ViewQuarterlyAllProcessAndDeliveryBindingSource.DataSource = List

            '
            If {"Billing", "Miscellaneous"}.Contains(CurRec.QYEND_HOLD_TYPE) Then
                Me.QYEND_HOLD_TYPETextBox.ForeColor = Color.Red
                Me.QYEND_HOLD_TYPETextBox.Font = New Font(Me.QYEND_HOLD_TYPETextBox.Font, FontStyle.Bold)
            Else
                Me.QYEND_HOLD_TYPETextBox.ForeColor = Color.Black
                Me.QYEND_HOLD_TYPETextBox.Font = New Font(Me.QYEND_HOLD_TYPETextBox.Font, FontStyle.Regular)
            End If

            btnW2OptForYY.Text = "Opt in for " + CurRec.Year.ToString()
            btn1099OptForYY.Text = "Opt in for " + CurRec.Year.ToString()
        End If
    End Sub

    Private Sub YearTsTextBox_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles YearTsTextBox.Validated, QtrComboBox.Validated, QtrComboBox.TextChanged
        Dim Y As Integer, Q As Integer
        Dim CurRec As view_QuarterlyShipAndEmailControl = View_QuarterlyShipAndEmailControlBindingSource.Current
        If Not Integer.TryParse(Me.YearTsTextBox.Text, Y) Then
            Me.YearTsTextBox.Text = If(CurRec IsNot Nothing, CurRec.Year, "")
        End If
        If Not Integer.TryParse(Me.QtrComboBox.Text, Q) Then
            Me.QtrComboBox.Text = If(CurRec IsNot Nothing, CurRec.Qtr, "")
        End If
        If Integer.TryParse(Me.YearTsTextBox.Text, Y) AndAlso Integer.TryParse(QtrComboBox.Text, Q) Then
            Dim Pos = Data.FindIndex(Function(p) p.Year = Y AndAlso p.Qtr = Q)
            Me.View_QuarterlyShipAndEmailControlBindingSource.Position = Pos
        End If
    End Sub

    Private Sub btnMoveLast_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveLast.Click
        MoveLast()
    End Sub

    Private Sub BandedGridViewProcess_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs)
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim CurRec As view_QuarterlyAllProcessAndDelivery = Me.BandedGridViewProcess.GetRow(Me.BandedGridViewProcess.FocusedRowHandle)
            e.Menu.Items.Clear()
            If CurRec.ReportID IsNot Nothing Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("View Report", AddressOf OpenReport) With {.Tag = e.HitInfo.RowHandle})
            End If
        End If
    End Sub

    Private Sub OpenReport(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As view_QuarterlyAllProcessAndDelivery = Me.BandedGridViewProcess.GetRow(rowHandle)
        'If Row.ReportID IsNot Nothing Then
        '    Dim Report = (From A In DB.SCHFED_FORMs Where A.ID = Row.ReportID Select A.RPT_FILE).SingleOrDefault
        '    If Report Is Nothing Then Exit Sub
        '    Dim Path = IO.Path.GetTempFileName
        '    Path = IO.Path.ChangeExtension(Path, "pdf")
        '    IO.File.WriteAllBytes(Path, Report.ToArray)
        '    Process.Start(Path)
        'End If
    End Sub

    Private Async Sub ButtonOptIn_Click(sender As Object, e As EventArgs) Handles btn1099OptForYY.Click, btn1099OptForAllYears.Click, btnW2OptForYY.Click, btnW2OptForAllYears.Click
        If MessageBox.Show("Are you sure you want to opt in?", "Opt in", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> System.Windows.Forms.DialogResult.Yes Then
            Return
        End If
        Dim NewCode As String = ""
        If sender.Equals(btnW2OptForYY) Then
            NewCode = "MM" + YearTsTextBox.Text.Substring(2)
        ElseIf sender.Equals(btnW2OptForAllYears) Then
            NewCode = "MM99"
        ElseIf sender.Equals(btn1099OptForYY) Then
            NewCode = "C9" + YearTsTextBox.Text.Substring(2)
        ElseIf sender.Equals(btn1099OptForAllYears) Then
            NewCode = "C999"
        End If

        txtUdf20.Text = (NewCode + txtUdf20.Text + New String(" ", 50)).ToString().Substring(0, 50).Trim()
        Dim udf = DB.COUSERDEFs.Where(Function(f) f.conum = CoNum).FirstOrDefault()
        udf.udf20_desc = txtUdf20.Text
        DB.SaveChanges()

        If MessageBox.Show("Do you want to reload form to show new changes?", "Reload", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            btnRefresh.PerformClick()
        End If
    End Sub

    Private Async Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadData()
    End Sub
End Class