﻿Imports System.ComponentModel

Public Class frmEmployeeBatchNotesAll

    Private DB As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoCode As Decimal

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PayrollNumber As Decimal?

    Private printingSystem As New DevExpress.XtraPrinting.PrintingSystem()

    Private Sub frmCalendarNotesAll_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If PayrollNumber.GetValueOrDefault = 0 Then PayrollNumber = Nothing

        Me.Label1.Text = "PowerGrid Notes For Co #: " & Me.CoCode & If(PayrollNumber.HasValue, " - Payroll # " & Me.PayrollNumber, "")
        DB = New dbEPDataDataContext(GetConnectionString)

        Dim Data As List(Of pr_batch_note)

        If Not PayrollNumber.HasValue Then
            Data = (From A In DB.pr_batch_notes
                    Join B In DB.pr_batch_lists On A.ListID Equals B.id
                    Where B.conum = CoCode
                    Select A
                    Order By A.NoteID Descending).Take(1000).ToList
        Else
            Data = (From A In DB.pr_batch_notes Where A.Conum = CoCode AndAlso A.PrNum = PayrollNumber Order By A.Priority, A.EmployeeID).ToList
        End If
        Me.pr_batch_noteBindingSource.DataSource = Data
        Me.colPrNum.Visible = Not PayrollNumber.HasValue
        Me.btnPrint.Visible = PayrollNumber.HasValue
        'Me.colPriority.Visible = PayrollNumber.HasValue
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        DB.SubmitChanges()
        Me.DialogResult = DialogResult.OK
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        'Using ps As New DevExpress.XtraPrinting.PrintingSystem()
        Dim link = New DevExpress.XtraPrinting.PrintableComponentLink With {.Component = Me.GridControl1}
        printingSystem.Links.Add(link)
        Dim CoNum = String.Format("Co #: {0} - PR #: {1}", Me.CoCode, Me.PayrollNumber)
        link.PageHeaderFooter = New DevExpress.XtraPrinting.PageHeaderFooter(
        New DevExpress.XtraPrinting.PageHeaderArea(New String() {CoNum, "", "Comments"},
                                                    New System.Drawing.Font("Arial", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte)), DevExpress.XtraPrinting.BrickAlignment.Near),
        New DevExpress.XtraPrinting.PageFooterArea(New String() {"[Page # of Pages #]", "", "[Date Printed] [Time Printed]"}, New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte)), DevExpress.XtraPrinting.BrickAlignment.Near))
        link.CreateDocument()
        link.ShowRibbonPreview(GetActiveLookAndFeel)
        'End Using
        Me.DialogResult = DialogResult.None
    End Sub


    Private Sub riCheckEditDone_CheckedChanged(sender As Object, e As EventArgs) Handles riCheckEditDone.CheckedChanged
        Me.GridView1.PostEditor()
    End Sub

    Private Sub GridView1_CustomDrawCell(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs) Handles GridView1.CustomDrawCell
        If e.Column.FieldName = "CheckNum" AndAlso e.CellValue Is Nothing Then
            e.DisplayText = "Not Paid"
            e.Appearance.BackColor = Color.Red
        End If
    End Sub
End Class