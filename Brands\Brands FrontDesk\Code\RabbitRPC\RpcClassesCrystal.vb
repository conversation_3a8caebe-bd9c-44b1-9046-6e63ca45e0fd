﻿Imports Newtonsoft.Json

Namespace RabbitRpcCrystal
    Public Class RpcClassesCrystal
        Private whoIsCalling As String = ""

        Public Enum FuncStatus
            [Error]
            Success
        End Enum
        Private Shared Function ExecuteRequest(ByVal request As FunctionStatus) As FunctionStatus
            request.UserName = UserName
            Dim response As String
            Dim rpcClient = RpcClientCrystal.Create()
            Dim objToSend = JsonConvert.SerializeObject(request)
            'log4net here the guid before And after same on server...
            Logger.Information($"parseAndExecuteRequest sent objID: {request.ObjId}")
            response = rpcClient.[Call](objToSend)
            'rpcClient.Close()
            Console.WriteLine(String.Format(" [.] Got '{0}'", response))
            Dim ret = JsonConvert.DeserializeObject(Of FunctionStatus)(response)

            If response = "" Then
                Return New FunctionStatus(FuncStatus.Error, "Not Responding.  Try again", Nothing, Nothing)
            End If
            Logger.Information($"parseAndExecuteRequest received objID: {request.ObjId}")
            Return JsonConvert.DeserializeObject(Of FunctionStatus)(response)
        End Function

        Async Function GetCrystalReportParams(ReportPath As String, ByVal params As List(Of String), errors As String) As Task(Of Tuple(Of Boolean, List(Of String)))
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "", "") With {.ReportPath = ReportPath, .UserName = UserName, .FuncToRun = FunctionToRun.GetReportParams, .errors = errors}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Message <> "" Then
                Throw New Exception(serObj.Message)
            ElseIf serObj.Status <> FuncStatus.Success Then
                Throw New Exception("GetCrystalReportParams did not succeed")
            End If

            Dim paramsReturn As List(Of String) = Nothing
            If serObj.Obj IsNot Nothing AndAlso serObj.ToString() <> "" Then
                paramsReturn = serObj.Obj.ToString().Split(",").ToList()
            End If

            Return New Tuple(Of Boolean, List(Of String))(serObj.Status, paramsReturn)
        End Function

        Async Function GetCrystalReportParamsFull(ReportPath As String, ByVal params As ParameterField(), errors As String) As Task(Of Tuple(Of Boolean, ParameterField()))
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "", "") With {.ReportPath = ReportPath, .UserName = UserName, .FuncToRun = FunctionToRun.GetReportParamsFull, .errors = errors}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Message <> "" Then
                Throw New Exception(serObj.Message)
            ElseIf serObj.Status <> FuncStatus.Success Then
                Throw New Exception("GetCrystalReportParams did not succeed")
            End If

            Dim paramsReturn As ParameterField() = Nothing
            If serObj.Params IsNot Nothing Then
                'params = serObj.Params
                paramsReturn = serObj.Params
            End If

            'Return serObj.Status
            Return New Tuple(Of Boolean, ParameterField())(serObj.Status, paramsReturn)
        End Function

        Async Function ProcessCrReportOnRabbit(ReportPath As String, ByVal params As ParameterField(), errors As String, userName As String, encrPwd As String, FileName As String) As Task(Of Tuple(Of Boolean, ParameterField()))
            Dim obj As FunctionStatus = New FunctionStatus(0, "", "", "") With {.ReportPath = ReportPath, .UserName = userName, .EncryptedPassword = encrPwd, .FuncToRun = FunctionToRun.ProcessCrystalReport, .errors = errors, .Params = params}
            Dim serObj As FunctionStatus = ExecuteRequest(obj)

            If serObj.Message <> "" Then
                Throw New Exception(serObj.Message)
            ElseIf serObj.Status <> FuncStatus.Success Then
                Throw New Exception("GetCrystalReportParams did not succeed")
            End If

            Dim paramsReturn As ParameterField() = Nothing
            If serObj.Params IsNot Nothing Then
                'params = serObj.Params
                paramsReturn = serObj.Params
            End If

            If serObj.ReportOutputFile > "" Then
                Dim fileBytes As Byte() = Convert.FromBase64String(serObj.ReportOutputFile)
                IO.File.WriteAllBytes(FileName, fileBytes)
            End If

            'Return serObj.Status
            Return New Tuple(Of Boolean, ParameterField())(serObj.Status, paramsReturn)
        End Function
    End Class

    Public Class FunctionStatus
        Private _status As Int16
        Private _message As String
        Private _obj As Object
        Private _reportObj As Object
        Public Property UserName As String
        Public Property EncryptedPassword
        Public Property Direction As String
        Public Property FuncToRun As FunctionToRun
        Public Property ReportPath As String
        Private Property _Params As ParameterField()
        Public Property ReportOutputFile As String
        Public Property errors As String
        Public Property ObjId As Guid

        Public Property Status As Int16
            Get
                Return _status
            End Get
            Private Set(ByVal value As Int16)
                _status = value
            End Set
        End Property

        Public Sub SetStatus(ByVal Status As Int16)
            Me.Status = Status
        End Sub

        Public Sub SetObj(ByVal obj As Object)
            Me._obj = obj
        End Sub

        Public Sub SetReportObj(ByVal reportObj As Object)
            Me._reportObj = reportObj
        End Sub

        Public Sub SetMessage(ByVal ErrorMessage As String)
            Me.Message = ErrorMessage
        End Sub

        Public Sub SetParams(ByRef Params() As ParameterField)
            Me._Params = Params
        End Sub

        Public Property Message As String
            Get
                Return _message
            End Get
            Private Set(ByVal value As String)
                _message = value
            End Set
        End Property

        Public Property Obj As Object
            Get
                Return _obj
            End Get
            Private Set(ByVal value As Object)
                _obj = value
            End Set
        End Property

        Public Property ReportObj As Object
            Get
                Return _reportObj
            End Get
            Private Set(ByVal value As Object)
                _reportObj = value
            End Set
        End Property

        Public Property Params As ParameterField()
            Get
                Return _Params
            End Get
            Set(ByVal value As ParameterField())
                _Params = value
            End Set
        End Property

        Public Enum FuncStatus

            [Error]
            Success
        End Enum

        Public Sub New(ByVal Status As Int16, ByVal Message As String, ByVal Obj As Object, ByVal ReportObj As Object)
            _status = Status
            _message = Message
            _obj = Obj
            _reportObj = ReportObj
            ObjId = Guid.NewGuid()
        End Sub

        Public Shared Function Success(ByVal obj As Object, reportObj As Object) As FunctionStatus
            Return New FunctionStatus(CType(FuncStatus.Success, Int16), "", obj, reportObj)
        End Function

        Public Shared Function [Error](ByVal Message As String) As FunctionStatus
            Return New FunctionStatus(CType(FuncStatus.[Error], Int16), Message, Nothing, Nothing)
        End Function
    End Class

    Public Class PayrollInfo
        Public Property TotalChecks As Decimal?
        Public Property TotalGross As Decimal?
        Public Property EndDate As DateTime?
    End Class

    Public Enum FunctionToRun
        GetReportParams
        GetReportParamsFull
        ProcessCrystalReport
    End Enum
End Namespace