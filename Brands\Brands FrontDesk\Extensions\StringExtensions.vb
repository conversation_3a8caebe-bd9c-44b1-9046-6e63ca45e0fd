﻿Imports System.Runtime.CompilerServices

Public Module StringExtensions
    <Extension>
    Public Function ToNewString(chars As IEnumerable(Of Char)) As String
        Return New String(chars.ToArray())
    End Function

    <Extension>
    Public Function GetNumeric(value As String) As Integer
        Return Integer.Parse(New String(value.Where(Function(d) Char.IsNumber(d)).ToArray))
    End Function
End Module
