﻿Imports System.ComponentModel
Imports DevExpress.XtraGrid

Public Class frmSearchEmployeeOrEmail

    Private Property db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property SelectedEmployee As EMPLOYEE

    Sub New()
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        TabbedControlGroup1.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
    End Sub

    Private Sub frmSearchEmployee_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        riLueCoName.DataSource = db.view_CompanySumarries.ToList()
        riLueCoName.DisplayMember = "CO_NAME"
        riLueCoName.ValueMember = "CONUM"
        teSearch.Focus()
        teSearch.Select()
    End Sub

    Private Async Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Try
            If teSearch.Text.Length < 3 Then
                DisplayMessageBox("Please enter a minimum of 3 characters.")
                Exit Sub
            End If
            lcRoot.ShowProgessPanel()
            Dim emps As IQueryable(Of EMPLOYEE) = db.EMPLOYEEs
            If rgFindBy.SelectedIndex = 0 Then
                emps = emps.Where(Function(em) em.F_NAME.StartsWith(teSearch.Text))
            ElseIf rgFindBy.SelectedIndex = 1 Then
                emps = emps.Where(Function(em) em.M_NAME.StartsWith(teSearch.Text))
            ElseIf rgFindBy.SelectedIndex = 2 Then
                emps = emps.Where(Function(em) em.L_NAME.StartsWith(teSearch.Text))
            ElseIf rgFindBy.SelectedIndex = 3 Then
                emps = emps.Where(Function(em) em.SSN.StartsWith(teSearch.Text))
            ElseIf rgFindBy.SelectedIndex = 4 Then
                TabbedControlGroup1.SelectedTabPageIndex = 1
                UcEmailsSearch1.LoadData(Nothing, $"%{teSearch.Text}%")
            End If
            If rgFindBy.SelectedIndex < 4 Then
                TabbedControlGroup1.SelectedTabPageIndex = 0
                BindingSource1.DataSource = Await Task.Run(Function() emps.ToList)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error searching for employee", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Sub


    Private Sub GridView1_CustomColumnDisplayText(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs) Handles GridView1.CustomColumnDisplayText
        If e.Column Is colGENDER Then
            If e.Value = 0 Then
                e.DisplayText = "Male"
            ElseIf e.Value = 1 Then
                e.DisplayText = "Female"
            ElseIf e.Value = 2 Then
                e.DisplayText = "Unspecified"
            End If
        End If
    End Sub

    Private Sub GridView1_DoubleClick(sender As Object, e As EventArgs) Handles GridView1.DoubleClick
        Try
            Dim row As EMPLOYEE = GridView1.GetRow(GridView1.FocusedRowHandle)
            If row Is Nothing Then Exit Sub
            MainForm.OpenCompForm(row.CONUM, 0, row.EMPNUM)
        Catch ex As Exception
            DisplayErrorMessage("Error opening company", ex)
        End Try
    End Sub

    Private Sub GridView1_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEditForEditing
        If e.RowHandle = GridControl.AutoFilterRowHandle AndAlso e.Column Is colCoName Then
            e.RepositoryItem = riTeForCompanyNameAutoFilter
        End If
    End Sub
End Class