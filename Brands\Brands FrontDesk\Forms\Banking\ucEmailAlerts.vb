﻿Public Class ucEmailAlerts
    Private db As dbEPDataDataContext

    Public Sub New(comp As COMPANY)
        InitializeComponent()

        Try
            db = New dbEPDataDataContext(GetConnectionString())
            LoadEmails(comp.CONUM)
        Catch ex As Exception
            DisplayErrorMessage("Error in instantiating ucEmailAlerts", ex)
        End Try
    End Sub

    Async Sub LoadEmails(CoNum As Decimal)
        Try
            Dim sql As String = $"exec custom.prc_RptSecurityAlertIncident {CoNum}"
            Me.GridControlAlertEmails.DataSource = Await Threading.Tasks.Task.Run(Function() Query(sql))

            If Me.GridViewAlertEmails.Columns("Dtm") IsNot Nothing Then
                Me.GridViewAlertEmails.Columns("Dtm").DisplayFormat.FormatString = "G"
            End If
            GridViewAlertEmails.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error Loading Emails", ex)
        End Try
    End Sub
End Class
