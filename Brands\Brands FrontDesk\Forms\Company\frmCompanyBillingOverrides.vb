﻿Imports System.ComponentModel
Public Class frmCompanyBillingOverrides

    Private Conum As Integer
    Private DB As dbEPDataDataContext
    Private blnOpenItems As Boolean

    Sub New()
        InitializeComponent()
    End Sub

    Private Sub ucCompanyBillingItems_Load(sender As Object, e As EventArgs) Handles Me.Load
        Try
            'may crash in design in parentform
            If LicenseManager.UsageMode = LicenseUsageMode.Designtime Then Exit Sub

            ucSearchCompany.BindPopupContainerEdit(pceSearchCompany)
            'LoadData()
        Catch ex As Exception
            Logger.Error(ex, "ucCompanyBillingItems_Load")
        End Try
    End Sub

    Sub LoadData()
        Try
            DB = New dbEPDataDataContext(GetConnectionString)

            gcItems.DataSource = Query(Of ACT_ITEM)("SELECT	i.*
FROM	ACT_ITEMS AS i
		INNER JOIN GLOBAL_LISTS gl ON gl.list_name = 'Invoice Report Categories' AND gl.cat_id = i.item_category_id
WHERE	(i.ITEM_TYPE = 'Flat' 
			OR gl.list_value IN ('DIRECT DEPOSIT', 'CHECK', 'NEW HIRE', 'SIGN CHECKS', 'STUFF CHECKS', 'W2')
		) 
		AND i.ITEM_NUM > 0 
		AND i.AACTIVE = 'YES'
ORDER BY 
		i.ITEM_CAT, i.ITEM_NAME, i.STD_PRICE;")

            gcDelivery.DataSource = (From i In DB.dbo_DELIVERies Where i.DEACTIVE = "YES" Select i Order By i.DELDESC).ToList()

            riDivisionLookup.DataSource = (From i In DB.DIVISIONs Where i.CONUM = Conum AndAlso (i.DDIVNUM = 0 OrElse i.DBANKSAME = "NO") Select i.DDIVNUM, i.DDIVNAME, Descr = i.DDIVNUM & "-" & i.DDIVNAME).ToList()

            'annnonmous type cannot add to it
            'gc_Billing_Overrides.DataSource = From i In DB.ACT_AUTO_PriceOverrides Where i.CONUM = iConum Select i.CONUM, i.DIVNUM, i.ITEM_NUM, i.ACTUAL_PRICE, i.Line_Id
            'create a list of ACT_AUTO_PriceOverrides and can add to it
            gc_Billing_Overrides.DataSource = (From i In DB.ACT_AUTO_PriceOverrides Where i.CONUM = Conum).ToList
        Catch ex As Exception
            Logger.Error(ex, "Error in CompanyBillingItems_LoadData")
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles sbSave.Click
        Try
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save billing overrides due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
            blnOpenItems = False
        Catch ex As Exception
            Logger.Error(ex, "Error in CompanyBillingItems_btnSave_Click")
            DisplayErrorMessage("Error saving data", ex)
        End Try
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles sbDelete.Click
        Try
            'remove focus from delete button
            gv_Billing_Overrides.Focus()
            Dim item As ACT_AUTO_PriceOverride = Me.gv_Billing_Overrides.GetFocusedRow
            If item Is Nothing Then Exit Sub

            If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you want to delete item{vbCrLf}""{item.ITEM_NAME}""", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = DialogResult.Yes Then
                DB.ACT_AUTO_PriceOverrides.DeleteOnSubmit(item)
                Me.gv_Billing_Overrides.DeleteRow(Me.gv_Billing_Overrides.FocusedRowHandle)
                blnOpenItems = True
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in CompanyBillingItems_btnDelete_Click")
            DisplayErrorMessage("Error deleting item", ex)
        End Try
    End Sub

    Private Sub gcItems_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles gcItems.MouseDoubleClick
        Try
            Dim hi = Me.gvItems.CalcHitInfo(e.Location)
            If hi.RowHandle >= 0 Then
                Dim item = CType(gvItems.GetRow(hi.RowHandle), ACT_ITEM)
                Dim NewItem = New ACT_AUTO_PriceOverride With {.ITEM_NUM = item.ITEM_NUM, .ITEM_NAME = item.ITEM_NAME, .ACTUAL_PRICE = item.STD_PRICE, .CONUM = Conum}
                DB.ACT_AUTO_PriceOverrides.InsertOnSubmit(NewItem)
                CType(gc_Billing_Overrides.DataSource, List(Of ACT_AUTO_PriceOverride)).Add(NewItem)
                gv_Billing_Overrides.RefreshData()
                gv_Billing_Overrides.MoveLast()
                blnOpenItems = True
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in CompanyBillingItems_gcItems_MouseDoubleClick")
            DisplayErrorMessage("Error in mouse double click", ex)
        End Try
    End Sub

    Private Sub gcDelivery_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles gcDelivery.MouseDoubleClick
        Try
            Dim hi = Me.gvDeliveries.CalcHitInfo(e.Location)
            If hi.RowHandle >= 0 Then
                Dim deliv = CType(gvDeliveries.GetRow(hi.RowHandle), dbo_DELIVERY)

                Dim NewItem = New ACT_AUTO_PriceOverride With {.ITEM_NUM = -1, .ITEM_NAME = deliv.DELDESC, .ACTUAL_PRICE = deliv.STD_PRICE, .CONUM = Conum}
                DB.ACT_AUTO_PriceOverrides.InsertOnSubmit(NewItem)
                CType(gv_Billing_Overrides.DataSource, List(Of ACT_AUTO_PriceOverride)).Add(NewItem)
                gv_Billing_Overrides.RefreshData()
                gv_Billing_Overrides.MoveLast()
                blnOpenItems = True
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in CompanyBillingItems_gcDelivery_MouseDoubleClick")
            DisplayErrorMessage("Error in mouse double click", ex)
        End Try
    End Sub

    Private Sub pceSearchCompany_EditValueChanged(sender As Object, e As EventArgs) Handles pceSearchCompany.EditValueChanged
        Conum = pceSearchCompany.EditValue
        LoadData()
    End Sub
End Class
