﻿Imports System.Text
Imports DevExpress.Spreadsheet
Imports DevExpress.XtraEditors

Public Class frmScorecard
    Private _db As dbEPDataDataContext
    ReadOnly Property DB As dbEPDataDataContext
        Get
            If _db Is Nothing Then _db = New dbEPDataDataContext(GetConnectionString)
            Return _db
        End Get
    End Property

    Private Sub frmScorecard_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Dim depts = (From a In DB.fn_1059Dept).ToList()
            depts.Add(New fn_1059DeptResult With {.Dept = "None"})

            ccbeDept.Properties.DataSource = depts
            ccbeDept.Properties.DisplayMember = "Dept"
            ccbeDept.Properties.DropDownRows = 10
            ccbeDept.Properties.NullText = ""
            ccbeDept.Properties.ValueMember = "Dept"

            Dim matrix = (From m In DB._1059Measures Join s In DB._1059MeasureEmpSetups On m.MeasureId Equals s.MeasureId Where s.GoalValue <> Nothing Select m.MeasureId, m.MeasureName).Distinct().OrderBy(Function(o) o.MeasureName).ToList()
            matrix.Insert(0, New With {Key .MeasureId = -1, Key .MeasureName = "(ALL)"})
            lueMatrix.Properties.DataSource = matrix
            lueMatrix.Properties.DisplayMember = "MeasureName"
            lueMatrix.Properties.DropDownRows = 10
            lueMatrix.Properties.NullText = ""
            lueMatrix.Properties.ValueMember = "MeasureId"
            lueMatrix.ItemIndex = 0

            deFrom.EditValue = DateTime.Today.AddDays(-30)
            deTo.EditValue = DateTime.Today

            Dim lstDateRanges As New List(Of String)()
            lstDateRanges.Add("Custom")
            lstDateRanges.Add("This Week")
            lstDateRanges.Add("Last Week")
            lstDateRanges.Add("This Month")
            lstDateRanges.Add("Last Month")
            lstDateRanges.Add("This Quarter")
            lstDateRanges.Add("Last Quarter")
            lueDateRange.Properties.DataSource = lstDateRanges
            lueDateRange.ItemIndex = 0
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub sbSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        If ccbeAgents.EditValue = "" Then
            XtraMessageBox.Show("Must supply Agent")
            Return
        ElseIf ccbeAgents.EditValue Like "*,*" And lueMatrix.EditValue = "-1" Then
            XtraMessageBox.Show("Must supply single Agent or Matrix")
            Return
        ElseIf Not IsDate(deFrom.EditValue) Or Not IsDate(deTo.EditValue) Then
            XtraMessageBox.Show("Date is missing")
            Return
        ElseIf Convert.ToDateTime(deFrom.EditValue) > Convert.ToDateTime(deTo.EditValue) Then
            XtraMessageBox.Show("Invalid Date range")
            Return
        End If

        SpreadsheetControl1.Visible = False

        'Dim excel As Microsoft.Office.Interop.Excel.Application = Nothing
        'Dim book As Workbook = Nothing

        Try
            'Cannot cast string to int.  EmpId may also contain multiple agents.
            Dim EmpId = ccbeAgents.EditValue.ToString()
            'measure in DB.prc_1059ScoreCardXml accepts an integer.
            Dim MatrixId = IIf(lueMatrix.EditValue = "-1", Nothing, lueMatrix.EditValue)

            Dim data = From d In DB.prc_1059ScoreCardXml(deFrom.EditValue, deTo.EditValue, EmpId.QuoteSQLString(), MatrixId).ToList()
            Dim sb = New StringBuilder(10000)

            Dim dr As prc_1059ScoreCardXmlResult
            For Each dr In data
                sb.Append(dr.DATA + vbCrLf)
            Next

            Dim filename As String = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\\ScoreCard.xml"

            System.IO.File.WriteAllText(filename, sb.ToString())

            'excel = New Microsoft.Office.Interop.Excel.Application()
            'book = excel.Application.Workbooks.OpenXML(filename)
            'excel.Visible = False
            'excel.DisplayAlerts = False

            Dim xlsxFile As String = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\ScoreCard.xlsx"
            'book.SaveAs(xlsxFile, XlFileFormat.xlOpenXMLWorkbook, Missing.Value, Missing.Value, False, False, XlSaveAsAccessMode.xlNoChange,
            '    XlSaveConflictResolution.xlUserResolution, True, Missing.Value, Missing.Value, Missing.Value)
            ucCompanyServices.ConvertXmlToXlsx(filename, xlsxFile)
            SpreadsheetControl1.LoadDocument(xlsxFile)
            SpreadsheetControl1.Visible = True
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
            'Finally
            '    If Not book Is Nothing Then
            '        book.Close(True)
            '        Marshal.ReleaseComObject(book)
            '    End If

            '    If Not excel Is Nothing Then
            '        excel.Quit()
            '        Marshal.ReleaseComObject(excel)
            '    End If
        End Try
    End Sub

    Private Sub ccbeDept_EditValueChanged(sender As Object, e As EventArgs) Handles ccbeDept.EditValueChanged
        Try
            Dim emps = (From emp In DB.fn_1059MeasureUsersByDept(ccbeDept.EditValue) Select emp.EmpNum, emp.Agent Order By Agent).ToList()
            ccbeAgents.Properties.Items.Clear()
            ccbeAgents.Properties.DataSource = emps
            ccbeAgents.Properties.DisplayMember = "Agent"
            ccbeAgents.Properties.DropDownRows = 10
            ccbeAgents.Properties.NullText = ""
            ccbeAgents.Properties.ValueMember = "EmpNum"
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub lueDateRange_EditValueChanged(sender As Object, e As EventArgs) Handles lueDateRange.EditValueChanged
        Try
            Dim today = DateTime.Today
            Select Case lueDateRange.EditValue
                                                                                    'DayOfWeek for Tue is 2 in VS so need to add 1
                Case "This Week"
                    deFrom.EditValue = today.AddDays(-today.DayOfWeek)                  'deduct todays DayOfWeek value.  If today is Tue=2, deduct 2 days
                    deTo.EditValue = CDate(deFrom.EditValue).AddDays(6)
                Case "Last Week"
                    deFrom.EditValue = today.AddDays(-today.DayOfWeek - 7)               'deduct days of week (will get sat of last week) - 6 days
                    deTo.EditValue = CDate(deFrom.EditValue).AddDays(6)
                Case "This Month"
                    deFrom.EditValue = today.AddDays(-(today.Day - 1))                  'deduct day of month -1 (will get first day of month)
                    deTo.EditValue = CDate(deFrom.EditValue).AddMonths(1).AddDays(-1)   'add month to deFrom and deduct one day
                Case "Last Month"
                    deFrom.EditValue = today.AddDays(-(today.Day - 1)).AddMonths(-1)     'deduct day of month -1 (will get first day of month) then add month
                    deTo.EditValue = CDate(deFrom.EditValue).AddMonths(1).AddDays(-1)   'add month to deFrom and deduct one day
                Case "This Quarter"
                    Dim iMonth = today.Quarter * 3 - 2
                    deFrom.EditValue = CDate(iMonth.ToString() + "/1/" + today.Year.ToString())
                    deTo.EditValue = CDate(deFrom.EditValue).AddMonths(3).AddDays(-1)
                Case "Last Quarter"
                    Dim iMonth = today.Quarter * 3 - 2
                    deFrom.EditValue = CDate(iMonth.ToString() + "/1/" + today.Year.ToString()).AddMonths(-3)
                    deTo.EditValue = CDate(deFrom.EditValue).AddMonths(3).AddDays(-1)
            End Select
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub deFrom_EditValueChanged(sender As Object, e As EventArgs) Handles deFrom.EditValueChanged
        Try
            If Not ActiveControl Is Nothing AndAlso ActiveControl.Name <> "lueDateRange" And lueDateRange.EditValue <> "Custom" Then lueDateRange.EditValue = "Custom"
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub deTo_EditValueChanged(sender As Object, e As EventArgs) Handles deTo.EditValueChanged
        Try
            If Not ActiveControl Is Nothing AndAlso ActiveControl.Name <> "lueDateRange" And lueDateRange.EditValue <> "Custom" Then lueDateRange.EditValue = "Custom"
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub ccbeAgents_EditValueChanged(sender As Object, e As EventArgs) Handles ccbeAgents.EditValueChanged
        Try
            SetTitle()
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub lueMatrix_EditValueChanged(sender As Object, e As EventArgs) Handles lueMatrix.EditValueChanged
        Try
            SetTitle()
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message)
        End Try
    End Sub

    Sub SetTitle()
        Try
            If Not ccbeAgents.EditValue Is Nothing AndAlso ccbeAgents.EditValue <> "" AndAlso Not ccbeAgents.EditValue Like "*,*" Then
                Dim name = (ccbeAgents.Text.ToString().Replace("  ", " ").Trim() + " _").Split(" ")
                Text = "SC " + name(0) + name(1).Substring(0, 1)                'FirstName + First char of last name
            ElseIf Not lueMatrix.EditValue = -1 Then
                Text = "SC " + lueMatrix.Text
            Else
                Text = "SC"
            End If
        Catch
            Text = "SC"
        End Try
    End Sub

    Private Sub btnAgents_Click(sender As Object, e As EventArgs) Handles btnAgents.Click
        MainForm.AddOrActivateForm(Of frmAgents)()
    End Sub

    Private Sub btnUsers_Click(sender As Object, e As EventArgs) Handles btnUsers.Click
        MainForm.AddOrActivateForm(Of frm1059UserRelationships)()
    End Sub
End Class