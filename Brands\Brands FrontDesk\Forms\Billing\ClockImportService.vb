﻿''Imports Brands_FrontDesk
''Imports DevExpress.Spreadsheet
''Imports DevExpress.Utils.Menu
''Imports DevExpress.XtraGrid.Views.Grid

'Namespace Billing
'    Public Class ClockImportService
'        Implements IBillingImportService

'        Private IsAodMode As Boolean

'        Public Sub New(_IsAodMode As Boolean)
'            IsAodMode = _IsAodMode
'        End Sub

'        Public SwipeClockData As List(Of SwipeClock)

'        Private _priceCodesList As List(Of Integer)
'        Private _InvoiceDate As DateTime
'        Private _globalBilling As List(Of ACT_ITEM)
'        Private _records As List(Of IRecord)

'        Public Property PriceCodesList As List(Of Integer) Implements IBillingImportService.PriceCodesList
'            Get
'                Return _priceCodesList
'            End Get
'            Set(value As List(Of Integer))
'                _priceCodesList = value
'            End Set
'        End Property

'        Public Property InvoiceDate As DateTime Implements IBillingImportService.InvoiceDate
'            Get
'                Return _InvoiceDate
'            End Get
'            Set(value As DateTime)
'                _InvoiceDate = value
'            End Set
'        End Property

'        Public Property globalBilling As List(Of ACT_ITEM) Implements IBillingImportService.globalBilling
'            Get
'                Return _globalBilling
'            End Get
'            Set(value As List(Of ACT_ITEM))
'                _globalBilling = value
'            End Set
'        End Property

'        Public ReadOnly Property Records As List(Of IRecord) Implements IBillingImportService.Records
'            Get
'                Return SwipeClockData.OfType(Of IRecord).ToList
'            End Get
'        End Property

'        Public Sub Initialize() Implements IBillingImportService.Initialize
'            db = New dbEPDataDataContext(GetConnectionString)
'            PriceCodesList = New List(Of Integer)([Enum].GetValues(GetType(PriceCodes)))
'            globalBilling = db.ACT_ITEMS.Where(Function(b) PriceCodesList.Contains(b.ITEM_NUM)).ToList
'            billingUtilities = New BillingUtilities(Me)
'        End Sub

'        Public Sub ImportFile() Implements IBillingImportService.ImportFile
'            If IsAodMode Then
'                ImportAodFile()
'            Else
'                ImportSwipeClockFile()
'            End If
'        End Sub

'        Private Sub ImportSwipeClockFile()
'            Dim fd = New OpenFileDialog()
'            If fd.ShowDialog() = DialogResult.OK Then
'                Dim book = New Workbook()
'                book.Options.Import.ThrowExceptionOnInvalidDocument = True
'                book.LoadDocument(fd.FileName)
'                Dim sheet = book.Worksheets.ActiveWorksheet
'                Dim list = New List(Of SwipeClock)
'                For index = 1 To sheet.Rows.LastUsedIndex
'                    Dim sc = New SwipeClock()
'                    Dim row = sheet.Rows.Item(index)
'                    sc.LineItem = row(0).DisplayText
'                    sc.ItemDate = row(1).Value.DateTimeValue
'                    sc.Description = row(2).DisplayText
'                    sc.Rate = row(4).Value.NumericValue
'                    sc.TaxRate = row(5).Value.NumericValue
'                    sc.TotalAmount = row(6).Value.NumericValue
'                    sc.ExtDescription = row(7).DisplayText
'                    sc.Client = row(8).DisplayText
'                    sc.Conum = row(9).Value.NumericValue
'                    sc.Employees = row(10).Value.NumericValue
'                    sc.Clocks = Math.Ceiling(row(11).Value.NumericValue)
'                    list.Add(sc)
'                Next
'                SwipeClockData = list
'                InvoiceDate = list.FirstOrDefault(Function(l) l.Description = "Timeclock Service")?.ItemDate
'            End If
'        End Sub

'        Private Sub ImportAodFile()
'            Dim fd = New OpenFileDialog()
'            If fd.ShowDialog <> DialogResult.OK Then Exit Sub
'            Dim book = New Workbook()
'            book.Options.Import.ThrowExceptionOnInvalidDocument = True
'            If Not book.LoadDocument(fd.FileName) Then Exit Sub
'            Dim sheet = book.Worksheets.ActiveWorksheet
'            Dim list = New List(Of SwipeClock)

'            For index = 1 To sheet.Rows.LastUsedIndex
'                Dim row = sheet.Rows.Item(index)
'                Dim exisitngRow = list.SingleOrDefault(Function(aod) aod.Client = row(1).DisplayText.Trim())
'                If exisitngRow IsNot Nothing Then
'                    If row(8).DisplayText = "Active Employees" Then
'                        exisitngRow.Employees = row(9).Value.NumericValue
'                    ElseIf row(8).DisplayText = "Standard User Accounts" Then
'                        exisitngRow.AdditionalUsers = row(9).Value.NumericValue
'                    End If
'                ElseIf row(1).DisplayText.IsNotNullOrWhiteSpace Then
'                    Dim sc = New SwipeClock()
'                    sc.Description = row(0).DisplayText
'                    sc.Client = row(1).DisplayText.Trim
'                    sc.ItemDate = row(7).Value.DateTimeValue
'                    sc.Conum = If((From c In db.CoOptions_Payrolls Where c.AodCode = sc.Client).SingleOrDefault()?.CoNum, 0)

'                    If row(8).DisplayText = "Active Employees" Then
'                        sc.Employees = row(9).Value.NumericValue
'                    ElseIf row(8).DisplayText = "Standard User Accounts" Then
'                        sc.AdditionalUsers = row(9).Value.NumericValue
'                    End If
'                    list.Add(sc)
'                End If
'            Next
'            SwipeClockData = list
'            InvoiceDate = list.FirstOrDefault()?.ItemDate
'        End Sub

'        Public Sub PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs)
'            If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
'                Dim row As SwipeClock = DirectCast(sender, GridView).GetRow(e.HitInfo.RowHandle)
'                e.Menu.Items.Add(New DXMenuItem("Invoice Employees Fee", Sub() InsertInvoiceLog(row.Conum, PriceCodes.EmployeesFee, row.PriceForEmployees, row.Employees)) With {.Enabled = row.PriceForEmployees > 0})
'                e.Menu.Items.Add(New DXMenuItem("Invoice Clock's Fee", Sub() InsertInvoiceLog(row.Conum, PriceCodes.ClocksFee, row.PriceForClocks + row.PriceForBaseFee, row.Clocks)) With {.Enabled = row.PriceForClocks > 0 OrElse row.PriceForBaseFee > 0})
'                If IsAodMode Then
'                    e.Menu.Items.Add(New DXMenuItem("Invoice Additional Users Fee", Sub() InsertInvoiceLog(row.Conum, PriceCodes.AdditionalUsers, row.PriceForAdditionalUsers, row.AdditionalUsers)) With {.Enabled = row.PriceForAdditionalUsers > 0})
'                    'e.Menu.Items.Add(New DXMenuItem("Map Code To Co#", Sub()
'                    '                                                       pccMapCoToAodCode.Tag = row
'                    '                                                       teAodCode.Text = row.Client
'                    '                                                       slueCoNumAodMap.EditValue = row.ClientTag
'                    '                                                       pccMapCoToAodCode.Show()
'                    '                                                   End Sub))
'                Else
'                    'e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Invoice Additional Users", Sub()
'                    '                                                                                      pccAdditionalUsersInvoice.Tag = row
'                    '                                                                                      pccAdditionalUsersInvoice.BringToFront()
'                    '                                                                                      pccAdditionalUsersInvoice.Show()
'                    '                                                                                  End Sub) With {.BeginGroup = True})
'                End If

'                If row.PriceForEmployees > 0 AndAlso (row.PriceForClocks > 0 OrElse row.PriceForBaseFee > 0) Then
'                    Dim caption = $"Invoice {If(row.PriceForEmployees > 0, "Employees Fee && ", "")}{If(row.PriceForClocks > 0 OrElse row.PriceForBaseFee > 0, "Clock's Fee &&", "")}{If(IsAodMode AndAlso row.PriceForAdditionalUsers > 0, "Additional Users Fee", "")}"
'                    caption = caption.RemoveFromEnd(" && ")
'                    e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem(caption, Sub() InvoiceRow(row)))
'                End If
'            End If
'        End Sub

'        'Public Property ActItemAdditionalUsersPrice As ACT_ITEM
'        'Public Property ActItemClocksPrice As ACT_ITEM
'        'Public Property ActItemEmployeesPrice As ACT_ITEM

'        'Public Property InvoiceLogEmployeesFee As List(Of INVOICE_LOG)
'        'Public Property InvoiceLogClocksFee As List(Of INVOICE_LOG)
'        'Public Property InvoiceLogAdditionalUsersFee As List(Of INVOICE_LOG)

'        Private Property db As dbEPDataDataContext
'        Private Property billingUtilities As BillingUtilities

'        Public Sub CalculatePrices() Implements IBillingImportService.CalculatePrices
'            If SwipeClockData Is Nothing Then Exit Sub

'            'ActItemAdditionalUsersPrice = (From b In db.ACT_ITEMs Where b.ITEM_NUM = PriceCodes.AdditionalUsers).Single
'            'ActItemClocksPrice = (From b In db.ACT_ITEMs Where b.ITEM_NUM = PriceCodes.ClocksFee).Single
'            'ActItemEmployeesPrice = (From b In db.ACT_ITEMs Where b.ITEM_NUM = PriceCodes.EmployeesFee).Single

'            'InvoiceLogEmployeesFee = (From i In db.INVOICE_LOGs Where i.ITEM_NUM = PriceCodes.EmployeesFee AndAlso i.ITEM_NAME = GetItemName(PriceCodes.EmployeesFee) AndAlso i.ITEM_DATE.Year = InvoiceDate.Year).ToList()
'            'InvoiceLogClocksFee = (From i In db.INVOICE_LOGs Where i.ITEM_NUM = PriceCodes.ClocksFee AndAlso i.ITEM_NAME = GetItemName(PriceCodes.ClocksFee) AndAlso i.ITEM_DATE.Year = InvoiceDate.Year).ToList()
'            'InvoiceLogAdditionalUsersFee = (From i In db.INVOICE_LOGs Where i.ITEM_NUM = PriceCodes.AdditionalUsers AndAlso i.ITEM_NAME = GetItemName(PriceCodes.AdditionalUsers) AndAlso i.ITEM_DATE.Year = InvoiceDate.Year).ToList()

'            For Each item In SwipeClockData
'                If item.Conum > 0 Then
'                    item.PriceForBaseFee = GetBasePrice(item.Conum)
'                    item.PriceForClocks = GetClocksPrice(item.Conum, item.Clocks, item.PriceForBaseFee > 0)
'                    item.PriceForEmployees = GetEmployeesPrice(item.Conum, item.Employees)
'                    If item.PriceForEmployees < 10 Then 'minimum price for employees fee is $10
'                        item.PriceForEmployees = 10
'                    End If
'                    If IsAodMode Then
'                        item.PriceForAdditionalUsers = GetPriceForAdditionalUsers(item.Conum, item.AdditionalUsers)
'                    End If
'                    item.TotalPrice = item.PriceForBaseFee + item.PriceForEmployees + item.PriceForClocks + item.PriceForAdditionalUsers
'                    GetIsInvoiced(item)
'                End If
'            Next
'        End Sub

'        Private Function GetBasePrice(coNum As Decimal) As Decimal
'            Dim overides = (From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = coNum AndAlso b.ITEM_NUM = PriceCodes.BaseFee)
'            If overides.Any Then
'                Return overides.Single.ACTUAL_PRICE
'            Else
'                Return 0
'            End If
'        End Function



'        Private Function GetPriceForAdditionalUsers(conum As Decimal, users As Decimal) As Decimal
'            Dim overides = (From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = conum AndAlso b.ITEM_NUM = PriceCodes.AdditionalUsers)
'            If overides.Any Then
'                Return overides.Single.ACTUAL_PRICE * users
'            Else
'                Return ActItemAdditionalUsersPrice.STD_PRICE * users
'            End If
'        End Function

'        Private Function GetClocksPrice(coNum As Decimal, clocks As Decimal, OnlyIfOverwritten As Boolean) As Decimal
'            Dim overides = (From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = coNum AndAlso b.ITEM_NUM = PriceCodes.ClocksFee)
'            If overides.Any() Then
'                Return overides.Single.ACTUAL_PRICE * clocks
'            ElseIf OnlyIfOverwritten Then
'                Return 0
'            Else
'                Return ActItemClocksPrice.STD_PRICE * clocks
'            End If
'        End Function

'        Private Function GetEmployeesPrice(coNum As Decimal, empsCount As Integer) As Decimal
'            Dim overides = (From b In db.ACT_AUTO_PriceOverrides Where b.CONUM = coNum AndAlso b.ITEM_NUM = PriceCodes.EmployeesFee).ToList
'            If overides.Any() Then
'                If overides.Count() = 1 Then
'                    If overides.First.PriceFromQty > empsCount Then
'                        Throw New Exception($"Error calcualting Price For Employees For Co#: {coNum}. The Employees Count is less than the minimum Price From Qty.{Environment.NewLine}Please fix and recalcualte the price again.")
'                    Else
'                        Return overides.First.ACTUAL_PRICE * empsCount
'                    End If
'                Else
'                    If overides.Select(Function(a) a.PriceFromQty).Min() > empsCount Then
'                        Throw New Exception($"Error calcualting Price For Employees For Co#: {coNum}. The Employees Count is less than the minimum Price From Qty.{Environment.NewLine}Please fix and recalcualte the price again.")
'                    Else
'                        Return overides.Where(Function(p) p.PriceFromQty <= empsCount).OrderBy(Function(p) p.PriceFromQty).Last.ACTUAL_PRICE * empsCount
'                    End If
'                End If
'            Else
'                Return ActItemEmployeesPrice.STD_PRICE * empsCount
'            End If
'        End Function

'        Private Function GetIsInvoiced(row As SwipeClock) As Boolean
'            If row.PriceForEmployees > 0 Then
'                row.IsPriceForEmployeesInvoiced = (From i In InvoiceLogEmployeesFee Where i.CONUM = row.Conum).Any
'            Else
'                row.IsPriceForEmployeesInvoiced = Nothing
'            End If

'            If row.PriceForClocks > 0 OrElse row.PriceForBaseFee > 0 Then
'                row.IsPriceForClocksInvoiced = (From i In InvoiceLogClocksFee Where i.CONUM = row.Conum).Any
'            Else
'                row.IsPriceForClocksInvoiced = Nothing
'            End If

'            If row.PriceForAdditionalUsers > 0 Then
'                row.IsPriceForAdditionalUsersInvoiced = (From i In InvoiceLogAdditionalUsersFee Where i.CONUM = row.Conum).Any
'            Else
'                row.IsPriceForAdditionalUsersInvoiced = Nothing
'            End If

'            row.IsAllInvoiced = (Not row.IsPriceForEmployeesInvoiced.HasValue OrElse row.IsPriceForEmployeesInvoiced) AndAlso
'        (Not row.IsPriceForClocksInvoiced.HasValue OrElse row.IsPriceForClocksInvoiced) AndAlso (Not IsAodMode OrElse (Not row.IsPriceForAdditionalUsersInvoiced.HasValue OrElse row.IsPriceForAdditionalUsersInvoiced))
'            Return row.IsAllInvoiced
'        End Function

'        Private Enum PriceCodes
'            BaseFee = 450
'            EmployeesFee = 358
'            ClocksFee = 371
'            AdditionalUsers = 419
'        End Enum




'        Public Class SwipeClock
'            Implements IRecord
'            Property Conum As Decimal Implements IRecord.Conum
'            Property LineItem As String
'            Property ItemDate As DateTime
'            Property Description As String
'            Property Rate As Decimal
'            Property TaxRate As Decimal
'            Property TotalAmount As Decimal
'            Property ExtDescription As String
'            Property Client As String
'            Property Employees As Decimal
'            Property Clocks As Decimal
'            Property AdditionalUsers As Decimal
'            Property PriceForEmployees As Decimal
'            Property PriceForClocks As Decimal
'            Property PriceForBaseFee As Decimal
'            Property PriceForAdditionalUsers As Decimal
'            Property TotalPrice As Decimal
'            Property IsPriceForEmployeesInvoiced As Boolean?
'            Property IsPriceForClocksInvoiced As Boolean?
'            Property IsPriceForAdditionalUsersInvoiced As Boolean?
'            Property IsAllInvoiced As Boolean Implements IRecord.IsAllInvoiced
'        End Class

'        Public Sub InvoiceRow(record As IRecord) Implements IBillingImportService.InvoiceRow
'            Dim row = DirectCast(record, SwipeClock)
'            If Not row.IsPriceForEmployeesInvoiced AndAlso row.PriceForEmployees > 0 Then
'                InsertInvoiceLog(row.Conum, PriceCodes.EmployeesFee, row.PriceForEmployees, row.Employees)
'            End If
'            If Not row.IsPriceForClocksInvoiced AndAlso (row.PriceForClocks > 0 OrElse row.PriceForBaseFee > 0) Then
'                InsertInvoiceLog(row.Conum, PriceCodes.ClocksFee, row.PriceForClocks + row.PriceForBaseFee, row.Clocks)
'            End If
'            If IsAodMode Then
'                If Not row.IsPriceForAdditionalUsersInvoiced AndAlso row.PriceForAdditionalUsers > 0 Then
'                    InsertInvoiceLog(row.Conum, PriceCodes.AdditionalUsers, row.PriceForAdditionalUsers, row.AdditionalUsers)
'                End If
'            End If
'        End Sub
'    End Class
'End Namespace