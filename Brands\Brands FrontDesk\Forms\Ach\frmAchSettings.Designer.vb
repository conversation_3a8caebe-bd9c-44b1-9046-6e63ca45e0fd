﻿Namespace Ach
    <Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
    Partial Class frmAchSettings
        Inherits DevExpress.XtraEditors.XtraForm

        'Form overrides dispose to clean up the component list.
        <System.Diagnostics.DebuggerNonUserCode()>
        Protected Overrides Sub Dispose(ByVal disposing As Boolean)
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
            MyBase.Dispose(disposing)
        End Sub

        'Required by the Windows Form Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Windows Form Designer
        'It can be modified using the Windows Form Designer.  
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()>
        Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
            Me.ccbeDefaultStatusFIlter = New DevExpress.XtraEditors.CheckedComboBoxEdit()
            Me.meStatusDropDown = New DevExpress.XtraEditors.MemoEdit()
            Me.btnSave = New DevExpress.XtraEditors.SimpleButton()
            Me.MemoEdit1 = New DevExpress.XtraEditors.MemoEdit()
            Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
            Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.BindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
            Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
            Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
            CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.LayoutControl1.SuspendLayout()
            CType(Me.ccbeDefaultStatusFIlter.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.meStatusDropDown.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.SuspendLayout()
            '
            'LayoutControl1
            '
            Me.LayoutControl1.Controls.Add(Me.TextEdit1)
            Me.LayoutControl1.Controls.Add(Me.ccbeDefaultStatusFIlter)
            Me.LayoutControl1.Controls.Add(Me.meStatusDropDown)
            Me.LayoutControl1.Controls.Add(Me.btnSave)
            Me.LayoutControl1.Controls.Add(Me.MemoEdit1)
            Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
            Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControl1.Name = "LayoutControl1"
            Me.LayoutControl1.Root = Me.LayoutControlGroup1
            Me.LayoutControl1.Size = New System.Drawing.Size(493, 380)
            Me.LayoutControl1.TabIndex = 0
            Me.LayoutControl1.Text = "LayoutControl1"
            '
            'ccbeDefaultStatusFIlter
            '
            Me.ccbeDefaultStatusFIlter.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "StatusDefaultFilter", True))
            Me.ccbeDefaultStatusFIlter.EditValue = ""
            Me.ccbeDefaultStatusFIlter.Location = New System.Drawing.Point(131, 286)
            Me.ccbeDefaultStatusFIlter.Name = "ccbeDefaultStatusFIlter"
            Me.ccbeDefaultStatusFIlter.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.ccbeDefaultStatusFIlter.Size = New System.Drawing.Size(338, 20)
            Me.ccbeDefaultStatusFIlter.StyleController = Me.LayoutControl1
            Me.ccbeDefaultStatusFIlter.TabIndex = 7
            '
            'meStatusDropDown
            '
            Me.meStatusDropDown.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "StatusListString", True))
            Me.meStatusDropDown.Location = New System.Drawing.Point(24, 62)
            Me.meStatusDropDown.Name = "meStatusDropDown"
            Me.meStatusDropDown.Size = New System.Drawing.Size(445, 220)
            Me.meStatusDropDown.StyleController = Me.LayoutControl1
            Me.meStatusDropDown.TabIndex = 6
            '
            'btnSave
            '
            Me.btnSave.Image = Global.Brands_FrontDesk.My.Resources.Resources.apply_16x16
            Me.btnSave.Location = New System.Drawing.Point(413, 346)
            Me.btnSave.Name = "btnSave"
            Me.btnSave.Size = New System.Drawing.Size(68, 22)
            Me.btnSave.StyleController = Me.LayoutControl1
            Me.btnSave.TabIndex = 5
            Me.btnSave.Text = "Save"
            '
            'MemoEdit1
            '
            Me.MemoEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "AutoEmailsSql", True))
            Me.MemoEdit1.Location = New System.Drawing.Point(24, 62)
            Me.MemoEdit1.Name = "MemoEdit1"
            Me.MemoEdit1.Properties.Appearance.Font = New System.Drawing.Font("Consolas", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            Me.MemoEdit1.Properties.Appearance.Options.UseFont = True
            Me.MemoEdit1.Size = New System.Drawing.Size(445, 268)
            Me.MemoEdit1.StyleController = Me.LayoutControl1
            Me.MemoEdit1.TabIndex = 4
            '
            'LayoutControlGroup1
            '
            Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
            Me.LayoutControlGroup1.GroupBordersVisible = False
            Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2, Me.EmptySpaceItem1, Me.TabbedControlGroup1})
            Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
            Me.LayoutControlGroup1.Size = New System.Drawing.Size(493, 380)
            Me.LayoutControlGroup1.TextVisible = False
            '
            'LayoutControlItem2
            '
            Me.LayoutControlItem2.Control = Me.btnSave
            Me.LayoutControlItem2.Location = New System.Drawing.Point(401, 334)
            Me.LayoutControlItem2.Name = "LayoutControlItem2"
            Me.LayoutControlItem2.Size = New System.Drawing.Size(72, 26)
            Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem2.TextVisible = False
            '
            'EmptySpaceItem1
            '
            Me.EmptySpaceItem1.AllowHotTrack = False
            Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 334)
            Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
            Me.EmptySpaceItem1.Size = New System.Drawing.Size(401, 26)
            Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
            '
            'TabbedControlGroup1
            '
            Me.TabbedControlGroup1.Location = New System.Drawing.Point(0, 0)
            Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
            Me.TabbedControlGroup1.SelectedTabPage = Me.LayoutControlGroup3
            Me.TabbedControlGroup1.SelectedTabPageIndex = 0
            Me.TabbedControlGroup1.Size = New System.Drawing.Size(473, 334)
            Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup3, Me.LayoutControlGroup2})
            '
            'LayoutControlGroup3
            '
            Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.LayoutControlItem4, Me.LayoutControlItem5})
            Me.LayoutControlGroup3.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
            Me.LayoutControlGroup3.Size = New System.Drawing.Size(449, 288)
            '
            'LayoutControlItem3
            '
            Me.LayoutControlItem3.Control = Me.meStatusDropDown
            Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem3.Name = "LayoutControlItem3"
            Me.LayoutControlItem3.Size = New System.Drawing.Size(449, 240)
            Me.LayoutControlItem3.Text = "Status Drop Down"
            Me.LayoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem3.TextSize = New System.Drawing.Size(104, 13)
            '
            'LayoutControlItem4
            '
            Me.LayoutControlItem4.Control = Me.ccbeDefaultStatusFIlter
            Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 240)
            Me.LayoutControlItem4.Name = "LayoutControlItem4"
            Me.LayoutControlItem4.Size = New System.Drawing.Size(449, 24)
            Me.LayoutControlItem4.Text = "Default Status Filter: "
            Me.LayoutControlItem4.TextSize = New System.Drawing.Size(104, 13)
            '
            'LayoutControlGroup2
            '
            Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1})
            Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
            Me.LayoutControlGroup2.Size = New System.Drawing.Size(449, 288)
            '
            'LayoutControlItem1
            '
            Me.LayoutControlItem1.Control = Me.MemoEdit1
            Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem1.Name = "LayoutControlItem1"
            Me.LayoutControlItem1.Size = New System.Drawing.Size(449, 288)
            Me.LayoutControlItem1.Text = "Auto Email Sql Script: "
            Me.LayoutControlItem1.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem1.TextSize = New System.Drawing.Size(104, 13)
            '
            'BindingSource1
            '
            Me.BindingSource1.DataSource = GetType(Brands_FrontDesk.Ach.AchSettings)
            '
            'TextEdit1
            '
            Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.BindingSource1, "InitialDirectoryForFiles", True))
            Me.TextEdit1.Location = New System.Drawing.Point(152, 310)
            Me.TextEdit1.Name = "TextEdit1"
            Me.TextEdit1.Size = New System.Drawing.Size(317, 20)
            Me.TextEdit1.StyleController = Me.LayoutControl1
            Me.TextEdit1.TabIndex = 8
            '
            'LayoutControlItem5
            '
            Me.LayoutControlItem5.Control = Me.TextEdit1
            Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 264)
            Me.LayoutControlItem5.Name = "LayoutControlItem5"
            Me.LayoutControlItem5.Size = New System.Drawing.Size(449, 24)
            Me.LayoutControlItem5.Text = "Initial Directory For Files: "
            Me.LayoutControlItem5.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
            Me.LayoutControlItem5.TextSize = New System.Drawing.Size(123, 13)
            Me.LayoutControlItem5.TextToControlDistance = 5
            '
            'frmAchSettings
            '
            Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(493, 380)
            Me.Controls.Add(Me.LayoutControl1)
            Me.Name = "frmAchSettings"
            Me.ShowIcon = False
            Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
            Me.Text = "Ach Settings"
            CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
            Me.LayoutControl1.ResumeLayout(False)
            CType(Me.ccbeDefaultStatusFIlter.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.meStatusDropDown.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
            Me.ResumeLayout(False)

        End Sub

        Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
        Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents MemoEdit1 As DevExpress.XtraEditors.MemoEdit
        Friend WithEvents btnSave As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents BindingSource1 As BindingSource
        Friend WithEvents ccbeDefaultStatusFIlter As DevExpress.XtraEditors.CheckedComboBoxEdit
        Friend WithEvents meStatusDropDown As DevExpress.XtraEditors.MemoEdit
        Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
        Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
        Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    End Class
End Namespace