﻿Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors

Public Class frmColumnTemplateSetup
    Sub New()
        InitializeComponent()
    End Sub

    Private dbTablesDB As dbEPDataDataContext
    Private dbColumnsDB As dbEPDataDataContext

    Private Sub frmColumnTemplateSetup_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            dbTablesDB = New dbEPDataDataContext(GetConnectionString)
            Dim tableList = dbTablesDB.CustomReportTables.ToList()
            tableList.Add(New CustomReportTable() With {.Name = "(All)"})
            gcTables.DataSource = tableList
            riGroupName.Items.Clear()
            riGroupName.Items.AddRange(dbTablesDB.CustomReportColumnTemplates.ToList.Where(Function(f) f.GroupName.IsNotNullOrWhiteSpace).Select(Function(r) r.GroupName).Distinct.ToArray)
            UcColumnTemplateOptions1.LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error loading tables", ex)
        End Try
    End Sub

    Private Sub btnRefreshTables_Click(sender As Object, e As EventArgs) Handles btnRefreshTables.Click
        LoadData()
    End Sub

    Private Sub gvTables_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvTables.FocusedRowObjectChanged
        LoadColumns(e.FocusedRowHandle)
    End Sub

    Private Sub btnAddTable_Click(sender As Object, e As EventArgs) Handles btnAddTable.Click
        AddOrEditTable()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        AddOrEditTable(gvTables.FocusedRowHandle)
    End Sub

    Private Sub btnRemoveTable_Click(sender As Object, e As EventArgs) Handles btnRemoveTable.Click
        DeleteTable(gvTables.FocusedRowHandle)
    End Sub

    Private Sub gvTables_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvTables.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit", Sub() AddOrEditTable(e.HitInfo.RowHandle), My.Resources.edit_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteTable(e.HitInfo.RowHandle), My.Resources.remove_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add", Sub() AddOrEditTable(), My.Resources.add_16x16) With {.BeginGroup = True})
        End If
    End Sub

    Private Sub gvReports_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvReports.PopupMenuShowing
        If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DXMenuItem("Edit", Sub() EditReport(), My.Resources.edit_16x16))
        End If
    End Sub

    Private Sub EditReport(Optional report As CustomReport = Nothing)
        If report Is Nothing Then report = gvReports.GetFocusedRow
        If report IsNot Nothing Then
            Dim frm = New frmCustomReportAddOrEdit(report)
            Dim result = frm.ShowDialog
            If result = DialogResult.OK Then
                If Not dbColumnsDB.SaveChanges Then EditReport(report)
            End If
            LoadColumns()
        End If
    End Sub

    Private Sub AddOrEditTable(Optional rowHandle As Integer? = Nothing)
        Dim row As CustomReportTable = Nothing
        If rowHandle.HasValue Then
            row = gvTables.GetRow(rowHandle)
        End If
        If row?.Name = "(All)" Then
            DisplayMessageBox("Hmmm. You can't edit me, i'm a virtual table only")
            Exit Sub
        End If
        Using frm = New frmTableAddOrEdit(row)
            If frm.ShowDialog = DialogResult.OK Then
                LoadData()
            End If
        End Using
    End Sub

    Private Sub DeleteTable(focusedRowHandle As Integer)
        Try
            Dim row As CustomReportTable = gvTables.GetRow(focusedRowHandle)
            If row Is Nothing Then
                XtraMessageBox.Show("No row selected.")
                Exit Sub
            ElseIf row.Name = "(All)" Then
                DisplayMessageBox("Hmmm. You can't delete me, i'm a virtual table only")
                Exit Sub
            ElseIf XtraMessageBox.Show($"Are you sure you would like delete table {row.Name}", "Delete Table", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                dbTablesDB.CustomReportTables.DeleteOnSubmit(row)
                If dbTablesDB.SaveChanges Then
                    LoadData()
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting table", ex)
        End Try
    End Sub

    Private Sub LoadColumns(Optional rowHandle As Integer? = Nothing)
        Try
            Dim row As CustomReportTable = Nothing
            row = gvTables.GetRow(If(rowHandle, gvTables.FocusedRowHandle))
            dbColumnsDB = New dbEPDataDataContext(GetConnectionString)
            If row Is Nothing Then
                gcColumnTemplate.DataSource = Nothing
                gcReports.DataSource = Nothing
            ElseIf row.Name = "(All)" Then
                gcColumnTemplate.DataSource = dbColumnsDB.CustomReportColumnTemplates.ToList()
                btnAddColumns.Enabled = False
                gcReports.DataSource = Nothing
                gcReports.Enabled = False
            Else
                gcColumnTemplate.DataSource = dbColumnsDB.CustomReportColumnTemplates.Where(Function(t) t.TableId = row.Id).ToList()
                btnAddColumns.Enabled = True
                gcReports.DataSource = row.CustomReportTableLinks.Select(Function(t) t.CustomReport).ToList()
                gcReports.Enabled = True
            End If
            gvColumnTemplate.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error loading columns", ex)
        End Try
    End Sub

    Private Sub btnAddColumns_Click(sender As Object, e As EventArgs) Handles btnAddColumns.Click
        Try
            Dim table As CustomReportTable = gvTables.GetFocusedRow
            Using frm = New frmColumnTemplatOptions(table.Id)
                If frm.ShowDialog = DialogResult.OK Then
                    LoadColumns()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error adding column", ex)
        End Try
    End Sub

    Private Sub gvColumnTemplate_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvColumnTemplate.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteColumnTemplateRow(e.HitInfo.RowHandle), My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub DeleteColumnTemplateRow(rowHandle As Integer)
        Dim row As CustomReportColumnTemplate = gvColumnTemplate.GetRow(rowHandle)
        If row Is Nothing Then
            XtraMessageBox.Show("No row selected.")
            Exit Sub
        End If
        If XtraMessageBox.Show($"Are you sure you would like to delete column [{row.ColumnDescription}]?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            gvColumnTemplate.DeleteRow(rowHandle)
            dbColumnsDB.CustomReportColumnTemplates.DeleteOnSubmit(row)
            dbColumnsDB.SaveChanges()
        End If
    End Sub

    Private Sub gvColumnTemplate_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvColumnTemplate.FocusedRowObjectChanged
        Try
            Dim row As CustomReportColumnTemplate = gvColumnTemplate.GetRow(e.FocusedRowHandle)
            If row IsNot Nothing Then
                UcColumnTemplateOptions1.SetDataSource(row)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error editing column.", ex)
        End Try
    End Sub

    Private Sub btnSaveChanges_Click(sender As Object, e As EventArgs) Handles btnSaveChanges.Click
        dbColumnsDB.SaveChanges()
    End Sub
End Class