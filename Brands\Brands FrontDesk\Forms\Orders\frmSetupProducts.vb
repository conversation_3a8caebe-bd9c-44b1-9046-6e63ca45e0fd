﻿Public Class frmSetupProducts

    Dim db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)

    Sub New()
        InitializeComponent()
        meInstructions.Text = GetUdfValue("Setup Product Instructions")
        slueActItem.AddClearButton
    End Sub

    Private Sub frmSetupProducts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        aCT_ITEMsBindingSource.DataSource = db.ACT_ITEMs.ToList
        suppliesBindingSource.DataSource = db.Supplies.ToList()
        GridView1.BestFitColumns()
    End Sub

    Private Sub slueActItem_EditValueChanged(sender As Object, e As EventArgs) Handles slueActItem.EditValueChanged
        If slueActItem.EditValue Is Nothing OrElse slueActItem.Text.IsNullOrWhiteSpace Then
            lcgItemNameAndPrice.Enabled = True
            teActItemName.Text = ""
            teActItemPrice.EditValue = Nothing
        Else
            lcgItemNameAndPrice.Enabled = False
            Dim row As ACT_ITEM = CType(aCT_ITEMsBindingSource.DataSource, List(Of ACT_ITEM))(slueActItem.Properties.GetIndexByKeyValue(slueActItem.EditValue))
            teActItemName.Text = row.ITEM_NAME
            teActItemPrice.EditValue = row.STD_PRICE
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            If db.SaveChanges() Then
                DialogResult = DialogResult.OK
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes.", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
    End Sub
End Class