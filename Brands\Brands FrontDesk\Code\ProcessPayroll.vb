﻿Imports Brands.Core.ZendeskServices

Public Class ProcessPayroll

    Private DB As dbEPDataDataContext

    Public Property SecondChecksOnly As Boolean
    Public Property IsReadOnly As Boolean
    Public Property AutoIsProcessed As Boolean
    Public Property AutoIsSubmitted As Boolean
    Property ActionList As List(Of ProcessPowergridStatus)
    Property _logger As Serilog.ILogger

    Public ReadOnly Property Logger As WebLogger
        Get
            Return WebLogger.GetInstance
        End Get
    End Property

    Public Event StatusChanged(ByVal ActionNumber As Decimal, ByVal Status As String)

    Private CoNum As Decimal, PrNum As Decimal, SendPayrollToAudit As Boolean
    Private Submit As Boolean = True
    Private API As PPxPayrollInterface
    Private PayRec As pr_batch_in_process
    Private ToVoidCount As Integer
    Private PayrollEnt As PAYROLL
    Private CurrentStep As Decimal
    Private TTO As List(Of pr_batch_override_auto)
    Private CoPayrollOptions As CoOptions_Payroll
    Private TaxOverrides As List(Of pr_batch_override)
    Private NetOverrides As List(Of pr_batch_override)
    Private LocalTaxOverrides As List(Of pr_batch_override)

    Public Sub New(CoNum As Decimal, PrNum As Decimal, sendPayrollToAudit As Boolean)
        _logger = modGlobals.Logger.ForContext(GetType(ProcessPayroll)).ForContext("Conum", CoNum).ForContext("Prnum", PrNum)
        _logger.Debug("lodaing data. Conum: {Conum} Prnum: {Prnum} sendPayrollToAudit: {sendPayrollToAudit} Username: {Username}", CoNum, PrNum, sendPayrollToAudit, UserName)
        Me.CoNum = CoNum
        Me.PrNum = PrNum
        Me.SendPayrollToAudit = sendPayrollToAudit

        API = EP_API()
        API.CompanyID = CoNum
        API.PayrollID = PrNum

        DB = New dbEPDataDataContext(GetConnectionString)
        'DB.LogToConsole()

        'Dim LastPayrollNum = (From A In DB.PAYROLLs Where A.CONUM = CoNum AndAlso A.PAYROLL_STATUS = "Entering Checks" Select New Decimal?(A.PRNUM)).MaxMax.GetValueOrDefault
        Dim LastPayrollNum = (From A In DB.PAYROLLs Where A.CONUM = CoNum AndAlso A.PAYROLL_STATUS = "Entering Checks" Select New Decimal?(A.PRNUM)).ToList().Max.GetValueOrDefault

        If LastPayrollNum <= 0 Then
            Throw New Exception(String.Format("System Error, Payroll not found. Last payroll number is {0}", LastPayrollNum))
        End If

        If API.PayrollID <> LastPayrollNum Then
            Throw New Exception(String.Format("Payroll Number is set to {0}{1}Should be {2}{1}Please report to Hershy", API.PayrollID, vbCrLf, LastPayrollNum))
        End If

        'PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = PrNum AndAlso A.IsDeleted.GetValueOrDefault = False).FirstOrDefault
        PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = PrNum AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
        If PayRec Is Nothing Then
            Throw New Exception("pr_batch_in_process row not found")
        End If

        PayRec.ProcessStatus = "Processing-0.1" 'step number
        CoPayrollOptions = (From A In DB.CoOptions_Payrolls Where A.CoNum = CoNum).SingleOrDefault
        PayRec.IncludeManuals = CoPayrollOptions Is Nothing OrElse nz(CoPayrollOptions.DoNotAutoIncludeManuals, False).GetValueOrDefault = False 'And Not Me.SecondChecksOnly
        PayRec.IncludeVoids = CoPayrollOptions Is Nothing OrElse nz(CoPayrollOptions.DoNotAutoIncludeVoids, False).GetValueOrDefault = False
        If PayRec.ProcessedBy Is Nothing Then
            PayRec.ProcessedBy = UserName
            PayRec.ProcessedDate = Now
            PayRec.ProcessStatus = "Processing-0.1"
        End If
        DB.SaveChanges()

        PayrollEnt = (From A In DB.PAYROLLs Where A.CONUM = CoNum AndAlso A.PRNUM = PrNum).Single
        If CoPayrollOptions Is Nothing Then CoPayrollOptions = New CoOptions_Payroll

        If PayRec.ProcessStatus.Contains("Processing-") Then
            CurrentStep = PayRec.ProcessStatus.Replace("Processing-", "")
        End If

        Dim qTTO = (From A In DB.pr_batch_override_autos
                    Where A.PRNUM = PrNum AndAlso A.CONUM = CoNum
                    Select A.EMPNUM, A.CHK_COUNTER).Distinct.ToList

        TTO = (From A In qTTO Select New pr_batch_override_auto With {.EMPNUM = A.EMPNUM, .CHK_COUNTER = A.CHK_COUNTER}).ToList

        LoadOverrides()

        ActionList = New List(Of ProcessPowergridStatus)
        ActionList.Add(New ProcessPowergridStatus With {.StepName = "Process Power-Grid", .SubStep = "", .Status = If(CurrentStep < 1, "Pending", "Done"), .StepNumber = 1})
        ActionList.Add(New ProcessPowergridStatus With {.StepName = "Calculate Totals", .SubStep = "", .Status = If(CurrentStep < 4, "Pending", "Done"), .StepNumber = 3})
        ActionList.Add(New ProcessPowergridStatus With {.StepName = "Submit Payroll", .SubStep = "", .Status = If(Submit, "Pending", "N/A"), .StepNumber = 4})

        If TTO.Count > 0 Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Check Overrides", .SubStep = "Auto Pays/Deds TTO Overrides", .Status = If(CurrentStep < 2.2, "Pending", "Done"), .StepNumber = 2.1})
        End If
        If TaxOverrides.Count > 0 Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Check Overrides", .SubStep = "Tax Overrides", .Status = If(CurrentStep < 2.3, "Pending", "Done"), .StepNumber = 2.2})
        End If
        If NetOverrides.Count > 0 Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Check Overrides", .SubStep = "Net Overrides", .Status = If(CurrentStep < 2.4, "Pending", "Done"), .StepNumber = 2.3})
        End If
        If LocalTaxOverrides.Count > 0 Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Check Overrides", .SubStep = "LOC Overrides", .Status = If(CurrentStep < 3, "Pending", "Done"), .StepNumber = 2.4})
        End If
        If Not String.IsNullOrEmpty(CoPayrollOptions.Run_prc_PrUtilityImport) Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Utility/Union Import", .SubStep = "", .Status = If(CurrentStep < 1.2, "Pending", "Done"), .StepNumber = 1.2})
        End If

        ToVoidCount = (From A As Void In DB.Voids Where A.CoNum = CoNum AndAlso A.VoidPrNum Is Nothing _
            AndAlso A.CheckDate.HasValue AndAlso PayrollEnt.CHECK_DATE.HasValue _
            AndAlso A.CheckDate.Value.Year = PayrollEnt.CHECK_DATE.Value.Year).Count
        If ToVoidCount > 0 Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Add Pending Voids", .SubStep = "", .Status = If(CurrentStep < 0.5, "Pending", "Done"), .StepNumber = 0.5})
        End If
        If PayRec.IncludeManuals Then
            Dim ManIncludeCount = (From A In DB.MAN_CHK_MASTs Where A.CONUM = CoNum AndAlso A.mpwStatus.ToLower = "new").Count
            If ManIncludeCount > 0 Then
                ActionList.Add(New ProcessPowergridStatus With {.StepName = "Add Manual Checks", .SubStep = "", .Status = If(CurrentStep < 0.25, "Pending", "Done"), .StepNumber = 0.25})
            End If
        End If
        If Not String.IsNullOrWhiteSpace(CoPayrollOptions.SendRegisterBeforSubmitting) Then
            ActionList.Add(New ProcessPowergridStatus With {.StepName = "Send Register", .SubStep = "", .Status = If(CurrentStep < 4, "Pending", "Done"), .StepNumber = 3.5})
        End If
    End Sub

    Private Sub LoadOverrides()
        Dim CheckOverridesP = (From A In DB.pr_batch_overrides
                               Where A.CONUM = CoNum AndAlso A.PRNUM = API.PayrollID).ToList

        TaxOverrides = (From A In CheckOverridesP
                        Where A.FedOverrideAmount.HasValue OrElse A.STOverrideAmount.HasValue OrElse A.DBOverrideAmount.HasValue OrElse A.FLIOverrideAmount.HasValue OrElse
                                  A.TaxFrequency IsNot Nothing OrElse A.ChkType IsNot Nothing OrElse A.OASDIOverrideAmount.HasValue OrElse A.MedicareOverrideAmount.HasValue OrElse
                                  A.ManualCheckNumber.HasValue OrElse A.WrkState IsNot Nothing OrElse A.ResState IsNot Nothing OrElse A.UIState IsNot Nothing
                                ).ToList

        NetOverrides = (From A In CheckOverridesP Where A.NetOverrideAmount.HasValue).ToList
        LocalTaxOverrides = (From A In CheckOverridesP Where A.LOCOverrideAmount.HasValue OrElse Not String.IsNullOrEmpty(A.ChkType)).ToList
    End Sub

    ''' <summary>
    ''' pr_batch_in_processes record should be saved before
    ''' </summary>
    ''' <returns></returns>
    Public Async Function Process(userName As String) As Task(Of Integer)
        _logger.Debug("Entering Process. Conum: {Conum} Prnum: {Prnum} Submit: {Submit} Username: {Username}", CoNum, PrNum, Submit, userName)
        Dim WithErrors As Boolean

        API.CheckPayrollStatus(CoNum)
        'API.PowerGridBatchID = PayRec.PowerGridID

        Dim whoIsCalling As String = ""

        Dim ExistingChecks As New List(Of String)
        If SecondChecksOnly Then
            Dim Q = (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum Select A.EMPNUM, A.CHK_COUNTER).ToList
            ExistingChecks = (From A In Q Select String.Format("{0}-{1}", A.EMPNUM, A.CHK_COUNTER)).ToList
        End If

        Try
            Dim NotesList As New List(Of pr_batch_msg)

Step1:
            'Include manual checks
            If PayRec.IncludeManuals AndAlso Not Me.SecondChecksOnly Then
                Dim ManCheckCount As Integer = 0
                DB.prc_IncludeManualChecksToPayroll(CoNum, PrNum, True, ManCheckCount)
                If ManCheckCount > 0 Then
                    WebLogger.PayrollProcessStep = 0.25
                    SetProcessStatus(0.25, "Processing")
                    Logger.Log(WebLogger.LogLevel.Info, "Adding manual checks")

                    Dim msg = New pr_batch_note With {.ListID = PayRec.PowerGridID,
                                                         .Conum = CoNum,
                                                         .PrNum = PrNum,
                                                         .EnteredBy = userName,
                                                         .rowguid = Guid.NewGuid(),
                                                         .DateEntered = DateTime.Now,
                                                         .Priority = "3-Low",
                                                         .EmployeeID = -997,
                                                         .Note = ManCheckCount & " manual check(s) added to payroll"}
                    DB.pr_batch_notes.InsertOnSubmit(msg)
                    DB.SaveChanges()

                    Logger.Log(WebLogger.LogLevel.Info, String.Format("{0} manual check(s) added", ManCheckCount))
                    SetProcessStatus(0.25, "Done")
                End If
            End If

            'Voids
            If PayRec.IncludeVoids AndAlso Not Me.SecondChecksOnly Then
                If ToVoidCount > 0 Then
                    WithErrors = False
                    WebLogger.PayrollProcessStep = 0.5
                    SetProcessStatus(0.5, "Processing")
                    Logger.Log(WebLogger.LogLevel.Info, String.Format("Adding Void records - {0} record(s)", ToVoidCount))

                    'this is handle duplicate voids 
                    Dim ToVoid = (From A As Void In DB.Voids Where A.CoNum = CoNum AndAlso A.VoidPrNum Is Nothing _
                                AndAlso A.CheckDate.HasValue AndAlso PayrollEnt.CHECK_DATE.HasValue _
                                AndAlso A.CheckDate.Value.Year = PayrollEnt.CHECK_DATE.Value.Year).ToList
                    For Each void_chk In ToVoid
                        Dim chk_mast = (From c In DB.CHK_MASTs Where c.CONUM = CoNum _
                                        AndAlso c.EMPNUM = void_chk.EmpNum _
                                        AndAlso c.CHK_NUM = void_chk.ChkNum _
                                        AndAlso (void_chk.Net.HasValue AndAlso (c.NET.Value = (-void_chk.Net.Value))) _
                                        AndAlso (void_chk.Gross.HasValue AndAlso (c.GROSS.Value = (-void_chk.Gross.Value))) _
                                        AndAlso c.CHK_TYPE.StartsWith("REV")).FirstOrDefault
                        If chk_mast IsNot Nothing Then
                            Dim msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                         .conum = CoNum,
                                                         .empnum = chk_mast.EMPNUM,
                                                         .chk_counter = chk_mast.CHK_COUNTER,
                                                         .msg_type = "VOID_ISSUE",
                                                         .batch_id = PayRec.PowerGridID,
                                                         .Priority = "1-High",
                                                         .msg_body = $"Chk#: {void_chk.ChkNum} was voided during payroll, however this check was already voided in Pr#: {chk_mast.PAYROLL_NUM}, confirm if needs to void again, if not then delete in EP"
                                                         }
                            DB.pr_batch_msgs.InsertOnSubmit(msg)
                            DB.SaveChanges()
                        End If
                    Next

                    Dim ProcessVoids = DB.prc_AddVoidsToPayroll(CoNum, PrNum, True)

                    'check if still there
                    Dim CurrentVoidCount = (From A As Void In DB.Voids Where A.CoNum = CoNum AndAlso A.VoidPrNum Is Nothing _
                                            AndAlso A.CheckDate.HasValue AndAlso PayrollEnt.CHECK_DATE.HasValue _
                                            AndAlso A.CheckDate.Value.Year = PayrollEnt.CHECK_DATE.Value.Year).Count
                    If CurrentVoidCount > 0 Then
                        WithErrors = True
                        Logger.Log(WebLogger.LogLevel.Error, String.Format("Void records not processed."))
                    Else
                        Dim msg = New pr_batch_note With {.ListID = PayRec.PowerGridID,
                                                          .Conum = CoNum,
                                                          .PrNum = PrNum,
                                                          .EnteredBy = userName,
                                                          .rowguid = Guid.NewGuid(),
                                                          .DateEntered = DateTime.Now,
                                                          .Priority = "3-Low",
                                                          .EmployeeID = -997,
                                                          .Note = ToVoidCount & " void check(s) added to payroll"}
                        DB.pr_batch_notes.InsertOnSubmit(msg)
                        DB.SaveChanges()
                        Logger.Log(WebLogger.LogLevel.Info, String.Format("{0} void check(s) added", ToVoidCount))
                    End If

                    SetProcessStatus(0.5, "Done" & If(WithErrors, " With Errors", ""))
                    DB.SaveChanges()
                End If
            End If

            If CurrentStep > 1 Then GoTo Step1_2
            'DisplayProgress("Processing PowerGrid")
            Me.SetProcessStatus(1, "Processing")
            If Me.IsReadOnly Then
                If (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso {"DD", "NORMAL"}.Contains(A.CHK_TYPE)).Any Then
                    'If Not UsePPxLibrary AndAlso Not API.UndoPayroll(CoNum, PrNum, False) Then
                    '    Throw New Exception("Undo-Payroll didn't work.")
                    '    'ElseIf Not API.UndoPayroll(CoNum, PrNum, False, PayRec.PowerGridID) Then
                    'ElseIf Not API.UndoPayroll(CoNum, PrNum, False, API.PowerGridBatchID) Then
                    '    Throw New Exception("Undo-Payroll didn't work.")
                    'End If

                    If Not API.UndoPayroll(CoNum, PrNum, False, API.PowerGridBatchID) Then
                        Throw New Exception("Undo-Payroll didn't work.")
                    End If
                End If
                'Check again
                If (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso {"DD", "NORMAL"}.Contains(A.CHK_TYPE)).Any Then
                    Throw New Exception("Undo-Payroll didn't work. There are still checks in the Chk Master table")
                End If
            End If

            'Reset check counters
            Dim updateCheckCounter As Boolean? = False
            DB.ExecuteCommand($"exec custom.prc_UpdatePowerGridCheckCounters {CoNum}, {PrNum}, '{PayRec.PowerGridID}', {updateCheckCounter}")
            If nz(updateCheckCounter, False).GetValueOrDefault Then
                LoadOverrides()
            End If

            If Not API.ProcessPowerGrid(CoNum, PrNum, PayRec.PowerGridID.ToString()) Then
                'TODO: display ???? i would maybe set the grid to just completed and advise to delete the payroll and open again and use same grid, but you have to check if you save all items from grid
                GoTo Finish
            End If

            'Dim HoursOnly = (From A In DB.pr_batch_hours_only Where A.CONUM = CoNum AndAlso PrNum = A.PRNUM Group By A.EMPNUM Into Group).ToList()
            Dim HoursOnly = (From A In (From a In DB.pr_batch_hours_only Where a.CONUM = CoNum AndAlso PrNum = a.PRNUM).ToList() Group By A.EMPNUM Into Group).ToList()

            For Each record In HoursOnly
                'Find check
                Dim ChkRecord = (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = record.EMPNUM Order By A.CHK_COUNTER).FirstOrDefault
                If ChkRecord Is Nothing Then
                    Dim Hours = " - " & String.Join(vbNewLine & " - ", (From A In record.Group Select String.Format("Pay Code: {0}, Dept: {2} , Hours: {1}", A.PayCode, A.Hours, A.DEPTNUM)).ToArray)
                    Dim msg As New pr_batch_msg With {.id = Guid.NewGuid,
                                                         .batch_id = PayRec.PowerGridID,
                                                         .conum = CoNum, .empnum = record.EMPNUM,
                                                         .msg_body = "Hours (for record only) not added - no check found. add manually in EP with 0 Rate." & vbNewLine & Hours,
                                                         .msg_type = "HOURS_FOR_RECORD", .Priority = "1-High"}
                    DB.pr_batch_msgs.InsertOnSubmit(msg)
                Else
                    'Dim ClNum = (From A In DB.CHK_DET_PAYs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = record.EMPNUM AndAlso A.CHK_COUNTER = ChkRecord.CHK_COUNTER Select New Decimal?(A.CL_NUM)).Max.GetValueOrDefault(0)
                    Dim ClNum = (From A In DB.CHK_DET_PAYs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = record.EMPNUM AndAlso A.CHK_COUNTER = ChkRecord.CHK_COUNTER Select New Decimal?(A.CL_NUM)).ToList().Max.GetValueOrDefault(0)
                    For Each hRecord In record.Group
                        ClNum += 1
                        Dim ChkDetailRecord = New CHK_DET_PAY With {.CONUM = CoNum, .PAYROLL_NUM = PrNum, .EMPNUM = record.EMPNUM, .CHK_COUNTER = ChkRecord.CHK_COUNTER, .CL_NUM = ClNum,
                                                                    .CL_DEPT = hRecord.DEPTNUM, .CL_REG_HRS = hRecord.Hours, .CL_CODE = hRecord.PayCode, .CL_OT_HRS = 0, .rowguid = Guid.NewGuid}
                        DB.CHK_DET_PAYs.InsertOnSubmit(ChkDetailRecord)
                    Next
                End If
                DB.SaveChanges()
            Next
            Me.SetProcessStatus(1, "Done")
            PayRec.ProcessStatus = "Processing-1.2"
            DB.SaveChanges()

Step1_2:    'Run_prc_PrUtilityImport
            If Not String.IsNullOrEmpty(CoPayrollOptions.Run_prc_PrUtilityImport) Then
                WebLogger.PayrollProcessStep = 1.2
                SetProcessStatus(1.2, "Processing")

                Dim UtilityImport As New PayrollUtilityImport(CoNum, DB)
                If UtilityImport.LoadData(PrNum) Then

                    Logger.Log(WebLogger.LogLevel.Info, String.Format("Adding utility import records - {0} record(s)", UtilityImport.UnionPays.Count))
                    UtilityImport.ProcessImport(PrNum)
                Else
                    Logger.Log(WebLogger.LogLevel.Info, String.Format("No utility import records to process"))
                End If

                Logger.Log(WebLogger.LogLevel.Info, "Done")
                SetProcessStatus(1.2, "Done")
                PayRec.ProcessStatus = "Processing-2.1"
                DB.SaveChanges()
            End If

Step2_1:
            'TTO recalculate
            If TTO.Count > 0 Then
                If CurrentStep > 2.1 Then GoTo Step2_2
                SetProcessStatus(2.1, "Processing")
                WebLogger.PayrollProcessStep = 2.1
                Logger.Log(WebLogger.LogLevel.Info, "Calculating Override Checks - " & TTO.Count & " Check(s)")
                For Each itm In TTO
                    Dim results = API.CalculateSingleCheck(CoNum, API.PayrollID, itm.EMPNUM, itm.CHK_COUNTER)
                    Dim ChkRecord = (From A In DB.CHK_MASTs Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER).FirstOrDefault
                    DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, ChkRecord)
                Next
                Logger.Log(WebLogger.LogLevel.Info, "Done")
                SetProcessStatus(2.1, "Done")

                PayRec.ProcessStatus = "Processing-2.2"
                DB.SaveChanges()
            End If

            If TTO.Count > 0 Then
                'Add pr_batch_msg for deductions that the amount <> overide_amount as it may be affected by the check recalculation
                Dim ded = (From d In DB.CHK_DET_DEDs Where d.CONUM = CoNum AndAlso d.PAYROLL_NUM = PrNum).ToList
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, ded)
                Dim ManDeds = (From A In DB.CHK_DET_DEDs
                               Join B In DB.pr_batch_overrides On A.CONUM Equals B.CONUM And A.PAYROLL_NUM Equals B.PRNUM And A.EMPNUM Equals B.EMPNUM And A.CHK_COUNTER Equals B.CHK_COUNTER
                               Where A.CONUM = CoNum AndAlso A.PAYROLL_NUM = PrNum _
                               AndAlso A.CL_TYPE = "Manual" AndAlso (A.CL_AMOUNT <> A.CL_OR_FLAT AndAlso A.CL_OR_PERCENT Is Nothing)
                               Select A.EMPNUM, A.CHK_COUNTER, A.CL_CODE, A.CL_AMOUNT, A.CL_OR_FLAT).ToList
                Dim ManDedsNote = (From A In ManDeds
                                   Select New pr_batch_msg With {
                                                    .id = Guid.NewGuid,
                                                    .conum = CoNum,
                                                    .empnum = A.EMPNUM,
                                                    .msg_type = "DEDAMT",
                                                    .batch_id = PayRec.PowerGridID,
                                                    .msg_body = String.Format("Ded#" & A.CL_CODE & " OR_FLAT is $" & A.CL_OR_FLAT & ", Should Be $" & A.CL_AMOUNT & ", Edit in EP and ReCalculate Check."),
                                                    .chk_counter = A.CHK_COUNTER}
                                                    ).ToList
                NotesList.AddRange(ManDedsNote)
            End If

Step2_2:
            Dim OverrideUtility As New PayrollCheckOverrides(CoNum, PrNum, PayRec.PowerGridID, DB) With {.ExcludedChecks = ExistingChecks}
            AddHandler OverrideUtility.LogEvent, AddressOf Logger.Log

            If CurrentStep > 2.2 Then GoTo Step2_3
            'Tax overrides
            If Not String.IsNullOrEmpty(CoPayrollOptions.ResStateWHOverride) Then
                OverrideUtility.ProcessResStateWHOverride(CoPayrollOptions.ResStateWHOverride)
            End If

            If TaxOverrides.Count > 0 Then
                WebLogger.PayrollProcessStep = 2.2
                SetProcessStatus(2.2, "Processing")
                'logger.Log(WebLogger.LogLevel.Info, String.Format("Calculating Tax Overrides Checks - {0} Check(s)", TaxOverrides.Count))

                OverrideUtility.ProcessTaxOverrides(TaxOverrides)
                Logger.Log(WebLogger.LogLevel.Info, "Done")
                SetProcessStatus(2.2, "Done")
                PayRec.ProcessStatus = "Processing-2.3"
                DB.SaveChanges()
            End If

Step2_3:
            If CurrentStep > 2.3 Then GoTo Step2_4
            'Net Overrides
            If NetOverrides.Count > 0 Then
                WebLogger.PayrollProcessStep = 2.3
                SetProcessStatus(2.3, "Processing")
                'logger.Log(WebLogger.LogLevel.Info, String.Format("Calculating Net Override Checks - {0} Check(s)", NetOverrides.Count))

                OverrideUtility.ProcessNetOverrides(NetOverrides)

                Logger.Log(WebLogger.LogLevel.Info, "Done")
                SetProcessStatus(2.3, "Done" & If(OverrideUtility.WithErrors, " With Errors", ""))
                PayRec.ProcessStatus = "Processing-2.4"
                DB.SaveChanges()
            End If

Step2_4:
            If CurrentStep > 2.4 Then GoTo Step3
            'Local tax overrides, manually update net
            If LocalTaxOverrides.Count > 0 Then
                'WithErros = False
                WebLogger.PayrollProcessStep = 2.4
                SetProcessStatus(2.4, "Processing")
                'logger.Log(WebLogger.LogLevel.Info, String.Format("Calculating Local Tax Overrides Checks - {0} Check(s)", LocalTaxOverrides.Count))
                OverrideUtility.ProcessLocalTaxOverride(LocalTaxOverrides)

                SetProcessStatus(2.4, "Done" & If(OverrideUtility.WithErrors, " With Errors", ""))
                PayRec.ProcessStatus = "Processing-3"
                DB.SaveChanges()
            End If

RoundChecksToNearestDollar:
            If nz(CoPayrollOptions.RoundChecksToNearestDollar, False).GetValueOrDefault Then
                OverrideUtility.RoundChecksToNearestDollar()
            End If
            If nz(CoPayrollOptions.NoExtraTaxesOnSecondCheck, False).GetValueOrDefault Then
                OverrideUtility.RemoveExtraTaxesFromSecondCheck()
            End If

            'Collect Override Notes
            DB.pr_batch_msgs.InsertAllOnSubmit(OverrideUtility.Notes)
            DB.SaveChanges()
Step3:
            If CurrentStep > 3 Then GoTo Step4
            Me.SetProcessStatus(3, "Processing")
            If Not API.CalculateTotals(CoNum, PrNum) Then
                GoTo Finish
            End If
            Me.SetProcessStatus(3, "Done")
            PayRec.ProcessStatus = "Processing-4"

            If PayRec.ZendeskTicketId.HasValue Then
                Await CloseZendeskTicket(PayRec.ZendeskTicketId.Value, userName)
            End If

            DB.SaveChanges()

            WebLogger.PayrollProcessStep = 4

            Dim MessageCount As Integer
            'Save messages

            'If Not UsePPxLibrary Then
            '    If CType(API, PPxPayroll).BatchMessages IsNot Nothing AndAlso CType(API, PPxPayroll).BatchMessages.Count > 0 Then
            '        Dim PayrollMessages = (From A In CType(API, PPxPayroll).BatchMessages
            '                               Group A By A.batch_id, A.chk_counter, Co = A.conum, A.empnum, A.msg_body, A.msg_type
            '                           Into Group Select batch_id, chk_counter, Co, empnum, msg_body, msg_type
            '                           )
            '        For Each pmsg In PayrollMessages
            '            Dim msg As New pr_batch_msg With {.id = Guid.NewGuid,
            '                                              .batch_id = pmsg.batch_id,
            '                                              .chk_counter = pmsg.chk_counter,
            '                                              .conum = pmsg.Co,
            '                                              .empnum = pmsg.empnum,
            '                                              .msg_body = pmsg.msg_body,
            '                                              .msg_type = pmsg.msg_type}
            '            Dim MsgID As Guid
            '            If Guid.TryParse(msg.msg_body, MsgID) Then
            '                msg.msg_body = "{" & msg.msg_body.ToUpper & "}"
            '            End If
            '            DB.pr_batch_msgs.InsertOnSubmit(msg)
            '        Next
            '        DB.SaveChanges()
            '    End If
            'Else
            If CType(API, PPxPayrollRabbit).pr_batch_list IsNot Nothing AndAlso CType(API, PPxPayrollRabbit).pr_batch_list.Count > 0 Then
                Dim PayrollMessages = (From A In CType(API, PPxPayrollRabbit).pr_batch_list
                                       Group A By A.batch_id, A.chk_counter, Co = A.conum, A.empnum, A.msg_body, A.msg_type
                                   Into Group Select batch_id, chk_counter, Co, empnum, msg_body, msg_type
                                   )
                For Each pmsg In PayrollMessages
                    Dim msg As New pr_batch_msg With {.id = Guid.NewGuid,
                                                      .batch_id = pmsg.batch_id,
                                                      .chk_counter = pmsg.chk_counter,
                                                      .conum = pmsg.Co,
                                                      .empnum = pmsg.empnum, 'Int32.Parse(pmsg.empnum),
                                                      .msg_body = pmsg.msg_body,
                                                      .msg_type = pmsg.msg_type}
                    Dim MsgID As Guid
                    If Guid.TryParse(msg.msg_body, MsgID) Then
                        msg.msg_body = "{" & msg.msg_body.ToUpper & "}"
                    End If
                    DB.pr_batch_msgs.InsertOnSubmit(msg)
                Next
                DB.SaveChanges()
            End If
            'End If

            If NotesList.Count > 0 Then
                Dim ManDed = (From A In NotesList)
                For Each Man In ManDed
                    Dim NewNote = New pr_batch_msg With {.id = Man.id,
                                                         .conum = Man.conum,
                                                         .empnum = Man.empnum,
                                                         .msg_type = Man.msg_type,
                                                         .batch_id = Man.batch_id,
                                                         .msg_body = Man.msg_body,
                                                         .chk_counter = Man.chk_counter}
                    DB.pr_batch_msgs.InsertOnSubmit(NewNote)
                Next
                DB.SaveChanges()
            End If
Step4_0:
            _logger.Information("Entering step 4.0")
            WebLogger.PayrollProcessStep = 4

            Me.AutoIsProcessed = True

            'get all messages
            If MessageCount = 0 Then
                'check for notes in our custom table
                MessageCount = (From A In DB.pr_batch_notes Where A.ListID = PayRec.PowerGridID AndAlso A.Priority <> "3-Low").Count
            End If

            If MessageCount = 0 Then
                'get notes with priority less than 3
                MessageCount = (From A In DB.NOTEs
                                Where A.conum = CoNum _
                                AndAlso {"Payroll", "Company", "Employee"}.Contains(A.category) _
                                AndAlso (A.expiration_date Is Nothing Or A.expiration_date > Today) _
                                AndAlso Not (A.priority.StartsWith("3"))
                                ).Count
            End If
            If MessageCount > 0 Then
                Logger.Log(WebLogger.LogLevel.Info, "Payroll contains notes. Submitted for specialist for review")
                Submit = False
            End If
            _logger.Information("Finished step 4.0")
Step4:
            _logger.Information("Entering step 4")
            _logger.Debug("MessageCount: {MessageCount} Submit: {Submit}", MessageCount, Submit)

            If PayrollEnt.PR_DESCR?.StartsWith("W2 Bill") Then
                _logger.Information("Executing custom.prc_InsertNachaPayrollCustom")
                'Insert Nacha for Refund Billing
                DB.ExecuteCommand(String.Format("custom.prc_InsertNachaPayrollCustom {0}, {1}", Me.CoNum, Me.PrNum))
            End If

            If Not SendPayrollToAudit AndAlso Submit Then
                API.CheckPayrollStatus(CoNum)

                Me.SetProcessStatus(4, "Processing")
                Logger.Log(WebLogger.LogLevel.Info, "Submitting Payroll")
                API.SubmitPayroll(CoNum, PrNum, 2, "")
                Logger.Log(WebLogger.LogLevel.Info, "Payroll Completed!")
                Me.SetProcessStatus(4, "Done")

                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, PayRec)
                PayRec.ProcessStatus = "Done"
                If Not DB.SaveChanges() Then
                    _logger.Error("Failed to save payroll status due to concurrency conflict")
                    Throw New InvalidOperationException("Unable to save payroll status due to a conflict with another user's changes.")
                End If

                Me.AutoIsSubmitted = True
            End If
            If SendPayrollToAudit Then
                PayRec.LastOpenedBy = userName
                PayRec.LastOpenedDate = Now
                PayRec.ProcessedBy = userName
                PayRec.CompleteBy = userName
                PayRec.CompleteDate = Now
            End If

            DB.SaveChanges()
            Me.SetProcessStatus(4, "Skipped")
            'Rest ProcessStatus on payroll in process record
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, PayRec)
            PayRec.ProcessStatus = "Ready"
            PayRec.ProcessedBy = userName
            DB.SaveChanges()

            Dim Supp_PR2 = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = API.PayrollID AndAlso A.EmployeeID = -999 AndAlso A.Note = "Supp After Processing").SingleOrDefault
            If Supp_PR2 IsNot Nothing Then
                PayrollEnt.SUPP_PR = "YES"
            End If

            'If supplemental payroll - clear calendar
            Dim CalWasCompleted = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = API.PayrollID AndAlso A.EmployeeID = -998).Any
            If PayrollEnt.SUPP_PR = "YES" AndAlso CalWasCompleted = False Then
                Dim CalEnt = (From A In DB.CALENDARs Where A.cal_id = API.CalendarID).SingleOrDefault
                If CalEnt IsNot Nothing Then
                    CalEnt.payroll_num = Nothing
                    CalEnt.completed = "NO"
                    CalEnt.completed_date = Nothing
                End If
                If Not DB.SaveChanges() Then
                    _logger.Error("Failed to save calendar changes due to concurrency conflict")
                    Throw New InvalidOperationException("Unable to save calendar changes due to a conflict with another user's changes.")
                End If
            ElseIf CalWasCompleted Then
                'set to preveiosly used payroll num
                ResetCalendar(CoNum, PrNum)
            End If
        Catch ex As Exception
            _logger.Error(ex, "Error in processing payroll. Co#: {CoNum} Pr#: {PrNum}", CoNum, PrNum)
            Throw
        End Try
        _logger.Information("Finished step 4")
Finish:
        'HideProgress()
        WebLogger.PayrollProcessStep = 0

        Return PayRec.ID
    End Function

    Private Sub SetProcessStatus(ByVal ActionNumber As Decimal, ByVal Status As String)
        RaiseEvent StatusChanged(ActionNumber, Status)
        _logger.Debug("Set Status. {ActionNumber} {Status}", ActionNumber, Status)
    End Sub

    Private Sub ResetCalendar(CoNum As Decimal, PrNum As Decimal?)
        Using DB = New dbEPDataDataContext(GetConnectionString)
            Dim CalWasCompleted = (From A In DB.pr_batch_notes Where A.Conum = CoNum AndAlso A.PrNum = PrNum AndAlso A.EmployeeID = -998).ToList
            If CalWasCompleted.Count > 0 Then
                For Each note In CalWasCompleted
                    Dim Values = System.Text.RegularExpressions.Regex.Matches(note.Note, ".*?<(.*?):(.*?)\>.*?", System.Text.RegularExpressions.RegexOptions.Compiled)
                    Dim CalID As Integer? = Nothing, CompletedDate As Date? = Nothing
                    Dim v As String
                    PrNum = Nothing
                    For x = 0 To Values.Count - 1
                        Select Case Values(x).Groups(1).Value
                            Case "cal_id"
                                CalID = Values(x).Groups(2).Value
                            Case "payroll_num"
                                PrNum = Values(x).Groups(2).Value
                            Case "completed_date"
                                v = Values(x).Groups(2).Value
                                If Not String.IsNullOrWhiteSpace(v) Then
                                    CompletedDate = v
                                End If
                        End Select
                    Next
                    Dim CalEnt = (From A In DB.CALENDARs Where A.cal_id = CalID).SingleOrDefault
                    If CalEnt IsNot Nothing Then
                        CalEnt.payroll_num = PrNum
                        CalEnt.completed = "YES"
                        CalEnt.completed_date = CompletedDate
                    End If
                Next
                If Not DB.SaveChanges() Then
                    _logger.Error("Failed to save calendar completion status due to concurrency conflict")
                    Throw New InvalidOperationException("Unable to save calendar completion status due to a conflict with another user's changes.")
                End If
            End If
        End Using
    End Sub

    Private Async Function CloseZendeskTicket(ticketId As Long, userName As String) As Task
        Try
            _logger.Information("Entering CloseZendeskTicket TicketId: {TicketId}", ticketId)
            Dim context = New Brands.DAL.EPDATAContext(GetConnectionString)
            Dim updateTicketService = New Brands.Core.ZendeskServices.UpdateTicketService(context, _logger, ZendeskApiUtils.GetZendeskApiByUsername(userName, context))
            Await updateTicketService.ApplyMacro(ticketId, Convert.ToInt64(GetUdfValue("Zendesk_PayrollComplete_MacroId")))
        Catch ex As Exception
            _logger.Error(ex, "Error in CloseZendeskTicket.")
        End Try
    End Function

End Class
