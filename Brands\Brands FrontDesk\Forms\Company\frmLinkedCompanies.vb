﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid

Public Class frmLinkedCompanies

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal

    Dim db As dbEPDataDataContext
    Dim AllCodes As List(Of Decimal)
    Dim Data As List(Of RelatedCompany)

    Private Sub frmLinkedCompanies_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        riCoLookup.DataSource = db.view_CompanySumarries.ToList
        Data = New List(Of RelatedCompany)

        Me.AllCodes = New List(Of Decimal)({Me.CoNum})
        Dim relatedCodes = (GetRelatedCompanies(Me.CoNum))

        For Each c In relatedCodes
            AllCodes.AddRange(GetRelatedCompanies(c))
        Next

        AllCodes = AllCodes.Distinct.ToList

        LoadData(AllCodes)
    End Sub

    Function GetRelatedCompanies(CoCode As Decimal) As List(Of Decimal)
        Dim Udf22 = (From A In db.COUSERDEFs Where A.conum = CoCode Select A.udf22_data).FirstOrDefault

        If Udf22.IsNotNullOrWhiteSpace Then
            Dim d As Decimal
            Dim CoCodes = (From A In Udf22.Split(",") Where A IsNot Nothing AndAlso Decimal.TryParse(nz(A, ""), d) Select Num = d).ToList
            CoCodes = (From A In CoCodes Where Not Me.AllCodes.Contains(A)).Distinct.ToList
            If CoCodes.Count > 0 Then AllCodes.AddRange(CoCodes)
            Return CoCodes
        End If
        Return New List(Of Decimal)
    End Function

    Sub LoadData(Codes As List(Of Decimal))
        Dim q = (From A In db.COMPANies Where Codes.Contains(A.CONUM) Select New RelatedCompany With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .CO_STATUS = A.CO_STATUS}).ToList
        Me.Data.AddRange(q)
        Me.COMPANYBindingSource.DataSource = Data
        Me.GridView1.RefreshData()
    End Sub

    Private Sub GridView1_ShowingEditor(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles GridView1.ShowingEditor
        If GridView1.FocusedRowHandle <> DevExpress.XtraGrid.GridControl.NewItemRowHandle Then
            e.Cancel = True
        End If
    End Sub

    Private Sub GridView1_ValidatingEditor(sender As Object, e As DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs) Handles GridView1.ValidatingEditor
        If GridView1.FocusedColumn.FieldName.ToUpper = "CONUM" Then
            If e.Value Is Nothing Then
                e.Valid = False
            Else
                Dim d As Decimal = e.Value
                If (From A In Me.Data Where A.CONUM = d).Any Then
                    e.ErrorText = "Company # {0} is already in the list".FormatWith(d)
                    e.Valid = False
                    DisplayMessageBox(e.ErrorText)
                End If
            End If
        End If
    End Sub

    Private Sub GridView1_InvalidValueException(sender As Object, e As DevExpress.XtraEditors.Controls.InvalidValueExceptionEventArgs) Handles GridView1.InvalidValueException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore
    End Sub

    Private Sub GridView1_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridView1.RowUpdated
        Dim row As RelatedCompany = e.Row
        Dim NewCo = row.CONUM
        Dim co = (From A In db.COMPANies Where A.CONUM = NewCo Select A.CO_NAME, A.CO_STATUS).FirstOrDefault
        row.CO_NAME = co.CO_NAME
        row.CO_STATUS = co.CO_STATUS
        GridView1.RefreshRow(e.RowHandle)

        Me.AllCodes.Add(NewCo)
        Dim related = GetRelatedCompanies(NewCo)
        If related.Count > 0 Then
            LoadData(related)
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Dim Removed = (From A In Data Where A.IsRemoved = True Select A.CONUM).ToList
        Dim Related = (From A In Me.Data Where A.IsRemoved = False Order By A.CONUM Select A.CONUM).ToList
        Dim Msg = "You are about to update UDF22 on " & Data.Count & " companies." & vbCrLf & "Continue?"
        If DevExpress.XtraEditors.XtraMessageBox.Show(Msg, "Confirm Update", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = DialogResult.Yes Then
            If Related.Count > 0 Then
                Dim UDF = String.Join(",", Related)
                If Related.Count = 1 AndAlso Related(0) = Me.CoNum Then UDF = String.Empty
                Dim sql = $"UPDATE dbo.COUSERDEFS SET udf22_data = {UDF.QuoteSQLString()} WHERE conum IN (" & String.Join(",", Related) & ")"
                Dim results = db.ExecuteCommand(sql)
            End If

            For Each rCo In Removed
                Dim udf = (From A In db.COUSERDEFs Where A.conum = rCo).FirstOrDefault
                Dim udf22 = udf.udf22_data
                If udf22.IsNotNullOrWhiteSpace Then
                    Dim s = udf22.Split(",").ToList
                    For Each itm In Related
                        If s.Contains(itm) Then
                            s.Remove(itm)
                        End If
                    Next
                    If s.Count = 1 AndAlso s(0) = rCo Then s.Clear() 'if it's the only company, just clear it
                    udf.udf22_data = String.Join(",", s)
                End If
            Next
            If Removed.Count > 0 Then
                db.SubmitChanges()
            End If
            Me.DialogResult = DialogResult.OK
        End If
    End Sub

    Private Sub GridView1_RowStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs) Handles GridView1.RowStyle
        If e.RowHandle < 0 Then Return
        Dim row As RelatedCompany = Me.GridView1.GetRow(e.RowHandle)
        If row.IsRemoved Then
            e.HighPriority = True
            e.Appearance.Font = New Font(e.Appearance.Font, FontStyle.Strikeout)
        ElseIf row.CONUM = Me.CoNum Then
            e.HighPriority = True
            e.Appearance.Font = New Font(e.Appearance.Font, FontStyle.Bold)
        End If
    End Sub

    Private Sub GridView1_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridView1.CellValueChanged
        If e.Column.FieldName.ToUpper = "CONUM" Then
            Me.GridView1.UpdateCurrentRow()
        End If
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Add", Sub()
                                                                             Me.GridView1.FocusedRowHandle = DevExpress.XtraGrid.GridControl.NewItemRowHandle
                                                                             GridView1.FocusedColumn = GridColumn1
                                                                             GridView1.ShowEditor()
                                                                         End Sub, My.Resources.add_16x16))
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Remove", click:=AddressOf DeleteRow, image:=My.Resources.remove_16x16))
        End If
    End Sub

    Sub DeleteRow()
        Dim Ix = Me.GridView1.FocusedRowHandle
        Dim row As RelatedCompany = Me.GridView1.GetRow(Ix)
        If row.CONUM = Me.CoNum Then Return
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = DialogResult.No Then Return
        row.IsRemoved = True
        Me.GridView1.RefreshRow(Ix)
    End Sub

    Private Sub GridView1_ShownEditor(sender As Object, e As EventArgs) Handles GridView1.ShownEditor
        Dim view As GridView = TryCast(sender, GridView)
        view.GridControl.BeginInvoke(New MethodInvoker(Sub()
                                                           Dim edit As PopupBaseEdit = TryCast(view.ActiveEditor, PopupBaseEdit)
                                                           If edit Is Nothing Then
                                                               Return
                                                           End If
                                                           edit.ShowPopup()
                                                       End Sub))
    End Sub
End Class

Class RelatedCompany
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CONUM As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CO_NAME As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CO_STATUS As String
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsRemoved As Boolean
End Class