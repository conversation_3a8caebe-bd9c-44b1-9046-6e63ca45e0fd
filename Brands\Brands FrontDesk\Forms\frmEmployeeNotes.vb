﻿Imports System.ComponentModel
Public Class frmEmployeeNotes

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmpNotes As List(Of NOTE)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property LineNotes As List(Of pr_batch_note)

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ListID As Guid
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeeID As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CheckNum As Integer
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PrNum As Decimal

    Dim _DB As dbEPDataDataContext

    Private Sub frmEmployeeNotes_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub LoadData()
        _DB = New dbEPDataDataContext(GetConnectionString)
        EmpNotes = (From A In _DB.NOTEs Where A.conum = Me.CoNum _
                          AndAlso (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) _
                          AndAlso A.category = "Employee" AndAlso A.empnum = EmployeeID).ToList

        Me.NOTEBindingSource.DataSource = EmpNotes
        Me.LineNotes = (From A In _DB.pr_batch_notes Where A.ListID = ListID AndAlso A.EmployeeID = EmployeeID AndAlso A.CheckNum = CheckNum AndAlso A.Conum = Me.CoNum AndAlso A.PrNum = Me.PrNum).ToList
        Me.PrbatchnoteBindingSource.DataSource = LineNotes
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        For Each n In LineNotes
            If n.NoteID = 0 Then
                n.ListID = Me.ListID
                n.Conum = Me.CoNum
                n.PrNum = Me.PrNum
                n.EmployeeID = Me.EmployeeID
                n.CheckNum = Me.CheckNum
                n.EnteredBy = UserName
                n.DateEntered = Now
                _DB.pr_batch_notes.InsertOnSubmit(n)
            End If
        Next
        _DB.SubmitChanges()
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub


    Private Sub gridViewLineNotes_PopupMenuShowing(ByVal sender As System.Object, ByVal e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gridViewLineNotes.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            e.Menu.Items.Clear()
            Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete Note", AddressOf OnDeleteRowClick, ImageCollection1.Images(0))
            mItem.Tag = e.HitInfo.RowHandle
            e.Menu.Items.Add(mItem)
        End If
    End Sub

    Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As pr_batch_note = Me.gridViewLineNotes.GetRow(rowHandle)
        Me.gridViewLineNotes.DeleteRow(rowHandle)
        If Row IsNot Nothing AndAlso Row.NoteID > 0 Then
            _DB.pr_batch_notes.DeleteOnSubmit(Row)
        End If
    End Sub

    Private Sub btnEditNotes_Click(sender As Object, e As EventArgs) Handles btnEditNotes.Click
        Dim frm = New frmNotes(CoNum, EmployeeID)
        frm.ShowDialog()
        LoadData()
    End Sub
End Class