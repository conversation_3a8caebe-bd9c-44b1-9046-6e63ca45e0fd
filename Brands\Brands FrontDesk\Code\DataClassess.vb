﻿Imports System.ComponentModel
Imports System.ComponentModel.DataAnnotations.Schema
Imports System.Data
'Imports System.Data.Linq.Mapping
Imports System.Data.SqlClient
Imports Brands_FrontDesk

Partial Public Class EMPLOYEE
    <NotMapped>
    Private _OriginaDDSplitlMet1 As String

    Private Sub OnDD_SPLIT_MET_1Changing(ByVal value As String)
        _OriginaDDSplitlMet1 = Me.DD_SPLIT_MET_1
    End Sub

    Private Sub OnDD_SPLIT_MET_1Changed()
        If Me.DD_SPLIT_AMT_1.HasValue Then
            If Not (_OriginaDDSplitlMet1 & "").Contains("Percent") AndAlso (Me.DD_SPLIT_MET_1 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_1 = Me.DD_SPLIT_AMT_1.Value / 100
            End If
        End If
    End Sub


    <NotMapped>
    Public Property FormattedDDAmount1() As String
        Get
            If String.IsNullOrEmpty(Me.DD_SPLIT_MET_1) OrElse Not Me.DD_SPLIT_AMT_1.HasValue Then Return String.Empty
            If Me.DD_SPLIT_MET_1.Contains("Percent") Then
                Return Me.DD_SPLIT_AMT_1.Value.ToString("p")
            Else
                Return Me.DD_SPLIT_AMT_1.Value.ToString("c")
            End If
        End Get
        Set(ByVal value As String)
            Dim valueD As Decimal
            If Decimal.TryParse(nz(value, ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, valueD) Then
                If (Me.DD_SPLIT_MET_1 & "").Contains("Percent") Then
                    valueD = valueD / 100
                End If
                Me.DD_SPLIT_AMT_1 = valueD
            End If
        End Set
    End Property

    Private _OriginaDDSplitlMet2 As String

    Private Sub OnDD_SPLIT_MET_2Changing(ByVal value As String)
        _OriginaDDSplitlMet2 = Me.DD_SPLIT_MET_2
    End Sub

    Private Sub OnDD_SPLIT_MET_2Changed()
        If Me.DD_SPLIT_AMT_2.HasValue Then
            If Not (_OriginaDDSplitlMet2 & "").Contains("Percent") AndAlso (Me.DD_SPLIT_MET_2 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_2 = Me.DD_SPLIT_AMT_2.Value / 100
            ElseIf (_OriginaDDSplitlMet2 & "").Contains("Percent") AndAlso Not (Me.DD_SPLIT_MET_2 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_2 = Me.DD_SPLIT_AMT_2.Value * 100
            End If
        End If
    End Sub

    <NotMapped>
    Public Property FormattedDDAmount2() As String
        Get
            If String.IsNullOrEmpty(Me.DD_SPLIT_MET_2) OrElse Not Me.DD_SPLIT_AMT_2.HasValue Then Return String.Empty
            If Me.DD_SPLIT_MET_2.Contains("Percent") Then
                Return Me.DD_SPLIT_AMT_2.Value.ToString("p")
            Else
                Return Me.DD_SPLIT_AMT_2.Value.ToString("c")
            End If
        End Get
        Set(ByVal value As String)
            Dim valueD As Decimal
            If Decimal.TryParse(nz(value, ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, valueD) Then
                If (Me.DD_SPLIT_MET_2 & "").Contains("Percent") Then
                    valueD = valueD / 100
                End If
                Me.DD_SPLIT_AMT_2 = valueD
            End If
        End Set
    End Property

    Private _OriginaDDSplitlMet3 As String

    Private Sub OnDD_SPLIT_MET_3Changing(ByVal value As String)
        _OriginaDDSplitlMet3 = Me.DD_SPLIT_MET_3
    End Sub

    Private Sub OnDD_SPLIT_MET_3Changed()
        If Me.DD_SPLIT_AMT_3.HasValue Then
            If Not (_OriginaDDSplitlMet3 & "").Contains("Percent") AndAlso (Me.DD_SPLIT_MET_3 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_3 = Me.DD_SPLIT_AMT_3.Value / 100
            ElseIf (_OriginaDDSplitlMet3 & "").Contains("Percent") AndAlso Not (Me.DD_SPLIT_MET_3 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_3 = Me.DD_SPLIT_AMT_3.Value * 100
            End If
        End If
    End Sub

    <NotMapped>
    Public Property FormattedDDAmount3() As String
        Get
            If String.IsNullOrEmpty(Me.DD_SPLIT_MET_3) OrElse Not Me.DD_SPLIT_AMT_3.HasValue Then Return String.Empty
            If Me.DD_SPLIT_MET_3.Contains("Percent") Then
                Return Me.DD_SPLIT_AMT_3.Value.ToString("p")
            Else
                Return Me.DD_SPLIT_AMT_3.Value.ToString("c")
            End If
        End Get
        Set(ByVal value As String)
            Dim valueD As Decimal
            If Decimal.TryParse(nz(value, ""), Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, valueD) Then
                If (Me.DD_SPLIT_MET_3 & "").Contains("Percent") Then
                    valueD = valueD / 100
                End If
                Me.DD_SPLIT_AMT_3 = valueD
            End If
        End Set
    End Property


    Private _OriginaDDSplitlMet4 As String

    Private Sub OnDD_SPLIT_MET_4Changing(ByVal value As String)
        _OriginaDDSplitlMet4 = Me.DD_SPLIT_MET_4
    End Sub

    Private Sub OnDD_SPLIT_MET_4Changed()
        If Me.DD_SPLIT_AMT_4.HasValue Then
            If Not (_OriginaDDSplitlMet4 & "").Contains("Percent") AndAlso (Me.DD_SPLIT_MET_4 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_4 = Me.DD_SPLIT_AMT_4.Value / 100
            ElseIf (_OriginaDDSplitlMet4 & "").Contains("Percent") AndAlso Not (Me.DD_SPLIT_MET_4 & "").Contains("Percent") Then
                Me.DD_SPLIT_AMT_4 = Me.DD_SPLIT_AMT_4.Value * 100
            End If
        End If
    End Sub

    <NotMapped>
    Public Property FormattedDDAmount4() As String
        Get
            If String.IsNullOrEmpty(Me.DD_SPLIT_MET_4) OrElse Not Me.DD_SPLIT_AMT_4.HasValue Then Return String.Empty
            If Me.DD_SPLIT_MET_4.Contains("Percent") Then
                Return Me.DD_SPLIT_AMT_4.Value.ToString("p")
            Else
                Return Me.DD_SPLIT_AMT_4.Value.ToString("c")
            End If
        End Get
        Set(ByVal value As String)
            Dim valueD As Decimal
            If Decimal.TryParse(value, Globalization.NumberStyles.Any, Globalization.CultureInfo.CurrentCulture, valueD) Then
                If (Me.DD_SPLIT_MET_4 & "").Contains("Percent") Then
                    valueD = valueD / 100
                End If
                Me.DD_SPLIT_AMT_4 = valueD
            End If
        End Set
    End Property


End Class

Class SuppliesOrderItemDisplay
    Property ProductID As Integer
    Property ProductName As String
    Property QtyPerPack As Integer
    Property PricePerPack As Decimal
    Property PackQty As Integer
    Property IsClock As Boolean
    Property WaiveCharge As Boolean
    Property WaiveChargeReason As String
End Class

Public Class CompanySummary
    Property CONUM As Decimal
    Property CO_NAME As String
    Property PR_CONTACT As String
    Property CO_DBA As String
    Property CO_PHONE As String
    Property CO_FAX As String
    Property CO_EMAIL As String
    Property CO_STATUS As String
    Property FED_ID As String
    <NotMapped>
    Public ReadOnly Property CoNumAndName() As String
        Get
            Return "{0} - {1}".FormatWith(CONUM, CO_NAME)
        End Get
    End Property
End Class

Partial Public Class PAYROLL
    <NotMapped>
    Public ReadOnly Property Frequency As String
        Get
            If Me.FED_UCI.ToString.Contains("1") Then
                Return Me.PAY_FREQ
            ElseIf Me.FED_UCI.ToString.Contains("2") Then
                Return Me.PAY_FREQ2
            ElseIf Me.FED_UCI.ToString.Contains("3") Then
                Return Me.PAY_FREQ3
            Else
                Return Nothing
            End If
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property TotalChecks As Integer
        Get
            Return If(Me.NUM_CHECKS.HasValue, Me.NUM_CHECKS.Value, 0) + If(Me.NUM_MANUAL.HasValue, Me.NUM_MANUAL.Value, 0) + If(Me.NUM_TAX.HasValue, Me.NUM_TAX.Value, 0) + If(Me.NUM_VOID.HasValue, Me.NUM_VOID.Value, 0)
        End Get
    End Property
End Class

Partial Public Class NOTE
    <NotMapped>
    Public ReadOnly Property ParsedNote As String
        Get
            Dim rtb As New RichTextBox
            rtb.Rtf = Me.note
            Return rtb.Text
        End Get
    End Property

End Class

Partial Public Class COMPANY

    <NotMapped>
    Public ReadOnly Property ShipAddress1
        Get
            If SHIPSAME = "YES" Then
                Return CO_NAME
            Else
                Return SH_NAME
            End If
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property ShipAddress2
        Get
            If SHIPSAME = "YES" Then
                Return CO_STREET
            Else
                Return SH_STREET
            End If
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property ShipAddress3
        Get
            If SHIPSAME = "YES" Then
                Return CO_CITY & ", " & CO_STATE & " " & CO_ZIP
            Else
                Return SH_CITY & ", " & SH_STATE & " " & SH_ZIP
            End If
        End Get
    End Property

    <Column, NotMapped>
    Public Property ShipSameBool As Boolean
        Get
            Return SHIPSAME.FromYesNoString()
        End Get
        Set
            SHIPSAME = ToYesNoString(Value)
        End Set
    End Property

End Class

Partial Public Class SuppliesOrderItem
    <NotMapped>
    Property ProductName As String
End Class

Partial Public Class CALENDAR
    <NotMapped>
    Property IsSelectedForPowergrid As Boolean
End Class

Partial Public Class prc_YearEnd_WithNoDecPayrollResult
    <NotMapped>
    Property IsSelect As Boolean
End Class

Partial Public Class view_QuarterlyShipAndEmailControl
    <NotMapped>
    Public ReadOnly Property QEDate
        Get
            Dim D = New Date(Me.Year, 1, 1).AddMonths(Me.Qtr * 3)
            Return D
        End Get
    End Property
End Class

Partial Public Class OTHER_PAY
    <NotMapped>
    Property Activate As Boolean
End Class

Partial Public Class DEDUCTION
    <NotMapped>
    Property Activate As Boolean
End Class

Partial Public Class STATE_EE_INFO
    <NotMapped>
    Public ReadOnly Property StateName As String
        Get
            Return USStates.GetStateName(Me.STATE)
        End Get
    End Property
End Class

Partial Public Class LOCAL_EE_INFO
    <NotMapped>
    Property LocalDescription As String

    ''Added by solomon
    ''do not attempt to import timestamp in dbml.  conflicts with employee trigger deleting the record as a result the record is not found
    'Property timestamp As System.Data.Linq.Binary
End Class

'Partial Public Class view_Calendar
'    Property CalenderNotes As List(Of CalendarNote)
'End Class

'<Global.System.Data.Linq.Mapping.TableAttribute()> _
'Partial Public Class prc_BrandsPowerGridColumnsResult
'    Property HasData As Boolean
'End Class

'Partial Public Class pr_batch_list
'    Public Property b_run_add_checks As Boolean
'    Public Property b_run_auto_hours As Boolean
'    Public Property b_run_auto_pays As Boolean
'    Public Property b_run_auto_deds As Boolean
'    Public Property b_run_auto_memos As Boolean
'    Public Property b_run_sick As Boolean
'    Public Property b_run_vacation As Boolean
'    Public Property b_run_personal As Boolean
'    Public Property b_run_pay_salary As Boolean
'    Public Property b_run_override_rate As Boolean
'    Public Property b_run_dd_flag As Boolean
'End Class
Partial Public Class view_MinimumWageReq

    <NotMapped>
    Private Property _isChecked As Boolean = True


    <NotMapped>
    <DefaultValue(True)> Public Property IsChecked() As Boolean
        Get
            If Me.effective_date = Me.NewMinWageDate AndAlso Me.NewMinWageDate > Today Then Return False
            Return _isChecked
        End Get
        Set(ByVal value As Boolean)
            If Me.effective_date = Me.NewMinWageDate AndAlso Me.NewMinWageDate > Today Then
                _isChecked = False
            Else
                _isChecked = value
            End If

        End Set
    End Property
End Class

Partial Public Class prc_MinimumWageRequirementsResult
    <NotMapped>
    Private Property _UpdateTypeOriginal
    <NotMapped>
    Public Property UpdateTypeOriginal As String
        Get
            Return _UpdateTypeOriginal
        End Get
        Set(value As String)
            _UpdateTypeOriginal = value
        End Set
    End Property
End Class

Partial Public Class pr_batch_msg
    <NotMapped>
    Public Property Priority As String
    <NotMapped>
    Public Property IsChecked As Boolean
End Class

Partial Public Class DEPARTMENT
    <NotMapped>
    Public ReadOnly Property DeptNumDesc As String
        Get
            Return Me.DEPTNUM & ": " & Me.DEPT_DESC
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property DeptKey As String
        Get
            Return Me.DIVNUMD & "-" & Me.DEPTNUM
        End Get
    End Property

End Class

Partial Public Class DIVISION
    <NotMapped>
    Public ReadOnly Property DivNumName As String
        Get
            Return Me.DDIVNUM & ": " & Me.DDIVNAME
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property Descr As String
        Get
            Return $"{DDIVNUM}-{DDIVNAME}"
        End Get
    End Property
End Class

Partial Public Class EMPLOYEE
    <NotMapped>
    Public ReadOnly Property FullName As String
        Get
            Return $"{Me.F_NAME} {Me.M_NAME}{If(Me.M_NAME.IsNullOrWhiteSpace = True, "", " ")}{Me.L_NAME}"
        End Get
    End Property
End Class

Public Class RunAutoStatus
    Property CalID As Integer
    Property CoNum As Decimal
    Property CoName As String
    Property Status As String
    Property Details As String
    Property CheckDate As Date?
    Property EndDate As Date?
    Property RunDate As Date?
    Property IsYearEnd As Boolean
    Property IsLogged As Boolean
    Property PrDesc As String
    Property VoidOnly As Boolean?
    Property SUPP_PR As String
End Class

Public Class CalendarInfo
    Property CalendarSet As Integer
    Property Prd As Integer
    Property IsSupp As Boolean
    Property LastOfMonth As String
    Property Frequency As String
    Property EndDate As Date
    Property IsFirstOfQtr As Boolean
    Property IsLastOfQtr As Boolean
End Class

Public Class ProcessPowergridStatus
    Property StepName As String
    Property SubStep As String
    Property Status As String
    Property SubStatus As String
    Property Details As String
    Property StepNumber As Decimal
End Class

Public Class TableChange
    Public Property TableName As String
    Public Property Key As String
    Public Property FieldName As String
    Public Property OldValue As String
    Public Property NewValue As String

    Public Overrides Function ToString() As String
        Return TableName.PadRight(30) & (Key & "").PadRight(20) & FieldName.PadRight(30) & (OldValue & "").PadRight(50) & (NewValue & "").PadRight(50)
    End Function
End Class

Partial Public Class pr_batch_employee_change
    Public Function GetFormattedString() As String
        If Me.change_log Is Nothing Then
            Return ""
        Else
            Dim sb As New System.Text.StringBuilder
            Dim Lines = (From A In Me.change_log.Split(vbCrLf) Select New TableChange With {.TableName = A.Substring(0, 30).Trim,
                                                                                            .Key = A.Substring(30, 20).Trim,
                                                                                            .FieldName = A.Substring(50, 30).Trim,
                                                                                            .OldValue = A.Substring(80, 50).Trim,
                                                                                            .NewValue = A.Substring(130, 50).Trim}
                                                                                        ).ToList
            Dim Tables = (From A In Lines Order By A.TableName, A.Key, A.FieldName
                          Group A By A.TableName, A.Key Into Group)

            For Each g In Tables
                sb.AppendLine(g.TableName & If(Not String.IsNullOrEmpty(g.Key), " - " & g.Key, ""))
                For Each v In g.Group
                    sb.AppendLine(vbTab & "   " & v.FieldName & ": <" & v.NewValue & "> - Was<" & v.OldValue & ">")
                Next
                sb.AppendLine()
            Next
            Return sb.ToString
        End If
    End Function
End Class

Partial Public Class pr_batch_row
    <NotMapped>
    Property ImportNote As String
End Class

Public Class RateCode
    Property RateCode As String
    Property Amount As Decimal?
    Property Division As Decimal?
    Property Department As Decimal?
End Class

' we need that to work around the following problem https://www.devexpress.com/Support/Center/Question/Details/Q502377
Partial Public Class view_FaxAndEmail
    <NotMapped>
    Public ReadOnly Property DeliveryCoNum() As Decimal
        Get
            Return If(CoNum.HasValue, CoNum.Value, 0)
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property CoNameCoNum() As Decimal
        Get
            Return If(CoNum.HasValue, CoNum.Value, 0)
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property SpecialistCoNum() As Decimal
        Get
            Return If(CoNum.HasValue, CoNum.Value, 0)
        End Get
    End Property

    <NotMapped>
    Public Property LastSpecialist As String


    <NotMapped>
    Private _categoryReadOnly As String

    <NotMapped>
    Public Property CategoryReadOnly() As String
        Get
            Return IIf(_categoryReadOnly.IsNotNullOrWhiteSpace, _categoryReadOnly, Category)
        End Get
        Set(ByVal value As String)
            _categoryReadOnly = value
        End Set
    End Property

    <NotMapped>
    Public ReadOnly Property Locked As String
        Get
            Return If(PayrollOpenedBy.IsNotNullOrWhiteSpace, $"{PayrollOpenedBy} - {PayrollOpenedOn?.ToString((If(PayrollOpenedOn?.Date = Now.Date, "h:mm tt", "")))}", "")
        End Get
    End Property
End Class

Partial Public Class prc_GetVoidsToVoidResult
    <NotMapped>
    Private _Status As String = "Include In Payroll"
    <NotMapped>
    Public Property Status() As String
        Get
            Return If(String.IsNullOrEmpty(_Status), "Include In Payroll", _Status)
        End Get
        Set(ByVal value As String)
            _Status = value
        End Set
    End Property

End Class

Partial Public Class COOPTION
    <NotMapped>
    Public ReadOnly Property TaxService As String
        Get
            If DOSTSFED = "YES" Then
                Return "CTS"
            ElseIf TOTAL_TAX = "YES" Then
                Return "TTF"
            Else
                Return "N/A"
            End If
        End Get
    End Property
End Class

Public Class ReportResults
    Sub New()
        Paths = New List(Of String)
    End Sub

    Public Property Cancalled As Boolean = False
    <Column, NotMapped>
    Public Property Paths As List(Of String)
    Public Property Rows As DataRow()
    Public Property AllFileExist As Boolean = False
    Public Overridable Property Comp As COMPANY
    Public Overridable Property EmailTemplate As ReportEmailTeplate
End Class


Public Class EmployeeEmail
    Public Property Email As String
    Public Property Name As String
    Public Property IsWebPost As Boolean
    Public Property CoNum As Decimal?
    Public Property EmpCoNum As Decimal?
    Public Property EmpNum As Decimal?
    Public Overrides Function ToString() As String
        Return Email
    End Function
End Class

Partial Public Class DEPARTMENT
    <NotMapped>
    Public ReadOnly Property Descr
        Get
            Return DEPTNUM & "-" & DEPT_DESC
        End Get
    End Property
End Class

'added by solomon on Jan 14, 16
Partial Public Class FaxCategoryStruct
    <NotMapped>
    Private _catPayrollAbbrevReadOnly As String
    <NotMapped>
    Public Property CatPayrollAbbrevReadOnly() As String
        Get
            If Category.Equals("Payroll") Then
                Return String.Empty
            ElseIf Category.StartsWith("Payroll-") Then
                Return Category.Substring("Payroll-".Length)
            ElseIf Category.StartsWith("Payroll ") Then
                Return Category.Substring("Payroll ".Length)
            Else
                Return Category
            End If
        End Get
        Set(ByVal value As String)
            _catPayrollAbbrevReadOnly = value
        End Set
    End Property
End Class

Partial Public Class DOCUMENT
    <NotMapped>
    Public Property ShowToClientBool As Boolean
        Get
            Return FromYesNoString(show2client)
        End Get
        Set(value As Boolean)
            show2client = value.ToYesNoString
        End Set
    End Property
End Class

Public Class CustomSqlParameter

    Public Sub New()

    End Sub

    Public Sub New(name As String, paramType As SqlDbType)
        ParameterName = name
        SqlDbType = paramType
    End Sub

    Public Sub New(param As SqlClient.SqlParameter)
        ParameterName = param.ParameterName
        SqlDbType = param.SqlDbType
        Size = param.Size
        IsNullable = param.IsNullable
        Value = param.Value
    End Sub

    Public Property ParameterName As String

    Public Property SqlDbType As SqlDbType

    Public Property Size As Integer

    Public Property IsNullable As Boolean

    Public Property Value As Object

    Public Property DropDownOptions As String

    Public Function ToSqlParameter() As SqlClient.SqlParameter
        Dim p As New SqlClient.SqlParameter With {.ParameterName = ParameterName, .SqlDbType = SqlDbType, .Size = Size, .IsNullable = IsNullable, .Value = Value}
        If Value Is Nothing OrElse Value.ToString().IsNullOrWhiteSpace Then
            p.SqlValue = DBNull.Value
        End If
        Return p
    End Function
End Class

'Partial Public Class prc_TaxReconListResult

'    Public Property TotalDiffrence As Decimal?
'        Get
'            Return split_due - cash_flow_sum
'        End Get
'        Set(value As Decimal?)

'        End Set
'    End Property
'End Class

Partial Public Class CHK_MAST
    <NotMapped>
    Public Property IsChecked As Boolean
End Class


Partial Public Class ReportEmailTeplate
    <NotMapped>
    Public Property IsSetOnCompany As String
    <NotMapped>
    Public ReadOnly Property ReportPath As String
        Get
            Dim uncPath = "\\brands.local\dfs\Reports\"
            Return Path.Replace("R:\", uncPath).Replace("r:\", uncPath)
        End Get
    End Property
End Class

Partial Class co_contact
    <NotMapped>
    Public Property pr_contact_Bool As Boolean
        Get
            Return pr_contact.FromYesNoString()
        End Get
        Set(value As Boolean)
            pr_contact = value.ToYesNoString
        End Set
    End Property

    <NotMapped>
    Public Property hr_contact_Bool As Boolean
        Get
            Return hr_contact.FromYesNoString()
        End Get
        Set(value As Boolean)
            hr_contact = value.ToYesNoString
        End Set
    End Property

    <NotMapped>
    Public Property qyend_contact_Bool As Boolean
        Get
            Return qyend_contact.FromYesNoString()
        End Get
        Set(value As Boolean)
            qyend_contact = value.ToYesNoString
        End Set
    End Property

    <NotMapped>
    Public Property co_cpa_Bool As Boolean
        Get
            Return co_cpa.FromYesNoString()
        End Get
        Set(value As Boolean)
            co_cpa = value.ToYesNoString
        End Set
    End Property

    <NotMapped>
    Public Property co_principal_Bool As Boolean
        Get
            Return co_principal.FromYesNoString()
        End Get
        Set(value As Boolean)
            co_principal = value.ToYesNoString
        End Set
    End Property
End Class


'Partial Class ReportQueue
'Public Property ProcessingDuration As TimeSpan
'    Get
'        Return TimeSpan.FromTicks(ProcessingDurationTicks)
'    End Get
'    Set(value As TimeSpan)
'        ProcessingDurationTicks = value.Ticks
'    End Set
'End Property
'End Class

Public Class EmployeeEndEditResults
    Public Sub New(result As DialogResult, emp As EMPLOYEE, Changes As String, IsNewEmployee As Boolean)
        Me.Result = result
        Me.emp = emp
        Me.Changes = Changes
        Me.IsNewEmployee = IsNewEmployee
    End Sub

    Public Property Result As DialogResult
    Public Property emp As EMPLOYEE
    Public Property Changes As String
    Public Property IsNewEmployee As Boolean
End Class

Namespace dsMainTableAdapters
    Partial Class NachaTaxDraftsTableAdapter
        Public Sub SetCommandTimeOut(ByVal timeOut As Integer)
            For Each command As SqlCommand In Me.CommandCollection
                command.CommandTimeout = timeOut
            Next
        End Sub
    End Class
End Namespace


Public Class BadgeCounters
    Public Property OpenPayrollTicketsCount As Integer
    Public Property OpenGeneralTicketsCount As Integer
    Public Property PrInProcessCount As Integer
End Class


Public Class PayrollPeriodDates

    Property [Set] As Integer
    Property Used As Boolean
    Property PayFreq As String
    Property PeriodStartDate As Date?
    Property PeridoEndDate As Date?

End Class

Partial Class prc_PayrollsIncompleteResult
    Public Property Note As String
    Public Property NotePriority As Int16
End Class

Public Class InvoiceForCo
    Public Property InvNum As Int32
    Public Property InvKey As Guid
End Class

Public Class TableInfo
    Public Property object_id As Int32
    Public Property TableName As String
End Class

Public Class TableCols
    Public Property ColName As String
    Public Property SysType As String
End Class

Partial Public Class pr_batch_msg
    <NotMapped>
    Public Property AgentRequired As Boolean = False
End Class

Public Class SqlParamIntString
    Public Property id As Int32
    Public Property value As String
End Class

Partial Public Class OtherStatesFilingControl
    Public ReadOnly Property ProcessFolder As String
        Get
            Return Me.state
        End Get
    End Property

    Public ReadOnly Property FilesFolder As String
        Get
            Return Me.state
        End Get
    End Property

    Public ReadOnly Property CoStatus As String
        Get
            If Company Is Nothing Then
                Return ""
            ElseIf Company.CO_STATUS = "Active Status" Then
                Return ""
            Else
                Return Company.CO_STATUS
            End If
        End Get
    End Property
    Public ReadOnly Property Review As String
        Get
            'If NsfAging IsNot Nothing OrElse NsfAmount IsNot Nothing OrElse Special_Filing_Method <> "" OrElse UdfExclCTF = True OrElse UdfExclNonTax = True OrElse UdfExclOther = True OrElse CoStatus <> "" Then
            If NsfAging IsNot Nothing OrElse NsfAmount IsNot Nothing OrElse Special_Filing_Method <> "" OrElse UdfExclNonTax = True OrElse UdfExclOther = True OrElse CoStatus <> "" Then
                Return "For Review"
            Else
                Return ""
            End If
        End Get
    End Property

    Public ReadOnly Property UdfExclOther_String As String
        Get
            Return If(UdfExclOther, "Exclude", "")
        End Get
    End Property

    Public ReadOnly Property UdfExclNonTax_String As String
        Get
            Return If(UdfExclNonTax, "Exclude", "")
        End Get
    End Property

    'Public ReadOnly Property UdfExclCTF_String As String
    '    Get
    '        Return If(UdfExclCTF, "Exclude", "")
    '    End Get
    'End Property
End Class

Partial Public Class TaxNotice
    <NotMapped>
    Public Property ClientActive As String
    <NotMapped>
    Public Property ClientActNoticePrd As String
    <NotMapped>
    Public Property ClientNsfNoticePrd As String
    <NotMapped>
    Public Property ClientInactiveStatus As String
    <NotMapped>
    Public Property HasJson As Boolean?
    Public ReadOnly Property InTable As Boolean
        Get
            Return ID > 0
        End Get
    End Property
    <NotMapped>
    Public Property FileName As String
End Class

'Partial Public Class CoOptions_Payroll
'    Public Property MinSubmissionDays As Byte
'    Public Property DontEnforcePlaid As Byte
'End Class

'Public Class FilingControls
'    Property ID As Int16
'    Property FileControl As String
'    Property IsYtd As Boolean?
'    Property Seq As Byte?
'    Property RibbonCaption As String
'    Property GridCaption As String
'    Property ControlSrc As String
'    Property ControlTable As String
'    Property SrcScriptID As Int16?
'    Property UpdateScriptID1 As Int16?
'    Property UpdateScriptID2 As Int16?
'    Property ShowUpdateScript1 As Boolean?
'    Property ShowUpdateScript2 As Boolean?
'    Property UpdateText1 As String
'    Property YearField As String
'    Property InsertParams As String
'    Property UpdateParamControl1 As String
'    Property UpdateParamControl2 As String
'    Property ShowToggleCustomFilter As Boolean?
'    Property Active As Boolean?
'    Property DevOnly As Boolean?
'    Property UpdateUrlText1 As String
'    Property UpdateText2 As String
'    Property UpdateUrlText2 As String
'    Property ToggleFilter As String
'    Property Cat As String
'End Class