﻿Imports System.Data
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports Microsoft.EntityFrameworkCore

Public Class frmTaxSheet
    Private _TaxSheet As TaxSheetEnum
    Private DB As dbEPDataDataContext

    Public ReadOnly Property TaxSheet As TaxSheetEnum
        Get
            Return _TaxSheet
        End Get
    End Property


    Public Sub New(TaxSheet As TaxSheetEnum, Text As String)

        ' This call is required by the designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.

        _TaxSheet = TaxSheet
        GridView1.ViewCaption = Text
        Me.Text = Text

        If TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then
            GridView1.OptionsSelection.MultiSelect = True
            GridView1.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect
        End If
    End Sub

    Public Enum TaxSheetEnum
        PaymentControlSheet = 1
        ManTaxAdjustCtrl = 2
        TaxCarryFwdSheet
    End Enum

    Sub LoadData()
        Try
            If Not DB Is Nothing Then
                If DB.GetChangeSet().Deletes.Count > 0 OrElse DB.GetChangeSet().Updates.Count > 0 OrElse DB.GetChangeSet().Inserts.Count > 0 Then
                    If XtraMessageBox.Show($"There are uncommited changes{vbCrLf} Are you sure you would like to proceed,{vbCrLf}you may be loosing unsaved changes", "Refresh?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) <> System.Windows.Forms.DialogResult.Yes Then
                        Return
                    End If
                End If
            End If

            DB = New dbEPDataDataContext(GetConnectionString())
            Dim dtCols As DataTable
            Dim Col As DevExpress.XtraGrid.Columns.GridColumn
            GridView1.Columns.Clear()
            dtCols = Query($"SELECT * FROM custom.FD_GridControlColumns WHERE Src = '{_TaxSheet.ToString}' AND Level = 1 Order By VisibleIndex")
            GridView1.SetCols(dtCols)

            Dim ColId = GridView1.Columns.Where(Function(c) c.FieldName.ToUpper() = "ID").FirstOrDefault()
            If ColId IsNot Nothing Then
                ColId.SortMode = DevExpress.XtraGrid.ColumnSortMode.Custom
                ColId.SortOrder = DevExpress.Data.ColumnSortOrder.Descending
            End If

            If _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then
                Dim dtCols2 = Query($"SELECT * FROM custom.FD_GridControlColumns WHERE Src = '{_TaxSheet.ToString}' AND Level = 2 Order By VisibleIndex")
                GridView2.Columns.Clear()
                GridView2.SetCols(dtCols2)

                Dim dtCols3 = Query($"SELECT * FROM custom.FD_GridControlColumns WHERE Src = '{_TaxSheet.ToString}' AND Level = 3 Order By VisibleIndex")
                GridView3.Columns.Clear()
                GridView3.SetCols(dtCols3)

                ' Try using DataSet approach for DevExpress Master-Detail
                Dim ds As New DataSet()

                ' Load master data
                Dim masterData = DB.view_BankingManualTaxAdjCtrls.OrderByDescending(Function(o) o.ID).ToList()
                Dim masterTable = ConvertToDataTable(masterData, "view_BankingManualTaxAdjCtrls")
                ds.Tables.Add(masterTable)

                ' Load detail data
                Dim detailData = DB.view_BankingManualTaxAdjCtrlDetails.ToList()
                Dim detailTable = ConvertToDataTable(detailData, "view_BankingManualTaxAdjCtrlDetails")
                ds.Tables.Add(detailTable)

                ' Load payment data
                Dim paymentData = DB.view_BankingManualTaxAdjCtrlPayments.ToList()
                Dim paymentTable = ConvertToDataTable(paymentData, "view_BankingManualTaxAdjCtrlPayments")
                ds.Tables.Add(paymentTable)

                ' Create relationships
                Try
                    Dim relation1 = New DataRelation("view_BankingManualTaxAdjCtrlDetails",
                                                   masterTable.Columns("ID"),
                                                   detailTable.Columns("MasterID"))
                    ds.Relations.Add(relation1)

                    Dim relation2 = New DataRelation("view_BankingManualTaxAdjCtrlPayments",
                                                   detailTable.Columns("ID"),
                                                   paymentTable.Columns("DetailID"))
                    ds.Relations.Add(relation2)

                    Debug.WriteLine($"Master records: {masterTable.Rows.Count}")
                    Debug.WriteLine($"Detail records: {detailTable.Rows.Count}")
                    Debug.WriteLine($"Payment records: {paymentTable.Rows.Count}")
                    Debug.WriteLine($"Relations created: {ds.Relations.Count}")

                Catch ex As Exception
                    Debug.WriteLine($"Error creating relations: {ex.Message}")
                End Try

                ' Set the DataSource
                GridControl1.DataSource = ds
                GridControl1.DataMember = "view_BankingManualTaxAdjCtrls"

                GridView1.BestFitColumns()
                lciConumsDelimited.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            ElseIf _TaxSheet = TaxSheetEnum.PaymentControlSheet Then
                'GridControl1.DataSource = DB.BankTaxPmtCtrls.OrderByDescending(Function(o) o.ID)
                GridControl1.DataSource = DB.BankTaxPmtCtrls.OrderByDescending(Function(o) o.ID).ToList()
                GridView1.BestFitColumns()

                Dim ColDepDate = GridView1.Columns.Where(Function(c) c.FieldName.ToUpper() = "DEPOSITDATE").FirstOrDefault()
                If ColDepDate IsNot Nothing Then
                    Dim colRepRICB As Repository.RepositoryItemComboBox = ColDepDate.ColumnEdit
                    colRepRICB.Items.AddRange(Query(Of String)("SELECT TOP 8 CONVERT(varchar, GETDATE() + rn.RN - 1, 101) FROM custom.RowNum rn").ToList())
                End If
            ElseIf _TaxSheet = TaxSheetEnum.TaxCarryFwdSheet Then
                'GridControl1.DataSource = DB.TaxCarryFwdSheets.OrderByDescending(Function(o) o.ID)
                GridControl1.DataSource = DB.TaxCarryFwdSheets.OrderByDescending(Function(o) o.ID).ToList()
                GridView1.BestFitColumns()
                lciConumsDelimited.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            End If

            For Each Col In GridView1.Columns
                Dim rowFound = dtCols.Select("FieldName = '" + Col.FieldName + "'")

                If rowFound.Count = 1 Then
                    If rowFound(0)("AutoWidth") = False Then
                        Col.Width = rowFound(0)("Width")
                    End If
                End If
            Next
            GC.Collect()
        Catch ex As Exception
            DisplayErrorMessage("Error in load data", ex)
        End Try
    End Sub

    Private Sub frmTaxSheet_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            If _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then
                lueFilter.Properties.DataSource = New List(Of String)(New String() {"Available Credits", "Ready to Refund", "All"})
                lueFilter.EditValue = "Available Credits"
            Else
                lcgFilter.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            End If

            DB = New dbEPDataDataContext(GetConnectionString())
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in load form", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Try
            Close()
        Catch ex As Exception
            DisplayErrorMessage("Error in close", ex)
        End Try
    End Sub

    Private Sub GridView1_CustomColumnSort(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnSortEventArgs) Handles GridView1.CustomColumnSort
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = sender
        If view Is Nothing Then
            Return
        End If
        Try
            If e.Column.FieldName = "ID" Then
                Dim val1 As Object = view.GetListSourceRowCellValue(e.ListSourceRowIndex1, "ID")
                Dim val2 As Object = view.GetListSourceRowCellValue(e.ListSourceRowIndex2, "ID")
                e.Handled = True

                If val2 = 0 Then
                    e.Result = System.Collections.Comparer.Default.Compare(1, 2)
                ElseIf val1 = 0 Then
                    e.Result = System.Collections.Comparer.Default.Compare(2, 1)
                Else
                    e.Result = System.Collections.Comparer.Default.Compare(val1, val2)
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in sorting", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            DB.SaveChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error saving tax sheet", ex)
        End Try
    End Sub

    Private Sub btnSaveAndExit_Click(sender As Object, e As EventArgs) Handles btnSaveAndExit.Click
        Try
            If DB.SaveChanges() Then
                Close()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error saving tax sheet", ex)
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            If _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then

                Dim TaxAdjCtrlRow = GridView1.GetFocusedRow()
                If TaxAdjCtrlRow Is Nothing Then Return
                Dim rowID = GetMasterRowID(TaxAdjCtrlRow)
                If Not rowID.HasValue Then Return

                If e.Allow AndAlso e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
                    Dim mnuScripts = New Menu.DXSubMenuItem("Scripts")
                    mnuScripts.Items.Add(New Menu.DXMenuItem("Add 941 Adjustments", Sub() OpenScript(1290)))
                    mnuScripts.Items.Add(New Menu.DXMenuItem("Add ST Adjustments", Sub() OpenScript(1292)))
                    mnuScripts.Items.Add(New DXMenuItem("Fix Negative Payroll Recon - With Refund To Client", Sub() OpenScript(1308, "Yes"), My.Resources.financial_16x16))
                    mnuScripts.Items.Add(New DXMenuItem("Fix Negative Payroll Recon - No Refund", Sub() OpenScript(1308, "No"), My.Resources.financial_16x16))
                    mnuScripts.Items.Add(New DXMenuItem("Fix All Negative Payrolls - Refund To Client above $5 only", Sub() OpenScript(1362), My.Resources.financial_16x16))
                    e.Menu.Items.Add(mnuScripts)
                End If
            End If

            Dim row = GridView1.GetRow(e.HitInfo.RowHandle)
            Dim mnuActions = New Menu.DXSubMenuItem("Actions")
            mnuActions.Items.Add(New DXMenuItem("Delete", Sub() DeleteRow(row, 1), My.Resources.delete_16x16))
            e.Menu.Items.Add(mnuActions)
        End If
    End Sub

    Private Sub DeleteRow(row As Object, level As Byte)
        If XtraMessageBox.Show($"Are you sure you would like to delete row?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If
        Try
            If _TaxSheet = TaxSheetEnum.PaymentControlSheet Then
                DB.BankTaxPmtCtrls.DeleteOnSubmit(row)
            ElseIf _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl AndAlso level = 1 Then
                Dim AdjRec As view_BankingManualTaxAdjCtrl = row
                Dim hasPmts = Query($"SELECT * 
FROM custom.view_BankingManualTaxAdjCtrlDetail det 
INNER JOIN custom.view_BankingManualTaxAdjCtrlPayment pmt 
ON pmt.DetailID = det.ID 
WHERE det.MasterID = {row.id}
").Rows.Count > 0

                If hasPmts Then
                    DisplayMessageBox($"There are payments for this Adjustment{vbCrLf}Must first delete payments", "Cannot Delete")
                    Return
                Else
                    DB.view_BankingManualTaxAdjCtrls.DeleteOnSubmit(row)
                End If
            ElseIf _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl AndAlso level = 3 Then
                DB.view_BankingManualTaxAdjCtrlPayments.DeleteOnSubmit(row)
            ElseIf _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl AndAlso level = 2 Then
                Dim detailRow As view_BankingManualTaxAdjCtrlDetail = row
                Dim tblDetailRow = DB.BankingManualTaxAdjCtrlDetails.Where(Function(d) d.ID = detailRow.ID).FirstOrDefault()
                DB.BankingManualTaxAdjCtrlDetails.DeleteOnSubmit(tblDetailRow)
            ElseIf _TaxSheet = TaxSheetEnum.TaxCarryFwdSheet Then
                DB.TaxCarryFwdSheets.DeleteOnSubmit(row)
            End If

            DB.SaveChanges()
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error when deleting row", ex)
        End Try
    End Sub

    Private Sub btnDelimConums_Click(sender As Object, e As EventArgs) Handles btnDelimConums.Click
        Try
            Dim conums As String = ""
            For x = 0 To GridView1.RowCount - 1
                If _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then
                    Dim rowCoNum = GetMasterRowCoNum(GridView1.GetRow(x))
                    If rowCoNum.HasValue Then
                        conums += rowCoNum.Value.ToString + ","
                    End If
                ElseIf _TaxSheet = TaxSheetEnum.TaxCarryFwdSheet Then
                    conums += CType(GridView1.GetRow(x), TaxCarryFwdSheet).CoNum.ToString + ","
                End If
            Next
            Clipboard.SetText(conums)
            MessageBox.Show("CoNums delimited were copied to clipboard")
        Catch ex As Exception
            DisplayErrorMessage("Error delimiting conums", ex)
        End Try
    End Sub

    Private Sub GridView1_MasterRowExpanded(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomMasterRowEventArgs) Handles GridView1.MasterRowExpanded
        Dim detailGC As DevExpress.XtraGrid.Views.Grid.GridView = GridView1.GetDetailView(e.RowHandle, 0)
        detailGC.BestFitColumns()
    End Sub

    Private Sub GridView2_MasterRowExpanded(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomMasterRowEventArgs) Handles GridView2.MasterRowExpanded
        Dim view = TryCast(sender, DevExpress.XtraGrid.Views.Grid.GridView)
        Dim detailView = TryCast(view.GetDetailView(e.RowHandle, e.RelationIndex), DevExpress.XtraGrid.Views.Grid.GridView)
        detailView.BestFitColumns()
    End Sub

    Private Async Sub OpenScript(id As Int16, Optional behavior As String = Nothing)
        Try
            Dim row = GridView1.GetFocusedRow()
            If row Is Nothing Then Return
            Dim rowID = GetMasterRowID(row)
            If Not rowID.HasValue Then Return

            Dim script As SqlScript = DB.SqlScripts.Where(Function(s) s.ID = id).FirstOrDefault()
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, script)
            If id = 1290 Then
                Dim fdAdjNotes = GetRowValue(row, "FDAdjNotes")
                If fdAdjNotes Is Nothing Then
                    DisplayMessageBox("No notes were found for Federal")
                    Return
                End If
                Dim matches = System.Text.RegularExpressions.Regex.Matches(fdAdjNotes.ToString(), "pr(?:[\s#])*(\d+)", System.Text.RegularExpressions.RegexOptions.Compiled + System.Text.RegularExpressions.RegexOptions.IgnoreCase)
                Dim adjPrNums = String.Join(",", (From v In matches Select v.Groups(1).Value).ToList())

                script.Parameters = $"@ManTaxAdjCtrlID={rowID.Value}; @AdjPrNums={adjPrNums}"
            ElseIf id = 1292 Then
                script.Parameters = $"@ManTaxAdjCtrlID={rowID.Value}; @AdjPrNums="
            ElseIf id = 1308 Then
                Dim coNum = GetMasterRowCoNum(row)
                Dim prNum = GetRowValue(row, "PrNum")
                script.Parameters = $"@CoNum={coNum}; @PrNum={prNum}"
                script.ParametersJson = Query(Of String)($"DECLARE @json NVARCHAR(MAX)
SELECT @json = ss.ParametersJson FROM custom.SqlScripts ss WHERE ss.ID = 1308
SET @json = JSON_MODIFY(JSON_MODIFY(JSON_MODIFY(JSON_MODIFY(@json, '$[0].Value', 'Full Amount'), '$[1].Value', '{row.CoNum}'), '$[2].Value', '{row.PrNum}'), '$[3].Value', '{behavior}')
SELECT @json
").First
            ElseIf id = 1362 Then
                Dim selectedRows = GridView1.GetSelectedRows(Of view_BankingManualTaxAdjCtrl)
                Dim listIds As String = ""
                For Each row In selectedRows
                    listIds += row.ID.ToString() + ","
                Next
                script.Parameters = $"@Ids={listIds}"
                script.ParametersJson = Query(Of String)($"DECLARE @json NVARCHAR(MAX)
SELECT @json = ss.ParametersJson FROM custom.SqlScripts ss WHERE ss.ID = 1362
SET @json = JSON_MODIFY(@json, '$[0].Value', '{listIds}')
SELECT @json
").First
            End If

            DB.SaveChanges()

            Dim sqlFrm As frmSqlScripts = frmMain.GetForm(frmSqlScripts.GetType())
            If sqlFrm IsNot Nothing Then
                frmMain.CloseForm(sqlFrm)
            End If
            frmMain.AddOrActivateForm(Of frmSqlScripts)()
            sqlFrm = frmMain.GetForm(frmSqlScripts.GetType())
            sqlFrm.FindAndFocusRecord(id)
            Await sqlFrm.ExecuteClick(id)
        Catch ex As Exception
            DisplayErrorMessage("Error in OpenScript", ex)
        End Try
    End Sub

    Private Sub GridView2_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView2.PopupMenuShowing
        Try
            If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 AndAlso _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then

                Dim detailView As GridView = sender
                Dim row = detailView.GetFocusedRow()

                e.Menu.Items.Add(New DXMenuItem("Toggle Auto Adjusted", Sub()
                                                                            Try
                                                                                ' TODO: Implement toggle for DataRowView
                                                                                ' This would require updating the underlying data source
                                                                                DisplayMessageBox("Toggle Auto Adjusted not implemented for DataSet mode")
                                                                            Catch ex As Exception
                                                                            End Try
                                                                        End Sub))
                e.Menu.Items.Add(New DXMenuItem("Delete", Sub() DeleteRow(row, 2), My.Resources.delete_16x16))
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in popup menu showing", ex)
        End Try
    End Sub

    Private Sub GridView3_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView3.PopupMenuShowing
        Try
            If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 AndAlso _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then
                'Dim row = GridView3.GetRow(e.HitInfo.RowHandle)

                Dim detailView As GridView = sender
                Dim row = detailView.GetFocusedRow()

                e.Menu.Items.Add(New DXMenuItem("Delete", Sub() DeleteRow(row, 3), My.Resources.delete_16x16))
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in popup menu showing", ex)
        End Try
    End Sub

    Private Sub GridView1_MasterRowExpanding(sender As Object, e As MasterRowCanExpandEventArgs) Handles GridView1.MasterRowExpanding
        Try
            ' Skip refresh when using DataSet - not needed for master-detail relationships
            ' Dim row = GridView1.GetFocusedRow()
            ' DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, row.view_BankingManualTaxAdjCtrlDetails)
        Catch ex As Exception
            DisplayErrorMessage("Error when expanding master row", ex)
        End Try
    End Sub

    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        Try
            If _TaxSheet = TaxSheetEnum.ManTaxAdjustCtrl Then
                If e.CellValue Is Nothing Then Return
                Dim row = GridView1.GetRow(e.RowHandle)
                If row IsNot Nothing Then
                    Dim totalNegative = GetRowValue(row, "TotalNegative")
                    If e.Column.FieldName.ToLower = "reconnegative" AndAlso nz(e.CellValue, 0) <> nz(totalNegative, 0) Then
                        e.Appearance.BackColor = Color.LightPink
                    End If
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in row cell style event", ex)
        End Try
    End Sub

    Private Sub btnRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles btnRefresh.ItemClick
        Try
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error refreshing", ex)
        End Try
    End Sub

    Private Sub lueFilter_EditValueChanged(sender As Object, e As EventArgs) Handles lueFilter.EditValueChanged
        If lueFilter.EditValue = Nothing OrElse lueFilter.EditValue = "" OrElse lueFilter.EditValue = "All" Then
            GridView1.ActiveFilterString = ""
        ElseIf lueFilter.EditValue = "Available Credits" Then
            GridView1.ActiveFilterString = "DateAdjusted IS NULL"
        ElseIf lueFilter.EditValue = "Ready to Refund" Then
            GridView1.ActiveFilterString = "DateAdjusted IS NOT NULL AND RefundDate IS NULL AND NOT ([ReconNegative] Is Null AND [NegativeType] = 'NegativeOnly')"
        End If
    End Sub

    Private Function GetRowValue(rowView As Object, columnName As String) As Object
        If TypeOf rowView Is DataRowView Then
            Dim drv As DataRowView = CType(rowView, DataRowView)
            Return drv(columnName)
        ElseIf rowView IsNot Nothing Then
            ' Try to get property value using reflection
            Dim prop = rowView.GetType().GetProperty(columnName)
            If prop IsNot Nothing Then
                Return prop.GetValue(rowView)
            End If
        End If
        Return Nothing
    End Function

    Private Function GetMasterRowID(rowView As Object) As Integer?
        Dim idValue = GetRowValue(rowView, "ID")
        If idValue IsNot Nothing AndAlso IsNumeric(idValue) Then
            Return Convert.ToInt32(idValue)
        End If
        Return Nothing
    End Function

    Private Function GetMasterRowCoNum(rowView As Object) As Decimal?
        Dim conumValue = GetRowValue(rowView, "CoNum")
        If conumValue IsNot Nothing AndAlso IsNumeric(conumValue) Then
            Return Convert.ToDecimal(conumValue)
        End If
        Return Nothing
    End Function

    Private Function ConvertToDataTable(Of T)(data As List(Of T), tableName As String) As DataTable
        Dim table As New DataTable(tableName)

        If data.Count = 0 Then Return table

        ' Get properties from the first item
        Dim properties = GetType(T).GetProperties()

        ' Add columns
        For Each prop In properties
            If prop.CanRead Then
                Dim colType = If(Nullable.GetUnderlyingType(prop.PropertyType), prop.PropertyType)
                table.Columns.Add(prop.Name, colType)
            End If
        Next

        ' Add rows
        For Each item In data
            Dim row = table.NewRow()
            For Each prop In properties
                If prop.CanRead Then
                    Dim value = prop.GetValue(item)
                    row(prop.Name) = If(value, DBNull.Value)
                End If
            Next
            table.Rows.Add(row)
        Next

        Return table
    End Function
End Class