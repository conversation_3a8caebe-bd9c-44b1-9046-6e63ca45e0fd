﻿Imports System.ComponentModel
Imports System.ComponentModel.DataAnnotations.Schema
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls
Imports DevExpress.XtraGrid.Views.Grid

Public Class frmCompanyUnion

    Dim DB As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
    Dim _UnionDepartments As List(Of UnionDepartment)
    Dim _UnionRates As List(Of UnionRate)
    Dim _IsLoaded As Boolean = False

    Dim _Departments As List(Of DEPARTMENT)

    Public Sub New()
        InitializeComponent()

        LoadRates()

        Dim DicIncludeExclude = New Dictionary(Of Boolean, String) From {{False, "Include"}, {True, "Exclude"}}
        Me.riDepartmentsIncludeExclude.DataSource = DicIncludeExclude
        Me.riDepartmentsIncludeExclude.ValueMember = "Key"
        Me.riDepartmentsIncludeExclude.DisplayMember = "Value"

        LoadData()
        UpdateFilterDropDowns()
        SetDuplicateOptionVisible()
        Me.slueCompany.Select()

        AddHandler riPayCodesLookup.KeyDown, AddressOf ClearEditor
        AddHandler riDedCodeList.KeyDown, AddressOf ClearEditor
        AddHandler riMemoCodesLookup.KeyDown, AddressOf ClearEditor
        AddHandler riPaidWorkCombo.KeyDown, AddressOf ClearEditor
    End Sub

    Private Sub RefreshDate()
        DB = New dbEPDataDataContext(GetConnectionString)
        LoadRates()
        LoadCompany()
        LoadRates()
        UpdateFilterDropDowns()
    End Sub

    Private Sub LoadRates()
        _UnionRates = DB.UnionRates.ToList
        RefreshUpdateLabels()
    End Sub

    Private Sub BindingSourceUnionDepartments_AddingNew(sender As Object, e As AddingNewEventArgs) Handles BindingSourceUnionDepartments.AddingNew
        Dim ud As New UnionDepartment With {.Conum = Me.slueCompany.EditValue, .DepartmentsExclude = False}
        e.NewObject = ud
        DB.UnionDepartments.InsertOnSubmit(ud)
    End Sub

    Private Sub BindingSourceUnionRates_AddingNew(sender As Object, e As AddingNewEventArgs) Handles BindingSourceUnionRates.AddingNew
        Dim ud As New UnionRate With {.IsActive = True}
        e.NewObject = ud
        DB.UnionRates.InsertOnSubmit(ud)
    End Sub

    Private Sub cbeName_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cbeName.SelectedIndexChanged, cbePosition.SelectedIndexChanged, cbeFund.SelectedIndexChanged
        UpdateFilterDropDowns()
    End Sub

    Public Sub UpdateFilterDropDowns()
        If IsInitializing Then Exit Sub
        cbeName.Properties.Items.Clear()
        cbeFund.Properties.Items.Clear()
        cbePosition.Properties.Items.Clear()

        If tcgUnions.SelectedTabPageIndex = 0 Then
            If _UnionDepartments IsNot Nothing Then

                Dim list As List(Of String) = _UnionDepartments _
                .Where(Function(u) (cbePosition.Text = "" OrElse u.Position = cbePosition.Text) AndAlso (cbeFund.Text = "" OrElse u.Fund = cbeFund.Text)) _
                .Select(Function(u) u.Name).ToList()
                cbeName.Properties.Items.AddRange(list.Distinct.ToList)
                list = _UnionDepartments _
                    .Where(Function(u) (cbeName.Text = "" OrElse u.Name = cbeName.Text) AndAlso (cbeFund.Text = "" OrElse u.Fund = cbeFund.Text)) _
                    .Select(Function(u) u.Position).ToList
                cbePosition.Properties.Items.AddRange(list.Distinct.ToList)

                list = _UnionDepartments _
                    .Where(Function(u) (cbeName.Text = "" OrElse u.Name = cbeName.Text) AndAlso (cbePosition.Text = "" OrElse u.Position = cbePosition.Text)) _
                    .Select(Function(u) u.Fund).ToList
                cbeFund.Properties.Items.AddRange(list.Distinct.ToList)

                Dim unionList As IEnumerable(Of UnionDepartment) = _UnionDepartments
                If Not cbeName.Text = "" Then
                    unionList = unionList.Where(Function(u) u.Name = cbeName.Text)
                End If
                If Not cbePosition.Text = "" Then
                    unionList = unionList.Where(Function(u) u.Position = cbePosition.Text)
                End If
                If Not cbeFund.Text = "" Then
                    unionList = unionList.Where(Function(u) u.Fund = cbeFund.Text)
                End If

                BindingSourceUnionDepartments.DataSource = unionList.ToList
                gvUnionDepartments.BestFitColumns()
                gvUnionDepartments.OptionsView.NewItemRowPosition = NewItemRowPosition.Bottom
            Else
                gvUnionDepartments.OptionsView.NewItemRowPosition = NewItemRowPosition.None
            End If
        Else
            Dim list As List(Of String) = _UnionRates _
            .Where(Function(u) (cbePosition.Text = "" OrElse u.Position = cbePosition.Text) AndAlso (cbeFund.Text = "" OrElse u.Fund = cbeFund.Text) AndAlso (Not ceActiveOnly.Checked OrElse u.IsActive = True)) _
            .Select(Function(u) u.Name).ToList()
            cbeName.Properties.Items.AddRange(list.Distinct.ToList)

            list = _UnionRates _
                .Where(Function(u) (cbeName.Text = "" OrElse u.Name = cbeName.Text) AndAlso (cbeFund.Text = "" OrElse u.Fund = cbeFund.Text) AndAlso (u.Position IsNot Nothing) AndAlso (Not ceActiveOnly.Checked OrElse u.IsActive = True)) _
                .Select(Function(u) u.Position).ToList
            cbePosition.Properties.Items.AddRange(list.Distinct.ToList)

            list = _UnionRates _
                .Where(Function(u) (cbeName.Text = "" OrElse u.Name = cbeName.Text) AndAlso (cbePosition.Text = "" OrElse u.Position = cbePosition.Text) AndAlso (u.Fund IsNot Nothing) AndAlso (Not ceActiveOnly.Checked OrElse u.IsActive = True)) _
                .Select(Function(u) u.Fund).ToList
            cbeFund.Properties.Items.AddRange(list.Distinct.ToList)

            Dim unionList As IEnumerable(Of UnionRate) = _UnionRates.Where(Function(u) (Not ceActiveOnly.Checked OrElse u.IsActive = True)).ToList()
            If Not cbeName.Text = "" Then
                unionList = unionList.Where(Function(u) u.Name = cbeName.Text)
            End If
            If Not cbePosition.Text = "" Then
                unionList = unionList.Where(Function(u) u.Position = cbePosition.Text)
            End If
            If Not cbeFund.Text = "" Then
                unionList = unionList.Where(Function(u) u.Fund = cbeFund.Text)
            End If
            If Me.chkLastDateOnly.Checked Then
                unionList = (From A In unionList Group By A.UnionNamePositionFund Into Group Select Group.OrderByDescending(Function(p) p.EffectiveDate).First).ToList
            End If
            BindingSourceUnionRates.DataSource = unionList
            gvUnionRates.BestFitColumns()
        End If
    End Sub

    Private Sub tcgUnions_SelectedPageChanged(sender As Object, e As DevExpress.XtraLayout.LayoutTabPageChangedEventArgs) Handles tcgUnions.SelectedPageChanged
        UpdateFilterDropDowns()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to save?", "Confirm Save", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.No Then Exit Sub
        For Each item In DB.GetChangeSet().Inserts
            If TypeOf item Is UnionDepartment Then CType(item, UnionDepartment).rowguid = Guid.NewGuid()
            If TypeOf item Is UnionRate Then CType(item, UnionRate).rowguid = Guid.NewGuid()
        Next
        If DB.SaveChanges() Then
            XtraMessageBox.Show("Saved")
        End If
        RefreshDate()
    End Sub

    Private Sub LoadData()
        'riCbUnionNamePositionFund.Items.AddRange(DB.UnionRates.Select(Function(u) u.UnionNamePositionFund).ToArray())
        'riSleComapny.DataSource = DB.COMPANies.Select(Function(c) New With {Key .CONUM = c.CONUM, Key .CO_NAME = c.CO_NAME, Key .CoNumAndName = "{0} - {1}".FormatWith(c.CONUM, c.CO_NAME)}).ToList
        'riSleComapny.DisplayMember = "CoNumAndName"
        'riSleComapny.ValueMember = "CONUM"

        slueCompany.Properties.DataSource = (
            From A In DB.COMPANies
            Order By A.CONUM
            Select New CompanySummary With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                                                .CO_DBA = A.CO_DBA, .CO_PHONE = A.CO_PHONE, .CO_FAX = A.CO_FAX,
                                                .CO_EMAIL = A.CO_EMAIL, .CO_STATUS = A.CO_STATUS}
                                            ).ToList
        slueCompany.Properties.DisplayMember = "CONUM"
        slueCompany.Properties.ValueMember = "CONUM"
    End Sub


    Private Sub gvUnionDepartments_ShowingEditor(sender As Object, e As CancelEventArgs) Handles gvUnionDepartments.ShowingEditor
        Try
            Dim view As GridView = CType(sender, GridView)
            If view.FocusedColumn Is colUnionNamePositionFund Then
                Dim ud As UnionDepartment = view.GetRow(view.FocusedRowHandle)
                If ud Is Nothing Then
                    e.Cancel = False
                Else
                    If IsNothing(ud.rowguid) OrElse ud.rowguid.Equals(Guid.Empty) Then
                        e.Cancel = False
                    Else
                        e.Cancel = True
                    End If
                End If
            Else
            End If
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        End Try
    End Sub

    Private Sub btnDuplicate_Click(sender As Object, e As EventArgs) Handles btnDuplicate.Click
        If NewEffectiveDate.Text.IsNullOrWhiteSpace Then
            XtraMessageBox.Show("Please select a New Effective Date.")
            Exit Sub
        End If
        Dim list = CType(BindingSourceUnionRates.DataSource, IEnumerable(Of UnionRate)).Where(Function(u) u.Checked).ToList
        Dim listToAdd = New List(Of UnionRate)
        For Each item In list
            Dim newItem = New UnionRate
            newItem.Memo = item.Memo
            newItem.PaidOrWorkhours = item.PaidOrWorkhours
            newItem.PerEvenOnly = item.PerEvenOnly
            newItem.PerQtyHours = item.PerQtyHours
            newItem.Rate = item.Rate
            newItem.UnionNamePositionFund = item.UnionNamePositionFund
            newItem.EffectiveDate = Me.NewEffectiveDate.DateTime
            newItem.IsActive = item.IsActive
            newItem.Expression1 = item.Expression1
            newItem.Expression2 = item.Expression2
            newItem.ExpressionCondition = item.ExpressionCondition
            listToAdd.Add(newItem)
        Next
        For Each item In listToAdd
            _UnionRates.Add(item)
            DB.UnionRates.InsertOnSubmit(item)
            UpdateFilterDropDowns()
        Next
        For Each itm In list
            itm.Checked = False
        Next
        Me.gvUnionRates.RefreshData()
    End Sub

    Private Sub riCeChecked_CheckedChanged(sender As Object, e As EventArgs) Handles riCeChecked.CheckedChanged
        Me.gvUnionRates.PostEditor()
        SetDuplicateOptionVisible()
    End Sub

    Private Sub SetDuplicateOptionVisible()
        Dim list = CType(BindingSourceUnionRates.DataSource, IEnumerable(Of UnionRate))
        If list Is Nothing Then Exit Sub
        If list.Any(Function(u) u.Checked) Then
            btnDuplicate.Enabled = True
        Else
            btnDuplicate.Enabled = False
        End If
    End Sub

    Private Sub slueCompany_EditValueChanged(sender As Object, e As EventArgs) Handles slueCompany.EditValueChanged
        LoadCompany()
    End Sub

    Private Sub LoadCompany()
        Me.txtCoName.EditValue = Nothing
        If Me.slueCompany.HasValue Then
            Dim CoNum As Decimal = Me.slueCompany.EditValue
            Me.txtCoName.EditValue = (From A In CType(Me.slueCompany.Properties.DataSource, IList) Where A.CONUM = CoNum Select A.CO_NAME).First

            Dim Div = (From A In DB.DIVISIONs Where A.CONUM = CoNum Select A.DDIVNUM, A.DDIVNAME, Descr = A.DDIVNUM & "-" & A.DDIVNAME).ToList
            Me.DIVISIONBindingSource.DataSource = Div

            _Departments = (From A In DB.DEPARTMENTs Where A.CONUM = CoNum).ToList
            Me.OTHERPAYBindingSource.DataSource = (From A In DB.OTHER_PAYS Where A.CONUM = CoNum Order By A.OTH_PAY_NUM).ToList
            Me.DEDUCTIONBindingSource.DataSource = (From A In DB.DEDUCTIONs Where A.CONUM = CoNum Order By A.DED_NUM).ToList
            Me.MEMOBindingSource.DataSource = (From A In DB.MEMOS Where A.CONUM = CoNum Order By A.MEMO_NUM).ToList
            riJobs1CheckedList.DataSource = (From a In DB.CO_JOBS Where a.conum = CoNum Order By a.job_id).ToList

            _UnionDepartments = DB.UnionDepartments.Where(Function(p) p.Conum = CoNum).ToList

            Dim UsedNames = (From A In _UnionDepartments Select A.UnionNamePositionFund).Distinct.ToList
            Dim ActiveUnionRates = (From A In _UnionRates Where A.IsActive OrElse UsedNames.Contains(A.UnionNamePositionFund) Select A.UnionNamePositionFund).Distinct.OrderBy(Function(p) p).ToArray
            riCbUnionNamePositionFund.Items.AddRange(ActiveUnionRates)

            Me.BindingSourceUnionDepartments.DataSource = _UnionDepartments
            UpdateFilterDropDowns()
            RefreshUpdateLabels()
        End If
    End Sub

    Private Sub gvUnionDepartments_CustomRowCellEditForEditing(sender As Object, e As CustomRowCellEditEventArgs) Handles gvUnionDepartments.CustomRowCellEditForEditing
        If e.Column.FieldName = "Departments" Then
            Dim ud As UnionDepartment = Me.gvUnionDepartments.GetFocusedRow
            Dim DivNum = ud.DivNum
            Dim SelectedDepartments As New List(Of Decimal)
            Dim CurrentValue As String = Me.gvUnionDepartments.FocusedValue
            If Not String.IsNullOrEmpty(CurrentValue) Then
                SelectedDepartments = (From A In CurrentValue.Split(",") Where Not String.IsNullOrEmpty(A.Trim) Select Convert.ToDecimal(A.Trim)).ToList
            End If
            Me.DEPARTMENTBindingSource.DataSource = (From A In _Departments
                                                     Where A.DIVNUMD = DivNum AndAlso (A.DPACTIVE = "YES" OrElse SelectedDepartments.Contains(A.DEPTNUM))
                                                     Select A.DEPTNUM, A.DEPT_DESC, A.DIVNUMD, Descr = A.DEPTNUM & "-" & A.DEPT_DESC).ToList
            e.RepositoryItem = riDepartmentChekcedList
        End If
    End Sub

    Private Sub gvUnionDepartments_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvUnionRates.PopupMenuShowing, gvUnionDepartments.PopupMenuShowing
        If e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", AddressOf DeleteRow, My.Resources.Action_Delete))
            If tcgUnions.SelectedTabPageIndex = 0 Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Copy Row", AddressOf CopyRow))
            End If
        End If
    End Sub

    Private Sub DeleteRow()
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to delete this row?", "Confirm delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then Exit Sub

        If tcgUnions.SelectedTabPageIndex = 0 Then
            Dim Row As UnionDepartment = Me.gvUnionDepartments.GetFocusedRow
            Me.gvUnionDepartments.DeleteRow(Me.gvUnionDepartments.FocusedRowHandle)
            DB.UnionDepartments.DeleteOnSubmit(Row)
        Else
            Dim Row As UnionRate = Me.gvUnionRates.GetFocusedRow
            Dim AllowDelete As Boolean
            If Row.rowguid = Guid.Empty Then
                AllowDelete = True
            Else
                'Check if this is the only record with this fund type
                Dim RecCount = (From A In DB.UnionRates Where A.UnionNamePositionFund = Row.UnionNamePositionFund).Count
                If RecCount > 1 Then
                    AllowDelete = True
                Else
                    Dim HasRelatedRecords = (From A In DB.UnionDepartments Where A.UnionNamePositionFund = Row.UnionNamePositionFund Select sCoNum = A.Conum.ToString).Distinct.ToList
                    If HasRelatedRecords.Count > 0 Then
                        DisplayMessageBox(String.Format("Cannot delete this row because it has related records in Union Departments, in following companies:{0}{1}", vbCrLf, String.Join(vbCrLf, HasRelatedRecords)))
                        Exit Sub
                    Else
                        AllowDelete = True
                    End If
                End If
            End If
            If AllowDelete Then
                'BindingSourceUnionRates.RemoveCurrent()
                Me.gvUnionRates.DeleteRow(Me.gvUnionRates.FocusedRowHandle)
                DB.UnionRates.DeleteOnSubmit(Row)
                If DB.GetChangeSet().Deletes.Count + DB.GetChangeSet().Inserts.Count + DB.GetChangeSet().Updates.Count() > 1 Then
                    If XtraMessageBox.Show("There's multiple changes, do wish to save now all changes ?", "Save All", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = System.Windows.Forms.DialogResult.No Then
                        Exit Sub
                    End If
                End If
                btnSave.PerformClick()
            End If
        End If
    End Sub

    Private Sub frmCompanyUnion_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Dim Changes = DB.GetChangeSet
        If Changes.Deletes.Count > 0 OrElse Changes.Inserts.Count > 0 OrElse Changes.Updates.Count > 0 Then
            Dim results = DevExpress.XtraEditors.XtraMessageBox.Show("You have changes not saved in the database. Do you want to save it?", "Save Changes?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Stop)
            If results = System.Windows.Forms.DialogResult.Cancel Then
                e.Cancel = True
            ElseIf results = System.Windows.Forms.DialogResult.Yes Then
                Me.btnSave.PerformClick()
            End If
        End If
    End Sub

    Dim rowguid As Guid
    Dim cancelGridEditing As Boolean
    Private Sub gvUnionRates_ShowingEditor(sender As Object, e As CancelEventArgs) Handles gvUnionRates.ShowingEditor
        If Me.gvUnionRates.FocusedColumn.FieldName = "Rate" Then
            Dim row As UnionRate = Me.gvUnionRates.GetFocusedRow
            If row IsNot Nothing AndAlso Not row.rowguid = Guid.Empty Then
                If row.rowguid = rowguid Then
                    e.Cancel = cancelGridEditing
                Else
                    rowguid = row.rowguid
                    Dim Msg = DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to change the rate on this record. Consider duplicating the record with a new effective date." & vbCrLf & "Continue edit?", "Confirm edit", MessageBoxButtons.YesNo, MessageBoxIcon.Stop)
                    If Msg = System.Windows.Forms.DialogResult.No Then
                        e.Cancel = True
                        cancelGridEditing = True
                    Else
                        cancelGridEditing = False
                    End If
                End If
            End If
        End If
    End Sub

    Private Sub btnUncheckAll_Click(sender As Object, e As EventArgs) Handles btnUncheckAll.Click
        Dim list = CType(BindingSourceUnionRates.DataSource, IEnumerable(Of UnionRate)).Where(Function(u) u.Checked).ToList
        For Each itm In list
            itm.Checked = False
        Next
        Me.gvUnionRates.RefreshData()
        SetDuplicateOptionVisible()
    End Sub

    Private Sub btnCheckAll_Click(sender As Object, e As EventArgs) Handles btnCheckAll.Click
        'Dim list = CType(BindingSourceUnionRates.DataSource, IEnumerable(Of UnionRate)).Where(Function(u) Not u.Checked).ToList
        For x = 0 To Me.gvUnionRates.DataRowCount - 1
            Dim item As UnionRate = Me.gvUnionRates.GetRow(x)
            item.Checked = True
        Next
        Me.gvUnionRates.RefreshData()
        SetDuplicateOptionVisible()
    End Sub

    Private Sub btnRefresh_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles btnRefresh.LinkClicked
        RefreshDate()
    End Sub

    Private Sub RefreshUpdateLabels()
        Dim changes = DB.GetChangeSet
        Dim uMsg As String
        Dim dMsg As String
        Dim q = (From A In changes.Updates Group By T = A.GetType.ToString Into Cnt = Count() Select Action = "Updates", T, Cnt
                ).Union(
                From A In changes.Inserts Group By T = A.GetType.ToString Into Cnt = Count() Select Action = "Inserts", T, Cnt
                ).Union(
                From A In changes.Deletes Group By T = A.GetType.ToString Into Cnt = Count() Select Action = "Deletes", T, Cnt
                ).ToList
        uMsg = String.Join(" - ", (From A In q Where A.T.EndsWith("UnionRate") Select String.Format("{0}: {1}", A.Action, A.Cnt)))
        dMsg = String.Join(" - ", (From A In q Where A.T.EndsWith("UnionDepartment") Select String.Format("{0}: {1}", A.Action, A.Cnt)))
        Me.lblUnionChanges.Text = If(String.IsNullOrEmpty(uMsg), "No Rate Changes", "Rates: " & uMsg)
        Me.lblDeptChanges.Text = If(String.IsNullOrEmpty(dMsg), "No Department Changes", "Departments: " & dMsg)
        SetDuplicateOptionVisible()
    End Sub

    Private Sub BindingSourceUnionDepartments_ListChanged(sender As Object, e As ListChangedEventArgs) Handles BindingSourceUnionRates.ListChanged, BindingSourceUnionDepartments.ListChanged
        RefreshUpdateLabels()
    End Sub

    'Private Sub BindingSourceUnionRates_CurrentItemChanged(sender As Object, e As EventArgs) Handles BindingSourceUnionRates.CurrentItemChanged, BindingSourceUnionDepartments.CurrentItemChanged
    '    RefreshUpdateLabels()
    'End Sub

    Private Sub gvUnionRates_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles gvUnionRates.RowUpdated, gvUnionDepartments.RowUpdated
        RefreshUpdateLabels()
    End Sub

    Private Sub lcClearFilters_Click(sender As Object, e As EventArgs) Handles lcClearFilters.Click
        cbeName.EditValue = Nothing
        cbeFund.EditValue = Nothing
        cbePosition.EditValue = Nothing
    End Sub

    Private Sub ceActiveOnly_CheckedChanged(sender As Object, e As EventArgs) Handles ceActiveOnly.CheckedChanged, chkLastDateOnly.CheckedChanged
        UpdateFilterDropDowns()
    End Sub

    Private Sub rgpCondition_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgpCondition.SelectedIndexChanged
        Me.pnlIfCondition.Visible = Me.rgpCondition.EditValue = "IF"
    End Sub

    Private Sub riPopupEexpressionCondition_QueryPopUp(sender As Object, e As CancelEventArgs) Handles riPopupEexpressionCondition.QueryPopUp
        Dim CurrentValue = Me.gvUnionRates.FocusedValue
        If CurrentValue Is Nothing Then
            Me.rgpCondition.EditValue = "NONE"
        ElseIf {"NONE", "SMALLER", "LARGER"}.Contains(CurrentValue) Then
            Me.rgpCondition.EditValue = CurrentValue
        Else
            Me.rgpCondition.EditValue = "IF"
            Me.txtCondition.EditValue = CurrentValue
        End If
    End Sub

    Private Sub riPopupEexpressionCondition_QueryResultValue(sender As Object, e As Controls.QueryResultValueEventArgs) Handles riPopupEexpressionCondition.QueryResultValue
        If rgpCondition.EditValue = "IF" Then
            If txtCondition.Text.Length > 0 Then
                e.Value = txtCondition.EditValue
            Else
                e.Value = "NONE"
            End If
        Else
            e.Value = rgpCondition.EditValue
        End If
    End Sub

    Private Sub btnOKG_Click(sender As Object, e As EventArgs) Handles btnOKG.Click
        Dim button As Control = CType(sender, Control)
        CType(button.Parent, PopupContainerControl).OwnerEdit.ClosePopup()
    End Sub

    Private Sub btnCancelK_Click(sender As Object, e As EventArgs) Handles btnCancelK.Click
        Dim button As Control = CType(sender, Control)
        CType(button.Parent, PopupContainerControl).OwnerEdit.CancelPopup()
    End Sub

    Private Sub CopyRow()
        Dim Row As UnionDepartment = Me.gvUnionDepartments.GetFocusedRow
        Dim NewRow As UnionDepartment = Me.BindingSourceUnionDepartments.AddNew
        NewRow.UnionNamePositionFund = Row.UnionNamePositionFund
        NewRow.DivNum = Row.DivNum
        NewRow.Departments = Row.Departments
        NewRow.DepartmentsExclude = Row.DepartmentsExclude
        NewRow.PayCode = Row.PayCode
        NewRow.DedCode = Row.DedCode
        NewRow.MemoCode = Row.MemoCode
        NewRow.PayrollLimit = Row.PayrollLimit
        NewRow.MonthlyLimit = Row.MonthlyLimit
        NewRow.AnnualLimit = Row.AnnualLimit
        NewRow.Pays = Row.Pays
        NewRow.PaysExclude = Row.PaysExclude
    End Sub

    Private Sub bbiUpdateWageRate_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiUpdateWageRate.ItemClick
        Using frm = New frmCompanyUnionUpdateWageRate
            frm.ShowDialog()
        End Using
    End Sub

    Private Sub gvUnionRates_ValidatingEditor(sender As Object, e As Controls.BaseContainerValidateEditorEventArgs) Handles gvUnionRates.ValidatingEditor
        If gvUnionRates.FocusedColumn.FieldName = "UnionNamePositionFund" AndAlso e.Value IsNot Nothing Then
            Dim Splits = e.Value.ToString.Split("_")
            If Splits.Length < 2 Then
                e.Valid = False
                e.ErrorText = "Union name should have 2 underscores"
            End If
        End If
    End Sub

    Private Sub gvUnionRates_InvalidValueException(sender As Object, e As InvalidValueExceptionEventArgs) Handles gvUnionRates.InvalidValueException
        e.ExceptionMode = ExceptionMode.DisplayError
        e.WindowCaption = "Input Error"
        ' Destroy the editor and discard the changes made within the edited cell 
        gvUnionRates.HideEditor()
    End Sub
End Class


Partial Public Class UnionDepartment
    Public ReadOnly Property Name() As String
        Get
            Return UnionNamePositionFund.Split("_")(0)
        End Get
    End Property

    Public ReadOnly Property Position As String
        Get
            Return UnionNamePositionFund.Split("_")(1)
        End Get
    End Property

    Public ReadOnly Property Fund As String
        Get
            Return UnionNamePositionFund.Split("_")(2)
        End Get
    End Property
End Class


Partial Public Class UnionRate
    <NotMapped>
    Public ReadOnly Property UnionNameParts() As String()
        Get
            Return UnionNamePositionFund.Split("_")
        End Get
    End Property

    <NotMapped>
    Public ReadOnly Property Name() As String
        Get
            Return UnionNamePositionFund.Split("_")(0)
        End Get
    End Property
    <NotMapped>
    Public ReadOnly Property Position As String
        Get
            Return If(UnionNameParts.Length > 1, UnionNameParts(1), Nothing)
        End Get
    End Property
    <NotMapped>
    Public ReadOnly Property Fund As String
        Get
            Return If(UnionNameParts.Length > 2, UnionNameParts(2), Nothing)
        End Get
    End Property
    <NotMapped>
    Public Property Checked As Boolean
End Class

Partial Public Class COMPANY
    Public ReadOnly Property CoNumAndName As String
        Get
            Return String.Format("{0} - {1}", CONUM, CO_NAME)
        End Get
    End Property
End Class