﻿'Imports CrystalDecisions.Shared
'Imports CrystalDecisions.CrystalReports.Engine
Imports System.ComponentModel
Imports System.Data
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors.Repository
Imports DXCustomControls
Imports DXCustomControls.CustomEditor
Imports Newtonsoft.Json

'Enum ParagraphElementKind
'    NumberParameter
'    BooleanParameter
'    StringParameter
'    DateTimeParameter
'    DateParameter
'End Enum

'Public Enum ParameterValueKind
'    NumberParameter
'    BooleanParameter
'    StringParameter
'    DateTimeParameter
'    DateParameter
'End Enum

'Public Class ParameterField
'    Public Name As Object
'    Public Value As Object
'    Public CurrentValues As CurValues
'    Public DefaultValues As CurValues
'    Public ParameterValueType As ParameterValueKind

'    Public Function HasCurrentValue() As Boolean
'        Return CurrentValues.Count > 0
'    End Function
'    Public Function GetFirstValue()
'        Return CurrentValues(0)
'    End Function

'    Public Class CurValues
'        Inherits Collections.ArrayList
'        Private obj As New List(Of Object)
'        Public Sub AddValue(obj As Object)
'            Me.obj.Add(obj)
'        End Sub

'        Public Overrides Sub Clear()
'            obj.Clear()
'        End Sub

'        Public Shadows Function Count()
'            Return obj.Count
'        End Function
'    End Class
'End Class

Public Class frmReportsParameters
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ReportParameters As List(Of FrontDeskReportParameter)
    'Private ReadOnly _reportDocument As ReportDocument
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CrystalOrigVersion As Boolean = False 'not sure why.  this is replacing the new signature passing in report
    'Private _ParamList As List(Of ParameterField)
    Private _ParamList As List(Of ParameterField)
    Private _FormMode As FormMode
    Dim paramList As List(Of SqlParameter)
    Dim customSqlParameterList As List(Of CustomSqlParameter)
    Dim _KeyValuePair As List(Of KeyValuePair)
    Dim db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property UseCustomEditors As Boolean = True
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Co As COMPANY
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property LastPrNum As Decimal?
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property LastCheckDate As Date?

    Public ReadOnly Property ParamFields As List(Of ParameterField)
        Get
            Return _ParamList
        End Get
    End Property

    Public Sub New(reportEmailTemplateId As Integer, conum As Decimal)
        InitializeComponent()
        _FormMode = FormMode.SavedReportParameters
        Dim report = db.ReportEmailTeplates.Single(Function(r) r.ID = reportEmailTemplateId)
        Dim parameters = report.ReportEmailTemplateParameters.OrderBy(Function(p) p.Sort).ToList()

        If Not parameters.Any Then
            DisplayErrorMessage($"Report Id: {reportEmailTemplateId} does not have any parameters setup", New NotImplementedException)
            Close()
        End If

        Dim list As List(Of FrontDeskReportParameter) = New List(Of FrontDeskReportParameter)

        For Each item In parameters
            Dim parameter = New FrontDeskReportParameter With {
                .ParameterId = item.Id,
                .Name = item.Name,
                .Description = item.Description,
                .DataType = item.DataType
            }
            If item.EditorType = "conum" Then
                parameter.Value = conum
            End If
            list.Add(parameter)
        Next

        ReportParameters = list

        colPromptText.Visible = True
        colName.FieldName = "Name"
        colPromptText.FieldName = "Description"
        colValue.FieldName = "Value"
        colValue.UnboundType = DevExpress.Data.UnboundColumnType.Bound

        If ReportParameters.Any(Function(p) p.Name = "df") AndAlso ReportParameters.Any(Function(p) p.Name = "dt") Then
            lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        ElseIf ReportParameters.Any(Function(p) p.Name = "From Date") AndAlso ReportParameters.Any(Function(p) p.Name = "To Date") Then
            lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        Else
            lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        End If
    End Sub

    'Public Sub New(_ReportDoc As ReportDocument)
    '    InitializeComponent()
    '    _reportDocument = _ReportDoc
    '    _FormMode = FormMode.Crytal
    'End Sub

    'use this constructor when setting a xsd manually to crystal reports 
    Public Sub New(ReportParams As List(Of ParameterField), Optional CrystalOrigVersion As Boolean = False)
        Me.CrystalOrigVersion = CrystalOrigVersion

        _ParamList = ReportParams
        InitializeComponent()
        _FormMode = FormMode.Crytal

        If Not CrystalOrigVersion Then
            Me.colName.Visible = False
            If ReportParams.Any(Function(p) p.Name = "df") AndAlso ReportParams.Any(Function(p) p.Name = "dt") Then
                lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
            Else
                lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
            End If
        End If
    End Sub

    'use this constructor when just using the form for any sql statment 
    Public Sub New(params As String())
        InitializeComponent()
        paramList = params.Select(Function(p) New SqlParameter(p, Nothing)).ToList
        For Each p In paramList
            p.SqlDbType = SqlDbType.VarChar
        Next
        _FormMode = FormMode.SqlParamaters
        colPromptText.Visible = False
        colName.FieldName = "ParameterName"
        colValue.FieldName = "Value"
        colValue.UnboundType = DevExpress.Data.UnboundColumnType.Bound
    End Sub

    Public Sub New(params As List(Of SqlParameter))
        InitializeComponent()
        paramList = params
        _FormMode = FormMode.SqlParamaters
        colPromptText.Visible = False
        colName.FieldName = "ParameterName"
        colValue.FieldName = "Value"
        colValue.UnboundType = DevExpress.Data.UnboundColumnType.Bound
    End Sub

    Public Sub New(keyValues As List(Of KeyValuePair), Optional availibleValues As List(Of String) = Nothing, Optional isSelectScript As Boolean = False)
        InitializeComponent()
        _KeyValuePair = keyValues
        _FormMode = FormMode.KeyValuePair

        Try
            For Each kvp In _KeyValuePair
                If isSelectScript AndAlso (kvp.Name.ToLower() = "@qtrenddate" OrElse kvp.Name.ToLower() = "@quarter_end" OrElse kvp.Name.ToLower() = "@quarterend") Then
                    kvp.Value = Query(Of Date)($"select custom.fn_GetQtrEndDate('{kvp.Value}')").FirstOrDefault()
                End If
            Next
        Catch ex As Exception

        End Try

        colPromptText.Visible = False
        colValue.FieldName = "Value"
        colValue.UnboundType = DevExpress.Data.UnboundColumnType.Bound
        If availibleValues IsNot Nothing Then
            riCeAvailibleValues.Items.AddRange(availibleValues)
            colValue.ColumnEdit = riCeAvailibleValues
        End If

        If keyValues IsNot Nothing AndAlso keyValues.Count <> 0 Then
            _ParamList = New List(Of ParameterField)
            For Each p In keyValues
                _ParamList.Add(New ParameterField With {.Name = p.Name})
            Next
        End If
    End Sub


    'Public Sub New(params As List(Of CustomSqlParameter))
    '    InitializeComponent()
    '    customSqlParameterList = params
    '    _FormMode = FormMode.CustomSqlParameter
    '    colPromptText.Visible = False
    '    colName.FieldName = "ParameterName"
    '    colValue.FieldName = "Value"
    '    colValue.UnboundType = DevExpress.Data.UnboundColumnType.Bound

    '    If params IsNot Nothing AndAlso params.Count <> 0 Then
    '        For Each p In params
    '            Dim pvt As ParagraphElementKind
    '            Select Case p.SqlDbType
    '                Case SqlDbType.BigInt
    '                    pvt = ParameterValueKind.NumberParameter
    '                Case SqlDbType.Bit
    '                    pvt = ParameterValueKind.BooleanParameter
    '                Case SqlDbType.Char, SqlDbType.NText, SqlDbType.VarChar, SqlDbType.NVarChar, SqlDbType.NChar
    '                    pvt = ParameterValueKind.StringParameter
    '                Case SqlDbType.BigInt, SqlDbType.Int, SqlDbType.Decimal, SqlDbType.Float, SqlDbType.Money, SqlDbType.SmallInt, SqlDbType.SmallMoney, SqlDbType.SmallInt, SqlDbType.SmallMoney
    '                    pvt = ParameterValueKind.NumberParameter
    '                Case SqlDbType.Date, SqlDbType.DateTime, SqlDbType.DateTime2, SqlDbType.SmallDateTime
    '            End Select
    '            _ParamList.Add(New ParameterField With {.CurrentValues = p.Value, .Name = p.ParameterName, .ParameterValueType = pvt})
    '        Next
    '    End If
    'End Sub

    Public Function GetSqlParamaters() As List(Of SqlParameter)
        Return paramList
    End Function

    Public Function GetCustomSqlParameters() As List(Of CustomSqlParameter)
        Return customSqlParameterList
    End Function

    Public Function GetKeyValuePairs() As List(Of KeyValuePair)
        Return _KeyValuePair
    End Function

    Public Function GetReportParameters() As List(Of FrontDeskReportParameter)
        Return ReportParameters
    End Function

    Private Sub frmReportsParameters_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            If _FormMode = FormMode.Crytal Then
                'If _reportDocument IsNot Nothing Then
                If CrystalOrigVersion Then
                    '_ParamList = _reportDocument.GetParameters()

                    'Dim rpcCls As New RabbitRpcCrystal.RpcClassesCrystal()
                    'Dim Errors As String = Nothing
                    'Dim params As ParameterField() = Nothing
                    ''Dim status = rpcCls.GetCrystalReportParams($"c:\temp\brandslogo.rpt", params, Errors)
                    'Dim status = rpcCls.GetCrystalReportParamsFull(_reportDocument.FileName, params, Errors)
                    '_ParamList = params.ToList()

                    If _ParamList.Any(Function(f) f?.Name.ToLower() = "conum") Then
                        _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "conum").Value = Co.CONUM
                    ElseIf _ParamList.Any(Function(f) f?.Name.ToLower() = "cono") Then
                        _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "cono").Value = Co.CONUM
                    End If

                    If LastPrNum.HasValue Then
                        If _ParamList.Any(Function(f) f?.Name.ToLower() = "prnum") Then
                            _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "prnum").Value = LastPrNum
                        ElseIf _ParamList.Any(Function(f) f?.Name.ToLower() = "prno") Then
                            _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "prno").Value = LastPrNum
                        ElseIf _ParamList.Any(Function(f) f?.Name.ToLower() = "prnumsp") Then
                            _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "prnumsp").Value = LastPrNum
                        End If
                    End If

                    If _ParamList.Any(Function(f) f?.Name.ToLower() = "year") Then
                        _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "year").Value = DateTime.Now.Year
                    End If

                    If _ParamList.Any(Function(f) f?.Name.ToLower() = "qtr") Then
                        _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "qtr").Value = GetQuarter()
                    ElseIf _ParamList.Any(Function(f) f?.Name.ToLower() = "quarter") Then
                        _ParamList.FirstOrDefault(Function(f) f?.Name.ToLower() = "quarter").Value = GetQuarter()
                    End If

                    If LastCheckDate.HasValue Then
                        '        REPORT.TrySetParameterValue(ParameterValueKind.DateTimeParameter, LastCheckDate)
                        '        REPORT.TrySetParameterValue(ParameterValueKind.DateParameter, LastCheckDate)
                    End If


                    If (_ParamList.Any(Function(p) p?.Name = "Date From") AndAlso _ParamList.Any(Function(p) p?.Name = "Date To")) OrElse (_ParamList.Any(Function(p) p?.Name = "df") AndAlso _ParamList.Any(Function(p) p?.Name = "dt")) Then
                        lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                    Else
                        lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                    End If
                End If

                'If (_ParamList.Any(Function(p) p.Name = "Date From") AndAlso _ParamList.Any(Function(p) p.Name = "Date To")) OrElse (_ParamList.Any(Function(p) p.Name = "df") AndAlso _ParamList.Any(Function(p) p.Name = "dt")) Then
                '    lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
                'Else
                '    lciDateRange.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                'End If

                Me.GridControl1.DataSource = _ParamList
            ElseIf _FormMode = FormMode.SqlParamaters Then
                GridControl1.DataSource = paramList
            ElseIf _FormMode = FormMode.KeyValuePair Then
                GridControl1.DataSource = _KeyValuePair
            ElseIf _FormMode = FormMode.CustomSqlParameter Then
                GridControl1.DataSource = customSqlParameterList
            ElseIf _FormMode = FormMode.SavedReportParameters Then
                GridControl1.DataSource = ReportParameters
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in load", ex)
        End Try
    End Sub

    Private Sub GridView1_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles GridView1.CustomUnboundColumnData
        If e.Row Is Nothing Then
            Return
        End If
        If _FormMode = FormMode.Crytal Then
            If e.Column Is colValue Then
                Dim row = CType(e.Row, ParameterField)
                Dim param As ParameterField = (From A In _ParamList Where A?.Name = row.Name).First
                If e.IsGetData Then
                    'e.Value = param.GetFirstValue()
                    e.Value = param.Value
                ElseIf e.IsSetData Then
                    'If _reportDocument IsNot Nothing Then
                    '    _reportDocument.ParameterFields(row.Name).CurrentValues.Clear()
                    '    _reportDocument.SetParameterValue(row.Name, e.Value)
                    '    If param IsNot Nothing Then
                    '        param.CurrentValues.Clear()
                    '        If Not String.IsNullOrEmpty(e.Value) Then param.CurrentValues.AddValue(e.Value)
                    '    End If
                    'Else
                    '    param.CurrentValues.Clear()
                    '    If Not String.IsNullOrEmpty(e.Value) Then param.CurrentValues.AddValue(e.Value)
                    'End If
                    param.Value = e.Value
                    param.CurrentValues?.Clear()
                End If
            End If
        End If
    End Sub

    Private Sub GridView1_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEditForEditing
        If e.Column Is colValue Then
            If _FormMode = FormMode.Crytal Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), ParameterField)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItem(row.Name, row)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.SqlParamaters Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), SqlParameter)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItemForSqlParameter(row)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.CustomSqlParameter Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), CustomSqlParameter)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItemForSqlParameter(row)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.KeyValuePair Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), KeyValuePair)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItem(row.Name, Nothing)

                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.SavedReportParameters Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), FrontDeskReportParameter)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItem(row.Name, Nothing)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            End If
        End If
    End Sub

    Private Sub GridView1_CustomRowCellEdit(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEdit
        If e.Column Is colValue Then
            If _FormMode = FormMode.Crytal Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), ParameterField)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItem(row.Name, row)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.SqlParamaters Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), SqlParameter)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItemForSqlParameter(row)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.CustomSqlParameter Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), CustomSqlParameter)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItemForSqlParameter(row)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.KeyValuePair Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), KeyValuePair)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItem(row.Name, Nothing)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            ElseIf _FormMode = FormMode.SavedReportParameters Then
                Dim row = CType(GridView1.GetRow(e.RowHandle), FrontDeskReportParameter)
                If row Is Nothing OrElse Not UseCustomEditors Then Exit Sub
                Dim rep = GetRepositoryItem(row.Name, Nothing)
                If rep IsNot Nothing Then
                    e.RepositoryItem = rep
                End If
            End If
        End If
    End Sub

    Private Function GetRepositoryItemForSqlParameter(param As SqlParameter) As RepositoryItem
        Dim rep = GetRepositoryItem(param.ParameterName, Nothing)
        If rep Is Nothing Then
            If param.DbType = DbType.Int32 Then
                rep = New RepositoryItemBaseSpinEdit
            End If
        End If
        Return rep
    End Function

    Private Function GetRepositoryItemForSqlParameter(param As CustomSqlParameter) As RepositoryItem
        If param.DropDownOptions.IsNotNullOrWhiteSpace Then
            Dim rep = New RepositoryItemComboBox()
            rep.Items.AddRange(param.DropDownOptions.Split(",").ToArray)
            Return rep
        Else
            Dim rep = GetRepositoryItem(param.ParameterName, Nothing)
            If rep Is Nothing Then
                If param.SqlDbType = DbType.Int32 Then
                    rep = New RepositoryItemBaseSpinEdit
                End If
            End If
            Return rep
        End If
    End Function


    Private Property _RepositoryItemsCache As Dictionary(Of String, RepositoryItem) = New Dictionary(Of String, RepositoryItem)
    Private Function GetRepositoryItem(name As String, param As ParameterField) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing
        Try
            name = name.ToLower

            If name.StartsWith("@") Then name = name.Replace("@", "")

            If Not _RepositoryItemsCache.TryGetValue(name, repItem) Then

                If param Is Nothing AndAlso _FormMode = FormMode.SqlParamaters OrElse _FormMode = FormMode.KeyValuePair Then
                    repItem = GetRepositoryItemByParamType(name)
                End If

                'use custom dropdown if param name is CoNum or EmpNum
                If name = "conum" OrElse name = "cf" Then
                    If _FormMode = FormMode.SqlParamaters OrElse _FormMode = FormMode.KeyValuePair OrElse _FormMode = FormMode.CustomSqlParameter Then
                        Return GetRepositoryItemForCoNum(name)
                    Else
                        Return New RepositoryItemBaseSpinEdit With {.ReadOnly = True}
                    End If
                ElseIf name = "empnum" OrElse name = "et" OrElse name = "ef" Then
                    'only if crystal report.
                    If _FormMode = FormMode.SavedReportParameters AndAlso GetSaveReportParameterConum().HasValue Then
                        Dim repo As RepositoryItemCustomEdit = GetRepositoryItemForEmpNum(name, GetSaveReportParameterConum)
                        Return repo
                    End If

                    Dim _CoParam = If(_ParamList Is Nothing, Nothing, _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf"))
                    'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                    '    Dim repo As RepositoryItemCustomEdit = GetRepositoryItemForEmpNum(name, Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value))
                    '    Return repo
                    'End If
                    If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                        Dim repo As RepositoryItemCustomEdit = GetRepositoryItemForEmpNum(name, Convert.ToDecimal(_CoParam.Value))
                        Return repo
                    End If
                ElseIf (name = "prnum" OrElse name = "pr") AndAlso _ParamList IsNot Nothing AndAlso _ParamList.Count <> 0 Then
                    Dim CoNum As Decimal?
                    Dim Year As Decimal? = Nothing
                    If _FormMode = FormMode.SqlParamaters OrElse _FormMode = FormMode.CustomSqlParameter Then
                        Dim _CoParam = _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
                        'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                        '    CoNum = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                        'End If
                        If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                            CoNum = Convert.ToDecimal(_CoParam.Value)
                        End If
                        Dim YearP = (From A In _ParamList Where A.Name = "y").FirstOrDefault
                        'If YearP IsNot Nothing AndAlso YearP.HasCurrentValue Then
                        '    Year = CType(YearP.CurrentValues(0), ParameterDiscreteValue).Value
                        'End If
                        If YearP IsNot Nothing AndAlso YearP.HasCurrentValue Then
                            Year = YearP.Value
                        End If
                    ElseIf _FormMode = FormMode.KeyValuePair Then
                        Dim _CoParam = _KeyValuePair.FirstOrDefault(Function(p) p.Name.ToLower = "@conum" OrElse p.Name = "conum")
                        If _CoParam IsNot Nothing AndAlso _CoParam.Value.IsNotNullOrWhiteSpace AndAlso IsNumeric(_CoParam.Value) Then
                            CoNum = Convert.ToDecimal(_CoParam.Value)
                        End If
                        Dim YearP = (From A In _KeyValuePair Where A.Name = "year").FirstOrDefault
                        If YearP IsNot Nothing AndAlso YearP.Value.IsNotNullOrWhiteSpace AndAlso IsNumeric(YearP.Value) Then
                            Year = YearP.Value
                        End If
                    ElseIf _FormMode = FormMode.Crytal Then
                        Dim _CoParam = _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
                        'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                        '    CoNum = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                        'End If
                        If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                            CoNum = Convert.ToDecimal(_CoParam.Value)
                        End If
                        _CoParam = _ParamList.FirstOrDefault(Function(p) p?.Name.ToLower = "year" OrElse p?.Name = "y")
                        'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                        '    Year = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                        'End If
                        If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                            Year = Convert.ToDecimal(_CoParam.Value)
                        End If
                    ElseIf _FormMode = FormMode.SavedReportParameters Then
                        Try
                            Dim pp = ReportParameters.SingleOrDefault(Function(p) p?.Name.ToLower = "conum" OrElse p?.Name = "cf")
                            Dim c As Decimal
                            If pp IsNot Nothing AndAlso Decimal.TryParse(nz(pp.Value, ""), c) Then
                                CoNum = c
                            End If
                        Catch ex As Exception
                            CoNum = GetSaveReportParameterConum()
                        End Try
                    End If


                    If CoNum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForPrNum(name, CoNum, Year)
                        Return repo
                    End If
                ElseIf name = "prnumsp" Then
                    Dim _CoParam = _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
                    'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                    '    Dim CoNum As Decimal = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                    If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                        Dim CoNum As Decimal = Convert.ToDecimal(_CoParam.Value)
                        Dim Year As Decimal? = Nothing
                        Dim YearP = (From A In _ParamList Where A.Name = "y").FirstOrDefault
                        If YearP IsNot Nothing AndAlso YearP.HasCurrentValue Then
                            'Year = CType(YearP.CurrentValues(0), ParameterDiscreteValue).Value
                            Year = YearP.Value
                        End If

                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForPrNumSp(name, CoNum, Year)
                        Return repo
                    End If
                ElseIf name = "s" Then
                    Dim r = New RepositoryItemLookUpEdit With {.DisplayMember = "Name", .ValueMember = "Value"}
                    Dim list = New List(Of KeyValuePair)
                    list.Add(New KeyValuePair With {.Name = "Active Only", .Value = "-1"})
                    list.Add(New KeyValuePair With {.Name = "Terminated Only", .Value = "1"})
                    list.Add(New KeyValuePair With {.Name = "All Employees", .Value = "2"})
                    r.DataSource = list
                    r.NullText = String.Empty
                    GridControl1.RepositoryItems.Add(r)
                    _RepositoryItemsCache.Add(name, r)
                    Return r
                ElseIf name = "divnum" OrElse name = "dv" Then
                    If _FormMode = FormMode.SavedReportParameters AndAlso GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForDivNum(name, GetSaveReportParameterConum)
                        Return repo
                    End If
                    Dim _CoParam = _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
                    'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                    '    Dim CoNum As Decimal = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                    '    Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForDivNum(name, CoNum)
                    '    Return repo
                    'End If
                    If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                        Dim CoNum As Decimal = Convert.ToDecimal(_CoParam.Value)
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForDivNum(name, CoNum)
                        Return repo
                    End If
                ElseIf name = "j" OrElse name = "jobnum" Then
                    If _FormMode = FormMode.SavedReportParameters AndAlso GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForJobNum(name, GetSaveReportParameterConum)
                        Return repo
                    End If

                    Dim _CoParam = _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
                    'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                    '    Dim CoNum As Decimal = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                    If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                        Dim CoNum As Decimal = Convert.ToDecimal(_CoParam.Value)

                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForJobNum(name, CoNum)
                        Return repo
                    End If
                ElseIf name = "dp" OrElse name = "depnum" Then
                    If _FormMode = FormMode.SavedReportParameters AndAlso GetSaveReportParameterConum.HasValue Then
                        Dim repo As RepositoryItemLookUpEdit = GetRepositoryItemForDepNum(name, GetSaveReportParameterConum)
                        Return repo
                    End If

                    Dim _CoParam = _ParamList.FirstOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
                    'If _CoParam IsNot Nothing AndAlso _CoParam.CurrentValues.Count > 0 Then
                    '    Dim CoNum As Decimal = Convert.ToDecimal(CType(_CoParam.CurrentValues(0), ParameterDiscreteValue).Value)
                    If _CoParam IsNot Nothing AndAlso _CoParam.HasCurrentValue Then
                        Dim CoNum As Decimal = Convert.ToDecimal(_CoParam.Value)

                        Dim repo As RepositoryItemCustomEdit = GetRepositoryItemForDepNum(name, CoNum)
                        Return repo
                    End If
                Else
                    Dim paramMap = db.ReportEPParamMaps.SingleOrDefault(Function(p) p.Param = name)
                    If paramMap IsNot Nothing AndAlso paramMap.AvailibleValues.IsNotNullOrWhiteSpace Then
                        Dim r = New RepositoryItemComboBox
                        r.Items.AddRange(paramMap.AvailibleValues.Split(","))
                        r.NullText = String.Empty
                        GridControl1.RepositoryItems.Add(r)
                        _RepositoryItemsCache.Add(name, r)
                        Return r
                    End If
                End If

                If param IsNot Nothing Then
                    repItem = GetRepositoryItemByParamType(name, param)
                End If

            End If
        Catch ex As Exception
            DisplayErrorMessage($"Error getting editor for {name}", ex)
        End Try
        Return repItem
    End Function

    Private Function GetSaveReportParameterConum() As Decimal?
        Dim pp = ReportParameters.SingleOrDefault(Function(p) p.Name.ToLower = "conum" OrElse p.Name = "cf")
        Dim c As Decimal
        If pp IsNot Nothing AndAlso Decimal.TryParse(nz(pp.Value, ""), c) Then
            Return c
        Else
            Return Nothing
        End If
    End Function

    Private Function GetRepositoryItemForEmpNum(ByVal name As String, ByVal coNum As Decimal) As DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim list As List(Of EMPLOYEE) = (New List(Of EMPLOYEE)(New EMPLOYEE() {New EMPLOYEE With {.EMPNUM = -1, .L_NAME = "", .F_NAME = "(ALL Employees)"}}))
        Dim AllEmployees = (From A In list.Union(db.EMPLOYEEs.Where(Function(c) c.CONUM = coNum).ToList())
                            Select New With {.EmpNum = A.EMPNUM, .LastName = A.L_NAME, .FistName = A.F_NAME, .TermDate = A.TERM_DATE, .FullName = "{0} - {1} {2}".FormatWith(A.EMPNUM, A.F_NAME, A.L_NAME)}).ToList
        Dim repo = New CustomEditor.RepositoryItemCustomEdit
        'AllEmployees.Add(CType(New With {.EmpNum = -1, .LastName = "", .FistName = "", .TermDate = "", .FullName = ""}, Object))
        repo.DataSource = AllEmployees
        repo.DisplayMember = "FullName"
        repo.ValueMember = "EmpNum"
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForCoNum(ByVal name As String) As RepositoryItem
        Try
            Dim companyListWithActiveStatus = (From A In db.COMPANies Select New With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .PR_CONTACT = A.PR_CONTACT,
                                                           .CO_FAX = A.CO_FAX, .FED_ID = A.FED_ID, .CO_EMAIL = A.CO_EMAIL, .CoNumAndName = "{0} - {1}".FormatWith(A.CONUM, A.CO_NAME), .Status = A.CO_STATUS}
                                                       ).ToList
            Dim compStatus = (From c In db.CO_UDFs Where c.UDF_DESCR.Equals("INACTIVE STATUS")).ToDictionary(Function(key) key.CONUM)

            For Each item In companyListWithActiveStatus.Where(Function(c) c.Status = "Active Status")
                Dim value As CO_UDF = Nothing
                If compStatus.TryGetValue(item.CONUM, value) Then
                    item.Status = value.UDF_STRING
                End If
            Next

            Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
            repo.DataSource = companyListWithActiveStatus
            repo.DisplayMember = "CoNumAndName"
            repo.ValueMember = "CONUM"
            AddHandler repo.EditValueChanged,
                Sub(s As Object, e As EventArgs)
                    Me.GridView1.PostEditor()
                    If _RepositoryItemsCache.ContainsKey("empnum") Then
                        _RepositoryItemsCache.Remove("empnum")
                        ClearParametersValue("empnum")
                        GridView1.RefreshData()
                    End If

                    If _RepositoryItemsCache.ContainsKey("prnum") Then
                        _RepositoryItemsCache.Remove("prnum")
                        ClearParametersValue("prnum")
                        GridView1.RefreshData()
                    End If
                    If _RepositoryItemsCache.ContainsKey("pr") Then
                        _RepositoryItemsCache.Remove("pr")
                        ClearParametersValue("pr")
                        GridView1.RefreshData()
                    End If
                End Sub
            GridControl1.RepositoryItems.Add(repo)
            _RepositoryItemsCache.Add(name, repo)
            Return repo
        Catch ex As Exception
            DisplayErrorMessage("Error getting repository item", ex)
        End Try
        Return Nothing
    End Function

    'added by solomon
    Private Function GetRepositoryItemForPrNumSp(ByVal name As String, ByVal CoNum As Decimal, ByVal Year As Decimal?) As RepositoryItemLookUpEdit
        Dim lstPrl As New List(Of PAYROLL)(New PAYROLL() {New PAYROLL() With {
    .PRNUM = -1,
    .CHECK_DATE = Nothing,
    .PR_DESCR = "(ALL)"
}})


        Dim list = (From A In lstPrl.Union(db.PAYROLLs.Where(Function(c) c.CONUM = CoNum AndAlso (Not Year.HasValue OrElse c.CHECK_DATE.GetValueOrDefault.Year = Year.Value))) Select New With {
        .PrNum = A.PRNUM,
    .CheckDate = A.CHECK_DATE,
    .PrDesc = A.PR_DESCR,
    .Value = [String].Format("{0}  {1} {2}", A.PRNUM, (If(A.CHECK_DATE.HasValue, A.CHECK_DATE.Value.ToShortDateString(), "")), A.PR_DESCR), .AllEmp = If(A.PRNUM = -1, -1, 0)
}).ToList().OrderBy(Function(s) s.AllEmp).ThenByDescending(Function(s) s.PrNum)

        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "PrNum", .ValueMember = "PrNum"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForPrNum(ByVal name As String, ByVal CoNum As Decimal, ByVal Year As Decimal?) As RepositoryItemLookUpEdit
        Dim CoPayrolls = (From A In db.PAYROLLs
                          Where A.CONUM = CoNum AndAlso (Not Year.HasValue OrElse A.CHECK_DATE.Value.Year = Year)
                          Select New With {.PrNum = A.PRNUM, .CheckDate = A.CHECK_DATE, .PrDescr = A.PR_DESCR}).OrderByDescending(Function(o) o.PrNum).ToList
        Dim list = (From A In CoPayrolls Select A.PrNum, A.CheckDate, Value = String.Format("{0}  {1} {2}", A.PrNum, If(A.CheckDate.HasValue, A.CheckDate.Value.ToShortDateString, ""), A.PrDescr)).ToList

        'Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "PrNum", .ValueMember = "PrNum"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemByParamType(ByVal name As String) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing

        If name.Contains("date") Then
            Dim repo = New RepositoryItemDateEdit
            GridControl1.RepositoryItems.Add(repo)
            repItem = repo
            _RepositoryItemsCache.Add(name, repo)
        End If

        Return repItem
    End Function

    Private Function GetRepositoryItemByParamType(ByVal name As String, ByVal param As ParameterField) As RepositoryItem
        Dim repItem As RepositoryItem = Nothing
        Select Case param.ParameterValueType

            Case ParameterValueKind.NumberParameter
                Dim repo = New RepositoryItemSpinEdit
                GridControl1.RepositoryItems.Add(repo)
                repItem = repo
                _RepositoryItemsCache.Add(name, repo)
                If name = "y" Then
                    AddHandler repo.EditValueChanged,
                    Sub(s As Object, e As EventArgs)
                        Me.GridView1.PostEditor()
                        If _RepositoryItemsCache.ContainsKey("prnum") Then
                            _RepositoryItemsCache.Remove("prnum")
                            '_ParamList.Single(Function(p) p.Name.ToLower = "prnum").CurrentValues.Clear()
                            _ParamList.Single(Function(p) p.Name.ToLower = "prnum").Value = Nothing
                            _ParamList.Single(Function(p) p.Name.ToLower = "prnum").CurrentValues.Clear()
                            GridView1.RefreshData()
                        End If
                        If _RepositoryItemsCache.ContainsKey("pr") Then
                            _RepositoryItemsCache.Remove("pr")
                            '_ParamList.Single(Function(p) p.Name.ToLower = "pr").CurrentValues.Clear()
                            _ParamList.Single(Function(p) p.Name.ToLower = "pr").Value = Nothing
                            _ParamList.Single(Function(p) p.Name.ToLower = "pr").CurrentValues.Clear()
                            GridView1.RefreshData()
                        End If
                    End Sub
                End If

            Case ParameterValueKind.StringParameter
                If param.DefaultValues IsNot Nothing AndAlso param.DefaultValues.GetType() <> GetType(Object) AndAlso param.DefaultValues.Count > 0 Then
                    Dim repo = New RepositoryItemComboBox
                    'repo.Items.AddRange(param.DefaultValues.ToArray().Cast(Of ParameterDiscreteValue).Select(Function(p) p.Value).ToArray())
                    If param.DefaultValues IsNot Nothing AndAlso param.DefaultValues.GetType() = GetType(Linq.JArray) Then
                        repo.Items.AddRange(CType(param.DefaultValues, Linq.JArray).Children.ToArray())
                    Else
                        repo.Items.AddRange(param.DefaultValues.ToArray())
                    End If

                    GridControl1.RepositoryItems.Add(repo)
                    repItem = repo
                    _RepositoryItemsCache.Add(name, repo)
                Else
                    Dim repo = New RepositoryItemTextEdit
                    GridControl1.RepositoryItems.Add(repo)
                    repItem = repo
                    _RepositoryItemsCache.Add(name, repo)
                End If

            Case ParameterValueKind.DateParameter
                Dim repo = New RepositoryItemDateEdit
                GridControl1.RepositoryItems.Add(repo)
                repItem = repo
                _RepositoryItemsCache.Add(name, repo)

            Case ParameterValueKind.DateTimeParameter
                Dim repo = New RepositoryItemDateEdit With {.EditMask = "yyyy/MM/dd HH:mm"}
                repo.Mask.UseMaskAsDisplayFormat = True
                GridControl1.RepositoryItems.Add(repo)
                repItem = repo
                _RepositoryItemsCache.Add(name, repo)
        End Select
        Return repItem
    End Function

    Private Function GetRepositoryItemForDivNum(ByVal name As String, ByVal CoNum As Decimal) As RepositoryItem
        Dim allDivs = (New List(Of DIVISION)(New DIVISION() {New DIVISION With {.DDIVNUM = -1, .DDIVNAME = "All Divisions"}}))
        Dim CoPayrolls = allDivs.Union((From A In db.DIVISIONs Where A.CONUM = CoNum).OrderByDescending(Function(o) o.DDIVNUM))
        Dim list = (From A In CoPayrolls Select DivNum = A.DDIVNUM, Value = A.DivNumName).ToList
        'Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "Value", .ValueMember = "DivNum"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForJobNum(ByVal name As String, ByVal CoNum As Decimal) As RepositoryItem
        Dim allJobs = (New List(Of CO_JOB)(New CO_JOB() {New CO_JOB With {.job_id = -1, .job_descr = "All Jobs"}}))
        Dim CoJobs = allJobs.Union((From A In db.CO_JOBS Where A.conum = CoNum).OrderByDescending(Function(o) o.job_id))
        Dim list = (From A In CoJobs Select JobId = A.job_id, Value = "{0}: {1}".FormatWith(A.job_id, A.job_descr)).ToList
        'Dim repo = New DXCustomControls.CustomEditor.RepositoryItemCustomEdit
        Dim repo = New RepositoryItemLookUpEdit() With {.DataSource = list, .DisplayMember = "Value", .ValueMember = "JobId"}
        repo.Columns.Add(New DevExpress.XtraEditors.Controls.LookUpColumnInfo("Value", 100))
        repo.PopupWidth = 100
        repo.NullText = String.Empty
        repo.ShowHeader = False
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Function GetRepositoryItemForDepNum(ByVal name As String, ByVal CoNum As Decimal) As RepositoryItem
        Dim list As List(Of DEPARTMENT) = (New List(Of DEPARTMENT)(New DEPARTMENT() {New DEPARTMENT With {.DEPTNUM = -1, .DEPT_DESC = "(ALL Departments)"}}))
        Dim AllDepartments = (From A In list.Union(db.DEPARTMENTs.Where(Function(c) c.CONUM = CoNum).ToList())
                              Select New With {.DivNum = A.DIVNUMD, .DepNum = A.DEPTNUM, .DeptDesc = A.DEPT_DESC, .Value = "{0}: {1}".FormatWith(A.DEPTNUM, A.DEPT_DESC)}).ToList
        Dim repo = New CustomEditor.RepositoryItemCustomEdit
        'AllEmployees.Add(CType(New With {.EmpNum = -1, .LastName = "", .FistName = "", .TermDate = "", .FullName = ""}, Object))
        repo.DataSource = AllDepartments
        repo.DisplayMember = "Value"
        repo.ValueMember = "DepNum"
        GridControl1.RepositoryItems.Add(repo)
        _RepositoryItemsCache.Add(name, repo)
        Return repo
    End Function

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.OK
    End Sub

    Private Sub frmReportsParameters_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.Control AndAlso e.KeyCode = Keys.Enter Then
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Enum FormMode
        Crytal = 0
        SqlParamaters = 1
        KeyValuePair = 2
        CustomSqlParameter = 3
        SavedReportParameters = 4
    End Enum

    Private Sub cbeDateRange_SelectedValueChanged(sender As Object, e As EventArgs) Handles cbeDateRange.SelectedValueChanged
        Dim df As DateTime
        Dim dt As DateTime
        DateFunctions.GetDateRange(cbeDateRange.Text, df, dt)

        If _FormMode = FormMode.SavedReportParameters Then
            Dim dfp = ReportParameters.SingleOrDefault(Function(p) p.Name = "df")
            Dim dtp = ReportParameters.SingleOrDefault(Function(p) p.Name = "dt")
            If dfp Is Nothing AndAlso dtp Is Nothing Then
                dfp = ReportParameters.SingleOrDefault(Function(p) p.Name = "From Date")
                dtp = ReportParameters.SingleOrDefault(Function(p) p.Name = "To Date")
            End If
            dfp.Value = df
            dtp.Value = dt
            GridControl1.RefreshDataSource()
            Exit Sub
        End If


        'If _reportDocument Is Nothing Then
        If CrystalOrigVersion OrElse _ParamList.FirstOrDefault(Function(p) p.Name = "df") IsNot Nothing Then
            Dim pp = _ParamList.Single(Function(p) p.Name = "df")
            pp.CurrentValues.Clear()
            'pp.CurrentValues.AddValue(df)
            pp.Value = df
            pp = _ParamList.Single(Function(p) p.Name = "dt")
            pp.CurrentValues.Clear()
            'pp.CurrentValues.AddValue(dt)
            pp.Value = dt
            GridControl1.RefreshDataSource()
        Else
            Dim pp = _ParamList.Single(Function(p) p.Name = "Date From")
            pp.CurrentValues.Clear()
            'pp.CurrentValues.AddValue(df)
            pp.Value = df
            pp = _ParamList.Single(Function(p) p.Name = "Date To")
            pp.CurrentValues.Clear()
            'pp.CurrentValues.AddValue(dt)
            pp.Value = dt
            GridControl1.RefreshDataSource()
        End If
    End Sub

    Private Sub ClearParametersValue(name As String)
        Select Case _FormMode
            Case FormMode.Crytal
                _ParamList.Single(Function(p) p.Name.ToLower = name).CurrentValues.Clear()
                _ParamList.Single(Function(p) p.Name.ToLower = name).Value = Nothing
            Case FormMode.KeyValuePair
                _KeyValuePair.Single(Function(k) String.Equals(k.Name.Replace("@", ""), name.Replace("@", ""), StringComparison.CurrentCultureIgnoreCase)).Value = Nothing
            Case FormMode.SqlParamaters
            Case FormMode.CustomSqlParameter
                paramList.Single(Function(p) p.ParameterName = name).Value = DBNull.Value
        End Select
    End Sub
End Class


Public Class FrontDeskReportParameter
    Inherits Brands.DAL.DomainModels.App.ReportParameter
    <JsonIgnore>
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property DataType As String
    <JsonIgnore>
    Property ReportEmailTemplateParameters As Brands.DAL.ReportEmailTemplateParameter
End Class
