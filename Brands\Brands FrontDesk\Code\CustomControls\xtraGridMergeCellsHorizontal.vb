﻿Imports DevExpress.Utils.Drawing
Imports DevExpress.XtraGrid.Columns
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.Drawing
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo


Public Class MyCellMergeHelper
    Private _MergedCells As New List(Of MyMergedCell)()
    Public ReadOnly Property MergedCells() As List(Of MyMergedCell)
        Get
            Return _MergedCells
        End Get
    End Property

    Private painter As MyGridPainter


    Private _view As GridView

    Public Sub New(ByVal view As GridView)
        _view = view
        AddHandler view.CustomDrawCell, AddressOf view_CustomDrawCell
        AddHandler view.GridControl.Paint, AddressOf GridControl_Paint
        AddHandler view.CellValueChanged, AddressOf view_CellValueChanged
        painter = New MyGridPainter(view)
    End Sub

    Public Function AddMergedCell(ByVal rowHandle As Integer, ByVal col1 As GridColumn, ByVal col2 As GridColumn) As MyMergedCell
        Dim cell As New MyMergedCell(rowHandle, col1, col2)
        _MergedCells.Add(cell)
        Return cell
    End Function
    Public Sub AddMergedCell(ByVal rowHandle As Integer, ByVal col1 As Integer, ByVal col2 As Integer, ByVal value As Object)
        AddMergedCell(rowHandle, _view.Columns(col1), _view.Columns(col2))
    End Sub

    Public Sub AddMergedCell(ByVal rowHandle As Integer, ByVal col1 As GridColumn, ByVal col2 As GridColumn, ByVal value As Object)
        Dim cell As MyMergedCell = AddMergedCell(rowHandle, col1, col2)
        SafeSetMergedCellValue(cell, value)
    End Sub



    Public Sub SafeSetMergedCellValue(ByVal cell As MyMergedCell, ByVal value As Object)
        If cell IsNot Nothing Then
            SafeSetCellValue(cell.RowHandle, cell.Column1, value)
            SafeSetCellValue(cell.RowHandle, cell.Column2, value)
        End If
    End Sub

    Public Sub SafeSetCellValue(ByVal rowHandle As Integer, ByVal column As GridColumn, ByVal value As Object)
        If _view.GetRowCellValue(rowHandle, column) IsNot value Then
            _view.SetRowCellValue(rowHandle, column, value)
        End If
    End Sub


    Private Function GetMergedCell(ByVal rowHandle As Integer, ByVal column As GridColumn) As MyMergedCell
        For Each cell As MyMergedCell In _MergedCells
            If cell.RowHandle = rowHandle AndAlso (column Is cell.Column1 OrElse column Is cell.Column2) Then
                Return cell
            End If
        Next cell
        Return Nothing
    End Function

    Private Function IsMergedCell(ByVal rowHandle As Integer, ByVal column As GridColumn) As Boolean
        Return GetMergedCell(rowHandle, column) IsNot Nothing
    End Function

    Private Sub DrawMergedCells(ByVal e As PaintEventArgs)
        For Each cell As MyMergedCell In _MergedCells
            painter.DrawMergedCell(cell, e)
        Next cell
    End Sub


    Private Sub view_CellValueChanged(ByVal sender As Object, ByVal e As CellValueChangedEventArgs)
        SafeSetMergedCellValue(GetMergedCell(e.RowHandle, e.Column), e.Value)
    End Sub

    Private Sub GridControl_Paint(ByVal sender As Object, ByVal e As PaintEventArgs)
        DrawMergedCells(e)
    End Sub

    Private Sub view_CustomDrawCell(ByVal sender As Object, ByVal e As RowCellCustomDrawEventArgs)
        If IsMergedCell(e.RowHandle, e.Column) Then
            e.Handled = Not painter.IsCustomPainting
        End If
    End Sub

End Class

Public Class MyGridPainter
    Inherits GridPainter

    Public Sub New(ByVal view As GridView)
        MyBase.New(view)

    End Sub

    Private _IsCustomPainting As Boolean
    Public Property IsCustomPainting() As Boolean
        Get
            Return _IsCustomPainting
        End Get
        Set(ByVal value As Boolean)
            _IsCustomPainting = value
        End Set
    End Property

    Public Overloads Sub DrawMergedCell(ByVal cell As MyMergedCell, ByVal e As PaintEventArgs)
        Try
            Dim delta As Integer = cell.Column1.VisibleIndex - cell.Column2.VisibleIndex
            If Math.Abs(delta) > 1 Then
                Return
            End If
            Dim vi As GridViewInfo = TryCast(View.GetViewInfo(), GridViewInfo)
            Dim gridCellInfo1 As GridCellInfo = vi.GetGridCellInfo(cell.RowHandle, cell.Column1)
            Dim gridCellInfo2 As GridCellInfo = vi.GetGridCellInfo(cell.RowHandle, cell.Column2)
            If gridCellInfo1 Is Nothing OrElse gridCellInfo2 Is Nothing Then
                Return
            End If
            Dim targetRect As Rectangle = Rectangle.Union(gridCellInfo1.Bounds, gridCellInfo2.Bounds)
            gridCellInfo1.Bounds = targetRect
            gridCellInfo1.CellValueRect = targetRect
            gridCellInfo2.Bounds = targetRect
            gridCellInfo2.CellValueRect = targetRect
            If delta < 0 Then
                gridCellInfo1 = gridCellInfo2
            End If
            Dim bounds As Rectangle
            If gridCellInfo1.ViewInfo IsNot Nothing Then
                bounds = gridCellInfo1.ViewInfo.Bounds
            Else
                bounds = gridCellInfo1.Bounds
            End If
            bounds.Width = targetRect.Width
            bounds.Height = targetRect.Height
            If gridCellInfo1.ViewInfo IsNot Nothing Then
                gridCellInfo1.ViewInfo.Bounds = bounds
                gridCellInfo1.ViewInfo.CalcViewInfo(e.Graphics)
            End If
            IsCustomPainting = True
            Dim cache As New GraphicsCache(e.Graphics)
            gridCellInfo1.Appearance.FillRectangle(cache, gridCellInfo1.Bounds)
            DrawRowCell(New GridViewDrawArgs(cache, vi, vi.ViewRects.Bounds), gridCellInfo1)
            IsCustomPainting = False
        Catch ex As Exception
            Logger.Error(ex, "Error in DrawMergedCell")
        End Try
    End Sub

End Class

Public Class MyMergedCell

    Public Sub New(ByVal rowHandle As Integer, ByVal col1 As GridColumn, ByVal col2 As GridColumn)
        _RowHandle = rowHandle
        _Column1 = col1
        _Column2 = col2
    End Sub

    Private _Column2 As GridColumn
    Private _Column1 As GridColumn
    Private _RowHandle As Integer
    Public Property RowHandle() As Integer
        Get
            Return _RowHandle
        End Get
        Set(ByVal value As Integer)
            _RowHandle = value
        End Set
    End Property


    Public Property Column1() As GridColumn
        Get
            Return _Column1
        End Get
        Set(ByVal value As GridColumn)
            _Column1 = value

        End Set
    End Property


    Public Property Column2() As GridColumn
        Get
            Return _Column2
        End Get
        Set(ByVal value As GridColumn)
            _Column2 = value

        End Set
    End Property


End Class
