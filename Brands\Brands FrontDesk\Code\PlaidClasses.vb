﻿Imports Newtonsoft.Json
Namespace PlaidQuickstartBlazor.Shared
    Public Class PlaidError
        Public Property error_type As String = String.Empty
        Public Property error_code As String = String.Empty
        Public Property error_message As String = String.Empty
        Public Property display_message As String
        Public Property error_type_path As String

        <JsonIgnore>
        Public ReadOnly Property Url As String
            Get
                Return If(error_type_path IsNot Nothing, $"https://plaid.com/docs/errors/{error_type_path}/#{error_code.ToLowerInvariant()}", Nothing)
            End Get
        End Property
    End Class

    Public Class DataTable
        Public Property Columns As Column() = Array.Empty(Of Column)()
        Public Property Rows As Row() = Array.Empty(Of Row)()

        Public Sub New()
        End Sub
    End Class

    Public Class Column
        Public Property Title As String = String.Empty
        Public Property IsRight As Boolean
    End Class

    Public Class Row
        Public Property Cells As String() = Array.Empty(Of String)()

        Public Sub New()
        End Sub

        Public Sub New(ParamArray cells As String())
            cells = cells
        End Sub
    End Class


    Friend Class ServerDataTable
        Inherits DataTable

        Private Sub New()

        End Sub

        Friend Sub New(ParamArray cols As String())
            Columns = cols.[Select](Function(x)
                                        Dim split = x.Split("/")
                                        Return New Column() With {
                                        .Title = split(0),
                                        .IsRight = split.Length > 1 AndAlso split(1) = "r"
                                    }
                                    End Function).ToArray()
        End Sub
    End Class

    Class Auth
        Public Property Name As String
        Public Property Balance As String
        Public Property Account As String
        Public Property Routing As String
    End Class

    Class Balance
        Public Property AcctName As String
        Public Property AcctID As String
        Public Property Balance As String
    End Class

    Class AuthGetRequestOptions2
        Inherits Going.Plaid.Entity.AuthGetRequestOptions

        Public Property min_last_updated_datetime As String
    End Class

    Class AccountsBalanceGetRequestOptions2
        Inherits Going.Plaid.Entity.AccountsBalanceGetRequestOptions

        Public Property min_last_updated_datetime As String
    End Class
End Namespace