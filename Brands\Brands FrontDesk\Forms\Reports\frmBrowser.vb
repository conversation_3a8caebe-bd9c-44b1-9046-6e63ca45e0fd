﻿Public Class frmBrowser
    Public Sub New(url As String)
        InitializeComponent()
        TextEdit1.Text = url
        WebBrowser1.Url = New Uri(url)
    End Sub

    Private Sub BarButtonItem1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem1.ItemClick
        WebBrowser1.Refresh()
    End Sub

    Private Sub frmBrowser_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub WebBrowser1_Navigating(sender As Object, e As WebBrowserNavigatingEventArgs) Handles WebBrowser1.Navigating

    End Sub

    Private Sub WebBrowser1_Navigated(sender As Object, e As WebBrowserNavigatedEventArgs) Handles WebBrowser1.Navigated

    End Sub

    Private Sub WebBrowser1_DocumentCompleted(sender As Object, e As WebBrowserDocumentCompletedEventArgs) Handles WebBrowser1.DocumentCompleted

    End Sub

    Private Sub TextEdit1_Validated(sender As Object, e As EventArgs) Handles TextEdit1.Validated
        Try
            WebBrowser1.Url = New Uri(TextEdit1.Text)
        Catch ex As Exception
            DisplayMessageBox("Error in web browser URL")
        End Try
    End Sub

    Private Sub BarButtonItem2_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem2.ItemClick
        Dim path = System.IO.Path.ChangeExtension(WebBrowser1.Url.OriginalString.Replace("file:///", ""), "html")
        System.IO.File.Move(WebBrowser1.Url.OriginalString.Replace("file:///", ""), path)
        'System.Diagnostics.Process.Start("file:///" & path)
        Dim psi As New System.Diagnostics.ProcessStartInfo()
        psi.FileName = "file:///" & path
        psi.UseShellExecute = True
        System.Diagnostics.Process.Start(psi)
    End Sub
End Class