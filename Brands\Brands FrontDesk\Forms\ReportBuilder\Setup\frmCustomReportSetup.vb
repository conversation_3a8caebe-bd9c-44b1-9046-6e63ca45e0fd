﻿Imports Brands_FrontDesk
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports Microsoft.EntityFrameworkCore

Public Class frmCustomReportSetup
    Private db As dbEPDataDataContext
    Private dbColumnDB As dbEPDataDataContext

    Private Property FocusedReport As CustomReport

    Sub New()
        InitializeComponent()
    End Sub

    Private Async Sub frmReportColumns_LoadAsync(sender As Object, e As EventArgs) Handles MyBase.Load
        Await LoadDataAsync()
    End Sub

    Private Async Sub btnRefresh_ClickAsync(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Await LoadDataAsync()
    End Sub

    Private Async Function LoadDataAsync() As Task
        Try
            lcRoot.ShowProgessPanel
            Dim i = gvCustomReports.FocusedRowHandle
            db = New dbEPDataDataContext(GetConnectionString)
            gcCustomReports.DataSource = Await Task.Run(Function() db.CustomReports.ToList)
            gvCustomReports.FocusedRowHandle = i
        Catch ex As Exception
            modGlobals.DisplayErrorMessage("Error laoding data", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Function

    Private Async Sub btnAddNewReport_ClickAsync(sender As Object, e As EventArgs) Handles btnAdd.Click
        Dim report = New CustomReport With {.AuthorName = UserName}
        If ShowAddOrEditReportForm(report, True) = DialogResult.OK Then
            Await LoadDataAsync()
        End If
    End Sub

    Private Sub gvCustomReports_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles gvCustomReports.PopupMenuShowing
        If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            e.Menu.Items.Add(New DXMenuItem("Edit", Sub() btnEditReport_ClickAsync(Nothing, Nothing), My.Resources.edit_16x16))
            e.Menu.Items.Add(New DXMenuItem("Delete", Sub() btnDeleteReport_ClickAsync(Nothing, Nothing), My.Resources.delete_16x16))
            e.Menu.Items.Add(New DXMenuItem("Duplicate Report", Sub() DuplicateReportAsync(), My.Resources.copy_16x16))
        End If
    End Sub

    Private Async Sub DuplicateReportAsync()
        Dim report As CustomReport = gvCustomReports.GetFocusedRow
        If report IsNot Nothing Then
            Dim newReport = report.CloneEntity
            newReport.Name &= " - Copy"
            Using db As New dbEPDataDataContext(GetConnectionString)
                db.CustomReports.InsertOnSubmit(newReport)
                db.SaveChanges()
                Await LoadDataAsync()
            End Using
        End If
    End Sub

    Private Async Sub btnEditReport_ClickAsync(sender As Object, e As EventArgs) Handles btnEdit.Click
        Dim report As CustomReport = gvCustomReports.GetFocusedRow
        If report IsNot Nothing Then
            ShowAddOrEditReportForm(report, False)
            Await LoadDataAsync()
        End If
    End Sub

    Private Async Sub btnDeleteReport_ClickAsync(sender As Object, e As EventArgs) Handles btnDelete.Click
        Dim report As CustomReport = gvCustomReports.GetFocusedRow
        If report IsNot Nothing Then
            If XtraMessageBox.Show($"Are you sure you would like to delete this Report [{report.Name}]?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                db.CustomReports.DeleteOnSubmit(report)
                db.SaveChanges()
                Await LoadDataAsync()
            End If
        End If
    End Sub

    Private Function ShowAddOrEditReportForm(report As CustomReport, isNew As Boolean) As DialogResult
        Dim frm = New frmCustomReportAddOrEdit(report)
        Dim result = frm.ShowDialog
        If result = DialogResult.OK Then
            If isNew Then db.CustomReports.InsertOnSubmit(report)
            If Not db.SaveChanges Then ShowAddOrEditReportForm(report, isNew)
        End If
        Return result
    End Function

    Private Sub gvCustomReports_DoubleClick(sender As Object, e As EventArgs) Handles gvCustomReports.DoubleClick
        Dim view As GridView = CType(sender, GridView)
        Dim pt As Point = view.GridControl.PointToClient(Control.MousePosition)
        Dim info As GridHitInfo = view.CalcHitInfo(pt)
        If (info.InRow OrElse info.InRowCell) AndAlso info.RowHandle >= 0 Then
            Dim row As CustomReport = view.GetRow(info.RowHandle)
            Dim frm = New frmReportBuilder(GetConnectionString(), row.ID, Me.LookAndFeel, False)
            MainForm.ShowForm(frm)
        End If
    End Sub

    Private Sub btnLinkedTables_Click(sender As Object, e As EventArgs) Handles btnLinkedTables.Click
        Try
            Dim report As CustomReport = gvCustomReports.GetFocusedRow
            Dim frm = New frmReportLinkTables(report)
            If frm.ShowDialog = DialogResult.OK Then
                Dim t = LoadDataAsync()
                'LoadReportTables()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error managing report linked tables", ex)
        End Try
    End Sub

    'Report columns functions 

    Private Async Sub gvCustomReports_FocusedRowObjectChangedAsync(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvCustomReports.FocusedRowObjectChanged
        FocusedReport = e.Row
        Await LoadDetailsAsync()
    End Sub

    Private Async Function LoadDetailsAsync() As Task
        If TabbedControlGroup2.SelectedTabPage Is lcgLinkedTables Then
            LoadReportTables()
        ElseIf TabbedControlGroup2.SelectedTabPage Is lcgReportLayouts Then
            Await LoadReportLayout()
        End If
    End Function

    Private Sub LoadReportTables()
        Try
            If FocusedReport IsNot Nothing Then
                gcReportLinkedTables.DataSource = FocusedReport.CustomReportTableLinks.Select(Function(t) t.CustomReportTable).ToList
            Else
                gcReportLinkedTables.DataSource = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading report columns", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Function LoadReportLayout() As Task
        Try
            lcRoot.ShowProgessPanel
            If FocusedReport IsNot Nothing Then
                dbColumnDB = New dbEPDataDataContext(GetConnectionString)
                gcReportLayouts.DataSource = Await Task.Run(Function() dbColumnDB.CustomReportLayouts.Include(Function(x) x.CustomReport).Where(Function(r) r.CustomReportId = FocusedReport.ID))
                gvReportLayouts.BestFitColumns()
            Else
                gcReportLayouts.DataSource = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading report layouts", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Function

    Private Sub gvReportLayouts_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles gvReportLayouts.PopupMenuShowing
        If e.HitInfo.InRow AndAlso e.HitInfo.RowHandle >= 0 Then
            Dim row As CustomReportLayout = gvReportLayouts.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DXMenuItem("Edit", Sub() EditReportLayoutAsync(row), My.Resources.edit_16x16))
            e.Menu.Items.Add(New DXMenuItem("Delete", Sub() DeleteReportLayoutAsync(row), My.Resources.delete_16x16))
        End If
    End Sub

    Private Async Sub DeleteReportLayoutAsync(row As CustomReportLayout)
        If XtraMessageBox.Show($"Are you sure you would like to delete Layout {row.LayoutName}", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If
        dbColumnDB.CustomReportLayouts.DeleteOnSubmit(row)
        dbColumnDB.SaveChanges()
        Await LoadDetailsAsync()
    End Sub

    Private Async Sub EditReportLayoutAsync(row As CustomReportLayout)
        Dim frm = New frmReportLayoutAddOrEdit(row)
        If frm.ShowDialog = DialogResult.OK Then
            dbColumnDB.SaveChanges()
        End If
        Await LoadDetailsAsync()
    End Sub

    Private Sub gvReportLayouts_DoubleClick(sender As Object, e As EventArgs) Handles gvReportLayouts.DoubleClick
        Dim view As GridView = CType(sender, GridView)
        Dim pt As Point = view.GridControl.PointToClient(Control.MousePosition)
        Dim info As GridHitInfo = view.CalcHitInfo(pt)
        If (info.InRow OrElse info.InRowCell) AndAlso info.RowHandle >= 0 Then
            Dim row As CustomReportLayout = view.GetRow(info.RowHandle)
            Dim frm = New frmReportBuilder(GetConnectionString(), row.CustomReport.ID, Me.LookAndFeel, False, row.ID)
            MainForm.ShowForm(frm)
        End If
    End Sub

    Private Async Sub TabbedControlGroup2_SelectedPageChangedAsync(sender As Object, e As DevExpress.XtraLayout.LayoutTabPageChangedEventArgs)
        Await LoadDetailsAsync()
    End Sub
End Class