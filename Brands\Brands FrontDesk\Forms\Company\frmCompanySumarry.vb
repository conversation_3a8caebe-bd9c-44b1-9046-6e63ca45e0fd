﻿Imports System.ComponentModel
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors

Public Class frmCompanySumarry

    Private Property _isPayperless As String
    Private Property _nextPay As List(Of fn_NextScheduledPayrollResult)
    Private Property _payrolls As List(Of PAYROLL)
    'Dim db As dbEPDataDataContext

    Dim Comp As COMPANY
    Public frmTask As TaskCompletionSource(Of Boolean) = New TaskCompletionSource(Of Boolean)
    Private _faxCategories As List(Of FaxCategoryStruct)
    Private _Udf4Value As String
    Private _coOptionsPayroll As CoOptions_Payroll
    Public callId As String
    Private _CompanyActivityLog As CompanyActivityLog
    Private _Timer As Timer = New Timer() With {.Enabled = True, .Interval = 1000 * 3}

    Public ReadOnly _coNum As Decimal
    Public ReadOnly _TimeOpened As DateTime = DateTime.Now.ToEST

    Private Property frmLogger As Serilog.ILogger

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property TabPage As CompanyTabPage
    Public DisplayManualBilling As Boolean = False

    Public ReadOnly Property CoNum As Decimal
        Get
            Return _coNum
        End Get
    End Property

    Public Sub New(coNum As Decimal, Optional tabIndex As CompanyTabPage = CompanyTabPage.CompanyInfo)
        Me.ShowWaitForm()
        frmLogger = Logger.ForContext(Of frmCompanySumarry)()
        _coNum = coNum
        InitializeComponent()
        bbiPaydeckUsers.Visibility = modGlobals.UserInRole("CoPaydeckManageUser").ToBarItemVisibility
        bbiPaydeckUsers.Enabled = modGlobals.UserInRole("CoPaydeckManageUser")
        Text = $"Co# [{CType(coNum, Integer):d}]"

        Me.TabPage = tabIndex
        frmLogger.Debug("Opening {form} with {CoNum}", Me.Name, coNum)
    End Sub

    Private Async Sub frmCompanySumarry_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If Not DesignMode Then
            If Not Me.TabPage = CompanyTabPage.None Then
                Dim tab = Me.TabPage
                Me.TabPage = CompanyTabPage.None
                Await LoadData(False, tab)
            End If
        End If
    End Sub

    Public Async Function LoadData(isRefresh As Boolean, Optional tabPage As CompanyTabPage = CompanyTabPage.CompanyInfo) As Task
        If Not isRefresh AndAlso tabPage = Me.TabPage Then
            Return
        ElseIf Me.TabPage = CompanyTabPage.EmployeeList AndAlso HasUnsavedEmployee() Then
            Return
        ElseIf isRefresh AndAlso tabPage = CompanyTabPage.None Then
            tabPage = CompanyTabPage.CompanyInfo
        End If
        Me.TabPage = tabPage

        frmTask = New TaskCompletionSource(Of Boolean)

        Dim cntrl As Control = Nothing
        Dim bbtn As BarButtonItem = Nothing
        If tabPage <> CompanyTabPage.None Then
            Using db = New dbEPDataDataContext(GetConnectionString)
                Comp = (From c In db.COMPANies Where c.CONUM = _coNum).Single
            End Using
        End If
        Try
            Select Case tabPage
                Case CompanyTabPage.CompanyInfo
                    Dim data = New ucCompanySummary.CompanySummaryData With {.conum = CoNum}
                    Await Task.Run(Sub()
                                       Dim db = New dbEPDataDataContext(GetConnectionString)
                                       Comp = (From c In db.COMPANies Where c.CONUM = _coNum).Single
                                       _nextPay = db.fn_NextScheduledPayroll(Comp.CONUM).ToList
                                       'Dim CO = (From A In db.COOPTIONs Where A.CONUM = _coNum Select A.rpt_style, A.rpt_pdfchks).SingleOrDefault
                                       _isPayperless = db.fn_GetIsPaperless(CoNum)
                                       _payrolls = (From c In db.PAYROLLs Where c.CONUM = _coNum Order By c.PRNUM).ToList
                                       _coOptionsPayroll = (From _cop In db.CoOptions_Payrolls Where _cop.CoNum = _coNum).FirstOrDefault
                                       _faxCategories = db.FaxCategories.ToList
                                       _Udf4Value = (From A In db.COUSERDEFs Where A.conum = CoNum Select A.udf4_data).Single
                                       data.company = Comp
                                       data.empsCount = db.EMPLOYEEs.Where(Function(e) e.CONUM = _coNum AndAlso Not e.TERM_DATE.HasValue).Count
                                       data.nextPay = _nextPay
                                       data.payrolls = _payrolls
                                       data._IsPaperless = _isPayperless
                                       data.tickets = (From A In db.view_CompanyActivityHistories Where A.CoNum = CoNum AndAlso A.Date > Today.AddMonths(-1) Order By A.Date Descending).ToList
                                       data.notes = (From A In Comp.NOTEs.ToList Where (A.expiration_date Is Nothing OrElse A.expiration_date.Value > Date.Today) AndAlso A.category <> "Employee").ToList
                                       data._CoOptions = (From a In db.COOPTIONs Where a.CONUM = _coNum).SingleOrDefault
                                       data._Transit = db.fn_GetCoTransit(CoNum)
                                       data.Services = db.prc_GetCompanyServices(CoNum).ToList()
                                   End Sub)

                    Me.ShowWaitForm
                    Dim ucCompSum = New ucCompanySummary(data)
                    cntrl = ucCompSum
                    bbtn = Me.BarButtonItem1
                Case CompanyTabPage.ReportBrowser
                    Dim ucReportEmailTemplate1 = New ucReportEmailTemplate With {.CoNum = Me.CoNum}
                    If _payrolls IsNot Nothing AndAlso _payrolls.Any() Then ucReportEmailTemplate1.LastCheckDate = (From p In _payrolls Where p.CHECK_DATE.HasValue Select p.CHECK_DATE).LastOrDefault
                    'If _payrolls IsNot Nothing AndAlso _payrolls.Any() Then ucReportEmailTemplate1.LastPrNum = (From p In _payrolls Select p.PRNUM).Max
                    If _payrolls IsNot Nothing AndAlso _payrolls.Any() Then ucReportEmailTemplate1.LastPrNum = (From p In _payrolls Select p.PRNUM).ToList().Max
                    cntrl = ucReportEmailTemplate1
                    bbtn = Me.bbiReportBrowser
                Case CompanyTabPage.ReportLibrary
                    cntrl = New ucReportLibrary With {.Comp = Comp}
                    bbtn = Me.BarButtonItem3
                Case CompanyTabPage.CompanyDocuments
                    cntrl = New ucCompanyDocuments With {.Comp = Comp}
                    CType(cntrl, ucCompanyDocuments).LoadData()
                    bbtn = Me.BarButtonItem6
                Case CompanyTabPage.DeliveryHistory
                    cntrl = New ucDeliveryHistory With {.CoNum = CoNum}
                    bbtn = Me.BarButtonItem7
                Case CompanyTabPage.ShippingAddress
                    cntrl = New ucShippingAddress With {.CoNum = CoNum}
                    bbtn = Me.BarButtonItem8
                Case CompanyTabPage.EmployeeList
                    cntrl = New ucEmployeeList With {.Conum = CoNum}
                    bbtn = Me.BarButtonItem9
                Case CompanyTabPage.RelatedContacts
                    cntrl = New ucCompanyRelatedContacts With {.Conum = CoNum}
                    bbtn = Me.BarButtonItem10
                Case CompanyTabPage.EmailAddresses
                    cntrl = New ucCompanyEmails With {.Conum = CoNum}
                    bbtn = Me.bbiEmailAddresses
                Case CompanyTabPage.BankAccounts
                    cntrl = New ucCompanyBankAccounts With {.Conum = CoNum}
                    bbtn = bbiBankAccounts
                Case CompanyTabPage.Reports
                    cntrl = New ucReports With {.Comp = Comp}
                    bbtn = bbiReports
                Case CompanyTabPage.PaydeckUsers
                    Dim control = New ucPaydeckUsers With {.Company = Comp}
                    control.SetFormMode(ucPaydeckUsers.FormMode.Company)
                    cntrl = control
                    bbtn = bbiPaydeckUsers
                Case CompanyTabPage.OrderSupplies
                    Dim control = New frmOrderSupplies(CoNum)
                    control.Company = Comp
                    control.IsPaperless = _isPayperless
                    Dim NextPayroll As String = ""
                    If {"Paperless", "WebPost"}.Contains(_isPayperless) Then
                        NextPayroll = $"{_isPayperless} Client"
                    Else
                        Dim row = (From n In _nextPay Where n.completed = "NO" Select n.process_date).Min
                        If row.HasValue Then
                            NextPayroll = "Next Payroll Date: " & If(row.HasValue, row.Value.ToShortDateString, "???")
                        End If
                        Dim CurrentPayroll As PAYROLL = _payrolls.LastOrDefault
                        If CurrentPayroll IsNot Nothing Then
                            Dim db As dbEPDataDataContext = New dbEPDataDataContext(GetConnectionString)
                            Dim LastScan = (From A In db.view_Deliveries Where A.CoNum = _coNum AndAlso A.PayrollNum = CurrentPayroll.PRNUM Order By A.ID Descending).FirstOrDefault
                            If LastScan Is Nothing Then
                                NextPayroll &= vbCrLf & $"{vbCrLf}There is an open unshipped payroll #: {CurrentPayroll.PRNUM}. Verify status."
                            End If
                        Else
                            NextPayroll &= vbCrLf & "Not Available, Ship Immediately"
                        End If
                    End If
                    control.NextPayroll = NextPayroll

                    control.TopLevel = False
                    cntrl = control
                    cntrl.Show()
                    'control.Refresh()

                    If DisplayManualBilling Then
                        control.xtpOrdersSupplies.Visible = False
                        control.xtpManualBilling.Visible = True
                        DisplayManualBilling = False
                    End If

                    bbtn = bbiOrderSupplies
                Case Else
                    Exit Function

            End Select
            DataPanel.SuspendLayout()
            DataPanel.Controls.Clear()
            If cntrl IsNot Nothing Then
                DataPanel.Controls.Add(cntrl)
                cntrl.Dock = DockStyle.Fill
            End If
            DataPanel.ResumeLayout()

            Dim itemLinks = New List(Of BarItemLink)
            If MainForm.RibbonControl1.MergedPages.Any() Then
                itemLinks = MainForm.RibbonControl1.MergedPages(0).Groups(Me.RibbonPageGroupCompanyTabs.Name).ItemLinks.ToList
            Else
                itemLinks = rpgCompany.ItemLinks.ToList()
            End If
            If bbtn IsNot Nothing Then
                Dim bItem As BarButtonItem = itemLinks.Where(Function(p As BarButtonItemLink) p.Item.Name = bbtn.Name).Select(Function(p) p.Item).FirstOrDefault
                If bItem IsNot Nothing Then bItem.Down = True
            End If

            Me.lblHeader.Text = $"Company # {Me.CoNum} ({Comp?.CO_NAME}) / {itemLinks.Where(Function(p As BarButtonItemLink) p.Item.Down).FirstOrDefault?.Caption}"

            If _Udf4Value <> "Yes" Then bbiNewPayroll.ItemAppearance.Normal.BackColor = Color.LightPink

            'pmEmailDropDown.AddItem(New DevExpress.Utils.Menu.DXPopupMenu())

            frmTask.TrySetResult(True)

            'show overrides if permissions set
            bbiWrike.Visibility = Permissions.AllowWrike.ToBarItemVisibility()
            AddHandler _Timer.Tick, Sub()
                                        Try
                                            If _CompanyActivityLog IsNot Nothing Then Throw New Exception("Existing CompanyActivityLog record Is Not null")
                                            Dim db = New dbEPDataDataContext(GetConnectionString)
                                            _CompanyActivityLog = New CompanyActivityLog With {.CoNum = CoNum, .OpenDate = DateTime.Now.ToEST, .UserId = UserName, .CallId = callId}
                                            db.CompanyActivityLogs.InsertOnSubmit(_CompanyActivityLog)
                                            db.SubmitChanges()
                                        Catch ex As Exception
                                            frmLogger.Error(ex, "Error While adding a record To CompanyActivityLog {CoNum}", CoNum)
                                        Finally
                                            _Timer.Dispose()
                                        End Try
                                    End Sub
            If Not isRefresh AndAlso _CompanyActivityLog Is Nothing Then _Timer.Start()

        Catch ex As Exception
            frmTask.TrySetException(ex)
            DisplayErrorMessage($"Error loading Company Summary Form For Co#: {_coNum}", ex)
            Close()
        Finally
            Me.CloseWaitForm()
        End Try
    End Function

    Private Async Sub RibbonControl1_ItemClick(sender As Object, e As ItemClickEventArgs) Handles RibbonControl1.ItemClick
        Try
            Await frmTask.Task
            If frmTask.Task.IsFaulted Then
                DisplayMessageBox("Form didn't loaded correctly, Please close it and try again")
                Exit Sub
            End If
            frmLogger.Information("Clicked on {Button}", e.Item.Caption)
            Try
                Dim group = MainForm.RibbonControl1.MergedPages(0).Groups(Me.RibbonPageGroupCompanyTabs.Name)
                If group.ItemLinks.Contains(e.Link) Then
                    'By setting the GroupIndex it automatically unchecks the other
                    Dim page As CompanyTabPage
                    If e.Item.Tag IsNot Nothing AndAlso [Enum].TryParse(Of CompanyTabPage)(e.Item.Tag.ToString, page) Then
                        Await Me.LoadData(False, page)
                    Else
                        DisplayErrorMessage("Don't know what to load", New Exception)
                    End If
                ElseIf e.Item Is bbiCoOptions Then
                    Dim CoFrm As New frmCoOptionsPayroll With {.CoNum = _coNum}
                    CoFrm.ShowDialog()
                    CoFrm.Dispose()
                ElseIf e.Item Is bbiNewPayroll Then
                    MainForm.OpenPowerGridPayroll(New OpenPowerGridPayrollOptions(_coNum))
                ElseIf e.Item Is bbiCalendar Then
                    Dim frm As New frmCalendarNotesAll With {.CoCode = _coNum}
                    frm.ShowDialog()
                    frm.Dispose()
                ElseIf e.Item Is bbiLegacyEmployee Then
                    Dim EmpForm As New frmEmployeeInfoOld With {.CoNum = _coNum}
                    EmpForm.ShowDialog()
                    EmpForm.Dispose()
                ElseIf e.Item Is bbiMinimumWage Then
                    Dim minWageFrm As New frmMinimumWageList With {.CoNum = _coNum}
                    minWageFrm.ShowDialog()
                    minWageFrm.Dispose()
                ElseIf e.Item Is bbiPowerGridEmpNotes Then
                    Dim frm As New frmEmployeeBatchNotesAll With {.CoCode = _coNum}
                    frm.ShowDialog()
                    frm.Dispose()
                ElseIf e.Item Is bbiPayrollStatus Then
                    Dim frm = New frmPayrollStatus(_coNum)
                    Dim results = frm.ShowDialog()
                    If results = DialogResult.Abort Then
                        Await LoadData(True)
                    End If
                    frm.Dispose()
                ElseIf e.Item Is bbiManualChecks Then
                    Dim frm As New frmManualChecks With {.CoNum = _coNum, .CheckDate = Today, .UpdateZendeskTicketId = bbiManualChecks.Tag}
                    Dim results = frm.ShowDialog
                    frm.Dispose()
                ElseIf e.Item Is bbiCallBack Then
                    Dim frm As New frmCallBackRequest
                    frm.Company = Comp
                    frm.ShowDialog()
                    frm.Dispose()
                ElseIf e.Item Is bbiOrderSupplies Then
                    Dim page As CompanyTabPage
                    If e.Item.Tag IsNot Nothing AndAlso [Enum].TryParse(Of CompanyTabPage)(e.Item.Tag.ToString, page) Then
                        Await Me.LoadData(False, page)
                    End If
                ElseIf e.Item Is bbiEditNotes Then
                    Dim frm = New frmNotes(Comp.CONUM)
                    frm.ShowDialog()
                ElseIf e.Item Is bbiQuarterlies Then
                    AddOrActivateQuarterlyCentral()
                ElseIf e.Item Is bbiRunUtilityImpot Then
                    Dim frm = New frmRunUtilityImport(CoNum)
                    frm.ShowDialog()
                ElseIf e.Item Is bbiEmail Then
                    EmailClient()
                ElseIf e.Item Is bbiGetDDDrafts Then
                    Dim frm = New frmGetDDDrafts(Comp)
                    MainForm.ShowForm(frm)
                ElseIf e.Item.Tag IsNot Nothing AndAlso TryCast(e.Item.Tag, ReportEmailTeplate) IsNot Nothing Then
                    ProcessQuickDropDownReportAsync(e.Item.Tag)
                ElseIf e.Item.Tag IsNot Nothing AndAlso TryCast(e.Item.Tag, FaxCategory) IsNot Nothing Then
                    SendEmailFromCS(e.Item.Tag)
                ElseIf e.Item Is bbiRefersh Then
                    Dim t = LoadData(True, Me.TabPage)
                ElseIf e.Item Is bbiNoMorrePayroll Then
                    Await modCompanyUtils.NoMorePayroll(CoNum)
                ElseIf e.Item Is bbiAddManualCheck Then
                    Using db = New dbEPDataDataContext(GetConnectionString)
                        Dim coopt = (From c In db.COOPTIONs Where c.CONUM = _coNum).Single
                        If coopt.OBC_ACCOUNTKEY <> 0 Then
                            XtraMessageBox.Show("You are attempting to add a manual check for an OBC client, this is not allowed", "OBC Client", MessageBoxButtons.OK, MessageBoxIcon.Error)
                            Return
                        End If
                    End Using
                    MainForm.ShowForm(New frmCheckFigures(_coNum))
                ElseIf e.Item Is bbiQtrEndHold Then
                    Dim frm = New frmUpdateQtrEndHold(CoNum)
                    frm.ShowDialog()
                ElseIf e.Item Is bbiWrikeAddTask Then
                    Dim frm = New frmWrikeAddOrEdit(CoNum)
                    frm.ShowDialog()
                ElseIf e.Item Is bbiWrikeLoadTasks Then
                    Dim list = MainForm.GetForms(Of frmWrike)
                    If list.Any Then
                        If XtraMessageBox.Show($"Wrike form is already oppened once.{vbCrLf}Would you like to open a new tab?", "Wrike Form", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                            Dim wFrm = New frmWrike(CoNum)
                            MainForm.ShowForm(wFrm)
                        Else
                            list(0).slueCoNum.EditValue = CoNum
                            MainForm.ActivateForm(list(0))
                        End If
                    Else
                        Dim wFrm = New frmWrike(CoNum)
                        MainForm.ShowForm(wFrm)
                    End If
                ElseIf e.Item Is bbiActivityLog Then
                    Dim frm = New frmCompanyActivityLog(CoNum)
                    frm.ShowDialog()
                End If
            Catch ex As Exception
                DisplayErrorMessage("Error in ribbon item click", ex)
            End Try
        Catch ex As Exception
            DisplayErrorMessage("Error while clicking on button {0}".FormatWith(e.Item.Caption), ex)
        End Try
    End Sub


    Private Sub AddOrActivateQuarterlyCentral()
        Dim openForms = MainForm.GetForms(Of frmQuarterlyCentral)()
        For Each frm In openForms
            If frm.CoNum = Comp.CONUM Then
                MainForm.ActivateForm(frm)
                Exit Sub
            End If
        Next
        Dim frm1 As New frmQuarterlyCentral(Comp.CONUM)
        'frm.Left = Me.CO_NAMETextBox.Left + Me.CO_NAMETextBox.Width
        MainForm.ShowForm(frm1)
    End Sub

    Private Sub CloseQuarterlyCentralForm()
        Try
            Dim openForms = MainForm.GetForms(Of frmQuarterlyCentral)()
            For Each frm In openForms
                If frm.CoNum = Comp.CONUM Then
                    frm.Close()
                    Exit Sub
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage($"Error closing frmQuarterlyCentral form. Conum {CoNum}", ex)
        End Try
    End Sub

    Private Sub SendEmailFromCS(cat As FaxCategory)
        Dim frm As frmEmailComp = New frmEmailComp(Comp.CONUM, False, False)
        If frm.ShowDialog() = DialogResult.OK Then
            Dim properties = EmailHelpers.ComposeEmailWithTicket(Comp.CONUM, cat.Category)
            properties.HiddenEmail.AddRange(frm.GetAllEmailAddresses)
            properties.ToEmail.AddRange(frm.GetSelectedEmailAddress().Select(Function(em) em.Email))
            properties.Show()
        End If
    End Sub

    Private Async Sub ProcessQuickDropDownReportAsync(report As ReportEmailTeplate)
        Dim UcReportEmailTemplate1 As ucReportEmailTemplate
        If Me.TabPage = CompanyTabPage.ReportBrowser Then
            UcReportEmailTemplate1 = Me.DataPanel.Controls(0)
        Else
            UcReportEmailTemplate1 = New ucReportEmailTemplate With {.CoNum = Me.CoNum}
        End If

        If report.Name = "Direct Deposit Authorization" Then
            Dim hasDd = Query(Of String)("Select IIF(TRAN_BANK_DD NOT IN ('*********','NATPAYMNT'), 'No', 'Yes')  FROM Company Where CONUM = @conum", New With {CoNum}).Single
            If hasDd = "No" Then
                Dim frm As New frmClientNotSetupDDDialog(CoNum)
                frm.ShowDialog()
                Return
            End If
        End If

        Try
            If Me.TabPage <> CompanyTabPage.ReportLibrary Then
                ProgressPanel1.Show()
            Else
                UcReportEmailTemplate1.lcRoot.ShowProgessPanel()
            End If
            Application.DoEvents()
            Dim processor = New ReportProcessor(Comp, report, FileType.Pdf) With {.showInRecentReports = True}
            processor.LastPrNum = UcReportEmailTemplate1.LastPrNum
            processor.LastCheckDate = UcReportEmailTemplate1.LastCheckDate
            Dim result = processor.ProcessReport()
            If result.EmailTemplate.ReportType = "Eml" Then
                Dim sender = New ReportSender(result)
                sender.EmailReport()
            ElseIf result.EmailTemplate.ReportType = "Zendesk" Then
                Await CanCreateTicketAsync()
                Await modZendeskIntegrationClient.CreateTicketAsync("", Comp, report, modReports.EmailSubject, New List(Of String)())
            ElseIf Not (result.Cancalled OrElse result.AllFileExist) Then
                DisplayMessageBox("No records returned for the parameters values entered")
            ElseIf Not result.Cancalled Then
                Dim sender = New ReportSender(result)
                Await sender.CreateTicketAsync()
                ' modReports.EmailReport(Comp, CType(report, ReportEmailTeplate), result.Path, True, result.Row)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error processing report: {0}".FormatWith(report.Name), ex)
        Finally
            If Me.TabPage <> CompanyTabPage.ReportBrowser Then
                ProgressPanel1.Hide()
                UcReportEmailTemplate1.Dispose()
            Else
                UcReportEmailTemplate1.lcRoot.HideProgressPanel()
            End If
        End Try
    End Sub


    Public Function ShouldShowFaxPanel() As Boolean
        Return Me.TabPage = CompanyTabPage.CompanyInfo OrElse Me.TabPage = CompanyTabPage.ShippingAddress
    End Function

    Private Sub EmailClient()
        Dim frm = New frmEmailComp(Comp.CONUM, True, False)
        frm.ShowZendeskButton()
        frm.ShowDialog()
    End Sub

    Private Sub ProgressPanel1_VisibleChanged(sender As Object, e As EventArgs) Handles ProgressPanel1.VisibleChanged
        If ProgressPanel1.Visible Then
            ProgressPanel1.BringToFront()
            ProgressPanel1.Location = New Point((Width - ProgressPanel1.Width) / 2, (Height - ProgressPanel1.Height) / 2)
            Application.DoEvents()
        End If
    End Sub

    Public Async Function EditEmployeeAsync(empNum As Integer) As Task
        Await Me.LoadData(False, CompanyTabPage.EmployeeList)
        'Me.TabPage = CompanyTabPage.EmployeeList
        CType(Me.DataPanel.Controls(0), ucEmployeeList).SelectEmployee(empNum)

        'Dim EmpForm As New frmEmployeeInfo() With {.CoNum = _coNum, .EmployeeID = empNum}
        'EmpForm.ShowDialog()
        'EmpForm.Dispose()
    End Function

    Private Sub frmCompanySumarry_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        Try
            If _Timer IsNot Nothing Then _Timer.Dispose()
            CloseQuarterlyCentralForm()
            If _CompanyActivityLog IsNot Nothing Then
                Dim db As dbEPDataDataContext
                db = New dbEPDataDataContext(GetConnectionString)
                Dim log = db.CompanyActivityLogs.SingleOrDefault(Function(c) c.ID = _CompanyActivityLog.ID)
                If log IsNot Nothing Then
                    log.CloseDate = DateTime.Now.ToEST
                    db.SaveChanges
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error closing company form", ex)
        End Try
    End Sub

    Private Sub pmEmailDropDown_BeforePopup(sender As Object, e As CancelEventArgs) Handles pmEmailDropDown.BeforePopup
        Try
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim _quickAccessReports As List(Of ReportEmailTeplate) = (From r In db.ReportEmailTeplates Where r.ShowInQucikDropDown).ToList
            pmEmailDropDown.ClearLinks()
            pmEmailDropDown.AddItems(_quickAccessReports.Select(Function(r) New BarButtonItem(RibbonControl1.Manager, r.Name) With {.Tag = r}).ToArray())
            Dim fromCsEmail = New BarButtonItem(RibbonControl1.Manager, "Send From CS Email") With {.ButtonStyle = BarButtonStyle.DropDown}
            Dim pm = New PopupMenu(RibbonControl1.Manager)
            pm.AddItems(_faxCategories.Where(Function(fc) fc.ShowInCallBackFrm.HasValue AndAlso fc.ShowInCallBackFrm.Value) _
                    .Select(Function(fc) New BarButtonItem(RibbonControl1.Manager, fc.Category) With {.Tag = fc}).ToArray)
            fromCsEmail.DropDownControl = pm
            pmEmailDropDown.AddItem(fromCsEmail).BeginGroup = True
        Catch ex As Exception
            DisplayErrorMessage("Error loading reports", ex)
        End Try
    End Sub

    Private Sub frmCompanySumarry_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            e.Cancel = HasUnsavedEmployee()
        Catch ex As Exception
            frmLogger.Error(ex, "Error in closing form")
            DisplayErrorMessage("Error while closing form", ex)
        End Try
    End Sub

    Function HasUnsavedEmployee() As Boolean
        If Me.TabPage = CompanyTabPage.EmployeeList AndAlso Me.DataPanel.Controls.Count > 0 AndAlso TypeOf Me.DataPanel.Controls(0) Is ucEmployeeList Then
            If CType(Me.DataPanel.Controls(0), ucEmployeeList).UcEmployeeInfo1.CheckPendingChanges() = DialogResult.Cancel Then
                Return True
            End If
        End If
        Return False
    End Function

    Public Enum CompanyTabPage
        None
        CompanyInfo
        ReportBrowser
        ReportLibrary
        CompanyDocuments
        DeliveryHistory
        ShippingAddress
        EmployeeList
        RelatedContacts
        EmailAddresses
        BankAccounts
        Reports
        PaydeckUsers
        OrderSupplies
    End Enum

    Private Sub frmCompanySumarry_Activated(sender As Object, e As EventArgs) Handles MyBase.Activated
        If MainForm.RibbonControl1.MergedPages?.Count > 0 Then MainForm.RibbonControl1.SelectedPage = MainForm.RibbonControl1.MergedPages(0)
    End Sub

    Private Sub frmCompanySumarry_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim IsOnNSF = db.ExecuteQuery(Of Boolean)($"SELECT custom.fn_IsOnNsf({CoNum})").FirstOrDefault()
                Dim IsOnBillingHold = nz(db.COOPTIONs.Where(Function(c) c.CONUM = CoNum).FirstOrDefault().QYEND_HOLD_TYPE?.ToString(), "").ToString().ToLower().Contains("billing")

                Dim issue As frmAcctOnHoldPopup.Issue = If(IsOnNSF AndAlso IsOnBillingHold, frmAcctOnHoldPopup.Issue.NsfAndOpenInvoice, If(IsOnNSF, frmAcctOnHoldPopup.Issue.Nsf, If(IsOnBillingHold, frmAcctOnHoldPopup.Issue.OpenInvoice, frmAcctOnHoldPopup.Issue.None)))

                If issue <> frmAcctOnHoldPopup.Issue.None Then
                    PictureEditAccountOnHold.Visible = True
                    Dim frm = New frmAcctOnHoldPopup(issue)
                    frm.ShowDialog()
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error in frmCompanySumarry_Shown", ex)
        End Try
    End Sub

    Private Sub PictureEditAccountOnHold_Click(sender As Object, e As EventArgs) Handles PictureEditAccountOnHold.Click
        Try
            Dim IsOnNSF = Query(Of Boolean)($"SELECT custom.fn_IsOnNsf({CoNum})").FirstOrDefault()
            Dim IsOnBillingHold = nz(Query(Of Boolean)($"SELECT BillingHold = CONVERT(BIT, 1) FROM COOPTIONS c WHERE c.CONUM = {CoNum} AND c.QYEND_HOLD_TYPE LIKE '%billing%'").FirstOrDefault(), False)
            Dim issue As frmAcctOnHoldPopup.Issue = If(IsOnNSF AndAlso IsOnBillingHold, frmAcctOnHoldPopup.Issue.NsfAndOpenInvoice, If(IsOnNSF, frmAcctOnHoldPopup.Issue.Nsf, If(IsOnBillingHold, frmAcctOnHoldPopup.Issue.OpenInvoice, frmAcctOnHoldPopup.Issue.None)))
            Dim frm = New frmAcctOnHoldPopup(issue)
            frm.ShowDialog()
        Catch ex As Exception
            DisplayErrorMessage("Error in PictureEditAccountOnHold_Click", ex)
        End Try

    End Sub
End Class