﻿Imports System.ComponentModel
Imports DevExpress.XtraEditors
Imports Microsoft.EntityFrameworkCore

Public Class frmMinimumWageList
    Sub New()
        InitializeComponent()
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeeRateEnt As employee_rate
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoNum As Decimal
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PayrollEndDate As Date = Nothing
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsPayroll As Boolean
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsNY As Boolean

    Private Sub frmMinimumWageList_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Label1.Text = "Minimum Wage Changes For Co #: " & Me.CoNum
        LoadData()
        TextBox1.Text = GetUdfValue("Minimum Wage Form How This Works Notes")
        gvEmployeesBelowMinimumWage.ChangeCopyBehavior
        gvEmployees.ChangeCopyBehavior
    End Sub

    Private Sub LoadData()
        Try
            Dim DB As New dbEPDataDataContext(GetConnectionString)

            Dim CoOptions = (From A In DB.CoOptions_Payrolls Where A.CoNum = CoNum Select A.Industry, A.UCI_County).SingleOrDefault
            Dim CoAddress = (From A In DB.COMPANies Where A.CONUM = CoNum Select A.CO_COUNTY, A.CO_STATE).SingleOrDefault
            Dim StateIDs = (From A In DB.STATE_IDs Where A.CONUM = CoNum Order By A.ST_ABBRV Select A.ST_ABBRV).Distinct.ToArray
            Dim empCount = (From A In DB.EMPLOYEEs Where A.CONUM = CoNum AndAlso A.TERM_DATE Is Nothing).Count
            Me.cbeState.Properties.Items.AddRange(StateIDs)
            Me.cbeState.Properties.Items.Insert(0, "")
            IsNY = StateIDs.Contains("NY")

            Me.GroupMinWageCategory.Expanded = IsNY

            If CoOptions IsNot Nothing AndAlso Not String.IsNullOrEmpty(CoOptions.UCI_County) Then
                Me.txtCounty.EditValue = CoOptions.UCI_County
            Else
                Me.txtCounty.EditValue = CoAddress.CO_COUNTY
            End If

            Me.txtIndustry.Properties.Items.Clear()
            Me.txtIndustry.Properties.Items.AddRange(GetUdfValueSplitted("MinimumWageIndustriesList"))

            If CoOptions IsNot Nothing AndAlso Not String.IsNullOrEmpty(CoOptions.Industry) Then
                Dim Indestry = CoOptions.Industry
                If Me.txtIndustry.Properties.Items.Contains(Indestry) Then
                    Me.txtIndustry.EditValue = CoOptions.Industry
                Else
                    Me.txtIndustry.EditValue = "Other"
                    Me.txtOtherIndustry.EditValue = CoOptions.Industry
                End If
                Me.txtOtherIndustry.Properties.ReadOnly = Me.txtIndustry.EditValue <> "Other"
            End If

            Me.txtEmployeeCount.EditValue = empCount.ToString("N0")

            Dim Data = DB.prc_MinimumWageRequirements(CoNum, "None", "NO").ToList
            For Each i In Data
                i.UpdateTypeOriginal = i.UpdateType
            Next

            Me.prc_MinimumWageRequirementsBindingSource.DataSource = Data

            Dim dt = Query("SELECT * FROM custom.MinimumWage mw OUTER APPLY( SELECT min(b.EffectiveDate) EffectiveEndDate FROM custom.MinimumWage b WHERE b.State = mw.State AND b.EffectiveDate > mw.EffectiveDate ) c WHERE mw.State IN" &
        " (SELECT sit.ST_ABBRV FROM STATE_IDS sit WHERE sit.CONUM = " & CoNum & " GROUP BY sit.CONUM,sit.ST_ABBRV )")
            GridControl2.DataSource = dt
            GridControl2.ForceInitialize()
            GridView2.Columns.ColumnByFieldName("ID").Visible = False
            gvEmployeesBelowMinimumWage.FocusedColumn = colEMPNUM
            gvEmployeesBelowMinimumWage.BestFitColumns()
            IsIndustrySetup()
        Catch ex As Exception
            DisplayErrorMessage("Error loading minimum wage data.", ex)
        End Try
    End Sub

    Private Sub IsIndustrySetup()
        If IsNY AndAlso txtIndustry.Text.IsNullOrWhiteSpace Then
            lcgEmployeesBelowMinimumWage.Enabled = False
            lcgEmployeesBelowMinimumWage.Text = "Employees Below Minimum Wage List -<size=+2><b><color=red> County Industry and Employee Terminations Must Be Completed and Confirmed Before Getting Your Final Minimum Rates</color></b></size>"
        Else
            lcgEmployeesBelowMinimumWage.Enabled = True
            lcgEmployeesBelowMinimumWage.Text = "Employees Below Minimum Wage List"
        End If
    End Sub

    Private Sub frmMinimumWageList_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
        ChangeIfPayroll()
        GridView2.ActiveFilterString = "[EffectiveDate] <= #{0:MM/dd/yyyy}# And ([EffectiveEndDate] Is Null Or [EffectiveEndDate] >= #{0:MM/dd/yyyy}#)".FormatWith(Today.Date)
    End Sub
    Private Sub ChangeIfPayroll()
        Dim MinWageList As List(Of prc_MinimumWageRequirementsResult) = prc_MinimumWageRequirementsBindingSource.DataSource
        Dim hasIssues = MinWageList.Any(Function(m) m.effective_date.HasValue AndAlso m.effective_date <= PayrollEndDate)
        lciIsPayrollMessage.Visibility = hasIssues.ToBarItemVisibility

        If IsPayroll AndAlso hasIssues Then
            lciApplyAnyRateChangesNow.Visibility = True.ToBarItemVisibility
            lciScheduleRateChanges.Visibility = False.ToBarItemVisibility
        Else
            lciScheduleRateChanges.Visibility = True.ToBarItemVisibility
            lciApplyAnyRateChangesNow.Visibility = False.ToBarItemVisibility
        End If

        If IsPayroll = True Then
            For i As Integer = 0 To gvEmployeesBelowMinimumWage.RowCount - 1
                Dim newMinWageDate As Date = gvEmployeesBelowMinimumWage.GetRowCellValue(i, "NewMinWageDate")
                Dim EffectiveDate As Date = gvEmployeesBelowMinimumWage.GetRowCellValue(i, "effective_date")
                If PayrollEndDate >= newMinWageDate Then
                    If IsDBNull(EffectiveDate) OrElse EffectiveDate <> Today Then
                        gvEmployeesBelowMinimumWage.SetRowCellValue(i, "NewMinWageDate", Today)
                    Else
                        gvEmployeesBelowMinimumWage.SetRowCellValue(i, "NewMinWageDate", Today.AddDays(-1))
                    End If
                End If
            Next
        End If
    End Sub

    Private Sub btnScheduleRateChanges_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScheduleRateChanges.Click
        Dim DB As New dbEPDataDataContext(GetConnectionString)
        Dim updateMinimumWageList = DirectCast(Me.prc_MinimumWageRequirementsBindingSource.List, List(Of prc_MinimumWageRequirementsResult))

        For Each minWageRow As prc_MinimumWageRequirementsResult In updateMinimumWageList
            If minWageRow.UpdateType = "NewRate1" OrElse minWageRow.UpdateType = "NewTipRate" OrElse minWageRow.UpdateType = "NewSalary" Then
                If minWageRow.NewMinWageDate > Today Then
                    EmployeeRateEnt = New employee_rate With {.conum = CoNum,
                                            .empnum = minWageRow.EMPNUM,
                                            .effective_date = minWageRow.NewMinWageDate,
                                            .rate = minWageRow.Type,
                                            .history = "NO"}
                    If minWageRow.Type = "Salary" Then
                        EmployeeRateEnt.amount = minWageRow.NewSalary
                    ElseIf minWageRow.Type = "Rate 1" Then
                        If minWageRow.UpdateType = "NewRate1" Then
                            EmployeeRateEnt.amount = minWageRow.NewRate1
                        ElseIf minWageRow.UpdateType = "NewTipRate" Then
                            EmployeeRateEnt.amount = minWageRow.NewMinTipRate
                        End If
                    End If
                    DB.employee_rates.InsertOnSubmit(EmployeeRateEnt)
                Else
                    Dim emp = (From A In DB.EMPLOYEEs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = minWageRow.EMPNUM Select A).SingleOrDefault()
                    emp = UpdateEmployeeRate(minWageRow, emp)
                End If
            End If
        Next
        DB.SubmitChanges()
        DB.Dispose()
        LoadData()
        ChangeIfPayroll()
    End Sub

    Private Sub btnApplyScheduledRateNow_Click(sender As Object, e As EventArgs) Handles btnApplyScheduledRateNow.Click
        Try
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim updateMinimumWageList = DirectCast(Me.prc_MinimumWageRequirementsBindingSource.List, List(Of prc_MinimumWageRequirementsResult))
                For Each minWageRow As prc_MinimumWageRequirementsResult In updateMinimumWageList
                    If minWageRow.UpdateType = "Scheduled" AndAlso PayrollEndDate >= minWageRow.NewMinWageDate Then
                        Dim scheduledEmployeeRates = db.employee_rates.Where(Function(er) er.conum = minWageRow.CoNum _
                                                                                 AndAlso er.empnum = minWageRow.EMPNUM _
                                                                                 AndAlso er.history = "NO" _
                                                                                 AndAlso Not er.updated_date.HasValue _
                                                                                 AndAlso er.effective_date = minWageRow.effective_date _
                                                                                 AndAlso er.rate = minWageRow.Type)
                        db.employee_rates.DeleteAllOnSubmit(scheduledEmployeeRates)
                        Dim emp = (From A In db.EMPLOYEEs Where A.CONUM = Me.CoNum AndAlso A.EMPNUM = minWageRow.EMPNUM Select A).SingleOrDefault()
                        UpdateEmployeeRate(minWageRow, emp)
                    End If
                Next
                db.SaveChanges()
                btnScheduleRateChanges_Click(Nothing, Nothing)
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error Applying the scheduled rate changes.", ex)
        End Try
    End Sub

    Private Shared Function UpdateEmployeeRate(minWageRow As prc_MinimumWageRequirementsResult, emp As EMPLOYEE) As EMPLOYEE
        If minWageRow.Type = "Salary" Then
            emp.SALARY_AMT = minWageRow.NewSalary
            emp.LAST_SALARY_RAISE = minWageRow.NewMinWageDate
        End If
        If minWageRow.Type = "Rate 1" Then
            If minWageRow.UpdateType = "NewRate1" Then
                emp.RATE_1 = minWageRow.NewRate1
            ElseIf minWageRow.UpdateType = "NewTipRate" Then
                emp.RATE_1 = minWageRow.NewMinTipRate
            End If
            emp.LAST_RAISE_1 = minWageRow.NewMinWageDate
        End If
        Return emp
    End Function

    Private Sub btnCheckAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUnselectAll.Click
        Dim booleanValue As Boolean
        If btnUnselectAll.Text = "Select All" Then
            booleanValue = True
            btnUnselectAll.Text = "Unselect All"
        Else
            booleanValue = False
            btnUnselectAll.Text = "Select All"
        End If

        For i As Integer = 0 To gvEmployeesBelowMinimumWage.RowCount - 1
            gvEmployeesBelowMinimumWage.SetRowCellValue(i, "IsChecked", booleanValue)
        Next
    End Sub

    Private Sub GridView2_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs) Handles GridView2.RowCellStyle
        Dim ed As DateTime = GridView2.GetRowCellValue(e.RowHandle, "EffectiveDate")
        Dim dt = GridView2.GetRowCellValue(e.RowHandle, "EffectiveEndDate")
        Dim eed As DateTime? = IIf(IsDBNull(dt), Nothing, dt)

        If eed.HasValue AndAlso eed.Value < Today Then
            e.Appearance.BackColor = Color.Red
            e.Appearance.ForeColor = Color.Black
        ElseIf ed > Today Then
            e.Appearance.BackColor = Color.Yellow
            e.Appearance.ForeColor = Color.Black
        Else
            e.Appearance.BackColor = Color.LightGreen
            e.Appearance.ForeColor = Color.Black
        End If

    End Sub

    Private Sub btnChangeCounty_Click(sender As Object, e As EventArgs) Handles btnChangeCounty.Click
        Me.pnlChangeCounty.Visible = True
    End Sub

    Private Sub txtState_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cbeState.SelectedIndexChanged
        Dim State As String = Me.cbeState.EditValue
        If String.IsNullOrEmpty(State) Then
            Me.txtChangeCounty.Properties.Items.Clear()
            Me.txtChangeCounty.EditValue = Nothing
        Else
            Using db As New dbEPDataDataContext(GetConnectionString)
                Dim Counties = (From A In db.ZIPS Where A.STATE = State Order By A.COUNTY Select A.COUNTY).Distinct.ToArray
                Me.txtChangeCounty.Properties.Items.AddRange(Counties)
            End Using
        End If
    End Sub

    Private Sub btnUpdateCounty_Click(sender As Object, e As EventArgs) Handles btnUpdateCounty.Click
        If String.IsNullOrEmpty(Me.txtChangeCounty.EditValue) Then
            DisplayMessageBox("Please select a county")
            Exit Sub
        End If
        Using db As New dbEPDataDataContext(GetConnectionString)
            Dim Options = (From A In db.CoOptions_Payrolls Where A.CoNum = CoNum).SingleOrDefault
            If Options Is Nothing Then
                Options = New CoOptions_Payroll With {.CoNum = Me.CoNum}
                db.CoOptions_Payrolls.InsertOnSubmit(Options)
            End If
            Options.UCI_County = Me.txtChangeCounty.EditValue
            If Not db.SaveChanges() Then
                DisplayErrorMessage("Unable to save county changes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
            Me.txtCounty.EditValue = Me.txtChangeCounty.EditValue
            Me.pnlChangeCounty.Visible = False
        End Using
        LoadData()
    End Sub

    Private Sub txtIndustry_SelectedIndexChanged(sender As Object, e As EventArgs) Handles txtIndustry.SelectedIndexChanged
        Dim industry As String = Me.txtIndustry.EditValue
        Me.txtOtherIndustry.Properties.ReadOnly = Not industry = "Other"
    End Sub

    Private Sub btnUpdateIndustry_Click(sender As Object, e As EventArgs) Handles btnUpdateIndustry.Click
        Using db As New dbEPDataDataContext(GetConnectionString)
            Dim Options = (From A In db.CoOptions_Payrolls Where A.CoNum = CoNum).SingleOrDefault
            If Options Is Nothing Then
                Options = New CoOptions_Payroll With {.CoNum = Me.CoNum}
                db.CoOptions_Payrolls.InsertOnSubmit(Options)
            End If
            Dim industry As String = Me.txtIndustry.EditValue
            If industry = "Other" Then industry = If(Me.txtOtherIndustry.Text.IsNullOrWhiteSpace, "Other", Me.txtOtherIndustry.Text)
            Options.Industry = industry
            db.SubmitChanges()
        End Using
        IsIndustrySetup()
        LoadData()
    End Sub


    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property db As dbEPDataDataContext
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property ActiveEmpsNotPaid As Dictionary(Of Decimal, prc_RptActiveEmpsNotPaidResult)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property EmployeeList As List(Of EMPLOYEE)

    Private Sub btnShowTermdate_Click(sender As Object, e As EventArgs) Handles btnShowTermdate.Click
        db = New dbEPDataDataContext(GetConnectionString)

        riLueDivs.DataSource = db.DIVISIONs.Where(Function(d) d.CONUM = CoNum).ToList()
        riLueDivs.ValueMember = "DDIVNUM"
        riLueDivs.DisplayMember = "DivNumName"

        riLueDeps.DataSource = db.DEPARTMENTs.Where(Function(d) d.CONUM = CoNum).ToList()
        riLueDeps.ValueMember = "DEPTNUM"
        riLueDeps.DisplayMember = "DeptNumDesc"

        EmployeeList = db.EMPLOYEEs.Where(Function(c) c.CONUM = CoNum).ToList
        eMPLOYEEsBindingSource.DataSource = EmployeeList
        'Dim result As Data.Linq.ISingleResult(Of prc_RptActiveEmpsNotPaidResult) = db.prc_RptActiveEmpsNotPaid(CoNum)
        'Dim list = result.ToList
        Dim list = db.Set(Of prc_RptActiveEmpsNotPaidResult)().FromSqlInterpolated($"EXEC prc_RptActiveEmpsNotPaid {CoNum}").ToList()
        ActiveEmpsNotPaid = list.ToDictionary(Function(a) a.EMPNUM)
        gvEmployees.ActiveFilterString = "[TERM_DATE] = ? AND [InactiveFor] > 0"
        gvEmployees.BestFitColumns()
        XtraTabPage2.PageVisible = True
        XtraTabControl1.SelectedTabPageIndex = 1
        rgEmployeeGridMode_SelectedIndexChanged(Nothing, Nothing)
    End Sub

    Private Sub gvEmployees_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs) Handles gvEmployees.CustomUnboundColumnData
        Try
            Dim row As EMPLOYEE = e.Row
            Dim ee_dfs = db.EE_UDFS.Where(Function(u) u.CONUM = row.CONUM AndAlso u.EMPNUM = row.EMPNUM).ToArray
            If e.Column Is colCounty Then
                If e.IsGetData Then
                    Dim eeudf = ee_dfs.SingleOrDefault(Function(u) u.UDF_DESCR = "UCI_County")?.UDF_STRING
                    e.Value = eeudf
                ElseIf e.IsSetData Then
                    If e.Value Is Nothing Then Exit Sub
                    Dim existingRow = ee_dfs.SingleOrDefault(Function(u) u.UDF_DESCR = "UCI_County")
                    If existingRow IsNot Nothing Then
                        existingRow.UDF_STRING = e.Value
                    Else
                        row.EE_UDFs.Add(New EE_UDF With {.UDF_DESCR = "UCI_County", .UDF_STRING = e.Value})
                    End If
                    db.SaveChanges()
                End If
            ElseIf e.IsGetData Then
                If e.Column Is colNote1 Then
                    e.Value = GetActiveEmpNotPaid(row.EMPNUM).Note
                ElseIf e.Column Is colNote2 Then
                    e.Value = GetActiveEmpNotPaid(row.EMPNUM).Note2
                ElseIf e.Column Is colInactiveFor Then
                    e.Value = GetActiveEmpNotPaid(row.EMPNUM).InactiveFor
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error updating employee county", ex)
        End Try
    End Sub

    Private Function GetActiveEmpNotPaid(empNum As Decimal) As prc_RptActiveEmpsNotPaidResult
        Dim result As prc_RptActiveEmpsNotPaidResult = New prc_RptActiveEmpsNotPaidResult
        ActiveEmpsNotPaid.TryGetValue(empNum, result)
        Return If(result, New prc_RptActiveEmpsNotPaidResult)
    End Function

    Private Sub gvEmployees_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles gvEmployees.CustomRowCellEditForEditing
        If e.Column Is colCounty Then
            Dim row As EMPLOYEE = gvEmployees.GetRow(e.RowHandle)
            RepositoryItemComboBox1.Items.Clear()
            Dim Counties = (From A In db.ZIPS Where A.STATE = row.UCI_STATE Order By A.COUNTY Select A.COUNTY).Distinct.ToArray
            RepositoryItemComboBox1.Items.AddRange(Counties)
            e.RepositoryItem = RepositoryItemComboBox1
        End If
    End Sub

    Private Sub RepositoryItemComboBox1_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles RepositoryItemComboBox1.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Delete Then
            Dim row As EMPLOYEE = gvEmployees.GetFocusedRow
            Dim existingRow = row.EE_UDFs.SingleOrDefault(Function(u) u.UDF_DESCR = "UCI_County")
            If existingRow IsNot Nothing Then
                If XtraMessageBox.Show("Are you sure you would like to Delete this information?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    db.EE_UDFS.DeleteOnSubmit(existingRow)
                    db.SaveChanges
                    Dim editor As DevExpress.XtraEditors.ComboBoxEdit = sender
                    editor.EditValue = Nothing
                End If
            End If
        End If
    End Sub

    Private Sub gvEmployees_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles gvEmployees.RowUpdated
        Try
            db.SaveChanges
        Catch ex As Exception
            DisplayErrorMessage("Error saving employee changes", ex)
        End Try
    End Sub

    Private Sub RiDeEmpTermDate_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles RiDeEmpTermDate.Validating
        gvEmployees.PostEditor()
        db.SaveChanges
    End Sub

    Private Sub rgEmployeeGridMode_SelectedIndexChanged(sender As Object, e As EventArgs) Handles rgEmployeeGridMode.SelectedIndexChanged
        Dim index = rgEmployeeGridMode.SelectedIndex
        colCounty.Visible = index = 1
        colNote1.Visible = index = 0
        colNote2.Visible = index = 0
        colInactiveFor.Visible = index = 0
        colTERM_DATE.Visible = index = 0
        lcgBulkUpdateEmp.Visibility = (index = 0).ToBarItemVisibility
        If index = 0 Then
            gvEmployees.ActiveFilterString = "[TERM_DATE] = ? AND [InactiveFor] > 0"
        Else
            gvEmployees.ActiveFilterString = "[TERM_DATE] = ?"
        End If
    End Sub

    Private Sub btnBulkTerminate_Click(sender As Object, e As EventArgs) Handles btnBulkTerminate.Click
        Dim empsToUpdate = EmployeeList.Where(Function(emp) Not emp.TERM_DATE.HasValue AndAlso GetActiveEmpNotPaid(emp.EMPNUM)?.InactiveFor.GetValueOrDefault >= seInactiveForPays.Value)
        If XtraMessageBox.Show($"Are you sure you would like to update {empsToUpdate.Count} Employees.", "Terminate", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            For Each emp In empsToUpdate
                Dim termDate = If(emp.LAST_PAID_DT.HasValue, emp.LAST_PAID_DT, New Date(DateTime.Now.AddYears(-1).Year, 12, 31))
                emp.TERM_DATE = termDate
            Next
            db.SaveChanges
            btnShowTermdate_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs) Handles XtraTabControl1.SelectedPageChanged
        If e.Page Is XtraTabPage1 Then
            LoadData()
        End If
    End Sub

    Private Sub GridView1_CustomRowCellEditForEditing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs) Handles gvEmployeesBelowMinimumWage.CustomRowCellEditForEditing
        If e.Column Is colUpdateType Then
            Dim row As prc_MinimumWageRequirementsResult = gvEmployeesBelowMinimumWage.GetRow(e.RowHandle)
            e.RepositoryItem = riUpdateType
            riUpdateType.ReadOnly = False
            riUpdateType.Items.Clear()
            Dim items = New List(Of String)

            If row.UpdateTypeOriginal = "Scheduled" Then
                items.Add("Scheduled")
                riUpdateType.ReadOnly = True
            ElseIf row.UpdateTypeOriginal = "NewSalary" Then
                If row.Type = "Salary" Then
                    items.AddRange("None", "ForReview", "NewSalary")
                End If
                If row.Type = "Rate 1" Then
                    items.AddRange("None", "ForReview", "NewRate1", "NewTipRate")
                End If
            ElseIf row.UpdateTypeOriginal = "NewRate1" OrElse row.UpdateTypeOriginal = "NewTipRate" Then
                items.AddRange("None", "ForReview", "NewTipRate", "NewRate1")
            ElseIf row.UpdateTypeOriginal = "ForReview" Then
                items.AddRange("NewRate1", "None", "ForReview", "NewTipRate")
            End If

            riUpdateType.Items.AddRange(items)
        End If
    End Sub

    Private Sub gvEmployeesBelowMinimumWage_CustomColumnDisplayText(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs) Handles gvEmployeesBelowMinimumWage.CustomColumnDisplayText
        If e.Column.ColumnType = GetType(System.Decimal?) Then
            If e.Value = 0 Then
                e.DisplayText = ""
            End If
        End If
    End Sub
End Class