﻿Imports Brands.Core.ZendeskServices

Public Module modZendeskIntegrationClient

    Public Function ticketAttachments(ticketId As Long) As List(Of ZendeskApi_v2.Models.Shared.Attachment)
        Dim result = New List(Of ZendeskApi_v2.Models.Shared.Attachment)
        Dim zendeskApi = GetZendeskApi()
        Dim ticketComments = zendeskApi.Tickets.GetTicketComments(ticketId)
        For Each comment In ticketComments.Comments
            For Each a In comment.Attachments
                result.Add(a)
            Next
        Next
        Return result
    End Function

    Public Function GetZendeskApi() As ZendeskApi_v2.ZendeskApi
        'Return New ZendeskApi_v2.ZendeskApi(GetUdfValue("ZendeskUrl"), GetZendeskEmailAddress(), GetUdfValue("Zendesk API Token"), "en-US")
        Using context = New Brands.DAL.EPDATAContext(modGlobals.GetConnectionString())
            Return ZendeskApiUtils.GetZendeskApi(GetZendeskEmailAddress(), context)
        End Using
    End Function

    Public Function GetUpdateTicketService()
        Dim _UpdateTicketService = New Brands.Core.ZendeskServices.UpdateTicketService(New Brands.DAL.EPDATAContext(GetConnectionString()), Logger, GetZendeskApi)
        Return _UpdateTicketService
    End Function

    Public Function GetZendeskEmailAddress() As String
        If PermissionsZD.ZendeskEmailAddress.IsNotNullOrWhiteSpace Then Return PermissionsZD.ZendeskEmailAddress
        If UserInfo IsNot Nothing AndAlso UserInfo.email.IsNotNullOrWhiteSpace() Then Return UserInfo.email
        Throw New Exception($"Your username: {Permissions.UserName} is not linked to any email address")
    End Function

    Public Function GetCreateTicketDraftService() As CreateTicketDraft
        Dim context = New Brands.DAL.EPDATAContext(modGlobals.GetConnectionString())
        Dim createTicketService = New CreateTicketDraft(context, Logger, ZendeskApiUtils.GetZendeskApi(GetZendeskEmailAddress(), context))
        Return createTicketService
    End Function

    Public Async Function CanCreateTicketAsync() As Task(Of Boolean)
        If Not PermissionsZD.ZendeskUserId.HasValue Then
            Logger.Warning("CanCreateTicketAsync. ZendeskUserId is empty")
            Throw New Exception("Your username was not setup with a Zendesk account, Please contact tech support.")
        End If
        If Not Await UserHasAnyActiveConnections() Then
            Logger.Warning("CanCreateTicketAsync. no active SignalR connections")
            Throw New Exception("You don't have any zendesk app open, please open zendesk in your browser and try again.")
        End If
        Return True
    End Function

    Public Async Function UserHasAnyActiveConnections() As Task(Of Boolean)
        Return (Await GetCreateTicketDraftService().GetUserConnections(PermissionsZD.UserName)).Any
    End Function

    Public Async Function CreateTicketAsync(sendToEmailAddress As String, comp As COMPANY, report As ReportEmailTeplate, emailSubject As String, attachments As List(Of String)) As Threading.Tasks.Task(Of Boolean)
        Dim id = Await CreateTicketGetIDAsync(sendToEmailAddress, comp, report, emailSubject, attachments)
        Return id > 0
    End Function
    Public Async Function CreateTicketGetIDAsync(sendToEmailAddress As String, comp As COMPANY, report As ReportEmailTeplate, emailSubject As String, attachments As List(Of String)) As Threading.Tasks.Task(Of Int64)
        Dim emailRecipience As List(Of EmployeeEmail) = New List(Of EmployeeEmail)
        If sendToEmailAddress.IsNotNullOrWhiteSpace() Then emailRecipience.Add(New EmployeeEmail With {.Email = sendToEmailAddress})
        Dim frm = New frmEmailComp(comp.CONUM, False, False)
        If frm.ShowDialog <> DialogResult.OK Then Return False
        emailRecipience.AddRange(frm.GetSelectedEmailAddress())
        If Not emailRecipience.Any() Then Return False

        Dim createTicketService = GetCreateTicketDraftService()
        Dim ticketOptions = New CreateTicketDraft.CreateTicketOptions With
        {
            .Conum = comp.CONUM,
            .Name = emailRecipience.First().Name,
            .Subject = emailSubject,
            .Comment = "Created In Front Desk",
            .IsCommentPublic = False,
            .CollaboratorEmails = emailRecipience.Skip(1).Select(Function(e) e.Email).ToList(),
            .ExecuateMacroId = If(report?.ExecuteMacroId, GetUdfValue_AsDecimal("Zendesk_ReportsRequested_TicketDraft_MacroId"))
        }

        Dim ticket = Await createTicketService.GetTicket(report?.ExecuteMacroId)
        If ticket.Comment IsNot Nothing AndAlso (ticket.Comment.HtmlBody.IsNotNullOrWhiteSpace() OrElse ticket.Comment.PlainBody.IsNotNullOrWhiteSpace()) Then
            ticketOptions.IgnoreTicketComment = True
            If ticket.Subject.IsNotNullOrWhiteSpace() Then
                ticketOptions.Subject = ticket.Subject
            End If
        End If

        Dim newTicket = Await createTicketService.CreateTicketAsync(emailRecipience.First().Email, ticketOptions, ticket)
        Await createTicketService.NavigateToTicket(newTicket.Ticket.Id.Value, UserName, If(report?.ApplyMacroToTicket, GetUdfValue_AsDecimal("Zendesk_ReportsRequested_MacroId")))
        If attachments.Any() Then
            Using frmAttachments = New frmDraggableAttachments(attachments)
                frmAttachments.ShowDialog()
            End Using
        End If
        Return newTicket.Ticket.Id.Value
    End Function
End Module
