﻿Imports System.ComponentModel
Imports DevExpress.Customization
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Alerter
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraEditors
Imports Newtonsoft.Json
Imports RestSharp

Public Class frmMain

    Dim DB As dbEPDataDataContext
    Private _IsClosing As Boolean
    Private Logger As Serilog.ILogger
    Private WithEvents bbiOtherStatesFilingControl As BarButtonItem
    Private WithEvents bbiOtherStatesFilingControlYtd As BarButtonItem
    Private WithEvents bbiOtherStatesFilingControlRecon As BarButtonItem

    Public Sub New()
        Me.Logger = modGlobals.Logger.ForContext(Of frmMain)
        If Not Debugger.IsAttached Then
            Dim SplashScreenManager1 As DevExpress.XtraSplashScreen.SplashScreenManager = New DevExpress.XtraSplashScreen.SplashScreenManager(Me, GetType(Global.Brands_FrontDesk.SplashScreen1), True, False)
        End If
        InitializeComponent()
        MainForm = Me
        DevExpress.Export.ExportSettings.DefaultExportType = DevExpress.Export.ExportType.WYSIWYG
        AddHandler DevExpress.LookAndFeel.UserLookAndFeel.Default.StyleChanged, AddressOf StyleChange

        ' Entity Framework test removed to avoid version conflicts
        ' The application should now start without DLL conflicts
    End Sub

    Private Async Sub frmMain_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        MainForm = Me
        Try
            DB = New dbEPDataDataContext(GetConnectionString)
            ReloadPermissions()

            If Permissions Is Nothing Then
                Logger.Fatal("The user {UserName} does not exist in the FrontDesk Permission system, Please ask your administrator to set you up.", UserName)
                XtraMessageBox.Show($"The user [{UserName}] does not exist in the FrontDesk Permission system, Please ask your administrator to set you up.")
                Close()
                Application.Exit()
            End If
            UserName = Permissions.UserName
            UserInfo = (From u In DB.DBUSERs Where u.name.ToLower = UserName.ToLower).SingleOrDefault
            SetPermissions()
            Text += Version
            AddHandler BarEditItem1.Edit.Validating, AddressOf Validatiing
        Catch ex As Exception
            DisplayErrorMessage("Error loading Main Window", ex)
        End Try

        Try
            Await Task.Delay(2000)
            Await Task.Run(Function() modSignalRClient.Initialize()).ConfigureAwait(False)
            Await modSignalRCoreClient.InitializeAsync()

            PermissionsZD = Permissions

            If UserName.ToUpper = "SOLOMON.W" OrElse UserName.ToUpper = "FD.AUTOUSER" Then
                PermissionsZD = DB.FrontDeskPermissions.Single(Function(u) u.UserName = "Joel.F")
                Await modSignalRCoreClient.InitializeAsync(PermissionsZD)
            End If

            'RefreshPayrollMenue()
            'Await RefreshFaxMenue()
            RefreshBadgeCounter()
            UpdateComputerName()
            Await ZendeskLogin.AutoLogin

            SetUpFilingControls()
        Catch ex As Exception
            DisplayErrorMessage("Error initializing the program", ex)
        End Try
    End Sub

    Sub AddFilingBarButtonItem(fc As FilingControls)
        Static SeqNotYtd As Int16 = 0
        Static SeqYtd As Int16 = 0
        Static SeqRecon As Int16 = 0
        Static PlacedStateYtd As Boolean = False
        Static PlacedStateNonYtd As Boolean = False

        Dim bbi As BarButtonItem = Nothing
        Dim pmFilingControls As PopupMenu = Nothing
        Dim bbiTmpOtherStatesFilingControl As BarButtonItem = Nothing
        Dim seq As Int16
        Dim PlacedState As Boolean = Nothing

        If fc.Cat = "Recon" Then
            SeqRecon += 1
            seq = SeqRecon
            pmFilingControls = PopupMenuFilingControlsRecon
            bbiTmpOtherStatesFilingControl = bbiOtherStatesFilingControlYtdTmp
            PlacedState = PlacedStateYtd
        ElseIf fc.IsYtd Then
            SeqYtd += 1
            seq = SeqYtd
            pmFilingControls = PopupMenuFilingControlsYtd
            bbiTmpOtherStatesFilingControl = bbiOtherStatesFilingControlYtdTmp
            PlacedState = PlacedStateYtd
        Else
            SeqNotYtd += 1
            seq = SeqNotYtd
            pmFilingControls = PopupMenuFilingControls
            bbiTmpOtherStatesFilingControl = bbiOtherStatesFilingControlTmp
            PlacedState = PlacedStateNonYtd
        End If

        'if seq was placed yet for controls add the states to it
        If seq <> fc.Seq AndAlso Not PlacedState Then
            bbi = pmFilingControls.ItemLinks.Item(seq - 1).Item
            With bbi
                .Caption = bbiTmpOtherStatesFilingControl.Caption
                .Tag = bbiTmpOtherStatesFilingControl.Tag

                'hide the current state bar item 
                bbiTmpOtherStatesFilingControl.Visibility = BarItemVisibility.Never

                If fc.Cat = "Recon" Then
                    bbiOtherStatesFilingControlRecon = bbi
                    PlacedStateYtd = True
                ElseIf fc.IsYtd Then
                    bbiOtherStatesFilingControlYtd = bbi
                    PlacedStateYtd = True
                Else
                    bbiOtherStatesFilingControl = bbi
                    PlacedStateNonYtd = True
                End If
            End With
        End If

        'set the current control
        With pmFilingControls.ItemLinks.Item(fc.Seq - 1).Item
            .Caption = fc.RibbonCaption
            .Tag = fc.ID
            .Name = $"FilingControls_bbi_item_{fc.ID}"
        End With
    End Sub

    Sub SetUpFilingControls()
        Try
            Dim FilingCtrls = Query(Of FilingControls)("SELECT * FROM custom.FilingControls WHERE Active = 1 AND DevOnly = 0 ORDER BY IsYtd, Seq").ToList()
#If DEBUG Then
            If System.Net.Dns.GetHostName().ToLower() = "brands116" Then
                FilingCtrls = Query(Of FilingControls)("SELECT * FROM custom.FilingControls WHERE Active = 1 ORDER BY IsYtd, Seq").ToList()
            End If
#End If

            Dim fc As FilingControls
            For Each fc In FilingCtrls
                AddFilingBarButtonItem(fc)
            Next

            'remove all controls not used
            Dim bbil As BarButtonItemLink
            For Each bbil In PopupMenuFilingControls.ItemLinks
                If bbil.Item.Caption = "NONE" Then
                    bbil.Item.Visibility = BarItemVisibility.Never
                End If
            Next

            'remove all controls not used
            For Each bbil In PopupMenuFilingControlsYtd.ItemLinks
                If bbil.Item.Caption = "NONE" Then
                    bbil.Item.Visibility = BarItemVisibility.Never
                End If
            Next

            'remove all controls not used
            For Each bbil In PopupMenuFilingControlsRecon.ItemLinks
                If bbil.Item.Caption = "NONE" Then
                    bbil.Item.Visibility = BarItemVisibility.Never
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error In SetUpFilingControls", ex)
        End Try
    End Sub

    Private Async Sub frmMain_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        Try
            Try
                If Not modGlobals.Version?.Contains("(") Then
                    TaskbarAssistant1.OverlayIcon = My.Resources.technology_16x16
                End If
            Catch ex As Exception
                Logger.Error(ex, "Error setting OverlayIcon")
            End Try

            If CommandLineArgs IsNot Nothing AndAlso CommandLineArgs.Length > 3 Then
                modGlobals.IsBackgroundProcess = True
                Select Case CommandLineArgs(3)
                    Case "RunAutoPayrolls"
                        SetUpLogger(New Brands_FrontDesk.KeyValuePair() With {.Name = "BackgroundProcess", .Value = "RunAutoPayrolls"})
                        SetUdfValue("AutoPayrollMachineName", Environment.MachineName)
                        SetUdfValue("AutoPayrollStartTime", DateTime.Now.ToString("g"))
                        SetUdfValue("AutoPayrollSelectedCalsCount", 0)
                        Dim frm = New frmCalendar
                        frm.AutoRunAutos = True
                        ShowForm(frm)
                        Application.DoEvents()
                        frm.ProcessAutos(False)
                        SetUdfValue("AutoPayrollEndTime", DateTime.Now.ToString("g"))
                        Application.Exit()
                    Case "RunMassEmails"
                        SetUpLogger(New Brands_FrontDesk.KeyValuePair() With {.Name = "BackgroundProcess", .Value = "RunMassEmails"})
                        Logger.Debug("Template Name: {TemplateName}", CommandLineArgs(4))
                        Dim template = (From r In DB.ReportMassEmailTemplates Where r.Name = CommandLineArgs(4)).Single
                        Dim frm = New frmReportMassEmailProcess(template, True)
                        ShowForm(frm)
                    Case "RunQuarterEndEmailReports"
                        SetUpLogger(New Brands_FrontDesk.KeyValuePair() With {.Name = "BackgroundProcess", .Value = "RunQuarterEndEmailReports"})
                        Dim frm = New frmQuarterEndEmailReports With {.AutoRun = True}
                        ShowForm(frm)
                    Case "frmPrintSchedular"
                        SetUpLogger(New Brands_FrontDesk.KeyValuePair() With {.Name = "BackgroundProcess", .Value = "frmPrintSchedular"})
                        Dim frm = New frmPrintSchedular(True)
                        ShowForm(frm)
                    Case "UnlimitedPlanInv"
                        SetUpLogger(New Brands_FrontDesk.KeyValuePair() With {.Name = "BackgroundProcess", .Value = "UnlimitedPlanInv"})
                        Dim frm = New frmYearEndDecPayroll With {.RunType = frmYearEndDecPayroll.RunTypeEnum.UnlimitedPlanInv}
                        ShowForm(frm)
                    Case "Test"
                        Using frm = New frmImportPbxReport
                            frm.ShowDialog()
                        End Using
                    Case Else
                        modGlobals.IsBackgroundProcess = False
                End Select
            End If
            'AddOrActivateForm(Of frmScorecard)()
            'AddOrActivateForm(Of frmAgents)()

            'Return
            Await Task.Delay(1000)
            AddOrActivateForm(Of frmDashboard)()
            Await Task.Delay(750)
            ShowSearchCompanyEditor()
        Catch ex As Exception
            Dim cma As String() = CommandLineArgs.Clone()
            If cma.Length >= 3 Then
                cma(2) = ""
            End If
            Logger.Error(ex, "There was an exception while running the program with cmd args {cmdargs}", cma)
            Serilog.Log.CloseAndFlush()
            Application.Exit()
        End Try
    End Sub

    Public Async Sub SetPermissions()
        Try
            DocumentManager1.BeginUpdate()
            bsiUserName.Caption = Permissions.UserName

            rpgSetup.Visible = Permissions.AllowSetup
            rpgSetup.Enabled = Permissions.AllowSetup

            SetButtonPermissiong(bbiDeliveries, Permissions.AllowDeliveryScan)
            SetButtonPermissiong(bbiPrintng, Permissions.AllowPrinting)
            SetButtonPermissiong(bbiPayrollAlerts, Permissions.AllowCheckers)
            SetButtonPermissiong(bbiDdAlerts, Permissions.AllowDD)
            SetButtonPermissiong(bbiCalendar, Permissions.AllowCalender)
            SetButtonPermissiong(bbiTaxRecon, Permissions.AllowTaxRecon)
            SetButtonPermissiong(bbiDdMonitor, Permissions.AllowDDMonitor)
            SetButtonPermissiong(bbiPostNacha, Permissions.AllowTaxRecon)
            SetButtonPermissiong(bbiAccountLeads, Permissions.AllowAccountLeads)
            SetButtonPermissiong(bbiSales, Permissions.AllowCommision)
            SetButtonPermissiong(bbiFaxes, Permissions.AllowFax)
            SetButtonPermissiong(bbiYearEndWithNoDecPayroll, Permissions.AllowYearEndDecPayroll)
            SetButtonPermissiong(bbiQuarterEndEmailReports, Permissions.QuarterEndEmailReports)
            SetButtonPermissiong(bbiWrike, Permissions.AllowWrike)
            SetButtonPermissiong(bbiUDF, Permissions.Manager.GetValueOrDefault())
            SetButtonPermissiong(bbiAchTransactionLog, Permissions.AllowAchTransactionLog)
            SetButtonPermissiong(bbiQuarterEndEmailReportsACA, Permissions.QuarterEndEmailReports)
            SetButtonPermissiong(bbiManagePermissions, (UserName = "hershy.w" OrElse UserName = "moti.e" OrElse UserName = "joel.f" OrElse UserName = "solomon.w"))
            SetButtonPermissiong(bbiEmpIncentive, (Permissions.UserName = "hershy.w" OrElse Permissions.UserName = "moti.e"))
            SetButtonPermissiong(bbiOpenDoor2, (Permissions.DoorPermission > 0))
            SetButtonPermissiong(bbiSendMassEmails, Permissions.AllowMassEmailTemplate)
            SetButtonPermissiong(bbiDocumentation, Permissions.AllowDocumentationView)
            SetButtonPermissiong(bbiDDReversal, {"CS", "Admin", "SuperAdmin"}.Contains(Permissions.DDReversalLevel))
            SetButtonPermissiong(bbiBillingImports, Permissions.AllowBilling)
            SetButtonPermissiong(bbiBillingOvrrides, Permissions.AllowBilling)
            SetButtonPermissiong(bbiOrdersBilling, Permissions.AllowBilling)
            SetButtonPermissiong(bbiManualBillingDraft, Permissions.AllowBilling)
            SetButtonPermissiong(bbiUnassignedPhoneNumbers, Permissions.AllowUnassignedPhoneNumber)
            SetButtonPermissiong(bbiResendDDVoucher, Permissions.AllowResendDDVoucher)
            SetButtonPermissiong(bbiPhoneSetup, Permissions.AllowPhoneSetup)
            'added by Solomon
            SetButtonPermissionByRole(bbiScoreCard, "ScoreCards")
            SetButtonPermissionByRole(bbiCovidEmailNotify, "CovidEmailNotify")
            SetButtonPermissionByRole(bbiAudit, "ViewAuditLog")
            SetButtonPermissionByRole(bbiAuditNew, "ViewAuditLog")
            SetButtonPermissionByRole(bbiSqlSessionManager, "ManageSqlSessions")
            SetButtonPermissionByRole(bbiProductReleaseNotes, "ProductReleaseNotes")
            SetButtonPermissiong(bbiCollectDbReports, Permissions.QuarterEndEmailReports)
            SetButtonPermissionByRole(bbiEncrypt, "Encrypt")
            SetButtonPermissionByRole(bbiPaydeckBanner, "AllowManagePayDeckBanner")
            SetButtonPermissionByRole(bbiPaydeckLoginAudit, "AllowViewPaydeckLoginAudit")
            SetButtonPermissiong(bbiFix941Negatives, Permissions.AllowBilling)
            SetButtonPermissionByRole(bbiClientSetLimit, "RiskManagementSetLimit")

            If tsmiOpenDoor2 IsNot Nothing Then tsmiOpenDoor2.Visible = (Permissions.DoorPermission.HasValue AndAlso Permissions.DoorPermission > 0)
            SaveLookAndFeelToRegistry()

            For Each page As RibbonPage In RibbonControl1.Pages
                For Each pageGroup As RibbonPageGroup In page.Groups
                    If pageGroup.ItemLinks.Any(Function(b) b.Enabled) Then
                        pageGroup.Visible = True
                    Else
                        pageGroup.Visible = False
                    End If
                Next
                If page.Groups.Any(Function(p) p.Visible) Then
                    page.Visible = True
                Else
                    page.Visible = False
                End If
            Next

            If Permissions.AllowSetup Then
                Dim versionWarningMessage = ExecupayAssemblyResolver.Instance.IsUsingLatestExecupayVersion
                If versionWarningMessage.IsNotNullOrWhiteSpace Then
                    bbiLatestExecupayAPI.Visibility = BarItemVisibility.Always
                    bbiLatestExecupayAPI.Hint = versionWarningMessage
                End If
            End If

            BarEditItem1.EditValue = Permissions.AutoLoginToZendesk
            rpWebAppSetup.Visible = Permissions.AllowWebAppSetup
            rpEmployeeDeckSetup.Visible = Permissions.AllowWebAppSetup

            Try
                Await Task.Delay(5000)
                Await modPhoneApi.InitializePhoneSystem(False)
            Catch ex As Exception
                Logger.Error(ex, "Error in SetPermissions - InitializePhoneSystem")
            End Try
        Catch ex As Exception
            Logger.Error(ex, "Error setting permission")
            DisplayErrorMessage("Error in SetPermission", ex)
        Finally
            DocumentManager1.EndUpdate()
        End Try
    End Sub

    Private Sub SetButtonPermissiong(bbi As BarButtonItem, allow As Boolean?)
        bbi.Visibility = allow.ToBarItemVisibility
        bbi.Enabled = allow.GetValueOrDefault
    End Sub

    'added by Solomon
    Private Sub SetButtonPermissionByRole(bbi As BarButtonItem, RoleName As String)
        Dim sql = String.Format("SELECT custom.fn_UserInRole('{0}', '{1}')", RoleName, UserName)
        Dim granted = DB.ExecuteQuery(Of Boolean)(sql).First()
        bbi.Visibility = granted.ToBarItemVisibility()
        bbi.Enabled = granted
    End Sub

    Private Sub RibbonControl1_ItemClick(sender As Object, e As ItemClickEventArgs) Handles RibbonControl1.ItemClick, bbiLatestExecupayAPI.ItemClick
        Dim bbi = TryCast(e.Item, BarButtonItem)

        If bbi Is Nothing Then Exit Sub
        Logger.Information("User clicked on {Button}", bbi.Caption)

        If bbi Is bbiFaxes Then
            AddOrActivateForm(Of frmFaxes)()
        ElseIf bbi Is bbiDeliveries Then
            AddOrActivateForm(Of frmDeliveries)()
        ElseIf bbi Is bbiPrintng Then
            AddOrActivateForm(Of frmPrintingScans)()
        ElseIf bbi Is bbiPayrollAlerts Then
            Dim frm = New frmPayrollAlerts With {.IsDD = False}
            ShowForm(frm)
        ElseIf bbi Is bbiDdAlerts Then
            Dim frm = New frmPayrollAlerts With {.IsDD = True}
            ShowForm(frm)
        ElseIf bbi Is bbiTaxRecon Then
            AddOrActivateForm(Of frmNewTaxRecon)()
        ElseIf bbi Is bbiCalendar Then
            AddOrActivateForm(Of frmCalendar)()
        ElseIf bbi Is bbiErrorLog Then
            AddOrActivateForm(Of frmErrorLogEntry)()
        ElseIf bbi Is bbiPostNacha Then
            AddOrActivateForm(Of frmNachaTaxDrafts)()
        ElseIf bbi Is bbiPayrollInProcess Then
            AddOrActivateForm(Of frmPayrollsInProcessList)()
        ElseIf bbi Is bbiYearEndWithNoDecPayroll OrElse bbi Is bbiInactiveClientsWithOInvoices OrElse bbi Is bbiZeroReturns OrElse bbi Is bbiRefundBilling OrElse bbi Is bbiUnlimitedPlanInvoice Then
            Dim frm = New frmYearEndDecPayroll
            Dim runType As frmYearEndDecPayroll.RunTypeEnum = Choose(Array.IndexOf({bbiYearEndWithNoDecPayroll, bbiInactiveClientsWithOInvoices, bbiZeroReturns, bbiRefundBilling, bbiUnlimitedPlanInvoice}, bbi) + 1,
                                                                     frmYearEndDecPayroll.RunTypeEnum.YearEnd, frmYearEndDecPayroll.RunTypeEnum.OpenInvoice, frmYearEndDecPayroll.RunTypeEnum.ZeroReurns, frmYearEndDecPayroll.RunTypeEnum.RefundBilling, frmYearEndDecPayroll.RunTypeEnum.UnlimitedPlanInv)
            CType(frm, frmYearEndDecPayroll).RunType = runType
            CType(frm, frmYearEndDecPayroll).AutoRun = IIf(runType = 5, False, True)
            ShowForm(frm)
        ElseIf bbi Is bbiDdMonitor Then
            AddOrActivateForm(Of frmDDMonitor)()
        ElseIf bbi Is bbiCompanyUnion Then
            AddOrActivateForm(Of frmCompanyUnion)()
        ElseIf bbi Is bbiUpdatePayrollPeriods Then
            AddOrActivateForm(Of frmUpdatePayrollPeriod)()
        ElseIf bbi Is bbiRunQuarterEmailEndReports Then
            'AddOrActivateForm(Of frmQuarterEndEmailReports)()
            'Dim frm = New frmQuarterEndEmailReports(frmQuarterEndEmailReports.RunTypeEnum.NonAca)
            'ShowForm(frm)
            AddOrActivateQuarterEndReport(frmQuarterEndEmailReports.RunTypeEnum.NonAca)
        ElseIf bbi Is bbiQuarterEndEmailReportsACA Then
            'Dim frm = New frmQuarterEndEmailReports(frmQuarterEndEmailReports.RunTypeEnum.Aca)
            'ShowForm(frm)
            'AddOrActivateForm(Of frmACAEmailReports)()
            AddOrActivateQuarterEndReport(frmQuarterEndEmailReports.RunTypeEnum.Aca)
        ElseIf bbi Is bbiSendMassEmails Then
            AddOrActivateForm(Of frmReportsMassEmail)()
        ElseIf bbi Is bbiManagePermissions AndAlso (UserName = "hershy.w" OrElse UserName = "moti.e" OrElse UserName = "joel.f" OrElse UserName = "solomon.w") Then
            AddOrActivateForm(Of frmPermissions)()
        ElseIf bbi Is bbiSqlScripts Then
            'AddOrActivateForm(Of frmSqlScripts)()
            Dim frm = New frmSqlScripts()
            ShowForm(frm)
        ElseIf bbi Is bbiPlanSetup Then
            Dim frm = New frmCommPlans
            frm.ShowDialog()
            frm.Dispose()
        ElseIf bbi Is bbiCommissionSetup Then
            AddOrActivateForm(Of frmCommCompanysetup)()
        ElseIf bbi Is bbiExportGridToExcel Then
            ExportToExcel(False)
        ElseIf bbi Is bbiExportToExcelFormated Then
            ExportToExcel(True)
        ElseIf bbi Is bbiDialPhone Then
            Dim frm = New frmPhone
            If frm.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                SearchComp(frm._SearchPhone, Nothing)
            End If
        ElseIf bbi Is bbiBanking Then
            SetBankingStatus()
        ElseIf bbi Is bbiPdfCombiner Then
            Dim f = New frmPdfCombine().ShowDialog
        ElseIf bbi Is bbiHelpDesk Then
            ShowSearchCompanyEditor()
        ElseIf bbi Is bbiCheckForUpdates Then
            modGlobals.CheckForUpdate(True, True)
        ElseIf bbi Is bbiWrike Then
            AddOrActivateForm(Of frmWrike)()
        ElseIf bbi Is bbiUDF Then
            AddOrActivateForm(Of frmUDF)()
        ElseIf bbi Is bbiFaxCategorySetup Then
            AddOrActivateForm(Of frmFaxCategories)()
        ElseIf bbi Is bbiAcaOptOut Then
            AddOrActivateForm(Of frmAcaServiceOptOut)()
        ElseIf bbi Is bbiAchTransactionLog Then
            AddOrActivateForm(Of Ach.frmAchTransactions)()
        ElseIf bbi Is bbiManageInventory Then
            Dim frm = New frmReceiveInventory
            frm.ShowDialog()
        ElseIf bbi Is bbiUdf20Desc Then
            Dim frm = New frmUpdateUdf20
            frm.ShowDialog()
        ElseIf bbi Is bbiNyDependentHealth Then
            Dim frm = New frmUpdateNYDependentHealth
            frm.ShowDialog()
        ElseIf bbi Is bbiAccountLeads Then
            AddOrActivateForm(Of frmAccountLeads)()
        ElseIf bbi Is bbiBillingImports Then
            AddOrActivateForm(Of Billing.frmBillingImports)()
        ElseIf bbi Is bbiEmailDesigner Then
            ShowForm(New frmEmailDesigner)
        ElseIf bbi Is bbiOpenDoor1 Then
            Using f = New frmOpenDoor
                f.ceDoor1.Checked = Not f.ceDoor1.Checked
            End Using
        ElseIf bbi Is bbiOpenDoor2 Then
            Using f = New frmOpenDoor
                f.ceDoor2.Checked = Not f.ceDoor2.Checked
            End Using
        ElseIf bbi Is bbiEmpIncentive Then
            AddOrActivateForm(Of frmEmpIncentive)()
        ElseIf bbi Is bbiWageRateIncrease Then
            AddOrActivateForm(Of frmEmployeeRateChange)()
        ElseIf bbi Is bbiReportBuilderSetup Then
            AddOrActivateForm(Of frmCustomReportSetup)()
        ElseIf bbi Is bbiReporting Then
            Dim frm = New frmReportWizard(UserName, GetConnectionString(), Sub(f) MainForm.ShowForm(f), Me.LookAndFeel, False)
            ShowForm(frm)
            'Dim frm = New frmReportWizard
            'If frm.ShowDialog() = DialogResult.OK Then
            '    Dim frm1 = New frmRebortBuilder(frm.SelectedReportLayout.CustomReportId, frm.SelectedReportLayout)
            '    MainForm.ShowForm(frm1)
            'End If
        ElseIf bbi Is bbiPrintSchedular Then
            AddOrActivateForm(Of frmPrintSchedular)()
        ElseIf bbi Is bbiManageExecupayAPIPath OrElse bbi Is bbiLatestExecupayAPI Then
            AddOrActivateForm(Of frmExecupayApiManagement)()
        ElseIf bbi Is bbiDocumentation Then
            AddOrActivateForm(Of frmDocumentation)()
        ElseIf bbi Is bbiCalculateChecks Then
            AddOrActivateForm(Of frmCalculateChecks)()
        ElseIf bbi Is bbiColumnTempaltesSetup Then
            AddOrActivateForm(Of frmColumnTemplateSetup)()
        ElseIf bbi Is bbiOrdersBilling Then
            AddOrActivateForm(Of frmOrdersBilling)()
        ElseIf bbi Is bbiSearchEmployeeOrEmail Then
            MainForm.AddOrActivateForm(Of frmSearchEmployeeOrEmail)()
        ElseIf bbi Is bbiBillingOvrrides Then
            MainForm.AddOrActivateForm(Of frmCompanyBillingOverrides)()
        ElseIf bbi Is bbiBillingCredits Then
            MainForm.AddOrActivateForm(Of frmBillingCredits)()
        ElseIf bbi Is bbiColorSwatch Then
            Using svgSkinPaletteSelector As New SvgSkinPaletteSelector(Me)
                Dim result = svgSkinPaletteSelector.ShowDialog()
            End Using
        ElseIf bbi Is bbiDDReversal Then
            AddOrActivateForm(Of frmDDReversal)()
        ElseIf bbi Is bbiManualBillingDraft Then
            AddOrActivateForm(Of frmManualBillingDraft)()
        ElseIf bbi Is bbiManageFeedback Then
            AddOrActivateForm(Of frmReviewFeedback)()
        ElseIf bbi Is bbiImportPbxReport Then
            Using frm = New frmImportPbxReport()
                frm.ShowDialog()
            End Using
        ElseIf bbi Is bbiImportPunch Then
            Using frm = New frmImportPunchReport
                frm.ShowDialog()
            End Using
        ElseIf bbi Is bbiLogInToZendesk Then
            ZendeskLogin.LoginToZendesk()
        ElseIf bbi Is bbiReportsSetup AndAlso Permissions.AllowWebAppSetup Then
            AddOrActivateForm(Of frmReportsSetup)()
        ElseIf bbi Is bbiReportCategorySetup AndAlso Permissions.AllowWebAppSetup Then
            AddOrActivateForm(Of frmReportrCategorySetup)()
        ElseIf bbi Is bbiReportsSubTab Then
            Using frm = New frmReportsSubTabs
                frm.ShowDialog()
            End Using
        ElseIf bbi Is bbiEmployeeLogins Then
            AddOrActivateForm(Of frmEmployeeLogins)()
        ElseIf bbi Is bbiHelpArticlesSetup Then
            AddOrActivateForm(Of frmHelpCenterSetup)()
        ElseIf bbi Is bbiConnectedSessions Then
            AddOrActivateForm(Of frmConnectedSessions)()
        ElseIf bbi Is bbiPaydeckBanner Then
            AddOrActivateForm(Of frmPayDeckBanners)()
        ElseIf bbi Is bbiPaydeckLoginAudit Then
            AddOrActivateForm(Of frmLoginAudit)()
        ElseIf bbi Is bbiPaydeckUsers Then
            AddOrActivateForm(Of frmPaydeckAllUsers)()
        ElseIf bbi Is bbiAuthUserEvents Then
            AddOrActivateForm(Of frmPaydeckAuthUserEvents)()
        ElseIf bbi Is bbiUnassignedPhoneNumbers Then
            AddOrActivateForm(Of frmUnassignedPhoneNumbers)()
        ElseIf bbi Is bbiEssCompanySetup Then
            AddOrActivateForm(Of frmEmployeeDeckCompanySetup)()
        ElseIf bbi Is bbiResendDDVoucher Then
            AddOrActivateForm(Of frmResendDDVoucher)()
        ElseIf bbi Is bbiPhoneSetup Then
            AddOrActivateForm(Of frmPhoneSetup)()
        ElseIf bbi Is bbiUserPhoneSetup Then
            Using frm = New frmPhoneSetup(UserName)
                frm.ShowDialog()
            End Using
        ElseIf bbi Is bbiDashboard Then
            AddOrActivateForm(Of frmDashboard)()
        ElseIf bbi Is bbiScoreCard Then
            'allow multiple Score Cards
            Dim frm As New frmScorecard()
            ShowForm(frm)
        ElseIf bbi Is bbiCovidEmailNotify Then
            AddOrActivateForm(Of frmCovidEmailNotify)()
        ElseIf bbi Is bbiAudit Then
            Dim FileName As String = "\\file1\Apps\CDCAuditViewer\CDC_Audit.exe"
            Dim p As New Process()
            Dim ps As New ProcessStartInfo(FileName) With {.UseShellExecute = True, .WindowStyle = ProcessWindowStyle.Maximized, .WorkingDirectory = "\\file1\Apps\CDCAuditViewer\"}
            p.StartInfo = ps
            Try
                p.Start()
            Catch ex As Exception
                If ex.Message = "The system cannot find the file specified" Then
                    DisplayErrorMessage($"File not found{vbCrLf}File Name: {FileName}", ex)
                Else
                    DisplayErrorMessage("Error opening file [{0}]".FormatWith(FileName), ex)
                End If
            End Try
        ElseIf bbi Is bbiAuditNew Then
            AddOrActivateForm(Of frmAudit)()
        ElseIf bbi Is bbiSqlSessionManager Then
            Dim FileName As String = "\\brands.local\dfs\Execupay\SQLSessionManager\SQLSessionManager.exe"
            Dim p As New Process()
            Dim ps As New ProcessStartInfo(FileName) With {.UseShellExecute = True, .WindowStyle = ProcessWindowStyle.Normal, .WorkingDirectory = "\\brands.local\dfs\Execupay\SQLSessionManager\"}
            p.StartInfo = ps
            Try
                p.Start()
            Catch ex As Exception
                If ex.Message = "The system cannot find the file specified" Then
                    DisplayErrorMessage($"File not found{vbCrLf}File Name: {FileName}", ex)
                Else
                    DisplayErrorMessage("Error opening file [{0}]".FormatWith(FileName), ex)
                End If
            End Try
        ElseIf bbi Is bbiProductReleaseNotes Then
            AddOrActivateForm(Of frmReleaseNotes)()
        ElseIf bbi Is bbiImportW2HealthCare Then
            AddOrActivateForm(Of frmImportW2HealthCare)()
        ElseIf bbi Is bbiFix941Negatives Then
            AddOrActivateForm(Of frmFix941Negatives)()
        ElseIf bbi Is bbiACAUpdateLowCost Then
            AddOrActivateForm(Of frmACACost)()
        ElseIf bbi Is bbiCollectDbReports OrElse bbi Is bbiCollectDbReports900 Then
            XtraMessageBox.Show($"This will process the db quarterly report for all{If(bbi Is bbiCollectDbReports900, " 900", "")} clients.{vbCrLf}{vbCrLf}To reprocess for a client, use the script ""Quarter End\QE\Delete Disability & FLI Programing Report for client"" to delete existing report and re-run.{vbCrLf}{vbCrLf}The db report is being picked up upon processing the quarterly.", "Quarterly Reports")

            If Not IO.Directory.Exists("C:\BrandsJobs\") OrElse Not IO.File.Exists("C:\BrandsJobs\CollectQuarterlyReports900.cmd") Then 'the 900 was added later
                XtraMessageBox.Show("Copying Brands Job Executable")
                Dim proc = New Process()
                proc.StartInfo.UseShellExecute = True
                proc.StartInfo.FileName = IO.Path.Combine(Environment.SystemDirectory, "xcopy.exe")
                proc.StartInfo.Arguments = "\\brands.local\dfs\Execupay\BrandsJobs C:\BrandsJobs /E /I /Y"
                proc.Start()
                proc.WaitForExit()
            End If
            Dim procBrandsJob = New Process()
            procBrandsJob.StartInfo.FileName = $"c:\BrandsJobs\CollectQuarterlyReports{If(bbi Is bbiCollectDbReports900, "900", "")}.cmd"
            procBrandsJob.StartInfo.UseShellExecute = True
            procBrandsJob.Start()
        ElseIf bbi Is bbiEncrypt Then
            Dim fe As New frmEncrypt()
            fe.ShowDialog()
        ElseIf bbi Is bbiRiskManagement Then
            AddOrActivateForm(Of frmRiskManagement)()
        ElseIf bbi Is bbiAcaFileBatch Then
            AddOrActivateForm(Of frmAcaFileBatch)()
        ElseIf bbi Is bbiCompanyTaxDashboard Then
            AddOrActivateForm(Of frmCompTaxDashboard)()
        ElseIf bbi Is bbiTaxNotice Then
            AddOrActivateForm(Of frmTaxNotices)()
        ElseIf bbi Is bbiTaxPaymentCtrl OrElse bbi Is bbiManualTaxAdjCtrl OrElse bbi Is bbiTaxCarryFwdSheet Then
            Dim tagId = CInt(bbi.Tag)
            Dim TaxSheet As frmTaxSheet.TaxSheetEnum = CType(tagId, frmTaxSheet.TaxSheetEnum)

            Dim frm = MainForm.GetForms(Of frmTaxSheet).Where(Function(f) f.TaxSheet = TaxSheet).FirstOrDefault()
            If frm Is Nothing Then
                frm = New frmTaxSheet(TaxSheet, bbi.Caption)
                MainForm.ShowForm(frm, True)
            Else
                MainForm.ActivateForm(frm, True)
            End If
        ElseIf bbi Is bbiOtherStatesFilingControl OrElse bbi Is bbiOtherStatesFilingControlYtd Then
            Dim filingOption As frm_OT_ST_FilingCtrl.FilingOptionEnum = If(bbi Is bbiOtherStatesFilingControl, frm_OT_ST_FilingCtrl.FilingOptionEnum.Qtr, frm_OT_ST_FilingCtrl.FilingOptionEnum.Yearly)
            Dim filingText As String = If(bbi Is bbiOtherStatesFilingControl, "Qtr", "W2 Ytd")

            Dim frm = MainForm.GetForms(Of frm_OT_ST_FilingCtrl).Where(Function(f) f.FilingOption = filingOption).FirstOrDefault()
            If frm Is Nothing Then
                frm = New frm_OT_ST_FilingCtrl(filingOption, filingText)
                MainForm.ShowForm(frm, True)
            Else
                MainForm.ActivateForm(frm, True)
            End If
        ElseIf bbi Is bbiFixFile Then
            Dim FileName As String = "\\Brands.local\dfs\Execupay\Solomon\Other\nys45att\NY45ATT.exe"
            Dim p As New Process()
            Dim ps As New ProcessStartInfo(FileName) With {.UseShellExecute = True, .WindowStyle = ProcessWindowStyle.Normal, .WorkingDirectory = "\\Brands.local\dfs\Execupay\Solomon\Other\nys45att\"}
            p.StartInfo = ps
            Try
                p.Start()
            Catch ex As Exception
                If ex.Message = "The system cannot find the file specified" Then
                    DisplayErrorMessage($"File not found{vbCrLf}File Name: {FileName}", ex)
                Else
                    DisplayErrorMessage("Error opening file [{0}]".FormatWith(FileName), ex)
                End If
            End Try
        ElseIf bbi Is bbiNYS45Control OrElse bbi Is bbiNJ927SuiControl OrElse bbi Is bbi941FilingControl OrElse bbi Is bbiMtaFilingControl OrElse bbi Is bbiFilingControlYtdACA OrElse bbi Is bbiFutaFilingControl OrElse bbi Is bbi1099FilingControl OrElse bbi Is bbiSSAW2 OrElse bbi Is bbi940FilingControl OrElse bbi Is bbiNJW2 OrElse bbi Is bbiLocalW2ControlYtd OrElse bbi.Name.ToString().StartsWith("FilingControls_bbi_item_") Then
            Dim tagId = CInt(bbi.Tag)
            Dim FileControl As Int16 = CType(tagId, Int16)

            Dim frm = MainForm.GetForms(Of frmFiling).Where(Function(f) f.FileControl.ID = FileControl).FirstOrDefault()
            If frm Is Nothing Then
                frm = New frmFiling(FileControl)
                MainForm.ShowForm(frm, True)
            Else
                MainForm.ActivateForm(frm, True)
            End If
        ElseIf bbi Is bbiObcVoidCheck Then
            AddOrActivateForm(Of frmObcVoidChecks)()
        ElseIf bbi Is bbiReturnNewKey Then
            Dim guid = New Guid()
            Dim NewKey = Guid.NewGuid().ToString()
            Clipboard.SetText(NewKey)
            MessageBox.Show(NewKey, $"Key Copied to Clipboard")
        ElseIf bbi Is bbiClientSetLimit Then
            If Not UserInRole("RiskManagementSetLimit") Then
                XtraMessageBox.Show("Only available for members of role RiskManagementSetLimit")
                Return
            ElseIf Not (XtraMessageBox.Show("Please confirm?" & vbCrLf & "If you're proceeding, enter password in next screen surrounded with '*'", "Confirm?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK) Then
                Return
            End If

            AddOrActivateForm(Of frmClientLimits)()
        ElseIf bbi Is bbiTestConcurrency Then
            ' Open the concurrency test form
            Dim testForm = New frmTestConcurrency()
            ShowForm(testForm)
        End If
    End Sub

    Private Async Sub frmMain_FormClosedAsync(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles MyBase.FormClosed
        Try
            Try
                modSignalRClient.Dispose()
            Catch ex As Exception
                Logger.Error(ex, "Error Disposing SignalR")
            End Try

            Dim count = 0
            While True
                Try
                    If count > 0 Then Await Task.Delay(500)
                    Logger.Debug("Closing Application. Retry Count: {Count}", count)
                    Application.Exit()
                    Exit While
                Catch ex As Exception
                    Logger.Error(ex, "Error closing application. Retry Count: {Count}", count)
                    If count > 3 Then
                        Logger.Error("Im giving up after {Count} retries", count)
                        Exit While
                    End If
                    count = count + 1
                End Try
            End While
        Catch ex As Exception
            Logger.Error(ex, "Error closing application")
        End Try
    End Sub

    Private Sub frmMain_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            _IsClosing = True
            For index = Application.OpenForms.Count - 1 To 0
                Try
                    If index > Application.OpenForms.Count - 1 Then Continue For
                    Dim frm = TryCast(Application.OpenForms(index), Form)
                    If frm IsNot Nothing AndAlso frm IsNot Me Then
                        Logger.Debug("Closing window {Name}", frm.Name)
                        frm.Close()
                    End If
                Catch ex As Exception
                    Logger.Error(ex, "Error closing window. {Index} {Name}", index)
                End Try
            Next
        Catch ex As Exception
            Logger.Error(ex, "Error closing all open forms b4 closing application")
        End Try
    End Sub

    Sub OpenPowerGridPayroll(options As OpenPowerGridPayrollOptions)
        Try
            Logger.Debug("Entering OpenPowerGridPayroll. With Options: {@Options}", options)
            Dim Udf4Value = (From A In DB.COUSERDEFs Where A.conum = options.Conum Select A.udf4_data).Single
            If Udf4Value Is Nothing OrElse Udf4Value.ToLower <> "yes" Then
                DisplayMessageBox("SilverPay was not approved for this client.")
                Exit Sub
            End If

            Dim frmP As New frmStartNewPayroll(options)
            Dim results = frmP.ShowDialog

            If results = System.Windows.Forms.DialogResult.Cancel Then
                Exit Sub
            End If

            If String.IsNullOrWhiteSpace(frmP.PrNum) Then
                XtraMessageBox.Show("Missing Prnum, Let Hershy Know, Take Screen Shot and Continue", "Missing Payroll Number", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            Dim OpenPayrollPrNum = frmP.PrNum
            Dim OpenCalendarID = frmP.CalendarID

            Dim mdiChild = GetDocument(GetType(frmBrandsPowerGridList))
            If mdiChild Is Nothing OrElse Not mdiChild.IsVisible Then mdiChild = GetDocument(GetType(frmBrandsPowerGrid))
            If mdiChild IsNot Nothing AndAlso Not mdiChild.IsDisposing Then
                Dim CoNum As Integer
                If TypeOf mdiChild.Control Is frmBrandsPowerGridList Then
                    CoNum = CType(mdiChild.Control, frmBrandsPowerGridList).CoNum
                Else
                    CoNum = CType(mdiChild.Control, frmBrandsPowerGrid).CoNum
                End If
                If CoNum = options.Conum Then
                    mdiChild.Control.Select()
                    ActivateForm(mdiChild.Control)
                    Exit Sub
                End If
            End If
            Dim frm As frmBrandsPowerGridList
            frm = New frmBrandsPowerGridList
            frm.CoNum = options.Conum
            frm.PrNum = OpenPayrollPrNum
            frm.CalendarID = OpenCalendarID
            frm.SelectedCalendarRec = options.SelectedCalendarRec
            frm.CallBackForm = options.CallBackForm
            ShowForm(frm)
            ActivateForm(frm)
        Catch ex As Exception
            DisplayErrorMessage("Error opening PowerGrid Payroll.", ex)
        End Try
    End Sub

    Private Sub frmMain_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        Try
            If e.Control AndAlso e.KeyCode = Keys.N Then
                Select Case DocumentManager1.View.ActiveDocument.Control.Name
                    Case "frmBrandsPowerGridList"
                        Dim frm As frmBrandsPowerGridList = DocumentManager1.View.ActiveDocument.Control
                        If frm.btnLoadEmployees.Visible AndAlso frm.btnLoadEmployees.Enabled Then
                            frm.btnLoadEmployees.PerformClick()
                        ElseIf frm.btnEditPowerGrid.Enabled Then
                            frm.btnEditPowerGrid.PerformClick()
                        End If
                End Select
            ElseIf e.Control AndAlso e.KeyCode = Keys.F12 Then
                frmPdfCombine.Show()
            ElseIf e.KeyCode = Keys.F3 Then
                Dim link As BarEditItemLink = TryCast(beiSearchCompany.Links(0), BarEditItemLink)
                link.ShowEditor()
                CType(link.ActiveEditor, PopupContainerEdit).ShowPopup()
            ElseIf e.KeyCode = Keys.R AndAlso e.Control AndAlso e.Alt AndAlso e.Shift Then
                ReloadPermissions()
                SetPermissions()
                XtraMessageBox.Show("Permissions Refreshed.")
            ElseIf e.KeyCode = Keys.T AndAlso e.Control AndAlso e.Alt AndAlso e.Shift Then
                Using frm = New frmTest()
                    frm.ShowDialog()
                End Using
            ElseIf e.KeyCode = Keys.G AndAlso e.Control AndAlso e.Alt AndAlso e.Shift Then
                Try
                    GC.Collect()
                    Threading.Thread.Sleep(100)
                    GC.Collect()
                    MessageBox.Show("GC Collect Set")
                Catch ex As Exception

                End Try
            ElseIf e.KeyCode = Keys.R AndAlso e.Control Then
                bbiReporting.PerformClick()
            ElseIf e.KeyCode = Keys.E AndAlso e.Control AndAlso e.Alt AndAlso e.Shift Then
                Throw New Exception("Testing some exception")
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in key down", ex)
        End Try
    End Sub

    Public Sub ExportToExcel(formated As Boolean, Optional fileName As String = Nothing, Optional initialDirectory As String = Nothing)
        Dim ctrl As Control = Me.GetActiveGrid

        If ctrl Is Nothing Then
            DisplayMessageBox("No active grid. Click first to set focus on the grid you want to export")
            Exit Sub
        End If

        Dim bFormatted As Boolean = formated
        If TypeOf ctrl Is DataGridView AndAlso Not bFormatted Then
            Brands_FrontDesk.ExportToExcel.Export(ctrl)
        ElseIf TypeOf ctrl Is DevExpress.XtraGrid.GridControl Then
            If Not bFormatted Then
                Brands_FrontDesk.ExportToExcel.Export(CType(ctrl, DevExpress.XtraGrid.GridControl).FocusedView)
            Else
                Dim view = CType(ctrl, DevExpress.XtraGrid.GridControl).FocusedView
                Dim FD As New SaveFileDialog
                FD.InitialDirectory = initialDirectory
                FD.FileName = fileName
                FD.DefaultExt = ".xlsx"
                FD.AddExtension = True
                FD.Filter = "Excel Files|*.xls*"
                If FD.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                    If TypeOf view Is DevExpress.XtraGrid.Views.Grid.GridView Then
                        CType(view, DevExpress.XtraGrid.Views.Grid.GridView).OptionsPrint.AutoWidth = False
                    ElseIf TypeOf view Is DevExpress.XtraGrid.Views.BandedGrid.BandedGridView Then
                        CType(view, DevExpress.XtraGrid.Views.BandedGrid.BandedGridView).OptionsPrint.AutoWidth = False
                    End If
                    view.Export(DevExpress.XtraPrinting.ExportTarget.Xlsx, FD.FileName)

                    Dim P As New ProcessStartInfo(FD.FileName)
                    Process.Start(P)
                End If
                FD.Dispose()
            End If
        End If
    End Sub

    Public Sub ExportToIIF(Optional fileName As String = Nothing, Optional initialDirectory As String = Nothing)
        Dim ctrl As Control = Me.GetActiveGrid

        If ctrl Is Nothing Then
            DisplayMessageBox("No active grid. Click first to set focus on the grid you want to export")
            Exit Sub
        End If

        If TypeOf ctrl Is DataGridView Then
            ExportIIF(ctrl, initialDirectory + fileName)
        ElseIf TypeOf ctrl Is DevExpress.XtraGrid.GridControl Then
            ExportIIF(CType(ctrl, DevExpress.XtraGrid.GridControl).FocusedView, initialDirectory + fileName)
        End If

        Dim P As New ProcessStartInfo(fileName)
        Process.Start(P)
    End Sub

    Public Sub ExportIIF(GridView As DevExpress.XtraGrid.Views.Grid.GridView, Filename As String)
        Dim sb As New Text.StringBuilder(1000)
        'Dim ArrayValues(GridView.DataRowCount - 1, GridView.Columns.Count - 1) As Object
        Dim CellValue As Object
        For RowNum = 0 To GridView.DataRowCount - 1
            For ColNum = 0 To GridView.Columns.Count - 1
                CellValue = GridView.GetRowCellValue(RowNum, GridView.Columns(ColNum))
                If TypeOf CellValue Is String Then CellValue = CellValue.ToString.Replace(vbCrLf, " ").Trim
                sb.Append(CellValue)
                If ColNum < GridView.Columns.Count - 1 Then
                    sb.Append(vbTab)
                End If
            Next
            sb.AppendLine()
        Next
        Dim sw = New IO.StreamWriter(Filename)
        sw.Write(sb.ToString)
        sw.Close()
    End Sub

    Public Sub ExportIIF(Grid As DataGridView, Filename As String)
        Dim sb As New Text.StringBuilder(1000)
        'Dim ArrayValues(Grid.RowCount - 1, Grid.ColumnCount - 1) As Object
        Dim CellValue As Object
        For RowNum = 0 To Grid.RowCount - 1
            For ColNum = 0 To Grid.ColumnCount - 1
                CellValue = Grid.Item(ColNum, RowNum).Value
                If TypeOf CellValue Is String Then CellValue = CellValue.ToString.Replace(vbCrLf, " ").Trim
                sb.Append(CellValue)
                If ColNum < Grid.ColumnCount - 1 Then
                    sb.Append(vbTab)
                End If
            Next
            sb.AppendLine()
        Next
        Dim sw = New IO.StreamWriter(Filename)
        sw.Write(sb.ToString)
        sw.Close()
    End Sub


    Private Function GetActiveGrid() As Control
        Dim Frm = Me.ActiveMdiChild
        If Frm Is Nothing Then Return Nothing

        Dim ctrl As Control = Frm

        Dim container = TryCast(ctrl, ContainerControl)
        While container IsNot Nothing
            ctrl = container.ActiveControl
            container = TryCast(ctrl, ContainerControl)
        End While

        If ctrl Is Nothing Then Return Nothing

A:
        If TypeOf ctrl Is DataGridView OrElse TypeOf ctrl Is DevExpress.XtraGrid.GridControl Then
            Return ctrl
        ElseIf ctrl.Parent IsNot Nothing Then
            ctrl = ctrl.Parent
            GoTo A
        End If
        Return Nothing
    End Function

    Public Sub ShowForm(form As Form, Optional HomeRibbon As Boolean = False)
        Try
            Logger.Information("Opening form {Form}", form.Name)
            Dim document = DocumentManager1.View.AddDocument(form)

            If HomeRibbon Then
                RibbonControl1.SelectedPage = rpHome
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error opening form", ex)
        End Try
    End Sub

    Public Sub AddOrActivateForm(Of T As {Form, New})()
        Try
            Dim frm = GetForm(GetType(T))
            If frm Is Nothing Then
                ShowForm(New T())
            Else
                Logger.Information("Activating form {Form}", frm.Name)
                ActivateForm(frm)
            End If
            RibbonControl1.SelectedPage = rpHome
        Catch ex As Exception
            DisplayErrorMessage("Error in AddOrActivateForm", ex)
        End Try
    End Sub

    Public Sub ActivateForm(form As Form, Optional HomeRibbon As Boolean = False)
        For Each f In DocumentManager1.View.Documents
            If Object.Equals(form, f.Control) Then
                Logger.Information("Activating form {Form}", form.Name)
                DocumentManager1.View.Controller.Activate(f)
                If HomeRibbon Then
                    RibbonControl1.SelectedPage = rpHome
                End If
                Exit For
            End If
        Next
    End Sub

    Public Sub CloseForm(form As Form)
        For Each f In DocumentManager1.View.Documents
            If Object.Equals(form, f.Control) Then
                'Logger.Information("Closing form {Form}", form.Name)
                DocumentManager1.View.Controller.Close(f)
                Exit For
            End If
        Next
    End Sub

    Public Function GetActiveForm() As Control
        Return DocumentManager1.View.ActiveDocument?.Control
    End Function

    Public Function GetDocument(type As Type) As DevExpress.XtraBars.Docking2010.Views.BaseDocument
        If type Is Nothing Then Return Nothing
        Try
            For Each f In DocumentManager1.View.Documents
                If f IsNot Nothing AndAlso f.Control.GetType() Is type Then
                    Return f
                End If
            Next
        Catch ex As Exception
            DisplayErrorMessage("Error in GetDocument", ex)
        End Try
        Return Nothing
    End Function

    Public Function GetForm(formType As Type) As Form
        For Each f In DocumentManager1.View.Documents
            If f.Control.GetType() Is formType Then
                Return f.Control
            End If
        Next
        Return Nothing
    End Function

    Public Function GetForms(Of T As Form)() As List(Of T)
        Dim list = New List(Of T)
        For Each f In DocumentManager1.View.Documents
            Dim frm = TryCast(f.Control, T)
            If frm IsNot Nothing Then list.Add(frm)
        Next
        For Each f In DocumentManager1.View.FloatDocuments
            Dim frm = TryCast(f.Control, T)
            If frm IsNot Nothing Then list.Add(frm)
        Next
        Return list
    End Function

    Private Sub ShowSearchCompanyEditor()
        Try
            ucSearchCompany.BindPopupContainerEdit(riPceSearchCompany)
            Dim link As BarEditItemLink = TryCast(beiSearchCompany.Links(0), BarEditItemLink)
            link.ShowEditor()
            CType(link.ActiveEditor, PopupContainerEdit).ShowPopup()
        Catch ex As Exception
            DisplayMessageBox(ex.Message)
        End Try
    End Sub

    Private Sub riSleSearchCompany_EditValueChanged(sender As Object, e As EventArgs) Handles riPceSearchCompany.EditValueChanged
        Dim slueCo = CType(sender, PopupContainerEdit)
        If slueCo.EditValue IsNot Nothing Then
            Dim co = slueCo.EditValue
            slueCo.EditValue = Nothing
            OpenCompForm(co)
        End If
    End Sub

    Public Async Sub OpenCompForm(ByVal CoNum As Decimal, Optional tabIndex As frmCompanySumarry.CompanyTabPage = frmCompanySumarry.CompanyTabPage.CompanyInfo, Optional empNum As Integer? = Nothing, Optional callId As String = Nothing, Optional OpenOrderSupplies As Boolean = False)
        Try
            Await OpenCompFormAsync(CoNum, tabIndex, empNum, callId, OpenOrderSupplies)
        Catch ex As Exception
            DisplayErrorMessage("Error opening Co#: {0}".FormatWith(CoNum), ex)
        End Try
    End Sub

    Public Function GetCompSummaryForm(CoNum As Decimal) As frmCompanySumarry
        For Each f In GetForms(Of frmCompanySumarry)()
            If f.CoNum = CoNum Then
                Return f
            End If
        Next
        Return Nothing
    End Function


    Public Async Function OpenCompFormAsync(ByVal CoNum As Decimal, Optional tabIndex As frmCompanySumarry.CompanyTabPage = frmCompanySumarry.CompanyTabPage.CompanyInfo, Optional empNum As Integer? = Nothing, Optional callId As String = Nothing, Optional OpenOrderSupplies As Boolean = False) As Task(Of frmCompanySumarry)
        Try
            If empNum.GetValueOrDefault <> 0 Then tabIndex = frmCompanySumarry.CompanyTabPage.None
            For Each f In GetForms(Of frmCompanySumarry)()
                If f.CoNum = CoNum Then
                    Dim link As BarEditItemLink = TryCast(beiSearchCompany.Links(0), BarEditItemLink)
                    link.CloseEditor()
                    ActivateForm(f)
                    Await f.LoadData(True, tabIndex)
                    If empNum.HasValue Then Await f.EditEmployeeAsync(empNum.Value)
                    Return f
                End If
            Next

            If My.Application.OpenForms.OfType(Of frmCompanySumarry).Count > 6 Then
                Dim frm1 = New frmCloseCompanyTabs
                frm1.ShowDialog()
                frm1.Dispose()
            End If
            Dim frm = New frmCompanySumarry(CoNum, tabIndex) With {.callId = callId}

            AddHandler frm.Load, Sub()
                                     Dim link As BarEditItemLink = TryCast(beiSearchCompany.Links(0), BarEditItemLink)
                                     link.CloseEditor()
                                 End Sub
            ShowForm(frm)
            If empNum.HasValue Then Await frm.EditEmployeeAsync(empNum.Value)

            If OpenOrderSupplies Then
                frm.DisplayManualBilling = True
                frm.bbiOrderSupplies.PerformClick()
            End If
            Return frm
        Catch ex As Exception
            DisplayErrorMessage("Error opening Co#: {0}".FormatWith(CoNum), ex)
        End Try
        Return Nothing
    End Function

    Public Sub SearchComp(number As String, callId As String)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim r = db.view_CompanySumarries.Where(Function(c) c.CO_PHONE = number.RemoveFromStart(1) OrElse c.CO_MODEM = number.RemoveFromStart(1))
        If r.Count = 1 Then
            OpenCompForm(r.First.CONUM, callId:=callId)
        Else
            Dim link As BarEditItemLink = TryCast(beiSearchCompany.Links(0), BarEditItemLink)
            If link Is Nothing Then Exit Sub
            link.ShowEditor()
            Dim popUp = TryCast(link.ActiveEditor, PopupContainerEdit)
            If popUp IsNot Nothing Then
                riPceSearchCompany.Tag = number.RemoveFromStart(1)
                popUp.ShowPopup()
            End If
        End If
    End Sub

    Private Sub SetBankingStatus()
        Try
            If Not modGlobals.Permissions.AllowBankingHold.HasValue OrElse Not modGlobals.Permissions.AllowBankingHold.Value Then
                DisplayMessageBox("You do not have sufficient permission to manage banking status.")
                Exit Sub
            End If
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim permission = db.FrontDeskOptions.Single
            db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, permission)
            Dim user = permission.BankingHoldBy
            If bbiBanking.Caption = "Banking Hold" Then
                If user.IsNullOrWhiteSpace Then
                    db.FrontDeskOptions.Single.BankingHoldBy = UserName
                    db.SaveChanges()

                    bbiBanking.Caption = "Release Hold"

                    Dim BankingForm As frmBankingFileTracker = CType(MainForm.GetForm(frmBankingFileTracker.GetType()), frmBankingFileTracker)
                    If BankingForm IsNot Nothing Then
                        MainForm.CloseForm(BankingForm)
                    End If
                    BankingForm = New frmBankingFileTracker()
                    MainForm.ShowForm(BankingForm)

                    'If GetUdfValue("RiskManagementBankAlert") = "YES" AndAlso Query(Of Int16)("SELECT COUNT(*) FROM custom.RiskManagementAlerts rma INNER JOIN PAYROLL p ON p.CONUM = rma.CoNum AND p.PRNUM = rma.PrNum WHERE rma.Status IN('Delayed DD', 'Wire Requested') AND p.PAYROLL_STATUS NOT IN ('Done')").Count > 0 Then
                    '    Dim frm As New frmDelayDD()
                    '    frm.ShowDialog()
                    'End If
                Else
                    XtraMessageBox.Show("Banking is already held by {0}".FormatWith(user))
                    bbiBanking.Caption = "Release Hold"
                    Exit Sub
                End If
            ElseIf bbiBanking.Caption = "Release Hold" Then
                If user.IsNullOrWhiteSpace Then
                    XtraMessageBox.Show("Banking was released already")
                    bbiBanking.Caption = "Banking Hold"
                    Exit Sub
                ElseIf user <> UserName Then
                    If Permissions.BankingDep = 1 Then
                        If XtraMessageBox.Show("Banking is held by {0}. Do you want to override it? {1}If you're proceeding, enter password in next screen surrounded with '*'".FormatWith(user, vbCrLf), "Override Hold ?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = System.Windows.Forms.DialogResult.Yes AndAlso frmLogin.ShowLogIn() = System.Windows.Forms.DialogResult.OK Then
                            db.FrontDeskOptions.Single.BankingHoldBy = Nothing
                            db.SaveChanges()
                            bbiBanking.Caption = "Banking Hold"
                        End If
                    Else
                        XtraMessageBox.Show("Banking is held by {0}".FormatWith(user))
                    End If
                Else
                    db.FrontDeskOptions.Single.BankingHoldBy = Nothing
                    db.SaveChanges()
                    bbiBanking.Caption = "Banking Hold"
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in setting bank status", ex)
        End Try
    End Sub

    Public Sub AddOrActivateQuarterEndReport(RunTypeVal As frmQuarterEndEmailReports.RunTypeEnum)
        For Each f In DocumentManager1.View.Documents
            If f.Control.GetType() Is GetType(frmQuarterEndEmailReports) AndAlso CType(f.Control, frmQuarterEndEmailReports).RunType = RunTypeVal Then
                DocumentManager1.View.Controller.Activate(f)
                Exit Sub
            End If
        Next
        ShowForm(New frmQuarterEndEmailReports(RunTypeVal))
    End Sub

    Public Function TryInvoke(method As [Delegate]) As Boolean
        If MainForm.IsHandleCreated AndAlso (Not MainForm.Disposing AndAlso Not MainForm.IsDisposed) Then
            MainForm.BeginInvoke(method)
            Return True
        Else
            Logger.Debug("not raising method TryInvoke", method.Method.Name)
            Return False
        End If
    End Function

    Public Sub ShowNotification(caption As String, text As String, image As Image, Optional action As Action = Nothing)
        TryInvoke(Sub()
                      Dim alertForm As Alerter.AlertFormCore = Nothing
                      Dim alertControl = New AlertControl With {.AllowHotTrack = True,
                            .FormLocation = AlertFormLocation.BottomRight,
                            .FormShowingEffect = AlertFormShowingEffect.MoveVertical,
                            .AutoFormDelay = TimeSpan.FromMinutes(1).TotalMilliseconds,
                            .AutoHeight = True}
                      AddHandler alertControl.BeforeFormShow, Sub(s, ev)
                                                                  alertForm = ev.AlertForm
                                                              End Sub

                      AddHandler alertControl.AlertClick, Sub()
                                                              If alertForm IsNot Nothing Then alertForm.Close()
                                                              Try
                                                                  action?.Invoke()
                                                              Catch ex As Exception
                                                                  Logger.Error(ex, "Error in ShowNotification when calling action")
                                                              End Try
                                                          End Sub
                      alertControl.Show(Me, caption, text, image)
                  End Sub)
    End Sub

    Private Sub ContextMenuStrip1_ItemClicked(sender As Object, e As ToolStripItemClickedEventArgs) Handles ContextMenuStrip1.ItemClicked
        If e.ClickedItem Is tsmiOpenDoor1 Then
            bbiOpenDoor1.PerformClick()
        Else
            bbiOpenDoor2.PerformClick()
        End If
    End Sub

    Private Sub StyleChange(sender As Object, e As EventArgs)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim per = db.FrontDeskPermissions.Single(Function(u) u.UserName = UserName)
        per.LookAndFeel = DevExpress.LookAndFeel.UserLookAndFeel.Default.ActiveSkinName
        per.SvgSkinPalette = DevExpress.LookAndFeel.UserLookAndFeel.Default.ActiveSvgPaletteName
        db.SaveChanges()
        ReloadPermissions()
        SaveLookAndFeelToRegistry()
    End Sub

    Private Sub SaveLookAndFeelToRegistry()
        If Permissions.LookAndFeel.IsNotNullOrWhiteSpace Then
            DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(Permissions.LookAndFeel, Permissions.SvgSkinPalette)
            bbiColorSwatch.Visibility = (Permissions.LookAndFeel = "The Bezier").ToBarItemVisibility()
        End If
        Dim regKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(Application.ProductName)
        If Permissions.SvgSkinPalette.IsNotNullOrWhiteSpace Then regKey.SetValue("SkinSvgPalette", Permissions.SvgSkinPalette)
        If Permissions.LookAndFeel.IsNotNullOrWhiteSpace Then regKey.SetValue("UserLookAndFeel", Permissions.LookAndFeel)
    End Sub

    Private Sub UpdateComputerName()
        Try
            If Permissions.ComputerName.IsNullOrWhiteSpace OrElse Permissions.ComputerName <> Environment.MachineName OrElse Permissions.MismatchComputerNameCount > 0 Then
                Using db = New dbEPDataDataContext(GetConnectionString)
                    Dim p = db.FrontDeskPermissions.Single(Function(u) u.UserName = Permissions.UserName)
                    If p.ComputerName.IsNullOrWhiteSpace() Then p.ComputerName = Environment.MachineName
                    If p.ComputerName <> Environment.MachineName Then
                        'AVD gets reset constantly
                        If p.MismatchComputerNameCount > 10 _
                                OrElse (p.ComputerName.StartsWith("BRND-AVD") AndAlso Environment.MachineName.StartsWith("BRND-AVD")) Then
                            If XtraMessageBox.Show($"Have you recently change your computer from: {p.ComputerName} to: {Environment.MachineName}?", "Changed MachineName", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                                p.ComputerName = Environment.MachineName
                                p.MismatchComputerNameCount = 0
                            Else
                                p.MismatchComputerNameCount = 0
                            End If
                        Else
                            p.MismatchComputerNameCount = p.MismatchComputerNameCount + 1
                        End If
                    Else
                        p.MismatchComputerNameCount = 0
                    End If
                    db.SaveChanges()
                End Using
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error updating computer name", ex)
        End Try
    End Sub

    Public Async Sub RefreshBadgeCounter(Optional counter As BadgeCounters = Nothing)
        Try
            Logger.Information("Entering RefreshBadgeCounter. Count{@Counter}", counter)
            If InvokeRequired Then
                Logger.Information("Switching RefreshBadgeCounter to UI thread")
                TryInvoke(Sub() RefreshBadgeCounter(counter))
                Exit Sub
            End If

            If counter Is Nothing Then
                Dim request = New RestRequest(Method.GET)
                Dim url As String = GetUdfValue("SignalRServer_WebApi")
                Dim response = Await request.ExecuteAsync($"{url}/api/TicketSystem", "Error in get TicketSystem from API")
                counter = JsonConvert.DeserializeObject(Of BadgeCounters)(response.Content)
            End If

            BadgePayrollInProcess.Properties.Text = counter.PrInProcessCount
            BadgePayrollInProcess.Visible = counter.PrInProcessCount > 0
            Me.bbiPayrollInProcess.ItemAppearance.Normal.BackColor = If(counter.PrInProcessCount > 0, Color.FromArgb(&HFF, &HFF, &H99), Color.FromName("Control"))

            Try
                If bbiPrintng.Visibility = BarItemVisibility.Always Then
                    Using db = New dbEPDataDataContext(GetConnectionString)
                        Dim finsihedChecksCount = db.PAYROLLs.Count(Function(p) p.PAYROLL_STATUS = "Finished Checks")
                        BadgePrintingCount.Properties.Text = finsihedChecksCount.ToString()
                        BadgePrintingCount.Visible = finsihedChecksCount > 0
                        bbiPrintng.ItemAppearance.Normal.BackColor = If(finsihedChecksCount > 0, Color.FromArgb(&HFF, &HFF, &H99), Color.FromName("Control"))
                    End Using
                End If
            Catch ex As Exception
                Logger.Error(ex, "Error in Printing count")
            End Try

        Catch ex As Exception
            Logger.Error(ex, "Error in RefreshBadgeCounter")
        End Try
    End Sub

    Private Sub RibbonControl1_Merge(sender As Object, e As RibbonMergeEventArgs) Handles RibbonControl1.Merge
        If Me.RibbonControl1.MergedPages?.Count > 0 Then Me.RibbonControl1.SelectedPage = Me.RibbonControl1.MergedPages(0)
    End Sub

    Public Sub TryBringToFront()
        If InvokeRequired Then
            TryInvoke(Sub() TryBringToFront())
            Return
        End If
        Try
            Me.TopMost = True
            Me.TopMost = False
            If Me.WindowState = FormWindowState.Minimized Then
                Me.WindowState = FormWindowState.Maximized
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in TryBringToFront")
        End Try
    End Sub

    Private Async Sub bsiSignalRStatus_CheckedChangedAsync(sender As Object, e As ItemClickEventArgs) Handles bsiSignalRStatus.CheckedChanged
        bsiSignalRStatus.ImageOptions.SvgImage = If(bsiSignalRStatus.Checked, My.Resources.actions_check, Nothing)
        If bsiSignalRStatus.Checked = bsiSignalRStatus.Tag Then Exit Sub
        Await modSignalRCoreClient.ChangeDefault()
    End Sub

    Private Sub Validatiing(sender As Object, e As CancelEventArgs)
        Try
            If Permissions.ZendeskEmailAddress.IsNullOrWhiteSpace Then
                XtraMessageBox.Show("You're not setup with a zendesk login")
                e.Cancel = True
            End If
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim user = db.FrontDeskPermissions.Single(Function(u) u.UserName = UserName)
                user.AutoLoginToZendesk = BarEditItem1.EditValue
                Logger.Debug("Setting AutoLoginToZendesk to {AutoLoginToZendesk}", user.AutoLoginToZendesk)
                db.SubmitChanges()
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error setting AutoLoginToZendesk", ex)
        End Try
    End Sub

    Private Sub btnUpdateApplication_Click(sender As Object, e As EventArgs) Handles btnUpdateApplication.Click
        Logger.Information("User Clicked on Update Application.")
        modGlobals.CheckForUpdate(False, True)
    End Sub

    Private Sub btnCancelUpdate_Click(sender As Object, e As EventArgs) Handles btnCancelUpdate.Click
        fpApplicationUpdateNotification.HidePopup()
    End Sub

    Private Sub toggleUsePPxLibrary_CheckedChanged(sender As Object, e As ItemClickEventArgs) Handles toggleUsePPxLibrary.CheckedChanged
        UsePPxLibrary = toggleUsePPxLibrary.Checked
    End Sub
End Class