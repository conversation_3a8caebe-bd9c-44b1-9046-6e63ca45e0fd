﻿Imports DevExpress.XtraEditors

Public Class frmSqlScriptsExpr
    Private db As dbEPDataDataContext

    Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
    End Sub

    Private Sub frmSqlScripts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in frmSqlScripts_Load", ex)
        End Try
    End Sub

    Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString())
            GridControl1.DataSource = db.SqlScriptsExpressions.ToList()
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error in LoadData", ex)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Close()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            db.SaveChanges()
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error in Save", ex)
        End Try
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub RepositoryItemButtonEdit1_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit1.Click
        Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsExpression)
        If row Is Nothing Then Return

        Dim Sql = row.SqlString

        Try
            Dim results = Query(Of String)(Sql)
            If results.Count <> 1 Then
                Throw New Exception("Expr does not return 1 result")
            End If

            row.Tested = True
            DisplayMessageBox(results.First, "Result")
        Catch ex As Exception
            row.Tested = False
            DisplayErrorMessage(ex.Message, ex, "Error testing SQL")
        End Try
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        Try
            Dim row = CType(GridView1.GetFocusedRow(), SqlScriptsExpression)
            If row Is Nothing Then Return

            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub() DeleteRow(row), My.Resources.delete_16x16))
        Catch ex As Exception
            DisplayErrorMessage("Error in GridView1_PopupMenuShowing", ex)
        End Try
    End Sub

    Private Sub DeleteRow(row As SqlScriptsExpression)
        If XtraMessageBox.Show($"Are you sure you would like to delete row?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If
        Try
            db.SqlScriptsExpressions.DeleteOnSubmit(row)
            db.SaveChanges
            LoadData()
        Catch ex As Exception
            DisplayErrorMessage("Error when deleting row", ex)
        End Try
    End Sub
End Class