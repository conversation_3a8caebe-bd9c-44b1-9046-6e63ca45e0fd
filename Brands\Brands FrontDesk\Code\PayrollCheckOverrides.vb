﻿Public Class PayrollCheckOverrides

    Property API As PPxPayrollInterface
    Property DB As dbEPDataDataContext
    Property Notes As List(Of pr_batch_msg)
    Property WithErrors As Boolean

    Property ExcludedChecks As New List(Of String)

    Dim CoNum As Decimal
    Dim PrNum As Decimal
    Dim whoIsCalling As String = ""
    Dim CheckOverridesP As List(Of pr_batch_override)
    Dim PowerGridBatchID As Guid

    Public Event LogEvent(ByVal logLevel As WebLogger.LogLevel, ByVal Message As String)


    Public Sub New(CoNum As Decimal, PrNum As Decimal, PowerGridBatchID As Guid, Optional DB As dbEPDataDataContext = Nothing)
        Me.CoNum = CoNum
        Me.PrNum = PrNum
        Me.DB = DB
        Me.API = EP_API()
        Me.PowerGridBatchID = PowerGridBatchID

        API.CompanyID = CoNum
        API.CheckPayrollStatus(CoNum)

        Notes = New List(Of pr_batch_msg)

        LoadOverrides()
    End Sub

    Private Sub LoadOverrides()
        If DB Is Nothing Then DB = New dbEPDataDataContext(GetConnectionString)
        CheckOverridesP = (From A In DB.pr_batch_overrides
                           Where A.CONUM = API.CompanyID AndAlso A.PRNUM = API.PayrollID).ToList
    End Sub

    Function CalcSingleCheck(CoNum As Decimal, PrNum As Decimal, EmpNum As Decimal, ChkCounter As Decimal) As Boolean
        Dim results As Boolean
        'If Not UsePPxLibrary Then
        '    results = CType(API, PPxPayroll).PayrollAPI.CalculateSingleCheck(API.ProviderID, API.CompanyID, API.PayrollID, EmpNum, ChkCounter, whoIsCalling, -1)
        'Else
        '    results = API.CalculateSingleCheck(CoNum, PrNum, EmpNum, ChkCounter)
        'End If
        results = API.CalculateSingleCheck(CoNum, PrNum, EmpNum, ChkCounter)
        Return results
    End Function

    Public Function ProcessAll() As Boolean
        Dim CoPayrollOptions = (From A In DB.CoOptions_Payrolls Where A.CoNum = Me.CoNum).FirstOrDefault
        If CoPayrollOptions Is Nothing Then CoPayrollOptions = New CoOptions_Payroll

        ProcessResStateWHOverride(CoPayrollOptions.ResStateWHOverride)

        ProcessTaxOverrides()

        ProcessNetOverrides()

        ProcessLocalTaxOverride()

        If CoPayrollOptions.RoundChecksToNearestDollar.GetValueOrDefault Then
            RoundChecksToNearestDollar()
        End If

        Return True
    End Function

    Sub ProcessResStateWHOverride(ResStateWHOverride As String)
        If DB Is Nothing Then DB = New dbEPDataDataContext(GetConnectionString)
        If ResStateWHOverride = "Reduce Res WH Amt by Work St WH Amt" Then
            Dim Checks = (From A In DB.CHK_MASTs
                          Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum AndAlso {"NORMAL", "DD"}.Contains(A.CHK_TYPE) AndAlso A.STATE_RES_AMT > 0 AndAlso A.STATE_AMOUNT > 0 AndAlso A.OR_STATE_RES_AMT Is Nothing).ToList

            Dim EmpNums = (From A In Checks Select A.EMPNUM).Distinct.ToList
            Dim StateInfos = (From A In DB.STATE_EE_INFOs
                              Where A.CONUM = Me.CoNum AndAlso EmpNums.Contains(A.EMPNUM) AndAlso
                                  (A.ST_WH_FIXED.GetValueOrDefault <> 0 OrElse A.ST_WH_EXTRA.GetValueOrDefault <> 0)
                              Select A.EMPNUM
                              ).ToList

            For Each CheckRecord In Checks
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
                If ExcludedChecks.Contains(CheckRecord.EMPNUM & "-" & CheckRecord.CHK_COUNTER) Then Continue For

                Dim HasStateOverrides = (From A In StateInfos Where A = CheckRecord.EMPNUM).Any
                If HasStateOverrides Then Continue For

                Dim Diff = CheckRecord.STATE_RES_AMT.Value - CheckRecord.STATE_AMOUNT.Value
                If Diff > 0 Then
                    CheckRecord.OR_STATE_RES_AMT = Diff
                Else
                    CheckRecord.OR_STATE_RES_AMT = 0
                End If

                DB.SubmitChanges()
                Dim results As Boolean

                results = CalcSingleCheck(CoNum, PrNum, CheckRecord.EMPNUM, CheckRecord.CHK_COUNTER)
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
            Next
        End If
    End Sub

    Sub ProcessTaxOverrides(Optional TaxOverrides As List(Of pr_batch_override) = Nothing)
        If TaxOverrides Is Nothing Then
            TaxOverrides = (From A In CheckOverridesP
                            Where A.FedOverrideAmount.HasValue OrElse A.STOverrideAmount.HasValue OrElse A.DBOverrideAmount.HasValue OrElse A.FLIOverrideAmount.HasValue OrElse
                                  A.TaxFrequency IsNot Nothing OrElse A.ChkType IsNot Nothing OrElse A.OASDIOverrideAmount.HasValue OrElse A.MedicareOverrideAmount.HasValue OrElse
                                  A.ManualCheckNumber.HasValue OrElse A.WrkState IsNot Nothing OrElse A.ResState IsNot Nothing OrElse A.UIState IsNot Nothing
                                    ).ToList
        End If

        If TaxOverrides.Count > 0 Then
            RaiseEvent LogEvent(WebLogger.LogLevel.Info, String.Format("Calculating Tax Overrides Checks - {0} Check(s)", TaxOverrides.Count))
            For Each rec In TaxOverrides
                Dim itm = rec
                Dim CheckRecord = (From A In DB.CHK_MASTs Where A.CONUM = itm.CONUM AndAlso A.PAYROLL_NUM = itm.PRNUM AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER).FirstOrDefault

                If CheckRecord Is Nothing Then
                    RaiseEvent LogEvent(WebLogger.LogLevel.Error, String.Format("Check # {0} not found for Employee # {1}", itm.CHK_COUNTER, itm.EMPNUM))
                    Continue For
                Else
                    DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
                    If itm.UIState IsNot Nothing AndAlso itm.UIState.Length = 2 Then _
                        CheckRecord.STATE_UCI = itm.UIState

                    If itm.WrkState IsNot Nothing AndAlso itm.WrkState.Length = 2 Then
                        CheckRecord.STATE_WH = itm.WrkState
                        If itm.ResState Is Nothing OrElse itm.ResState <> itm.WrkState Then CheckRecord.STATE_WH_RES = itm.ResState 'Only set res st if not same as work st, or res st = ‘’
                    End If

                    If itm.FedOverrideAmount.HasValue Then _
                        CheckRecord.OR_FED_WH = If(itm.FedOverrideAmount.Value < 1, CheckRecord.GROSS * itm.FedOverrideAmount.Value, itm.FedOverrideAmount.Value)

                    If itm.STOverrideAmount.HasValue Then _
                        CheckRecord.OR_STATE_AMOUNT = If(itm.STOverrideAmount.Value < 1, CheckRecord.GROSS * itm.STOverrideAmount.Value, itm.STOverrideAmount.Value)

                    If itm.OASDIOverrideAmount.HasValue Then _
                            CheckRecord.OR_OASDI = If(itm.OASDIOverrideAmount.Value < 1, CheckRecord.GROSS * itm.OASDIOverrideAmount.Value, itm.OASDIOverrideAmount.Value)

                    If itm.MedicareOverrideAmount.HasValue Then _
                        CheckRecord.OR_MEDICARE = If(itm.MedicareOverrideAmount.Value < 1, CheckRecord.GROSS * itm.MedicareOverrideAmount.Value, itm.MedicareOverrideAmount.Value)

                    If itm.DBOverrideAmount.HasValue Then
                        Dim CalculateOverrideDisability As validateOverrideDB = overrideDisability(itm.DBOverrideAmount, CheckRecord.GROSS, CheckRecord.PAY_FREQ, CheckRecord.STATE_UCI)
                        If CalculateOverrideDisability.DBOverrideAmount >= 0 Then
                            CheckRecord.OR_EMP_DIS = CalculateOverrideDisability.DBOverrideAmount
                        Else
                            Me.Notes.Add(New pr_batch_msg With {.id = Guid.NewGuid,
                                                    .conum = API.CompanyID,
                                                    .empnum = CheckRecord.EMPNUM,
                                                    .msg_type = "DBOver",
                                                    .batch_id = Me.PowerGridBatchID,
                                                    .msg_body = CalculateOverrideDisability.message,
                                                    .chk_counter = CheckRecord.CHK_COUNTER})
                        End If
                    End If

                    If itm.FLIOverrideAmount.HasValue AndAlso CheckRecord.STATE_UCI = "NY" Then _
                        CheckRecord.OR_EE_FLI = If(itm.FLIOverrideAmount.Value < 1, CheckRecord.GROSS * itm.FLIOverrideAmount.Value, itm.FLIOverrideAmount.Value)

                    If itm.TaxFrequency IsNot Nothing Then CheckRecord.PAY_FREQ = itm.TaxFrequency
                    If itm.ChkType & "" <> "" Then
                        CheckRecord.CHK_TYPE = itm.ChkType
                        If itm.ChkType = "MANUAL" OrElse itm.ChkType = "SICK" Then
                            CheckRecord.CHK_NUM = itm.ManualCheckNumber
                            If Not itm.FedOverrideAmount.HasValue Then
                                CheckRecord.OR_FED_WH = 0
                            End If
                            If Not itm.STOverrideAmount.HasValue Then
                                CheckRecord.OR_STATE_AMOUNT = 0
                            End If
                            If Not itm.DBOverrideAmount.HasValue Then
                                CheckRecord.OR_EMP_DIS = 0
                            End If
                        End If
                    End If

                    DB.SubmitChanges()
                    Dim retryCount = 0
Calculate:
                    Try
                        retryCount += 1
                        Dim results = CalcSingleCheck(CoNum, PrNum, itm.EMPNUM, itm.CHK_COUNTER)
                        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
                    Catch ex As Exception
                        If retryCount < 3 Then
                            GoTo Calculate
                        Else
                            DisplayMessageBox("Error calclualting check override - Emp# " & itm.EMPNUM & " - Check Counter " & itm.CHK_COUNTER)
                        End If
                    End Try
                End If
            Next 'Override
            'Logger.Log(WebLogger.LogLevel.Info, "Done")
            DB.SubmitChanges()
        End If
    End Sub

    Sub ProcessNetOverrides(Optional NetOverrides As List(Of pr_batch_override) = Nothing)
        If NetOverrides Is Nothing Then
            NetOverrides = (From A In CheckOverridesP Where A.NetOverrideAmount.HasValue).ToList
        End If
        If NetOverrides.Count > 0 Then
            WithErrors = False
            RaiseEvent LogEvent(WebLogger.LogLevel.Info, String.Format("Calculating Net Override Checks - {0} Check(s)", NetOverrides.Count))
            For Each rec In NetOverrides
                Dim itm = rec

                If ExcludedChecks.Contains(rec.EMPNUM & "-" & rec.CHK_COUNTER) Then Continue For

                Dim CheckRecordQ = (From A In DB.CHK_MASTs Where A.CONUM = itm.CONUM AndAlso A.PAYROLL_NUM = itm.PRNUM AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER).ToList()
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecordQ)
                Dim CheckRecord = CheckRecordQ.FirstOrDefault


                If CheckRecord Is Nothing Then
                    RaiseEvent LogEvent(WebLogger.LogLevel.Error, String.Format("Check # {0} not found for Employee # {1}", itm.CHK_COUNTER, itm.EMPNUM))
                    Continue For
                Else
                    Dim msg As pr_batch_msg = Nothing
                    Dim OriginalAmount As Decimal, locDiff As Decimal, OriginalDedAmount As Decimal?, ExcludeAmount As Decimal
                    Dim CheckDetail As CHK_DET_PAY = Nothing
                    Dim CheckDeduction As CHK_DET_DED = Nothing

                    If itm.NetOverrideAdjustType.StartsWith("Deduction") Then
                        'skip if netgetive
                        If CheckRecord.NET < 0 Then Continue For

                        If rec.NetOverrideAdjustType.ToLower.Contains("credit to net") Then
                            ExcludeAmount = If((From A In CheckRecord.CHK_DET_DEDs Where A.DEDUCTION IsNot Nothing AndAlso A.DEDUCTION.DED_CALC = "Credit To Net" Select A.CL_AMOUNT).Sum, 0)
                        End If

                        CheckDeduction = (From A In CheckRecord.CHK_DET_DEDs Where A.CL_CODE = rec.NetOverrideDedNum).FirstOrDefault
                        If CheckDeduction IsNot Nothing Then
                            OriginalDedAmount = CheckDeduction.CL_AMOUNT.GetValueOrDefault
                        Else
                            Dim EEDept = (From A In DB.EMPLOYEEs Where A.CONUM = CheckRecord.CONUM AndAlso A.EMPNUM = CheckRecord.EMPNUM Select A.DEPTNUM).SingleOrDefault
                            'Dim MaxCL = If((From A In CheckRecord.CHK_DET_DEDs Select New Decimal?(A.CL_NUM)).Max, 0)
                            Dim MaxCL = If((From A In CheckRecord.CHK_DET_DEDs Select New Decimal?(A.CL_NUM)).ToList().Max, 0)
                            CheckDeduction = New CHK_DET_DED With {.CL_CODE = rec.NetOverrideDedNum, .CL_NUM = MaxCL + 1, .CL_DEPT = EEDept, .CL_TYPE = "MANUAL", .CL_AMOUNT = 0}
                            CheckRecord.CHK_DET_DEDs.Add(CheckDeduction)
                        End If
                    Else
                        Dim CheckDetailQ = (From A In DB.CHK_DET_PAYs Where A.CONUM = itm.CONUM AndAlso A.PAYROLL_NUM = itm.PRNUM AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER Where A.CL_CODE <> 1 Order By A.CL_CODE).ToList()
                        CheckDetail = CheckDetailQ.FirstOrDefault
                        If CheckDetail Is Nothing Then
                            itm.NetOverrideAdjustType = "Fed"
                        End If

                        OriginalAmount = If(itm.NetOverrideAdjustType = "Gross", CheckDetail.CL_REG_PAY, CheckRecord.FED_WH)

                        If itm.LOCOverrideAmount.HasValue Then
                            Dim AllCheckDetailRecords = CheckRecord.CHK_DET_PAYs.ToList
                            Dim OriginalLoc = AllCheckDetailRecords.Sum(Function(p) p.CL_LOC_AMT1.GetValueOrDefault)
                            locDiff = If(itm.LOCOverrideAmount.HasValue, (itm.LOCOverrideAmount.Value - OriginalLoc), 0)
                        End If
                    End If

                    Dim Skipped As Boolean = False

                    For X = 0 To 19
                        If CheckRecord.NET = (itm.NetOverrideAmount + locDiff) Then
                            Exit For
                        Else
                            Dim Diff As Decimal
                            If Not itm.NetOverrideAdjustType.StartsWith("Deduction") Then
                                Diff = (itm.NetOverrideAmount + locDiff) - CheckRecord.NET
                                Dim DiffMax As Decimal = CheckRecord.NET * 0.25
                                'If Math.Abs(Diff) > 10 Then
                                If Math.Abs(Diff) > Math.Abs(DiffMax) Then
                                    'Log error
                                    msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                    .batch_id = Me.PowerGridBatchID,
                                                    .chk_counter = itm.CHK_COUNTER,
                                                    .conum = itm.CONUM,
                                                    .empnum = itm.EMPNUM,
                                                    .msg_body = String.Format("Unable to automatically adjust net to {0}. Difference is more than 25%. Fix manually in EP.", itm.NetOverrideAmount.GetValueOrDefault.ToString("c")),
                                                    .msg_type = "NETOVR"}
                                    Skipped = True
                                    Exit For
                                End If
                            End If
                            If itm.NetOverrideAdjustType = "Fed" Then
                                If itm.FedOverrideAmount.HasValue Then
                                    msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                    .batch_id = Me.PowerGridBatchID,
                                                    .chk_counter = itm.CHK_COUNTER,
                                                    .conum = itm.CONUM,
                                                    .empnum = itm.EMPNUM,
                                                    .msg_body = String.Format("Unable to automatically adjust net to {0}. Has Fed Override value of {1}. Fix manually in EP.", itm.NetOverrideAmount.GetValueOrDefault.ToString("c"), itm.FedOverrideAmount.GetValueOrDefault.ToString("c")),
                                                    .msg_type = "NETOVR"}
                                    Skipped = True
                                    Exit For
                                End If
                                Dim FedORAmount As Decimal
                                If CheckRecord.OR_FED_WH Is Nothing AndAlso CheckRecord.FED_WH Is Nothing Then
                                    FedORAmount = -Diff
                                    'CheckRecord.OR_FED_WH = -Diff
                                ElseIf CheckRecord.OR_FED_WH Is Nothing Then
                                    FedORAmount = (CheckRecord.FED_WH - Diff)
                                    'CheckRecord.OR_FED_WH = (CheckRecord.FED_WH - Diff)
                                Else
                                    FedORAmount = CheckRecord.OR_FED_WH - Diff
                                    'CheckRecord.OR_FED_WH -= Diff
                                End If
                                If FedORAmount > 0 Then
                                    CheckRecord.OR_FED_WH = FedORAmount
                                Else
                                    msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                    .batch_id = Me.PowerGridBatchID,
                                                    .chk_counter = itm.CHK_COUNTER,
                                                    .conum = itm.CONUM,
                                                    .empnum = itm.EMPNUM,
                                                    .msg_body = String.Format("Unable to automatically adjust net to {0} because it results in a negetive fed of {2}.{1}Adjust manually in EP by reducing other tax amounts if any.{1}Otherwise Contact client if to increase gross so to get to the desired net.",
                                                                                itm.NetOverrideAmount.GetValueOrDefault.ToString("c"),
                                                                                vbCrLf,
                                                                                FedORAmount.ToString("c")),
                                                    .msg_type = "NETOVR"}
                                    Skipped = True
                                    Exit For
                                End If
                            ElseIf itm.NetOverrideAdjustType = "Gross" Then
                                CheckDetail.CL_REG_PAY += CalculateFicaAdd(Diff)
                            Else 'by deduction
                                CheckDeduction.CL_AMOUNT += (CheckRecord.NET - ExcludeAmount - rec.NetOverrideAmount)
                            End If

                            DB.SubmitChanges()
                            Dim results = CalcSingleCheck(API.CompanyID, API.PayrollID, itm.EMPNUM, itm.CHK_COUNTER)
                            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
                            If CheckDetail IsNot Nothing Then DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckDetail)
                            If CheckDeduction IsNot Nothing Then DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckDeduction)
                        End If
                    Next 'Counter 
                    If Not Skipped AndAlso CheckRecord.NET <> (itm.NetOverrideAmount + locDiff) Then
                        'Put back original value
                        If itm.NetOverrideAdjustType = "Gross" Then
                            CheckDetail.CL_REG_PAY = OriginalAmount
                        ElseIf itm.NetOverrideAdjustType = "Fed" Then
                            CheckRecord.FED_WH = OriginalAmount
                            CheckRecord.OR_FED_WH = Nothing
                        Else
                            If OriginalDedAmount.HasValue Then
                                CheckDeduction.CL_AMOUNT = OriginalDedAmount
                            Else
                                DB.CHK_DET_DEDs.DeleteOnSubmit(CheckDeduction)
                            End If
                        End If
                        DB.SubmitChanges()
                        Dim results = CalcSingleCheck(API.CompanyID, API.PayrollID, itm.EMPNUM, itm.CHK_COUNTER)
                        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)

                        'Log error
                        msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                .batch_id = Me.PowerGridBatchID,
                                                .chk_counter = itm.CHK_COUNTER,
                                                .conum = itm.CONUM,
                                                .empnum = itm.EMPNUM,
                                                .msg_body = String.Format("Unable to automatically adjust net to {0}. Fix manually in EP.", itm.NetOverrideAmount.GetValueOrDefault.ToString("c")),
                                                .msg_type = "NETOVR"}
                    End If
                    If msg IsNot Nothing Then
                        WithErrors = True
                        'DB.pr_batch_msgs.InsertOnSubmit(msg)
                        Me.Notes.Add(msg)
                    End If
                End If 'CheckRecord Is Nothing
            Next 'Override
            'Logger.Log(WebLogger.LogLevel.Info, "Done")
            DB.SubmitChanges()
        End If
    End Sub

    Sub ProcessLocalTaxOverride(Optional LocalTaxOverrides As List(Of pr_batch_override) = Nothing)
        If LocalTaxOverrides Is Nothing Then
            LocalTaxOverrides = (From A In CheckOverridesP Where A.LOCOverrideAmount.HasValue OrElse A.ChkType IsNot Nothing).ToList
        End If
        If LocalTaxOverrides.Count > 0 Then
            WithErrors = False
            RaiseEvent LogEvent(WebLogger.LogLevel.Info, String.Format("Calculating Local Tax Overrides Checks - {0} Check(s)", LocalTaxOverrides.Count))
            For Each rec In LocalTaxOverrides
                Dim itm = rec

                If ExcludedChecks.Contains(rec.EMPNUM & "-" & rec.CHK_COUNTER) Then Continue For

                Dim CheckRecord = (From A In DB.CHK_MASTs Where A.CONUM = itm.CONUM AndAlso A.PAYROLL_NUM = itm.PRNUM AndAlso A.EMPNUM = itm.EMPNUM AndAlso A.CHK_COUNTER = itm.CHK_COUNTER).FirstOrDefault

                If CheckRecord Is Nothing Then
                    RaiseEvent LogEvent(WebLogger.LogLevel.Error, String.Format("Check # {0} not found for Employee # {1}", itm.CHK_COUNTER, itm.EMPNUM))
                    Continue For
                Else
                    DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
                    Dim AllCheckDetailRecords = CheckRecord.CHK_DET_PAYs.ToList
                    Dim CheckDetail = AllCheckDetailRecords.FirstOrDefault
                    Dim OriginlalNet = CheckRecord.NET.GetValueOrDefault
                    Dim OriginalLoc = AllCheckDetailRecords.Sum(Function(p) p.CL_LOC_AMT1.GetValueOrDefault)
                    Dim TotalGross = CheckRecord.GROSS.GetValueOrDefault
                    Dim msg As pr_batch_msg = Nothing
                    Dim ManOrSick = itm.ChkType = "MANUAL" OrElse itm.ChkType = "SICK"

                    If TotalGross = 0 Then
                        msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                            .batch_id = Me.PowerGridBatchID,
                                            .chk_counter = itm.CHK_COUNTER,
                                            .conum = itm.CONUM,
                                            .empnum = itm.EMPNUM,
                                            .msg_body = String.Format("Unable to automatically adjust local to {0}. $0.00 Gross.", itm.LOCOverrideAmount.GetValueOrDefault.ToString("c")),
                                            .msg_type = "LOCAL1"}
                    ElseIf (CheckDetail.CL_LOC_CD1 = 0 OrElse OriginalLoc = 0) AndAlso itm.LOCOverrideAmount.HasValue AndAlso itm.LOCOverrideAmount.Value = 0 Then
                        Continue For
                    ElseIf CheckDetail.CL_LOC_CD1 = 0 AndAlso itm.LOCOverrideAmount.HasValue Then
                        msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                    .batch_id = Me.PowerGridBatchID,
                                                    .chk_counter = itm.CHK_COUNTER,
                                                    .conum = itm.CONUM,
                                                    .empnum = itm.EMPNUM,
                                                    .msg_body = String.Format("Unable to automatically adjust local to {0}, Employee is not set subject to local, Fix manually in EP.", itm.LOCOverrideAmount.GetValueOrDefault.ToString("c")),
                                                    .msg_type = "LOCAL1"}
                    Else
                        If ManOrSick AndAlso Not itm.LOCOverrideAmount.HasValue Then
                            CheckDetail.OR_CL_LOC_AMT1 = 0
                        Else
                            CheckDetail.OR_CL_LOC_AMT1 = If(itm.LOCOverrideAmount.Value < 1, CheckRecord.GROSS * itm.LOCOverrideAmount.Value, itm.LOCOverrideAmount.Value)
                        End If
                        Dim LocalAmount = CheckDetail.OR_CL_LOC_AMT1.Value
                        Dim TotalAmount As Decimal = 0
                        For X = 0 To AllCheckDetailRecords.Count - 1
                            Dim CheckDetailLine = AllCheckDetailRecords(X)
                            Dim Pct As Decimal = (CheckDetailLine.CL_REG_PAY.GetValueOrDefault + CheckDetailLine.CL_OT_PAY.GetValueOrDefault) / TotalGross
                            CheckDetailLine.CL_LOC_AMT1 = Decimal.Round(LocalAmount * Pct, 2)
                            TotalAmount += CheckDetailLine.CL_LOC_AMT1
                        Next
                        'Round
                        Dim DiffAmount = LocalAmount - TotalAmount
                        If DiffAmount <> 0 Then
                            AllCheckDetailRecords(0).CL_LOC_AMT1 += DiffAmount
                        End If

                        Dim NewNet = (OriginlalNet + OriginalLoc) - LocalAmount
                        CheckRecord.NET = NewNet
                        If itm.NetOverrideAmount.HasValue AndAlso itm.NetOverrideAmount.Value <> NewNet Then
                            msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                .batch_id = Me.PowerGridBatchID,
                                                .chk_counter = itm.CHK_COUNTER,
                                                .conum = itm.CONUM,
                                                .empnum = itm.EMPNUM,
                                                .msg_body = String.Format("Unable to automatically adjust net to {0}. Fix manually in EP.", itm.NetOverrideAmount.GetValueOrDefault.ToString("c")),
                                                .msg_type = "NETOVR"}
                        End If
                    End If
                    If msg IsNot Nothing Then
                        Me.Notes.Add(msg)
                        'DB.pr_batch_msgs.InsertOnSubmit(msg)
                        WithErrors = True
                    End If
                End If

                DB.SubmitChanges()
                'do not recalculate, as it results in restoring original local
                'Dim results = CalcSingleCheck(CoNum, PrNum, itm.EMPNUM, itm.CHK_COUNTER)
            Next 'Override
            DB.SubmitChanges()
        End If
    End Sub

    Sub RoundChecksToNearestDollar()
        Dim emp1099 = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum
                       Join E In DB.EMPLOYEEs On A.CONUM Equals E.CONUM And A.EMPNUM Equals E.EMPNUM
                       Where E.EMP_TYPE.ToUpper = "CONTRACT"
                       Select E.EMPNUM).ToList
        Dim Checks = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum AndAlso {"NORMAL", "DD"}.Contains(A.CHK_TYPE)).ToList

        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Checks)

        For Each CheckRecord In Checks
            If ExcludedChecks.Contains(CheckRecord.EMPNUM & "-" & CheckRecord.CHK_COUNTER) Then Continue For

            If emp1099.Contains(CheckRecord.EMPNUM) Then Continue For
            Dim Diff = CheckRecord.NET.Value - Decimal.Round(CheckRecord.NET.Value, 0)
            If Diff = 0 Then Continue For
            Dim itm = (From A In DB.pr_batch_overrides_setups Where A.EmpNum = CheckRecord.EMPNUM AndAlso A.CheckCounter = CheckRecord.CHK_COUNTER AndAlso A.FedOverrideAmount IsNot Nothing).FirstOrDefault
            If itm IsNot Nothing AndAlso itm.FedOverrideAmount.HasValue Then
                Dim msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                    .batch_id = Me.PowerGridBatchID,
                                    .chk_counter = itm.CheckCounter,
                                    .conum = itm.CoNum,
                                    .empnum = itm.EmpNum,
                                    .msg_body = String.Format("Unable to automatically adjust net to nearest dollar. Has Fed Override value of {1}. Fix manually in EP.", itm.NetOverrideAmount.GetValueOrDefault.ToString("c"), itm.FedOverrideAmount.GetValueOrDefault.ToString("c")),
                                    .msg_type = "NETOVR"}
                Me.WithErrors = True
                DB.pr_batch_msgs.InsertOnSubmit(msg)
            End If
            If CheckRecord.FED_WH + Diff < 0 Then
                Diff = CheckRecord.NET - Decimal.Floor(CheckRecord.NET)
            End If
            CheckRecord.FED_WH += Diff
            CheckRecord.NET -= Diff
        Next
        DB.SubmitChanges()
    End Sub

    Sub RemoveExtraTaxesFromSecondCheck()
        Dim Checks = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum AndAlso A.CHK_COUNTER > 1 AndAlso {"NORMAL", "DD"}.Contains(A.CHK_TYPE)).ToList
        If Checks.Count = 0 Then Return

        Dim dExtraFed = (From A In DB.EMPLOYEEs Where A.CONUM = Me.CoNum AndAlso A.FED_WH_EXTRA.GetValueOrDefault <> 0 Select A.EMPNUM, A.FED_WH_EXTRA).
            ToDictionary(Function(p) p.EMPNUM, Function(p) p.FED_WH_EXTRA.GetValueOrDefault)

        Dim lExtraST = (From A In DB.STATE_EE_INFOs Where A.CONUM = Me.CoNum AndAlso A.ST_WH_EXTRA.GetValueOrDefault <> 0 Select A.EMPNUM, A.ST_WH_EXTRA, A.STATE).ToList

        Dim lExtraLC = (From A In DB.local_ee_infos Where A.conum = Me.CoNum AndAlso A.extra_wh.GetValueOrDefault <> 0 Select A.empnum, A.extra_wh, A.local_id).ToList

        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Checks)

        For Each CheckRecord In Checks
            If ExcludedChecks.Contains(CheckRecord.EMPNUM & "-" & CheckRecord.CHK_COUNTER) Then Continue For

            Dim IsChanged = False, IsDetailsChanged = False
            Dim CheckDetail As List(Of CHK_DET_PAY) = Nothing

            Dim ExtraFed As Decimal
            If dExtraFed.TryGetValue(CheckRecord.EMPNUM, ExtraFed) Then
                If CheckRecord.FED_WH >= ExtraFed AndAlso CheckRecord.OR_FED_WH.GetValueOrDefault = 0 Then
                    CheckRecord.OR_FED_WH = CheckRecord.FED_WH - ExtraFed
                    IsChanged = True
                End If
            End If

            Dim dExtraSt = (From A In lExtraST
                            Where A.EMPNUM = CheckRecord.EMPNUM).
                            ToDictionary(Function(p) p.STATE, Function(p) p.ST_WH_EXTRA.GetValueOrDefault)
            If dExtraSt.Count > 0 Then
                Dim ExtraSt As Decimal
                If dExtraSt.TryGetValue(CheckRecord.STATE_WH, ExtraSt) Then
                    If CheckRecord.STATE_AMOUNT >= ExtraSt AndAlso CheckRecord.OR_STATE_AMOUNT.GetValueOrDefault = 0 Then
                        CheckRecord.OR_STATE_AMOUNT = CheckRecord.STATE_AMOUNT - ExtraSt
                        IsChanged = True
                    End If
                End If

                If CheckRecord.STATE_WH_RES.IsNotNullOrWhiteSpace AndAlso dExtraSt.TryGetValue(CheckRecord.STATE_WH_RES, ExtraSt) Then
                    If CheckRecord.STATE_RES_AMT >= ExtraSt AndAlso CheckRecord.OR_STATE_RES_AMT.GetValueOrDefault = 0 Then
                        CheckRecord.OR_STATE_RES_AMT = CheckRecord.STATE_RES_AMT - ExtraSt
                        IsChanged = True
                    End If
                End If
            End If

            Dim eExtraLc = (From A In lExtraLC Where A.empnum = CheckRecord.EMPNUM).ToList
            If eExtraLc.Count > 0 Then
                CheckDetail = CheckRecord.CHK_DET_PAYs.ToList
                Dim list = (From A In CheckDetail Select New CheckDetail With {.CL_LOC_CD = A.CL_LOC_CD1, .CL_LOC_AMT = A.CL_LOC_AMT1, .OR_CL_LOC_AMT = A.OR_CL_LOC_AMT1, .CheckDetPay = A, .Position = 1}).
                                        Union(From A In CheckDetail Select New CheckDetail With {.CL_LOC_CD = A.CL_LOC_CD2, .CL_LOC_AMT = A.CL_LOC_AMT2, .OR_CL_LOC_AMT = A.OR_CL_LOC_AMT2, .CheckDetPay = A, .Position = 2}).
                                        Union(From A In CheckDetail Select New CheckDetail With {.CL_LOC_CD = A.CL_LOC_CD3, .CL_LOC_AMT = A.CL_LOC_AMT3, .OR_CL_LOC_AMT = A.OR_CL_LOC_AMT3, .CheckDetPay = A, .Position = 3}).
                                        Union(From A In CheckDetail Select New CheckDetail With {.CL_LOC_CD = A.CL_LOC_CD4, .CL_LOC_AMT = A.CL_LOC_AMT4, .OR_CL_LOC_AMT = A.OR_CL_LOC_AMT4, .CheckDetPay = A, .Position = 4}).
                                        Union(From A In CheckDetail Select New CheckDetail With {.CL_LOC_CD = A.CL_LOC_CD5, .CL_LOC_AMT = A.CL_LOC_AMT5, .OR_CL_LOC_AMT = A.OR_CL_LOC_AMT5, .CheckDetPay = A, .Position = 5})
                Dim AllLocalCodes = (From A In list
                                     Where A.CL_LOC_CD IsNot Nothing AndAlso A.CL_LOC_AMT.GetValueOrDefault > 0
                                     Join B In eExtraLc On A.CL_LOC_CD Equals B.local_id
                                     Where A.CL_LOC_AMT >= B.extra_wh AndAlso A.OR_CL_LOC_AMT.GetValueOrDefault = 0
                                     Select detail = A.CheckDetPay, A.Position, A.CL_LOC_AMT, B.extra_wh).ToList
                For Each line In AllLocalCodes
                    Dim amount = line.CL_LOC_AMT - line.extra_wh
                    line.detail.GetType.GetProperty("OR_CL_LOC_AMT" & line.Position).SetValue(line.detail, amount)
                    IsDetailsChanged = True
                Next
            End If

            DB.SubmitChanges()
            If IsChanged OrElse IsDetailsChanged Then
                Dim results = CalcSingleCheck(API.CompanyID, API.PayrollID, CheckRecord.EMPNUM, CheckRecord.CHK_COUNTER)
                DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckRecord)
            End If
            If CheckDetail IsNot Nothing Then DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, CheckDetail)
        Next
        DB.SubmitChanges()
    End Sub

    Class CheckDetail
        Public CL_LOC_CD As Decimal?
        Public CL_LOC_AMT As Decimal?
        Public OR_CL_LOC_AMT As Decimal?
        Public CheckDetPay As CHK_DET_PAY
        Public Position As Integer
    End Class

End Class
