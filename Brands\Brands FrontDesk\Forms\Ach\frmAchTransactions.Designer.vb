﻿Namespace Ach
    <Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
    Partial Class frmAchTransactions
        Inherits DevExpress.XtraBars.Ribbon.RibbonForm

        'Form overrides dispose to clean up the component list.
        <System.Diagnostics.DebuggerNonUserCode()>
        Protected Overrides Sub Dispose(ByVal disposing As Boolean)
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
            MyBase.Dispose(disposing)
        End Sub

        'Required by the Windows Form Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Windows Form Designer
        'It can be modified using the Windows Form Designer.  
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()>
        Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAchTransactions))
            Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
            Me.bsAch = New System.Windows.Forms.BindingSource(Me.components)
            Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colId = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colDateFaxReceived = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riDate = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
            Me.colEffDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCoNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riSlueCoNum = New DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit()
            Me.RepositoryItemSearchLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colCoNumAndName = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colFED_ID = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPR_CONTACT = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_EMAIL = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_FAX = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_PHONE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEntryDescType = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riCbeDesc = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
            Me.colCode = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riCbeCode = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
            Me.colDebit = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riCbeAmounts = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
            Me.colCredit = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colActions = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riCbeActions = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
            Me.colChagee = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colNsfFee = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPrNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCheckDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colNotes = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riBeInformation = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
            Me.colStatus = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riStatus = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
            Me.colExcludeFromAutoEmails = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEmailTemplate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riCbeEmailTemplates = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
            Me.colLastEmailDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEmpNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAccountNumber = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colRoutingNumber = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAccountType = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedAccountNumber = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedRoutingNumber = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedAccountType = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedAction = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCorrectedData = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colName = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAddUser = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAddDateTime = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colChgUser = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colChgDateTime = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colFollowUpDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsClientFault = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colRedraftClearedDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colBrands_Bank_Cleared_Date = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colMarked_Brands_Cleared_By = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colClient_Refund_Processed_Date = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colNACHAENTRY_TransNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colMarked_Brands_Cleared_On = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
            Me.bbiRefresh = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiEmailClient = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiOpenCo = New DevExpress.XtraBars.BarButtonItem()
            Me.BarButtonItem1 = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiSetCoPassword = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiQtrEndHold = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiImportAchFile = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiImportNatPayFile = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiSetEmpDDType = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiOpenEmp = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiAutoEmails = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiAchSettings = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRemoveCoPass = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRequestWire = New DevExpress.XtraBars.BarButtonItem()
            Me.PopupMenuRequestWire = New DevExpress.XtraBars.PopupMenu(Me.components)
            Me.BarSubItemRequestWireDD = New DevExpress.XtraBars.BarSubItem()
            Me.bbiRequestWireDDNewTicket = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRequestWireDDAttach = New DevExpress.XtraBars.BarButtonItem()
            Me.BarSubItemRequestWireTax = New DevExpress.XtraBars.BarSubItem()
            Me.bbiRequestWireTaxNewTicket = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRequestWireTaxAttach = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRequestWireNewTicket = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRequestWireAttach = New DevExpress.XtraBars.BarButtonItem()
            Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
            Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
            Me.rpgCoOptions = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
            Me.RepositoryItemDateEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
            Me.RepositoryItemDateEdit3 = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
            Me.riTeCoNmAutoFilter = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
            Me.riLuePrNum = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
            Me.riEmpNum = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
            Me.EMPLOYEEBindingSource = New System.Windows.Forms.BindingSource(Me.components)
            Me.riTeReadOnly = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
            Me.LayoutViewCard2 = New DevExpress.XtraGrid.Views.Layout.LayoutViewCard()
            Me.item1 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.LayoutViewCard1 = New DevExpress.XtraGrid.Views.Layout.LayoutViewCard()
            Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
            Me.GridControlRelatedCompanies = New DevExpress.XtraGrid.GridControl()
            Me.GridViewRelatedCompanies = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.riButtonEditCoNum = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
            Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.UcCompInfo1 = New Brands_FrontDesk.ucCompInfo()
            Me.UcFaxAndEmailGrid1 = New Brands_FrontDesk.ucFaxAndEmailGrid()
            Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
            Me.AchTransactionDailyLogBindingSource = New System.Windows.Forms.BindingSource(Me.components)
            Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
            Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
            Me.DateEdit2 = New DevExpress.XtraEditors.DateEdit()
            Me.DateEdit1 = New DevExpress.XtraEditors.DateEdit()
            Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
            Me.meAchLog = New DevExpress.XtraEditors.MemoEdit()
            Me.MemoEdit1 = New DevExpress.XtraEditors.MemoEdit()
            Me.btnNoReturnsExist = New DevExpress.XtraEditors.SimpleButton()
            Me.btnRefreshImportLog = New DevExpress.XtraEditors.SimpleButton()
            Me.btnImport = New DevExpress.XtraEditors.SimpleButton()
            Me.cbeFileType = New DevExpress.XtraEditors.ComboBoxEdit()
            Me.beFilePath = New DevExpress.XtraEditors.ButtonEdit()
            Me.deTransactionsDate = New DevExpress.XtraEditors.DateEdit()
            Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
            Me.achTransactionDailyLogsBindingSource = New System.Windows.Forms.BindingSource(Me.components)
            Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colId1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colTransactionsDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colFilePath = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colFileType = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsCompleted = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAddUser1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colAddDateTime1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.deToDate = New DevExpress.XtraEditors.DateEdit()
            Me.deFromDate = New DevExpress.XtraEditors.DateEdit()
            Me.ccbeStatusFilter = New DevExpress.XtraEditors.CheckedComboBoxEdit()
            Me.gcImportedData = New DevExpress.XtraGrid.GridControl()
            Me.BindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
            Me.gvImportedData = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colDateReceived = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEntryDescType1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colRejectionCode = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colDebitAmount = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCreditAmount = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colEffectiveDate = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colName1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedRoutingNumber1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedAccountNumber1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedAccountType1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colImportedAction1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCorrectedData1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
            Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.TabbedControlGroup2 = New DevExpress.XtraLayout.TabbedControlGroup()
            Me.LayoutControlGroup8 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem22 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem4 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup6 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup7 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.lciRelatedComp = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.lciRelatedCompanies = New DevExpress.XtraLayout.LayoutControlItem()
            Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
            Me.SplitterItem2 = New DevExpress.XtraLayout.SplitterItem()
            Me.LayoutControlGroup5 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.SplitterItem3 = New DevExpress.XtraLayout.SplitterItem()
            Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup9 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup12 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.LayoutControlGroup10 = New DevExpress.XtraLayout.LayoutControlGroup()
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.bsAch, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riDate, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riDate.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riSlueCoNum, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemSearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riCbeDesc, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riCbeCode, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riCbeAmounts, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riCbeActions, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riBeInformation, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riStatus, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riCbeEmailTemplates, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.PopupMenuRequestWire, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemDateEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemDateEdit2.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemDateEdit3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemDateEdit3.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riTeCoNmAutoFilter, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riLuePrNum, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riEmpNum, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EMPLOYEEBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riTeReadOnly, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutViewCard2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.item1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutViewCard1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.lcRoot.SuspendLayout()
            CType(Me.GridControlRelatedCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridViewRelatedCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.riButtonEditCoNum, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.AchTransactionDailyLogBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.DateEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.DateEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.DateEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.DateEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.meAchLog.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.cbeFileType.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.beFilePath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deTransactionsDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deTransactionsDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.achTransactionDailyLogsBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deToDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deToDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deFromDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.ccbeStatusFilter.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gcImportedData, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gvImportedData, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TabbedControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lciRelatedComp, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lciRelatedCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.SplitterItem3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup9, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup12, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup10, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.SuspendLayout()
            '
            'GridControl1
            '
            Me.GridControl1.DataSource = Me.bsAch
            Me.GridControl1.Location = New System.Drawing.Point(24, 69)
            Me.GridControl1.MainView = Me.GridView1
            Me.GridControl1.MenuManager = Me.RibbonControl1
            Me.GridControl1.Name = "GridControl1"
            Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riSlueCoNum, Me.riCbeDesc, Me.riCbeCode, Me.riTeCoNmAutoFilter, Me.riBeInformation, Me.riLuePrNum, Me.riCbeActions, Me.riCbeAmounts, Me.riCbeEmailTemplates, Me.riDate, Me.riEmpNum, Me.riTeReadOnly, Me.riStatus})
            Me.GridControl1.Size = New System.Drawing.Size(812, 392)
            Me.GridControl1.TabIndex = 4
            Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
            '
            'bsAch
            '
            Me.bsAch.DataSource = GetType(Brands_FrontDesk.AchTransactionsLog)
            '
            'GridView1
            '
            Me.GridView1.Appearance.HeaderPanel.Font = New System.Drawing.Font("Arial Narrow", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            Me.GridView1.Appearance.HeaderPanel.Options.UseFont = True
            Me.GridView1.Appearance.Row.Font = New System.Drawing.Font("Tahoma", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            Me.GridView1.Appearance.Row.Options.UseFont = True
            Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colId, Me.colDateFaxReceived, Me.colEffDate, Me.colCoNum, Me.colEntryDescType, Me.colCode, Me.colDebit, Me.colCredit, Me.colActions, Me.colChagee, Me.colNsfFee, Me.colPrNum, Me.colCheckDate, Me.colNotes, Me.colStatus, Me.colExcludeFromAutoEmails, Me.colEmailTemplate, Me.colLastEmailDate, Me.colEmpNum, Me.colAccountNumber, Me.colRoutingNumber, Me.colAccountType, Me.colImportedAccountNumber, Me.colImportedRoutingNumber, Me.colImportedAccountType, Me.colImportedAction, Me.colCorrectedData, Me.colName, Me.colAddUser, Me.colAddDateTime, Me.colChgUser, Me.colChgDateTime, Me.colFollowUpDate, Me.colIsClientFault, Me.colRedraftClearedDate, Me.colBrands_Bank_Cleared_Date, Me.colMarked_Brands_Cleared_By, Me.colClient_Refund_Processed_Date, Me.colNACHAENTRY_TransNum, Me.colMarked_Brands_Cleared_On})
            Me.GridView1.GridControl = Me.GridControl1
            Me.GridView1.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "CoNum", Nothing, "(Co Num: Count={0})")})
            Me.GridView1.Name = "GridView1"
            Me.GridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.[True]
            Me.GridView1.OptionsBehavior.EditingMode = DevExpress.XtraGrid.Views.Grid.GridEditingMode.EditFormInplace
            Me.GridView1.OptionsEditForm.BindingMode = DevExpress.XtraGrid.Views.Grid.EditFormBindingMode.Direct
            Me.GridView1.OptionsEditForm.EditFormColumnCount = 5
            Me.GridView1.OptionsView.ColumnAutoWidth = False
            Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
            Me.GridView1.OptionsView.EnableAppearanceOddRow = True
            Me.GridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Top
            Me.GridView1.OptionsView.ShowAutoFilterRow = True
            Me.GridView1.OptionsView.ShowGroupPanel = False
            Me.GridView1.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.colDateFaxReceived, DevExpress.Data.ColumnSortOrder.Descending)})
            '
            'colId
            '
            Me.colId.Caption = "ID"
            Me.colId.FieldName = "ID"
            Me.colId.Name = "colId"
            '
            'colDateFaxReceived
            '
            Me.colDateFaxReceived.Caption = "Date Received"
            Me.colDateFaxReceived.ColumnEdit = Me.riDate
            Me.colDateFaxReceived.DisplayFormat.FormatString = "g"
            Me.colDateFaxReceived.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            Me.colDateFaxReceived.FieldName = "DateReceived"
            Me.colDateFaxReceived.Name = "colDateFaxReceived"
            Me.colDateFaxReceived.Visible = True
            Me.colDateFaxReceived.VisibleIndex = 0
            '
            'riDate
            '
            Me.riDate.AllowMouseWheel = False
            Me.riDate.AutoHeight = False
            Me.riDate.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riDate.CalendarTimeEditing = DevExpress.Utils.DefaultBoolean.[True]
            Me.riDate.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riDate.DisplayFormat.FormatString = "g"
            Me.riDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            Me.riDate.EditFormat.FormatString = "g"
            Me.riDate.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            Me.riDate.Mask.BeepOnError = True
            Me.riDate.Mask.EditMask = "g"
            Me.riDate.Name = "riDate"
            '
            'colEffDate
            '
            Me.colEffDate.Caption = "File Eff Date"
            Me.colEffDate.FieldName = "EffectiveDate"
            Me.colEffDate.Name = "colEffDate"
            Me.colEffDate.Visible = True
            Me.colEffDate.VisibleIndex = 1
            '
            'colCoNum
            '
            Me.colCoNum.ColumnEdit = Me.riSlueCoNum
            Me.colCoNum.FieldName = "CoNum"
            Me.colCoNum.Name = "colCoNum"
            Me.colCoNum.OptionsEditForm.ColumnSpan = 3
            Me.colCoNum.OptionsEditForm.UseEditorColRowSpan = False
            Me.colCoNum.Visible = True
            Me.colCoNum.VisibleIndex = 2
            '
            'riSlueCoNum
            '
            Me.riSlueCoNum.AutoHeight = False
            Me.riSlueCoNum.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riSlueCoNum.DisplayMember = "CoNumAndName"
            Me.riSlueCoNum.Name = "riSlueCoNum"
            Me.riSlueCoNum.NullText = ""
            Me.riSlueCoNum.PopupFormSize = New System.Drawing.Size(1000, 0)
            Me.riSlueCoNum.PopupView = Me.RepositoryItemSearchLookUpEdit1View
            Me.riSlueCoNum.ValueMember = "CONUM"
            '
            'RepositoryItemSearchLookUpEdit1View
            '
            Me.RepositoryItemSearchLookUpEdit1View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNumAndName, Me.colFED_ID, Me.colPR_CONTACT, Me.colCO_EMAIL, Me.colCO_FAX, Me.colCO_PHONE, Me.colCO_STATUS})
            Me.RepositoryItemSearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
            Me.RepositoryItemSearchLookUpEdit1View.Name = "RepositoryItemSearchLookUpEdit1View"
            Me.RepositoryItemSearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
            Me.RepositoryItemSearchLookUpEdit1View.OptionsView.ShowGroupPanel = False
            '
            'colCoNumAndName
            '
            Me.colCoNumAndName.FieldName = "CoNumAndName"
            Me.colCoNumAndName.Name = "colCoNumAndName"
            Me.colCoNumAndName.Visible = True
            Me.colCoNumAndName.VisibleIndex = 0
            '
            'colFED_ID
            '
            Me.colFED_ID.FieldName = "FED_ID"
            Me.colFED_ID.Name = "colFED_ID"
            Me.colFED_ID.Visible = True
            Me.colFED_ID.VisibleIndex = 1
            '
            'colPR_CONTACT
            '
            Me.colPR_CONTACT.FieldName = "PR_CONTACT"
            Me.colPR_CONTACT.Name = "colPR_CONTACT"
            Me.colPR_CONTACT.Visible = True
            Me.colPR_CONTACT.VisibleIndex = 2
            '
            'colCO_EMAIL
            '
            Me.colCO_EMAIL.FieldName = "CO_EMAIL"
            Me.colCO_EMAIL.Name = "colCO_EMAIL"
            Me.colCO_EMAIL.Visible = True
            Me.colCO_EMAIL.VisibleIndex = 3
            '
            'colCO_FAX
            '
            Me.colCO_FAX.FieldName = "CO_FAX"
            Me.colCO_FAX.Name = "colCO_FAX"
            Me.colCO_FAX.Visible = True
            Me.colCO_FAX.VisibleIndex = 4
            '
            'colCO_PHONE
            '
            Me.colCO_PHONE.FieldName = "CO_PHONE"
            Me.colCO_PHONE.Name = "colCO_PHONE"
            Me.colCO_PHONE.Visible = True
            Me.colCO_PHONE.VisibleIndex = 5
            '
            'colCO_STATUS
            '
            Me.colCO_STATUS.FieldName = "CO_STATUS"
            Me.colCO_STATUS.Name = "colCO_STATUS"
            Me.colCO_STATUS.Visible = True
            Me.colCO_STATUS.VisibleIndex = 6
            '
            'colEntryDescType
            '
            Me.colEntryDescType.ColumnEdit = Me.riCbeDesc
            Me.colEntryDescType.FieldName = "EntryDescType"
            Me.colEntryDescType.Name = "colEntryDescType"
            Me.colEntryDescType.OptionsEditForm.StartNewRow = True
            Me.colEntryDescType.Visible = True
            Me.colEntryDescType.VisibleIndex = 3
            '
            'riCbeDesc
            '
            Me.riCbeDesc.AutoHeight = False
            Me.riCbeDesc.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riCbeDesc.Name = "riCbeDesc"
            '
            'colCode
            '
            Me.colCode.ColumnEdit = Me.riCbeCode
            Me.colCode.FieldName = "RejectionCode"
            Me.colCode.Name = "colCode"
            Me.colCode.Visible = True
            Me.colCode.VisibleIndex = 4
            '
            'riCbeCode
            '
            Me.riCbeCode.AutoHeight = False
            Me.riCbeCode.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riCbeCode.Name = "riCbeCode"
            '
            'colDebit
            '
            Me.colDebit.ColumnEdit = Me.riCbeAmounts
            Me.colDebit.DisplayFormat.FormatString = "C2"
            Me.colDebit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colDebit.FieldName = "DebitAmount"
            Me.colDebit.Name = "colDebit"
            Me.colDebit.Visible = True
            Me.colDebit.VisibleIndex = 5
            '
            'riCbeAmounts
            '
            Me.riCbeAmounts.AutoHeight = False
            Me.riCbeAmounts.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riCbeAmounts.DisplayFormat.FormatString = "c"
            Me.riCbeAmounts.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.riCbeAmounts.Name = "riCbeAmounts"
            '
            'colCredit
            '
            Me.colCredit.ColumnEdit = Me.riCbeAmounts
            Me.colCredit.FieldName = "CreditAmount"
            Me.colCredit.Name = "colCredit"
            Me.colCredit.Visible = True
            Me.colCredit.VisibleIndex = 6
            Me.colCredit.Width = 69
            '
            'colActions
            '
            Me.colActions.Caption = "Action Taken"
            Me.colActions.ColumnEdit = Me.riCbeActions
            Me.colActions.FieldName = "ActionsTaken"
            Me.colActions.Name = "colActions"
            Me.colActions.Visible = True
            Me.colActions.VisibleIndex = 7
            '
            'riCbeActions
            '
            Me.riCbeActions.AutoHeight = False
            Me.riCbeActions.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riCbeActions.Name = "riCbeActions"
            '
            'colChagee
            '
            Me.colChagee.FieldName = "Charge"
            Me.colChagee.Name = "colChagee"
            Me.colChagee.Visible = True
            Me.colChagee.VisibleIndex = 8
            '
            'colNsfFee
            '
            Me.colNsfFee.Caption = "NSF"
            Me.colNsfFee.ColumnEdit = Me.riCbeAmounts
            Me.colNsfFee.DisplayFormat.FormatString = "C2"
            Me.colNsfFee.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colNsfFee.FieldName = "NsfFee"
            Me.colNsfFee.Name = "colNsfFee"
            Me.colNsfFee.Visible = True
            Me.colNsfFee.VisibleIndex = 9
            '
            'colPrNum
            '
            Me.colPrNum.Caption = "Pr#"
            Me.colPrNum.FieldName = "PrNum"
            Me.colPrNum.Name = "colPrNum"
            Me.colPrNum.Visible = True
            Me.colPrNum.VisibleIndex = 10
            '
            'colCheckDate
            '
            Me.colCheckDate.Caption = "Check Date"
            Me.colCheckDate.FieldName = "CheckDate"
            Me.colCheckDate.Name = "colCheckDate"
            Me.colCheckDate.Visible = True
            Me.colCheckDate.VisibleIndex = 11
            '
            'colNotes
            '
            Me.colNotes.ColumnEdit = Me.riBeInformation
            Me.colNotes.FieldName = "Notes"
            Me.colNotes.Name = "colNotes"
            Me.colNotes.Visible = True
            Me.colNotes.VisibleIndex = 12
            '
            'riBeInformation
            '
            Me.riBeInformation.AutoHeight = False
            Me.riBeInformation.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
            Me.riBeInformation.Name = "riBeInformation"
            Me.riBeInformation.ReadOnly = True
            '
            'colStatus
            '
            Me.colStatus.ColumnEdit = Me.riStatus
            Me.colStatus.FieldName = "Status"
            Me.colStatus.Name = "colStatus"
            Me.colStatus.Visible = True
            Me.colStatus.VisibleIndex = 13
            '
            'riStatus
            '
            Me.riStatus.AutoHeight = False
            Me.riStatus.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riStatus.Name = "riStatus"
            Me.riStatus.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
            '
            'colExcludeFromAutoEmails
            '
            Me.colExcludeFromAutoEmails.FieldName = "ExcludeFromAutoEmails"
            Me.colExcludeFromAutoEmails.Name = "colExcludeFromAutoEmails"
            Me.colExcludeFromAutoEmails.Visible = True
            Me.colExcludeFromAutoEmails.VisibleIndex = 14
            '
            'colEmailTemplate
            '
            Me.colEmailTemplate.Caption = "Email Template"
            Me.colEmailTemplate.ColumnEdit = Me.riCbeEmailTemplates
            Me.colEmailTemplate.FieldName = "EmailTemplate"
            Me.colEmailTemplate.Name = "colEmailTemplate"
            Me.colEmailTemplate.Visible = True
            Me.colEmailTemplate.VisibleIndex = 15
            '
            'riCbeEmailTemplates
            '
            Me.riCbeEmailTemplates.AutoHeight = False
            Me.riCbeEmailTemplates.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riCbeEmailTemplates.Name = "riCbeEmailTemplates"
            Me.riCbeEmailTemplates.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
            '
            'colLastEmailDate
            '
            Me.colLastEmailDate.Caption = "Last Email Date"
            Me.colLastEmailDate.FieldName = "LastEmailDate"
            Me.colLastEmailDate.Name = "colLastEmailDate"
            Me.colLastEmailDate.Visible = True
            Me.colLastEmailDate.VisibleIndex = 16
            '
            'colEmpNum
            '
            Me.colEmpNum.Caption = "Emp #"
            Me.colEmpNum.FieldName = "EmpNum"
            Me.colEmpNum.Name = "colEmpNum"
            Me.colEmpNum.Visible = True
            Me.colEmpNum.VisibleIndex = 17
            '
            'colAccountNumber
            '
            Me.colAccountNumber.Caption = "Account#"
            Me.colAccountNumber.FieldName = "AccountNumber"
            Me.colAccountNumber.Name = "colAccountNumber"
            Me.colAccountNumber.OptionsEditForm.StartNewRow = True
            Me.colAccountNumber.Visible = True
            Me.colAccountNumber.VisibleIndex = 18
            '
            'colRoutingNumber
            '
            Me.colRoutingNumber.Caption = "Routing#"
            Me.colRoutingNumber.FieldName = "RoutingNumber"
            Me.colRoutingNumber.Name = "colRoutingNumber"
            Me.colRoutingNumber.Visible = True
            Me.colRoutingNumber.VisibleIndex = 19
            '
            'colAccountType
            '
            Me.colAccountType.Caption = "Account Type"
            Me.colAccountType.FieldName = "AccountType"
            Me.colAccountType.Name = "colAccountType"
            Me.colAccountType.Visible = True
            Me.colAccountType.VisibleIndex = 20
            '
            'colImportedAccountNumber
            '
            Me.colImportedAccountNumber.Caption = "Imported Account #"
            Me.colImportedAccountNumber.FieldName = "ImportedAccountNumber"
            Me.colImportedAccountNumber.Name = "colImportedAccountNumber"
            Me.colImportedAccountNumber.OptionsEditForm.StartNewRow = True
            Me.colImportedAccountNumber.Visible = True
            Me.colImportedAccountNumber.VisibleIndex = 21
            '
            'colImportedRoutingNumber
            '
            Me.colImportedRoutingNumber.Caption = "Imported Routing #"
            Me.colImportedRoutingNumber.FieldName = "ImportedRoutingNumber"
            Me.colImportedRoutingNumber.Name = "colImportedRoutingNumber"
            Me.colImportedRoutingNumber.Visible = True
            Me.colImportedRoutingNumber.VisibleIndex = 22
            '
            'colImportedAccountType
            '
            Me.colImportedAccountType.Caption = "Imported Account Type"
            Me.colImportedAccountType.FieldName = "ImportedAccountType"
            Me.colImportedAccountType.Name = "colImportedAccountType"
            Me.colImportedAccountType.Visible = True
            Me.colImportedAccountType.VisibleIndex = 23
            '
            'colImportedAction
            '
            Me.colImportedAction.Caption = "Imported Action"
            Me.colImportedAction.FieldName = "ImportedAction"
            Me.colImportedAction.Name = "colImportedAction"
            Me.colImportedAction.Visible = True
            Me.colImportedAction.VisibleIndex = 24
            '
            'colCorrectedData
            '
            Me.colCorrectedData.Caption = "Corrected Data"
            Me.colCorrectedData.FieldName = "CorrectedData"
            Me.colCorrectedData.Name = "colCorrectedData"
            Me.colCorrectedData.OptionsEditForm.ColumnSpan = 2
            Me.colCorrectedData.OptionsEditForm.UseEditorColRowSpan = False
            Me.colCorrectedData.Visible = True
            Me.colCorrectedData.VisibleIndex = 25
            '
            'colName
            '
            Me.colName.Caption = "Name"
            Me.colName.FieldName = "Name"
            Me.colName.Name = "colName"
            Me.colName.OptionsEditForm.ColumnSpan = 3
            Me.colName.OptionsEditForm.UseEditorColRowSpan = False
            Me.colName.Visible = True
            Me.colName.VisibleIndex = 26
            '
            'colAddUser
            '
            Me.colAddUser.FieldName = "AddUser"
            Me.colAddUser.Name = "colAddUser"
            Me.colAddUser.OptionsColumn.AllowEdit = False
            Me.colAddUser.OptionsColumn.ReadOnly = True
            Me.colAddUser.Visible = True
            Me.colAddUser.VisibleIndex = 27
            '
            'colAddDateTime
            '
            Me.colAddDateTime.DisplayFormat.FormatString = "g"
            Me.colAddDateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            Me.colAddDateTime.FieldName = "AddDateTime"
            Me.colAddDateTime.Name = "colAddDateTime"
            Me.colAddDateTime.OptionsColumn.AllowEdit = False
            Me.colAddDateTime.OptionsColumn.ReadOnly = True
            Me.colAddDateTime.Visible = True
            Me.colAddDateTime.VisibleIndex = 28
            '
            'colChgUser
            '
            Me.colChgUser.FieldName = "ChgUser"
            Me.colChgUser.Name = "colChgUser"
            Me.colChgUser.OptionsColumn.AllowEdit = False
            Me.colChgUser.OptionsColumn.ReadOnly = True
            Me.colChgUser.Visible = True
            Me.colChgUser.VisibleIndex = 29
            '
            'colChgDateTime
            '
            Me.colChgDateTime.DisplayFormat.FormatString = "g"
            Me.colChgDateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            Me.colChgDateTime.FieldName = "ChgDateTime"
            Me.colChgDateTime.Name = "colChgDateTime"
            Me.colChgDateTime.OptionsColumn.AllowEdit = False
            Me.colChgDateTime.OptionsColumn.ReadOnly = True
            Me.colChgDateTime.Visible = True
            Me.colChgDateTime.VisibleIndex = 30
            '
            'colFollowUpDate
            '
            Me.colFollowUpDate.FieldName = "FollowUpDate"
            Me.colFollowUpDate.Name = "colFollowUpDate"
            Me.colFollowUpDate.Visible = True
            Me.colFollowUpDate.VisibleIndex = 31
            '
            'colIsClientFault
            '
            Me.colIsClientFault.FieldName = "IsClientFault"
            Me.colIsClientFault.Name = "colIsClientFault"
            Me.colIsClientFault.Visible = True
            Me.colIsClientFault.VisibleIndex = 32
            '
            'colRedraftClearedDate
            '
            Me.colRedraftClearedDate.FieldName = "RedraftClearedDate"
            Me.colRedraftClearedDate.Name = "colRedraftClearedDate"
            Me.colRedraftClearedDate.Visible = True
            Me.colRedraftClearedDate.VisibleIndex = 33
            '
            'colBrands_Bank_Cleared_Date
            '
            Me.colBrands_Bank_Cleared_Date.Caption = "Funds Received"
            Me.colBrands_Bank_Cleared_Date.FieldName = "Brands_Bank_Cleared_Date"
            Me.colBrands_Bank_Cleared_Date.Name = "colBrands_Bank_Cleared_Date"
            Me.colBrands_Bank_Cleared_Date.Visible = True
            Me.colBrands_Bank_Cleared_Date.VisibleIndex = 34
            '
            'colMarked_Brands_Cleared_By
            '
            Me.colMarked_Brands_Cleared_By.Caption = "User"
            Me.colMarked_Brands_Cleared_By.FieldName = "Marked_Brands_Cleared_By"
            Me.colMarked_Brands_Cleared_By.Name = "colMarked_Brands_Cleared_By"
            Me.colMarked_Brands_Cleared_By.OptionsColumn.ReadOnly = True
            Me.colMarked_Brands_Cleared_By.Visible = True
            Me.colMarked_Brands_Cleared_By.VisibleIndex = 35
            '
            'colClient_Refund_Processed_Date
            '
            Me.colClient_Refund_Processed_Date.Caption = "Refund Date"
            Me.colClient_Refund_Processed_Date.FieldName = "Client_Refund_Processed_Date"
            Me.colClient_Refund_Processed_Date.Name = "colClient_Refund_Processed_Date"
            Me.colClient_Refund_Processed_Date.Visible = True
            Me.colClient_Refund_Processed_Date.VisibleIndex = 38
            '
            'colNACHAENTRY_TransNum
            '
            Me.colNACHAENTRY_TransNum.Caption = "Refund ID"
            Me.colNACHAENTRY_TransNum.FieldName = "NACHAENTRY_TransNum"
            Me.colNACHAENTRY_TransNum.Name = "colNACHAENTRY_TransNum"
            Me.colNACHAENTRY_TransNum.OptionsColumn.ReadOnly = True
            Me.colNACHAENTRY_TransNum.Visible = True
            Me.colNACHAENTRY_TransNum.VisibleIndex = 37
            '
            'colMarked_Brands_Cleared_On
            '
            Me.colMarked_Brands_Cleared_On.Caption = "Date Received"
            Me.colMarked_Brands_Cleared_On.FieldName = "Marked_Brands_Cleared_On"
            Me.colMarked_Brands_Cleared_On.Name = "colMarked_Brands_Cleared_On"
            Me.colMarked_Brands_Cleared_On.OptionsColumn.ReadOnly = True
            Me.colMarked_Brands_Cleared_On.Visible = True
            Me.colMarked_Brands_Cleared_On.VisibleIndex = 36
            '
            'RibbonControl1
            '
            Me.RibbonControl1.ExpandCollapseItem.Id = 0
            Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.RibbonControl1.SearchEditItem, Me.bbiRefresh, Me.bbiEmailClient, Me.bbiOpenCo, Me.BarButtonItem1, Me.bbiSetCoPassword, Me.bbiQtrEndHold, Me.bbiImportAchFile, Me.bbiImportNatPayFile, Me.bbiSetEmpDDType, Me.bbiOpenEmp, Me.bbiAutoEmails, Me.bbiAchSettings, Me.bbiRemoveCoPass, Me.bbiRequestWire, Me.BarSubItemRequestWireDD, Me.BarSubItemRequestWireTax, Me.bbiRequestWireDDNewTicket, Me.bbiRequestWireDDAttach, Me.bbiRequestWireTaxNewTicket, Me.bbiRequestWireTaxAttach, Me.bbiRequestWireNewTicket, Me.bbiRequestWireAttach})
            Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
            Me.RibbonControl1.MaxItemId = 23
            Me.RibbonControl1.Name = "RibbonControl1"
            Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
            Me.RibbonControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemDateEdit2, Me.RepositoryItemDateEdit3})
            Me.RibbonControl1.Size = New System.Drawing.Size(1353, 148)
            '
            'bbiRefresh
            '
            Me.bbiRefresh.Caption = "Refresh"
            Me.bbiRefresh.Id = 1
            Me.bbiRefresh.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.refresh2_16x16
            Me.bbiRefresh.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.refresh2_32x32
            Me.bbiRefresh.Name = "bbiRefresh"
            '
            'bbiEmailClient
            '
            Me.bbiEmailClient.Caption = "Email Client"
            Me.bbiEmailClient.Id = 4
            Me.bbiEmailClient.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.mail_16x16
            Me.bbiEmailClient.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.mail_32x32
            Me.bbiEmailClient.Name = "bbiEmailClient"
            '
            'bbiOpenCo
            '
            Me.bbiOpenCo.Caption = "Open Co"
            Me.bbiOpenCo.Id = 5
            Me.bbiOpenCo.ImageOptions.Image = CType(resources.GetObject("bbiOpenCo.ImageOptions.Image"), System.Drawing.Image)
            Me.bbiOpenCo.ImageOptions.LargeImage = CType(resources.GetObject("bbiOpenCo.ImageOptions.LargeImage"), System.Drawing.Image)
            Me.bbiOpenCo.Name = "bbiOpenCo"
            '
            'BarButtonItem1
            '
            Me.BarButtonItem1.Caption = "BarButtonItem1"
            Me.BarButtonItem1.Id = 6
            Me.BarButtonItem1.Name = "BarButtonItem1"
            '
            'bbiSetCoPassword
            '
            Me.bbiSetCoPassword.Caption = "Set Co Pass"
            Me.bbiSetCoPassword.Id = 1
            Me.bbiSetCoPassword.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.lock
            Me.bbiSetCoPassword.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.Lock_32x32
            Me.bbiSetCoPassword.Name = "bbiSetCoPassword"
            '
            'bbiQtrEndHold
            '
            Me.bbiQtrEndHold.Caption = "Qtr End Hold"
            Me.bbiQtrEndHold.Id = 2
            Me.bbiQtrEndHold.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.locknavigation_16x16
            Me.bbiQtrEndHold.Name = "bbiQtrEndHold"
            '
            'bbiImportAchFile
            '
            Me.bbiImportAchFile.Caption = "Ach File"
            Me.bbiImportAchFile.Id = 3
            Me.bbiImportAchFile.Name = "bbiImportAchFile"
            '
            'bbiImportNatPayFile
            '
            Me.bbiImportNatPayFile.Caption = "NatPay File"
            Me.bbiImportNatPayFile.Id = 4
            Me.bbiImportNatPayFile.Name = "bbiImportNatPayFile"
            '
            'bbiSetEmpDDType
            '
            Me.bbiSetEmpDDType.Caption = "Set Emp DD Type"
            Me.bbiSetEmpDDType.Id = 5
            Me.bbiSetEmpDDType.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.status_16x16
            Me.bbiSetEmpDDType.Name = "bbiSetEmpDDType"
            '
            'bbiOpenEmp
            '
            Me.bbiOpenEmp.Caption = "Open Emp"
            Me.bbiOpenEmp.Id = 8
            Me.bbiOpenEmp.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.customer_32x32
            Me.bbiOpenEmp.Name = "bbiOpenEmp"
            '
            'bbiAutoEmails
            '
            Me.bbiAutoEmails.Caption = "Auto Emails"
            Me.bbiAutoEmails.Id = 11
            Me.bbiAutoEmails.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.emailtemplate_32x32
            Me.bbiAutoEmails.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.emailtemplate_32x32
            Me.bbiAutoEmails.Name = "bbiAutoEmails"
            '
            'bbiAchSettings
            '
            Me.bbiAchSettings.Caption = "Settings"
            Me.bbiAchSettings.Id = 12
            Me.bbiAchSettings.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.technology_32x32
            Me.bbiAchSettings.Name = "bbiAchSettings"
            '
            'bbiRemoveCoPass
            '
            Me.bbiRemoveCoPass.Caption = "Remove Co Pass"
            Me.bbiRemoveCoPass.Id = 13
            Me.bbiRemoveCoPass.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.Unlcok_32x32
            Me.bbiRemoveCoPass.Name = "bbiRemoveCoPass"
            '
            'bbiRequestWire
            '
            Me.bbiRequestWire.ActAsDropDown = True
            Me.bbiRequestWire.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown
            Me.bbiRequestWire.Caption = "Request Wire"
            Me.bbiRequestWire.DropDownControl = Me.PopupMenuRequestWire
            Me.bbiRequestWire.Id = 14
            Me.bbiRequestWire.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.RequestWire2
            Me.bbiRequestWire.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.RequestWire2
            Me.bbiRequestWire.Name = "bbiRequestWire"
            '
            'PopupMenuRequestWire
            '
            Me.PopupMenuRequestWire.ItemLinks.Add(Me.BarSubItemRequestWireDD)
            Me.PopupMenuRequestWire.ItemLinks.Add(Me.BarSubItemRequestWireTax)
            Me.PopupMenuRequestWire.ItemLinks.Add(Me.bbiRequestWireNewTicket)
            Me.PopupMenuRequestWire.ItemLinks.Add(Me.bbiRequestWireAttach)
            Me.PopupMenuRequestWire.Name = "PopupMenuRequestWire"
            Me.PopupMenuRequestWire.Ribbon = Me.RibbonControl1
            '
            'BarSubItemRequestWireDD
            '
            Me.BarSubItemRequestWireDD.Caption = "Direct Deposit"
            Me.BarSubItemRequestWireDD.Id = 15
            Me.BarSubItemRequestWireDD.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireDDNewTicket), New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireDDAttach)})
            Me.BarSubItemRequestWireDD.Name = "BarSubItemRequestWireDD"
            Me.BarSubItemRequestWireDD.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
            '
            'bbiRequestWireDDNewTicket
            '
            Me.bbiRequestWireDDNewTicket.Caption = "Start Ticket In Zendesk"
            Me.bbiRequestWireDDNewTicket.Id = 17
            Me.bbiRequestWireDDNewTicket.Name = "bbiRequestWireDDNewTicket"
            '
            'bbiRequestWireDDAttach
            '
            Me.bbiRequestWireDDAttach.Caption = "Attach To Ticket In Zendesk"
            Me.bbiRequestWireDDAttach.Id = 18
            Me.bbiRequestWireDDAttach.Name = "bbiRequestWireDDAttach"
            '
            'BarSubItemRequestWireTax
            '
            Me.BarSubItemRequestWireTax.Caption = "Tax"
            Me.BarSubItemRequestWireTax.Id = 16
            Me.BarSubItemRequestWireTax.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireTaxNewTicket), New DevExpress.XtraBars.LinkPersistInfo(Me.bbiRequestWireTaxAttach)})
            Me.BarSubItemRequestWireTax.Name = "BarSubItemRequestWireTax"
            Me.BarSubItemRequestWireTax.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
            '
            'bbiRequestWireTaxNewTicket
            '
            Me.bbiRequestWireTaxNewTicket.Caption = "Start Ticket In Zendesk"
            Me.bbiRequestWireTaxNewTicket.Id = 19
            Me.bbiRequestWireTaxNewTicket.Name = "bbiRequestWireTaxNewTicket"
            '
            'bbiRequestWireTaxAttach
            '
            Me.bbiRequestWireTaxAttach.Caption = "Attach To Ticket In Zendesk"
            Me.bbiRequestWireTaxAttach.Id = 20
            Me.bbiRequestWireTaxAttach.Name = "bbiRequestWireTaxAttach"
            '
            'bbiRequestWireNewTicket
            '
            Me.bbiRequestWireNewTicket.Caption = "Start Ticket In Zendesk"
            Me.bbiRequestWireNewTicket.Id = 21
            Me.bbiRequestWireNewTicket.Name = "bbiRequestWireNewTicket"
            '
            'bbiRequestWireAttach
            '
            Me.bbiRequestWireAttach.Caption = "Attach To Ticket In Zendesk"
            Me.bbiRequestWireAttach.Id = 22
            Me.bbiRequestWireAttach.Name = "bbiRequestWireAttach"
            '
            'RibbonPage1
            '
            Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1, Me.rpgCoOptions})
            Me.RibbonPage1.Name = "RibbonPage1"
            Me.RibbonPage1.Text = "Home"
            '
            'RibbonPageGroup1
            '
            Me.RibbonPageGroup1.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.[False]
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRefresh)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiAutoEmails)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRequestWire)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiAchSettings)
            Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
            Me.RibbonPageGroup1.Text = "Actions"
            '
            'rpgCoOptions
            '
            Me.rpgCoOptions.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.[False]
            Me.rpgCoOptions.Enabled = False
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiEmailClient)
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiOpenCo)
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiOpenEmp)
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiSetCoPassword)
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiRemoveCoPass)
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiQtrEndHold)
            Me.rpgCoOptions.ItemLinks.Add(Me.bbiSetEmpDDType)
            Me.rpgCoOptions.Name = "rpgCoOptions"
            Me.rpgCoOptions.Text = "Co Options"
            '
            'RepositoryItemDateEdit2
            '
            Me.RepositoryItemDateEdit2.AutoHeight = False
            Me.RepositoryItemDateEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.RepositoryItemDateEdit2.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.RepositoryItemDateEdit2.Name = "RepositoryItemDateEdit2"
            '
            'RepositoryItemDateEdit3
            '
            Me.RepositoryItemDateEdit3.AutoHeight = False
            Me.RepositoryItemDateEdit3.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.RepositoryItemDateEdit3.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.RepositoryItemDateEdit3.Name = "RepositoryItemDateEdit3"
            '
            'riTeCoNmAutoFilter
            '
            Me.riTeCoNmAutoFilter.AutoHeight = False
            Me.riTeCoNmAutoFilter.Name = "riTeCoNmAutoFilter"
            '
            'riLuePrNum
            '
            Me.riLuePrNum.AutoHeight = False
            Me.riLuePrNum.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riLuePrNum.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("PrNum", "Pr#"), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("CheckDate", "Check Date", 20, DevExpress.Utils.FormatType.DateTime, "d", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
            Me.riLuePrNum.DisplayMember = "Display"
            Me.riLuePrNum.Name = "riLuePrNum"
            Me.riLuePrNum.NullText = ""
            Me.riLuePrNum.ValueMember = "PRNUM"
            '
            'riEmpNum
            '
            Me.riEmpNum.AllowNullInput = DevExpress.Utils.DefaultBoolean.[True]
            Me.riEmpNum.AutoHeight = False
            Me.riEmpNum.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.riEmpNum.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("EMPNUM", "EMPNUM", 52, DevExpress.Utils.FormatType.Numeric, "", True, DevExpress.Utils.HorzAlignment.Far, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("F_NAME", "F_NAME", 50, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("M_NAME", "M_NAME", 25, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("L_NAME", "L_NAME", 49, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.Near, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("TERM_DATE", "Term Date", 50, DevExpress.Utils.FormatType.DateTime, "d", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
            Me.riEmpNum.DataSource = Me.EMPLOYEEBindingSource
            Me.riEmpNum.DisplayMember = "EMPNUM"
            Me.riEmpNum.KeyMember = "EMPNUM"
            Me.riEmpNum.Name = "riEmpNum"
            Me.riEmpNum.NullText = ""
            Me.riEmpNum.PopupFormMinSize = New System.Drawing.Size(400, 0)
            Me.riEmpNum.PopupWidth = 400
            Me.riEmpNum.ValueMember = "EMPNUM"
            '
            'EMPLOYEEBindingSource
            '
            Me.EMPLOYEEBindingSource.DataSource = GetType(Brands_FrontDesk.EMPLOYEE)
            '
            'riTeReadOnly
            '
            Me.riTeReadOnly.AutoHeight = False
            Me.riTeReadOnly.Name = "riTeReadOnly"
            Me.riTeReadOnly.ReadOnly = True
            '
            'LayoutViewCard2
            '
            Me.LayoutViewCard2.CustomizationFormText = "TemplateCard"
            Me.LayoutViewCard2.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText
            Me.LayoutViewCard2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.item1})
            Me.LayoutViewCard2.Name = "LayoutViewCard2"
            Me.LayoutViewCard2.OptionsItemText.TextToControlDistance = 5
            Me.LayoutViewCard2.Text = "TemplateCard"
            '
            'item1
            '
            Me.item1.AllowHotTrack = False
            Me.item1.CustomizationFormText = "item1"
            Me.item1.Location = New System.Drawing.Point(0, 0)
            Me.item1.Name = "item1"
            Me.item1.Size = New System.Drawing.Size(938, 192)
            Me.item1.TextSize = New System.Drawing.Size(0, 0)
            '
            'LayoutViewCard1
            '
            Me.LayoutViewCard1.CustomizationFormText = "TemplateCard"
            Me.LayoutViewCard1.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText
            Me.LayoutViewCard1.Name = "layoutViewTemplateCard"
            Me.LayoutViewCard1.OptionsItemText.TextToControlDistance = 5
            Me.LayoutViewCard1.Text = "TemplateCard"
            '
            'lcRoot
            '
            Me.lcRoot.Controls.Add(Me.GridControlRelatedCompanies)
            Me.lcRoot.Controls.Add(Me.UcCompInfo1)
            Me.lcRoot.Controls.Add(Me.UcFaxAndEmailGrid1)
            Me.lcRoot.Controls.Add(Me.CheckEdit1)
            Me.lcRoot.Controls.Add(Me.TextEdit3)
            Me.lcRoot.Controls.Add(Me.TextEdit2)
            Me.lcRoot.Controls.Add(Me.DateEdit2)
            Me.lcRoot.Controls.Add(Me.DateEdit1)
            Me.lcRoot.Controls.Add(Me.TextEdit1)
            Me.lcRoot.Controls.Add(Me.meAchLog)
            Me.lcRoot.Controls.Add(Me.MemoEdit1)
            Me.lcRoot.Controls.Add(Me.btnNoReturnsExist)
            Me.lcRoot.Controls.Add(Me.btnRefreshImportLog)
            Me.lcRoot.Controls.Add(Me.btnImport)
            Me.lcRoot.Controls.Add(Me.cbeFileType)
            Me.lcRoot.Controls.Add(Me.beFilePath)
            Me.lcRoot.Controls.Add(Me.deTransactionsDate)
            Me.lcRoot.Controls.Add(Me.GridControl2)
            Me.lcRoot.Controls.Add(Me.deToDate)
            Me.lcRoot.Controls.Add(Me.deFromDate)
            Me.lcRoot.Controls.Add(Me.ccbeStatusFilter)
            Me.lcRoot.Controls.Add(Me.gcImportedData)
            Me.lcRoot.Controls.Add(Me.GridControl1)
            Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
            Me.lcRoot.Location = New System.Drawing.Point(0, 148)
            Me.lcRoot.Name = "lcRoot"
            Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(136, 322, 453, 411)
            Me.lcRoot.Root = Me.LayoutControlGroup1
            Me.lcRoot.Size = New System.Drawing.Size(1353, 685)
            Me.lcRoot.TabIndex = 2
            Me.lcRoot.Text = "LayoutControl1"
            '
            'GridControlRelatedCompanies
            '
            Me.GridControlRelatedCompanies.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
            Me.GridControlRelatedCompanies.Location = New System.Drawing.Point(888, 532)
            Me.GridControlRelatedCompanies.MainView = Me.GridViewRelatedCompanies
            Me.GridControlRelatedCompanies.Name = "GridControlRelatedCompanies"
            Me.GridControlRelatedCompanies.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riButtonEditCoNum})
            Me.GridControlRelatedCompanies.Size = New System.Drawing.Size(422, 110)
            Me.GridControlRelatedCompanies.TabIndex = 1
            Me.GridControlRelatedCompanies.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewRelatedCompanies})
            '
            'GridViewRelatedCompanies
            '
            Me.GridViewRelatedCompanies.Appearance.ViewCaption.Options.UseTextOptions = True
            Me.GridViewRelatedCompanies.Appearance.ViewCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
            Me.GridViewRelatedCompanies.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2})
            Me.GridViewRelatedCompanies.GridControl = Me.GridControlRelatedCompanies
            Me.GridViewRelatedCompanies.LevelIndent = 0
            Me.GridViewRelatedCompanies.Name = "GridViewRelatedCompanies"
            Me.GridViewRelatedCompanies.OptionsBehavior.ReadOnly = True
            Me.GridViewRelatedCompanies.OptionsSelection.EnableAppearanceFocusedCell = False
            Me.GridViewRelatedCompanies.OptionsSelection.EnableAppearanceFocusedRow = False
            Me.GridViewRelatedCompanies.OptionsView.ColumnAutoWidth = False
            Me.GridViewRelatedCompanies.OptionsView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways
            Me.GridViewRelatedCompanies.OptionsView.ShowGroupPanel = False
            Me.GridViewRelatedCompanies.OptionsView.ShowIndicator = False
            Me.GridViewRelatedCompanies.OptionsView.ShowViewCaption = True
            Me.GridViewRelatedCompanies.PreviewIndent = 0
            Me.GridViewRelatedCompanies.ViewCaption = "Related Companies"
            '
            'GridColumn1
            '
            Me.GridColumn1.Caption = "Co #"
            Me.GridColumn1.ColumnEdit = Me.riButtonEditCoNum
            Me.GridColumn1.FieldName = "CONUM"
            Me.GridColumn1.Name = "GridColumn1"
            Me.GridColumn1.Visible = True
            Me.GridColumn1.VisibleIndex = 0
            '
            'riButtonEditCoNum
            '
            Me.riButtonEditCoNum.AutoHeight = False
            Me.riButtonEditCoNum.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Search)})
            Me.riButtonEditCoNum.Name = "riButtonEditCoNum"
            '
            'GridColumn2
            '
            Me.GridColumn2.Caption = "Co Name"
            Me.GridColumn2.FieldName = "CO_NAME"
            Me.GridColumn2.Name = "GridColumn2"
            Me.GridColumn2.Visible = True
            Me.GridColumn2.VisibleIndex = 1
            '
            'UcCompInfo1
            '
            Me.UcCompInfo1.Location = New System.Drawing.Point(876, 51)
            Me.UcCompInfo1.Margin = New System.Windows.Forms.Padding(0)
            Me.UcCompInfo1.Name = "UcCompInfo1"
            Me.UcCompInfo1.Size = New System.Drawing.Size(446, 436)
            Me.UcCompInfo1.TabIndex = 6
            '
            'UcFaxAndEmailGrid1
            '
            Me.UcFaxAndEmailGrid1._AssignedTo = Brands_FrontDesk.ucFaxAndEmailGrid.AssignedTo.CS
            Me.UcFaxAndEmailGrid1._Category = "NSF"
            Me.UcFaxAndEmailGrid1._CategoryAllowEdit = False
            Me.UcFaxAndEmailGrid1._ColCategoryVisible = False
            Me.UcFaxAndEmailGrid1._ColDeliveryVisible = False
            Me.UcFaxAndEmailGrid1._ColIsDoneVisible = True
            Me.UcFaxAndEmailGrid1._ColIsOutboundVisible = True
            Me.UcFaxAndEmailGrid1._ColLastSpecialistVisible = True
            Me.UcFaxAndEmailGrid1._ColSpecialistOnlyVisible = False
            Me.UcFaxAndEmailGrid1._ColSubjectVisible = True
            Me.UcFaxAndEmailGrid1._ColTicketNumberVisible = True
            Me.UcFaxAndEmailGrid1._FormType = Brands_FrontDesk.ucFaxAndEmailGrid.FormType.Category
            Me.UcFaxAndEmailGrid1._GridFilter = ""
            Me.UcFaxAndEmailGrid1._ShowAcquireAndReleaseButtons = False
            Me.UcFaxAndEmailGrid1._ShowButtonOptions = False
            Me.UcFaxAndEmailGrid1._ShowFilters = False
            Me.UcFaxAndEmailGrid1._ViewCaption = "NSF"
            Me.UcFaxAndEmailGrid1.Location = New System.Drawing.Point(29, 498)
            Me.UcFaxAndEmailGrid1.Name = "UcFaxAndEmailGrid1"
            Me.UcFaxAndEmailGrid1.RefreshButtonAction = Nothing
            Me.UcFaxAndEmailGrid1.Size = New System.Drawing.Size(802, 158)
            Me.UcFaxAndEmailGrid1.TabIndex = 8
            '
            'CheckEdit1
            '
            Me.CheckEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.AchTransactionDailyLogBindingSource, "IsCompleted", True))
            Me.CheckEdit1.Location = New System.Drawing.Point(1218, 556)
            Me.CheckEdit1.MenuManager = Me.RibbonControl1
            Me.CheckEdit1.Name = "CheckEdit1"
            Me.CheckEdit1.Properties.Caption = "Is Completed"
            Me.CheckEdit1.Properties.ReadOnly = True
            Me.CheckEdit1.Size = New System.Drawing.Size(92, 19)
            Me.CheckEdit1.StyleController = Me.lcRoot
            Me.CheckEdit1.TabIndex = 36
            '
            'AchTransactionDailyLogBindingSource
            '
            Me.AchTransactionDailyLogBindingSource.DataSource = GetType(Brands_FrontDesk.AchTransactionDailyLog)
            '
            'TextEdit3
            '
            Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.AchTransactionDailyLogBindingSource, "FilePath", True))
            Me.TextEdit3.Location = New System.Drawing.Point(946, 556)
            Me.TextEdit3.MenuManager = Me.RibbonControl1
            Me.TextEdit3.Name = "TextEdit3"
            Me.TextEdit3.Properties.ReadOnly = True
            Me.TextEdit3.Size = New System.Drawing.Size(268, 20)
            Me.TextEdit3.StyleController = Me.lcRoot
            Me.TextEdit3.TabIndex = 35
            '
            'TextEdit2
            '
            Me.TextEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.AchTransactionDailyLogBindingSource, "FileType", True))
            Me.TextEdit2.Location = New System.Drawing.Point(1158, 532)
            Me.TextEdit2.MenuManager = Me.RibbonControl1
            Me.TextEdit2.Name = "TextEdit2"
            Me.TextEdit2.Properties.ReadOnly = True
            Me.TextEdit2.Size = New System.Drawing.Size(152, 20)
            Me.TextEdit2.StyleController = Me.lcRoot
            Me.TextEdit2.TabIndex = 34
            '
            'DateEdit2
            '
            Me.DateEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.AchTransactionDailyLogBindingSource, "TransactionsDate", True))
            Me.DateEdit2.EditValue = Nothing
            Me.DateEdit2.Location = New System.Drawing.Point(946, 532)
            Me.DateEdit2.MenuManager = Me.RibbonControl1
            Me.DateEdit2.Name = "DateEdit2"
            Me.DateEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.DateEdit2.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.DateEdit2.Properties.ReadOnly = True
            Me.DateEdit2.Size = New System.Drawing.Size(150, 20)
            Me.DateEdit2.StyleController = Me.lcRoot
            Me.DateEdit2.TabIndex = 33
            '
            'DateEdit1
            '
            Me.DateEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.AchTransactionDailyLogBindingSource, "AddDateTime", True))
            Me.DateEdit1.EditValue = Nothing
            Me.DateEdit1.Location = New System.Drawing.Point(1159, 580)
            Me.DateEdit1.MenuManager = Me.RibbonControl1
            Me.DateEdit1.Name = "DateEdit1"
            Me.DateEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.DateEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.DateEdit1.Properties.ReadOnly = True
            Me.DateEdit1.Size = New System.Drawing.Size(151, 20)
            Me.DateEdit1.StyleController = Me.lcRoot
            Me.DateEdit1.TabIndex = 32
            '
            'TextEdit1
            '
            Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.AchTransactionDailyLogBindingSource, "AddUser", True))
            Me.TextEdit1.Location = New System.Drawing.Point(946, 580)
            Me.TextEdit1.MenuManager = Me.RibbonControl1
            Me.TextEdit1.Name = "TextEdit1"
            Me.TextEdit1.Properties.ReadOnly = True
            Me.TextEdit1.Size = New System.Drawing.Size(151, 20)
            Me.TextEdit1.StyleController = Me.lcRoot
            Me.TextEdit1.TabIndex = 31
            '
            'meAchLog
            '
            Me.meAchLog.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAch, "Log", True))
            Me.meAchLog.Location = New System.Drawing.Point(888, 532)
            Me.meAchLog.MenuManager = Me.RibbonControl1
            Me.meAchLog.Name = "meAchLog"
            Me.meAchLog.Properties.ReadOnly = True
            Me.meAchLog.Size = New System.Drawing.Size(422, 110)
            Me.meAchLog.StyleController = Me.lcRoot
            Me.meAchLog.TabIndex = 8
            '
            'MemoEdit1
            '
            Me.MemoEdit1.Location = New System.Drawing.Point(888, 532)
            Me.MemoEdit1.MenuManager = Me.RibbonControl1
            Me.MemoEdit1.Name = "MemoEdit1"
            Me.MemoEdit1.Properties.ReadOnly = True
            Me.MemoEdit1.Size = New System.Drawing.Size(422, 110)
            Me.MemoEdit1.StyleController = Me.lcRoot
            Me.MemoEdit1.TabIndex = 7
            '
            'btnNoReturnsExist
            '
            Me.btnNoReturnsExist.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.cancel_16x16
            Me.btnNoReturnsExist.Location = New System.Drawing.Point(1208, 77)
            Me.btnNoReturnsExist.Name = "btnNoReturnsExist"
            Me.btnNoReturnsExist.Size = New System.Drawing.Size(109, 22)
            Me.btnNoReturnsExist.StyleController = Me.lcRoot
            Me.btnNoReturnsExist.TabIndex = 30
            Me.btnNoReturnsExist.Text = "No Returns Exist"
            '
            'btnRefreshImportLog
            '
            Me.btnRefreshImportLog.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.refresh_16x16
            Me.btnRefreshImportLog.Location = New System.Drawing.Point(1254, 45)
            Me.btnRefreshImportLog.Name = "btnRefreshImportLog"
            Me.btnRefreshImportLog.Size = New System.Drawing.Size(75, 22)
            Me.btnRefreshImportLog.StyleController = Me.lcRoot
            Me.btnRefreshImportLog.TabIndex = 29
            Me.btnRefreshImportLog.Text = "Refresh"
            '
            'btnImport
            '
            Me.btnImport.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.redo_16x16
            Me.btnImport.Location = New System.Drawing.Point(1143, 77)
            Me.btnImport.Name = "btnImport"
            Me.btnImport.Size = New System.Drawing.Size(61, 22)
            Me.btnImport.StyleController = Me.lcRoot
            Me.btnImport.TabIndex = 28
            Me.btnImport.Text = "Import"
            '
            'cbeFileType
            '
            Me.cbeFileType.Location = New System.Drawing.Point(781, 77)
            Me.cbeFileType.MenuManager = Me.RibbonControl1
            Me.cbeFileType.Name = "cbeFileType"
            Me.cbeFileType.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.cbeFileType.Properties.Items.AddRange(New Object() {"Ach", "NatPay"})
            Me.cbeFileType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
            Me.cbeFileType.Size = New System.Drawing.Size(122, 20)
            Me.cbeFileType.StyleController = Me.lcRoot
            Me.cbeFileType.TabIndex = 27
            '
            'beFilePath
            '
            Me.beFilePath.Location = New System.Drawing.Point(133, 77)
            Me.beFilePath.MenuManager = Me.RibbonControl1
            Me.beFilePath.Name = "beFilePath"
            Me.beFilePath.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
            Me.beFilePath.Properties.ReadOnly = True
            Me.beFilePath.Size = New System.Drawing.Size(547, 20)
            Me.beFilePath.StyleController = Me.lcRoot
            Me.beFilePath.TabIndex = 26
            '
            'deTransactionsDate
            '
            Me.deTransactionsDate.EditValue = Nothing
            Me.deTransactionsDate.Location = New System.Drawing.Point(1004, 77)
            Me.deTransactionsDate.MenuManager = Me.RibbonControl1
            Me.deTransactionsDate.Name = "deTransactionsDate"
            Me.deTransactionsDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deTransactionsDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deTransactionsDate.Size = New System.Drawing.Size(125, 20)
            Me.deTransactionsDate.StyleController = Me.lcRoot
            Me.deTransactionsDate.TabIndex = 25
            '
            'GridControl2
            '
            Me.GridControl2.DataSource = Me.achTransactionDailyLogsBindingSource
            Me.GridControl2.Location = New System.Drawing.Point(24, 71)
            Me.GridControl2.MainView = Me.GridView2
            Me.GridControl2.MenuManager = Me.RibbonControl1
            Me.GridControl2.Name = "GridControl2"
            Me.GridControl2.Size = New System.Drawing.Size(1305, 590)
            Me.GridControl2.TabIndex = 24
            Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
            '
            'achTransactionDailyLogsBindingSource
            '
            Me.achTransactionDailyLogsBindingSource.AllowNew = False
            Me.achTransactionDailyLogsBindingSource.DataSource = GetType(Brands_FrontDesk.AchTransactionDailyLog)
            '
            'GridView2
            '
            Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colId1, Me.colTransactionsDate, Me.colFilePath, Me.colFileType, Me.colIsCompleted, Me.colAddUser1, Me.colAddDateTime1})
            Me.GridView2.GridControl = Me.GridControl2
            Me.GridView2.Name = "GridView2"
            Me.GridView2.OptionsBehavior.Editable = False
            Me.GridView2.OptionsDetail.EnableMasterViewMode = False
            Me.GridView2.OptionsView.EnableAppearanceEvenRow = True
            Me.GridView2.OptionsView.EnableAppearanceOddRow = True
            Me.GridView2.OptionsView.ShowGroupPanel = False
            Me.GridView2.OptionsView.ShowIndicator = False
            '
            'colId1
            '
            Me.colId1.FieldName = "Id"
            Me.colId1.Name = "colId1"
            '
            'colTransactionsDate
            '
            Me.colTransactionsDate.FieldName = "TransactionsDate"
            Me.colTransactionsDate.Name = "colTransactionsDate"
            Me.colTransactionsDate.Visible = True
            Me.colTransactionsDate.VisibleIndex = 0
            '
            'colFilePath
            '
            Me.colFilePath.FieldName = "FilePath"
            Me.colFilePath.Name = "colFilePath"
            Me.colFilePath.Visible = True
            Me.colFilePath.VisibleIndex = 1
            '
            'colFileType
            '
            Me.colFileType.FieldName = "FileType"
            Me.colFileType.Name = "colFileType"
            Me.colFileType.Visible = True
            Me.colFileType.VisibleIndex = 2
            '
            'colIsCompleted
            '
            Me.colIsCompleted.FieldName = "IsCompleted"
            Me.colIsCompleted.Name = "colIsCompleted"
            Me.colIsCompleted.Visible = True
            Me.colIsCompleted.VisibleIndex = 3
            '
            'colAddUser1
            '
            Me.colAddUser1.FieldName = "AddUser"
            Me.colAddUser1.Name = "colAddUser1"
            Me.colAddUser1.Visible = True
            Me.colAddUser1.VisibleIndex = 4
            '
            'colAddDateTime1
            '
            Me.colAddDateTime1.FieldName = "AddDateTime"
            Me.colAddDateTime1.Name = "colAddDateTime1"
            Me.colAddDateTime1.Visible = True
            Me.colAddDateTime1.VisibleIndex = 5
            '
            'deToDate
            '
            Me.deToDate.EditValue = Nothing
            Me.deToDate.Location = New System.Drawing.Point(668, 45)
            Me.deToDate.MenuManager = Me.RibbonControl1
            Me.deToDate.Name = "deToDate"
            Me.deToDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deToDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deToDate.Size = New System.Drawing.Size(105, 20)
            Me.deToDate.StyleController = Me.lcRoot
            Me.deToDate.TabIndex = 13
            '
            'deFromDate
            '
            Me.deFromDate.EditValue = Nothing
            Me.deFromDate.Location = New System.Drawing.Point(496, 45)
            Me.deFromDate.MenuManager = Me.RibbonControl1
            Me.deFromDate.Name = "deFromDate"
            Me.deFromDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deFromDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deFromDate.Size = New System.Drawing.Size(118, 20)
            Me.deFromDate.StyleController = Me.lcRoot
            Me.deFromDate.TabIndex = 12
            '
            'ccbeStatusFilter
            '
            Me.ccbeStatusFilter.Location = New System.Drawing.Point(67, 45)
            Me.ccbeStatusFilter.MenuManager = Me.RibbonControl1
            Me.ccbeStatusFilter.Name = "ccbeStatusFilter"
            Me.ccbeStatusFilter.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.ccbeStatusFilter.Size = New System.Drawing.Size(363, 20)
            Me.ccbeStatusFilter.StyleController = Me.lcRoot
            Me.ccbeStatusFilter.TabIndex = 11
            '
            'gcImportedData
            '
            Me.gcImportedData.DataSource = Me.BindingSource1
            Me.gcImportedData.Location = New System.Drawing.Point(24, 115)
            Me.gcImportedData.MainView = Me.gvImportedData
            Me.gcImportedData.MenuManager = Me.RibbonControl1
            Me.gcImportedData.Name = "gcImportedData"
            Me.gcImportedData.Size = New System.Drawing.Size(1305, 546)
            Me.gcImportedData.TabIndex = 9
            Me.gcImportedData.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvImportedData})
            '
            'BindingSource1
            '
            Me.BindingSource1.DataSource = GetType(Brands_FrontDesk.AchTransactionsLog)
            '
            'gvImportedData
            '
            Me.gvImportedData.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colDateReceived, Me.colEntryDescType1, Me.colRejectionCode, Me.colDebitAmount, Me.colCreditAmount, Me.colEffectiveDate, Me.colName1, Me.colImportedRoutingNumber1, Me.colImportedAccountNumber1, Me.colImportedAccountType1, Me.colImportedAction1, Me.colCorrectedData1})
            Me.gvImportedData.GridControl = Me.gcImportedData
            Me.gvImportedData.Name = "gvImportedData"
            Me.gvImportedData.OptionsBehavior.Editable = False
            Me.gvImportedData.OptionsView.ColumnAutoWidth = False
            Me.gvImportedData.OptionsView.EnableAppearanceEvenRow = True
            Me.gvImportedData.OptionsView.EnableAppearanceOddRow = True
            Me.gvImportedData.OptionsView.ShowGroupPanel = False
            Me.gvImportedData.OptionsView.ShowIndicator = False
            '
            'colDateReceived
            '
            Me.colDateReceived.FieldName = "DateReceived"
            Me.colDateReceived.Name = "colDateReceived"
            Me.colDateReceived.Visible = True
            Me.colDateReceived.VisibleIndex = 0
            '
            'colEntryDescType1
            '
            Me.colEntryDescType1.FieldName = "EntryDescType"
            Me.colEntryDescType1.Name = "colEntryDescType1"
            Me.colEntryDescType1.Visible = True
            Me.colEntryDescType1.VisibleIndex = 1
            '
            'colRejectionCode
            '
            Me.colRejectionCode.FieldName = "RejectionCode"
            Me.colRejectionCode.Name = "colRejectionCode"
            Me.colRejectionCode.Visible = True
            Me.colRejectionCode.VisibleIndex = 2
            '
            'colDebitAmount
            '
            Me.colDebitAmount.FieldName = "DebitAmount"
            Me.colDebitAmount.Name = "colDebitAmount"
            Me.colDebitAmount.Visible = True
            Me.colDebitAmount.VisibleIndex = 3
            '
            'colCreditAmount
            '
            Me.colCreditAmount.FieldName = "CreditAmount"
            Me.colCreditAmount.Name = "colCreditAmount"
            Me.colCreditAmount.Visible = True
            Me.colCreditAmount.VisibleIndex = 4
            '
            'colEffectiveDate
            '
            Me.colEffectiveDate.FieldName = "EffectiveDate"
            Me.colEffectiveDate.Name = "colEffectiveDate"
            Me.colEffectiveDate.Visible = True
            Me.colEffectiveDate.VisibleIndex = 5
            '
            'colName1
            '
            Me.colName1.FieldName = "Name"
            Me.colName1.Name = "colName1"
            Me.colName1.Visible = True
            Me.colName1.VisibleIndex = 6
            '
            'colImportedRoutingNumber1
            '
            Me.colImportedRoutingNumber1.FieldName = "ImportedRoutingNumber"
            Me.colImportedRoutingNumber1.Name = "colImportedRoutingNumber1"
            Me.colImportedRoutingNumber1.Visible = True
            Me.colImportedRoutingNumber1.VisibleIndex = 7
            '
            'colImportedAccountNumber1
            '
            Me.colImportedAccountNumber1.FieldName = "ImportedAccountNumber"
            Me.colImportedAccountNumber1.Name = "colImportedAccountNumber1"
            Me.colImportedAccountNumber1.Visible = True
            Me.colImportedAccountNumber1.VisibleIndex = 8
            '
            'colImportedAccountType1
            '
            Me.colImportedAccountType1.FieldName = "ImportedAccountType"
            Me.colImportedAccountType1.Name = "colImportedAccountType1"
            Me.colImportedAccountType1.Visible = True
            Me.colImportedAccountType1.VisibleIndex = 9
            '
            'colImportedAction1
            '
            Me.colImportedAction1.FieldName = "ImportedAction"
            Me.colImportedAction1.Name = "colImportedAction1"
            Me.colImportedAction1.Visible = True
            Me.colImportedAction1.VisibleIndex = 10
            '
            'colCorrectedData1
            '
            Me.colCorrectedData1.FieldName = "CorrectedData"
            Me.colCorrectedData1.Name = "colCorrectedData1"
            Me.colCorrectedData1.Visible = True
            Me.colCorrectedData1.VisibleIndex = 11
            '
            'LayoutControlGroup1
            '
            Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
            Me.LayoutControlGroup1.GroupBordersVisible = False
            Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.TabbedControlGroup1})
            Me.LayoutControlGroup1.Name = "Root"
            Me.LayoutControlGroup1.Size = New System.Drawing.Size(1353, 685)
            Me.LayoutControlGroup1.TextVisible = False
            '
            'TabbedControlGroup1
            '
            Me.TabbedControlGroup1.Location = New System.Drawing.Point(0, 0)
            Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
            Me.TabbedControlGroup1.SelectedTabPage = Me.LayoutControlGroup2
            Me.TabbedControlGroup1.Size = New System.Drawing.Size(1333, 665)
            Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2, Me.LayoutControlGroup3, Me.LayoutControlGroup12})
            '
            'LayoutControlGroup2
            '
            Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlGroup4, Me.SplitterItem2, Me.LayoutControlGroup5, Me.SplitterItem3, Me.LayoutControlItem7, Me.EmptySpaceItem1, Me.LayoutControlItem8, Me.LayoutControlItem9})
            Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
            Me.LayoutControlGroup2.Size = New System.Drawing.Size(1309, 620)
            Me.LayoutControlGroup2.Text = "Saved Transactions"
            '
            'LayoutControlItem1
            '
            Me.LayoutControlItem1.Control = Me.GridControl1
            Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 24)
            Me.LayoutControlItem1.Name = "LayoutControlItem1"
            Me.LayoutControlItem1.Size = New System.Drawing.Size(816, 396)
            Me.LayoutControlItem1.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem1.TextVisible = False
            '
            'LayoutControlGroup4
            '
            Me.LayoutControlGroup4.AppearanceGroup.Font = New System.Drawing.Font("Tahoma", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            Me.LayoutControlGroup4.AppearanceGroup.Options.UseFont = True
            Me.LayoutControlGroup4.AppearanceGroup.Options.UseTextOptions = True
            Me.LayoutControlGroup4.AppearanceGroup.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            Me.LayoutControlGroup4.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted
            Me.LayoutControlGroup4.ExpandButtonVisible = True
            Me.LayoutControlGroup4.ExpandOnDoubleClick = True
            Me.LayoutControlGroup4.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText
            Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem10, Me.TabbedControlGroup2, Me.SplitterItem1})
            Me.LayoutControlGroup4.Location = New System.Drawing.Point(824, 0)
            Me.LayoutControlGroup4.Name = "LayoutControlGroup4"
            Me.LayoutControlGroup4.Padding = New DevExpress.XtraLayout.Utils.Padding(4, 4, 4, 4)
            Me.LayoutControlGroup4.Size = New System.Drawing.Size(485, 620)
            Me.LayoutControlGroup4.Text = "Company Info"
            Me.LayoutControlGroup4.TextLocation = DevExpress.Utils.Locations.Left
            '
            'LayoutControlItem10
            '
            Me.LayoutControlItem10.Control = Me.UcCompInfo1
            Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem10.Name = "LayoutControlItem10"
            Me.LayoutControlItem10.Size = New System.Drawing.Size(450, 440)
            Me.LayoutControlItem10.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem10.TextVisible = False
            '
            'TabbedControlGroup2
            '
            Me.TabbedControlGroup2.Location = New System.Drawing.Point(0, 448)
            Me.TabbedControlGroup2.Name = "TabbedControlGroup2"
            Me.TabbedControlGroup2.SelectedTabPage = Me.LayoutControlGroup8
            Me.TabbedControlGroup2.Size = New System.Drawing.Size(450, 159)
            Me.TabbedControlGroup2.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup6, Me.LayoutControlGroup7, Me.LayoutControlGroup8, Me.lciRelatedComp})
            '
            'LayoutControlGroup8
            '
            Me.LayoutControlGroup8.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem6, Me.LayoutControlItem18, Me.LayoutControlItem19, Me.LayoutControlItem20, Me.LayoutControlItem22, Me.EmptySpaceItem4, Me.LayoutControlItem17})
            Me.LayoutControlGroup8.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup8.Name = "LayoutControlGroup8"
            Me.LayoutControlGroup8.Size = New System.Drawing.Size(426, 114)
            Me.LayoutControlGroup8.Text = "Import Info"
            '
            'LayoutControlItem6
            '
            Me.LayoutControlItem6.Control = Me.TextEdit1
            Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 48)
            Me.LayoutControlItem6.Name = "LayoutControlItem6"
            Me.LayoutControlItem6.Size = New System.Drawing.Size(213, 24)
            Me.LayoutControlItem6.Text = "By User: "
            Me.LayoutControlItem6.TextSize = New System.Drawing.Size(55, 13)
            '
            'LayoutControlItem18
            '
            Me.LayoutControlItem18.Control = Me.DateEdit2
            Me.LayoutControlItem18.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem18.Name = "LayoutControlItem18"
            Me.LayoutControlItem18.Size = New System.Drawing.Size(212, 24)
            Me.LayoutControlItem18.Text = "Tran Date: "
            Me.LayoutControlItem18.TextSize = New System.Drawing.Size(55, 13)
            '
            'LayoutControlItem19
            '
            Me.LayoutControlItem19.Control = Me.TextEdit2
            Me.LayoutControlItem19.Location = New System.Drawing.Point(212, 0)
            Me.LayoutControlItem19.Name = "LayoutControlItem19"
            Me.LayoutControlItem19.Size = New System.Drawing.Size(214, 24)
            Me.LayoutControlItem19.Text = "File Type: "
            Me.LayoutControlItem19.TextSize = New System.Drawing.Size(55, 13)
            '
            'LayoutControlItem20
            '
            Me.LayoutControlItem20.Control = Me.TextEdit3
            Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 24)
            Me.LayoutControlItem20.Name = "LayoutControlItem20"
            Me.LayoutControlItem20.Size = New System.Drawing.Size(330, 24)
            Me.LayoutControlItem20.Text = "File Path: "
            Me.LayoutControlItem20.TextSize = New System.Drawing.Size(55, 13)
            '
            'LayoutControlItem22
            '
            Me.LayoutControlItem22.Control = Me.CheckEdit1
            Me.LayoutControlItem22.Location = New System.Drawing.Point(330, 24)
            Me.LayoutControlItem22.MaxSize = New System.Drawing.Size(96, 24)
            Me.LayoutControlItem22.MinSize = New System.Drawing.Size(96, 24)
            Me.LayoutControlItem22.Name = "LayoutControlItem22"
            Me.LayoutControlItem22.Size = New System.Drawing.Size(96, 24)
            Me.LayoutControlItem22.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
            Me.LayoutControlItem22.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem22.TextVisible = False
            '
            'EmptySpaceItem4
            '
            Me.EmptySpaceItem4.AllowHotTrack = False
            Me.EmptySpaceItem4.Location = New System.Drawing.Point(0, 72)
            Me.EmptySpaceItem4.Name = "EmptySpaceItem4"
            Me.EmptySpaceItem4.Size = New System.Drawing.Size(426, 42)
            Me.EmptySpaceItem4.TextSize = New System.Drawing.Size(0, 0)
            '
            'LayoutControlItem17
            '
            Me.LayoutControlItem17.Control = Me.DateEdit1
            Me.LayoutControlItem17.Location = New System.Drawing.Point(213, 48)
            Me.LayoutControlItem17.Name = "LayoutControlItem17"
            Me.LayoutControlItem17.Size = New System.Drawing.Size(213, 24)
            Me.LayoutControlItem17.Text = "On: "
            Me.LayoutControlItem17.TextSize = New System.Drawing.Size(55, 13)
            '
            'LayoutControlGroup6
            '
            Me.LayoutControlGroup6.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3})
            Me.LayoutControlGroup6.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup6.Name = "LayoutControlGroup6"
            Me.LayoutControlGroup6.Size = New System.Drawing.Size(426, 114)
            Me.LayoutControlGroup6.Text = "Notes"
            '
            'LayoutControlItem3
            '
            Me.LayoutControlItem3.Control = Me.MemoEdit1
            Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem3.Name = "LayoutControlItem3"
            Me.LayoutControlItem3.Size = New System.Drawing.Size(426, 114)
            Me.LayoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem3.TextVisible = False
            '
            'LayoutControlGroup7
            '
            Me.LayoutControlGroup7.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4})
            Me.LayoutControlGroup7.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup7.Name = "LayoutControlGroup7"
            Me.LayoutControlGroup7.Size = New System.Drawing.Size(426, 114)
            Me.LayoutControlGroup7.Text = "Log"
            '
            'LayoutControlItem4
            '
            Me.LayoutControlItem4.Control = Me.meAchLog
            Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem4.Name = "LayoutControlItem4"
            Me.LayoutControlItem4.Size = New System.Drawing.Size(426, 114)
            Me.LayoutControlItem4.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem4.TextVisible = False
            '
            'lciRelatedComp
            '
            Me.lciRelatedComp.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lciRelatedCompanies})
            Me.lciRelatedComp.Location = New System.Drawing.Point(0, 0)
            Me.lciRelatedComp.Name = "lciRelatedComp"
            Me.lciRelatedComp.Size = New System.Drawing.Size(426, 114)
            Me.lciRelatedComp.Text = "Related Companies"
            '
            'lciRelatedCompanies
            '
            Me.lciRelatedCompanies.Control = Me.GridControlRelatedCompanies
            Me.lciRelatedCompanies.Location = New System.Drawing.Point(0, 0)
            Me.lciRelatedCompanies.Name = "lciRelatedCompanies"
            Me.lciRelatedCompanies.Size = New System.Drawing.Size(426, 114)
            Me.lciRelatedCompanies.TextSize = New System.Drawing.Size(0, 0)
            Me.lciRelatedCompanies.TextVisible = False
            '
            'SplitterItem1
            '
            Me.SplitterItem1.AllowHotTrack = True
            Me.SplitterItem1.Location = New System.Drawing.Point(0, 440)
            Me.SplitterItem1.Name = "SplitterItem1"
            Me.SplitterItem1.Size = New System.Drawing.Size(450, 8)
            '
            'SplitterItem2
            '
            Me.SplitterItem2.AllowHotTrack = True
            Me.SplitterItem2.Location = New System.Drawing.Point(816, 24)
            Me.SplitterItem2.Name = "SplitterItem2"
            Me.SplitterItem2.Size = New System.Drawing.Size(8, 596)
            '
            'LayoutControlGroup5
            '
            Me.LayoutControlGroup5.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted
            Me.LayoutControlGroup5.ExpandButtonVisible = True
            Me.LayoutControlGroup5.ExpandOnDoubleClick = True
            Me.LayoutControlGroup5.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem5})
            Me.LayoutControlGroup5.Location = New System.Drawing.Point(0, 428)
            Me.LayoutControlGroup5.Name = "LayoutControlGroup5"
            Me.LayoutControlGroup5.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
            Me.LayoutControlGroup5.Size = New System.Drawing.Size(816, 192)
            Me.LayoutControlGroup5.Text = "Related Emails: "
            '
            'LayoutControlItem5
            '
            Me.LayoutControlItem5.Control = Me.UcFaxAndEmailGrid1
            Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem5.Name = "LayoutControlItem5"
            Me.LayoutControlItem5.Size = New System.Drawing.Size(806, 162)
            Me.LayoutControlItem5.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem5.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem5.TextVisible = False
            '
            'SplitterItem3
            '
            Me.SplitterItem3.AllowHotTrack = True
            Me.SplitterItem3.Location = New System.Drawing.Point(0, 420)
            Me.SplitterItem3.Name = "SplitterItem3"
            Me.SplitterItem3.Size = New System.Drawing.Size(816, 8)
            '
            'LayoutControlItem7
            '
            Me.LayoutControlItem7.Control = Me.ccbeStatusFilter
            Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem7.Name = "LayoutControlItem7"
            Me.LayoutControlItem7.Size = New System.Drawing.Size(410, 24)
            Me.LayoutControlItem7.Text = "Status: "
            Me.LayoutControlItem7.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
            Me.LayoutControlItem7.TextSize = New System.Drawing.Size(38, 13)
            Me.LayoutControlItem7.TextToControlDistance = 5
            '
            'EmptySpaceItem1
            '
            Me.EmptySpaceItem1.AllowHotTrack = False
            Me.EmptySpaceItem1.Location = New System.Drawing.Point(753, 0)
            Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
            Me.EmptySpaceItem1.Size = New System.Drawing.Size(71, 24)
            Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
            '
            'LayoutControlItem8
            '
            Me.LayoutControlItem8.Control = Me.deFromDate
            Me.LayoutControlItem8.Location = New System.Drawing.Point(410, 0)
            Me.LayoutControlItem8.Name = "LayoutControlItem8"
            Me.LayoutControlItem8.Size = New System.Drawing.Size(184, 24)
            Me.LayoutControlItem8.Text = "From Date: "
            Me.LayoutControlItem8.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
            Me.LayoutControlItem8.TextSize = New System.Drawing.Size(57, 13)
            Me.LayoutControlItem8.TextToControlDistance = 5
            '
            'LayoutControlItem9
            '
            Me.LayoutControlItem9.Control = Me.deToDate
            Me.LayoutControlItem9.Location = New System.Drawing.Point(594, 0)
            Me.LayoutControlItem9.Name = "LayoutControlItem9"
            Me.LayoutControlItem9.Size = New System.Drawing.Size(159, 24)
            Me.LayoutControlItem9.Text = "To Date: "
            Me.LayoutControlItem9.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
            Me.LayoutControlItem9.TextSize = New System.Drawing.Size(45, 13)
            Me.LayoutControlItem9.TextToControlDistance = 5
            '
            'LayoutControlGroup3
            '
            Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2, Me.LayoutControlGroup9})
            Me.LayoutControlGroup3.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
            Me.LayoutControlGroup3.Size = New System.Drawing.Size(1309, 620)
            Me.LayoutControlGroup3.Text = "Import File"
            '
            'LayoutControlItem2
            '
            Me.LayoutControlItem2.Control = Me.gcImportedData
            Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 70)
            Me.LayoutControlItem2.Name = "LayoutControlItem2"
            Me.LayoutControlItem2.Size = New System.Drawing.Size(1309, 550)
            Me.LayoutControlItem2.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem2.TextVisible = False
            '
            'LayoutControlGroup9
            '
            Me.LayoutControlGroup9.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem12, Me.LayoutControlItem11, Me.LayoutControlItem13, Me.LayoutControlItem14, Me.EmptySpaceItem2, Me.LayoutControlItem16})
            Me.LayoutControlGroup9.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup9.Name = "LayoutControlGroup9"
            Me.LayoutControlGroup9.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
            Me.LayoutControlGroup9.Size = New System.Drawing.Size(1309, 70)
            Me.LayoutControlGroup9.Text = "File Import"
            '
            'LayoutControlItem12
            '
            Me.LayoutControlItem12.Control = Me.beFilePath
            Me.LayoutControlItem12.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem12.Name = "LayoutControlItem12"
            Me.LayoutControlItem12.Size = New System.Drawing.Size(648, 26)
            Me.LayoutControlItem12.Text = "File: "
            Me.LayoutControlItem12.TextSize = New System.Drawing.Size(94, 13)
            '
            'LayoutControlItem11
            '
            Me.LayoutControlItem11.Control = Me.deTransactionsDate
            Me.LayoutControlItem11.Location = New System.Drawing.Point(871, 0)
            Me.LayoutControlItem11.Name = "LayoutControlItem11"
            Me.LayoutControlItem11.Size = New System.Drawing.Size(226, 26)
            Me.LayoutControlItem11.Text = "Transactions Date: "
            Me.LayoutControlItem11.TextSize = New System.Drawing.Size(94, 13)
            '
            'LayoutControlItem13
            '
            Me.LayoutControlItem13.Control = Me.cbeFileType
            Me.LayoutControlItem13.Location = New System.Drawing.Point(648, 0)
            Me.LayoutControlItem13.Name = "LayoutControlItem13"
            Me.LayoutControlItem13.Size = New System.Drawing.Size(223, 26)
            Me.LayoutControlItem13.Text = "File Type: "
            Me.LayoutControlItem13.TextSize = New System.Drawing.Size(94, 13)
            '
            'LayoutControlItem14
            '
            Me.LayoutControlItem14.Control = Me.btnImport
            Me.LayoutControlItem14.Location = New System.Drawing.Point(1107, 0)
            Me.LayoutControlItem14.Name = "LayoutControlItem14"
            Me.LayoutControlItem14.Size = New System.Drawing.Size(65, 26)
            Me.LayoutControlItem14.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem14.TextVisible = False
            '
            'EmptySpaceItem2
            '
            Me.EmptySpaceItem2.AllowHotTrack = False
            Me.EmptySpaceItem2.Location = New System.Drawing.Point(1097, 0)
            Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
            Me.EmptySpaceItem2.Size = New System.Drawing.Size(10, 26)
            Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
            '
            'LayoutControlItem16
            '
            Me.LayoutControlItem16.Control = Me.btnNoReturnsExist
            Me.LayoutControlItem16.Location = New System.Drawing.Point(1172, 0)
            Me.LayoutControlItem16.Name = "LayoutControlItem16"
            Me.LayoutControlItem16.Size = New System.Drawing.Size(113, 26)
            Me.LayoutControlItem16.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem16.TextVisible = False
            '
            'LayoutControlGroup12
            '
            Me.LayoutControlGroup12.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem21, Me.LayoutControlItem15, Me.EmptySpaceItem3})
            Me.LayoutControlGroup12.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup12.Name = "LayoutControlGroup12"
            Me.LayoutControlGroup12.Size = New System.Drawing.Size(1309, 620)
            Me.LayoutControlGroup12.Text = "Import Log"
            '
            'LayoutControlItem21
            '
            Me.LayoutControlItem21.Control = Me.GridControl2
            Me.LayoutControlItem21.Location = New System.Drawing.Point(0, 26)
            Me.LayoutControlItem21.Name = "LayoutControlItem21"
            Me.LayoutControlItem21.Size = New System.Drawing.Size(1309, 594)
            Me.LayoutControlItem21.TextLocation = DevExpress.Utils.Locations.Top
            Me.LayoutControlItem21.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem21.TextVisible = False
            '
            'LayoutControlItem15
            '
            Me.LayoutControlItem15.Control = Me.btnRefreshImportLog
            Me.LayoutControlItem15.Location = New System.Drawing.Point(1230, 0)
            Me.LayoutControlItem15.Name = "LayoutControlItem15"
            Me.LayoutControlItem15.Size = New System.Drawing.Size(79, 26)
            Me.LayoutControlItem15.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem15.TextVisible = False
            '
            'EmptySpaceItem3
            '
            Me.EmptySpaceItem3.AllowHotTrack = False
            Me.EmptySpaceItem3.Location = New System.Drawing.Point(0, 0)
            Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
            Me.EmptySpaceItem3.Size = New System.Drawing.Size(1230, 26)
            Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
            '
            'LayoutControlGroup10
            '
            Me.LayoutControlGroup10.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup10.Name = "LayoutControlGroup10"
            Me.LayoutControlGroup10.Size = New System.Drawing.Size(420, 82)
            Me.LayoutControlGroup10.Text = "new section"
            '
            'frmAchTransactions
            '
            Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(1353, 833)
            Me.Controls.Add(Me.lcRoot)
            Me.Controls.Add(Me.RibbonControl1)
            Me.Name = "frmAchTransactions"
            Me.Ribbon = Me.RibbonControl1
            Me.Text = "Ach Transactions"
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.bsAch, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riDate.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riDate, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riSlueCoNum, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemSearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riCbeDesc, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riCbeCode, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riCbeAmounts, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riCbeActions, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riBeInformation, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riStatus, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riCbeEmailTemplates, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.PopupMenuRequestWire, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemDateEdit2.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemDateEdit2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemDateEdit3.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemDateEdit3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riTeCoNmAutoFilter, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riLuePrNum, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riEmpNum, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EMPLOYEEBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riTeReadOnly, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutViewCard2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.item1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutViewCard1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
            Me.lcRoot.ResumeLayout(False)
            CType(Me.GridControlRelatedCompanies, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridViewRelatedCompanies, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.riButtonEditCoNum, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.AchTransactionDailyLogBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.DateEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.DateEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.DateEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.DateEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.meAchLog.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.cbeFileType.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.beFilePath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deTransactionsDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deTransactionsDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.achTransactionDailyLogsBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deToDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deToDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deFromDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deFromDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.ccbeStatusFilter.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gcImportedData, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gvImportedData, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TabbedControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lciRelatedComp, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lciRelatedCompanies, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.SplitterItem3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup9, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup12, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup10, System.ComponentModel.ISupportInitialize).EndInit()
            Me.ResumeLayout(False)
            Me.PerformLayout()

        End Sub

        Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
        Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
        Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
        Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
        Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
        Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents colDateFaxReceived As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCoNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEntryDescType As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCode As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colDebit As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCredit As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEffDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colActions As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colChagee As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colNsfFee As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colNotes As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riSlueCoNum As DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit
        Friend WithEvents RepositoryItemSearchLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents colCoNumAndName As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colFED_ID As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPR_CONTACT As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_EMAIL As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_FAX As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_PHONE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiRefresh As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents riCbeDesc As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
        Friend WithEvents riCbeCode As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
        Friend WithEvents riTeCoNmAutoFilter As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
        Friend WithEvents riBeInformation As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
        Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
        Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents colPrNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riLuePrNum As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
        Friend WithEvents riCbeActions As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
        Friend WithEvents riCbeAmounts As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
        Friend WithEvents bbiEmailClient As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents UcCompInfo1 As ucCompInfo
        Friend WithEvents MemoEdit1 As DevExpress.XtraEditors.MemoEdit
        Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents colCheckDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents SplitterItem2 As DevExpress.XtraLayout.SplitterItem
        Friend WithEvents bbiOpenCo As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents BarButtonItem1 As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents colAccountNumber As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colRoutingNumber As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiSetCoPassword As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiQtrEndHold As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents colName As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colAccountType As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedAction As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCorrectedData As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedAccountNumber As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedRoutingNumber As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedAccountType As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents UcFaxAndEmailGrid1 As ucFaxAndEmailGrid
        Friend WithEvents LayoutControlGroup5 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents SplitterItem3 As DevExpress.XtraLayout.SplitterItem
        Friend WithEvents colEmailTemplate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riCbeEmailTemplates As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
        Friend WithEvents colLastEmailDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riDate As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
        Friend WithEvents colId As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEmpNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bsAch As BindingSource
        Friend WithEvents gcImportedData As DevExpress.XtraGrid.GridControl
        Friend WithEvents gvImportedData As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents BindingSource1 As BindingSource
        Friend WithEvents colDateReceived As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEntryDescType1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colRejectionCode As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colDebitAmount As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCreditAmount As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colEffectiveDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colName1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedRoutingNumber1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedAccountNumber1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedAccountType1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colImportedAction1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCorrectedData1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiImportAchFile As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiImportNatPayFile As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiSetEmpDDType As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents RepositoryItemDateEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
        Friend WithEvents RepositoryItemDateEdit3 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
        Friend WithEvents rpgCoOptions As DevExpress.XtraBars.Ribbon.RibbonPageGroup
        Friend WithEvents bbiOpenEmp As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents riEmpNum As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
        Friend WithEvents EMPLOYEEBindingSource As BindingSource
        Friend WithEvents LayoutViewCard1 As DevExpress.XtraGrid.Views.Layout.LayoutViewCard
        Friend WithEvents LayoutViewCard2 As DevExpress.XtraGrid.Views.Layout.LayoutViewCard
        Friend WithEvents item1 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents colAddUser As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colAddDateTime As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colChgUser As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colChgDateTime As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riTeReadOnly As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
        Friend WithEvents bbiAutoEmails As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiAchSettings As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents deToDate As DevExpress.XtraEditors.DateEdit
        Friend WithEvents deFromDate As DevExpress.XtraEditors.DateEdit
        Friend WithEvents ccbeStatusFilter As DevExpress.XtraEditors.CheckedComboBoxEdit
        Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents meAchLog As DevExpress.XtraEditors.MemoEdit
        Friend WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
        Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents LayoutControlGroup12 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents colStatus As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colExcludeFromAutoEmails As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riStatus As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
        Friend WithEvents btnImport As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents cbeFileType As DevExpress.XtraEditors.ComboBoxEdit
        Friend WithEvents beFilePath As DevExpress.XtraEditors.ButtonEdit
        Friend WithEvents deTransactionsDate As DevExpress.XtraEditors.DateEdit
        Friend WithEvents LayoutControlGroup9 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents achTransactionDailyLogsBindingSource As BindingSource
        Friend WithEvents colId1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colTransactionsDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colFilePath As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colFileType As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsCompleted As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colAddUser1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colAddDateTime1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents btnRefreshImportLog As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents btnNoReturnsExist As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
        Friend WithEvents AchTransactionDailyLogBindingSource As BindingSource
        Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
        Friend WithEvents TextEdit2 As DevExpress.XtraEditors.TextEdit
        Friend WithEvents DateEdit2 As DevExpress.XtraEditors.DateEdit
        Friend WithEvents DateEdit1 As DevExpress.XtraEditors.DateEdit
        Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
        Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents TabbedControlGroup2 As DevExpress.XtraLayout.TabbedControlGroup
        Friend WithEvents LayoutControlGroup8 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem22 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlGroup6 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlGroup7 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
        Friend WithEvents colFollowUpDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsClientFault As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colRedraftClearedDate As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents EmptySpaceItem4 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents bbiRemoveCoPass As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents colBrands_Bank_Cleared_Date As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colMarked_Brands_Cleared_By As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colMarked_Brands_Cleared_On As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colNACHAENTRY_TransNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colClient_Refund_Processed_Date As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiRequestWire As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents PopupMenuRequestWire As DevExpress.XtraBars.PopupMenu
        Friend WithEvents BarSubItemRequestWireDD As DevExpress.XtraBars.BarSubItem
        Friend WithEvents bbiRequestWireDDNewTicket As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiRequestWireDDAttach As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents BarSubItemRequestWireTax As DevExpress.XtraBars.BarSubItem
        Friend WithEvents bbiRequestWireTaxNewTicket As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiRequestWireTaxAttach As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiRequestWireNewTicket As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiRequestWireAttach As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents LayoutControlGroup10 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents lciRelatedComp As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents GridControlRelatedCompanies As DevExpress.XtraGrid.GridControl
        Friend WithEvents GridViewRelatedCompanies As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents riButtonEditCoNum As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
        Friend WithEvents lciRelatedCompanies As DevExpress.XtraLayout.LayoutControlItem
    End Class
End Namespace