﻿Imports System.Collections.ObjectModel
Imports System.ComponentModel
Imports System.Net.Mail
Imports System.Timers
Imports DevExpress.Data.Extensions
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports DevExpress.XtraNavBar

Public Class frmFaxes

    Dim Categories As List(Of FaxCategoryStruct)
    Dim CompanyList As List(Of CompanySummary)
    Dim _IsLoading As Boolean
    Dim _Category As String = "(My Tickets)"
    Dim List As ObservableCollection(Of view_FaxAndEmail)
    Dim frmLogger As Serilog.ILogger
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property _FaxAdvancedSearch As FaxAdvancedSearch
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property timer As Timer
    Private Property UsersInMyGroup As List(Of String)

    Public Sub New()
        Dim SplashScreenManager1 As DevExpress.XtraSplashScreen.SplashScreenManager = New DevExpress.XtraSplashScreen.SplashScreenManager(Me, GetType(Global.Brands_FrontDesk.WaitForm1), True, False)
        SplashScreenManager1.ShowWaitForm()
        InitializeComponent()
        deFrom.DateTime = DateTime.Today.AddDays(-7)
        deTo.DateTime = DateTime.Now.AddDays(2)
        frmLogger = Logger.ForContext(Of frmFaxes)
        Me.TabControlEmailAndFax.ShowTabHeader = DevExpress.Utils.DefaultBoolean.False
        UcFaxAndEmail1.ShowPayrollButton = False
        timer = New Timer(60 * 5 * 1000) With {.Enabled = True}
        AddHandler timer.Elapsed, New ElapsedEventHandler(AddressOf TimerHandler)
        AddHandler modSignalRClient.OnRefereshFaxes, AddressOf LoadListEvent
        AddHandler modSignalRClient.OnFaxOrEmailUpdated, AddressOf SignalRUpdates
        SplashScreenManager1.CloseWaitForm()
        cbShowSubject.Checked = My.Settings.FaxForm_ShowSubject
        GridViewFaxes.SetGridLayoutAndAddMenues("GridViewFaxes")
    End Sub

    Private Sub frmFaxes_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        RemoveHandler modSignalRClient.OnFaxOrEmailUpdated, AddressOf SignalRUpdates
        RemoveHandler modSignalRClient.OnRefereshFaxes, AddressOf LoadListEvent
    End Sub

    Private Sub frmFaxes_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim db = New dbEPDataDataContext(GetConnectionString)
        db.CommandTimeout = 1000

        Me.FaxCategoryBindingSource.DataSource = Categories
        ceFavoritesOnly.Checked = Permissions.FaxCatFavoritesList.IsNotNullOrWhiteSpace
        LoadCategories()
        Dim comList = db.COMPANies.Select(Function(c) New With {Key .CONUM = c.CONUM, Key .CO_NAME = c.CO_NAME, Key .Specialist = c.OP_OWNER, Key .CoNumAndName = "{0} - {1}".FormatWith(c.CONUM, c.CO_NAME)}).ToList

        riSlueCoNumName.DataSource = db.view_CompanySumarries.ToList()
        riSlueCoNumName.DisplayMember = "CoNumAndName"
        riSlueCoNumName.ValueMember = "CONUM"

        riLueSpecialist.DataSource = comList
        riLueSpecialist.ValueMember = "CONUM"
        riLueSpecialist.DisplayMember = "Specialist"

        LoadList()
    End Sub

    Private Sub LoadCategories()
        Try
            NavBarControl1.Groups(0).ItemLinks.Clear()
            Me.NavBarControl1.Groups(0).ItemLinks.Add(New DevExpress.XtraNavBar.NavBarItem With {.Caption = "(My Tickets)", .Name = "(My Tickets)"})
            For Each Cat In GetCategories(ceFavoritesOnly.Checked)
                Dim item = New DevExpress.XtraNavBar.NavBarItem With {.Caption = Cat.Category, .CanDrag = False, .Name = Cat.Category}
                Me.NavBarControl1.Groups(0).ItemLinks.Add(item)
            Next
            Me.NavBarControl1.Groups(0).ItemLinks.Add(New DevExpress.XtraNavBar.NavBarItem With {.Caption = "(All)", .Name = "(All)"})
            Me.NavBarControl1.Groups(0).SelectedLinkIndex = 0

        Catch ex As Exception
            DisplayErrorMessage("Error loading categories.", ex)
        End Try
    End Sub

    Private Function GetCategories(FavoritesOnly As Boolean) As List(Of FaxCategoryStruct)
        Dim _list = New List(Of FaxCategoryStruct)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Categories = (From A In db.FaxCategories Where A.Visible Order By A.SortOrder).ToList
        Dim faxCatFavList As List(Of String) = If(Permissions.FaxCatFavoritesList.IsNullOrWhiteSpace(), New List(Of String), Permissions.FaxCatFavoritesList.Split(",").ToList())
        For Each Cat In Categories
            If Cat.Users.IsNotNullOrWhiteSpace Then
                If Not Cat.Users.Split(",").Select(Function(u) u.ToLower().Trim).Contains(UserName.ToLower) Then
                    Continue For
                End If
            End If
            If FavoritesOnly Then
                If Not faxCatFavList.Contains(Cat.Category) Then Continue For
            End If
            _list.Add(Cat)
        Next
        If Permissions.UserGroup.IsNotNullOrWhiteSpace Then
            UsersInMyGroup = db.FrontDeskPermissions.Where(Function(u) u.UserGroup = Permissions.UserGroup).Select(Function(u) u.UserName).ToList()
        End If
        Return _list
    End Function

    Private Sub LoadListEvent(obj As Object, e As EventArgs)
        LoadList()
    End Sub

    Private lockingObject As Object = New Object
    Sub SignalRUpdates(obj As Object, e As EventArgs)
        Try
            SyncLock lockingObject
                Dim fax As view_FaxAndEmail = obj
                If _FaxAdvancedSearch IsNot Nothing Then Exit Sub

                Dim existingFax = List.SingleOrDefault(Function(f) f.Source = fax.Source AndAlso f.ID = fax.ID)
                If (DoesFaxMatchFilter(fax)) Then
                    If existingFax Is Nothing Then
                        List.Add(fax)
                    Else
                        Dim idx = List.FindIndex(Function(f) f.Source = fax.Source AndAlso f.ID = fax.ID)
                        List(idx) = fax
                    End If
                Else
                    List.Remove(existingFax)
                End If
            End SyncLock

            GridControlFaxes.RefreshDataSource()
        Catch ex As Exception
            Logger.Error(ex, "Error in background SignalR updating view_FaxAndEmail")
        End Try
    End Sub

    Private Function DoesFaxMatchFilter(Fax As view_FaxAndEmail)
        If _Category = "(My Tickets)" Then
            If UsersInMyGroup IsNot Nothing AndAlso UsersInMyGroup.Any Then
                If Not UsersInMyGroup.Contains(Fax.AssignToUser) Then Return False
            Else
                If Fax.AssignToUser <> UserName Then Return False
            End If
        ElseIf _Category = "(All)" Then
        Else
            If Fax.Category <> _Category Then Return False
        End If

        If _Category <> "(My Tickets)" Then
            If cbeAssignedToFilter.Text = "Assigned" AndAlso (Fax.AssignToUser.IsNullOrWhiteSpace OrElse Fax.AssignToUser <> "CS.O") Then Return False
            If cbeAssignedToFilter.Text = "Not Assigned" AndAlso (Fax.AssignToUser.IsNotNullOrWhiteSpace OrElse Fax.AssignToUser <> "CS.O") Then Return False
        End If

        If Fax.IsDone AndAlso Not chkShowDone.Checked Then Return False
        Return True
    End Function

    Private Async Sub SetCaptionCount()
        Try
            Dim dic = New Dictionary(Of String, Integer)
            Dim _db = New dbEPDataDataContext(GetConnectionString)
            Await Task.Run(Sub()
                               For Each c In Categories
                                   Dim count As Integer
                                   If c.Category = "General" Then
                                       count = _db.view_FaxAndEmails.Where(Function(v) (v.Category = c.Category OrElse v.Category = "" OrElse v.Category Is Nothing) AndAlso (Not v.IsDone)).Count()
                                   Else
                                       count = _db.view_FaxAndEmails.Where(Function(v) v.Category = c.Category AndAlso (Not v.IsDone)).Count()
                                   End If
                                   dic.Add(c.Category, count)
                               Next
                           End Sub)
            If IsDisposed Then Exit Sub

            For Each item As DevExpress.XtraNavBar.NavBarItemLink In NavBarControl1.Groups(0).ItemLinks
                If item.ItemName = "(All)" OrElse item.ItemName = "(My Tickets)" Then Continue For
                item.Item.Caption = String.Format("{0} ({1})", item.ItemName, dic(item.ItemName))
                If IsDisposed Then Exit Sub
            Next
        Catch ex As Exception
        End Try
    End Sub

    Private Sub ShowWaitForm()
        If SplashScreenManager.IsSplashFormVisible OrElse Not _IsLoading Then
            Exit Sub
        End If
        SplashScreenManager.TryShowWaitForm(True)
        SplashScreenManager.SplashFormLocation = New Point((Width - WaitForm1.Width) / 2, (Height - WaitForm1.Height) / 2)
    End Sub

    Async Sub LoadList()
        _IsLoading = True
        SetEnabled(False)
        If Not Me.IsInitializing Then
            ShowWaitForm()
        End If

        Try
            If Me.NavBarControl1.ActiveGroup Is Nothing Then
                _IsLoading = False
                Exit Sub
            End If

            Dim db = New dbEPDataDataContext(GetConnectionString)
            db.CommandTimeout = 1000
            SetUdfColumns()

            lcAssignedTo.Visible = _Category <> "(My Tickets)"
            cbeAssignedToFilter.Visible = _Category <> "(My Tickets)"

            Dim fromDate = deFrom.DateTime
            Dim toDate = deTo.DateTime

            Await Task.Run(Sub()
                               Dim Q As IQueryable(Of view_FaxAndEmail)
                               If _FaxAdvancedSearch IsNot Nothing Then
                                   Q = From a In db.view_FaxAndEmails
                                   If _FaxAdvancedSearch.TicketNum.HasValue AndAlso _FaxAdvancedSearch.TicketNum.Value <> 0 Then Q = From a In Q Where a.TicketNum = _FaxAdvancedSearch.TicketNum.Value
                                   If _FaxAdvancedSearch.FromEmail.IsNotNullOrWhiteSpace Then Q = From a In Q Where a.From.ToLower.Contains(_FaxAdvancedSearch.FromEmail.ToLower)
                                   If _FaxAdvancedSearch.CoNum.HasValue AndAlso _FaxAdvancedSearch.CoNum.Value <> 0 Then Q = From a In Q Where a.CoNum = _FaxAdvancedSearch.CoNum.Value
                               Else
                                   Q = From A In db.view_FaxAndEmails
                                       Where (A.IsDone = False OrElse Me.chkShowDone.Checked) AndAlso A.Date.Value.Date >= fromDate.Date AndAlso A.Date.Value.Date <= toDate.Date
                                       Order By A.Date Descending

                                   If _Category = "General" Then
                                       Q = From A In Q Where A.Category Is Nothing OrElse A.Category = _Category
                                   ElseIf _Category = "(My Tickets)" Then
                                       If UsersInMyGroup IsNot Nothing AndAlso UsersInMyGroup.Any Then
                                           Q = From a In Q Where UsersInMyGroup.Contains(a.AssignToUser)
                                       Else
                                           Q = From A In Q Where A.AssignToUser.ToLower = UserName.ToLower
                                       End If
                                   ElseIf _Category <> "(All)" Then
                                       Q = From A In Q Where A.Category = _Category
                                   End If

                                   If _Category <> "(My Tickets)" Then
                                       If cbeAssignedToFilter.Text = "Assigned" Then
                                           Q = From A In Q Where A.AssignToUser <> "CS.O" AndAlso (A.AssignToUser IsNot Nothing OrElse A.AssignToUser <> "")
                                       ElseIf cbeAssignedToFilter.Text = "Not Assigned" Then
                                           Q = From A In Q Where A.AssignToUser Is Nothing OrElse A.AssignToUser = "" OrElse A.AssignToUser = "CS.O"
                                       End If
                                   End If
                               End If
                               List = New ObservableCollection(Of view_FaxAndEmail)(Q.ToList)
                           End Sub)

            RefreshGridFromList()

            SetCaptionCount()
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        Finally
            SetEnabled(True)
            _IsLoading = False
            Try
                If SplashScreenManager.IsSplashFormVisible Then
                    SplashScreenManager.CloseWaitForm()
                End If
            Catch ex As Exception
            End Try
        End Try
    End Sub

    Private Sub RefreshGridFromList()
        _IsLoading = False
        SetEnabled(False)
        Try
            Dim curRowHandle = GridViewFaxes.FocusedRowHandle
            GridControlFaxes.DataSource = List
            'Me.FaxListBindingSource.DataSource = List
            RowChanged()
            GridViewFaxes.BestFitColumns()
            GridViewFaxes.RefreshData()
            colFrom.Width = 250
            colCoNum.Width = 150
            riLueSpecialist.BestFit()
            GridViewFaxes.FocusedRowHandle = curRowHandle
            GridViewFaxes.MakeRowVisible(curRowHandle)
        Catch ex As Exception
            modGlobals.DisplayMessageBox(ex.Message)
        Finally
            SetEnabled(True)
        End Try
    End Sub

    Private Sub SetUdfColumns()
        If _Category = "(All)" Then
            colCategory.Visible = True
            btnAdvancedSearch.Visible = True
            Exit Sub
        ElseIf _Category = "(My Tickets)" Then
            colCategory.Visible = True
            Exit Sub
        Else
            colCategory.Visible = False
            btnAdvancedSearch.Visible = False
            _FaxAdvancedSearch = Nothing
        End If
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim cat = db.FaxCategories.Single(Function(c) c.Category = _Category)

        colUdfDateTime1.Visible = cat.UdfDateTime1Caption.IsNotNullOrWhiteSpace
        colUdfDateTime2.Visible = cat.UdfDateTime2Caption.IsNotNullOrWhiteSpace
        colUdfDecimal1.Visible = cat.UdfDecimal1Caption.IsNotNullOrWhiteSpace
        colUdfDecimal2.Visible = cat.UdfDecimal2Caption.IsNotNullOrWhiteSpace
        colUdfInt1.Visible = cat.UdfInt1Caption.IsNotNullOrWhiteSpace
        colUdfInt2.Visible = cat.UdfInt2Caption.IsNotNullOrWhiteSpace
        colUdfString1.Visible = cat.UdfString1Caption.IsNotNullOrWhiteSpace
        colUdfString2.Visible = cat.UdfString2Caption.IsNotNullOrWhiteSpace

        colUdfDateTime1.Caption = cat.UdfDateTime1Caption
        colUdfDateTime2.Caption = cat.UdfDateTime2Caption
        colUdfDecimal1.Caption = cat.UdfDecimal1Caption
        colUdfDecimal2.Caption = cat.UdfDecimal2Caption
        colUdfInt1.Caption = cat.UdfInt1Caption
        colUdfInt2.Caption = cat.UdfInt2Caption
        colUdfString1.Caption = cat.UdfString1Caption
        colUdfString2.Caption = cat.UdfString2Caption
    End Sub

    Private Sub GridViewFaxes_RowUpdated(sender As Object, e As Views.Base.RowObjectEventArgs) Handles GridViewFaxes.RowUpdated
        'update UDF values
        Try
            Dim row As view_FaxAndEmail = GridViewFaxes.GetRow(e.RowHandle)
            If row Is Nothing Then Exit Sub
            Dim db = New dbEPDataDataContext(GetConnectionString)
            If row.Source = "Fax" Then
                Dim fax = db.Faxes.Single(Function(f) f.FaxID = row.ID)
                fax.UdfDateTime1 = row.UdfDateTime1
                fax.UdfDateTime2 = row.UdfDateTime2
                fax.UdfDecimal1 = row.UdfDecimal1
                fax.UdfDecimal2 = row.UdfDecimal2
                fax.UdfInt1 = row.UdfInt1
                fax.UdfInt2 = row.UdfInt2
                fax.UdfString1 = row.UdfString1
                fax.UdfString2 = row.UdfString2
            ElseIf row.Source = "Email" Then
                Dim email = db.Emails.Single(Function(em) em.EmailNum = row.ID)
                email.UdfDateTime1 = row.UdfDateTime1
                email.UdfDateTime2 = row.UdfDateTime2
                email.UdfDecimal1 = row.UdfDecimal1
                email.UdfDecimal2 = row.UdfDecimal2
                email.UdfInt1 = row.UdfInt1
                email.UdfInt2 = row.UdfInt2
                email.UdfString1 = row.UdfString1
                email.UdfString2 = row.UdfString2
            ElseIf row.Source = "FrontDeskTicket" Then
                Dim ticket = db.FrontDeskTickets.Single(Function(em) em.ID = row.ID)
                ticket.UdfDateTime1 = row.UdfDateTime1
                ticket.UdfDateTime2 = row.UdfDateTime2
                ticket.UdfDecimal1 = row.UdfDecimal1
                ticket.UdfDecimal2 = row.UdfDecimal2
                ticket.UdfInt1 = row.UdfInt1
                ticket.UdfInt2 = row.UdfInt2
                ticket.UdfString1 = row.UdfString1
                ticket.UdfString2 = row.UdfString2
            Else
                Throw New ArgumentOutOfRangeException(NameOf(row.Source), $"Unknown source: {row.Source}")
            End If
            db.SaveChanges()
        Catch ex As Exception
            DisplayErrorMessage("Error saving changes", ex)
        End Try
    End Sub

    Private Function GetFocusedRow() As view_FaxAndEmail
        Return GridViewFaxes.GetRow(GridViewFaxes.FocusedRowHandle)
    End Function


    Private Sub RowChanged()
        Try
            If _IsLoading Then
                RefreshGridFromList()
            End If

            Dim Item As view_FaxAndEmail = GetFocusedRow()

            If Item Is Nothing Then
                Me.TabControlEmailAndFax.SelectedTabPageIndex = 0
                If MainForm.GetActiveForm() Is Me Then MainForm.RibbonControl1.UnMergeRibbon()
                Exit Sub
            End If

            If UcFaxAndEmail1._view_FaxAndEmail Is Item Then
                Exit Sub
            End If

            Using _db = New dbEPDataDataContext(GetConnectionString)
                UcFaxAndEmail1.TabControl.Transition.AllowTransition = DefaultBoolean.False
                Dim _ticket = _db.Tickets.Single(Function(t) t.TicketNum = Item.TicketNum)
                If MainForm.GetActiveForm Is Me Then
                    MainForm.RibbonControl1.UnMergeRibbon()
                    MainForm.RibbonControl1.MergeRibbon(UcFaxAndEmail1.RibbonControl1)
                End If
                UcFaxAndEmail1.Initialize(Item, _ticket, True)
                TabControlEmailAndFax.SelectedTabPageIndex = 1
            End Using
            'If Item.Source = "Fax" Then
            '    If Me.TabControlEmailAndFax.SelectedTabPageIndex = 1 AndAlso UcFax1.FaxEnt IsNot Nothing AndAlso UcFax1.FaxEnt.FaxID = Item.ID Then

            '    Else
            '        Dim fax = (From A In _db.Faxes Where A.FaxID = Item.ID).Single
            '        UcFax1.Initialize(fax, _ticket)
            '    End If

            '    Me.TabControlEmailAndFax.SelectedTabPageIndex = 1
            '    If MainForm.GetActiveForm() Is Me Then
            '        MainForm.RibbonControl1.UnMergeRibbon()
            '        MainForm.RibbonControl1.MergeRibbon(UcFax1.RibbonControl1)
            '    End If
            'ElseIf Item.Source = "Email" Then
            '    If Me.TabControlEmailAndFax.SelectedTabPageIndex = 0 AndAlso UcEmail1.SelectedEmail IsNot Nothing AndAlso UcEmail1.SelectedEmail.EmailNum = Item.ID Then
            '        Exit Sub
            '    End If
            '    Me.TabControlEmailAndFax.SelectedTabPageIndex = 0
            '    If MainForm.GetActiveForm() Is Me Then
            '        MainForm.RibbonControl1.UnMergeRibbon()
            '        MainForm.RibbonControl1.MergeRibbon(UcEmail1.RibbonControl1)
            '    End If
            '    Dim email = (From A In _db.Emails Where A.EmailID = Item.EmailID).Single
            '    UcEmail1.Initialize(email, _ticket, False)
            'ElseIf Item.Source = "FrontDeskTicket" Then
            '    If Me.TabControlEmailAndFax.SelectedTabPageIndex = 2 AndAlso UcTicket.SelectedFrontDeskTicket IsNot Nothing AndAlso UcTicket.SelectedFrontDeskTicket.ID = Item.ID Then
            '        Exit Sub
            '    End If
            '    Me.TabControlEmailAndFax.SelectedTabPageIndex = 2
            '    If MainForm.GetActiveForm() Is Me Then
            '        MainForm.RibbonControl1.UnMergeRibbon()
            '        MainForm.RibbonControl1.MergeRibbon(UcTicket.RibbonControl1)
            '    End If
            '    Dim ticket = (From A In _db.FrontDeskTickets Where A.ID = Item.ID).Single
            '    UcTicket.Initialize(ticket, _ticket, False)
            'Else
            '    Throw New Exception($"Unknown Source {Item.Source}")
            'End If
        Catch ex As Exception
            DisplayErrorMessage("Error in changing row", ex)
        End Try
    End Sub

    Sub UpdateCategory(Row As view_FaxAndEmail, previosCat As String)
        Try
            Dim DbEntDB = New dbEPDataDataContext(GetConnectionString)
            If Row IsNot Nothing Then
                Dim SelectedCategory As FaxCategory = If(Row.Category Is Nothing, Nothing, DbEntDB.FaxCategories.Single(Function(c) c.Category.Equals(Row.Category)))
                If SelectedCategory IsNot Nothing AndAlso SelectedCategory.Bin = "Payroll" AndAlso Not Row.CoNum.HasValue Then
                    XtraMessageBox.Show("Please select a company number.")
                    Exit Sub
                End If
                DbEntDB.AuditLogs.InsertOnSubmit(Row.CreateAuditLog("Category Changed", $"{Row.Source} was moved from [{previosCat}] To [{Row.Category}]", Row.Category, previosCat))

                If Row.Source = "Fax" Then
                    Dim Ent = (From A In DbEntDB.Faxes Where A.FileName = Row.FileName).Single
                    If Ent Is Nothing Then
                        Ent = New Fax With {.FileName = Row.FileName, .Date = Row.Date}
                        DbEntDB.Faxes.InsertOnSubmit(Ent)
                    End If
                    Ent.FaxCategory = SelectedCategory
                    Ent.LastUpdatedBy = UserName
                    Ent.LastUpdatedDate = Now
                ElseIf Row.Source = "Email" Then
                    Dim Ent = (From A In DbEntDB.Emails Where A.EmailID = Row.EmailID).Single
                    Ent.Category = If(SelectedCategory IsNot Nothing, SelectedCategory.Category, Nothing)
                    Ent.LastUpdatedBy = UserName
                    Ent.LastUpdatedDate = Now
                ElseIf Row.Source = "FrontDeskTicket" Then
                    Dim Ent = (From A In DbEntDB.FrontDeskTickets Where A.ID = Row.ID).Single
                    Ent.Category = If(SelectedCategory IsNot Nothing, SelectedCategory.Category, Nothing)
                    Ent.LastUpdatedBy = UserName
                    Ent.LastUpdatedDate = Now
                Else
                    Throw New Exception("Unknown row source type.")
                End If

                Try
                    DbEntDB.SubmitChanges()
                    modSignalRClient.PushFaxOrEmailUpdate(Row.Source, Row.ID, "Updated Category")
                Catch ex As Exception
                    DisplayErrorMessage("Error saving changes", ex)
                    Exit Sub
                End Try

                'db = New dbEPDataDataContext(GetConnectionString)
                'Row = db.view_FaxAndEmails.Single(Function(f) f.Source = Row.Source AndAlso f.ID = Row.ID)
                'db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Row)

                If Row.Source = "Fax" AndAlso SelectedCategory IsNot Nothing AndAlso _Category <> SelectedCategory.Category Then
                    Dim CopyToPath = SelectedCategory.CopyToPath
                    If Not String.IsNullOrEmpty(CopyToPath) Then
                        If Not IO.Directory.Exists(CopyToPath) Then
                            DisplayMessageBox(String.Format("Cannot copy to {0}{1}Path does not exist", CopyToPath, vbCrLf))
                        Else
                            Dim FileName As String = ""
                            If Row.CoNum.HasValue Then
                                FileName &= Row.CoNum & "_"
                            End If
                            FileName &= IO.Path.GetFileName(Row.FileName)
                            IO.File.Copy(Row.FileName, IO.Path.Combine(CopyToPath, FileName))
                        End If
                    End If
                End If
                If SelectedCategory IsNot Nothing AndAlso _Category.CompareTo("(All)") = 1 AndAlso _Category.CompareTo("(My Tickets)") = 1 AndAlso SelectedCategory.Category <> _Category OrElse (Row.IsDone AndAlso Not Me.chkShowDone.Checked AndAlso _FaxAdvancedSearch Is Nothing) Then
                    GridViewFaxes.DeleteSelectedRows()
                    'Me.FaxListBindingSource.RemoveCurrent()
                End If

                If SelectedCategory IsNot Nothing AndAlso _Category <> SelectedCategory.Category Then
                    SendEmail(Row)
                End If

            Else
                DisplayMessageBox("No row selected")
            End If
            RowChanged()
        Catch ex As Exception
            DisplayErrorMessage("Error saving Fax Or Email.", ex)
        End Try
    End Sub

    Private Async Sub SendEmail(ByVal row As view_FaxAndEmail)
        Try  'get users email address
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim User = (From A In db.DBUSERs.ToList Where A.name.ToLower = UserName.ToLower).SingleOrDefault

            Dim emailCategory As FaxCategory = (From A In Categories Where A.Category = row.Category).Single
            Dim company As view_CompanySumarry = db.view_CompanySumarries.SingleOrDefault(Function(c) c.CONUM.Equals(row.CoNum))
            Dim MM As New MailMessage
            Dim MC As New SmtpClient

            Logger.Debug("Sending email for {Source} ID: {ID} For Co#: {CoNum}", row.Source, row.ID, row.CoNum)

            Await SendInternalEmail(row, emailCategory, User, company)

            'customer/external email
            Dim email As Email = Nothing
            If row.Source = "Email" Then
                email = (From e In db.Emails Where e.EmailID = row.EmailID).Single
                If email.IsOutbound Then Exit Sub

                Dim firstEmail = db.Emails.FirstOrDefault(Function(e) e.TicketNum = row.TicketNum AndAlso e.EmailNotificationSentOn.HasValue)
                If firstEmail IsNot Nothing Then
                    ShowAlert("Email Was Sent Already", "Email was already sent on {0}".FormatWith(firstEmail.EmailNotificationSentOn.Value))
                    Exit Sub
                End If

            ElseIf row.Source = "Fax" Then
                If Not row.CoNum.HasValue Then Exit Sub
                Dim fax = (From f In db.Faxes Where f.FaxID = row.ID).Single
                Dim firstFax = db.Faxes.FirstOrDefault(Function(f) f.TicketNum = row.TicketNum AndAlso f.EmailNotificationSentOn.HasValue)
                If firstFax IsNot Nothing Then
                    ShowAlert("Email Was Sent Already", "Email was already sent on {0}".FormatWith(firstFax.EmailNotificationSentOn.Value))
                    Exit Sub
                End If
            End If

            'db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, row)

            Dim list = New List(Of String)
            If row.Source = "Email" Then
                list = ParseEmail(String.Format("{0};{1}", email.From, email.To)).Where(Function(e) Not e.ToLower.EndsWith("@brandspaycheck.com")).Distinct.ToList
                If Not list.Any Then
                    Exit Sub
                End If
            Else
                If company.CO_EMAIL.IsNullOrWhiteSpace Then Exit Sub
                list.Add(company.CO_EMAIL.Replace(";", ","))
            End If

            MM.To.Add(String.Join(",", list))
            MM.Bcc.Add("<EMAIL>")

            If row.FaxCategory.Bin = "Payroll" AndAlso company IsNot Nothing Then
                MM.From = New MailAddress("<EMAIL>", "Payroll")
                MM.Subject = $"[#{row.TicketNum}] Your {row.Source} was received and will be processed shortly"
                MM.Body = String.Format("Dear {0},{1}{1}This Email is to confirm receipt of your payroll. We will be processing your request shortly.{1}{1} Have a great day".FormatWith(If(company.PR_CONTACT.IsNullOrWhiteSpace, "Client", company.PR_CONTACT), vbCrLf))
            ElseIf row.FaxCategory.Bin = "CS" AndAlso company IsNot Nothing Then
                MM.From = New MailAddress("<EMAIL>", "Customer Service")
                If row.Source = "Email" Then
                    MM.Subject = $"[#{row.TicketNum}] {email.Subject}"
                Else
                    MM.Subject = $"[#{row.TicketNum}] Fax Received"
                End If
                MM.Body = String.Format("Dear {0}, {1}{1}Thank you for contacting us. {1}This is an automated response confirming the receipt of your ticket. Our team will get back to you as soon as possible. {1}When replying, please make sure that the ticket ID is kept in the subject so that we can track your replies.{1}{1}Have a great day" _
                                        .FormatWith(If(company IsNot Nothing AndAlso company.PR_CONTACT.IsNullOrWhiteSpace, "Client", company.PR_CONTACT), vbCrLf))
            Else
                Exit Sub
            End If

            MC = New SmtpClient
            ShowAlert("Sending Email", String.Join(",", MM.To.Select(Function(e) e.Address)))
            Await Task.Run(Sub() MC.Send(MM))
            If row.Source = "Email" Then
                db.ExecuteCommand(String.Format("UPDATE custom.Emails SET EmailNotificationSentOn = GetDate() WHERE EmailID = {0}", row.EmailID))
            Else
                db.ExecuteCommand(String.Format("UPDATE custom.Faxes SET EmailNotificationSentOn = GetDate() WHERE FaxId = {0}", row.ID))
            End If
            modSignalRClient.FaxOrEmailUpdated(row, "SendEmail")
        Catch ex As Exception
            DisplayErrorMessage("Error sending email", ex)
        End Try
    End Sub

    Private Async Function SendInternalEmail(row As view_FaxAndEmail, category As FaxCategory, User As DBUSER, Co As view_CompanySumarry) As Task
        Try
            Dim db = New dbEPDataDataContext(GetConnectionString)
            If IsNothing(User) OrElse User.email.IsNullOrWhiteSpace() Then
                XtraMessageBox.Show("No email address found for user {0}. {1} Can't send email to Specialist".FormatWith(UserName, vbCrLf))
                Exit Function
            End If

            Dim MM = New System.Net.Mail.MailMessage
            Dim MC = New System.Net.Mail.SmtpClient
            If category.EmailSubject.IsNotNullOrWhiteSpace OrElse category.EmailAddress.IsNotNullOrWhiteSpace Then
                MM.From = New MailAddress(User.email, User.name)
                If category.EmailAddress.IsNotNullOrWhiteSpace() Then
                    MM.To.Add(category.EmailAddress.Replace(";", ","))
                Else
                    If row.CoNum.HasValue AndAlso Co IsNot Nothing Then
                        Dim CompanySpecialistUser = (From A In db.DBUSERs.ToList Where A.name.ToLower = Co.OP_OWNER.ToLower).SingleOrDefault
                        If IsNothing(CompanySpecialistUser) Then
                            XtraMessageBox.Show("No email address found for Specialist {0}. {1} Can't send email".FormatWith(UserName, vbCrLf))
                        Else
                            MM.To.Add(New MailAddress(CompanySpecialistUser.email, CompanySpecialistUser.name))
                        End If
                    End If
                End If
                If MM.To.Any Then
                    If category.EmailSubject.IsNullOrWhiteSpace() OrElse Co Is Nothing Then
                        MM.Subject = category.Category & " Received Notification"
                    Else
                        MM.Subject = category.EmailSubject.FormatWith(row.Source) & " - Co #: {0} Co Name: {1}".FormatWith(row.CoNum, Co.CO_NAME)
                    End If
                    MM.Body = If(category.EmailBody.IsNullOrWhiteSpace(), "A fax payroll was received and added to fax payroll bin in from deskFax Received", category.EmailBody.FormatWith(row.Source))
                    ShowAlert("Sending Email", String.Join(",", MM.To.Select(Function(e) e.Address)))
                    Dim DbEntDB = New dbEPDataDataContext(GetConnectionString)
                    Await Task.Run(Sub() MC.Send(MM))
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error sending internal Email for new Fax/Email", ex)
        End Try
    End Function

    Private Sub SearchLookUpEdit1View_CustomColumnDisplayText(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs)
        If e.Column.FieldName = "CoNum" AndAlso e.Value = 0 Then
            e.DisplayText = "Not Set"
        End If
    End Sub

    Private Sub chkShowDone_CheckedChanged(sender As Object, e As EventArgs) Handles chkShowDone.CheckedChanged
        LoadList()
    End Sub

    Private Sub ComboBoxEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles cbeAssignedToFilter.EditValueChanged
        If deFrom.DateTime = CType(Nothing, DateTime) OrElse deTo.DateTime = CType(Nothing, DateTime) Then Exit Sub
        LoadList()
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        LoadList()
        MainForm.RefreshBadgeCounter()
    End Sub

    Private Sub TimerHandler()
        If Not _IsLoading AndAlso GetUdfValue("UseTimerToRefreshFaxAndEmail") = "True" Then
            MainForm.TryInvoke(Sub() LoadList())
        End If
    End Sub

    Private Sub riCeMarkDone_EditValueChanging(sender As Object, e As DevExpress.XtraEditors.Controls.ChangingEventArgs) Handles riCeMarkDone.EditValueChanging
        Dim row = GetFocusedRow()
        If row Is Nothing Then Exit Sub
        If row.FaxCategory IsNot Nothing Then
            If row.FaxCategory.UsersAllowMarkDone.IsNotNullOrWhiteSpace Then
                If Not row.FaxCategory.UsersAllowMarkDone.Split(",").Select(Function(u) u.ToLower().Trim).Contains(UserName.ToLower) Then
                    DisplayMessageBox("Sorry, You don't have permission to change status in this Category.")
                    e.Cancel = True
                    Exit Sub
                End If
            End If

            If row.FaxCategory.ConfirmOnMarkDone Then
                If Not XtraMessageBox.Show($"Are you sure you would like to mark this item {IIf(e.NewValue = True, "Done", "Undone")}?", "Change Status", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                    e.Cancel = True
                End If
            End If
        End If
    End Sub

    Private Sub riCeMarkDone_CheckedChanged(sender As Object, e As EventArgs) Handles riCeMarkDone.CheckedChanged
        Try
            GridViewFaxes.PostEditor()
            Dim row = GetFocusedRow()
            If row Is Nothing Then Exit Sub
            Dim isDone = DirectCast(sender, CheckEdit).Checked
            If row.TryMarkDone(isDone) Then
                row.IsDone = isDone
                If isDone And Not Me.chkShowDone.Checked Then
                    GridViewFaxes.DeleteSelectedRows()
                End If
            End If
            GridViewFaxes.RefreshRow(GridViewFaxes.FocusedRowHandle)
        Catch ex As Exception
            DisplayErrorMessage("Error changing status on Fax/Email", ex)
        End Try
    End Sub

    Private Sub riSlueCoNumName_Popup(sender As Object, e As EventArgs) Handles riSlueCoNumName.Popup
        Try
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim slue = CType(sender, SearchLookUpEdit)
            slue.SuspendLayout()
            slue.Properties.View.Columns("CO_EMAIL").ClearFilter()
            slue.Properties.View.Columns("CO_FAX").ClearFilter()
            'slue.Properties.View.ActiveFilterString = ""
            Dim Item As view_FaxAndEmail = GetFocusedRow()
            slue.Tag = Item

            If Item IsNot Nothing AndAlso Item.Source = "Email" Then
                Dim addresses = Item.From.RemoveFromStart("<").RemoveFromEnd(">")
                If db.COMPANies.Any(Function(f) f.CO_EMAIL.Contains(addresses)) Then
                    slue.Properties.View.ActiveFilterString = "[CO_EMAIL] Like '%{0}%'".FormatWith(addresses)
                Else
                    slue.Properties.View.ActiveFilterString = "[CO_EMAIL] Like '%{0}%'".FormatWith(addresses.Split("@")(1))
                End If
            ElseIf Item IsNot Nothing AndAlso Item.Source = "Fax" AndAlso Item.From <> "Fax" Then
                slue.Properties.View.ActiveFilterString = $"[CO_FAX] = '{Item.From.Substring(1)}'"
            End If
            slue.ResumeLayout()
        Catch ex As Exception
            DisplayErrorMessage("error filtering for co#", ex)
        End Try
    End Sub

    Private Sub riSlueCoNumName_CloseUp(sender As Object, e As DevExpress.XtraEditors.Controls.CloseUpEventArgs) Handles riSlueCoNumName.CloseUp
        Try
            Dim slue = CType(sender, SearchLookUpEdit)
            Dim row As view_FaxAndEmail = slue.Tag
            'Dim row = CType(FaxListBindingSource.Current, view_FaxAndEmail)
            Dim DbEntDB = New dbEPDataDataContext(GetConnectionString)
            row = DbEntDB.view_FaxAndEmails.SingleOrDefault(Function(f) f.Source = row.Source AndAlso f.ID = row.ID)
            If row Is Nothing Then Exit Sub
            Dim message = If(row.CoNum.HasValue, $"Co# was changed from {row.CoNum} To {e.Value}", $"{row.Source} Co# Assigned To {e.Value}")
            DbEntDB.AuditLogs.InsertOnSubmit(row.CreateAuditLog("Co# Assigned", message, If(e.Value, ""), If(row.CoNum.HasValue, row.CoNum, "")))
            If e.CloseMode = PopupCloseMode.Normal Then
                If row.Source = "Fax" Then
                    Dim fax = (From f In DbEntDB.Faxes Where f.FaxID = row.ID).Single
                    fax.CoNum = e.Value
                ElseIf row.Source = "Email" Then
                    Dim email = (From em In DbEntDB.Emails Where em.EmailNum = row.ID).Single
                    email.CoNum = e.Value
                ElseIf row.Source = "FrontDeskTicket" Then
                    Dim ticket = DbEntDB.FrontDeskTickets.Single(Function(t) t.ID = row.ID)
                    ticket.CoNum = e.Value
                ElseIf row.Source = "EmailOutbound" Then
                    Dim emailo = (From em In DbEntDB.EmailOutbounds Where em.ID = row.ID).Single
                    emailo.CoNum = e.Value
                Else
                    Throw New Exception($"Unknown Source {row.Source}")
                End If
                e.AcceptValue = DbEntDB.SaveChanges

            ElseIf e.CloseMode = PopupCloseMode.Cancel Then
                If row.Category = "Payroll" Then
                    e.AcceptValue = False
                    'db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, row)
                    'GridViewFaxes.RefreshRow(GridViewFaxes.FocusedRowHandle)
                    DisplayMessageBox("Error, All {0}es in Payroll category must have a CoNum assigned.".FormatWith(row.Source))
                    Exit Sub
                End If
                If row.Source = "Fax" Then
                    Dim fax = (From f In DbEntDB.Faxes Where f.FaxID = row.ID).Single
                    fax.CoNum = Nothing
                ElseIf row.Source = "Email" Then
                    Dim email = (From em In DbEntDB.Emails Where em.EmailNum = row.ID).Single
                    email.CoNum = Nothing
                ElseIf row.Source = "FrontDeskTicket" Then
                    Dim ticket = DbEntDB.FrontDeskTickets.Single(Function(t) t.ID = row.ID)
                    ticket.CoNum = Nothing
                ElseIf row.Source = "EmailOutbound" Then
                    Dim emailo = (From em In DbEntDB.EmailOutbounds Where em.ID = row.ID).Single
                    emailo.CoNum = Nothing
                Else
                    Throw New Exception($"Unknown Source {row.Source}")
                End If
                e.AcceptValue = DbEntDB.SaveChanges
            End If
            'db = New dbEPDataDataContext(GetConnectionString)
            'row = db.view_FaxAndEmails.Single(Function(f) f.Source = row.Source AndAlso f.ID = row.ID)
            'db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, row)
            modSignalRClient.FaxOrEmailUpdated(row, "Setting Co#")
            GridViewFaxes.RefreshRow(GridViewFaxes.FocusedRowHandle)
        Catch ex As Exception
            DisplayErrorMessage("Error setting Co# on Fax/Email.", ex)
        End Try
    End Sub


#Region "Drag & Drop "
    Private downHitInfo As GridHitInfo = Nothing
    Private dragableFax As view_FaxAndEmail = Nothing

    Private Sub GridViewFaxes_MouseDown(ByVal sender As Object, ByVal e As MouseEventArgs) Handles GridViewFaxes.MouseDown
        Dim view As GridView = TryCast(sender, GridView)
        downHitInfo = Nothing
        dragableFax = Nothing

        Dim hitInfo As GridHitInfo = view.CalcHitInfo(New Point(e.X, e.Y))
        If Control.ModifierKeys <> Keys.None Then
            Return
        End If

        If e.Button = MouseButtons.Left AndAlso hitInfo.InRow AndAlso hitInfo.RowHandle <> GridControl.NewItemRowHandle Then
            downHitInfo = hitInfo
            dragableFax = CType(view.GetRow(hitInfo.RowHandle), view_FaxAndEmail)
        End If
    End Sub

    Private Sub GridControlFaxes_GiveFeedback(sender As Object, e As GiveFeedbackEventArgs) Handles GridControlFaxes.GiveFeedback
        If Not System.Windows.Forms.SystemInformation.TerminalServerSession Then
            e.UseDefaultCursors = False
        End If
        'Cursor.Current = GetCursor("test " & e.Effect.ToString())
    End Sub

    Private Sub GridViewFaxes_MouseMove(ByVal sender As Object, ByVal e As MouseEventArgs) Handles GridViewFaxes.MouseMove
        Try
            Dim view As GridView = TryCast(sender, GridView)
            If e.Button = MouseButtons.Left AndAlso downHitInfo IsNot Nothing Then
                Dim dragSize As Size = SystemInformation.DragSize
                Dim dragRect As New Rectangle(New Point(downHitInfo.HitPoint.X - dragSize.Width \ 2, downHitInfo.HitPoint.Y - dragSize.Height \ 2), dragSize)

                If (Not dragRect.Contains(New Point(e.X, e.Y))) Then
                    view.GridControl.DoDragDrop(dragableFax, DragDropEffects.All)
                    downHitInfo = Nothing
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in MouseMove event", ex)
        End Try
    End Sub

    Private Sub NavBarControl1_DragOver(sender As Object, e As DragEventArgs) Handles NavBarControl1.DragOver
        If e.Data.GetDataPresent(GetType(view_FaxAndEmail)) Then
            Dim faxAndEmail As view_FaxAndEmail = TryCast(e.Data.GetData(GetType(view_FaxAndEmail)), view_FaxAndEmail)
            If faxAndEmail Is Nothing Then
                Return
            End If

            Dim grid As NavBarControl = TryCast(sender, NavBarControl)
            Dim navBarHitInfo = grid.CalcHitInfo(grid.PointToClient(New Point(e.X, e.Y)))
            If navBarHitInfo.InLink Then
                If faxAndEmail.Category <> navBarHitInfo.Link.ItemName AndAlso (navBarHitInfo.Link.ItemName <> "(All)" OrElse navBarHitInfo.Link.ItemName <> "(My Tickets)") Then
                    e.Effect = DragDropEffects.Move
                    Cursor.Current = GetCursor(String.Format("{0}", navBarHitInfo.Link.ItemName, e.Effect), True)
                Else
                    e.Effect = DragDropEffects.None
                    Cursor.Current = GetCursor("Not Allowed.", False)
                End If
            Else
                e.Effect = DragDropEffects.None
                Cursor.Current = GetCursor("No Cat Selected.", False)
            End If
        End If
    End Sub

    Private Sub NavBarControl1_DragDrop(sender As Object, e As DragEventArgs) Handles NavBarControl1.DragDrop
        Dim grid As NavBarControl = TryCast(sender, NavBarControl)
        If e.Data.GetDataPresent(GetType(view_FaxAndEmail)) Then
            Dim faxAndEmail As view_FaxAndEmail = TryCast(e.Data.GetData(GetType(view_FaxAndEmail)), view_FaxAndEmail)
            If faxAndEmail Is Nothing Then
                Return
            End If

            Dim previosCat As String
            Dim db = New dbEPDataDataContext(GetConnectionString)
            Dim navBarHitInfo = grid.CalcHitInfo(grid.PointToClient(New Point(e.X, e.Y)))
            If navBarHitInfo.InLink Then
                If faxAndEmail.Category <> navBarHitInfo.Link.ItemName AndAlso navBarHitInfo.Link.ItemName <> "(All)" AndAlso navBarHitInfo.Link.ItemName <> "(My Tickets)" Then
                    Dim SelectedCategory As FaxCategory = If(navBarHitInfo.Link.ItemName Is Nothing, Nothing, db.FaxCategories.Single(Function(c) c.Category.Equals(navBarHitInfo.Link.ItemName)))

                    If String.Equals(SelectedCategory.Category, "Payroll", StringComparison.CurrentCulture) AndAlso (faxAndEmail.CoNum Is Nothing OrElse faxAndEmail.CoNum = 0) Then
                        XtraMessageBox.Show("All {0}es in Payroll must have a CoNum assigned.".FormatWith(faxAndEmail.Source))
                        Exit Sub
                    ElseIf String.Equals(SelectedCategory.Category, "Client Support", StringComparison.CurrentCulture) AndAlso (faxAndEmail.CoNum Is Nothing OrElse faxAndEmail.CoNum = 0) Then
                        If XtraMessageBox.Show("You did not assign a CoNum, Continue?", "Continue", MessageBoxButtons.YesNo) = System.Windows.Forms.DialogResult.No Then
                            Exit Sub
                        End If
                    End If

                    If Not SelectedCategory.AllowAddNew Then
                        XtraMessageBox.Show("Category {0} is read only.".FormatWith(SelectedCategory.Category))
                        Exit Sub
                    End If
                    previosCat = faxAndEmail.Category
                    faxAndEmail.FaxCategory = SelectedCategory
                    UpdateCategory(faxAndEmail, previosCat)
                    ShowAlert("{0} moved".FormatWith(faxAndEmail.Source), "successfully moved from: {0} to {1}".FormatWith(previosCat, SelectedCategory.Category), DevExpress.XtraBars.Alerter.AlertFormLocation.BottomLeft)
                End If
            End If

        End If
    End Sub

    Dim _cursorsList As Dictionary(Of String, Cursor) = New Dictionary(Of String, Cursor)
    Private Function GetCursor(msg As String, allow As Boolean) As Cursor
        Dim _cursor As Cursor
        Try
            If Not _cursorsList.TryGetValue(msg, _cursor) Then
                Dim mouseUc As ucFaxMoveMouse = New ucFaxMoveMouse
                mouseUc.Init(msg, allow)
                Dim bmp = New Bitmap(mouseUc.Width * 2, mouseUc.Height * 2)
                mouseUc.DrawToBitmap(bmp, New Rectangle(250, 60, 250, 60))
                _cursor = New Cursor(bmp.GetHicon)
                _cursorsList.Add(msg, _cursor)
            End If
        Catch ex As Exception
            Logger.Error(ex, "error in GetCursor")
        End Try
        Return _cursor
    End Function
#End Region

    Private Sub GridViewFaxes_CustomRowCellEditForEditing(sender As Object, e As CustomRowCellEditEventArgs) Handles GridViewFaxes.CustomRowCellEditForEditing
        If e.RowHandle = GridControl.AutoFilterRowHandle Then
            If e.Column Is colCoNum Then
                e.RepositoryItem = riTeCoNmAutoFilter
            ElseIf e.Column Is colUdfDateTime1 OrElse e.Column Is colUdfDateTime2 Then
                e.RepositoryItem = RepositoryItemDateEdit1
            End If
        End If
    End Sub

    Private Sub frmFaxes_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        ShowWaitForm()
    End Sub

    Private Sub NavBarControl1_LinkClicked(sender As Object, e As NavBarLinkEventArgs) Handles NavBarControl1.LinkClicked
        _Category = Me.NavBarControl1.ActiveGroup.SelectedLink.ItemName
        LoadList()
        riSlueCoNumName.ShowClearButton = NavBarControl1.SelectedLink.ItemName <> "Payroll"
    End Sub

    Private Sub SetEnabled(val As Boolean)
        btnRefresh.Enabled = val
        Me.GridControlFaxes.Enabled = val
        Panel1.Enabled = val
        NavBarControl1.Enabled = val
        TabControlEmailAndFax.Enabled = val
    End Sub

    Private Sub GridViewFaxes_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridViewFaxes.PopupMenuShowing
        'TODO allow replying email to a fax, E.X.cutomer ask for confirmation 
        'If e.MenuType = GridMenuType.Row AndAlso e.HitInfo.InRow Then
        '    Dim row = CType(FaxListBindingSource.Current, view_FaxAndEmail)
        '    If row Is Nothing Then Exit Sub
        '    e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("
        'End If

        Dim row As view_FaxAndEmail = GridViewFaxes.GetFocusedRow
        If row IsNot Nothing AndAlso row.PayrollOpenedBy.IsNotNullOrWhiteSpace Then
            e.Menu.Items.Add(New DXMenuItem("Release Lock", Sub()
                                                                ReleaseFaxOrEmail(row)
                                                            End Sub, My.Resources.locknavigation_16x16))
        End If
    End Sub

    Private Sub ReleaseFaxOrEmail(row As view_FaxAndEmail)
        If XtraMessageBox.Show($"Are you sure you would like to Release the lock from this {row.Source}? {vbCrLf}If you choose to go ahead, Please make sure that {row.PayrollOpenedBy} is aware about it.", "Release Lock", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
            frmLogger.Debug("Releasing lock from {Source} {Id}. Was openned by {User}", row.Source, row.ID, row.PayrollOpenedBy)
            Using db = New dbEPDataDataContext(GetConnectionString)
                Dim ticket = db.Tickets.Single(Function(t) t.TicketNum = row.TicketNum)
                ticket.Release(db, True)
            End Using
        End If
    End Sub

    Private Sub GridViewFaxes_FocusedRowObjectChanged(sender As Object, e As Views.Base.FocusedRowObjectChangedEventArgs) Handles GridViewFaxes.FocusedRowObjectChanged
        RowChanged()
    End Sub

    Private Sub GridViewFaxes_FocusedRowChanged(sender As Object, e As Views.Base.FocusedRowChangedEventArgs) Handles GridViewFaxes.FocusedRowChanged
        RowChanged()
    End Sub

    Private Sub GridViewFaxes_DataSourceChanged(sender As Object, e As EventArgs) Handles GridViewFaxes.DataSourceChanged
        RowChanged()
    End Sub

    Private Sub GridViewFaxes_RowCountChanged(sender As Object, e As EventArgs) Handles GridViewFaxes.RowCountChanged
        RowChanged()
    End Sub

    Private Async Sub frmFaxes_Activated(sender As Object, e As EventArgs) Handles MyBase.Activated
        Await Task.Delay(100)
        MainForm.RibbonControl1.UnMergeRibbon()
        If TabControlEmailAndFax.SelectedTabPageIndex = 1 Then
            MainForm.RibbonControl1.MergeRibbon(UcFaxAndEmail1.RibbonControl1)
        End If
        'If TabControlEmailAndFax.SelectedTabPageIndex = 0 Then
        '    MainForm.RibbonControl1.MergeRibbon(UcEmail1.RibbonControl1)
        'ElseIf TabControlEmailAndFax.SelectedTabPageIndex = 1 Then
        '    MainForm.RibbonControl1.MergeRibbon(UcFax1.RibbonControl1)
        'ElseIf TabControlEmailAndFax.SelectedTabPageIndex = 2 Then
        '    MainForm.RibbonControl1.MergeRibbon(ucTicket.RibbonControl1)
        'Else
        '    MainForm.RibbonControl1.UnMergeRibbon()
        'End If
        'RowChanged()
    End Sub

    Private Sub frmFaxes_Deactivate(sender As Object, e As EventArgs) Handles MyBase.Deactivate
        Try
            MainForm.RibbonControl1.UnMergeRibbon()
        Catch ex As Exception
            DisplayErrorMessage("Error unmerging ribbon", ex)
        End Try
    End Sub

    Private Sub riSlueCoNumName_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles riSlueCoNumName.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Search Then
            Dim editor As SearchLookUpEdit = sender
            If editor.EditValue IsNot Nothing AndAlso editor.EditValue <> 0 Then MainForm.OpenCompForm(editor.EditValue)
        End If
    End Sub

    Private Sub btnAdvancedSearch_Click(sender As Object, e As EventArgs) Handles btnAdvancedSearch.Click
        Dim frm = New frmFaxesAdvancedSearch(_FaxAdvancedSearch)
        If frm.ShowDialog = DialogResult.OK Then
            _FaxAdvancedSearch = frm._FaxAdvancedSearch
        Else
            _FaxAdvancedSearch = Nothing
        End If
        LoadList()
    End Sub

    Public Class FaxAdvancedSearch
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property TicketNum As Decimal?
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property FromEmail As String
        <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
        Public Property CoNum As Decimal?
    End Class

    Private Sub btnManageCategoryFavorites_Click(sender As Object, e As EventArgs) Handles btnManageCategoryFavorites.Click
        Dim frm = New frmFaxesCategoryFavorites(GetCategories(False))
        If frm.ShowDialog = DialogResult.OK Then
            LoadCategories()
        End If
    End Sub

    Private Sub ceFavoritesOnly_CheckedChanged(sender As Object, e As EventArgs) Handles ceFavoritesOnly.CheckedChanged
        LoadCategories()
    End Sub

    Private Sub cbShowSubject_CheckedChanged(sender As Object, e As EventArgs) Handles cbShowSubject.CheckedChanged
        frmLogger.Debug("Changing cbShowSubject to: {cbShowSubject}", cbShowSubject.Checked)
        My.Settings.FaxForm_ShowSubject = cbShowSubject.Checked
        My.Settings.Save()

        If cbShowSubject.Checked Then
            GridViewFaxes.FocusRectStyle = DrawFocusRectStyle.RowFullFocus
            GridViewFaxes.OptionsSelection.EnableAppearanceFocusedCell = False
            GridViewFaxes.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            GridViewFaxes.OptionsView.ShowPreview = True
            GridViewFaxes.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False
        Else
            GridViewFaxes.FocusRectStyle = DrawFocusRectStyle.CellFocus
            GridViewFaxes.OptionsSelection.EnableAppearanceFocusedCell = True
            GridViewFaxes.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.True
            GridViewFaxes.OptionsView.ShowPreview = False
            GridViewFaxes.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.True
        End If
    End Sub

    Private Sub GridViewFaxes_DoubleClick(sender As Object, e As EventArgs) Handles GridViewFaxes.DoubleClick
        Dim ea As DXMouseEventArgs = TryCast(e, DXMouseEventArgs)
        Dim view As GridView = TryCast(sender, GridView)
        Dim info As GridHitInfo = view.CalcHitInfo(ea.Location)
        If info.InRow OrElse info.InRowCell Then
            Dim row As view_FaxAndEmail = GridViewFaxes.GetRow(info.RowHandle)
            row.OpenFaxOrEmailDetails
        End If
    End Sub
End Class