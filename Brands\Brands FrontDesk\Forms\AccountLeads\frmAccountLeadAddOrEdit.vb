﻿Imports Brands_FrontDesk
Imports DevExpress.XtraEditors

Public Class frmAccountLeadAddOrEdit

    Private db As dbEPDataDataContext
    Private _Lead As AccountLead
    Private _IsNewRow As Boolean


    Public Sub New(Optional lead As AccountLead = Nothing, Optional isNewRow As Boolean = False)
        InitializeComponent()
        _Lead = lead
        _IsNewRow = isNewRow
    End Sub

    Private Sub frmAccountLeadAddOrEdit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        db = New dbEPDataDataContext(GetConnectionString)
        cbeStatus.Properties.Items.AddRange(GetUdfValue("AccountLeadsStatusList").Replace(vbCrLf, "~").Split("~"))
        cbeCategory.Properties.Items.AddRange(GetUdfValue("AccountLeadsCategoryList").Replace(vbCrLf, "~").Split("~"))
        cbeAignedTo.Properties.Items.AddRange(db.FrontDeskPermissions.Where(Function(u) u.AllowAccountLeads).Select(Function(u) u.UserName).ToArray)
        cbePayFreuency.Properties.Items.AddRange(db.GLOBAL_LISTs.Where(Function(g) g.list_name = "Frequency").Select(Function(g) g.list_value).ToArray())

        Dim States = db.ZIPs.Select(Function(z) z.STATE).Distinct().ToArray
        cbeCoState.Properties.Items.AddRange(States)
        cbeShipState.Properties.Items.AddRange(States)
        teListEmployeeWorkStates.Properties.Tokens.AddRange(States.Select(Function(s) New TokenEditToken(s)))
        teConnectecCompanies.Properties.Tokens.AddRange(db.view_CompanySumarries.Select(Function(c) New TokenEditToken(c.CoNumAndName, c.CONUM)))

        bePhone.AddPhoneButton()
        beCell.AddPhoneButton()
        beEmail.AddEmailButton()
        beFax.AddFaxButton

        If _Lead Is Nothing Then
            _Lead = New AccountLead
            db.AccountLeads.InsertOnSubmit(_Lead)
        ElseIf _IsNewRow Then
            db.AccountLeads.InsertOnSubmit(_Lead)
        Else
            _Lead = (From l In db.AccountLeads Where l.ID = _Lead.ID).Single
        End If
        bsCompany.DataSource = db.view_CompanySumarries.ToList()
        BindingSource1.DataSource = _Lead
        teListEmployeeWorkStates.EditValue = _Lead.EmployeeWorkStates
        teConnectecCompanies.EditValue = _Lead.ConnectedCompanies
        slueCoNum.AddDeleteButton
        slueReferedByCoNum.AddDeleteButton
        TextEdit2.Focus()
    End Sub

    Private Sub meNotes_Click(sender As Object, e As EventArgs) Handles meNotes.Click
        Dim frm = New frmEditNotes(meNotes.Text)
        If frm.ShowDialog = DialogResult.OK Then
            meNotes.Text = frm.GetNote
        End If
    End Sub

    Private Sub bbiSave_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSave.ItemClick
        BindingSource1.EndEdit()
        If SaveChanges() Then
            DialogResult = DialogResult.OK
            Close()
        End If
    End Sub

    Private Sub teCoZip_Validated(sender As Object, e As EventArgs) Handles teCoZip.Validated
        If Not Me.teCoZip.HasValue Then Return
        If Me.teCoZip.EditValue = Me.teCoZip.OldEditValue & "" Then Return
        Dim Zip = (From A In db.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.teCoZip.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            _Lead.CoState = Zip.STATE
            _Lead.CoCity = Zip.CITY
            _Lead.CoCounty = Zip.COUNTY
        End If
    End Sub

    Private Sub teShipZip_Validated(sender As Object, e As EventArgs) Handles teShipZip.Validated
        If Not Me.teShipZip.HasValue Then Return
        If Me.teShipZip.EditValue = Me.teShipZip.OldEditValue & "" Then Return
        Dim Zip = (From A In db.ZIPS Where A.CITYTYPE = "D" AndAlso A.ZIP = Me.teShipZip.EditValue.ToString).FirstOrDefault
        If Zip IsNot Nothing Then
            _Lead.ShippingState = Zip.STATE
            _Lead.ShippingCity = Zip.CITY
        End If
    End Sub

    Private Sub teRoutingNumber_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles teRoutingNumber.Validating
        If db.fn_ValidRoutingNumbers(teRoutingNumber.EditValue) = "False" Then
            XtraMessageBox.Show("Invalid routing #")
            e.Cancel = True
        End If
    End Sub

    Private Sub teFedId_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles teFedId.Validating
        Dim co = db.COMPANies.FirstOrDefault(Function(c) c.FED_ID = teFedId.Text)
        If co IsNot Nothing Then
            XtraMessageBox.Show($"This Fed Id is already being used by Company {co.CONUM}")
            e.Cancel = True
        End If
    End Sub

    Private Function SaveChanges() As Boolean
        Try
            If _Lead.ID = 0 Then
                _Lead.AddUser = UserName
                _Lead.AddDate = DateTime.Now
            Else
                _Lead.ChgUser = UserName
                _Lead.ChgDate = DateTime.Now
            End If

            _Lead.EmployeeWorkStates = teListEmployeeWorkStates.EditValue
            _Lead.ConnectedCompanies = teConnectecCompanies.EditValue

            Return db.SaveChanges
        Catch ex As Exception
            Logger.Error(ex, "Error saving changes")
            DisplayErrorMessage("Error saving changes", ex)
            Return False
        End Try
    End Function

    Private Sub frmAccountLeadAddOrEdit_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        BindingSource1.EndEdit()
        Dim changes = db.GetChangeSet()
        If (changes.Deletes.Count + changes.Inserts.Count + changes.Updates.Count) > 0 Then
            Dim result = XtraMessageBox.Show("You have unsaved changes." & vbCrLf & "Would you like to save changes?", "Save Changes?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning)
            If result = DialogResult.Cancel Then
                e.Cancel = True
            ElseIf result = DialogResult.Yes Then
                e.Cancel = Not SaveChanges()
            End If
        End If
    End Sub

    Private Sub btnAddConnectedCompany_Click(sender As Object, e As EventArgs) Handles btnAddConnectedCompany.Click
        Try
            Dim args = New XtraInputBoxArgs()
            args.Caption = "Link Company"
            args.Prompt = "Please select a company"
            Dim pce = New PopupContainerEdit
            pce.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
            ucSearchCompany.BindPopupContainerEdit(pce)
            args.Editor = pce
            Dim conum As Decimal? = XtraInputBox.Show(args)
            If conum.HasValue Then
                If teConnectecCompanies.EditValue Is Nothing Then
                    teConnectecCompanies.EditValue = conum.ToString
                Else
                    If teConnectecCompanies.EditValue.ToString.Split(",").Contains(conum) Then
                        XtraMessageBox.Show($"Compnay {conum} is already linked.")
                    Else
                        teConnectecCompanies.EditValue &= $",{conum}"
                    End If
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error adding company", ex)
        End Try
    End Sub

    Private Sub teCoStreetAddress_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teCoStreetAddress.PreviewKeyDown
        If e.KeyCode = Keys.Enter OrElse e.KeyCode = Keys.Tab Then
            teCoZip.Focus()
        End If
    End Sub

    Private Sub teShipStreetAddress_PreviewKeyDown(sender As Object, e As PreviewKeyDownEventArgs) Handles teShipStreetAddress.PreviewKeyDown
        If e.KeyCode = Keys.Enter OrElse e.KeyCode = Keys.Tab Then
            teShipZip.Focus()
        End If
    End Sub
End Class