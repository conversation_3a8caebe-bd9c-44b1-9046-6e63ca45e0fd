﻿Imports System.ComponentModel

Public Class frmRunAutos

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property RunAutoList As List(Of RunAutoStatus)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property YearEndBilling As frmYearEndDecPayroll
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CloseWhenDone As Boolean

    Private _CurrentItem As RunAutoStatus
    Private _CurrentIX As Integer
    Private _Cancelled As Boolean

    Private _DB As dbEPDataDataContext
    Private API As PPxPayrollInterface
    Private logger As WebLogger
    Private frmLogger As Serilog.ILogger = modGlobals.Logger.ForContext(Of frmRunAutos)()
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property RunOnBackgroundThread As Boolean

    Private Sub frmRunAutos_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        logger = WebLogger.GetInstance
        For Each PrinterName In Printing.PrinterSettings.InstalledPrinters
            Me.cbPrinters.Properties.Items.Add(PrinterName)
        Next
        Dim defaultPrinting = New Printing.PrinterSettings
        Me.cbPrinters.EditValue = defaultPrinting.PrinterName

        _DB = New dbEPDataDataContext(GetConnectionString)
        Me.BindingSource1.DataSource = RunAutoList
        frmLogger.Information("Opening form (frm)RunAutos. With RunAutoList Count: {Count}", RunAutoList.Count)
        If RunOnBackgroundThread Then
            Me.BackgroundWorker1.RunWorkerAsync()
        Else
            Dim _ex As Exception = Nothing
            Try
                Application.DoEvents()
                BackgroundWorker1_DoWork(Nothing, New System.ComponentModel.DoWorkEventArgs(Nothing))
            Catch ex As Exception
                _ex = ex
            End Try
            BackgroundWorker1_RunWorkerCompleted(Nothing, New System.ComponentModel.RunWorkerCompletedEventArgs(Nothing, _ex, False))
        End If
    End Sub

    Private Async Sub BackgroundWorker1_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        API = EP_API()
        API.ppxPayrollEntryType = "BrandsXpressAuto"
        If YearEndBilling IsNot Nothing Then API.ppxPayrollEntryType = YearEndBilling.txtEntryType.EditValue
        logger = WebLogger.GetInstance
        logger.ShowMsgBoxOnError = False

        AddHandler logger.ActionLog, AddressOf DisplayLog

        For Me._CurrentIX = 0 To RunAutoList.Count - 1
            Try
                Dim itm = RunAutoList(_CurrentIX)
                Me._CurrentItem = itm
                frmLogger.Debug("Processing Co#: {CoNum} PrStatus: {PrStatus}", itm.CoNum, itm.Status)

                If Me.BackgroundWorker1.CancellationPending Then
                    frmLogger.Debug("User Canceled")
                    e.Result = "Canceled"
                    e.Cancel = True
                    Exit For
                End If

                itm.Status = "Processing"
                RefreshGrid()

                API.CompanyID = itm.CoNum
                API.CheckPayrollStatus()
                If API.PayrollID > 0 AndAlso API.PayrollStatus = "Entering Checks" Then
                    itm.Status = "Skipped"
                    Dim msg = String.Format("Payroll already exists (# {0})", API.PayrollID)
                    itm.Details = msg
                    frmLogger.Debug("Skipped - " & msg & " {@Item}", itm)
                    Continue For
                End If

                If Me.YearEndBilling Is Nothing Then
                    'Start Payroll
                    API.CalendarID = itm.CalID

                    'If Not UsePPxLibrary Then
                    '    API.CreateNewPayrollFromCalendar()
                    'Else
                    '    API.CreateNewPayrollFromCalendar(itm.CoNum)
                    'End If

                    API.CreateNewPayrollFromCalendar(itm.CoNum)

                    If API.PayrollID <= 0 Then
                        Continue For
                    End If
                    itm.Details &= " Payroll # " & API.PayrollID

                    'Create PowerGrid

                    'If Not UsePPxLibrary Then
                    '    Dim ppx = CType(API, PPxPayroll)
                    '    Dim Payroll = ppx.PayrollAPI.GetSinglePayrollInfo(API.ProviderID, API.CompanyID, API.PayrollID, -1)
                    'Else
                    API.GetSinglePayrollInfo(itm.CoNum, API.PayrollID)
                    'End If

                    Dim BatchList = New pr_batch_list With {.run_add_checks = "NO",
                                                    .run_auto_hours = "NO",
                                                    .run_auto_pays = "YES",
                                                    .run_auto_deds = "YES",
                                                    .run_auto_memos = "YES",
                                                    .run_sick = "YES",
                                                    .run_vacation = "YES",
                                                    .run_personal = "YES",
                                                    .run_pay_salary = "YES",
                                                    .run_dd_flag = "YES",
                                                    .run_override_rate = "NONE",
                                                    .conum = itm.CoNum,
                                                    .status = "In Progress",
                                                    .obj_ref = 101,
                                                    .provider_id = 0,
                                                    .name = String.Format("{0}_{1}_{2}_{3}", itm.CoNum, API.PayrollID, itm.EndDate.Value.ToString("MM-dd-yyyy"), itm.CheckDate.Value.ToString("MM-dd-yyyy"))
                                                   }

                    Using frmPowerGrid = New frmBrandsPowerGrid With {.CoNum = itm.CoNum, .PrNum = API.PayrollID, .BatchList = BatchList, .IsAuto = True, .CalanderID = itm.CalID}
                        frmPowerGrid.LoadData()

                        Await frmPowerGrid.Process(False).ConfigureAwait(False)

                        If frmPowerGrid.AutoIsSubmitted Then
                            itm.Status = "Submitted"
                        ElseIf frmPowerGrid.AutoIsProcessed Then
                            itm.Status = "Processed"
                        Else
                            itm.Status = "Unknown"
                        End If
                        'frmPowerGrid.Dispose()
                    End Using
                Else
                    Me.ProcessYearEndBill(itm)
                End If

                Dim Log As New AutoPayrollsLog With {.CalID = itm.CalID,
                                                     .CoCode = itm.CoNum,
                                                     .CoName = itm.CoName,
                                                     .Status = itm.Status,
                                                     .Details = itm.Details,
                                                     .ProcessDate = Date.Now,
                                                     .UserName = UserName}
                _DB.AutoPayrollsLogs.InsertOnSubmit(Log)
                If Not _DB.SaveChanges() Then
                    frmLogger.Error("Failed to save auto payroll log due to concurrency conflict")
                    ' Continue processing even if save fails
                End If
                itm.IsLogged = True
            Catch ex As Exception
                logger.Log(WebLogger.LogLevel.Error, ex.Message)
                frmLogger.Error(ex, "Index {index} {Item}", Me._CurrentIX, Me._CurrentItem)
            End Try
        Next
        RemoveHandler logger.ActionLog, AddressOf DisplayLog
        API.ppxPayrollEntryType = "BrandsXpress"
        frmLogger.Debug("Setting API.ppxPayrollEntryType to BrandsXpress")
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        RefreshGrid()
        If e.Error IsNot Nothing Then
            logger.Log(WebLogger.LogLevel.Error, e.Error.Message & vbCrLf & vbCrLf & e.Error.StackTrace)
            frmLogger.Debug(e.Error, "Error with BackgroundWorker.")
        End If
        frmLogger.Debug(e.Error, "BackgroundWorker Compeleted.")

        For Each itm In RunAutoList
            If Not itm.IsLogged AndAlso itm.Status <> "Waiting" Then
                Dim Log As New AutoPayrollsLog With {.CalID = itm.CalID,
                                                     .CoCode = itm.CoNum,
                                                     .CoName = itm.CoName,
                                                     .Status = itm.Status,
                                                     .Details = itm.Details,
                                                     .ProcessDate = Date.Now,
                                                     .UserName = UserName}
                _DB.AutoPayrollsLogs.InsertOnSubmit(Log)
            End If
        Next
        If Not _DB.SaveChanges() Then
            frmLogger.Error("Failed to save auto payroll logs due to concurrency conflict")
            ' Continue processing even if save fails
        End If

        Me.btnCancel.Enabled = False
        Me.btnPrint.Enabled = True
        frmLogger.Debug("Saved AutoPayrollsLog")
        If Me.CloseWhenDone Then
            If Me.InvokeRequired Then
                Me.BeginInvoke(Sub() Close())
            Else
                Close()
            End If
        End If
    End Sub

    'Private Sub RunAPIStep(ByVal StepName As String)
    '    If Not Me.BackgroundWorker1.CancellationPending Then
    '        Dim API = EP_API()
    '        Dim Method = API.GetType.GetMethod(StepName, Reflection.BindingFlags.Public Or Reflection.BindingFlags.Instance Or Reflection.BindingFlags.IgnoreCase)
    '        Dim results = Method.Invoke(API, Nothing)
    '    End If
    'End Sub

    Public Sub DisplayLog(ByVal sender As Object, ByVal e As ActionLogTypeEventArgs)
        modGlobals.Logger.Information("Payroll Log. Co#: {CoNum} Pr#: {PrNum} LogLevel: {LogLevel} Message: {Message}", _CurrentItem.CoNum, API.PayrollID, e.LogLevel, e.Message)
        If e.Message.IndexOf("::") > 0 Then
            e.Message = e.Message.Substring(0, e.Message.IndexOf("::") - 1)
        End If
        If Not String.IsNullOrEmpty(_CurrentItem.Details) Then
            _CurrentItem.Details &= vbCrLf
        End If
        _CurrentItem.Details &= e.Message
        If e.Ex IsNot Nothing Then
            _CurrentItem.Status = "Error"
        End If
        RefreshGrid()
    End Sub

    Sub RefreshGrid()
        If Me.InvokeRequired Then
            Me.BeginInvoke(Sub() RefreshGrid())
            Exit Sub
        End If
        Me.GridView1.RefreshData()
        Me.GridView1.FocusedRowHandle = _CurrentIX
        Me.GridView1.MakeRowVisible(_CurrentIX)
        If Me.BackgroundWorker1.IsBusy Then
            Me.lblStatus.Text = String.Format("Processing {0} of {1}", _CurrentIX + 1, RunAutoList.Count)
            If _Cancelled Then Me.lblStatus.Text &= " - Cancelled"
        Else
            Me.lblStatus.Text = String.Format("Completed {0} of {1}", Me._CurrentIX, RunAutoList.Count)
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to cancel?", "Confirm Cancel", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.Yes Then
            Me.BackgroundWorker1.CancelAsync()
            _Cancelled = True
        End If
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim SelectedPrinter = Me.cbPrinters.EditValue
        If SelectedPrinter Is Nothing Then
            DisplayMessageBox("No Printer Selected")
            Exit Sub
        Else
            Me.printGridLink.Print(SelectedPrinter)
        End If
    End Sub

    Private Sub ProcessYearEndBill(itm As RunAutoStatus)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim LastPayrollNum = (From A In db.PAYROLLs Where A.CONUM = itm.CoNum Order By A.PRNUM Descending).FirstOrDefault
        Dim NewPayrollNum As Decimal = 1
        If LastPayrollNum IsNot Nothing Then
            NewPayrollNum = LastPayrollNum.PRNUM + 1
        End If
        Dim CHG As Decimal = 1
        If itm.PrDesc = "Zero Returns" Then 'YearEndBilling.RunType = frmYearEndDecPayroll.RunTypeEnum.ZeroReurns Then
            CHG = 0
        End If
        Dim CompanyData = (From A In db.COMPANies Where A.CONUM = itm.CoNum Select A.CONUM, A.START_CHK_NUM, A.FED_DEP_STAT).Single

        Dim PER1_ST_DATE As Date?
        Dim PER1_END_DATE As Date?

        If itm.CheckDate.HasValue Then
            If itm.CheckDate.Value.Month < 4 Then
                PER1_ST_DATE = New Date(itm.CheckDate.Value.Year - 1, 12, 1)
                PER1_END_DATE = New Date(itm.CheckDate.Value.Year - 1, 12, 31)
            ElseIf itm.CheckDate.Value.Month = 12 Then
                PER1_ST_DATE = New Date(itm.CheckDate.Value.Year, 12, 1)
                PER1_END_DATE = New Date(itm.CheckDate.Value.Year, 12, 31)
            End If
        End If

        Dim NewPayrollRec As New PAYROLL With {.CONUM = itm.CoNum,
                                                .PRNUM = NewPayrollNum,
                                                .CHECK_DATE = itm.CheckDate,
                                                .START_TIME = Now,
                                                .RUN_DATE = itm.RunDate,
                                                .FED_UCI = 1,
                                                .NUM_CHECKS = 0,
                                                .NUM_MANUAL = 0,
                                                .NUM_DD = 0,
                                                .NUM_TAX = 0,
                                                .NUM_VOID = 0,
                                                .BEG_CKNUM = CompanyData.START_CHK_NUM,
                                                .OPNAME = UserName,
                                                .CHG_CD = CHG,
                                                .BILL_ACH_STAT = "N/A",
                                                .TAX941 = 0, .TOTHRS = 0, .GROSS = 0, .FEDWH = 0, .OASDI = 0, .MED = 0, .STWH = 0, .LOCWH = 0, .EIC = 0, .NET = 0,
                                                .PAYROLL_STATUS = "Entering Checks",
                                                .DD_FG = 0,
                                                .ENTRY_TYPE = YearEndBilling.txtEntryType.EditValue,
                                                .SUPP_PR = itm.SUPP_PR,
                                                .TOTALTAX_STATUS = "N/A",
                                                .AUTO_HOURS = "NO",
                                                .AUTO_PAYS = "NO", .AUTO_DEDS = "NO", .AUTO_MEMO = "NO",
                                                .acc_sick = "NO", .acc_per = "NO", .acc_vac = "NO",
                                                .mpwWebStatus = 60,
                                                .PERIOD_CODE3 = Nothing, .PERIOD_CODE2 = Nothing, .PERIOD_CODE = Nothing,
                                                .STDEP = Now, .LOCDEP = Now,
                                                .PR_DESCR = itm.PrDesc,' Me.YearEndBilling.txtPRDesc.EditValue,
                                                .rowguid = Guid.Parse(Guid.NewGuid.ToString.ToUpper),
                                                .PER1_ST_DATE = PER1_ST_DATE,
                                                .PER1_END_DATE = PER1_END_DATE,
                                                .PAY_FREQ = "", .EOM_FG1 = 1
                                                }
        db.PAYROLLs.InsertOnSubmit(NewPayrollRec)

        'Also update divison check numbers
        Dim Divisions = (From A In db.DIVISIONs Where A.CONUM = itm.CoNum AndAlso A.DBANKSAME = "NO" AndAlso A.DSTART_CHK_NUM > A.PR_CKNUM).ToList
        For Each divison In Divisions
            divison.PR_CKNUM = divison.DSTART_CHK_NUM
        Next

        If Not db.SaveChanges() Then
            frmLogger.Error("Failed to save division check numbers due to concurrency conflict")
            ' Continue processing even if save fails
        End If
        API.CompanyID = itm.CoNum
        API.PayrollID = NewPayrollRec.PRNUM

        If itm.PrDesc.Contains("Zero Returns") Then ' YearEndBilling.RunType = frmYearEndDecPayroll.RunTypeEnum.ZeroReurns Then  
            db.prc_CreateZeroCheck(itm.CoNum, NewPayrollRec.PRNUM)
            'db.prc_CreateInvoice(itm.CoNum, NewPayrollRec.PRNUM, "Zero Returns")
        End If

        'for Zero Returns do not create invoice if VoidOnly is set
        If Not itm.PrDesc.Contains("Zero Returns") OrElse (itm.VoidOnly Is Nothing Or itm.VoidOnly = 0) Then
            db.prc_CreateInvoice(itm.CoNum, NewPayrollRec.PRNUM, itm.PrDesc)
        End If

        itm.Details &= " Payroll # " & API.PayrollID

        Dim intID As Integer = 0

        Dim PayRec = (From A In db.pr_batch_in_processes Where A.CoNum = itm.CoNum AndAlso A.PrNum = NewPayrollNum AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
        'Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = CoNum AndAlso A.PrNum = API.PayrollID AndAlso A.IsDeleted.GetValueOrDefault = False).FirstOrDefault
        If PayRec Is Nothing Then
            PayRec = New pr_batch_in_process With {.CoNum = itm.CoNum,
                                                   .PrNum = NewPayrollNum,
                                                   .ProcessedDate = Now,
                                                   .ProcessedBy = UserName,
                                                   .rowguid = Guid.NewGuid}
            db.pr_batch_in_processes.InsertOnSubmit(PayRec)
            PayRec.ProcessStatus = "Processing-2" 'step number 
        End If
        If PayRec.ProcessedBy Is Nothing Then
            PayRec.ProcessedBy = UserName
            PayRec.ProcessedDate = Now
            PayRec.ProcessStatus = "Processing-3"
        End If
        If Not db.SaveChanges() Then
            frmLogger.Error("Failed to save payroll processing status due to concurrency conflict")
            ' Continue processing even if save fails
        End If
        intID = PayRec.ID

        Dim CoPayrollOptions = db.CoOptions_Payrolls.SingleOrDefault(Function(c) c.CoNum = itm.CoNum)
        PayRec.IncludeManuals = CoPayrollOptions Is Nothing OrElse CoPayrollOptions.DoNotAutoIncludeManuals.GetValueOrDefault = False
        PayRec.IncludeVoids = CoPayrollOptions Is Nothing OrElse CoPayrollOptions.DoNotAutoIncludeVoids.GetValueOrDefault = False

        'Include manual checks
        If PayRec.IncludeManuals Then
            Dim ManCheckCount As Integer = 0
            db.prc_IncludeManualChecksToPayroll(itm.CoNum, NewPayrollRec.PRNUM, True, ManCheckCount)
            If ManCheckCount > 0 Then
                NewPayrollRec.CHG_CD = 1
                WebLogger.PayrollProcessStep = 0.25
                'SetProcessStatus(0.25, "Processing")
                logger.Log(WebLogger.LogLevel.Info, "Adding manual checks")

                Dim msg = New pr_batch_note With {.ListID = Guid.NewGuid(),
                                                         .Conum = itm.CoNum,
                                                         .PrNum = NewPayrollRec.PRNUM,
                                                         .EnteredBy = UserName,
                                                         .rowguid = Guid.NewGuid(),
                                                         .DateEntered = DateTime.Now,
                                                         .Priority = "3-Low",
                                                         .EmployeeID = -997,
                                                         .Note = ManCheckCount & " manual check(s) added to payroll"}
                db.pr_batch_notes.InsertOnSubmit(msg)
                If Not db.SaveChanges() Then
                    frmLogger.Error("Failed to save manual check notes due to concurrency conflict")
                    ' Continue processing even if save fails
                End If

                logger.Log(WebLogger.LogLevel.Info, String.Format("{0} manual check(s) added", ManCheckCount))
                'SetProcessStatus(0.25, "Done")
            End If
        End If


        'Voids
        If PayRec.IncludeVoids Then
            Dim ToVoidCount = (From A In db.Voids Where A.CoNum = itm.CoNum AndAlso A.VoidPrNum Is Nothing AndAlso
                                                       A.CheckDate.HasValue AndAlso itm.CheckDate.HasValue AndAlso
                                                       A.CheckDate.Value.Year = itm.CheckDate.Value.Year).Count
            If ToVoidCount > 0 Then
                NewPayrollRec.CHG_CD = 1
                WebLogger.PayrollProcessStep = 0.5
                'SetProcessStatus(0.5, "Processing")
                logger.Log(WebLogger.LogLevel.Info, String.Format("Adding Void records - {0} record(s)", ToVoidCount))

                'this is handle duplicate voids 
                Dim ToVoid = (From A In db.Voids Where A.CoNum = itm.CoNum AndAlso A.VoidPrNum Is Nothing AndAlso
                                                       A.CheckDate.HasValue AndAlso itm.CheckDate.HasValue AndAlso
                                                       A.CheckDate.Value.Year = itm.CheckDate.Value.Year).ToList
                For Each void_chk In ToVoid
                    Dim chk_mast = (From c In db.CHK_MASTs Where c.CONUM = itm.CoNum _
                                        AndAlso c.EMPNUM = void_chk.EmpNum _
                                        AndAlso c.CHK_NUM = void_chk.ChkNum _
                                        AndAlso (void_chk.Net.HasValue AndAlso (c.NET.Value = (-void_chk.Net.Value))) _
                                        AndAlso (void_chk.Gross.HasValue AndAlso (c.GROSS.Value = (-void_chk.Gross.Value))) _
                                        AndAlso c.CHK_TYPE.StartsWith("REV")).FirstOrDefault
                    If chk_mast IsNot Nothing Then
                        Dim msg = New pr_batch_msg With {.id = Guid.NewGuid,
                                                         .conum = itm.CoNum,
                                                         .empnum = chk_mast.EMPNUM,
                                                         .chk_counter = chk_mast.CHK_COUNTER,
                                                         .msg_type = "VOID_ISSUE",
                                                         .batch_id = API.PowerGridBatchID,
                                                         .Priority = "1-High",
                                                         .msg_body = "Chk#: {0} was voided during payroll, however this check was already voided in Pr#: {1}, confirm if needs to void again, if not then delete in EP".FormatWith(void_chk.ChkNum, chk_mast.PAYROLL_NUM)}
                        db.pr_batch_msgs.InsertOnSubmit(msg)
                        If Not db.SaveChanges() Then
                            frmLogger.Error("Failed to save void issue message due to concurrency conflict")
                            ' Continue processing even if save fails
                        End If
                    End If
                Next

                db.prc_AddVoidsToPayroll(itm.CoNum, NewPayrollRec.PRNUM, True)

                'check if still there
                Dim CurrentVoidCount = (From A In db.Voids Where A.CoNum = itm.CoNum AndAlso A.VoidPrNum Is Nothing).Count
                If CurrentVoidCount > 0 Then
                    logger.Log(WebLogger.LogLevel.Error, "Void records not processed.")
                Else
                    Dim msg = New pr_batch_note With {.ListID = Guid.NewGuid,
                                                          .Conum = itm.CoNum,
                                                          .PrNum = NewPayrollRec.PRNUM,
                                                          .EnteredBy = UserName,
                                                          .rowguid = Guid.NewGuid(),
                                                          .DateEntered = DateTime.Now,
                                                          .Priority = "3-Low",
                                                          .EmployeeID = -997,
                                                          .Note = ToVoidCount & "void check(s) added to payroll"}
                    db.pr_batch_notes.InsertOnSubmit(msg)
                    If Not db.SaveChanges() Then
                        frmLogger.Error("Failed to save void check notes due to concurrency conflict")
                        ' Continue processing even if save fails
                    End If
                    logger.Log(WebLogger.LogLevel.Info, String.Format("{0} void check(s) added", ToVoidCount))
                End If

                'SetProcessStatus(0.5, "Done" & If(WithErrors, " With Errors", ""))
                If Not db.SaveChanges() Then
                    frmLogger.Error("Failed to save void processing status due to concurrency conflict")
                    ' Continue processing even if save fails
                End If
            End If
        End If

        Dim CurrentStep As Decimal
        If PayRec.ProcessStatus.Contains("Processing-") Then
            CurrentStep = PayRec.ProcessStatus.Replace("Processing-", "")
        End If

        Dim Submit As Boolean = True

        Try
            Dim NotesList As New List(Of pr_batch_msg)

Step3:
            If CurrentStep > 3 Then GoTo Step4
            PayRec.ProcessStatus = "Processing-3"

            Dim CalcTotalSuccess As Boolean

            'If Not UsePPxLibrary Then
            '    CalcTotalSuccess = API.CalculateTotals()
            'Else
            CalcTotalSuccess = API.CalculateTotals(itm.CoNum, NewPayrollRec.PRNUM)
            'End If

            If Not CalcTotalSuccess Then
                itm.Status = "Unknown"
                GoTo Finish
            End If

            itm.Status = "Processed"

            Application.DoEvents()

            WebLogger.PayrollProcessStep = 3

            Dim MessageCount As Integer
            'Save messages
            'If Not UsePPxLibrary Then
            '    'dont see how API.BatchMessages is being populated
            '    Dim ppx = CType(API, PPxPayroll)
            '    If ppx.BatchMessages IsNot Nothing AndAlso ppx.BatchMessages.Count > 0 Then
            '        Dim PayrollMessages = (From A In ppx.BatchMessages
            '                               Group A By A.batch_id, A.chk_counter, A.conum, A.empnum, A.msg_body, A.msg_type
            '                               Into Group Select batch_id, chk_counter, conum, empnum, msg_body, msg_type
            '                               )
            '        For Each pmsg In PayrollMessages
            '            Dim msg As New pr_batch_msg With {.id = Guid.NewGuid,
            '                                              .batch_id = pmsg.batch_id,
            '                                              .chk_counter = pmsg.chk_counter,
            '                                              .conum = pmsg.conum,
            '                                              .empnum = pmsg.empnum,
            '                                              .msg_body = pmsg.msg_body,
            '                                              .msg_type = pmsg.msg_type}
            '            Dim MsgID As Guid
            '            If Guid.TryParse(msg.msg_body, MsgID) Then
            '                msg.msg_body = String.Format("{{{0}}}", msg.msg_body.ToUpper)
            '            End If
            '            db.pr_batch_msgs.InsertOnSubmit(msg)
            '        Next
            '        db.SubmitChanges()
            '        'MessageCount = PayrollMessages.Count
            '    End If
            'Else
            Dim ppx = CType(API, PPxPayrollRabbit)
            If ppx.pr_batch_list IsNot Nothing AndAlso ppx.pr_batch_list.Count > 0 Then
                Dim PayrollMessages = (From A In ppx.pr_batch_list
                                       Group A By A.batch_id, A.chk_counter, A.conum, A.empnum, A.msg_body, A.msg_type
                                       Into Group Select batch_id, chk_counter, conum, empnum, msg_body, msg_type
                                       )
                For Each pmsg In PayrollMessages
                    Dim msg As New pr_batch_msg With {.id = Guid.NewGuid,
                                                      .batch_id = pmsg.batch_id,
                                                      .chk_counter = pmsg.chk_counter,
                                                      .conum = pmsg.conum,
                                                      .empnum = pmsg.empnum,
                                                      .msg_body = pmsg.msg_body,
                                                      .msg_type = pmsg.msg_type}
                    Dim MsgID As Guid
                    If Guid.TryParse(msg.msg_body, MsgID) Then
                        msg.msg_body = String.Format("{{{0}}}", msg.msg_body.ToUpper)
                    End If
                    db.pr_batch_msgs.InsertOnSubmit(msg)
                Next
                If Not db.SaveChanges() Then
                    frmLogger.Error("Failed to save payroll messages due to concurrency conflict")
                    ' Continue processing even if save fails
                End If
            End If
            'End If
            MessageCount = (From A In db.pr_batch_msgs Where A.batch_id = API.PowerGridBatchID).Count

            If MessageCount > 0 Then
                logger.Log(WebLogger.LogLevel.Info, "Payroll contains notes. Submitted for specialist for review")
                Submit = False
            End If

            'Insert Nacha for Refund Billing
            db.ExecuteCommand(String.Format("custom.prc_InsertNachaPayrollCustom {0}, {1}", itm.CoNum, NewPayrollRec.PRNUM))

Step4:
            If Submit Then
                API.CheckPayrollStatus()
                If API.PPxPayrollStep = PPxPayrollSteps.Edit Then
                    API.PPxPayrollStep = PPxPayrollSteps.Complete
                End If
                Application.DoEvents()
                'DisplayProgress("Submitting Payroll")

                WebLogger.PayrollProcessStep = 4
                logger.Log(WebLogger.LogLevel.Info, "Submitting Payroll")

                'If Not UsePPxLibrary Then
                '    API.SubmitPayroll(2)
                'Else
                API.SubmitPayroll(itm.CoNum, NewPayrollRec.PRNUM, 2, "")
                'End If

                logger.Log(WebLogger.LogLevel.Info, "Payroll Completed!")

                db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, PayRec)
                PayRec.ProcessStatus = "Done"
                If Not db.SaveChanges() Then
                    frmLogger.Error("Failed to save payroll status due to concurrency conflict")
                    ' Continue processing even if save fails
                End If

                itm.Status = "Submitted"
            End If
            PayRec.ProcessStatus = "Ready"
            If Not db.SaveChanges() Then
                frmLogger.Error("Failed to save payroll ready status due to concurrency conflict")
                ' Continue processing even if save fails
            End If

        Catch ex As Exception
            'EmailErrorMessage(ex)
            logger.Log(WebLogger.LogLevel.Error, ex.Message)
            frmLogger.Error(ex, "Error in AutoPayroll")
        End Try

        'Solomon modified on Jan 5, '23. Exclude W2 Bill and Per Payroll
        Dim BillFreq = (From f In db.COOPTIONs Where f.CONUM = NewPayrollRec.CONUM).FirstOrDefault()?.CO_BILL_FREQ
        If NewPayrollRec.CHECK_DATE.Value.Month = 1 AndAlso Not (NewPayrollRec.PR_DESCR = "W2 Bill" AndAlso BillFreq = "Per Payroll") Then
            db.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, NewPayrollRec)
            NewPayrollRec.CHECK_DATE = New Date(NewPayrollRec.CHECK_DATE.Value.Year - 1, 12, 31)
            If Not db.SaveChanges() Then
                frmLogger.Error("Failed to save check date change due to concurrency conflict")
                ' Continue processing even if save fails
            End If
        End If
Finish:
        WebLogger.PayrollProcessStep = 0
        db.Dispose()
    End Sub


End Class