﻿Imports System.IO
Imports System.Text
Imports Brands_FrontDesk.Wrike
Imports DevExpress.Utils
Imports DevExpress.Utils.Menu
Imports DevExpress.XtraBars
Imports DevExpress.XtraEditors
Imports DevExpress.XtraTreeList
Imports DevExpress.XtraTreeList.Columns

Public Class frmWrike


    Private Property db As dbEPDataDataContext
    Private Property UserPermission As FrontDeskPermission
    Private Property TasksList As List(Of Wrike.Tasks)
    Private api As Wrike.Api
    Private SelectedFolder As Wrike.Folder

    Public Sub New()
        Dim SplashScreenManager1 As DevExpress.XtraSplashScreen.SplashScreenManager = New DevExpress.XtraSplashScreen.SplashScreenManager(Me, GetType(Global.Brands_FrontDesk.WaitForm1), True, False)
        SplashScreenManager1.ShowWaitForm()
        InitializeComponent()
        db = New dbEPDataDataContext(GetConnectionString)
        SplashScreenManager1.CloseWaitForm()
        TabbedControlGroup1.ShowTabHeader = DefaultBoolean.False
        TabbedControlGroup1.SelectedTabPage = lcgTaskDetails
    End Sub

    Public Sub New(co As Decimal)
        MyBase.New()
        slueCoNum.EditValue = co
    End Sub

    Private Async Sub frmWrike_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            lcRoot.ShowProgessPanel
            UserPermission = db.FrontDeskPermissions.Single(Function(u) u.UserName = UserName)
            If UserPermission.WrikeRefreshToken.IsNotNullOrWhiteSpace() Then
                Await Initialize()
                Await LoadTasks()
            End If
            ViewCompanySumarryBindingSource.DataSource = db.view_CompanySumarries.ToList()
            AddHandler UcWrikeAddOrEdit1.OnTaskUpdated, Sub(task As Wrike.Tasks, ee As EventArgs)
                                                            Dim index = TasksList.FindIndex(Function(t) t.id = task.id)
                                                            TasksList(index) = task
                                                            tlTasks.RefreshDataSource()
                                                        End Sub

            lcRoot.HideProgressPanel
        Catch ex As Exception
            lcRoot.HideProgressPanel
            DisplayErrorMessage("Error loading Wrike form", ex)
            Close()
        End Try
    End Sub

    Private Async Function Initialize(Optional reload As Boolean = False) As Task
        Try
            lcRoot.ShowProgessPanel
            api = Await Wrike.Api.Create(reload)
            bsFolders.DataSource = api.Folders
            tlFolders.ExpandAll()
            tlFolders.Nodes.FirstNode.NextNode.Expanded = False
            riCcbeFolders.DataSource = api.Folders

            tlTasks.ForceInitialize()
            For Each udf In api.Account.customFields
                Dim col As TreeListColumn = tlTasks.Columns.ColumnByFieldName(udf.id)
                If col Is Nothing Then col = tlTasks.Columns.Add()
                col.OptionsColumn.ReadOnly = False
                col.OptionsColumn.AllowEdit = False
                col.FieldName = udf.id
                col.Caption = udf.title
                If udf.id = api.CoNumUdfField Then
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Integer
                    col.ColumnEdit = riHleCoNum
                    col.VisibleIndex = 0
                    col.OptionsColumn.AllowEdit = True
                ElseIf udf.id = api.PriorityUdfField Then
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Integer
                    col.OptionsColumn.AllowEdit = False
                    col.VisibleIndex = 1
                ElseIf udf.type = "Checkbox" Then
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Boolean
                ElseIf udf.type = "Date" Then
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.DateTime
                ElseIf udf.type = "Numeric" Then
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Integer
                Else
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object
                End If
                col.Visible = True
            Next
            tlTasks.ForceInitialize()
            If UserPermission.WrikeGridLayout.IsNotNullOrWhiteSpace Then
                SetGridLayout(UserPermission.WrikeGridLayout)
            End If

            pmEmailTemplates.ClearLinks()
            For Each emlTemplate In db.ReportEmailTeplates.Where(Function(t) t.Tag = "Wrike")
                Dim bbi = New BarButtonItem(RibbonControl1.Manager, emlTemplate.Name) With {.Tag = emlTemplate}
                AddHandler bbi.ItemClick, AddressOf bbiEmailClient_ItemClick
                pmEmailTemplates.AddItem(bbi)
            Next
            lcRoot.HideProgressPanel
        Catch ex As Exception
            lcRoot.HideProgressPanel
            DisplayErrorMessage("Error Initialize Wrike data", ex)
        End Try
    End Function

    Private Async Function LoadTasks(Optional taskId As String = "") As Task
        Try
            lcRoot.ShowProgessPanel()
            Dim status As String = ""
            If ccbeStatus.Properties.Items.GetCheckedValues.Any Then
                status = "[" & String.Join(",", ccbeStatus.Properties.Items.GetCheckedValues.Select(Function(i) """" & i.ToString() & """").ToArray()) & "]"
            End If
            Dim conum As Decimal? = Nothing
            If slueCoNum.EditValue IsNot Nothing AndAlso slueCoNum.EditValue.ToString.IsNotNullOrWhiteSpace Then conum = slueCoNum.EditValue
            Dim folder As String = String.Empty
            If SelectedFolder IsNot Nothing AndAlso SelectedFolder.id <> api.Account.rootFolderId Then folder = SelectedFolder.id
            Dim data = Await api.QueryTasks(taskId, folder, status, DateEdit1.DateTime.IsMinDataThenNothing, DateEdit2.DateTime.IsMinDataThenNothing, conum)
            TasksList = data
            tlTasks.DataSource = TasksList
        Catch ex As Exception
            lcRoot.HideProgressPanel()
            DisplayErrorMessage("Error getting Wrike tasks", ex)
        Finally
            lcRoot.HideProgressPanel()
        End Try
    End Function

    Private Async Sub TreeList1_FocusedNodeChanged(sender As Object, e As DevExpress.XtraTreeList.FocusedNodeChangedEventArgs) Handles tlTasks.FocusedNodeChanged
        Try
            Dim task As Wrike.Tasks = tlTasks.GetDataRecordByNode(e.Node)
            Await UcWrikeAddOrEdit1.SetTask(task, api)
        Catch ex As Exception
            DisplayErrorMessage("Error loading Task details.", ex)
        End Try
    End Sub

    Private Sub riHleCoNum_OpenLink(sender As Object, e As DevExpress.XtraEditors.Controls.OpenLinkEventArgs) Handles riHleCoNum.OpenLink
        If e.EditValue IsNot Nothing Then
            MainForm.OpenCompForm(e.EditValue)
            e.Handled = True
        End If
    End Sub

    Private Sub tlLueFolders_Properties_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles slueCoNum.ButtonClick
        If e.Button.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Delete Then TryCast(sender, BaseEdit).EditValue = Nothing
    End Sub

    Private Sub tlLueFolders_EditValueChanged(sender As Object, e As EventArgs)
        If UserPermission IsNot Nothing Then bbiRefresh.PerformClick()
    End Sub

    Private Sub TreeList1_CustomUnboundColumnData(sender As Object, e As DevExpress.XtraTreeList.TreeListCustomColumnDataEventArgs) Handles tlTasks.CustomUnboundColumnData
        Try
            Dim row As Wrike.Tasks = e.Row
            If e.IsGetData Then
                Dim cf = row.customFields?.SingleOrDefault(Function(c) c.id = e.Column.FieldName)
                If cf IsNot Nothing Then
                    If e.Column.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Boolean Then
                        Dim b As Boolean
                        If Boolean.TryParse(cf.value, b) Then
                            e.Value = b
                        End If
                    Else
                        e.Value = cf.value
                    End If
                End If
                'ElseIf e.IsSetData Then
                '    Dim udfList = New List(Of KeyValuePair)
                '    udfList.Add(New KeyValuePair(e.Column.FieldName, e.Value))
                '    Await UpdateTask(row, True, udfList)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error showing custom field in Wrike Form", ex)
        End Try
    End Sub

    Private Async Sub bbiSetUdfFields_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSetUdfFields.ItemClick
        Dim frm = New frmSetUdfFields(api.Account.customFields.ToList())
        frm.ShowDialog()
        api = Await Wrike.Api.Create(True)
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        Dim t = LoadTasks()
    End Sub

    Private Async Sub bbiSetWrikeToken_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSetWrikeToken.ItemClick
        Try
            If UserPermission.WrikeRefreshToken.IsNotNullOrWhiteSpace Then
                If XtraMessageBox.Show("Wrike API key is already set, Would you like to reset it?", "Reset Token?", MessageBoxButtons.YesNo) <> System.Windows.Forms.DialogResult.Yes Then Exit Sub
            End If
            Process.Start("https://www.wrike.com/oauth2/authorize?client_id=byItdZmA&response_type=code")
            Dim input = InputBox("Please enter the wrike Code.", "Wrike API Code")
            If input.IsNotNullOrWhiteSpace() Then
                Dim result = Await Wrike.Api.AuthorizeByToken(input)
                UserPermission.WrikeRefreshToken = result.RefreshToken
                db.SaveChanges()
                Await Initialize()
                Await LoadTasks()
            End If

        Catch ex As Exception
            DisplayErrorMessage("Error getting Wrike API key", ex)
        End Try
    End Sub

    Private Sub tlTasks_PopupMenuShowing(sender As Object, e As DevExpress.XtraTreeList.PopupMenuShowingEventArgs) Handles tlTasks.PopupMenuShowing
        Dim row As Wrike.Tasks = tlTasks.GetDataRecordByNode(tlTasks.FocusedNode)
        e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Edit", Sub()
                                                                          Dim frm = New frmWrikeAddOrEdit(row)
                                                                          frm.ShowDialog()
                                                                      End Sub, My.Resources.edit_16x16))
    End Sub

    Private Sub tlTasks_NodeCellStyle(sender As Object, e As DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs) Handles tlTasks.NodeCellStyle
        Dim row As Wrike.Tasks = tlTasks.GetDataRecordByNode(e.Node)
        If row.dates IsNot Nothing AndAlso row.dates.type = "Planned" Then
            If row.dates.start < Today Then
                e.Appearance.BackColor = Color.LightSalmon
                e.Appearance.ForeColor = Color.Black
            End If
        ElseIf Today.AddDays(-14) > row.updatedDate Then
            e.Appearance.BackColor = Color.LightYellow
            e.Appearance.ForeColor = Color.Black
        End If
    End Sub

    Private Sub bbiSaveLayout_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiSaveLayout.ItemClick
        Using db As New dbEPDataDataContext(GetConnectionString)
            Dim per = (From p In db.FrontDeskPermissions Where p.UserName = UserName).Single
            per.WrikeGridLayout = GetGridLayout()
            db.SaveChanges
        End Using
    End Sub

    Private Function GetGridLayout() As String
        Dim stream = New System.IO.MemoryStream()
        tlTasks.SaveLayoutToStream(stream, OptionsLayoutBase.FullLayout)
        stream.Seek(0, SeekOrigin.Begin)
        Return New StreamReader(stream).ReadToEnd()
    End Function

    Private Sub SetGridLayout(layout As String)
        Dim byteArray = Encoding.ASCII.GetBytes(layout)
        Dim ms = New MemoryStream(byteArray)
        tlTasks.RestoreLayoutFromStream(ms, OptionsLayoutBase.FullLayout)
    End Sub

    Private Sub bbiEmailClient_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiEmail.ItemClick
        Try
            If e.Item.Tag Is Nothing Then Exit Sub
            lcRoot.ShowProgessPanel
            Dim row As Wrike.Tasks = tlTasks.GetDataRecordByNode(tlTasks.FocusedNode)
            If row Is Nothing Then
                XtraMessageBox.Show("Please select a row.")
                Exit Sub
            End If
            Dim emlTemplate As ReportEmailTeplate = e.Item.Tag
            Dim email = New ReportProcessor(row.CoNum, emlTemplate, FileType.Xls)
            email.DefaultParamValues = New List(Of KeyValuePair)
            email.DefaultParamValues.Add(New KeyValuePair("@CoNum", row.CoNum))
            email.DefaultParamValues.Add(New KeyValuePair("@PrContact", UcWrikeAddOrEdit1.teContact.Text))
            email.showParametersForm = True
            Dim result = email.ProcessReport
            Dim emlSender = New ReportSender(result) With {.showWebPost = False}
            If UcWrikeAddOrEdit1.beContactEmail.Text.IsNotNullOrWhiteSpace Then
                emlSender.sendToEmailAddress = UcWrikeAddOrEdit1.beContactEmail.Text
            Else
                emlSender.showEmailList = True
            End If
            emlSender.EmailReport(True)
        Catch ex As Exception
            DisplayErrorMessage("Error sending email.", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Async Sub slueCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles slueCoNum.EditValueChanged
        If UserPermission IsNot Nothing Then Await LoadTasks()
    End Sub

    Private Async Sub bbiFullRefresh_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bbiFullRefresh.ItemClick
        Await Initialize(True)
        Await LoadTasks()
    End Sub

    Private Async Sub tlFolders_FocusedNodeChanged(sender As Object, e As DevExpress.XtraTreeList.FocusedNodeChangedEventArgs) Handles tlFolders.FocusedNodeChanged
        Try
            SelectedFolder = tlFolders.GetDataRecordByNode(e.Node)
            Await LoadTasks()
            If SelectedFolder.project IsNot Nothing Then
                UcWrikeFolderProjectAddOrEdit1.SetFolder(SelectedFolder, api)
                TabbedControlGroup1.SelectedTabPage = lcgProjectDetails
            Else
                TabbedControlGroup1.SelectedTabPage = lcgTaskDetails
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error setting SelectedFolder", ex)
        End Try
    End Sub

    Private Sub tlFolders_PopupMenuShowing(sender As Object, e As DevExpress.XtraTreeList.PopupMenuShowingEventArgs) Handles tlFolders.PopupMenuShowing
        Dim p = tlFolders.CalcHitInfo(e.Point)
        Dim row As Wrike.Folder = tlFolders.GetDataRecordByNode(p.Node)
        If e.Menu.MenuType = DevExpress.XtraTreeList.Menu.TreeListMenuType.Node Then
            If row.scope = "WsFolder" Then
                e.Menu.Items.Add(New DXMenuItem("Copy", Sub()
                                                            ShowCopyFolder(row, p)
                                                        End Sub, My.Resources.addtext_16x16))
                e.Menu.Items.Add(New DXMenuItem("Delete", Sub()
                                                              DeleteFolder(row)
                                                          End Sub, My.Resources.delete_16x16))
                Dim menu = New DXSubMenuItem("Add")
                menu.Items.Add(New DXMenuItem("Project", Sub() AddFolderProject(row.id, True)))
                menu.Items.Add(New DXMenuItem("Folder", Sub() AddFolderProject(row.id, False)))
                e.Menu.Items.Add(menu)
            ElseIf row.scope = "RbFolder" Then
                e.Menu.Items.Add(New DXMenuItem("Restore", Sub()
                                                               RestorFolder(row)
                                                           End Sub))
            End If
        End If
    End Sub

    Private Sub AddFolderProject(parentId As String, project As Boolean)
        Dim folder = New Wrike.Folder
        If project Then folder.project = New Project
        UcWrikeFolderProjectAddOrEdit1.SetFolder(folder, api, parentId)
        TabbedControlGroup1.SelectedTabPage = lcgProjectDetails
    End Sub

    Private Async Sub RestorFolder(ByVal row As Folder)
        Try
            lcRoot.ShowProgessPanel
            Await api.RestoreFolder(row)
            Await Initialize(True)
        Catch ex As Exception
            DisplayErrorMessage("Error deleting folder", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub
    Private Async Sub DeleteFolder(row As Wrike.Folder)
        Try
            If XtraMessageBox.Show($"Are you sure you would like to delete folder: {row.title}?", "Delete", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                lcRoot.ShowProgessPanel
                Await api.DeleteFolder(row)
                Await Initialize(True)
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting folder", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub ShowCopyFolder(row As Wrike.Folder, p As TreeListHitInfo)
        pccCopyFolder.Tag = row
        Dim _point = New Point(p.MousePoint.X + 20, p.MousePoint.Y + 30)
        If pccCopyFolder.Height + p.MousePoint.Y > Height Then
            _point.Y = _point.Y - pccCopyFolder.Height
        End If
        pccCopyFolder.Location = _point
        tlFolders.Enabled = False
        tlTasks.Enabled = False
        teCopyFolderTItle.Text = $"Copy - {row.title}"
        pccCopyFolder.Show()
        teCopyFolderTItle.Focus()
        teCopyFolderTItle.Select()
    End Sub

    Private Sub btnCancelCopy_Click(sender As Object, e As EventArgs) Handles btnCancelCopy.Click
        pccCopyFolder.Hide()
    End Sub

    Private Async Sub btnCopyFolder_Click(sender As Object, e As EventArgs) Handles btnCopyFolder.Click
        Try
            lcRoot.ShowProgessPanel
            Dim row As Wrike.Folder = pccCopyFolder.Tag
            Await api.CopyFolder(row, teCopyFolderTItle.Text)
            pccCopyFolder.Hide()
            Await Initialize(True)
            Await LoadTasks()
        Catch ex As Exception
            DisplayErrorMessage("Error copying folder", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub teCopyFolderTItle_EditValueChanged(sender As Object, e As EventArgs) Handles teCopyFolderTItle.EditValueChanged
        btnCopyFolder.Enabled = teCopyFolderTItle.Text.IsNotNullOrWhiteSpace
    End Sub

    Private Sub pccCopyFolder_VisibleChanged(sender As Object, e As EventArgs) Handles pccCopyFolder.VisibleChanged
        If Not pccCopyFolder.Visible Then
            tlFolders.Enabled = True
            tlTasks.Enabled = True
            pccCopyFolder.Tag = Nothing
        End If
    End Sub

    Private Sub teCopyFolderTItle_KeyDown(sender As Object, e As KeyEventArgs) Handles teCopyFolderTItle.KeyDown
        If e.KeyCode = Keys.Escape Then
            pccCopyFolder.Hide()
        ElseIf e.KeyCode = Keys.Enter Then
            btnCopyFolder.PerformClick()
        End If
    End Sub

    Private Sub tlTasks_MouseClick(sender As Object, e As MouseEventArgs) Handles tlTasks.MouseClick
        TabbedControlGroup1.SelectedTabPage = lcgTaskDetails
    End Sub

    Private Sub tlFolders_MouseClick(sender As Object, e As MouseEventArgs) Handles tlFolders.MouseClick
        If SelectedFolder.project IsNot Nothing Then
            TabbedControlGroup1.SelectedTabPage = lcgProjectDetails
        End If
    End Sub

    Private Sub TabbedControlGroup1_SelectedPageChanged(sender As Object, e As DevExpress.XtraLayout.LayoutTabPageChangedEventArgs) Handles TabbedControlGroup1.SelectedPageChanged
        If e.Page Is lcgTaskDetails Then
            tlTasks.OptionsSelection.InvertSelection = True
            tlTasks.OptionsView.FocusRectStyle = DrawFocusRectStyle.RowFocus
            tlTasks.Appearance.HideSelectionRow.Options.UseFont = True
            tlTasks.OptionsView.ShowIndicator = True
        Else
            tlTasks.OptionsSelection.InvertSelection = False
            tlTasks.OptionsView.FocusRectStyle = DrawFocusRectStyle.CellFocus
            tlTasks.Appearance.HideSelectionRow.Options.UseFont = False
            tlTasks.OptionsView.ShowIndicator = False
        End If
    End Sub

    Private Sub btnCollapsAllFolders_Click(sender As Object, e As EventArgs) Handles btnCollapsAllFolders.Click
        tlFolders.CollapseAll()
    End Sub

    Private Sub btnExpandAllFolders_Click(sender As Object, e As EventArgs) Handles btnExpandAllFolders.Click
        tlFolders.ExpandAll()
    End Sub

    Private Sub tlFolders_CustomDrawNodeIndicator(sender As Object, e As CustomDrawNodeIndicatorEventArgs) Handles tlFolders.CustomDrawNodeIndicator
        Dim f As Wrike.Folder = tlFolders.GetDataRecordByNode(e.Node)
        If f Is Nothing Then
            Exit Sub
        End If
        Select Case f.color
            Case "Purple1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xe1bee7")
            Case "Purple2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xce93d8")
            Case "Purple3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xba68c8")
            Case "Purple4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x8e24aa")
            Case "Indigo1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xd1c4e9")
            Case "Indigo2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xb39ddb")
            Case "Indigo3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x9575cd")
            Case "Indigo4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x5e35b1")
            Case "DarkBlue1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xc5cae9")
            Case "DarkBlue2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x9fa8da")
            Case "DarkBlue3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x7986cb")
            Case "DarkBlue4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x3949ab")
            Case "Blue1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xbbdefb")
            Case "Blue2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x90caf9")
            Case "Blue3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x64b5f6")
            Case "Blue4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x1e88e5")
            Case "Turquoise1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xb2ebf2")
            Case "Turquoise2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x80deea")
            Case "Turquoise3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x4dd0e1")
            Case "Turquoise4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x00acc1")
            Case "DarkCyan1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xb2dfdb")
            Case "DarkCyan2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x80cbc4")
            Case "DarkCyan3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x4db6ac")
            Case "DarkCyan4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x00897b")
            Case "Green1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xc8e6c9")
            Case "Green2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xa5d6a7")
            Case "Green3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x81c784")
            Case "Green4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x43a047")
            Case "YellowGreen1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xe6ee9c")
            Case "YellowGreen2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xdce775")
            Case "YellowGreen3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xc0ca33")
            Case "YellowGreen4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xafb42b")
            Case "Yellow1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xfff59d")
            Case "Yellow2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xffee58")
            Case "Yellow3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xfbc02d")
            Case "Yellow4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xf9a825")
            Case "Orange1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xffcc80")
            Case "Orange2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xffb74d")
            Case "Orange3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xff9800")
            Case "Orange4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xf57c00")
            Case "Red1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xffcdd2")
            Case "Red2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xef9a9a")
            Case "Red3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xe57373")
            Case "Red4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xe53935")
            Case "Pink1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xf8bbd0")
            Case "Pink2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xf48fb1")
            Case "Pink3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xf06292")
            Case "Pink4"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xd81b60")
            Case "None"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xffffff")
            Case "Person"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x000000")
            Case "Gray1"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0xb0bec5")
            Case "Gray2"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x546e7a")
            Case "Gray3"
                e.Appearance.BackColor = ColorTranslator.FromHtml("0x2d3e4f")
        End Select
        Using br = New SolidBrush(e.Appearance.BackColor)
            e.Graphics.FillRectangle(br, e.Bounds)
        End Using
        e.Handled = True
    End Sub

    Private Sub tlFolders_CustomDrawNodeImages(sender As Object, e As CustomDrawNodeImagesEventArgs) Handles tlFolders.CustomDrawNodeImages
        Dim f As Wrike.Folder = tlFolders.GetDataRecordByNode(e.Node)
        If f.project Is Nothing Then
            e.SelectImageIndex = -1
        End If
    End Sub
End Class

