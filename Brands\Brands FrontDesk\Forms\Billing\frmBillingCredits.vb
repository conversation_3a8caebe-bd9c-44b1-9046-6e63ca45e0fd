﻿Imports DevExpress.XtraGrid.Views.Grid

Public Class frmBillingCredits

    Private Property db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
    End Sub

    Private Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString)

            Dim invLogs = db.view_BillingCredits.ToList()
            InvoiceBindingSource.DataSource = invLogs.OrderBy(Function(p) p.CONUM).ThenBy(Function(p) p.ITEM_DATE)
            GridView1.BestFitColumns()
        Catch ex As Exception
            DisplayErrorMessage("Error loading Data", ex)
        End Try
    End Sub

    Private Sub frmManualBillingDraft_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub frmBillingCredits_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        Dim find As DevExpress.XtraGrid.Controls.FindControl = GridView1.GridControl.Controls.Find("FindControlCore", True)(0)
        find.FindEdit.Focus()
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.HitInfo.RowHandle >= 0 AndAlso e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row Then
            Dim Row As view_BillingCredit = Me.GridView1.GetRow(e.HitInfo.RowHandle)
            If Row IsNot Nothing AndAlso Row.Src = "InvoiceCreditsPending" Then
                e.Menu.Items.Clear()
                Dim mItem = New DevExpress.Utils.Menu.DXMenuItem("Delete record", AddressOf OnDeleteRowClick, ImageCollection1.Images(0))
                mItem.Tag = e.HitInfo.RowHandle
                e.Menu.Items.Add(mItem)

                Dim mMoveToInvoiceLog = New DevExpress.Utils.Menu.DXMenuItem("Move to invoice", AddressOf OnMoveToInvoiceLogClick, ImageCollection1.Images(1))
                mMoveToInvoiceLog.Tag = e.HitInfo.RowHandle
                e.Menu.Items.Add(mMoveToInvoiceLog)
            End If
        End If
    End Sub

    Sub OnDeleteRowClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As view_BillingCredit = Me.GridView1.GetRow(rowHandle)
        If Row IsNot Nothing AndAlso Row.Src = "InvoiceCreditsPending" Then
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to delete this row?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                Exit Sub
            End If
            Me.GridView1.DeleteRow(rowHandle)
            db.ExecuteCommand("delete custom.InvoiceCreditsPending where id = " + Row.ICP_ID.ToString())
        End If
    End Sub

    Sub OnMoveToInvoiceLogClick(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As DevExpress.Utils.Menu.DXMenuItem = sender
        Dim rowHandle As Integer = item.Tag
        Dim Row As view_BillingCredit = Me.GridView1.GetRow(rowHandle)
        If Row IsNot Nothing AndAlso Row.Src = "InvoiceCreditsPending" Then
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to move this row to invoice log?", "Confirm Move", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
                Exit Sub
            End If
            db.ExecuteCommand("exec custom.prc_MoveInvCrPendingToInvoiceLog " + Row.ICP_ID.ToString())
            Me.GridView1.DeleteRow(rowHandle)
        End If
    End Sub

    Private Sub GridView1_ShowingEditor(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles GridView1.ShowingEditor
        Dim Row As view_BillingCredit = Me.GridView1.GetRow(GridView1.FocusedRowHandle)
        e.Cancel = (Row.Src <> "InvoiceCreditsPending")
    End Sub

    Private Sub GridView1_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridView1.RowUpdated
        If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to update this row?", "Confirm Update", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = System.Windows.Forms.DialogResult.No Then
            LoadData()
            Exit Sub
        End If

        Dim Row As view_BillingCredit = Me.GridView1.GetRow(GridView1.FocusedRowHandle)
        Dim Record As InvoiceCreditsPending = db.InvoiceCreditsPendings.Where(Function(f) f.ID = Row.ICP_ID).First()

        Record.ITEM_DATE = Row.ITEM_DATE
        Record.PRICE = Row.PRICE
        Record.CHK_ENTRIES = Row.CHK_ENTRIES
        Record.PRNUM = Row.PRNUM
        Record.ITEM_NAME = Row.ITEM_NAME
        Record.DIVNUM = Row.DIVNUM
        db.SubmitChanges()
    End Sub
End Class
