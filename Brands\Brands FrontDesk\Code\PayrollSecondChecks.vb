﻿Public Class PayrollSecondChecks

    Property API As PPxPayrollInterface
    Property DB As dbEPDataDataContext
    Property CoNum As Decimal
    Property PrNum As Decimal

    Property SecondChecks As List(Of pr_batch_overrides_setup)

    Dim whoIsCalling As String = ""
    Dim PayrollEnt As PAYROLL

    Public Sub New(CoNum As Decimal, PrNum As Decimal)
        Me.CoNum = CoNum
        Me.PrNum = PrNum
        Me.API = EP_API()

        API.CompanyID = CoNum

        'If Not UsePPxLibrary Then
        '    API.CheckPayrollStatus()
        'Else
        '    API.CheckPayrollStatus(CoNum)
        'End If

        API.CheckPayrollStatus(CoNum)

        Me.LoadSecondChecks()
    End Sub

    Private Function LoadSecondChecks() As Boolean
        If DB Is Nothing Then DB = New dbEPDataDataContext(GetConnectionString)
        Me.SecondChecks = New List(Of pr_batch_overrides_setup)

        Dim qSecondChecks = (From A In DB.pr_batch_overrides_setups
                             Where A.CoNum = Me.CoNum AndAlso A.RecordType = "Second Check"
                            ).ToList

        If qSecondChecks.Count > 0 Then
            PayrollEnt = (From A In DB.PAYROLLs Where A.CONUM = Me.CoNum AndAlso A.PRNUM = Me.PrNum).Single
            Dim PaidEmployees = (From A In DB.CHK_MASTs Where A.CONUM = Me.CoNum AndAlso A.PAYROLL_NUM = Me.PrNum AndAlso A.GROSS > 0 Select A.EMPNUM).ToList

            Dim CalData = (From A In DB.CALENDARs Where A.conum = Me.CoNum AndAlso A.payroll_num = Me.PrNum).ToList
            If CalData.Count = 0 Then
                'not attached to calendar
                'skip
                Return False
            End If
            Dim CalInfo = New List(Of CalendarInfo)
            For X = 0 To CalData.Count - 1
                Dim cal = CalData(X)
                Dim NextCal = (From A In DB.CALENDARs
                               Where A.conum = Me.CoNum AndAlso A.period_id = cal.period_id AndAlso A.process_date > cal.process_date
                               Order By A.process_date).FirstOrDefault
                Dim PrevCal = (From A In DB.CALENDARs
                               Where A.conum = Me.CoNum AndAlso A.period_id = cal.period_id AndAlso A.process_date < cal.process_date
                               Order By A.process_date Descending).FirstOrDefault
                Dim IsLastOfMonth = NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Month <> cal.check_date.GetValueOrDefault.Month
                Dim IsLastOfQtr = NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Quarter <> cal.check_date.GetValueOrDefault.Quarter
                Dim IsFirstOfQtr = PrevCal Is Nothing OrElse PrevCal.check_date.GetValueOrDefault.Quarter <> cal.check_date.GetValueOrDefault.Quarter
                CalInfo.Add(New CalendarInfo With {.CalendarSet = cal.period_id,
                                                   .Prd = cal.prd,
                                                   .IsSupp = PayrollEnt.SUPP_PR = "YES",
                                                   .LastOfMonth = If(IsLastOfMonth, "Last Of Month", ""),
                                                   .Frequency = cal.frequency,
                                                   .IsFirstOfQtr = IsFirstOfQtr,
                                                   .IsLastOfQtr = IsLastOfQtr})
            Next

            For Each check In qSecondChecks
                Dim HasFirstCheck = PaidEmployees.Contains(check.EmpNum)
                If Not HasFirstCheck Then Continue For

                Dim IsPay = (From A In CalInfo Where (A.Prd = 1 AndAlso check.Prd1) _
                                                 OrElse (A.Prd = 2 AndAlso check.Prd2) _
                                                 OrElse (A.Prd = 3 AndAlso check.Prd3) _
                                                 OrElse (A.Prd = 4 AndAlso check.Prd4) _
                                                 OrElse (A.Prd = 5 AndAlso check.Prd5) _
                                                 OrElse (Not String.IsNullOrEmpty(A.LastOfMonth) AndAlso check.LastOfMonth) _
                                                 OrElse (A.IsLastOfQtr AndAlso check.LastOfQtr.GetValueOrDefault) _
                                                 OrElse (A.IsFirstOfQtr AndAlso check.FirstOfQtr.GetValueOrDefault)).Any

                If Not String.IsNullOrEmpty(check.FilterToPayFreq) AndAlso Not CalInfo.Any(Function(p) p.Frequency = check.FilterToPayFreq) Then
                    IsPay = False
                End If

                If IsPay OrElse check.AllPrds.GetValueOrDefault Then
                    Me.SecondChecks.Add(check)
                End If
            Next
        End If

        Return Me.SecondChecks.Count > 0
    End Function

    Public Async Function ProcessSecondChecks(DisplayProgress As Boolean) As Threading.Tasks.Task(Of ProcessResults)
        If SecondChecks Is Nothing Then
            Me.LoadSecondChecks()
        End If

        If Me.SecondChecks.Count = 0 Then
            Return "No Checks"
        End If

        'Create PowerGrid

        'If Not UsePPxLibrary Then
        '    Dim ppx = CType(API, PPxPayroll)
        '    Dim Payroll = ppx.PayrollAPI.GetSinglePayrollInfo(EP_API.ProviderID, Me.CoNum, Me.PrNum, -1)
        'Else
        '    API.GetSinglePayrollInfo(Me.CoNum, Me.PrNum)
        'End If

        API.GetSinglePayrollInfo(Me.CoNum, Me.PrNum)

        Dim BatchList = New pr_batch_list With {.run_add_checks = "NO",
                                            .run_auto_hours = "NO",
                                            .run_auto_pays = "YES",
                                            .run_auto_deds = "YES",
                                            .run_auto_memos = "YES",
                                            .run_sick = "YES",
                                            .run_vacation = "YES",
                                            .run_personal = "YES",
                                            .run_pay_salary = "YES",
                                            .run_dd_flag = "YES",
                                            .run_override_rate = "NONE",
                                            .conum = Me.CoNum,
                                            .status = "In Progress",
                                            .obj_ref = 101,
                                            .provider_id = 0,
                                            .name = String.Format("{0}_{1}_{2}_{3}", Me.CoNum, Me.PrNum, "Second Checks", PayrollEnt.CHECK_DATE.Value.ToString("MM-dd-yyyy"))
                                            }

        Dim frmPowerGrid = New frmBrandsPowerGrid With {.CoNum = Me.CoNum, .PrNum = Me.PrNum, .BatchList = BatchList, .IsAuto = DisplayProgress, .SecondChecksOnly = True}
        frmPowerGrid.LoadData()

        Await frmPowerGrid.Process(True)
        If DisplayProgress Then
            If frmPowerGrid.ProcessingStatusForm IsNot Nothing Then _
                frmPowerGrid.ProcessingStatusForm.Owner = MainForm
        End If

        Dim results As ProcessResults
        If frmPowerGrid.AutoIsSubmitted Then
            results = ProcessResults.Submitted
        ElseIf frmPowerGrid.AutoIsProcessed Then
            results = ProcessResults.Processed
        Else
            results = ProcessResults.Unknown
        End If
        frmPowerGrid.Dispose()

        Return results

    End Function

    Public Enum ProcessResults
        Unknown
        Submitted
        Processed
    End Enum

End Class
