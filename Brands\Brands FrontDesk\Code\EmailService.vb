﻿Public Class EmailService

    Private Property _fromAddress As String
    Property _fromAddressDisplay As String
    Property ToEmail As List(Of String) = New List(Of String)
    Property CcEmail As List(Of String) = New List(Of String)
    Property BccEmail As List(Of String) = New List(Of String)
    Property Attachments As List(Of String) = New List(Of String)
    Property Subject As String
    Property Body As String

    Property CoNUm As Decimal
    Property Category As String
    Property ReplyOrFwdToEmail As Email
    Property CreateNewTicket As Boolean = False
    Dim db As dbEPDataDataContext
    Public Property _NewEmail As Email = Nothing

    Public Sub New()
        db = New dbEPDataDataContext(GetConnectionString)
        Dim user = db.DBUSERs.Single(Function(u) u.name.ToLower = UserName.ToLower)
        _fromAddress = user.email
    End Sub

    Public Sub New(_fromAddress As String)
        Me._fromAddress = _fromAddress
        db = New dbEPDataDataContext(GetConnectionString)
    End Sub

    Public Sub New(_fromAddress As String, _fromAddressDisplay As String)
        Me._fromAddress = _fromAddress
        Me._fromAddressDisplay = _fromAddressDisplay
        db = New dbEPDataDataContext(GetConnectionString)
    End Sub

    Public Sub SendEmail()
        Dim MM = CreateMailMessage()
        Dim Client As New Net.Mail.SmtpClient()
        Client.Send(MM)
        If _NewEmail IsNot Nothing Then
            _NewEmail.TryMarkDone
        End If
    End Sub

    Private Function CreateMailMessage() As Net.Mail.MailMessage
        If _fromAddressDisplay.IsNullOrWhiteSpace Then
            Dim user = db.DBUSERs.Single(Function(u) u.name.ToLower = UserName.ToLower)
            _fromAddressDisplay = user.name.Replace(".", " ")
        End If

        Dim MM As New Net.Mail.MailMessage
        MM.IsBodyHtml = Body.StartsWith("<") AndAlso Body.EndsWith(">")
        MM.From = New Net.Mail.MailAddress($"{_fromAddressDisplay} <{_fromAddress}>", _fromAddressDisplay)
        MM.Sender = New Net.Mail.MailAddress($"{_fromAddressDisplay} <{_fromAddress}>", _fromAddressDisplay)

        ToEmail.ForEach(Sub(e) MM.To.Add(e.Replace(";", ",")))
        CcEmail.ForEach(Sub(e) MM.CC.Add(e.Replace(";", ",")))
        BccEmail.ForEach(Sub(e) MM.Bcc.Add(e.Replace(";", ",")))

        MM.Subject = Subject
        MM.Body = Body

        For Each a In Attachments
            MM.Attachments.Add(New System.Net.Mail.Attachment(a))
        Next

        If CreateNewTicket Then
            _NewEmail = FaxAndEmailUtilities.CreateTicket(MM, Category, CoNUm)
        ElseIf ReplyOrFwdToEmail IsNot Nothing Then
            _NewEmail = FaxAndEmailUtilities.SaveReplyAndFwdTicket(MM, ReplyOrFwdToEmail, db)
        End If

        Return MM
    End Function

End Class
