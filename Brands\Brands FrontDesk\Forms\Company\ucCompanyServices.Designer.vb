﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucCompanyServices
    Inherits System.Windows.Forms.UserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.prc_GetCompanyServicesResultBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.pnlTop = New DevExpress.XtraEditors.SidePanel()
        Me.btnPrint = New DevExpress.XtraEditors.SimpleButton()
        Me.GridControlRelatedCompanies = New DevExpress.XtraGrid.GridControl()
        Me.GridViewRelatedCompanies = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riButtonEditCoNum = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridControlDivision = New DevExpress.XtraGrid.GridControl()
        Me.GridViewDivisions = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ServicesOfferedBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.ServiceBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.xtpServices = New DevExpress.XtraTab.XtraTabPage()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colServiceID = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colEnrolled = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colLastOffered = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colToOffer = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colComments = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.pnlBottom = New DevExpress.XtraEditors.SidePanel()
        Me.GridControlServicesOffered = New DevExpress.XtraGrid.GridControl()
        Me.GridViewServicesOffered = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colServiceID1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.riLookUpEditServices = New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
        Me.colDateOffered = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemDateEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
        Me.colByUser = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colToName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colToNumberExt = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colResponse = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFollowUpDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNotes = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemMemoEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.btnAddOffer = New DevExpress.XtraEditors.SimpleButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.xtpPriceIncrease = New DevExpress.XtraTab.XtraTabPage()
        Me.lblNoData = New DevExpress.XtraEditors.LabelControl()
        Me.SpreadsheetControl1 = New DevExpress.XtraSpreadsheet.SpreadsheetControl()
        CType(Me.prc_GetCompanyServicesResultBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        CType(Me.GridControlRelatedCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewRelatedCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riButtonEditCoNum, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControlDivision, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewDivisions, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ServicesOfferedBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ServiceBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.xtpServices.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlBottom.SuspendLayout()
        CType(Me.GridControlServicesOffered, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridViewServicesOffered, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.riLookUpEditServices, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemDateEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemDateEdit1.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.xtpPriceIncrease.SuspendLayout()
        Me.SuspendLayout()
        '
        'prc_GetCompanyServicesResultBindingSource
        '
        Me.prc_GetCompanyServicesResultBindingSource.DataSource = GetType(Brands_FrontDesk.prc_GetCompanyServicesResult)
        '
        'pnlTop
        '
        Me.pnlTop.Controls.Add(Me.btnPrint)
        Me.pnlTop.Controls.Add(Me.GridControlRelatedCompanies)
        Me.pnlTop.Controls.Add(Me.GridControlDivision)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlTop.Location = New System.Drawing.Point(0, 0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(708, 149)
        Me.pnlTop.TabIndex = 2
        Me.pnlTop.Text = "SidePanel2"
        '
        'btnPrint
        '
        Me.btnPrint.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.print_16x16
        Me.btnPrint.Location = New System.Drawing.Point(643, 3)
        Me.btnPrint.Name = "btnPrint"
        Me.btnPrint.Size = New System.Drawing.Size(59, 23)
        Me.btnPrint.TabIndex = 2
        Me.btnPrint.Text = "Print"
        '
        'GridControlRelatedCompanies
        '
        Me.GridControlRelatedCompanies.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.GridControlRelatedCompanies.Location = New System.Drawing.Point(409, 31)
        Me.GridControlRelatedCompanies.MainView = Me.GridViewRelatedCompanies
        Me.GridControlRelatedCompanies.Name = "GridControlRelatedCompanies"
        Me.GridControlRelatedCompanies.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.riButtonEditCoNum})
        Me.GridControlRelatedCompanies.Size = New System.Drawing.Size(296, 106)
        Me.GridControlRelatedCompanies.TabIndex = 1
        Me.GridControlRelatedCompanies.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewRelatedCompanies})
        '
        'GridViewRelatedCompanies
        '
        Me.GridViewRelatedCompanies.Appearance.ViewCaption.Options.UseTextOptions = True
        Me.GridViewRelatedCompanies.Appearance.ViewCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridViewRelatedCompanies.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2})
        Me.GridViewRelatedCompanies.GridControl = Me.GridControlRelatedCompanies
        Me.GridViewRelatedCompanies.LevelIndent = 0
        Me.GridViewRelatedCompanies.Name = "GridViewRelatedCompanies"
        Me.GridViewRelatedCompanies.OptionsBehavior.ReadOnly = True
        Me.GridViewRelatedCompanies.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridViewRelatedCompanies.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.GridViewRelatedCompanies.OptionsView.ColumnAutoWidth = False
        Me.GridViewRelatedCompanies.OptionsView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways
        Me.GridViewRelatedCompanies.OptionsView.ShowGroupPanel = False
        Me.GridViewRelatedCompanies.OptionsView.ShowIndicator = False
        Me.GridViewRelatedCompanies.OptionsView.ShowViewCaption = True
        Me.GridViewRelatedCompanies.PreviewIndent = 0
        Me.GridViewRelatedCompanies.ViewCaption = "Related Companies"
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "Co #"
        Me.GridColumn1.ColumnEdit = Me.riButtonEditCoNum
        Me.GridColumn1.FieldName = "CONUM"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        '
        'riButtonEditCoNum
        '
        Me.riButtonEditCoNum.AutoHeight = False
        Me.riButtonEditCoNum.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Search)})
        Me.riButtonEditCoNum.Name = "riButtonEditCoNum"
        '
        'GridColumn2
        '
        Me.GridColumn2.Caption = "Co Name"
        Me.GridColumn2.FieldName = "CO_NAME"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        '
        'GridControlDivision
        '
        Me.GridControlDivision.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.GridControlDivision.Location = New System.Drawing.Point(11, 31)
        Me.GridControlDivision.MainView = Me.GridViewDivisions
        Me.GridControlDivision.Name = "GridControlDivision"
        Me.GridControlDivision.Size = New System.Drawing.Size(392, 106)
        Me.GridControlDivision.TabIndex = 0
        Me.GridControlDivision.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewDivisions})
        '
        'GridViewDivisions
        '
        Me.GridViewDivisions.Appearance.ViewCaption.Options.UseTextOptions = True
        Me.GridViewDivisions.Appearance.ViewCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridViewDivisions.GridControl = Me.GridControlDivision
        Me.GridViewDivisions.LevelIndent = 0
        Me.GridViewDivisions.Name = "GridViewDivisions"
        Me.GridViewDivisions.OptionsBehavior.ReadOnly = True
        Me.GridViewDivisions.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridViewDivisions.OptionsSelection.EnableAppearanceFocusedRow = False
        Me.GridViewDivisions.OptionsView.ColumnAutoWidth = False
        Me.GridViewDivisions.OptionsView.ShowGroupPanel = False
        Me.GridViewDivisions.OptionsView.ShowIndicator = False
        Me.GridViewDivisions.OptionsView.ShowViewCaption = True
        Me.GridViewDivisions.PreviewIndent = 0
        Me.GridViewDivisions.ViewCaption = "Divisions"
        '
        'ServicesOfferedBindingSource
        '
        Me.ServicesOfferedBindingSource.DataSource = GetType(Brands_FrontDesk.ServicesOffered)
        '
        'ServiceBindingSource
        '
        Me.ServiceBindingSource.DataSource = GetType(Brands_FrontDesk.Service)
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.XtraTabControl1.Location = New System.Drawing.Point(0, 149)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.xtpServices
        Me.XtraTabControl1.Size = New System.Drawing.Size(708, 561)
        Me.XtraTabControl1.TabIndex = 4
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.xtpServices, Me.xtpPriceIncrease})
        '
        'xtpServices
        '
        Me.xtpServices.Appearance.PageClient.BackColor = System.Drawing.Color.DarkRed
        Me.xtpServices.Appearance.PageClient.Options.UseBackColor = True
        Me.xtpServices.Controls.Add(Me.GridControl1)
        Me.xtpServices.Controls.Add(Me.pnlBottom)
        Me.xtpServices.Name = "xtpServices"
        Me.xtpServices.Size = New System.Drawing.Size(706, 532)
        Me.xtpServices.Text = "Company Services"
        '
        'GridControl1
        '
        Me.GridControl1.DataSource = Me.prc_GetCompanyServicesResultBindingSource
        Me.GridControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControl1.Location = New System.Drawing.Point(0, 0)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(706, 271)
        Me.GridControl1.TabIndex = 1
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.Row.Font = New System.Drawing.Font("Segoe UI Semibold", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.Appearance.Row.Options.UseFont = True
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colServiceID, Me.colEnrolled, Me.colLastOffered, Me.colToOffer, Me.colComments})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.LevelIndent = 0
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.PreviewIndent = 0
        '
        'colServiceID
        '
        Me.colServiceID.Caption = "Service"
        Me.colServiceID.FieldName = "ServiceID"
        Me.colServiceID.Name = "colServiceID"
        Me.colServiceID.Visible = True
        Me.colServiceID.VisibleIndex = 0
        Me.colServiceID.Width = 116
        '
        'colEnrolled
        '
        Me.colEnrolled.FieldName = "Enrolled"
        Me.colEnrolled.Name = "colEnrolled"
        Me.colEnrolled.Visible = True
        Me.colEnrolled.VisibleIndex = 1
        Me.colEnrolled.Width = 116
        '
        'colLastOffered
        '
        Me.colLastOffered.DisplayFormat.FormatString = "d"
        Me.colLastOffered.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colLastOffered.FieldName = "LastOffered"
        Me.colLastOffered.Name = "colLastOffered"
        Me.colLastOffered.Visible = True
        Me.colLastOffered.VisibleIndex = 2
        '
        'colToOffer
        '
        Me.colToOffer.Caption = "Offer"
        Me.colToOffer.FieldName = "ToOffer"
        Me.colToOffer.Name = "colToOffer"
        Me.colToOffer.Width = 40
        '
        'colComments
        '
        Me.colComments.FieldName = "Comments"
        Me.colComments.Name = "colComments"
        Me.colComments.Width = 195
        '
        'pnlBottom
        '
        Me.pnlBottom.Controls.Add(Me.GridControlServicesOffered)
        Me.pnlBottom.Controls.Add(Me.Panel1)
        Me.pnlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlBottom.Location = New System.Drawing.Point(0, 271)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(706, 261)
        Me.pnlBottom.TabIndex = 4
        Me.pnlBottom.Text = "SidePanel3"
        '
        'GridControlServicesOffered
        '
        Me.GridControlServicesOffered.DataSource = Me.ServicesOfferedBindingSource
        Me.GridControlServicesOffered.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControlServicesOffered.Location = New System.Drawing.Point(0, 27)
        Me.GridControlServicesOffered.MainView = Me.GridViewServicesOffered
        Me.GridControlServicesOffered.Name = "GridControlServicesOffered"
        Me.GridControlServicesOffered.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemMemoEdit1, Me.RepositoryItemDateEdit1, Me.riLookUpEditServices})
        Me.GridControlServicesOffered.Size = New System.Drawing.Size(706, 234)
        Me.GridControlServicesOffered.TabIndex = 0
        Me.GridControlServicesOffered.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridViewServicesOffered})
        '
        'GridViewServicesOffered
        '
        Me.GridViewServicesOffered.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colServiceID1, Me.colDateOffered, Me.colByUser, Me.colToName, Me.colToNumberExt, Me.colResponse, Me.colFollowUpDate, Me.colNotes})
        Me.GridViewServicesOffered.GridControl = Me.GridControlServicesOffered
        Me.GridViewServicesOffered.LevelIndent = 0
        Me.GridViewServicesOffered.Name = "GridViewServicesOffered"
        Me.GridViewServicesOffered.OptionsBehavior.EditingMode = DevExpress.XtraGrid.Views.Grid.GridEditingMode.EditForm
        Me.GridViewServicesOffered.OptionsEditForm.EditFormColumnCount = 1
        Me.GridViewServicesOffered.PreviewIndent = 0
        Me.GridViewServicesOffered.ViewCaption = "Services Offerred"
        '
        'colServiceID1
        '
        Me.colServiceID1.Caption = "Service"
        Me.colServiceID1.ColumnEdit = Me.riLookUpEditServices
        Me.colServiceID1.FieldName = "ServiceID"
        Me.colServiceID1.Name = "colServiceID1"
        Me.colServiceID1.Visible = True
        Me.colServiceID1.VisibleIndex = 0
        Me.colServiceID1.Width = 37
        '
        'riLookUpEditServices
        '
        Me.riLookUpEditServices.AutoHeight = False
        Me.riLookUpEditServices.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.riLookUpEditServices.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("ServiceID", "Service")})
        Me.riLookUpEditServices.DataSource = Me.ServiceBindingSource
        Me.riLookUpEditServices.DisplayMember = "ServiceID"
        Me.riLookUpEditServices.Name = "riLookUpEditServices"
        Me.riLookUpEditServices.NullText = ""
        Me.riLookUpEditServices.ShowHeader = False
        Me.riLookUpEditServices.ValueMember = "ServiceID"
        '
        'colDateOffered
        '
        Me.colDateOffered.ColumnEdit = Me.RepositoryItemDateEdit1
        Me.colDateOffered.FieldName = "DateOffered"
        Me.colDateOffered.Name = "colDateOffered"
        Me.colDateOffered.Visible = True
        Me.colDateOffered.VisibleIndex = 1
        Me.colDateOffered.Width = 37
        '
        'RepositoryItemDateEdit1
        '
        Me.RepositoryItemDateEdit1.AutoHeight = False
        Me.RepositoryItemDateEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemDateEdit1.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemDateEdit1.EditFormat.FormatString = "g"
        Me.RepositoryItemDateEdit1.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.RepositoryItemDateEdit1.Name = "RepositoryItemDateEdit1"
        '
        'colByUser
        '
        Me.colByUser.FieldName = "ByUser"
        Me.colByUser.Name = "colByUser"
        Me.colByUser.Visible = True
        Me.colByUser.VisibleIndex = 2
        Me.colByUser.Width = 37
        '
        'colToName
        '
        Me.colToName.FieldName = "ToName"
        Me.colToName.Name = "colToName"
        Me.colToName.Visible = True
        Me.colToName.VisibleIndex = 3
        Me.colToName.Width = 37
        '
        'colToNumberExt
        '
        Me.colToNumberExt.Caption = "Num / Ext."
        Me.colToNumberExt.FieldName = "ToNumberExt"
        Me.colToNumberExt.Name = "colToNumberExt"
        Me.colToNumberExt.Visible = True
        Me.colToNumberExt.VisibleIndex = 4
        Me.colToNumberExt.Width = 37
        '
        'colResponse
        '
        Me.colResponse.FieldName = "Response"
        Me.colResponse.Name = "colResponse"
        Me.colResponse.Visible = True
        Me.colResponse.VisibleIndex = 5
        Me.colResponse.Width = 37
        '
        'colFollowUpDate
        '
        Me.colFollowUpDate.FieldName = "FollowUpDate"
        Me.colFollowUpDate.Name = "colFollowUpDate"
        Me.colFollowUpDate.Visible = True
        Me.colFollowUpDate.VisibleIndex = 6
        Me.colFollowUpDate.Width = 37
        '
        'colNotes
        '
        Me.colNotes.ColumnEdit = Me.RepositoryItemMemoEdit1
        Me.colNotes.FieldName = "Notes"
        Me.colNotes.Name = "colNotes"
        Me.colNotes.Visible = True
        Me.colNotes.VisibleIndex = 7
        Me.colNotes.Width = 37
        '
        'RepositoryItemMemoEdit1
        '
        Me.RepositoryItemMemoEdit1.Name = "RepositoryItemMemoEdit1"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.btnAddOffer)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 1)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(706, 26)
        Me.Panel1.TabIndex = 1
        '
        'btnAddOffer
        '
        Me.btnAddOffer.Location = New System.Drawing.Point(156, 3)
        Me.btnAddOffer.Name = "btnAddOffer"
        Me.btnAddOffer.Size = New System.Drawing.Size(75, 20)
        Me.btnAddOffer.TabIndex = 1
        Me.btnAddOffer.Text = "Add Record"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(3, 2)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(132, 20)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "Services Offerred"
        '
        'xtpPriceIncrease
        '
        Me.xtpPriceIncrease.Controls.Add(Me.lblNoData)
        Me.xtpPriceIncrease.Controls.Add(Me.SpreadsheetControl1)
        Me.xtpPriceIncrease.Name = "xtpPriceIncrease"
        Me.xtpPriceIncrease.Size = New System.Drawing.Size(706, 532)
        Me.xtpPriceIncrease.Text = "Price Increase"
        '
        'lblNoData
        '
        Me.lblNoData.Appearance.Font = New System.Drawing.Font("Tahoma", 28.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel)
        Me.lblNoData.Appearance.Options.UseFont = True
        Me.lblNoData.Location = New System.Drawing.Point(31, 27)
        Me.lblNoData.Name = "lblNoData"
        Me.lblNoData.Size = New System.Drawing.Size(219, 34)
        Me.lblNoData.TabIndex = 1
        Me.lblNoData.Text = "No Data Available"
        Me.lblNoData.Visible = False
        '
        'SpreadsheetControl1
        '
        Me.SpreadsheetControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SpreadsheetControl1.Location = New System.Drawing.Point(0, 0)
        Me.SpreadsheetControl1.Name = "SpreadsheetControl1"
        Me.SpreadsheetControl1.Size = New System.Drawing.Size(706, 532)
        Me.SpreadsheetControl1.TabIndex = 0
        Me.SpreadsheetControl1.Text = "SpreadsheetControl1"
        '
        'ucCompanyServices
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.pnlTop)
        Me.Name = "ucCompanyServices"
        Me.Size = New System.Drawing.Size(708, 710)
        CType(Me.prc_GetCompanyServicesResultBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        CType(Me.GridControlRelatedCompanies, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewRelatedCompanies, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riButtonEditCoNum, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControlDivision, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewDivisions, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ServicesOfferedBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ServiceBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.xtpServices.ResumeLayout(False)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlBottom.ResumeLayout(False)
        CType(Me.GridControlServicesOffered, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridViewServicesOffered, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.riLookUpEditServices, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemDateEdit1.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemDateEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.xtpPriceIncrease.ResumeLayout(False)
        Me.xtpPriceIncrease.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents prc_GetCompanyServicesResultBindingSource As BindingSource
    Friend WithEvents pnlTop As DevExpress.XtraEditors.SidePanel
    Friend WithEvents GridControlDivision As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridViewDivisions As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridControlRelatedCompanies As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridViewRelatedCompanies As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents btnPrint As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ServicesOfferedBindingSource As BindingSource
    Friend WithEvents ServiceBindingSource As BindingSource
    Friend WithEvents riButtonEditCoNum As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents xtpServices As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents pnlBottom As DevExpress.XtraEditors.SidePanel
    Friend WithEvents GridControlServicesOffered As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridViewServicesOffered As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colServiceID1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents riLookUpEditServices As DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit
    Friend WithEvents colDateOffered As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemDateEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
    Friend WithEvents colByUser As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colToName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colToNumberExt As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colResponse As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFollowUpDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNotes As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemMemoEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit
    Friend WithEvents Panel1 As Panel
    Friend WithEvents btnAddOffer As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label1 As Label
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colServiceID As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colEnrolled As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colLastOffered As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colToOffer As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colComments As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents xtpPriceIncrease As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents SpreadsheetControl1 As DevExpress.XtraSpreadsheet.SpreadsheetControl
    Friend WithEvents lblNoData As DevExpress.XtraEditors.LabelControl
End Class
