﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class ucAccountLeadDetails
    Inherits DevExpress.XtraEditors.XtraUserControl

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ucAccountLeadDetails))
        Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
        Me.MemoEdit1 = New DevExpress.XtraEditors.MemoEdit()
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.hllcEditNotes = New DevExpress.XtraEditors.HyperlinkLabelControl()
        Me.btnAddFile = New DevExpress.XtraEditors.SimpleButton()
        Me.gcFiles = New DevExpress.XtraGrid.GridControl()
        Me.bsRecentFiles = New System.Windows.Forms.BindingSource(Me.components)
        Me.gvFiles = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colFileName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCreateDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colFilePath = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colExtension = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colReportName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNum1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colCoNumDef = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonEdit4 = New DevExpress.XtraEditors.ButtonEdit()
        Me.bsAccountLead = New System.Windows.Forms.BindingSource(Me.components)
        Me.ButtonEdit3 = New DevExpress.XtraEditors.ButtonEdit()
        Me.ButtonEdit2 = New DevExpress.XtraEditors.ButtonEdit()
        Me.TextEdit5 = New DevExpress.XtraEditors.TextEdit()
        Me.ButtonEdit1 = New DevExpress.XtraEditors.ButtonEdit()
        Me.TextEdit4 = New DevExpress.XtraEditors.TextEdit()
        Me.SpinEdit1 = New DevExpress.XtraEditors.SpinEdit()
        Me.DateEdit3 = New DevExpress.XtraEditors.DateEdit()
        Me.CheckEdit6 = New DevExpress.XtraEditors.CheckEdit()
        Me.DateEdit2 = New DevExpress.XtraEditors.DateEdit()
        Me.DateEdit1 = New DevExpress.XtraEditors.DateEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.CheckEdit5 = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEdit4 = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEdit3 = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEdit2 = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.SearchLookUpEdit1 = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.bsCompanies = New System.Windows.Forms.BindingSource(Me.components)
        Me.SearchLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.meNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.cbeStatus = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LayoutControlItem40 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
        Me.lcgLeadDetails = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem21 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem22 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem23 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem24 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgOnlineEnrollment = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem39 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lcgAttachments = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem27 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem28 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.lcgNotes = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem26 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem25 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.lcRoot.SuspendLayout()
        CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.gcFiles, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsRecentFiles, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.gvFiles, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ButtonEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsAccountLead, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ButtonEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ButtonEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ButtonEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SpinEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.bsCompanies, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.meNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbeStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem40, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgLeadDetails, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgOnlineEnrollment, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem39, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgAttachments, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem28, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lcgNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lcRoot
        '
        Me.lcRoot.Controls.Add(Me.MemoEdit1)
        Me.lcRoot.Controls.Add(Me.PanelControl1)
        Me.lcRoot.Controls.Add(Me.hllcEditNotes)
        Me.lcRoot.Controls.Add(Me.btnAddFile)
        Me.lcRoot.Controls.Add(Me.gcFiles)
        Me.lcRoot.Controls.Add(Me.LabelControl3)
        Me.lcRoot.Controls.Add(Me.ButtonEdit4)
        Me.lcRoot.Controls.Add(Me.ButtonEdit3)
        Me.lcRoot.Controls.Add(Me.ButtonEdit2)
        Me.lcRoot.Controls.Add(Me.TextEdit5)
        Me.lcRoot.Controls.Add(Me.ButtonEdit1)
        Me.lcRoot.Controls.Add(Me.TextEdit4)
        Me.lcRoot.Controls.Add(Me.SpinEdit1)
        Me.lcRoot.Controls.Add(Me.DateEdit3)
        Me.lcRoot.Controls.Add(Me.CheckEdit6)
        Me.lcRoot.Controls.Add(Me.DateEdit2)
        Me.lcRoot.Controls.Add(Me.DateEdit1)
        Me.lcRoot.Controls.Add(Me.TextEdit3)
        Me.lcRoot.Controls.Add(Me.CheckEdit5)
        Me.lcRoot.Controls.Add(Me.CheckEdit4)
        Me.lcRoot.Controls.Add(Me.CheckEdit3)
        Me.lcRoot.Controls.Add(Me.CheckEdit2)
        Me.lcRoot.Controls.Add(Me.CheckEdit1)
        Me.lcRoot.Controls.Add(Me.SearchLookUpEdit1)
        Me.lcRoot.Controls.Add(Me.meNotes)
        Me.lcRoot.Controls.Add(Me.TextEdit1)
        Me.lcRoot.Controls.Add(Me.cbeStatus)
        Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lcRoot.HiddenItems.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem40})
        Me.lcRoot.Location = New System.Drawing.Point(0, 0)
        Me.lcRoot.Name = "lcRoot"
        Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(295, 51, 1015, 681)
        Me.lcRoot.Root = Me.LayoutControlGroup1
        Me.lcRoot.Size = New System.Drawing.Size(626, 778)
        Me.lcRoot.TabIndex = 0
        Me.lcRoot.Text = "LayoutControl1"
        '
        'MemoEdit1
        '
        Me.MemoEdit1.EditValue = resources.GetString("MemoEdit1.EditValue")
        Me.MemoEdit1.Location = New System.Drawing.Point(315, 548)
        Me.MemoEdit1.Name = "MemoEdit1"
        Me.MemoEdit1.Properties.Appearance.Font = New System.Drawing.Font("Consolas", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEdit1.Properties.Appearance.Options.UseFont = True
        Me.MemoEdit1.Size = New System.Drawing.Size(303, 222)
        Me.MemoEdit1.StyleController = Me.lcRoot
        Me.MemoEdit1.TabIndex = 48
        '
        'PanelControl1
        '
        Me.PanelControl1.Controls.Add(Me.LabelControl1)
        Me.PanelControl1.Location = New System.Drawing.Point(8, 362)
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(610, 408)
        Me.PanelControl1.TabIndex = 47
        '
        'LabelControl1
        '
        Me.LabelControl1.AllowHtmlString = True
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Segoe UI", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseTextOptions = True
        Me.LabelControl1.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl1.Location = New System.Drawing.Point(5, 5)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(600, 112)
        Me.LabelControl1.TabIndex = 0
        Me.LabelControl1.Text = resources.GetString("LabelControl1.Text")
        '
        'hllcEditNotes
        '
        Me.hllcEditNotes.Cursor = System.Windows.Forms.Cursors.Hand
        Me.hllcEditNotes.Location = New System.Drawing.Point(605, 33)
        Me.hllcEditNotes.Name = "hllcEditNotes"
        Me.hllcEditNotes.Size = New System.Drawing.Size(18, 13)
        Me.hllcEditNotes.StyleController = Me.lcRoot
        Me.hllcEditNotes.TabIndex = 31
        Me.hllcEditNotes.Text = "Edit"
        '
        'btnAddFile
        '
        Me.btnAddFile.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.add_16x16
        Me.btnAddFile.Location = New System.Drawing.Point(539, 33)
        Me.btnAddFile.Name = "btnAddFile"
        Me.btnAddFile.Size = New System.Drawing.Size(84, 22)
        Me.btnAddFile.StyleController = Me.lcRoot
        Me.btnAddFile.TabIndex = 33
        Me.btnAddFile.Text = "Add File"
        '
        'gcFiles
        '
        Me.gcFiles.DataSource = Me.bsRecentFiles
        Me.gcFiles.Location = New System.Drawing.Point(3, 59)
        Me.gcFiles.MainView = Me.gvFiles
        Me.gcFiles.Name = "gcFiles"
        Me.gcFiles.Size = New System.Drawing.Size(620, 716)
        Me.gcFiles.TabIndex = 32
        Me.gcFiles.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvFiles})
        '
        'bsRecentFiles
        '
        Me.bsRecentFiles.DataSource = GetType(Brands_FrontDesk.ucRecentReports.RecentFiles)
        '
        'gvFiles
        '
        Me.gvFiles.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colFileName, Me.colCreateDate, Me.colFilePath, Me.colExtension, Me.colReportName, Me.colCoNum1, Me.colCoNumDef})
        Me.gvFiles.GridControl = Me.gcFiles
        Me.gvFiles.Name = "gvFiles"
        Me.gvFiles.OptionsBehavior.Editable = False
        Me.gvFiles.OptionsView.ShowGroupPanel = False
        Me.gvFiles.OptionsView.ShowIndicator = False
        '
        'colFileName
        '
        Me.colFileName.FieldName = "FileName"
        Me.colFileName.Name = "colFileName"
        Me.colFileName.Visible = True
        Me.colFileName.VisibleIndex = 0
        '
        'colCreateDate
        '
        Me.colCreateDate.FieldName = "CreateDate"
        Me.colCreateDate.Name = "colCreateDate"
        Me.colCreateDate.Visible = True
        Me.colCreateDate.VisibleIndex = 1
        '
        'colFilePath
        '
        Me.colFilePath.FieldName = "FilePath"
        Me.colFilePath.Name = "colFilePath"
        '
        'colExtension
        '
        Me.colExtension.FieldName = "Extension"
        Me.colExtension.Name = "colExtension"
        '
        'colReportName
        '
        Me.colReportName.FieldName = "ReportName"
        Me.colReportName.Name = "colReportName"
        Me.colReportName.OptionsColumn.ReadOnly = True
        '
        'colCoNum1
        '
        Me.colCoNum1.FieldName = "CoNum"
        Me.colCoNum1.Name = "colCoNum1"
        Me.colCoNum1.OptionsColumn.ReadOnly = True
        '
        'colCoNumDef
        '
        Me.colCoNumDef.FieldName = "CoNumDef"
        Me.colCoNumDef.Name = "colCoNumDef"
        Me.colCoNumDef.OptionsColumn.ReadOnly = True
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(3, 33)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(35, 13)
        Me.LabelControl3.StyleController = Me.lcRoot
        Me.LabelControl3.TabIndex = 30
        Me.LabelControl3.Text = "Notes: "
        '
        'ButtonEdit4
        '
        Me.ButtonEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoFax", True))
        Me.ButtonEdit4.Location = New System.Drawing.Point(59, 279)
        Me.ButtonEdit4.Name = "ButtonEdit4"
        Me.ButtonEdit4.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.ButtonEdit4.Properties.ReadOnly = True
        Me.ButtonEdit4.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ButtonEdit4.Size = New System.Drawing.Size(559, 20)
        Me.ButtonEdit4.StyleController = Me.lcRoot
        Me.ButtonEdit4.TabIndex = 29
        '
        'bsAccountLead
        '
        Me.bsAccountLead.DataSource = GetType(Brands_FrontDesk.AccountLead)
        '
        'ButtonEdit3
        '
        Me.ButtonEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoEmail", True))
        Me.ButtonEdit3.Location = New System.Drawing.Point(59, 255)
        Me.ButtonEdit3.Name = "ButtonEdit3"
        Me.ButtonEdit3.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.ButtonEdit3.Properties.ReadOnly = True
        Me.ButtonEdit3.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ButtonEdit3.Size = New System.Drawing.Size(559, 20)
        Me.ButtonEdit3.StyleController = Me.lcRoot
        Me.ButtonEdit3.TabIndex = 28
        '
        'ButtonEdit2
        '
        Me.ButtonEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoCellNumber", True))
        Me.ButtonEdit2.Location = New System.Drawing.Point(59, 231)
        Me.ButtonEdit2.Name = "ButtonEdit2"
        Me.ButtonEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.ButtonEdit2.Properties.ReadOnly = True
        Me.ButtonEdit2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ButtonEdit2.Size = New System.Drawing.Size(559, 20)
        Me.ButtonEdit2.StyleController = Me.lcRoot
        Me.ButtonEdit2.TabIndex = 27
        '
        'TextEdit5
        '
        Me.TextEdit5.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoPhoneNubmerExt", True))
        Me.TextEdit5.Location = New System.Drawing.Point(523, 207)
        Me.TextEdit5.Name = "TextEdit5"
        Me.TextEdit5.Properties.ReadOnly = True
        Me.TextEdit5.Size = New System.Drawing.Size(95, 20)
        Me.TextEdit5.StyleController = Me.lcRoot
        Me.TextEdit5.TabIndex = 26
        '
        'ButtonEdit1
        '
        Me.ButtonEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoPhoneNumber", True))
        Me.ButtonEdit1.Location = New System.Drawing.Point(59, 207)
        Me.ButtonEdit1.Name = "ButtonEdit1"
        Me.ButtonEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton()})
        Me.ButtonEdit1.Properties.ReadOnly = True
        Me.ButtonEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ButtonEdit1.Size = New System.Drawing.Size(432, 20)
        Me.ButtonEdit1.StyleController = Me.lcRoot
        Me.ButtonEdit1.TabIndex = 25
        '
        'TextEdit4
        '
        Me.TextEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "ContactName", True))
        Me.TextEdit4.Location = New System.Drawing.Point(59, 183)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Properties.ReadOnly = True
        Me.TextEdit4.Size = New System.Drawing.Size(559, 20)
        Me.TextEdit4.StyleController = Me.lcRoot
        Me.TextEdit4.TabIndex = 24
        '
        'SpinEdit1
        '
        Me.SpinEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "NumberOfEmployees", True))
        Me.SpinEdit1.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.SpinEdit1.Location = New System.Drawing.Point(475, 129)
        Me.SpinEdit1.Name = "SpinEdit1"
        Me.SpinEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.SpinEdit1.Properties.ReadOnly = True
        Me.SpinEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.SpinEdit1.Size = New System.Drawing.Size(148, 20)
        Me.SpinEdit1.StyleController = Me.lcRoot
        Me.SpinEdit1.TabIndex = 23
        '
        'DateEdit3
        '
        Me.DateEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "StartingDate", True))
        Me.DateEdit3.EditValue = Nothing
        Me.DateEdit3.Location = New System.Drawing.Point(85, 129)
        Me.DateEdit3.Name = "DateEdit3"
        Me.DateEdit3.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit3.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit3.Properties.ReadOnly = True
        Me.DateEdit3.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DateEdit3.Size = New System.Drawing.Size(304, 20)
        Me.DateEdit3.StyleController = Me.lcRoot
        Me.DateEdit3.TabIndex = 22
        '
        'CheckEdit6
        '
        Me.CheckEdit6.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "IsNewAccount", True))
        Me.CheckEdit6.Enabled = False
        Me.CheckEdit6.Location = New System.Drawing.Point(496, 81)
        Me.CheckEdit6.Name = "CheckEdit6"
        Me.CheckEdit6.Properties.Caption = "New Account"
        Me.CheckEdit6.Size = New System.Drawing.Size(127, 20)
        Me.CheckEdit6.StyleController = Me.lcRoot
        Me.CheckEdit6.TabIndex = 21
        '
        'DateEdit2
        '
        Me.DateEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "LastFollowedUpDate", True))
        Me.DateEdit2.EditValue = Nothing
        Me.DateEdit2.Location = New System.Drawing.Point(397, 57)
        Me.DateEdit2.Name = "DateEdit2"
        Me.DateEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit2.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit2.Properties.ReadOnly = True
        Me.DateEdit2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DateEdit2.Size = New System.Drawing.Size(226, 20)
        Me.DateEdit2.StyleController = Me.lcRoot
        Me.DateEdit2.TabIndex = 20
        '
        'DateEdit1
        '
        Me.DateEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "NextFollowUpDate", True))
        Me.DateEdit1.EditValue = Nothing
        Me.DateEdit1.Location = New System.Drawing.Point(85, 57)
        Me.DateEdit1.Name = "DateEdit1"
        Me.DateEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateEdit1.Properties.ReadOnly = True
        Me.DateEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.DateEdit1.Size = New System.Drawing.Size(226, 20)
        Me.DateEdit1.StyleController = Me.lcRoot
        Me.DateEdit1.TabIndex = 19
        '
        'TextEdit3
        '
        Me.TextEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "Category", True))
        Me.TextEdit3.Location = New System.Drawing.Point(397, 33)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.ReadOnly = True
        Me.TextEdit3.Size = New System.Drawing.Size(226, 20)
        Me.TextEdit3.StyleController = Me.lcRoot
        Me.TextEdit3.TabIndex = 18
        '
        'CheckEdit5
        '
        Me.CheckEdit5.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "IsPrincipal", True))
        Me.CheckEdit5.Enabled = False
        Me.CheckEdit5.Location = New System.Drawing.Point(417, 303)
        Me.CheckEdit5.Name = "CheckEdit5"
        Me.CheckEdit5.Properties.Caption = "Is Principal"
        Me.CheckEdit5.Size = New System.Drawing.Size(115, 20)
        Me.CheckEdit5.StyleController = Me.lcRoot
        Me.CheckEdit5.TabIndex = 15
        '
        'CheckEdit4
        '
        Me.CheckEdit4.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "IsHr", True))
        Me.CheckEdit4.Enabled = False
        Me.CheckEdit4.Location = New System.Drawing.Point(343, 303)
        Me.CheckEdit4.Name = "CheckEdit4"
        Me.CheckEdit4.Properties.Caption = "Is Hr"
        Me.CheckEdit4.Size = New System.Drawing.Size(70, 20)
        Me.CheckEdit4.StyleController = Me.lcRoot
        Me.CheckEdit4.TabIndex = 14
        '
        'CheckEdit3
        '
        Me.CheckEdit3.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "IsPayroll", True))
        Me.CheckEdit3.Enabled = False
        Me.CheckEdit3.Location = New System.Drawing.Point(8, 303)
        Me.CheckEdit3.Name = "CheckEdit3"
        Me.CheckEdit3.Properties.Caption = "Is Payroll"
        Me.CheckEdit3.Size = New System.Drawing.Size(161, 20)
        Me.CheckEdit3.StyleController = Me.lcRoot
        Me.CheckEdit3.TabIndex = 13
        '
        'CheckEdit2
        '
        Me.CheckEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "IsCpa", True))
        Me.CheckEdit2.Enabled = False
        Me.CheckEdit2.Location = New System.Drawing.Point(536, 303)
        Me.CheckEdit2.Name = "CheckEdit2"
        Me.CheckEdit2.Properties.Caption = "Is Cpa"
        Me.CheckEdit2.Size = New System.Drawing.Size(82, 20)
        Me.CheckEdit2.StyleController = Me.lcRoot
        Me.CheckEdit2.TabIndex = 12
        '
        'CheckEdit1
        '
        Me.CheckEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "IsEndOfPeriod", True))
        Me.CheckEdit1.Enabled = False
        Me.CheckEdit1.Location = New System.Drawing.Point(173, 303)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Caption = "Is End Of Period"
        Me.CheckEdit1.Size = New System.Drawing.Size(166, 20)
        Me.CheckEdit1.StyleController = Me.lcRoot
        Me.CheckEdit1.TabIndex = 11
        '
        'SearchLookUpEdit1
        '
        Me.SearchLookUpEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoNum", True))
        Me.SearchLookUpEdit1.Location = New System.Drawing.Point(85, 105)
        Me.SearchLookUpEdit1.Name = "SearchLookUpEdit1"
        Me.SearchLookUpEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.SearchLookUpEdit1.Properties.DataSource = Me.bsCompanies
        Me.SearchLookUpEdit1.Properties.DisplayMember = "CoNumAndName"
        Me.SearchLookUpEdit1.Properties.PopupView = Me.SearchLookUpEdit1View
        Me.SearchLookUpEdit1.Properties.ReadOnly = True
        Me.SearchLookUpEdit1.Properties.ValueMember = "CONUM"
        Me.SearchLookUpEdit1.Size = New System.Drawing.Size(538, 20)
        Me.SearchLookUpEdit1.StyleController = Me.lcRoot
        Me.SearchLookUpEdit1.TabIndex = 8
        '
        'bsCompanies
        '
        Me.bsCompanies.DataSource = GetType(Brands_FrontDesk.view_CompanySumarry)
        '
        'SearchLookUpEdit1View
        '
        Me.SearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.SearchLookUpEdit1View.Name = "SearchLookUpEdit1View"
        Me.SearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.SearchLookUpEdit1View.OptionsView.ShowGroupPanel = False
        '
        'meNotes
        '
        Me.meNotes.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "Notes", True))
        Me.meNotes.Location = New System.Drawing.Point(3, 50)
        Me.meNotes.Name = "meNotes"
        Me.meNotes.Properties.ReadOnly = True
        Me.meNotes.Size = New System.Drawing.Size(620, 725)
        Me.meNotes.StyleController = Me.lcRoot
        Me.meNotes.TabIndex = 7
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "CoName", True))
        Me.TextEdit1.Location = New System.Drawing.Point(85, 81)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.ReadOnly = True
        Me.TextEdit1.Size = New System.Drawing.Size(407, 20)
        Me.TextEdit1.StyleController = Me.lcRoot
        Me.TextEdit1.TabIndex = 6
        '
        'cbeStatus
        '
        Me.cbeStatus.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.bsAccountLead, "Status", True))
        Me.cbeStatus.Location = New System.Drawing.Point(85, 33)
        Me.cbeStatus.Name = "cbeStatus"
        Me.cbeStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbeStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbeStatus.Size = New System.Drawing.Size(226, 20)
        Me.cbeStatus.StyleController = Me.lcRoot
        Me.cbeStatus.TabIndex = 17
        '
        'LayoutControlItem40
        '
        Me.LayoutControlItem40.Control = Me.MemoEdit1
        Me.LayoutControlItem40.Location = New System.Drawing.Point(307, 170)
        Me.LayoutControlItem40.Name = "LayoutControlItem40"
        Me.LayoutControlItem40.Size = New System.Drawing.Size(307, 242)
        Me.LayoutControlItem40.TextLocation = DevExpress.Utils.Locations.Top
        Me.LayoutControlItem40.TextSize = New System.Drawing.Size(102, 13)
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.TabbedControlGroup1})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(626, 778)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'TabbedControlGroup1
        '
        Me.TabbedControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
        Me.TabbedControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.TabbedControlGroup1.SelectedTabPage = Me.lcgLeadDetails
        Me.TabbedControlGroup1.SelectedTabPageIndex = 0
        Me.TabbedControlGroup1.Size = New System.Drawing.Size(626, 778)
        Me.TabbedControlGroup1.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
        Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lcgLeadDetails, Me.lcgAttachments, Me.lcgNotes})
        '
        'lcgLeadDetails
        '
        Me.lcgLeadDetails.CaptionImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.info_16x16
        Me.lcgLeadDetails.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.LayoutControlItem5, Me.LayoutControlItem14, Me.LayoutControlItem16, Me.LayoutControlItem17, Me.LayoutControlItem18, Me.LayoutControlGroup3, Me.lcgOnlineEnrollment, Me.LayoutControlItem7, Me.LayoutControlItem6, Me.LayoutControlItem15})
        Me.lcgLeadDetails.Location = New System.Drawing.Point(0, 0)
        Me.lcgLeadDetails.Name = "lcgLeadDetails"
        Me.lcgLeadDetails.Size = New System.Drawing.Size(624, 746)
        Me.lcgLeadDetails.Text = "Lead Details"
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.TextEdit1
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(493, 24)
        Me.LayoutControlItem3.Text = "Co Name: "
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.SearchLookUpEdit1
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(624, 24)
        Me.LayoutControlItem5.Text = "Co#: "
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.DateEdit1
        Me.LayoutControlItem14.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(312, 24)
        Me.LayoutControlItem14.Text = "Next Follow Up: "
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.CheckEdit6
        Me.LayoutControlItem16.Location = New System.Drawing.Point(493, 48)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(131, 24)
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem16.TextVisible = False
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.DateEdit3
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(390, 24)
        Me.LayoutControlItem17.Text = "Starting Date: "
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.SpinEdit1
        Me.LayoutControlItem18.Location = New System.Drawing.Point(390, 96)
        Me.LayoutControlItem18.Name = "LayoutControlItem18"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(234, 24)
        Me.LayoutControlItem18.Text = "# Of Emps"
        Me.LayoutControlItem18.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.CaptionImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.customer_16x161
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem19, Me.LayoutControlItem20, Me.LayoutControlItem21, Me.LayoutControlItem22, Me.LayoutControlItem23, Me.LayoutControlItem24, Me.LayoutControlItem10, Me.LayoutControlItem11, Me.LayoutControlItem8, Me.LayoutControlItem9, Me.LayoutControlItem12})
        Me.LayoutControlGroup3.Location = New System.Drawing.Point(0, 120)
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal
        Me.LayoutControlGroup3.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(624, 179)
        Me.LayoutControlGroup3.Text = "Contact Info"
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.TextEdit4
        Me.LayoutControlItem19.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(614, 24)
        Me.LayoutControlItem19.Text = "Name: "
        Me.LayoutControlItem19.TextSize = New System.Drawing.Size(48, 13)
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.ButtonEdit1
        Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(487, 24)
        Me.LayoutControlItem20.Text = "Phone #: "
        Me.LayoutControlItem20.TextSize = New System.Drawing.Size(48, 13)
        '
        'LayoutControlItem21
        '
        Me.LayoutControlItem21.Control = Me.TextEdit5
        Me.LayoutControlItem21.Location = New System.Drawing.Point(487, 24)
        Me.LayoutControlItem21.Name = "LayoutControlItem21"
        Me.LayoutControlItem21.Size = New System.Drawing.Size(127, 24)
        Me.LayoutControlItem21.Text = "Ext: "
        Me.LayoutControlItem21.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.AutoSize
        Me.LayoutControlItem21.TextSize = New System.Drawing.Size(23, 13)
        Me.LayoutControlItem21.TextToControlDistance = 5
        '
        'LayoutControlItem22
        '
        Me.LayoutControlItem22.Control = Me.ButtonEdit2
        Me.LayoutControlItem22.Location = New System.Drawing.Point(0, 48)
        Me.LayoutControlItem22.Name = "LayoutControlItem22"
        Me.LayoutControlItem22.Size = New System.Drawing.Size(614, 24)
        Me.LayoutControlItem22.Text = "Cell #: "
        Me.LayoutControlItem22.TextSize = New System.Drawing.Size(48, 13)
        '
        'LayoutControlItem23
        '
        Me.LayoutControlItem23.Control = Me.ButtonEdit3
        Me.LayoutControlItem23.Location = New System.Drawing.Point(0, 72)
        Me.LayoutControlItem23.Name = "LayoutControlItem23"
        Me.LayoutControlItem23.Size = New System.Drawing.Size(614, 24)
        Me.LayoutControlItem23.Text = "Email: "
        Me.LayoutControlItem23.TextSize = New System.Drawing.Size(48, 13)
        '
        'LayoutControlItem24
        '
        Me.LayoutControlItem24.Control = Me.ButtonEdit4
        Me.LayoutControlItem24.Location = New System.Drawing.Point(0, 96)
        Me.LayoutControlItem24.Name = "LayoutControlItem24"
        Me.LayoutControlItem24.Size = New System.Drawing.Size(614, 24)
        Me.LayoutControlItem24.Text = "Fax #: "
        Me.LayoutControlItem24.TextSize = New System.Drawing.Size(48, 13)
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.CheckEdit3
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 120)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(165, 24)
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem10.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.CheckEdit4
        Me.LayoutControlItem11.Location = New System.Drawing.Point(335, 120)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(74, 24)
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem11.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.CheckEdit1
        Me.LayoutControlItem8.Location = New System.Drawing.Point(165, 120)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(170, 24)
        Me.LayoutControlItem8.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.CheckEdit2
        Me.LayoutControlItem9.Location = New System.Drawing.Point(528, 120)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(86, 24)
        Me.LayoutControlItem9.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.CheckEdit5
        Me.LayoutControlItem12.Location = New System.Drawing.Point(409, 120)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(119, 24)
        Me.LayoutControlItem12.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem12.TextVisible = False
        '
        'lcgOnlineEnrollment
        '
        Me.lcgOnlineEnrollment.CaptionImageOptions.SvgImage = Global.Brands_FrontDesk.My.Resources.Resources.World
        Me.lcgOnlineEnrollment.CaptionImageOptions.SvgImageSize = New System.Drawing.Size(16, 16)
        Me.lcgOnlineEnrollment.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem39})
        Me.lcgOnlineEnrollment.Location = New System.Drawing.Point(0, 299)
        Me.lcgOnlineEnrollment.Name = "lcgOnlineEnrollment"
        Me.lcgOnlineEnrollment.Padding = New DevExpress.XtraLayout.Utils.Padding(2, 2, 2, 2)
        Me.lcgOnlineEnrollment.Size = New System.Drawing.Size(624, 447)
        Me.lcgOnlineEnrollment.Text = "Online Enrollment"
        '
        'LayoutControlItem39
        '
        Me.LayoutControlItem39.Control = Me.PanelControl1
        Me.LayoutControlItem39.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem39.Name = "LayoutControlItem39"
        Me.LayoutControlItem39.Size = New System.Drawing.Size(614, 412)
        Me.LayoutControlItem39.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem39.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.TextEdit3
        Me.LayoutControlItem7.Location = New System.Drawing.Point(312, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(312, 24)
        Me.LayoutControlItem7.Text = "Category: "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.cbeStatus
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(312, 24)
        Me.LayoutControlItem6.Text = "Status: "
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(79, 13)
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.DateEdit2
        Me.LayoutControlItem15.Location = New System.Drawing.Point(312, 24)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(312, 24)
        Me.LayoutControlItem15.Text = "Last Follow Up: "
        Me.LayoutControlItem15.TextSize = New System.Drawing.Size(79, 13)
        '
        'lcgAttachments
        '
        Me.lcgAttachments.CaptionImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.attachment_16x16
        Me.lcgAttachments.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem27, Me.LayoutControlItem28, Me.EmptySpaceItem3})
        Me.lcgAttachments.Location = New System.Drawing.Point(0, 0)
        Me.lcgAttachments.Name = "lcgAttachments"
        Me.lcgAttachments.Size = New System.Drawing.Size(624, 746)
        Me.lcgAttachments.Text = "Attachmnets"
        '
        'LayoutControlItem27
        '
        Me.LayoutControlItem27.Control = Me.gcFiles
        Me.LayoutControlItem27.Location = New System.Drawing.Point(0, 26)
        Me.LayoutControlItem27.Name = "LayoutControlItem27"
        Me.LayoutControlItem27.Size = New System.Drawing.Size(624, 720)
        Me.LayoutControlItem27.TextLocation = DevExpress.Utils.Locations.Top
        Me.LayoutControlItem27.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem27.TextVisible = False
        '
        'LayoutControlItem28
        '
        Me.LayoutControlItem28.Control = Me.btnAddFile
        Me.LayoutControlItem28.Location = New System.Drawing.Point(536, 0)
        Me.LayoutControlItem28.Name = "LayoutControlItem28"
        Me.LayoutControlItem28.Size = New System.Drawing.Size(88, 26)
        Me.LayoutControlItem28.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem28.TextVisible = False
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.AllowHotTrack = False
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(0, 0)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem3"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(536, 26)
        Me.EmptySpaceItem3.TextSize = New System.Drawing.Size(0, 0)
        '
        'lcgNotes
        '
        Me.lcgNotes.CaptionImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.bonote_16x16
        Me.lcgNotes.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.LayoutControlItem26, Me.LayoutControlItem25, Me.EmptySpaceItem2})
        Me.lcgNotes.Location = New System.Drawing.Point(0, 0)
        Me.lcgNotes.Name = "lcgNotes"
        Me.lcgNotes.Size = New System.Drawing.Size(624, 746)
        Me.lcgNotes.Text = "Notes"
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.meNotes
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 17)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(624, 729)
        Me.LayoutControlItem4.Text = "Notes: "
        Me.LayoutControlItem4.TextLocation = DevExpress.Utils.Locations.Top
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem26
        '
        Me.LayoutControlItem26.Control = Me.hllcEditNotes
        Me.LayoutControlItem26.Location = New System.Drawing.Point(602, 0)
        Me.LayoutControlItem26.Name = "LayoutControlItem26"
        Me.LayoutControlItem26.Size = New System.Drawing.Size(22, 17)
        Me.LayoutControlItem26.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem26.TextVisible = False
        '
        'LayoutControlItem25
        '
        Me.LayoutControlItem25.Control = Me.LabelControl3
        Me.LayoutControlItem25.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem25.Name = "LayoutControlItem25"
        Me.LayoutControlItem25.Size = New System.Drawing.Size(39, 17)
        Me.LayoutControlItem25.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem25.TextVisible = False
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.AllowHotTrack = False
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(39, 0)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(563, 17)
        Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
        '
        'ucAccountLeadDetails
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.lcRoot)
        Me.Margin = New System.Windows.Forms.Padding(0)
        Me.Name = "ucAccountLeadDetails"
        Me.Size = New System.Drawing.Size(626, 778)
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.lcRoot.ResumeLayout(False)
        CType(Me.MemoEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        CType(Me.gcFiles, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsRecentFiles, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.gvFiles, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ButtonEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsAccountLead, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ButtonEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ButtonEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ButtonEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SpinEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit3.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit2.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit1.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.bsCompanies, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.meNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbeStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem40, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgLeadDetails, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem23, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem24, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgOnlineEnrollment, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem39, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgAttachments, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem27, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem28, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lcgNotes, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem26, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem25, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents bsAccountLead As BindingSource
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents meNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents CheckEdit5 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEdit4 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEdit3 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEdit2 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents SearchLookUpEdit1 As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents SearchLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents bsCompanies As BindingSource
    Friend WithEvents hllcEditNotes As DevExpress.XtraEditors.HyperlinkLabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonEdit4 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents ButtonEdit3 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents ButtonEdit2 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents TextEdit5 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ButtonEdit1 As DevExpress.XtraEditors.ButtonEdit
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SpinEdit1 As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents DateEdit3 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents CheckEdit6 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents DateEdit2 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents DateEdit1 As DevExpress.XtraEditors.DateEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnAddFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents gcFiles As DevExpress.XtraGrid.GridControl
    Friend WithEvents gvFiles As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents bsRecentFiles As BindingSource
    Friend WithEvents colFileName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCreateDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colFilePath As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colExtension As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colReportName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNum1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colCoNumDef As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
    Friend WithEvents lcgLeadDetails As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem21 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem22 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem23 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem24 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem25 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem26 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents lcgAttachments As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem27 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem28 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents lcgOnlineEnrollment As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents lcgNotes As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents cbeStatus As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents MemoEdit1 As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LayoutControlItem39 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem40 As DevExpress.XtraLayout.LayoutControlItem
End Class
