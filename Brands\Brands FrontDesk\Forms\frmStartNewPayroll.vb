﻿Imports System.ComponentModel

Public Class frmStartNewPayroll

    Dim DB As dbEPDataDataContext
    Dim CalendarData As List(Of CALENDAR)
    Dim BatchNotes As List(Of pr_batch_note)
    Dim DisableNewPayroll As Boolean
    Dim _DateConfirmed As Boolean
    Private _SelectedSets As New List(Of CALENDAR)
    Dim HasChangedCal As Boolean = False

    Public Sub New(_options As OpenPowerGridPayrollOptions)
        InitializeComponent()
        Me.Options = _options
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PrNum As Decimal

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CalendarID As Integer

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property PayrollEnt As PAYROLL

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public ReadOnly Property Options As OpenPowerGridPayrollOptions

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property SelectedSets As List(Of CALENDAR)
        Get
            Return _SelectedSets
        End Get
        Set(value As List(Of CALENDAR))
            _SelectedSets = value
        End Set
    End Property

    Private Sub frmStartNewPayroll_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        SetScreen()
        DB = New dbEPDataDataContext(GetConnectionString)

        Dim API = EP_API()

        EP_API.CompanyID = Options.Conum

        'If Not UsePPxLibrary Then
        '    CType(API, PPxPayroll).CheckPayrollStatus()
        'Else
        API.CheckPayrollStatus(Options.Conum)
        'End If

        If EP_API.PayrollID > 0 AndAlso EP_API.PayrollStatus = "Entering Checks" Then
            'Check for processed power-grid
            Dim PGProcessed As Boolean
            Dim Q = From A In DB.pr_batch_lists
                    Join B In DB.pr_batch_grid_schemas On A.schema_id Equals B.schema_id
                    Where B.name = "Brands" AndAlso A.conum = Options.Conum AndAlso A.prnum = EP_API.PayrollID AndAlso A.status = "Processed"
            If Q.Count > 0 Then
                PGProcessed = True
            End If
            Me.PrNum = EP_API.PayrollID
            Me.PayrollEnt = (From A In DB.PAYROLLs Where A.CONUM = Options.Conum AndAlso A.PRNUM = EP_API.PayrollID).Single
            Me.PAYROLLBindingSource.DataSource = PayrollEnt
            'If Me.PayrollEnt.FED_UCI.GetValueOrDefault < 2 Then
            '    Me.PER1_ST_DATE.EditValue = PayrollEnt.PER1_ST_DATE
            '    Me.PER1_END_DATE.EditValue = PayrollEnt.PER1_END_DATE
            '    Me.PERIOD_CODE.EditValue = PayrollEnt.PERIOD_CODE
            '    Me.PAY_FREQ.EditValue = PayrollEnt.PAY_FREQ
            'ElseIf Me.PayrollEnt.FED_UCI.GetValueOrDefault < 3 Then
            '    Me.PER1_ST_DATE.EditValue = PayrollEnt.PER2_ST_DATE
            '    Me.PER1_END_DATE.EditValue = PayrollEnt.PER2_END_DATE
            '    Me.PERIOD_CODE.EditValue = PayrollEnt.PERIOD_CODE2
            '    Me.PAY_FREQ.EditValue = PayrollEnt.PAY_FREQ2
            'Else
            '    Me.PER1_ST_DATE.EditValue = PayrollEnt.PER3_ST_DATE
            '    Me.PER1_END_DATE.EditValue = PayrollEnt.PER3_END_DATE
            '    Me.PERIOD_CODE.EditValue = PayrollEnt.PERIOD_CODE3
            '    Me.PAY_FREQ.EditValue = PayrollEnt.PAY_FREQ3
            'End If
            'Me.CHECK_DATE.EditValue = PayrollEnt.CHECK_DATE
            'Me.chkSupp_PR.EditValue = PayrollEnt.SUPP_PR
            'Me.TextEditPrDesc.EditValue = PayrollEnt.PR_DESCR
            If PayrollEnt.PR_DESCR & "" = "Bonus Run" Then
                Me.rbtnBunusRun.Checked = True
            ElseIf PayrollEnt.PR_DESCR & "" = "Bonus Checks" Then
                Me.rbtnBunusChecks.Checked = True
            End If

            Dim ChkCnts = (From A In DB.CHK_MASTs
                           Where A.CONUM = Options.Conum AndAlso A.PAYROLL_NUM = PrNum
                           Group By CHK_TYPE = A.CHK_TYPE Into Count()
                           Select New With {CHK_TYPE, .count = Count}).ToList()

            Dim RgChkCnts = (From A In DB.CHK_MASTs Where A.CONUM = Options.Conum AndAlso A.PAYROLL_NUM = PrNum AndAlso (A.CHK_TYPE = "Normal" OrElse A.CHK_TYPE = "DD")).Count
            Dim MemoCnt As String = ""
            If ChkCnts.Count > 0 Then
                For Each Cnt In ChkCnts
                    MemoCnt &= String.Format("{0} {1}: {2}", CStr(Cnt.count), Cnt.CHK_TYPE, vbCrLf)
                Next
            End If

            'From person In Data
            'Group person By grpId = person.GroupId Into Group
            'Select id = grpId, count = Group.Count()()
            Me.MemoEdit1.Visible = True
            Me.MemoEdit1.Lines = {IIf(Not PGProcessed, "Payroll already open", "PowerGrid Processed - Use EP to edit and Submit"),
                                  "Payroll Number: " & EP_API.PayrollID,
                                  "Status: " & EP_API.PayrollStatus,
                                  "Opened By: " & PayrollEnt.OPNAME,
                                  "Payroll Description: " & PayrollEnt.PR_DESCR,
                                  MemoCnt}
            If PGProcessed Then
                DisplayMessageBox("PowerGrid Processed - Use EP to edit and Submit")
                Me.btnStartPayroll.Enabled = False
                DisableNewPayroll = True
            Else
                If ChkCnts.Count > 0 Then
                    Dim Msg = String.Format("Payroll already contains {0} check{1} that will not show in grid.{2}{3}{4}{2}{2}Continue anyway?", ChkCnts.Count, If(ChkCnts.Count = 1, "", "s"), vbCrLf, MemoCnt, If(RgChkCnts > 0, "Recommended to edit payroll in EP", ""))
                    Dim Results = DevExpress.XtraEditors.XtraMessageBox.Show(Msg, "Duplicate Checks Alert", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2)
                    If Results = System.Windows.Forms.DialogResult.No Then
                        Me.btnStartPayroll.Enabled = False
                        DisableNewPayroll = True
                    End If
                End If
            End If
        Else
            Me.MemoEdit1.Visible = False
            Me.chkSupp_PR.EditValue = "NO"
        End If

        If btnStartPayroll.Enabled Then
            Dim Password = (From A In DB.COMPANies Where A.CONUM = Options.Conum Select A.PR_PASSWORD).SingleOrDefault
            Me.pnlPassword.Visible = Not String.IsNullOrEmpty(Password)
            Me.btnStartPayroll.Enabled = Not pnlPassword.Visible
            Me.txtPassword.EditValue = Password
        Else
            Me.pnlPassword.Visible = False
        End If
        CheckMinWageHasIssues()

        LoadData()

        If PayrollEnt Is Nothing AndAlso SelectedSets.Count > 1 Then
            Dim frm As New frmStartNewPayrollMultiSet(SelectedSets, frmStartNewPayrollMultiSet.FormType.Payroll)
            Dim results = frm.ShowDialog
            If results = System.Windows.Forms.DialogResult.Cancel Then
                Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
                frm.Dispose()
                Exit Sub
            Else
                SelectedSets = (From A In frm.CalendarData Where A.IsSelectedForPowergrid = True Order By A.period_id).ToList
            End If
        End If

        If SelectedSets.Count > 0 Then
            Me.gridCalendar.ForceInitialize()
            For Each Cal In SelectedSets
                Dim i = CalendarData.IndexOf(Cal)
                Me.GridViewCalendar.FocusedRowHandle = CalendarData.FindIndex(Function(c) c.cal_id = Cal.cal_id)
                Me.GridViewCalendar.MakeRowVisible(Me.GridViewCalendar.FocusedRowHandle)
                Dim c2 As CALENDAR = GridViewCalendar.GetRow(26)
                Cal.IsSelectedForPowergrid = True
                'Me.GridViewCalendar.SetRowCellValue(Me.GridViewCalendar.FocusedRowHandle, colSelectCalendar, True)
            Next
        End If
        'If PayrollEnt IsNot Nothing Then
        'Me.colSelectCalendar.OptionsColumn.AllowEdit = False
        '  Else
        For Each Cal In SelectedSets
            Me.GridViewCalendar.FocusedRowHandle = CalendarData.IndexOf(Cal)
            Me.riSelectCal_EditValueChanged(Me, New EventArgs)
        Next

        '  End If
        Dim IsSetUsed As Boolean
        For Each chk In {Me.chkUseSet1, Me.chkUseSet2, Me.chkUseSet3}
            Dim Num As Integer = Integer.Parse(chk.Name.Last)
            chk.Visible = HasSet(Num, IsSetUsed)
            chk.Checked = (From A In SelectedSets Where A.period_id = Num).Any
            'chk.Enabled = Not IsSetUsed
        Next

        For i = 0 To GridViewCalendar.RowCount
            Dim cal As CALENDAR = GridViewCalendar.GetRow(i)
            If cal IsNot Nothing AndAlso cal.IsSelectedForPowergrid Then
                GridViewCalendar.FocusedRowHandle = i
                GridViewCalendar.MakeRowVisible(i)
                Exit For
            End If
        Next


        For Each cal In SelectedSets
            Dim selectedPayrollIndex = CalendarData.FindIndex(Function(c) c.IsSelectedForPowergrid AndAlso c.period_id = cal.period_id)
            If selectedPayrollIndex > 0 Then
                For i = selectedPayrollIndex To 0 Step -1
                    If i = 0 Then Exit For
                    Dim lastPayroll = CalendarData(i - 1)
                    If lastPayroll.period_id = CalendarData(selectedPayrollIndex).period_id Then
                        If lastPayroll.completed = "NO" Then
                            DisplayMessageBox("Previous Calendar for Period {0} was not completed, double check the dates you like to do payroll for.".FormatWith(cal.period_id))
                        End If
                        Exit For
                    End If

                Next
            End If
        Next

    End Sub

    Private Sub SetScreen()
        Try
            If Options.CenterToScreen IsNot Nothing Then
                Me.CenterToScreeen(Options.CenterToScreen)
                TopMost = True
                TopMost = False
            End If
        Catch ex As Exception
            Logger.Error(ex, "Error in SetScreen")
            DisplayErrorMessage("Error setting screen", ex)
        End Try
    End Sub

    Private Function HasSet(ByVal SetNum As Integer, ByRef IsUsed As Boolean) As Boolean
        IsUsed = (Me.PayrollEnt IsNot Nothing AndAlso Me.PayrollEnt.FED_UCI.Value.ToString.Contains(SetNum.ToString))
        Dim Has = (From A In Me.CalendarData Where A.period_id = SetNum).Count > 0
        Return Has
    End Function


    Dim MinWageList As List(Of prc_MinimumWageRequirementsResult)
    Private Function CheckMinWageHasIssues(Optional refresh As Boolean = False) As Boolean
        If Not CHECK_DATE.HasValue Then Return True
        If MinWageList Is Nothing OrElse refresh Then
            MinWageList = DB.prc_MinimumWageRequirements(Options.Conum, "NONE", "NO").ToList
        End If

        Dim endDate As Date
        If chkSupp_PR.Checked OrElse chk_Supp_PR2.Checked Then
            endDate = CHECK_DATE.EditValue
        Else
            endDate = If(PER1_END_DATE.HasValue, PER1_END_DATE.EditValue, If(PER2_END_DATE.HasValue, PER2_END_DATE.EditValue, If(PER3_END_DATE.HasValue, PER3_END_DATE.EditValue, CHECK_DATE.EditValue)))
        End If
        Dim hasIssues = MinWageList.Any(Function(m) Not m.effective_date.HasValue OrElse m.effective_date <= endDate)

        If hasIssues Then
            Me.GroupBoxSetMinimumWage.Visible = True
            Me.btnMinimumWage.ForeColor = Color.Red
            Return True
        Else
            Me.GroupBoxSetMinimumWage.Visible = False
            Me.btnMinimumWage.ForeColor = Color.FromName("ControlText")
            Return False
        End If

    End Function


    Private Sub riSelectCal_EditValueChanging(ByVal sender As System.Object, ByVal e As DevExpress.XtraEditors.Controls.ChangingEventArgs)
        Dim Cal As CALENDAR = GridViewCalendar.GetRow(GridViewCalendar.FocusedRowHandle)
        Dim CD As Date = Me.CHECK_DATE.EditValue
        If CD < Today Then
            'If Cal.check_date < Today Then
            Dim results = MessageBox.Show("Check Date is a past date. Continue?", "Caution", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2)
            If results = System.Windows.Forms.DialogResult.No Then
                e.Cancel = True
                Exit Sub
            End If
            'ElseIf Cal.check_date > Today.AddDays(6) Then
        ElseIf CD > Today.AddDays(6) Then
            Dim results = MessageBox.Show("Check Date is a next week. Continue?", "Caution", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2)
            If results = System.Windows.Forms.DialogResult.No Then
                e.Cancel = True
                Exit Sub
            End If
        End If
        If Cal.completed = "Yes" OrElse Cal.payroll_num.HasValue Then
            Dim PrDesc = (From A In DB.PAYROLLs Where A.CONUM = Options.Conum AndAlso A.PRNUM = Cal.payroll_num.GetValueOrDefault Select A.PR_DESCR).SingleOrDefault
            Dim results = MessageBox.Show("Calendar already used for" &
                                          vbCrLf & vbTab & "Payroll #: " & Cal.payroll_num &
                                          If(Not String.IsNullOrEmpty(PrDesc), vbCrLf & vbTab & "Payroll Description: " & PrDesc, "") &
                                          vbCrLf & "Continue?", "Caution", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2)
            If results = System.Windows.Forms.DialogResult.No Then
                e.Cancel = True
                Exit Sub
            End If
        End If
        _DateConfirmed = True
    End Sub

    Private Sub riSelectCal_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles riSelectCal.EditValueChanged
        Me.GridViewCalendar.PostEditor()
        Me.GridViewCalendar.UpdateCurrentRow()
        'Uncheck Other
        Dim Cal As CALENDAR = Me.GridViewCalendar.GetRow(Me.GridViewCalendar.FocusedRowHandle)
        For RowIx = 0 To GridViewCalendar.RowCount
            If RowIx = GridViewCalendar.FocusedRowHandle Then Continue For
            If GridViewCalendar.GetRowCellValue(RowIx, "period_id") = Cal.period_id Then
                If GridViewCalendar.GetRowCellValue(RowIx, colSelectCalendar) = True Then
                    GridViewCalendar.SetRowCellValue(RowIx, colSelectCalendar, False)
                End If
            End If
        Next

        'check each period, if no check is selected, uncheck top check box 
        Dim calList As List(Of CALENDAR) = (From A As CALENDAR In CType(Me.gridCalendar.DataSource, IList) Where A.IsSelectedForPowergrid = True).ToList
        chkUseSet1.Checked = calList.Exists(Function(c As CALENDAR) c.period_id = 1)
        chkUseSet2.Checked = calList.Exists(Function(c As CALENDAR) c.period_id = 2)
        chkUseSet3.Checked = calList.Exists(Function(c As CALENDAR) c.period_id = 3)

        SetPGDates(True)
    End Sub

    Sub SetPGDates(ByVal SetSelected As Boolean)
        Dim Cal As List(Of CALENDAR) = (From A As CALENDAR In CType(Me.gridCalendar.DataSource, IList) Where A.IsSelectedForPowergrid = True).ToList 'Me.GridViewCalendar.GetRow(Me.GridViewCalendar.FocusedRowHandle)

        For Each c As CALENDAR In Cal
            If c IsNot Nothing Then
                Select Case c.period_id
                    Case 1
                        Me.PER1_ST_DATE.EditValue = c.start_date.Value.ToShortDateString
                        Me.PER1_END_DATE.EditValue = c.end_date.Value.ToShortDateString
                        Me.PERIOD_CODE.EditValue = c.prd
                        Me.PAY_FREQ.EditValue = c.frequency
                    Case 2
                        Me.PER2_ST_DATE.EditValue = c.start_date.Value.ToShortDateString
                        Me.PER2_END_DATE.EditValue = c.end_date.Value.ToShortDateString
                        Me.PERIOD_CODE2.EditValue = c.prd
                        Me.PAY_FREQ2.EditValue = c.frequency
                    Case 3
                        Me.PER3_ST_DATE.EditValue = c.start_date.Value.ToShortDateString
                        Me.PER3_END_DATE.EditValue = c.end_date.Value.ToShortDateString
                        Me.PERIOD_CODE3.EditValue = c.prd
                        Me.PAY_FREQ3.EditValue = c.frequency
                End Select
            End If
        Next

        If Not SetSelected OrElse Cal Is Nothing OrElse Cal.Count = 0 Then Exit Sub
        If Cal.Count = 1 Then
            Me.CHECK_DATE.EditValue = Cal.First.check_date
        Else
            Me.CHECK_DATE.EditValue = (From A As CALENDAR In CType(Me.gridCalendar.DataSource, IList) Where A.IsSelectedForPowergrid = True Select A.check_date).ToList().Max
        End If

        'Me.CHECK_DATE.EditValue = If(SetSelected, Me.GridViewCalendar.GetRow(Me.GridViewCalendar.FocusedRowHandle), MaxCheckDate)
    End Sub

    Sub LoadData()
        'Load Calendar
        CalendarData = (From A In DB.CALENDARs Order By A.check_date, A.period_id, A.cal_id
                        Where A.conum = Options.Conum _
                        AndAlso A.check_date >= Today.AddMonths(-3) _
                        AndAlso A.check_date <= Today.AddMonths(3)).ToList
        Me.gridCalendar.DataSource = CalendarData
        Me.GridViewCalendar.RefreshData()


        'CalendarData = Me.gridCalendar.DataSource
        SelectedSets = New List(Of CALENDAR)
        If CalendarData.Count > 0 Then
            If PayrollEnt Is Nothing Then
                Dim pSets = (From A In CalendarData Group By A.period_id Into Group Select period_id).ToList
                For Each pSet In pSets
                    Dim Cal = (From A In CalendarData Where A.check_date >= Today AndAlso A.period_id = pSet AndAlso A.completed <> "YES").FirstOrDefault
                    If Cal IsNot Nothing Then
                        SelectedSets.Add(Cal)
                    End If
                Next
            Else
                Dim Rec = (From A In DB.pr_batch_in_processes Where A.CoNum = Options.Conum AndAlso A.PrNum = EP_API.PayrollID AndAlso (A.IsDeleted = False OrElse A.IsDeleted Is Nothing)).FirstOrDefault
                If Rec IsNot Nothing Then
                    Dim UsedCals = (From A In {Rec.CalendarID, Rec.CalendarID2, Rec.CalendarID3} Where A.HasValue Select A.GetValueOrDefault).ToList
                    For Each CalID In UsedCals
                        Dim Cal = (From A In CalendarData Where A.cal_id = CalID).FirstOrDefault
                        If Cal Is Nothing Then
                            DisplayMessageBox("Calendar used when opening this payroll has been deleted")
                        Else
                            SelectedSets.Add(Cal)
                        End If
                    Next
                Else
                    SelectedSets = (From A In CalendarData Where A.payroll_num = PayrollEnt.PRNUM).ToList
                End If
            End If
        End If
    End Sub

    Private Sub btnStartPayroll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStartPayroll.Click
        Try
            Dim Cals = (From A In CalendarData Where A.IsSelectedForPowergrid = True Order By A.period_id).ToList
            'Dim Cal As CALENDAR = Me.GridViewCalendar.GetRow(Me.GridViewCalendar.FocusedRowHandle)
            If Cals.Count = 0 Then
                DisplayMessageBox("Please select a Calendar")
                Exit Sub
            End If

            If Not AllowPayrollOnlineClients(Cals) Then
                Exit Sub
            End If

            Dim SelectedCalID = (From A In Cals Select A.cal_id).ToList
            Dim FirstCal = Cals.First
            Dim calenderRule = (From A In DB.CALENDAR_RULES Where A.conum = FirstCal.conum AndAlso A.period_id = FirstCal.period_id).Single
            CalendarID = FirstCal.cal_id
            If calenderRule.ProcessTime = "" Then

                Me.GroupBoxSetProcessTime.Visible = True
                If Me.CheckEditSkip.Checked Then
                    'do nothing
                ElseIf Me.txtSetProcessTime.Time = DateTime.MinValue Then ' "#12:00:00 AM#" 

                    Me.txtSetProcessTime.Focus()
                    Dim msg = "Please ask client to provide a default process time First!"
                    DevExpress.XtraEditors.XtraMessageBox.Show(msg, "Process Time", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                    Exit Sub
                Else

                    calenderRule.ProcessTime = txtSetProcessTime.Time.ToShortTimeString()
                    If Not DB.SaveChanges() Then
                        DisplayErrorMessage("Unable to save process time due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                        Exit Sub
                    End If
                End If
            End If


            If Me.CheckEditSkipMinWage.Checked Then
                'do nothing
            ElseIf CheckMinWageHasIssues() Then
                Dim msg = "Update employee Minimum Wage First!"
                DevExpress.XtraEditors.XtraMessageBox.Show(msg, "Minimum Wage", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Exit Sub
            End If

            Dim CheckDateNextWeekAnsweredResults As DialogResult
            Dim CD As Date = Me.CHECK_DATE.EditValue
            If Not _DateConfirmed Then
                If CD < Today Then
                    Dim results = MessageBox.Show("Check Date is a past date. Continue?", "Caution", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2)
                    If results = System.Windows.Forms.DialogResult.No Then
                        Exit Sub
                    End If
                ElseIf CD > Today.AddDays(6) Then
                    CheckDateNextWeekAnsweredResults = MessageBox.Show("Check Date is a next week. Continue?", "Caution", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2)
                    If CheckDateNextWeekAnsweredResults = System.Windows.Forms.DialogResult.No Then
                        Exit Sub
                    End If
                End If
            End If

            Dim CalDDRec = (From A In DB.view_Calendars Where SelectedCalID.Contains(A.CalID) AndAlso A.DDCount > 0).ToList
            If CalDDRec.Count > 0 Then
                Dim Msg As String = Nothing
                If CD.DayOfWeek = DayOfWeek.Saturday OrElse CD.DayOfWeek = DayOfWeek.Sunday Then
                    Msg = "Weekend check dates are not allowed for DD clients. Allow the date?"
                ElseIf (From A In DB.Bank_Holidays Where A.holiday_date = CD).Count > 0 Then
                    Msg = "Holiday check dates are not allowed for DD clients. Allow the date?"
                End If
                If Not String.IsNullOrEmpty(Msg) Then
                    If MessageBox.Show(Msg, "Confirm Check Date", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2) = DialogResult.No Then
                        CHECK_DATE.Select()
                        Exit Sub
                    End If
                End If
            End If

            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Cals)

            Dim CompanyData = (From A In DB.COMPANies Where A.CONUM = Options.Conum Select A.CONUM, A.START_CHK_NUM, A.FED_DEP_STAT).Single
            If PayrollEnt Is Nothing Then
                Dim CalWasCompleted = New List(Of pr_batch_note)
                For X = 0 To Cals.Count - 1
                    Dim cal = Cals(X)
                    If cal.payroll_num.HasValue Then
                        Dim Note = New pr_batch_note With {.Conum = Options.Conum, .PrNum = 0, .EmployeeID = -998, .rowguid = Guid.NewGuid}
                        Note.Note = $"Calendar was already completed - <cal_id:{cal.cal_id}>, <payroll_num:{cal.payroll_num}>, <completed_date:{cal.completed_date}>"
                        CalWasCompleted.Add(Note)
                        DB.pr_batch_notes.InsertOnSubmit(Note)

                        cal.payroll_num = Nothing
                        cal.completed = "NO"
                        cal.completed_date = Nothing
                        DB.SubmitChanges()
                    End If
                Next

                EP_API.CalendarID = FirstCal.cal_id


                Dim payrollFromCalStatus As Boolean
                'If Not UsePPxLibrary Then
                '    payrollFromCalStatus = CType(EP_API(), PPxPayroll).CreateNewPayrollFromCalendar()
                'Else
                payrollFromCalStatus = EP_API.CreateNewPayrollFromCalendar(Options.Conum)
                'End If

                If Not payrollFromCalStatus Then
                    Dim PayrollException = EP_API.PayrollException
                    If PayrollException.Message = "The conversion of a varchar data type to a smalldatetime data type resulted in an out-of-range value." Then
                        'create payroll record manually
                        DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, FirstCal)
                        Dim PayrollsAll = (From A In DB.PAYROLLs Where A.CONUM = Options.Conum).ToList
                        Dim LastPrNum As Decimal = 0
                        If PayrollsAll.Count > 0 Then
                            LastPrNum = (From A In PayrollsAll Select A.PRNUM).Max
                            Dim LastPrStat = (From A In PayrollsAll Where A.PRNUM = LastPrNum Select A.PAYROLL_STATUS).SingleOrDefault
                            If LastPrStat = "Entering Checks" Then
                                Exit Sub
                            End If
                        End If
                        'Dim LastPayrollNum = (From A In DB.PAYROLLs Where A.CONUM = Options.Conum Select New Decimal?(A.PRNUM)).Max.GetValueOrDefault
                        'Dim LastPayrollStatus = (From A In DB.PAYROLLs Where A.CONUM = Options.Conum And A.PRNUM = LastPayrollNum Select A.PAYROLL_STATUS).SingleOrDefault
                        'If LastPayrollStatus = "Entering Checks" Then
                        '    Exit Sub
                        'End If
                        'If EP_API.PayrollID > 0 And EP_API.PayrollStatus = "Entering Checks" Then
                        '    Exit Sub
                        'End If
                        Dim NewPayrollNum As Decimal = 1
                        'If FirstCal.payroll_num.HasValue Then
                        '    NewPayrollNum = FirstCal.payroll_num
                        'Else
                        '    NewPayrollNum = LastPayrollNum + 1
                        'End If
                        If LastPrNum > 0 Then
                            NewPayrollNum = LastPrNum + 1
                        End If

                        'If Not EP_API.CreateNewPayroll() Then
                        '    DisplayErrorMessage("Cannot start payroll. Use Execupay to start payroll")
                        '    Exit Sub
                        'End If
                        'Dim CompanyData = (From A In DB.COMPANies Where A.CONUM = Options.Conum Select A.CONUM, A.START_CHK_NUM, A.FED_DEP_STAT).Single
                        Dim NewPayrollRec As New PAYROLL With {.CONUM = Options.Conum,
                                                               .PRNUM = NewPayrollNum,
                                                               .CHECK_DATE = Me.CHECK_DATE.EditValue,
                                                               .START_TIME = Now,
                                                               .RUN_DATE = Today,
                                                               .FED_UCI = FirstCal.period_id,
                                                               .NUM_CHECKS = 0,
                                                               .NUM_MANUAL = 0,
                                                               .NUM_DD = 0,
                                                               .NUM_TAX = 0,
                                                               .NUM_VOID = 0,
                                                               .BEG_CKNUM = CompanyData.START_CHK_NUM,
                                                               .OPNAME = UserName,
                                                               .CHG_CD = 1,
                                                               .BILL_ACH_STAT = "N/A",
                                                               .TAX941 = 0, .TOTHRS = 0, .GROSS = 0, .FEDWH = 0, .OASDI = 0, .MED = 0, .STWH = 0, .LOCWH = 0, .EIC = 0, .NET = 0,
                                                               .PAYROLL_STATUS = "Entering Checks",
                                                               .DD_FG = 1,
                                                               .ENTRY_TYPE = "BrandsXpress",
                                                               .SUPP_PR = "NO",
                                                               .TOTALTAX_STATUS = "N/A",
                                                               .AUTO_HOURS = "NO",
                                                               .AUTO_PAYS = "YES", .AUTO_DEDS = "YES", .AUTO_MEMO = "YES",
                                                               .acc_sick = "YES", .acc_per = "YES", .acc_vac = "YES",
                                                               .mpwWebStatus = 60,
                                                               .PERIOD_CODE3 = Nothing, .PERIOD_CODE2 = Nothing, .PERIOD_CODE = Nothing,
                                                               .STDEP = Now, .LOCDEP = Now,
                                                               .rowguid = Guid.Parse(Guid.NewGuid.ToString.ToUpper)
                                                               }
                        With NewPayrollRec
                            Select Case FirstCal.period_id
                                Case 1
                                    .PER1_ST_DATE = FirstCal.start_date
                                    .PER1_END_DATE = FirstCal.end_date
                                    .PAY_FREQ = FirstCal.frequency
                                    .PERIOD_CODE = FirstCal.prd
                                Case 2
                                    .PER2_ST_DATE = FirstCal.start_date
                                    .PER2_END_DATE = FirstCal.end_date
                                    .PAY_FREQ2 = FirstCal.frequency
                                    .PERIOD_CODE2 = FirstCal.prd
                                Case 3
                                    .PER3_ST_DATE = FirstCal.start_date
                                    .PER3_END_DATE = FirstCal.end_date
                                    .PAY_FREQ3 = FirstCal.frequency
                                    .PERIOD_CODE3 = FirstCal.prd
                            End Select
                        End With

                        DB.PAYROLLs.InsertOnSubmit(NewPayrollRec)

                        'Also update divison check numbers
                        Dim Divisions = (From A In DB.DIVISIONs Where A.CONUM = Options.Conum AndAlso A.DBANKSAME = "NO" AndAlso A.DSTART_CHK_NUM > A.PR_CKNUM).ToList
                        For Each divison In Divisions
                            divison.PR_CKNUM = divison.DSTART_CHK_NUM
                        Next

                        FirstCal.payroll_num = NewPayrollNum
                        If Not DB.SaveChanges() Then
                            DisplayErrorMessage("Unable to save new payroll due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                            Exit Sub
                        End If

                        'If Not UsePPxLibrary Then
                        '    CType(EP_API(), PPxPayroll).CheckPayrollStatus()
                        'Else
                        EP_API.CheckPayrollStatus(Options.Conum)
                        'End If

                        DevExpress.XtraEditors.XtraMessageBox.Show("Silverpay was able to create the new payroll. You may ignore the previous error message.", "Note", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Else
                        DisplayMessageBox("Cannot start payroll. Use Execupay to start payroll only," + vbCrLf + "then come back to SilverPay to process checks")
                        Exit Sub
                    End If 'EP_API.PayrollException.Message = xxx
                End If 'Not EP_API.CreateNewPayrollFromCalendar

                If EP_API.PayrollID <= 0 Then
                    DisplayMessageBox("Unable to create payroll, payroll ID returned 0")
                    Exit Sub
                Else
                    If CalWasCompleted.Count > 0 Then
                        For Each note In CalWasCompleted
                            note.PrNum = EP_API.PayrollID
                        Next
                        If Not DB.SaveChanges() Then
                            DisplayErrorMessage("Unable to save calendar notes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                            Exit Sub
                        End If
                    End If
                End If
                Me.PayrollEnt = (From A In DB.PAYROLLs Where A.CONUM = Options.Conum AndAlso A.PRNUM = EP_API.PayrollID).Single
                Me.PrNum = EP_API.PayrollID
            Else
                Me.PrNum = PayrollEnt.PRNUM
                Dim PayRec = (From A In DB.pr_batch_in_processes Where A.CoNum = Options.Conum AndAlso A.PrNum = EP_API.PayrollID AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault

                ' Get all calendars currently assigned to this payroll and clear them
                Dim calList = (From c In DB.CALENDARs Where c.conum = Options.Conum AndAlso c.payroll_num = EP_API.PayrollID).ToList

                Dim cal1 As CALENDAR = Cals.SingleOrDefault(Function(c As CALENDAR) c.period_id = 1)
                Dim cal2 As CALENDAR = Cals.SingleOrDefault(Function(c As CALENDAR) c.period_id = 2)
                Dim cal3 As CALENDAR = Cals.SingleOrDefault(Function(c As CALENDAR) c.period_id = 3)

                If PayRec IsNot Nothing Then
                    If cal1 IsNot Nothing Then
                        If IsNothing(PayRec.CalendarID) OrElse PayRec.CalendarID <> cal1.cal_id Then
                            PayRec.CalendarID = cal1.cal_id
                            HasChangedCal = True
                        End If
                    ElseIf Not IsNothing(PayRec.CalendarID) Then
                        PayRec.CalendarID = Nothing
                        HasChangedCal = True
                    End If

                    If cal2 IsNot Nothing Then
                        If IsNothing(PayRec.CalendarID2) OrElse PayRec.CalendarID2 <> cal2.cal_id Then
                            PayRec.CalendarID2 = cal2.cal_id
                            HasChangedCal = True
                        End If
                    ElseIf Not IsNothing(PayRec.CalendarID2) Then
                        PayRec.CalendarID2 = Nothing
                        HasChangedCal = True
                    End If

                    If cal3 IsNot Nothing Then
                        If IsNothing(PayRec.CalendarID3) OrElse PayRec.CalendarID3 <> cal3.cal_id Then
                            PayRec.CalendarID3 = cal3.cal_id
                            HasChangedCal = True
                        End If
                    ElseIf Not IsNothing(PayRec.CalendarID3) Then
                        PayRec.CalendarID3 = Nothing
                        HasChangedCal = True
                    End If

                    PayRec.FaxId = Options._FaxId
                    PayRec.EmailNum = Options._EmailID
                    PayRec.ZendeskTicketId = Options._ZendeskTicketId
                End If

                ' Clear payroll_num from any calendars currently assigned to this payroll
                ' Only save if there are actually calendars to clear
                If calList.Count > 0 Then
                    For Each c In calList
                        c.payroll_num = Nothing
                    Next

                    If Not DB.SaveChanges() Then
                        DisplayErrorMessage("Unable to save calendar changes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                        Exit Sub
                    End If
                End If
            End If 'If PayrollEnt Is Nothing Then

            If Cals.Count > 1 OrElse FirstCal.period_id <> 1 OrElse HasChangedCal Then
                Dim FedUCI As String = ""
                If Me.chkUseSet1.Checked Then FedUCI &= "1"
                If Me.chkUseSet2.Checked Then FedUCI &= "2"
                If Me.chkUseSet3.Checked Then FedUCI &= "3"
                PayrollEnt.FED_UCI = FedUCI
            End If

            ' Refresh calendar entities BEFORE making changes to ensure we have latest data
            DB.Refresh(Data.Linq.RefreshMode.OverwriteCurrentValues, Cals)

            For X = 0 To Cals.Count - 1
                Dim Cal = Cals(X)
                If Cal.payroll_num.GetValueOrDefault <> EP_API.PayrollID Then
                    Cal.payroll_num = EP_API.PayrollID
                End If
            Next
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save calendar assignments due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Exit Sub
            End If

            Dim ppxPayrollCalendar As New PPxPayrollCalendar With {.CalendarID = FirstCal.cal_id, .CheckDate = FirstCal.check_date,
                                                                    .EndDate = FirstCal.end_date, .Frequency = FirstCal.frequency,
                                                                    .ProcessDate = FirstCal.process_date, .StartDate = FirstCal.start_date,
                                                                    .Title = FirstCal.title}
            EP_API.PayrollCalendar = ppxPayrollCalendar

            'Save Dates
            'If Cals.Count > 1 Or FirstCal.period_id <> 1 Or HasChangedCal Then
            'Dim CompanyData = (From A In DB.COMPANies Where A.CONUM = Options.Conum Select A.CONUM, A.START_CHK_NUM, A.FED_DEP_STAT).Single
            'change next cal to do the following, see last cal for each period where has prnum and select all next cals where month = to current month of chk date and if exist then return or min check date or cnt and change below syntax to count
            If Me.PayrollEnt.FED_UCI.Value.ToString.Contains("1") Then
                'PayrollEnt.PER1_ST_DATE = CDate(Me.PER1_ST_DATE.EditValue)
                'PayrollEnt.PER1_END_DATE = CDate(Me.PER1_END_DATE.EditValue)
                PayrollEnt.PERIOD_CODE = Me.PERIOD_CODE.EditValue
                PayrollEnt.PAY_FREQ = Me.PAY_FREQ.EditValue
                Dim NextCal = (From A In DB.CALENDARs
                               Where A.conum = Options.Conum AndAlso A.period_id = 1 AndAlso A.check_date > FirstCal.check_date
                               Order By A.check_date).FirstOrDefault
                Dim Eom = If(NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Month <> FirstCal.check_date.GetValueOrDefault.Month OrElse CompanyData.FED_DEP_STAT = "SEMI-WEEKLY", 1, 0)
                PayrollEnt.EOM_FG1 = Eom
            Else
                PayrollEnt.PAY_FREQ = ""
            End If
            If Me.PayrollEnt.FED_UCI.Value.ToString.Contains("2") Then
                'PayrollEnt.PER2_ST_DATE = CDate(Me.PER2_ST_DATE.EditValue)
                'PayrollEnt.PER2_END_DATE = CDate(Me.PER2_END_DATE.EditValue)
                PayrollEnt.PERIOD_CODE2 = Me.PERIOD_CODE2.EditValue
                PayrollEnt.PAY_FREQ2 = Me.PAY_FREQ2.EditValue
                Dim NextCal = (From A In DB.CALENDARs
                               Where A.conum = Options.Conum AndAlso A.period_id = 2 AndAlso A.check_date > FirstCal.check_date
                               Order By A.check_date).FirstOrDefault
                Dim Eom = If(NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Month <> FirstCal.check_date.GetValueOrDefault.Month OrElse CompanyData.FED_DEP_STAT = "SEMI-WEEKLY", 1, 0)
                PayrollEnt.EOM_FG2 = Eom
                PayrollEnt.EOM_FG1 = Eom
            Else
                PayrollEnt.PAY_FREQ2 = ""
            End If
            If Me.PayrollEnt.FED_UCI.Value.ToString.Contains("3") Then
                'PayrollEnt.PER3_ST_DATE = CDate(Me.PER3_ST_DATE.EditValue)
                'PayrollEnt.PER3_END_DATE = CDate(Me.PER3_END_DATE.EditValue)
                PayrollEnt.PERIOD_CODE3 = Me.PERIOD_CODE3.EditValue
                PayrollEnt.PAY_FREQ3 = Me.PAY_FREQ3.EditValue
                Dim NextCal = (From A In DB.CALENDARs
                               Where A.conum = Options.Conum AndAlso A.period_id = 3 AndAlso A.check_date > FirstCal.check_date
                               Order By A.check_date).FirstOrDefault
                Dim Eom = If(NextCal Is Nothing OrElse NextCal.check_date.GetValueOrDefault.Month <> FirstCal.check_date.GetValueOrDefault.Month OrElse CompanyData.FED_DEP_STAT = "SEMI-WEEKLY", 1, 0)
                PayrollEnt.EOM_FG3 = Eom
                PayrollEnt.EOM_FG1 = Eom
            Else
                PayrollEnt.PAY_FREQ3 = ""
            End If
            'End If
            If Me.chkUseSet1.Checked Then
                PayrollEnt.PER1_ST_DATE = CDate(Me.PER1_ST_DATE.EditValue)
                PayrollEnt.PER1_END_DATE = CDate(Me.PER1_END_DATE.EditValue)
            End If
            If Me.chkUseSet2.Checked Then
                PayrollEnt.PER2_ST_DATE = CDate(Me.PER2_ST_DATE.EditValue)
                PayrollEnt.PER2_END_DATE = CDate(Me.PER2_END_DATE.EditValue)
            End If
            If Me.chkUseSet3.Checked Then
                PayrollEnt.PER3_ST_DATE = CDate(Me.PER3_ST_DATE.EditValue)
                PayrollEnt.PER3_END_DATE = CDate(Me.PER3_END_DATE.EditValue)
            End If
            PayrollEnt.CHECK_DATE = CDate(Me.CHECK_DATE.EditValue)
            PayrollEnt.SUPP_PR = Me.chkSupp_PR.EditValue

            Dim Supp_PR2 = (From A In DB.pr_batch_notes Where A.Conum = PayrollEnt.CONUM AndAlso A.PrNum = PayrollEnt.PRNUM AndAlso A.EmployeeID = -999).SingleOrDefault
            If Me.chk_Supp_PR2.Checked Then
                If Supp_PR2 Is Nothing Then
                    Supp_PR2 = New pr_batch_note With {.Conum = PayrollEnt.CONUM, .PrNum = PayrollEnt.PRNUM, .EmployeeID = -999, .rowguid = Guid.NewGuid}
                    DB.pr_batch_notes.InsertOnSubmit(Supp_PR2)
                End If
                Supp_PR2.Note = "Supp After Processing"
            Else
                If Supp_PR2 IsNot Nothing Then
                    DB.pr_batch_notes.DeleteOnSubmit(Supp_PR2)
                End If
            End If
            Dim BonusOptions = (From A In DB.pr_batch_notes Where A.Conum = PayrollEnt.CONUM AndAlso A.PrNum = PayrollEnt.PRNUM AndAlso A.EmployeeID = -996).SingleOrDefault
            If Me.rbtnBunusRun.Checked OrElse Me.rbtnBunusChecks.Checked Then
                If BonusOptions Is Nothing Then
                    BonusOptions = New pr_batch_note With {.Conum = PayrollEnt.CONUM, .PrNum = PayrollEnt.PRNUM, .EmployeeID = -996, .rowguid = Guid.NewGuid}
                    DB.pr_batch_notes.InsertOnSubmit(BonusOptions)
                End If
                BonusOptions.Note = String.Format("Turn Off DD: {0}, Fica Only: {1}, Amounts Are Net: {2}",
                                                            chkBonusTurnOffDD.Checked.ToYesNoString,
                                                            chkBunosFicaOnly.Checked.ToYesNoString,
                                                            chkBonusAmountsAreNet.Checked.ToYesNoString)
            Else
                If BonusOptions IsNot Nothing Then
                    DB.pr_batch_notes.DeleteOnSubmit(BonusOptions)
                End If
            End If

            PayrollEnt.PR_DESCR = If(IsDBNull(Me.TextEditPrDesc.EditValue), Nothing, Me.TextEditPrDesc.EditValue)

            Dim PayRec1 = (From A In DB.pr_batch_in_processes Where A.CoNum = Options.Conum AndAlso A.PrNum = EP_API.PayrollID AndAlso (Not A.IsDeleted.HasValue OrElse A.IsDeleted.Value = False)).FirstOrDefault
            If PayRec1 Is Nothing Then
                PayRec1 = New pr_batch_in_process With {.CoNum = Options.Conum,
                                                       .PrNum = Me.PrNum,
                                                       .rowguid = Guid.NewGuid,
                                                       .CalendarID = Cals(0).cal_id,
                                                       .ProcessStatus = "Open",
                                                       .FaxId = Options._FaxId,
                                                       .EmailNum = Options._EmailID,
                                                       .ZendeskTicketId = Options._ZendeskTicketId,
                                                       .CheckDateNextWeekAnswered = CheckDateNextWeekAnsweredResults = DialogResult.Yes}

                If Cals.Count > 1 Then PayRec1.CalendarID2 = Cals(1).cal_id
                If Cals.Count > 2 Then PayRec1.CalendarID3 = Cals(2).cal_id
                DB.pr_batch_in_processes.InsertOnSubmit(PayRec1)
            Else
                PayRec1.CheckDateNextWeekAnswered = CheckDateNextWeekAnsweredResults = DialogResult.Yes
            End If
            If Not DB.SaveChanges() Then
                DisplayErrorMessage("Unable to save payroll batch process due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Exit Sub
            End If
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
        Catch ex As Exception
            DisplayErrorMessage("Error while staring new payroll", ex)
        End Try
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = System.Windows.Forms.DialogResult.Cancel
    End Sub

    Private Sub txtConfirmPassword_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtConfirmPassword.EditValueChanged
        If String.Equals(txtConfirmPassword.EditValue, txtPassword.EditValue) Then
            If Not DisableNewPayroll Then
                Me.btnStartPayroll.Enabled = True
                Me.btnStartPayroll.Focus()
            Else

            End If
        End If
    End Sub

    Private Sub frmStartNewPayroll_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control AndAlso e.KeyCode = Keys.N Then
            If btnStartPayroll.Enabled Then
                btnStartPayroll.PerformClick()
                e.SuppressKeyPress = True
            End If
        End If
    End Sub

    Private Sub chk_Supp_PR2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chk_Supp_PR2.CheckedChanged
        If Me.chk_Supp_PR2.Checked Then chkSupp_PR.Checked = False
    End Sub

    Private Sub chkSupp_PR_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkSupp_PR.CheckedChanged
        If Me.chkSupp_PR.Checked Then chk_Supp_PR2.Checked = False
    End Sub

    Private Sub SimpleButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SimpleButton1.Click
        Dim Forms As New List(Of Form)()
        Dim formType As Type = Type.GetType("System.Windows.Forms.Form")
        For Each t As Type In sender.GetType().Assembly.GetTypes()
            If UCase(t.BaseType.ToString) = "SYSTEM.WINDOWS.FORMS.FORM" Then
                Debug.Print(t.Name) 'MsgBox(t.Name)
            End If
        Next
    End Sub

    Private Sub btnMinimumWage_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMinimumWage.Click
        'Dim endDate As Date = PER1_END_DATE.EditValue
        Dim endDate As Date
        If chkSupp_PR.Checked OrElse chk_Supp_PR2.Checked Then
            endDate = PayrollEnt.CHECK_DATE
        Else
            endDate = If(PER1_END_DATE.HasValue, PER1_END_DATE.EditValue, If(PER2_END_DATE.HasValue, PER2_END_DATE.EditValue, PER3_END_DATE.EditValue))
        End If
        Dim minWageFrm As New frmMinimumWageList With {.CoNum = Options.Conum, .IsPayroll = True, .PayrollEndDate = endDate}
        minWageFrm.ShowDialog()
        minWageFrm.Dispose()
        CheckMinWageHasIssues(True)
    End Sub

    Private Sub rbtnBunusRun_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbtnBunusRun.CheckedChanged, rbtnBunusChecks.CheckedChanged
        Me.pnlBonusOptions.Visible = Me.rbtnBunusRun.Checked OrElse Me.rbtnBunusChecks.Checked
        If Me.rbtnBunusRun.Checked Then
            Me.TextEditPrDesc.EditValue = "Bonus Run"
            Me.chkSupp_PR.Checked = True
        ElseIf Me.rbtnBunusChecks.Checked Then
            Me.TextEditPrDesc.EditValue = "Bonus Checks"
        Else
            If PayrollEnt.PR_DESCR.Contains("Bonus") Then
                Me.TextEditPrDesc.EditValue = ""
            End If
        End If
    End Sub

    Private Sub chkUseSet1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkUseSet1.CheckedChanged, chkUseSet2.CheckedChanged, chkUseSet3.CheckedChanged
        Dim chk As DevExpress.XtraEditors.CheckEdit = sender
        Dim Num = Integer.Parse(chk.Name.Last)
        Dim p = {Me.pnlSet1, Me.pnlSet2, Me.pnlSet3}
        p(Num - 1).Visible = chk.Checked

        If Not chk.Checked Then
            Dim SelectedCal = (From A As CALENDAR In CType(Me.gridCalendar.DataSource, IList) Where A.period_id = Num AndAlso A.IsSelectedForPowergrid).FirstOrDefault
            If SelectedCal IsNot Nothing Then
                SelectedCal.IsSelectedForPowergrid = False
            End If
        End If

        Dim UsedSets As New List(Of Integer)
        If Me.chkUseSet1.Checked Then UsedSets.Add(1)
        If Me.chkUseSet2.Checked Then UsedSets.Add(2)
        If Me.chkUseSet3.Checked Then UsedSets.Add(3)


        Me.gridCalendar.DataSource = (From A In Me.CalendarData Where UsedSets.Contains(A.period_id)).ToList
        For Each pSet In UsedSets
            Dim SelectedCal = (From A As CALENDAR In CType(Me.gridCalendar.DataSource, IList) Where A.period_id = pSet AndAlso A.IsSelectedForPowergrid).FirstOrDefault
            If SelectedCal Is Nothing Then
                Dim Cal = (From A In CalendarData Where A.check_date >= Today AndAlso A.period_id = pSet AndAlso A.completed <> "YES").FirstOrDefault
                If Cal IsNot Nothing Then
                    Me.GridViewCalendar.FocusedRowHandle = CalendarData.IndexOf(Cal)
                    Me.GridViewCalendar.MakeRowVisible(Me.GridViewCalendar.FocusedRowHandle)
                    Cal.IsSelectedForPowergrid = True
                    'Me.GridViewCalendar.SetRowCellValue(Me.GridViewCalendar.FocusedRowHandle, colSelectCalendar, True)
                End If
            End If
        Next
        SetPGDates(True)
        CheckMinWageHasIssues()
    End Sub

    Private Function AllowPayrollOnlineClients(selectedCals As List(Of CALENDAR)) As Boolean

        If selectedCals.All(Function(c) c.entry_type = "Auto PR") Then
            Return True
        End If

        Dim co = DB.CoOptions_Payrolls.SingleOrDefault(Function(c) c.CoNum = Options.Conum)
        If co IsNot Nothing AndAlso co.SuppressNewPayrollOnlineAlert Then
            Return True
        End If

        'if there's a payroll within the last 3 payroll's which was done online. block user and ask for reason 
        Dim last3Payrolls = DB.PAYROLLs.Where(Function(p) p.CONUM = Options.Conum AndAlso p.FED_UCI.HasValue)

        'only load from payroll if fed_uci contains period_id
        For Each pId In selectedCals.Where(Function(c) c.entry_type <> "Auto PR").Select(Function(c) c.period_id).ToList()
            last3Payrolls = last3Payrolls.Where(Function(p) p.FED_UCI.Value.ToString().Contains(pId))
        Next
        last3Payrolls = last3Payrolls.OrderByDescending(Function(p) p.PRNUM).Take(3)

        If last3Payrolls.Any(Function(p) p.ENTRY_TYPE = "Xpress" OrElse p.OPNAME.StartsWith("952")) Then
            Dim dialog = New frmPayrollOnlineClientCalledIn(Options.Conum, False).ShowDialog()
            If dialog <> System.Windows.Forms.DialogResult.OK Then Return False
        Else
            Dim cnt = DB.PAYROLLs.Where(Function(p) p.CONUM = Options.Conum AndAlso Not p.PAYROLL_STATUS.StartsWith("Prior")).Count()
            If cnt < 4 Then
                Dim dialog = New frmPayrollOnlineClientCalledIn(Options.Conum, True).ShowDialog()
                If dialog <> System.Windows.Forms.DialogResult.OK Then Return False
            End If
        End If
        Return True
    End Function

    Private Sub txtSetProcessTime_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles txtSetProcessTime.Validating

        If txtSetProcessTime.Text = "" Then
            e.Cancel = False
            Exit Sub
        Else
            Dim t = Convert.ToDateTime(txtSetProcessTime.Text)
            If t < Today.AddHours(7) OrElse t > Today.AddHours(16) Then
                DisplayMessageBox("Please enter a Time between 7:00 AM & 4:00 PM")
                e.Cancel = True
                Exit Sub
            Else
                If t.Minute = 0 OrElse t.Minute = 30 Then

                Else
                    DisplayMessageBox("Please enter a Time by quarter hour")
                    e.Cancel = True
                    Exit Sub
                End If
            End If
        End If
    End Sub

    Private Sub MinWageEvent_EditValueChanged(sender As Object, e As EventArgs) Handles CHECK_DATE.EditValueChanged, chkSupp_PR.EditValueChanged, chk_Supp_PR2.EditValueChanged, PER3_END_DATE.EditValueChanged, PER2_END_DATE.EditValueChanged, PER1_END_DATE.EditValueChanged
        CheckMinWageHasIssues()
    End Sub
End Class