﻿Imports System.Data
Imports Microsoft.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Controls

Public Class frmAudit
    Private Db As dbEPDataDataContext
    Private filter As String = "''"
    Private tc As List(Of TableCols)
    Private filterCkControls() As CheckEdit
    Private filterControls() As LookUpEdit
    Private filterLblControls() As LabelControl
    'Private filterCkControlsChecked() As Boolean = {False, False, False, False, False, False, False, False, False, False}
    Dim Data As Object

    Private Function FillData(ByVal sql As String) As DataTable
        Dim adp As SqlDataAdapter = New SqlDataAdapter(sql, Db.Connection)
        Dim ds As DataSet = New DataSet()
        adp.SelectCommand.CommandTimeout = 300
        adp.Fill(ds)
        Data = ds.Tables(0)
        Return Data
    End Function

    Private Sub frmAudit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Db = New dbEPDataDataContext(GetConnectionString)

            lueTables.Properties.DataSource = Query(Of TableInfo)("exec custom.prc_RptCDCTables")
            lueTables.Properties.ValueMember = "object_id"
            lueTables.Properties.DisplayMember = "TableName"
            lueTables.Properties.Columns("TableName").Caption = "Audit Table"

            TablePanel1.Rows(0).Visible = False

            deDateTo.EditValue = CDate(Date.Now.ToShortDateString()).AddHours(23).AddMinutes(59).AddSeconds(59).AddMilliseconds(997)
            deDateFrom.EditValue = Date.Now.AddDays(-6).ToShortDateString()

            filterCkControls = {ceFilter1, ceFilter2, ceFilter3, ceFilter4, ceFilter5, ceFilter6, ceFilter7, ceFilter8, ceFilter9, ceFilter10}
            filterControls = {lueValue1, lueValue2, lueValue3, lueValue4, lueValue5, lueValue6, lueValue7, lueValue8, lueValue9, lueValue10}
            filterLblControls = {lcFilter1, lcFilter2, lcFilter3, lcFilter4, lcFilter5, lcFilter6, lcFilter7, lcFilter8, lcFilter9, lcFilter10}

            lueActionFilter.Properties.DataSource = Query("SELECT Value = 0, Action = 'All' UNION ALL SELECT 2, 'Insert' UNION ALL SELECT 3, 'Update' UNION ALL SELECT 1, 'Delete'")
            lueActionFilter.Properties.ValueMember = "Value"
            lueActionFilter.Properties.DisplayMember = "Action"
            lueActionFilter.Properties.Columns.Add(New LookUpColumnInfo With {.FieldName = "Action", .Caption = "Action"})

            lciSearch.Enabled = False
            lciApply.Enabled = False
            lciExport.Enabled = False
        Catch ex As Exception
            DisplayErrorMessage("Error in frmAudit_Load", ex)
        End Try
    End Sub

    Sub ResetFilters()
        Dim f As Int16
        For f = 0 To filterCkControls.Length - 1
            Dim ctrlCk = filterCkControls(f)
            ctrlCk.Checked = False
        Next
        ceHost.Checked = False
        ceAppName.Checked = False
        ceModBy.Checked = False
        ceColumn.Checked = False
        ceAction.Checked = False
        lciSearch.Enabled = True
        lciApply.Enabled = False
        lciExport.Enabled = False
    End Sub

    Private Function QuoteString(str As String) As String
        Return "'" + str.Replace("'", "''") + "'"
    End Function

    Private Function EnQuoteString(str As String) As String
        If str.StartsWith("'") AndAlso str.EndsWith("'") AndAlso str.Length >= 2 Then
            Return str.Substring(1, str.Length - 2).Replace("''", "'")
        Else
            Return str
        End If
    End Function

    Private Function AddFilter(ByRef Filter As String, Add As String) As String
        If Filter.StartsWith("'") AndAlso Filter = "''" Then
            Filter = QuoteString(Add)
        ElseIf Filter.StartsWith("'") Then
            Filter = QuoteString(EnQuoteString(Filter) + " AND " + Add)
        ElseIf Filter = "" Then
            Filter = Add
        Else
            Filter = Filter + " AND " + Add
        End If
        Return Filter
    End Function

    Private Async Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Try
            LayoutControl1.ShowProgessPanel()
            If lueTables.EditValue = Nothing OrElse deDateFrom.EditValue = Nothing OrElse deDateTo.EditValue = Nothing Then Return
            Me.filter = "''"
            If ceHost.Checked AndAlso txtHostName.EditValue <> "" Then AddFilter(Me.filter, "__$Host = '" + txtHostName.Text + "'")
            If ceAppName.Checked AndAlso txtAppName.EditValue <> "" Then AddFilter(Me.filter, "__$Application = '" + txtAppName.Text + "'")
            If ceModBy.Checked AndAlso txtModifiedBy.EditValue <> "" Then AddFilter(Me.filter, "__$ModifiedBy = '" + txtModifiedBy.Text + "'")
            If ceAction.Checked AndAlso lueActionFilter.EditValue > "0" Then AddFilter(Me.filter, "__$Operation " + IIf(lueActionFilter.EditValue = 3, ">=3", "=" + lueActionFilter.EditValue.ToString()))

            Dim f As Int16
            For f = 0 To tc.Count - 1
                Dim ctrlCk = filterCkControls(f)
                Dim ctrl = filterControls(f)
                If ctrlCk.Checked AndAlso ctrl.EditValue <> Nothing Then
                    If tc(f).SysType = "decimal" Then
                        Dim IsDecimal = Decimal.TryParse(nz(ctrl.EditValue, ""), Nothing)

                        If IsDecimal Then
                            AddFilter(Me.filter, tc(f).ColName + " = " + ctrl.EditValue.ToString)
                        Else
                            ctrlCk.Checked = False
                        End If
                    Else
                        AddFilter(Me.filter, tc(f).ColName + " = '" + ctrl.EditValue + "'")
                    End If
                End If
            Next

            Dim colFilter As String = ""
            If ceColumn.Checked AndAlso lueColumn.EditValue <> Nothing Then
                colFilter = lueColumn.EditValue
            End If

            Dim sql As String = String.Format("EXEC custom." + If(chkToday.Checked, "rpt_Audit_Data_local @table_Name", "rpt_CDC_AuditData @tableName") + " = '{0}'
                ,@StartDate = '{1}'
                ,@EndDate = '{2}'
                ,@Filter = {3}
                ,@ColName = '{4}'
                ,@MaxRows = {5}
            ", lueTables.Text.Substring(0, lueTables.Text.Length - If(lueTables.Text.ToLower.EndsWith("_t"), 2, 0)), deDateFrom.EditValue, deDateTo.EditValue, filter, colFilter, SpinEditRows.EditValue)
            GridView1.Columns.Clear()

            Await Task.Run(Function() FillData(sql))
            GridControl1.DataSource = Data
            GridView1.Columns("ModifiedDate").DisplayFormat.FormatString = "HH:mm:ss MMM dd yyyy "
            lciApply.Enabled = True
            lciExport.Enabled = True

            ''store check state
            'For f = 0 To filterCkControls.Length - 1
            '    'filterCkControlsChecked(f) = filterCkControls(f).Checked
            '    If filterCkControls(f).Checked Then
            '        filterCkControls(f).Enabled = False
            '    End If
            'Next

            lciGrid.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        Catch ex As Exception
            DisplayErrorMessage("Error in frmAudit_ApplyFilter", ex)
        End Try
        LayoutControl1.HideProgressPanel()
    End Sub


    Private Sub lueTables_Validated(sender As Object, e As EventArgs) Handles lueTables.Validated
        Try
            If lueTables.EditValue = Nothing Then
                Return
            End If

            If lueTables.EditValue <> lueTables.OldEditValue Then
                lciGrid.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
                ResetFilters()

                tc = Query(Of TableCols)(String.Format("exec custom.prc_RptCDCTables 'PK_For_Table', '{0}'", lueTables.Text)).ToList()

                Dim x As Int16
                For x = 0 To tc.Count - 1
                    TryCast(filterLblControls(x), LabelControl).Text = tc(x).ColName
                    TryCast(filterCkControls(x), CheckEdit).Checked = False
                    If x = 0 Then
                        If tc(x).ColName.ToLower = "conum" Then
                            lueValue1.Properties.TextEditStyle = TextEditStyles.DisableTextEditor
                            Dim companyList = (From A In Db.COMPANies Select New With {.CONUM = A.CONUM, .CO_NAME = A.CO_NAME, .CoNumAndName = "{0} - {1}".FormatWith(A.CONUM, A.CO_NAME)}).ToList
                            Dim ER = New With {.CONUM = 0D, .CO_NAME = "Choose", .CoNumAndName = "Choose"}
                            companyList.Insert(0, ER)
                            With lueValue1.Properties
                                .Columns.Clear()
                                .DataSource = companyList
                                .DisplayMember = "CoNumAndName"
                                .ValueMember = "CONUM"
                                .NullText = Nothing
                                .Columns.Add(New LookUpColumnInfo With {.Caption = "CoNum", .FieldName = "CONUM", .Width = 150, .Visible = True})
                                .Columns.Add(New LookUpColumnInfo With {.Caption = "Co_Name", .FieldName = "CO_NAME", .Width = 500})
                            End With
                        Else
                            lueValue1.Properties.DataSource = Nothing
                            lueValue1.Properties.TextEditStyle = TextEditStyles.Standard
                            With lueValue1.Properties
                                .Columns.Clear()
                                .DisplayMember = ""
                                .ValueMember = ""
                                .NullText = Nothing
                            End With
                        End If
                    End If
                    filterControls(x).EditValue = Nothing
                    'set visibility of filters to be shown
                    If filterCkControls(x).Visible = False Then
                        filterCkControls(x).Visible = True
                        filterControls(x).Visible = True
                        filterLblControls(x).Visible = True
                    End If
                Next

                'hide other filters
                For x = tc.Count To 9
                    If filterCkControls(x).Visible = True Then
                        filterCkControls(x).Visible = False
                        filterControls(x).Visible = False
                        filterControls(x).EditValue = Nothing
                        filterLblControls(x).Visible = False
                    End If
                Next

                Dim tcAllCol = Query(Of TableCols)(String.Format("exec custom.prc_RptCDCTables 'Cols_For_Table', '{0}'", lueTables.Text)).ToList()
                lueColumn.Properties.DataSource = tcAllCol
                lueColumn.Properties.DisplayMember = "ColName"
                lueColumn.Properties.ValueMember = "ColName"
                lueColumn.Properties.NullText = Nothing
            End If

            Dim sql As String = String.Format("EXEC custom." + If(chkToday.Checked, "rpt_Audit_Data_local @table_Name", "rpt_CDC_AuditData @tableName") + " = '{0}'
							 ,@StartDate = NULL
							 ,@EndDate = NULL
							 ,@Filter = '1=0'
                             ,@ColName = ''
							 ,@MaxRows = 1
        ", lueTables.Text.Substring(0, lueTables.Text.Length - If(lueTables.Text.ToLower.EndsWith("_t"), 2, 0)))
            GridView1.Columns.Clear()
            Data = Query(sql)
            GridControl1.DataSource = Data
            lciGrid.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always
        Catch ex As Exception
            DisplayErrorMessage("Error in frmAudit_lueTables_Validated", ex)
        End Try
    End Sub

    Private Sub lueTables_KeyUp(sender As Object, e As KeyEventArgs) Handles lueTables.KeyUp
        If e.KeyCode = Keys.Enter Then
            lueTables.DoValidate()
        End If
    End Sub

    Private Sub lueValue_Validated(sender As Object, e As EventArgs) Handles lueValue1.Validated, lueValue2.Validated, lueValue3.Validated, lueValue4.Validated, lueValue5.Validated, lueValue6.Validated, lueValue7.Validated, lueValue8.Validated, lueValue9.Validated, lueValue10.Validated
        Dim ctrl As LookUpEdit = sender
        If ctrl.HasValue AndAlso ctrl.OldEditValue <> ctrl.EditValue Then

            Dim x As Int16
            For x = 0 To filterControls.Length - 1
                If filterControls(x).Equals(ctrl) Then
                    filterCkControls(x).Checked = True
                    Exit For
                End If
            Next
        End If
    End Sub

    Private Sub txt_Validated(sender As Object, e As EventArgs) Handles txtHostName.Validated, txtAppName.Validated, txtModifiedBy.Validated
        Dim ctrl As TextEdit = sender
        If ctrl.HasValue Then
            If ctrl.Equals(txtHostName) Then
                ceHost.Checked = True
            ElseIf ctrl.Equals(txtAppName) Then
                ceAppName.Checked = True
            ElseIf ctrl.Equals(txtModifiedBy) Then
                ceModBy.Checked = True
            End If
        End If
    End Sub

    Private Sub lueColumn_Validated(sender As Object, e As EventArgs) Handles lueColumn.Validated
        If lueColumn.HasValue AndAlso ceColumn.Checked = False Then
            ceColumn.Checked = True
        End If
    End Sub

    Private Sub lueActionFilter_Validated(sender As Object, e As EventArgs) Handles lueActionFilter.Validated
        If lueActionFilter.HasValue AndAlso ceAction.Checked = False Then
            ceAction.Checked = True
        End If
    End Sub

    Private Sub btnApply_Click(sender As Object, e As EventArgs) Handles btnApply.Click
        Dim dv As DataView = New DataView(Data)
        Dim ApplyFilter As String = ""
        If ceHost.Checked AndAlso txtHostName.EditValue <> "" Then AddFilter(ApplyFilter, "Host = '" + txtHostName.Text + "'")
        If ceAppName.Checked AndAlso txtAppName.EditValue <> "" Then AddFilter(ApplyFilter, "Application = '" + txtAppName.Text + "'")
        If ceModBy.Checked AndAlso txtModifiedBy.EditValue <> "" Then AddFilter(ApplyFilter, "ModifiedBy = '" + txtModifiedBy.Text + "'")
        If ceAction.Checked AndAlso lueActionFilter.EditValue > "0" Then AddFilter(ApplyFilter, "Operation = '" + lueActionFilter.Text + "'")
        If ceColumn.Checked AndAlso lueColumn.EditValue > "" Then AddFilter(ApplyFilter, "column_name = '" + lueColumn.Text + "'")

        Dim f As Int16
        For f = 0 To tc.Count - 1
            Dim ctrlCk = filterCkControls(f)
            Dim ctrl = filterControls(f)
            If ctrlCk.Checked AndAlso ctrl.EditValue <> Nothing Then
                If tc(f).SysType = "decimal" Then
                    Dim IsDecimal = Decimal.TryParse(nz(ctrl.EditValue, ""), Nothing)

                    If IsDecimal Then
                        AddFilter(ApplyFilter, tc(f).ColName + " = " + ctrl.EditValue.ToString)
                    End If
                Else
                    AddFilter(ApplyFilter, tc(f).ColName + " = '" + ctrl.EditValue + "'")
                End If
            End If
        Next

        If ceColumn.Checked AndAlso lueColumn.EditValue <> Nothing Then
            AddFilter(ApplyFilter, "column_name = '" + lueColumn.EditValue + "'")
        End If

        dv.RowFilter = ApplyFilter
        GridControl1.DataSource = dv
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        Try
            Using FD As New SaveFileDialog With {.DefaultExt = ".xlsx", .AddExtension = True, .Filter = "Excel Files|*.xls*", .FileName = "Audit_Data"}
                If FD.ShowDialog = System.Windows.Forms.DialogResult.OK Then
                    GridView1.OptionsPrint.AutoWidth = False
                    GridView1.Export(DevExpress.XtraPrinting.ExportTarget.Xlsx, FD.FileName)
                    Dim P As New ProcessStartInfo(FD.FileName)
                    Process.Start(P)
                End If
            End Using
        Catch ex As Exception
            DisplayErrorMessage("Error exporting data.", ex, True)
        End Try
    End Sub

    Private Sub GridView1_CustomDrawEmptyForeground(sender As Object, e As DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs) Handles GridView1.CustomDrawEmptyForeground
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = TryCast(sender, DevExpress.XtraGrid.Views.Grid.GridView)
        If view.RowCount <> 0 Then
            Return
        End If
        Dim drawFormat As New StringFormat()
        drawFormat.LineAlignment = StringAlignment.Center
        drawFormat.Alignment = drawFormat.LineAlignment
        Dim f As Font = New Font("Arial", 20)
        e.Graphics.DrawString("No items exist in this view", f, SystemBrushes.ControlDark, New RectangleF(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height), drawFormat)
    End Sub

    Private Sub lueTables_EditValueChanged(sender As Object, e As EventArgs) Handles lueTables.EditValueChanged
        lueTables.DoValidate()
    End Sub

    Private Sub lueColumn_EditValueChanged(sender As Object, e As EventArgs) Handles lueColumn.EditValueChanged
        lueColumn.DoValidate()
    End Sub

    Private Sub lueActionFilter_EditValueChanged(sender As Object, e As EventArgs) Handles lueActionFilter.EditValueChanged
        lueActionFilter.DoValidate()
    End Sub

    Private Sub lueValue_EditValueChanged(sender As Object, e As EventArgs) Handles lueValue1.EditValueChanged, lueValue2.EditValueChanged, lueValue3.EditValueChanged, lueValue4.EditValueChanged, lueValue5.EditValueChanged, lueValue6.EditValueChanged, lueValue7.EditValueChanged, lueValue8.EditValueChanged, lueValue9.EditValueChanged, lueValue10.EditValueChanged
        CType(sender, LookUpEdit).DoValidate()
    End Sub

    Private Sub chkToday_CheckedChanged(sender As Object, e As EventArgs) Handles chkToday.CheckedChanged
        If chkToday.Checked Then
            deDateFrom.EditValue = Today
            deDateTo.EditValue = Today.AddDays(1).AddHours(23).AddMinutes(59).AddSeconds(59).AddMilliseconds(997)
            deDateFrom.Enabled = False
            deDateTo.Enabled = False
        Else
            deDateFrom.Enabled = True
            deDateTo.Enabled = True
        End If
    End Sub
End Class

