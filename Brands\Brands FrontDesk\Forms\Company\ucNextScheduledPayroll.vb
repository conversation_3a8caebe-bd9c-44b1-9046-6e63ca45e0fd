﻿Public Class ucNextScheduledPayroll

    Private ReadOnly _coNum As Decimal
    Private db As dbEPDataDataContext

    Private _data As List(Of fn_NextScheduledPayrollResult)

    Public Sub New(coNum As Decimal, nextPay As List(Of fn_NextScheduledPayrollResult))
        InitializeComponent()
        _coNum = coNum
        _data = nextPay
        BindingSource1.DataSource = (From A In nextPay Where A.completed = "NO" OrElse A.completed Is Nothing).ToList
    End Sub


    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.Allow AndAlso e.MenuType = DevExpress.XtraGrid.Views.Grid.GridMenuType.Row AndAlso e.HitInfo.InRow Then
            Dim row As fn_NextScheduledPayrollResult = GridView1.GetRow(e.HitInfo.RowHandle)
            If row.completed = "NO" Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Change Process Time", Sub()
                                                                                                 panelChangeProcessTime.Show()
                                                                                                 If row.ProcessTime <> "" Then
                                                                                                     SetProcessTime(row.ProcessTime)
                                                                                                 Else
                                                                                                     SetProcessTime("07:00AM")
                                                                                                 End If
                                                                                             End Sub))
            ElseIf row.payroll_num.HasValue Then
                e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Reopen Calendar (USE ONLY TO PEROCESS PPX ONLINE)", Sub()
                                                                                                                               If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure?", "Confirm Re-Open", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = DialogResult.Yes Then
                                                                                                                                   ReOpencalendar(row)
                                                                                                                               End If
                                                                                                                           End Sub))
            End If
        End If
    End Sub

    Friend Sub ShowRecent(Show As Boolean)
        If Show Then
            BindingSource1.DataSource = _data
        Else
            BindingSource1.DataSource = (From A In _data Where A.completed = "NO" OrElse A.completed Is Nothing).ToList
        End If
        Me.GridView1.FocusedRowHandle = 0
    End Sub

    Private Sub btnSetProcessTime_Click(sender As Object, e As EventArgs) Handles btnSetProcessTime.Click
        Dim row As fn_NextScheduledPayrollResult = GridView1.GetRow(GridView1.FocusedRowHandle)
        Dim db = New dbEPDataDataContext(GetConnectionString)
        Dim calenderRule = (From A In db.CALENDAR_RULES Where A.conum = row.conum AndAlso A.period_id = row.period_id).Single
        If calenderRule.ProcessTime = GetProcessTime().ToLongTimeString() Then Exit Sub
        If String.Compare(calenderRule.ProcessTime, "") Then
            If GetProcessTime() = DateTime.MinValue Then
                DisplayMessageBox("Please provide a default process time")
                Exit Sub
            Else
                calenderRule.ProcessTime = GetProcessTime().ToShortTimeString
                If Not db.SaveChanges() Then
                    DisplayErrorMessage("Unable to save process time due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                    Exit Sub
                End If
                MainForm.OpenCompForm(row.conum)
            End If
        End If
    End Sub

    Private Sub panelChangeProcessTime_VisibleChanged(sender As Object, e As EventArgs) Handles panelChangeProcessTime.VisibleChanged
        If panelChangeProcessTime.Visible Then
            GridControl1.Enabled = False
            panelChangeProcessTime.BringToFront()
            panelChangeProcessTime.Location = New Point((Me.Width - panelChangeProcessTime.Width) / 2, (Me.Height - panelChangeProcessTime.Height) / 2)
        Else
            GridControl1.Enabled = True
        End If
    End Sub

    Private Sub btnCloseProcessTimePopUp_Click(sender As Object, e As EventArgs) Handles btnCloseProcessTimePopUp.Click
        panelChangeProcessTime.Hide()
    End Sub

    Private Function GetProcessTime() As DateTime
        Return DateTime.Parse(cbeProcessTime.EditValue)
    End Function

    Private Sub SetProcessTime(time As String)
        Dim dt As DateTime
        If DateTime.TryParse(time, dt) Then
            Dim hr = If(dt.Hour > 12, dt.Hour - 12, dt.Hour)
            cbeProcessTime.SelectedItem = If(hr.ToString().Length = 1, "0" & hr, hr) & ":" & If(dt.Minute = 0, "00", dt.Minute) & If(dt.Hour < 12, "AM", "PM")
        Else
            SetProcessTime("07:00AM")
        End If
    End Sub

    Sub ReOpencalendar(row As fn_NextScheduledPayrollResult)
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim cal = (From A In db.CALENDARs Where A.conum = row.conum AndAlso A.cal_id = row.cal_id).Single
            cal.payroll_num = Nothing
            cal.completed = "NO"
            cal.completed_date = Nothing
            If Not db.SaveChanges() Then
                DisplayErrorMessage("Unable to save calendar changes due to a conflict with another user's changes. Please try again.", New Exception("Concurrency conflict"))
                Return
            End If
        End Using
        row.completed = "NO"
        Me.GridView1.RefreshData()
    End Sub

End Class
