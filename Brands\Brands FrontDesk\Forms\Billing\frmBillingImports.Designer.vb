﻿Namespace Billing

    <Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
    Partial Class frmBillingImports
        Inherits DevExpress.XtraEditors.XtraForm

        'Form overrides dispose to clean up the component list.
        <System.Diagnostics.DebuggerNonUserCode()>
        Protected Overrides Sub Dispose(ByVal disposing As Boolean)
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
            MyBase.Dispose(disposing)
        End Sub

        'Required by the Windows Form Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Windows Form Designer
        'It can be modified using the Windows Form Designer.  
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()>
        Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBillingImports))
            Me.lcRoot = New DevExpress.XtraLayout.LayoutControl()
            Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
            Me.pccMapCoToAodCode = New DevExpress.XtraBars.PopupControlContainer(Me.components)
            Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
            Me.btnCancelCoMap = New DevExpress.XtraEditors.SimpleButton()
            Me.btnSaveCoMap = New DevExpress.XtraEditors.SimpleButton()
            Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
            Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
            Me.slueCoNumAodMap = New DevExpress.XtraEditors.SearchLookUpEdit()
            Me.RibbonControl1 = New DevExpress.XtraBars.Ribbon.RibbonControl()
            Me.bbiImport = New DevExpress.XtraBars.BarButtonItem()
            Me.pmImport = New DevExpress.XtraBars.PopupMenu(Me.components)
            Me.bbiSwipeClockFile = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiAodFile = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiShugoFile = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiOpenCompany = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiRecalculatePrice = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiInvoiceMany = New DevExpress.XtraBars.BarButtonItem()
            Me.btsiShowBilled = New DevExpress.XtraBars.BarToggleSwitchItem()
            Me.bbiAodFile_Old = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiInactiveClientWOpenInvoices = New DevExpress.XtraBars.BarButtonItem()
            Me.bbiCaptureBill = New DevExpress.XtraBars.BarButtonItem()
            Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
            Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
            Me.RepositoryItemDateEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemDateEdit()
            Me.bsViewCompanySummary = New System.Windows.Forms.BindingSource(Me.components)
            Me.SearchLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colCoNumAndName = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPR_CONTACT = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_PHONE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_FAX = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_EMAIL = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCO_STATUS = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.teAodCode = New DevExpress.XtraEditors.TextEdit()
            Me.pccAdditionalUsersInvoice = New DevExpress.XtraBars.PopupControlContainer(Me.components)
            Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
            Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
            Me.btnAdditUsersCancel = New DevExpress.XtraEditors.SimpleButton()
            Me.seAdditionalUsersCount = New DevExpress.XtraEditors.SpinEdit()
            Me.brnAdditUsersInvoice = New DevExpress.XtraEditors.SimpleButton()
            Me.deInvoiceDate = New DevExpress.XtraEditors.DateEdit()
            Me.gcRecentInvoiceLog = New DevExpress.XtraGrid.GridControl()
            Me.gvRecentInvoiceLog = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.coINVOICE_DATE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_NAME2 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_NUM2 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPRICE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colCHK_ENTRIES = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_FREQ_TYPE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colInvoiceNum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.gcGlobalBilling = New DevExpress.XtraGrid.GridControl()
            Me.gvGlobalBilling = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colITEM_NUM1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_NAME1 = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colSTD_PRICE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.gcBillingOverides = New DevExpress.XtraGrid.GridControl()
            Me.BindingSource1 = New System.Windows.Forms.BindingSource(Me.components)
            Me.gvBillingOverides = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colCONUM = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_NUM = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_NAME = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colPriceFromQty = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colACTUAL_PRICE = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colITEM_FREQ = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colDIVNUM = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colLine_Id = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.UcCompInfo1 = New Brands_FrontDesk.ucCompInfo()
            Me.CheckShowNonProcessed = New DevExpress.XtraEditors.CheckEdit()
            Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.SplitterItem1 = New DevExpress.XtraLayout.SplitterItem()
            Me.SplitterItem2 = New DevExpress.XtraLayout.SplitterItem()
            Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.TabbedControlGroup1 = New DevExpress.XtraLayout.TabbedControlGroup()
            Me.lcgBillingOverides = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.lciGlobalBilling = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.tcgMainGrid = New DevExpress.XtraLayout.TabbedControlGroup()
            Me.lcgGrid = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.lcgWelcomeMessage = New DevExpress.XtraLayout.LayoutControlGroup()
            Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
            Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
            Me.BindingSource2 = New System.Windows.Forms.BindingSource(Me.components)
            Me.PopupMenu1 = New DevExpress.XtraBars.PopupMenu(Me.components)
            CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.lcRoot.SuspendLayout()
            CType(Me.pccMapCoToAodCode, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.pccMapCoToAodCode.SuspendLayout()
            CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.GroupControl2.SuspendLayout()
            CType(Me.slueCoNumAodMap.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.pmImport, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemDateEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.RepositoryItemDateEdit1.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.bsViewCompanySummary, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.teAodCode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.pccAdditionalUsersInvoice, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.pccAdditionalUsersInvoice.SuspendLayout()
            CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.GroupControl1.SuspendLayout()
            CType(Me.seAdditionalUsersCount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deInvoiceDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.deInvoiceDate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gcRecentInvoiceLog, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gvRecentInvoiceLog, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gcGlobalBilling, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gvGlobalBilling, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gcBillingOverides, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.gvBillingOverides, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.CheckShowNonProcessed.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lcgBillingOverides, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lciGlobalBilling, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.tcgMainGrid, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lcgGrid, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.lcgWelcomeMessage, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.BindingSource2, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.SuspendLayout()
            '
            'lcRoot
            '
            Me.lcRoot.Controls.Add(Me.LabelControl4)
            Me.lcRoot.Controls.Add(Me.pccMapCoToAodCode)
            Me.lcRoot.Controls.Add(Me.pccAdditionalUsersInvoice)
            Me.lcRoot.Controls.Add(Me.deInvoiceDate)
            Me.lcRoot.Controls.Add(Me.gcRecentInvoiceLog)
            Me.lcRoot.Controls.Add(Me.gcGlobalBilling)
            Me.lcRoot.Controls.Add(Me.gcBillingOverides)
            Me.lcRoot.Controls.Add(Me.UcCompInfo1)
            Me.lcRoot.Controls.Add(Me.CheckShowNonProcessed)
            Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
            Me.lcRoot.Location = New System.Drawing.Point(0, 142)
            Me.lcRoot.Name = "lcRoot"
            Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(348, 180, 906, 791)
            Me.lcRoot.Root = Me.LayoutControlGroup1
            Me.lcRoot.Size = New System.Drawing.Size(1463, 610)
            Me.lcRoot.TabIndex = 0
            Me.lcRoot.Text = "LayoutControl1"
            '
            'LabelControl4
            '
            Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Rockwell", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            Me.LabelControl4.Appearance.Options.UseFont = True
            Me.LabelControl4.Appearance.Options.UseTextOptions = True
            Me.LabelControl4.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            Me.LabelControl4.Location = New System.Drawing.Point(14, 38)
            Me.LabelControl4.Name = "LabelControl4"
            Me.LabelControl4.Size = New System.Drawing.Size(955, 347)
            Me.LabelControl4.StyleController = Me.lcRoot
            Me.LabelControl4.TabIndex = 12
            Me.LabelControl4.Text = "Please Import a file."
            '
            'pccMapCoToAodCode
            '
            Me.pccMapCoToAodCode.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            Me.pccMapCoToAodCode.Controls.Add(Me.GroupControl2)
            Me.pccMapCoToAodCode.Location = New System.Drawing.Point(1036, 208)
            Me.pccMapCoToAodCode.Name = "pccMapCoToAodCode"
            Me.pccMapCoToAodCode.Ribbon = Me.RibbonControl1
            Me.pccMapCoToAodCode.Size = New System.Drawing.Size(362, 121)
            Me.pccMapCoToAodCode.TabIndex = 11
            Me.pccMapCoToAodCode.Visible = False
            '
            'GroupControl2
            '
            Me.GroupControl2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(192, Byte), Integer))
            Me.GroupControl2.Appearance.Options.UseBorderColor = True
            Me.GroupControl2.Controls.Add(Me.btnCancelCoMap)
            Me.GroupControl2.Controls.Add(Me.btnSaveCoMap)
            Me.GroupControl2.Controls.Add(Me.LabelControl3)
            Me.GroupControl2.Controls.Add(Me.LabelControl2)
            Me.GroupControl2.Controls.Add(Me.slueCoNumAodMap)
            Me.GroupControl2.Controls.Add(Me.teAodCode)
            Me.GroupControl2.Dock = System.Windows.Forms.DockStyle.Fill
            Me.GroupControl2.Location = New System.Drawing.Point(0, 0)
            Me.GroupControl2.Name = "GroupControl2"
            Me.GroupControl2.Size = New System.Drawing.Size(362, 121)
            Me.GroupControl2.TabIndex = 0
            Me.GroupControl2.Text = "Map Company To Aod Code"
            '
            'btnCancelCoMap
            '
            Me.btnCancelCoMap.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.cancel_16x16
            Me.btnCancelCoMap.Location = New System.Drawing.Point(6, 92)
            Me.btnCancelCoMap.Name = "btnCancelCoMap"
            Me.btnCancelCoMap.Size = New System.Drawing.Size(75, 23)
            Me.btnCancelCoMap.TabIndex = 5
            Me.btnCancelCoMap.Text = "Cancel"
            '
            'btnSaveCoMap
            '
            Me.btnSaveCoMap.ImageOptions.Image = Global.Brands_FrontDesk.My.Resources.Resources.assignto_16x16
            Me.btnSaveCoMap.Location = New System.Drawing.Point(280, 93)
            Me.btnSaveCoMap.Name = "btnSaveCoMap"
            Me.btnSaveCoMap.Size = New System.Drawing.Size(75, 23)
            Me.btnSaveCoMap.TabIndex = 4
            Me.btnSaveCoMap.Text = "Save"
            '
            'LabelControl3
            '
            Me.LabelControl3.Location = New System.Drawing.Point(6, 62)
            Me.LabelControl3.Name = "LabelControl3"
            Me.LabelControl3.Size = New System.Drawing.Size(28, 13)
            Me.LabelControl3.TabIndex = 3
            Me.LabelControl3.Text = "Co#: "
            '
            'LabelControl2
            '
            Me.LabelControl2.Location = New System.Drawing.Point(6, 33)
            Me.LabelControl2.Name = "LabelControl2"
            Me.LabelControl2.Size = New System.Drawing.Size(32, 13)
            Me.LabelControl2.TabIndex = 2
            Me.LabelControl2.Text = "Code: "
            '
            'slueCoNumAodMap
            '
            Me.slueCoNumAodMap.EditValue = ""
            Me.slueCoNumAodMap.Location = New System.Drawing.Point(44, 59)
            Me.slueCoNumAodMap.MenuManager = Me.RibbonControl1
            Me.slueCoNumAodMap.Name = "slueCoNumAodMap"
            Me.slueCoNumAodMap.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.slueCoNumAodMap.Properties.DataSource = Me.bsViewCompanySummary
            Me.slueCoNumAodMap.Properties.DisplayMember = "CoNumAndName"
            Me.slueCoNumAodMap.Properties.PopupFormSize = New System.Drawing.Size(800, 0)
            Me.slueCoNumAodMap.Properties.PopupView = Me.SearchLookUpEdit1View
            Me.slueCoNumAodMap.Properties.ValueMember = "CONUM"
            Me.slueCoNumAodMap.Size = New System.Drawing.Size(311, 20)
            Me.slueCoNumAodMap.TabIndex = 1
            '
            'RibbonControl1
            '
            Me.RibbonControl1.ExpandCollapseItem.Id = 0
            Me.RibbonControl1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.RibbonControl1.ExpandCollapseItem, Me.RibbonControl1.SearchEditItem, Me.bbiImport, Me.bbiOpenCompany, Me.bbiRecalculatePrice, Me.bbiInvoiceMany, Me.btsiShowBilled, Me.bbiSwipeClockFile, Me.bbiAodFile_Old, Me.bbiAodFile, Me.bbiShugoFile, Me.bbiInactiveClientWOpenInvoices, Me.bbiCaptureBill})
            Me.RibbonControl1.Location = New System.Drawing.Point(0, 0)
            Me.RibbonControl1.MaxItemId = 26
            Me.RibbonControl1.Name = "RibbonControl1"
            Me.RibbonControl1.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1})
            Me.RibbonControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemDateEdit1})
            Me.RibbonControl1.Size = New System.Drawing.Size(1463, 142)
            '
            'bbiImport
            '
            Me.bbiImport.ActAsDropDown = True
            Me.bbiImport.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown
            Me.bbiImport.Caption = "Import"
            Me.bbiImport.DropDownControl = Me.pmImport
            Me.bbiImport.Id = 1
            Me.bbiImport.ImageOptions.Image = CType(resources.GetObject("bbiImport.ImageOptions.Image"), System.Drawing.Image)
            Me.bbiImport.ImageOptions.LargeImage = CType(resources.GetObject("bbiImport.ImageOptions.LargeImage"), System.Drawing.Image)
            Me.bbiImport.Name = "bbiImport"
            '
            'pmImport
            '
            Me.pmImport.ItemLinks.Add(Me.bbiSwipeClockFile)
            Me.pmImport.ItemLinks.Add(Me.bbiAodFile)
            Me.pmImport.ItemLinks.Add(Me.bbiShugoFile)
            Me.pmImport.Name = "pmImport"
            Me.pmImport.Ribbon = Me.RibbonControl1
            '
            'bbiSwipeClockFile
            '
            Me.bbiSwipeClockFile.Caption = "Swipe Clock File"
            Me.bbiSwipeClockFile.Id = 7
            Me.bbiSwipeClockFile.Name = "bbiSwipeClockFile"
            '
            'bbiAodFile
            '
            Me.bbiAodFile.Caption = "Aod File"
            Me.bbiAodFile.Id = 9
            Me.bbiAodFile.Name = "bbiAodFile"
            '
            'bbiShugoFile
            '
            Me.bbiShugoFile.Caption = "Shugo File"
            Me.bbiShugoFile.Id = 10
            Me.bbiShugoFile.Name = "bbiShugoFile"
            '
            'bbiOpenCompany
            '
            Me.bbiOpenCompany.Caption = "Open Co"
            Me.bbiOpenCompany.Id = 2
            Me.bbiOpenCompany.ImageOptions.Image = CType(resources.GetObject("bbiOpenCompany.ImageOptions.Image"), System.Drawing.Image)
            Me.bbiOpenCompany.ImageOptions.LargeImage = CType(resources.GetObject("bbiOpenCompany.ImageOptions.LargeImage"), System.Drawing.Image)
            Me.bbiOpenCompany.Name = "bbiOpenCompany"
            '
            'bbiRecalculatePrice
            '
            Me.bbiRecalculatePrice.Caption = "Recalculate Price"
            Me.bbiRecalculatePrice.Id = 3
            Me.bbiRecalculatePrice.ImageOptions.Image = CType(resources.GetObject("bbiRecalculatePrice.ImageOptions.Image"), System.Drawing.Image)
            Me.bbiRecalculatePrice.ImageOptions.LargeImage = CType(resources.GetObject("bbiRecalculatePrice.ImageOptions.LargeImage"), System.Drawing.Image)
            Me.bbiRecalculatePrice.Name = "bbiRecalculatePrice"
            '
            'bbiInvoiceMany
            '
            Me.bbiInvoiceMany.Caption = "Invoice"
            Me.bbiInvoiceMany.Enabled = False
            Me.bbiInvoiceMany.Id = 5
            Me.bbiInvoiceMany.ImageOptions.Image = CType(resources.GetObject("bbiInvoiceMany.ImageOptions.Image"), System.Drawing.Image)
            Me.bbiInvoiceMany.ImageOptions.LargeImage = CType(resources.GetObject("bbiInvoiceMany.ImageOptions.LargeImage"), System.Drawing.Image)
            Me.bbiInvoiceMany.Name = "bbiInvoiceMany"
            '
            'btsiShowBilled
            '
            Me.btsiShowBilled.Caption = "Unbilled Only"
            Me.btsiShowBilled.Id = 6
            Me.btsiShowBilled.Name = "btsiShowBilled"
            '
            'bbiAodFile_Old
            '
            Me.bbiAodFile_Old.Caption = "Aod File - OLD"
            Me.bbiAodFile_Old.Id = 8
            Me.bbiAodFile_Old.Name = "bbiAodFile_Old"
            '
            'bbiInactiveClientWOpenInvoices
            '
            Me.bbiInactiveClientWOpenInvoices.Caption = "Inactive Clients W Open Invoices"
            Me.bbiInactiveClientWOpenInvoices.Id = 24
            Me.bbiInactiveClientWOpenInvoices.ImageOptions.Image = CType(resources.GetObject("bbiInactiveClientWOpenInvoices.ImageOptions.Image"), System.Drawing.Image)
            Me.bbiInactiveClientWOpenInvoices.Name = "bbiInactiveClientWOpenInvoices"
            Me.bbiInactiveClientWOpenInvoices.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large
            '
            'bbiCaptureBill
            '
            Me.bbiCaptureBill.Caption = "Capture Bill"
            Me.bbiCaptureBill.Id = 25
            Me.bbiCaptureBill.ImageOptions.LargeImage = Global.Brands_FrontDesk.My.Resources.Resources.CaptureBill
            Me.bbiCaptureBill.Name = "bbiCaptureBill"
            '
            'RibbonPage1
            '
            Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1})
            Me.RibbonPage1.Name = "RibbonPage1"
            Me.RibbonPage1.Text = "Home"
            '
            'RibbonPageGroup1
            '
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiImport)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiOpenCompany)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiRecalculatePrice)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiInvoiceMany)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiInactiveClientWOpenInvoices)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.btsiShowBilled)
            Me.RibbonPageGroup1.ItemLinks.Add(Me.bbiCaptureBill)
            Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
            Me.RibbonPageGroup1.Text = "Import"
            '
            'RepositoryItemDateEdit1
            '
            Me.RepositoryItemDateEdit1.AutoHeight = False
            Me.RepositoryItemDateEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.RepositoryItemDateEdit1.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.RepositoryItemDateEdit1.Name = "RepositoryItemDateEdit1"
            '
            'bsViewCompanySummary
            '
            Me.bsViewCompanySummary.DataSource = GetType(Brands_FrontDesk.view_CompanySumarry)
            '
            'SearchLookUpEdit1View
            '
            Me.SearchLookUpEdit1View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCoNumAndName, Me.colPR_CONTACT, Me.colCO_PHONE, Me.colCO_FAX, Me.colCO_EMAIL, Me.colCO_STATUS})
            Me.SearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
            Me.SearchLookUpEdit1View.Name = "SearchLookUpEdit1View"
            Me.SearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
            Me.SearchLookUpEdit1View.OptionsView.ShowGroupPanel = False
            '
            'colCoNumAndName
            '
            Me.colCoNumAndName.FieldName = "CoNumAndName"
            Me.colCoNumAndName.Name = "colCoNumAndName"
            Me.colCoNumAndName.Visible = True
            Me.colCoNumAndName.VisibleIndex = 0
            Me.colCoNumAndName.Width = 100
            '
            'colPR_CONTACT
            '
            Me.colPR_CONTACT.FieldName = "PR_CONTACT"
            Me.colPR_CONTACT.Name = "colPR_CONTACT"
            Me.colPR_CONTACT.Visible = True
            Me.colPR_CONTACT.VisibleIndex = 1
            Me.colPR_CONTACT.Width = 56
            '
            'colCO_PHONE
            '
            Me.colCO_PHONE.FieldName = "CO_PHONE"
            Me.colCO_PHONE.Name = "colCO_PHONE"
            Me.colCO_PHONE.Visible = True
            Me.colCO_PHONE.VisibleIndex = 2
            Me.colCO_PHONE.Width = 56
            '
            'colCO_FAX
            '
            Me.colCO_FAX.FieldName = "CO_FAX"
            Me.colCO_FAX.Name = "colCO_FAX"
            Me.colCO_FAX.Visible = True
            Me.colCO_FAX.VisibleIndex = 3
            Me.colCO_FAX.Width = 56
            '
            'colCO_EMAIL
            '
            Me.colCO_EMAIL.FieldName = "CO_EMAIL"
            Me.colCO_EMAIL.Name = "colCO_EMAIL"
            Me.colCO_EMAIL.Visible = True
            Me.colCO_EMAIL.VisibleIndex = 4
            Me.colCO_EMAIL.Width = 56
            '
            'colCO_STATUS
            '
            Me.colCO_STATUS.FieldName = "CO_STATUS"
            Me.colCO_STATUS.Name = "colCO_STATUS"
            Me.colCO_STATUS.Visible = True
            Me.colCO_STATUS.VisibleIndex = 5
            Me.colCO_STATUS.Width = 60
            '
            'teAodCode
            '
            Me.teAodCode.Location = New System.Drawing.Point(44, 30)
            Me.teAodCode.MenuManager = Me.RibbonControl1
            Me.teAodCode.Name = "teAodCode"
            Me.teAodCode.Properties.ReadOnly = True
            Me.teAodCode.Size = New System.Drawing.Size(178, 20)
            Me.teAodCode.TabIndex = 0
            '
            'pccAdditionalUsersInvoice
            '
            Me.pccAdditionalUsersInvoice.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            Me.pccAdditionalUsersInvoice.Controls.Add(Me.GroupControl1)
            Me.pccAdditionalUsersInvoice.Location = New System.Drawing.Point(1069, 96)
            Me.pccAdditionalUsersInvoice.Name = "pccAdditionalUsersInvoice"
            Me.pccAdditionalUsersInvoice.Ribbon = Me.RibbonControl1
            Me.pccAdditionalUsersInvoice.Size = New System.Drawing.Size(214, 89)
            Me.pccAdditionalUsersInvoice.TabIndex = 10
            Me.pccAdditionalUsersInvoice.Visible = False
            '
            'GroupControl1
            '
            Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
            Me.GroupControl1.AppearanceCaption.Options.UseFont = True
            Me.GroupControl1.Controls.Add(Me.LabelControl1)
            Me.GroupControl1.Controls.Add(Me.btnAdditUsersCancel)
            Me.GroupControl1.Controls.Add(Me.seAdditionalUsersCount)
            Me.GroupControl1.Controls.Add(Me.brnAdditUsersInvoice)
            Me.GroupControl1.Dock = System.Windows.Forms.DockStyle.Fill
            Me.GroupControl1.Location = New System.Drawing.Point(0, 0)
            Me.GroupControl1.Name = "GroupControl1"
            Me.GroupControl1.Size = New System.Drawing.Size(214, 89)
            Me.GroupControl1.TabIndex = 4
            Me.GroupControl1.Text = "Invoice For Additional Users"
            '
            'LabelControl1
            '
            Me.LabelControl1.Location = New System.Drawing.Point(5, 32)
            Me.LabelControl1.Name = "LabelControl1"
            Me.LabelControl1.Size = New System.Drawing.Size(134, 13)
            Me.LabelControl1.TabIndex = 0
            Me.LabelControl1.Text = "Invoice For Additional Users"
            '
            'btnAdditUsersCancel
            '
            Me.btnAdditUsersCancel.ImageOptions.Image = CType(resources.GetObject("btnAdditUsersCancel.ImageOptions.Image"), System.Drawing.Image)
            Me.btnAdditUsersCancel.Location = New System.Drawing.Point(5, 55)
            Me.btnAdditUsersCancel.Name = "btnAdditUsersCancel"
            Me.btnAdditUsersCancel.Size = New System.Drawing.Size(63, 23)
            Me.btnAdditUsersCancel.TabIndex = 3
            Me.btnAdditUsersCancel.Text = "Cancel"
            '
            'seAdditionalUsersCount
            '
            Me.seAdditionalUsersCount.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
            Me.seAdditionalUsersCount.Location = New System.Drawing.Point(145, 29)
            Me.seAdditionalUsersCount.MenuManager = Me.RibbonControl1
            Me.seAdditionalUsersCount.Name = "seAdditionalUsersCount"
            Me.seAdditionalUsersCount.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.seAdditionalUsersCount.Size = New System.Drawing.Size(62, 20)
            Me.seAdditionalUsersCount.TabIndex = 1
            '
            'brnAdditUsersInvoice
            '
            Me.brnAdditUsersInvoice.ImageOptions.Image = CType(resources.GetObject("brnAdditUsersInvoice.ImageOptions.Image"), System.Drawing.Image)
            Me.brnAdditUsersInvoice.Location = New System.Drawing.Point(132, 55)
            Me.brnAdditUsersInvoice.Name = "brnAdditUsersInvoice"
            Me.brnAdditUsersInvoice.Size = New System.Drawing.Size(75, 23)
            Me.brnAdditUsersInvoice.TabIndex = 2
            Me.brnAdditUsersInvoice.Text = "Invoice"
            '
            'deInvoiceDate
            '
            Me.deInvoiceDate.EditValue = Nothing
            Me.deInvoiceDate.Location = New System.Drawing.Point(83, 12)
            Me.deInvoiceDate.MenuManager = Me.RibbonControl1
            Me.deInvoiceDate.Name = "deInvoiceDate"
            Me.deInvoiceDate.Properties.AllowMouseWheel = False
            Me.deInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
            Me.deInvoiceDate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
            Me.deInvoiceDate.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
            Me.deInvoiceDate.Size = New System.Drawing.Size(176, 20)
            Me.deInvoiceDate.StyleController = Me.lcRoot
            Me.deInvoiceDate.TabIndex = 9
            '
            'gcRecentInvoiceLog
            '
            Me.gcRecentInvoiceLog.Location = New System.Drawing.Point(12, 403)
            Me.gcRecentInvoiceLog.MainView = Me.gvRecentInvoiceLog
            Me.gcRecentInvoiceLog.MenuManager = Me.RibbonControl1
            Me.gcRecentInvoiceLog.Name = "gcRecentInvoiceLog"
            Me.gcRecentInvoiceLog.Size = New System.Drawing.Size(963, 195)
            Me.gcRecentInvoiceLog.TabIndex = 8
            Me.gcRecentInvoiceLog.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvRecentInvoiceLog})
            '
            'gvRecentInvoiceLog
            '
            Me.gvRecentInvoiceLog.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.coINVOICE_DATE, Me.colITEM_NAME2, Me.colITEM_NUM2, Me.colPRICE, Me.colCHK_ENTRIES, Me.colITEM_FREQ_TYPE, Me.colInvoiceNum})
            Me.gvRecentInvoiceLog.GridControl = Me.gcRecentInvoiceLog
            Me.gvRecentInvoiceLog.GroupCount = 1
            Me.gvRecentInvoiceLog.GroupSummary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Sum, "PRICE", Nothing, "Total: {0:c} ")})
            Me.gvRecentInvoiceLog.Name = "gvRecentInvoiceLog"
            Me.gvRecentInvoiceLog.OptionsBehavior.Editable = False
            Me.gvRecentInvoiceLog.OptionsView.ShowIndicator = False
            Me.gvRecentInvoiceLog.OptionsView.ShowViewCaption = True
            Me.gvRecentInvoiceLog.SortInfo.AddRange(New DevExpress.XtraGrid.Columns.GridColumnSortInfo() {New DevExpress.XtraGrid.Columns.GridColumnSortInfo(Me.coINVOICE_DATE, DevExpress.Data.ColumnSortOrder.Descending)})
            Me.gvRecentInvoiceLog.ViewCaption = "Recent Invoices"
            '
            'coINVOICE_DATE
            '
            Me.coINVOICE_DATE.Caption = "Date"
            Me.coINVOICE_DATE.FieldName = "invoice_date"
            Me.coINVOICE_DATE.GroupInterval = DevExpress.XtraGrid.ColumnGroupInterval.DateMonth
            Me.coINVOICE_DATE.Name = "coINVOICE_DATE"
            Me.coINVOICE_DATE.Visible = True
            Me.coINVOICE_DATE.VisibleIndex = 0
            '
            'colITEM_NAME2
            '
            Me.colITEM_NAME2.Caption = "Name"
            Me.colITEM_NAME2.FieldName = "ITEM_NAME"
            Me.colITEM_NAME2.Name = "colITEM_NAME2"
            Me.colITEM_NAME2.Visible = True
            Me.colITEM_NAME2.VisibleIndex = 0
            '
            'colITEM_NUM2
            '
            Me.colITEM_NUM2.Caption = "Item #"
            Me.colITEM_NUM2.FieldName = "ITEM_NUM"
            Me.colITEM_NUM2.Name = "colITEM_NUM2"
            Me.colITEM_NUM2.Visible = True
            Me.colITEM_NUM2.VisibleIndex = 1
            '
            'colPRICE
            '
            Me.colPRICE.Caption = "Price"
            Me.colPRICE.DisplayFormat.FormatString = "c"
            Me.colPRICE.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colPRICE.FieldName = "PRICE"
            Me.colPRICE.Name = "colPRICE"
            Me.colPRICE.Visible = True
            Me.colPRICE.VisibleIndex = 2
            '
            'colCHK_ENTRIES
            '
            Me.colCHK_ENTRIES.FieldName = "CHK_ENTRIES"
            Me.colCHK_ENTRIES.Name = "colCHK_ENTRIES"
            Me.colCHK_ENTRIES.Visible = True
            Me.colCHK_ENTRIES.VisibleIndex = 3
            '
            'colITEM_FREQ_TYPE
            '
            Me.colITEM_FREQ_TYPE.FieldName = "ITEM_FREQ_TYPE"
            Me.colITEM_FREQ_TYPE.Name = "colITEM_FREQ_TYPE"
            Me.colITEM_FREQ_TYPE.Visible = True
            Me.colITEM_FREQ_TYPE.VisibleIndex = 4
            '
            'colInvoiceNum
            '
            Me.colInvoiceNum.Caption = "Invoice #"
            Me.colInvoiceNum.FieldName = "INVOICE_NUM"
            Me.colInvoiceNum.Name = "colInvoiceNum"
            Me.colInvoiceNum.Visible = True
            Me.colInvoiceNum.VisibleIndex = 5
            '
            'gcGlobalBilling
            '
            Me.gcGlobalBilling.Location = New System.Drawing.Point(999, 423)
            Me.gcGlobalBilling.MainView = Me.gvGlobalBilling
            Me.gcGlobalBilling.MenuManager = Me.RibbonControl1
            Me.gcGlobalBilling.Name = "gcGlobalBilling"
            Me.gcGlobalBilling.Size = New System.Drawing.Size(440, 163)
            Me.gcGlobalBilling.TabIndex = 7
            Me.gcGlobalBilling.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvGlobalBilling})
            '
            'gvGlobalBilling
            '
            Me.gvGlobalBilling.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colITEM_NUM1, Me.colITEM_NAME1, Me.colSTD_PRICE})
            Me.gvGlobalBilling.GridControl = Me.gcGlobalBilling
            Me.gvGlobalBilling.Name = "gvGlobalBilling"
            Me.gvGlobalBilling.OptionsBehavior.Editable = False
            Me.gvGlobalBilling.OptionsView.ShowGroupPanel = False
            Me.gvGlobalBilling.OptionsView.ShowIndicator = False
            '
            'colITEM_NUM1
            '
            Me.colITEM_NUM1.Caption = "Item #"
            Me.colITEM_NUM1.FieldName = "ITEM_NUM"
            Me.colITEM_NUM1.Name = "colITEM_NUM1"
            Me.colITEM_NUM1.Visible = True
            Me.colITEM_NUM1.VisibleIndex = 0
            '
            'colITEM_NAME1
            '
            Me.colITEM_NAME1.Caption = "Name"
            Me.colITEM_NAME1.FieldName = "ITEM_NAME"
            Me.colITEM_NAME1.Name = "colITEM_NAME1"
            Me.colITEM_NAME1.Visible = True
            Me.colITEM_NAME1.VisibleIndex = 1
            '
            'colSTD_PRICE
            '
            Me.colSTD_PRICE.Caption = "Price"
            Me.colSTD_PRICE.FieldName = "STD_PRICE"
            Me.colSTD_PRICE.Name = "colSTD_PRICE"
            Me.colSTD_PRICE.Visible = True
            Me.colSTD_PRICE.VisibleIndex = 2
            '
            'gcBillingOverides
            '
            Me.gcBillingOverides.DataSource = Me.BindingSource1
            Me.gcBillingOverides.Location = New System.Drawing.Point(999, 69)
            Me.gcBillingOverides.MainView = Me.gvBillingOverides
            Me.gcBillingOverides.MenuManager = Me.RibbonControl1
            Me.gcBillingOverides.Name = "gcBillingOverides"
            Me.gcBillingOverides.Size = New System.Drawing.Size(440, 334)
            Me.gcBillingOverides.TabIndex = 6
            Me.gcBillingOverides.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.gvBillingOverides})
            '
            'BindingSource1
            '
            Me.BindingSource1.DataSource = GetType(Brands_FrontDesk.ACT_AUTO_PriceOverride)
            '
            'gvBillingOverides
            '
            Me.gvBillingOverides.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colCONUM, Me.colITEM_NUM, Me.colITEM_NAME, Me.colPriceFromQty, Me.colACTUAL_PRICE, Me.colITEM_FREQ, Me.colDIVNUM, Me.colLine_Id})
            Me.gvBillingOverides.GridControl = Me.gcBillingOverides
            Me.gvBillingOverides.Name = "gvBillingOverides"
            Me.gvBillingOverides.OptionsBehavior.Editable = False
            Me.gvBillingOverides.OptionsView.ShowGroupPanel = False
            Me.gvBillingOverides.OptionsView.ShowIndicator = False
            '
            'colCONUM
            '
            Me.colCONUM.FieldName = "CONUM"
            Me.colCONUM.Name = "colCONUM"
            '
            'colITEM_NUM
            '
            Me.colITEM_NUM.Caption = "Item #"
            Me.colITEM_NUM.FieldName = "ITEM_NUM"
            Me.colITEM_NUM.Name = "colITEM_NUM"
            Me.colITEM_NUM.Visible = True
            Me.colITEM_NUM.VisibleIndex = 0
            '
            'colITEM_NAME
            '
            Me.colITEM_NAME.Caption = "Name"
            Me.colITEM_NAME.FieldName = "ITEM_NAME"
            Me.colITEM_NAME.Name = "colITEM_NAME"
            Me.colITEM_NAME.Visible = True
            Me.colITEM_NAME.VisibleIndex = 1
            '
            'colPriceFromQty
            '
            Me.colPriceFromQty.Caption = "Price From Qty"
            Me.colPriceFromQty.FieldName = "PriceFromQty"
            Me.colPriceFromQty.Name = "colPriceFromQty"
            Me.colPriceFromQty.Visible = True
            Me.colPriceFromQty.VisibleIndex = 2
            '
            'colACTUAL_PRICE
            '
            Me.colACTUAL_PRICE.Caption = "Actual Price"
            Me.colACTUAL_PRICE.FieldName = "ACTUAL_PRICE"
            Me.colACTUAL_PRICE.Name = "colACTUAL_PRICE"
            Me.colACTUAL_PRICE.Visible = True
            Me.colACTUAL_PRICE.VisibleIndex = 3
            '
            'colITEM_FREQ
            '
            Me.colITEM_FREQ.FieldName = "ITEM_FREQ"
            Me.colITEM_FREQ.Name = "colITEM_FREQ"
            '
            'colDIVNUM
            '
            Me.colDIVNUM.FieldName = "DIVNUM"
            Me.colDIVNUM.Name = "colDIVNUM"
            '
            'colLine_Id
            '
            Me.colLine_Id.FieldName = "Line_Id"
            Me.colLine_Id.Name = "colLine_Id"
            '
            'UcCompInfo1
            '
            Me.UcCompInfo1.Location = New System.Drawing.Point(999, 69)
            Me.UcCompInfo1.Name = "UcCompInfo1"
            Me.UcCompInfo1.Size = New System.Drawing.Size(440, 517)
            Me.UcCompInfo1.TabIndex = 5
            '
            'CheckShowNonProcessed
            '
            Me.CheckShowNonProcessed.Location = New System.Drawing.Point(302, 12)
            Me.CheckShowNonProcessed.MenuManager = Me.RibbonControl1
            Me.CheckShowNonProcessed.Name = "CheckShowNonProcessed"
            Me.CheckShowNonProcessed.Properties.Caption = "Show Unmatched"
            Me.CheckShowNonProcessed.Size = New System.Drawing.Size(119, 19)
            Me.CheckShowNonProcessed.StyleController = Me.lcRoot
            Me.CheckShowNonProcessed.TabIndex = 13
            '
            'LayoutControlGroup1
            '
            Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
            Me.LayoutControlGroup1.GroupBordersVisible = False
            Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.SplitterItem1, Me.SplitterItem2, Me.LayoutControlItem5, Me.TabbedControlGroup1, Me.LayoutControlItem4, Me.tcgMainGrid, Me.LayoutControlItem6, Me.EmptySpaceItem1, Me.EmptySpaceItem2})
            Me.LayoutControlGroup1.Name = "Root"
            Me.LayoutControlGroup1.Size = New System.Drawing.Size(1463, 610)
            Me.LayoutControlGroup1.TextVisible = False
            '
            'SplitterItem1
            '
            Me.SplitterItem1.AllowHotTrack = True
            Me.SplitterItem1.Inverted = True
            Me.SplitterItem1.IsCollapsible = DevExpress.Utils.DefaultBoolean.[True]
            Me.SplitterItem1.Location = New System.Drawing.Point(967, 24)
            Me.SplitterItem1.Name = "SplitterItem1"
            Me.SplitterItem1.Size = New System.Drawing.Size(8, 566)
            '
            'SplitterItem2
            '
            Me.SplitterItem2.AllowHotTrack = True
            Me.SplitterItem2.Inverted = True
            Me.SplitterItem2.IsCollapsible = DevExpress.Utils.DefaultBoolean.[True]
            Me.SplitterItem2.Location = New System.Drawing.Point(0, 383)
            Me.SplitterItem2.Name = "SplitterItem2"
            Me.SplitterItem2.Size = New System.Drawing.Size(967, 8)
            '
            'LayoutControlItem5
            '
            Me.LayoutControlItem5.Control = Me.deInvoiceDate
            Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem5.Name = "LayoutControlItem5"
            Me.LayoutControlItem5.Size = New System.Drawing.Size(251, 24)
            Me.LayoutControlItem5.Text = "Invoice Date: "
            Me.LayoutControlItem5.TextSize = New System.Drawing.Size(68, 13)
            '
            'TabbedControlGroup1
            '
            Me.TabbedControlGroup1.Location = New System.Drawing.Point(975, 24)
            Me.TabbedControlGroup1.Name = "TabbedControlGroup1"
            Me.TabbedControlGroup1.SelectedTabPage = Me.lcgBillingOverides
            Me.TabbedControlGroup1.Size = New System.Drawing.Size(468, 566)
            Me.TabbedControlGroup1.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2, Me.lcgBillingOverides})
            '
            'lcgBillingOverides
            '
            Me.lcgBillingOverides.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.lciGlobalBilling})
            Me.lcgBillingOverides.Location = New System.Drawing.Point(0, 0)
            Me.lcgBillingOverides.Name = "lcgBillingOverides"
            Me.lcgBillingOverides.Size = New System.Drawing.Size(444, 521)
            Me.lcgBillingOverides.Text = "Billng Overides"
            '
            'LayoutControlItem3
            '
            Me.LayoutControlItem3.Control = Me.gcBillingOverides
            Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem3.Name = "LayoutControlItem3"
            Me.LayoutControlItem3.Size = New System.Drawing.Size(444, 338)
            Me.LayoutControlItem3.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem3.TextVisible = False
            '
            'lciGlobalBilling
            '
            Me.lciGlobalBilling.Control = Me.gcGlobalBilling
            Me.lciGlobalBilling.Location = New System.Drawing.Point(0, 338)
            Me.lciGlobalBilling.Name = "lciGlobalBilling"
            Me.lciGlobalBilling.Size = New System.Drawing.Size(444, 183)
            Me.lciGlobalBilling.Text = "Global Prices"
            Me.lciGlobalBilling.TextLocation = DevExpress.Utils.Locations.Top
            Me.lciGlobalBilling.TextSize = New System.Drawing.Size(68, 13)
            '
            'LayoutControlGroup2
            '
            Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2})
            Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
            Me.LayoutControlGroup2.Size = New System.Drawing.Size(444, 521)
            Me.LayoutControlGroup2.Text = "Co Info"
            '
            'LayoutControlItem2
            '
            Me.LayoutControlItem2.Control = Me.UcCompInfo1
            Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem2.Name = "LayoutControlItem2"
            Me.LayoutControlItem2.Size = New System.Drawing.Size(444, 521)
            Me.LayoutControlItem2.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem2.TextVisible = False
            '
            'LayoutControlItem4
            '
            Me.LayoutControlItem4.Control = Me.gcRecentInvoiceLog
            Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 391)
            Me.LayoutControlItem4.Name = "LayoutControlItem4"
            Me.LayoutControlItem4.Size = New System.Drawing.Size(967, 199)
            Me.LayoutControlItem4.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem4.TextVisible = False
            '
            'tcgMainGrid
            '
            Me.tcgMainGrid.Location = New System.Drawing.Point(0, 24)
            Me.tcgMainGrid.Name = "tcgMainGrid"
            Me.tcgMainGrid.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
            Me.tcgMainGrid.SelectedTabPage = Me.lcgGrid
            Me.tcgMainGrid.ShowTabHeader = DevExpress.Utils.DefaultBoolean.[False]
            Me.tcgMainGrid.Size = New System.Drawing.Size(967, 359)
            Me.tcgMainGrid.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
            Me.tcgMainGrid.TabPages.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.lcgWelcomeMessage, Me.lcgGrid})
            '
            'lcgGrid
            '
            Me.lcgGrid.Location = New System.Drawing.Point(0, 0)
            Me.lcgGrid.Name = "lcgGrid"
            Me.lcgGrid.Padding = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0)
            Me.lcgGrid.Size = New System.Drawing.Size(959, 351)
            Me.lcgGrid.Text = "Grid"
            '
            'lcgWelcomeMessage
            '
            Me.lcgWelcomeMessage.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1})
            Me.lcgWelcomeMessage.Location = New System.Drawing.Point(0, 0)
            Me.lcgWelcomeMessage.Name = "lcgWelcomeMessage"
            Me.lcgWelcomeMessage.Size = New System.Drawing.Size(959, 351)
            Me.lcgWelcomeMessage.Text = "Welcome Message"
            '
            'LayoutControlItem1
            '
            Me.LayoutControlItem1.Control = Me.LabelControl4
            Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
            Me.LayoutControlItem1.MinSize = New System.Drawing.Size(70, 17)
            Me.LayoutControlItem1.Name = "LayoutControlItem1"
            Me.LayoutControlItem1.Size = New System.Drawing.Size(959, 351)
            Me.LayoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
            Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem1.TextVisible = False
            '
            'LayoutControlItem6
            '
            Me.LayoutControlItem6.Control = Me.CheckShowNonProcessed
            Me.LayoutControlItem6.Location = New System.Drawing.Point(290, 0)
            Me.LayoutControlItem6.Name = "LayoutControlItem6"
            Me.LayoutControlItem6.Size = New System.Drawing.Size(123, 24)
            Me.LayoutControlItem6.TextSize = New System.Drawing.Size(0, 0)
            Me.LayoutControlItem6.TextVisible = False
            '
            'EmptySpaceItem1
            '
            Me.EmptySpaceItem1.AllowHotTrack = False
            Me.EmptySpaceItem1.Location = New System.Drawing.Point(413, 0)
            Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
            Me.EmptySpaceItem1.Size = New System.Drawing.Size(1030, 24)
            Me.EmptySpaceItem1.TextSize = New System.Drawing.Size(0, 0)
            '
            'EmptySpaceItem2
            '
            Me.EmptySpaceItem2.AllowHotTrack = False
            Me.EmptySpaceItem2.Location = New System.Drawing.Point(251, 0)
            Me.EmptySpaceItem2.Name = "EmptySpaceItem2"
            Me.EmptySpaceItem2.Size = New System.Drawing.Size(39, 24)
            Me.EmptySpaceItem2.TextSize = New System.Drawing.Size(0, 0)
            '
            'PopupMenu1
            '
            Me.PopupMenu1.Name = "PopupMenu1"
            Me.PopupMenu1.Ribbon = Me.RibbonControl1
            '
            'frmBillingImports
            '
            Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.ClientSize = New System.Drawing.Size(1463, 752)
            Me.Controls.Add(Me.lcRoot)
            Me.Controls.Add(Me.RibbonControl1)
            Me.Name = "frmBillingImports"
            Me.Text = "Billing Import"
            CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
            Me.lcRoot.ResumeLayout(False)
            CType(Me.pccMapCoToAodCode, System.ComponentModel.ISupportInitialize).EndInit()
            Me.pccMapCoToAodCode.ResumeLayout(False)
            CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
            Me.GroupControl2.ResumeLayout(False)
            Me.GroupControl2.PerformLayout()
            CType(Me.slueCoNumAodMap.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RibbonControl1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.pmImport, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemDateEdit1.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.RepositoryItemDateEdit1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.bsViewCompanySummary, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.teAodCode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.pccAdditionalUsersInvoice, System.ComponentModel.ISupportInitialize).EndInit()
            Me.pccAdditionalUsersInvoice.ResumeLayout(False)
            CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
            Me.GroupControl1.ResumeLayout(False)
            Me.GroupControl1.PerformLayout()
            CType(Me.seAdditionalUsersCount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deInvoiceDate.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.deInvoiceDate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gcRecentInvoiceLog, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gvRecentInvoiceLog, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gcGlobalBilling, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gvGlobalBilling, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gcBillingOverides, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.BindingSource1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.gvBillingOverides, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.CheckShowNonProcessed.Properties, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.SplitterItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.SplitterItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.TabbedControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lcgBillingOverides, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lciGlobalBilling, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.tcgMainGrid, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lcgGrid, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.lcgWelcomeMessage, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.BindingSource2, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).EndInit()
            Me.ResumeLayout(False)
            Me.PerformLayout()

        End Sub

        Friend WithEvents lcRoot As DevExpress.XtraLayout.LayoutControl
        Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents RibbonControl1 As DevExpress.XtraBars.Ribbon.RibbonControl
        Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
        Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
        Friend WithEvents bbiImport As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents UcCompInfo1 As ucCompInfo
        Friend WithEvents SplitterItem1 As DevExpress.XtraLayout.SplitterItem
        Friend WithEvents bbiOpenCompany As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents gcBillingOverides As DevExpress.XtraGrid.GridControl
        Friend WithEvents gvBillingOverides As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents BindingSource1 As BindingSource
        Friend WithEvents colCONUM As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_NUM As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_FREQ As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colDIVNUM As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colACTUAL_PRICE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_NAME As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colLine_Id As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPriceFromQty As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents BindingSource2 As BindingSource
        Friend WithEvents gcGlobalBilling As DevExpress.XtraGrid.GridControl
        Friend WithEvents gvGlobalBilling As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents colITEM_NUM1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_NAME1 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colSTD_PRICE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents gcRecentInvoiceLog As DevExpress.XtraGrid.GridControl
        Friend WithEvents gvRecentInvoiceLog As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents SplitterItem2 As DevExpress.XtraLayout.SplitterItem
        Friend WithEvents coINVOICE_DATE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_NUM2 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPRICE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCHK_ENTRIES As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_NAME2 As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colITEM_FREQ_TYPE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiRecalculatePrice As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents RepositoryItemDateEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemDateEdit
        Friend WithEvents deInvoiceDate As DevExpress.XtraEditors.DateEdit
        Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents colInvoiceNum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiInvoiceMany As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents pccAdditionalUsersInvoice As DevExpress.XtraBars.PopupControlContainer
        Friend WithEvents btnAdditUsersCancel As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents brnAdditUsersInvoice As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents seAdditionalUsersCount As DevExpress.XtraEditors.SpinEdit
        Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
        Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
        Friend WithEvents btsiShowBilled As DevExpress.XtraBars.BarToggleSwitchItem
        Friend WithEvents pmImport As DevExpress.XtraBars.PopupMenu
        Friend WithEvents bbiSwipeClockFile As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiAodFile_Old As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents pccMapCoToAodCode As DevExpress.XtraBars.PopupControlContainer
        Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
        Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
        Friend WithEvents slueCoNumAodMap As DevExpress.XtraEditors.SearchLookUpEdit
        Friend WithEvents SearchLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents teAodCode As DevExpress.XtraEditors.TextEdit
        Friend WithEvents btnCancelCoMap As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents btnSaveCoMap As DevExpress.XtraEditors.SimpleButton
        Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
        Friend WithEvents bsViewCompanySummary As BindingSource
        Friend WithEvents colCoNumAndName As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colPR_CONTACT As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_PHONE As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_FAX As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_EMAIL As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colCO_STATUS As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents bbiAodFile As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents PopupMenu1 As DevExpress.XtraBars.PopupMenu
        Friend WithEvents TabbedControlGroup1 As DevExpress.XtraLayout.TabbedControlGroup
        Friend WithEvents lcgBillingOverides As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents lciGlobalBilling As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents bbiShugoFile As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents tcgMainGrid As DevExpress.XtraLayout.TabbedControlGroup
        Friend WithEvents lcgGrid As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents lcgWelcomeMessage As DevExpress.XtraLayout.LayoutControlGroup
        Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
        Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents CheckShowNonProcessed As DevExpress.XtraEditors.CheckEdit
        Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
        Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
        Friend WithEvents bbiInactiveClientWOpenInvoices As DevExpress.XtraBars.BarButtonItem
        Friend WithEvents bbiCaptureBill As DevExpress.XtraBars.BarButtonItem
    End Class
End Namespace