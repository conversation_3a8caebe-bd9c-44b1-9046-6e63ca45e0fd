﻿Imports DevExpress.XtraReports.UI

Public Class rptCompanyServices

    Private Sub GroupHeaderBand1_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles GroupHeaderBand1.BeforePrint
        Me.pnlShipAddress.Visible = Me.GetCurrentColumnValue("SHIPSAME") = "NO"

        Dim emails As String = GetCurrentColumnValue("CO_EMAIL")
        Me.CoEmail.Lines = emails.Split({","c, ";"c})

        Me.IndustryOptions.Visible = String.IsNullOrEmpty(Me.Industry.Value)
    End Sub

    Private Sub XrSubreport1_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles XrSubreportDivisions.BeforePrint
        CType(sender, XRSubreport).ReportSource.DataSource = Me.BindingSourceDivisions
    End Sub

    Private Sub XrSubreportRelatedCompanies_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles XrSubreportRelatedCompanies.BeforePrint
        If Me.BindingSourceRelatedCompanies.Count = 0 Then
            e.Cancel = True
        Else
            CType(sender, XRSubreport).ReportSource.DataSource = Me.BindingSourceRelatedCompanies
        End If
    End Sub

    Private Sub XrSubreportCalendar_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles XrSubreportCalendar.BeforePrint
        CType(sender, XRSubreport).ReportSource.DataSource = Me.BindingSourceCalendar
    End Sub

    Private Sub XrSubreportAutos_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles XrSubreportAutos.BeforePrint
        CType(sender, XRSubreport).ReportSource.DataSource = Me.BindingSourceAutos
    End Sub

    Private Sub XrSubreportNotes_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles XrSubreportNotes.BeforePrint
        CType(sender, XRSubreport).ReportSource.DataSource = Me.BindingSourceNotes
    End Sub

    Private Sub XrSubreportContacts_BeforePrint(sender As Object, e As Printing.PrintEventArgs) Handles XrSubreportContacts.BeforePrint
        If Me.BindingSourceContacts.Count = 0 Then
            e.Cancel = True
        Else
            CType(sender, XRSubreport).ReportSource.DataSource = Me.BindingSourceContacts
        End If
    End Sub
End Class