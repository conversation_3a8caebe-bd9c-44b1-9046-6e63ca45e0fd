﻿Imports System.ComponentModel
Public Class ucCompanyDocuments

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Comp As COMPANY
    Private Property db As dbEPDataDataContext

    Sub New()
        InitializeComponent()
    End Sub

    Private Sub ucCompanyDocuments_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'try added by solomon
        <PERSON>
            'may crash in design in parentform
            If Not UserName Is Nothing Then LoadData()
        Catch ex As Exception
            Logger.Error(ex, "ucCompanyBillingItems_Load")
        End Try
    End Sub

    Public Sub LoadData()
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            'BindingSource1.DataSource = db.documents.Where(Function(c) c.conum = Comp.CONUM AndAlso c.issue_id Is Nothing).OrderByDescending(Function(o) o.create_date)
            BindingSource1.DataSource = db.documents.Where(Function(c) c.conum = Comp.CONUM AndAlso c.issue_id Is Nothing).OrderByDescending(Function(o) o.create_date).ToList()
        Catch ex As Exception
            DisplayErrorMessage("Error loading documents", ex)
        End Try
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Dim frm = New frmCompanyDocumentsAddOrEdit(Comp.CONUM)
        If frm.ShowDialog = DialogResult.OK Then
            LoadData()
        End If
    End Sub

    Private Sub btnViewFile_Click(sender As Object, e As EventArgs) Handles btnViewFile.Click
        Try
            Dim row As DOCUMENT = BindingSource1.Current
            If row IsNot Nothing Then
                Dim doc = (From d In db.document_storages Where d.document_id = row.doc_id).FirstOrDefault
                If doc IsNot Nothing Then
                    Dim fileName = $"{System.IO.Path.GetTempPath()}{System.IO.Path.GetFileNameWithoutExtension(doc.name)}-{Guid.NewGuid()}{System.IO.Path.GetExtension(doc.name)}"
                    System.IO.File.WriteAllBytes(fileName, doc.blob.ToArray)
                    Process.Start(fileName)
                End If
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error showing file", ex)
        End Try
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        Dim row As DOCUMENT = BindingSource1.Current
        If row IsNot Nothing AndAlso DevExpress.XtraEditors.XtraMessageBox.Show("Delete - Are you sure?", "Delete", MessageBoxButtons.YesNo) = DialogResult.Yes Then
            db.documents.DeleteOnSubmit(row)
            Dim list = (From ds In db.document_storages Where ds.document_id = row.doc_id AndAlso ds.conum = Comp.CONUM).ToList
            db.document_storages.DeleteAllOnSubmit(list)
            db.SaveChanges()
            LoadData()
        End If
    End Sub

    Private Sub btnFileReplace_Click(sender As Object, e As EventArgs) Handles btnFileReplace.Click
        Dim row As DOCUMENT = BindingSource1.Current
        If row IsNot Nothing Then
            Dim frm = New frmCompanyDocumentsAddOrEdit(Comp.CONUM, row)
            If frm.ShowDialog = DialogResult.OK Then
                LoadData()
            End If
        End If
    End Sub
End Class