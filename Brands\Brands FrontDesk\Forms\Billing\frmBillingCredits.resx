﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="InvoiceBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="ImageCollection1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>670, 17</value>
  </metadata>
  <assembly alias="DevExpress.Utils.v24.2" name="DevExpress.Utils.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="ImageCollection1.ImageStream" type="DevExpress.Utils.ImageCollectionStreamer, DevExpress.Utils.v24.2" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFpEZXZFeHByZXNzLlV0aWxzLnYyMC4xLCBWZXJzaW9uPTIwLjEu
        NC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI4OGQxNzU0ZDcwMGU0OWEMAwAAAFFT
        eXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRv
        a2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAAChEZXZFeHByZXNzLlV0aWxzLkltYWdlQ29sbGVjdGlvblN0
        cmVhbWVyAgAAAAlJbWFnZVNpemUERGF0YQQHE1N5c3RlbS5EcmF3aW5nLlNpemUDAAAAAgIAAAAF/P//
        /xNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAV3aWR0aAZoZWlnaHQAAAgIAwAAABAAAAAQAAAACQUAAAAP
        BQAAAEEGAAACWQMAAIlQTkcNChoKAAAADUlIRFIAAAAQAAAAEAgGAAAAH/P/YQAAAARnQU1BAACvyDcF
        iukAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAC60lEQVQ4T21SW0gVURQ9e+bM
        e67OzJ25V/PS9Z06WJFQ0YPCij56WBQVSkXkhyb0gsigPqQyjSKK3kUvQ6y0iCSitxlhZh9ppSVZQSFi
        RX1FRJz2uSZodWAPe9Zea3H23oe0ZGSQNox3vp/zID09dCQUIuWBACnVNFIkSWShIJB5AMMCD/9ALG9K
        TSVdOTmFfbm5L3t9v+Z6NJrJi8WKQpZRSub/Kxb2W3bqJdedjLlIakeMiN6MRu91Z2WxPt9nH7Kz6xDz
        OXGxKP4tpiccZ+qTxMSrlabZMh0gjYOw13GWn0tIaG9LSWG9aNSTmVl/zPNGc5M/Qv6RaoLBgscJic2V
        htE/C2ATYjKptm1OENYbxtwdltV0JxJhH9PSWFdy8uU9ljUGa5QTG0LeytZw+FmlrvfPANiMmIYBpNw0
        yUZdx5wISyVpSpmqNtZ7HnsfjbKOSOQymuZd87x1Tz2vZ5emfcoH2IJcLhgYYomqkmJZJitw4njEfEEY
        vYTSutO2zd4mJbHn4XD7C8/7vN/Qv6J4G3IMjFhrMYMinPQyHNYiXFfBQL/iOICMKtM80x0KsW68TZfr
        /qzS9YNYix8qjhnwPQ9dFSe4ghB/P+gc7vLcX9eseNbuOOy2ZTUUSNIorNNhBsN+UDxblkMPg8FDr12X
        HTCMX7MBWnep6vfWuDh2IxConSCKI5En/mPAxYWKEml2nJOvUHzUMNhMgCOIJ00D2F0hy98emSa7omln
        FUK8QZNBsbhaUZKbbedspxNkp3SdzRGEMy4hYazx6cZPAdi5VZK+NOkaq1HV44hhGV8iHmGVoqQ02/b5
        TsdmNSheIAgX8InxVxbrl3MwrEkA28sp7b+laT+qZXkfYnEkB8DYIMu772GPF/HauJFGH2AsFqXB9gZv
        ieFMBKhYS2nfGko7UMt5hOJ+S4opfYPiu7jCqYjJQ8VDTPhNnAkAZWhUinmAg7yiTgbIHw+QhznOaOCV
        /S/wcBN1IIjwG2Qzvg9vpJp8AAAAAElFTkSuQmCC4AIAAIlQTkcNChoKAAAADUlIRFIAAAAQAAAAEAgG
        AAAAH/P/YQAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAKCSURBVDhPlZPrS5Nh
        GMbVzexLGEVqHjpZZM5j4AFyLQdmS4WJutS0NdjUYbLN4yotjLSD6eYhK7HSKBIKyw/5oeZUrCibsQTn
        7MAs+mT2N1zdz/vEFtiHuuDH/bxwXz9eeN7X74/4EwH/Adv3xl9lkGRrWpKhOZcMkzUdRoJNkyUNRgs9
        s9mVBgNR05EyR51AXuUJUJ9NwvDYHbx3f+O4PJhzLePdggezv3k7/0VgfukrqLOBV3lEZeYEjE61Y+R5
        Kz7+vI+l1Xtwrw4Rd+H+cRuLK4NwrQxgYeUmbAvnmSCYV3lExfVxuDCoRHN/LmaWzWvx+HB8v8oEG3mV
        R1xkikXzjRw0WbMx8ckoYPsrBrzyCG+wiVd5xPk1MWjsyYLpmhzji1UYd+nx1KmFti0etd0paB2W45mr
        iqjEi6U6JtjMqzziPP0eaFv3o7rtAEbn1QL6ywk41ZGEWU8v9FcSMfpBjUfOcgy9UTLBFsJ7lYFHddFQ
        NyeipCEWDxyFeOg4Bt0lCXRtEuRWR+LiSCYGXipwfVqOwdd5TBBK+ASHNTtw4kwCiutjhaU+4smcAUVN
        UShsjERBQwR6pzIFbs0omCCM8ArWycu2o/x0PFS1+/ji5CFkVYSgz66AyrwN+XXhsE7KBPqns5ggnPAJ
        ZCWROG6OQ5EpBt201GOXofNxPhW3oqI9HmNOIyz2gwI9U3ImiCB8ggxVOEqbuIAtWe1SmlJMfm4RZteE
        j+4JGRNEEV5BUHpBGEob41BgiPEuWmwcdu60ZaCL4FO6RiDeKw0+maoMRaoyBLmV0cipIHS7wW5HQLsL
        Cm00jtDM1uxcpQ77lL0CdhARQcT6f4Dt0S/t5/cL8f6lDrF2UzwAAAAASUVORK5CYIIL
</value>
  </data>
</root>