﻿Namespace Billing
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucShugoFileGrid
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
            Me.components = New System.ComponentModel.Container()
            Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
            Me.shugoFileBindingSource = New System.Windows.Forms.BindingSource(Me.components)
            Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
            Me.colConum = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colConame = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubFullEmps = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubFullPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsHubFullInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubPlusEmps = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubPlusPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsHubPlusInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubBasicEmps = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubBasicPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsHubBasicInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubHREmps = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubHRPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsHubHRInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colBaseFee = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsBaseFeeInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubFlightFull = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubFlightFullPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsHubFlightFullInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubFlightBasic = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colHubFlightBasicPrice = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsHubFlightBasicInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            Me.colIsAllInvoiced = New DevExpress.XtraGrid.Columns.GridColumn()
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.shugoFileBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
            Me.SuspendLayout()
            '
            'GridControl1
            '
            Me.GridControl1.DataSource = Me.shugoFileBindingSource
            Me.GridControl1.Dock = System.Windows.Forms.DockStyle.Fill
            Me.GridControl1.Location = New System.Drawing.Point(0, 0)
            Me.GridControl1.MainView = Me.GridView1
            Me.GridControl1.Name = "GridControl1"
            Me.GridControl1.Size = New System.Drawing.Size(921, 488)
            Me.GridControl1.TabIndex = 0
            Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
            '
            'shugoFileBindingSource
            '
            Me.shugoFileBindingSource.DataSource = GetType(Brands_FrontDesk.Billing.ucShugoFileGrid.ShugoFile)
            '
            'GridView1
            '
            Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colConum, Me.colConame, Me.colHubFullEmps, Me.colHubFullPrice, Me.colIsHubFullInvoiced, Me.colHubPlusEmps, Me.colHubPlusPrice, Me.colIsHubPlusInvoiced, Me.colHubBasicEmps, Me.colHubBasicPrice, Me.colIsHubBasicInvoiced, Me.colHubHREmps, Me.colHubHRPrice, Me.colIsHubHRInvoiced, Me.colBaseFee, Me.colIsBaseFeeInvoiced, Me.colHubFlightFull, Me.colHubFlightFullPrice, Me.colIsHubFlightFullInvoiced, Me.colHubFlightBasic, Me.colHubFlightBasicPrice, Me.colIsHubFlightBasicInvoiced, Me.colIsAllInvoiced})
            Me.GridView1.GridControl = Me.GridControl1
            Me.GridView1.Name = "GridView1"
            Me.GridView1.OptionsBehavior.Editable = False
            Me.GridView1.OptionsSelection.MultiSelect = True
            Me.GridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect
            Me.GridView1.OptionsView.ColumnAutoWidth = False
            Me.GridView1.OptionsView.ShowFooter = True
            Me.GridView1.OptionsView.ShowGroupPanel = False
            '
            'colConum
            '
            Me.colConum.Caption = "CO #"
            Me.colConum.FieldName = "Conum"
            Me.colConum.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left
            Me.colConum.Name = "colConum"
            Me.colConum.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Count, "Conum", "{0}")})
            Me.colConum.Visible = True
            Me.colConum.VisibleIndex = 1
            '
            'colConame
            '
            Me.colConame.Caption = "Co Name"
            Me.colConame.FieldName = "Coname"
            Me.colConame.Name = "colConame"
            Me.colConame.Visible = True
            Me.colConame.VisibleIndex = 2
            '
            'colHubFullEmps
            '
            Me.colHubFullEmps.Caption = "Hub Full Emps - 469"
            Me.colHubFullEmps.FieldName = "HubFullEmps"
            Me.colHubFullEmps.Name = "colHubFullEmps"
            Me.colHubFullEmps.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubFullEmps", "Total={0:0.##}")})
            Me.colHubFullEmps.Visible = True
            Me.colHubFullEmps.VisibleIndex = 3
            '
            'colHubFullPrice
            '
            Me.colHubFullPrice.DisplayFormat.FormatString = "c"
            Me.colHubFullPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colHubFullPrice.FieldName = "HubFullPrice"
            Me.colHubFullPrice.Name = "colHubFullPrice"
            Me.colHubFullPrice.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubFullPrice", "SUM={0:0.##}")})
            Me.colHubFullPrice.Visible = True
            Me.colHubFullPrice.VisibleIndex = 4
            '
            'colIsHubFullInvoiced
            '
            Me.colIsHubFullInvoiced.FieldName = "IsHubFullInvoiced"
            Me.colIsHubFullInvoiced.Name = "colIsHubFullInvoiced"
            Me.colIsHubFullInvoiced.Visible = True
            Me.colIsHubFullInvoiced.VisibleIndex = 5
            '
            'colHubPlusEmps
            '
            Me.colHubPlusEmps.Caption = "Hub Plus Emps - 468"
            Me.colHubPlusEmps.FieldName = "HubPlusEmps"
            Me.colHubPlusEmps.Name = "colHubPlusEmps"
            Me.colHubPlusEmps.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubPlusEmps", "SUM={0:0.##}")})
            Me.colHubPlusEmps.Visible = True
            Me.colHubPlusEmps.VisibleIndex = 6
            '
            'colHubPlusPrice
            '
            Me.colHubPlusPrice.DisplayFormat.FormatString = "c"
            Me.colHubPlusPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colHubPlusPrice.FieldName = "HubPlusPrice"
            Me.colHubPlusPrice.Name = "colHubPlusPrice"
            Me.colHubPlusPrice.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubPlusPrice", "SUM={0:0.##}")})
            Me.colHubPlusPrice.Visible = True
            Me.colHubPlusPrice.VisibleIndex = 7
            '
            'colIsHubPlusInvoiced
            '
            Me.colIsHubPlusInvoiced.FieldName = "IsHubPlusInvoiced"
            Me.colIsHubPlusInvoiced.Name = "colIsHubPlusInvoiced"
            Me.colIsHubPlusInvoiced.Visible = True
            Me.colIsHubPlusInvoiced.VisibleIndex = 8
            '
            'colHubBasicEmps
            '
            Me.colHubBasicEmps.Caption = "Hub Basic Emps - 467"
            Me.colHubBasicEmps.FieldName = "HubBasicEmps"
            Me.colHubBasicEmps.Name = "colHubBasicEmps"
            Me.colHubBasicEmps.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubBasicEmps", "SUM={0:0.##}")})
            Me.colHubBasicEmps.Visible = True
            Me.colHubBasicEmps.VisibleIndex = 9
            '
            'colHubBasicPrice
            '
            Me.colHubBasicPrice.DisplayFormat.FormatString = "c"
            Me.colHubBasicPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colHubBasicPrice.FieldName = "HubBasicPrice"
            Me.colHubBasicPrice.Name = "colHubBasicPrice"
            Me.colHubBasicPrice.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubBasicPrice", "SUM={0:0.##}")})
            Me.colHubBasicPrice.Visible = True
            Me.colHubBasicPrice.VisibleIndex = 10
            '
            'colIsHubBasicInvoiced
            '
            Me.colIsHubBasicInvoiced.FieldName = "IsHubBasicInvoiced"
            Me.colIsHubBasicInvoiced.Name = "colIsHubBasicInvoiced"
            Me.colIsHubBasicInvoiced.Visible = True
            Me.colIsHubBasicInvoiced.VisibleIndex = 11
            '
            'colHubHREmps
            '
            Me.colHubHREmps.Caption = "Hub HR Emps - 470"
            Me.colHubHREmps.FieldName = "HubHREmps"
            Me.colHubHREmps.Name = "colHubHREmps"
            Me.colHubHREmps.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubHREmps", "SUM={0:0.##}")})
            Me.colHubHREmps.Visible = True
            Me.colHubHREmps.VisibleIndex = 12
            '
            'colHubHRPrice
            '
            Me.colHubHRPrice.DisplayFormat.FormatString = "c"
            Me.colHubHRPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colHubHRPrice.FieldName = "HubHRPrice"
            Me.colHubHRPrice.Name = "colHubHRPrice"
            Me.colHubHRPrice.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubHRPrice", "SUM={0:0.##}")})
            Me.colHubHRPrice.Visible = True
            Me.colHubHRPrice.VisibleIndex = 13
            '
            'colIsHubHRInvoiced
            '
            Me.colIsHubHRInvoiced.FieldName = "IsHubHRInvoiced"
            Me.colIsHubHRInvoiced.Name = "colIsHubHRInvoiced"
            Me.colIsHubHRInvoiced.Visible = True
            Me.colIsHubHRInvoiced.VisibleIndex = 14
            '
            'colBaseFee
            '
            Me.colBaseFee.Caption = "Base Fee - 479"
            Me.colBaseFee.DisplayFormat.FormatString = "c"
            Me.colBaseFee.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colBaseFee.FieldName = "BaseFee"
            Me.colBaseFee.Name = "colBaseFee"
            Me.colBaseFee.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "BaseFee", "SUM={0:c}")})
            Me.colBaseFee.Visible = True
            Me.colBaseFee.VisibleIndex = 15
            '
            'colIsBaseFeeInvoiced
            '
            Me.colIsBaseFeeInvoiced.FieldName = "IsBaseFeeInvoiced"
            Me.colIsBaseFeeInvoiced.Name = "colIsBaseFeeInvoiced"
            Me.colIsBaseFeeInvoiced.Visible = True
            Me.colIsBaseFeeInvoiced.VisibleIndex = 16
            '
            'colHubFlightFull
            '
            Me.colHubFlightFull.Caption = "Hub Flight Full - 476"
            Me.colHubFlightFull.FieldName = "HubFlightFull"
            Me.colHubFlightFull.Name = "colHubFlightFull"
            Me.colHubFlightFull.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubFlightFull", "SUM={0:0.##}")})
            Me.colHubFlightFull.Visible = True
            Me.colHubFlightFull.VisibleIndex = 17
            '
            'colHubFlightFullPrice
            '
            Me.colHubFlightFullPrice.DisplayFormat.FormatString = "c"
            Me.colHubFlightFullPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colHubFlightFullPrice.FieldName = "HubFlightFullPrice"
            Me.colHubFlightFullPrice.Name = "colHubFlightFullPrice"
            Me.colHubFlightFullPrice.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubFlightFullPrice", "SUM={0:c}")})
            Me.colHubFlightFullPrice.Visible = True
            Me.colHubFlightFullPrice.VisibleIndex = 18
            '
            'colIsHubFlightFullInvoiced
            '
            Me.colIsHubFlightFullInvoiced.FieldName = "IsHubFlightFullInvoiced"
            Me.colIsHubFlightFullInvoiced.Name = "colIsHubFlightFullInvoiced"
            Me.colIsHubFlightFullInvoiced.Visible = True
            Me.colIsHubFlightFullInvoiced.VisibleIndex = 19
            '
            'colHubFlightBasic
            '
            Me.colHubFlightBasic.Caption = "Hub Flight Basic - 477"
            Me.colHubFlightBasic.FieldName = "HubFlightBasic"
            Me.colHubFlightBasic.Name = "colHubFlightBasic"
            Me.colHubFlightBasic.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubFlightBasic", "SUM={0:0.##}")})
            Me.colHubFlightBasic.Visible = True
            Me.colHubFlightBasic.VisibleIndex = 20
            '
            'colHubFlightBasicPrice
            '
            Me.colHubFlightBasicPrice.DisplayFormat.FormatString = "c"
            Me.colHubFlightBasicPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            Me.colHubFlightBasicPrice.FieldName = "HubFlightBasicPrice"
            Me.colHubFlightBasicPrice.Name = "colHubFlightBasicPrice"
            Me.colHubFlightBasicPrice.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "HubFlightBasicPrice", "SUM={0:c}")})
            Me.colHubFlightBasicPrice.Visible = True
            Me.colHubFlightBasicPrice.VisibleIndex = 21
            '
            'colIsHubFlightBasicInvoiced
            '
            Me.colIsHubFlightBasicInvoiced.FieldName = "IsHubFlightBasicInvoiced"
            Me.colIsHubFlightBasicInvoiced.Name = "colIsHubFlightBasicInvoiced"
            Me.colIsHubFlightBasicInvoiced.Visible = True
            Me.colIsHubFlightBasicInvoiced.VisibleIndex = 22
            '
            'colIsAllInvoiced
            '
            Me.colIsAllInvoiced.FieldName = "IsAllInvoiced"
            Me.colIsAllInvoiced.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Right
            Me.colIsAllInvoiced.Name = "colIsAllInvoiced"
            Me.colIsAllInvoiced.Visible = True
            Me.colIsAllInvoiced.VisibleIndex = 23
            '
            'ucShugoFileGrid
            '
            Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
            Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
            Me.Controls.Add(Me.GridControl1)
            Me.Name = "ucShugoFileGrid"
            Me.Size = New System.Drawing.Size(921, 488)
            CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.shugoFileBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
            CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
            Me.ResumeLayout(False)

        End Sub

        Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
        Friend WithEvents shugoFileBindingSource As BindingSource
        Friend WithEvents colConum As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colConame As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubFullEmps As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubFullPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsHubFullInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubPlusEmps As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubPlusPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsHubPlusInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubBasicEmps As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubBasicPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsHubBasicInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubHREmps As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubHRPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsHubHRInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsAllInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colBaseFee As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsBaseFeeInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubFlightFull As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubFlightFullPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsHubFlightFullInvoiced As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubFlightBasic As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colHubFlightBasicPrice As DevExpress.XtraGrid.Columns.GridColumn
        Friend WithEvents colIsHubFlightBasicInvoiced As DevExpress.XtraGrid.Columns.GridColumn
    End Class
End Namespace