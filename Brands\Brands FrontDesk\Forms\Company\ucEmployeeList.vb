﻿Imports System.ComponentModel
Imports System.Linq.Expressions
Imports Brands_FrontDesk.ucEmployeeInfo
Imports DevExpress.XtraBars
Imports Microsoft.EntityFrameworkCore
Imports Serilog

Public Class ucEmployeeList

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property Logger As ILogger
    Private _conum As Decimal
    Private AllEmployees As List(Of EmployeeList)
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property FilteredEmployeeList As List(Of EmployeeList)
    Private Property SelectedEmployee As EmployeeList

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property Conum As Decimal
        Get
            Return _conum
        End Get
        Set(value As Decimal)
            UcEmployeeInfo1.CoNum = value
            _conum = value
        End Set
    End Property

    Public Sub New()
        Logger = modGlobals.Logger.ForContext(Of ucEmployeeList)
        InitializeComponent()
        ddbFilter.StyleController = Nothing
        ddbFilter.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        AddHandler UcEmployeeInfo1.EndEditResults, AddressOf EndEditResults
        For Each col As DevExpress.XtraGrid.Columns.GridColumn In gvEmployeeList.Columns
            If Not col.OptionsColumn.ShowInCustomizationForm Then Continue For
            Dim bci = New BarCheckItem() With {.Caption = col.Caption, .Checked = col.Visible, .Tag = col.FieldName, .CloseSubMenuOnClick = False}
            AddHandler bci.CheckedChanged, AddressOf ColumnChooserCheckedChanged
            bsiColumnChooser.AddItem(bci)
        Next
    End Sub

    Private Sub ColumnChooserCheckedChanged(sender As Object, e As ItemClickEventArgs)
        Dim bci As BarCheckItem = sender
        Dim col = gvEmployeeList.Columns.ColumnByFieldName(bci.Tag)
        col.Visible = bci.Checked
        If bci.Checked Then gvEmployeeList.MakeColumnVisible(col)
    End Sub

    Private Sub EndEditResults(result As EmployeeEndEditResults)
        If result.Result = DialogResult.OK Then
            Dim emp = GetEmployees(Function(em) em.CONUM = Conum AndAlso em.EMPNUM = result.emp.EMPNUM).Single
            If result.IsNewEmployee Then
                AllEmployees.Add(emp)
                EmployeeListBindingSource.Add(emp)
                Dim index = AllEmployees.FindIndex(Function(e) e.EmpNum = result.emp.EMPNUM)
                gvEmployeeList.FocusedRowHandle = index
            Else
                Dim index = AllEmployees.FindIndex(Function(e) e.EmpNum = result.emp.EMPNUM)
                AllEmployees(index) = emp

                index = FilteredEmployeeList.FindIndex(Function(e) e.EmpNum = result.emp.EMPNUM)
                FilteredEmployeeList(index) = emp

                gvEmployeeList.RefreshRow(gvEmployeeList.FocusedRowHandle)
            End If
        End If
    End Sub

    Private Sub ucEmployeeList_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If DesignMode Then Exit Sub
        LoadData()
    End Sub

    Private Sub LoadData()
        If DesignMode Then Exit Sub
        Try
            AllEmployees = GetEmployees(Function(em) em.CONUM = Conum)
            Dim ctxDB = New dbEPDataDataContext(GetConnectionString)
            'Solomon changed binding soure due to core change
            'DEPARTMENTBindingSource.DataSource = ctx.DEPARTMENTs.Where(Function(c) c.CONUM = Conum)
            DEPARTMENTBindingSource.DataSource = New Castle.Components.DictionaryAdapter.BindingList(Of DEPARTMENT)(ctxDB.DEPARTMENTs.Where(Function(c) c.CONUM = Conum).ToList())
            'DIVISIONBindingSource.DataSource = ctx.DIVISIONs.Where(Function(d) d.CONUM = Conum)
            DIVISIONBindingSource.DataSource = New Castle.Components.DictionaryAdapter.BindingList(Of DIVISION)(ctxDB.DIVISIONs.Where(Function(c) c.CONUM = Conum).ToList())
            EmployeeStatusFilter("Active Only")
        Catch ex As Exception
            Logger.Error(ex, "Error loading data")
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Function GetEmployees(predicate As Expression(Of Func(Of EMPLOYEE, Boolean))) As List(Of EmployeeList)
        Using ctxDB = New dbEPDataDataContext(GetConnectionString)
            Dim _list = ctxDB.EMPLOYEEs.Where(predicate) _
                .Select(Function(em) New EmployeeList With {.EmpNum = em.EMPNUM, .FirstName = em.F_NAME, .LastName = em.L_NAME, .MiddleName = em.M_NAME, .DepNum = em.DEPTNUM, .StartDate = em.START_DATE, .TermDate = em.TERM_DATE, .Ssn = em.SSN, .Tin = em.TIN, .DivNum = em.DIVNUM}).ToList
            Return _list
        End Using
    End Function

    Public Sub RefreshData()
        LoadData()
    End Sub

    Private Sub bciEmployeeStatusFilter_CheckedChanged(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bciAllEmployees.ItemClick, bciActiveOnly.ItemClick, bciRecentlyAdded.ItemClick, bciTerminatedOnly.ItemClick
        If UcEmployeeInfo1.HasChanges() Then
            Dim Changes = New pr_batch_employee_change With {.change_log = Me.UcEmployeeInfo1.GetChanges}
            DisplayMessageBox("You must save or cancel changes before filtering the employees list." & vbCrLf & Changes.GetFormattedString)
            DirectCast(e.Item, BarCheckItem).Checked = Not DirectCast(e.Item, BarCheckItem).Checked
            Exit Sub
        End If
        bciAllEmployees.Checked = False
        bciActiveOnly.Checked = False
        bciRecentlyAdded.Checked = False
        bciTerminatedOnly.Checked = False
        EmployeeStatusFilter(e.Item.Caption)
        DirectCast(e.Item, BarCheckItem).Checked = True
    End Sub

    Private Sub EmployeeStatusFilter(empStatus As String)
        Dim list As IEnumerable(Of EmployeeList) = AllEmployees
        If empStatus = "Active Only" Then
            list = list.Where(Function(em) Not em.TermDate.HasValue)
        ElseIf empStatus = "Terminated Only" Then
            list = list.Where(Function(em) em.TermDate.HasValue)
        ElseIf empStatus = "Recently Added" Then
            list = list.Where(Function(em) em.StartDate > Today.AddMonths(-1))
        End If
        FilteredEmployeeList = list.ToList
        EmployeeListBindingSource.DataSource = FilteredEmployeeList
        UcEmployeeInfo1.SetDataNavigator(EmployeeListBindingSource)
        If FilteredEmployeeList.Count = 0 AndAlso UcEmployeeInfo1.EmployeeEnt Is Nothing Then
            UcEmployeeInfo1.NewEmployee()
        End If
        'lbcEmployees.DataSource = list
    End Sub

    Private Async Sub gvEmployeeList_ValidateRowAsync(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles gvEmployeeList.ValidateRow
        If UcEmployeeInfo1.HasAlerts() Then
            e.Valid = False
            UcEmployeeInfo1.HandleAlerts()
            Return
        End If

        Dim tmpSelectedEmployee As EmployeeList = gvEmployeeList.GetRow(e.RowHandle)
        If UcEmployeeInfo1.HasChanges() Then
            Dim Changes = New pr_batch_employee_change With {.change_log = UcEmployeeInfo1.GetChanges}
            DisplayMessageBox("You must save or cancel changes before switching to another employee." & vbCrLf & Changes.GetFormattedString)
            e.Valid = False
            Exit Sub
        Else
            Await Task.Delay(250)
            Dim b As Boolean = gvEmployeeList.GetRowCellValue(e.RowHandle, colHasChanges)
            gvEmployeeList.SetRowCellValue(e.RowHandle, colHasChanges, Not b)
        End If
    End Sub

    Private Sub gvEmployeeList_InvalidRowException(sender As Object, e As DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs) Handles gvEmployeeList.InvalidRowException
        e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction
    End Sub

    Private Sub LoadEmployee()
        Dim rowHandle = gvEmployeeList.FocusedRowHandle
        Dim b As Boolean = gvEmployeeList.GetRowCellValue(rowHandle, colHasChanges)
        gvEmployeeList.SetRowCellValue(rowHandle, colHasChanges, Not b)
    End Sub

    Private Sub gvEmployeeList_FocusedRowObjectChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs) Handles gvEmployeeList.FocusedRowObjectChanged
        Try
            SelectedEmployee = e.Row
            If SelectedEmployee Is Nothing Then
                UcEmployeeInfo1.NewEmployee()
            Else
                UcEmployeeInfo1.LoadEmployee(SelectedEmployee)
            End If
            Dim b As Boolean = gvEmployeeList.GetRowCellValue(e.RowHandle, colHasChanges)
            gvEmployeeList.SetRowCellValue(e.RowHandle, colHasChanges, Not b)
        Catch ex As Exception
            Logger.Error(ex, "Error loading employee")
            DisplayMessageBox("Error loading employee")
        End Try
    End Sub

    Public Sub SelectEmployee(empnum As Decimal)
        Me.gcEmployeeList.ForceInitialize()
        If empnum > 0 Then
            bciAllEmployees.PerformClick()
            Dim rowHandle = gvEmployeeList.LocateByValue("EmpNum", empnum)
            If rowHandle <> DevExpress.XtraGrid.GridControl.InvalidRowHandle Then
                gvEmployeeList.FocusedRowHandle = rowHandle
                gvEmployeeList.MakeRowVisible(rowHandle)
            End If
        End If
    End Sub
End Class
