﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucCompanySummary
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim CustomHeaderButtonImageOptions1 As DevExpress.XtraBars.Docking.CustomHeaderButtonImageOptions = New DevExpress.XtraBars.Docking.CustomHeaderButtonImageOptions()
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Me.DocumentManager1 = New DevExpress.XtraBars.Docking2010.DocumentManager(Me.components)
        Me.WidgetView1 = New DevExpress.XtraBars.Docking2010.Views.Widget.WidgetView(Me.components)
        Me.ucCompInfoDocument = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        Me.ucNextScheduledPayrollDocument = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        Me.ucMostRecentPayrollDocument = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        Me.ucCompanyActivityHistoryWidgetDocument = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        Me.ucCompanyServicesDocument = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        Me.ucCompanyEmailAlertsDocument = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        Me.RowDefinition1 = New DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition()
        Me.RowDefinition2 = New DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition()
        Me.RowDefinition3 = New DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition()
        Me.RowDefinition4 = New DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition()
        Me.RowDefinition5 = New DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition()
        Me.StackGroup1 = New DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup(Me.components)
        Me.StackGroup2 = New DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup(Me.components)
        Me.ColumnDefinition3 = New DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition()
        Me.ColumnDefinition1 = New DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition()
        Me.ColumnDefinition2 = New DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition()
        Me.ucUserLogs = New DevExpress.XtraBars.Docking2010.Views.Widget.Document(Me.components)
        CType(Me.DocumentManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.WidgetView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucCompInfoDocument, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucNextScheduledPayrollDocument, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucMostRecentPayrollDocument, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucCompanyActivityHistoryWidgetDocument, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucCompanyServicesDocument, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucCompanyEmailAlertsDocument, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RowDefinition1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RowDefinition2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RowDefinition3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RowDefinition4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RowDefinition5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StackGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StackGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ColumnDefinition3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ColumnDefinition1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ColumnDefinition2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ucUserLogs, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'DocumentManager1
        '
        Me.DocumentManager1.ContainerControl = Me
        Me.DocumentManager1.ShowThumbnailsInTaskBar = DevExpress.Utils.DefaultBoolean.[False]
        Me.DocumentManager1.View = Me.WidgetView1
        Me.DocumentManager1.ViewCollection.AddRange(New DevExpress.XtraBars.Docking2010.Views.BaseView() {Me.WidgetView1})
        '
        'WidgetView1
        '
        Me.WidgetView1.AllowDocumentStateChangeAnimation = DevExpress.Utils.DefaultBoolean.[True]
        Me.WidgetView1.AllowDragDropWobbleAnimation = DevExpress.Utils.DefaultBoolean.[True]
        Me.WidgetView1.AllowResizeAnimation = DevExpress.Utils.DefaultBoolean.[True]
        Me.WidgetView1.AllowStartupAnimation = DevExpress.Utils.DefaultBoolean.[True]
        Me.WidgetView1.Columns.AddRange(New DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition() {Me.ColumnDefinition1, Me.ColumnDefinition2, Me.ColumnDefinition3})
        Me.WidgetView1.Documents.AddRange(New DevExpress.XtraBars.Docking2010.Views.BaseDocument() {Me.ucCompInfoDocument, Me.ucNextScheduledPayrollDocument, Me.ucMostRecentPayrollDocument, Me.ucCompanyActivityHistoryWidgetDocument, Me.ucCompanyServicesDocument, Me.ucCompanyEmailAlertsDocument, Me.ucUserLogs})
        Me.WidgetView1.LayoutMode = DevExpress.XtraBars.Docking2010.Views.Widget.LayoutMode.TableLayout
        Me.WidgetView1.RootContainer.Orientation = System.Windows.Forms.Orientation.Vertical
        Me.WidgetView1.Rows.AddRange(New DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition() {Me.RowDefinition1, Me.RowDefinition2, Me.RowDefinition3, Me.RowDefinition4, Me.RowDefinition5})
        Me.WidgetView1.StackGroups.AddRange(New DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup() {Me.StackGroup1, Me.StackGroup2})
        Me.WidgetView1.UseLoadingIndicator = DevExpress.Utils.DefaultBoolean.[True]
        Me.WidgetView1.WindowsDialogProperties.NameColumnWidth = 5
        Me.WidgetView1.WindowsDialogProperties.PathColumnWidth = 5
        '
        'ucCompInfoDocument
        '
        Me.ucCompInfoDocument.Caption = "Company Info"
        Me.ucCompInfoDocument.ControlName = "ucCompInfo"
        Me.ucCompInfoDocument.ControlTypeName = "Brands_FrontDesk.ucCompInfo"
        Me.ucCompInfoDocument.Height = 437
        Me.ucCompInfoDocument.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[False]
        Me.ucCompInfoDocument.RowSpan = 2
        Me.ucCompInfoDocument.Width = 444
        '
        'ucNextScheduledPayrollDocument
        '
        Me.ucNextScheduledPayrollDocument.Caption = "Next Scheduled Payroll"
        Me.ucNextScheduledPayrollDocument.ControlName = "ucNextScheduledPayroll"
        Me.ucNextScheduledPayrollDocument.ControlTypeName = "Brands_FrontDesk.ucNextScheduledPayroll"
        Me.ucNextScheduledPayrollDocument.CustomHeaderButtons.AddRange(New DevExpress.XtraBars.Docking2010.IButton() {New DevExpress.XtraBars.Docking.CustomHeaderButton("Show Recent", True, CustomHeaderButtonImageOptions1, DevExpress.XtraBars.Docking2010.ButtonStyle.CheckButton, "", -1, True, Nothing, True, False, True, SerializableAppearanceObject1, Nothing, -1)})
        Me.ucNextScheduledPayrollDocument.Height = 181
        Me.ucNextScheduledPayrollDocument.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[False]
        Me.ucNextScheduledPayrollDocument.RowIndex = 2
        Me.ucNextScheduledPayrollDocument.Width = 444
        '
        'ucMostRecentPayrollDocument
        '
        Me.ucMostRecentPayrollDocument.Caption = "Most Recent Payroll"
        Me.ucMostRecentPayrollDocument.ColumnIndex = 1
        Me.ucMostRecentPayrollDocument.ControlName = "ucMostRecentPayroll"
        Me.ucMostRecentPayrollDocument.ControlTypeName = "Brands_FrontDesk.ucMostRecentPayroll"
        Me.ucMostRecentPayrollDocument.Height = 98
        Me.ucMostRecentPayrollDocument.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[False]
        Me.ucMostRecentPayrollDocument.Width = 183
        '
        'ucCompanyActivityHistoryWidgetDocument
        '
        Me.ucCompanyActivityHistoryWidgetDocument.Caption = "Activity History"
        Me.ucCompanyActivityHistoryWidgetDocument.ColumnIndex = 1
        Me.ucCompanyActivityHistoryWidgetDocument.ControlName = "ucCompanyActivityHistoryWidget"
        Me.ucCompanyActivityHistoryWidgetDocument.ControlTypeName = "Brands_FrontDesk.ucCompanyActivityHistoryWidget"
        Me.ucCompanyActivityHistoryWidgetDocument.Height = 489
        Me.ucCompanyActivityHistoryWidgetDocument.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[False]
        Me.ucCompanyActivityHistoryWidgetDocument.RowIndex = 1
        Me.ucCompanyActivityHistoryWidgetDocument.RowSpan = 2
        Me.ucCompanyActivityHistoryWidgetDocument.Width = 444
        '
        'ucCompanyServicesDocument
        '
        Me.ucCompanyServicesDocument.Caption = "Services"
        Me.ucCompanyServicesDocument.ControlName = "ucCompanyServices"
        Me.ucCompanyServicesDocument.ControlTypeName = "Brands_FrontDesk.ucCompanyServices"
        Me.ucCompanyServicesDocument.Header = "Services"
        Me.ucCompanyServicesDocument.Height = 136
        Me.ucCompanyServicesDocument.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[False]
        Me.ucCompanyServicesDocument.Properties.AllowCollapse = DevExpress.Utils.DefaultBoolean.[True]
        Me.ucCompanyServicesDocument.RowIndex = 3
        Me.ucCompanyServicesDocument.RowSpan = 2
        Me.ucCompanyServicesDocument.Width = 444
        '
        'ucCompanyEmailAlertsDocument
        '
        Me.ucCompanyEmailAlertsDocument.Caption = "Email Alerts"
        Me.ucCompanyEmailAlertsDocument.ColumnIndex = 1
        Me.ucCompanyEmailAlertsDocument.ControlName = "ucCompanyEmailAlerts"
        Me.ucCompanyEmailAlertsDocument.ControlTypeName = "Brands_FrontDesk.ucEmailAlerts"
        Me.ucCompanyEmailAlertsDocument.Header = "Email Alerts"
        Me.ucCompanyEmailAlertsDocument.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[True]
        Me.ucCompanyEmailAlertsDocument.Properties.AllowCollapse = DevExpress.Utils.DefaultBoolean.[True]
        Me.ucCompanyEmailAlertsDocument.RowIndex = 3
        Me.ucCompanyEmailAlertsDocument.RowSpan = 2
        '
        'RowDefinition1
        '
        Me.RowDefinition1.Length.UnitValue = 2.0R
        '
        'StackGroup1
        '
        Me.StackGroup1.Items.AddRange(New DevExpress.XtraBars.Docking2010.Views.Widget.Document() {Me.ucCompanyServicesDocument})
        '
        'ucUserLogs
        '
        Me.ucUserLogs.Caption = "User Logs"
        Me.ucUserLogs.ColumnIndex = 2
        Me.ucUserLogs.ControlName = "ucUserLogs"
        Me.ucUserLogs.ControlTypeName = "Brands_FrontDesk.ucUserLogs"
        Me.ucUserLogs.Header = "User Logs"
        Me.ucUserLogs.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.[True]
        Me.ucUserLogs.Properties.AllowCollapse = DevExpress.Utils.DefaultBoolean.[True]
        Me.ucUserLogs.RowSpan = 5
        '
        'ucCompanySummary
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Name = "ucCompanySummary"
        Me.Size = New System.Drawing.Size(931, 556)
        CType(Me.DocumentManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.WidgetView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucCompInfoDocument, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucNextScheduledPayrollDocument, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucMostRecentPayrollDocument, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucCompanyActivityHistoryWidgetDocument, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucCompanyServicesDocument, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucCompanyEmailAlertsDocument, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RowDefinition1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RowDefinition2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RowDefinition3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RowDefinition4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RowDefinition5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StackGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StackGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ColumnDefinition3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ColumnDefinition1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ColumnDefinition2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ucUserLogs, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents DocumentManager1 As DevExpress.XtraBars.Docking2010.DocumentManager
    Friend WithEvents WidgetView1 As DevExpress.XtraBars.Docking2010.Views.Widget.WidgetView
    Friend WithEvents ucCompInfoDocument As DevExpress.XtraBars.Docking2010.Views.Widget.Document
    Friend WithEvents ucNextScheduledPayrollDocument As DevExpress.XtraBars.Docking2010.Views.Widget.Document
    Friend WithEvents ucMostRecentPayrollDocument As DevExpress.XtraBars.Docking2010.Views.Widget.Document
    Friend WithEvents ucCompanyActivityHistoryWidgetDocument As DevExpress.XtraBars.Docking2010.Views.Widget.Document
    Friend WithEvents ucCompanyServicesDocument As DevExpress.XtraBars.Docking2010.Views.Widget.Document
    Friend WithEvents StackGroup1 As DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup
    Friend WithEvents StackGroup2 As DevExpress.XtraBars.Docking2010.Views.Widget.StackGroup
    Friend WithEvents RowDefinition1 As DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition
    Friend WithEvents RowDefinition2 As DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition
    Friend WithEvents RowDefinition3 As DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition
    Friend WithEvents RowDefinition4 As DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition
    Friend WithEvents RowDefinition5 As DevExpress.XtraBars.Docking2010.Views.Widget.RowDefinition
    Friend WithEvents ucCompanyEmailAlertsDocument As DevExpress.XtraBars.Docking2010.Views.Widget.Document
    Friend WithEvents ColumnDefinition1 As DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition
    Friend WithEvents ColumnDefinition2 As DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition
    Friend WithEvents ColumnDefinition3 As DevExpress.XtraBars.Docking2010.Views.Widget.ColumnDefinition
    Friend WithEvents ucUserLogs As DevExpress.XtraBars.Docking2010.Views.Widget.Document
End Class
