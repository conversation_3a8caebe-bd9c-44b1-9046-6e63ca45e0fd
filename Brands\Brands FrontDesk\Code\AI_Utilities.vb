﻿'Imports Azure.AI.FormRecognizer.DocumentAnalysis
Imports System.IO
Imports System.Net.Http
Imports System.Net.Http.Headers
Imports Newtonsoft.Json.Linq

Module AI_Utilities
    Public Async Function AIAnalyzePdfAsync(ByVal pdfFilePath As String) As Threading.Tasks.Task(Of Int32)
        'Dim id As Int32
        'Dim db = New dbEPDataDataContext(GetConnectionString)
        'Dim endpointFormRecog As String = "https://taxnotice2.cognitiveservices.azure.com/"
        'Dim subscriptionKeyFormRecog As String = GetUdfValue("Azure AI cognitiveservices subscription key")
        'Dim client = New DocumentAnalysisClient(New Uri(endpointFormRecog), New AzureKeyCredential(subscriptionKeyFormRecog))

        'Dim response As AnalyzeDocumentOperation
        'Using stream = New FileStream(pdfFilePath, FileMode.Open)
        '    response = Await client.AnalyzeDocumentAsync(WaitUntil.Completed, "prebuilt-document", stream)
        'End Using

        'Dim result = response.Value
        'Dim json = Newtonsoft.Json.JsonConvert.SerializeObject(result)
        'id = Query(Of Int32)($"insert custom.AI_FormRecognitionJson(Content) select '{json.Replace("'", "''")}' select @@identity").First()

        'For Each field In result.KeyValuePairs
        '    Dim fieldName As String = field.Key.Content
        '    Dim fieldValue = field.Value?.Content
        'Next

        'Return id
        Return Nothing
    End Function

    Async Function AIDetectBrand(imageFilePath As String) As Threading.Tasks.Task(Of String)
        Dim endpoint As String = "https://taxnoticescomptuervision.cognitiveservices.azure.com/"
        Dim subscriptionKey As String = GetUdfValue("Azure AI comptuervision subscription key")
        Dim client As New HttpClient()
        Dim uri As String = $"{endpoint}/vision/v3.2/analyze?visualFeatures=Brands"

        Using content As New MultipartFormDataContent
            Dim byteData As Byte() = System.IO.File.ReadAllBytes(imageFilePath)
            content.Add(New ByteArrayContent(byteData), "file", IO.Path.GetFileName(imageFilePath))

            client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey)

            Dim response As HttpResponseMessage = Await client.PostAsync(uri, content)
            Dim contentString As String = Await response.Content.ReadAsStringAsync()
            Dim jsonContent As JObject = JObject.Parse(contentString)

            If jsonContent("brands") IsNot Nothing Then
                For Each brand In jsonContent("brands")
                    Dim name As String = brand("name").ToString()
                    Console.WriteLine("Brand/Logo: " & name)
                    ' Check if the brand name matches a known state-based logo or symbol
                    If name.Contains("Oregon") Then
                        Return "Oregon"
                    End If
                Next
            End If
        End Using

        Return "Brand/State not found"
    End Function

    Async Function AIDetectBrandv2(imagePath As String) As Threading.Tasks.Task(Of String)
        Dim endpoint As String = "https://taxnoticescomptuervision.cognitiveservices.azure.com/"
        Dim subscriptionKey As String = GetUdfValue("Azure AI comptuervision subscription key")

        Dim client As New HttpClient()
        Dim uri As String = $"{endpoint}/vision/v3.2/analyze?visualFeatures=Brands"

        Try
            Dim byteData As Byte() = File.ReadAllBytes(imagePath)

            Using content As New ByteArrayContent(byteData)
                content.Headers.ContentType = New MediaTypeHeaderValue("application/octet-stream")
                client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey)

                Dim response As HttpResponseMessage = Await client.PostAsync(uri, content)

                If response.IsSuccessStatusCode Then
                    Dim contentString As String = Await response.Content.ReadAsStringAsync()
                    Dim jsonContent As JObject = JObject.Parse(contentString)

                    If jsonContent("brands") IsNot Nothing Then
                        For Each brand In jsonContent("brands")
                            Dim name As String = brand("name").ToString()
                            Console.WriteLine("Brand/Logo: " & name)
                            Return name
                        Next
                    End If
                    Return "Brand/State not found"
                Else
                    Return "API Error: " & response.ReasonPhrase
                End If
            End Using
        Catch ex As Exception
            Return "Error: " & ex.Message
        End Try
    End Function

    'Async Function DetectLogoWithCustomVision(imagePath As String) As Threading.Tasks.Task(Of String)
    '    Dim endpoint As String = "https://taxnoticescomptuervision.cognitiveservices.azure.com/"
    '    Dim subscriptionKey As String = "7TOjOuNKZasos3HPswMqvuKkZKEtcVvUkTetIv3Oi6IfYA8JVjYXJQQJ99ALACYeBjFXJ3w3AAAFACOGEKn3"

    '    Dim client As New HttpClient()
    '    Dim uri As String = $"{endpoint}/customvision/v3.0/Prediction/{projectId}/detect/iterations/{iterationName}/image"

    '    Try
    '        Dim byteData As Byte() = File.ReadAllBytes(imagePath)

    '        Using content As New ByteArrayContent(byteData)
    '            content.Headers.ContentType = New MediaTypeHeaderValue("application/octet-stream")
    '            client.DefaultRequestHeaders.Add("Prediction-Key", subscriptionKey)

    '            Dim response As HttpResponseMessage = Await client.PostAsync(uri, content)

    '            If response.IsSuccessStatusCode Then
    '                Dim contentString As String = Await response.Content.ReadAsStringAsync()
    '                Dim jsonContent As JObject = JObject.Parse(contentString)

    '                ' Process the predictions
    '                If jsonContent("predictions") IsNot Nothing Then
    '                    For Each prediction In jsonContent("predictions")
    '                        Dim tag As String = prediction("tagName").ToString()
    '                        Dim probability As Double = prediction("probability").ToObject(Of Double)()

    '                        If probability > 0.5 Then ' Adjust threshold as needed
    '                            Return $"Logo Detected: {tag} with {probability:P0}"
    '                        End If
    '                    Next
    '                End If

    '                Return "Logo not found"
    '            Else
    '                Return "API Error: " & response.ReasonPhrase
    '            End If
    '        End Using
    '    Catch ex As Exception
    '        Return "Error: " & ex.Message
    '    End Try
    'End Function
End Module
