Imports DevExpress.XtraEditors

Public Class frmTestConcurrency
    Inherits XtraForm

    Private WithEvents btnTestBasicSave As SimpleButton
    Private WithEvents btnTestConcurrencyConflict As SimpleButton
    Private WithEvents btnTestMultipleConflicts As SimpleButton
    Private WithEvents btnTestRetryLogic As SimpleButton
    Private WithEvents txtResults As MemoEdit
    Private WithEvents lblStatus As LabelControl

    Public Sub New()
        InitializeComponent()
        SetupForm()
    End Sub

    Private Sub InitializeComponent()
        Me.btnTestBasicSave = New SimpleButton()
        Me.btnTestConcurrencyConflict = New SimpleButton()
        Me.btnTestMultipleConflicts = New SimpleButton()
        Me.btnTestRetryLogic = New SimpleButton()
        Me.txtResults = New MemoEdit()
        Me.lblStatus = New LabelControl()

        Me.SuspendLayout()

        ' Form
        Me.Text = "SaveChanges Concurrency Test"
        Me.Size = New System.Drawing.Size(800, 600)
        Me.StartPosition = FormStartPosition.CenterScreen

        ' Status Label
        Me.lblStatus.Text = "Ready to run tests..."
        Me.lblStatus.Location = New System.Drawing.Point(12, 12)
        Me.lblStatus.Size = New System.Drawing.Size(760, 20)

        ' Test Buttons
        Me.btnTestBasicSave.Text = "Test Basic Save"
        Me.btnTestBasicSave.Location = New System.Drawing.Point(12, 40)
        Me.btnTestBasicSave.Size = New System.Drawing.Size(150, 30)

        Me.btnTestConcurrencyConflict.Text = "Test Concurrency Conflict"
        Me.btnTestConcurrencyConflict.Location = New System.Drawing.Point(170, 40)
        Me.btnTestConcurrencyConflict.Size = New System.Drawing.Size(150, 30)

        Me.btnTestMultipleConflicts.Text = "Test Multiple Conflicts"
        Me.btnTestMultipleConflicts.Location = New System.Drawing.Point(328, 40)
        Me.btnTestMultipleConflicts.Size = New System.Drawing.Size(150, 30)

        Me.btnTestRetryLogic.Text = "Test Retry Logic"
        Me.btnTestRetryLogic.Location = New System.Drawing.Point(486, 40)
        Me.btnTestRetryLogic.Size = New System.Drawing.Size(150, 30)

        ' Results Text Box
        Me.txtResults.Location = New System.Drawing.Point(12, 80)
        Me.txtResults.Size = New System.Drawing.Size(760, 480)
        Me.txtResults.Properties.ReadOnly = True
        Me.txtResults.Properties.ScrollBars = ScrollBars.Both

        ' Add controls to form
        Me.Controls.Add(Me.lblStatus)
        Me.Controls.Add(Me.btnTestBasicSave)
        Me.Controls.Add(Me.btnTestConcurrencyConflict)
        Me.Controls.Add(Me.btnTestMultipleConflicts)
        Me.Controls.Add(Me.btnTestRetryLogic)
        Me.Controls.Add(Me.txtResults)

        Me.ResumeLayout(False)
    End Sub

    Private Sub SetupForm()
        AddTestResult("=== SaveChanges Concurrency Test Suite ===")
        AddTestResult("This test suite verifies the SaveChanges extension method handles concurrency conflicts correctly.")
        AddTestResult("")
    End Sub

    Private Sub AddTestResult(message As String)
        If Me.InvokeRequired Then
            Me.Invoke(Sub() AddTestResult(message))
            Return
        End If

        txtResults.Text += $"{DateTime.Now:HH:mm:ss.fff} - {message}{Environment.NewLine}"
        txtResults.SelectionStart = txtResults.Text.Length
        txtResults.ScrollToCaret()
        Application.DoEvents()
    End Sub

    Private Sub UpdateStatus(status As String)
        If Me.InvokeRequired Then
            Me.Invoke(Sub() UpdateStatus(status))
            Return
        End If

        lblStatus.Text = status
        Application.DoEvents()
    End Sub

    Private Async Sub btnTestBasicSave_Click(sender As Object, e As EventArgs) Handles btnTestBasicSave.Click
        UpdateStatus("Running Basic Save Test...")
        AddTestResult("")
        AddTestResult("=== TEST 1: Basic Save Operation ===")

        Try
            Using context As New dbEPDataDataContext(GetConnectionString())
                ' Test basic save operation
                AddTestResult("Creating test data context...")

                ' Try to save without any changes (should succeed immediately)
                AddTestResult("Attempting basic SaveChanges() call...")
                Dim result = context.SaveChanges()

                If result Then
                    AddTestResult("✅ SUCCESS: Basic SaveChanges() completed successfully")
                Else
                    AddTestResult("❌ FAILED: Basic SaveChanges() returned False")
                End If
            End Using

        Catch ex As Exception
            AddTestResult($"❌ EXCEPTION: {ex.Message}")
            AddTestResult($"Stack Trace: {ex.StackTrace}")
        End Try

        UpdateStatus("Basic Save Test Completed")
    End Sub

    Private Async Sub btnTestConcurrencyConflict_Click(sender As Object, e As EventArgs) Handles btnTestConcurrencyConflict.Click
        UpdateStatus("Running Concurrency Conflict Test...")
        AddTestResult("")
        AddTestResult("=== TEST 2: SaveChanges Method Verification ===")

        Try
            ' This test verifies the SaveChanges method works correctly
            AddTestResult("Testing SaveChanges method implementation...")

            Using context1 As New dbEPDataDataContext(GetConnectionString())
                AddTestResult("Created data context")

                ' Test the SaveChanges method directly
                AddTestResult("Calling SaveChanges() method...")
                Dim result1 = context1.SaveChanges()
                AddTestResult($"SaveChanges result: {result1}")

                ' Test multiple calls
                AddTestResult("Testing multiple SaveChanges calls...")
                For i As Integer = 1 To 3
                    Dim result = context1.SaveChanges()
                    AddTestResult($"SaveChanges call {i} result: {result}")
                Next

                AddTestResult("✅ SUCCESS: SaveChanges method verification completed")
            End Using

        Catch ex As Exception
            AddTestResult($"❌ EXCEPTION: {ex.Message}")
            AddTestResult($"Exception Type: {ex.GetType().Name}")
            If ex.InnerException IsNot Nothing Then
                AddTestResult($"Inner Exception: {ex.InnerException.Message}")
            End If
        End Try

        UpdateStatus("SaveChanges Method Test Completed")
    End Sub

    Private Async Sub btnTestMultipleConflicts_Click(sender As Object, eventArgs As EventArgs) Handles btnTestMultipleConflicts.Click
        UpdateStatus("Running Real Concurrency Conflict Test...")
        AddTestResult("")
        AddTestResult("=== TEST 3: REAL CONCURRENCY CONFLICT TEST ===")

        Try
            ' Find an existing email record to modify (safer than creating new ones)
            Dim testEmailNum As Integer = 0
            Using setupContext As New dbEPDataDataContext(GetConnectionString())
                Dim existingEmail = setupContext.Emails.FirstOrDefault()
                If existingEmail Is Nothing Then
                    AddTestResult("❌ No existing email records found to test with")
                    Return
                End If
                testEmailNum = existingEmail.EmailNum
                AddTestResult($"✅ Using existing email EmailNum: {testEmailNum} for concurrency test")
            End Using

            ' Now create multiple threads that will modify the SAME record simultaneously
            AddTestResult($"🚀 Starting 3 threads to modify EmailNum {testEmailNum} simultaneously...")
            AddTestResult("This will test if SaveChanges handles concurrency conflicts properly...")

            Dim tasks As New List(Of Task(Of String))
            Dim conflictCount As Integer = 0
            Dim successCount As Integer = 0

            For i As Integer = 1 To 3
                Dim taskIndex = i
                Dim capturedEmailNum = testEmailNum ' Capture for closure
                Dim task As Task(Of String) = task.Run(Function() As String
                                                           Try
                                                               Using context As New dbEPDataDataContext(GetConnectionString())
                                                                   ' Load the same record in each thread using EmailNum (primary key)
                                                                   Dim email = context.Emails.Single(Function(e) e.EmailNum = capturedEmailNum)

                                                                   ' Modify it differently in each thread
                                                                   email.Body = $"Modified by Thread {taskIndex} at {DateTime.Now:HH:mm:ss.fff}"
                                                                   email.LastUpdatedBy = $"Thread{taskIndex}"
                                                                   email.LastUpdatedDate = DateTime.Now

                                                                   ' Small delay to increase chance of conflict
                                                                   Threading.Thread.Sleep(50)

                                                                   ' This SHOULD cause concurrency conflicts!
                                                                   Dim result = context.SaveChanges()

                                                                   If result Then
                                                                       Threading.Interlocked.Increment(successCount)
                                                                       Return $"Thread {taskIndex}: ✅ SUCCESS - SaveChanges worked (retry logic handled conflicts)"
                                                                   Else
                                                                       Return $"Thread {taskIndex}: ❌ FAILED - SaveChanges returned False"
                                                                   End If
                                                               End Using
                                                           Catch ex As Exception
                                                               If ex.Message.ToLower().Contains("conflict") OrElse ex.Message.ToLower().Contains("concurrency") Then
                                                                   Threading.Interlocked.Increment(conflictCount)
                                                                   Return $"Thread {taskIndex}: 🔄 CONFLICT DETECTED - {ex.Message} (This is expected!)"
                                                               Else
                                                                   Return $"Thread {taskIndex}: ❌ UNEXPECTED ERROR - {ex.Message}"
                                                               End If
                                                           End Try
                                                       End Function)
                tasks.Add(task)
            Next

            ' Start all threads simultaneously
            AddTestResult("🚀 Starting 3 threads to modify the SAME record simultaneously...")
            AddTestResult("This WILL cause concurrency conflicts - testing if retry logic handles them...")

            Dim results = Await Task.WhenAll(tasks)

            ' Display results
            For Each result In results
                AddTestResult(result)
            Next

            AddTestResult($"📊 FINAL RESULTS: {successCount} successful saves, {conflictCount} conflicts detected")

            If successCount > 0 And conflictCount > 0 Then
                AddTestResult("🎯 PERFECT! Conflicts occurred but retry logic handled them successfully!")
            ElseIf successCount > 0 And conflictCount = 0 Then
                AddTestResult("⚠️ WARNING: No conflicts detected - test may need adjustment")
            Else
                AddTestResult("❌ FAILED: No successful saves completed")
            End If

        Catch ex As Exception
            AddTestResult($"❌ TEST EXCEPTION: {ex.Message}")
        End Try

        UpdateStatus("Real Concurrency Test Completed")
    End Sub

    Private Async Sub btnTestRetryLogic_Click(sender As Object, eventArgs As EventArgs) Handles btnTestRetryLogic.Click
        UpdateStatus("Running Retry Logic Stress Test...")
        AddTestResult("")
        AddTestResult("=== TEST 4: RETRY LOGIC STRESS TEST ===")

        Try
            ' Find an existing email record for stress testing
            Dim testEmailNum As Integer = 0
            Using setupContext As New dbEPDataDataContext(GetConnectionString())
                Dim existingEmail = setupContext.Emails.FirstOrDefault()
                If existingEmail Is Nothing Then
                    AddTestResult("❌ No existing email records found to test with")
                    Return
                End If
                testEmailNum = existingEmail.EmailNum
                AddTestResult($"✅ Using existing email EmailNum: {testEmailNum} for stress test")
            End Using

            ' Launch 10 threads that will ALL try to modify the same record rapidly
            AddTestResult("🔥 Launching 10 threads to HAMMER the same record...")
            AddTestResult("This will force maximum concurrency conflicts and test retry logic!")

            Dim tasks As New List(Of Task(Of String))
            Dim totalRetries As Integer = 0
            Dim successCount As Integer = 0

            For i As Integer = 1 To 10
                Dim threadId = i
                Dim capturedEmailNum = testEmailNum ' Capture for closure
                Dim task As Task(Of String) = task.Run(Function() As String
                                                           Dim retryCount As Integer = 0
                                                           Try
                                                               Using context As New dbEPDataDataContext(GetConnectionString())
                                                                   Dim email = context.Emails.Single(Function(e) e.EmailNum = capturedEmailNum)
                                                                   email.Body = $"HAMMERED by Thread {threadId} at {DateTime.Now:HH:mm:ss.fff}"
                                                                   email.LastUpdatedBy = $"StressThread{threadId}"
                                                                   email.LastUpdatedDate = DateTime.Now

                                                                   ' This will definitely cause conflicts and trigger retries
                                                                   Dim result = context.SaveChanges()

                                                                   If result Then
                                                                       Threading.Interlocked.Increment(successCount)
                                                                       Return $"Thread {threadId}: ✅ SUCCESS after retries"
                                                                   Else
                                                                       Return $"Thread {threadId}: ❌ FAILED even with retries"
                                                                   End If
                                                               End Using
                                                           Catch ex As Exception
                                                               Return $"Thread {threadId}: 💥 EXCEPTION: {ex.Message}"
                                                           End Try
                                                       End Function)
                tasks.Add(task)
            Next

            ' Execute all threads simultaneously
            Dim results = Await Task.WhenAll(tasks)

            ' Show results
            For Each result In results
                AddTestResult(result)
            Next

            AddTestResult($"📊 STRESS TEST RESULTS:")
            AddTestResult($"   • {successCount} threads succeeded")
            AddTestResult($"   • {10 - successCount} threads failed")

            If successCount >= 8 Then
                AddTestResult("🎯 EXCELLENT! Retry logic handled the stress test well!")
            ElseIf successCount >= 5 Then
                AddTestResult("✅ GOOD! Most operations succeeded despite heavy conflicts")
            Else
                AddTestResult("⚠️ CONCERNING: Many operations failed - retry logic may need tuning")
            End If

            AddTestResult("💡 Check application logs for retry attempt details:")
            AddTestResult("   Look for: 'Attempting Entity Framework SaveChanges() on attempt X'")

        Catch ex As Exception
            AddTestResult($"❌ STRESS TEST EXCEPTION: {ex.Message}")
        End Try

        UpdateStatus("Retry Logic Stress Test Completed")
    End Sub

End Class
