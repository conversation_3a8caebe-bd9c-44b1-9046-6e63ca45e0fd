﻿Imports DevExpress.XtraEditors

Public Class frmClientLimits
    Private _isLoading As Boolean = True

    Private _coNum As Decimal?
    Private _AchRiskOrig As Boolean
    Private _DontEnforcePlaidOrig As Boolean
    Private _RiskStatusOrig As String
    Private Property db As dbEPDataDataContext
    Private Property frmLogger As Serilog.ILogger

    Sub New()
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmClientLimits)()
    End Sub

    Sub New(coNum As Decimal)
        InitializeComponent()
        frmLogger = Logger.ForContext(Of frmClientLimits)()
        _coNum = coNum
    End Sub

    Async Sub LoadAchTran(CoNum As Decimal)
        Try
            Dim Q = From A In db.AchTransactionsLogs Where Not A.IsDeleted AndAlso A.CoNum = CoNum

            Me.GridControlAchTran.DataSource = Await Threading.Tasks.Task.Run(Function() Q.ToList)

            If GridViewAchTran.SortInfo.Count = 0 Then
                If Me.GridViewAchTran.Columns("ID") IsNot Nothing AndAlso Me.GridViewAchTran.Columns("ID").Visible Then
                    Me.GridViewAchTran.Columns("ID").Visible = False
                End If

                GridViewAchTran.SortInfo.Add(New DevExpress.XtraGrid.Columns.GridColumnSortInfo(GridViewAchTran.Columns("DateReceived"), DevExpress.Data.ColumnSortOrder.Descending))

                Me.GridViewAchTran.Columns("Status").VisibleIndex = 4

                Me.GridViewAchTran.Columns("DateReceived").BestFit()
                Me.GridViewAchTran.Columns("CoNum").BestFit()
                Me.GridViewAchTran.Columns("EntryDescType").BestFit()
                Me.GridViewAchTran.Columns("RejectionCode").BestFit()
                Me.GridViewAchTran.Columns("Status").BestFit()
                Me.GridViewAchTran.Columns("DebitAmount").BestFit()
                Me.GridViewAchTran.Columns("CreditAmount").BestFit()
                GridViewAchTran.ActiveFilterString = "[EmpNum] Is Null And [DebitAmount] Is Not Null"
                GridViewAchTran.FocusedRowHandle = 0
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading data", ex)
        End Try
    End Sub

    Private Sub frmClientLimits_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            db = New dbEPDataDataContext(GetConnectionString)
            BindingSource1.DataSource = db.view_CompanySumarries.ToList()
            lueRiskStatus.Properties.DataSource = GetUdfValueSplitted("RiskStatusOptions")
            If _coNum.HasValue Then
                sluCoNum.EditValue = _coNum
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error in frmClientLimits_Load", ex)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click, btnSaveAndExit.Click
        Try
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you would like to update settings?", "Update settings?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Return
            End If

            Dim sql = $"UPDATE custom.CoOptions_Payroll SET WHERE CoNum = {_coNum}"

            If _coNum IsNot Nothing Then
                Dim cop = Query(Of CoOptions_Payroll)($"SELECT * FROM custom.CoOptions_Payroll cop WHERE cop.CoNum = {_coNum}").FirstOrDefault()
                If teLimit.EditValue IsNot Nothing Then
                    frmLogger.Information("Setting Co#: {CoNum} OrigLimit: {OrigLimit} to NewLimit: {Limit}", _coNum, teOldLimit.EditValue, teLimit.EditValue)
                    'cop.DdPerPrLimit = teLimit.EditValue
                    sql = sql.Replace(" WHERE ", $" DdPerPrLimit = {teLimit.EditValue} WHERE ")
                End If

                If SpinEditMinSubmissionDays.EditValue <> cop.MinSubmissionDays Then
                    'cop.RiskStatus = lueRiskStatus.EditValue
                    sql = sql.Replace(" WHERE ", $"{If(sql.Contains("SET WHERE"), "", ",")} MinSubmissionDays = {SpinEditMinSubmissionDays.EditValue} WHERE ")
                End If

                If tglAchRisk.EditValue <> _AchRiskOrig OrElse lueRiskStatus.EditValue <> _RiskStatusOrig OrElse tglDontEnforcePlaid.EditValue <> _DontEnforcePlaidOrig Then
                    If lueRiskStatus.EditValue <> _RiskStatusOrig Then
                        'cop.RiskStatus = lueRiskStatus.EditValue
                        sql = sql.Replace(" WHERE ", $"{If(sql.Contains("SET WHERE"), "", ",")} RiskStatus = '{lueRiskStatus.EditValue}' WHERE ")
                    End If

                    If tglAchRisk.EditValue <> _AchRiskOrig Then
                        'cop.AchRisk = tglAchRisk.EditValue
                        sql = sql.Replace(" WHERE ", $"{If(sql.Contains("SET WHERE"), "", ",")} AchRisk = {If(tglAchRisk.EditValue, 1, 0)} WHERE ")
                        sql += $" exec custom.prc_FDCoNumMultiAction 'Add User Log', {_coNum}, 'NSF / Billing Logs', 'ACH Risk was {IIf(tglAchRisk.EditValue, "added", "removed")}'"
                    End If

                    If tglDontEnforcePlaid.EditValue <> _DontEnforcePlaidOrig Then
                        sql = sql.Replace(" WHERE ", $"{If(sql.Contains("SET WHERE"), "", ",")} DontEnforcePlaid = {If(tglDontEnforcePlaid.EditValue, 1, 0)} WHERE ")
                    End If
                End If

                If Not sql.Contains("SET WHERE") Then
                    db.ExecuteCommand(sql)
                End If

                If db.SaveChanges Then sluCoNum.EditValue = Nothing
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error updating company limits", ex)
        End Try
        If sender.Equals(btnSaveAndExit) Then Close()
    End Sub

    Private Sub sluCoNum_EditValueChanged(sender As Object, e As EventArgs) Handles sluCoNum.EditValueChanged
        Try
            _isLoading = True
            _coNum = If(sluCoNum.EditValue = Nothing OrElse sluCoNum.EditValue.ToString() = "", Nothing, Decimal.Parse(sluCoNum.EditValue))
            teLimit.EditValue = Nothing
            If sluCoNum.EditValue IsNot Nothing AndAlso sluCoNum.EditValue.ToString().IsNotNullOrWhiteSpace AndAlso sluCoNum.Text.IsNotNullOrWhiteSpace Then
                frmLogger.Debug("Loading Co#: {CoNum} Limit: {Limit}", _coNum, teOldLimit.EditValue)
                Dim cop = Query(Of CoOptions_Payroll)($"SELECT * FROM custom.CoOptions_Payroll cop WHERE cop.CoNum = {_coNum}").FirstOrDefault()
                teOldLimit.EditValue = cop.DdPerPrLimit
                LoadAchTran(_coNum)
                _AchRiskOrig = cop.AchRisk
                _DontEnforcePlaidOrig = cop.DontEnforcePlaid
                _RiskStatusOrig = cop.RiskStatus
                lueRiskStatus.EditValue = _RiskStatusOrig
                tglAchRisk.EditValue = _AchRiskOrig
                tglDontEnforcePlaid.EditValue = _DontEnforcePlaidOrig
                SpinEditMinSubmissionDays.EditValue = cop.MinSubmissionDays
            Else
                _AchRiskOrig = False
                _RiskStatusOrig = ""
                _DontEnforcePlaidOrig = False
                _coNum = Nothing
                teOldLimit.EditValue = Nothing
                tglAchRisk.EditValue = False
                tglDontEnforcePlaid.EditValue = False
                lueRiskStatus.EditValue = ""
                GridControlAchTran.DataSource = Nothing
                SpinEditMinSubmissionDays.EditValue = 2
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error loading company limits.", ex)
        Finally
            _isLoading = False
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub tglAchRisk_EditValueChanging(sender As Object, e As Controls.ChangingEventArgs) Handles tglAchRisk.EditValueChanging
        If e.NewValue <> _AchRiskOrig AndAlso XtraMessageBox.Show($"Are you sure you want To change ACA Risk status To {IIf(e.NewValue, "ON", "OFF")}?", "Change risk Status?", MessageBoxButtons.YesNo, MessageBoxIcon.Information) <> DialogResult.Yes Then
            e.Cancel = True
        End If
    End Sub

    Private Sub tglDontEnforcePlaid_EditValueChanging(sender As Object, e As Controls.ChangingEventArgs) Handles tglDontEnforcePlaid.EditValueChanging
        If e.NewValue <> _DontEnforcePlaidOrig AndAlso XtraMessageBox.Show($"Are you sure you want To change DontEnforcePlaid status To {IIf(e.NewValue, "ON", "OFF")}?", "Change Dont Enforce Plaid Status?", MessageBoxButtons.YesNo, MessageBoxIcon.Information) <> DialogResult.Yes Then
            e.Cancel = True
        End If
    End Sub


    Private Sub tglAchRisk_Toggled(sender As Object, e As EventArgs) Handles tglAchRisk.Toggled
        If tglAchRisk.EditValue = True Then
            teLimit.Enabled = False
            If sluCoNum.OldEditValue IsNot Nothing AndAlso sluCoNum.OldEditValue.ToString() <> "" AndAlso tglAchRisk.OldEditValue <> tglAchRisk.EditValue AndAlso sluCoNum.EditValue = sluCoNum.OldEditValue Then
                teLimit.EditValue = 0
            End If
        Else
            teLimit.Enabled = True
            teLimit.EditValue = Nothing
        End If
    End Sub
End Class