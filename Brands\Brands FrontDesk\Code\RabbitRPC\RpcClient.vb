﻿Imports System.Collections.Concurrent
Imports System.Text
Imports RabbitMQ.Client
Imports RabbitMQ.Client.Events

Namespace RabbitRpc
    Public Class RpcClient
        Private ReadOnly connection As IConnection
        Private ReadOnly channel As IModel
        Private ReadOnly replyQueueName As String
        Private ReadOnly consumer As EventingBasicConsumer
        Private ReadOnly respQueue As BlockingCollection(Of String) = New BlockingCollection(Of String)()
        Private ReadOnly props As IBasicProperties

        Public Sub New()
            Dim factory = New ConnectionFactory() With {
                .HostName = "appserver",
                .Port = 5672,
                .UserName = "WebUser",
                .Password = "TSvkJ5HC53ONFHmw"
            }
            connection = factory.CreateConnection()
            channel = connection.CreateModel()
            replyQueueName = channel.QueueDeclare().QueueName
            consumer = New EventingBasicConsumer(channel)
            props = channel.CreateBasicProperties()
            Dim correlationId = Guid.NewGuid().ToString()
            props.CorrelationId = correlationId
            props.ReplyTo = replyQueueName
            AddHandler consumer.Received, AddressOf ConsumerReceived

            channel.BasicConsume(consumer:=consumer, queue:=replyQueueName, autoAck:=True)
        End Sub

        Private Sub ConsumerReceived(sender As Object, e As BasicDeliverEventArgs)
            Dim body = e.Body.ToArray()
            Dim response = Encoding.UTF8.GetString(body)

            If e.BasicProperties.CorrelationId = props.CorrelationId Then
                respQueue.Add(response)
            End If
        End Sub

        Public Function [Call](ByVal message As String) As String
            Dim messageBytes = Encoding.UTF8.GetBytes(message)
            channel.BasicPublish(exchange:="", routingKey:="rpc_queue", basicProperties:=props, body:=messageBytes)
            Dim strOut = ""
            Dim resp = respQueue.TryTake(strOut, New TimeSpan(0, 10, 3))
            Return strOut
        End Function

        Public Sub Close()
            connection.Close()
        End Sub
    End Class
End Namespace
