﻿Imports System.ComponentModel
Imports Brands_FrontDesk
Imports DevExpress.XtraEditors

Public Class frmEmpIncentive
    Sub New()

        InitializeComponent()
    End Sub

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property _db As dbEPDataDataContext

    Private Sub frmEmpIntensive_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadData()
    End Sub

    Private Sub LoadData()
        Try
            lcRoot.ShowProgessPanel
            _db = New dbEPDataDataContext(GetConnectionString)
            empIntensivesBindingSource.DataSource = _db.EmpIncentives.Where(Function(e) (bciUnpaidOnly.Checked AndAlso Not e.Paid) OrElse Not bciUnpaidOnly.Checked).ToList()
            ViewCompanySumarryBindingSource.DataSource = _db.view_CompanySumarries.ToList()
            riCbeUsers.Items.AddRange(_db.EmpIncentives.Select(Function(i) i.ByUser).Distinct().ToArray)
            riCbeItems.Items.AddRange(_db.EmpIncentives.Select(Function(i) i.Item).Distinct().ToArray)
        Catch ex As Exception
            DisplayErrorMessage("Error loading data.", ex)
        Finally
            lcRoot.HideProgressPanel
        End Try
    End Sub

    Private Sub GridView1_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridView1.RowUpdated
        Dim row As EmpIncentive = e.Row
        If row.Id = 0 Then
            _db.EmpIncentives.InsertOnSubmit(row)
        End If
        _db.SaveChanges
    End Sub

    Private Sub GridView1_PopupMenuShowing(sender As Object, e As DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs) Handles GridView1.PopupMenuShowing
        If e.HitInfo.InRow Then
            Dim row As EmpIncentive = GridView1.GetRow(e.HitInfo.RowHandle)
            e.Menu.Items.Add(New DevExpress.Utils.Menu.DXMenuItem("Delete", Sub()
                                                                                DeleteRow(row)
                                                                            End Sub, My.Resources.delete_16x16))
        End If
    End Sub

    Private Sub DeleteRow(row As EmpIncentive)
        Try
            If XtraMessageBox.Show("Are you sure you would like to delete this row?", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                _db.EmpIncentives.DeleteOnSubmit(row)
                _db.SaveChanges
                LoadData()
            End If
        Catch ex As Exception
            DisplayErrorMessage("Error deleting row", ex)
        End Try
    End Sub

    Private Sub RepositoryItemSearchLookUpEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles riSlueCoNum.EditValueChanged
        Dim edit As SearchLookUpEdit = sender
        If edit.EditValue IsNot DBNull.Value AndAlso edit.EditValue IsNot Nothing AndAlso edit.EditValue <> 0 Then
            Dim conum As Decimal = edit.EditValue
            Dim name = _db.view_CompanySumarries.Single(Function(c) c.CONUM = conum).CO_NAME
            GridView1.SetFocusedRowCellValue(colCoName, name)
            'Dim row As EmpIntensive = GridView1.GetRow(GridView1.FocusedRowHandle)
            'row.CoName = name
            GridView1.PostEditor()
            GridView1.RefreshRow(GridView1.FocusedRowHandle)
            GridView1.FocusedColumn = colDateContacted
        End If
    End Sub

    Private Sub bbiRefresh_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bbiRefresh.ItemClick
        LoadData()
    End Sub

    Private Sub bciUnpaidOnly_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bciUnpaidOnly.ItemClick
        LoadData()
    End Sub
End Class