﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ucMostRecentPayroll
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.lcRoot = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.teTransit = New DevExpress.XtraEditors.TextEdit()
        Me.teLastEntryType = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.tePickupBy = New DevExpress.XtraEditors.TextEdit()
        Me.tePickup = New DevExpress.XtraEditors.TextEdit()
        Me.teFrequency = New DevExpress.XtraEditors.TextEdit()
        Me.DataNavigator1 = New DevExpress.XtraEditors.DataNavigator()
        Me.TotalChecksTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PAYROLL_STATUSTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.ENTRY_TYPETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PR_DESCRTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.OPNAMETextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.PRNUMSpinEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CHECK_DATEDateEdit = New DevExpress.XtraEditors.TextEdit()
        Me.START_TIMEDateEdit = New DevExpress.XtraEditors.TextEdit()
        Me.print_dateDateEdit = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForPRNUM = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForSTART_TIME = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNUM_CHECKS = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForCHECK_DATE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForPAYROLL_STATUS = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForOPNAME = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForENTRY_TYPE = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForPR_DESCR = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForprint_date = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lciLastEntryType = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.PAYROLLBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.lcRoot.SuspendLayout()
        CType(Me.teTransit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teLastEntryType.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tePickupBy.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tePickup.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.teFrequency.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TotalChecksTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PAYROLL_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ENTRY_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PR_DESCRTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.OPNAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PRNUMSpinEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CHECK_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.START_TIMEDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.print_dateDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPRNUM, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForSTART_TIME, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNUM_CHECKS, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForCHECK_DATE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPAYROLL_STATUS, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForOPNAME, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForENTRY_TYPE, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForPR_DESCR, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForprint_date, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lciLastEntryType, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PAYROLLBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lcRoot
        '
        Me.lcRoot.Controls.Add(Me.teTransit)
        Me.lcRoot.Controls.Add(Me.teLastEntryType)
        Me.lcRoot.Controls.Add(Me.TextEdit2)
        Me.lcRoot.Controls.Add(Me.TextEdit1)
        Me.lcRoot.Controls.Add(Me.tePickupBy)
        Me.lcRoot.Controls.Add(Me.tePickup)
        Me.lcRoot.Controls.Add(Me.teFrequency)
        Me.lcRoot.Controls.Add(Me.DataNavigator1)
        Me.lcRoot.Controls.Add(Me.TotalChecksTextEdit)
        Me.lcRoot.Controls.Add(Me.PAYROLL_STATUSTextEdit)
        Me.lcRoot.Controls.Add(Me.ENTRY_TYPETextEdit)
        Me.lcRoot.Controls.Add(Me.PR_DESCRTextEdit)
        Me.lcRoot.Controls.Add(Me.OPNAMETextEdit)
        Me.lcRoot.Controls.Add(Me.PRNUMSpinEdit)
        Me.lcRoot.Controls.Add(Me.CHECK_DATEDateEdit)
        Me.lcRoot.Controls.Add(Me.START_TIMEDateEdit)
        Me.lcRoot.Controls.Add(Me.print_dateDateEdit)
        Me.lcRoot.DataSource = Me.PAYROLLBindingSource
        Me.lcRoot.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lcRoot.Location = New System.Drawing.Point(0, 0)
        Me.lcRoot.Name = "lcRoot"
        Me.lcRoot.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = New System.Drawing.Rectangle(644, 278, 250, 350)
        Me.lcRoot.OptionsView.IsReadOnly = DevExpress.Utils.DefaultBoolean.[True]
        Me.lcRoot.Root = Me.LayoutControlGroup1
        Me.lcRoot.Size = New System.Drawing.Size(619, 257)
        Me.lcRoot.TabIndex = 0
        Me.lcRoot.Text = "DataLayoutControl1"
        '
        'teTransit
        '
        Me.teTransit.Location = New System.Drawing.Point(79, 26)
        Me.teTransit.Name = "teTransit"
        Me.teTransit.Properties.ReadOnly = True
        Me.teTransit.Size = New System.Drawing.Size(533, 20)
        Me.teTransit.StyleController = Me.lcRoot
        Me.teTransit.TabIndex = 21
        '
        'teLastEntryType
        '
        Me.teLastEntryType.Location = New System.Drawing.Point(79, 2)
        Me.teLastEntryType.Name = "teLastEntryType"
        Me.teLastEntryType.Properties.Appearance.Font = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Bold)
        Me.teLastEntryType.Properties.Appearance.Options.UseFont = True
        Me.teLastEntryType.Properties.Appearance.Options.UseTextOptions = True
        Me.teLastEntryType.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.teLastEntryType.Properties.ReadOnly = True
        Me.teLastEntryType.Size = New System.Drawing.Size(533, 20)
        Me.teLastEntryType.StyleController = Me.lcRoot
        Me.teLastEntryType.TabIndex = 20
        '
        'TextEdit2
        '
        Me.TextEdit2.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "PER2_END_DATE", True))
        Me.TextEdit2.Location = New System.Drawing.Point(383, 194)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.DisplayFormat.FormatString = "d"
        Me.TextEdit2.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.TextEdit2.Properties.ReadOnly = True
        Me.TextEdit2.Size = New System.Drawing.Size(229, 20)
        Me.TextEdit2.StyleController = Me.lcRoot
        Me.TextEdit2.TabIndex = 19
        '
        'TextEdit1
        '
        Me.TextEdit1.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "PER1_END_DATE", True))
        Me.TextEdit1.Location = New System.Drawing.Point(79, 194)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.DisplayFormat.FormatString = "d"
        Me.TextEdit1.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.TextEdit1.Properties.ReadOnly = True
        Me.TextEdit1.Size = New System.Drawing.Size(228, 20)
        Me.TextEdit1.StyleController = Me.lcRoot
        Me.TextEdit1.TabIndex = 18
        '
        'tePickupBy
        '
        Me.tePickupBy.Location = New System.Drawing.Point(79, 170)
        Me.tePickupBy.Name = "tePickupBy"
        Me.tePickupBy.Properties.ReadOnly = True
        Me.tePickupBy.Size = New System.Drawing.Size(228, 20)
        Me.tePickupBy.StyleController = Me.lcRoot
        Me.tePickupBy.TabIndex = 17
        '
        'tePickup
        '
        Me.tePickup.Location = New System.Drawing.Point(79, 146)
        Me.tePickup.Name = "tePickup"
        Me.tePickup.Properties.ReadOnly = True
        Me.tePickup.Size = New System.Drawing.Size(228, 20)
        Me.tePickup.StyleController = Me.lcRoot
        Me.tePickup.TabIndex = 16
        '
        'teFrequency
        '
        Me.teFrequency.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "Frequency", True))
        Me.teFrequency.Location = New System.Drawing.Point(383, 146)
        Me.teFrequency.Name = "teFrequency"
        Me.teFrequency.Properties.ReadOnly = True
        Me.teFrequency.Size = New System.Drawing.Size(229, 20)
        Me.teFrequency.StyleController = Me.lcRoot
        Me.teFrequency.TabIndex = 15
        '
        'DataNavigator1
        '
        Me.DataNavigator1.Buttons.Append.Visible = False
        Me.DataNavigator1.Buttons.CancelEdit.Visible = False
        Me.DataNavigator1.Buttons.EndEdit.Visible = False
        Me.DataNavigator1.Buttons.Remove.Visible = False
        Me.DataNavigator1.DataSource = Me.PAYROLLBindingSource
        Me.DataNavigator1.Location = New System.Drawing.Point(7, 218)
        Me.DataNavigator1.Name = "DataNavigator1"
        Me.DataNavigator1.Size = New System.Drawing.Size(241, 19)
        Me.DataNavigator1.StyleController = Me.lcRoot
        Me.DataNavigator1.TabIndex = 12
        Me.DataNavigator1.Text = "DataNavigator1"
        Me.DataNavigator1.TextLocation = DevExpress.XtraEditors.NavigatorButtonsTextLocation.Begin
        '
        'TotalChecksTextEdit
        '
        Me.TotalChecksTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "TotalChecks", True))
        Me.TotalChecksTextEdit.Location = New System.Drawing.Point(383, 170)
        Me.TotalChecksTextEdit.Name = "TotalChecksTextEdit"
        Me.TotalChecksTextEdit.Properties.ReadOnly = True
        Me.TotalChecksTextEdit.Size = New System.Drawing.Size(229, 20)
        Me.TotalChecksTextEdit.StyleController = Me.lcRoot
        Me.TotalChecksTextEdit.TabIndex = 8
        '
        'PAYROLL_STATUSTextEdit
        '
        Me.PAYROLL_STATUSTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "PAYROLL_STATUS", True))
        Me.PAYROLL_STATUSTextEdit.Location = New System.Drawing.Point(383, 50)
        Me.PAYROLL_STATUSTextEdit.Name = "PAYROLL_STATUSTextEdit"
        Me.PAYROLL_STATUSTextEdit.Properties.ReadOnly = True
        Me.PAYROLL_STATUSTextEdit.Size = New System.Drawing.Size(229, 20)
        Me.PAYROLL_STATUSTextEdit.StyleController = Me.lcRoot
        Me.PAYROLL_STATUSTextEdit.TabIndex = 9
        '
        'ENTRY_TYPETextEdit
        '
        Me.ENTRY_TYPETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "ENTRY_TYPE", True))
        Me.ENTRY_TYPETextEdit.Location = New System.Drawing.Point(383, 98)
        Me.ENTRY_TYPETextEdit.Name = "ENTRY_TYPETextEdit"
        Me.ENTRY_TYPETextEdit.Properties.ReadOnly = True
        Me.ENTRY_TYPETextEdit.Size = New System.Drawing.Size(229, 20)
        Me.ENTRY_TYPETextEdit.StyleController = Me.lcRoot
        Me.ENTRY_TYPETextEdit.TabIndex = 10
        '
        'PR_DESCRTextEdit
        '
        Me.PR_DESCRTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "PR_DESCR", True))
        Me.PR_DESCRTextEdit.Location = New System.Drawing.Point(383, 122)
        Me.PR_DESCRTextEdit.Name = "PR_DESCRTextEdit"
        Me.PR_DESCRTextEdit.Properties.ReadOnly = True
        Me.PR_DESCRTextEdit.Size = New System.Drawing.Size(229, 20)
        Me.PR_DESCRTextEdit.StyleController = Me.lcRoot
        Me.PR_DESCRTextEdit.TabIndex = 11
        '
        'OPNAMETextEdit
        '
        Me.OPNAMETextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "OPNAME", True))
        Me.OPNAMETextEdit.Location = New System.Drawing.Point(383, 74)
        Me.OPNAMETextEdit.Name = "OPNAMETextEdit"
        Me.OPNAMETextEdit.Properties.ReadOnly = True
        Me.OPNAMETextEdit.Size = New System.Drawing.Size(229, 20)
        Me.OPNAMETextEdit.StyleController = Me.lcRoot
        Me.OPNAMETextEdit.TabIndex = 14
        '
        'PRNUMSpinEdit
        '
        Me.PRNUMSpinEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "PRNUM", True))
        Me.PRNUMSpinEdit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.PRNUMSpinEdit.Location = New System.Drawing.Point(79, 50)
        Me.PRNUMSpinEdit.Name = "PRNUMSpinEdit"
        Me.PRNUMSpinEdit.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered
        Me.PRNUMSpinEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.PRNUMSpinEdit.Properties.ReadOnly = True
        Me.PRNUMSpinEdit.Size = New System.Drawing.Size(228, 20)
        Me.PRNUMSpinEdit.StyleController = Me.lcRoot
        Me.PRNUMSpinEdit.TabIndex = 4
        '
        'CHECK_DATEDateEdit
        '
        Me.CHECK_DATEDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "CHECK_DATE", True))
        Me.CHECK_DATEDateEdit.Location = New System.Drawing.Point(79, 74)
        Me.CHECK_DATEDateEdit.Name = "CHECK_DATEDateEdit"
        Me.CHECK_DATEDateEdit.Properties.DisplayFormat.FormatString = "d"
        Me.CHECK_DATEDateEdit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.CHECK_DATEDateEdit.Properties.EditFormat.FormatString = "d"
        Me.CHECK_DATEDateEdit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.CHECK_DATEDateEdit.Properties.Mask.EditMask = "d"
        Me.CHECK_DATEDateEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.DateTime
        Me.CHECK_DATEDateEdit.Properties.ReadOnly = True
        Me.CHECK_DATEDateEdit.Size = New System.Drawing.Size(228, 20)
        Me.CHECK_DATEDateEdit.StyleController = Me.lcRoot
        Me.CHECK_DATEDateEdit.TabIndex = 5
        '
        'START_TIMEDateEdit
        '
        Me.START_TIMEDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "START_TIME", True))
        Me.START_TIMEDateEdit.Location = New System.Drawing.Point(79, 98)
        Me.START_TIMEDateEdit.Name = "START_TIMEDateEdit"
        Me.START_TIMEDateEdit.Properties.DisplayFormat.FormatString = "g"
        Me.START_TIMEDateEdit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.START_TIMEDateEdit.Properties.EditFormat.FormatString = "d"
        Me.START_TIMEDateEdit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.START_TIMEDateEdit.Properties.Mask.EditMask = "d"
        Me.START_TIMEDateEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.DateTime
        Me.START_TIMEDateEdit.Properties.ReadOnly = True
        Me.START_TIMEDateEdit.Size = New System.Drawing.Size(228, 20)
        Me.START_TIMEDateEdit.StyleController = Me.lcRoot
        Me.START_TIMEDateEdit.TabIndex = 6
        '
        'print_dateDateEdit
        '
        Me.print_dateDateEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.PAYROLLBindingSource, "print_date", True))
        Me.print_dateDateEdit.Location = New System.Drawing.Point(79, 122)
        Me.print_dateDateEdit.Name = "print_dateDateEdit"
        Me.print_dateDateEdit.Properties.DisplayFormat.FormatString = "g"
        Me.print_dateDateEdit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.print_dateDateEdit.Properties.EditFormat.FormatString = "d"
        Me.print_dateDateEdit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.print_dateDateEdit.Properties.Mask.EditMask = "d"
        Me.print_dateDateEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.DateTime
        Me.print_dateDateEdit.Properties.ReadOnly = True
        Me.print_dateDateEdit.Size = New System.Drawing.Size(228, 20)
        Me.print_dateDateEdit.StyleController = Me.lcRoot
        Me.print_dateDateEdit.TabIndex = 13
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup2})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Padding = New DevExpress.XtraLayout.Utils.Padding(5, 5, 0, 0)
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(619, 257)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.AllowDrawBackground = False
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForPRNUM, Me.ItemForSTART_TIME, Me.ItemForNUM_CHECKS, Me.LayoutControlItem1, Me.ItemForCHECK_DATE, Me.ItemForPAYROLL_STATUS, Me.ItemForOPNAME, Me.ItemForENTRY_TYPE, Me.ItemForPR_DESCR, Me.ItemForprint_date, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.lciLastEntryType, Me.LayoutControlItem7})
        Me.LayoutControlGroup2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup2.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(609, 257)
        '
        'ItemForPRNUM
        '
        Me.ItemForPRNUM.Control = Me.PRNUMSpinEdit
        Me.ItemForPRNUM.Location = New System.Drawing.Point(0, 48)
        Me.ItemForPRNUM.Name = "ItemForPRNUM"
        Me.ItemForPRNUM.Size = New System.Drawing.Size(304, 24)
        Me.ItemForPRNUM.Text = "Payroll #"
        Me.ItemForPRNUM.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForSTART_TIME
        '
        Me.ItemForSTART_TIME.Control = Me.START_TIMEDateEdit
        Me.ItemForSTART_TIME.Location = New System.Drawing.Point(0, 96)
        Me.ItemForSTART_TIME.Name = "ItemForSTART_TIME"
        Me.ItemForSTART_TIME.Size = New System.Drawing.Size(304, 24)
        Me.ItemForSTART_TIME.Text = "Start Time"
        Me.ItemForSTART_TIME.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForNUM_CHECKS
        '
        Me.ItemForNUM_CHECKS.Control = Me.TotalChecksTextEdit
        Me.ItemForNUM_CHECKS.Location = New System.Drawing.Point(304, 168)
        Me.ItemForNUM_CHECKS.Name = "ItemForNUM_CHECKS"
        Me.ItemForNUM_CHECKS.Size = New System.Drawing.Size(305, 24)
        Me.ItemForNUM_CHECKS.Text = "# of checks"
        Me.ItemForNUM_CHECKS.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.DataNavigator1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 216)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(609, 41)
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(0, 0)
        Me.LayoutControlItem1.TextVisible = False
        '
        'ItemForCHECK_DATE
        '
        Me.ItemForCHECK_DATE.Control = Me.CHECK_DATEDateEdit
        Me.ItemForCHECK_DATE.Location = New System.Drawing.Point(0, 72)
        Me.ItemForCHECK_DATE.Name = "ItemForCHECK_DATE"
        Me.ItemForCHECK_DATE.Size = New System.Drawing.Size(304, 24)
        Me.ItemForCHECK_DATE.Text = "Check Date"
        Me.ItemForCHECK_DATE.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForPAYROLL_STATUS
        '
        Me.ItemForPAYROLL_STATUS.Control = Me.PAYROLL_STATUSTextEdit
        Me.ItemForPAYROLL_STATUS.Location = New System.Drawing.Point(304, 48)
        Me.ItemForPAYROLL_STATUS.Name = "ItemForPAYROLL_STATUS"
        Me.ItemForPAYROLL_STATUS.Size = New System.Drawing.Size(305, 24)
        Me.ItemForPAYROLL_STATUS.Text = "Payroll Status"
        Me.ItemForPAYROLL_STATUS.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForOPNAME
        '
        Me.ItemForOPNAME.Control = Me.OPNAMETextEdit
        Me.ItemForOPNAME.Location = New System.Drawing.Point(304, 72)
        Me.ItemForOPNAME.Name = "ItemForOPNAME"
        Me.ItemForOPNAME.Size = New System.Drawing.Size(305, 24)
        Me.ItemForOPNAME.Text = "Done By"
        Me.ItemForOPNAME.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForENTRY_TYPE
        '
        Me.ItemForENTRY_TYPE.Control = Me.ENTRY_TYPETextEdit
        Me.ItemForENTRY_TYPE.Location = New System.Drawing.Point(304, 96)
        Me.ItemForENTRY_TYPE.Name = "ItemForENTRY_TYPE"
        Me.ItemForENTRY_TYPE.Size = New System.Drawing.Size(305, 24)
        Me.ItemForENTRY_TYPE.Text = "Entry Type"
        Me.ItemForENTRY_TYPE.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForPR_DESCR
        '
        Me.ItemForPR_DESCR.Control = Me.PR_DESCRTextEdit
        Me.ItemForPR_DESCR.Location = New System.Drawing.Point(304, 120)
        Me.ItemForPR_DESCR.Name = "ItemForPR_DESCR"
        Me.ItemForPR_DESCR.Size = New System.Drawing.Size(305, 24)
        Me.ItemForPR_DESCR.Text = "Payroll Desc"
        Me.ItemForPR_DESCR.TextSize = New System.Drawing.Size(69, 13)
        '
        'ItemForprint_date
        '
        Me.ItemForprint_date.Control = Me.print_dateDateEdit
        Me.ItemForprint_date.Location = New System.Drawing.Point(0, 120)
        Me.ItemForprint_date.Name = "ItemForprint_date"
        Me.ItemForprint_date.Size = New System.Drawing.Size(304, 24)
        Me.ItemForprint_date.Text = "Print Time"
        Me.ItemForprint_date.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.teFrequency
        Me.LayoutControlItem2.Location = New System.Drawing.Point(304, 144)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(305, 24)
        Me.LayoutControlItem2.Text = "Frequency"
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.tePickup
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 144)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(304, 24)
        Me.LayoutControlItem3.Text = "Pickup"
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.tePickupBy
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 168)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(304, 24)
        Me.LayoutControlItem4.Text = "Pickup By"
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.TextEdit1
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 192)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(304, 24)
        Me.LayoutControlItem5.Text = "Prd1 End Date"
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.TextEdit2
        Me.LayoutControlItem6.Location = New System.Drawing.Point(304, 192)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(305, 24)
        Me.LayoutControlItem6.Text = "Prd2 End Date"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(69, 13)
        '
        'lciLastEntryType
        '
        Me.lciLastEntryType.Control = Me.teLastEntryType
        Me.lciLastEntryType.Location = New System.Drawing.Point(0, 0)
        Me.lciLastEntryType.Name = "lciLastEntryType"
        Me.lciLastEntryType.Size = New System.Drawing.Size(609, 24)
        Me.lciLastEntryType.Text = "Online: "
        Me.lciLastEntryType.TextSize = New System.Drawing.Size(69, 13)
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.teTransit
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 24)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(609, 24)
        Me.LayoutControlItem7.Text = "Transit: "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(69, 13)
        '
        'PAYROLLBindingSource
        '
        Me.PAYROLLBindingSource.DataSource = GetType(Brands_FrontDesk.PAYROLL)
        '
        'ucMostRecentPayroll
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.lcRoot)
        Me.Name = "ucMostRecentPayroll"
        Me.Size = New System.Drawing.Size(619, 257)
        CType(Me.lcRoot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.lcRoot.ResumeLayout(False)
        CType(Me.teTransit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teLastEntryType.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tePickupBy.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tePickup.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.teFrequency.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TotalChecksTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PAYROLL_STATUSTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ENTRY_TYPETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PR_DESCRTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.OPNAMETextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PRNUMSpinEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CHECK_DATEDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.START_TIMEDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.print_dateDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPRNUM, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForSTART_TIME, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNUM_CHECKS, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForCHECK_DATE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPAYROLL_STATUS, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForOPNAME, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForENTRY_TYPE, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForPR_DESCR, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForprint_date, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lciLastEntryType, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PAYROLLBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PAYROLLBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents lcRoot As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents DataNavigator1 As DevExpress.XtraEditors.DataNavigator
    Friend WithEvents TotalChecksTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PAYROLL_STATUSTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ENTRY_TYPETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PR_DESCRTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForPRNUM As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForCHECK_DATE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForSTART_TIME As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNUM_CHECKS As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForPAYROLL_STATUS As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForENTRY_TYPE As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForPR_DESCR As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForprint_date As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents OPNAMETextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ItemForOPNAME As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teFrequency As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents tePickupBy As DevExpress.XtraEditors.TextEdit
    Friend WithEvents tePickup As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents PRNUMSpinEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CHECK_DATEDateEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents START_TIMEDateEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents print_dateDateEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teLastEntryType As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lciLastEntryType As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents teTransit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
End Class
