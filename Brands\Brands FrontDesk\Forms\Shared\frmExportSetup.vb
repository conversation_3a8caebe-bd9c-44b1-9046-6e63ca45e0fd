﻿Imports System.ComponentModel
Imports System.Data
Imports DevExpress.XtraEditors
Public Class frmExportSetup

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property IsNewRecord As Boolean
    'Private dtYesNo As DataTable

    'Friend WithEvents dsBrands As dsBrands

    Dim CoList As List(Of view_CompanySumarry)
    Dim DB As dbEPDataDataContext

    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Property CoOptionsEnt As CoOptions_Export
    <DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)>
    Public Property DefaultEmailSubjectName As String
    Dim ExportFormats As List(Of ExportFormat)

    Dim FileCategories As List(Of FileCategory)

    Private UpdateFormatNotes As Boolean
    Private CoOptionsID As Integer?
    Private ReportLayoutID As Integer?
    Private CoNum As Decimal
    Private IsReportSchedule As Boolean
    Private _IsLoaded As Boolean

    Private _setupID As Integer?, _CoNum As Decimal?, _reportLayoutID As Integer?

    Public Sub New(setupID As Integer?, Optional CoNum As Decimal? = Nothing, Optional reportLayoutID As Integer? = Nothing)
        ' This call is required by the designer.
        ' Add any initialization after the InitializeComponent() call.
        InitializeComponent()

        _setupID = setupID
        _CoNum = CoNum
        _reportLayoutID = reportLayoutID
    End Sub

    Private Sub frmExportSetup_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DB = New dbEPDataDataContext(GetConnectionString)

        Me.ExportFormats = (From A In DB.ExportFormats Order By A.FormatName).ToList

        lueBillCategory.Properties.DataSource = Query("SELECT am.ID, ai.ITEM_NUM, Name = ISNULL(ai.ITEM_NAME, am.Item), am.Info
FROM custom.AccntMapping am 
LEFT JOIN ACT_ITEMS ai ON ai.ITEM_NUM = am.ITEM_NUM 
WHERE ISNULL(ai.ITEM_NAME, am.Item) IS NOT NULL AND am.ShowInQBExport = 1
UNION ALL
SELECT NULL, NULL, '<Choose>', NULL
ORDER BY 3
")
        lueBillCategory.Properties.ValueMember = "ID"
        lueBillCategory.Properties.DisplayMember = "Name"

        LoadData(_setupID, _CoNum, _reportLayoutID)
    End Sub

    Sub LoadData(setupID As Integer?, Optional CoNum As Decimal? = Nothing, Optional reportLayoutID As Integer? = Nothing)
        Me.CoOptionsID = setupID.GetValueOrDefault
        Me.ReportLayoutID = reportLayoutID
        If (CoNum.HasValue) Then Me.CoNum = CoNum
        Me.IsNewRecord = setupID.GetValueOrDefault = 0
        Me.IsReportSchedule = reportLayoutID.HasValue

        If reportLayoutID.HasValue Then
            Me.CoOptionsEnt = (From A In DB.CoOptions_Exports Where A.CustomReportLayoutID = Me.ReportLayoutID AndAlso A.CoCode = Me.CoNum).FirstOrDefault
            Me.IsNewRecord = Me.CoOptionsEnt Is Nothing
            Me.CoOptionsID = CoOptionsEnt?.ID
        End If

        Dim CoSelect = From A In DB.COMPANies
        If Me.CoNum > 0 Then
            CoSelect = From A In CoSelect Where A.CONUM = Me.CoNum
        End If
        If IsNewRecord Then
            Me.CoOptionsEnt = New CoOptions_Export With {.CoCode = Me.CoNum, .EmailSubject = DefaultEmailSubjectName}
            If IsReportSchedule Then
                Me.CoOptionsEnt.ExportFormat = 116
                Me.CoOptionsEnt.CustomReportLayoutID = Me.ReportLayoutID
                Me.CoOptionsEnt.FileCategory = "Clients Reports"
                Me.CoOptionsEnt.CutOffDate = DateTime.Today
                Me.CoOptionsEnt.AutoSend = True
                Dim rep = (From A In DB.CustomReportLayouts Where A.ID = Me.ReportLayoutID Select A.LayoutName).FirstOrDefault
                Me.CoOptionsEnt.ExportName = rep
            End If
            Me.rgrpSchedulePattern.EditValue = "Payroll"
            DB.CoOptions_Exports.InsertOnSubmit(Me.CoOptionsEnt)
        ElseIf Me.CoOptionsEnt Is Nothing Then
            Me.CoOptionsEnt = (From A In DB.CoOptions_Exports Where A.ID = Me.CoOptionsID).FirstOrDefault
            CoSelect = From A In CoSelect Where A.CONUM = Me.CoOptionsEnt.CoCode
        End If
        Dim Companies = (From A In CoSelect Select New With {A.CONUM, A.CO_NAME}).ToList
        Me.CoList = (From a In Companies Select New view_CompanySumarry With {.CONUM = a.CONUM, .CO_NAME = a.CO_NAME}).ToList

        Me.CoCodeComboBox.Properties.DataSource = Me.CoList
        Me.ExportFormatComboBox.Properties.DataSource = ExportFormats

        'Dim FileCategories = Brands_FrontDesk.modGlobals.GetUdfValue("QBExportFileCategorory")
        'If Not String.IsNullOrEmpty(FileCategories) Then
        '    Me.cboFileCategory.Items.AddRange((From A In FileCategories.Split(",") Select v = A.Trim Order By v).ToArray)
        'End If
        If Not IsReportSchedule Then
            FileCategories = (From A In DB.FileCategories Order By A.FileCategory).ToList
            Me.cboFileCategory.Properties.DataSource = FileCategories
        End If

        Me.ExportFormatComboBox.Enabled = Me.CoOptionsEnt.CustomReportLayoutID Is Nothing

        'used to locate which control took most time to load
        'we ended up removing the filepath behavior.  Qb directory had 250k files and had to loop all.

        'Dim objBindingCollection() As Binding
        'Dim objCollection() As Object
        'ReDim objBindingCollection(0)
        'ReDim objCollection(0)

        'For Each containor In Me.Controls
        '    For Each obj As Control In containor.controls
        '        If TypeOf obj Is TextEdit OrElse TypeOf obj Is LookUpEdit OrElse TypeOf obj Is CheckEdit OrElse TypeOf obj Is CheckedComboBoxEdit OrElse TypeOf obj Is ImageComboBoxEdit OrElse TypeOf obj Is NumericUpDown OrElse TypeOf obj Is MemoEdit Then
        '            ReDim Preserve objBindingCollection(objBindingCollection.Count)
        '            ReDim Preserve objCollection(objCollection.Count)

        '            If obj.DataBindings().Count <> 0 Then
        '                objCollection(objCollection.Count - 1) = obj
        '                objBindingCollection(objBindingCollection.Count - 1) = obj.DataBindings()(0)
        '                obj.DataBindings.RemoveAt(0)
        '            End If
        '        End If
        '    Next
        'Next

        'uh uh here is the issue with gl ?????
        'FileLocationTextBox.DataBindings.RemoveAt(0)
        If CoOptionsEnt.DaysAfterCheckDate Is Nothing Then
            Me.DaysAfterCheckDateNumericUpDown.DataBindings.Clear()
        End If

        If CoOptionsEnt.DaysAfterPayrollDate Is Nothing Then
            Me.DaysAfterPayrollDateNumericUpDown.DataBindings.Clear()
        End If

        If CoOptionsEnt.PerPayrollScheduleType = 1 Then
            Me.DaysAfterPayrollDateNumericUpDown.Visible = True
            If Me.DaysAfterPayrollDateNumericUpDown.DataBindings.Count = 0 Then
                Me.DaysAfterPayrollDateNumericUpDown.DataBindings.Add(New System.Windows.Forms.Binding("Value", Me.BindingSource1, "DaysAfterPayrollDate", True))
            End If
            Me.DaysAfterCheckDateNumericUpDown.Visible = False
        ElseIf CoOptionsEnt.PerPayrollScheduleType = 2 Then
            Me.DaysAfterCheckDateNumericUpDown.Visible = True
            If Me.DaysAfterCheckDateNumericUpDown.DataBindings.Count = 0 Then
                Me.DaysAfterCheckDateNumericUpDown.DataBindings.Add(New System.Windows.Forms.Binding("Value", Me.BindingSource1, "DaysAfterCheckDate", True))
            End If
            Me.DaysAfterPayrollDateNumericUpDown.Visible = False
        Else
            Me.DaysAfterPayrollDateNumericUpDown.Visible = False
            Me.DaysAfterCheckDateNumericUpDown.Visible = False
        End If

        Me.BindingSource1.DataSource = CoOptionsEnt

        ''we start from 1
        'If objCollection.Count > 1 Then
        '    Dim x As Integer
        '    For x = 1 To objCollection.Count - 1
        '        If Not objCollection(x) Is Nothing AndAlso objCollection(x).name <> "FileLocationTextBox" Then
        '            objCollection(x).DataBindings.Add(objBindingCollection(x))
        '        End If
        '    Next
        'End If

        _IsLoaded = True

        If Not IsNewRecord Then
            'added by solomon
            ShowAction()

            Dim PerPayrollScheduleType As Short = CoOptionsEnt.PerPayrollScheduleType.GetValueOrDefault
            Me.rbtnSendImmediately.Checked = PerPayrollScheduleType = 0
            Me.rbtnSendAfrerPrintDate.Checked = PerPayrollScheduleType = 1
            Me.rbtnSendAfterCheckDate.Checked = PerPayrollScheduleType = 2

            If CoOptionsEnt.RecurrencePatttern IsNot Nothing Then
                Dim pattern = DevExpress.XtraScheduler.Compatibility.StaticAppointmentFactory.CreateAppointment(DevExpress.XtraScheduler.AppointmentType.Pattern)
                pattern.RecurrenceInfo.FromXml(CoOptionsEnt.RecurrencePatttern)
                Dim rInfor = pattern.RecurrenceInfo
                Select Case rInfor.Type
                    Case DevExpress.XtraScheduler.RecurrenceType.Weekly
                        Me.WeeklyRecurrenceControl1.RecurrenceInfo = rInfor
                    Case DevExpress.XtraScheduler.RecurrenceType.Monthly
                        Me.MonthlyRecurrenceControl1.RecurrenceInfo = rInfor
                    Case DevExpress.XtraScheduler.RecurrenceType.Yearly
                        Me.YearlyRecurrenceControl1.RecurrenceInfo = rInfor
                End Select
                Me.rgrpSchedulePattern.EditValue = rInfor.Type.ToString
                Me.txtScheduleTime.EditValue = rInfor.Start.TimeOfDay
            Else
                Me.rgrpSchedulePattern.EditValue = "Payroll"
            End If

            Me.ActiveControl = Me.EmailToTextBox
        End If
        CoCodeComboBox.Enabled = IsNewRecord
        cmdDelete.Enabled = Not IsNewRecord

        If IsReportSchedule Then
            Dim h = Me.pnlQBOptions.Height
            Me.Height -= h
            Me.XtraTabPageFormatNotes.PageVisible = False
        End If
        Me.pnlQBOptions.Visible = Not IsReportSchedule
        Me.pnlFtp.Visible = Not IsReportSchedule
        Me.CenterToScreen()
        btnCopy.Visible = Not IsNewRecord
        labelCopyToCoNote.Visible = IsNewRecord
    End Sub

    Sub ShowAction()
        If Me.CoOptionsEnt.ExportFormat = 0 Then Exit Sub
        'Dim DB As New dbBrandsDataContext
        Dim FtpInfo = (From A In Me.ExportFormats Where A.FormatID = CoOptionsEnt.ExportFormat).FirstOrDefault

        If FtpInfo.FtpSessionId IsNot Nothing Then
            txtFtp.ReadOnly = True
            txtFtp.Text = FtpInfo.FtpSession.HostName
            txtFtpFolder.Visible = False
            txtFtpPort.Visible = False
            cbEnableFTP.Visible = False
        Else
            txtFtp.EditValue = CoOptionsEnt.FtpHostName
            txtFtp.ReadOnly = False
            txtFtpFolder.Visible = True
            txtFtpPort.Visible = True
            cbEnableFTP.Visible = True
            cbEnableFTP.Checked = txtFtp.Text.IsNotNullOrWhiteSpace
        End If
        cbEnableFTP_CheckedChanged(Nothing, Nothing)
    End Sub

    Private Sub cbEnableFTP_CheckedChanged(sender As Object, e As EventArgs) Handles cbEnableFTP.CheckedChanged
        GroupFtp.Enabled = cbEnableFTP.Checked
        If cbEnableFTP.Checked Then

        Else
            CoOptionsEnt.FtpHostName = Nothing
            CoOptionsEnt.FtpUserName = Nothing
            CoOptionsEnt.FtpPassword = Nothing
            CoOptionsEnt.FtpFolder = Nothing
            CoOptionsEnt.FtpPort = Nothing
            txtFtp.Text = Nothing
            BindingSource1.EndEdit()
        End If
    End Sub

    Sub LoadDivisions()
        If Me.CoCodeComboBox.EditValue Is Nothing Then Exit Sub
        Dim CoCode As Decimal = Me.CoCodeComboBox.EditValue
        Using db = New dbEPDataDataContext(GetConnectionString)
            Dim Divisions = (From A In db.DIVISIONs Where A.CONUM = CoCode Order By A.DDIVNUM Select DivNum = A.DDIVNUM, DivName = A.DDIVNUM & " " & A.DDIVNAME).ToList
            Me.DivisionsTextBox.Properties.DataSource = Divisions
            Me.DivisionsTextBox.Properties.ValueMember = "DivNum"
            Me.DivisionsTextBox.Properties.DisplayMember = "DivName"

            Dim Departments = (From A In db.DEPARTMENTs Where A.CONUM = CoCode Order By A.DEPTNUM Select DeptNum = A.DEPTNUM, DeptName = A.DEPTNUM & " " & A.DEPT_DESC).Distinct.ToList
            Me.DepartmentsTextBox.Properties.DataSource = Departments
            Me.DepartmentsTextBox.Properties.ValueMember = "DeptNum"
            Me.DepartmentsTextBox.Properties.DisplayMember = "DeptName"
        End Using
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        Try
            Me.Validate()
            Dim CutOffDate = Me.txtCustOffDate.DateTime

            If Me.CoOptionsEnt.CoCode = 0 Then
                DisplayMessageBox("Company not selected")
                Return
            ElseIf Me.CoOptionsEnt.ExportFormat = 0 Then
                DisplayMessageBox("Export format not selected")
                Return
            ElseIf Not Me.IsReportSchedule AndAlso Not Me.cboFileCategory.HasValue Then
                DisplayMessageBox("File category not selected")
                Me.cboFileCategory.Select()
                Return
            ElseIf Not Me.IsReportSchedule AndAlso Not Me.lueBillCategory.HasValue Then
                Dim BillingCatExcludedFormats = GetUdfValue("Export Formats Not Requiring Billing Category").Split(",")
                If BillingCatExcludedFormats.Where(Function(f) f = ExportFormatComboBox.EditValue.ToString()).Count = 0 Then
                    DisplayMessageBox("Bill category not selected")
                    Me.lueBillCategory.Select()
                    Return
                End If
            End If

            If rgrpSchedulePattern.EditValue <> "Payroll" Then
                Dim rType As DevExpress.XtraScheduler.RecurrenceType = [Enum].Parse(GetType(DevExpress.XtraScheduler.RecurrenceType), Me.rgrpSchedulePattern.EditValue)
                Dim rInfo As DevExpress.XtraScheduler.RecurrenceInfo = Nothing
                Dim vArgs = New DevExpress.XtraScheduler.UI.ValidationArgs
                Select Case rType
                    Case DevExpress.XtraScheduler.RecurrenceType.Weekly
                        Me.WeeklyRecurrenceControl1.ValidateValues(vArgs)
                        rInfo = Me.WeeklyRecurrenceControl1.RecurrenceInfo
                    Case DevExpress.XtraScheduler.RecurrenceType.Monthly
                        Me.MonthlyRecurrenceControl1.ValidateValues(vArgs)
                        rInfo = Me.MonthlyRecurrenceControl1.RecurrenceInfo
                    Case DevExpress.XtraScheduler.RecurrenceType.Yearly
                        Me.YearlyRecurrenceControl1.ValidateValues(vArgs)
                        rInfo = Me.YearlyRecurrenceControl1.RecurrenceInfo
                End Select
                If Not vArgs.Valid Then
                    MessageBox.Show(vArgs.ErrorMessage, "Invalid Schedule")
                    Exit Sub
                End If
                rInfo.Start = CutOffDate.Add(Me.txtScheduleTime.Time.TimeOfDay)

                Dim pattern = DevExpress.XtraScheduler.Compatibility.StaticAppointmentFactory.CreateAppointment(DevExpress.XtraScheduler.AppointmentType.Pattern)
                pattern.RecurrenceInfo.FromXml(rInfo.ToXml)
                pattern.AllDay = True

                CoOptionsEnt.RecurrenceType = rType.ToString
                CoOptionsEnt.RecurrencePatttern = rInfo.ToXml
                Dim description = DevExpress.XtraScheduler.RecurrenceInfo.GetDescription(pattern, DayOfWeek.Sunday)
                CoOptionsEnt.RecurrenceDescription = description.Replace("for 1 day", "")

                pattern.AllDay = False 'set to false to get the correct time
                Dim calculator = DevExpress.XtraScheduler.OccurrenceCalculator.CreateInstance(rInfo)
                Dim StartDate = CutOffDate
                Dim sqldate = SqlTypes.SqlDateTime.MinValue.Value
                If sqldate > StartDate OrElse DateTime.MinValue > StartDate Then
                    XtraMessageBox.Show("invalid start date")
                    Exit Sub
                End If
                Dim LastRunDate = CoOptionsEnt.LastRunDate
                If LastRunDate.HasValue AndAlso LastRunDate.Value.Date > StartDate Then StartDate = LastRunDate.Value.Date.AddHours(23)
                Dim occurrences = calculator.CalcOccurrences(New DevExpress.XtraScheduler.TimeInterval(StartDate, Today.AddYears(1).AddDays(1)), pattern)
                Dim q = String.Join(",", (From A In occurrences Select A.Start.ToString("g")))
                CoOptionsEnt.Recurrences = q
                CoOptionsEnt.NextRunDate = calculator.FindNextOccurrenceTimeAfter(Today, pattern)
            Else
                CoOptionsEnt.RecurrenceType = Nothing
                CoOptionsEnt.RecurrencePatttern = Nothing
                CoOptionsEnt.RecurrenceDescription = Nothing
                CoOptionsEnt.Recurrences = Nothing
                CoOptionsEnt.NextRunDate = Nothing
                If rbtnSendImmediately.Checked Then
                    CoOptionsEnt.PerPayrollScheduleType = 0
                ElseIf rbtnSendAfrerPrintDate.Checked Then
                    CoOptionsEnt.PerPayrollScheduleType = 1
                    CoOptionsEnt.DaysAfterPayrollDate = Me.DaysAfterPayrollDateNumericUpDown.Value
                ElseIf rbtnSendAfterCheckDate.Checked Then
                    CoOptionsEnt.PerPayrollScheduleType = 2
                    CoOptionsEnt.DaysAfterCheckDate = Me.DaysAfterCheckDateNumericUpDown.Value
                End If
            End If

            If Not txtFtp.ReadOnly Then
                CoOptionsEnt.FtpHostName = txtFtp.Text
                If Not txtFtp.HasValue Then
                    Dim fieldName As String = String.Empty
                    If Not txtFtpUsername.HasValue Then fieldName = "Username"
                    If Not txtFtpPassword.HasValue Then fieldName = "Password"
                    If Not txtFtpFolder.HasValue Then fieldName = "Folder"
                    If Not txtFtpPort.HasValue Then fieldName = "Port"
                    If fieldName.IsNullOrWhiteSpace Then
                        DisplayMessageBox($"FTP {fieldName} is required (When FTP Host is set)")
                        Exit Sub
                    End If
                End If
            End If

            'CoOptionsEnt.FileCategory = Me.cboFileCategory.EditValue
            If IsNewRecord Then txtCreatedBy.Text = Environment.UserName + "  " + Now.ToString("MM/dd/yyyy HH:mm") + "  " + Environment.MachineName
            txtModfiedBy.Text = Environment.UserName + "  " + Now.ToString("MM/dd/yyyy HH:mm") + "  " + Environment.MachineName

            If Me.CoOptionsEnt.CustomReportLayoutID.HasValue AndAlso Me.CoOptionsEnt.FileNameOverride Is Nothing Then
                Me.CoOptionsEnt.FileNameOverride = Me.CoOptionsEnt.ExportName
            End If

            BindingSource1.EndEdit()

            Dim Reccount = DB.SaveChanges

            If Me.UpdateFormatNotes Then
                Dim selectedFormat = (From A In Me.ExportFormats Where A.FormatID = CoOptionsEnt.ExportFormat).First
                selectedFormat.Notes = Me.FileFormatNotes.EditValue
                Reccount = DB.SaveChanges()
            End If

            Me.DialogResult = System.Windows.Forms.DialogResult.OK
            Me.Close()
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        BindingSource1.CancelEdit()
        DialogResult = System.Windows.Forms.DialogResult.Cancel
        Close()
    End Sub

    Private Sub CoCodeComboBox_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CoCodeComboBox.EditValueChanged
        'Dim co As dsBrands.COMPANYRow = dsBrands.COMPANY.FindByCONUM(CoCodeComboBox.SelectedValue)
        Dim CoName As String = Nothing
        If Me.CoCodeComboBox.HasValue Then
            CoName = (From A In Me.CoList Where A.CONUM = Me.CoCodeComboBox.EditValue Select A.CO_NAME).FirstOrDefault
        End If
        If CoName IsNot Nothing Then
            txtCompanyName.Text = CoName
            LoadDivisions()
        Else
            txtCompanyName.Text = Nothing
        End If
    End Sub

    Private Sub ValidateData()
        If Me.AutoSendCheckBox.Checked AndAlso String.IsNullOrEmpty(Me.EmailToTextBox.Text) Then
            MessageBox.Show("Auto send Is only valid With an email address")
            Me.AutoSendCheckBox.Checked = False
        End If
    End Sub

    Private Sub ExportFormatComboBox_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ExportFormatComboBox.Validated
        If Not _IsLoaded Then Exit Sub
        If Not Me.ExportFormatComboBox.IsModified Then Exit Sub
        Me.Validate()
        If Me.CoOptionsEnt.ExportFormat = 0 Then Exit Sub

        Dim FF = (From A In Me.ExportFormats Where A.FormatID = CoOptionsEnt.ExportFormat).FirstOrDefault

        Me.XtraTabPageFormatNotes.Text = FF.FormatName & " File-Type Notes"
        If Not String.IsNullOrEmpty(FF.Notes) Then
            Me.FileFormatNotes.EditValue = FF.Notes
            Me.XtraTabPageFormatNotes.Appearance.Header.Font = New Font(Me.XtraTabPageFormatNotes.Appearance.Header.Font, FontStyle.Bold)
        End If

        Me.CoOptionsEnt.FileLocation = FF.FileLocation
        Me.CoOptionsEnt.IncludeAttachement = FF.IncludeAttachement.GetValueOrDefault

        'added by solomon
        ShowAction()

        Me.BindingSource1.ResetBindings(False)
    End Sub

    Private Sub lblFormatsLink_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblFormat.Click
        Process.Start(lblFormat.Tag)
    End Sub

    Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
        If MessageBox.Show("Are you sure you want To delete this setup?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Stop) = System.Windows.Forms.DialogResult.Yes Then
            'Dim DB As New dbBrandsDataContext
            'Dim Ent = (From A In DB.CoOptions Where A.ID = My.Forms.frmMain.CoOptionsID).SingleOrDefault
            'DB.ExtractLogs.DeleteAllOnSubmit(Ent.ExtractLogs.ToList)
            DB.CoOptions_Exports.DeleteOnSubmit(CoOptionsEnt)
            DB.SubmitChanges()
            'Me.CoOptionsTableAdapter.Delete(My.Forms.frmMain.CoOptionsID)
            Me.DialogResult = System.Windows.Forms.DialogResult.OK
            Me.Close()
        End If
    End Sub

    Private Sub rbtnSendImmediately_CheckedChanged(sender As Object, e As EventArgs) Handles rbtnSendImmediately.CheckedChanged, rbtnSendAfterCheckDate.CheckedChanged, rbtnSendAfrerPrintDate.CheckedChanged
        Me.DaysAfterCheckDateNumericUpDown.Enabled = rbtnSendAfterCheckDate.Checked
        Me.DaysAfterPayrollDateNumericUpDown.Enabled = rbtnSendAfrerPrintDate.Checked
        Me.DaysAfterCheckDateNumericUpDown.Visible = rbtnSendAfterCheckDate.Checked
        Me.DaysAfterPayrollDateNumericUpDown.Visible = rbtnSendAfrerPrintDate.Checked
        If rbtnSendImmediately.Checked Then
            Me.DaysAfterPayrollDateNumericUpDown.Value = 0
            Me.DaysAfterCheckDateNumericUpDown.Value = 0
        ElseIf rbtnSendAfrerPrintDate.Checked Then
            Me.DaysAfterCheckDateNumericUpDown.Value = 0
        ElseIf rbtnSendAfterCheckDate.Checked Then
            Me.DaysAfterPayrollDateNumericUpDown.Value = 0
        End If
    End Sub

    Private Sub rgrpSchedulePattern_EditValueChanged(sender As Object, e As EventArgs) Handles rgrpSchedulePattern.EditValueChanged
        Me.MonthlyRecurrenceControl1.Visible = rgrpSchedulePattern.EditValue = "Monthly"
        Me.YearlyRecurrenceControl1.Visible = rgrpSchedulePattern.EditValue = "Yearly"
        Me.WeeklyRecurrenceControl1.Visible = rgrpSchedulePattern.EditValue = "Weekly"
        Me.PerPayrollSchedule.Visible = rgrpSchedulePattern.EditValue = "Payroll"
        Me.pnlScheduleTime.Visible = rgrpSchedulePattern.EditValue <> "Payroll"
    End Sub

    Private Sub DepartmentsTextBox_CustomDisplayText(sender As Object, e As DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs) Handles DivisionsTextBox.CustomDisplayText, DepartmentsTextBox.CustomDisplayText
        If e.Value Is Nothing OrElse IsDBNull(e.Value) Then
            Return
        Else
            e.DisplayText = e.Value
        End If
    End Sub

    Private Sub cboFileCategory_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cboFileCategory.EditValueChanged
        If Not Me.cboFileCategory.HasValue Then Exit Sub
        Dim selected = (From a In FileCategories Where a.FileCategory = Me.cboFileCategory.EditValue).FirstOrDefault
        If selected IsNot Nothing AndAlso IsNewRecord Then
            If selected.SetAfterCheckDate Then
                Me.rbtnSendAfterCheckDate.Checked = True
                Me.DaysAfterCheckDateNumericUpDown.Value = selected.DaysAfterCheckDate
            ElseIf selected.SetAfterPrintDate Then
                Me.rbtnSendAfrerPrintDate.Checked = True
                Me.DaysAfterPayrollDateNumericUpDown.Value = selected.DaysAfterPrintDate
            End If
        End If
    End Sub

    Private Sub FileFormatNotes_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles FileFormatNotes.Validating
        If Me.FileFormatNotes.IsModified Then
            Me.UpdateFormatNotes = True
        End If
    End Sub

    Private Sub sbFolder_Click(sender As Object, e As EventArgs) Handles sbFolder.Click
        Dim folder As New FolderBrowserDialog()
        If folder.ShowDialog() = DialogResult.OK Then
            FileLocationTextBox.Text = folder.SelectedPath
        End If
    End Sub

    Private Sub btnCopy_Click(sender As Object, e As EventArgs) Handles btnCopy.Click
        Try
            Dim args = New XtraInputBoxArgs()
            args.Caption = "Copy GL to Company"
            args.Prompt = "Please select a company"
            Dim pce = New PopupContainerEdit
            pce.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
            ucSearchCompany.BindPopupContainerEdit(pce)
            args.Editor = pce
            Dim conum As Decimal? = XtraInputBox.Show(args)

            If Not conum.HasValue Then Return

            'args.Prompt = "Please put a Building Code to use"
            'Dim txtNewBuildingCode As TextEdit = New TextEdit()
            'txtNewBuildingCode.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Custom.RegEx
            'args.Editor = txtNewBuildingCode
            'args.DefaultResponse = 425
            'Dim BuildingCode As Decimal? = XtraInputBox.Show(args)

            'If Not BuildingCode.HasValue Then Return

            'MessageBox.Show(conum, BuildingCode)

            'DB.ExecuteCommand(String.Format("EPDATA.custom.prc_SetupStdGLReportForCo", 425)

            DB.ExecuteCommand(String.Format("exec brands.dbo.prc_CopyExport {0}, {1}", CoOptionsID, conum.Value))
            MessageBox.Show($"Copied setup to company #{conum.Value}")
        Catch ex As Exception
            DisplayErrorMessage("error in copying export", ex)
        End Try
    End Sub
End Class